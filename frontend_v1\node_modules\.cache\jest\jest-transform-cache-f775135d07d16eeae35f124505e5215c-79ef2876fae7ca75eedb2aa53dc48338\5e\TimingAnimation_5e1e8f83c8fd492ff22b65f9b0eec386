96b5ba2c81837b71088d7afe7e0d4bff
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _readOnlyError2 = _interopRequireDefault(require("@babel/runtime/helpers/readOnlyError"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _AnimatedColor = _interopRequireDefault(require("../nodes/AnimatedColor"));
var _Animation2 = _interopRequireDefault(require("./Animation"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var _easeInOut;
function easeInOut() {
  if (!_easeInOut) {
    var Easing = require("../Easing").default;
    _easeInOut = Easing.inOut(Easing.ease);
  }
  return _easeInOut;
}
var TimingAnimation = exports.default = function (_Animation) {
  function TimingAnimation(config) {
    var _config$easing, _config$duration, _config$delay;
    var _this;
    (0, _classCallCheck2.default)(this, TimingAnimation);
    _this = _callSuper(this, TimingAnimation, [config]);
    _this._toValue = config.toValue;
    _this._easing = (_config$easing = config.easing) != null ? _config$easing : easeInOut();
    _this._duration = (_config$duration = config.duration) != null ? _config$duration : 500;
    _this._delay = (_config$delay = config.delay) != null ? _config$delay : 0;
    _this._platformConfig = config.platformConfig;
    return _this;
  }
  (0, _inherits2.default)(TimingAnimation, _Animation);
  return (0, _createClass2.default)(TimingAnimation, [{
    key: "__getNativeAnimationConfig",
    value: function __getNativeAnimationConfig() {
      var frameDuration = 1000.0 / 60.0;
      var frames = [];
      var numFrames = Math.round(this._duration / frameDuration);
      for (var frame = 0; frame < numFrames; frame++) {
        frames.push(this._easing(frame / numFrames));
      }
      frames.push(this._easing(1));
      return {
        type: 'frames',
        frames: frames,
        toValue: this._toValue,
        iterations: this.__iterations,
        platformConfig: this._platformConfig,
        debugID: this.__getDebugID()
      };
    }
  }, {
    key: "start",
    value: function start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue) {
      var _this2 = this;
      _superPropGet(TimingAnimation, "start", this, 3)([fromValue, onUpdate, onEnd, previousAnimation, animatedValue]);
      this._fromValue = fromValue;
      this._onUpdate = onUpdate;
      var start = function start() {
        _this2._startTime = Date.now();
        var useNativeDriver = _this2.__startAnimationIfNative(animatedValue);
        if (!useNativeDriver) {
          if (_this2._duration === 0) {
            _this2._onUpdate(_this2._toValue);
            _this2.__notifyAnimationEnd({
              finished: true
            });
          } else {
            _this2._animationFrame = requestAnimationFrame(function () {
              return _this2.onUpdate();
            });
          }
        }
      };
      if (this._delay) {
        this._timeout = setTimeout(start, this._delay);
      } else {
        start();
      }
    }
  }, {
    key: "onUpdate",
    value: function onUpdate() {
      var now = Date.now();
      if (now >= this._startTime + this._duration) {
        if (this._duration === 0) {
          this._onUpdate(this._toValue);
        } else {
          this._onUpdate(this._fromValue + this._easing(1) * (this._toValue - this._fromValue));
        }
        this.__notifyAnimationEnd({
          finished: true
        });
        return;
      }
      this._onUpdate(this._fromValue + this._easing((now - this._startTime) / this._duration) * (this._toValue - this._fromValue));
      if (this.__active) {
        this._animationFrame = requestAnimationFrame(this.onUpdate.bind(this));
      }
    }
  }, {
    key: "stop",
    value: function stop() {
      _superPropGet(TimingAnimation, "stop", this, 3)([]);
      clearTimeout(this._timeout);
      if (this._animationFrame != null) {
        global.cancelAnimationFrame(this._animationFrame);
      }
      this.__notifyAnimationEnd({
        finished: false
      });
    }
  }]);
}(_Animation2.default);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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