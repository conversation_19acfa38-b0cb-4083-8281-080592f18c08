500724a5f803ed9afedd0a36b6546e22
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _Colors = _interopRequireDefault(require("../../constants/Colors"));
var _contrastValidator = require("../contrastValidator");
describe('Button Contrast Validation - WCAG 2.1 AA Compliance', function () {
  describe('Primary Button Contrast', function () {
    it('should meet WCAG AA standards for primary button text', function () {
      var backgroundColors = [_Colors.default.interactive.primary.default, _Colors.default.interactive.primary.hover, _Colors.default.interactive.primary.pressed];
      var textColor = _Colors.default.interactive.primary.text;
      backgroundColors.forEach(function (backgroundColor, index) {
        var stateName = ['default', 'hover', 'pressed'][index];
        var validation = (0, _contrastValidator.validateContrast)(textColor, backgroundColor);
        expect(validation.isValid).toBe(true);
        expect(validation.ratio).toBeGreaterThanOrEqual(_contrastValidator.CONTRAST_STANDARDS.AA_NORMAL);
        console.log(`Primary ${stateName}: ${validation.ratio.toFixed(2)}:1 contrast`);
      });
    });
    it('should have appropriate disabled state contrast', function () {
      var validation = (0, _contrastValidator.validateContrast)(_Colors.default.interactive.primary.textDisabled, _Colors.default.interactive.primary.disabled);
      expect(validation.ratio).toBeGreaterThan(1.5);
      console.log(`Primary disabled: ${validation.ratio.toFixed(2)}:1 contrast`);
    });
  });
  describe('Secondary Button Contrast', function () {
    it('should meet WCAG AA standards for secondary button text on white background', function () {
      var validation = (0, _contrastValidator.validateContrast)(_Colors.default.interactive.secondary.text, '#FFFFFF');
      expect(validation.isValid).toBe(true);
      expect(validation.ratio).toBeGreaterThanOrEqual(_contrastValidator.CONTRAST_STANDARDS.AA_NORMAL);
      console.log(`Secondary text: ${validation.ratio.toFixed(2)}:1 contrast`);
    });
    it('should meet WCAG AA standards for secondary button border', function () {
      var validation = (0, _contrastValidator.validateContrast)(_Colors.default.interactive.secondary.border, '#FFFFFF');
      expect(validation.isValid).toBe(true);
      expect(validation.ratio).toBeGreaterThanOrEqual(_contrastValidator.CONTRAST_STANDARDS.NON_TEXT);
      console.log(`Secondary border: ${validation.ratio.toFixed(2)}:1 contrast`);
    });
  });
  describe('Destructive Button Contrast', function () {
    it('should meet WCAG AA standards for destructive button text', function () {
      var backgroundColors = [_Colors.default.interactive.destructive.default, _Colors.default.interactive.destructive.hover, _Colors.default.interactive.destructive.pressed];
      var textColor = _Colors.default.interactive.destructive.text;
      backgroundColors.forEach(function (backgroundColor, index) {
        var stateName = ['default', 'hover', 'pressed'][index];
        var validation = (0, _contrastValidator.validateContrast)(textColor, backgroundColor);
        expect(validation.isValid).toBe(true);
        expect(validation.ratio).toBeGreaterThanOrEqual(_contrastValidator.CONTRAST_STANDARDS.AA_NORMAL);
        console.log(`Destructive ${stateName}: ${validation.ratio.toFixed(2)}:1 contrast`);
      });
    });
  });
  describe('Ghost Button Contrast', function () {
    it('should meet WCAG AA standards for ghost button text on white background', function () {
      var validation = (0, _contrastValidator.validateContrast)(_Colors.default.interactive.ghost.text, '#FFFFFF');
      expect(validation.isValid).toBe(true);
      expect(validation.ratio).toBeGreaterThanOrEqual(_contrastValidator.CONTRAST_STANDARDS.AA_NORMAL);
      console.log(`Ghost text: ${validation.ratio.toFixed(2)}:1 contrast`);
    });
  });
  describe('Focus States Contrast', function () {
    it('should meet WCAG AA standards for focus indicators', function () {
      var focusColors = [_Colors.default.focus.ring, _Colors.default.focus.outline, _Colors.default.focus.sage];
      focusColors.forEach(function (focusColor, index) {
        var colorName = ['ring', 'outline', 'sage'][index];
        var validation = (0, _contrastValidator.validateContrast)(focusColor, '#FFFFFF');
        expect(validation.ratio).toBeGreaterThanOrEqual(_contrastValidator.CONTRAST_STANDARDS.NON_TEXT);
        console.log(`Focus ${colorName}: ${validation.ratio.toFixed(2)}:1 contrast`);
      });
    });
  });
  describe('Comprehensive Color Combinations', function () {
    it('should validate all interactive color combinations', function () {
      var combinations = [{
        name: 'Primary Default',
        foreground: _Colors.default.interactive.primary.text,
        background: _Colors.default.interactive.primary.default,
        expected: 'pass'
      }, {
        name: 'Primary Hover',
        foreground: _Colors.default.interactive.primary.text,
        background: _Colors.default.interactive.primary.hover,
        expected: 'pass'
      }, {
        name: 'Primary Pressed',
        foreground: _Colors.default.interactive.primary.text,
        background: _Colors.default.interactive.primary.pressed,
        expected: 'pass'
      }, {
        name: 'Secondary Text',
        foreground: _Colors.default.interactive.secondary.text,
        background: '#FFFFFF',
        expected: 'pass'
      }, {
        name: 'Secondary Border',
        foreground: _Colors.default.interactive.secondary.border,
        background: '#FFFFFF',
        expected: 'pass'
      }, {
        name: 'Destructive Default',
        foreground: _Colors.default.interactive.destructive.text,
        background: _Colors.default.interactive.destructive.default,
        expected: 'pass'
      }, {
        name: 'Ghost Text',
        foreground: _Colors.default.interactive.ghost.text,
        background: '#FFFFFF',
        expected: 'pass'
      }];
      var results = [];
      combinations.forEach(function (_ref) {
        var name = _ref.name,
          foreground = _ref.foreground,
          background = _ref.background,
          expected = _ref.expected;
        var validation = (0, _contrastValidator.validateContrast)(foreground, background);
        results.push({
          name: name,
          ratio: validation.ratio,
          isValid: validation.isValid
        });
        if (expected === 'pass') {
          expect(validation.isValid).toBe(true);
        }
        console.log(`${name}: ${validation.ratio.toFixed(2)}:1 - ${validation.isValid ? 'PASS' : 'FAIL'}`);
      });
      var passCount = results.filter(function (r) {
        return r.isValid;
      }).length;
      var totalCount = results.length;
      console.log(`\nContrast Validation Summary: ${passCount}/${totalCount} combinations passed`);
      expect(passCount).toBe(totalCount);
    });
  });
  describe('Large Text Contrast', function () {
    it('should meet WCAG AA standards for large text (18pt+)', function () {
      var combinations = [{
        name: 'Primary Large Text',
        foreground: _Colors.default.interactive.primary.text,
        background: _Colors.default.interactive.primary.default
      }, {
        name: 'Secondary Large Text',
        foreground: _Colors.default.interactive.secondary.text,
        background: '#FFFFFF'
      }];
      combinations.forEach(function (_ref2) {
        var name = _ref2.name,
          foreground = _ref2.foreground,
          background = _ref2.background;
        var validation = (0, _contrastValidator.validateContrast)(foreground, background, true);
        expect(validation.isValid).toBe(true);
        expect(validation.ratio).toBeGreaterThanOrEqual(_contrastValidator.CONTRAST_STANDARDS.AA_LARGE);
        console.log(`${name} (Large): ${validation.ratio.toFixed(2)}:1 contrast`);
      });
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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