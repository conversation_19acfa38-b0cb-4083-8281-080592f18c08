{"version": 3, "names": ["_asyncStorage", "_interopRequireDefault", "require", "_performanceMonitor", "DEFAULT_CONFIG", "maxMemorySize", "maxStorageSize", "defaultTTL", "cleanupInterval", "enableCompression", "enableEncryption", "CachingService", "config", "arguments", "length", "undefined", "_classCallCheck2", "default", "memoryCache", "Map", "cleanupTimer", "Object", "assign", "metrics", "memoryUsage", "storageUsage", "hitRate", "missRate", "totalRequests", "totalHits", "totalMisses", "startCleanupTimer", "_createClass2", "key", "value", "_initialize", "_asyncToGenerator2", "loadStorageMetrics", "cleanupExpiredEntries", "console", "log", "error", "initialize", "apply", "set<PERSON><PERSON>ory", "data", "ttl", "entry", "timestamp", "Date", "now", "size", "calculateSize", "accessCount", "lastAccessed", "getMemoryUsage", "evictLeastRecentlyUsed", "set", "updateMemoryUsage", "performanceMonitor", "trackCacheOperation", "get<PERSON><PERSON>ory", "get", "updateHitRate", "delete", "_setStorage", "serialized", "JSON", "stringify", "AsyncStorage", "setItem", "setStorage", "_x", "_x2", "_x3", "_getStorage", "getItem", "parse", "removeItem", "getStorage", "_x4", "_cacheApiResponse", "endpoint", "params", "generateApiCache<PERSON>ey", "cacheApiResponse", "_x5", "_x6", "_x7", "_x8", "_getCachedApiResponse", "getCachedApiResponse", "_x9", "_x0", "_clearAll", "clear", "keys", "getAllKeys", "cacheKeys", "filter", "startsWith", "multiRemove", "resetMetrics", "clearAll", "getMetrics", "sortedParams", "sort", "reduce", "result", "Array", "from", "values", "total", "oldestEntry", "_ref", "entries", "_ref2", "_slicedToArray2", "_cleanupExpiredEntries", "_ref3", "_ref4", "_loadStorageMetrics", "totalSize", "_this", "setInterval", "destroy", "clearInterval", "cachingService", "exports", "_default"], "sources": ["cachingService.ts"], "sourcesContent": ["/**\n * Comprehensive Caching Service for Performance Optimization\n *\n * Provides multi-level caching strategies including:\n * - Memory caching for frequently accessed data\n * - Persistent storage caching for offline support\n * - Image caching for optimized loading\n * - API response caching with TTL\n * - Component state caching\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport AsyncStorage from '@react-native-async-storage/async-storage';\n\nimport { performanceMonitor } from './performanceMonitor';\n\n// Cache configuration\nexport interface CacheConfig {\n  maxMemorySize: number; // MB\n  maxStorageSize: number; // MB\n  defaultTTL: number; // milliseconds\n  cleanupInterval: number; // milliseconds\n  enableCompression: boolean;\n  enableEncryption: boolean;\n}\n\nexport interface CacheEntry<T = any> {\n  data: T;\n  timestamp: number;\n  ttl: number;\n  size: number;\n  accessCount: number;\n  lastAccessed: number;\n}\n\nexport interface CacheMetrics {\n  memoryUsage: number;\n  storageUsage: number;\n  hitRate: number;\n  missRate: number;\n  totalRequests: number;\n  totalHits: number;\n  totalMisses: number;\n}\n\nconst DEFAULT_CONFIG: CacheConfig = {\n  maxMemorySize: 50, // 50MB\n  maxStorageSize: 100, // 100MB\n  defaultTTL: 30 * 60 * 1000, // 30 minutes\n  cleanupInterval: 5 * 60 * 1000, // 5 minutes\n  enableCompression: true,\n  enableEncryption: false,\n};\n\nclass CachingService {\n  private memoryCache = new Map<string, CacheEntry>();\n  private config: CacheConfig;\n  private metrics: CacheMetrics;\n  private cleanupTimer: NodeJS.Timeout | null = null;\n\n  constructor(config: Partial<CacheConfig> = {}) {\n    this.config = { ...DEFAULT_CONFIG, ...config };\n    this.metrics = {\n      memoryUsage: 0,\n      storageUsage: 0,\n      hitRate: 0,\n      missRate: 0,\n      totalRequests: 0,\n      totalHits: 0,\n      totalMisses: 0,\n    };\n\n    this.startCleanupTimer();\n  }\n\n  /**\n   * Initialize the caching service\n   */\n  async initialize(): Promise<void> {\n    try {\n      await this.loadStorageMetrics();\n      await this.cleanupExpiredEntries();\n\n      console.log('[CachingService] Initialized successfully');\n    } catch (error) {\n      console.error('[CachingService] Initialization failed:', error);\n    }\n  }\n\n  /**\n   * Store data in memory cache\n   */\n  setMemory<T>(key: string, data: T, ttl?: number): void {\n    const entry: CacheEntry<T> = {\n      data,\n      timestamp: Date.now(),\n      ttl: ttl || this.config.defaultTTL,\n      size: this.calculateSize(data),\n      accessCount: 0,\n      lastAccessed: Date.now(),\n    };\n\n    // Check memory limits\n    if (\n      this.getMemoryUsage() + entry.size >\n      this.config.maxMemorySize * 1024 * 1024\n    ) {\n      this.evictLeastRecentlyUsed();\n    }\n\n    this.memoryCache.set(key, entry);\n    this.updateMemoryUsage();\n\n    performanceMonitor.trackCacheOperation('memory_set', key, entry.size);\n  }\n\n  /**\n   * Get data from memory cache\n   */\n  getMemory<T>(key: string): T | null {\n    this.metrics.totalRequests++;\n\n    const entry = this.memoryCache.get(key) as CacheEntry<T> | undefined;\n\n    if (!entry) {\n      this.metrics.totalMisses++;\n      this.updateHitRate();\n      return null;\n    }\n\n    // Check if expired\n    if (Date.now() - entry.timestamp > entry.ttl) {\n      this.memoryCache.delete(key);\n      this.updateMemoryUsage();\n      this.metrics.totalMisses++;\n      this.updateHitRate();\n      return null;\n    }\n\n    // Update access statistics\n    entry.accessCount++;\n    entry.lastAccessed = Date.now();\n\n    this.metrics.totalHits++;\n    this.updateHitRate();\n\n    performanceMonitor.trackCacheOperation('memory_get', key, entry.size);\n\n    return entry.data;\n  }\n\n  /**\n   * Store data in persistent storage\n   */\n  async setStorage<T>(key: string, data: T, ttl?: number): Promise<void> {\n    try {\n      const entry: CacheEntry<T> = {\n        data,\n        timestamp: Date.now(),\n        ttl: ttl || this.config.defaultTTL,\n        size: this.calculateSize(data),\n        accessCount: 0,\n        lastAccessed: Date.now(),\n      };\n\n      const serialized = JSON.stringify(entry);\n\n      if (this.config.enableCompression) {\n        // In a real implementation, you'd use a compression library\n        // For now, we'll just store as-is\n      }\n\n      await AsyncStorage.setItem(`cache_${key}`, serialized);\n\n      performanceMonitor.trackCacheOperation('storage_set', key, entry.size);\n    } catch (error) {\n      console.error('[CachingService] Storage set failed:', error);\n    }\n  }\n\n  /**\n   * Get data from persistent storage\n   */\n  async getStorage<T>(key: string): Promise<T | null> {\n    try {\n      this.metrics.totalRequests++;\n\n      const serialized = await AsyncStorage.getItem(`cache_${key}`);\n\n      if (!serialized) {\n        this.metrics.totalMisses++;\n        this.updateHitRate();\n        return null;\n      }\n\n      const entry: CacheEntry<T> = JSON.parse(serialized);\n\n      // Check if expired\n      if (Date.now() - entry.timestamp > entry.ttl) {\n        await AsyncStorage.removeItem(`cache_${key}`);\n        this.metrics.totalMisses++;\n        this.updateHitRate();\n        return null;\n      }\n\n      // Update access statistics\n      entry.accessCount++;\n      entry.lastAccessed = Date.now();\n      await AsyncStorage.setItem(`cache_${key}`, JSON.stringify(entry));\n\n      this.metrics.totalHits++;\n      this.updateHitRate();\n\n      performanceMonitor.trackCacheOperation('storage_get', key, entry.size);\n\n      return entry.data;\n    } catch (error) {\n      console.error('[CachingService] Storage get failed:', error);\n      this.metrics.totalMisses++;\n      this.updateHitRate();\n      return null;\n    }\n  }\n\n  /**\n   * Cache API response with automatic key generation\n   */\n  async cacheApiResponse<T>(\n    endpoint: string,\n    params: Record<string, any>,\n    data: T,\n    ttl?: number,\n  ): Promise<void> {\n    const key = this.generateApiCacheKey(endpoint, params);\n\n    // Store in both memory and storage for optimal performance\n    this.setMemory(key, data, ttl);\n    await this.setStorage(key, data, ttl);\n  }\n\n  /**\n   * Get cached API response\n   */\n  async getCachedApiResponse<T>(\n    endpoint: string,\n    params: Record<string, any>,\n  ): Promise<T | null> {\n    const key = this.generateApiCacheKey(endpoint, params);\n\n    // Try memory first (faster)\n    let data = this.getMemory<T>(key);\n    if (data) {\n      return data;\n    }\n\n    // Fallback to storage\n    data = await this.getStorage<T>(key);\n    if (data) {\n      // Promote to memory cache\n      this.setMemory(key, data);\n    }\n\n    return data;\n  }\n\n  /**\n   * Clear all caches\n   */\n  async clearAll(): Promise<void> {\n    this.memoryCache.clear();\n    this.updateMemoryUsage();\n\n    try {\n      const keys = await AsyncStorage.getAllKeys();\n      const cacheKeys = keys.filter(key => key.startsWith('cache_'));\n      await AsyncStorage.multiRemove(cacheKeys);\n    } catch (error) {\n      console.error('[CachingService] Clear all failed:', error);\n    }\n\n    this.resetMetrics();\n  }\n\n  /**\n   * Get cache metrics\n   */\n  getMetrics(): CacheMetrics {\n    return { ...this.metrics };\n  }\n\n  /**\n   * Private helper methods\n   */\n  private generateApiCacheKey(\n    endpoint: string,\n    params: Record<string, any>,\n  ): string {\n    const sortedParams = Object.keys(params)\n      .sort()\n      .reduce(\n        (result, key) => {\n          result[key] = params[key];\n          return result;\n        },\n        {} as Record<string, any>,\n      );\n\n    return `api_${endpoint}_${JSON.stringify(sortedParams)}`;\n  }\n\n  private calculateSize(data: any): number {\n    // Rough estimation of object size in bytes\n    return JSON.stringify(data).length * 2; // UTF-16 encoding\n  }\n\n  private getMemoryUsage(): number {\n    return Array.from(this.memoryCache.values()).reduce(\n      (total, entry) => total + entry.size,\n      0,\n    );\n  }\n\n  private updateMemoryUsage(): void {\n    this.metrics.memoryUsage = this.getMemoryUsage();\n  }\n\n  private updateHitRate(): void {\n    if (this.metrics.totalRequests > 0) {\n      this.metrics.hitRate =\n        this.metrics.totalHits / this.metrics.totalRequests;\n      this.metrics.missRate =\n        this.metrics.totalMisses / this.metrics.totalRequests;\n    }\n  }\n\n  private evictLeastRecentlyUsed(): void {\n    let oldestEntry: { key: string; lastAccessed: number } | null = null;\n\n    for (const [key, entry] of this.memoryCache.entries()) {\n      if (!oldestEntry || entry.lastAccessed < oldestEntry.lastAccessed) {\n        oldestEntry = { key, lastAccessed: entry.lastAccessed };\n      }\n    }\n\n    if (oldestEntry) {\n      this.memoryCache.delete(oldestEntry.key);\n      console.log(`[CachingService] Evicted LRU entry: ${oldestEntry.key}`);\n    }\n  }\n\n  private async cleanupExpiredEntries(): Promise<void> {\n    // Cleanup memory cache\n    const now = Date.now();\n    for (const [key, entry] of this.memoryCache.entries()) {\n      if (now - entry.timestamp > entry.ttl) {\n        this.memoryCache.delete(key);\n      }\n    }\n    this.updateMemoryUsage();\n\n    // Cleanup storage cache\n    try {\n      const keys = await AsyncStorage.getAllKeys();\n      const cacheKeys = keys.filter(key => key.startsWith('cache_'));\n\n      for (const key of cacheKeys) {\n        const serialized = await AsyncStorage.getItem(key);\n        if (serialized) {\n          const entry: CacheEntry = JSON.parse(serialized);\n          if (now - entry.timestamp > entry.ttl) {\n            await AsyncStorage.removeItem(key);\n          }\n        }\n      }\n    } catch (error) {\n      console.error('[CachingService] Cleanup failed:', error);\n    }\n  }\n\n  private async loadStorageMetrics(): Promise<void> {\n    try {\n      const keys = await AsyncStorage.getAllKeys();\n      const cacheKeys = keys.filter(key => key.startsWith('cache_'));\n\n      let totalSize = 0;\n      for (const key of cacheKeys) {\n        const serialized = await AsyncStorage.getItem(key);\n        if (serialized) {\n          totalSize += serialized.length * 2; // UTF-16 encoding\n        }\n      }\n\n      this.metrics.storageUsage = totalSize;\n    } catch (error) {\n      console.error('[CachingService] Load storage metrics failed:', error);\n    }\n  }\n\n  private startCleanupTimer(): void {\n    this.cleanupTimer = setInterval(() => {\n      this.cleanupExpiredEntries();\n    }, this.config.cleanupInterval);\n  }\n\n  private resetMetrics(): void {\n    this.metrics = {\n      memoryUsage: 0,\n      storageUsage: 0,\n      hitRate: 0,\n      missRate: 0,\n      totalRequests: 0,\n      totalHits: 0,\n      totalMisses: 0,\n    };\n  }\n\n  /**\n   * Cleanup on service destruction\n   */\n  destroy(): void {\n    if (this.cleanupTimer) {\n      clearInterval(this.cleanupTimer);\n      this.cleanupTimer = null;\n    }\n  }\n}\n\n// Export singleton instance\nexport const cachingService = new CachingService();\n\nexport default cachingService;\n"], "mappings": ";;;;;;;;;AAcA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,mBAAA,GAAAD,OAAA;AA+BA,IAAME,cAA2B,GAAG;EAClCC,aAAa,EAAE,EAAE;EACjBC,cAAc,EAAE,GAAG;EACnBC,UAAU,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;EAC1BC,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;EAC9BC,iBAAiB,EAAE,IAAI;EACvBC,gBAAgB,EAAE;AACpB,CAAC;AAAC,IAEIC,cAAc;EAMlB,SAAAA,eAAA,EAA+C;IAAA,IAAnCC,MAA4B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAAA,IAAAG,gBAAA,CAAAC,OAAA,QAAAN,cAAA;IAAA,KALrCO,WAAW,GAAG,IAAIC,GAAG,CAAqB,CAAC;IAAA,KAG3CC,YAAY,GAA0B,IAAI;IAGhD,IAAI,CAACR,MAAM,GAAAS,MAAA,CAAAC,MAAA,KAAQlB,cAAc,EAAKQ,MAAM,CAAE;IAC9C,IAAI,CAACW,OAAO,GAAG;MACbC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE,CAAC;MACfC,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAE,CAAC;MACXC,aAAa,EAAE,CAAC;MAChBC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;IACf,CAAC;IAED,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC1B;EAAC,WAAAC,aAAA,CAAAf,OAAA,EAAAN,cAAA;IAAAsB,GAAA;IAAAC,KAAA;MAAA,IAAAC,WAAA,OAAAC,kBAAA,CAAAnB,OAAA,EAKD,aAAkC;QAChC,IAAI;UACF,MAAM,IAAI,CAACoB,kBAAkB,CAAC,CAAC;UAC/B,MAAM,IAAI,CAACC,qBAAqB,CAAC,CAAC;UAElCC,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;QAC1D,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QACjE;MACF,CAAC;MAAA,SATKC,UAAUA,CAAA;QAAA,OAAAP,WAAA,CAAAQ,KAAA,OAAA9B,SAAA;MAAA;MAAA,OAAV6B,UAAU;IAAA;EAAA;IAAAT,GAAA;IAAAC,KAAA,EAchB,SAAAU,SAASA,CAAIX,GAAW,EAAEY,IAAO,EAAEC,GAAY,EAAQ;MACrD,IAAMC,KAAoB,GAAG;QAC3BF,IAAI,EAAJA,IAAI;QACJG,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBJ,GAAG,EAAEA,GAAG,IAAI,IAAI,CAAClC,MAAM,CAACL,UAAU;QAClC4C,IAAI,EAAE,IAAI,CAACC,aAAa,CAACP,IAAI,CAAC;QAC9BQ,WAAW,EAAE,CAAC;QACdC,YAAY,EAAEL,IAAI,CAACC,GAAG,CAAC;MACzB,CAAC;MAGD,IACE,IAAI,CAACK,cAAc,CAAC,CAAC,GAAGR,KAAK,CAACI,IAAI,GAClC,IAAI,CAACvC,MAAM,CAACP,aAAa,GAAG,IAAI,GAAG,IAAI,EACvC;QACA,IAAI,CAACmD,sBAAsB,CAAC,CAAC;MAC/B;MAEA,IAAI,CAACtC,WAAW,CAACuC,GAAG,CAACxB,GAAG,EAAEc,KAAK,CAAC;MAChC,IAAI,CAACW,iBAAiB,CAAC,CAAC;MAExBC,sCAAkB,CAACC,mBAAmB,CAAC,YAAY,EAAE3B,GAAG,EAAEc,KAAK,CAACI,IAAI,CAAC;IACvE;EAAC;IAAAlB,GAAA;IAAAC,KAAA,EAKD,SAAA2B,SAASA,CAAI5B,GAAW,EAAY;MAClC,IAAI,CAACV,OAAO,CAACK,aAAa,EAAE;MAE5B,IAAMmB,KAAK,GAAG,IAAI,CAAC7B,WAAW,CAAC4C,GAAG,CAAC7B,GAAG,CAA8B;MAEpE,IAAI,CAACc,KAAK,EAAE;QACV,IAAI,CAACxB,OAAO,CAACO,WAAW,EAAE;QAC1B,IAAI,CAACiC,aAAa,CAAC,CAAC;QACpB,OAAO,IAAI;MACb;MAGA,IAAId,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGH,KAAK,CAACC,SAAS,GAAGD,KAAK,CAACD,GAAG,EAAE;QAC5C,IAAI,CAAC5B,WAAW,CAAC8C,MAAM,CAAC/B,GAAG,CAAC;QAC5B,IAAI,CAACyB,iBAAiB,CAAC,CAAC;QACxB,IAAI,CAACnC,OAAO,CAACO,WAAW,EAAE;QAC1B,IAAI,CAACiC,aAAa,CAAC,CAAC;QACpB,OAAO,IAAI;MACb;MAGAhB,KAAK,CAACM,WAAW,EAAE;MACnBN,KAAK,CAACO,YAAY,GAAGL,IAAI,CAACC,GAAG,CAAC,CAAC;MAE/B,IAAI,CAAC3B,OAAO,CAACM,SAAS,EAAE;MACxB,IAAI,CAACkC,aAAa,CAAC,CAAC;MAEpBJ,sCAAkB,CAACC,mBAAmB,CAAC,YAAY,EAAE3B,GAAG,EAAEc,KAAK,CAACI,IAAI,CAAC;MAErE,OAAOJ,KAAK,CAACF,IAAI;IACnB;EAAC;IAAAZ,GAAA;IAAAC,KAAA;MAAA,IAAA+B,WAAA,OAAA7B,kBAAA,CAAAnB,OAAA,EAKD,WAAoBgB,GAAW,EAAEY,IAAO,EAAEC,GAAY,EAAiB;QACrE,IAAI;UACF,IAAMC,KAAoB,GAAG;YAC3BF,IAAI,EAAJA,IAAI;YACJG,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;YACrBJ,GAAG,EAAEA,GAAG,IAAI,IAAI,CAAClC,MAAM,CAACL,UAAU;YAClC4C,IAAI,EAAE,IAAI,CAACC,aAAa,CAACP,IAAI,CAAC;YAC9BQ,WAAW,EAAE,CAAC;YACdC,YAAY,EAAEL,IAAI,CAACC,GAAG,CAAC;UACzB,CAAC;UAED,IAAMgB,UAAU,GAAGC,IAAI,CAACC,SAAS,CAACrB,KAAK,CAAC;UAExC,IAAI,IAAI,CAACnC,MAAM,CAACH,iBAAiB,EAAE,CAGnC;UAEA,MAAM4D,qBAAY,CAACC,OAAO,CAAC,SAASrC,GAAG,EAAE,EAAEiC,UAAU,CAAC;UAEtDP,sCAAkB,CAACC,mBAAmB,CAAC,aAAa,EAAE3B,GAAG,EAAEc,KAAK,CAACI,IAAI,CAAC;QACxE,CAAC,CAAC,OAAOV,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC9D;MACF,CAAC;MAAA,SAxBK8B,UAAUA,CAAAC,EAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAT,WAAA,CAAAtB,KAAA,OAAA9B,SAAA;MAAA;MAAA,OAAV0D,UAAU;IAAA;EAAA;IAAAtC,GAAA;IAAAC,KAAA;MAAA,IAAAyC,WAAA,OAAAvC,kBAAA,CAAAnB,OAAA,EA6BhB,WAAoBgB,GAAW,EAAqB;QAClD,IAAI;UACF,IAAI,CAACV,OAAO,CAACK,aAAa,EAAE;UAE5B,IAAMsC,UAAU,SAASG,qBAAY,CAACO,OAAO,CAAC,SAAS3C,GAAG,EAAE,CAAC;UAE7D,IAAI,CAACiC,UAAU,EAAE;YACf,IAAI,CAAC3C,OAAO,CAACO,WAAW,EAAE;YAC1B,IAAI,CAACiC,aAAa,CAAC,CAAC;YACpB,OAAO,IAAI;UACb;UAEA,IAAMhB,KAAoB,GAAGoB,IAAI,CAACU,KAAK,CAACX,UAAU,CAAC;UAGnD,IAAIjB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGH,KAAK,CAACC,SAAS,GAAGD,KAAK,CAACD,GAAG,EAAE;YAC5C,MAAMuB,qBAAY,CAACS,UAAU,CAAC,SAAS7C,GAAG,EAAE,CAAC;YAC7C,IAAI,CAACV,OAAO,CAACO,WAAW,EAAE;YAC1B,IAAI,CAACiC,aAAa,CAAC,CAAC;YACpB,OAAO,IAAI;UACb;UAGAhB,KAAK,CAACM,WAAW,EAAE;UACnBN,KAAK,CAACO,YAAY,GAAGL,IAAI,CAACC,GAAG,CAAC,CAAC;UAC/B,MAAMmB,qBAAY,CAACC,OAAO,CAAC,SAASrC,GAAG,EAAE,EAAEkC,IAAI,CAACC,SAAS,CAACrB,KAAK,CAAC,CAAC;UAEjE,IAAI,CAACxB,OAAO,CAACM,SAAS,EAAE;UACxB,IAAI,CAACkC,aAAa,CAAC,CAAC;UAEpBJ,sCAAkB,CAACC,mBAAmB,CAAC,aAAa,EAAE3B,GAAG,EAAEc,KAAK,CAACI,IAAI,CAAC;UAEtE,OAAOJ,KAAK,CAACF,IAAI;QACnB,CAAC,CAAC,OAAOJ,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;UAC5D,IAAI,CAAClB,OAAO,CAACO,WAAW,EAAE;UAC1B,IAAI,CAACiC,aAAa,CAAC,CAAC;UACpB,OAAO,IAAI;QACb;MACF,CAAC;MAAA,SAvCKgB,UAAUA,CAAAC,GAAA;QAAA,OAAAL,WAAA,CAAAhC,KAAA,OAAA9B,SAAA;MAAA;MAAA,OAAVkE,UAAU;IAAA;EAAA;IAAA9C,GAAA;IAAAC,KAAA;MAAA,IAAA+C,iBAAA,OAAA7C,kBAAA,CAAAnB,OAAA,EA4ChB,WACEiE,QAAgB,EAChBC,MAA2B,EAC3BtC,IAAO,EACPC,GAAY,EACG;QACf,IAAMb,GAAG,GAAG,IAAI,CAACmD,mBAAmB,CAACF,QAAQ,EAAEC,MAAM,CAAC;QAGtD,IAAI,CAACvC,SAAS,CAACX,GAAG,EAAEY,IAAI,EAAEC,GAAG,CAAC;QAC9B,MAAM,IAAI,CAACyB,UAAU,CAACtC,GAAG,EAAEY,IAAI,EAAEC,GAAG,CAAC;MACvC,CAAC;MAAA,SAXKuC,gBAAgBA,CAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAR,iBAAA,CAAAtC,KAAA,OAAA9B,SAAA;MAAA;MAAA,OAAhBwE,gBAAgB;IAAA;EAAA;IAAApD,GAAA;IAAAC,KAAA;MAAA,IAAAwD,qBAAA,OAAAtD,kBAAA,CAAAnB,OAAA,EAgBtB,WACEiE,QAAgB,EAChBC,MAA2B,EACR;QACnB,IAAMlD,GAAG,GAAG,IAAI,CAACmD,mBAAmB,CAACF,QAAQ,EAAEC,MAAM,CAAC;QAGtD,IAAItC,IAAI,GAAG,IAAI,CAACgB,SAAS,CAAI5B,GAAG,CAAC;QACjC,IAAIY,IAAI,EAAE;UACR,OAAOA,IAAI;QACb;QAGAA,IAAI,SAAS,IAAI,CAACkC,UAAU,CAAI9C,GAAG,CAAC;QACpC,IAAIY,IAAI,EAAE;UAER,IAAI,CAACD,SAAS,CAACX,GAAG,EAAEY,IAAI,CAAC;QAC3B;QAEA,OAAOA,IAAI;MACb,CAAC;MAAA,SApBK8C,oBAAoBA,CAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAH,qBAAA,CAAA/C,KAAA,OAAA9B,SAAA;MAAA;MAAA,OAApB8E,oBAAoB;IAAA;EAAA;IAAA1D,GAAA;IAAAC,KAAA;MAAA,IAAA4D,SAAA,OAAA1D,kBAAA,CAAAnB,OAAA,EAyB1B,aAAgC;QAC9B,IAAI,CAACC,WAAW,CAAC6E,KAAK,CAAC,CAAC;QACxB,IAAI,CAACrC,iBAAiB,CAAC,CAAC;QAExB,IAAI;UACF,IAAMsC,IAAI,SAAS3B,qBAAY,CAAC4B,UAAU,CAAC,CAAC;UAC5C,IAAMC,SAAS,GAAGF,IAAI,CAACG,MAAM,CAAC,UAAAlE,GAAG;YAAA,OAAIA,GAAG,CAACmE,UAAU,CAAC,QAAQ,CAAC;UAAA,EAAC;UAC9D,MAAM/B,qBAAY,CAACgC,WAAW,CAACH,SAAS,CAAC;QAC3C,CAAC,CAAC,OAAOzD,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC5D;QAEA,IAAI,CAAC6D,YAAY,CAAC,CAAC;MACrB,CAAC;MAAA,SAbKC,QAAQA,CAAA;QAAA,OAAAT,SAAA,CAAAnD,KAAA,OAAA9B,SAAA;MAAA;MAAA,OAAR0F,QAAQ;IAAA;EAAA;IAAAtE,GAAA;IAAAC,KAAA,EAkBd,SAAAsE,UAAUA,CAAA,EAAiB;MACzB,OAAAnF,MAAA,CAAAC,MAAA,KAAY,IAAI,CAACC,OAAO;IAC1B;EAAC;IAAAU,GAAA;IAAAC,KAAA,EAKD,SAAQkD,mBAAmBA,CACzBF,QAAgB,EAChBC,MAA2B,EACnB;MACR,IAAMsB,YAAY,GAAGpF,MAAM,CAAC2E,IAAI,CAACb,MAAM,CAAC,CACrCuB,IAAI,CAAC,CAAC,CACNC,MAAM,CACL,UAACC,MAAM,EAAE3E,GAAG,EAAK;QACf2E,MAAM,CAAC3E,GAAG,CAAC,GAAGkD,MAAM,CAAClD,GAAG,CAAC;QACzB,OAAO2E,MAAM;MACf,CAAC,EACD,CAAC,CACH,CAAC;MAEH,OAAO,OAAO1B,QAAQ,IAAIf,IAAI,CAACC,SAAS,CAACqC,YAAY,CAAC,EAAE;IAC1D;EAAC;IAAAxE,GAAA;IAAAC,KAAA,EAED,SAAQkB,aAAaA,CAACP,IAAS,EAAU;MAEvC,OAAOsB,IAAI,CAACC,SAAS,CAACvB,IAAI,CAAC,CAAC/B,MAAM,GAAG,CAAC;IACxC;EAAC;IAAAmB,GAAA;IAAAC,KAAA,EAED,SAAQqB,cAAcA,CAAA,EAAW;MAC/B,OAAOsD,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC5F,WAAW,CAAC6F,MAAM,CAAC,CAAC,CAAC,CAACJ,MAAM,CACjD,UAACK,KAAK,EAAEjE,KAAK;QAAA,OAAKiE,KAAK,GAAGjE,KAAK,CAACI,IAAI;MAAA,GACpC,CACF,CAAC;IACH;EAAC;IAAAlB,GAAA;IAAAC,KAAA,EAED,SAAQwB,iBAAiBA,CAAA,EAAS;MAChC,IAAI,CAACnC,OAAO,CAACC,WAAW,GAAG,IAAI,CAAC+B,cAAc,CAAC,CAAC;IAClD;EAAC;IAAAtB,GAAA;IAAAC,KAAA,EAED,SAAQ6B,aAAaA,CAAA,EAAS;MAC5B,IAAI,IAAI,CAACxC,OAAO,CAACK,aAAa,GAAG,CAAC,EAAE;QAClC,IAAI,CAACL,OAAO,CAACG,OAAO,GAClB,IAAI,CAACH,OAAO,CAACM,SAAS,GAAG,IAAI,CAACN,OAAO,CAACK,aAAa;QACrD,IAAI,CAACL,OAAO,CAACI,QAAQ,GACnB,IAAI,CAACJ,OAAO,CAACO,WAAW,GAAG,IAAI,CAACP,OAAO,CAACK,aAAa;MACzD;IACF;EAAC;IAAAK,GAAA;IAAAC,KAAA,EAED,SAAQsB,sBAAsBA,CAAA,EAAS;MACrC,IAAIyD,WAAyD,GAAG,IAAI;MAEpE,SAAAC,IAAA,IAA2B,IAAI,CAAChG,WAAW,CAACiG,OAAO,CAAC,CAAC,EAAE;QAAA,IAAAC,KAAA,OAAAC,eAAA,CAAApG,OAAA,EAAAiG,IAAA;QAAA,IAA3CjF,GAAG,GAAAmF,KAAA;QAAA,IAAErE,KAAK,GAAAqE,KAAA;QACpB,IAAI,CAACH,WAAW,IAAIlE,KAAK,CAACO,YAAY,GAAG2D,WAAW,CAAC3D,YAAY,EAAE;UACjE2D,WAAW,GAAG;YAAEhF,GAAG,EAAHA,GAAG;YAAEqB,YAAY,EAAEP,KAAK,CAACO;UAAa,CAAC;QACzD;MACF;MAEA,IAAI2D,WAAW,EAAE;QACf,IAAI,CAAC/F,WAAW,CAAC8C,MAAM,CAACiD,WAAW,CAAChF,GAAG,CAAC;QACxCM,OAAO,CAACC,GAAG,CAAC,uCAAuCyE,WAAW,CAAChF,GAAG,EAAE,CAAC;MACvE;IACF;EAAC;IAAAA,GAAA;IAAAC,KAAA;MAAA,IAAAoF,sBAAA,OAAAlF,kBAAA,CAAAnB,OAAA,EAED,aAAqD;QAEnD,IAAMiC,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;QACtB,SAAAqE,KAAA,IAA2B,IAAI,CAACrG,WAAW,CAACiG,OAAO,CAAC,CAAC,EAAE;UAAA,IAAAK,KAAA,OAAAH,eAAA,CAAApG,OAAA,EAAAsG,KAAA;UAAA,IAA3CtF,GAAG,GAAAuF,KAAA;UAAA,IAAEzE,KAAK,GAAAyE,KAAA;UACpB,IAAItE,GAAG,GAAGH,KAAK,CAACC,SAAS,GAAGD,KAAK,CAACD,GAAG,EAAE;YACrC,IAAI,CAAC5B,WAAW,CAAC8C,MAAM,CAAC/B,GAAG,CAAC;UAC9B;QACF;QACA,IAAI,CAACyB,iBAAiB,CAAC,CAAC;QAGxB,IAAI;UACF,IAAMsC,IAAI,SAAS3B,qBAAY,CAAC4B,UAAU,CAAC,CAAC;UAC5C,IAAMC,SAAS,GAAGF,IAAI,CAACG,MAAM,CAAC,UAAAlE,GAAG;YAAA,OAAIA,GAAG,CAACmE,UAAU,CAAC,QAAQ,CAAC;UAAA,EAAC;UAE9D,KAAK,IAAMnE,IAAG,IAAIiE,SAAS,EAAE;YAC3B,IAAMhC,UAAU,SAASG,qBAAY,CAACO,OAAO,CAAC3C,IAAG,CAAC;YAClD,IAAIiC,UAAU,EAAE;cACd,IAAMnB,MAAiB,GAAGoB,IAAI,CAACU,KAAK,CAACX,UAAU,CAAC;cAChD,IAAIhB,GAAG,GAAGH,MAAK,CAACC,SAAS,GAAGD,MAAK,CAACD,GAAG,EAAE;gBACrC,MAAMuB,qBAAY,CAACS,UAAU,CAAC7C,IAAG,CAAC;cACpC;YACF;UACF;QACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QAC1D;MACF,CAAC;MAAA,SA3BaH,qBAAqBA,CAAA;QAAA,OAAAgF,sBAAA,CAAA3E,KAAA,OAAA9B,SAAA;MAAA;MAAA,OAArByB,qBAAqB;IAAA;EAAA;IAAAL,GAAA;IAAAC,KAAA;MAAA,IAAAuF,mBAAA,OAAArF,kBAAA,CAAAnB,OAAA,EA6BnC,aAAkD;QAChD,IAAI;UACF,IAAM+E,IAAI,SAAS3B,qBAAY,CAAC4B,UAAU,CAAC,CAAC;UAC5C,IAAMC,SAAS,GAAGF,IAAI,CAACG,MAAM,CAAC,UAAAlE,GAAG;YAAA,OAAIA,GAAG,CAACmE,UAAU,CAAC,QAAQ,CAAC;UAAA,EAAC;UAE9D,IAAIsB,SAAS,GAAG,CAAC;UACjB,KAAK,IAAMzF,GAAG,IAAIiE,SAAS,EAAE;YAC3B,IAAMhC,UAAU,SAASG,qBAAY,CAACO,OAAO,CAAC3C,GAAG,CAAC;YAClD,IAAIiC,UAAU,EAAE;cACdwD,SAAS,IAAIxD,UAAU,CAACpD,MAAM,GAAG,CAAC;YACpC;UACF;UAEA,IAAI,CAACS,OAAO,CAACE,YAAY,GAAGiG,SAAS;QACvC,CAAC,CAAC,OAAOjF,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACvE;MACF,CAAC;MAAA,SAjBaJ,kBAAkBA,CAAA;QAAA,OAAAoF,mBAAA,CAAA9E,KAAA,OAAA9B,SAAA;MAAA;MAAA,OAAlBwB,kBAAkB;IAAA;EAAA;IAAAJ,GAAA;IAAAC,KAAA,EAmBhC,SAAQH,iBAAiBA,CAAA,EAAS;MAAA,IAAA4F,KAAA;MAChC,IAAI,CAACvG,YAAY,GAAGwG,WAAW,CAAC,YAAM;QACpCD,KAAI,CAACrF,qBAAqB,CAAC,CAAC;MAC9B,CAAC,EAAE,IAAI,CAAC1B,MAAM,CAACJ,eAAe,CAAC;IACjC;EAAC;IAAAyB,GAAA;IAAAC,KAAA,EAED,SAAQoE,YAAYA,CAAA,EAAS;MAC3B,IAAI,CAAC/E,OAAO,GAAG;QACbC,WAAW,EAAE,CAAC;QACdC,YAAY,EAAE,CAAC;QACfC,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE,CAAC;QACXC,aAAa,EAAE,CAAC;QAChBC,SAAS,EAAE,CAAC;QACZC,WAAW,EAAE;MACf,CAAC;IACH;EAAC;IAAAG,GAAA;IAAAC,KAAA,EAKD,SAAA2F,OAAOA,CAAA,EAAS;MACd,IAAI,IAAI,CAACzG,YAAY,EAAE;QACrB0G,aAAa,CAAC,IAAI,CAAC1G,YAAY,CAAC;QAChC,IAAI,CAACA,YAAY,GAAG,IAAI;MAC1B;IACF;EAAC;AAAA;AAII,IAAM2G,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAG,IAAIpH,cAAc,CAAC,CAAC;AAAC,IAAAsH,QAAA,GAAAD,OAAA,CAAA/G,OAAA,GAEpC8G,cAAc", "ignoreList": []}