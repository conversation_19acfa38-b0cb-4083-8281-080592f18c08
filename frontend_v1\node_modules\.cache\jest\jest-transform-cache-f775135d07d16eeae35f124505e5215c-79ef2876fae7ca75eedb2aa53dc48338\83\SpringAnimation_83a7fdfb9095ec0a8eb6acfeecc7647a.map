{"version": 3, "names": ["_AnimatedColor", "_interopRequireDefault", "require", "SpringConfig", "_interopRequireWildcard", "_Animation2", "_invariant", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_callSuper", "_getPrototypeOf2", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "_superPropGet", "p", "_get2", "SpringAnimation", "exports", "_Animation", "config", "_config$overshootClam", "_config$restDisplacem", "_config$restSpeedThre", "_config$velocity", "_config$velocity2", "_config$delay", "_this", "_classCallCheck2", "_overshootClamping", "overshootClamping", "_restDisplacementThreshold", "restDisplacementThreshold", "_restSpeedThreshold", "restSpeedThreshold", "_initialVelocity", "velocity", "_lastVelocity", "_toValue", "toValue", "_delay", "delay", "_platformConfig", "platformConfig", "stiffness", "undefined", "damping", "mass", "_config$stiffness", "_config$damping", "_config$mass", "invariant", "bounciness", "speed", "tension", "friction", "_stiffness", "_damping", "_mass", "_config$bounciness", "_config$speed", "springConfig", "fromBouncinessAndSpeed", "_config$tension", "_config$friction", "fromOrigamiTensionAndFriction", "_inherits2", "_createClass2", "key", "value", "__getNativeAnimationConfig", "_this$_initialVelocit", "type", "initialVelocity", "iterations", "__iterations", "debugID", "__getDebugID", "start", "fromValue", "onUpdate", "onEnd", "previousAnimation", "animatedValue", "_this2", "_startPosition", "_lastPosition", "_onUpdate", "_lastTime", "Date", "now", "_frameTime", "internalState", "getInternalState", "lastPosition", "lastVelocity", "lastTime", "useNativeDriver", "__startAnimationIfNative", "_timeout", "setTimeout", "MAX_STEPS", "deltaTime", "c", "m", "k", "v0", "zeta", "Math", "sqrt", "omega0", "omega1", "x0", "position", "envelope", "exp", "sin", "cos", "__active", "isOvershooting", "isVelocity", "abs", "isDisplacement", "__notifyAnimationEnd", "finished", "_animationFrame", "requestAnimationFrame", "bind", "stop", "clearTimeout", "global", "cancelAnimationFrame", "Animation"], "sources": ["SpringAnimation.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {PlatformConfig} from '../AnimatedPlatformConfig';\nimport type AnimatedInterpolation from '../nodes/AnimatedInterpolation';\nimport type AnimatedValue from '../nodes/AnimatedValue';\nimport type AnimatedValueXY from '../nodes/AnimatedValueXY';\nimport type {AnimationConfig, EndCallback} from './Animation';\n\nimport AnimatedColor from '../nodes/AnimatedColor';\nimport * as SpringConfig from '../SpringConfig';\nimport Animation from './Animation';\nimport invariant from 'invariant';\n\nexport type SpringAnimationConfig = $ReadOnly<{\n  ...AnimationConfig,\n  toValue:\n    | number\n    | AnimatedValue\n    | {\n        x: number,\n        y: number,\n        ...\n      }\n    | AnimatedValueXY\n    | {\n        r: number,\n        g: number,\n        b: number,\n        a: number,\n        ...\n      }\n    | AnimatedColor\n    | AnimatedInterpolation<number>,\n  overshootClamping?: boolean,\n  restDisplacementThreshold?: number,\n  restSpeedThreshold?: number,\n  velocity?:\n    | number\n    | $ReadOnly<{\n        x: number,\n        y: number,\n        ...\n      }>,\n  bounciness?: number,\n  speed?: number,\n  tension?: number,\n  friction?: number,\n  stiffness?: number,\n  damping?: number,\n  mass?: number,\n  delay?: number,\n  ...\n}>;\n\nexport type SpringAnimationConfigSingle = $ReadOnly<{\n  ...AnimationConfig,\n  toValue: number,\n  overshootClamping?: boolean,\n  restDisplacementThreshold?: number,\n  restSpeedThreshold?: number,\n  velocity?: number,\n  bounciness?: number,\n  speed?: number,\n  tension?: number,\n  friction?: number,\n  stiffness?: number,\n  damping?: number,\n  mass?: number,\n  delay?: number,\n  ...\n}>;\n\nopaque type SpringAnimationInternalState = $ReadOnly<{\n  lastPosition: number,\n  lastVelocity: number,\n  lastTime: number,\n}>;\n\nexport default class SpringAnimation extends Animation {\n  _overshootClamping: boolean;\n  _restDisplacementThreshold: number;\n  _restSpeedThreshold: number;\n  _lastVelocity: number;\n  _startPosition: number;\n  _lastPosition: number;\n  _fromValue: number;\n  _toValue: number;\n  _stiffness: number;\n  _damping: number;\n  _mass: number;\n  _initialVelocity: number;\n  _delay: number;\n  _timeout: ?TimeoutID;\n  _startTime: number;\n  _lastTime: number;\n  _frameTime: number;\n  _onUpdate: (value: number) => void;\n  _animationFrame: ?AnimationFrameID;\n  _platformConfig: ?PlatformConfig;\n\n  constructor(config: SpringAnimationConfigSingle) {\n    super(config);\n\n    this._overshootClamping = config.overshootClamping ?? false;\n    this._restDisplacementThreshold = config.restDisplacementThreshold ?? 0.001;\n    this._restSpeedThreshold = config.restSpeedThreshold ?? 0.001;\n    this._initialVelocity = config.velocity ?? 0;\n    this._lastVelocity = config.velocity ?? 0;\n    this._toValue = config.toValue;\n    this._delay = config.delay ?? 0;\n    this._platformConfig = config.platformConfig;\n\n    if (\n      config.stiffness !== undefined ||\n      config.damping !== undefined ||\n      config.mass !== undefined\n    ) {\n      invariant(\n        config.bounciness === undefined &&\n          config.speed === undefined &&\n          config.tension === undefined &&\n          config.friction === undefined,\n        'You can define one of bounciness/speed, tension/friction, or stiffness/damping/mass, but not more than one',\n      );\n      this._stiffness = config.stiffness ?? 100;\n      this._damping = config.damping ?? 10;\n      this._mass = config.mass ?? 1;\n    } else if (config.bounciness !== undefined || config.speed !== undefined) {\n      // Convert the origami bounciness/speed values to stiffness/damping\n      // We assume mass is 1.\n      invariant(\n        config.tension === undefined &&\n          config.friction === undefined &&\n          config.stiffness === undefined &&\n          config.damping === undefined &&\n          config.mass === undefined,\n        'You can define one of bounciness/speed, tension/friction, or stiffness/damping/mass, but not more than one',\n      );\n      const springConfig = SpringConfig.fromBouncinessAndSpeed(\n        config.bounciness ?? 8,\n        config.speed ?? 12,\n      );\n      this._stiffness = springConfig.stiffness;\n      this._damping = springConfig.damping;\n      this._mass = 1;\n    } else {\n      // Convert the origami tension/friction values to stiffness/damping\n      // We assume mass is 1.\n      const springConfig = SpringConfig.fromOrigamiTensionAndFriction(\n        config.tension ?? 40,\n        config.friction ?? 7,\n      );\n      this._stiffness = springConfig.stiffness;\n      this._damping = springConfig.damping;\n      this._mass = 1;\n    }\n\n    invariant(this._stiffness > 0, 'Stiffness value must be greater than 0');\n    invariant(this._damping > 0, 'Damping value must be greater than 0');\n    invariant(this._mass > 0, 'Mass value must be greater than 0');\n  }\n\n  __getNativeAnimationConfig(): $ReadOnly<{\n    damping: number,\n    initialVelocity: number,\n    iterations: number,\n    mass: number,\n    platformConfig: ?PlatformConfig,\n    overshootClamping: boolean,\n    restDisplacementThreshold: number,\n    restSpeedThreshold: number,\n    stiffness: number,\n    toValue: number,\n    type: 'spring',\n    ...\n  }> {\n    return {\n      type: 'spring',\n      overshootClamping: this._overshootClamping,\n      restDisplacementThreshold: this._restDisplacementThreshold,\n      restSpeedThreshold: this._restSpeedThreshold,\n      stiffness: this._stiffness,\n      damping: this._damping,\n      mass: this._mass,\n      initialVelocity: this._initialVelocity ?? this._lastVelocity,\n      toValue: this._toValue,\n      iterations: this.__iterations,\n      platformConfig: this._platformConfig,\n      debugID: this.__getDebugID(),\n    };\n  }\n\n  start(\n    fromValue: number,\n    onUpdate: (value: number) => void,\n    onEnd: ?EndCallback,\n    previousAnimation: ?Animation,\n    animatedValue: AnimatedValue,\n  ): void {\n    super.start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue);\n\n    this._startPosition = fromValue;\n    this._lastPosition = this._startPosition;\n\n    this._onUpdate = onUpdate;\n    this._lastTime = Date.now();\n    this._frameTime = 0.0;\n\n    if (previousAnimation instanceof SpringAnimation) {\n      const internalState = previousAnimation.getInternalState();\n      this._lastPosition = internalState.lastPosition;\n      this._lastVelocity = internalState.lastVelocity;\n      // Set the initial velocity to the last velocity\n      this._initialVelocity = this._lastVelocity;\n      this._lastTime = internalState.lastTime;\n    }\n\n    const start = () => {\n      const useNativeDriver = this.__startAnimationIfNative(animatedValue);\n      if (!useNativeDriver) {\n        this.onUpdate();\n      }\n    };\n\n    //  If this._delay is more than 0, we start after the timeout.\n    if (this._delay) {\n      this._timeout = setTimeout(start, this._delay);\n    } else {\n      start();\n    }\n  }\n\n  getInternalState(): SpringAnimationInternalState {\n    return {\n      lastPosition: this._lastPosition,\n      lastVelocity: this._lastVelocity,\n      lastTime: this._lastTime,\n    };\n  }\n\n  /**\n   * This spring model is based off of a damped harmonic oscillator\n   * (https://en.wikipedia.org/wiki/Harmonic_oscillator#Damped_harmonic_oscillator).\n   *\n   * We use the closed form of the second order differential equation:\n   *\n   * x'' + (2ζ⍵_0)x' + ⍵^2x = 0\n   *\n   * where\n   *    ⍵_0 = √(k / m) (undamped angular frequency of the oscillator),\n   *    ζ = c / 2√mk (damping ratio),\n   *    c = damping constant\n   *    k = stiffness\n   *    m = mass\n   *\n   * The derivation of the closed form is described in detail here:\n   * http://planetmath.org/sites/default/files/texpdf/39745.pdf\n   *\n   * This algorithm happens to match the algorithm used by CASpringAnimation,\n   * a QuartzCore (iOS) API that creates spring animations.\n   */\n  onUpdate(): void {\n    // If for some reason we lost a lot of frames (e.g. process large payload or\n    // stopped in the debugger), we only advance by 4 frames worth of\n    // computation and will continue on the next frame. It's better to have it\n    // running at faster speed than jumping to the end.\n    const MAX_STEPS = 64;\n    let now = Date.now();\n    if (now > this._lastTime + MAX_STEPS) {\n      now = this._lastTime + MAX_STEPS;\n    }\n\n    const deltaTime = (now - this._lastTime) / 1000;\n    this._frameTime += deltaTime;\n\n    const c: number = this._damping;\n    const m: number = this._mass;\n    const k: number = this._stiffness;\n    const v0: number = -this._initialVelocity;\n\n    const zeta = c / (2 * Math.sqrt(k * m)); // damping ratio\n    const omega0 = Math.sqrt(k / m); // undamped angular frequency of the oscillator (rad/ms)\n    const omega1 = omega0 * Math.sqrt(1.0 - zeta * zeta); // exponential decay\n    const x0 = this._toValue - this._startPosition; // calculate the oscillation from x0 = 1 to x = 0\n\n    let position = 0.0;\n    let velocity = 0.0;\n    const t = this._frameTime;\n    if (zeta < 1) {\n      // Under damped\n      const envelope = Math.exp(-zeta * omega0 * t);\n      position =\n        this._toValue -\n        envelope *\n          (((v0 + zeta * omega0 * x0) / omega1) * Math.sin(omega1 * t) +\n            x0 * Math.cos(omega1 * t));\n      // This looks crazy -- it's actually just the derivative of the\n      // oscillation function\n      velocity =\n        zeta *\n          omega0 *\n          envelope *\n          ((Math.sin(omega1 * t) * (v0 + zeta * omega0 * x0)) / omega1 +\n            x0 * Math.cos(omega1 * t)) -\n        envelope *\n          (Math.cos(omega1 * t) * (v0 + zeta * omega0 * x0) -\n            omega1 * x0 * Math.sin(omega1 * t));\n    } else {\n      // Critically damped\n      const envelope = Math.exp(-omega0 * t);\n      position = this._toValue - envelope * (x0 + (v0 + omega0 * x0) * t);\n      velocity =\n        envelope * (v0 * (t * omega0 - 1) + t * x0 * (omega0 * omega0));\n    }\n\n    this._lastTime = now;\n    this._lastPosition = position;\n    this._lastVelocity = velocity;\n\n    this._onUpdate(position);\n    if (!this.__active) {\n      // a listener might have stopped us in _onUpdate\n      return;\n    }\n\n    // Conditions for stopping the spring animation\n    let isOvershooting = false;\n    if (this._overshootClamping && this._stiffness !== 0) {\n      if (this._startPosition < this._toValue) {\n        isOvershooting = position > this._toValue;\n      } else {\n        isOvershooting = position < this._toValue;\n      }\n    }\n    const isVelocity = Math.abs(velocity) <= this._restSpeedThreshold;\n    let isDisplacement = true;\n    if (this._stiffness !== 0) {\n      isDisplacement =\n        Math.abs(this._toValue - position) <= this._restDisplacementThreshold;\n    }\n\n    if (isOvershooting || (isVelocity && isDisplacement)) {\n      if (this._stiffness !== 0) {\n        // Ensure that we end up with a round value\n        this._lastPosition = this._toValue;\n        this._lastVelocity = 0;\n        this._onUpdate(this._toValue);\n      }\n\n      this.__notifyAnimationEnd({finished: true});\n      return;\n    }\n    // $FlowFixMe[method-unbinding] added when improving typing for this parameters\n    this._animationFrame = requestAnimationFrame(this.onUpdate.bind(this));\n  }\n\n  stop(): void {\n    super.stop();\n    clearTimeout(this._timeout);\n    if (this._animationFrame != null) {\n      global.cancelAnimationFrame(this._animationFrame);\n    }\n    this.__notifyAnimationEnd({finished: false});\n  }\n}\n"], "mappings": ";;;;;;;;;;;;AAgBA,IAAAA,cAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,WAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,UAAA,GAAAL,sBAAA,CAAAC,OAAA;AAAkC,SAAAE,wBAAAG,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAL,uBAAA,YAAAA,wBAAAG,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,SAAAmB,WAAAnB,CAAA,EAAAK,CAAA,EAAAN,CAAA,WAAAM,CAAA,OAAAe,gBAAA,CAAAX,OAAA,EAAAJ,CAAA,OAAAgB,2BAAA,CAAAZ,OAAA,EAAAT,CAAA,EAAAsB,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAnB,CAAA,EAAAN,CAAA,YAAAqB,gBAAA,CAAAX,OAAA,EAAAT,CAAA,EAAAyB,WAAA,IAAApB,CAAA,CAAAqB,KAAA,CAAA1B,CAAA,EAAAD,CAAA;AAAA,SAAAuB,0BAAA,cAAAtB,CAAA,IAAA2B,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAd,IAAA,CAAAQ,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAA3B,CAAA,aAAAsB,yBAAA,YAAAA,0BAAA,aAAAtB,CAAA;AAAA,SAAA8B,cAAA9B,CAAA,EAAAK,CAAA,EAAAN,CAAA,EAAAG,CAAA,QAAA6B,CAAA,OAAAC,KAAA,CAAAvB,OAAA,MAAAW,gBAAA,CAAAX,OAAA,MAAAP,CAAA,GAAAF,CAAA,CAAA4B,SAAA,GAAA5B,CAAA,GAAAK,CAAA,EAAAN,CAAA,cAAAG,CAAA,yBAAA6B,CAAA,aAAA/B,CAAA,WAAA+B,CAAA,CAAAL,KAAA,CAAA3B,CAAA,EAAAC,CAAA,OAAA+B,CAAA;AAAA,IAmEbE,eAAe,GAAAC,OAAA,CAAAzB,OAAA,aAAA0B,UAAA;EAsBlC,SAAAF,gBAAYG,MAAmC,EAAE;IAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,iBAAA,EAAAC,aAAA;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAnC,OAAA,QAAAwB,eAAA;IAC/CU,KAAA,GAAAxB,UAAA,OAAAc,eAAA,GAAMG,MAAM;IAEZO,KAAA,CAAKE,kBAAkB,IAAAR,qBAAA,GAAGD,MAAM,CAACU,iBAAiB,YAAAT,qBAAA,GAAI,KAAK;IAC3DM,KAAA,CAAKI,0BAA0B,IAAAT,qBAAA,GAAGF,MAAM,CAACY,yBAAyB,YAAAV,qBAAA,GAAI,KAAK;IAC3EK,KAAA,CAAKM,mBAAmB,IAAAV,qBAAA,GAAGH,MAAM,CAACc,kBAAkB,YAAAX,qBAAA,GAAI,KAAK;IAC7DI,KAAA,CAAKQ,gBAAgB,IAAAX,gBAAA,GAAGJ,MAAM,CAACgB,QAAQ,YAAAZ,gBAAA,GAAI,CAAC;IAC5CG,KAAA,CAAKU,aAAa,IAAAZ,iBAAA,GAAGL,MAAM,CAACgB,QAAQ,YAAAX,iBAAA,GAAI,CAAC;IACzCE,KAAA,CAAKW,QAAQ,GAAGlB,MAAM,CAACmB,OAAO;IAC9BZ,KAAA,CAAKa,MAAM,IAAAd,aAAA,GAAGN,MAAM,CAACqB,KAAK,YAAAf,aAAA,GAAI,CAAC;IAC/BC,KAAA,CAAKe,eAAe,GAAGtB,MAAM,CAACuB,cAAc;IAE5C,IACEvB,MAAM,CAACwB,SAAS,KAAKC,SAAS,IAC9BzB,MAAM,CAAC0B,OAAO,KAAKD,SAAS,IAC5BzB,MAAM,CAAC2B,IAAI,KAAKF,SAAS,EACzB;MAAA,IAAAG,iBAAA,EAAAC,eAAA,EAAAC,YAAA;MACA,IAAAC,kBAAS,EACP/B,MAAM,CAACgC,UAAU,KAAKP,SAAS,IAC7BzB,MAAM,CAACiC,KAAK,KAAKR,SAAS,IAC1BzB,MAAM,CAACkC,OAAO,KAAKT,SAAS,IAC5BzB,MAAM,CAACmC,QAAQ,KAAKV,SAAS,EAC/B,4GACF,CAAC;MACDlB,KAAA,CAAK6B,UAAU,IAAAR,iBAAA,GAAG5B,MAAM,CAACwB,SAAS,YAAAI,iBAAA,GAAI,GAAG;MACzCrB,KAAA,CAAK8B,QAAQ,IAAAR,eAAA,GAAG7B,MAAM,CAAC0B,OAAO,YAAAG,eAAA,GAAI,EAAE;MACpCtB,KAAA,CAAK+B,KAAK,IAAAR,YAAA,GAAG9B,MAAM,CAAC2B,IAAI,YAAAG,YAAA,GAAI,CAAC;IAC/B,CAAC,MAAM,IAAI9B,MAAM,CAACgC,UAAU,KAAKP,SAAS,IAAIzB,MAAM,CAACiC,KAAK,KAAKR,SAAS,EAAE;MAAA,IAAAc,kBAAA,EAAAC,aAAA;MAGxE,IAAAT,kBAAS,EACP/B,MAAM,CAACkC,OAAO,KAAKT,SAAS,IAC1BzB,MAAM,CAACmC,QAAQ,KAAKV,SAAS,IAC7BzB,MAAM,CAACwB,SAAS,KAAKC,SAAS,IAC9BzB,MAAM,CAAC0B,OAAO,KAAKD,SAAS,IAC5BzB,MAAM,CAAC2B,IAAI,KAAKF,SAAS,EAC3B,4GACF,CAAC;MACD,IAAMgB,YAAY,GAAGlF,YAAY,CAACmF,sBAAsB,EAAAH,kBAAA,GACtDvC,MAAM,CAACgC,UAAU,YAAAO,kBAAA,GAAI,CAAC,GAAAC,aAAA,GACtBxC,MAAM,CAACiC,KAAK,YAAAO,aAAA,GAAI,EAClB,CAAC;MACDjC,KAAA,CAAK6B,UAAU,GAAGK,YAAY,CAACjB,SAAS;MACxCjB,KAAA,CAAK8B,QAAQ,GAAGI,YAAY,CAACf,OAAO;MACpCnB,KAAA,CAAK+B,KAAK,GAAG,CAAC;IAChB,CAAC,MAAM;MAAA,IAAAK,eAAA,EAAAC,gBAAA;MAGL,IAAMH,aAAY,GAAGlF,YAAY,CAACsF,6BAA6B,EAAAF,eAAA,GAC7D3C,MAAM,CAACkC,OAAO,YAAAS,eAAA,GAAI,EAAE,GAAAC,gBAAA,GACpB5C,MAAM,CAACmC,QAAQ,YAAAS,gBAAA,GAAI,CACrB,CAAC;MACDrC,KAAA,CAAK6B,UAAU,GAAGK,aAAY,CAACjB,SAAS;MACxCjB,KAAA,CAAK8B,QAAQ,GAAGI,aAAY,CAACf,OAAO;MACpCnB,KAAA,CAAK+B,KAAK,GAAG,CAAC;IAChB;IAEA,IAAAP,kBAAS,EAACxB,KAAA,CAAK6B,UAAU,GAAG,CAAC,EAAE,wCAAwC,CAAC;IACxE,IAAAL,kBAAS,EAACxB,KAAA,CAAK8B,QAAQ,GAAG,CAAC,EAAE,sCAAsC,CAAC;IACpE,IAAAN,kBAAS,EAACxB,KAAA,CAAK+B,KAAK,GAAG,CAAC,EAAE,mCAAmC,CAAC;IAAC,OAAA/B,KAAA;EACjE;EAAC,IAAAuC,UAAA,CAAAzE,OAAA,EAAAwB,eAAA,EAAAE,UAAA;EAAA,WAAAgD,aAAA,CAAA1E,OAAA,EAAAwB,eAAA;IAAAmD,GAAA;IAAAC,KAAA,EAED,SAAAC,0BAA0BA,CAAA,EAavB;MAAA,IAAAC,qBAAA;MACD,OAAO;QACLC,IAAI,EAAE,QAAQ;QACd1C,iBAAiB,EAAE,IAAI,CAACD,kBAAkB;QAC1CG,yBAAyB,EAAE,IAAI,CAACD,0BAA0B;QAC1DG,kBAAkB,EAAE,IAAI,CAACD,mBAAmB;QAC5CW,SAAS,EAAE,IAAI,CAACY,UAAU;QAC1BV,OAAO,EAAE,IAAI,CAACW,QAAQ;QACtBV,IAAI,EAAE,IAAI,CAACW,KAAK;QAChBe,eAAe,GAAAF,qBAAA,GAAE,IAAI,CAACpC,gBAAgB,YAAAoC,qBAAA,GAAI,IAAI,CAAClC,aAAa;QAC5DE,OAAO,EAAE,IAAI,CAACD,QAAQ;QACtBoC,UAAU,EAAE,IAAI,CAACC,YAAY;QAC7BhC,cAAc,EAAE,IAAI,CAACD,eAAe;QACpCkC,OAAO,EAAE,IAAI,CAACC,YAAY,CAAC;MAC7B,CAAC;IACH;EAAC;IAAAT,GAAA;IAAAC,KAAA,EAED,SAAAS,KAAKA,CACHC,SAAiB,EACjBC,QAAiC,EACjCC,KAAmB,EACnBC,iBAA6B,EAC7BC,aAA4B,EACtB;MAAA,IAAAC,MAAA;MACNtE,aAAA,CAAAG,eAAA,qBAAY8D,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,aAAa;MAExE,IAAI,CAACE,cAAc,GAAGN,SAAS;MAC/B,IAAI,CAACO,aAAa,GAAG,IAAI,CAACD,cAAc;MAExC,IAAI,CAACE,SAAS,GAAGP,QAAQ;MACzB,IAAI,CAACQ,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MAC3B,IAAI,CAACC,UAAU,GAAG,GAAG;MAErB,IAAIT,iBAAiB,YAAYjE,eAAe,EAAE;QAChD,IAAM2E,aAAa,GAAGV,iBAAiB,CAACW,gBAAgB,CAAC,CAAC;QAC1D,IAAI,CAACP,aAAa,GAAGM,aAAa,CAACE,YAAY;QAC/C,IAAI,CAACzD,aAAa,GAAGuD,aAAa,CAACG,YAAY;QAE/C,IAAI,CAAC5D,gBAAgB,GAAG,IAAI,CAACE,aAAa;QAC1C,IAAI,CAACmD,SAAS,GAAGI,aAAa,CAACI,QAAQ;MACzC;MAEA,IAAMlB,KAAK,GAAG,SAARA,KAAKA,CAAA,EAAS;QAClB,IAAMmB,eAAe,GAAGb,MAAI,CAACc,wBAAwB,CAACf,aAAa,CAAC;QACpE,IAAI,CAACc,eAAe,EAAE;UACpBb,MAAI,CAACJ,QAAQ,CAAC,CAAC;QACjB;MACF,CAAC;MAGD,IAAI,IAAI,CAACxC,MAAM,EAAE;QACf,IAAI,CAAC2D,QAAQ,GAAGC,UAAU,CAACtB,KAAK,EAAE,IAAI,CAACtC,MAAM,CAAC;MAChD,CAAC,MAAM;QACLsC,KAAK,CAAC,CAAC;MACT;IACF;EAAC;IAAAV,GAAA;IAAAC,KAAA,EAED,SAAAwB,gBAAgBA,CAAA,EAAiC;MAC/C,OAAO;QACLC,YAAY,EAAE,IAAI,CAACR,aAAa;QAChCS,YAAY,EAAE,IAAI,CAAC1D,aAAa;QAChC2D,QAAQ,EAAE,IAAI,CAACR;MACjB,CAAC;IACH;EAAC;IAAApB,GAAA;IAAAC,KAAA,EAuBD,SAAAW,QAAQA,CAAA,EAAS;MAKf,IAAMqB,SAAS,GAAG,EAAE;MACpB,IAAIX,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;MACpB,IAAIA,GAAG,GAAG,IAAI,CAACF,SAAS,GAAGa,SAAS,EAAE;QACpCX,GAAG,GAAG,IAAI,CAACF,SAAS,GAAGa,SAAS;MAClC;MAEA,IAAMC,SAAS,GAAG,CAACZ,GAAG,GAAG,IAAI,CAACF,SAAS,IAAI,IAAI;MAC/C,IAAI,CAACG,UAAU,IAAIW,SAAS;MAE5B,IAAMC,CAAS,GAAG,IAAI,CAAC9C,QAAQ;MAC/B,IAAM+C,CAAS,GAAG,IAAI,CAAC9C,KAAK;MAC5B,IAAM+C,CAAS,GAAG,IAAI,CAACjD,UAAU;MACjC,IAAMkD,EAAU,GAAG,CAAC,IAAI,CAACvE,gBAAgB;MAEzC,IAAMwE,IAAI,GAAGJ,CAAC,IAAI,CAAC,GAAGK,IAAI,CAACC,IAAI,CAACJ,CAAC,GAAGD,CAAC,CAAC,CAAC;MACvC,IAAMM,MAAM,GAAGF,IAAI,CAACC,IAAI,CAACJ,CAAC,GAAGD,CAAC,CAAC;MAC/B,IAAMO,MAAM,GAAGD,MAAM,GAAGF,IAAI,CAACC,IAAI,CAAC,GAAG,GAAGF,IAAI,GAAGA,IAAI,CAAC;MACpD,IAAMK,EAAE,GAAG,IAAI,CAAC1E,QAAQ,GAAG,IAAI,CAAC+C,cAAc;MAE9C,IAAI4B,QAAQ,GAAG,GAAG;MAClB,IAAI7E,QAAQ,GAAG,GAAG;MAClB,IAAMpD,CAAC,GAAG,IAAI,CAAC2G,UAAU;MACzB,IAAIgB,IAAI,GAAG,CAAC,EAAE;QAEZ,IAAMO,QAAQ,GAAGN,IAAI,CAACO,GAAG,CAAC,CAACR,IAAI,GAAGG,MAAM,GAAG9H,CAAC,CAAC;QAC7CiI,QAAQ,GACN,IAAI,CAAC3E,QAAQ,GACb4E,QAAQ,IACJ,CAACR,EAAE,GAAGC,IAAI,GAAGG,MAAM,GAAGE,EAAE,IAAID,MAAM,GAAIH,IAAI,CAACQ,GAAG,CAACL,MAAM,GAAG/H,CAAC,CAAC,GAC1DgI,EAAE,GAAGJ,IAAI,CAACS,GAAG,CAACN,MAAM,GAAG/H,CAAC,CAAC,CAAC;QAGhCoD,QAAQ,GACNuE,IAAI,GACFG,MAAM,GACNI,QAAQ,IACNN,IAAI,CAACQ,GAAG,CAACL,MAAM,GAAG/H,CAAC,CAAC,IAAI0H,EAAE,GAAGC,IAAI,GAAGG,MAAM,GAAGE,EAAE,CAAC,GAAID,MAAM,GAC1DC,EAAE,GAAGJ,IAAI,CAACS,GAAG,CAACN,MAAM,GAAG/H,CAAC,CAAC,CAAC,GAC9BkI,QAAQ,IACLN,IAAI,CAACS,GAAG,CAACN,MAAM,GAAG/H,CAAC,CAAC,IAAI0H,EAAE,GAAGC,IAAI,GAAGG,MAAM,GAAGE,EAAE,CAAC,GAC/CD,MAAM,GAAGC,EAAE,GAAGJ,IAAI,CAACQ,GAAG,CAACL,MAAM,GAAG/H,CAAC,CAAC,CAAC;MAC3C,CAAC,MAAM;QAEL,IAAMkI,SAAQ,GAAGN,IAAI,CAACO,GAAG,CAAC,CAACL,MAAM,GAAG9H,CAAC,CAAC;QACtCiI,QAAQ,GAAG,IAAI,CAAC3E,QAAQ,GAAG4E,SAAQ,IAAIF,EAAE,GAAG,CAACN,EAAE,GAAGI,MAAM,GAAGE,EAAE,IAAIhI,CAAC,CAAC;QACnEoD,QAAQ,GACN8E,SAAQ,IAAIR,EAAE,IAAI1H,CAAC,GAAG8H,MAAM,GAAG,CAAC,CAAC,GAAG9H,CAAC,GAAGgI,EAAE,IAAIF,MAAM,GAAGA,MAAM,CAAC,CAAC;MACnE;MAEA,IAAI,CAACtB,SAAS,GAAGE,GAAG;MACpB,IAAI,CAACJ,aAAa,GAAG2B,QAAQ;MAC7B,IAAI,CAAC5E,aAAa,GAAGD,QAAQ;MAE7B,IAAI,CAACmD,SAAS,CAAC0B,QAAQ,CAAC;MACxB,IAAI,CAAC,IAAI,CAACK,QAAQ,EAAE;QAElB;MACF;MAGA,IAAIC,cAAc,GAAG,KAAK;MAC1B,IAAI,IAAI,CAAC1F,kBAAkB,IAAI,IAAI,CAAC2B,UAAU,KAAK,CAAC,EAAE;QACpD,IAAI,IAAI,CAAC6B,cAAc,GAAG,IAAI,CAAC/C,QAAQ,EAAE;UACvCiF,cAAc,GAAGN,QAAQ,GAAG,IAAI,CAAC3E,QAAQ;QAC3C,CAAC,MAAM;UACLiF,cAAc,GAAGN,QAAQ,GAAG,IAAI,CAAC3E,QAAQ;QAC3C;MACF;MACA,IAAMkF,UAAU,GAAGZ,IAAI,CAACa,GAAG,CAACrF,QAAQ,CAAC,IAAI,IAAI,CAACH,mBAAmB;MACjE,IAAIyF,cAAc,GAAG,IAAI;MACzB,IAAI,IAAI,CAAClE,UAAU,KAAK,CAAC,EAAE;QACzBkE,cAAc,GACZd,IAAI,CAACa,GAAG,CAAC,IAAI,CAACnF,QAAQ,GAAG2E,QAAQ,CAAC,IAAI,IAAI,CAAClF,0BAA0B;MACzE;MAEA,IAAIwF,cAAc,IAAKC,UAAU,IAAIE,cAAe,EAAE;QACpD,IAAI,IAAI,CAAClE,UAAU,KAAK,CAAC,EAAE;UAEzB,IAAI,CAAC8B,aAAa,GAAG,IAAI,CAAChD,QAAQ;UAClC,IAAI,CAACD,aAAa,GAAG,CAAC;UACtB,IAAI,CAACkD,SAAS,CAAC,IAAI,CAACjD,QAAQ,CAAC;QAC/B;QAEA,IAAI,CAACqF,oBAAoB,CAAC;UAACC,QAAQ,EAAE;QAAI,CAAC,CAAC;QAC3C;MACF;MAEA,IAAI,CAACC,eAAe,GAAGC,qBAAqB,CAAC,IAAI,CAAC9C,QAAQ,CAAC+C,IAAI,CAAC,IAAI,CAAC,CAAC;IACxE;EAAC;IAAA3D,GAAA;IAAAC,KAAA,EAED,SAAA2D,IAAIA,CAAA,EAAS;MACXlH,aAAA,CAAAG,eAAA;MACAgH,YAAY,CAAC,IAAI,CAAC9B,QAAQ,CAAC;MAC3B,IAAI,IAAI,CAAC0B,eAAe,IAAI,IAAI,EAAE;QAChCK,MAAM,CAACC,oBAAoB,CAAC,IAAI,CAACN,eAAe,CAAC;MACnD;MACA,IAAI,CAACF,oBAAoB,CAAC;QAACC,QAAQ,EAAE;MAAK,CAAC,CAAC;IAC9C;EAAC;AAAA,EA7R0CQ,mBAAS", "ignoreList": []}