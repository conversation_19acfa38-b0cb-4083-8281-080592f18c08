4ba748a2eae3dc1c546ed4a0eab08ed2
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.__INTERNAL_VIEW_CONFIG = void 0;
var _codegenNativeComponent = _interopRequireDefault(require("../../../../Libraries/Utilities/codegenNativeComponent"));
var NativeComponentRegistry = require('react-native/Libraries/NativeComponent/NativeComponentRegistry');
var nativeComponentName = 'RCTActivityIndicatorView';
var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {
  uiViewClassName: "RCTActivityIndicatorView",
  validAttributes: {
    hidesWhenStopped: true,
    animating: true,
    color: {
      process: require('react-native/Libraries/StyleSheet/processColor').default
    },
    size: true
  }
};
var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, function () {
  return __INTERNAL_VIEW_CONFIG;
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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