# Provider Data Flow Analysis - Frontend v1

## Overview
This document maps the current provider data flow in frontend_v1 and identifies all mocked data sources that need to be migrated to backend APIs.

## Current Provider Data Sources

### 1. Mock Data Sources (Need Backend Migration)

#### A. Provider Authentication (`useProviderAuth.ts`)
- **Location**: `src/hooks/useProviderAuth.ts`
- **Mock Data**: Lines 84-106
- **Issue**: Hardcoded provider authentication with mock credentials
- **Backend Needed**: Real provider authentication API

#### B. Provider Store (`providerSlice.ts`)
- **Location**: `src/store/providerSlice.ts`
- **Mock Data**: Lines 278-350+ (loadProfile function)
- **Issue**: Entire provider profile is mocked
- **Backend Needed**: Provider profile API endpoints

#### C. Provider Settings (`ProviderSettings.tsx`)
- **Location**: `src/features/provider/settings/ProviderSettings.tsx`
- **Mock Data**: Lines 84-109
- **Issue**: All provider settings are hardcoded
- **Backend Needed**: Provider settings CRUD API

#### D. Provider Profile Management (`ProviderProfileManagement.tsx`)
- **Location**: `src/features/provider/profile/ProviderProfileManagement.tsx`
- **Mock Data**: Lines 89-120+
- **Issue**: Provider profile data is mocked
- **Backend Needed**: Provider profile management API

#### E. Provider Job Management (`ProviderJobManagement.tsx`)
- **Location**: `src/features/provider/jobs/ProviderJobManagement.tsx`
- **Mock Data**: Lines 77+ (referenced but not shown in full)
- **Issue**: Provider job/booking data is mocked
- **Backend Needed**: Provider booking management API

#### F. Notification Management (`NotificationManagementScreen.tsx`)
- **Location**: `src/features/service-management/NotificationManagementScreen.tsx`
- **Mock Data**: Lines 84-130+
- **Issue**: Notification settings are hardcoded
- **Backend Needed**: Notification preferences API

#### G. Providers List (`providersSlice.ts`)
- **Location**: `src/store/providersSlice.ts`
- **Mock Data**: Lines 106-130
- **Issue**: Uses test accounts instead of real provider data
- **Backend Needed**: Provider search/listing API

### 2. Partially Integrated Sources (Need Completion)

#### A. Provider Service (`providerService.ts`)
- **Location**: `src/services/providerService.ts`
- **Status**: Has API endpoints defined but some may not be fully implemented
- **API Endpoints Used**:
  - `/api/v1/catalog/providers/featured/` (Line 214)
  - `/api/v1/catalog/providers/` (Line 244)
  - `/api/v1/customer/nearby/providers/` (Line 388)
- **Issue**: Some endpoints may return mock data or not exist

#### B. Provider Details Screen (`ProviderDetailsScreen.tsx`)
- **Location**: `src/screens/ProviderDetailsScreen.tsx`
- **Status**: ✅ **FIXED** - Now properly handles backend API responses
- **API Used**: `/api/catalog/providers/{id}/services/`

### 3. Backend APIs Available

#### A. Catalog APIs (Working)
- **Provider Details**: `/api/catalog/providers/{id}/`
- **Provider Services**: `/api/catalog/providers/{id}/services/` ✅ **FIXED**
- **Featured Providers**: `/api/catalog/providers/featured/`
- **Provider Search**: `/api/catalog/providers/`

#### B. Provider-Specific APIs (Need Implementation)
- **Provider Dashboard**: `/api/v1/provider/dashboard/`
- **Provider Profile**: `/api/v1/provider/profile/`
- **Provider Settings**: `/api/v1/provider/business-settings/`
- **Provider Services Management**: `/api/v1/provider/services/`

## Components Using Provider Data

### 1. Provider Display Components
- **ProviderCard** (`src/components/providers/ProviderCard.tsx`)
- **ProviderProfileCard** (`src/components/providers/ProviderProfileCard.tsx`)
- **EnhancedProviderProfile** (`src/components/providers/EnhancedProviderProfile.tsx`)

### 2. Provider Management Screens
- **ProviderPortal** (`src/features/provider/ProviderPortal.tsx`)
- **ProviderDashboard** (`src/features/provider/dashboard/ProviderDashboard.tsx`)
- **ProviderProfileManagement** (`src/features/provider/profile/ProviderProfileManagement.tsx`)
- **ProviderSettings** (`src/features/provider/settings/ProviderSettings.tsx`)

### 3. Provider Discovery Screens
- **SearchScreen** (`src/screens/SearchScreen.tsx`) - Lines 326-350
- **StoresScreen** (referenced in frontend_v0)
- **ProviderDetailsScreen** (`src/screens/ProviderDetailsScreen.tsx`) ✅ **FIXED**

## Migration Priority

### High Priority (Core Functionality)
1. **Provider Authentication** - Critical for provider login
2. **Provider Profile API** - Essential for provider management
3. **Provider Services Management** - Core business functionality
4. **Provider Search/Listing** - Customer-facing feature

### Medium Priority (Management Features)
1. **Provider Settings** - Important for provider experience
2. **Provider Dashboard Data** - Analytics and insights
3. **Notification Management** - User experience enhancement

### Low Priority (Enhancement Features)
1. **Provider Portfolio Management** - Nice-to-have feature
2. **Advanced Analytics** - Future enhancement

## Next Steps

1. **Create Backend Provider APIs** - Implement missing provider-specific endpoints
2. **Migrate Provider Authentication** - Replace mock auth with real backend
3. **Update Provider Components** - Connect all components to backend APIs
4. **Test Provider Flow** - Verify complete provider functionality
