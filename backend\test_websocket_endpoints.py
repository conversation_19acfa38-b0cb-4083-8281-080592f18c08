#!/usr/bin/env python3
"""
WebSocket Endpoint Test Script
Tests all WebSocket endpoints and messaging functionality
"""

import asyncio
import websockets
import json
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
import django
django.setup()

from django.contrib.auth import get_user_model
from apps.messaging.models import Conversation, Message
from apps.bookings.models import BookingNotification
from channels.db import database_sync_to_async

User = get_user_model()

WS_BASE_URL = 'ws://************:8000'
TEST_TIMEOUT = 10

async def test_websocket_endpoint(uri, test_name, auth_token=None):
    """Test a WebSocket endpoint"""
    print(f"\n🔌 Testing {test_name}")
    print(f"   Endpoint: {uri}")
    
    try:
        # Add auth token to URL if provided
        if auth_token:
            separator = '&' if '?' in uri else '?'
            uri = f"{uri}{separator}token={auth_token}"
        
        # Connect to WebSocket
        async with websockets.connect(uri, timeout=TEST_TIMEOUT) as websocket:
            print(f"   ✅ Connection successful")
            
            # Send a test message
            test_message = {
                "type": "test_message",
                "content": "Hello from test script"
            }
            await websocket.send(json.dumps(test_message))
            print(f"   📤 Test message sent")
            
            # Try to receive a response (with timeout)
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5)
                print(f"   📥 Response received: {response[:100]}...")
                return True
            except asyncio.TimeoutError:
                print(f"   ⏰ No response received (timeout)")
                return True  # Connection worked, just no response
                
    except websockets.exceptions.ConnectionClosed as e:
        print(f"   ❌ Connection closed: {e}")
        return False
    except websockets.exceptions.InvalidStatusCode as e:
        print(f"   ❌ Invalid status code: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Connection failed: {e}")
        return False

async def test_messaging_websocket(conversation_id, auth_token=None):
    """Test messaging WebSocket with actual message sending"""
    uri = f"{WS_BASE_URL}/ws/messaging/{conversation_id}/"
    print(f"\n💬 Testing Messaging WebSocket")
    print(f"   Endpoint: {uri}")
    
    try:
        if auth_token:
            separator = '&' if '?' in uri else '?'
            uri = f"{uri}{separator}token={auth_token}"
        
        async with websockets.connect(uri, timeout=TEST_TIMEOUT) as websocket:
            print(f"   ✅ Connection successful")
            
            # Send a chat message
            chat_message = {
                "type": "chat_message",
                "content": "Test message from WebSocket test script",
                "reply_to": None
            }
            await websocket.send(json.dumps(chat_message))
            print(f"   📤 Chat message sent")
            
            # Send typing indicator
            typing_message = {
                "type": "typing_indicator",
                "is_typing": True
            }
            await websocket.send(json.dumps(typing_message))
            print(f"   ⌨️  Typing indicator sent")
            
            # Wait for responses
            try:
                for i in range(3):  # Try to receive up to 3 messages
                    response = await asyncio.wait_for(websocket.recv(), timeout=3)
                    print(f"   📥 Response {i+1}: {response[:100]}...")
            except asyncio.TimeoutError:
                print(f"   ⏰ No more responses")
            
            return True
            
    except Exception as e:
        print(f"   ❌ Messaging test failed: {e}")
        return False

@database_sync_to_async
def get_or_create_user(email, **kwargs):
    """Get or create user synchronously"""
    try:
        return User.objects.get(email=email), False
    except User.DoesNotExist:
        return User.objects.create_user(email=email, **kwargs), True

@database_sync_to_async
def get_or_create_conversation():
    """Get or create conversation synchronously"""
    conversation, created = Conversation.objects.get_or_create(
        title="Test Conversation",
        defaults={'conversation_type': 'general'}
    )
    return conversation, created

@database_sync_to_async
def add_participants_to_conversation(conversation, customer, provider):
    """Add participants to conversation"""
    conversation.participants.add(customer, provider)

async def create_test_data():
    """Create test data for WebSocket testing"""
    print("\n🔧 Creating test data...")

    # Create test users
    customer, customer_created = await get_or_create_user(
        email='<EMAIL>',
        password='testpass123',
        first_name='Test',
        last_name='Customer',
        role='customer'
    )
    if customer_created:
        print(f"   👤 Created customer: {customer.email}")
    else:
        print(f"   👤 Using existing customer: {customer.email}")

    provider, provider_created = await get_or_create_user(
        email='<EMAIL>',
        password='testpass123',
        first_name='Test',
        last_name='Provider',
        role='provider'
    )
    if provider_created:
        print(f"   👤 Created provider: {provider.email}")
    else:
        print(f"   👤 Using existing provider: {provider.email}")

    # Create test conversation
    conversation, created = await get_or_create_conversation()
    if created:
        await add_participants_to_conversation(conversation, customer, provider)
        print(f"   💬 Created conversation: {conversation.id}")
    else:
        print(f"   💬 Using existing conversation: {conversation.id}")

    return customer, provider, conversation

async def main():
    """Main test function"""
    print("🚀 WebSocket Endpoint Testing")
    print("=" * 50)
    
    # Create test data
    customer, provider, conversation = await create_test_data()
    
    # Test endpoints
    test_results = []
    
    # Test basic WebSocket endpoints without auth
    endpoints = [
        (f"{WS_BASE_URL}/ws/messaging/{conversation.id}/", "Messaging WebSocket (no auth)"),
        (f"{WS_BASE_URL}/ws/notifications/{customer.id}/", "Notifications WebSocket (no auth)"),
    ]
    
    for uri, name in endpoints:
        result = await test_websocket_endpoint(uri, name)
        test_results.append((name, result))
    
    # Test with mock auth token (this will fail auth but test connection)
    mock_token = "mock_jwt_token_for_testing"
    auth_endpoints = [
        (f"{WS_BASE_URL}/ws/messaging/{conversation.id}/", "Messaging WebSocket (with mock auth)"),
        (f"{WS_BASE_URL}/ws/notifications/{customer.id}/", "Notifications WebSocket (with mock auth)"),
    ]
    
    for uri, name in auth_endpoints:
        result = await test_websocket_endpoint(uri, name, mock_token)
        test_results.append((name, result))
    
    # Test messaging functionality
    messaging_result = await test_messaging_websocket(conversation.id, mock_token)
    test_results.append(("Messaging Functionality", messaging_result))
    
    # Print results
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 Summary: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All WebSocket endpoints are accessible!")
        print("\n🔧 Next Steps:")
        print("1. ✅ WebSocket routing is working")
        print("2. ✅ Endpoints are accessible")
        print("3. 🔄 Implement proper JWT authentication")
        print("4. 🔄 Test with real user tokens")
        print("5. 🔄 Integrate with frontend services")
    else:
        print("⚠️  Some endpoints need attention")
        print("\n🔧 Troubleshooting:")
        print("1. Check Django Channels configuration")
        print("2. Verify WebSocket routing")
        print("3. Check ASGI application setup")
        print("4. Verify network connectivity")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test script failed: {e}")
        sys.exit(1)
