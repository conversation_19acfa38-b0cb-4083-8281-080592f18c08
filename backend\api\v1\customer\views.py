"""
Customer API Views - Enhanced based on Backend Agent feedback
Role-based API endpoints specifically designed for customer functionality
"""

from rest_framework import viewsets, status, permissions
from rest_framework.views import APIView
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.throttling import UserRateThrottle
from django.db.models import Q, Prefetch
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django.core.cache import cache
from drf_spectacular.utils import extend_schema, OpenApiParameter

from apps.catalog.models import Service, ServiceProvider, ServiceCategory
from apps.bookings.models import Booking
from apps.catalog.serializers import ServiceSerializer, ServiceProviderSerializer
from apps.bookings.serializers import BookingDetailSerializer
from .serializers import (
    CustomerServiceSerializer,
    CustomerBookingSerializer,
    CustomerSearchSerializer,
    CustomerFavoriteSerializer,
)
from .permissions import IsCustomerUser
from .throttling import CustomerAPIThrottle


class CustomerServiceViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Customer-specific service discovery and browsing
    Enhanced with performance optimizations and customer-focused features
    """
    serializer_class = CustomerServiceSerializer
    permission_classes = [permissions.IsAuthenticated, IsCustomerUser]
    throttle_classes = [CustomerAPIThrottle]
    
    def get_queryset(self):
        """Optimized queryset with customer-specific filtering"""
        return Service.objects.select_related(
            'provider', 'category'
        ).prefetch_related(
            'gallery_images', 'availability_slots'
        ).filter(
            is_active=True,
            provider__is_active=True
        ).order_by('-created_at')

    @extend_schema(
        summary="List available services for customers",
        description="Get paginated list of services with customer-specific optimizations",
        parameters=[
            OpenApiParameter('category', str, description='Filter by category ID'),
            OpenApiParameter('location', str, description='Filter by location (lat,lng,radius)'),
            OpenApiParameter('price_min', float, description='Minimum price filter'),
            OpenApiParameter('price_max', float, description='Maximum price filter'),
            OpenApiParameter('rating_min', float, description='Minimum rating filter'),
        ]
    )
    def list(self, request, *args, **kwargs):
        """Enhanced list with customer-specific filtering"""
        queryset = self.get_queryset()
        
        # Apply customer-specific filters
        category = request.query_params.get('category')
        if category:
            queryset = queryset.filter(category_id=category)
            
        location = request.query_params.get('location')
        if location:
            # Parse location: "lat,lng,radius"
            try:
                lat, lng, radius = map(float, location.split(','))
                # Add location-based filtering logic here
                # This would integrate with PostGIS for geospatial queries
            except ValueError:
                pass
                
        price_min = request.query_params.get('price_min')
        if price_min:
            queryset = queryset.filter(price__gte=float(price_min))
            
        price_max = request.query_params.get('price_max')
        if price_max:
            queryset = queryset.filter(price__lte=float(price_max))
            
        rating_min = request.query_params.get('rating_min')
        if rating_min:
            queryset = queryset.filter(average_rating__gte=float(rating_min))
        
        return super().list(request, *args, **kwargs)

    @action(detail=False, methods=['get'])
    @method_decorator(cache_page(60 * 15))  # Cache for 15 minutes
    def featured(self, request):
        """Get featured services for customers"""
        featured_services = self.get_queryset().filter(
            is_featured=True
        )[:10]
        
        serializer = self.get_serializer(featured_services, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def nearby(self, request):
        """Get services near customer location"""
        # This would implement location-based search
        # For now, return popular services
        nearby_services = self.get_queryset().filter(
            provider__is_verified=True
        ).order_by('-average_rating')[:20]
        
        serializer = self.get_serializer(nearby_services, many=True)
        return Response(serializer.data)


class CustomerBookingViewSet(viewsets.ModelViewSet):
    """
    Customer booking management with optimistic updates support
    Enhanced with customer-specific features and performance optimizations
    """
    serializer_class = CustomerBookingSerializer
    permission_classes = [permissions.IsAuthenticated, IsCustomerUser]
    throttle_classes = [CustomerAPIThrottle]
    
    def get_queryset(self):
        """Customer can only see their own bookings"""
        return Booking.objects.select_related(
            'service', 'service__provider', 'customer'
        ).filter(
            customer=self.request.user
        ).order_by('-created_at')

    @extend_schema(
        summary="Create new booking",
        description="Create a new booking with optimistic update support"
    )
    def create(self, request, *args, **kwargs):
        """Enhanced booking creation with validation"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # Add customer to booking
        serializer.validated_data['customer'] = request.user
        
        # Perform additional validation
        service = serializer.validated_data['service']
        if not service.is_available_for_booking():
            return Response(
                {'error': 'Service is not available for booking'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        booking = serializer.save()
        
        # Clear relevant caches
        cache.delete(f'customer_bookings_{request.user.id}')
        
        return Response(
            self.get_serializer(booking).data,
            status=status.HTTP_201_CREATED
        )

    @action(detail=False, methods=['get'])
    def upcoming(self, request):
        """Get customer's upcoming bookings"""
        cache_key = f'customer_upcoming_bookings_{request.user.id}'
        cached_data = cache.get(cache_key)
        
        if cached_data is None:
            upcoming_bookings = self.get_queryset().filter(
                status__in=['confirmed', 'pending'],
                scheduled_datetime__gte=timezone.now()
            )
            serializer = self.get_serializer(upcoming_bookings, many=True)
            cached_data = serializer.data
            cache.set(cache_key, cached_data, 60 * 30)  # Cache for 30 minutes
        
        return Response(cached_data)

    @action(detail=False, methods=['get'])
    def history(self, request):
        """Get customer's booking history"""
        history_bookings = self.get_queryset().filter(
            status__in=['completed', 'cancelled']
        )
        
        page = self.paginate_queryset(history_bookings)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(history_bookings, many=True)
        return Response(serializer.data)


class CustomerSearchViewSet(viewsets.ViewSet):
    """
    Enhanced search functionality for customers
    Implements PostgreSQL full-text search with geolocation and advanced filtering
    """
    permission_classes = [permissions.IsAuthenticated, IsCustomerUser]
    throttle_classes = [CustomerAPIThrottle]

    @extend_schema(
        summary="Search services and providers",
        description="Full-text search with advanced filtering options including geolocation",
        parameters=[
            OpenApiParameter('q', str, description='Search query'),
            OpenApiParameter('type', str, description='Search type: services, providers, or all'),
            OpenApiParameter('category', str, description='Category filter'),
            OpenApiParameter('latitude', float, description='User latitude for distance calculation'),
            OpenApiParameter('longitude', float, description='User longitude for distance calculation'),
            OpenApiParameter('radius', float, description='Search radius in kilometers (default: 50)'),
            OpenApiParameter('price_min', float, description='Minimum price filter'),
            OpenApiParameter('price_max', float, description='Maximum price filter'),
            OpenApiParameter('rating_min', float, description='Minimum rating filter'),
            OpenApiParameter('sort_by', str, description='Sort by: relevance, price, rating, distance'),
            OpenApiParameter('page', int, description='Page number for pagination'),
            OpenApiParameter('page_size', int, description='Number of results per page (max 50)'),
        ]
    )
    @action(detail=False, methods=['get'])
    def search(self, request):
        """Advanced search with full-text capabilities and geolocation"""
        from math import radians, cos, sin, asin, sqrt
        from django.db.models import Case, When, Value, FloatField, F
        from decimal import Decimal

        # Get search parameters
        query = request.query_params.get('q', '').strip()
        search_type = request.query_params.get('type', 'all')
        category = request.query_params.get('category', '')

        # Geolocation parameters
        user_lat = request.query_params.get('latitude')
        user_lng = request.query_params.get('longitude')
        radius = float(request.query_params.get('radius', 50))  # Default 50km

        # Filter parameters
        price_min = request.query_params.get('price_min')
        price_max = request.query_params.get('price_max')
        rating_min = request.query_params.get('rating_min')
        sort_by = request.query_params.get('sort_by', 'relevance')

        # Pagination parameters
        page = int(request.query_params.get('page', 1))
        page_size = min(int(request.query_params.get('page_size', 20)), 50)
        offset = (page - 1) * page_size

        # Validate required parameters for location-based search
        if user_lat and user_lng:
            try:
                user_lat = float(user_lat)
                user_lng = float(user_lng)
            except (ValueError, TypeError):
                return Response({'error': 'Invalid latitude or longitude'}, status=400)

        results = {}

        # Helper function to calculate distance using Haversine formula
        def add_distance_annotation(queryset, user_lat, user_lng):
            if user_lat is None or user_lng is None:
                return queryset.annotate(distance=Value(None, output_field=FloatField()))

            # Haversine formula for distance calculation
            # Note: This is an approximation. For production, consider using PostGIS
            return queryset.extra(
                select={
                    'distance': '''
                        6371 * acos(
                            cos(radians(%s)) * cos(radians(latitude)) *
                            cos(radians(longitude) - radians(%s)) +
                            sin(radians(%s)) * sin(radians(latitude))
                        )
                    '''
                },
                select_params=[user_lat, user_lng, user_lat]
            )

        if search_type in ['services', 'all']:
            # Build services query
            services_query = Service.objects.filter(is_active=True, is_available=True)

            # Apply text search
            if query:
                services_query = services_query.filter(
                    Q(name__icontains=query) |
                    Q(description__icontains=query) |
                    Q(short_description__icontains=query) |
                    Q(category__name__icontains=query) |
                    Q(provider__business_name__icontains=query)
                )

            # Apply category filter
            if category:
                services_query = services_query.filter(category__name__iexact=category)

            # Apply price filters
            if price_min:
                services_query = services_query.filter(base_price__gte=Decimal(price_min))
            if price_max:
                services_query = services_query.filter(base_price__lte=Decimal(price_max))

            # Apply provider rating filter
            if rating_min:
                services_query = services_query.filter(provider__rating__gte=float(rating_min))

            # Add distance calculation and location filter
            services_query = add_distance_annotation(services_query, user_lat, user_lng)
            if user_lat and user_lng and radius:
                services_query = services_query.extra(
                    where=['''
                        6371 * acos(
                            cos(radians(%s)) * cos(radians(latitude)) *
                            cos(radians(longitude) - radians(%s)) +
                            sin(radians(%s)) * sin(radians(latitude))
                        ) <= %s
                    '''],
                    params=[user_lat, user_lng, user_lat, radius]
                )

            # Apply sorting
            if sort_by == 'price':
                services_query = services_query.order_by('base_price')
            elif sort_by == 'rating':
                services_query = services_query.order_by('-provider__rating')
            elif sort_by == 'distance' and user_lat and user_lng:
                services_query = services_query.order_by('distance')
            else:  # relevance or default
                services_query = services_query.order_by('-is_popular', '-provider__rating', 'base_price')

            # Select related for efficiency
            services_query = services_query.select_related('provider', 'category')

            # Get total count for pagination
            total_services = services_query.count()

            # Apply pagination
            services = services_query[offset:offset + page_size]

            # Serialize results
            services_data = CustomerServiceSerializer(services, many=True).data

            # Add distance to serialized data if available
            for i, service in enumerate(services):
                if hasattr(service, 'distance') and service.distance is not None:
                    services_data[i]['distance'] = round(service.distance, 2)

            results['services'] = {
                'results': services_data,
                'count': total_services,
                'next': f"?page={page + 1}" if offset + page_size < total_services else None,
                'previous': f"?page={page - 1}" if page > 1 else None,
            }

        if search_type in ['providers', 'all']:
            # Build providers query
            providers_query = ServiceProvider.objects.filter(is_active=True)

            # Apply text search
            if query:
                providers_query = providers_query.filter(
                    Q(business_name__icontains=query) |
                    Q(business_description__icontains=query) |
                    Q(city__icontains=query) |
                    Q(state__icontains=query)
                )

            # Apply rating filter
            if rating_min:
                providers_query = providers_query.filter(rating__gte=float(rating_min))

            # Add distance calculation and location filter
            providers_query = add_distance_annotation(providers_query, user_lat, user_lng)
            if user_lat and user_lng and radius:
                providers_query = providers_query.extra(
                    where=['''
                        6371 * acos(
                            cos(radians(%s)) * cos(radians(latitude)) *
                            cos(radians(longitude) - radians(%s)) +
                            sin(radians(%s)) * sin(radians(latitude))
                        ) <= %s
                    '''],
                    params=[user_lat, user_lng, user_lat, radius]
                )

            # Apply sorting
            if sort_by == 'rating':
                providers_query = providers_query.order_by('-rating')
            elif sort_by == 'distance' and user_lat and user_lng:
                providers_query = providers_query.order_by('distance')
            else:  # relevance or default
                providers_query = providers_query.order_by('-is_featured', '-rating', '-review_count')

            # Prefetch related for efficiency
            providers_query = providers_query.prefetch_related('services')

            # Get total count for pagination
            total_providers = providers_query.count()

            # Apply pagination
            providers = providers_query[offset:offset + page_size]

            # Serialize results
            providers_data = ServiceProviderSerializer(providers, many=True).data

            # Add distance to serialized data if available
            for i, provider in enumerate(providers):
                if hasattr(provider, 'distance') and provider.distance is not None:
                    providers_data[i]['distance'] = round(provider.distance, 2)

            results['providers'] = {
                'results': providers_data,
                'count': total_providers,
                'next': f"?page={page + 1}" if offset + page_size < total_providers else None,
                'previous': f"?page={page - 1}" if page > 1 else None,
            }

        # Add search metadata
        results['search_metadata'] = {
            'query': query,
            'search_type': search_type,
            'filters_applied': {
                'category': category,
                'location': {'latitude': user_lat, 'longitude': user_lng, 'radius': radius} if user_lat and user_lng else None,
                'price_range': {'min': price_min, 'max': price_max} if price_min or price_max else None,
                'rating_min': rating_min,
                'sort_by': sort_by,
            },
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_results': sum([
                    results.get('services', {}).get('count', 0),
                    results.get('providers', {}).get('count', 0)
                ])
            }
        }

        return Response(results)

    @extend_schema(
        summary="Get search suggestions",
        description="Get search suggestions based on query input",
        parameters=[
            OpenApiParameter('q', str, description='Partial search query'),
            OpenApiParameter('limit', int, description='Maximum number of suggestions (default: 10)'),
        ]
    )
    @action(detail=False, methods=['get'])
    def suggestions(self, request):
        """Get search suggestions for autocomplete"""
        query = request.query_params.get('q', '').strip()
        limit = min(int(request.query_params.get('limit', 10)), 20)

        if len(query) < 2:
            return Response({'suggestions': []})

        suggestions = []

        # Service name suggestions
        service_suggestions = Service.objects.filter(
            name__icontains=query,
            is_active=True
        ).values_list('name', flat=True).distinct()[:limit//2]

        for service_name in service_suggestions:
            suggestions.append({
                'text': service_name,
                'type': 'service',
                'category': 'Services'
            })

        # Provider business name suggestions
        provider_suggestions = ServiceProvider.objects.filter(
            business_name__icontains=query,
            is_active=True
        ).values_list('business_name', flat=True).distinct()[:limit//2]

        for business_name in provider_suggestions:
            suggestions.append({
                'text': business_name,
                'type': 'provider',
                'category': 'Providers'
            })

        # Category suggestions
        if len(suggestions) < limit:
            category_suggestions = ServiceCategory.objects.filter(
                name__icontains=query,
                is_active=True
            ).values_list('name', flat=True).distinct()[:limit - len(suggestions)]

            for category_name in category_suggestions:
                suggestions.append({
                    'text': category_name,
                    'type': 'category',
                    'category': 'Categories'
                })

        return Response({'suggestions': suggestions[:limit]})

    @extend_schema(
        summary="Get popular searches",
        description="Get popular search terms and trending categories",
    )
    @action(detail=False, methods=['get'])
    def popular(self, request):
        """Get popular search terms and categories"""
        # Get popular categories
        popular_categories = ServiceCategory.objects.filter(
            is_active=True,
            is_popular=True
        ).order_by('-service_count')[:10]

        # Get popular services (most booked)
        popular_services = Service.objects.filter(
            is_active=True,
            is_popular=True
        ).order_by('-booking_count')[:10]

        # Get featured providers
        featured_providers = ServiceProvider.objects.filter(
            is_active=True,
            is_featured=True
        ).order_by('-rating')[:5]

        return Response({
            'popular_categories': [
                {'name': cat.name, 'service_count': cat.service_count}
                for cat in popular_categories
            ],
            'popular_services': [
                {'name': service.name, 'provider': service.provider.business_name}
                for service in popular_services
            ],
            'featured_providers': [
                {'business_name': provider.business_name, 'rating': provider.rating}
                for provider in featured_providers
            ]
        })


class CustomerFavoriteViewSet(viewsets.ModelViewSet):
    """
    Customer favorites management
    """
    serializer_class = CustomerFavoriteSerializer
    permission_classes = [permissions.IsAuthenticated, IsCustomerUser]
    throttle_classes = [CustomerAPIThrottle]
    
    def get_queryset(self):
        """Customer can only see their own favorites"""
        # This would require a Favorite model to be implemented
        # For now, return empty queryset
        return Service.objects.none()
    
    @action(detail=False, methods=['post'])
    def add(self, request):
        """Add service to favorites"""
        service_id = request.data.get('service_id')
        if not service_id:
            return Response({'error': 'service_id is required'}, status=400)
        
        # Implementation would add to favorites
        return Response({'message': 'Added to favorites'})
    
    @action(detail=False, methods=['delete'])
    def remove(self, request):
        """Remove service from favorites"""
        service_id = request.data.get('service_id')
        if not service_id:
            return Response({'error': 'service_id is required'}, status=400)
        
        # Implementation would remove from favorites
        return Response({'message': 'Removed from favorites'})


# Additional View Classes for URL patterns
class CustomerDashboardView(APIView):
    """Customer dashboard view"""
    permission_classes = [permissions.IsAuthenticated, IsCustomerUser]

    def get(self, request):
        return Response({'message': 'Customer dashboard'})


class CustomerProfileView(APIView):
    """Customer profile view"""
    permission_classes = [permissions.IsAuthenticated, IsCustomerUser]

    def get(self, request):
        return Response({'message': 'Customer profile'})


class AdvancedSearchView(APIView):
    """Advanced search view"""
    permission_classes = [permissions.IsAuthenticated, IsCustomerUser]

    def get(self, request):
        return Response({'message': 'Advanced search'})


class SearchSuggestionsView(APIView):
    """Search suggestions view"""
    permission_classes = [permissions.IsAuthenticated, IsCustomerUser]

    def get(self, request):
        return Response({'message': 'Search suggestions'})


class QuickBookingView(APIView):
    """Quick booking view"""
    permission_classes = [permissions.IsAuthenticated, IsCustomerUser]

    def post(self, request):
        return Response({'message': 'Quick booking'})


class CancelBookingView(APIView):
    """Cancel booking view"""
    permission_classes = [permissions.IsAuthenticated, IsCustomerUser]

    def post(self, request, booking_id):
        return Response({'message': f'Cancel booking {booking_id}'})


class RescheduleBookingView(APIView):
    """Reschedule booking view"""
    permission_classes = [permissions.IsAuthenticated, IsCustomerUser]

    def post(self, request, booking_id):
        return Response({'message': f'Reschedule booking {booking_id}'})


class BulkAddFavoritesView(APIView):
    """Bulk add favorites view"""
    permission_classes = [permissions.IsAuthenticated, IsCustomerUser]

    def post(self, request):
        return Response({'message': 'Bulk add favorites'})


class BulkRemoveFavoritesView(APIView):
    """Bulk remove favorites view"""
    permission_classes = [permissions.IsAuthenticated, IsCustomerUser]

    def post(self, request):
        return Response({'message': 'Bulk remove favorites'})


class RecommendationsView(APIView):
    """Recommendations view"""
    permission_classes = [permissions.IsAuthenticated, IsCustomerUser]

    def get(self, request):
        return Response({'message': 'Recommendations'})


class PersonalizedRecommendationsView(APIView):
    """Personalized recommendations view"""
    permission_classes = [permissions.IsAuthenticated, IsCustomerUser]

    def get(self, request):
        return Response({'message': 'Personalized recommendations'})


class NearbyServicesView(APIView):
    """Nearby services view"""
    permission_classes = [permissions.IsAuthenticated, IsCustomerUser]

    def get(self, request):
        return Response({'message': 'Nearby services'})


class NearbyProvidersView(APIView):
    """Nearby providers view"""
    permission_classes = [permissions.IsAuthenticated, IsCustomerUser]

    def get(self, request):
        return Response({'message': 'Nearby providers'})


class CustomerReviewsView(APIView):
    """Customer reviews view"""
    permission_classes = [permissions.IsAuthenticated, IsCustomerUser]

    def get(self, request):
        return Response({'message': 'Customer reviews'})


class CreateReviewView(APIView):
    """Create review view"""
    permission_classes = [permissions.IsAuthenticated, IsCustomerUser]

    def post(self, request):
        return Response({'message': 'Create review'})


class NotificationPreferencesView(APIView):
    """Notification preferences view"""
    permission_classes = [permissions.IsAuthenticated, IsCustomerUser]

    def get(self, request):
        return Response({'message': 'Notification preferences'})

    def post(self, request):
        return Response({'message': 'Update notification preferences'})


class CustomerUsageAnalyticsView(APIView):
    """Customer usage analytics view"""
    permission_classes = [permissions.IsAuthenticated, IsCustomerUser]

    def get(self, request):
        return Response({'message': 'Customer usage analytics'})
