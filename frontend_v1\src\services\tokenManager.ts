/**
 * Token Manager - Secure Token Storage and Management
 *
 * Service Contract:
 * - Manages JWT access and refresh tokens securely
 * - <PERSON>les token expiration and automatic refresh
 * - Provides secure storage using AsyncStorage/SecureStore
 * - Implements token validation and cleanup
 * - Supports token rotation and security best practices
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

// Storage keys
const STORAGE_KEYS = {
  ACCESS_TOKEN: 'auth_access_token',
  REFRESH_TOKEN: 'auth_refresh_token',
  TOKEN_EXPIRY: 'auth_token_expiry',
  USER_DATA: 'auth_user_data',
} as const;

export interface TokenData {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
  user?: any;
}

export interface TokenValidationResult {
  isValid: boolean;
  needsRefresh: boolean;
  expiresIn: number;
}

class TokenManager {
  private accessToken: string | null = null;
  private refreshToken: string | null = null;
  private expiresAt: number | null = null;
  private refreshPromise: Promise<string> | null = null;

  /**
   * Initialize token manager and load existing tokens
   */
  async initialize(): Promise<void> {
    try {
      await this.loadTokensFromStorage();
      console.log('✅ TokenManager: Initialized successfully');
    } catch (error) {
      console.error('❌ TokenManager: Initialization failed:', error);
    }
  }

  /**
   * Store tokens securely
   */
  async storeTokens(tokenData: TokenData): Promise<void> {
    try {
      this.accessToken = tokenData.accessToken;
      this.refreshToken = tokenData.refreshToken;
      this.expiresAt = tokenData.expiresAt;

      // Use SecureStore for sensitive data on mobile, AsyncStorage on web
      if (Platform.OS === 'web') {
        await AsyncStorage.multiSet([
          [STORAGE_KEYS.ACCESS_TOKEN, tokenData.accessToken],
          [STORAGE_KEYS.REFRESH_TOKEN, tokenData.refreshToken],
          [STORAGE_KEYS.TOKEN_EXPIRY, tokenData.expiresAt.toString()],
          [STORAGE_KEYS.USER_DATA, JSON.stringify(tokenData.user || {})],
        ]);
      } else {
        await Promise.all([
          SecureStore.setItemAsync(STORAGE_KEYS.ACCESS_TOKEN, tokenData.accessToken),
          SecureStore.setItemAsync(STORAGE_KEYS.REFRESH_TOKEN, tokenData.refreshToken),
          SecureStore.setItemAsync(STORAGE_KEYS.TOKEN_EXPIRY, tokenData.expiresAt.toString()),
          AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(tokenData.user || {})),
        ]);
      }

      console.log('✅ TokenManager: Tokens stored securely');
    } catch (error) {
      console.error('❌ TokenManager: Failed to store tokens:', error);
      throw new Error('Failed to store authentication tokens');
    }
  }

  /**
   * Load tokens from storage
   */
  private async loadTokensFromStorage(): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        const [accessToken, refreshToken, expiryStr] = await AsyncStorage.multiGet([
          STORAGE_KEYS.ACCESS_TOKEN,
          STORAGE_KEYS.REFRESH_TOKEN,
          STORAGE_KEYS.TOKEN_EXPIRY,
        ]);

        this.accessToken = accessToken[1];
        this.refreshToken = refreshToken[1];
        this.expiresAt = expiryStr[1] ? parseInt(expiryStr[1], 10) : null;
      } else {
        const [accessToken, refreshToken, expiryStr] = await Promise.all([
          SecureStore.getItemAsync(STORAGE_KEYS.ACCESS_TOKEN),
          SecureStore.getItemAsync(STORAGE_KEYS.REFRESH_TOKEN),
          SecureStore.getItemAsync(STORAGE_KEYS.TOKEN_EXPIRY),
        ]);

        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        this.expiresAt = expiryStr ? parseInt(expiryStr, 10) : null;
      }
    } catch (error) {
      console.error('❌ TokenManager: Failed to load tokens:', error);
    }
  }

  /**
   * Get current access token
   */
  getAccessToken(): string | null {
    return this.accessToken;
  }

  /**
   * Get current refresh token
   */
  getRefreshToken(): string | null {
    return this.refreshToken;
  }

  /**
   * Validate current token
   */
  validateToken(): TokenValidationResult {
    if (!this.accessToken || !this.expiresAt) {
      return {
        isValid: false,
        needsRefresh: !!this.refreshToken,
        expiresIn: 0,
      };
    }

    const now = Date.now();
    const expiresIn = this.expiresAt - now;
    const needsRefresh = expiresIn < 5 * 60 * 1000; // Refresh if expires in 5 minutes

    return {
      isValid: expiresIn > 0,
      needsRefresh,
      expiresIn,
    };
  }

  /**
   * Refresh access token
   */
  async refreshAccessToken(): Promise<string> {
    // Prevent multiple simultaneous refresh requests
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    if (!this.refreshToken) {
      throw new Error('No refresh token available');
    }

    this.refreshPromise = this.performTokenRefresh();
    
    try {
      const newAccessToken = await this.refreshPromise;
      return newAccessToken;
    } finally {
      this.refreshPromise = null;
    }
  }

  /**
   * Perform the actual token refresh
   */
  private async performTokenRefresh(): Promise<string> {
    try {
      const { authService } = await import('./authService');
      const response = await authService.refreshToken(this.refreshToken!);

      // Update stored tokens
      const newExpiresAt = Date.now() + 30 * 60 * 1000; // 30 minutes
      await this.storeTokens({
        accessToken: response.access,
        refreshToken: this.refreshToken!,
        expiresAt: newExpiresAt,
      });

      console.log('✅ TokenManager: Token refreshed successfully');
      return response.access;
    } catch (error) {
      console.error('❌ TokenManager: Token refresh failed:', error);
      await this.clearTokens();
      throw error;
    }
  }

  /**
   * Clear all stored tokens
   */
  async clearTokens(): Promise<void> {
    try {
      this.accessToken = null;
      this.refreshToken = null;
      this.expiresAt = null;

      if (Platform.OS === 'web') {
        await AsyncStorage.multiRemove([
          STORAGE_KEYS.ACCESS_TOKEN,
          STORAGE_KEYS.REFRESH_TOKEN,
          STORAGE_KEYS.TOKEN_EXPIRY,
          STORAGE_KEYS.USER_DATA,
        ]);
      } else {
        await Promise.all([
          SecureStore.deleteItemAsync(STORAGE_KEYS.ACCESS_TOKEN).catch(() => {}),
          SecureStore.deleteItemAsync(STORAGE_KEYS.REFRESH_TOKEN).catch(() => {}),
          SecureStore.deleteItemAsync(STORAGE_KEYS.TOKEN_EXPIRY).catch(() => {}),
          AsyncStorage.removeItem(STORAGE_KEYS.USER_DATA),
        ]);
      }

      console.log('✅ TokenManager: Tokens cleared');
    } catch (error) {
      console.error('❌ TokenManager: Failed to clear tokens:', error);
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    const validation = this.validateToken();
    return validation.isValid;
  }

  /**
   * Get user data from storage
   */
  async getUserData(): Promise<any> {
    try {
      const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('❌ TokenManager: Failed to get user data:', error);
      return null;
    }
  }
}

// Export singleton instance
export const tokenManager = new TokenManager();
export default tokenManager;
