/**
 * Error Monitoring Service for Unified Error Handling System
 * 
 * Provides comprehensive error monitoring, reporting, and analytics
 * for production debugging and system health monitoring.
 * 
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { Platform } from 'react-native';

import { logger } from '../../utils/logger';

import { 
  ErrorReport, 
  ErrorType, 
  ErrorSeverity, 
  ErrorCategory,
  ErrorMetrics,
  ErrorBreadcrumb 
} from './types';

export interface ErrorMonitoringConfig {
  enableRealTimeMonitoring: boolean;
  enableOfflineStorage: boolean;
  enablePerformanceTracking: boolean;
  enableUserBehaviorTracking: boolean;
  enableCrashReporting: boolean;
  maxStoredErrors: number;
  reportingInterval: number;
  performanceThresholds: {
    renderTime: number;
    memoryUsage: number;
    networkLatency: number;
  };
}

export interface ErrorTrend {
  date: string;
  errorCount: number;
  errorTypes: Record<ErrorType, number>;
  severityDistribution: Record<ErrorSeverity, number>;
  topErrors: Array<{
    message: string;
    count: number;
    lastOccurrence: number;
  }>;
}

export interface SystemHealth {
  errorRate: number;
  crashRate: number;
  performanceScore: number;
  userSatisfactionScore: number;
  systemStability: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
  recommendations: string[];
}

export class ErrorMonitoringService {
  private config: ErrorMonitoringConfig;
  private errorHistory: Map<string, ErrorReport> = new Map();
  private errorTrends: ErrorTrend[] = [];
  private performanceMetrics: Map<string, number[]> = new Map();
  private userBehaviorData: ErrorBreadcrumb[] = [];
  private isInitialized = false;
  private reportingTimer?: NodeJS.Timeout;

  constructor(config: Partial<ErrorMonitoringConfig> = {}) {
    this.config = {
      enableRealTimeMonitoring: true,
      enableOfflineStorage: true,
      enablePerformanceTracking: true,
      enableUserBehaviorTracking: true,
      enableCrashReporting: true,
      maxStoredErrors: 1000,
      reportingInterval: 300000, // 5 minutes
      performanceThresholds: {
        renderTime: 16, // 60fps
        memoryUsage: 100, // MB
        networkLatency: 1000, // ms
      },
      ...config
    };
  }

  /**
   * Initialize the error monitoring service
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      logger.warn('[ErrorMonitoring] Service already initialized');
      return;
    }

    try {
      // Load stored error data
      if (this.config.enableOfflineStorage) {
        await this.loadStoredData();
      }

      // Set up real-time monitoring
      if (this.config.enableRealTimeMonitoring) {
        this.setupRealTimeMonitoring();
      }

      // Set up periodic reporting
      this.setupPeriodicReporting();

      // Set up performance monitoring
      if (this.config.enablePerformanceTracking) {
        this.setupPerformanceMonitoring();
      }

      this.isInitialized = true;
      logger.info('[ErrorMonitoring] Service initialized successfully');

    } catch (error) {
      logger.error('[ErrorMonitoring] Failed to initialize service:', error);
      throw error;
    }
  }

  /**
   * Record an error for monitoring
   */
  async recordError(errorReport: ErrorReport): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      // Store error in memory
      this.errorHistory.set(errorReport.id, errorReport);

      // Update error trends
      this.updateErrorTrends(errorReport);

      // Store offline if enabled
      if (this.config.enableOfflineStorage) {
        await this.storeErrorOffline(errorReport);
      }

      // Track user behavior if enabled
      if (this.config.enableUserBehaviorTracking && errorReport.breadcrumbs) {
        this.userBehaviorData.push(...errorReport.breadcrumbs);
        this.trimUserBehaviorData();
      }

      // Trigger real-time alerts for critical errors
      if (errorReport.severity === ErrorSeverity.CRITICAL) {
        await this.triggerCriticalErrorAlert(errorReport);
      }

      logger.debug('[ErrorMonitoring] Error recorded:', {
        id: errorReport.id,
        type: errorReport.type,
        severity: errorReport.severity
      });

    } catch (error) {
      logger.error('[ErrorMonitoring] Failed to record error:', error);
    }
  }

  /**
   * Get comprehensive error metrics
   */
  getErrorMetrics(): ErrorMetrics {
    const errors = Array.from(this.errorHistory.values());
    const now = Date.now();
    const last24Hours = now - (24 * 60 * 60 * 1000);
    const recentErrors = errors.filter(e => e.timestamp > last24Hours);

    // Calculate error rates
    const totalErrors = errors.length;
    const recentErrorCount = recentErrors.length;
    const errorRate = recentErrorCount / Math.max(1, totalErrors) * 100;

    // Group by type and severity
    const errorsByType = this.groupErrorsByType(recentErrors);
    const errorsBySeverity = this.groupErrorsBySeverity(recentErrors);

    // Calculate crash rate
    const crashes = recentErrors.filter(e => 
      e.type === ErrorType.SYSTEM || e.severity === ErrorSeverity.CRITICAL
    );
    const crashRate = crashes.length / Math.max(1, recentErrorCount) * 100;

    // Get top errors
    const topErrors = this.getTopErrors(recentErrors, 10);

    return {
      totalErrors: totalErrors,
      recentErrors: recentErrorCount,
      errorRate,
      crashRate,
      errorsByType,
      errorsBySeverity,
      topErrors,
      lastUpdated: now
    };
  }

  /**
   * Get system health assessment
   */
  getSystemHealth(): SystemHealth {
    const metrics = this.getErrorMetrics();
    const performanceScore = this.calculatePerformanceScore();
    
    // Calculate overall system stability
    let systemStability: SystemHealth['systemStability'] = 'excellent';
    const recommendations: string[] = [];

    if (metrics.crashRate > 5) {
      systemStability = 'critical';
      recommendations.push('Critical: High crash rate detected. Immediate investigation required.');
    } else if (metrics.errorRate > 20) {
      systemStability = 'poor';
      recommendations.push('High error rate detected. Review error patterns and implement fixes.');
    } else if (metrics.errorRate > 10) {
      systemStability = 'fair';
      recommendations.push('Moderate error rate. Monitor trends and address recurring issues.');
    } else if (metrics.errorRate > 5) {
      systemStability = 'good';
      recommendations.push('Low error rate. Continue monitoring for patterns.');
    }

    if (performanceScore < 60) {
      recommendations.push('Performance issues detected. Optimize critical rendering paths.');
    }

    return {
      errorRate: metrics.errorRate,
      crashRate: metrics.crashRate,
      performanceScore,
      userSatisfactionScore: this.calculateUserSatisfactionScore(),
      systemStability,
      recommendations
    };
  }

  /**
   * Get error trends over time
   */
  getErrorTrends(days: number = 7): ErrorTrend[] {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    
    return this.errorTrends.filter(trend => 
      new Date(trend.date) >= cutoffDate
    ).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }

  /**
   * Export error data for analysis
   */
  async exportErrorData(format: 'json' | 'csv' = 'json'): Promise<string> {
    const errors = Array.from(this.errorHistory.values());
    const metrics = this.getErrorMetrics();
    const trends = this.getErrorTrends(30);
    const systemHealth = this.getSystemHealth();

    const exportData = {
      metadata: {
        exportDate: new Date().toISOString(),
        totalErrors: errors.length,
        platform: Platform.OS,
        version: '2.0.0'
      },
      errors,
      metrics,
      trends,
      systemHealth,
      userBehaviorData: this.userBehaviorData.slice(-100) // Last 100 breadcrumbs
    };

    if (format === 'json') {
      return JSON.stringify(exportData, null, 2);
    } else {
      // Convert to CSV format
      return this.convertToCSV(errors);
    }
  }

  /**
   * Clear error history and reset monitoring
   */
  async clearErrorHistory(): Promise<void> {
    this.errorHistory.clear();
    this.errorTrends = [];
    this.userBehaviorData = [];
    this.performanceMetrics.clear();

    if (this.config.enableOfflineStorage) {
      await AsyncStorage.multiRemove([
        'error_monitoring_errors',
        'error_monitoring_trends',
        'error_monitoring_behavior'
      ]);
    }

    logger.info('[ErrorMonitoring] Error history cleared');
  }

  /**
   * Update monitoring configuration
   */
  updateConfig(newConfig: Partial<ErrorMonitoringConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Restart monitoring with new config
    if (this.isInitialized) {
      this.setupRealTimeMonitoring();
      this.setupPeriodicReporting();
    }

    logger.info('[ErrorMonitoring] Configuration updated');
  }

  /**
   * Cleanup and shutdown monitoring service
   */
  async shutdown(): Promise<void> {
    if (this.reportingTimer) {
      clearInterval(this.reportingTimer);
    }

    if (this.config.enableOfflineStorage) {
      await this.saveDataToStorage();
    }

    this.isInitialized = false;
    logger.info('[ErrorMonitoring] Service shutdown complete');
  }

  // Private helper methods
  private async loadStoredData(): Promise<void> {
    try {
      const [storedErrors, storedTrends, storedBehavior] = await AsyncStorage.multiGet([
        'error_monitoring_errors',
        'error_monitoring_trends',
        'error_monitoring_behavior'
      ]);

      if (storedErrors[1]) {
        const errors = JSON.parse(storedErrors[1]);
        errors.forEach((error: ErrorReport) => {
          this.errorHistory.set(error.id, error);
        });
      }

      if (storedTrends[1]) {
        this.errorTrends = JSON.parse(storedTrends[1]);
      }

      if (storedBehavior[1]) {
        this.userBehaviorData = JSON.parse(storedBehavior[1]);
      }

    } catch (error) {
      logger.error('[ErrorMonitoring] Failed to load stored data:', error);
    }
  }

  private async saveDataToStorage(): Promise<void> {
    try {
      const errors = Array.from(this.errorHistory.values()).slice(-this.config.maxStoredErrors);
      const trends = this.errorTrends.slice(-30); // Keep last 30 days
      const behavior = this.userBehaviorData.slice(-500); // Keep last 500 breadcrumbs

      await AsyncStorage.multiSet([
        ['error_monitoring_errors', JSON.stringify(errors)],
        ['error_monitoring_trends', JSON.stringify(trends)],
        ['error_monitoring_behavior', JSON.stringify(behavior)]
      ]);

    } catch (error) {
      logger.error('[ErrorMonitoring] Failed to save data to storage:', error);
    }
  }

  private setupRealTimeMonitoring(): void {
    // Set up network monitoring
    NetInfo.addEventListener(state => {
      if (!state.isConnected) {
        this.recordNetworkEvent('disconnected');
      }
    });

    // Set up memory monitoring
    if (Platform.OS === 'android') {
      // Android-specific memory monitoring
      this.setupAndroidMemoryMonitoring();
    }
  }

  private setupPeriodicReporting(): void {
    if (this.reportingTimer) {
      clearInterval(this.reportingTimer);
    }

    this.reportingTimer = setInterval(async () => {
      await this.generatePeriodicReport();
    }, this.config.reportingInterval);
  }

  private setupPerformanceMonitoring(): void {
    // Monitor render performance
    this.trackPerformanceMetric('renderTime', 0);
    
    // Monitor memory usage
    if (global.performance && global.performance.memory) {
      const memoryUsage = (global.performance.memory as any).usedJSHeapSize / 1024 / 1024;
      this.trackPerformanceMetric('memoryUsage', memoryUsage);
    }
  }

  private trackPerformanceMetric(metric: string, value: number): void {
    if (!this.performanceMetrics.has(metric)) {
      this.performanceMetrics.set(metric, []);
    }
    
    const values = this.performanceMetrics.get(metric)!;
    values.push(value);
    
    // Keep only last 100 values
    if (values.length > 100) {
      values.shift();
    }
  }

  private updateErrorTrends(errorReport: ErrorReport): void {
    const today = new Date().toISOString().split('T')[0];
    let todayTrend = this.errorTrends.find(t => t.date === today);
    
    if (!todayTrend) {
      todayTrend = {
        date: today,
        errorCount: 0,
        errorTypes: {} as Record<ErrorType, number>,
        severityDistribution: {} as Record<ErrorSeverity, number>,
        topErrors: []
      };
      this.errorTrends.push(todayTrend);
    }
    
    todayTrend.errorCount++;
    todayTrend.errorTypes[errorReport.type] = (todayTrend.errorTypes[errorReport.type] || 0) + 1;
    todayTrend.severityDistribution[errorReport.severity] = 
      (todayTrend.severityDistribution[errorReport.severity] || 0) + 1;
    
    // Update top errors
    const existingError = todayTrend.topErrors.find(e => e.message === errorReport.message);
    if (existingError) {
      existingError.count++;
      existingError.lastOccurrence = errorReport.timestamp;
    } else {
      todayTrend.topErrors.push({
        message: errorReport.message,
        count: 1,
        lastOccurrence: errorReport.timestamp
      });
    }
    
    // Sort and limit top errors
    todayTrend.topErrors.sort((a, b) => b.count - a.count);
    todayTrend.topErrors = todayTrend.topErrors.slice(0, 10);
  }

  private async storeErrorOffline(errorReport: ErrorReport): Promise<void> {
    try {
      const key = `error_${errorReport.id}`;
      await AsyncStorage.setItem(key, JSON.stringify(errorReport));
    } catch (error) {
      logger.error('[ErrorMonitoring] Failed to store error offline:', error);
    }
  }

  private async triggerCriticalErrorAlert(errorReport: ErrorReport): Promise<void> {
    logger.error('[ErrorMonitoring] CRITICAL ERROR DETECTED:', {
      id: errorReport.id,
      message: errorReport.message,
      type: errorReport.type,
      timestamp: new Date(errorReport.timestamp).toISOString()
    });

    // Here you could integrate with external alerting systems
    // like Slack, email notifications, or push notifications
  }

  private trimUserBehaviorData(): void {
    if (this.userBehaviorData.length > 1000) {
      this.userBehaviorData = this.userBehaviorData.slice(-500);
    }
  }

  private groupErrorsByType(errors: ErrorReport[]): Record<ErrorType, number> {
    return errors.reduce((acc, error) => {
      acc[error.type] = (acc[error.type] || 0) + 1;
      return acc;
    }, {} as Record<ErrorType, number>);
  }

  private groupErrorsBySeverity(errors: ErrorReport[]): Record<ErrorSeverity, number> {
    return errors.reduce((acc, error) => {
      acc[error.severity] = (acc[error.severity] || 0) + 1;
      return acc;
    }, {} as Record<ErrorSeverity, number>);
  }

  private getTopErrors(errors: ErrorReport[], limit: number): Array<{message: string; count: number; lastOccurrence: number}> {
    const errorCounts = new Map<string, {count: number; lastOccurrence: number}>();
    
    errors.forEach(error => {
      const existing = errorCounts.get(error.message);
      if (existing) {
        existing.count++;
        existing.lastOccurrence = Math.max(existing.lastOccurrence, error.timestamp);
      } else {
        errorCounts.set(error.message, {
          count: 1,
          lastOccurrence: error.timestamp
        });
      }
    });
    
    return Array.from(errorCounts.entries())
      .map(([message, data]) => ({ message, ...data }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }

  private calculatePerformanceScore(): number {
    let score = 100;
    
    // Check render time performance
    const renderTimes = this.performanceMetrics.get('renderTime') || [];
    if (renderTimes.length > 0) {
      const avgRenderTime = renderTimes.reduce((a, b) => a + b, 0) / renderTimes.length;
      if (avgRenderTime > this.config.performanceThresholds.renderTime) {
        score -= 20;
      }
    }
    
    // Check memory usage
    const memoryUsages = this.performanceMetrics.get('memoryUsage') || [];
    if (memoryUsages.length > 0) {
      const avgMemoryUsage = memoryUsages.reduce((a, b) => a + b, 0) / memoryUsages.length;
      if (avgMemoryUsage > this.config.performanceThresholds.memoryUsage) {
        score -= 15;
      }
    }
    
    return Math.max(0, score);
  }

  private calculateUserSatisfactionScore(): number {
    const metrics = this.getErrorMetrics();
    let score = 100;
    
    // Reduce score based on error rate
    score -= metrics.errorRate * 2;
    
    // Reduce score based on crash rate
    score -= metrics.crashRate * 5;
    
    return Math.max(0, Math.min(100, score));
  }

  private recordNetworkEvent(event: string): void {
    logger.info(`[ErrorMonitoring] Network event: ${event}`);
  }

  private setupAndroidMemoryMonitoring(): void {
    // Android-specific memory monitoring implementation
    logger.info('[ErrorMonitoring] Android memory monitoring enabled');
  }

  private async generatePeriodicReport(): Promise<void> {
    const metrics = this.getErrorMetrics();
    const systemHealth = this.getSystemHealth();
    
    logger.info('[ErrorMonitoring] Periodic Report:', {
      errorRate: metrics.errorRate,
      crashRate: metrics.crashRate,
      systemStability: systemHealth.systemStability,
      totalErrors: metrics.totalErrors
    });
    
    // Save data to storage
    if (this.config.enableOfflineStorage) {
      await this.saveDataToStorage();
    }
  }

  private convertToCSV(errors: ErrorReport[]): string {
    const headers = ['ID', 'Type', 'Severity', 'Message', 'Timestamp', 'Component', 'Action'];
    const rows = errors.map(error => [
      error.id,
      error.type,
      error.severity,
      error.message.replace(/,/g, ';'), // Escape commas
      new Date(error.timestamp).toISOString(),
      error.context?.component || '',
      error.context?.action || ''
    ]);
    
    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }
}

// Export singleton instance
export const errorMonitoringService = new ErrorMonitoringService();
