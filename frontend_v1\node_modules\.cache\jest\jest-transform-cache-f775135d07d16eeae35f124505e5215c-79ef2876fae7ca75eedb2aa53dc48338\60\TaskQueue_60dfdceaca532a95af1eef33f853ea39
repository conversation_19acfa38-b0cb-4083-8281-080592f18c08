2967ce2dd0c7c1891cbdebacd0d7366e
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var infoLog = require("../Utilities/infoLog").default;
var invariant = require('invariant');
var DEBUG = false;
var TaskQueue = function () {
  function TaskQueue(_ref) {
    var onMoreTasks = _ref.onMoreTasks;
    (0, _classCallCheck2.default)(this, TaskQueue);
    this._onMoreTasks = onMoreTasks;
    this._queueStack = [{
      tasks: [],
      popable: false
    }];
  }
  return (0, _createClass2.default)(TaskQueue, [{
    key: "enqueue",
    value: function enqueue(task) {
      this._getCurrentQueue().push(task);
    }
  }, {
    key: "enqueueTasks",
    value: function enqueueTasks(tasks) {
      var _this = this;
      tasks.forEach(function (task) {
        return _this.enqueue(task);
      });
    }
  }, {
    key: "cancelTasks",
    value: function cancelTasks(tasksToCancel) {
      this._queueStack = this._queueStack.map(function (queue) {
        return Object.assign({}, queue, {
          tasks: queue.tasks.filter(function (task) {
            return tasksToCancel.indexOf(task) === -1;
          })
        });
      }).filter(function (queue, idx) {
        return queue.tasks.length > 0 || idx === 0;
      });
    }
  }, {
    key: "hasTasksToProcess",
    value: function hasTasksToProcess() {
      return this._getCurrentQueue().length > 0;
    }
  }, {
    key: "processNext",
    value: function processNext() {
      var queue = this._getCurrentQueue();
      if (queue.length) {
        var task = queue.shift();
        try {
          if (typeof task === 'object' && task.gen) {
            DEBUG && infoLog('TaskQueue: genPromise for task ' + task.name);
            this._genPromise(task);
          } else if (typeof task === 'object' && task.run) {
            DEBUG && infoLog('TaskQueue: run task ' + task.name);
            task.run();
          } else {
            invariant(typeof task === 'function', 'Expected Function, SimpleTask, or PromiseTask, but got:\n' + JSON.stringify(task, null, 2));
            DEBUG && infoLog('TaskQueue: run anonymous task');
            task();
          }
        } catch (e) {
          e.message = 'TaskQueue: Error with task ' + (task.name || '') + ': ' + e.message;
          throw e;
        }
      }
    }
  }, {
    key: "_getCurrentQueue",
    value: function _getCurrentQueue() {
      var stackIdx = this._queueStack.length - 1;
      var queue = this._queueStack[stackIdx];
      if (queue.popable && queue.tasks.length === 0 && this._queueStack.length > 1) {
        this._queueStack.pop();
        DEBUG && infoLog('TaskQueue: popped queue: ', {
          stackIdx: stackIdx,
          queueStackSize: this._queueStack.length
        });
        return this._getCurrentQueue();
      } else {
        return queue.tasks;
      }
    }
  }, {
    key: "_genPromise",
    value: function _genPromise(task) {
      var _this2 = this;
      this._queueStack.push({
        tasks: [],
        popable: false
      });
      var stackIdx = this._queueStack.length - 1;
      var stackItem = this._queueStack[stackIdx];
      DEBUG && infoLog('TaskQueue: push new queue: ', {
        stackIdx: stackIdx
      });
      DEBUG && infoLog('TaskQueue: exec gen task ' + task.name);
      task.gen().then(function () {
        DEBUG && infoLog('TaskQueue: onThen for gen task ' + task.name, {
          stackIdx: stackIdx,
          queueStackSize: _this2._queueStack.length
        });
        stackItem.popable = true;
        _this2.hasTasksToProcess() && _this2._onMoreTasks();
      }).catch(function (ex) {
        setTimeout(function () {
          ex.message = `TaskQueue: Error resolving Promise in task ${task.name}: ${ex.message}`;
          throw ex;
        }, 0);
      });
    }
  }]);
}();
var _default = exports.default = TaskQueue;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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