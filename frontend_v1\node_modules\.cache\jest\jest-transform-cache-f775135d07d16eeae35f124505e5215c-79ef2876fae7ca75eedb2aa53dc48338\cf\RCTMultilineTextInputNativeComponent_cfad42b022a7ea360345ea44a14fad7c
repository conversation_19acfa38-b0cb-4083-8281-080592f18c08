58ccc0ffb37979dd462cb42e0e02f741
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.__INTERNAL_VIEW_CONFIG = exports.Commands = void 0;
var NativeComponentRegistry = _interopRequireWildcard(require("../../NativeComponent/NativeComponentRegistry"));
var _codegenNativeCommands = _interopRequireDefault(require("../../Utilities/codegenNativeCommands"));
var _RCTTextInputViewConfig = _interopRequireDefault(require("./RCTTextInputViewConfig"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var Commands = exports.Commands = (0, _codegenNativeCommands.default)({
  supportedCommands: ['focus', 'blur', 'setTextAndSelection']
});
var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = Object.assign({
  uiViewClassName: 'RCTMultilineTextInputView'
}, _RCTTextInputViewConfig.default, {
  validAttributes: Object.assign({}, _RCTTextInputViewConfig.default.validAttributes, {
    dataDetectorTypes: true
  })
});
var MultilineTextInputNativeComponent = NativeComponentRegistry.get('RCTMultilineTextInputView', function () {
  return __INTERNAL_VIEW_CONFIG;
});
var _default = exports.default = MultilineTextInputNativeComponent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJOYXRpdmVDb21wb25lbnRSZWdpc3RyeSIsIl9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkIiwicmVxdWlyZSIsIl9jb2RlZ2VuTmF0aXZlQ29tbWFuZHMiLCJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0IiwiX1JDVFRleHRJbnB1dFZpZXdDb25maWciLCJlIiwidCIsIldlYWtNYXAiLCJyIiwibiIsIl9fZXNNb2R1bGUiLCJvIiwiaSIsImYiLCJfX3Byb3RvX18iLCJkZWZhdWx0IiwiaGFzIiwiZ2V0Iiwic2V0IiwiX3QiLCJoYXNPd25Qcm9wZXJ0eSIsImNhbGwiLCJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImdldE93blByb3BlcnR5RGVzY3JpcHRvciIsIkNvbW1hbmRzIiwiZXhwb3J0cyIsImNvZGVnZW5OYXRpdmVDb21tYW5kcyIsInN1cHBvcnRlZENvbW1hbmRzIiwiX19JTlRFUk5BTF9WSUVXX0NPTkZJRyIsImFzc2lnbiIsInVpVmlld0NsYXNzTmFtZSIsIlJDVFRleHRJbnB1dFZpZXdDb25maWciLCJ2YWxpZEF0dHJpYnV0ZXMiLCJkYXRhRGV0ZWN0b3JUeXBlcyIsIk11bHRpbGluZVRleHRJbnB1dE5hdGl2ZUNvbXBvbmVudCIsIl9kZWZhdWx0Il0sInNvdXJjZXMiOlsiUkNUTXVsdGlsaW5lVGV4dElucHV0TmF0aXZlQ29tcG9uZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqXG4gKiBAZmxvdyBzdHJpY3QtbG9jYWxcbiAqIEBmb3JtYXRcbiAqL1xuXG5pbXBvcnQgdHlwZSB7SG9zdENvbXBvbmVudH0gZnJvbSAnLi4vLi4vLi4vc3JjL3ByaXZhdGUvdHlwZXMvSG9zdENvbXBvbmVudCc7XG5pbXBvcnQgdHlwZSB7UGFydGlhbFZpZXdDb25maWd9IGZyb20gJy4uLy4uL1JlbmRlcmVyL3NoaW1zL1JlYWN0TmF0aXZlVHlwZXMnO1xuaW1wb3J0IHR5cGUge1RleHRJbnB1dE5hdGl2ZUNvbW1hbmRzfSBmcm9tICcuL1RleHRJbnB1dE5hdGl2ZUNvbW1hbmRzJztcblxuaW1wb3J0ICogYXMgTmF0aXZlQ29tcG9uZW50UmVnaXN0cnkgZnJvbSAnLi4vLi4vTmF0aXZlQ29tcG9uZW50L05hdGl2ZUNvbXBvbmVudFJlZ2lzdHJ5JztcbmltcG9ydCBjb2RlZ2VuTmF0aXZlQ29tbWFuZHMgZnJvbSAnLi4vLi4vVXRpbGl0aWVzL2NvZGVnZW5OYXRpdmVDb21tYW5kcyc7XG5pbXBvcnQgUkNUVGV4dElucHV0Vmlld0NvbmZpZyBmcm9tICcuL1JDVFRleHRJbnB1dFZpZXdDb25maWcnO1xuXG50eXBlIE5hdGl2ZVR5cGUgPSBIb3N0Q29tcG9uZW50PHsuLi59PjtcblxudHlwZSBOYXRpdmVDb21tYW5kcyA9IFRleHRJbnB1dE5hdGl2ZUNvbW1hbmRzPE5hdGl2ZVR5cGU+O1xuXG5leHBvcnQgY29uc3QgQ29tbWFuZHM6IE5hdGl2ZUNvbW1hbmRzID0gY29kZWdlbk5hdGl2ZUNvbW1hbmRzPE5hdGl2ZUNvbW1hbmRzPih7XG4gIHN1cHBvcnRlZENvbW1hbmRzOiBbJ2ZvY3VzJywgJ2JsdXInLCAnc2V0VGV4dEFuZFNlbGVjdGlvbiddLFxufSk7XG5cbmV4cG9ydCBjb25zdCBfX0lOVEVSTkFMX1ZJRVdfQ09ORklHOiBQYXJ0aWFsVmlld0NvbmZpZyA9IHtcbiAgdWlWaWV3Q2xhc3NOYW1lOiAnUkNUTXVsdGlsaW5lVGV4dElucHV0VmlldycsXG4gIC4uLlJDVFRleHRJbnB1dFZpZXdDb25maWcsXG4gIHZhbGlkQXR0cmlidXRlczoge1xuICAgIC4uLlJDVFRleHRJbnB1dFZpZXdDb25maWcudmFsaWRBdHRyaWJ1dGVzLFxuICAgIGRhdGFEZXRlY3RvclR5cGVzOiB0cnVlLFxuICB9LFxufTtcblxuY29uc3QgTXVsdGlsaW5lVGV4dElucHV0TmF0aXZlQ29tcG9uZW50OiBIb3N0Q29tcG9uZW50PHsuLi59PiA9XG4gIE5hdGl2ZUNvbXBvbmVudFJlZ2lzdHJ5LmdldDx7Li4ufT4oXG4gICAgJ1JDVE11bHRpbGluZVRleHRJbnB1dFZpZXcnLFxuICAgICgpID0+IF9fSU5URVJOQUxfVklFV19DT05GSUcsXG4gICk7XG5cbi8vIGZsb3dsaW50LW5leHQtbGluZSB1bmNsZWFyLXR5cGU6b2ZmXG5leHBvcnQgZGVmYXVsdCAoKE11bHRpbGluZVRleHRJbnB1dE5hdGl2ZUNvbXBvbmVudDogYW55KTogSG9zdENvbXBvbmVudDx7Li4ufT4pO1xuIl0sIm1hcHBpbmdzIjoiOzs7OztBQWNBLElBQUFBLHVCQUFBLEdBQUFDLHVCQUFBLENBQUFDLE9BQUE7QUFDQSxJQUFBQyxzQkFBQSxHQUFBQyxzQkFBQSxDQUFBRixPQUFBO0FBQ0EsSUFBQUcsdUJBQUEsR0FBQUQsc0JBQUEsQ0FBQUYsT0FBQTtBQUE4RCxTQUFBRCx3QkFBQUssQ0FBQSxFQUFBQyxDQUFBLDZCQUFBQyxPQUFBLE1BQUFDLENBQUEsT0FBQUQsT0FBQSxJQUFBRSxDQUFBLE9BQUFGLE9BQUEsWUFBQVAsdUJBQUEsWUFBQUEsd0JBQUFLLENBQUEsRUFBQUMsQ0FBQSxTQUFBQSxDQUFBLElBQUFELENBQUEsSUFBQUEsQ0FBQSxDQUFBSyxVQUFBLFNBQUFMLENBQUEsTUFBQU0sQ0FBQSxFQUFBQyxDQUFBLEVBQUFDLENBQUEsS0FBQUMsU0FBQSxRQUFBQyxPQUFBLEVBQUFWLENBQUEsaUJBQUFBLENBQUEsdUJBQUFBLENBQUEseUJBQUFBLENBQUEsU0FBQVEsQ0FBQSxNQUFBRixDQUFBLEdBQUFMLENBQUEsR0FBQUcsQ0FBQSxHQUFBRCxDQUFBLFFBQUFHLENBQUEsQ0FBQUssR0FBQSxDQUFBWCxDQUFBLFVBQUFNLENBQUEsQ0FBQU0sR0FBQSxDQUFBWixDQUFBLEdBQUFNLENBQUEsQ0FBQU8sR0FBQSxDQUFBYixDQUFBLEVBQUFRLENBQUEsY0FBQU0sRUFBQSxJQUFBZCxDQUFBLGdCQUFBYyxFQUFBLE9BQUFDLGNBQUEsQ0FBQUMsSUFBQSxDQUFBaEIsQ0FBQSxFQUFBYyxFQUFBLE9BQUFQLENBQUEsSUFBQUQsQ0FBQSxHQUFBVyxNQUFBLENBQUFDLGNBQUEsS0FBQUQsTUFBQSxDQUFBRSx3QkFBQSxDQUFBbkIsQ0FBQSxFQUFBYyxFQUFBLE9BQUFQLENBQUEsQ0FBQUssR0FBQSxJQUFBTCxDQUFBLENBQUFNLEdBQUEsSUFBQVAsQ0FBQSxDQUFBRSxDQUFBLEVBQUFNLEVBQUEsRUFBQVAsQ0FBQSxJQUFBQyxDQUFBLENBQUFNLEVBQUEsSUFBQWQsQ0FBQSxDQUFBYyxFQUFBLFdBQUFOLENBQUEsS0FBQVIsQ0FBQSxFQUFBQyxDQUFBO0FBTXZELElBQU1tQixRQUF3QixHQUFBQyxPQUFBLENBQUFELFFBQUEsR0FBRyxJQUFBRSw4QkFBcUIsRUFBaUI7RUFDNUVDLGlCQUFpQixFQUFFLENBQUMsT0FBTyxFQUFFLE1BQU0sRUFBRSxxQkFBcUI7QUFDNUQsQ0FBQyxDQUFDO0FBRUssSUFBTUMsc0JBQXlDLEdBQUFILE9BQUEsQ0FBQUcsc0JBQUEsR0FBQVAsTUFBQSxDQUFBUSxNQUFBO0VBQ3BEQyxlQUFlLEVBQUU7QUFBMkIsR0FDekNDLCtCQUFzQjtFQUN6QkMsZUFBZSxFQUFBWCxNQUFBLENBQUFRLE1BQUEsS0FDVkUsK0JBQXNCLENBQUNDLGVBQWU7SUFDekNDLGlCQUFpQixFQUFFO0VBQUk7QUFDeEIsRUFDRjtBQUVELElBQU1DLGlDQUF1RCxHQUMzRHBDLHVCQUF1QixDQUFDa0IsR0FBRyxDQUN6QiwyQkFBMkIsRUFDM0I7RUFBQSxPQUFNWSxzQkFBc0I7QUFBQSxDQUM5QixDQUFDO0FBQUMsSUFBQU8sUUFBQSxHQUFBVixPQUFBLENBQUFYLE9BQUEsR0FHYW9CLGlDQUFpQyIsImlnbm9yZUxpc3QiOltdfQ==