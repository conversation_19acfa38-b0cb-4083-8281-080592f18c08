/**
 * Monitoring Integration Service
 * Centralized service to integrate all monitoring, analytics, and performance tracking
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { logger } from '../utils/logger';

import { analyticsService } from './analyticsService';
import { unifiedErrorHandlingService } from './unifiedErrorHandling';
import { performanceMonitor } from './performanceMonitor';

export interface MonitoringConfig {
  enableAnalytics: boolean;
  enablePerformanceMonitoring: boolean;
  enableErrorReporting: boolean;
  enableCrashReporting: boolean;
  enableUserBehaviorTracking: boolean;
  enablePerformanceAlerts: boolean;
  enableRealTimeMonitoring: boolean;
  privacyCompliant: boolean;
  debugMode: boolean;
}

export interface MonitoringMetrics {
  performance: {
    appStartTime: number;
    renderTime: number;
    memoryUsage: number;
    networkLatency: number;
    errorRate: number;
    crashRate: number;
  };
  user: {
    sessionDuration: number;
    screenViews: number;
    interactions: number;
    conversionRate: number;
  };
  business: {
    bookingCompletionRate: number;
    userRetentionRate: number;
    averageSessionValue: number;
    featureUsageRate: Record<string, number>;
  };
}

class MonitoringIntegrationService {
  private config: MonitoringConfig;
  private isInitialized = false;
  private metrics: Partial<MonitoringMetrics> = {};
  private alertThresholds = {
    errorRate: 0.05, // 5%
    crashRate: 0.01, // 1%
    slowRenderThreshold: 16, // 16ms (60fps)
    memoryThreshold: 100 * 1024 * 1024, // 100MB
    networkLatencyThreshold: 2000, // 2 seconds
  };

  constructor() {
    this.config = {
      enableAnalytics: true,
      enablePerformanceMonitoring: true,
      enableErrorReporting: true,
      enableCrashReporting: true,
      enableUserBehaviorTracking: true,
      enablePerformanceAlerts: true,
      enableRealTimeMonitoring: true,
      privacyCompliant: true,
      debugMode: __DEV__,
    };
  }

  /**
   * Initialize all monitoring services
   */
  async initialize(customConfig?: Partial<MonitoringConfig>): Promise<void> {
    try {
      // Merge custom config
      this.config = { ...this.config, ...customConfig };

      logger.info('🔧 Initializing Monitoring Integration Service', {
        config: this.config,
      });

      // Initialize analytics service (already initialized as singleton)
      if (this.config.enableAnalytics) {
        // Analytics service is already initialized as singleton
        // Just verify it's working by setting privacy settings
        await analyticsService.updatePrivacySettings({
          analyticsEnabled: true,
          performanceTrackingEnabled: this.config.enablePerformanceMonitoring,
          crashReportingEnabled: this.config.enableCrashReporting,
          userBehaviorTrackingEnabled: this.config.enableUserBehaviorTracking,
        });
        logger.info('✅ Analytics Service configured');
      }

      // Initialize performance monitoring (already initialized as singleton)
      if (this.config.enablePerformanceMonitoring) {
        // Performance monitor is already initialized as singleton
        // Just start monitoring if needed
        logger.info('✅ Performance Monitor ready');
      }

      // Initialize unified error handling service
      if (this.config.enableErrorReporting) {
        await unifiedErrorHandlingService.initialize();
        await unifiedErrorHandlingService.updateConfig({
          enableUserFeedback: true,
          enableAnalytics: this.config.enableAnalytics,
          enableCrashReporting: this.config.enableCrashReporting,
        });
        logger.info('✅ Error Reporting Service initialized');
      }

      // Enhanced error handling service is already initialized as singleton
      // No additional configuration needed
      logger.info('✅ Enhanced Error Handling Service ready');

      // Set up performance alerts
      if (this.config.enablePerformanceAlerts) {
        this.setupPerformanceAlerts();
      }

      // Set up real-time monitoring
      if (this.config.enableRealTimeMonitoring) {
        this.setupRealTimeMonitoring();
      }

      this.isInitialized = true;
      logger.info('🎉 Monitoring Integration Service fully initialized');

      // Track initialization
      if (this.config.enableAnalytics) {
        await analyticsService.trackEvent('monitoring_initialized', 'system', {
          config: this.config,
          timestamp: Date.now(),
        });
      }
    } catch (error) {
      logger.error(
        '❌ Failed to initialize Monitoring Integration Service',
        error,
      );
      throw error;
    }
  }

  /**
   * Track application startup performance
   */
  async trackAppStartup(startTime: number): Promise<void> {
    if (!this.isInitialized) return;

    const appStartTime = Date.now() - startTime;

    // Track with performance monitor
    performanceMonitor.trackAppStart(appStartTime);

    // Track with analytics
    if (this.config.enableAnalytics) {
      await analyticsService.trackPerformance(
        'app_startup_time',
        appStartTime,
        {
          timestamp: Date.now(),
          deviceInfo: await this.getDeviceInfo(),
        },
      );
    }

    // Update metrics
    this.metrics.performance = {
      ...this.metrics.performance,
      appStartTime,
    };

    logger.info('📊 App startup tracked', { appStartTime });
  }

  /**
   * Track screen navigation
   */
  async trackScreenNavigation(
    fromScreen: string,
    toScreen: string,
    navigationTime: number,
  ): Promise<void> {
    if (!this.isInitialized) return;

    // Track with performance monitor
    performanceMonitor.trackNavigation(fromScreen, toScreen, navigationTime);

    // Track with analytics
    if (this.config.enableAnalytics) {
      await analyticsService.trackNavigation(fromScreen, toScreen, {
        navigationTime,
        timestamp: Date.now(),
      });
    }

    logger.debug('🧭 Navigation tracked', {
      fromScreen,
      toScreen,
      navigationTime,
    });
  }

  /**
   * Track user interaction
   */
  async trackUserInteraction(
    action: string,
    element: string,
    properties?: Record<string, any>,
  ): Promise<void> {
    if (!this.isInitialized || !this.config.enableUserBehaviorTracking) return;

    // Track with analytics
    if (this.config.enableAnalytics) {
      await analyticsService.trackEvent(action, 'interaction', {
        element,
        timestamp: Date.now(),
        ...properties,
      });
    }

    // Track with performance monitor
    performanceMonitor.trackUserInteraction(action, 0, {
      element,
      ...properties,
    });

    logger.debug('👆 User interaction tracked', {
      action,
      element,
      properties,
    });
  }

  /**
   * Track error occurrence
   */
  async trackError(
    error: Error,
    context: string,
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium',
  ): Promise<void> {
    if (!this.isInitialized) return;

    // Track with unified error handling service
    if (this.config.enableErrorReporting) {
      await unifiedErrorHandlingService.handleError(
        error,
        {
          component: 'MonitoringIntegration',
          action: context,
          severity: severity as any
        }
      );
    }

    // Track with analytics
    if (this.config.enableAnalytics) {
      await analyticsService.trackError(error, context, { severity });
    }

    // Track with performance monitor
    performanceMonitor.trackError(error, { context, severity });

    logger.error('🚨 Error tracked', {
      error: error.message,
      context,
      severity,
    });
  }

  /**
   * Track performance metric
   */
  async trackPerformanceMetric(
    metricName: string,
    value: number,
    properties?: Record<string, any>,
  ): Promise<void> {
    if (!this.isInitialized || !this.config.enablePerformanceMonitoring) return;

    // Track with performance monitor
    performanceMonitor.addMetric({
      name: metricName,
      value,
      timestamp: Date.now(),
      category: 'performance',
      metadata: properties,
    });

    // Track with analytics
    if (this.config.enableAnalytics) {
      await analyticsService.trackPerformance(metricName, value, properties);
    }

    // Check for performance alerts
    this.checkPerformanceThresholds(metricName, value);

    logger.debug('📊 Performance metric tracked', {
      metricName,
      value,
      properties,
    });
  }

  /**
   * Get current monitoring metrics
   */
  getMetrics(): Partial<MonitoringMetrics> {
    return this.metrics;
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary() {
    return performanceMonitor.getMetrics();
  }

  /**
   * Setup performance alerts
   */
  private setupPerformanceAlerts(): void {
    // Monitor for slow renders
    performanceMonitor.onSlowRender(
      (renderTime: number, componentName: string) => {
        if (renderTime > this.alertThresholds.slowRenderThreshold) {
          this.triggerAlert('slow_render', {
            renderTime,
            componentName,
            threshold: this.alertThresholds.slowRenderThreshold,
          });
        }
      },
    );

    logger.info('⚠️ Performance alerts configured');
  }

  /**
   * Setup real-time monitoring
   */
  private setupRealTimeMonitoring(): void {
    // Monitor memory usage
    setInterval(() => {
      if (global.performance && global.performance.memory) {
        const memoryUsage = global.performance.memory.usedJSHeapSize;
        if (memoryUsage > this.alertThresholds.memoryThreshold) {
          this.triggerAlert('high_memory_usage', {
            memoryUsage,
            threshold: this.alertThresholds.memoryThreshold,
          });
        }
      }
    }, 30000); // Check every 30 seconds

    logger.info('📡 Real-time monitoring configured');
  }

  /**
   * Check performance thresholds
   */
  private checkPerformanceThresholds(metricName: string, value: number): void {
    const thresholds: Record<string, number> = {
      render_time: this.alertThresholds.slowRenderThreshold,
      network_latency: this.alertThresholds.networkLatencyThreshold,
      error_rate: this.alertThresholds.errorRate,
      crash_rate: this.alertThresholds.crashRate,
    };

    const threshold = thresholds[metricName];
    if (threshold && value > threshold) {
      this.triggerAlert('performance_threshold_exceeded', {
        metricName,
        value,
        threshold,
      });
    }
  }

  /**
   * Trigger performance alert
   */
  private triggerAlert(alertType: string, data: Record<string, any>): void {
    logger.warn(`⚠️ Performance Alert: ${alertType}`, data);

    if (this.config.enableAnalytics) {
      analyticsService.trackEvent('performance_alert', 'alert', {
        alertType,
        ...data,
        timestamp: Date.now(),
      });
    }
  }

  /**
   * Get device information
   */
  private async getDeviceInfo(): Promise<Record<string, any>> {
    // This would typically use react-native-device-info
    return {
      platform: 'react-native',
      timestamp: Date.now(),
    };
  }

  /**
   * Shutdown monitoring services
   */
  async shutdown(): Promise<void> {
    logger.info('🔄 Shutting down Monitoring Integration Service');

    // Flush any pending analytics
    if (this.config.enableAnalytics) {
      await analyticsService.flush();
    }

    this.isInitialized = false;
    logger.info('✅ Monitoring Integration Service shutdown complete');
  }
}

// Export singleton instance
export const monitoringIntegrationService = new MonitoringIntegrationService();
export default monitoringIntegrationService;
