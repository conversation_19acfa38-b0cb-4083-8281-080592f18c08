{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_reactNative", "_ThemeContext", "_imageAccessibilityUtils", "_Text", "_jsxRuntime", "_excluded", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "LazyImage", "exports", "_ref", "source", "placeholder", "fallback", "width", "height", "aspectRatio", "_ref$lazy", "lazy", "_ref$threshold", "threshold", "_ref$fadeInDuration", "fadeInDuration", "onLoadStart", "onLoadEnd", "onError", "containerStyle", "imageStyle", "_ref$testID", "testID", "accessibilityLabel", "accessibilityHint", "_ref$accessibilityRol", "accessibilityRole", "alt", "_ref$isDecorative", "isDecorative", "imageContext", "_ref$validateAccessib", "validateAccessibility", "__DEV__", "imageProps", "_objectWithoutProperties2", "_useTheme", "useTheme", "isDark", "colors", "enhancedAccessibilityProps", "React", "useMemo", "generateImageAccessibilityProps", "accessible", "importantForAccessibility", "_useState", "useState", "_useState2", "_slicedToArray2", "isInView", "setIsInView", "_useState3", "_useState4", "isLoading", "setIsLoading", "_useState5", "_useState6", "isLoaded", "setIsLoaded", "_useState7", "_useState8", "<PERSON><PERSON><PERSON><PERSON>", "setHasError", "_useState9", "_useState0", "metrics", "setMetrics", "containerRef", "useRef", "fadeAnim", "Animated", "Value", "current", "observerRef", "screenWidth", "Dimensions", "calculatedWidth", "calculatedHeight", "useEffect", "immediateLoadTimer", "setTimeout", "preloadTimer", "uri", "Image", "prefetch", "catch", "clearTimeout", "handleLayout", "useCallback", "event", "y", "nativeEvent", "layout", "screenHeight", "handleLoadStart", "startTime", "performance", "now", "wasLazy", "handleLoadEnd", "endTime", "prev", "assign", "loadTime", "timing", "toValue", "duration", "useNativeDriver", "start", "handleError", "error", "getImageSource", "containerStyles", "styles", "container", "backgroundColor", "gray", "filter", "Boolean", "imageStyles", "image", "renderPlaceholder", "jsx", "View", "style", "children", "placeholder<PERSON><PERSON><PERSON>", "Text", "placeholderText", "color", "renderError", "jsxs", "<PERSON><PERSON><PERSON><PERSON>", "errorText", "semantic", "ref", "onLayout", "imageContainer", "opacity", "metricsOverlay", "metricsText", "toFixed", "StyleSheet", "create", "position", "overflow", "borderRadius", "resizeMode", "top", "left", "right", "bottom", "alignItems", "justifyContent", "paddingHorizontal", "paddingVertical", "fontSize", "fontWeight", "textAlign", "_default"], "sources": ["LazyImage.tsx"], "sourcesContent": ["/**\n * Lazy Loading Image Component\n *\n * Implements REC-PERF-001: Lazy loading for offscreen images.\n * Provides performance-optimized image loading with intersection observer.\n *\n * Features:\n * - Intersection Observer API for viewport detection\n * - Progressive loading with placeholder\n * - Error handling and fallback images\n * - Accessibility support\n * - Performance metrics tracking\n * - Memory management\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport React, { useState, useRef, useEffect, useCallback } from 'react';\nimport {\n  View,\n  Image,\n  Dimensions,\n  ImageProps,\n  StyleSheet,\n  ViewStyle,\n  ImageStyle,\n  Animated,\n} from 'react-native';\n\nimport { useTheme } from '../../contexts/ThemeContext';\nimport { HyperMinimalistTheme } from '../../design-system/HyperMinimalistTheme';\nimport {\n  ImageContext,\n  ImageAccessibilityProps,\n  generateImageAccessibilityProps,\n} from '../../utils/imageAccessibilityUtils';\nimport { Text } from '../atoms/Text';\n\n// Lazy image props interface\nexport interface LazyImageProps extends Omit<ImageProps, 'source'> {\n  source: { uri: string } | number;\n  placeholder?: { uri: string } | number;\n  fallback?: { uri: string } | number;\n  width?: number;\n  height?: number;\n  aspectRatio?: number;\n  lazy?: boolean;\n  threshold?: number;\n  fadeInDuration?: number;\n  onLoadStart?: () => void;\n  onLoadEnd?: () => void;\n  onError?: (error: any) => void;\n  containerStyle?: ViewStyle;\n  imageStyle?: ImageStyle;\n  testID?: string;\n  // Enhanced accessibility props for WCAG compliance\n  accessibilityLabel?: string;\n  accessibilityHint?: string;\n  accessibilityRole?: 'image' | 'imagebutton' | 'none';\n  alt?: string; // Alternative text for screen readers\n  isDecorative?: boolean; // Mark decorative images to hide from screen readers\n\n  // New comprehensive accessibility features\n  imageContext?: ImageContext; // Context for automatic accessibility generation\n  validateAccessibility?: boolean; // Enable accessibility validation in development\n}\n\n// Performance metrics interface\ninterface LoadMetrics {\n  startTime: number;\n  endTime?: number;\n  loadTime?: number;\n  wasLazy: boolean;\n  imageSize?: { width: number; height: number };\n}\n\nexport const LazyImage: React.FC<LazyImageProps> = ({\n  source,\n  placeholder,\n  fallback,\n  width,\n  height,\n  aspectRatio,\n  lazy = true,\n  threshold = 0.1,\n  fadeInDuration = 300,\n  onLoadStart,\n  onLoadEnd,\n  onError,\n  containerStyle,\n  imageStyle,\n  testID = 'lazy-image',\n  // Enhanced accessibility props\n  accessibilityLabel,\n  accessibilityHint,\n  accessibilityRole = 'image',\n  alt,\n  isDecorative = false,\n  // New accessibility features\n  imageContext,\n  validateAccessibility = __DEV__,\n  ...imageProps\n}) => {\n  const { isDark, colors } = useTheme();\n\n  // Generate enhanced accessibility props if imageContext is provided\n  const enhancedAccessibilityProps = React.useMemo(() => {\n    if (imageContext) {\n      return generateImageAccessibilityProps(\n        imageContext,\n        alt || accessibilityLabel,\n      );\n    }\n\n    // Fallback to manual accessibility props\n    return {\n      accessibilityLabel: alt || accessibilityLabel,\n      accessibilityHint,\n      accessibilityRole,\n      accessible: !isDecorative,\n      importantForAccessibility: isDecorative ? 'no' : 'yes',\n    } as ImageAccessibilityProps;\n  }, [\n    imageContext,\n    alt,\n    accessibilityLabel,\n    accessibilityHint,\n    accessibilityRole,\n    isDecorative,\n  ]);\n\n  const [isInView, setIsInView] = useState(!lazy);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isLoaded, setIsLoaded] = useState(false);\n  const [hasError, setHasError] = useState(false);\n  const [metrics, setMetrics] = useState<LoadMetrics | null>(null);\n\n  const containerRef = useRef<View>(null);\n  const fadeAnim = useRef(new Animated.Value(0)).current;\n  const observerRef = useRef<IntersectionObserver | null>(null);\n\n  // Calculate dimensions\n  const screenWidth = Dimensions.get('window').width;\n  const calculatedWidth = width || screenWidth;\n  const calculatedHeight =\n    height || (aspectRatio ? calculatedWidth / aspectRatio : calculatedWidth);\n\n  // Enhanced React Native lazy loading with viewport detection\n  useEffect(() => {\n    if (!lazy) {\n      setIsInView(true);\n      return;\n    }\n\n    // Immediate load for images that are likely above the fold\n    const immediateLoadTimer = setTimeout(() => {\n      setIsInView(true);\n    }, 100); // Small delay to allow layout to settle\n\n    // Preload images that are likely to be viewed soon\n    const preloadTimer = setTimeout(() => {\n      if (typeof source === 'object' && source.uri) {\n        // Preload the image to improve loading speed when it becomes visible\n        Image.prefetch(source.uri).catch(() => {\n          // Silently handle prefetch errors\n        });\n      }\n    }, 500); // Increased delay for preloading\n\n    return () => {\n      clearTimeout(immediateLoadTimer);\n      clearTimeout(preloadTimer);\n    };\n  }, [lazy, source]);\n\n  // Layout-based viewport detection\n  const handleLayout = useCallback(\n    (event: any) => {\n      if (!lazy || isInView) return;\n\n      const { y } = event.nativeEvent.layout;\n      const screenHeight = Dimensions.get('window').height;\n\n      // Load if image is within viewport + threshold\n      if (y < screenHeight * (1 + threshold)) {\n        setIsInView(true);\n      }\n    },\n    [lazy, isInView, threshold],\n  );\n\n  // Handle image load start\n  const handleLoadStart = useCallback(() => {\n    setIsLoading(true);\n    setHasError(false);\n\n    const startTime = performance.now();\n    setMetrics({\n      startTime,\n      wasLazy: lazy,\n    });\n\n    onLoadStart?.();\n  }, [lazy, onLoadStart]);\n\n  // Handle image load success\n  const handleLoadEnd = useCallback(() => {\n    setIsLoading(false);\n    setIsLoaded(true);\n\n    // Update metrics\n    const endTime = performance.now();\n    setMetrics(prev =>\n      prev\n        ? {\n            ...prev,\n            endTime,\n            loadTime: endTime - prev.startTime,\n          }\n        : null,\n    );\n\n    // Fade in animation\n    Animated.timing(fadeAnim, {\n      toValue: 1,\n      duration: fadeInDuration,\n      useNativeDriver: true,\n    }).start();\n\n    onLoadEnd?.();\n  }, [fadeAnim, fadeInDuration, onLoadEnd]);\n\n  // Handle image load error\n  const handleError = useCallback(\n    (error: any) => {\n      setIsLoading(false);\n      setHasError(true);\n\n      // Update metrics\n      const endTime = performance.now();\n      setMetrics(prev =>\n        prev\n          ? {\n              ...prev,\n              endTime,\n              loadTime: endTime - prev.startTime,\n            }\n          : null,\n      );\n\n      onError?.(error);\n    },\n    [onError],\n  );\n\n  // Get image source\n  const getImageSource = () => {\n    if (hasError && fallback) {\n      return fallback;\n    }\n    if (!isInView && placeholder) {\n      return placeholder;\n    }\n    return source;\n  };\n\n  // Container styles\n  const containerStyles: ViewStyle = [\n    styles.container,\n    {\n      width: calculatedWidth,\n      height: calculatedHeight,\n      backgroundColor: colors.gray[100],\n    },\n    containerStyle,\n  ].filter(Boolean) as ViewStyle;\n\n  // Image styles\n  const imageStyles: ImageStyle = [\n    styles.image,\n    {\n      width: calculatedWidth,\n      height: calculatedHeight,\n    },\n    imageStyle,\n  ].filter(Boolean) as ImageStyle;\n\n  // Render placeholder\n  const renderPlaceholder = () => (\n    <View style={[styles.placeholder, { backgroundColor: colors.gray[100] }]}>\n      <View\n        style={[\n          styles.placeholderContent,\n          { backgroundColor: colors.gray[200] },\n        ]}>\n        <Text style={[styles.placeholderText, { color: colors.gray[500] }]}>\n          {isLoading ? 'Loading...' : hasError ? 'Failed to load' : 'Image'}\n        </Text>\n      </View>\n    </View>\n  );\n\n  // Render error state\n  const renderError = () => (\n    <View\n      style={[styles.errorContainer, { backgroundColor: colors.gray[100] }]}>\n      <Text style={[styles.errorText, { color: colors.semantic.error }]}>\n        Failed to load image\n      </Text>\n      {fallback && (\n        <Image\n          source={fallback}\n          style={imageStyles}\n          onLoadStart={handleLoadStart}\n          onLoadEnd={handleLoadEnd}\n          onError={handleError}\n          // Enhanced WCAG-compliant accessibility props\n          {...enhancedAccessibilityProps}\n          {...imageProps}\n        />\n      )}\n    </View>\n  );\n\n  return (\n    <View\n      ref={containerRef}\n      style={containerStyles}\n      testID={testID}\n      accessibilityRole=\"image\"\n      accessibilityLabel={imageProps.accessibilityLabel || 'Image'}\n      onLayout={handleLayout}>\n      {/* Placeholder/Loading state */}\n      {(!isInView || isLoading || (!isLoaded && !hasError)) &&\n        renderPlaceholder()}\n\n      {/* Error state */}\n      {hasError && !fallback && renderError()}\n\n      {/* Main image */}\n      {isInView && !hasError && (\n        <Animated.View\n          style={[\n            styles.imageContainer,\n            {\n              opacity: fadeAnim,\n            },\n          ]}>\n          <Image\n            source={getImageSource()}\n            style={imageStyles}\n            onLoadStart={handleLoadStart}\n            onLoadEnd={handleLoadEnd}\n            onError={handleError}\n            // Enhanced WCAG-compliant accessibility props\n            {...enhancedAccessibilityProps}\n            {...imageProps}\n          />\n        </Animated.View>\n      )}\n\n      {/* Performance metrics (development only) */}\n      {__DEV__ && metrics && metrics.loadTime && (\n        <View style={styles.metricsOverlay}>\n          <Text style={styles.metricsText}>\n            {metrics.loadTime.toFixed(0)}ms\n            {metrics.wasLazy ? ' (lazy)' : ''}\n          </Text>\n        </View>\n      )}\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    position: 'relative',\n    overflow: 'hidden',\n    borderRadius: 8,\n  },\n\n  image: {\n    resizeMode: 'cover',\n  },\n\n  imageContainer: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n  },\n\n  placeholder: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n\n  placeholderContent: {\n    paddingHorizontal: 12,\n    paddingVertical: 8,\n    borderRadius: 6,\n  },\n\n  placeholderText: {\n    fontSize: 13,\n    fontWeight: '500',\n  },\n\n  errorContainer: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n\n  errorText: {\n    fontSize: 13,\n    fontWeight: '500',\n    textAlign: 'center',\n  },\n\n  metricsOverlay: {\n    position: 'absolute',\n    top: 4,\n    right: 4,\n    backgroundColor: 'rgba(0, 0, 0, 0.7)',\n    paddingHorizontal: 6,\n    paddingVertical: 2,\n    borderRadius: 4,\n  },\n\n  metricsText: {\n    color: '#FFFFFF',\n    fontSize: 10,\n    fontWeight: '500',\n  },\n});\n\nexport default LazyImage;\n"], "mappings": ";;;;;;;AAkBA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAWA,IAAAE,aAAA,GAAAF,OAAA;AAEA,IAAAG,wBAAA,GAAAH,OAAA;AAKA,IAAAI,KAAA,GAAAJ,OAAA;AAAqC,IAAAK,WAAA,GAAAL,OAAA;AAAA,IAAAM,SAAA;AAAA,SAAAP,wBAAAQ,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAV,uBAAA,YAAAA,wBAAAQ,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAwC9B,IAAMmB,SAAmC,GAAAC,OAAA,CAAAD,SAAA,GAAG,SAAtCA,SAAmCA,CAAAE,IAAA,EA0B1C;EAAA,IAzBJC,MAAM,GAAAD,IAAA,CAANC,MAAM;IACNC,WAAW,GAAAF,IAAA,CAAXE,WAAW;IACXC,QAAQ,GAAAH,IAAA,CAARG,QAAQ;IACRC,KAAK,GAAAJ,IAAA,CAALI,KAAK;IACLC,MAAM,GAAAL,IAAA,CAANK,MAAM;IACNC,WAAW,GAAAN,IAAA,CAAXM,WAAW;IAAAC,SAAA,GAAAP,IAAA,CACXQ,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG,IAAI,GAAAA,SAAA;IAAAE,cAAA,GAAAT,IAAA,CACXU,SAAS;IAATA,SAAS,GAAAD,cAAA,cAAG,GAAG,GAAAA,cAAA;IAAAE,mBAAA,GAAAX,IAAA,CACfY,cAAc;IAAdA,cAAc,GAAAD,mBAAA,cAAG,GAAG,GAAAA,mBAAA;IACpBE,WAAW,GAAAb,IAAA,CAAXa,WAAW;IACXC,SAAS,GAAAd,IAAA,CAATc,SAAS;IACTC,OAAO,GAAAf,IAAA,CAAPe,OAAO;IACPC,cAAc,GAAAhB,IAAA,CAAdgB,cAAc;IACdC,UAAU,GAAAjB,IAAA,CAAViB,UAAU;IAAAC,WAAA,GAAAlB,IAAA,CACVmB,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,YAAY,GAAAA,WAAA;IAErBE,kBAAkB,GAAApB,IAAA,CAAlBoB,kBAAkB;IAClBC,iBAAiB,GAAArB,IAAA,CAAjBqB,iBAAiB;IAAAC,qBAAA,GAAAtB,IAAA,CACjBuB,iBAAiB;IAAjBA,iBAAiB,GAAAD,qBAAA,cAAG,OAAO,GAAAA,qBAAA;IAC3BE,GAAG,GAAAxB,IAAA,CAAHwB,GAAG;IAAAC,iBAAA,GAAAzB,IAAA,CACH0B,YAAY;IAAZA,YAAY,GAAAD,iBAAA,cAAG,KAAK,GAAAA,iBAAA;IAEpBE,YAAY,GAAA3B,IAAA,CAAZ2B,YAAY;IAAAC,qBAAA,GAAA5B,IAAA,CACZ6B,qBAAqB;IAArBA,qBAAqB,GAAAD,qBAAA,cAAGE,OAAO,GAAAF,qBAAA;IAC5BG,UAAU,OAAAC,yBAAA,CAAA5C,OAAA,EAAAY,IAAA,EAAAvB,SAAA;EAEb,IAAAwD,SAAA,GAA2B,IAAAC,sBAAQ,EAAC,CAAC;IAA7BC,MAAM,GAAAF,SAAA,CAANE,MAAM;IAAEC,MAAM,GAAAH,SAAA,CAANG,MAAM;EAGtB,IAAMC,0BAA0B,GAAGC,cAAK,CAACC,OAAO,CAAC,YAAM;IACrD,IAAIZ,YAAY,EAAE;MAChB,OAAO,IAAAa,wDAA+B,EACpCb,YAAY,EACZH,GAAG,IAAIJ,kBACT,CAAC;IACH;IAGA,OAAO;MACLA,kBAAkB,EAAEI,GAAG,IAAIJ,kBAAkB;MAC7CC,iBAAiB,EAAjBA,iBAAiB;MACjBE,iBAAiB,EAAjBA,iBAAiB;MACjBkB,UAAU,EAAE,CAACf,YAAY;MACzBgB,yBAAyB,EAAEhB,YAAY,GAAG,IAAI,GAAG;IACnD,CAAC;EACH,CAAC,EAAE,CACDC,YAAY,EACZH,GAAG,EACHJ,kBAAkB,EAClBC,iBAAiB,EACjBE,iBAAiB,EACjBG,YAAY,CACb,CAAC;EAEF,IAAAiB,SAAA,GAAgC,IAAAC,eAAQ,EAAC,CAACpC,IAAI,CAAC;IAAAqC,UAAA,OAAAC,eAAA,CAAA1D,OAAA,EAAAuD,SAAA;IAAxCI,QAAQ,GAAAF,UAAA;IAAEG,WAAW,GAAAH,UAAA;EAC5B,IAAAI,UAAA,GAAkC,IAAAL,eAAQ,EAAC,KAAK,CAAC;IAAAM,UAAA,OAAAJ,eAAA,CAAA1D,OAAA,EAAA6D,UAAA;IAA1CE,SAAS,GAAAD,UAAA;IAAEE,YAAY,GAAAF,UAAA;EAC9B,IAAAG,UAAA,GAAgC,IAAAT,eAAQ,EAAC,KAAK,CAAC;IAAAU,UAAA,OAAAR,eAAA,CAAA1D,OAAA,EAAAiE,UAAA;IAAxCE,QAAQ,GAAAD,UAAA;IAAEE,WAAW,GAAAF,UAAA;EAC5B,IAAAG,UAAA,GAAgC,IAAAb,eAAQ,EAAC,KAAK,CAAC;IAAAc,UAAA,OAAAZ,eAAA,CAAA1D,OAAA,EAAAqE,UAAA;IAAxCE,QAAQ,GAAAD,UAAA;IAAEE,WAAW,GAAAF,UAAA;EAC5B,IAAAG,UAAA,GAA8B,IAAAjB,eAAQ,EAAqB,IAAI,CAAC;IAAAkB,UAAA,OAAAhB,eAAA,CAAA1D,OAAA,EAAAyE,UAAA;IAAzDE,OAAO,GAAAD,UAAA;IAAEE,UAAU,GAAAF,UAAA;EAE1B,IAAMG,YAAY,GAAG,IAAAC,aAAM,EAAO,IAAI,CAAC;EACvC,IAAMC,QAAQ,GAAG,IAAAD,aAAM,EAAC,IAAIE,qBAAQ,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO;EACtD,IAAMC,WAAW,GAAG,IAAAL,aAAM,EAA8B,IAAI,CAAC;EAG7D,IAAMM,WAAW,GAAGC,uBAAU,CAACnF,GAAG,CAAC,QAAQ,CAAC,CAACc,KAAK;EAClD,IAAMsE,eAAe,GAAGtE,KAAK,IAAIoE,WAAW;EAC5C,IAAMG,gBAAgB,GACpBtE,MAAM,KAAKC,WAAW,GAAGoE,eAAe,GAAGpE,WAAW,GAAGoE,eAAe,CAAC;EAG3E,IAAAE,gBAAS,EAAC,YAAM;IACd,IAAI,CAACpE,IAAI,EAAE;MACTwC,WAAW,CAAC,IAAI,CAAC;MACjB;IACF;IAGA,IAAM6B,kBAAkB,GAAGC,UAAU,CAAC,YAAM;MAC1C9B,WAAW,CAAC,IAAI,CAAC;IACnB,CAAC,EAAE,GAAG,CAAC;IAGP,IAAM+B,YAAY,GAAGD,UAAU,CAAC,YAAM;MACpC,IAAI,OAAO7E,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAAC+E,GAAG,EAAE;QAE5CC,kBAAK,CAACC,QAAQ,CAACjF,MAAM,CAAC+E,GAAG,CAAC,CAACG,KAAK,CAAC,YAAM,CAEvC,CAAC,CAAC;MACJ;IACF,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,YAAM;MACXC,YAAY,CAACP,kBAAkB,CAAC;MAChCO,YAAY,CAACL,YAAY,CAAC;IAC5B,CAAC;EACH,CAAC,EAAE,CAACvE,IAAI,EAAEP,MAAM,CAAC,CAAC;EAGlB,IAAMoF,YAAY,GAAG,IAAAC,kBAAW,EAC9B,UAACC,KAAU,EAAK;IACd,IAAI,CAAC/E,IAAI,IAAIuC,QAAQ,EAAE;IAEvB,IAAQyC,CAAC,GAAKD,KAAK,CAACE,WAAW,CAACC,MAAM,CAA9BF,CAAC;IACT,IAAMG,YAAY,GAAGlB,uBAAU,CAACnF,GAAG,CAAC,QAAQ,CAAC,CAACe,MAAM;IAGpD,IAAImF,CAAC,GAAGG,YAAY,IAAI,CAAC,GAAGjF,SAAS,CAAC,EAAE;MACtCsC,WAAW,CAAC,IAAI,CAAC;IACnB;EACF,CAAC,EACD,CAACxC,IAAI,EAAEuC,QAAQ,EAAErC,SAAS,CAC5B,CAAC;EAGD,IAAMkF,eAAe,GAAG,IAAAN,kBAAW,EAAC,YAAM;IACxClC,YAAY,CAAC,IAAI,CAAC;IAClBQ,WAAW,CAAC,KAAK,CAAC;IAElB,IAAMiC,SAAS,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;IACnC/B,UAAU,CAAC;MACT6B,SAAS,EAATA,SAAS;MACTG,OAAO,EAAExF;IACX,CAAC,CAAC;IAEFK,WAAW,YAAXA,WAAW,CAAG,CAAC;EACjB,CAAC,EAAE,CAACL,IAAI,EAAEK,WAAW,CAAC,CAAC;EAGvB,IAAMoF,aAAa,GAAG,IAAAX,kBAAW,EAAC,YAAM;IACtClC,YAAY,CAAC,KAAK,CAAC;IACnBI,WAAW,CAAC,IAAI,CAAC;IAGjB,IAAM0C,OAAO,GAAGJ,WAAW,CAACC,GAAG,CAAC,CAAC;IACjC/B,UAAU,CAAC,UAAAmC,IAAI;MAAA,OACbA,IAAI,GAAAxG,MAAA,CAAAyG,MAAA,KAEKD,IAAI;QACPD,OAAO,EAAPA,OAAO;QACPG,QAAQ,EAAEH,OAAO,GAAGC,IAAI,CAACN;MAAS,KAEpC,IAAI;IAAA,CACV,CAAC;IAGDzB,qBAAQ,CAACkC,MAAM,CAACnC,QAAQ,EAAE;MACxBoC,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAE5F,cAAc;MACxB6F,eAAe,EAAE;IACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IAEV5F,SAAS,YAATA,SAAS,CAAG,CAAC;EACf,CAAC,EAAE,CAACqD,QAAQ,EAAEvD,cAAc,EAAEE,SAAS,CAAC,CAAC;EAGzC,IAAM6F,WAAW,GAAG,IAAArB,kBAAW,EAC7B,UAACsB,KAAU,EAAK;IACdxD,YAAY,CAAC,KAAK,CAAC;IACnBQ,WAAW,CAAC,IAAI,CAAC;IAGjB,IAAMsC,OAAO,GAAGJ,WAAW,CAACC,GAAG,CAAC,CAAC;IACjC/B,UAAU,CAAC,UAAAmC,IAAI;MAAA,OACbA,IAAI,GAAAxG,MAAA,CAAAyG,MAAA,KAEKD,IAAI;QACPD,OAAO,EAAPA,OAAO;QACPG,QAAQ,EAAEH,OAAO,GAAGC,IAAI,CAACN;MAAS,KAEpC,IAAI;IAAA,CACV,CAAC;IAED9E,OAAO,YAAPA,OAAO,CAAG6F,KAAK,CAAC;EAClB,CAAC,EACD,CAAC7F,OAAO,CACV,CAAC;EAGD,IAAM8F,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAC3B,IAAIlD,QAAQ,IAAIxD,QAAQ,EAAE;MACxB,OAAOA,QAAQ;IACjB;IACA,IAAI,CAAC4C,QAAQ,IAAI7C,WAAW,EAAE;MAC5B,OAAOA,WAAW;IACpB;IACA,OAAOD,MAAM;EACf,CAAC;EAGD,IAAM6G,eAA0B,GAAG,CACjCC,MAAM,CAACC,SAAS,EAChB;IACE5G,KAAK,EAAEsE,eAAe;IACtBrE,MAAM,EAAEsE,gBAAgB;IACxBsC,eAAe,EAAE7E,MAAM,CAAC8E,IAAI,CAAC,GAAG;EAClC,CAAC,EACDlG,cAAc,CACf,CAACmG,MAAM,CAACC,OAAO,CAAc;EAG9B,IAAMC,WAAuB,GAAG,CAC9BN,MAAM,CAACO,KAAK,EACZ;IACElH,KAAK,EAAEsE,eAAe;IACtBrE,MAAM,EAAEsE;EACV,CAAC,EACD1D,UAAU,CACX,CAACkG,MAAM,CAACC,OAAO,CAAe;EAG/B,IAAMG,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA;IAAA,OACrB,IAAA/I,WAAA,CAAAgJ,GAAA,EAACpJ,YAAA,CAAAqJ,IAAI;MAACC,KAAK,EAAE,CAACX,MAAM,CAAC7G,WAAW,EAAE;QAAE+G,eAAe,EAAE7E,MAAM,CAAC8E,IAAI,CAAC,GAAG;MAAE,CAAC,CAAE;MAAAS,QAAA,EACvE,IAAAnJ,WAAA,CAAAgJ,GAAA,EAACpJ,YAAA,CAAAqJ,IAAI;QACHC,KAAK,EAAE,CACLX,MAAM,CAACa,kBAAkB,EACzB;UAAEX,eAAe,EAAE7E,MAAM,CAAC8E,IAAI,CAAC,GAAG;QAAE,CAAC,CACrC;QAAAS,QAAA,EACF,IAAAnJ,WAAA,CAAAgJ,GAAA,EAACjJ,KAAA,CAAAsJ,IAAI;UAACH,KAAK,EAAE,CAACX,MAAM,CAACe,eAAe,EAAE;YAAEC,KAAK,EAAE3F,MAAM,CAAC8E,IAAI,CAAC,GAAG;UAAE,CAAC,CAAE;UAAAS,QAAA,EAChExE,SAAS,GAAG,YAAY,GAAGQ,QAAQ,GAAG,gBAAgB,GAAG;QAAO,CAC7D;MAAC,CACH;IAAC,CACH,CAAC;EAAA,CACR;EAGD,IAAMqE,WAAW,GAAG,SAAdA,WAAWA,CAAA;IAAA,OACf,IAAAxJ,WAAA,CAAAyJ,IAAA,EAAC7J,YAAA,CAAAqJ,IAAI;MACHC,KAAK,EAAE,CAACX,MAAM,CAACmB,cAAc,EAAE;QAAEjB,eAAe,EAAE7E,MAAM,CAAC8E,IAAI,CAAC,GAAG;MAAE,CAAC,CAAE;MAAAS,QAAA,GACtE,IAAAnJ,WAAA,CAAAgJ,GAAA,EAACjJ,KAAA,CAAAsJ,IAAI;QAACH,KAAK,EAAE,CAACX,MAAM,CAACoB,SAAS,EAAE;UAAEJ,KAAK,EAAE3F,MAAM,CAACgG,QAAQ,CAACxB;QAAM,CAAC,CAAE;QAAAe,QAAA,EAAC;MAEnE,CAAM,CAAC,EACNxH,QAAQ,IACP,IAAA3B,WAAA,CAAAgJ,GAAA,EAACpJ,YAAA,CAAA6G,KAAK,EAAAtF,MAAA,CAAAyG,MAAA;QACJnG,MAAM,EAAEE,QAAS;QACjBuH,KAAK,EAAEL,WAAY;QACnBxG,WAAW,EAAE+E,eAAgB;QAC7B9E,SAAS,EAAEmF,aAAc;QACzBlF,OAAO,EAAE4F;MAAY,GAEjBtE,0BAA0B,EAC1BN,UAAU,CACf,CACF;IAAA,CACG,CAAC;EAAA,CACR;EAED,OACE,IAAAvD,WAAA,CAAAyJ,IAAA,EAAC7J,YAAA,CAAAqJ,IAAI;IACHY,GAAG,EAAEpE,YAAa;IAClByD,KAAK,EAAEZ,eAAgB;IACvB3F,MAAM,EAAEA,MAAO;IACfI,iBAAiB,EAAC,OAAO;IACzBH,kBAAkB,EAAEW,UAAU,CAACX,kBAAkB,IAAI,OAAQ;IAC7DkH,QAAQ,EAAEjD,YAAa;IAAAsC,QAAA,GAEtB,CAAC,CAAC5E,QAAQ,IAAII,SAAS,IAAK,CAACI,QAAQ,IAAI,CAACI,QAAS,KAClD4D,iBAAiB,CAAC,CAAC,EAGpB5D,QAAQ,IAAI,CAACxD,QAAQ,IAAI6H,WAAW,CAAC,CAAC,EAGtCjF,QAAQ,IAAI,CAACY,QAAQ,IACpB,IAAAnF,WAAA,CAAAgJ,GAAA,EAACpJ,YAAA,CAAAgG,QAAQ,CAACqD,IAAI;MACZC,KAAK,EAAE,CACLX,MAAM,CAACwB,cAAc,EACrB;QACEC,OAAO,EAAErE;MACX,CAAC,CACD;MAAAwD,QAAA,EACF,IAAAnJ,WAAA,CAAAgJ,GAAA,EAACpJ,YAAA,CAAA6G,KAAK,EAAAtF,MAAA,CAAAyG,MAAA;QACJnG,MAAM,EAAE4G,cAAc,CAAC,CAAE;QACzBa,KAAK,EAAEL,WAAY;QACnBxG,WAAW,EAAE+E,eAAgB;QAC7B9E,SAAS,EAAEmF,aAAc;QACzBlF,OAAO,EAAE4F;MAAY,GAEjBtE,0BAA0B,EAC1BN,UAAU,CACf;IAAC,CACW,CAChB,EAGAD,OAAO,IAAIiC,OAAO,IAAIA,OAAO,CAACsC,QAAQ,IACrC,IAAA7H,WAAA,CAAAgJ,GAAA,EAACpJ,YAAA,CAAAqJ,IAAI;MAACC,KAAK,EAAEX,MAAM,CAAC0B,cAAe;MAAAd,QAAA,EACjC,IAAAnJ,WAAA,CAAAyJ,IAAA,EAAC1J,KAAA,CAAAsJ,IAAI;QAACH,KAAK,EAAEX,MAAM,CAAC2B,WAAY;QAAAf,QAAA,GAC7B5D,OAAO,CAACsC,QAAQ,CAACsC,OAAO,CAAC,CAAC,CAAC,EAAC,IAC7B,EAAC5E,OAAO,CAACiC,OAAO,GAAG,SAAS,GAAG,EAAE;MAAA,CAC7B;IAAC,CACH,CACP;EAAA,CACG,CAAC;AAEX,CAAC;AAED,IAAMe,MAAM,GAAG6B,uBAAU,CAACC,MAAM,CAAC;EAC/B7B,SAAS,EAAE;IACT8B,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,QAAQ;IAClBC,YAAY,EAAE;EAChB,CAAC;EAED1B,KAAK,EAAE;IACL2B,UAAU,EAAE;EACd,CAAC;EAEDV,cAAc,EAAE;IACdO,QAAQ,EAAE,UAAU;IACpBI,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV,CAAC;EAEDnJ,WAAW,EAAE;IACX4I,QAAQ,EAAE,UAAU;IACpBI,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EAED3B,kBAAkB,EAAE;IAClB4B,iBAAiB,EAAE,EAAE;IACrBC,eAAe,EAAE,CAAC;IAClBT,YAAY,EAAE;EAChB,CAAC;EAEDlB,eAAe,EAAE;IACf4B,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd,CAAC;EAEDzB,cAAc,EAAE;IACdY,QAAQ,EAAE,UAAU;IACpBI,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EAEDpB,SAAS,EAAE;IACTuB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,KAAK;IACjBC,SAAS,EAAE;EACb,CAAC;EAEDnB,cAAc,EAAE;IACdK,QAAQ,EAAE,UAAU;IACpBI,GAAG,EAAE,CAAC;IACNE,KAAK,EAAE,CAAC;IACRnC,eAAe,EAAE,oBAAoB;IACrCuC,iBAAiB,EAAE,CAAC;IACpBC,eAAe,EAAE,CAAC;IAClBT,YAAY,EAAE;EAChB,CAAC;EAEDN,WAAW,EAAE;IACXX,KAAK,EAAE,SAAS;IAChB2B,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAAC,IAAAE,QAAA,GAAA9J,OAAA,CAAAX,OAAA,GAEYU,SAAS", "ignoreList": []}