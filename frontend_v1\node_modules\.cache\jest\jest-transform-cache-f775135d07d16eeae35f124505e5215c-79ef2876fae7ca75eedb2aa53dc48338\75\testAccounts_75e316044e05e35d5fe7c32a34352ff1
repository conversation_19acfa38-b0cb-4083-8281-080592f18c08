0600bc932698847e3a9b5c740d6b939e
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getTestAccountsByRole = exports.getTestAccountsByCity = exports.getTestAccountsByCategory = exports.getRandomTestAccount = exports.findTestAccountByEmail = exports.TEST_ACCOUNTS_SUMMARY = exports.SKINCARE_PROVIDERS = exports.SALON_PROVIDERS = exports.QUICK_LOGIN_ACCOUNTS = exports.NAIL_SERVICES_PROVIDERS = exports.MASSAGE_PROVIDERS = exports.LASH_SERVICES_PROVIDERS = exports.CUSTOMER_TEST_ACCOUNTS = exports.BRAIDING_PROVIDERS = exports.BARBER_PROVIDERS = exports.ALL_TEST_ACCOUNTS = exports.ALL_SERVICE_PROVIDERS = void 0;
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var CUSTOMER_TEST_ACCOUNTS = exports.CUSTOMER_TEST_ACCOUNTS = [{
  id: 'customer_1',
  email: '<EMAIL>',
  password: 'testpass123',
  role: 'customer',
  firstName: 'Test',
  lastName: 'User',
  description: 'Basic test customer account'
}, {
  id: 'customer_2',
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'customer',
  firstName: 'Sarah',
  lastName: 'Johnson',
  description: 'Premium customer account'
}, {
  id: 'customer_3',
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'customer',
  firstName: 'Michael',
  lastName: 'Chen',
  description: 'Regular customer account'
}, {
  id: 'customer_4',
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'customer',
  firstName: 'Emily',
  lastName: 'Davis',
  description: 'Frequent customer account'
}, {
  id: 'customer_5',
  email: '<EMAIL>',
  password: 'testpass123',
  role: 'customer',
  firstName: 'Vierla',
  lastName: 'Test',
  description: 'Vierla test customer account'
}, {
  id: 'customer_6',
  email: '<EMAIL>',
  password: 'testpass123',
  role: 'customer',
  firstName: 'Test',
  lastName: 'Customer',
  description: 'Basic test customer account'
}, {
  id: 'provider_test',
  email: '<EMAIL>',
  password: 'testpass123',
  role: 'service_provider',
  firstName: 'Test',
  lastName: 'Provider',
  businessName: 'Test Provider Business',
  category: 'General',
  city: 'Ottawa',
  description: 'General test provider account'
}];
var BARBER_PROVIDERS = exports.BARBER_PROVIDERS = [{
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Marcus',
  lastName: 'Johnson',
  businessName: 'Elite Cuts Barbershop',
  category: 'Barber',
  city: 'Ottawa',
  description: 'Traditional barbering, classic cuts, and beard grooming'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'David',
  lastName: 'Thompson',
  businessName: 'Classic Barber Co',
  category: 'Barber',
  city: 'Toronto',
  description: "Traditional and contemporary men's grooming services"
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'James',
  lastName: 'Wilson',
  businessName: 'Modern Cuts Barbershop',
  category: 'Barber',
  city: 'Ottawa',
  description: 'Modern barbering techniques with classic service'
}];
var SALON_PROVIDERS = exports.SALON_PROVIDERS = [{
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Emma',
  lastName: 'Rodriguez',
  businessName: 'Trendy Cuts Salon',
  category: 'Salon',
  city: 'Ottawa',
  description: 'Creative hair designs and color specialists'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Lisa',
  lastName: 'Wang',
  businessName: 'Luxe Hair Boutique',
  category: 'Salon',
  city: 'Ottawa',
  description: 'Premium hair care and styling services'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Sarah',
  lastName: 'Mitchell',
  businessName: 'Bella Hair Studio',
  category: 'Salon',
  city: 'Toronto',
  description: 'Full-service salon specializing in color and styling'
}];
var NAIL_SERVICES_PROVIDERS = exports.NAIL_SERVICES_PROVIDERS = [{
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider1',
  lastName: 'Nail',
  businessName: 'Nail Services Studio 1',
  category: 'Nail Services',
  city: 'Ottawa',
  description: 'Manicures, pedicures, nail art, and nail care'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider2',
  lastName: 'Nail',
  businessName: 'Nail Services Studio 2',
  category: 'Nail Services',
  city: 'Ottawa',
  description: 'Manicures, pedicures, nail art, and nail care'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider3',
  lastName: 'Nail',
  businessName: 'Nail Services Studio 3',
  category: 'Nail Services',
  city: 'Toronto',
  description: 'Manicures, pedicures, nail art, and nail care'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider4',
  lastName: 'Nail',
  businessName: 'Nail Services Studio 4',
  category: 'Nail Services',
  city: 'Toronto',
  description: 'Manicures, pedicures, nail art, and nail care'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider5',
  lastName: 'Nail',
  businessName: 'Nail Services Studio 5',
  category: 'Nail Services',
  city: 'Ottawa',
  description: 'Manicures, pedicures, nail art, and nail care'
}];
var LASH_SERVICES_PROVIDERS = exports.LASH_SERVICES_PROVIDERS = [{
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider1',
  lastName: 'Lash',
  businessName: 'Lash Services Studio 1',
  category: 'Lash Services',
  city: 'Ottawa',
  description: 'Eyelash extensions, lifts, tinting, and brow services'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider2',
  lastName: 'Lash',
  businessName: 'Lash Services Studio 2',
  category: 'Lash Services',
  city: 'Ottawa',
  description: 'Eyelash extensions, lifts, tinting, and brow services'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider3',
  lastName: 'Lash',
  businessName: 'Lash Services Studio 3',
  category: 'Lash Services',
  city: 'Toronto',
  description: 'Eyelash extensions, lifts, tinting, and brow services'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider4',
  lastName: 'Lash',
  businessName: 'Lash Services Studio 4',
  category: 'Lash Services',
  city: 'Toronto',
  description: 'Eyelash extensions, lifts, tinting, and brow services'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider5',
  lastName: 'Lash',
  businessName: 'Lash Services Studio 5',
  category: 'Lash Services',
  city: 'Ottawa',
  description: 'Eyelash extensions, lifts, tinting, and brow services'
}];
var BRAIDING_PROVIDERS = exports.BRAIDING_PROVIDERS = [{
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider1',
  lastName: 'Braiding',
  businessName: 'Braiding Studio 1',
  category: 'Braiding',
  city: 'Ottawa',
  description: 'Professional braiding and protective styling'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider2',
  lastName: 'Braiding',
  businessName: 'Braiding Studio 2',
  category: 'Braiding',
  city: 'Ottawa',
  description: 'Professional braiding and protective styling'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider3',
  lastName: 'Braiding',
  businessName: 'Braiding Studio 3',
  category: 'Braiding',
  city: 'Toronto',
  description: 'Professional braiding and protective styling'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider4',
  lastName: 'Braiding',
  businessName: 'Braiding Studio 4',
  category: 'Braiding',
  city: 'Toronto',
  description: 'Professional braiding and protective styling'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider5',
  lastName: 'Braiding',
  businessName: 'Braiding Studio 5',
  category: 'Braiding',
  city: 'Ottawa',
  description: 'Professional braiding and protective styling'
}];
var MASSAGE_PROVIDERS = exports.MASSAGE_PROVIDERS = [{
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider1',
  lastName: 'Massage',
  businessName: 'Massage Studio 1',
  category: 'Massage',
  city: 'Ottawa',
  description: 'Therapeutic and relaxation massage services'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider2',
  lastName: 'Massage',
  businessName: 'Massage Studio 2',
  category: 'Massage',
  city: 'Ottawa',
  description: 'Therapeutic and relaxation massage services'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider3',
  lastName: 'Massage',
  businessName: 'Massage Studio 3',
  category: 'Massage',
  city: 'Toronto',
  description: 'Therapeutic and relaxation massage services'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider4',
  lastName: 'Massage',
  businessName: 'Massage Studio 4',
  category: 'Massage',
  city: 'Toronto',
  description: 'Therapeutic and relaxation massage services'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider5',
  lastName: 'Massage',
  businessName: 'Massage Studio 5',
  category: 'Massage',
  city: 'Ottawa',
  description: 'Therapeutic and relaxation massage services'
}];
var SKINCARE_PROVIDERS = exports.SKINCARE_PROVIDERS = [{
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider1',
  lastName: 'Skincare',
  businessName: 'Skincare Studio 1',
  category: 'Skincare',
  city: 'Ottawa',
  description: 'Facial treatments, skincare consultations, and beauty treatments'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider2',
  lastName: 'Skincare',
  businessName: 'Skincare Studio 2',
  category: 'Skincare',
  city: 'Ottawa',
  description: 'Facial treatments, skincare consultations, and beauty treatments'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider3',
  lastName: 'Skincare',
  businessName: 'Skincare Studio 3',
  category: 'Skincare',
  city: 'Toronto',
  description: 'Facial treatments, skincare consultations, and beauty treatments'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider4',
  lastName: 'Skincare',
  businessName: 'Skincare Studio 4',
  category: 'Skincare',
  city: 'Toronto',
  description: 'Facial treatments, skincare consultations, and beauty treatments'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider5',
  lastName: 'Skincare',
  businessName: 'Skincare Studio 5',
  category: 'Skincare',
  city: 'Ottawa',
  description: 'Facial treatments, skincare consultations, and beauty treatments'
}];
var ALL_SERVICE_PROVIDERS = exports.ALL_SERVICE_PROVIDERS = [].concat(BARBER_PROVIDERS, SALON_PROVIDERS, NAIL_SERVICES_PROVIDERS, LASH_SERVICES_PROVIDERS, BRAIDING_PROVIDERS, MASSAGE_PROVIDERS, SKINCARE_PROVIDERS);
var ALL_TEST_ACCOUNTS = exports.ALL_TEST_ACCOUNTS = [].concat(CUSTOMER_TEST_ACCOUNTS, (0, _toConsumableArray2.default)(ALL_SERVICE_PROVIDERS));
var getTestAccountsByRole = exports.getTestAccountsByRole = function getTestAccountsByRole(role) {
  return ALL_TEST_ACCOUNTS.filter(function (account) {
    return account.role === role;
  });
};
var getTestAccountsByCategory = exports.getTestAccountsByCategory = function getTestAccountsByCategory(category) {
  return ALL_SERVICE_PROVIDERS.filter(function (account) {
    return account.category === category;
  });
};
var getTestAccountsByCity = exports.getTestAccountsByCity = function getTestAccountsByCity(city) {
  return ALL_TEST_ACCOUNTS.filter(function (account) {
    return account.city === city;
  });
};
var getRandomTestAccount = exports.getRandomTestAccount = function getRandomTestAccount(role) {
  var accounts = role ? getTestAccountsByRole(role) : ALL_TEST_ACCOUNTS;
  var randomIndex = Math.floor(Math.random() * accounts.length);
  return accounts[randomIndex];
};
var findTestAccountByEmail = exports.findTestAccountByEmail = function findTestAccountByEmail(email) {
  return ALL_TEST_ACCOUNTS.find(function (account) {
    return account.email === email;
  });
};
var QUICK_LOGIN_ACCOUNTS = exports.QUICK_LOGIN_ACCOUNTS = {
  CUSTOMER: CUSTOMER_TEST_ACCOUNTS[0],
  BARBER_PROVIDER: BARBER_PROVIDERS[0],
  SALON_PROVIDER: SALON_PROVIDERS[0],
  NAIL_PROVIDER: NAIL_SERVICES_PROVIDERS[0],
  LASH_PROVIDER: LASH_SERVICES_PROVIDERS[0],
  BRAIDING_PROVIDER: BRAIDING_PROVIDERS[0],
  MASSAGE_PROVIDER: MASSAGE_PROVIDERS[0],
  SKINCARE_PROVIDER: SKINCARE_PROVIDERS[0]
};
var TEST_ACCOUNTS_SUMMARY = exports.TEST_ACCOUNTS_SUMMARY = {
  total: ALL_TEST_ACCOUNTS.length,
  customers: CUSTOMER_TEST_ACCOUNTS.length,
  providers: ALL_SERVICE_PROVIDERS.length,
  categories: {
    Barber: BARBER_PROVIDERS.length,
    Salon: SALON_PROVIDERS.length,
    'Nail Services': NAIL_SERVICES_PROVIDERS.length,
    'Lash Services': LASH_SERVICES_PROVIDERS.length,
    Braiding: BRAIDING_PROVIDERS.length,
    Massage: MASSAGE_PROVIDERS.length,
    Skincare: SKINCARE_PROVIDERS.length
  },
  cities: {
    Ottawa: getTestAccountsByCity('Ottawa').length,
    Toronto: getTestAccountsByCity('Toronto').length
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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