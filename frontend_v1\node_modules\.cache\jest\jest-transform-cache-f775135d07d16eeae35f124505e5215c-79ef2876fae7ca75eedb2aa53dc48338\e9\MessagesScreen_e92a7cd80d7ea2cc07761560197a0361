b5768c78fe6739ef9705dc183bc7fc71
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MessagesScreen = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _vectorIcons = require("@expo/vector-icons");
var _native = require("@react-navigation/native");
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _SafeAreaWrapper = require("../components/ui/SafeAreaWrapper");
var _messagingService = require("../services/messagingService");
var _ThemeContext = require("../contexts/ThemeContext");
var _responsiveUtils = require("../utils/responsiveUtils");
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var getAvatarColor = function getAvatarColor(name) {
  var avatarColors = ['#5A7A63', '#4A6B52', '#3A5B42', '#10B981', '#F59E0B', '#3B82F6'];
  var index = name.charCodeAt(0) % avatarColors.length;
  return avatarColors[index];
};
var formatLastMessageTime = function formatLastMessageTime(timestamp) {
  var now = new Date();
  var messageTime = new Date(timestamp);
  var diffInMinutes = Math.floor((now.getTime() - messageTime.getTime()) / (1000 * 60));
  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m`;
  var diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours}h`;
  var diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) return `${diffInDays}d`;
  return messageTime.toLocaleDateString();
};
var MessagesScreen = exports.MessagesScreen = function MessagesScreen() {
  var _useTheme = (0, _ThemeContext.useTheme)(),
    colors = _useTheme.colors;
  var styles = createStyles(colors);
  var navigation = (0, _native.useNavigation)();
  var _useState = (0, _react.useState)([]),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    conversations = _useState2[0],
    setConversations = _useState2[1];
  var _useState3 = (0, _react.useState)(false),
    _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
    refreshing = _useState4[0],
    setRefreshing = _useState4[1];
  var _useState5 = (0, _react.useState)(true),
    _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
    loading = _useState6[0],
    setLoading = _useState6[1];
  var _useState7 = (0, _react.useState)(null),
    _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
    error = _useState8[0],
    setError = _useState8[1];
  (0, _react.useEffect)(function () {
    loadConversations();
  }, []);
  var loadConversations = function () {
    var _ref = (0, _asyncToGenerator2.default)(function* () {
      setLoading(true);
      setError(null);
      try {
        var response = yield _messagingService.messagingService.getConversations();
        var transformedConversations = response.conversations.map(function (conv) {
          var _conv$last_message, _conv$last_message2;
          return {
            id: conv.id.toString(),
            participantName: conv.participants.filter(function (p) {
              return p.id !== conv.current_user_id;
            }).map(function (p) {
              return `${p.first_name} ${p.last_name}`;
            }).join(', ') || 'Unknown',
            lastMessage: ((_conv$last_message = conv.last_message) == null ? void 0 : _conv$last_message.content) || 'No messages yet',
            timestamp: ((_conv$last_message2 = conv.last_message) == null ? void 0 : _conv$last_message2.created_at) || conv.updated_at,
            unreadCount: conv.unread_count || 0
          };
        });
        setConversations(transformedConversations);
      } catch (err) {
        console.error('Error loading conversations:', err);
        setError('Failed to load conversations. Please try again.');
      } finally {
        setLoading(false);
      }
    });
    return function loadConversations() {
      return _ref.apply(this, arguments);
    };
  }();
  var handleRefresh = function () {
    var _ref2 = (0, _asyncToGenerator2.default)(function* () {
      setRefreshing(true);
      setError(null);
      yield loadConversations();
      setRefreshing(false);
    });
    return function handleRefresh() {
      return _ref2.apply(this, arguments);
    };
  }();
  var handleConversationPress = function handleConversationPress(conversation) {
    setConversations(function (prev) {
      return prev.map(function (conv) {
        return conv.id === conversation.id ? Object.assign({}, conv, {
          unreadCount: 0
        }) : conv;
      });
    });
    navigation.navigate('Conversation', {
      conversationId: conversation.id,
      participantName: conversation.participantName
    });
  };
  var renderEmptyState = function renderEmptyState() {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.emptyState,
      children: [(0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
        name: "chatbubbles-outline",
        size: 64,
        color: colors.sage200
      }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: styles.emptyStateText,
        children: "No conversations yet"
      }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: styles.emptyStateSubtext,
        children: "Your messages with providers will appear here"
      })]
    });
  };
  var renderConversation = function renderConversation(_ref3) {
    var item = _ref3.item;
    var hasUnread = item.unreadCount > 0;
    var isOnline = Math.random() > 0.5;
    return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
      onPress: function onPress() {
        return handleConversationPress(item);
      },
      testID: `conversation-${item.id}`,
      accessibilityLabel: `Conversation with ${item.participantName}`,
      accessibilityHint: "Tap to open chat",
      style: [styles.conversationItem, hasUnread && styles.unreadConversation],
      activeOpacity: 0.7,
      children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.conversationContent,
        children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.avatarContainer,
          children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: [styles.avatar, {
              backgroundColor: getAvatarColor(item.participantName)
            }],
            children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: styles.avatarText,
              children: item.participantName.charAt(0).toUpperCase()
            })
          }), isOnline && (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: styles.onlineIndicator
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.conversationInfo,
          children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.conversationHeader,
            children: [(0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: [styles.participantName, hasUnread && styles.unreadText],
              numberOfLines: 1,
              children: item.participantName
            }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: styles.timestamp,
              children: formatLastMessageTime(item.timestamp)
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.lastMessageContainer,
            children: [(0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: [styles.lastMessage, hasUnread && styles.unreadText],
              numberOfLines: 2,
              children: item.lastMessage
            }), hasUnread && (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: styles.unreadBadge,
              children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
                style: styles.unreadCount,
                children: item.unreadCount > 99 ? '99+' : item.unreadCount
              })
            })]
          })]
        })]
      })
    });
  };
  return (0, _jsxRuntime.jsxs)(_SafeAreaWrapper.SafeAreaScreen, {
    backgroundColor: colors.background.primary,
    statusBarStyle: "dark-content",
    respectNotch: true,
    respectGestures: true,
    testID: "messages-screen",
    children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.header,
      children: [(0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: styles.title,
        children: "Messages"
      }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: styles.newMessageButton,
        testID: "new-message-button",
        accessibilityLabel: "Start new conversation",
        accessibilityRole: "button",
        children: (0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
          name: "create-outline",
          size: 24,
          color: colors.sage400
        })
      })]
    }), loading && conversations.length === 0 ? (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.loadingContainer,
      children: [(0, _jsxRuntime.jsx)(_reactNative.ActivityIndicator, {
        size: "large",
        color: colors.sage400
      }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: styles.loadingText,
        children: "Loading conversations..."
      })]
    }) : (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
      data: conversations,
      renderItem: renderConversation,
      keyExtractor: function keyExtractor(item) {
        return item.id;
      },
      style: styles.conversationsList,
      contentContainerStyle: styles.conversationsContent,
      showsVerticalScrollIndicator: false,
      refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
        refreshing: refreshing,
        onRefresh: handleRefresh,
        colors: [colors.sage400],
        tintColor: colors.sage400
      }),
      ListEmptyComponent: renderEmptyState
    }), error && (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.errorContainer,
      children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: styles.errorText,
        children: "Error loading conversations. Please try again."
      })
    })]
  });
};
var createStyles = function createStyles(colors) {
  return _reactNative.StyleSheet.create({
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: (0, _responsiveUtils.getResponsiveSpacing)(20),
      paddingVertical: (0, _responsiveUtils.getResponsiveSpacing)(16),
      backgroundColor: colors.background.primary,
      borderBottomWidth: 1,
      borderBottomColor: colors.sage100
    },
    title: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(24),
      fontWeight: '700',
      color: colors.text.primary
    },
    newMessageButton: {
      padding: (0, _responsiveUtils.getResponsiveSpacing)(8),
      borderRadius: (0, _responsiveUtils.getResponsiveSpacing)(8)
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: (0, _responsiveUtils.getResponsiveSpacing)(40)
    },
    loadingText: {
      marginTop: (0, _responsiveUtils.getResponsiveSpacing)(12),
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(16),
      color: colors.text.secondary
    },
    conversationsList: {
      flex: 1
    },
    conversationsContent: {
      paddingVertical: (0, _responsiveUtils.getResponsiveSpacing)(8)
    },
    conversationItem: {
      backgroundColor: colors.background.primary,
      paddingHorizontal: (0, _responsiveUtils.getResponsiveSpacing)(20),
      paddingVertical: (0, _responsiveUtils.getResponsiveSpacing)(16),
      borderBottomWidth: 1,
      borderBottomColor: colors.sage50
    },
    unreadConversation: {
      backgroundColor: colors.sage25
    },
    conversationContent: {
      flexDirection: 'row',
      alignItems: 'center'
    },
    avatarContainer: {
      position: 'relative',
      marginRight: (0, _responsiveUtils.getResponsiveSpacing)(12)
    },
    avatar: {
      width: (0, _responsiveUtils.getMinimumTouchTarget)(48),
      height: (0, _responsiveUtils.getMinimumTouchTarget)(48),
      borderRadius: (0, _responsiveUtils.getMinimumTouchTarget)(24),
      justifyContent: 'center',
      alignItems: 'center'
    },
    avatarText: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(18),
      fontWeight: '600',
      color: '#FFFFFF'
    },
    onlineIndicator: {
      position: 'absolute',
      bottom: 2,
      right: 2,
      width: 12,
      height: 12,
      borderRadius: 6,
      backgroundColor: '#10B981',
      borderWidth: 2,
      borderColor: colors.background.primary
    },
    conversationInfo: {
      flex: 1
    },
    conversationHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: (0, _responsiveUtils.getResponsiveSpacing)(4)
    },
    participantName: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(16),
      fontWeight: '600',
      color: colors.text.primary,
      flex: 1,
      marginRight: (0, _responsiveUtils.getResponsiveSpacing)(8)
    },
    timestamp: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(12),
      color: colors.text.tertiary
    },
    lastMessageContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between'
    },
    lastMessage: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(14),
      color: colors.text.secondary,
      flex: 1,
      marginRight: (0, _responsiveUtils.getResponsiveSpacing)(8)
    },
    unreadText: {
      fontWeight: '600',
      color: colors.text.primary
    },
    unreadBadge: {
      backgroundColor: colors.sage400,
      borderRadius: 10,
      minWidth: 20,
      height: 20,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 6
    },
    unreadCount: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(12),
      fontWeight: '600',
      color: '#FFFFFF'
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: (0, _responsiveUtils.getResponsiveSpacing)(40),
      paddingVertical: (0, _responsiveUtils.getResponsiveSpacing)(60)
    },
    emptyStateText: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(18),
      fontWeight: '600',
      color: colors.text.primary,
      marginTop: (0, _responsiveUtils.getResponsiveSpacing)(16),
      marginBottom: (0, _responsiveUtils.getResponsiveSpacing)(8)
    },
    emptyStateSubtext: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(14),
      color: colors.text.secondary,
      textAlign: 'center',
      lineHeight: 20
    },
    errorContainer: {
      padding: (0, _responsiveUtils.getResponsiveSpacing)(20),
      alignItems: 'center'
    },
    errorText: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(16),
      color: colors.error,
      textAlign: 'center'
    }
  });
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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