6cdaaa43cdbeea4a6e8f58b01fdf87da
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.advancedCachingService = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _asyncStorage = _interopRequireDefault(require("@react-native-async-storage/async-storage"));
var _performanceMonitoringService = require("./performanceMonitoringService");
var AdvancedCachingService = function () {
  function AdvancedCachingService() {
    var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    (0, _classCallCheck2.default)(this, AdvancedCachingService);
    this.memoryCache = new Map();
    this.stats = {
      memoryHits: 0,
      memoryMisses: 0,
      storageHits: 0,
      storageMisses: 0,
      totalSize: 0,
      entryCount: 0,
      hitRate: 0,
      averageAccessTime: 0
    };
    this.cleanupTimer = null;
    this.config = Object.assign({
      maxMemorySize: 50 * 1024 * 1024,
      maxStorageSize: 200 * 1024 * 1024,
      defaultTTL: 24 * 60 * 60 * 1000,
      compressionThreshold: 1024,
      encryptionEnabled: false,
      enableAnalytics: true,
      cleanupInterval: 5 * 60 * 1000
    }, config);
    this.startCleanupTimer();
  }
  return (0, _createClass2.default)(AdvancedCachingService, [{
    key: "get",
    value: (function () {
      var _get = (0, _asyncToGenerator2.default)(function* (key, fallback) {
        var startTime = Date.now();
        try {
          var memoryEntry = this.memoryCache.get(key);
          if (memoryEntry && this.isEntryValid(memoryEntry)) {
            this.updateAccessStats(memoryEntry);
            this.stats.memoryHits++;
            this.trackAccessTime(Date.now() - startTime);
            return memoryEntry.data;
          }
          var storageEntry = yield this.getFromStorage(key);
          if (storageEntry && this.isEntryValid(storageEntry)) {
            this.memoryCache.set(key, storageEntry);
            this.updateAccessStats(storageEntry);
            this.stats.storageHits++;
            this.trackAccessTime(Date.now() - startTime);
            return storageEntry.data;
          }
          if (fallback) {
            var data = yield fallback();
            yield this.set(key, data);
            this.trackAccessTime(Date.now() - startTime);
            return data;
          }
          this.stats.memoryMisses++;
          this.stats.storageMisses++;
          this.trackAccessTime(Date.now() - startTime);
          return null;
        } catch (error) {
          console.error('[Cache] Error getting item:', error);
          this.trackAccessTime(Date.now() - startTime);
          return null;
        }
      });
      function get(_x, _x2) {
        return _get.apply(this, arguments);
      }
      return get;
    }())
  }, {
    key: "set",
    value: (function () {
      var _set = (0, _asyncToGenerator2.default)(function* (key, data) {
        var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
        var _options$ttl = options.ttl,
          ttl = _options$ttl === void 0 ? this.config.defaultTTL : _options$ttl,
          _options$tags = options.tags,
          tags = _options$tags === void 0 ? [] : _options$tags,
          _options$strategy = options.strategy,
          strategy = _options$strategy === void 0 ? {
            type: 'lru'
          } : _options$strategy,
          _options$forceStorage = options.forceStorage,
          forceStorage = _options$forceStorage === void 0 ? false : _options$forceStorage;
        try {
          var serializedData = JSON.stringify(data);
          var size = new Blob([serializedData]).size;
          var shouldCompress = size > this.config.compressionThreshold;
          var shouldEncrypt = this.config.encryptionEnabled;
          var entry = {
            key: key,
            data: data,
            timestamp: Date.now(),
            ttl: ttl,
            size: size,
            accessCount: 1,
            lastAccessed: Date.now(),
            tags: tags,
            compressed: shouldCompress,
            encrypted: shouldEncrypt
          };
          if (size <= this.config.maxMemorySize / 10) {
            this.memoryCache.set(key, entry);
            this.enforceMemoryLimits(strategy);
          }
          if (forceStorage || size > this.config.maxMemorySize / 20) {
            yield this.setInStorage(key, entry);
          }
          this.updateStats();
          this.trackCacheOperation('set', key, size);
        } catch (error) {
          console.error('[Cache] Error setting item:', error);
        }
      });
      function set(_x3, _x4) {
        return _set.apply(this, arguments);
      }
      return set;
    }())
  }, {
    key: "remove",
    value: (function () {
      var _remove = (0, _asyncToGenerator2.default)(function* (key) {
        try {
          this.memoryCache.delete(key);
          yield _asyncStorage.default.removeItem(`cache_${key}`);
          this.updateStats();
          this.trackCacheOperation('remove', key);
        } catch (error) {
          console.error('[Cache] Error removing item:', error);
        }
      });
      function remove(_x5) {
        return _remove.apply(this, arguments);
      }
      return remove;
    }())
  }, {
    key: "clearByTags",
    value: (function () {
      var _clearByTags = (0, _asyncToGenerator2.default)(function* (tags) {
        var _this = this;
        try {
          var keysToRemove = [];
          for (var _ref of this.memoryCache.entries()) {
            var _ref2 = (0, _slicedToArray2.default)(_ref, 2);
            var key = _ref2[0];
            var entry = _ref2[1];
            if (entry.tags.some(function (tag) {
              return tags.includes(tag);
            })) {
              keysToRemove.push(key);
            }
          }
          keysToRemove.forEach(function (key) {
            return _this.memoryCache.delete(key);
          });
          var storageKeys = yield _asyncStorage.default.getAllKeys();
          var cacheKeys = storageKeys.filter(function (key) {
            return key.startsWith('cache_');
          });
          for (var storageKey of cacheKeys) {
            try {
              var entryData = yield _asyncStorage.default.getItem(storageKey);
              if (entryData) {
                var _entry = JSON.parse(entryData);
                if (_entry.tags && _entry.tags.some(function (tag) {
                  return tags.includes(tag);
                })) {
                  yield _asyncStorage.default.removeItem(storageKey);
                }
              }
            } catch (error) {
              console.warn('[Cache] Error checking storage entry:', error);
            }
          }
          this.updateStats();
          this.trackCacheOperation('clearByTags', tags.join(','));
        } catch (error) {
          console.error('[Cache] Error clearing by tags:', error);
        }
      });
      function clearByTags(_x6) {
        return _clearByTags.apply(this, arguments);
      }
      return clearByTags;
    }())
  }, {
    key: "clear",
    value: (function () {
      var _clear = (0, _asyncToGenerator2.default)(function* () {
        try {
          this.memoryCache.clear();
          var keys = yield _asyncStorage.default.getAllKeys();
          var cacheKeys = keys.filter(function (key) {
            return key.startsWith('cache_');
          });
          yield _asyncStorage.default.multiRemove(cacheKeys);
          this.resetStats();
          this.trackCacheOperation('clear');
        } catch (error) {
          console.error('[Cache] Error clearing cache:', error);
        }
      });
      function clear() {
        return _clear.apply(this, arguments);
      }
      return clear;
    }())
  }, {
    key: "preload",
    value: (function () {
      var _preload = (0, _asyncToGenerator2.default)(function* (entries) {
        var _this2 = this;
        var startTime = Date.now();
        try {
          var promises = entries.map(function () {
            var _ref4 = (0, _asyncToGenerator2.default)(function* (_ref3) {
              var key = _ref3.key,
                loader = _ref3.loader,
                ttl = _ref3.ttl;
              try {
                var data = yield loader();
                yield _this2.set(key, data, {
                  ttl: ttl
                });
              } catch (error) {
                console.warn(`[Cache] Failed to preload ${key}:`, error);
              }
            });
            return function (_x8) {
              return _ref4.apply(this, arguments);
            };
          }());
          yield Promise.allSettled(promises);
          var duration = Date.now() - startTime;
          _performanceMonitoringService.performanceMonitoringService.trackMetric('cache_preload_duration', duration, 'ms', {
            entriesCount: entries.length
          });
        } catch (error) {
          console.error('[Cache] Error during preload:', error);
        }
      });
      function preload(_x7) {
        return _preload.apply(this, arguments);
      }
      return preload;
    }())
  }, {
    key: "getStats",
    value: function getStats() {
      this.updateStats();
      return Object.assign({}, this.stats);
    }
  }, {
    key: "optimize",
    value: (function () {
      var _optimize = (0, _asyncToGenerator2.default)(function* () {
        try {
          yield this.cleanup();
          yield this.compressLargeEntries();
          this.optimizeMemoryCache();
          this.trackCacheOperation('optimize');
        } catch (error) {
          console.error('[Cache] Error during optimization:', error);
        }
      });
      function optimize() {
        return _optimize.apply(this, arguments);
      }
      return optimize;
    }())
  }, {
    key: "getFromStorage",
    value: function () {
      var _getFromStorage = (0, _asyncToGenerator2.default)(function* (key) {
        try {
          var data = yield _asyncStorage.default.getItem(`cache_${key}`);
          if (!data) return null;
          var entry = JSON.parse(data);
          return entry;
        } catch (error) {
          console.warn('[Cache] Error reading from storage:', error);
          return null;
        }
      });
      function getFromStorage(_x9) {
        return _getFromStorage.apply(this, arguments);
      }
      return getFromStorage;
    }()
  }, {
    key: "setInStorage",
    value: function () {
      var _setInStorage = (0, _asyncToGenerator2.default)(function* (key, entry) {
        try {
          var data = JSON.stringify(entry);
          yield _asyncStorage.default.setItem(`cache_${key}`, data);
        } catch (error) {
          console.warn('[Cache] Error writing to storage:', error);
        }
      });
      function setInStorage(_x0, _x1) {
        return _setInStorage.apply(this, arguments);
      }
      return setInStorage;
    }()
  }, {
    key: "isEntryValid",
    value: function isEntryValid(entry) {
      var now = Date.now();
      return now - entry.timestamp < entry.ttl;
    }
  }, {
    key: "updateAccessStats",
    value: function updateAccessStats(entry) {
      entry.accessCount++;
      entry.lastAccessed = Date.now();
    }
  }, {
    key: "enforceMemoryLimits",
    value: function enforceMemoryLimits(strategy) {
      var currentSize = this.calculateMemorySize();
      if (currentSize > this.config.maxMemorySize) {
        this.evictEntries(strategy);
      }
    }
  }, {
    key: "evictEntries",
    value: function evictEntries(strategy) {
      var entries = Array.from(this.memoryCache.entries());
      switch (strategy.type) {
        case 'lru':
          entries.sort(function (_ref5, _ref6) {
            var _ref7 = (0, _slicedToArray2.default)(_ref5, 2),
              a = _ref7[1];
            var _ref8 = (0, _slicedToArray2.default)(_ref6, 2),
              b = _ref8[1];
            return a.lastAccessed - b.lastAccessed;
          });
          break;
        case 'lfu':
          entries.sort(function (_ref9, _ref0) {
            var _ref1 = (0, _slicedToArray2.default)(_ref9, 2),
              a = _ref1[1];
            var _ref10 = (0, _slicedToArray2.default)(_ref0, 2),
              b = _ref10[1];
            return a.accessCount - b.accessCount;
          });
          break;
        case 'ttl':
          entries.sort(function (_ref11, _ref12) {
            var _ref13 = (0, _slicedToArray2.default)(_ref11, 2),
              a = _ref13[1];
            var _ref14 = (0, _slicedToArray2.default)(_ref12, 2),
              b = _ref14[1];
            return a.timestamp - b.timestamp;
          });
          break;
        case 'fifo':
          entries.sort(function (_ref15, _ref16) {
            var _ref17 = (0, _slicedToArray2.default)(_ref15, 2),
              a = _ref17[1];
            var _ref18 = (0, _slicedToArray2.default)(_ref16, 2),
              b = _ref18[1];
            return a.timestamp - b.timestamp;
          });
          break;
      }
      var toRemove = Math.ceil(entries.length * 0.25);
      for (var i = 0; i < toRemove; i++) {
        this.memoryCache.delete(entries[i][0]);
      }
    }
  }, {
    key: "calculateMemorySize",
    value: function calculateMemorySize() {
      var totalSize = 0;
      for (var entry of this.memoryCache.values()) {
        totalSize += entry.size;
      }
      return totalSize;
    }
  }, {
    key: "updateStats",
    value: function updateStats() {
      this.stats.entryCount = this.memoryCache.size;
      this.stats.totalSize = this.calculateMemorySize();
      var totalHits = this.stats.memoryHits + this.stats.storageHits;
      var totalRequests = totalHits + this.stats.memoryMisses + this.stats.storageMisses;
      this.stats.hitRate = totalRequests > 0 ? totalHits / totalRequests * 100 : 0;
    }
  }, {
    key: "resetStats",
    value: function resetStats() {
      this.stats = {
        memoryHits: 0,
        memoryMisses: 0,
        storageHits: 0,
        storageMisses: 0,
        totalSize: 0,
        entryCount: 0,
        hitRate: 0,
        averageAccessTime: 0
      };
    }
  }, {
    key: "trackAccessTime",
    value: function trackAccessTime(duration) {
      var currentAvg = this.stats.averageAccessTime;
      var totalRequests = this.stats.memoryHits + this.stats.storageHits + this.stats.memoryMisses + this.stats.storageMisses;
      this.stats.averageAccessTime = (currentAvg * (totalRequests - 1) + duration) / totalRequests;
    }
  }, {
    key: "trackCacheOperation",
    value: function trackCacheOperation(operation, key, size) {
      if (this.config.enableAnalytics) {
        _performanceMonitoringService.performanceMonitoringService.trackMetric(`cache_${operation}`, 1, 'count', {
          key: key,
          size: size
        }, ['cache', operation]);
      }
    }
  }, {
    key: "startCleanupTimer",
    value: function startCleanupTimer() {
      var _this3 = this;
      this.cleanupTimer = setInterval(function () {
        _this3.cleanup();
      }, this.config.cleanupInterval);
    }
  }, {
    key: "cleanup",
    value: function () {
      var _cleanup = (0, _asyncToGenerator2.default)(function* () {
        var _this4 = this;
        var now = Date.now();
        var expiredKeys = [];
        for (var _ref19 of this.memoryCache.entries()) {
          var _ref20 = (0, _slicedToArray2.default)(_ref19, 2);
          var key = _ref20[0];
          var entry = _ref20[1];
          if (!this.isEntryValid(entry)) {
            expiredKeys.push(key);
          }
        }
        expiredKeys.forEach(function (key) {
          return _this4.memoryCache.delete(key);
        });
        if (Math.random() < 0.1) {
          yield this.cleanupStorage();
        }
        this.updateStats();
      });
      function cleanup() {
        return _cleanup.apply(this, arguments);
      }
      return cleanup;
    }()
  }, {
    key: "cleanupStorage",
    value: function () {
      var _cleanupStorage = (0, _asyncToGenerator2.default)(function* () {
        try {
          var keys = yield _asyncStorage.default.getAllKeys();
          var cacheKeys = keys.filter(function (key) {
            return key.startsWith('cache_');
          });
          for (var key of cacheKeys) {
            try {
              var data = yield _asyncStorage.default.getItem(key);
              if (data) {
                var entry = JSON.parse(data);
                if (!this.isEntryValid(entry)) {
                  yield _asyncStorage.default.removeItem(key);
                }
              }
            } catch (error) {
              yield _asyncStorage.default.removeItem(key);
            }
          }
        } catch (error) {
          console.warn('[Cache] Error during storage cleanup:', error);
        }
      });
      function cleanupStorage() {
        return _cleanupStorage.apply(this, arguments);
      }
      return cleanupStorage;
    }()
  }, {
    key: "compressLargeEntries",
    value: function () {
      var _compressLargeEntries = (0, _asyncToGenerator2.default)(function* () {});
      function compressLargeEntries() {
        return _compressLargeEntries.apply(this, arguments);
      }
      return compressLargeEntries;
    }()
  }, {
    key: "optimizeMemoryCache",
    value: function optimizeMemoryCache() {
      var _this5 = this;
      var entries = Array.from(this.memoryCache.entries());
      entries.sort(function (_ref21, _ref22) {
        var _ref23 = (0, _slicedToArray2.default)(_ref21, 2),
          a = _ref23[1];
        var _ref24 = (0, _slicedToArray2.default)(_ref22, 2),
          b = _ref24[1];
        return b.accessCount - a.accessCount;
      });
      this.memoryCache.clear();
      entries.slice(0, Math.min(entries.length, 100)).forEach(function (_ref25) {
        var _ref26 = (0, _slicedToArray2.default)(_ref25, 2),
          key = _ref26[0],
          entry = _ref26[1];
        _this5.memoryCache.set(key, entry);
      });
    }
  }]);
}();
var advancedCachingService = exports.advancedCachingService = new AdvancedCachingService();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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