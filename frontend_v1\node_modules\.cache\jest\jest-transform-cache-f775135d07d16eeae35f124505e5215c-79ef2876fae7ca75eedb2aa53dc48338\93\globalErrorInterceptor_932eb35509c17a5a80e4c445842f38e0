3d3de7a0fa5b4985c7af96ff9ddcdd94
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.resetErrorTracking = exports.installGlobalErrorInterceptor = exports.hasRecentThemeErrors = exports.getFallbackTheme = exports.getErrorStats = void 0;
var errorCount = 0;
var lastErrorTime = 0;
var MAX_ERRORS_PER_MINUTE = 10;
var FALLBACK_THEME = {
  colors: {
    primary: {
      default: '#4A6B52',
      light: '#6B8A74',
      dark: '#2A4B32',
      contrast: '#FFFFFF'
    },
    text: {
      primary: '#1A1A1A',
      secondary: '#6B7280',
      tertiary: '#9CA3AF',
      inverse: '#FFFFFF',
      disabled: '#9CA3AF',
      onPrimary: '#FFFFFF',
      onSecondary: '#FFFFFF',
      link: '#4A6B52',
      linkHover: '#2A4B32'
    },
    background: {
      primary: '#FFFFFF',
      secondary: '#F9FAFB',
      tertiary: '#F3F4F6',
      elevated: '#FFFFFF',
      overlay: 'rgba(0, 0, 0, 0.5)',
      sage: '#F4F7F5'
    },
    surface: {
      primary: '#FFFFFF',
      secondary: '#F9FAFB',
      tertiary: '#F3F4F6',
      inverse: '#1A1A1A',
      disabled: '#F3F4F6'
    },
    border: {
      light: '#E5E7EB',
      medium: '#D1D5DB',
      dark: '#9CA3AF',
      focus: '#4A6B52',
      error: '#EF4444',
      success: '#10B981'
    }
  },
  isDark: false
};
var THEME_ERROR_PATTERNS = [/Cannot read property 'primary' of undefined/, /Cannot read property 'text' of undefined/, /Cannot read property 'background' of undefined/, /Cannot read property 'surface' of undefined/, /Cannot read property 'colors' of undefined/, /Cannot read properties of undefined \(reading 'primary'\)/, /Cannot read properties of undefined \(reading 'text'\)/, /Cannot read properties of undefined \(reading 'background'\)/, /Cannot read properties of undefined \(reading 'surface'\)/, /Cannot read properties of undefined \(reading 'colors'\)/];
var isThemeError = function isThemeError(error) {
  if (!error || !error.message) return false;
  return THEME_ERROR_PATTERNS.some(function (pattern) {
    return pattern.test(error.message);
  });
};
var shouldHandleError = function shouldHandleError() {
  var now = Date.now();
  if (now - lastErrorTime > 60000) {
    errorCount = 0;
  }
  lastErrorTime = now;
  errorCount++;
  return errorCount <= MAX_ERRORS_PER_MINUTE;
};
var globalErrorHandler = function globalErrorHandler(error) {
  var isFatal = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
  if (!isThemeError(error)) {
    return;
  }
  if (!shouldHandleError()) {
    console.warn('[GlobalErrorInterceptor] Too many theme errors, throttling...');
    return;
  }
  console.error('[GlobalErrorInterceptor] 🛡️ Theme error intercepted:', error.message);
  console.error('[GlobalErrorInterceptor] Stack trace:', error.stack);
  try {
    if (typeof global !== 'undefined') {
      global.__VIERLA_FALLBACK_THEME__ = FALLBACK_THEME;
      console.log('[GlobalErrorInterceptor] ✅ Fallback theme stored in global');
    }
    console.log('[GlobalErrorInterceptor] 🔄 Attempting graceful recovery...');
    if (!isFatal) {
      console.log('[GlobalErrorInterceptor] ✅ Non-fatal theme error handled gracefully');
      return;
    }
    console.warn('[GlobalErrorInterceptor] ⚠️ Fatal theme error detected, attempting recovery...');
  } catch (recoveryError) {
    console.error('[GlobalErrorInterceptor] ❌ Recovery attempt failed:', recoveryError);
  }
};
var installGlobalErrorInterceptor = exports.installGlobalErrorInterceptor = function installGlobalErrorInterceptor() {
  console.log('[GlobalErrorInterceptor] Installing global error interceptor...');
  try {
    var _global$ErrorUtils;
    if (global && global.ErrorUtils) {
      var originalReportError = global.ErrorUtils.reportError;
      var originalReportFatalError = global.ErrorUtils.reportFatalError;
      global.ErrorUtils.reportError = function (error) {
        globalErrorHandler(error, false);
        if (originalReportError && !isThemeError(error)) {
          originalReportError.call(this, error);
        }
      };
      global.ErrorUtils.reportFatalError = function (error) {
        globalErrorHandler(error, true);
        if (originalReportFatalError && !isThemeError(error)) {
          originalReportFatalError.call(this, error);
        }
      };
      console.log('[GlobalErrorInterceptor] ✅ Installed on ErrorUtils');
    }
    if (global && typeof ((_global$ErrorUtils = global.ErrorUtils) == null ? void 0 : _global$ErrorUtils.setGlobalHandler) === 'function') {
      var originalHandler = (global.ErrorUtils.getGlobalHandler == null ? void 0 : global.ErrorUtils.getGlobalHandler()) || null;
      global.ErrorUtils.setGlobalHandler(function (error, isFatal) {
        globalErrorHandler(error, isFatal || false);
        if (originalHandler && !isThemeError(error)) {
          originalHandler(error, isFatal);
        }
      });
      console.log('[GlobalErrorInterceptor] ✅ Installed global handler');
    }
    if (typeof window !== 'undefined') {
      var originalOnError = window.onerror;
      window.onerror = function (message, source, lineno, colno, error) {
        if (error && isThemeError(error)) {
          globalErrorHandler(error, false);
          return true;
        }
        if (originalOnError) {
          return originalOnError.call(this, message, source, lineno, colno, error);
        }
        return false;
      };
      console.log('[GlobalErrorInterceptor] ✅ Installed on window.onerror');
    }
    console.log('[GlobalErrorInterceptor] ✅ Global error interceptor installed successfully');
  } catch (error) {
    console.error('[GlobalErrorInterceptor] ❌ Failed to install global error interceptor:', error);
  }
};
var getFallbackTheme = exports.getFallbackTheme = function getFallbackTheme() {
  return FALLBACK_THEME;
};
var hasRecentThemeErrors = exports.hasRecentThemeErrors = function hasRecentThemeErrors() {
  return errorCount > 0 && Date.now() - lastErrorTime < 60000;
};
var resetErrorTracking = exports.resetErrorTracking = function resetErrorTracking() {
  errorCount = 0;
  lastErrorTime = 0;
  console.log('[GlobalErrorInterceptor] Error tracking reset');
};
var getErrorStats = exports.getErrorStats = function getErrorStats() {
  return {
    errorCount: errorCount,
    lastErrorTime: lastErrorTime,
    hasRecentErrors: hasRecentThemeErrors()
  };
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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