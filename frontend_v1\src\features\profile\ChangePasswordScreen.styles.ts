/**
 * Change Password Screen Styles - Responsive Design System
 *
 * Style Contract:
 * - Implements responsive design principles
 * - Follows Vierla design system
 * - Supports theme-based styling
 * - Ensures accessibility compliance
 * - Optimized for both iOS and Android
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { StyleSheet } from 'react-native';

import { Colors } from '../../constants/Colors';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../../utils/responsiveUtils';

export const createStyles = (colors: typeof Colors.light) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },

    scrollView: {
      flex: 1,
    },

    header: {
      paddingHorizontal: getResponsiveSpacing(20),
      paddingVertical: getResponsiveSpacing(32),
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },

    title: {
      fontSize: getResponsiveFontSize(28),
      fontWeight: '700',
      color: colors.text,
      marginBottom: getResponsiveSpacing(8),
    },

    subtitle: {
      fontSize: getResponsiveFontSize(16),
      color: colors.textSecondary,
      lineHeight: getResponsiveFontSize(22),
    },

    formContainer: {
      paddingHorizontal: getResponsiveSpacing(20),
      paddingVertical: getResponsiveSpacing(24),
    },

    fieldContainer: {
      marginBottom: getResponsiveSpacing(24),
    },

    fieldLabel: {
      fontSize: getResponsiveFontSize(16),
      fontWeight: '500',
      color: colors.text,
      marginBottom: getResponsiveSpacing(8),
    },

    passwordInputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: getResponsiveSpacing(8),
      backgroundColor: colors.surface,
    },

    passwordInput: {
      flex: 1,
      paddingHorizontal: getResponsiveSpacing(16),
      paddingVertical: getResponsiveSpacing(12),
      fontSize: getResponsiveFontSize(16),
      color: colors.text,
    },

    fieldInputError: {
      borderColor: colors.error,
    },

    visibilityToggle: {
      paddingHorizontal: getResponsiveSpacing(16),
      paddingVertical: getResponsiveSpacing(12),
    },

    visibilityToggleText: {
      fontSize: getResponsiveFontSize(14),
      fontWeight: '500',
      color: colors.primary,
    },

    errorText: {
      fontSize: getResponsiveFontSize(14),
      color: colors.error,
      marginTop: getResponsiveSpacing(4),
    },

    requirementsContainer: {
      marginTop: getResponsiveSpacing(16),
      padding: getResponsiveSpacing(16),
      backgroundColor: colors.surfaceVariant,
      borderRadius: getResponsiveSpacing(8),
      borderWidth: 1,
      borderColor: colors.border,
    },

    requirementsTitle: {
      fontSize: getResponsiveFontSize(16),
      fontWeight: '600',
      color: colors.text,
      marginBottom: getResponsiveSpacing(12),
    },

    requirementItem: {
      marginBottom: getResponsiveSpacing(4),
    },

    requirementText: {
      fontSize: getResponsiveFontSize(14),
      color: colors.textSecondary,
    },

    requirementMet: {
      color: colors.success,
      fontWeight: '500',
    },

    changeButton: {
      backgroundColor: colors.primary?.default || '#5A7A63',
      paddingVertical: getResponsiveSpacing(16),
      paddingHorizontal: getResponsiveSpacing(24),
      borderRadius: getResponsiveSpacing(8),
      alignItems: 'center',
      marginTop: getResponsiveSpacing(32),
    },

    changeButtonDisabled: {
      opacity: 0.6,
    },

    changeButtonText: {
      fontSize: getResponsiveFontSize(16),
      fontWeight: '600',
      color: colors.white,
    },
  });
