{"version": 3, "names": ["_NativeAnimatedHelper", "_interopRequireDefault", "require", "_invariant", "_uniqueId", "_assertNativeAnimatedModule", "NativeAnimatedHelper", "assertNativeAnimatedModule", "_listeners", "_classPrivateFieldLooseKey2", "default", "AnimatedNode", "exports", "config", "_classCallCheck2", "Object", "defineProperty", "writable", "value", "Map", "_platformConfig", "undefined", "__isNative", "__nativeTag", "__debugID", "__DEV__", "debugID", "_createClass2", "key", "__attach", "__detach", "removeAllListeners", "API", "dropAnimatedNode", "__getValue", "__getAnimatedValue", "__add<PERSON><PERSON>d", "child", "__remove<PERSON><PERSON>d", "__get<PERSON><PERSON><PERSON><PERSON>", "__makeNative", "platformConfig", "invariant", "addListener", "callback", "id", "String", "_classPrivateFieldLooseBase2", "set", "removeListener", "delete", "clear", "hasListeners", "size", "__onAnimatedValueUpdateReceived", "__callListeners", "event", "for<PERSON>ach", "listener", "__getNativeTag", "nativeTag", "generateNewNodeTag", "__getNativeConfig", "createAnimatedNode", "Error", "__getPlatformConfig", "__setPlatformConfig", "toJSON", "__getDebugID"], "sources": ["AnimatedNode.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n */\n\nimport type {PlatformConfig} from '../AnimatedPlatformConfig';\n\nimport NativeAnimatedHelper from '../../../src/private/animated/NativeAnimatedHelper';\nimport invariant from 'invariant';\n\ntype ValueListenerCallback = (state: {value: number, ...}) => mixed;\n\nexport type AnimatedNodeConfig = $ReadOnly<{\n  debugID?: string,\n}>;\n\nlet _uniqueId = 1;\nlet _assertNativeAnimatedModule: ?() => void = () => {\n  NativeAnimatedHelper.assertNativeAnimatedModule();\n  // We only have to assert that the module exists once. After we've asserted\n  // this, clear out the function so we know to skip it in the future.\n  _assertNativeAnimatedModule = null;\n};\n\nexport default class AnimatedNode {\n  #listeners: Map<string, ValueListenerCallback> = new Map();\n\n  _platformConfig: ?PlatformConfig = undefined;\n\n  constructor(\n    config?: ?$ReadOnly<{\n      ...AnimatedNodeConfig,\n      ...\n    }>,\n  ) {\n    if (__DEV__) {\n      this.__debugID = config?.debugID;\n    }\n  }\n\n  __attach(): void {}\n  __detach(): void {\n    this.removeAllListeners();\n    if (this.__isNative && this.__nativeTag != null) {\n      NativeAnimatedHelper.API.dropAnimatedNode(this.__nativeTag);\n      this.__nativeTag = undefined;\n    }\n  }\n  __getValue(): any {}\n  __getAnimatedValue(): any {\n    return this.__getValue();\n  }\n  __addChild(child: AnimatedNode) {}\n  __removeChild(child: AnimatedNode) {}\n  __getChildren(): $ReadOnlyArray<AnimatedNode> {\n    return [];\n  }\n\n  /* Methods and props used by native Animated impl */\n  __isNative: boolean = false;\n  __nativeTag: ?number = undefined;\n\n  __makeNative(platformConfig: ?PlatformConfig): void {\n    // Subclasses are expected to set `__isNative` to true before this.\n    invariant(\n      this.__isNative,\n      'This node cannot be made a \"native\" animated node',\n    );\n\n    this._platformConfig = platformConfig;\n  }\n\n  /**\n   * Adds an asynchronous listener to the value so you can observe updates from\n   * animations.  This is useful because there is no way to\n   * synchronously read the value because it might be driven natively.\n   *\n   * See https://reactnative.dev/docs/animatedvalue#addlistener\n   */\n  addListener(callback: (value: any) => mixed): string {\n    const id = String(_uniqueId++);\n    this.#listeners.set(id, callback);\n    return id;\n  }\n\n  /**\n   * Unregister a listener. The `id` param shall match the identifier\n   * previously returned by `addListener()`.\n   *\n   * See https://reactnative.dev/docs/animatedvalue#removelistener\n   */\n  removeListener(id: string): void {\n    this.#listeners.delete(id);\n  }\n\n  /**\n   * Remove all registered listeners.\n   *\n   * See https://reactnative.dev/docs/animatedvalue#removealllisteners\n   */\n  removeAllListeners(): void {\n    this.#listeners.clear();\n  }\n\n  hasListeners(): boolean {\n    return this.#listeners.size > 0;\n  }\n\n  __onAnimatedValueUpdateReceived(value: number): void {\n    this.__callListeners(value);\n  }\n\n  __callListeners(value: number): void {\n    const event = {value};\n    this.#listeners.forEach(listener => {\n      listener(event);\n    });\n  }\n\n  __getNativeTag(): number {\n    let nativeTag = this.__nativeTag;\n    if (nativeTag == null) {\n      _assertNativeAnimatedModule?.();\n\n      // `__isNative` is initialized as false and only ever set to true. So we\n      // only need to check it once here when initializing `__nativeTag`.\n      invariant(\n        this.__isNative,\n        'Attempt to get native tag from node not marked as \"native\"',\n      );\n\n      nativeTag = NativeAnimatedHelper.generateNewNodeTag();\n      this.__nativeTag = nativeTag;\n\n      const config = this.__getNativeConfig();\n      if (this._platformConfig) {\n        config.platformConfig = this._platformConfig;\n      }\n      NativeAnimatedHelper.API.createAnimatedNode(nativeTag, config);\n    }\n    return nativeTag;\n  }\n\n  __getNativeConfig(): Object {\n    throw new Error(\n      'This JS animated node type cannot be used as native animated node',\n    );\n  }\n\n  __getPlatformConfig(): ?PlatformConfig {\n    return this._platformConfig;\n  }\n\n  __setPlatformConfig(platformConfig: ?PlatformConfig) {\n    this._platformConfig = platformConfig;\n  }\n\n  /**\n   * NOTE: This is intended to prevent `JSON.stringify` from throwing \"cyclic\n   * structure\" errors in React DevTools. Avoid depending on this!\n   */\n  toJSON(): mixed {\n    return this.__getValue();\n  }\n\n  __debugID: ?string = undefined;\n\n  __getDebugID(): ?string {\n    if (__DEV__) {\n      return this.__debugID;\n    }\n    return undefined;\n  }\n}\n"], "mappings": ";;;;;;;;;AAYA,IAAAA,qBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAF,sBAAA,CAAAC,OAAA;AAQA,IAAIE,SAAS,GAAG,CAAC;AACjB,IAAIC,4BAAwC,GAAG,SAA3CA,2BAAwCA,CAAA,EAAS;EACnDC,6BAAoB,CAACC,0BAA0B,CAAC,CAAC;EAGjDF,4BAA2B,GAAG,IAAI;AACpC,CAAC;AAAC,IAAAG,UAAA,OAAAC,2BAAA,CAAAC,OAAA;AAAA,IAEmBC,YAAY,GAAAC,OAAA,CAAAF,OAAA;EAK/B,SAAAC,aACEE,MAGE,EACF;IAAA,IAAAC,gBAAA,CAAAJ,OAAA,QAAAC,YAAA;IAAAI,MAAA,CAAAC,cAAA,OAAAR,UAAA;MAAAS,QAAA;MAAAC,KAAA,EAT+C,IAAIC,GAAG,CAAC;IAAC;IAAA,KAE1DC,eAAe,GAAoBC,SAAS;IAAA,KAgC5CC,UAAU,GAAY,KAAK;IAAA,KAC3BC,WAAW,GAAYF,SAAS;IAAA,KAyGhCG,SAAS,GAAYH,SAAS;IAlI5B,IAAII,OAAO,EAAE;MACX,IAAI,CAACD,SAAS,GAAGX,MAAM,oBAANA,MAAM,CAAEa,OAAO;IAClC;EACF;EAAC,WAAAC,aAAA,CAAAjB,OAAA,EAAAC,YAAA;IAAAiB,GAAA;IAAAV,KAAA,EAED,SAAAW,QAAQA,CAAA,EAAS,CAAC;EAAC;IAAAD,GAAA;IAAAV,KAAA,EACnB,SAAAY,QAAQA,CAAA,EAAS;MACf,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACzB,IAAI,IAAI,CAACT,UAAU,IAAI,IAAI,CAACC,WAAW,IAAI,IAAI,EAAE;QAC/CjB,6BAAoB,CAAC0B,GAAG,CAACC,gBAAgB,CAAC,IAAI,CAACV,WAAW,CAAC;QAC3D,IAAI,CAACA,WAAW,GAAGF,SAAS;MAC9B;IACF;EAAC;IAAAO,GAAA;IAAAV,KAAA,EACD,SAAAgB,UAAUA,CAAA,EAAQ,CAAC;EAAC;IAAAN,GAAA;IAAAV,KAAA,EACpB,SAAAiB,kBAAkBA,CAAA,EAAQ;MACxB,OAAO,IAAI,CAACD,UAAU,CAAC,CAAC;IAC1B;EAAC;IAAAN,GAAA;IAAAV,KAAA,EACD,SAAAkB,UAAUA,CAACC,KAAmB,EAAE,CAAC;EAAC;IAAAT,GAAA;IAAAV,KAAA,EAClC,SAAAoB,aAAaA,CAACD,KAAmB,EAAE,CAAC;EAAC;IAAAT,GAAA;IAAAV,KAAA,EACrC,SAAAqB,aAAaA,CAAA,EAAiC;MAC5C,OAAO,EAAE;IACX;EAAC;IAAAX,GAAA;IAAAV,KAAA,EAMD,SAAAsB,YAAYA,CAACC,cAA+B,EAAQ;MAElD,IAAAC,kBAAS,EACP,IAAI,CAACpB,UAAU,EACf,mDACF,CAAC;MAED,IAAI,CAACF,eAAe,GAAGqB,cAAc;IACvC;EAAC;IAAAb,GAAA;IAAAV,KAAA,EASD,SAAAyB,WAAWA,CAACC,QAA+B,EAAU;MACnD,IAAMC,EAAE,GAAGC,MAAM,CAAC1C,SAAS,EAAE,CAAC;MAC9B,IAAA2C,4BAAA,CAAArC,OAAA,MAAI,EAAAF,UAAA,EAAAA,UAAA,EAAYwC,GAAG,CAACH,EAAE,EAAED,QAAQ,CAAC;MACjC,OAAOC,EAAE;IACX;EAAC;IAAAjB,GAAA;IAAAV,KAAA,EAQD,SAAA+B,cAAcA,CAACJ,EAAU,EAAQ;MAC/B,IAAAE,4BAAA,CAAArC,OAAA,MAAI,EAAAF,UAAA,EAAAA,UAAA,EAAY0C,MAAM,CAACL,EAAE,CAAC;IAC5B;EAAC;IAAAjB,GAAA;IAAAV,KAAA,EAOD,SAAAa,kBAAkBA,CAAA,EAAS;MACzB,IAAAgB,4BAAA,CAAArC,OAAA,MAAI,EAAAF,UAAA,EAAAA,UAAA,EAAY2C,KAAK,CAAC,CAAC;IACzB;EAAC;IAAAvB,GAAA;IAAAV,KAAA,EAED,SAAAkC,YAAYA,CAAA,EAAY;MACtB,OAAO,IAAAL,4BAAA,CAAArC,OAAA,MAAI,EAAAF,UAAA,EAAAA,UAAA,EAAY6C,IAAI,GAAG,CAAC;IACjC;EAAC;IAAAzB,GAAA;IAAAV,KAAA,EAED,SAAAoC,+BAA+BA,CAACpC,KAAa,EAAQ;MACnD,IAAI,CAACqC,eAAe,CAACrC,KAAK,CAAC;IAC7B;EAAC;IAAAU,GAAA;IAAAV,KAAA,EAED,SAAAqC,eAAeA,CAACrC,KAAa,EAAQ;MACnC,IAAMsC,KAAK,GAAG;QAACtC,KAAK,EAALA;MAAK,CAAC;MACrB,IAAA6B,4BAAA,CAAArC,OAAA,MAAI,EAAAF,UAAA,EAAAA,UAAA,EAAYiD,OAAO,CAAC,UAAAC,QAAQ,EAAI;QAClCA,QAAQ,CAACF,KAAK,CAAC;MACjB,CAAC,CAAC;IACJ;EAAC;IAAA5B,GAAA;IAAAV,KAAA,EAED,SAAAyC,cAAcA,CAAA,EAAW;MACvB,IAAIC,SAAS,GAAG,IAAI,CAACrC,WAAW;MAChC,IAAIqC,SAAS,IAAI,IAAI,EAAE;QACrBvD,4BAA2B,YAA3BA,4BAA2B,CAAG,CAAC;QAI/B,IAAAqC,kBAAS,EACP,IAAI,CAACpB,UAAU,EACf,4DACF,CAAC;QAEDsC,SAAS,GAAGtD,6BAAoB,CAACuD,kBAAkB,CAAC,CAAC;QACrD,IAAI,CAACtC,WAAW,GAAGqC,SAAS;QAE5B,IAAM/C,MAAM,GAAG,IAAI,CAACiD,iBAAiB,CAAC,CAAC;QACvC,IAAI,IAAI,CAAC1C,eAAe,EAAE;UACxBP,MAAM,CAAC4B,cAAc,GAAG,IAAI,CAACrB,eAAe;QAC9C;QACAd,6BAAoB,CAAC0B,GAAG,CAAC+B,kBAAkB,CAACH,SAAS,EAAE/C,MAAM,CAAC;MAChE;MACA,OAAO+C,SAAS;IAClB;EAAC;IAAAhC,GAAA;IAAAV,KAAA,EAED,SAAA4C,iBAAiBA,CAAA,EAAW;MAC1B,MAAM,IAAIE,KAAK,CACb,mEACF,CAAC;IACH;EAAC;IAAApC,GAAA;IAAAV,KAAA,EAED,SAAA+C,mBAAmBA,CAAA,EAAoB;MACrC,OAAO,IAAI,CAAC7C,eAAe;IAC7B;EAAC;IAAAQ,GAAA;IAAAV,KAAA,EAED,SAAAgD,mBAAmBA,CAACzB,cAA+B,EAAE;MACnD,IAAI,CAACrB,eAAe,GAAGqB,cAAc;IACvC;EAAC;IAAAb,GAAA;IAAAV,KAAA,EAMD,SAAAiD,MAAMA,CAAA,EAAU;MACd,OAAO,IAAI,CAACjC,UAAU,CAAC,CAAC;IAC1B;EAAC;IAAAN,GAAA;IAAAV,KAAA,EAID,SAAAkD,YAAYA,CAAA,EAAY;MACtB,IAAI3C,OAAO,EAAE;QACX,OAAO,IAAI,CAACD,SAAS;MACvB;MACA,OAAOH,SAAS;IAClB;EAAC;AAAA", "ignoreList": []}