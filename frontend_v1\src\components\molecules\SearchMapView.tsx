/**
 * SearchMapView Component - Google Maps Integration for Search
 *
 * Enhanced map component specifically designed for search functionality
 * with provider markers, clustering, and location-based search
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import * as Location from 'expo-location';
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  Platform,
  Dimensions,
  ActivityIndicator,
  Text,
} from 'react-native';
import MapView, { Marker, Region, PROVIDER_GOOGLE } from 'react-native-maps';

import { useTheme } from '../../contexts/ThemeContext';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../../utils/responsiveUtils';
import { ServiceProvider } from '../../features/service-discovery/types';

const { width, height } = Dimensions.get('window');

// Types
interface SearchMapViewProps {
  providers: ServiceProvider[];
  onProviderPress?: (provider: ServiceProvider) => void;
  onRegionChange?: (region: Region) => void;
  showUserLocation?: boolean;
  initialRegion?: Region;
  style?: any;
  searchRadius?: number; // in kilometers
  isLoading?: boolean;
}

interface MapMarker {
  id: string;
  coordinate: {
    latitude: number;
    longitude: number;
  };
  provider: ServiceProvider;
}

const SearchMapView: React.FC<SearchMapViewProps> = ({
  providers,
  onProviderPress,
  onRegionChange,
  showUserLocation = true,
  initialRegion,
  style,
  searchRadius = 10,
  isLoading = false,
}) => {
  const { colors } = useTheme();
  const mapRef = useRef<MapView>(null);
  
  const [region, setRegion] = useState<Region>(
    initialRegion || {
      latitude: 45.4215, // Ottawa default
      longitude: -75.6972,
      latitudeDelta: 0.0922,
      longitudeDelta: 0.0421,
    }
  );
  
  const [userLocation, setUserLocation] = useState<Location.LocationObject | null>(null);
  const [locationPermission, setLocationPermission] = useState<boolean>(false);
  const [isMapReady, setIsMapReady] = useState<boolean>(false);

  // Request location permission and get user location
  useEffect(() => {
    if (showUserLocation) {
      requestLocationPermission();
    }
  }, [showUserLocation]);

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      
      if (status === 'granted') {
        setLocationPermission(true);
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Balanced,
        });
        setUserLocation(location);
        
        // Update region to user location
        const newRegion = {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          latitudeDelta: 0.0922,
          longitudeDelta: 0.0421,
        };
        setRegion(newRegion);
        
        // Animate to user location
        if (mapRef.current && isMapReady) {
          mapRef.current.animateToRegion(newRegion, 1000);
        }
      } else {
        Alert.alert(
          'Location Permission',
          'Location access is needed to show nearby providers.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
    }
  };

  // Convert providers to map markers
  const markers: MapMarker[] = providers
    .filter(provider => provider.latitude && provider.longitude)
    .map(provider => ({
      id: provider.id,
      coordinate: {
        latitude: provider.latitude!,
        longitude: provider.longitude!,
      },
      provider,
    }));

  const handleRegionChangeComplete = (newRegion: Region) => {
    setRegion(newRegion);
    onRegionChange?.(newRegion);
  };

  const handleMarkerPress = (marker: MapMarker) => {
    onProviderPress?.(marker.provider);
  };

  const handleMapReady = () => {
    setIsMapReady(true);
    
    // If we have user location and map is ready, animate to it
    if (userLocation && showUserLocation) {
      const newRegion = {
        latitude: userLocation.coords.latitude,
        longitude: userLocation.coords.longitude,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      };
      mapRef.current?.animateToRegion(newRegion, 1000);
    }
  };

  // Fit map to show all markers
  const fitToMarkers = () => {
    if (markers.length > 0 && mapRef.current && isMapReady) {
      mapRef.current.fitToCoordinates(
        markers.map(marker => marker.coordinate),
        {
          edgePadding: {
            top: getResponsiveSpacing(4),
            right: getResponsiveSpacing(4),
            bottom: getResponsiveSpacing(4),
            left: getResponsiveSpacing(4),
          },
          animated: true,
        }
      );
    }
  };

  // Fit to markers when providers change
  useEffect(() => {
    if (markers.length > 0 && isMapReady) {
      // Small delay to ensure map is fully rendered
      setTimeout(fitToMarkers, 500);
    }
  }, [providers, isMapReady]);

  return (
    <View style={[styles.container, style]}>
      {isLoading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text.primary }]}>
            Loading providers...
          </Text>
        </View>
      )}
      
      <MapView
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        region={region}
        onRegionChangeComplete={handleRegionChangeComplete}
        onMapReady={handleMapReady}
        showsUserLocation={showUserLocation && locationPermission}
        showsMyLocationButton={showUserLocation && locationPermission}
        showsCompass={true}
        showsScale={true}
        toolbarEnabled={false}
        loadingEnabled={true}
        loadingIndicatorColor={colors.primary}
        loadingBackgroundColor={colors.background.primary}
      >
        {/* Provider markers */}
        {markers.map((marker) => (
          <Marker
            key={marker.id}
            coordinate={marker.coordinate}
            onPress={() => handleMarkerPress(marker)}
            title={marker.provider.business_name}
            description={`Rating: ${marker.provider.rating.toFixed(1)} ⭐`}
          >
            <View style={[styles.markerContainer, { backgroundColor: colors.primary?.default || '#5A7A63' }]}>
              <Text style={styles.markerText}>
                {marker.provider.business_name.charAt(0).toUpperCase()}
              </Text>
            </View>
          </Marker>
        ))}
      </MapView>
      
      {/* Results count */}
      {markers.length > 0 && (
        <View style={[styles.resultsCount, { backgroundColor: colors.background.secondary }]}>
          <Text style={[styles.resultsCountText, { color: colors.text.primary }]}>
            {markers.length} provider{markers.length !== 1 ? 's' : ''} found
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  map: {
    width: '100%',
    height: '100%',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  loadingText: {
    marginTop: getResponsiveSpacing(2),
    fontSize: getResponsiveFontSize(16),
    fontWeight: '500',
  },
  markerContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  markerText: {
    color: '#FFFFFF',
    fontSize: getResponsiveFontSize(16),
    fontWeight: 'bold',
  },
  resultsCount: {
    position: 'absolute',
    top: getResponsiveSpacing(2),
    left: getResponsiveSpacing(2),
    paddingHorizontal: getResponsiveSpacing(3),
    paddingVertical: getResponsiveSpacing(1),
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  resultsCountText: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '500',
  },
});

export default SearchMapView;
