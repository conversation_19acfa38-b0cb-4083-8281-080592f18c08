{"version": 3, "names": ["Animated", "require", "default", "_default", "exports"], "sources": ["Animated.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n * @oncall react_native\n */\n\nimport typeof * as AnimatedExports from './AnimatedExports';\n\n// The AnimatedExports module is typed as multiple exports to allow\n// for an implicit namespace, but underneath is's a single default export.\nconst Animated: AnimatedExports = (require('./AnimatedExports') as $FlowFixMe)\n  .default;\n\nexport default Animated;\n"], "mappings": ";;;;AAeA,IAAMA,QAAyB,GAAIC,OAAO,oBAAoB,CAAC,CAC5DC,OAAO;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAF,OAAA,GAEIF,QAAQ", "ignoreList": []}