{"version": 3, "names": ["_reactNative", "require", "isIOS", "exports", "Platform", "OS", "isAndroid", "isWeb", "safePlatformSelect", "platforms", "ios", "undefined", "android", "web", "default", "Error", "platformStyles", "shadow", "elevation", "arguments", "length", "shadowColor", "shadowOffset", "width", "height", "shadowOpacity", "shadowRadius", "borderRadius", "size", "radiusMap", "small", "medium", "large", "touchTarget", "statusBarHeight", "navigationBarHeight", "tabBarHeight", "PlatformConstants", "isIOSPlatform", "isAndroidPlatform", "isWebPlatform", "minTouchTarget", "defaultSpacing", "xs", "sm", "md", "lg", "xl", "animationDuration", "fast", "normal", "slow", "zIndex", "modal", "overlay", "dropdown", "header", "fab", "getPlatformProps", "component", "barStyle", "backgroundColor", "translucent", "bounces", "showsVerticalScrollIndicator", "showsHorizontalScrollIndicator", "overScrollMode", "clearButtonMode", "enablesReturnKeyAutomatically", "underlineColorAndroid", "textAlignVertical", "platformBehaviors", "supportsHaptics", "keyboard<PERSON><PERSON><PERSON><PERSON>", "modalPresentationStyle", "gestureEnabled", "platform", "select", "styles", "constants", "props", "behaviors", "is"], "sources": ["platformUtils.ts"], "sourcesContent": ["/**\n * Platform Utilities - Cross-Platform Compatibility\n *\n * Provides utilities for handling platform-specific differences\n * between iOS and Android in a safe and consistent manner.\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport { Platform } from 'react-native';\n\n// Platform detection\nexport const isIOS = Platform.OS === 'ios';\nexport const isAndroid = Platform.OS === 'android';\nexport const isWeb = Platform.OS === 'web';\n\n// Safe platform selection utility\nexport const safePlatformSelect = <T>(platforms: {\n  ios?: T;\n  android?: T;\n  web?: T;\n  default?: T;\n}): T => {\n  if (isIOS && platforms.ios !== undefined) {\n    return platforms.ios;\n  }\n\n  if (isAndroid && platforms.android !== undefined) {\n    return platforms.android;\n  }\n\n  if (isWeb && platforms.web !== undefined) {\n    return platforms.web;\n  }\n\n  if (platforms.default !== undefined) {\n    return platforms.default;\n  }\n\n  // Fallback to iOS value if available, then Android, then throw error\n  if (platforms.ios !== undefined) {\n    return platforms.ios;\n  }\n\n  if (platforms.android !== undefined) {\n    return platforms.android;\n  }\n\n  throw new Error(\n    'No platform-specific value provided and no default value specified',\n  );\n};\n\n// Platform-specific style helpers\nexport const platformStyles = {\n  // Shadow styles\n  shadow: (elevation: number = 4) =>\n    safePlatformSelect({\n      ios: {\n        shadowColor: '#000',\n        shadowOffset: { width: 0, height: elevation / 2 },\n        shadowOpacity: 0.1,\n        shadowRadius: elevation,\n      },\n      android: {\n        elevation,\n      },\n      default: {},\n    }),\n\n  // Border radius\n  borderRadius: (size: 'small' | 'medium' | 'large' = 'medium') => {\n    const radiusMap = {\n      small: safePlatformSelect({ ios: 8, android: 4, default: 6 }),\n      medium: safePlatformSelect({ ios: 12, android: 8, default: 10 }),\n      large: safePlatformSelect({ ios: 16, android: 12, default: 14 }),\n    };\n    return radiusMap[size];\n  },\n\n  // Touch target sizes\n  touchTarget: () =>\n    safePlatformSelect({\n      ios: 44,\n      android: 48,\n      default: 44,\n    }),\n\n  // Status bar height\n  statusBarHeight: () =>\n    safePlatformSelect({\n      ios: 20, // Will be overridden by safe area on newer devices\n      android: 24,\n      default: 20,\n    }),\n\n  // Navigation bar height\n  navigationBarHeight: () =>\n    safePlatformSelect({\n      ios: 44,\n      android: 56,\n      default: 44,\n    }),\n\n  // Tab bar height\n  tabBarHeight: () =>\n    safePlatformSelect({\n      ios: 49,\n      android: 56,\n      default: 49,\n    }),\n};\n\n// Platform-specific constants\nexport const PlatformConstants = {\n  isIOSPlatform: isIOS,\n  isAndroidPlatform: isAndroid,\n  isWebPlatform: isWeb,\n\n  // Minimum touch targets\n  minTouchTarget: platformStyles.touchTarget(),\n\n  // Default spacing\n  defaultSpacing: {\n    xs: 4,\n    sm: 8,\n    md: 16,\n    lg: 24,\n    xl: 32,\n  },\n\n  // Animation durations\n  animationDuration: {\n    fast: 200,\n    normal: 300,\n    slow: 500,\n  },\n\n  // Z-index layers\n  zIndex: {\n    modal: 1000,\n    overlay: 900,\n    dropdown: 800,\n    header: 700,\n    fab: 600,\n  },\n};\n\n// Platform-specific component props\nexport const getPlatformProps = (\n  component: 'StatusBar' | 'ScrollView' | 'TextInput',\n) => {\n  switch (component) {\n    case 'StatusBar':\n      return safePlatformSelect({\n        ios: {\n          barStyle: 'dark-content',\n          backgroundColor: 'transparent',\n        },\n        android: {\n          barStyle: 'dark-content',\n          backgroundColor: '#FFFFFF',\n          translucent: false,\n        },\n        default: {\n          barStyle: 'dark-content',\n        },\n      });\n\n    case 'ScrollView':\n      return safePlatformSelect({\n        ios: {\n          bounces: true,\n          showsVerticalScrollIndicator: false,\n          showsHorizontalScrollIndicator: false,\n        },\n        android: {\n          bounces: false,\n          showsVerticalScrollIndicator: false,\n          showsHorizontalScrollIndicator: false,\n          overScrollMode: 'never',\n        },\n        default: {\n          bounces: true,\n          showsVerticalScrollIndicator: false,\n          showsHorizontalScrollIndicator: false,\n        },\n      });\n\n    case 'TextInput':\n      return safePlatformSelect({\n        ios: {\n          clearButtonMode: 'while-editing',\n          enablesReturnKeyAutomatically: true,\n        },\n        android: {\n          underlineColorAndroid: 'transparent',\n          textAlignVertical: 'center',\n        },\n        default: {},\n      });\n\n    default:\n      return {};\n  }\n};\n\n// Platform-specific behavior helpers\nexport const platformBehaviors = {\n  // Haptic feedback\n  supportsHaptics: () => isIOS,\n\n  // Keyboard behavior\n  keyboardBehavior: () =>\n    safePlatformSelect({\n      ios: 'padding',\n      android: 'height',\n      default: 'padding',\n    }),\n\n  // Modal presentation\n  modalPresentationStyle: () =>\n    safePlatformSelect({\n      ios: 'pageSheet',\n      android: 'none',\n      default: 'none',\n    }),\n\n  // Navigation gestures\n  gestureEnabled: () =>\n    safePlatformSelect({\n      ios: true,\n      android: false,\n      default: true,\n    }),\n};\n\n// Export utility function for easy access\nexport const platform = {\n  select: safePlatformSelect,\n  styles: platformStyles,\n  constants: PlatformConstants,\n  props: getPlatformProps,\n  behaviors: platformBehaviors,\n  is: {\n    ios: isIOS,\n    android: isAndroid,\n    web: isWeb,\n  },\n};\n"], "mappings": ";;;;AAUA,IAAAA,YAAA,GAAAC,OAAA;AAGO,IAAMC,KAAK,GAAAC,OAAA,CAAAD,KAAA,GAAGE,qBAAQ,CAACC,EAAE,KAAK,KAAK;AACnC,IAAMC,SAAS,GAAAH,OAAA,CAAAG,SAAA,GAAGF,qBAAQ,CAACC,EAAE,KAAK,SAAS;AAC3C,IAAME,KAAK,GAAAJ,OAAA,CAAAI,KAAA,GAAGH,qBAAQ,CAACC,EAAE,KAAK,KAAK;AAGnC,IAAMG,kBAAkB,GAAAL,OAAA,CAAAK,kBAAA,GAAG,SAArBA,kBAAkBA,CAAOC,SAKrC,EAAQ;EACP,IAAIP,KAAK,IAAIO,SAAS,CAACC,GAAG,KAAKC,SAAS,EAAE;IACxC,OAAOF,SAAS,CAACC,GAAG;EACtB;EAEA,IAAIJ,SAAS,IAAIG,SAAS,CAACG,OAAO,KAAKD,SAAS,EAAE;IAChD,OAAOF,SAAS,CAACG,OAAO;EAC1B;EAEA,IAAIL,KAAK,IAAIE,SAAS,CAACI,GAAG,KAAKF,SAAS,EAAE;IACxC,OAAOF,SAAS,CAACI,GAAG;EACtB;EAEA,IAAIJ,SAAS,CAACK,OAAO,KAAKH,SAAS,EAAE;IACnC,OAAOF,SAAS,CAACK,OAAO;EAC1B;EAGA,IAAIL,SAAS,CAACC,GAAG,KAAKC,SAAS,EAAE;IAC/B,OAAOF,SAAS,CAACC,GAAG;EACtB;EAEA,IAAID,SAAS,CAACG,OAAO,KAAKD,SAAS,EAAE;IACnC,OAAOF,SAAS,CAACG,OAAO;EAC1B;EAEA,MAAM,IAAIG,KAAK,CACb,oEACF,CAAC;AACH,CAAC;AAGM,IAAMC,cAAc,GAAAb,OAAA,CAAAa,cAAA,GAAG;EAE5BC,MAAM,EAAE,SAARA,MAAMA,CAAA;IAAA,IAAGC,SAAiB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAR,SAAA,GAAAQ,SAAA,MAAG,CAAC;IAAA,OAC5BX,kBAAkB,CAAC;MACjBE,GAAG,EAAE;QACHW,WAAW,EAAE,MAAM;QACnBC,YAAY,EAAE;UAAEC,KAAK,EAAE,CAAC;UAAEC,MAAM,EAAEN,SAAS,GAAG;QAAE,CAAC;QACjDO,aAAa,EAAE,GAAG;QAClBC,YAAY,EAAER;MAChB,CAAC;MACDN,OAAO,EAAE;QACPM,SAAS,EAATA;MACF,CAAC;MACDJ,OAAO,EAAE,CAAC;IACZ,CAAC,CAAC;EAAA;EAGJa,YAAY,EAAE,SAAdA,YAAYA,CAAA,EAAqD;IAAA,IAAlDC,IAAkC,GAAAT,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAR,SAAA,GAAAQ,SAAA,MAAG,QAAQ;IAC1D,IAAMU,SAAS,GAAG;MAChBC,KAAK,EAAEtB,kBAAkB,CAAC;QAAEE,GAAG,EAAE,CAAC;QAAEE,OAAO,EAAE,CAAC;QAAEE,OAAO,EAAE;MAAE,CAAC,CAAC;MAC7DiB,MAAM,EAAEvB,kBAAkB,CAAC;QAAEE,GAAG,EAAE,EAAE;QAAEE,OAAO,EAAE,CAAC;QAAEE,OAAO,EAAE;MAAG,CAAC,CAAC;MAChEkB,KAAK,EAAExB,kBAAkB,CAAC;QAAEE,GAAG,EAAE,EAAE;QAAEE,OAAO,EAAE,EAAE;QAAEE,OAAO,EAAE;MAAG,CAAC;IACjE,CAAC;IACD,OAAOe,SAAS,CAACD,IAAI,CAAC;EACxB,CAAC;EAGDK,WAAW,EAAE,SAAbA,WAAWA,CAAA;IAAA,OACTzB,kBAAkB,CAAC;MACjBE,GAAG,EAAE,EAAE;MACPE,OAAO,EAAE,EAAE;MACXE,OAAO,EAAE;IACX,CAAC,CAAC;EAAA;EAGJoB,eAAe,EAAE,SAAjBA,eAAeA,CAAA;IAAA,OACb1B,kBAAkB,CAAC;MACjBE,GAAG,EAAE,EAAE;MACPE,OAAO,EAAE,EAAE;MACXE,OAAO,EAAE;IACX,CAAC,CAAC;EAAA;EAGJqB,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAA;IAAA,OACjB3B,kBAAkB,CAAC;MACjBE,GAAG,EAAE,EAAE;MACPE,OAAO,EAAE,EAAE;MACXE,OAAO,EAAE;IACX,CAAC,CAAC;EAAA;EAGJsB,YAAY,EAAE,SAAdA,YAAYA,CAAA;IAAA,OACV5B,kBAAkB,CAAC;MACjBE,GAAG,EAAE,EAAE;MACPE,OAAO,EAAE,EAAE;MACXE,OAAO,EAAE;IACX,CAAC,CAAC;EAAA;AACN,CAAC;AAGM,IAAMuB,iBAAiB,GAAAlC,OAAA,CAAAkC,iBAAA,GAAG;EAC/BC,aAAa,EAAEpC,KAAK;EACpBqC,iBAAiB,EAAEjC,SAAS;EAC5BkC,aAAa,EAAEjC,KAAK;EAGpBkC,cAAc,EAAEzB,cAAc,CAACiB,WAAW,CAAC,CAAC;EAG5CS,cAAc,EAAE;IACdC,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE,EAAE;IACNC,EAAE,EAAE,EAAE;IACNC,EAAE,EAAE;EACN,CAAC;EAGDC,iBAAiB,EAAE;IACjBC,IAAI,EAAE,GAAG;IACTC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE;EACR,CAAC;EAGDC,MAAM,EAAE;IACNC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAE,GAAG;IACZC,QAAQ,EAAE,GAAG;IACbC,MAAM,EAAE,GAAG;IACXC,GAAG,EAAE;EACP;AACF,CAAC;AAGM,IAAMC,gBAAgB,GAAAvD,OAAA,CAAAuD,gBAAA,GAAG,SAAnBA,gBAAgBA,CAC3BC,SAAmD,EAChD;EACH,QAAQA,SAAS;IACf,KAAK,WAAW;MACd,OAAOnD,kBAAkB,CAAC;QACxBE,GAAG,EAAE;UACHkD,QAAQ,EAAE,cAAc;UACxBC,eAAe,EAAE;QACnB,CAAC;QACDjD,OAAO,EAAE;UACPgD,QAAQ,EAAE,cAAc;UACxBC,eAAe,EAAE,SAAS;UAC1BC,WAAW,EAAE;QACf,CAAC;QACDhD,OAAO,EAAE;UACP8C,QAAQ,EAAE;QACZ;MACF,CAAC,CAAC;IAEJ,KAAK,YAAY;MACf,OAAOpD,kBAAkB,CAAC;QACxBE,GAAG,EAAE;UACHqD,OAAO,EAAE,IAAI;UACbC,4BAA4B,EAAE,KAAK;UACnCC,8BAA8B,EAAE;QAClC,CAAC;QACDrD,OAAO,EAAE;UACPmD,OAAO,EAAE,KAAK;UACdC,4BAA4B,EAAE,KAAK;UACnCC,8BAA8B,EAAE,KAAK;UACrCC,cAAc,EAAE;QAClB,CAAC;QACDpD,OAAO,EAAE;UACPiD,OAAO,EAAE,IAAI;UACbC,4BAA4B,EAAE,KAAK;UACnCC,8BAA8B,EAAE;QAClC;MACF,CAAC,CAAC;IAEJ,KAAK,WAAW;MACd,OAAOzD,kBAAkB,CAAC;QACxBE,GAAG,EAAE;UACHyD,eAAe,EAAE,eAAe;UAChCC,6BAA6B,EAAE;QACjC,CAAC;QACDxD,OAAO,EAAE;UACPyD,qBAAqB,EAAE,aAAa;UACpCC,iBAAiB,EAAE;QACrB,CAAC;QACDxD,OAAO,EAAE,CAAC;MACZ,CAAC,CAAC;IAEJ;MACE,OAAO,CAAC,CAAC;EACb;AACF,CAAC;AAGM,IAAMyD,iBAAiB,GAAApE,OAAA,CAAAoE,iBAAA,GAAG;EAE/BC,eAAe,EAAE,SAAjBA,eAAeA,CAAA;IAAA,OAAQtE,KAAK;EAAA;EAG5BuE,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAA;IAAA,OACdjE,kBAAkB,CAAC;MACjBE,GAAG,EAAE,SAAS;MACdE,OAAO,EAAE,QAAQ;MACjBE,OAAO,EAAE;IACX,CAAC,CAAC;EAAA;EAGJ4D,sBAAsB,EAAE,SAAxBA,sBAAsBA,CAAA;IAAA,OACpBlE,kBAAkB,CAAC;MACjBE,GAAG,EAAE,WAAW;MAChBE,OAAO,EAAE,MAAM;MACfE,OAAO,EAAE;IACX,CAAC,CAAC;EAAA;EAGJ6D,cAAc,EAAE,SAAhBA,cAAcA,CAAA;IAAA,OACZnE,kBAAkB,CAAC;MACjBE,GAAG,EAAE,IAAI;MACTE,OAAO,EAAE,KAAK;MACdE,OAAO,EAAE;IACX,CAAC,CAAC;EAAA;AACN,CAAC;AAGM,IAAM8D,QAAQ,GAAAzE,OAAA,CAAAyE,QAAA,GAAG;EACtBC,MAAM,EAAErE,kBAAkB;EAC1BsE,MAAM,EAAE9D,cAAc;EACtB+D,SAAS,EAAE1C,iBAAiB;EAC5B2C,KAAK,EAAEtB,gBAAgB;EACvBuB,SAAS,EAAEV,iBAAiB;EAC5BW,EAAE,EAAE;IACFxE,GAAG,EAAER,KAAK;IACVU,OAAO,EAAEN,SAAS;IAClBO,GAAG,EAAEN;EACP;AACF,CAAC", "ignoreList": []}