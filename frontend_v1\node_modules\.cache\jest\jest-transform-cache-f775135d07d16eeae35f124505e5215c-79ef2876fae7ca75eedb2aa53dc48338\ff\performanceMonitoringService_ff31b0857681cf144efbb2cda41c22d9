af7068d0aaa9136ed9cb9e519d210ffb
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.performanceMonitoringService = void 0;
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var PerformanceMonitoringService = function () {
  function PerformanceMonitoringService() {
    (0, _classCallCheck2.default)(this, PerformanceMonitoringService);
    this.metrics = [];
    this.alerts = [];
    this.observers = [];
    this.isMonitoring = false;
    this.reportingInterval = null;
    this.thresholds = [{
      metric: 'lcp',
      warning: 2500,
      critical: 4000,
      unit: 'ms'
    }, {
      metric: 'fid',
      warning: 100,
      critical: 300,
      unit: 'ms'
    }, {
      metric: 'cls',
      warning: 0.1,
      critical: 0.25,
      unit: 'score'
    }, {
      metric: 'fcp',
      warning: 1800,
      critical: 3000,
      unit: 'ms'
    }, {
      metric: 'ttfb',
      warning: 800,
      critical: 1800,
      unit: 'ms'
    }, {
      metric: 'memory_usage',
      warning: 50,
      critical: 80,
      unit: 'mb'
    }, {
      metric: 'bundle_size',
      warning: 1000,
      critical: 2000,
      unit: 'kb'
    }];
    this.sessionId = this.generateSessionId();
    this.initializeMonitoring();
  }
  return (0, _createClass2.default)(PerformanceMonitoringService, [{
    key: "startMonitoring",
    value: function startMonitoring() {
      if (this.isMonitoring) return;
      this.isMonitoring = true;
      this.setupPerformanceObservers();
      this.startPeriodicReporting();
      this.trackAppLaunch();
      console.log('[Performance] Monitoring started');
    }
  }, {
    key: "stopMonitoring",
    value: function stopMonitoring() {
      if (!this.isMonitoring) return;
      this.isMonitoring = false;
      this.cleanupObservers();
      this.stopPeriodicReporting();
      console.log('[Performance] Monitoring stopped');
    }
  }, {
    key: "trackMetric",
    value: function trackMetric(name, value) {
      var unit = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'ms';
      var context = arguments.length > 3 ? arguments[3] : undefined;
      var tags = arguments.length > 4 ? arguments[4] : undefined;
      var metric = {
        name: name,
        value: value,
        unit: unit,
        timestamp: Date.now(),
        context: context,
        tags: tags
      };
      this.metrics.push(metric);
      this.checkThresholds(metric);
      if (__DEV__) {
        console.log(`[Performance] ${name}: ${value}${unit}`, context);
      }
    }
  }, {
    key: "trackCustomMetric",
    value: function trackCustomMetric(name, value) {
      var unit = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'ms';
      var context = arguments.length > 3 ? arguments[3] : undefined;
      var tags = arguments.length > 4 ? arguments[4] : undefined;
      this.trackMetric(name, value, unit, context, tags);
    }
  }, {
    key: "trackScreenRender",
    value: function trackScreenRender(screenName, renderTime) {
      this.trackMetric('screen_render_time', renderTime, 'ms', {
        screen: screenName
      }, ['render', 'screen']);
    }
  }, {
    key: "trackApiRequest",
    value: function trackApiRequest(endpoint, method, duration, status, size) {
      this.trackMetric('api_request_duration', duration, 'ms', {
        endpoint: endpoint,
        method: method,
        status: status,
        size: size
      }, ['api', 'network']);
      if (size) {
        this.trackMetric('api_response_size', size, 'bytes', {
          endpoint: endpoint,
          method: method
        }, ['api', 'network', 'size']);
      }
    }
  }, {
    key: "trackMemoryUsage",
    value: function trackMemoryUsage() {
      if (typeof performance !== 'undefined' && 'memory' in performance) {
        var memory = performance.memory;
        this.trackMetric('memory_used', memory.usedJSHeapSize / 1024 / 1024, 'mb', {
          total: memory.totalJSHeapSize / 1024 / 1024,
          limit: memory.jsHeapSizeLimit / 1024 / 1024
        }, ['memory']);
      }
    }
  }, {
    key: "trackBundleSize",
    value: function trackBundleSize(size) {
      this.trackMetric('bundle_size', size / 1024, 'kb', {}, ['bundle', 'size']);
    }
  }, {
    key: "trackInteraction",
    value: function trackInteraction(type, duration, element) {
      this.trackMetric('interaction_duration', duration, 'ms', {
        type: type,
        element: element
      }, ['interaction', 'user']);
    }
  }, {
    key: "getPerformanceReport",
    value: function getPerformanceReport() {
      return {
        sessionId: this.sessionId,
        deviceInfo: this.getDeviceInfo(),
        networkInfo: this.getNetworkInfo(),
        appInfo: this.getAppInfo(),
        metrics: (0, _toConsumableArray2.default)(this.metrics),
        coreWebVitals: this.getCoreWebVitals(),
        customMetrics: this.getCustomMetrics(),
        errors: [],
        timestamp: Date.now()
      };
    }
  }, {
    key: "getAlerts",
    value: function getAlerts() {
      return (0, _toConsumableArray2.default)(this.alerts);
    }
  }, {
    key: "clearData",
    value: function clearData() {
      this.metrics = [];
      this.alerts = [];
      this.sessionId = this.generateSessionId();
    }
  }, {
    key: "exportData",
    value: function exportData() {
      var report = this.getPerformanceReport();
      return JSON.stringify(report, null, 2);
    }
  }, {
    key: "initializeMonitoring",
    value: function initializeMonitoring() {
      if (!__DEV__) {
        this.startMonitoring();
      }
    }
  }, {
    key: "setupPerformanceObservers",
    value: function setupPerformanceObservers() {}
  }, {
    key: "cleanupObservers",
    value: function cleanupObservers() {
      this.observers.forEach(function (observer) {
        try {
          observer.disconnect();
        } catch (error) {
          console.warn('[Performance] Failed to disconnect observer:', error);
        }
      });
      this.observers = [];
    }
  }, {
    key: "startPeriodicReporting",
    value: function startPeriodicReporting() {
      var _this = this;
      this.reportingInterval = setInterval(function () {
        _this.trackMemoryUsage();
        _this.sendPerformanceReport();
      }, 30000);
    }
  }, {
    key: "stopPeriodicReporting",
    value: function stopPeriodicReporting() {
      if (this.reportingInterval) {
        clearInterval(this.reportingInterval);
        this.reportingInterval = null;
      }
    }
  }, {
    key: "trackAppLaunch",
    value: function trackAppLaunch() {
      var launchTime = Date.now();
      this.trackMetric('app_launch_time', launchTime, 'ms', {}, ['launch']);
    }
  }, {
    key: "checkThresholds",
    value: function checkThresholds(metric) {
      var threshold = this.thresholds.find(function (t) {
        return t.metric === metric.name;
      });
      if (!threshold) return;
      var alertType = null;
      var thresholdValue = 0;
      if (metric.value >= threshold.critical) {
        alertType = 'critical';
        thresholdValue = threshold.critical;
      } else if (metric.value >= threshold.warning) {
        alertType = 'warning';
        thresholdValue = threshold.warning;
      }
      if (alertType) {
        var alert = {
          id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: alertType,
          metric: metric.name,
          value: metric.value,
          threshold: thresholdValue,
          message: `${metric.name} exceeded ${alertType} threshold: ${metric.value}${metric.unit} > ${thresholdValue}${threshold.unit}`,
          timestamp: Date.now(),
          resolved: false
        };
        this.alerts.push(alert);
        if (__DEV__) {
          console.warn(`[Performance Alert] ${alert.message}`);
        }
      }
    }
  }, {
    key: "sendPerformanceReport",
    value: function sendPerformanceReport() {
      var report = this.getPerformanceReport();
      if (__DEV__) {
        console.log('[Performance] Report generated:', {
          metrics: report.metrics.length,
          alerts: this.alerts.length
        });
      }
    }
  }, {
    key: "getDeviceInfo",
    value: function getDeviceInfo() {
      return {
        platform: 'ios',
        osVersion: '17.0',
        deviceModel: 'iPhone 15',
        screenSize: {
          width: 393,
          height: 852
        },
        isLowEndDevice: false
      };
    }
  }, {
    key: "getNetworkInfo",
    value: function getNetworkInfo() {
      return {
        type: 'wifi',
        effectiveType: '4g'
      };
    }
  }, {
    key: "getAppInfo",
    value: function getAppInfo() {
      return {
        version: '1.0.0',
        buildNumber: '1',
        environment: __DEV__ ? 'development' : 'production'
      };
    }
  }, {
    key: "getCoreWebVitals",
    value: function getCoreWebVitals() {
      var _this2 = this;
      var getMetricValue = function getMetricValue(name) {
        var metric = _this2.metrics.find(function (m) {
          return m.name === name;
        });
        return metric ? metric.value : 0;
      };
      return {
        lcp: getMetricValue('lcp'),
        fid: getMetricValue('fid'),
        cls: getMetricValue('cls'),
        fcp: getMetricValue('fcp'),
        ttfb: getMetricValue('ttfb')
      };
    }
  }, {
    key: "getCustomMetrics",
    value: function getCustomMetrics() {
      var customMetrics = {};
      this.metrics.forEach(function (metric) {
        if (!['lcp', 'fid', 'cls', 'fcp', 'ttfb'].includes(metric.name)) {
          customMetrics[metric.name] = metric.value;
        }
      });
      return customMetrics;
    }
  }, {
    key: "generateSessionId",
    value: function generateSessionId() {
      return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }]);
}();
var performanceMonitoringService = exports.performanceMonitoringService = new PerformanceMonitoringService();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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