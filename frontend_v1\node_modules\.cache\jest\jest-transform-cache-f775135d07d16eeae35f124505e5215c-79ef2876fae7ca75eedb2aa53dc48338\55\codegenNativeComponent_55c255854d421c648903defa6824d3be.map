{"version": 3, "names": ["_requireNativeComponent", "_interopRequireDefault", "require", "_UIManager", "codegenNativeComponent", "componentName", "options", "global", "RN$Bridgeless", "__DEV__", "console", "warn", "componentNameInUse", "paperComponentName", "paperComponentNameDeprecated", "UIManager", "hasViewManagerConfig", "_options$paperCompone", "Error", "requireNativeComponent", "_default", "exports", "default"], "sources": ["codegenNativeComponent.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\n// TODO: move this file to shims/ReactNative (requires React update and sync)\n\nimport type {HostComponent} from '../../src/private/types/HostComponent';\n\nimport requireNativeComponent from '../../Libraries/ReactNative/requireNativeComponent';\nimport UIManager from '../ReactNative/UIManager';\n\n// TODO: import from CodegenSchema once workspaces are enabled\ntype Options = $ReadOnly<{\n  interfaceOnly?: boolean,\n  paperComponentName?: string,\n  paperComponentNameDeprecated?: string,\n  excludedPlatforms?: $ReadOnlyArray<'iOS' | 'android'>,\n}>;\n\nexport type NativeComponentType<T> = HostComponent<T>;\n\n// If this function runs then that means the view configs were not\n// generated at build time using `GenerateViewConfigJs.js`. Thus\n// we need to `requireNativeComponent` to get the view configs from view managers.\n// `requireNativeComponent` is not available in Bridgeless mode.\n// e.g. This function runs at runtime if `codegenNativeComponent` was not called\n// from a file suffixed with NativeComponent.js.\nfunction codegenNativeComponent<Props: {...}>(\n  componentName: string,\n  options?: Options,\n): NativeComponentType<Props> {\n  if (global.RN$Bridgeless === true && __DEV__) {\n    console.warn(\n      `Codegen didn't run for ${componentName}. This will be an error in the future. Make sure you are using @react-native/babel-preset when building your JavaScript code.`,\n    );\n  }\n\n  let componentNameInUse =\n    options && options.paperComponentName != null\n      ? options.paperComponentName\n      : componentName;\n\n  if (options != null && options.paperComponentNameDeprecated != null) {\n    if (UIManager.hasViewManagerConfig(componentName)) {\n      componentNameInUse = componentName;\n    } else if (\n      options.paperComponentNameDeprecated != null &&\n      UIManager.hasViewManagerConfig(options.paperComponentNameDeprecated)\n    ) {\n      // $FlowFixMe[incompatible-type]\n      componentNameInUse = options.paperComponentNameDeprecated;\n    } else {\n      throw new Error(\n        `Failed to find native component for either ${componentName} or ${\n          options.paperComponentNameDeprecated ?? '(unknown)'\n        }`,\n      );\n    }\n  }\n\n  return (requireNativeComponent<Props>(\n    // $FlowFixMe[incompatible-call]\n    componentNameInUse,\n  ): HostComponent<Props>);\n}\n\nexport default codegenNativeComponent;\n"], "mappings": ";;;;;AAcA,IAAAA,uBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAF,sBAAA,CAAAC,OAAA;AAkBA,SAASE,sBAAsBA,CAC7BC,aAAqB,EACrBC,OAAiB,EACW;EAC5B,IAAIC,MAAM,CAACC,aAAa,KAAK,IAAI,IAAIC,OAAO,EAAE;IAC5CC,OAAO,CAACC,IAAI,CACV,0BAA0BN,aAAa,+HACzC,CAAC;EACH;EAEA,IAAIO,kBAAkB,GACpBN,OAAO,IAAIA,OAAO,CAACO,kBAAkB,IAAI,IAAI,GACzCP,OAAO,CAACO,kBAAkB,GAC1BR,aAAa;EAEnB,IAAIC,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACQ,4BAA4B,IAAI,IAAI,EAAE;IACnE,IAAIC,kBAAS,CAACC,oBAAoB,CAACX,aAAa,CAAC,EAAE;MACjDO,kBAAkB,GAAGP,aAAa;IACpC,CAAC,MAAM,IACLC,OAAO,CAACQ,4BAA4B,IAAI,IAAI,IAC5CC,kBAAS,CAACC,oBAAoB,CAACV,OAAO,CAACQ,4BAA4B,CAAC,EACpE;MAEAF,kBAAkB,GAAGN,OAAO,CAACQ,4BAA4B;IAC3D,CAAC,MAAM;MAAA,IAAAG,qBAAA;MACL,MAAM,IAAIC,KAAK,CACb,8CAA8Cb,aAAa,QAAAY,qBAAA,GACzDX,OAAO,CAACQ,4BAA4B,YAAAG,qBAAA,GAAI,WAAW,EAEvD,CAAC;IACH;EACF;EAEA,OAAQ,IAAAE,+BAAsB,EAE5BP,kBACF,CAAC;AACH;AAAC,IAAAQ,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEclB,sBAAsB", "ignoreList": []}