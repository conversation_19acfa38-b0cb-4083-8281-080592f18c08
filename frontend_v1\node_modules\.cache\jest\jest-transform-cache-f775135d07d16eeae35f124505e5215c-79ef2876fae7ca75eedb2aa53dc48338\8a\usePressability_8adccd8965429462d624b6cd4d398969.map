{"version": 3, "names": ["_Pressability", "_interopRequireDefault", "require", "_react", "usePressability", "config", "pressabilityRef", "useRef", "current", "Pressability", "pressability", "useEffect", "configure", "reset", "getEventHandlers"], "sources": ["usePressability.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport Pressability, {\n  type EventHandlers,\n  type PressabilityConfig,\n} from './Pressability';\nimport {useEffect, useRef} from 'react';\n\ndeclare function usePressability(config: PressabilityConfig): EventHandlers;\ndeclare function usePressability(config: null | void): null | EventHandlers;\n\n/**\n * Creates a persistent instance of `Pressability` that automatically configures\n * itself and resets. Accepts null `config` to support lazy initialization. Once\n * initialized, will not un-initialize until the component has been unmounted.\n *\n * In order to use `usePressability`, do the following:\n *\n *   const config = useMemo(...);\n *   const eventHandlers = usePressability(config);\n *   const pressableView = <View {...eventHandlers} />;\n *\n */\nexport default function usePressability(\n  config: ?PressabilityConfig,\n): null | EventHandlers {\n  const pressabilityRef = useRef<?Pressability>(null);\n  if (config != null && pressabilityRef.current == null) {\n    pressabilityRef.current = new Pressability(config);\n  }\n  const pressability = pressabilityRef.current;\n\n  // On the initial mount, this is a no-op. On updates, `pressability` will be\n  // re-configured to use the new configuration.\n  useEffect(() => {\n    if (config != null && pressability != null) {\n      pressability.configure(config);\n    }\n  }, [config, pressability]);\n\n  // On unmount, reset pending state and timers inside `pressability`. This is\n  // a separate effect because we do not want to reset when `config` changes.\n  useEffect(() => {\n    if (pressability != null) {\n      return () => {\n        pressability.reset();\n      };\n    }\n  }, [pressability]);\n\n  return pressability == null ? null : pressability.getEventHandlers();\n}\n"], "mappings": ";;;;;AAUA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AAIA,IAAAC,MAAA,GAAAD,OAAA;AAiBe,SAASE,eAAeA,CACrCC,MAA2B,EACL;EACtB,IAAMC,eAAe,GAAG,IAAAC,aAAM,EAAgB,IAAI,CAAC;EACnD,IAAIF,MAAM,IAAI,IAAI,IAAIC,eAAe,CAACE,OAAO,IAAI,IAAI,EAAE;IACrDF,eAAe,CAACE,OAAO,GAAG,IAAIC,qBAAY,CAACJ,MAAM,CAAC;EACpD;EACA,IAAMK,YAAY,GAAGJ,eAAe,CAACE,OAAO;EAI5C,IAAAG,gBAAS,EAAC,YAAM;IACd,IAAIN,MAAM,IAAI,IAAI,IAAIK,YAAY,IAAI,IAAI,EAAE;MAC1CA,YAAY,CAACE,SAAS,CAACP,MAAM,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,MAAM,EAAEK,YAAY,CAAC,CAAC;EAI1B,IAAAC,gBAAS,EAAC,YAAM;IACd,IAAID,YAAY,IAAI,IAAI,EAAE;MACxB,OAAO,YAAM;QACXA,YAAY,CAACG,KAAK,CAAC,CAAC;MACtB,CAAC;IACH;EACF,CAAC,EAAE,CAACH,YAAY,CAAC,CAAC;EAElB,OAAOA,YAAY,IAAI,IAAI,GAAG,IAAI,GAAGA,YAAY,CAACI,gBAAgB,CAAC,CAAC;AACtE", "ignoreList": []}