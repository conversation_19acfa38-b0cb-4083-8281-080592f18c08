a63141bdc55d30115d148dd6e7d602ae
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _View = _interopRequireDefault(require("../../Components/View/View"));
var _Pressability = _interopRequireDefault(require("../../Pressability/Pressability"));
var _PressabilityDebug = require("../../Pressability/PressabilityDebug");
var _RendererProxy = require("../../ReactNative/RendererProxy");
var _processColor = _interopRequireDefault(require("../../StyleSheet/processColor"));
var _Platform = _interopRequireDefault(require("../../Utilities/Platform"));
var _ViewNativeComponent = require("../View/ViewNativeComponent");
var _invariant = _interopRequireDefault(require("invariant"));
var React = _interopRequireWildcard(require("react"));
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["onBlur", "onFocus"];
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var TouchableNativeFeedback = function (_React$Component) {
  function TouchableNativeFeedback() {
    var _this;
    (0, _classCallCheck2.default)(this, TouchableNativeFeedback);
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _callSuper(this, TouchableNativeFeedback, [].concat(args));
    _this.state = {
      pressability: new _Pressability.default(_this._createPressabilityConfig())
    };
    return _this;
  }
  (0, _inherits2.default)(TouchableNativeFeedback, _React$Component);
  return (0, _createClass2.default)(TouchableNativeFeedback, [{
    key: "_createPressabilityConfig",
    value: function _createPressabilityConfig() {
      var _this$props$ariaDisa,
        _this$props$accessibi,
        _this2 = this;
      var accessibilityStateDisabled = (_this$props$ariaDisa = this.props['aria-disabled']) != null ? _this$props$ariaDisa : (_this$props$accessibi = this.props.accessibilityState) == null ? void 0 : _this$props$accessibi.disabled;
      return {
        cancelable: !this.props.rejectResponderTermination,
        disabled: this.props.disabled != null ? this.props.disabled : accessibilityStateDisabled,
        hitSlop: this.props.hitSlop,
        delayLongPress: this.props.delayLongPress,
        delayPressIn: this.props.delayPressIn,
        delayPressOut: this.props.delayPressOut,
        minPressDuration: 0,
        pressRectOffset: this.props.pressRetentionOffset,
        android_disableSound: this.props.touchSoundDisabled,
        onLongPress: this.props.onLongPress,
        onPress: this.props.onPress,
        onPressIn: function onPressIn(event) {
          if (_Platform.default.OS === 'android') {
            _this2._dispatchHotspotUpdate(event);
            _this2._dispatchPressedStateChange(true);
          }
          if (_this2.props.onPressIn != null) {
            _this2.props.onPressIn(event);
          }
        },
        onPressMove: function onPressMove(event) {
          if (_Platform.default.OS === 'android') {
            _this2._dispatchHotspotUpdate(event);
          }
        },
        onPressOut: function onPressOut(event) {
          if (_Platform.default.OS === 'android') {
            _this2._dispatchPressedStateChange(false);
          }
          if (_this2.props.onPressOut != null) {
            _this2.props.onPressOut(event);
          }
        }
      };
    }
  }, {
    key: "_dispatchPressedStateChange",
    value: function _dispatchPressedStateChange(pressed) {
      if (_Platform.default.OS === 'android') {
        var hostComponentRef = (0, _RendererProxy.findHostInstance_DEPRECATED)(this);
        if (hostComponentRef == null) {
          console.warn('Touchable: Unable to find HostComponent instance. ' + 'Has your Touchable component been unmounted?');
        } else {
          _ViewNativeComponent.Commands.setPressed(hostComponentRef, pressed);
        }
      }
    }
  }, {
    key: "_dispatchHotspotUpdate",
    value: function _dispatchHotspotUpdate(event) {
      if (_Platform.default.OS === 'android') {
        var _event$nativeEvent = event.nativeEvent,
          locationX = _event$nativeEvent.locationX,
          locationY = _event$nativeEvent.locationY;
        var hostComponentRef = (0, _RendererProxy.findHostInstance_DEPRECATED)(this);
        if (hostComponentRef == null) {
          console.warn('Touchable: Unable to find HostComponent instance. ' + 'Has your Touchable component been unmounted?');
        } else {
          _ViewNativeComponent.Commands.hotspotUpdate(hostComponentRef, locationX != null ? locationX : 0, locationY != null ? locationY : 0);
        }
      }
    }
  }, {
    key: "render",
    value: function render() {
      var _this$props$ariaBusy, _this$props$accessibi2, _this$props$ariaChec, _this$props$accessibi3, _this$props$ariaDisa2, _this$props$accessibi4, _this$props$ariaExpa, _this$props$accessibi5, _this$props$ariaSele, _this$props$accessibi6, _this$props$ariaValu, _this$props$accessibi7, _this$props$ariaValu2, _this$props$accessibi8, _this$props$ariaValu3, _this$props$accessibi9, _this$props$ariaValu4, _this$props$accessibi0, _this$props$ariaLive, _this$props$ariaLabe, _this$props$ariaModa, _this$props$ariaHidd, _this$props$id;
      var element = React.Children.only(this.props.children);
      var children = [element.props.children];
      if (__DEV__) {
        if (element.type === _View.default) {
          children.push((0, _jsxRuntime.jsx)(_PressabilityDebug.PressabilityDebugView, {
            color: "brown",
            hitSlop: this.props.hitSlop
          }));
        }
      }
      var _this$state$pressabil = this.state.pressability.getEventHandlers(),
        onBlur = _this$state$pressabil.onBlur,
        onFocus = _this$state$pressabil.onFocus,
        eventHandlersWithoutBlurAndFocus = (0, _objectWithoutProperties2.default)(_this$state$pressabil, _excluded);
      var _accessibilityState = {
        busy: (_this$props$ariaBusy = this.props['aria-busy']) != null ? _this$props$ariaBusy : (_this$props$accessibi2 = this.props.accessibilityState) == null ? void 0 : _this$props$accessibi2.busy,
        checked: (_this$props$ariaChec = this.props['aria-checked']) != null ? _this$props$ariaChec : (_this$props$accessibi3 = this.props.accessibilityState) == null ? void 0 : _this$props$accessibi3.checked,
        disabled: (_this$props$ariaDisa2 = this.props['aria-disabled']) != null ? _this$props$ariaDisa2 : (_this$props$accessibi4 = this.props.accessibilityState) == null ? void 0 : _this$props$accessibi4.disabled,
        expanded: (_this$props$ariaExpa = this.props['aria-expanded']) != null ? _this$props$ariaExpa : (_this$props$accessibi5 = this.props.accessibilityState) == null ? void 0 : _this$props$accessibi5.expanded,
        selected: (_this$props$ariaSele = this.props['aria-selected']) != null ? _this$props$ariaSele : (_this$props$accessibi6 = this.props.accessibilityState) == null ? void 0 : _this$props$accessibi6.selected
      };
      _accessibilityState = this.props.disabled != null ? Object.assign({}, _accessibilityState, {
        disabled: this.props.disabled
      }) : _accessibilityState;
      var accessibilityValue = {
        max: (_this$props$ariaValu = this.props['aria-valuemax']) != null ? _this$props$ariaValu : (_this$props$accessibi7 = this.props.accessibilityValue) == null ? void 0 : _this$props$accessibi7.max,
        min: (_this$props$ariaValu2 = this.props['aria-valuemin']) != null ? _this$props$ariaValu2 : (_this$props$accessibi8 = this.props.accessibilityValue) == null ? void 0 : _this$props$accessibi8.min,
        now: (_this$props$ariaValu3 = this.props['aria-valuenow']) != null ? _this$props$ariaValu3 : (_this$props$accessibi9 = this.props.accessibilityValue) == null ? void 0 : _this$props$accessibi9.now,
        text: (_this$props$ariaValu4 = this.props['aria-valuetext']) != null ? _this$props$ariaValu4 : (_this$props$accessibi0 = this.props.accessibilityValue) == null ? void 0 : _this$props$accessibi0.text
      };
      var accessibilityLiveRegion = this.props['aria-live'] === 'off' ? 'none' : (_this$props$ariaLive = this.props['aria-live']) != null ? _this$props$ariaLive : this.props.accessibilityLiveRegion;
      var accessibilityLabel = (_this$props$ariaLabe = this.props['aria-label']) != null ? _this$props$ariaLabe : this.props.accessibilityLabel;
      return React.cloneElement.apply(React, [element, Object.assign({}, eventHandlersWithoutBlurAndFocus, getBackgroundProp(this.props.background === undefined ? TouchableNativeFeedback.SelectableBackground() : this.props.background, this.props.useForeground === true), {
        accessible: this.props.accessible !== false,
        accessibilityHint: this.props.accessibilityHint,
        accessibilityLanguage: this.props.accessibilityLanguage,
        accessibilityLabel: accessibilityLabel,
        accessibilityRole: this.props.accessibilityRole,
        accessibilityState: _accessibilityState,
        accessibilityActions: this.props.accessibilityActions,
        onAccessibilityAction: this.props.onAccessibilityAction,
        accessibilityValue: accessibilityValue,
        importantForAccessibility: this.props['aria-hidden'] === true ? 'no-hide-descendants' : this.props.importantForAccessibility,
        accessibilityViewIsModal: (_this$props$ariaModa = this.props['aria-modal']) != null ? _this$props$ariaModa : this.props.accessibilityViewIsModal,
        accessibilityLiveRegion: accessibilityLiveRegion,
        accessibilityElementsHidden: (_this$props$ariaHidd = this.props['aria-hidden']) != null ? _this$props$ariaHidd : this.props.accessibilityElementsHidden,
        hasTVPreferredFocus: this.props.hasTVPreferredFocus,
        hitSlop: this.props.hitSlop,
        focusable: this.props.focusable !== false && this.props.onPress !== undefined && !this.props.disabled,
        nativeID: (_this$props$id = this.props.id) != null ? _this$props$id : this.props.nativeID,
        nextFocusDown: this.props.nextFocusDown,
        nextFocusForward: this.props.nextFocusForward,
        nextFocusLeft: this.props.nextFocusLeft,
        nextFocusRight: this.props.nextFocusRight,
        nextFocusUp: this.props.nextFocusUp,
        onLayout: this.props.onLayout,
        testID: this.props.testID
      })].concat(children));
    }
  }, {
    key: "componentDidUpdate",
    value: function componentDidUpdate(prevProps, prevState) {
      this.state.pressability.configure(this._createPressabilityConfig());
    }
  }, {
    key: "componentDidMount",
    value: function componentDidMount() {
      this.state.pressability.configure(this._createPressabilityConfig());
    }
  }, {
    key: "componentWillUnmount",
    value: function componentWillUnmount() {
      this.state.pressability.reset();
    }
  }]);
}(React.Component);
TouchableNativeFeedback.SelectableBackground = function (rippleRadius) {
  return {
    type: 'ThemeAttrAndroid',
    attribute: 'selectableItemBackground',
    rippleRadius: rippleRadius
  };
};
TouchableNativeFeedback.SelectableBackgroundBorderless = function (rippleRadius) {
  return {
    type: 'ThemeAttrAndroid',
    attribute: 'selectableItemBackgroundBorderless',
    rippleRadius: rippleRadius
  };
};
TouchableNativeFeedback.Ripple = function (color, borderless, rippleRadius) {
  var processedColor = (0, _processColor.default)(color);
  (0, _invariant.default)(processedColor == null || typeof processedColor === 'number', 'Unexpected color given for Ripple color');
  return {
    type: 'RippleAndroid',
    color: processedColor,
    borderless: borderless,
    rippleRadius: rippleRadius
  };
};
TouchableNativeFeedback.canUseNativeForeground = function () {
  return _Platform.default.OS === 'android';
};
var getBackgroundProp = _Platform.default.OS === 'android' ? function (background, useForeground) {
  return useForeground && TouchableNativeFeedback.canUseNativeForeground() ? {
    nativeForegroundAndroid: background
  } : {
    nativeBackgroundAndroid: background
  };
} : function (background, useForeground) {
  return null;
};
TouchableNativeFeedback.displayName = 'TouchableNativeFeedback';
var _default = exports.default = TouchableNativeFeedback;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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