{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_Box", "_jsxRuntime", "_excluded", "Card", "exports", "_ref", "children", "props", "_objectWithoutProperties2", "default", "jsx", "Box", "Object", "assign", "backgroundColor", "padding", "borderRadius"], "sources": ["Card.tsx"], "sourcesContent": ["/**\n * Card Component - Styled Container Atom\n *\n * A styled container component that provides card-like appearance\n * with consistent styling across the application.\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport React from 'react';\n\nimport { Box, BoxProps } from './Box';\n\nexport interface CardProps extends BoxProps {\n  children: React.ReactNode;\n}\n\nexport const Card: React.FC<CardProps> = ({ children, ...props }) => {\n  return (\n    <Box\n      backgroundColor=\"surface\"\n      padding=\"medium\"\n      borderRadius=\"medium\"\n      {...props}>\n      {children}\n    </Box>\n  );\n};\n"], "mappings": ";;;;;;AAUA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,IAAA,GAAAD,OAAA;AAAsC,IAAAE,WAAA,GAAAF,OAAA;AAAA,IAAAG,SAAA;AAM/B,IAAMC,IAAyB,GAAAC,OAAA,CAAAD,IAAA,GAAG,SAA5BA,IAAyBA,CAAAE,IAAA,EAA+B;EAAA,IAAzBC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IAAKC,KAAK,OAAAC,yBAAA,CAAAC,OAAA,EAAAJ,IAAA,EAAAH,SAAA;EAC5D,OACE,IAAAD,WAAA,CAAAS,GAAA,EAACV,IAAA,CAAAW,GAAG,EAAAC,MAAA,CAAAC,MAAA;IACFC,eAAe,EAAC,SAAS;IACzBC,OAAO,EAAC,QAAQ;IAChBC,YAAY,EAAC;EAAQ,GACjBT,KAAK;IAAAD,QAAA,EACRA;EAAQ,EACN,CAAC;AAEV,CAAC", "ignoreList": []}