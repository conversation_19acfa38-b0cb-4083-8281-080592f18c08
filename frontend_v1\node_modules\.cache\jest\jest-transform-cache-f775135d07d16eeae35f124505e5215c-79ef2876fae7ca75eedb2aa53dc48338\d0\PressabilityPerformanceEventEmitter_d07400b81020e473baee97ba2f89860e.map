{"version": 3, "names": ["PressabilityPerformanceEventEmitter", "_classCallCheck2", "default", "_listeners", "_createClass2", "key", "value", "addListener", "listener", "push", "removeListener", "index", "indexOf", "splice", "emitEvent", "constructEvent", "length", "event", "for<PERSON>ach", "PressabilityPerformanceEventEmitterSingleton", "_default", "exports"], "sources": ["PressabilityPerformanceEventEmitter.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport {type PressabilityTouchSignal as TouchSignal} from './PressabilityTypes.js';\n\nexport type PressabilityPerformanceEvent = $ReadOnly<{\n  signal: TouchSignal,\n  nativeTimestamp: number,\n}>;\nexport type PressabilityPerformanceEventListener =\n  PressabilityPerformanceEvent => void;\n\nclass PressabilityPerformanceEventEmitter {\n  _listeners: Array<PressabilityPerformanceEventListener> = [];\n\n  constructor() {}\n\n  addListener(listener: PressabilityPerformanceEventListener): void {\n    this._listeners.push(listener);\n  }\n\n  removeListener(listener: PressabilityPerformanceEventListener): void {\n    const index = this._listeners.indexOf(listener);\n    if (index > -1) {\n      this._listeners.splice(index, 1);\n    }\n  }\n\n  emitEvent(constructEvent: () => PressabilityPerformanceEvent): void {\n    if (this._listeners.length === 0) {\n      return;\n    }\n\n    const event = constructEvent();\n    this._listeners.forEach(listener => listener(event));\n  }\n}\n\nconst PressabilityPerformanceEventEmitterSingleton: PressabilityPerformanceEventEmitter =\n  new PressabilityPerformanceEventEmitter();\n\nexport default PressabilityPerformanceEventEmitterSingleton;\n"], "mappings": ";;;;;;;IAmBMA,mCAAmC;EAGvC,SAAAA,oCAAA,EAAc;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAF,mCAAA;IAAA,KAFdG,UAAU,GAAgD,EAAE;EAE7C;EAAC,WAAAC,aAAA,CAAAF,OAAA,EAAAF,mCAAA;IAAAK,GAAA;IAAAC,KAAA,EAEhB,SAAAC,WAAWA,CAACC,QAA8C,EAAQ;MAChE,IAAI,CAACL,UAAU,CAACM,IAAI,CAACD,QAAQ,CAAC;IAChC;EAAC;IAAAH,GAAA;IAAAC,KAAA,EAED,SAAAI,cAAcA,CAACF,QAA8C,EAAQ;MACnE,IAAMG,KAAK,GAAG,IAAI,CAACR,UAAU,CAACS,OAAO,CAACJ,QAAQ,CAAC;MAC/C,IAAIG,KAAK,GAAG,CAAC,CAAC,EAAE;QACd,IAAI,CAACR,UAAU,CAACU,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAClC;IACF;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAED,SAAAQ,SAASA,CAACC,cAAkD,EAAQ;MAClE,IAAI,IAAI,CAACZ,UAAU,CAACa,MAAM,KAAK,CAAC,EAAE;QAChC;MACF;MAEA,IAAMC,KAAK,GAAGF,cAAc,CAAC,CAAC;MAC9B,IAAI,CAACZ,UAAU,CAACe,OAAO,CAAC,UAAAV,QAAQ;QAAA,OAAIA,QAAQ,CAACS,KAAK,CAAC;MAAA,EAAC;IACtD;EAAC;AAAA;AAGH,IAAME,4CAAiF,GACrF,IAAInB,mCAAmC,CAAC,CAAC;AAAC,IAAAoB,QAAA,GAAAC,OAAA,CAAAnB,OAAA,GAE7BiB,4CAA4C", "ignoreList": []}