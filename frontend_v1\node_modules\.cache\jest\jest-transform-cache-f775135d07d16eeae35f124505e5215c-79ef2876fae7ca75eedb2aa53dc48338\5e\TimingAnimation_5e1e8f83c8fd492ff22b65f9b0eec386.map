{"version": 3, "names": ["_AnimatedColor", "_interopRequireDefault", "require", "_Animation2", "_callSuper", "t", "o", "e", "_getPrototypeOf2", "default", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "_get2", "_easeInOut", "easeInOut", "Easing", "inOut", "ease", "TimingAnimation", "exports", "_Animation", "config", "_config$easing", "_config$duration", "_config$delay", "_this", "_classCallCheck2", "_toValue", "toValue", "_easing", "easing", "_duration", "duration", "_delay", "delay", "_platformConfig", "platformConfig", "_inherits2", "_createClass2", "key", "value", "__getNativeAnimationConfig", "frameDuration", "frames", "numFrames", "Math", "round", "frame", "push", "type", "iterations", "__iterations", "debugID", "__getDebugID", "start", "fromValue", "onUpdate", "onEnd", "previousAnimation", "animatedValue", "_this2", "_fromValue", "_onUpdate", "_startTime", "Date", "now", "useNativeDriver", "__startAnimationIfNative", "__notifyAnimationEnd", "finished", "_animationFrame", "requestAnimationFrame", "_timeout", "setTimeout", "__active", "bind", "stop", "clearTimeout", "global", "cancelAnimationFrame", "Animation"], "sources": ["TimingAnimation.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {PlatformConfig} from '../AnimatedPlatformConfig';\nimport type {RgbaValue} from '../nodes/AnimatedColor';\nimport type AnimatedInterpolation from '../nodes/AnimatedInterpolation';\nimport type AnimatedValue from '../nodes/AnimatedValue';\nimport type AnimatedValueXY from '../nodes/AnimatedValueXY';\nimport type {AnimationConfig, EndCallback} from './Animation';\n\nimport AnimatedColor from '../nodes/AnimatedColor';\nimport Animation from './Animation';\n\nexport type TimingAnimationConfig = $ReadOnly<{\n  ...AnimationConfig,\n  toValue:\n    | number\n    | AnimatedValue\n    | $ReadOnly<{\n        x: number,\n        y: number,\n        ...\n      }>\n    | AnimatedValueXY\n    | RgbaValue\n    | AnimatedColor\n    | AnimatedInterpolation<number>,\n  easing?: (value: number) => number,\n  duration?: number,\n  delay?: number,\n  ...\n}>;\n\nexport type TimingAnimationConfigSingle = $ReadOnly<{\n  ...AnimationConfig,\n  toValue: number,\n  easing?: (value: number) => number,\n  duration?: number,\n  delay?: number,\n  ...\n}>;\n\nlet _easeInOut;\nfunction easeInOut() {\n  if (!_easeInOut) {\n    const Easing = require('../Easing').default;\n    _easeInOut = Easing.inOut(Easing.ease);\n  }\n  return _easeInOut;\n}\n\nexport default class TimingAnimation extends Animation {\n  _startTime: number;\n  _fromValue: number;\n  _toValue: number;\n  _duration: number;\n  _delay: number;\n  _easing: (value: number) => number;\n  _onUpdate: (value: number) => void;\n  _animationFrame: ?AnimationFrameID;\n  _timeout: ?TimeoutID;\n  _platformConfig: ?PlatformConfig;\n\n  constructor(config: TimingAnimationConfigSingle) {\n    super(config);\n\n    this._toValue = config.toValue;\n    this._easing = config.easing ?? easeInOut();\n    this._duration = config.duration ?? 500;\n    this._delay = config.delay ?? 0;\n    this._platformConfig = config.platformConfig;\n  }\n\n  __getNativeAnimationConfig(): $ReadOnly<{\n    type: 'frames',\n    frames: $ReadOnlyArray<number>,\n    toValue: number,\n    iterations: number,\n    platformConfig: ?PlatformConfig,\n    ...\n  }> {\n    const frameDuration = 1000.0 / 60.0;\n    const frames = [];\n    const numFrames = Math.round(this._duration / frameDuration);\n    for (let frame = 0; frame < numFrames; frame++) {\n      frames.push(this._easing(frame / numFrames));\n    }\n    frames.push(this._easing(1));\n    return {\n      type: 'frames',\n      frames,\n      toValue: this._toValue,\n      iterations: this.__iterations,\n      platformConfig: this._platformConfig,\n      debugID: this.__getDebugID(),\n    };\n  }\n\n  start(\n    fromValue: number,\n    onUpdate: (value: number) => void,\n    onEnd: ?EndCallback,\n    previousAnimation: ?Animation,\n    animatedValue: AnimatedValue,\n  ): void {\n    super.start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue);\n\n    this._fromValue = fromValue;\n    this._onUpdate = onUpdate;\n\n    const start = () => {\n      this._startTime = Date.now();\n\n      const useNativeDriver = this.__startAnimationIfNative(animatedValue);\n      if (!useNativeDriver) {\n        // Animations that sometimes have 0 duration and sometimes do not\n        // still need to use the native driver when duration is 0 so as to\n        // not cause intermixed JS and native animations.\n        if (this._duration === 0) {\n          this._onUpdate(this._toValue);\n          this.__notifyAnimationEnd({finished: true});\n        } else {\n          this._animationFrame = requestAnimationFrame(() => this.onUpdate());\n        }\n      }\n    };\n    if (this._delay) {\n      this._timeout = setTimeout(start, this._delay);\n    } else {\n      start();\n    }\n  }\n\n  onUpdate(): void {\n    const now = Date.now();\n    if (now >= this._startTime + this._duration) {\n      if (this._duration === 0) {\n        this._onUpdate(this._toValue);\n      } else {\n        this._onUpdate(\n          this._fromValue + this._easing(1) * (this._toValue - this._fromValue),\n        );\n      }\n      this.__notifyAnimationEnd({finished: true});\n      return;\n    }\n\n    this._onUpdate(\n      this._fromValue +\n        this._easing((now - this._startTime) / this._duration) *\n          (this._toValue - this._fromValue),\n    );\n    if (this.__active) {\n      // $FlowFixMe[method-unbinding] added when improving typing for this parameters\n      this._animationFrame = requestAnimationFrame(this.onUpdate.bind(this));\n    }\n  }\n\n  stop(): void {\n    super.stop();\n    clearTimeout(this._timeout);\n    if (this._animationFrame != null) {\n      global.cancelAnimationFrame(this._animationFrame);\n    }\n    this.__notifyAnimationEnd({finished: false});\n  }\n}\n"], "mappings": ";;;;;;;;;;;;AAiBA,IAAAA,cAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,WAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAoC,SAAAE,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAE,gBAAA,CAAAC,OAAA,EAAAH,CAAA,OAAAI,2BAAA,CAAAD,OAAA,EAAAJ,CAAA,EAAAM,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAP,CAAA,EAAAC,CAAA,YAAAC,gBAAA,CAAAC,OAAA,EAAAJ,CAAA,EAAAS,WAAA,IAAAR,CAAA,CAAAS,KAAA,CAAAV,CAAA,EAAAE,CAAA;AAAA,SAAAI,0BAAA,cAAAN,CAAA,IAAAW,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAX,CAAA,aAAAM,yBAAA,YAAAA,0BAAA,aAAAN,CAAA;AAAA,SAAAe,cAAAf,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAc,CAAA,QAAAC,CAAA,OAAAC,KAAA,CAAAd,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAY,CAAA,GAAAhB,CAAA,CAAAY,SAAA,GAAAZ,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAc,CAAA,yBAAAC,CAAA,aAAAjB,CAAA,WAAAiB,CAAA,CAAAP,KAAA,CAAAR,CAAA,EAAAF,CAAA,OAAAiB,CAAA;AA+BpC,IAAIE,UAAU;AACd,SAASC,SAASA,CAAA,EAAG;EACnB,IAAI,CAACD,UAAU,EAAE;IACf,IAAME,MAAM,GAAGxB,OAAO,YAAY,CAAC,CAACO,OAAO;IAC3Ce,UAAU,GAAGE,MAAM,CAACC,KAAK,CAACD,MAAM,CAACE,IAAI,CAAC;EACxC;EACA,OAAOJ,UAAU;AACnB;AAAC,IAEoBK,eAAe,GAAAC,OAAA,CAAArB,OAAA,aAAAsB,UAAA;EAYlC,SAAAF,gBAAYG,MAAmC,EAAE;IAAA,IAAAC,cAAA,EAAAC,gBAAA,EAAAC,aAAA;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAA5B,OAAA,QAAAoB,eAAA;IAC/CO,KAAA,GAAAhC,UAAA,OAAAyB,eAAA,GAAMG,MAAM;IAEZI,KAAA,CAAKE,QAAQ,GAAGN,MAAM,CAACO,OAAO;IAC9BH,KAAA,CAAKI,OAAO,IAAAP,cAAA,GAAGD,MAAM,CAACS,MAAM,YAAAR,cAAA,GAAIR,SAAS,CAAC,CAAC;IAC3CW,KAAA,CAAKM,SAAS,IAAAR,gBAAA,GAAGF,MAAM,CAACW,QAAQ,YAAAT,gBAAA,GAAI,GAAG;IACvCE,KAAA,CAAKQ,MAAM,IAAAT,aAAA,GAAGH,MAAM,CAACa,KAAK,YAAAV,aAAA,GAAI,CAAC;IAC/BC,KAAA,CAAKU,eAAe,GAAGd,MAAM,CAACe,cAAc;IAAC,OAAAX,KAAA;EAC/C;EAAC,IAAAY,UAAA,CAAAvC,OAAA,EAAAoB,eAAA,EAAAE,UAAA;EAAA,WAAAkB,aAAA,CAAAxC,OAAA,EAAAoB,eAAA;IAAAqB,GAAA;IAAAC,KAAA,EAED,SAAAC,0BAA0BA,CAAA,EAOvB;MACD,IAAMC,aAAa,GAAG,MAAM,GAAG,IAAI;MACnC,IAAMC,MAAM,GAAG,EAAE;MACjB,IAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAI,CAACf,SAAS,GAAGW,aAAa,CAAC;MAC5D,KAAK,IAAIK,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGH,SAAS,EAAEG,KAAK,EAAE,EAAE;QAC9CJ,MAAM,CAACK,IAAI,CAAC,IAAI,CAACnB,OAAO,CAACkB,KAAK,GAAGH,SAAS,CAAC,CAAC;MAC9C;MACAD,MAAM,CAACK,IAAI,CAAC,IAAI,CAACnB,OAAO,CAAC,CAAC,CAAC,CAAC;MAC5B,OAAO;QACLoB,IAAI,EAAE,QAAQ;QACdN,MAAM,EAANA,MAAM;QACNf,OAAO,EAAE,IAAI,CAACD,QAAQ;QACtBuB,UAAU,EAAE,IAAI,CAACC,YAAY;QAC7Bf,cAAc,EAAE,IAAI,CAACD,eAAe;QACpCiB,OAAO,EAAE,IAAI,CAACC,YAAY,CAAC;MAC7B,CAAC;IACH;EAAC;IAAAd,GAAA;IAAAC,KAAA,EAED,SAAAc,KAAKA,CACHC,SAAiB,EACjBC,QAAiC,EACjCC,KAAmB,EACnBC,iBAA6B,EAC7BC,aAA4B,EACtB;MAAA,IAAAC,MAAA;MACNnD,aAAA,CAAAS,eAAA,qBAAYqC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,aAAa;MAExE,IAAI,CAACE,UAAU,GAAGN,SAAS;MAC3B,IAAI,CAACO,SAAS,GAAGN,QAAQ;MAEzB,IAAMF,KAAK,GAAG,SAARA,KAAKA,CAAA,EAAS;QAClBM,MAAI,CAACG,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;QAE5B,IAAMC,eAAe,GAAGN,MAAI,CAACO,wBAAwB,CAACR,aAAa,CAAC;QACpE,IAAI,CAACO,eAAe,EAAE;UAIpB,IAAIN,MAAI,CAAC7B,SAAS,KAAK,CAAC,EAAE;YACxB6B,MAAI,CAACE,SAAS,CAACF,MAAI,CAACjC,QAAQ,CAAC;YAC7BiC,MAAI,CAACQ,oBAAoB,CAAC;cAACC,QAAQ,EAAE;YAAI,CAAC,CAAC;UAC7C,CAAC,MAAM;YACLT,MAAI,CAACU,eAAe,GAAGC,qBAAqB,CAAC;cAAA,OAAMX,MAAI,CAACJ,QAAQ,CAAC,CAAC;YAAA,EAAC;UACrE;QACF;MACF,CAAC;MACD,IAAI,IAAI,CAACvB,MAAM,EAAE;QACf,IAAI,CAACuC,QAAQ,GAAGC,UAAU,CAACnB,KAAK,EAAE,IAAI,CAACrB,MAAM,CAAC;MAChD,CAAC,MAAM;QACLqB,KAAK,CAAC,CAAC;MACT;IACF;EAAC;IAAAf,GAAA;IAAAC,KAAA,EAED,SAAAgB,QAAQA,CAAA,EAAS;MACf,IAAMS,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;MACtB,IAAIA,GAAG,IAAI,IAAI,CAACF,UAAU,GAAG,IAAI,CAAChC,SAAS,EAAE;QAC3C,IAAI,IAAI,CAACA,SAAS,KAAK,CAAC,EAAE;UACxB,IAAI,CAAC+B,SAAS,CAAC,IAAI,CAACnC,QAAQ,CAAC;QAC/B,CAAC,MAAM;UACL,IAAI,CAACmC,SAAS,CACZ,IAAI,CAACD,UAAU,GAAG,IAAI,CAAChC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAACF,QAAQ,GAAG,IAAI,CAACkC,UAAU,CACtE,CAAC;QACH;QACA,IAAI,CAACO,oBAAoB,CAAC;UAACC,QAAQ,EAAE;QAAI,CAAC,CAAC;QAC3C;MACF;MAEA,IAAI,CAACP,SAAS,CACZ,IAAI,CAACD,UAAU,GACb,IAAI,CAAChC,OAAO,CAAC,CAACoC,GAAG,GAAG,IAAI,CAACF,UAAU,IAAI,IAAI,CAAChC,SAAS,CAAC,IACnD,IAAI,CAACJ,QAAQ,GAAG,IAAI,CAACkC,UAAU,CACtC,CAAC;MACD,IAAI,IAAI,CAACa,QAAQ,EAAE;QAEjB,IAAI,CAACJ,eAAe,GAAGC,qBAAqB,CAAC,IAAI,CAACf,QAAQ,CAACmB,IAAI,CAAC,IAAI,CAAC,CAAC;MACxE;IACF;EAAC;IAAApC,GAAA;IAAAC,KAAA,EAED,SAAAoC,IAAIA,CAAA,EAAS;MACXnE,aAAA,CAAAS,eAAA;MACA2D,YAAY,CAAC,IAAI,CAACL,QAAQ,CAAC;MAC3B,IAAI,IAAI,CAACF,eAAe,IAAI,IAAI,EAAE;QAChCQ,MAAM,CAACC,oBAAoB,CAAC,IAAI,CAACT,eAAe,CAAC;MACnD;MACA,IAAI,CAACF,oBAAoB,CAAC;QAACC,QAAQ,EAAE;MAAK,CAAC,CAAC;IAC9C;EAAC;AAAA,EAlH0CW,mBAAS", "ignoreList": []}