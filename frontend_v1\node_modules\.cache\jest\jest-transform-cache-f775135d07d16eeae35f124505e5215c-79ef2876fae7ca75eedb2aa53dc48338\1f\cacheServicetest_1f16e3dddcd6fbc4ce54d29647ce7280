8dfbb1e58b8c3b667acab11c8e0ab21b
_getJestObj().mock('@react-native-async-storage/async-storage', function () {
  return {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    getAllKeys: jest.fn(),
    multiRemove: jest.fn()
  };
});
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _asyncStorage = _interopRequireDefault(require("@react-native-async-storage/async-storage"));
var _cacheService = require("../cacheService");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
jest.useFakeTimers();
var mockAsyncStorage = _asyncStorage.default;
describe('CacheService', function () {
  beforeEach(function () {
    jest.clearAllMocks();
    jest.clearAllTimers();
    _cacheService.cacheService.clear();
    mockAsyncStorage.getItem.mockResolvedValue(null);
    mockAsyncStorage.setItem.mockResolvedValue();
    mockAsyncStorage.removeItem.mockResolvedValue();
    mockAsyncStorage.getAllKeys.mockResolvedValue([]);
    mockAsyncStorage.multiRemove.mockResolvedValue();
  });
  afterEach(function () {
    jest.clearAllTimers();
  });
  describe('Basic Cache Operations', function () {
    it('sets and gets data from memory cache', (0, _asyncToGenerator2.default)(function* () {
      var key = 'test-key';
      var data = {
        message: 'Hello, World!'
      };
      yield _cacheService.cacheService.set(key, data);
      var result = yield _cacheService.cacheService.get(key);
      expect(result).toEqual(data);
    }));
    it('returns null for non-existent keys', (0, _asyncToGenerator2.default)(function* () {
      var result = yield _cacheService.cacheService.get('non-existent-key');
      expect(result).toBeNull();
    }));
    it('removes data from cache', (0, _asyncToGenerator2.default)(function* () {
      var key = 'test-key';
      var data = {
        message: 'Hello, World!'
      };
      yield _cacheService.cacheService.set(key, data);
      yield _cacheService.cacheService.remove(key);
      var result = yield _cacheService.cacheService.get(key);
      expect(result).toBeNull();
    }));
    it('clears all cache data', (0, _asyncToGenerator2.default)(function* () {
      yield _cacheService.cacheService.set('key1', 'data1');
      yield _cacheService.cacheService.set('key2', 'data2');
      yield _cacheService.cacheService.clear();
      var result1 = yield _cacheService.cacheService.get('key1');
      var result2 = yield _cacheService.cacheService.get('key2');
      expect(result1).toBeNull();
      expect(result2).toBeNull();
    }));
  });
  describe('TTL and Expiration', function () {
    it('respects TTL for cache entries', (0, _asyncToGenerator2.default)(function* () {
      var key = 'expiring-key';
      var data = 'expiring-data';
      var ttl = 100;
      yield _cacheService.cacheService.set(key, data, ttl);
      var result = yield _cacheService.cacheService.get(key);
      expect(result).toBe(data);
      yield new Promise(function (resolve) {
        return setTimeout(resolve, 150);
      });
      result = yield _cacheService.cacheService.get(key);
      expect(result).toBeNull();
    }));
    it('uses default TTL when not specified', (0, _asyncToGenerator2.default)(function* () {
      var key = 'default-ttl-key';
      var data = 'default-ttl-data';
      yield _cacheService.cacheService.set(key, data);
      var entryInfo = _cacheService.cacheService.getEntryInfo(key);
      expect(entryInfo == null ? void 0 : entryInfo.ttl).toBe(5 * 60 * 1000);
    }));
    it('updates TTL on cache hit', (0, _asyncToGenerator2.default)(function* () {
      var key = 'update-ttl-key';
      var data = 'update-ttl-data';
      yield _cacheService.cacheService.set(key, data);
      var initialInfo = _cacheService.cacheService.getEntryInfo(key);
      var initialLastAccessed = initialInfo == null ? void 0 : initialInfo.lastAccessed;
      yield new Promise(function (resolve) {
        return setTimeout(resolve, 10);
      });
      yield _cacheService.cacheService.get(key);
      var updatedInfo = _cacheService.cacheService.getEntryInfo(key);
      expect(updatedInfo == null ? void 0 : updatedInfo.lastAccessed).toBeGreaterThan(initialLastAccessed);
    }));
  });
  describe('Storage Cache Integration', function () {
    it('falls back to storage cache when memory cache misses', (0, _asyncToGenerator2.default)(function* () {
      var key = 'storage-key';
      var data = {
        stored: 'data'
      };
      var storageData = JSON.stringify({
        data: data,
        timestamp: Date.now(),
        ttl: 5 * 60 * 1000,
        version: '1.0.0',
        accessCount: 0,
        lastAccessed: Date.now()
      });
      mockAsyncStorage.getItem.mockResolvedValue(storageData);
      var result = yield _cacheService.cacheService.get(key);
      expect(result).toEqual(data);
      expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('@vierla_cache_' + key);
    }));
    it('stores data in both memory and storage by default', (0, _asyncToGenerator2.default)(function* () {
      var key = 'dual-storage-key';
      var data = {
        dual: 'storage'
      };
      yield _cacheService.cacheService.set(key, data);
      var memoryResult = yield _cacheService.cacheService.get(key);
      expect(memoryResult).toEqual(data);
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith('@vierla_cache_' + key, expect.stringContaining('"dual":"storage"'));
    }));
    it('supports memory-only storage option', (0, _asyncToGenerator2.default)(function* () {
      var key = 'memory-only-key';
      var data = {
        memory: 'only'
      };
      yield _cacheService.cacheService.set(key, data, undefined, {
        memoryOnly: true
      });
      var result = yield _cacheService.cacheService.get(key);
      expect(result).toEqual(data);
      expect(mockAsyncStorage.setItem).not.toHaveBeenCalled();
    }));
    it('supports storage-only option', (0, _asyncToGenerator2.default)(function* () {
      var key = 'storage-only-key';
      var data = {
        storage: 'only'
      };
      yield _cacheService.cacheService.set(key, data, undefined, {
        storageOnly: true
      });
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith('@vierla_cache_' + key, expect.stringContaining('"storage":"only"'));
    }));
  });
  describe('Memory Management', function () {
    it('enforces memory limits', (0, _asyncToGenerator2.default)(function* () {
      var testCache = new _cacheService.cacheService.constructor({
        maxMemorySize: 1000
      });
      var largeData = 'x'.repeat(500);
      yield testCache.set('key1', largeData);
      yield testCache.set('key2', largeData);
      yield testCache.set('key3', largeData);
      var stats = testCache.getStats();
      expect(stats.totalSize).toBeLessThanOrEqual(1000);
    }));
    it('uses LFU + LRU eviction strategy', (0, _asyncToGenerator2.default)(function* () {
      var testCache = new _cacheService.cacheService.constructor({
        maxMemorySize: 1000
      });
      var data = 'x'.repeat(200);
      yield testCache.set('frequent', data);
      yield testCache.set('infrequent', data);
      yield testCache.set('recent', data);
      yield testCache.get('frequent');
      yield testCache.get('frequent');
      yield testCache.get('frequent');
      yield new Promise(function (resolve) {
        return setTimeout(resolve, 10);
      });
      yield testCache.get('recent');
      yield testCache.set('trigger', data);
      yield testCache.set('eviction', data);
      var infrequentResult = yield testCache.get('infrequent');
      expect(infrequentResult).toBeNull();
      var frequentResult = yield testCache.get('frequent');
      expect(frequentResult).toBe(data);
    }));
  });
  describe('Cache Statistics', function () {
    it('tracks cache hits and misses', (0, _asyncToGenerator2.default)(function* () {
      var key = 'stats-key';
      var data = 'stats-data';
      yield _cacheService.cacheService.get('non-existent');
      yield _cacheService.cacheService.set(key, data);
      yield _cacheService.cacheService.get(key);
      var stats = _cacheService.cacheService.getStats();
      expect(stats.memoryHits).toBe(1);
      expect(stats.memoryMisses).toBeGreaterThan(0);
    }));
    it('calculates hit rate correctly', (0, _asyncToGenerator2.default)(function* () {
      yield _cacheService.cacheService.set('key1', 'data1');
      yield _cacheService.cacheService.set('key2', 'data2');
      yield _cacheService.cacheService.get('key1');
      yield _cacheService.cacheService.get('key2');
      yield _cacheService.cacheService.get('non-existent');
      var stats = _cacheService.cacheService.getStats();
      expect(stats.hitRate).toBeCloseTo(2 / 3, 2);
    }));
    it('tracks entry count and total size', (0, _asyncToGenerator2.default)(function* () {
      yield _cacheService.cacheService.set('key1', 'small');
      yield _cacheService.cacheService.set('key2', 'larger data string');
      var stats = _cacheService.cacheService.getStats();
      expect(stats.entryCount).toBe(2);
      expect(stats.totalSize).toBeGreaterThan(0);
    }));
  });
  describe('Cache Preloading', function () {
    it('preloads multiple entries', (0, _asyncToGenerator2.default)(function* () {
      var entries = [{
        key: 'preload1',
        data: 'data1'
      }, {
        key: 'preload2',
        data: 'data2',
        ttl: 1000
      }, {
        key: 'preload3',
        data: {
          complex: 'object'
        }
      }];
      yield _cacheService.cacheService.preload(entries);
      for (var entry of entries) {
        var result = yield _cacheService.cacheService.get(entry.key);
        expect(result).toEqual(entry.data);
      }
    }));
    it('handles preload failures gracefully', (0, _asyncToGenerator2.default)(function* () {
      var originalSet = _cacheService.cacheService.set;
      _cacheService.cacheService.set = jest.fn().mockRejectedValueOnce(new Error('Set failed'));
      var entries = [{
        key: 'success',
        data: 'data1'
      }, {
        key: 'failure',
        data: 'data2'
      }];
      yield expect(_cacheService.cacheService.preload(entries)).resolves.toBeUndefined();
      _cacheService.cacheService.set = originalSet;
    }));
  });
  describe('Pattern-based Invalidation', function () {
    it('invalidates entries matching pattern', (0, _asyncToGenerator2.default)(function* () {
      yield _cacheService.cacheService.set('user:1:profile', 'profile1');
      yield _cacheService.cacheService.set('user:1:settings', 'settings1');
      yield _cacheService.cacheService.set('user:2:profile', 'profile2');
      yield _cacheService.cacheService.set('other:data', 'other');
      yield _cacheService.cacheService.invalidatePattern(/^user:1:/);
      expect(yield _cacheService.cacheService.get('user:1:profile')).toBeNull();
      expect(yield _cacheService.cacheService.get('user:1:settings')).toBeNull();
      expect(yield _cacheService.cacheService.get('user:2:profile')).toBe('profile2');
      expect(yield _cacheService.cacheService.get('other:data')).toBe('other');
    }));
    it('invalidates storage entries matching pattern', (0, _asyncToGenerator2.default)(function* () {
      mockAsyncStorage.getAllKeys.mockResolvedValue(['@vierla_cache_user:1:profile', '@vierla_cache_user:1:settings', '@vierla_cache_user:2:profile', '@vierla_cache_other:data']);
      yield _cacheService.cacheService.invalidatePattern(/^user:1:/);
      expect(mockAsyncStorage.multiRemove).toHaveBeenCalledWith(['@vierla_cache_user:1:profile', '@vierla_cache_user:1:settings']);
    }));
  });
  describe('Entry Information', function () {
    it('provides entry metadata', (0, _asyncToGenerator2.default)(function* () {
      var key = 'info-key';
      var data = 'info-data';
      var ttl = 10000;
      yield _cacheService.cacheService.set(key, data, ttl);
      var info = _cacheService.cacheService.getEntryInfo(key);
      expect(info).toBeDefined();
      expect(info == null ? void 0 : info.ttl).toBe(ttl);
      expect(info == null ? void 0 : info.version).toBe('1.0.0');
      expect(info == null ? void 0 : info.accessCount).toBe(0);
      expect(info == null ? void 0 : info.timestamp).toBeGreaterThan(0);
      expect(info == null ? void 0 : info.lastAccessed).toBeGreaterThan(0);
    }));
    it('returns null for non-existent entries', function () {
      var info = _cacheService.cacheService.getEntryInfo('non-existent');
      expect(info).toBeNull();
    });
    it('updates access count on cache hits', (0, _asyncToGenerator2.default)(function* () {
      var key = 'access-count-key';
      yield _cacheService.cacheService.set(key, 'data');
      yield _cacheService.cacheService.get(key);
      yield _cacheService.cacheService.get(key);
      yield _cacheService.cacheService.get(key);
      var info = _cacheService.cacheService.getEntryInfo(key);
      expect(info == null ? void 0 : info.accessCount).toBe(3);
    }));
  });
  describe('Error Handling', function () {
    it('handles AsyncStorage errors gracefully', (0, _asyncToGenerator2.default)(function* () {
      mockAsyncStorage.getItem.mockRejectedValue(new Error('Storage error'));
      var result = yield _cacheService.cacheService.get('error-key');
      expect(result).toBeNull();
    }));
    it('handles set errors gracefully', (0, _asyncToGenerator2.default)(function* () {
      mockAsyncStorage.setItem.mockRejectedValue(new Error('Storage full'));
      yield expect(_cacheService.cacheService.set('error-key', 'data')).resolves.toBeUndefined();
    }));
    it('handles remove errors gracefully', (0, _asyncToGenerator2.default)(function* () {
      mockAsyncStorage.removeItem.mockRejectedValue(new Error('Remove error'));
      yield expect(_cacheService.cacheService.remove('error-key')).resolves.toBeUndefined();
    }));
    it('handles clear errors gracefully', (0, _asyncToGenerator2.default)(function* () {
      mockAsyncStorage.getAllKeys.mockRejectedValue(new Error('Keys error'));
      yield expect(_cacheService.cacheService.clear()).resolves.toBeUndefined();
    }));
  });
  describe('Service Lifecycle', function () {
    it('destroys service correctly', function () {
      var clearIntervalSpy = jest.spyOn(global, 'clearInterval');
      _cacheService.cacheService.destroy();
      expect(clearIntervalSpy).toHaveBeenCalled();
      clearIntervalSpy.mockRestore();
    });
    it('cleans up expired entries periodically', (0, _asyncToGenerator2.default)(function* () {
      var key = 'cleanup-key';
      var shortTtl = 50;
      yield _cacheService.cacheService.set(key, 'data', shortTtl);
      expect(yield _cacheService.cacheService.get(key)).toBe('data');
      yield new Promise(function (resolve) {
        return setTimeout(resolve, 100);
      });
      expect(yield _cacheService.cacheService.get(key)).toBeNull();
    }));
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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