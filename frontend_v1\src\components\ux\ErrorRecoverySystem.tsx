/**
 * Error Recognition and Recovery System
 *
 * Comprehensive error handling system that helps users recognize errors,
 * understand what went wrong, and provides clear recovery paths.
 *
 * Features:
 * - Clear error messages in plain language
 * - Specific recovery suggestions
 * - Automatic error recovery where possible
 * - Error context and cause explanation
 * - Progressive error disclosure
 * - Error prevention guidance
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Ionicons } from '@expo/vector-icons';
import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useEffect,
} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Modal,
  Alert,
  Animated,
} from 'react-native';

import { useTheme } from '../../contexts/ThemeContext';
import { useUndo } from '../../hooks/useUndo';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../../utils/responsiveUtils';
import { Text } from '../atoms/Text';
import { useActionFeedback } from '../ui/ActionFeedbackSystem';
import { UnifiedButton } from '../ui/UnifiedButton';

export interface ErrorInfo {
  id: string;
  type:
    | 'network'
    | 'validation'
    | 'permission'
    | 'system'
    | 'user'
    | 'business';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  technicalDetails?: string;
  context?: Record<string, any>;
  timestamp: Date;
  userAction?: string;
  recoveryOptions: RecoveryOption[];
  preventionTips?: string[];
  relatedErrors?: string[];
  autoRecoverable?: boolean;
}

export interface RecoveryOption {
  id: string;
  label: string;
  description: string;
  action: () => void | Promise<void>;
  type: 'primary' | 'secondary' | 'alternative';
  icon?: keyof typeof Ionicons.glyphMap;
  estimatedTime?: string;
  successRate?: number; // 0-100
}

export interface ErrorPattern {
  pattern: RegExp | string;
  errorType: ErrorInfo['type'];
  severity: ErrorInfo['severity'];
  messageTemplate: string;
  recoveryTemplate: RecoveryOption[];
}

interface ErrorRecoveryState {
  activeErrors: ErrorInfo[];
  errorHistory: ErrorInfo[];
  errorPatterns: ErrorPattern[];
  autoRecoveryEnabled: boolean;
  showTechnicalDetails: boolean;
  userPreferences: {
    preferredRecoveryType: 'automatic' | 'guided' | 'manual';
    showPreventionTips: boolean;
    errorReportingEnabled: boolean;
  };
}

interface ErrorRecoveryContextType {
  state: ErrorRecoveryState;
  reportError: (error: Partial<ErrorInfo>) => string;
  resolveError: (errorId: string) => void;
  dismissError: (errorId: string) => void;
  retryLastAction: () => void;
  getRecoveryGuidance: (errorType: ErrorInfo['type']) => string[];
  enableAutoRecovery: (enabled: boolean) => void;
  updatePreferences: (
    preferences: Partial<ErrorRecoveryState['userPreferences']>,
  ) => void;
  clearErrorHistory: () => void;
  getErrorStats: () => {
    total: number;
    byType: Record<string, number>;
    byDay: Record<string, number>;
  };
}

const ErrorRecoveryContext = createContext<ErrorRecoveryContextType | null>(
  null,
);

export const useErrorRecovery = () => {
  const context = useContext(ErrorRecoveryContext);
  if (!context) {
    throw new Error(
      'useErrorRecovery must be used within ErrorRecoveryProvider',
    );
  }
  return context;
};

// Predefined error patterns and recovery options
const DEFAULT_ERROR_PATTERNS: ErrorPattern[] = [
  {
    pattern: /network|connection|timeout/i,
    errorType: 'network',
    severity: 'medium',
    messageTemplate:
      'Connection problem detected. Please check your internet connection.',
    recoveryTemplate: [
      {
        id: 'retry-connection',
        label: 'Retry',
        description: 'Try the action again',
        action: () => {},
        type: 'primary',
        icon: 'refresh',
        estimatedTime: '5 seconds',
        successRate: 85,
      },
      {
        id: 'check-connection',
        label: 'Check Connection',
        description: 'Verify your internet connection',
        action: () => {},
        type: 'secondary',
        icon: 'wifi',
      },
    ],
  },
  {
    pattern: /validation|invalid|required/i,
    errorType: 'validation',
    severity: 'low',
    messageTemplate:
      "Let's fix a few things so you can continue. We've highlighted what needs attention.",
    recoveryTemplate: [
      {
        id: 'fix-validation',
        label: 'Fix Issues',
        description: 'Review and correct the highlighted fields',
        action: () => {},
        type: 'primary',
        icon: 'create',
      },
      {
        id: 'get-help',
        label: 'Get Help',
        description: 'Learn more about the requirements',
        action: () => {},
        type: 'secondary',
        icon: 'help-circle',
      },
    ],
  },
  {
    pattern: /permission|unauthorized|forbidden/i,
    errorType: 'permission',
    severity: 'high',
    messageTemplate: "You don't have permission to perform this action.",
    recoveryTemplate: [
      {
        id: 'login-again',
        label: 'Sign In Again',
        description: 'Refresh your authentication',
        action: () => {},
        type: 'primary',
        icon: 'log-in',
      },
      {
        id: 'contact-support',
        label: 'Contact Support',
        description: 'Get help with permissions',
        action: () => {},
        type: 'secondary',
        icon: 'chatbubble',
      },
    ],
  },
];

interface ErrorRecoveryProviderProps {
  children: React.ReactNode;
}

export const ErrorRecoveryProvider: React.FC<ErrorRecoveryProviderProps> = ({
  children,
}) => {
  const [state, setState] = useState<ErrorRecoveryState>({
    activeErrors: [],
    errorHistory: [],
    errorPatterns: DEFAULT_ERROR_PATTERNS,
    autoRecoveryEnabled: true,
    showTechnicalDetails: false,
    userPreferences: {
      preferredRecoveryType: 'guided',
      showPreventionTips: true,
      errorReportingEnabled: true,
    },
  });

  const { startAction, updateAction, completeAction } = useActionFeedback();
  const { registerCustom } = useUndo();

  const generateErrorId = () =>
    `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  const matchErrorPattern = (errorMessage: string): ErrorPattern | null => {
    for (const pattern of state.errorPatterns) {
      const regex =
        typeof pattern.pattern === 'string'
          ? new RegExp(pattern.pattern, 'i')
          : pattern.pattern;

      if (regex.test(errorMessage)) {
        return pattern;
      }
    }
    return null;
  };

  const reportError = useCallback(
    (errorData: Partial<ErrorInfo>): string => {
      const errorId = generateErrorId();
      const pattern = errorData.message
        ? matchErrorPattern(errorData.message)
        : null;

      const error: ErrorInfo = {
        id: errorId,
        type: errorData.type || pattern?.errorType || 'system',
        severity: errorData.severity || pattern?.severity || 'medium',
        title: errorData.title || "We're Here to Help",
        message:
          errorData.message ||
          pattern?.messageTemplate ||
          'Something unexpected happened, but we can fix this together.',
        technicalDetails: errorData.technicalDetails,
        context: errorData.context,
        timestamp: new Date(),
        userAction: errorData.userAction,
        recoveryOptions:
          errorData.recoveryOptions || pattern?.recoveryTemplate || [],
        preventionTips: errorData.preventionTips,
        relatedErrors: errorData.relatedErrors,
        autoRecoverable: errorData.autoRecoverable || false,
      };

      setState(prev => ({
        ...prev,
        activeErrors: [...prev.activeErrors, error],
        errorHistory: [...prev.errorHistory, error],
      }));

      // Attempt auto-recovery if enabled and possible
      if (state.autoRecoveryEnabled && error.autoRecoverable) {
        setTimeout(() => {
          attemptAutoRecovery(error);
        }, 1000);
      }

      // Register undo operation for recoverable errors
      if (error.recoveryOptions.length > 0) {
        registerCustom('reset', `Error recovery for: ${error.title}`, () => {
          const primaryRecovery = error.recoveryOptions.find(
            option => option.type === 'primary',
          );
          if (primaryRecovery) {
            primaryRecovery.action();
          }
        });
      }

      return errorId;
    },
    [state.autoRecoveryEnabled, state.errorPatterns, registerCustom],
  );

  const attemptAutoRecovery = async (error: ErrorInfo) => {
    const actionId = startAction({
      type: 'general',
      action: 'Auto Recovery',
      message: `Attempting to recover from: ${error.title}`,
    });

    try {
      updateAction(actionId, {
        status: 'loading',
        progress: 50,
        message: 'Analyzing error...',
      });

      // Simulate auto-recovery logic
      await new Promise(resolve => setTimeout(resolve, 2000));

      const primaryRecovery = error.recoveryOptions.find(
        option => option.type === 'primary',
      );
      if (primaryRecovery) {
        await primaryRecovery.action();
        resolveError(error.id);
        completeAction(actionId, true, 'Error recovered automatically');
      } else {
        completeAction(actionId, false, 'Auto-recovery not available');
      }
    } catch (recoveryError) {
      completeAction(actionId, false, 'Auto-recovery failed');
    }
  };

  const resolveError = useCallback((errorId: string) => {
    setState(prev => ({
      ...prev,
      activeErrors: prev.activeErrors.filter(error => error.id !== errorId),
    }));
  }, []);

  const dismissError = useCallback((errorId: string) => {
    setState(prev => ({
      ...prev,
      activeErrors: prev.activeErrors.filter(error => error.id !== errorId),
    }));
  }, []);

  const retryLastAction = useCallback(() => {
    const lastError = state.errorHistory[state.errorHistory.length - 1];
    if (lastError && lastError.userAction) {
      // Retry the last action that caused an error
      console.log('Retrying last action:', lastError.userAction);
    }
  }, [state.errorHistory]);

  const getRecoveryGuidance = useCallback(
    (errorType: ErrorInfo['type']): string[] => {
      const guidance: Record<ErrorInfo['type'], string[]> = {
        network: [
          'Check your internet connection',
          'Try switching between WiFi and mobile data',
          'Restart the app if the problem persists',
          'Contact support if you continue having issues',
        ],
        validation: [
          'Review all required fields',
          'Check for proper formatting (email, phone, etc.)',
          'Ensure all information is complete',
          'Look for any error messages near form fields',
        ],
        permission: [
          'Sign out and sign back in',
          'Check if your account has the necessary permissions',
          'Contact your administrator if needed',
          'Try again in a few minutes',
        ],
        system: [
          'Try refreshing the page or restarting the app',
          'Clear the app cache if possible',
          'Update the app to the latest version',
          'Contact support with error details',
        ],
        user: [
          'Double-check your input',
          'Follow the provided instructions carefully',
          'Use the help documentation for guidance',
          'Contact support if you need assistance',
        ],
        business: [
          'Review the business rules and requirements',
          'Check if you meet all necessary conditions',
          'Contact support for clarification',
          'Try a different approach if available',
        ],
      };

      return guidance[errorType] || guidance.system;
    },
    [],
  );

  const enableAutoRecovery = useCallback((enabled: boolean) => {
    setState(prev => ({ ...prev, autoRecoveryEnabled: enabled }));
  }, []);

  const updatePreferences = useCallback(
    (preferences: Partial<ErrorRecoveryState['userPreferences']>) => {
      setState(prev => ({
        ...prev,
        userPreferences: { ...prev.userPreferences, ...preferences },
      }));
    },
    [],
  );

  const clearErrorHistory = useCallback(() => {
    setState(prev => ({ ...prev, errorHistory: [] }));
  }, []);

  const getErrorStats = useCallback(() => {
    const total = state.errorHistory.length;
    const byType: Record<string, number> = {};
    const byDay: Record<string, number> = {};

    state.errorHistory.forEach(error => {
      // Count by type
      byType[error.type] = (byType[error.type] || 0) + 1;

      // Count by day
      const day = error.timestamp.toDateString();
      byDay[day] = (byDay[day] || 0) + 1;
    });

    return { total, byType, byDay };
  }, [state.errorHistory]);

  const contextValue: ErrorRecoveryContextType = {
    state,
    reportError,
    resolveError,
    dismissError,
    retryLastAction,
    getRecoveryGuidance,
    enableAutoRecovery,
    updatePreferences,
    clearErrorHistory,
    getErrorStats,
  };

  return (
    <ErrorRecoveryContext.Provider value={contextValue}>
      {children}
      {/* Error Display Overlay */}
      {state.activeErrors.map(error => (
        <ErrorRecoveryModal
          key={error.id}
          error={error}
          onResolve={() => resolveError(error.id)}
          onDismiss={() => dismissError(error.id)}
          showTechnicalDetails={state.showTechnicalDetails}
          userPreferences={state.userPreferences}
        />
      ))}
    </ErrorRecoveryContext.Provider>
  );
};

// Error Recovery Modal Component
interface ErrorRecoveryModalProps {
  error: ErrorInfo;
  onResolve: () => void;
  onDismiss: () => void;
  showTechnicalDetails: boolean;
  userPreferences: ErrorRecoveryState['userPreferences'];
}

const ErrorRecoveryModal: React.FC<ErrorRecoveryModalProps> = ({
  error,
  onResolve,
  onDismiss,
  showTechnicalDetails,
  userPreferences,
}) => {
  const { colors } = useTheme();

  const [showDetails, setShowDetails] = useState(false);
  const [selectedRecovery, setSelectedRecovery] =
    useState<RecoveryOption | null>(null);
  const [isRecovering, setIsRecovering] = useState(false);

  const slideAnim = React.useRef(new Animated.Value(300)).current;

  useEffect(() => {
    Animated.spring(slideAnim, {
      toValue: 0,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
  }, []);

  const getSeverityColor = () => {
    switch (error.severity) {
      case 'low':
        return colors.success || '#10B981';
      case 'medium':
        return colors.warning || '#F59E0B';
      case 'high':
        return colors.error || '#DC2626';
      case 'critical':
        return colors.error || '#DC2626';
      default:
        return colors.text?.secondary || '#6B7280';
    }
  };

  const getSeverityIcon = () => {
    switch (error.severity) {
      case 'low':
        return 'information-circle';
      case 'medium':
        return 'warning';
      case 'high':
        return 'alert-circle';
      case 'critical':
        return 'close-circle';
      default:
        return 'help-circle';
    }
  };

  const handleRecoveryAction = async (option: RecoveryOption) => {
    setIsRecovering(true);
    setSelectedRecovery(option);

    try {
      await option.action();
      onResolve();
    } catch (recoveryError) {
      console.error('Recovery action failed:', recoveryError);
      Alert.alert(
        'Recovery Failed',
        'The recovery action could not be completed. Please try another option or contact support.',
        [{ text: 'OK' }],
      );
    } finally {
      setIsRecovering(false);
      setSelectedRecovery(null);
    }
  };

  const renderRecoveryOptions = () => (
    <View style={styles.recoveryOptions}>
      <Text
        variant="body"
        weight="medium"
        color="primary"
        style={styles.recoveryTitle}>
        What would you like to do?
      </Text>

      {error.recoveryOptions.map((option, index) => (
        <TouchableOpacity
          key={option.id}
          style={[
            styles.recoveryOption,
            {
              backgroundColor:
                option.type === 'primary' ? colors.accent[50] : colors.gray[50],
              borderColor:
                option.type === 'primary'
                  ? colors.accent[200]
                  : colors.gray[200],
            },
          ]}
          onPress={() => handleRecoveryAction(option)}
          disabled={isRecovering}>
          <View style={styles.recoveryOptionContent}>
            {option.icon && (
              <Ionicons
                name={option.icon}
                size={20}
                color={
                  option.type === 'primary'
                    ? colors.accent[500]
                    : colors.gray[600]
                }
                style={styles.recoveryIcon}
              />
            )}

            <View style={styles.recoveryText}>
              <Text
                variant="body"
                weight="medium"
                color={option.type === 'primary' ? 'accent' : 'primary'}>
                {option.label}
              </Text>

              <Text variant="caption" color="secondary">
                {option.description}
              </Text>

              {option.estimatedTime && (
                <Text variant="caption" color="secondary">
                  Estimated time: {option.estimatedTime}
                </Text>
              )}
            </View>

            {option.successRate && (
              <View style={styles.successRate}>
                <Text variant="caption" color="secondary">
                  {option.successRate}% success
                </Text>
              </View>
            )}
          </View>

          {isRecovering && selectedRecovery?.id === option.id && (
            <View style={styles.loadingIndicator}>
              <Text variant="caption" color="accent">
                Working...
              </Text>
            </View>
          )}
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderPreventionTips = () => {
    if (!userPreferences.showPreventionTips || !error.preventionTips?.length) {
      return null;
    }

    return (
      <View style={styles.preventionTips}>
        <Text
          variant="body"
          weight="medium"
          color="primary"
          style={styles.tipsTitle}>
          💡 How to prevent this in the future:
        </Text>

        {error.preventionTips.map((tip, index) => (
          <View key={index} style={styles.tipItem}>
            <Text variant="caption" color="secondary">
              • {tip}
            </Text>
          </View>
        ))}
      </View>
    );
  };

  return (
    <Modal visible transparent animationType="fade">
      <View style={styles.modalOverlay}>
        <Animated.View
          style={[
            styles.modalContent,
            {
              backgroundColor: colors.white,
              transform: [{ translateY: slideAnim }],
            },
          ]}>
          {/* Header */}
          <View style={styles.errorHeader}>
            <View style={styles.errorIconContainer}>
              <Ionicons
                name={getSeverityIcon()}
                size={32}
                color={getSeverityColor()}
              />
            </View>

            <View style={styles.errorTitleContainer}>
              <Text variant="h3" weight="semibold" color="primary">
                {error.title}
              </Text>

              <Text variant="caption" color="secondary">
                {error.type} • {error.severity} severity
              </Text>
            </View>

            <TouchableOpacity onPress={onDismiss} style={styles.dismissButton}>
              <Ionicons name="close" size={24} color={colors.gray[600]} />
            </TouchableOpacity>
          </View>

          <ScrollView
            style={styles.errorContent}
            showsVerticalScrollIndicator={false}>
            {/* Error Message */}
            <View style={styles.errorMessage}>
              <Text variant="body" color="secondary" style={styles.messageText}>
                {error.message}
              </Text>
            </View>

            {/* Recovery Options */}
            {error.recoveryOptions.length > 0 && renderRecoveryOptions()}

            {/* Prevention Tips */}
            {renderPreventionTips()}

            {/* Technical Details */}
            {(showTechnicalDetails || error.technicalDetails) && (
              <View style={styles.technicalDetails}>
                <TouchableOpacity
                  style={styles.detailsToggle}
                  onPress={() => setShowDetails(!showDetails)}>
                  <Text variant="caption" color="accent">
                    Technical Details
                  </Text>
                  <Ionicons
                    name={showDetails ? 'chevron-up' : 'chevron-down'}
                    size={16}
                    color={colors.accent[500]}
                  />
                </TouchableOpacity>

                {showDetails && error.technicalDetails && (
                  <View
                    style={[
                      styles.detailsContent,
                      { backgroundColor: colors.gray[50] },
                    ]}>
                    <Text
                      variant="caption"
                      color="secondary"
                      style={styles.technicalText}>
                      {error.technicalDetails}
                    </Text>
                  </View>
                )}
              </View>
            )}
          </ScrollView>

          {/* Footer Actions */}
          <View style={styles.modalFooter}>
            <UnifiedButton
              title="Dismiss"
              onPress={onDismiss}
              variant="secondary"
              style={styles.footerButton}
            />

            {error.recoveryOptions.length === 0 && (
              <UnifiedButton
                title="Get Help"
                onPress={() => {
                  // Open help or contact support
                  console.log('Open help for error:', error.type);
                }}
                variant="primary"
                style={styles.footerButton}
              />
            )}
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
  },
  errorHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: getResponsiveSpacing(4),
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  errorIconContainer: {
    marginRight: getResponsiveSpacing(3),
  },
  errorTitleContainer: {
    flex: 1,
  },
  dismissButton: {
    padding: getResponsiveSpacing(1),
  },
  errorContent: {
    flex: 1,
    padding: getResponsiveSpacing(4),
  },
  errorMessage: {
    marginBottom: getResponsiveSpacing(6),
  },
  messageText: {
    lineHeight: 24,
  },
  recoveryOptions: {
    marginBottom: getResponsiveSpacing(6),
  },
  recoveryTitle: {
    marginBottom: getResponsiveSpacing(3),
  },
  recoveryOption: {
    borderWidth: 1,
    borderRadius: 12,
    padding: getResponsiveSpacing(4),
    marginBottom: getResponsiveSpacing(3),
  },
  recoveryOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  recoveryIcon: {
    marginRight: getResponsiveSpacing(3),
  },
  recoveryText: {
    flex: 1,
  },
  successRate: {
    alignItems: 'flex-end',
  },
  loadingIndicator: {
    position: 'absolute',
    right: getResponsiveSpacing(4),
    top: getResponsiveSpacing(4),
  },
  preventionTips: {
    marginBottom: getResponsiveSpacing(6),
    padding: getResponsiveSpacing(4),
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    borderLeftWidth: 3,
    borderLeftColor: '#3b82f6',
  },
  tipsTitle: {
    marginBottom: getResponsiveSpacing(2),
  },
  tipItem: {
    marginBottom: getResponsiveSpacing(1),
  },
  technicalDetails: {
    marginBottom: getResponsiveSpacing(4),
  },
  detailsToggle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: getResponsiveSpacing(2),
  },
  detailsContent: {
    padding: getResponsiveSpacing(3),
    borderRadius: 8,
    marginTop: getResponsiveSpacing(2),
  },
  technicalText: {
    fontFamily: 'monospace',
    lineHeight: 18,
  },
  modalFooter: {
    flexDirection: 'row',
    padding: getResponsiveSpacing(4),
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    gap: getResponsiveSpacing(3),
  },
  footerButton: {
    flex: 1,
  },
});
