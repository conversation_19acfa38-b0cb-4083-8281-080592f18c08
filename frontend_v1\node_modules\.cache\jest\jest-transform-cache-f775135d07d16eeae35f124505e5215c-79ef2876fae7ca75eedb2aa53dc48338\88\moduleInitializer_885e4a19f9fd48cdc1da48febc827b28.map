{"version": 3, "names": ["moduleCache", "Map", "safeModuleLoader", "exports", "modulePath", "moduleLoader", "fallback", "maxRetries", "arguments", "length", "undefined", "cache<PERSON>ey", "has", "console", "log", "get", "attempts", "lastError", "module", "Error", "set", "error", "delay", "initializeCriticalModules", "colorsModule", "require", "Colors", "primary", "default", "light", "dark", "contrast", "text", "secondary", "tertiary", "background", "surface", "DarkModeColors", "sage200", "sage300", "sage400", "sage500", "sage600", "checkModuleHealth", "moduleName", "moduleObject", "requiredProperties", "prop", "clearModuleCache", "clear", "getModuleCacheStatus", "status", "_ref", "entries", "_ref2", "_slicedToArray2", "key", "value", "emergencyModuleReset", "warn", "setTimeout", "getModuleCache"], "sources": ["moduleInitializer.ts"], "sourcesContent": ["/**\n * Module Initializer for Hermes Engine\n *\n * Handles safe module loading and initialization to prevent\n * \"Cannot read property 'primary' of undefined\" errors in Hermes engine\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\n// Global module cache to prevent re-initialization issues\nconst moduleCache = new Map<string, any>();\n\n// Safe module loader with retry mechanism\nexport const safeModuleLoader = <T>(\n  modulePath: string,\n  moduleLoader: () => T,\n  fallback: T,\n  maxRetries: number = 3,\n): T => {\n  const cacheKey = `module_${modulePath}`;\n\n  // Check cache first\n  if (moduleCache.has(cacheKey)) {\n    console.log(`[ModuleInitializer] Using cached module: ${modulePath}`);\n    return moduleCache.get(cacheKey);\n  }\n\n  let attempts = 0;\n  let lastError: Error | null = null;\n\n  while (attempts < maxRetries) {\n    try {\n      console.log(\n        `[ModuleInitializer] Loading module: ${modulePath} (attempt ${attempts + 1})`,\n      );\n\n      const module = moduleLoader();\n\n      if (!module) {\n        throw new Error(`Module ${modulePath} returned null or undefined`);\n      }\n\n      // Validate the module structure\n      if (typeof module === 'object' && module !== null) {\n        console.log(\n          `[ModuleInitializer] ✅ Successfully loaded module: ${modulePath}`,\n        );\n        moduleCache.set(cacheKey, module);\n        return module;\n      } else {\n        throw new Error(`Module ${modulePath} is not a valid object`);\n      }\n    } catch (error) {\n      lastError = error as Error;\n      attempts++;\n      console.error(\n        `[ModuleInitializer] ❌ Failed to load module ${modulePath} (attempt ${attempts}):`,\n        error,\n      );\n\n      if (attempts < maxRetries) {\n        // Wait a bit before retrying\n        const delay = attempts * 100; // Exponential backoff\n        console.log(`[ModuleInitializer] Retrying in ${delay}ms...`);\n        // Note: In React Native, we can't use setTimeout in module initialization\n        // So we'll just continue to the next attempt\n      }\n    }\n  }\n\n  console.error(\n    `[ModuleInitializer] ❌ Failed to load module ${modulePath} after ${maxRetries} attempts. Using fallback.`,\n  );\n  console.error(`[ModuleInitializer] Last error:`, lastError);\n\n  // Cache the fallback to prevent repeated failures\n  moduleCache.set(cacheKey, fallback);\n  return fallback;\n};\n\n// Initialize critical modules early\nexport const initializeCriticalModules = (): void => {\n  console.log('[ModuleInitializer] Initializing critical modules...');\n\n  try {\n    // Pre-load Colors module\n    safeModuleLoader(\n      'Colors',\n      () => {\n        const colorsModule = require('../constants/Colors');\n        if (!colorsModule.Colors) {\n          throw new Error('Colors module does not export Colors object');\n        }\n        return colorsModule;\n      },\n      {\n        Colors: {\n          primary: {\n            default: '#4A6B52',\n            light: '#6B8A74',\n            dark: '#2A4B32',\n            contrast: '#FFFFFF',\n          },\n          text: {\n            primary: '#1A1A1A',\n            secondary: '#6B7280',\n            tertiary: '#9CA3AF',\n          },\n          background: {\n            primary: '#FFFFFF',\n            secondary: '#F9FAFB',\n            tertiary: '#F3F4F6',\n          },\n          surface: {\n            primary: '#FFFFFF',\n            secondary: '#F9FAFB',\n            tertiary: '#F3F4F6',\n          },\n        },\n        DarkModeColors: {\n          sage200: '#1F3A26',\n          sage300: '#2A4B32',\n          sage400: '#4A6B52',\n          sage500: '#5A7A63',\n          sage600: '#6B8A74',\n        },\n      },\n    );\n\n    console.log(\n      '[ModuleInitializer] ✅ Critical modules initialized successfully',\n    );\n  } catch (error) {\n    console.error(\n      '[ModuleInitializer] ❌ Failed to initialize critical modules:',\n      error,\n    );\n  }\n};\n\n// Module health check\nexport const checkModuleHealth = (\n  moduleName: string,\n  moduleObject: any,\n): boolean => {\n  try {\n    if (!moduleObject || typeof moduleObject !== 'object') {\n      console.error(\n        `[ModuleInitializer] Health check failed for ${moduleName}: not an object`,\n      );\n      return false;\n    }\n\n    // Specific checks for Colors module\n    if (moduleName === 'Colors') {\n      const requiredProperties = ['primary', 'text', 'background', 'surface'];\n      for (const prop of requiredProperties) {\n        if (!moduleObject[prop]) {\n          console.error(\n            `[ModuleInitializer] Health check failed for ${moduleName}: missing ${prop}`,\n          );\n          return false;\n        }\n      }\n\n      // Check primary object structure\n      if (!moduleObject.primary.default) {\n        console.error(\n          `[ModuleInitializer] Health check failed for ${moduleName}: missing primary.default`,\n        );\n        return false;\n      }\n    }\n\n    console.log(`[ModuleInitializer] ✅ Health check passed for ${moduleName}`);\n    return true;\n  } catch (error) {\n    console.error(\n      `[ModuleInitializer] Health check error for ${moduleName}:`,\n      error,\n    );\n    return false;\n  }\n};\n\n// Clear module cache (for development/testing)\nexport const clearModuleCache = (): void => {\n  console.log('[ModuleInitializer] Clearing module cache');\n  moduleCache.clear();\n};\n\n// Get module cache status\nexport const getModuleCacheStatus = (): Record<string, boolean> => {\n  const status: Record<string, boolean> = {};\n  for (const [key, value] of moduleCache.entries()) {\n    status[key] = value !== null && value !== undefined;\n  }\n  return status;\n};\n\n// Emergency module reset\nexport const emergencyModuleReset = (): void => {\n  console.warn('[ModuleInitializer] 🚨 Emergency module reset triggered');\n  clearModuleCache();\n\n  // Force re-initialization of critical modules\n  setTimeout(() => {\n    initializeCriticalModules();\n  }, 100);\n};\n\n// Export module cache for debugging\nexport const getModuleCache = (): Map<string, any> => {\n  return moduleCache;\n};\n"], "mappings": ";;;;;;AAWA,IAAMA,WAAW,GAAG,IAAIC,GAAG,CAAc,CAAC;AAGnC,IAAMC,gBAAgB,GAAAC,OAAA,CAAAD,gBAAA,GAAG,SAAnBA,gBAAgBA,CAC3BE,UAAkB,EAClBC,YAAqB,EACrBC,QAAW,EAEL;EAAA,IADNC,UAAkB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAEtB,IAAMG,QAAQ,GAAG,UAAUP,UAAU,EAAE;EAGvC,IAAIJ,WAAW,CAACY,GAAG,CAACD,QAAQ,CAAC,EAAE;IAC7BE,OAAO,CAACC,GAAG,CAAC,4CAA4CV,UAAU,EAAE,CAAC;IACrE,OAAOJ,WAAW,CAACe,GAAG,CAACJ,QAAQ,CAAC;EAClC;EAEA,IAAIK,QAAQ,GAAG,CAAC;EAChB,IAAIC,SAAuB,GAAG,IAAI;EAElC,OAAOD,QAAQ,GAAGT,UAAU,EAAE;IAC5B,IAAI;MACFM,OAAO,CAACC,GAAG,CACT,uCAAuCV,UAAU,aAAaY,QAAQ,GAAG,CAAC,GAC5E,CAAC;MAED,IAAME,MAAM,GAAGb,YAAY,CAAC,CAAC;MAE7B,IAAI,CAACa,MAAM,EAAE;QACX,MAAM,IAAIC,KAAK,CAAC,UAAUf,UAAU,6BAA6B,CAAC;MACpE;MAGA,IAAI,OAAOc,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,EAAE;QACjDL,OAAO,CAACC,GAAG,CACT,qDAAqDV,UAAU,EACjE,CAAC;QACDJ,WAAW,CAACoB,GAAG,CAACT,QAAQ,EAAEO,MAAM,CAAC;QACjC,OAAOA,MAAM;MACf,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAAC,UAAUf,UAAU,wBAAwB,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdJ,SAAS,GAAGI,KAAc;MAC1BL,QAAQ,EAAE;MACVH,OAAO,CAACQ,KAAK,CACX,+CAA+CjB,UAAU,aAAaY,QAAQ,IAAI,EAClFK,KACF,CAAC;MAED,IAAIL,QAAQ,GAAGT,UAAU,EAAE;QAEzB,IAAMe,KAAK,GAAGN,QAAQ,GAAG,GAAG;QAC5BH,OAAO,CAACC,GAAG,CAAC,mCAAmCQ,KAAK,OAAO,CAAC;MAG9D;IACF;EACF;EAEAT,OAAO,CAACQ,KAAK,CACX,+CAA+CjB,UAAU,UAAUG,UAAU,4BAC/E,CAAC;EACDM,OAAO,CAACQ,KAAK,CAAC,iCAAiC,EAAEJ,SAAS,CAAC;EAG3DjB,WAAW,CAACoB,GAAG,CAACT,QAAQ,EAAEL,QAAQ,CAAC;EACnC,OAAOA,QAAQ;AACjB,CAAC;AAGM,IAAMiB,yBAAyB,GAAApB,OAAA,CAAAoB,yBAAA,GAAG,SAA5BA,yBAAyBA,CAAA,EAAe;EACnDV,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;EAEnE,IAAI;IAEFZ,gBAAgB,CACd,QAAQ,EACR,YAAM;MACJ,IAAMsB,YAAY,GAAGC,OAAO,sBAAsB,CAAC;MACnD,IAAI,CAACD,YAAY,CAACE,MAAM,EAAE;QACxB,MAAM,IAAIP,KAAK,CAAC,6CAA6C,CAAC;MAChE;MACA,OAAOK,YAAY;IACrB,CAAC,EACD;MACEE,MAAM,EAAE;QACNC,OAAO,EAAE;UACPC,OAAO,EAAE,SAAS;UAClBC,KAAK,EAAE,SAAS;UAChBC,IAAI,EAAE,SAAS;UACfC,QAAQ,EAAE;QACZ,CAAC;QACDC,IAAI,EAAE;UACJL,OAAO,EAAE,SAAS;UAClBM,SAAS,EAAE,SAAS;UACpBC,QAAQ,EAAE;QACZ,CAAC;QACDC,UAAU,EAAE;UACVR,OAAO,EAAE,SAAS;UAClBM,SAAS,EAAE,SAAS;UACpBC,QAAQ,EAAE;QACZ,CAAC;QACDE,OAAO,EAAE;UACPT,OAAO,EAAE,SAAS;UAClBM,SAAS,EAAE,SAAS;UACpBC,QAAQ,EAAE;QACZ;MACF,CAAC;MACDG,cAAc,EAAE;QACdC,OAAO,EAAE,SAAS;QAClBC,OAAO,EAAE,SAAS;QAClBC,OAAO,EAAE,SAAS;QAClBC,OAAO,EAAE,SAAS;QAClBC,OAAO,EAAE;MACX;IACF,CACF,CAAC;IAED7B,OAAO,CAACC,GAAG,CACT,iEACF,CAAC;EACH,CAAC,CAAC,OAAOO,KAAK,EAAE;IACdR,OAAO,CAACQ,KAAK,CACX,8DAA8D,EAC9DA,KACF,CAAC;EACH;AACF,CAAC;AAGM,IAAMsB,iBAAiB,GAAAxC,OAAA,CAAAwC,iBAAA,GAAG,SAApBA,iBAAiBA,CAC5BC,UAAkB,EAClBC,YAAiB,EACL;EACZ,IAAI;IACF,IAAI,CAACA,YAAY,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;MACrDhC,OAAO,CAACQ,KAAK,CACX,+CAA+CuB,UAAU,iBAC3D,CAAC;MACD,OAAO,KAAK;IACd;IAGA,IAAIA,UAAU,KAAK,QAAQ,EAAE;MAC3B,IAAME,kBAAkB,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,SAAS,CAAC;MACvE,KAAK,IAAMC,IAAI,IAAID,kBAAkB,EAAE;QACrC,IAAI,CAACD,YAAY,CAACE,IAAI,CAAC,EAAE;UACvBlC,OAAO,CAACQ,KAAK,CACX,+CAA+CuB,UAAU,aAAaG,IAAI,EAC5E,CAAC;UACD,OAAO,KAAK;QACd;MACF;MAGA,IAAI,CAACF,YAAY,CAAClB,OAAO,CAACC,OAAO,EAAE;QACjCf,OAAO,CAACQ,KAAK,CACX,+CAA+CuB,UAAU,2BAC3D,CAAC;QACD,OAAO,KAAK;MACd;IACF;IAEA/B,OAAO,CAACC,GAAG,CAAC,iDAAiD8B,UAAU,EAAE,CAAC;IAC1E,OAAO,IAAI;EACb,CAAC,CAAC,OAAOvB,KAAK,EAAE;IACdR,OAAO,CAACQ,KAAK,CACX,8CAA8CuB,UAAU,GAAG,EAC3DvB,KACF,CAAC;IACD,OAAO,KAAK;EACd;AACF,CAAC;AAGM,IAAM2B,gBAAgB,GAAA7C,OAAA,CAAA6C,gBAAA,GAAG,SAAnBA,gBAAgBA,CAAA,EAAe;EAC1CnC,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;EACxDd,WAAW,CAACiD,KAAK,CAAC,CAAC;AACrB,CAAC;AAGM,IAAMC,oBAAoB,GAAA/C,OAAA,CAAA+C,oBAAA,GAAG,SAAvBA,oBAAoBA,CAAA,EAAkC;EACjE,IAAMC,MAA+B,GAAG,CAAC,CAAC;EAC1C,SAAAC,IAAA,IAA2BpD,WAAW,CAACqD,OAAO,CAAC,CAAC,EAAE;IAAA,IAAAC,KAAA,OAAAC,eAAA,CAAA3B,OAAA,EAAAwB,IAAA;IAAA,IAAtCI,GAAG,GAAAF,KAAA;IAAA,IAAEG,KAAK,GAAAH,KAAA;IACpBH,MAAM,CAACK,GAAG,CAAC,GAAGC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK/C,SAAS;EACrD;EACA,OAAOyC,MAAM;AACf,CAAC;AAGM,IAAMO,oBAAoB,GAAAvD,OAAA,CAAAuD,oBAAA,GAAG,SAAvBA,oBAAoBA,CAAA,EAAe;EAC9C7C,OAAO,CAAC8C,IAAI,CAAC,yDAAyD,CAAC;EACvEX,gBAAgB,CAAC,CAAC;EAGlBY,UAAU,CAAC,YAAM;IACfrC,yBAAyB,CAAC,CAAC;EAC7B,CAAC,EAAE,GAAG,CAAC;AACT,CAAC;AAGM,IAAMsC,cAAc,GAAA1D,OAAA,CAAA0D,cAAA,GAAG,SAAjBA,cAAcA,CAAA,EAA2B;EACpD,OAAO7D,WAAW;AACpB,CAAC", "ignoreList": []}