816bf0f6ae996cddfbfde397789002d2
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.isTablet = exports.isSmallPhone = exports.isPortrait = exports.isLargePhone = exports.isLandscape = exports.isIPhoneX = exports.isIPhoneWithNotch = exports.isAndroidWithGestures = exports.hasNotch = exports.hasDynamicIsland = exports.getScreenDimensions = exports.getSafeAreaTop = exports.getSafeAreaInsets = exports.getSafeAreaBottom = exports.getResponsiveSpacing = exports.getResponsiveIconSize = exports.getResponsiveFontSize = exports.getPrimaryTouchTarget = exports.getPlatformShadow = exports.getPlatformBorderRadius = exports.getMinimumTouchTarget = exports.getDeviceType = exports.SCREEN_WIDTH = exports.SCREEN_HEIGHT = exports.PlatformConstants = exports.DeviceTypes = exports.Breakpoints = void 0;
var _reactNative = require("react-native");
var _Dimensions$get = _reactNative.Dimensions.get('window'),
  SCREEN_WIDTH = exports.SCREEN_WIDTH = _Dimensions$get.width,
  SCREEN_HEIGHT = exports.SCREEN_HEIGHT = _Dimensions$get.height;
var DeviceTypes = exports.DeviceTypes = {
  PHONE: 'phone',
  TABLET: 'tablet',
  SMALL_PHONE: 'small_phone',
  LARGE_PHONE: 'large_phone'
};
var Breakpoints = exports.Breakpoints = {
  SMALL_PHONE: 375,
  LARGE_PHONE: 414,
  TABLET: 768,
  LARGE_TABLET: 1024
};
var PlatformConstants = exports.PlatformConstants = {
  iOS: {
    statusBarHeight: _reactNative.Platform.OS === 'ios' ? SCREEN_HEIGHT >= 812 ? 44 : 20 : 0,
    navigationBarHeight: _reactNative.Platform.OS === 'ios' ? SCREEN_HEIGHT >= 812 ? 88 : 64 : 0,
    tabBarHeight: _reactNative.Platform.OS === 'ios' ? SCREEN_HEIGHT >= 812 ? 83 : 49 : 0,
    safeAreaBottom: _reactNative.Platform.OS === 'ios' ? SCREEN_HEIGHT >= 812 ? 34 : 0 : 0,
    borderRadius: {
      small: 8,
      medium: 12,
      large: 16,
      xlarge: 20
    },
    shadowStyle: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2
      },
      shadowOpacity: 0.1,
      shadowRadius: 4
    },
    touchTargetSize: 44
  },
  Android: {
    statusBarHeight: _reactNative.StatusBar.currentHeight || 24,
    navigationBarHeight: 56,
    tabBarHeight: 56,
    safeAreaBottom: 0,
    borderRadius: {
      small: 4,
      medium: 8,
      large: 12,
      xlarge: 16
    },
    shadowStyle: {
      elevation: 4
    },
    touchTargetSize: 48
  }
};
var getDeviceType = exports.getDeviceType = function getDeviceType() {
  if (SCREEN_WIDTH >= Breakpoints.TABLET) {
    return DeviceTypes.TABLET;
  } else if (SCREEN_WIDTH >= Breakpoints.LARGE_PHONE) {
    return DeviceTypes.LARGE_PHONE;
  } else if (SCREEN_WIDTH >= Breakpoints.SMALL_PHONE) {
    return DeviceTypes.PHONE;
  } else {
    return DeviceTypes.SMALL_PHONE;
  }
};
var isTablet = exports.isTablet = function isTablet() {
  return SCREEN_WIDTH >= Breakpoints.TABLET;
};
var isSmallPhone = exports.isSmallPhone = function isSmallPhone() {
  return SCREEN_WIDTH < Breakpoints.SMALL_PHONE;
};
var isLargePhone = exports.isLargePhone = function isLargePhone() {
  return SCREEN_WIDTH >= Breakpoints.LARGE_PHONE;
};
var hasNotch = exports.hasNotch = function hasNotch() {
  return _reactNative.Platform.OS === 'ios' && SCREEN_HEIGHT >= 812;
};
var hasDynamicIsland = exports.hasDynamicIsland = function hasDynamicIsland() {
  return _reactNative.Platform.OS === 'ios' && SCREEN_HEIGHT >= 852;
};
var isIPhoneX = exports.isIPhoneX = function isIPhoneX() {
  return _reactNative.Platform.OS === 'ios' && SCREEN_HEIGHT === 812 && SCREEN_WIDTH === 375;
};
var isIPhoneWithNotch = exports.isIPhoneWithNotch = function isIPhoneWithNotch() {
  return _reactNative.Platform.OS === 'ios' && (hasNotch() || hasDynamicIsland());
};
var isAndroidWithGestures = exports.isAndroidWithGestures = function isAndroidWithGestures() {
  return _reactNative.Platform.OS === 'android' && _reactNative.Platform.Version >= 29;
};
var getSafeAreaInsets = exports.getSafeAreaInsets = function getSafeAreaInsets() {
  if (_reactNative.Platform.OS === 'ios') {
    if (hasDynamicIsland()) {
      return {
        top: 59,
        bottom: 34,
        left: 0,
        right: 0
      };
    } else if (hasNotch()) {
      return {
        top: 44,
        bottom: 34,
        left: 0,
        right: 0
      };
    } else {
      return {
        top: 20,
        bottom: 0,
        left: 0,
        right: 0
      };
    }
  } else {
    return {
      top: _reactNative.StatusBar.currentHeight || 24,
      bottom: 0,
      left: 0,
      right: 0
    };
  }
};
var getSafeAreaTop = exports.getSafeAreaTop = function getSafeAreaTop() {
  var insets = getSafeAreaInsets();
  return insets.top;
};
var getSafeAreaBottom = exports.getSafeAreaBottom = function getSafeAreaBottom() {
  var insets = getSafeAreaInsets();
  return insets.bottom;
};
var getResponsiveSpacing = exports.getResponsiveSpacing = function getResponsiveSpacing(baseSpacing) {
  var scale = Math.min(SCREEN_WIDTH / 375, 1.2);
  return Math.round(baseSpacing * scale);
};
var getResponsiveFontSize = exports.getResponsiveFontSize = function getResponsiveFontSize(baseFontSize) {
  var scale = SCREEN_WIDTH / 375;
  var newSize = baseFontSize * scale;
  if (newSize < 12) return 12;
  if (newSize > 32) return 32;
  return Math.round(newSize);
};
var getResponsiveIconSize = exports.getResponsiveIconSize = function getResponsiveIconSize(baseSize) {
  var scale = Math.min(SCREEN_WIDTH / 375, 1.1);
  return Math.round(baseSize * scale);
};
var getPrimaryTouchTarget = exports.getPrimaryTouchTarget = function getPrimaryTouchTarget() {
  return _reactNative.Platform.OS === 'ios' ? PlatformConstants.iOS.touchTargetSize : PlatformConstants.Android.touchTargetSize;
};
var getMinimumTouchTarget = exports.getMinimumTouchTarget = function getMinimumTouchTarget() {
  return Math.max(44, getPrimaryTouchTarget());
};
var getPlatformShadow = exports.getPlatformShadow = function getPlatformShadow() {
  var elevation = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 4;
  if (_reactNative.Platform.OS === 'ios') {
    return {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: elevation / 2
      },
      shadowOpacity: 0.1,
      shadowRadius: elevation
    };
  } else {
    return {
      elevation: elevation
    };
  }
};
var getPlatformBorderRadius = exports.getPlatformBorderRadius = function getPlatformBorderRadius() {
  var size = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'medium';
  return _reactNative.Platform.OS === 'ios' ? PlatformConstants.iOS.borderRadius[size] : PlatformConstants.Android.borderRadius[size];
};
var getScreenDimensions = exports.getScreenDimensions = function getScreenDimensions() {
  return {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT,
    aspectRatio: SCREEN_WIDTH / SCREEN_HEIGHT
  };
};
var isLandscape = exports.isLandscape = function isLandscape() {
  return SCREEN_WIDTH > SCREEN_HEIGHT;
};
var isPortrait = exports.isPortrait = function isPortrait() {
  return SCREEN_HEIGHT > SCREEN_WIDTH;
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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