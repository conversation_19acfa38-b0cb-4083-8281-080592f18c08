{"version": 3, "names": ["_authSlice", "require", "_navigationAnalytics", "_interopRequireDefault", "NavigationGuardsService", "_classCallCheck2", "default", "routeConfigs", "Map", "initializeRouteConfigs", "_createClass2", "key", "value", "addRouteConfig", "name", "requiresAuth", "allowedRoles", "requiresVerification", "config", "set", "canNavigate", "routeName", "params", "get", "console", "warn", "allowed", "authStore", "useAuthStore", "getState", "isAuthenticated", "userRole", "user", "navigationAnalytics", "trackNavigationError", "redirectTo", "reason", "length", "includes", "getDefaultRouteForRole", "requiresRole", "requiresOnboarding", "hasCompletedOnboarding", "isVerified", "customValidator", "customResult", "validateNavigationFlow", "currentRoute", "targetRoute", "flowContext", "validFlows", "ProviderDetails", "ServiceDetails", "BookingScreen", "Checkout", "Payment", "Profile", "EditProfile", "AccountSettings", "Messages", "Conversation", "allowedTargets", "backNavigation", "isValidBackNavigation", "validTargets", "backNavigationMap", "BookingConfirmation", "validBackTargets", "handleNavigationGuardFailure", "result", "originalRoute", "navigation", "reset", "index", "routes", "error", "getRouteConfig", "isAllowedForRole", "navigationGuards", "exports", "_default"], "sources": ["navigationGuards.ts"], "sourcesContent": ["/**\n * Navigation Guards Service - Handle navigation permissions and validation\n *\n * Service Contract:\n * - Validates navigation permissions based on user role and state\n * - <PERSON><PERSON> authentication requirements for protected routes\n * - Implements navigation flow validation\n * - Provides error handling for invalid navigation attempts\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport { useAuthStore } from '../store/authSlice';\n\nimport navigationAnalytics from './navigationAnalytics';\n\nexport interface NavigationGuardResult {\n  allowed: boolean;\n  redirectTo?: string;\n  reason?: string;\n  requiresAuth?: boolean;\n  requiresRole?: 'customer' | 'provider';\n}\n\nexport interface RouteConfig {\n  name: string;\n  requiresAuth: boolean;\n  allowedRoles?: ('customer' | 'provider')[];\n  requiresOnboarding?: boolean;\n  requiresVerification?: boolean;\n  customValidator?: (userState: any) => NavigationGuardResult;\n}\n\nclass NavigationGuardsService {\n  private routeConfigs: Map<string, RouteConfig> = new Map();\n\n  constructor() {\n    this.initializeRouteConfigs();\n  }\n\n  /**\n   * Initialize route configurations\n   */\n  private initializeRouteConfigs(): void {\n    // Public routes (no authentication required)\n    this.addRouteConfig({\n      name: 'Welcome',\n      requiresAuth: false,\n    });\n\n    this.addRouteConfig({\n      name: 'Login',\n      requiresAuth: false,\n    });\n\n    this.addRouteConfig({\n      name: 'Register',\n      requiresAuth: false,\n    });\n\n    this.addRouteConfig({\n      name: 'ForgotPassword',\n      requiresAuth: false,\n    });\n\n    // Customer routes\n    this.addRouteConfig({\n      name: 'CustomerTabs',\n      requiresAuth: true,\n      allowedRoles: ['customer'],\n    });\n\n    this.addRouteConfig({\n      name: 'Home',\n      requiresAuth: true,\n      allowedRoles: ['customer'],\n    });\n\n    this.addRouteConfig({\n      name: 'Search',\n      requiresAuth: true,\n      allowedRoles: ['customer'],\n    });\n\n    this.addRouteConfig({\n      name: 'Bookings',\n      requiresAuth: true,\n      allowedRoles: ['customer'],\n    });\n\n    this.addRouteConfig({\n      name: 'Messages',\n      requiresAuth: true,\n      allowedRoles: ['customer'],\n    });\n\n    this.addRouteConfig({\n      name: 'Profile',\n      requiresAuth: true,\n      allowedRoles: ['customer'],\n    });\n\n    this.addRouteConfig({\n      name: 'ProviderDetails',\n      requiresAuth: true,\n      allowedRoles: ['customer'],\n    });\n\n    this.addRouteConfig({\n      name: 'ServiceDetails',\n      requiresAuth: true,\n      allowedRoles: ['customer'],\n    });\n\n    this.addRouteConfig({\n      name: 'BookingScreen',\n      requiresAuth: true,\n      allowedRoles: ['customer'],\n    });\n\n    this.addRouteConfig({\n      name: 'Checkout',\n      requiresAuth: true,\n      allowedRoles: ['customer'],\n      requiresVerification: true,\n    });\n\n    this.addRouteConfig({\n      name: 'Payment',\n      requiresAuth: true,\n      allowedRoles: ['customer'],\n      requiresVerification: true,\n    });\n\n    // Provider routes\n    this.addRouteConfig({\n      name: 'ProviderTabs',\n      requiresAuth: true,\n      allowedRoles: ['provider'],\n    });\n\n    this.addRouteConfig({\n      name: 'ProviderDashboard',\n      requiresAuth: true,\n      allowedRoles: ['provider'],\n    });\n\n    this.addRouteConfig({\n      name: 'ProviderBookings',\n      requiresAuth: true,\n      allowedRoles: ['provider'],\n    });\n\n    this.addRouteConfig({\n      name: 'ProviderServices',\n      requiresAuth: true,\n      allowedRoles: ['provider'],\n    });\n\n    this.addRouteConfig({\n      name: 'ProviderProfile',\n      requiresAuth: true,\n      allowedRoles: ['provider'],\n    });\n\n    // Shared authenticated routes\n    this.addRouteConfig({\n      name: 'Conversation',\n      requiresAuth: true,\n      allowedRoles: ['customer', 'provider'],\n    });\n\n    this.addRouteConfig({\n      name: 'Notifications',\n      requiresAuth: true,\n      allowedRoles: ['customer', 'provider'],\n    });\n\n    this.addRouteConfig({\n      name: 'AccountSettings',\n      requiresAuth: true,\n      allowedRoles: ['customer', 'provider'],\n    });\n\n    this.addRouteConfig({\n      name: 'EditProfile',\n      requiresAuth: true,\n      allowedRoles: ['customer', 'provider'],\n    });\n  }\n\n  /**\n   * Add a route configuration\n   */\n  addRouteConfig(config: RouteConfig): void {\n    this.routeConfigs.set(config.name, config);\n  }\n\n  /**\n   * Check if navigation to a route is allowed\n   */\n  canNavigate(routeName: string, params?: any): NavigationGuardResult {\n    const config = this.routeConfigs.get(routeName);\n\n    if (!config) {\n      // Route not configured - allow by default but log warning\n      console.warn(`Navigation guard: Route '${routeName}' not configured`);\n      return { allowed: true };\n    }\n\n    const authStore = useAuthStore.getState();\n    const { isAuthenticated, userRole, user } = authStore;\n\n    // Check authentication requirement\n    if (config.requiresAuth && !isAuthenticated) {\n      navigationAnalytics.trackNavigationError(\n        'Authentication required',\n        routeName,\n        { requiresAuth: true },\n      );\n\n      return {\n        allowed: false,\n        redirectTo: 'Login',\n        reason: 'Authentication required',\n        requiresAuth: true,\n      };\n    }\n\n    // Check role requirement\n    if (config.allowedRoles && config.allowedRoles.length > 0) {\n      if (!userRole || !config.allowedRoles.includes(userRole)) {\n        navigationAnalytics.trackNavigationError(\n          'Insufficient role permissions',\n          routeName,\n          { userRole, allowedRoles: config.allowedRoles },\n        );\n\n        return {\n          allowed: false,\n          redirectTo: this.getDefaultRouteForRole(userRole),\n          reason: 'Insufficient role permissions',\n          requiresRole: config.allowedRoles[0],\n        };\n      }\n    }\n\n    // Check onboarding requirement\n    if (config.requiresOnboarding && user && !user.hasCompletedOnboarding) {\n      return {\n        allowed: false,\n        redirectTo:\n          userRole === 'customer' ? 'CustomerOnboarding' : 'ProviderOnboarding',\n        reason: 'Onboarding required',\n      };\n    }\n\n    // Check verification requirement\n    if (config.requiresVerification && user && !user.isVerified) {\n      return {\n        allowed: false,\n        redirectTo: 'VerificationRequired',\n        reason: 'Account verification required',\n      };\n    }\n\n    // Run custom validator if provided\n    if (config.customValidator) {\n      const customResult = config.customValidator({\n        user,\n        userRole,\n        isAuthenticated,\n      });\n      if (!customResult.allowed) {\n        navigationAnalytics.trackNavigationError(\n          customResult.reason || 'Custom validation failed',\n          routeName,\n          { customValidator: true },\n        );\n        return customResult;\n      }\n    }\n\n    // All checks passed\n    return { allowed: true };\n  }\n\n  /**\n   * Get default route for a user role\n   */\n  private getDefaultRouteForRole(userRole: string | null): string {\n    switch (userRole) {\n      case 'customer':\n        return 'CustomerTabs';\n      case 'provider':\n        return 'ProviderTabs';\n      default:\n        return 'Login';\n    }\n  }\n\n  /**\n   * Validate navigation flow (e.g., booking flow)\n   */\n  validateNavigationFlow(\n    currentRoute: string,\n    targetRoute: string,\n    flowContext?: any,\n  ): NavigationGuardResult {\n    // Define valid navigation flows\n    const validFlows: Record<string, string[]> = {\n      // Booking flow\n      ProviderDetails: ['ServiceDetails', 'BookingScreen'],\n      ServiceDetails: ['BookingScreen', 'ProviderDetails'],\n      BookingScreen: ['Checkout', 'ServiceDetails'],\n      Checkout: ['Payment', 'BookingScreen'],\n      Payment: ['BookingConfirmation', 'Checkout'],\n\n      // Profile flow\n      Profile: ['EditProfile', 'AccountSettings'],\n      EditProfile: ['Profile'],\n      AccountSettings: ['Profile'],\n\n      // Messaging flow\n      Messages: ['Conversation'],\n      Conversation: ['Messages'],\n    };\n\n    const allowedTargets = validFlows[currentRoute];\n\n    if (allowedTargets && !allowedTargets.includes(targetRoute)) {\n      // Check if it's a valid back navigation\n      const backNavigation = this.isValidBackNavigation(\n        currentRoute,\n        targetRoute,\n      );\n      if (!backNavigation) {\n        navigationAnalytics.trackNavigationError(\n          'Invalid navigation flow',\n          targetRoute,\n          { currentRoute, validTargets: allowedTargets },\n        );\n\n        return {\n          allowed: false,\n          reason: 'Invalid navigation flow',\n        };\n      }\n    }\n\n    return { allowed: true };\n  }\n\n  /**\n   * Check if navigation is a valid back navigation\n   */\n  private isValidBackNavigation(\n    currentRoute: string,\n    targetRoute: string,\n  ): boolean {\n    // Define valid back navigation patterns\n    const backNavigationMap: Record<string, string[]> = {\n      ServiceDetails: ['ProviderDetails', 'Search', 'Home'],\n      BookingScreen: ['ServiceDetails', 'ProviderDetails'],\n      Checkout: ['BookingScreen'],\n      Payment: ['Checkout'],\n      BookingConfirmation: ['Home', 'Bookings'],\n      EditProfile: ['Profile'],\n      AccountSettings: ['Profile'],\n      Conversation: ['Messages'],\n      ProviderDetails: ['Search', 'Home'],\n    };\n\n    const validBackTargets = backNavigationMap[currentRoute];\n    return validBackTargets ? validBackTargets.includes(targetRoute) : true;\n  }\n\n  /**\n   * Handle navigation guard failure\n   */\n  handleNavigationGuardFailure(\n    result: NavigationGuardResult,\n    originalRoute: string,\n    navigation: any,\n  ): void {\n    if (result.redirectTo) {\n      // Redirect to appropriate route\n      navigation.reset({\n        index: 0,\n        routes: [{ name: result.redirectTo }],\n      });\n    } else {\n      // Show error message or handle appropriately\n      console.error('Navigation blocked:', result.reason);\n    }\n\n    // Track the navigation failure\n    navigationAnalytics.trackNavigationError(\n      result.reason || 'Navigation guard failure',\n      originalRoute,\n      {\n        redirectTo: result.redirectTo,\n        requiresAuth: result.requiresAuth,\n        requiresRole: result.requiresRole,\n      },\n    );\n  }\n\n  /**\n   * Get route configuration\n   */\n  getRouteConfig(routeName: string): RouteConfig | undefined {\n    return this.routeConfigs.get(routeName);\n  }\n\n  /**\n   * Check if route requires authentication\n   */\n  requiresAuth(routeName: string): boolean {\n    const config = this.routeConfigs.get(routeName);\n    return config?.requiresAuth || false;\n  }\n\n  /**\n   * Check if route is allowed for user role\n   */\n  isAllowedForRole(routeName: string, userRole: string): boolean {\n    const config = this.routeConfigs.get(routeName);\n    if (!config || !config.allowedRoles) return true;\n    return config.allowedRoles.includes(userRole as 'customer' | 'provider');\n  }\n}\n\n// Export singleton instance\nexport const navigationGuards = new NavigationGuardsService();\nexport default navigationGuards;\n"], "mappings": ";;;;;;;AAaA,IAAAA,UAAA,GAAAC,OAAA;AAEA,IAAAC,oBAAA,GAAAC,sBAAA,CAAAF,OAAA;AAAwD,IAmBlDG,uBAAuB;EAG3B,SAAAA,wBAAA,EAAc;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAF,uBAAA;IAAA,KAFNG,YAAY,GAA6B,IAAIC,GAAG,CAAC,CAAC;IAGxD,IAAI,CAACC,sBAAsB,CAAC,CAAC;EAC/B;EAAC,WAAAC,aAAA,CAAAJ,OAAA,EAAAF,uBAAA;IAAAO,GAAA;IAAAC,KAAA,EAKD,SAAQH,sBAAsBA,CAAA,EAAS;MAErC,IAAI,CAACI,cAAc,CAAC;QAClBC,IAAI,EAAE,SAAS;QACfC,YAAY,EAAE;MAChB,CAAC,CAAC;MAEF,IAAI,CAACF,cAAc,CAAC;QAClBC,IAAI,EAAE,OAAO;QACbC,YAAY,EAAE;MAChB,CAAC,CAAC;MAEF,IAAI,CAACF,cAAc,CAAC;QAClBC,IAAI,EAAE,UAAU;QAChBC,YAAY,EAAE;MAChB,CAAC,CAAC;MAEF,IAAI,CAACF,cAAc,CAAC;QAClBC,IAAI,EAAE,gBAAgB;QACtBC,YAAY,EAAE;MAChB,CAAC,CAAC;MAGF,IAAI,CAACF,cAAc,CAAC;QAClBC,IAAI,EAAE,cAAc;QACpBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,CAAC,UAAU;MAC3B,CAAC,CAAC;MAEF,IAAI,CAACH,cAAc,CAAC;QAClBC,IAAI,EAAE,MAAM;QACZC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,CAAC,UAAU;MAC3B,CAAC,CAAC;MAEF,IAAI,CAACH,cAAc,CAAC;QAClBC,IAAI,EAAE,QAAQ;QACdC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,CAAC,UAAU;MAC3B,CAAC,CAAC;MAEF,IAAI,CAACH,cAAc,CAAC;QAClBC,IAAI,EAAE,UAAU;QAChBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,CAAC,UAAU;MAC3B,CAAC,CAAC;MAEF,IAAI,CAACH,cAAc,CAAC;QAClBC,IAAI,EAAE,UAAU;QAChBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,CAAC,UAAU;MAC3B,CAAC,CAAC;MAEF,IAAI,CAACH,cAAc,CAAC;QAClBC,IAAI,EAAE,SAAS;QACfC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,CAAC,UAAU;MAC3B,CAAC,CAAC;MAEF,IAAI,CAACH,cAAc,CAAC;QAClBC,IAAI,EAAE,iBAAiB;QACvBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,CAAC,UAAU;MAC3B,CAAC,CAAC;MAEF,IAAI,CAACH,cAAc,CAAC;QAClBC,IAAI,EAAE,gBAAgB;QACtBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,CAAC,UAAU;MAC3B,CAAC,CAAC;MAEF,IAAI,CAACH,cAAc,CAAC;QAClBC,IAAI,EAAE,eAAe;QACrBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,CAAC,UAAU;MAC3B,CAAC,CAAC;MAEF,IAAI,CAACH,cAAc,CAAC;QAClBC,IAAI,EAAE,UAAU;QAChBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,CAAC,UAAU,CAAC;QAC1BC,oBAAoB,EAAE;MACxB,CAAC,CAAC;MAEF,IAAI,CAACJ,cAAc,CAAC;QAClBC,IAAI,EAAE,SAAS;QACfC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,CAAC,UAAU,CAAC;QAC1BC,oBAAoB,EAAE;MACxB,CAAC,CAAC;MAGF,IAAI,CAACJ,cAAc,CAAC;QAClBC,IAAI,EAAE,cAAc;QACpBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,CAAC,UAAU;MAC3B,CAAC,CAAC;MAEF,IAAI,CAACH,cAAc,CAAC;QAClBC,IAAI,EAAE,mBAAmB;QACzBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,CAAC,UAAU;MAC3B,CAAC,CAAC;MAEF,IAAI,CAACH,cAAc,CAAC;QAClBC,IAAI,EAAE,kBAAkB;QACxBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,CAAC,UAAU;MAC3B,CAAC,CAAC;MAEF,IAAI,CAACH,cAAc,CAAC;QAClBC,IAAI,EAAE,kBAAkB;QACxBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,CAAC,UAAU;MAC3B,CAAC,CAAC;MAEF,IAAI,CAACH,cAAc,CAAC;QAClBC,IAAI,EAAE,iBAAiB;QACvBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,CAAC,UAAU;MAC3B,CAAC,CAAC;MAGF,IAAI,CAACH,cAAc,CAAC;QAClBC,IAAI,EAAE,cAAc;QACpBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,CAAC,UAAU,EAAE,UAAU;MACvC,CAAC,CAAC;MAEF,IAAI,CAACH,cAAc,CAAC;QAClBC,IAAI,EAAE,eAAe;QACrBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,CAAC,UAAU,EAAE,UAAU;MACvC,CAAC,CAAC;MAEF,IAAI,CAACH,cAAc,CAAC;QAClBC,IAAI,EAAE,iBAAiB;QACvBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,CAAC,UAAU,EAAE,UAAU;MACvC,CAAC,CAAC;MAEF,IAAI,CAACH,cAAc,CAAC;QAClBC,IAAI,EAAE,aAAa;QACnBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,CAAC,UAAU,EAAE,UAAU;MACvC,CAAC,CAAC;IACJ;EAAC;IAAAL,GAAA;IAAAC,KAAA,EAKD,SAAAC,cAAcA,CAACK,MAAmB,EAAQ;MACxC,IAAI,CAACX,YAAY,CAACY,GAAG,CAACD,MAAM,CAACJ,IAAI,EAAEI,MAAM,CAAC;IAC5C;EAAC;IAAAP,GAAA;IAAAC,KAAA,EAKD,SAAAQ,WAAWA,CAACC,SAAiB,EAAEC,MAAY,EAAyB;MAClE,IAAMJ,MAAM,GAAG,IAAI,CAACX,YAAY,CAACgB,GAAG,CAACF,SAAS,CAAC;MAE/C,IAAI,CAACH,MAAM,EAAE;QAEXM,OAAO,CAACC,IAAI,CAAC,4BAA4BJ,SAAS,kBAAkB,CAAC;QACrE,OAAO;UAAEK,OAAO,EAAE;QAAK,CAAC;MAC1B;MAEA,IAAMC,SAAS,GAAGC,uBAAY,CAACC,QAAQ,CAAC,CAAC;MACzC,IAAQC,eAAe,GAAqBH,SAAS,CAA7CG,eAAe;QAAEC,QAAQ,GAAWJ,SAAS,CAA5BI,QAAQ;QAAEC,IAAI,GAAKL,SAAS,CAAlBK,IAAI;MAGvC,IAAId,MAAM,CAACH,YAAY,IAAI,CAACe,eAAe,EAAE;QAC3CG,4BAAmB,CAACC,oBAAoB,CACtC,yBAAyB,EACzBb,SAAS,EACT;UAAEN,YAAY,EAAE;QAAK,CACvB,CAAC;QAED,OAAO;UACLW,OAAO,EAAE,KAAK;UACdS,UAAU,EAAE,OAAO;UACnBC,MAAM,EAAE,yBAAyB;UACjCrB,YAAY,EAAE;QAChB,CAAC;MACH;MAGA,IAAIG,MAAM,CAACF,YAAY,IAAIE,MAAM,CAACF,YAAY,CAACqB,MAAM,GAAG,CAAC,EAAE;QACzD,IAAI,CAACN,QAAQ,IAAI,CAACb,MAAM,CAACF,YAAY,CAACsB,QAAQ,CAACP,QAAQ,CAAC,EAAE;UACxDE,4BAAmB,CAACC,oBAAoB,CACtC,+BAA+B,EAC/Bb,SAAS,EACT;YAAEU,QAAQ,EAARA,QAAQ;YAAEf,YAAY,EAAEE,MAAM,CAACF;UAAa,CAChD,CAAC;UAED,OAAO;YACLU,OAAO,EAAE,KAAK;YACdS,UAAU,EAAE,IAAI,CAACI,sBAAsB,CAACR,QAAQ,CAAC;YACjDK,MAAM,EAAE,+BAA+B;YACvCI,YAAY,EAAEtB,MAAM,CAACF,YAAY,CAAC,CAAC;UACrC,CAAC;QACH;MACF;MAGA,IAAIE,MAAM,CAACuB,kBAAkB,IAAIT,IAAI,IAAI,CAACA,IAAI,CAACU,sBAAsB,EAAE;QACrE,OAAO;UACLhB,OAAO,EAAE,KAAK;UACdS,UAAU,EACRJ,QAAQ,KAAK,UAAU,GAAG,oBAAoB,GAAG,oBAAoB;UACvEK,MAAM,EAAE;QACV,CAAC;MACH;MAGA,IAAIlB,MAAM,CAACD,oBAAoB,IAAIe,IAAI,IAAI,CAACA,IAAI,CAACW,UAAU,EAAE;QAC3D,OAAO;UACLjB,OAAO,EAAE,KAAK;UACdS,UAAU,EAAE,sBAAsB;UAClCC,MAAM,EAAE;QACV,CAAC;MACH;MAGA,IAAIlB,MAAM,CAAC0B,eAAe,EAAE;QAC1B,IAAMC,YAAY,GAAG3B,MAAM,CAAC0B,eAAe,CAAC;UAC1CZ,IAAI,EAAJA,IAAI;UACJD,QAAQ,EAARA,QAAQ;UACRD,eAAe,EAAfA;QACF,CAAC,CAAC;QACF,IAAI,CAACe,YAAY,CAACnB,OAAO,EAAE;UACzBO,4BAAmB,CAACC,oBAAoB,CACtCW,YAAY,CAACT,MAAM,IAAI,0BAA0B,EACjDf,SAAS,EACT;YAAEuB,eAAe,EAAE;UAAK,CAC1B,CAAC;UACD,OAAOC,YAAY;QACrB;MACF;MAGA,OAAO;QAAEnB,OAAO,EAAE;MAAK,CAAC;IAC1B;EAAC;IAAAf,GAAA;IAAAC,KAAA,EAKD,SAAQ2B,sBAAsBA,CAACR,QAAuB,EAAU;MAC9D,QAAQA,QAAQ;QACd,KAAK,UAAU;UACb,OAAO,cAAc;QACvB,KAAK,UAAU;UACb,OAAO,cAAc;QACvB;UACE,OAAO,OAAO;MAClB;IACF;EAAC;IAAApB,GAAA;IAAAC,KAAA,EAKD,SAAAkC,sBAAsBA,CACpBC,YAAoB,EACpBC,WAAmB,EACnBC,WAAiB,EACM;MAEvB,IAAMC,UAAoC,GAAG;QAE3CC,eAAe,EAAE,CAAC,gBAAgB,EAAE,eAAe,CAAC;QACpDC,cAAc,EAAE,CAAC,eAAe,EAAE,iBAAiB,CAAC;QACpDC,aAAa,EAAE,CAAC,UAAU,EAAE,gBAAgB,CAAC;QAC7CC,QAAQ,EAAE,CAAC,SAAS,EAAE,eAAe,CAAC;QACtCC,OAAO,EAAE,CAAC,qBAAqB,EAAE,UAAU,CAAC;QAG5CC,OAAO,EAAE,CAAC,aAAa,EAAE,iBAAiB,CAAC;QAC3CC,WAAW,EAAE,CAAC,SAAS,CAAC;QACxBC,eAAe,EAAE,CAAC,SAAS,CAAC;QAG5BC,QAAQ,EAAE,CAAC,cAAc,CAAC;QAC1BC,YAAY,EAAE,CAAC,UAAU;MAC3B,CAAC;MAED,IAAMC,cAAc,GAAGX,UAAU,CAACH,YAAY,CAAC;MAE/C,IAAIc,cAAc,IAAI,CAACA,cAAc,CAACvB,QAAQ,CAACU,WAAW,CAAC,EAAE;QAE3D,IAAMc,cAAc,GAAG,IAAI,CAACC,qBAAqB,CAC/ChB,YAAY,EACZC,WACF,CAAC;QACD,IAAI,CAACc,cAAc,EAAE;UACnB7B,4BAAmB,CAACC,oBAAoB,CACtC,yBAAyB,EACzBc,WAAW,EACX;YAAED,YAAY,EAAZA,YAAY;YAAEiB,YAAY,EAAEH;UAAe,CAC/C,CAAC;UAED,OAAO;YACLnC,OAAO,EAAE,KAAK;YACdU,MAAM,EAAE;UACV,CAAC;QACH;MACF;MAEA,OAAO;QAAEV,OAAO,EAAE;MAAK,CAAC;IAC1B;EAAC;IAAAf,GAAA;IAAAC,KAAA,EAKD,SAAQmD,qBAAqBA,CAC3BhB,YAAoB,EACpBC,WAAmB,EACV;MAET,IAAMiB,iBAA2C,GAAG;QAClDb,cAAc,EAAE,CAAC,iBAAiB,EAAE,QAAQ,EAAE,MAAM,CAAC;QACrDC,aAAa,EAAE,CAAC,gBAAgB,EAAE,iBAAiB,CAAC;QACpDC,QAAQ,EAAE,CAAC,eAAe,CAAC;QAC3BC,OAAO,EAAE,CAAC,UAAU,CAAC;QACrBW,mBAAmB,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;QACzCT,WAAW,EAAE,CAAC,SAAS,CAAC;QACxBC,eAAe,EAAE,CAAC,SAAS,CAAC;QAC5BE,YAAY,EAAE,CAAC,UAAU,CAAC;QAC1BT,eAAe,EAAE,CAAC,QAAQ,EAAE,MAAM;MACpC,CAAC;MAED,IAAMgB,gBAAgB,GAAGF,iBAAiB,CAAClB,YAAY,CAAC;MACxD,OAAOoB,gBAAgB,GAAGA,gBAAgB,CAAC7B,QAAQ,CAACU,WAAW,CAAC,GAAG,IAAI;IACzE;EAAC;IAAArC,GAAA;IAAAC,KAAA,EAKD,SAAAwD,4BAA4BA,CAC1BC,MAA6B,EAC7BC,aAAqB,EACrBC,UAAe,EACT;MACN,IAAIF,MAAM,CAAClC,UAAU,EAAE;QAErBoC,UAAU,CAACC,KAAK,CAAC;UACfC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;YAAE5D,IAAI,EAAEuD,MAAM,CAAClC;UAAW,CAAC;QACtC,CAAC,CAAC;MACJ,CAAC,MAAM;QAELX,OAAO,CAACmD,KAAK,CAAC,qBAAqB,EAAEN,MAAM,CAACjC,MAAM,CAAC;MACrD;MAGAH,4BAAmB,CAACC,oBAAoB,CACtCmC,MAAM,CAACjC,MAAM,IAAI,0BAA0B,EAC3CkC,aAAa,EACb;QACEnC,UAAU,EAAEkC,MAAM,CAAClC,UAAU;QAC7BpB,YAAY,EAAEsD,MAAM,CAACtD,YAAY;QACjCyB,YAAY,EAAE6B,MAAM,CAAC7B;MACvB,CACF,CAAC;IACH;EAAC;IAAA7B,GAAA;IAAAC,KAAA,EAKD,SAAAgE,cAAcA,CAACvD,SAAiB,EAA2B;MACzD,OAAO,IAAI,CAACd,YAAY,CAACgB,GAAG,CAACF,SAAS,CAAC;IACzC;EAAC;IAAAV,GAAA;IAAAC,KAAA,EAKD,SAAAG,YAAYA,CAACM,SAAiB,EAAW;MACvC,IAAMH,MAAM,GAAG,IAAI,CAACX,YAAY,CAACgB,GAAG,CAACF,SAAS,CAAC;MAC/C,OAAO,CAAAH,MAAM,oBAANA,MAAM,CAAEH,YAAY,KAAI,KAAK;IACtC;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EAKD,SAAAiE,gBAAgBA,CAACxD,SAAiB,EAAEU,QAAgB,EAAW;MAC7D,IAAMb,MAAM,GAAG,IAAI,CAACX,YAAY,CAACgB,GAAG,CAACF,SAAS,CAAC;MAC/C,IAAI,CAACH,MAAM,IAAI,CAACA,MAAM,CAACF,YAAY,EAAE,OAAO,IAAI;MAChD,OAAOE,MAAM,CAACF,YAAY,CAACsB,QAAQ,CAACP,QAAmC,CAAC;IAC1E;EAAC;AAAA;AAII,IAAM+C,gBAAgB,GAAAC,OAAA,CAAAD,gBAAA,GAAG,IAAI1E,uBAAuB,CAAC,CAAC;AAAC,IAAA4E,QAAA,GAAAD,OAAA,CAAAzE,OAAA,GAC/CwE,gBAAgB", "ignoreList": []}