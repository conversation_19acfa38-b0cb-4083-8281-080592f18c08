07b38b0eda7dd9153fc96e7b32929a0a
_getJestObj().mock("../../services/bookingService");
_getJestObj().mock("../../services/paymentService");
_getJestObj().mock('react-native/Libraries/Alert/Alert', function () {
  return {
    alert: jest.fn()
  };
});
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _reactNative = require("@testing-library/react-native");
var _react = _interopRequireDefault(require("react"));
var _reactNative2 = require("react-native");
var _bookingService = require("../../services/bookingService");
var _paymentService = require("../../services/paymentService");
var _integrationTestUtils = require("../../utils/integrationTestUtils");
var _BookingFlowScreen = require("../BookingFlowScreen");
var _jsxRuntime = require("react/jsx-runtime");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var mockBookingService = _bookingService.bookingService;
var mockPaymentService = _paymentService.paymentService;
describe('BookingFlowScreen', function () {
  var mockRoute = {
    params: {
      providerId: 'test_provider_123',
      serviceId: 'test_service_123',
      serviceName: 'Test Service',
      servicePrice: 50.0,
      serviceDuration: 60
    }
  };
  var mockNavigation = {
    navigate: jest.fn(),
    goBack: jest.fn(),
    replace: jest.fn()
  };
  beforeEach(function () {
    jest.clearAllMocks();
    mockBookingService.createBooking.mockResolvedValue(_integrationTestUtils.mockApiResponses.createBooking);
    mockBookingService.getProviderAvailability.mockResolvedValue({
      availableSlots: ['09:00', '10:00', '11:00', '14:00', '15:00'],
      date: '2024-01-15'
    });
    mockPaymentService.processPayment.mockResolvedValue({
      success: true,
      transactionId: 'test_transaction_123'
    });
  });
  describe('Service Selection Step', function () {
    it('should display service selection when no service is pre-selected', (0, _asyncToGenerator2.default)(function* () {
      var routeWithoutService = {
        params: {
          providerId: 'test_provider_123'
        }
      };
      var _renderWithIntegratio = (0, _integrationTestUtils.renderWithIntegrationSetup)((0, _jsxRuntime.jsx)(_BookingFlowScreen.BookingFlowScreen, {
          route: routeWithoutService,
          navigation: mockNavigation
        })),
        getByTestId = _renderWithIntegratio.getByTestId;
      yield (0, _reactNative.waitFor)(function () {
        expect(getByTestId('service-selection-step')).toBeTruthy();
      });
    }));
    it('should skip service selection when service is pre-selected', (0, _asyncToGenerator2.default)(function* () {
      var _renderWithIntegratio2 = (0, _integrationTestUtils.renderWithIntegrationSetup)((0, _jsxRuntime.jsx)(_BookingFlowScreen.BookingFlowScreen, {
          route: mockRoute,
          navigation: mockNavigation
        })),
        getByTestId = _renderWithIntegratio2.getByTestId;
      yield (0, _reactNative.waitFor)(function () {
        expect(getByTestId('time-slot-selection-step')).toBeTruthy();
      });
    }));
    it('should handle service selection and proceed to time slot selection', (0, _asyncToGenerator2.default)(function* () {
      var routeWithoutService = {
        params: {
          providerId: 'test_provider_123'
        }
      };
      var _renderWithIntegratio3 = (0, _integrationTestUtils.renderWithIntegrationSetup)((0, _jsxRuntime.jsx)(_BookingFlowScreen.BookingFlowScreen, {
          route: routeWithoutService,
          navigation: mockNavigation
        })),
        getByTestId = _renderWithIntegratio3.getByTestId;
      yield _integrationTestUtils.testBookingFlow.selectService(getByTestId, 'test_service_123');
      expect(getByTestId('time-slot-selection-step')).toBeTruthy();
    }));
  });
  describe('Time Slot Selection Step', function () {
    it('should display available time slots', (0, _asyncToGenerator2.default)(function* () {
      var _renderWithIntegratio4 = (0, _integrationTestUtils.renderWithIntegrationSetup)((0, _jsxRuntime.jsx)(_BookingFlowScreen.BookingFlowScreen, {
          route: mockRoute,
          navigation: mockNavigation
        })),
        getByTestId = _renderWithIntegratio4.getByTestId;
      yield (0, _reactNative.waitFor)(function () {
        expect(getByTestId('time-slot-09:00')).toBeTruthy();
        expect(getByTestId('time-slot-10:00')).toBeTruthy();
        expect(getByTestId('time-slot-11:00')).toBeTruthy();
      });
    }));
    it('should handle time slot selection and proceed to customer info', (0, _asyncToGenerator2.default)(function* () {
      var _renderWithIntegratio5 = (0, _integrationTestUtils.renderWithIntegrationSetup)((0, _jsxRuntime.jsx)(_BookingFlowScreen.BookingFlowScreen, {
          route: mockRoute,
          navigation: mockNavigation
        })),
        getByTestId = _renderWithIntegratio5.getByTestId;
      yield _integrationTestUtils.testBookingFlow.selectTimeSlot(getByTestId, '10:00');
      expect(getByTestId('customer-info-step')).toBeTruthy();
    }));
    it('should handle availability loading errors', (0, _asyncToGenerator2.default)(function* () {
      mockBookingService.getProviderAvailability.mockRejectedValue(new Error('Failed to load availability'));
      var _renderWithIntegratio6 = (0, _integrationTestUtils.renderWithIntegrationSetup)((0, _jsxRuntime.jsx)(_BookingFlowScreen.BookingFlowScreen, {
          route: mockRoute,
          navigation: mockNavigation
        })),
        getByTestId = _renderWithIntegratio6.getByTestId;
      yield (0, _reactNative.waitFor)(function () {
        expect(getByTestId('error-message')).toBeTruthy();
      });
    }));
  });
  describe('Customer Information Step', function () {
    it('should pre-fill customer information for logged-in users', (0, _asyncToGenerator2.default)(function* () {
      var testUser = (0, _integrationTestUtils.createTestUser)();
      var _renderWithIntegratio7 = (0, _integrationTestUtils.renderWithIntegrationSetup)((0, _jsxRuntime.jsx)(_BookingFlowScreen.BookingFlowScreen, {
          route: mockRoute,
          navigation: mockNavigation
        }), {
          store: {
            getState: function getState() {
              return {
                auth: {
                  user: testUser,
                  authToken: 'test_token',
                  isAuthenticated: true
                }
              };
            }
          }
        }),
        getByTestId = _renderWithIntegratio7.getByTestId;
      yield _integrationTestUtils.testBookingFlow.selectTimeSlot(getByTestId, '10:00');
      yield (0, _reactNative.waitFor)(function () {
        var firstNameInput = getByTestId('first-name-input');
        expect(firstNameInput.props.value).toBe(testUser.firstName);
      });
    }));
    it('should validate required fields', (0, _asyncToGenerator2.default)(function* () {
      var _renderWithIntegratio8 = (0, _integrationTestUtils.renderWithIntegrationSetup)((0, _jsxRuntime.jsx)(_BookingFlowScreen.BookingFlowScreen, {
          route: mockRoute,
          navigation: mockNavigation
        })),
        getByTestId = _renderWithIntegratio8.getByTestId;
      yield _integrationTestUtils.testBookingFlow.selectTimeSlot(getByTestId, '10:00');
      var continueButton = getByTestId('continue-button');
      _reactNative.fireEvent.press(continueButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(getByTestId('validation-error')).toBeTruthy();
      });
    }));
    it('should proceed to payment step with valid information', (0, _asyncToGenerator2.default)(function* () {
      var _renderWithIntegratio9 = (0, _integrationTestUtils.renderWithIntegrationSetup)((0, _jsxRuntime.jsx)(_BookingFlowScreen.BookingFlowScreen, {
          route: mockRoute,
          navigation: mockNavigation
        })),
        getByTestId = _renderWithIntegratio9.getByTestId;
      yield _integrationTestUtils.testBookingFlow.selectTimeSlot(getByTestId, '10:00');
      yield _integrationTestUtils.testBookingFlow.fillCustomerInfo(getByTestId, {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+**********'
      });
      expect(getByTestId('payment-step')).toBeTruthy();
    }));
  });
  describe('Payment Step', function () {
    it('should display payment methods', (0, _asyncToGenerator2.default)(function* () {
      var _renderWithIntegratio0 = (0, _integrationTestUtils.renderWithIntegrationSetup)((0, _jsxRuntime.jsx)(_BookingFlowScreen.BookingFlowScreen, {
          route: mockRoute,
          navigation: mockNavigation
        })),
        getByTestId = _renderWithIntegratio0.getByTestId;
      yield _integrationTestUtils.testBookingFlow.selectTimeSlot(getByTestId, '10:00');
      yield _integrationTestUtils.testBookingFlow.fillCustomerInfo(getByTestId, {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+**********'
      });
      yield (0, _reactNative.waitFor)(function () {
        expect(getByTestId('payment-methods-list')).toBeTruthy();
      });
    }));
    it('should proceed to summary with selected payment method', (0, _asyncToGenerator2.default)(function* () {
      var _renderWithIntegratio1 = (0, _integrationTestUtils.renderWithIntegrationSetup)((0, _jsxRuntime.jsx)(_BookingFlowScreen.BookingFlowScreen, {
          route: mockRoute,
          navigation: mockNavigation
        })),
        getByTestId = _renderWithIntegratio1.getByTestId;
      yield _integrationTestUtils.testBookingFlow.selectTimeSlot(getByTestId, '10:00');
      yield _integrationTestUtils.testBookingFlow.fillCustomerInfo(getByTestId, {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+**********'
      });
      yield _integrationTestUtils.testBookingFlow.selectPaymentMethod(getByTestId, 'test_payment_method');
      expect(getByTestId('booking-summary-step')).toBeTruthy();
    }));
  });
  describe('Booking Confirmation', function () {
    it('should create booking and process payment successfully', (0, _asyncToGenerator2.default)(function* () {
      var _renderWithIntegratio10 = (0, _integrationTestUtils.renderWithIntegrationSetup)((0, _jsxRuntime.jsx)(_BookingFlowScreen.BookingFlowScreen, {
          route: mockRoute,
          navigation: mockNavigation
        })),
        getByTestId = _renderWithIntegratio10.getByTestId;
      yield _integrationTestUtils.testBookingFlow.selectTimeSlot(getByTestId, '10:00');
      yield _integrationTestUtils.testBookingFlow.fillCustomerInfo(getByTestId, {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+**********'
      });
      yield _integrationTestUtils.testBookingFlow.selectPaymentMethod(getByTestId, 'test_payment_method');
      yield _integrationTestUtils.testBookingFlow.confirmBooking(getByTestId);
      expect(mockBookingService.createBooking).toHaveBeenCalledWith({
        providerId: 'test_provider_123',
        serviceId: 'test_service_123',
        scheduledDate: expect.any(String),
        scheduledTime: '10:00',
        customerInfo: expect.objectContaining({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '+**********'
        })
      });
      expect(mockPaymentService.processPayment).toHaveBeenCalled();
      expect(mockNavigation.replace).toHaveBeenCalledWith('BookingConfirmation', expect.any(Object));
    }));
    it('should handle booking creation errors', (0, _asyncToGenerator2.default)(function* () {
      mockBookingService.createBooking.mockRejectedValue(new Error('Failed to create booking'));
      var _renderWithIntegratio11 = (0, _integrationTestUtils.renderWithIntegrationSetup)((0, _jsxRuntime.jsx)(_BookingFlowScreen.BookingFlowScreen, {
          route: mockRoute,
          navigation: mockNavigation
        })),
        getByTestId = _renderWithIntegratio11.getByTestId;
      yield _integrationTestUtils.testBookingFlow.selectTimeSlot(getByTestId, '10:00');
      yield _integrationTestUtils.testBookingFlow.fillCustomerInfo(getByTestId, {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+**********'
      });
      yield _integrationTestUtils.testBookingFlow.selectPaymentMethod(getByTestId, 'test_payment_method');
      var confirmButton = getByTestId('confirm-booking-button');
      _reactNative.fireEvent.press(confirmButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative2.Alert.alert).toHaveBeenCalledWith('Booking Failed', expect.stringContaining('Failed to create booking'));
      });
    }));
    it('should handle payment processing errors', (0, _asyncToGenerator2.default)(function* () {
      mockPaymentService.processPayment.mockRejectedValue(new Error('Payment failed'));
      var _renderWithIntegratio12 = (0, _integrationTestUtils.renderWithIntegrationSetup)((0, _jsxRuntime.jsx)(_BookingFlowScreen.BookingFlowScreen, {
          route: mockRoute,
          navigation: mockNavigation
        })),
        getByTestId = _renderWithIntegratio12.getByTestId;
      yield _integrationTestUtils.testBookingFlow.selectTimeSlot(getByTestId, '10:00');
      yield _integrationTestUtils.testBookingFlow.fillCustomerInfo(getByTestId, {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+**********'
      });
      yield _integrationTestUtils.testBookingFlow.selectPaymentMethod(getByTestId, 'test_payment_method');
      var confirmButton = getByTestId('confirm-booking-button');
      _reactNative.fireEvent.press(confirmButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative2.Alert.alert).toHaveBeenCalledWith('Booking Failed', expect.stringContaining('Payment failed'));
      });
    }));
  });
  describe('Navigation and Step Management', function () {
    it('should handle back navigation correctly', (0, _asyncToGenerator2.default)(function* () {
      var _renderWithIntegratio13 = (0, _integrationTestUtils.renderWithIntegrationSetup)((0, _jsxRuntime.jsx)(_BookingFlowScreen.BookingFlowScreen, {
          route: mockRoute,
          navigation: mockNavigation
        })),
        getByTestId = _renderWithIntegratio13.getByTestId;
      yield _integrationTestUtils.testBookingFlow.selectTimeSlot(getByTestId, '10:00');
      var backButton = getByTestId('back-button');
      _reactNative.fireEvent.press(backButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(getByTestId('time-slot-selection-step')).toBeTruthy();
      });
    }));
    it('should handle close navigation', (0, _asyncToGenerator2.default)(function* () {
      var _renderWithIntegratio14 = (0, _integrationTestUtils.renderWithIntegrationSetup)((0, _jsxRuntime.jsx)(_BookingFlowScreen.BookingFlowScreen, {
          route: mockRoute,
          navigation: mockNavigation
        })),
        getByTestId = _renderWithIntegratio14.getByTestId;
      var closeButton = getByTestId('close-button');
      _reactNative.fireEvent.press(closeButton);
      expect(mockNavigation.goBack).toHaveBeenCalled();
    }));
  });
  describe('Performance Testing', function () {
    it('should render within performance bounds', (0, _asyncToGenerator2.default)(function* () {
      var renderTime = yield _integrationTestUtils.performanceTestUtils.measureRenderTime((0, _jsxRuntime.jsx)(_BookingFlowScreen.BookingFlowScreen, {
        route: mockRoute,
        navigation: mockNavigation
      }));
      _integrationTestUtils.performanceTestUtils.expectPerformanceWithinBounds(renderTime, 1000);
    }));
    it('should handle step transitions efficiently', (0, _asyncToGenerator2.default)(function* () {
      var _renderWithIntegratio15 = (0, _integrationTestUtils.renderWithIntegrationSetup)((0, _jsxRuntime.jsx)(_BookingFlowScreen.BookingFlowScreen, {
          route: mockRoute,
          navigation: mockNavigation
        })),
        getByTestId = _renderWithIntegratio15.getByTestId;
      var interactionTime = yield _integrationTestUtils.performanceTestUtils.measureInteractionTime(getByTestId, 'time-slot-10:00');
      _integrationTestUtils.performanceTestUtils.expectPerformanceWithinBounds(interactionTime, 500);
    }));
  });
  describe('Accessibility Testing', function () {
    it('should have proper accessibility labels', (0, _asyncToGenerator2.default)(function* () {
      var _renderWithIntegratio16 = (0, _integrationTestUtils.renderWithIntegrationSetup)((0, _jsxRuntime.jsx)(_BookingFlowScreen.BookingFlowScreen, {
          route: mockRoute,
          navigation: mockNavigation
        })),
        getByTestId = _renderWithIntegratio16.getByTestId;
      var accessibilityTestIds = ['close-button', 'back-button', 'next-button', 'booking-flow-screen'];
      _integrationTestUtils.accessibilityTestUtils.checkAccessibilityLabels(getByTestId, accessibilityTestIds);
    }));
    it('should have proper accessibility roles', (0, _asyncToGenerator2.default)(function* () {
      var _renderWithIntegratio17 = (0, _integrationTestUtils.renderWithIntegrationSetup)((0, _jsxRuntime.jsx)(_BookingFlowScreen.BookingFlowScreen, {
          route: mockRoute,
          navigation: mockNavigation
        })),
        getByTestId = _renderWithIntegratio17.getByTestId;
      var accessibilityTestIds = ['close-button', 'back-button', 'next-button'];
      _integrationTestUtils.accessibilityTestUtils.checkAccessibilityRoles(getByTestId, accessibilityTestIds);
    }));
    it('should meet minimum touch target requirements', (0, _asyncToGenerator2.default)(function* () {
      var _renderWithIntegratio18 = (0, _integrationTestUtils.renderWithIntegrationSetup)((0, _jsxRuntime.jsx)(_BookingFlowScreen.BookingFlowScreen, {
          route: mockRoute,
          navigation: mockNavigation
        })),
        getByTestId = _renderWithIntegratio18.getByTestId;
      var touchTargetTestIds = ['close-button', 'back-button', 'next-button'];
      _integrationTestUtils.accessibilityTestUtils.checkMinimumTouchTargets(getByTestId, touchTargetTestIds);
    }));
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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