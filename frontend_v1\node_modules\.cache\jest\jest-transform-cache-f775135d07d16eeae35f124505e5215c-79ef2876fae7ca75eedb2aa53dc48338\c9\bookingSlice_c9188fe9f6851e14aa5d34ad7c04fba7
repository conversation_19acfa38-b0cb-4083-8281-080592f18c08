9cda4f98fafe2ecf56ebf3139a4a1641
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useBookingStore = exports.bookingSelectors = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _zustand = require("zustand");
var _middleware = require("zustand/middleware");
var _bookingService = require("../services/bookingService");
var initialState = {
  bookings: [],
  currentBooking: null,
  status: 'idle',
  error: null,
  filter: 'all',
  sortBy: 'date',
  sortOrder: 'desc',
  searchQuery: '',
  currentPage: 1,
  itemsPerPage: 10,
  totalItems: 0
};
var useBookingStore = exports.useBookingStore = (0, _zustand.create)()((0, _middleware.devtools)((0, _middleware.persist)(function (set, get) {
  return Object.assign({}, initialState, {
    setBookings: function setBookings(bookings) {
      return set(function (state) {
        return Object.assign({}, state, {
          bookings: bookings,
          totalItems: bookings.length
        });
      }, false, 'booking/setBookings');
    },
    addBooking: function addBooking(booking) {
      return set(function (state) {
        return Object.assign({}, state, {
          bookings: [booking].concat((0, _toConsumableArray2.default)(state.bookings)),
          totalItems: state.totalItems + 1
        });
      }, false, 'booking/addBooking');
    },
    updateBooking: function updateBooking(id, updates) {
      return set(function (state) {
        var _state$currentBooking;
        return Object.assign({}, state, {
          bookings: state.bookings.map(function (booking) {
            return booking.id === id ? Object.assign({}, booking, updates, {
              updatedAt: new Date().toISOString()
            }) : booking;
          }),
          currentBooking: ((_state$currentBooking = state.currentBooking) == null ? void 0 : _state$currentBooking.id) === id ? Object.assign({}, state.currentBooking, updates, {
            updatedAt: new Date().toISOString()
          }) : state.currentBooking
        });
      }, false, 'booking/updateBooking');
    },
    removeBooking: function removeBooking(id) {
      return set(function (state) {
        var _state$currentBooking2;
        return Object.assign({}, state, {
          bookings: state.bookings.filter(function (booking) {
            return booking.id !== id;
          }),
          totalItems: state.totalItems - 1,
          currentBooking: ((_state$currentBooking2 = state.currentBooking) == null ? void 0 : _state$currentBooking2.id) === id ? null : state.currentBooking
        });
      }, false, 'booking/removeBooking');
    },
    setCurrentBooking: function setCurrentBooking(booking) {
      return set(function (state) {
        return Object.assign({}, state, {
          currentBooking: booking
        });
      }, false, 'booking/setCurrentBooking');
    },
    fetchBookingsStart: function fetchBookingsStart() {
      return set(function (state) {
        return Object.assign({}, state, {
          status: 'loading',
          error: null
        });
      }, false, 'booking/fetchBookingsStart');
    },
    fetchBookingsSuccess: function fetchBookingsSuccess(bookings, total) {
      return set(function (state) {
        return Object.assign({}, state, {
          bookings: bookings,
          totalItems: total,
          status: 'success',
          error: null
        });
      }, false, 'booking/fetchBookingsSuccess');
    },
    fetchBookingsFailure: function fetchBookingsFailure(error) {
      return set(function (state) {
        return Object.assign({}, state, {
          status: 'error',
          error: error
        });
      }, false, 'booking/fetchBookingsFailure');
    },
    createBookingStart: function createBookingStart() {
      return set(function (state) {
        return Object.assign({}, state, {
          status: 'loading',
          error: null
        });
      }, false, 'booking/createBookingStart');
    },
    createBookingSuccess: function createBookingSuccess(booking) {
      return set(function (state) {
        return Object.assign({}, state, {
          bookings: [booking].concat((0, _toConsumableArray2.default)(state.bookings)),
          totalItems: state.totalItems + 1,
          status: 'success',
          error: null
        });
      }, false, 'booking/createBookingSuccess');
    },
    createBookingFailure: function createBookingFailure(error) {
      return set(function (state) {
        return Object.assign({}, state, {
          status: 'error',
          error: error
        });
      }, false, 'booking/createBookingFailure');
    },
    updateBookingStart: function updateBookingStart() {
      return set(function (state) {
        return Object.assign({}, state, {
          status: 'loading',
          error: null
        });
      }, false, 'booking/updateBookingStart');
    },
    updateBookingSuccess: function updateBookingSuccess(booking) {
      return set(function (state) {
        var _state$currentBooking3;
        return Object.assign({}, state, {
          bookings: state.bookings.map(function (b) {
            return b.id === booking.id ? booking : b;
          }),
          currentBooking: ((_state$currentBooking3 = state.currentBooking) == null ? void 0 : _state$currentBooking3.id) === booking.id ? booking : state.currentBooking,
          status: 'success',
          error: null
        });
      }, false, 'booking/updateBookingSuccess');
    },
    updateBookingFailure: function updateBookingFailure(error) {
      return set(function (state) {
        return Object.assign({}, state, {
          status: 'error',
          error: error
        });
      }, false, 'booking/updateBookingFailure');
    },
    setFilter: function setFilter(filter) {
      return set(function (state) {
        return Object.assign({}, state, {
          filter: filter,
          currentPage: 1
        });
      }, false, 'booking/setFilter');
    },
    setSorting: function setSorting(sortBy, order) {
      return set(function (state) {
        return Object.assign({}, state, {
          sortBy: sortBy,
          sortOrder: order
        });
      }, false, 'booking/setSorting');
    },
    setSearchQuery: function setSearchQuery(query) {
      return set(function (state) {
        return Object.assign({}, state, {
          searchQuery: query,
          currentPage: 1
        });
      }, false, 'booking/setSearchQuery');
    },
    setPage: function setPage(page) {
      return set(function (state) {
        return Object.assign({}, state, {
          currentPage: page
        });
      }, false, 'booking/setPage');
    },
    setItemsPerPage: function setItemsPerPage(items) {
      return set(function (state) {
        return Object.assign({}, state, {
          itemsPerPage: items,
          currentPage: 1
        });
      }, false, 'booking/setItemsPerPage');
    },
    getFilteredBookings: function getFilteredBookings() {
      var _get = get(),
        bookings = _get.bookings,
        filter = _get.filter,
        sortBy = _get.sortBy,
        sortOrder = _get.sortOrder,
        searchQuery = _get.searchQuery;
      var filtered = bookings;
      if (filter !== 'all') {
        switch (filter) {
          case 'upcoming':
            filtered = bookings.filter(function (booking) {
              return ['pending', 'confirmed'].includes(booking.status) && new Date(booking.scheduledDate) >= new Date();
            });
            break;
          case 'completed':
            filtered = bookings.filter(function (booking) {
              return booking.status === 'completed';
            });
            break;
          case 'cancelled':
            filtered = bookings.filter(function (booking) {
              return booking.status === 'cancelled';
            });
            break;
        }
      }
      if (searchQuery) {
        var query = searchQuery.toLowerCase();
        filtered = filtered.filter(function (booking) {
          return booking.providerName.toLowerCase().includes(query) || booking.services.some(function (service) {
            return service.name.toLowerCase().includes(query) || service.category.toLowerCase().includes(query);
          }) || booking.address.city.toLowerCase().includes(query);
        });
      }
      filtered.sort(function (a, b) {
        var comparison = 0;
        switch (sortBy) {
          case 'date':
            comparison = new Date(a.scheduledDate).getTime() - new Date(b.scheduledDate).getTime();
            break;
          case 'price':
            comparison = a.totalPrice - b.totalPrice;
            break;
          case 'status':
            comparison = a.status.localeCompare(b.status);
            break;
          case 'provider':
            comparison = a.providerName.localeCompare(b.providerName);
            break;
        }
        return sortOrder === 'asc' ? comparison : -comparison;
      });
      return filtered;
    },
    getUpcomingBookings: function getUpcomingBookings() {
      var _get2 = get(),
        bookings = _get2.bookings;
      return bookings.filter(function (booking) {
        return ['pending', 'confirmed'].includes(booking.status) && new Date(booking.scheduledDate) >= new Date();
      });
    },
    getCompletedBookings: function getCompletedBookings() {
      var _get3 = get(),
        bookings = _get3.bookings;
      return bookings.filter(function (booking) {
        return booking.status === 'completed';
      });
    },
    getTotalSpent: function getTotalSpent() {
      var _get4 = get(),
        bookings = _get4.bookings;
      return bookings.filter(function (booking) {
        return booking.status === 'completed';
      }).reduce(function (total, booking) {
        return total + booking.totalPrice;
      }, 0);
    },
    getBookingById: function getBookingById(id) {
      var _get5 = get(),
        bookings = _get5.bookings;
      return bookings.find(function (booking) {
        return booking.id === id;
      });
    },
    reset: function reset() {
      return set(function () {
        return Object.assign({}, initialState);
      }, false, 'booking/reset');
    },
    fetchBookings: function () {
      var _fetchBookings = (0, _asyncToGenerator2.default)(function* (filters) {
        try {
          set(function (state) {
            return Object.assign({}, state, {
              status: 'loading',
              error: null
            });
          }, false, 'booking/fetchBookings/start');
          var response = yield _bookingService.bookingService.getBookings(filters);
          var transformedBookings = response.results.map(function (apiBooking) {
            var _apiBooking$scheduled;
            return {
              id: apiBooking.id,
              customerId: apiBooking.customer_id,
              providerId: apiBooking.provider_id,
              providerName: apiBooking.provider_name,
              services: [{
                id: apiBooking.service_id,
                name: apiBooking.service_name,
                description: '',
                duration: apiBooking.duration_minutes,
                price: apiBooking.base_price,
                category: apiBooking.service_category
              }],
              scheduledDate: apiBooking.scheduled_datetime.split('T')[0],
              scheduledTime: ((_apiBooking$scheduled = apiBooking.scheduled_datetime.split('T')[1]) == null ? void 0 : _apiBooking$scheduled.substring(0, 5)) || '',
              duration: apiBooking.duration_minutes,
              totalPrice: apiBooking.total_amount,
              status: apiBooking.status,
              paymentStatus: apiBooking.payment_status,
              notes: apiBooking.notes,
              address: {
                street: '',
                city: '',
                state: '',
                zipCode: ''
              },
              createdAt: apiBooking.created_at,
              updatedAt: apiBooking.updated_at
            };
          });
          set(function (state) {
            return Object.assign({}, state, {
              bookings: transformedBookings,
              totalItems: response.count,
              status: 'success',
              error: null
            });
          }, false, 'booking/fetchBookings/success');
        } catch (error) {
          set(function (state) {
            return Object.assign({}, state, {
              status: 'error',
              error: error.message || 'Failed to fetch bookings'
            });
          }, false, 'booking/fetchBookings/error');
        }
      });
      function fetchBookings(_x) {
        return _fetchBookings.apply(this, arguments);
      }
      return fetchBookings;
    }(),
    createBooking: function () {
      var _createBooking = (0, _asyncToGenerator2.default)(function* (bookingData) {
        try {
          var _apiBooking$scheduled2;
          set(function (state) {
            return Object.assign({}, state, {
              status: 'loading',
              error: null
            });
          }, false, 'booking/createBooking/start');
          var apiBooking = yield _bookingService.bookingService.createBooking(bookingData);
          var transformedBooking = {
            id: apiBooking.id,
            customerId: apiBooking.customer_id,
            providerId: apiBooking.provider_id,
            providerName: apiBooking.provider_name,
            services: [{
              id: apiBooking.service_id,
              name: apiBooking.service_name,
              description: '',
              duration: apiBooking.duration_minutes,
              price: apiBooking.base_price,
              category: apiBooking.service_category
            }],
            scheduledDate: apiBooking.scheduled_datetime.split('T')[0],
            scheduledTime: ((_apiBooking$scheduled2 = apiBooking.scheduled_datetime.split('T')[1]) == null ? void 0 : _apiBooking$scheduled2.substring(0, 5)) || '',
            duration: apiBooking.duration_minutes,
            totalPrice: apiBooking.total_amount,
            status: apiBooking.status,
            paymentStatus: apiBooking.payment_status,
            notes: apiBooking.notes,
            address: {
              street: '',
              city: '',
              state: '',
              zipCode: ''
            },
            createdAt: apiBooking.created_at,
            updatedAt: apiBooking.updated_at
          };
          set(function (state) {
            return Object.assign({}, state, {
              bookings: [transformedBooking].concat((0, _toConsumableArray2.default)(state.bookings)),
              totalItems: state.totalItems + 1,
              status: 'success',
              error: null
            });
          }, false, 'booking/createBooking/success');
          return transformedBooking;
        } catch (error) {
          set(function (state) {
            return Object.assign({}, state, {
              status: 'error',
              error: error.message || 'Failed to create booking'
            });
          }, false, 'booking/createBooking/error');
          throw error;
        }
      });
      function createBooking(_x2) {
        return _createBooking.apply(this, arguments);
      }
      return createBooking;
    }(),
    cancelBooking: function () {
      var _cancelBooking = (0, _asyncToGenerator2.default)(function* (bookingId, reason) {
        try {
          set(function (state) {
            return Object.assign({}, state, {
              status: 'loading',
              error: null
            });
          }, false, 'booking/cancelBooking/start');
          yield _bookingService.bookingService.cancelBooking(bookingId, reason);
          set(function (state) {
            return Object.assign({}, state, {
              bookings: state.bookings.map(function (booking) {
                return booking.id === bookingId ? Object.assign({}, booking, {
                  status: 'cancelled',
                  cancellationReason: reason
                }) : booking;
              }),
              status: 'success',
              error: null
            });
          }, false, 'booking/cancelBooking/success');
        } catch (error) {
          set(function (state) {
            return Object.assign({}, state, {
              status: 'error',
              error: error.message || 'Failed to cancel booking'
            });
          }, false, 'booking/cancelBooking/error');
          throw error;
        }
      });
      function cancelBooking(_x3, _x4) {
        return _cancelBooking.apply(this, arguments);
      }
      return cancelBooking;
    }()
  });
}, {
  name: 'booking-store',
  partialize: function partialize(state) {
    return {
      bookings: state.bookings,
      filter: state.filter,
      sortBy: state.sortBy,
      sortOrder: state.sortOrder
    };
  }
}), {
  name: 'booking-store'
}));
var bookingSelectors = exports.bookingSelectors = {
  bookings: function bookings() {
    return useBookingStore(function (state) {
      return state.bookings;
    });
  },
  filteredBookings: function filteredBookings() {
    return useBookingStore(function (state) {
      return state.getFilteredBookings();
    });
  },
  upcomingBookings: function upcomingBookings() {
    return useBookingStore(function (state) {
      return state.getUpcomingBookings();
    });
  },
  completedBookings: function completedBookings() {
    return useBookingStore(function (state) {
      return state.getCompletedBookings();
    });
  },
  currentBooking: function currentBooking() {
    return useBookingStore(function (state) {
      return state.currentBooking;
    });
  },
  status: function status() {
    return useBookingStore(function (state) {
      return state.status;
    });
  },
  error: function error() {
    return useBookingStore(function (state) {
      return state.error;
    });
  },
  totalSpent: function totalSpent() {
    return useBookingStore(function (state) {
      return state.getTotalSpent();
    });
  },
  filter: function filter() {
    return useBookingStore(function (state) {
      return state.filter;
    });
  },
  searchQuery: function searchQuery() {
    return useBookingStore(function (state) {
      return state.searchQuery;
    });
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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