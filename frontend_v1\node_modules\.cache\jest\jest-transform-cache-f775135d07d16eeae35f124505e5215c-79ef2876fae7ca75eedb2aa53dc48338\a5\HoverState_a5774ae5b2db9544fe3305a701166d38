e64e3e2d7b203e1f58742be426cd94ca
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.isHoverEnabled = isHoverEnabled;
var _Platform = _interopRequireDefault(require("../Utilities/Platform"));
var isEnabled = false;
if (_Platform.default.OS === 'web') {
  var canUseDOM = Boolean(typeof window !== 'undefined' && window.document && window.document.createElement);
  if (canUseDOM) {
    var HOVER_THRESHOLD_MS = 1000;
    var lastTouchTimestamp = 0;
    var enableHover = function enableHover() {
      if (isEnabled || Date.now() - lastTouchTimestamp < HOVER_THRESHOLD_MS) {
        return;
      }
      isEnabled = true;
    };
    var disableHover = function disableHover() {
      lastTouchTimestamp = Date.now();
      if (isEnabled) {
        isEnabled = false;
      }
    };
    document.addEventListener('touchstart', disableHover, true);
    document.addEventListener('touchmove', disableHover, true);
    document.addEventListener('mousemove', enableHover, true);
  }
}
function isHoverEnabled() {
  return isEnabled;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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