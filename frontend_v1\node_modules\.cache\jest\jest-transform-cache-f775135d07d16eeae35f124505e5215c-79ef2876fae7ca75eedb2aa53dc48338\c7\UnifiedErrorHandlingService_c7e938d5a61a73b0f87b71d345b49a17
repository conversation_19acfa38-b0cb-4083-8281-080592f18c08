8535d45839dffed91b3d19369db938ba
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.unifiedErrorHandlingService = exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _asyncStorage = _interopRequireDefault(require("@react-native-async-storage/async-storage"));
var _netinfo = _interopRequireDefault(require("@react-native-community/netinfo"));
var _types = require("./types");
var _UserFeedbackService = require("./UserFeedbackService");
var _AnalyticsIntegrationService = require("./AnalyticsIntegrationService");
var _ErrorMonitoringService = require("./ErrorMonitoringService");
var UnifiedErrorHandlingService = function () {
  function UnifiedErrorHandlingService() {
    (0, _classCallCheck2.default)(this, UnifiedErrorHandlingService);
    this.errorQueue = [];
    this.errorListeners = [];
    this.recoveryStrategies = new Map();
    this.breadcrumbs = [];
    this.isInitialized = false;
    this.config = this.getDefaultConfig();
    this.metrics = this.initializeMetrics();
    this.setupRecoveryStrategies();
  }
  return (0, _createClass2.default)(UnifiedErrorHandlingService, [{
    key: "initialize",
    value: (function () {
      var _initialize = (0, _asyncToGenerator2.default)(function* () {
        if (this.isInitialized) return;
        try {
          yield _UserFeedbackService.userFeedbackService.initialize == null ? void 0 : _UserFeedbackService.userFeedbackService.initialize();
          yield _AnalyticsIntegrationService.analyticsIntegrationService.initialize();
          yield _ErrorMonitoringService.errorMonitoringService.initialize();
          yield this.loadOfflineErrors();
          this.setupNetworkMonitoring();
          this.setupErrorQueueFlush();
          this.isInitialized = true;
          console.log('✅ UnifiedErrorHandlingService initialized');
        } catch (error) {
          console.error('❌ Failed to initialize UnifiedErrorHandlingService:', error);
        }
      });
      function initialize() {
        return _initialize.apply(this, arguments);
      }
      return initialize;
    }())
  }, {
    key: "handleError",
    value: (function () {
      var _handleError = (0, _asyncToGenerator2.default)(function* (error) {
        var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
        var userMessage = arguments.length > 2 ? arguments[2] : undefined;
        var unifiedError = this.normalizeError(error, context);
        var errorReport = yield this.createErrorReport(unifiedError, userMessage);
        this.addBreadcrumb({
          timestamp: Date.now(),
          category: 'system_event',
          message: `Error handled: ${unifiedError.type}`,
          level: 'error',
          data: {
            errorId: unifiedError.id,
            type: unifiedError.type
          }
        });
        yield this.processError(errorReport);
        return errorReport;
      });
      function handleError(_x) {
        return _handleError.apply(this, arguments);
      }
      return handleError;
    }())
  }, {
    key: "handleNetworkError",
    value: (function () {
      var _handleNetworkError = (0, _asyncToGenerator2.default)(function* (error) {
        var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
        var networkContext = Object.assign({}, context, {
          type: _types.ErrorType.NETWORK,
          severity: _types.ErrorSeverity.MEDIUM
        });
        return this.handleError(error, networkContext);
      });
      function handleNetworkError(_x2) {
        return _handleNetworkError.apply(this, arguments);
      }
      return handleNetworkError;
    }())
  }, {
    key: "handleAuthError",
    value: function () {
      var _handleAuthError = (0, _asyncToGenerator2.default)(function* (error) {
        var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
        var authContext = Object.assign({}, context, {
          type: _types.ErrorType.AUTHENTICATION,
          severity: _types.ErrorSeverity.HIGH
        });
        return this.handleError(error, authContext);
      });
      function handleAuthError(_x3) {
        return _handleAuthError.apply(this, arguments);
      }
      return handleAuthError;
    }()
  }, {
    key: "handleValidationError",
    value: function () {
      var _handleValidationError = (0, _asyncToGenerator2.default)(function* (error) {
        var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
        var validationContext = Object.assign({}, context, {
          type: _types.ErrorType.VALIDATION,
          severity: _types.ErrorSeverity.LOW
        });
        return this.handleError(error, validationContext);
      });
      function handleValidationError(_x4) {
        return _handleValidationError.apply(this, arguments);
      }
      return handleValidationError;
    }()
  }, {
    key: "handleWebSocketError",
    value: function () {
      var _handleWebSocketError = (0, _asyncToGenerator2.default)(function* (error) {
        var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
        var wsContext = Object.assign({}, context, {
          type: _types.ErrorType.WEBSOCKET,
          severity: _types.ErrorSeverity.MEDIUM
        });
        return this.handleError(error, wsContext);
      });
      function handleWebSocketError(_x5) {
        return _handleWebSocketError.apply(this, arguments);
      }
      return handleWebSocketError;
    }()
  }, {
    key: "addBreadcrumb",
    value: function addBreadcrumb(breadcrumb) {
      this.breadcrumbs.push(breadcrumb);
      if (this.breadcrumbs.length > 50) {
        this.breadcrumbs = this.breadcrumbs.slice(-50);
      }
    }
  }, {
    key: "addErrorListener",
    value: function addErrorListener(listener) {
      var _this = this;
      this.errorListeners.push(listener);
      return function () {
        var index = _this.errorListeners.indexOf(listener);
        if (index > -1) {
          _this.errorListeners.splice(index, 1);
        }
      };
    }
  }, {
    key: "addRecoveryStrategy",
    value: function addRecoveryStrategy(strategy) {
      this.recoveryStrategies.set(strategy.id, strategy);
    }
  }, {
    key: "getMetrics",
    value: function getMetrics() {
      return Object.assign({}, this.metrics);
    }
  }, {
    key: "clearErrorQueue",
    value: function clearErrorQueue() {
      this.errorQueue = [];
      _asyncStorage.default.removeItem('unified_error_queue');
    }
  }, {
    key: "updateConfig",
    value: function updateConfig(newConfig) {
      this.config = Object.assign({}, this.config, newConfig);
    }
  }, {
    key: "normalizeError",
    value: function normalizeError(error, context) {
      if (error instanceof _types.UnifiedError) {
        return error;
      }
      if (typeof error === 'string') {
        return new _types.UnifiedError(error, context.type || _types.ErrorType.UNKNOWN, context.severity || _types.ErrorSeverity.MEDIUM, context);
      }
      var errorType = this.determineErrorType(error, context);
      var severity = this.determineSeverity(error, context);
      return new _types.UnifiedError(error.message, errorType, severity, context, error);
    }
  }, {
    key: "determineErrorType",
    value: function determineErrorType(error, context) {
      if (context.type) return context.type;
      var message = error.message.toLowerCase();
      if (message.includes('network') || message.includes('fetch')) {
        return _types.ErrorType.NETWORK;
      }
      if (message.includes('unauthorized') || message.includes('401')) {
        return _types.ErrorType.AUTHENTICATION;
      }
      if (message.includes('forbidden') || message.includes('403')) {
        return _types.ErrorType.AUTHORIZATION;
      }
      if (message.includes('not found') || message.includes('404')) {
        return _types.ErrorType.NOT_FOUND;
      }
      if (message.includes('timeout')) {
        return _types.ErrorType.TIMEOUT;
      }
      if (message.includes('websocket') || message.includes('ws')) {
        return _types.ErrorType.WEBSOCKET;
      }
      if (message.includes('theme') || message.includes('colors')) {
        return _types.ErrorType.THEME;
      }
      if (message.includes('lazy') || message.includes('loading')) {
        return _types.ErrorType.LAZY_LOADING;
      }
      return _types.ErrorType.UNKNOWN;
    }
  }, {
    key: "determineSeverity",
    value: function determineSeverity(error, context) {
      if (context.severity) return context.severity;
      var errorType = this.determineErrorType(error, context);
      switch (errorType) {
        case _types.ErrorType.AUTHENTICATION:
        case _types.ErrorType.AUTHORIZATION:
        case _types.ErrorType.SERVER_ERROR:
          return _types.ErrorSeverity.HIGH;
        case _types.ErrorType.SYSTEM:
        case _types.ErrorType.THEME:
          return _types.ErrorSeverity.CRITICAL;
        case _types.ErrorType.VALIDATION:
        case _types.ErrorType.NOT_FOUND:
          return _types.ErrorSeverity.LOW;
        default:
          return _types.ErrorSeverity.MEDIUM;
      }
    }
  }, {
    key: "createErrorReport",
    value: function () {
      var _createErrorReport = (0, _asyncToGenerator2.default)(function* (error, userMessage) {
        var report = {
          id: error.id,
          type: error.type,
          category: error.category,
          severity: error.severity,
          message: error.message,
          technicalMessage: error.technicalMessage,
          userMessage: userMessage || error.userMessage,
          stack: error.stack,
          context: Object.assign({}, error.context, {
            breadcrumbs: (0, _toConsumableArray2.default)(this.breadcrumbs)
          }),
          timestamp: error.timestamp,
          recoveryStrategy: undefined,
          recoveryAttempts: 0,
          recovered: false,
          reported: false,
          userFeedback: this.createUserFeedbackConfig(error),
          analyticsData: this.createAnalyticsData(error)
        };
        return report;
      });
      function createErrorReport(_x6, _x7) {
        return _createErrorReport.apply(this, arguments);
      }
      return createErrorReport;
    }()
  }, {
    key: "createUserFeedbackConfig",
    value: function createUserFeedbackConfig(error) {
      var shouldShow = error.severity !== _types.ErrorSeverity.LOW && this.config.enableUserFeedback;
      return {
        showToUser: shouldShow,
        title: this.getUserFeedbackTitle(error.type),
        message: error.userMessage,
        variant: this.config.defaultFeedbackVariant,
        dismissible: error.severity !== _types.ErrorSeverity.CRITICAL,
        autoHide: error.severity === _types.ErrorSeverity.LOW,
        hideDelay: 5000
      };
    }
  }, {
    key: "getUserFeedbackTitle",
    value: function getUserFeedbackTitle(type) {
      var titles = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, _types.ErrorType.NETWORK, 'Connection Problem'), _types.ErrorType.AUTHENTICATION, 'Sign In Required'), _types.ErrorType.AUTHORIZATION, 'Access Denied'), _types.ErrorType.VALIDATION, 'Input Error'), _types.ErrorType.NOT_FOUND, 'Not Found'), _types.ErrorType.SERVER_ERROR, 'Server Error'), _types.ErrorType.TIMEOUT, 'Request Timeout'), _types.ErrorType.WEBSOCKET, 'Connection Lost'), _types.ErrorType.THEME, 'Display Issue'), _types.ErrorType.LAZY_LOADING, 'Loading Error');
      return titles[type] || 'Error';
    }
  }, {
    key: "createAnalyticsData",
    value: function createAnalyticsData(error) {
      var _error$context$device, _error$context$device2;
      return {
        errorType: error.type,
        errorCategory: error.category,
        severity: error.severity,
        component: error.context.component,
        screen: error.context.screen,
        action: error.context.action,
        timestamp: error.timestamp,
        platform: (_error$context$device = error.context.deviceInfo) == null ? void 0 : _error$context$device.platform,
        networkStatus: (_error$context$device2 = error.context.deviceInfo) == null ? void 0 : _error$context$device2.networkStatus
      };
    }
  }, {
    key: "getDefaultConfig",
    value: function getDefaultConfig() {
      return {
        enableLogging: true,
        enableReporting: true,
        enableUserFeedback: true,
        enableRecovery: true,
        enableAnalytics: true,
        userFeedbackThreshold: _types.ErrorSeverity.MEDIUM,
        defaultFeedbackVariant: 'toast',
        maxRecoveryAttempts: 3,
        defaultRetryDelay: 2000,
        enableProgressiveRetry: true,
        reportingThreshold: _types.ErrorSeverity.MEDIUM,
        enableOfflineStorage: true,
        maxOfflineErrors: 100,
        maxErrorQueueSize: 50,
        errorQueueFlushInterval: 30000,
        enableDebugMode: __DEV__,
        enableStackTrace: __DEV__
      };
    }
  }, {
    key: "initializeMetrics",
    value: function initializeMetrics() {
      return {
        totalErrors: 0,
        errorsByType: {},
        errorsBySeverity: {},
        errorsByCategory: {},
        recoverySuccessRate: 0,
        averageRecoveryTime: 0
      };
    }
  }, {
    key: "setupRecoveryStrategies",
    value: function setupRecoveryStrategies() {
      this.addRecoveryStrategy({
        id: 'network_retry',
        name: 'Network Retry',
        description: 'Retry network requests after connection is restored',
        canRecover: function canRecover(error) {
          return error.type === _types.ErrorType.NETWORK;
        },
        recover: function () {
          var _recover = (0, _asyncToGenerator2.default)(function* () {
            var netInfo = yield _netinfo.default.fetch();
            return netInfo.isConnected || false;
          });
          function recover() {
            return _recover.apply(this, arguments);
          }
          return recover;
        }(),
        maxAttempts: 3,
        retryDelay: 2000,
        progressiveDelay: true
      });
      this.addRecoveryStrategy({
        id: 'websocket_reconnect',
        name: 'WebSocket Reconnect',
        description: 'Reconnect WebSocket connections',
        canRecover: function canRecover(error) {
          return error.type === _types.ErrorType.WEBSOCKET;
        },
        recover: function () {
          var _recover2 = (0, _asyncToGenerator2.default)(function* () {
            return true;
          });
          function recover() {
            return _recover2.apply(this, arguments);
          }
          return recover;
        }(),
        maxAttempts: 5,
        retryDelay: 1000,
        progressiveDelay: true
      });
    }
  }, {
    key: "processError",
    value: function () {
      var _processError = (0, _asyncToGenerator2.default)(function* (errorReport) {
        var _errorReport$userFeed;
        this.errorQueue.push(errorReport);
        if (this.errorQueue.length > this.config.maxErrorQueueSize) {
          this.errorQueue.shift();
        }
        this.updateMetrics(errorReport);
        if (this.config.enableLogging) {
          this.logError(errorReport);
        }
        if (this.config.enableRecovery) {
          yield this.attemptRecovery(errorReport);
        }
        if (this.config.enableUserFeedback && (_errorReport$userFeed = errorReport.userFeedback) != null && _errorReport$userFeed.showToUser) {
          yield _UserFeedbackService.userFeedbackService.showFeedback(errorReport);
        }
        this.notifyListeners(errorReport);
        if (this.config.enableOfflineStorage) {
          yield this.storeOfflineError(errorReport);
        }
        if (this.config.enableReporting && errorReport.severity >= this.config.reportingThreshold) {
          yield _AnalyticsIntegrationService.analyticsIntegrationService.reportError(errorReport);
        }
        yield _ErrorMonitoringService.errorMonitoringService.recordError(errorReport);
      });
      function processError(_x8) {
        return _processError.apply(this, arguments);
      }
      return processError;
    }()
  }, {
    key: "updateMetrics",
    value: function updateMetrics(errorReport) {
      this.metrics.totalErrors++;
      this.metrics.errorsByType[errorReport.type] = (this.metrics.errorsByType[errorReport.type] || 0) + 1;
      this.metrics.errorsBySeverity[errorReport.severity] = (this.metrics.errorsBySeverity[errorReport.severity] || 0) + 1;
      this.metrics.errorsByCategory[errorReport.category] = (this.metrics.errorsByCategory[errorReport.category] || 0) + 1;
    }
  }, {
    key: "logError",
    value: function logError(errorReport) {
      var logLevel = this.getLogLevel(errorReport.severity);
      var logMessage = `[UnifiedErrorHandling] ${errorReport.type}: ${errorReport.message}`;
      console[logLevel](logMessage, {
        id: errorReport.id,
        type: errorReport.type,
        severity: errorReport.severity,
        context: errorReport.context,
        stack: this.config.enableStackTrace ? errorReport.stack : undefined
      });
    }
  }, {
    key: "getLogLevel",
    value: function getLogLevel(severity) {
      switch (severity) {
        case _types.ErrorSeverity.LOW:
          return 'log';
        case _types.ErrorSeverity.MEDIUM:
          return 'warn';
        case _types.ErrorSeverity.HIGH:
        case _types.ErrorSeverity.CRITICAL:
          return 'error';
        default:
          return 'warn';
      }
    }
  }, {
    key: "attemptRecovery",
    value: function () {
      var _attemptRecovery = (0, _asyncToGenerator2.default)(function* (errorReport) {
        var _this2 = this;
        var strategies = Array.from(this.recoveryStrategies.values()).filter(function (strategy) {
          return strategy.canRecover(errorReport, errorReport.context);
        });
        var _loop = function* _loop() {
            if (errorReport.recoveryAttempts >= strategy.maxAttempts) return 0;
            try {
              errorReport.recoveryAttempts++;
              var delay = strategy.progressiveDelay ? strategy.retryDelay * errorReport.recoveryAttempts : strategy.retryDelay || _this2.config.defaultRetryDelay;
              yield new Promise(function (resolve) {
                return setTimeout(resolve, delay);
              });
              var recovered = yield strategy.recover(errorReport, errorReport.context);
              if (recovered) {
                errorReport.recovered = true;
                errorReport.recoveryStrategy = strategy.name;
                console.log(`✅ Error recovered using strategy: ${strategy.name}`);
                return 1;
              }
            } catch (recoveryError) {
              console.warn(`❌ Recovery strategy failed: ${strategy.name}`, recoveryError);
            }
          },
          _ret;
        for (var strategy of strategies) {
          _ret = yield* _loop();
          if (_ret === 0) continue;
          if (_ret === 1) break;
        }
      });
      function attemptRecovery(_x9) {
        return _attemptRecovery.apply(this, arguments);
      }
      return attemptRecovery;
    }()
  }, {
    key: "notifyListeners",
    value: function notifyListeners(errorReport) {
      this.errorListeners.forEach(function (listener) {
        try {
          listener(errorReport);
        } catch (error) {
          console.warn('Error listener failed:', error);
        }
      });
    }
  }, {
    key: "storeOfflineError",
    value: function () {
      var _storeOfflineError = (0, _asyncToGenerator2.default)(function* (errorReport) {
        try {
          var stored = yield _asyncStorage.default.getItem('unified_error_queue');
          var errors = stored ? JSON.parse(stored) : [];
          errors.push(errorReport);
          if (errors.length > this.config.maxOfflineErrors) {
            errors.splice(0, errors.length - this.config.maxOfflineErrors);
          }
          yield _asyncStorage.default.setItem('unified_error_queue', JSON.stringify(errors));
        } catch (error) {
          console.warn('Failed to store offline error:', error);
        }
      });
      function storeOfflineError(_x0) {
        return _storeOfflineError.apply(this, arguments);
      }
      return storeOfflineError;
    }()
  }, {
    key: "loadOfflineErrors",
    value: function () {
      var _loadOfflineErrors = (0, _asyncToGenerator2.default)(function* () {
        try {
          var stored = yield _asyncStorage.default.getItem('unified_error_queue');
          if (stored) {
            var _this$errorQueue;
            var errors = JSON.parse(stored);
            (_this$errorQueue = this.errorQueue).push.apply(_this$errorQueue, (0, _toConsumableArray2.default)(errors));
            yield _asyncStorage.default.removeItem('unified_error_queue');
          }
        } catch (error) {
          console.warn('Failed to load offline errors:', error);
        }
      });
      function loadOfflineErrors() {
        return _loadOfflineErrors.apply(this, arguments);
      }
      return loadOfflineErrors;
    }()
  }, {
    key: "setupNetworkMonitoring",
    value: function setupNetworkMonitoring() {
      var _this3 = this;
      _netinfo.default.addEventListener(function (state) {
        if (_this3.config.enableLogging) {
          console.log('📶 Network status changed:', state.isConnected ? 'online' : 'offline');
        }
      });
    }
  }, {
    key: "setupErrorQueueFlush",
    value: function setupErrorQueueFlush() {
      var _this4 = this;
      setInterval(function () {
        if (_this4.errorQueue.length > 0 && _this4.config.enableReporting) {
          var unreported = _this4.errorQueue.filter(function (error) {
            return !error.reported;
          });
          unreported.forEach(function (error) {
            return _AnalyticsIntegrationService.analyticsIntegrationService.reportError(error);
          });
        }
      }, this.config.errorQueueFlushInterval);
    }
  }, {
    key: "getMonitoringMetrics",
    value: function getMonitoringMetrics() {
      return _ErrorMonitoringService.errorMonitoringService.getErrorMetrics();
    }
  }, {
    key: "getSystemHealth",
    value: function getSystemHealth() {
      return _ErrorMonitoringService.errorMonitoringService.getSystemHealth();
    }
  }, {
    key: "getErrorTrends",
    value: function getErrorTrends(days) {
      return _ErrorMonitoringService.errorMonitoringService.getErrorTrends(days);
    }
  }, {
    key: "exportErrorData",
    value: (function () {
      var _exportErrorData = (0, _asyncToGenerator2.default)(function* (format) {
        return _ErrorMonitoringService.errorMonitoringService.exportErrorData(format);
      });
      function exportErrorData(_x1) {
        return _exportErrorData.apply(this, arguments);
      }
      return exportErrorData;
    }())
  }, {
    key: "clearMonitoringHistory",
    value: (function () {
      var _clearMonitoringHistory = (0, _asyncToGenerator2.default)(function* () {
        return _ErrorMonitoringService.errorMonitoringService.clearErrorHistory();
      });
      function clearMonitoringHistory() {
        return _clearMonitoringHistory.apply(this, arguments);
      }
      return clearMonitoringHistory;
    }())
  }]);
}();
var _serviceInstance = null;
var unifiedErrorHandlingService = exports.unifiedErrorHandlingService = function () {
  if (!_serviceInstance) {
    _serviceInstance = new UnifiedErrorHandlingService();
  }
  return _serviceInstance;
}();
var _default = exports.default = unifiedErrorHandlingService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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