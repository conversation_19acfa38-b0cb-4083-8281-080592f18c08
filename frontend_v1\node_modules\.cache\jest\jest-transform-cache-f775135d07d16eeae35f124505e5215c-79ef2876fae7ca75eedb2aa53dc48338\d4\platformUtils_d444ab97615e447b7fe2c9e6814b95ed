adc9c8a3f92b8370da40131cb3123825
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.safePlatformSelect = exports.platformStyles = exports.platformBehaviors = exports.platform = exports.isWeb = exports.isIOS = exports.isAndroid = exports.getPlatformProps = exports.PlatformConstants = void 0;
var _reactNative = require("react-native");
var isIOS = exports.isIOS = _reactNative.Platform.OS === 'ios';
var isAndroid = exports.isAndroid = _reactNative.Platform.OS === 'android';
var isWeb = exports.isWeb = _reactNative.Platform.OS === 'web';
var safePlatformSelect = exports.safePlatformSelect = function safePlatformSelect(platforms) {
  if (isIOS && platforms.ios !== undefined) {
    return platforms.ios;
  }
  if (isAndroid && platforms.android !== undefined) {
    return platforms.android;
  }
  if (isWeb && platforms.web !== undefined) {
    return platforms.web;
  }
  if (platforms.default !== undefined) {
    return platforms.default;
  }
  if (platforms.ios !== undefined) {
    return platforms.ios;
  }
  if (platforms.android !== undefined) {
    return platforms.android;
  }
  throw new Error('No platform-specific value provided and no default value specified');
};
var platformStyles = exports.platformStyles = {
  shadow: function shadow() {
    var elevation = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 4;
    return safePlatformSelect({
      ios: {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: elevation / 2
        },
        shadowOpacity: 0.1,
        shadowRadius: elevation
      },
      android: {
        elevation: elevation
      },
      default: {}
    });
  },
  borderRadius: function borderRadius() {
    var size = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'medium';
    var radiusMap = {
      small: safePlatformSelect({
        ios: 8,
        android: 4,
        default: 6
      }),
      medium: safePlatformSelect({
        ios: 12,
        android: 8,
        default: 10
      }),
      large: safePlatformSelect({
        ios: 16,
        android: 12,
        default: 14
      })
    };
    return radiusMap[size];
  },
  touchTarget: function touchTarget() {
    return safePlatformSelect({
      ios: 44,
      android: 48,
      default: 44
    });
  },
  statusBarHeight: function statusBarHeight() {
    return safePlatformSelect({
      ios: 20,
      android: 24,
      default: 20
    });
  },
  navigationBarHeight: function navigationBarHeight() {
    return safePlatformSelect({
      ios: 44,
      android: 56,
      default: 44
    });
  },
  tabBarHeight: function tabBarHeight() {
    return safePlatformSelect({
      ios: 49,
      android: 56,
      default: 49
    });
  }
};
var PlatformConstants = exports.PlatformConstants = {
  isIOSPlatform: isIOS,
  isAndroidPlatform: isAndroid,
  isWebPlatform: isWeb,
  minTouchTarget: platformStyles.touchTarget(),
  defaultSpacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32
  },
  animationDuration: {
    fast: 200,
    normal: 300,
    slow: 500
  },
  zIndex: {
    modal: 1000,
    overlay: 900,
    dropdown: 800,
    header: 700,
    fab: 600
  }
};
var getPlatformProps = exports.getPlatformProps = function getPlatformProps(component) {
  switch (component) {
    case 'StatusBar':
      return safePlatformSelect({
        ios: {
          barStyle: 'dark-content',
          backgroundColor: 'transparent'
        },
        android: {
          barStyle: 'dark-content',
          backgroundColor: '#FFFFFF',
          translucent: false
        },
        default: {
          barStyle: 'dark-content'
        }
      });
    case 'ScrollView':
      return safePlatformSelect({
        ios: {
          bounces: true,
          showsVerticalScrollIndicator: false,
          showsHorizontalScrollIndicator: false
        },
        android: {
          bounces: false,
          showsVerticalScrollIndicator: false,
          showsHorizontalScrollIndicator: false,
          overScrollMode: 'never'
        },
        default: {
          bounces: true,
          showsVerticalScrollIndicator: false,
          showsHorizontalScrollIndicator: false
        }
      });
    case 'TextInput':
      return safePlatformSelect({
        ios: {
          clearButtonMode: 'while-editing',
          enablesReturnKeyAutomatically: true
        },
        android: {
          underlineColorAndroid: 'transparent',
          textAlignVertical: 'center'
        },
        default: {}
      });
    default:
      return {};
  }
};
var platformBehaviors = exports.platformBehaviors = {
  supportsHaptics: function supportsHaptics() {
    return isIOS;
  },
  keyboardBehavior: function keyboardBehavior() {
    return safePlatformSelect({
      ios: 'padding',
      android: 'height',
      default: 'padding'
    });
  },
  modalPresentationStyle: function modalPresentationStyle() {
    return safePlatformSelect({
      ios: 'pageSheet',
      android: 'none',
      default: 'none'
    });
  },
  gestureEnabled: function gestureEnabled() {
    return safePlatformSelect({
      ios: true,
      android: false,
      default: true
    });
  }
};
var platform = exports.platform = {
  select: safePlatformSelect,
  styles: platformStyles,
  constants: PlatformConstants,
  props: getPlatformProps,
  behaviors: platformBehaviors,
  is: {
    ios: isIOS,
    android: isAndroid,
    web: isWeb
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfcmVhY3ROYXRpdmUiLCJyZXF1aXJlIiwiaXNJT1MiLCJleHBvcnRzIiwiUGxhdGZvcm0iLCJPUyIsImlzQW5kcm9pZCIsImlzV2ViIiwic2FmZVBsYXRmb3JtU2VsZWN0IiwicGxhdGZvcm1zIiwiaW9zIiwidW5kZWZpbmVkIiwiYW5kcm9pZCIsIndlYiIsImRlZmF1bHQiLCJFcnJvciIsInBsYXRmb3JtU3R5bGVzIiwic2hhZG93IiwiZWxldmF0aW9uIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwic2hhZG93Q29sb3IiLCJzaGFkb3dPZmZzZXQiLCJ3aWR0aCIsImhlaWdodCIsInNoYWRvd09wYWNpdHkiLCJzaGFkb3dSYWRpdXMiLCJib3JkZXJSYWRpdXMiLCJzaXplIiwicmFkaXVzTWFwIiwic21hbGwiLCJtZWRpdW0iLCJsYXJnZSIsInRvdWNoVGFyZ2V0Iiwic3RhdHVzQmFySGVpZ2h0IiwibmF2aWdhdGlvbkJhckhlaWdodCIsInRhYkJhckhlaWdodCIsIlBsYXRmb3JtQ29uc3RhbnRzIiwiaXNJT1NQbGF0Zm9ybSIsImlzQW5kcm9pZFBsYXRmb3JtIiwiaXNXZWJQbGF0Zm9ybSIsIm1pblRvdWNoVGFyZ2V0IiwiZGVmYXVsdFNwYWNpbmciLCJ4cyIsInNtIiwibWQiLCJsZyIsInhsIiwiYW5pbWF0aW9uRHVyYXRpb24iLCJmYXN0Iiwibm9ybWFsIiwic2xvdyIsInpJbmRleCIsIm1vZGFsIiwib3ZlcmxheSIsImRyb3Bkb3duIiwiaGVhZGVyIiwiZmFiIiwiZ2V0UGxhdGZvcm1Qcm9wcyIsImNvbXBvbmVudCIsImJhclN0eWxlIiwiYmFja2dyb3VuZENvbG9yIiwidHJhbnNsdWNlbnQiLCJib3VuY2VzIiwic2hvd3NWZXJ0aWNhbFNjcm9sbEluZGljYXRvciIsInNob3dzSG9yaXpvbnRhbFNjcm9sbEluZGljYXRvciIsIm92ZXJTY3JvbGxNb2RlIiwiY2xlYXJCdXR0b25Nb2RlIiwiZW5hYmxlc1JldHVybktleUF1dG9tYXRpY2FsbHkiLCJ1bmRlcmxpbmVDb2xvckFuZHJvaWQiLCJ0ZXh0QWxpZ25WZXJ0aWNhbCIsInBsYXRmb3JtQmVoYXZpb3JzIiwic3VwcG9ydHNIYXB0aWNzIiwia2V5Ym9hcmRCZWhhdmlvciIsIm1vZGFsUHJlc2VudGF0aW9uU3R5bGUiLCJnZXN0dXJlRW5hYmxlZCIsInBsYXRmb3JtIiwic2VsZWN0Iiwic3R5bGVzIiwiY29uc3RhbnRzIiwicHJvcHMiLCJiZWhhdmlvcnMiLCJpcyJdLCJzb3VyY2VzIjpbInBsYXRmb3JtVXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBQbGF0Zm9ybSBVdGlsaXRpZXMgLSBDcm9zcy1QbGF0Zm9ybSBDb21wYXRpYmlsaXR5XG4gKlxuICogUHJvdmlkZXMgdXRpbGl0aWVzIGZvciBoYW5kbGluZyBwbGF0Zm9ybS1zcGVjaWZpYyBkaWZmZXJlbmNlc1xuICogYmV0d2VlbiBpT1MgYW5kIEFuZHJvaWQgaW4gYSBzYWZlIGFuZCBjb25zaXN0ZW50IG1hbm5lci5cbiAqXG4gKiBAdmVyc2lvbiAxLjAuMFxuICogQGF1dGhvciBWaWVybGEgRGV2ZWxvcG1lbnQgVGVhbVxuICovXG5cbmltcG9ydCB7IFBsYXRmb3JtIH0gZnJvbSAncmVhY3QtbmF0aXZlJztcblxuLy8gUGxhdGZvcm0gZGV0ZWN0aW9uXG5leHBvcnQgY29uc3QgaXNJT1MgPSBQbGF0Zm9ybS5PUyA9PT0gJ2lvcyc7XG5leHBvcnQgY29uc3QgaXNBbmRyb2lkID0gUGxhdGZvcm0uT1MgPT09ICdhbmRyb2lkJztcbmV4cG9ydCBjb25zdCBpc1dlYiA9IFBsYXRmb3JtLk9TID09PSAnd2ViJztcblxuLy8gU2FmZSBwbGF0Zm9ybSBzZWxlY3Rpb24gdXRpbGl0eVxuZXhwb3J0IGNvbnN0IHNhZmVQbGF0Zm9ybVNlbGVjdCA9IDxUPihwbGF0Zm9ybXM6IHtcbiAgaW9zPzogVDtcbiAgYW5kcm9pZD86IFQ7XG4gIHdlYj86IFQ7XG4gIGRlZmF1bHQ/OiBUO1xufSk6IFQgPT4ge1xuICBpZiAoaXNJT1MgJiYgcGxhdGZvcm1zLmlvcyAhPT0gdW5kZWZpbmVkKSB7XG4gICAgcmV0dXJuIHBsYXRmb3Jtcy5pb3M7XG4gIH1cblxuICBpZiAoaXNBbmRyb2lkICYmIHBsYXRmb3Jtcy5hbmRyb2lkICE9PSB1bmRlZmluZWQpIHtcbiAgICByZXR1cm4gcGxhdGZvcm1zLmFuZHJvaWQ7XG4gIH1cblxuICBpZiAoaXNXZWIgJiYgcGxhdGZvcm1zLndlYiAhPT0gdW5kZWZpbmVkKSB7XG4gICAgcmV0dXJuIHBsYXRmb3Jtcy53ZWI7XG4gIH1cblxuICBpZiAocGxhdGZvcm1zLmRlZmF1bHQgIT09IHVuZGVmaW5lZCkge1xuICAgIHJldHVybiBwbGF0Zm9ybXMuZGVmYXVsdDtcbiAgfVxuXG4gIC8vIEZhbGxiYWNrIHRvIGlPUyB2YWx1ZSBpZiBhdmFpbGFibGUsIHRoZW4gQW5kcm9pZCwgdGhlbiB0aHJvdyBlcnJvclxuICBpZiAocGxhdGZvcm1zLmlvcyAhPT0gdW5kZWZpbmVkKSB7XG4gICAgcmV0dXJuIHBsYXRmb3Jtcy5pb3M7XG4gIH1cblxuICBpZiAocGxhdGZvcm1zLmFuZHJvaWQgIT09IHVuZGVmaW5lZCkge1xuICAgIHJldHVybiBwbGF0Zm9ybXMuYW5kcm9pZDtcbiAgfVxuXG4gIHRocm93IG5ldyBFcnJvcihcbiAgICAnTm8gcGxhdGZvcm0tc3BlY2lmaWMgdmFsdWUgcHJvdmlkZWQgYW5kIG5vIGRlZmF1bHQgdmFsdWUgc3BlY2lmaWVkJyxcbiAgKTtcbn07XG5cbi8vIFBsYXRmb3JtLXNwZWNpZmljIHN0eWxlIGhlbHBlcnNcbmV4cG9ydCBjb25zdCBwbGF0Zm9ybVN0eWxlcyA9IHtcbiAgLy8gU2hhZG93IHN0eWxlc1xuICBzaGFkb3c6IChlbGV2YXRpb246IG51bWJlciA9IDQpID0+XG4gICAgc2FmZVBsYXRmb3JtU2VsZWN0KHtcbiAgICAgIGlvczoge1xuICAgICAgICBzaGFkb3dDb2xvcjogJyMwMDAnLFxuICAgICAgICBzaGFkb3dPZmZzZXQ6IHsgd2lkdGg6IDAsIGhlaWdodDogZWxldmF0aW9uIC8gMiB9LFxuICAgICAgICBzaGFkb3dPcGFjaXR5OiAwLjEsXG4gICAgICAgIHNoYWRvd1JhZGl1czogZWxldmF0aW9uLFxuICAgICAgfSxcbiAgICAgIGFuZHJvaWQ6IHtcbiAgICAgICAgZWxldmF0aW9uLFxuICAgICAgfSxcbiAgICAgIGRlZmF1bHQ6IHt9LFxuICAgIH0pLFxuXG4gIC8vIEJvcmRlciByYWRpdXNcbiAgYm9yZGVyUmFkaXVzOiAoc2l6ZTogJ3NtYWxsJyB8ICdtZWRpdW0nIHwgJ2xhcmdlJyA9ICdtZWRpdW0nKSA9PiB7XG4gICAgY29uc3QgcmFkaXVzTWFwID0ge1xuICAgICAgc21hbGw6IHNhZmVQbGF0Zm9ybVNlbGVjdCh7IGlvczogOCwgYW5kcm9pZDogNCwgZGVmYXVsdDogNiB9KSxcbiAgICAgIG1lZGl1bTogc2FmZVBsYXRmb3JtU2VsZWN0KHsgaW9zOiAxMiwgYW5kcm9pZDogOCwgZGVmYXVsdDogMTAgfSksXG4gICAgICBsYXJnZTogc2FmZVBsYXRmb3JtU2VsZWN0KHsgaW9zOiAxNiwgYW5kcm9pZDogMTIsIGRlZmF1bHQ6IDE0IH0pLFxuICAgIH07XG4gICAgcmV0dXJuIHJhZGl1c01hcFtzaXplXTtcbiAgfSxcblxuICAvLyBUb3VjaCB0YXJnZXQgc2l6ZXNcbiAgdG91Y2hUYXJnZXQ6ICgpID0+XG4gICAgc2FmZVBsYXRmb3JtU2VsZWN0KHtcbiAgICAgIGlvczogNDQsXG4gICAgICBhbmRyb2lkOiA0OCxcbiAgICAgIGRlZmF1bHQ6IDQ0LFxuICAgIH0pLFxuXG4gIC8vIFN0YXR1cyBiYXIgaGVpZ2h0XG4gIHN0YXR1c0JhckhlaWdodDogKCkgPT5cbiAgICBzYWZlUGxhdGZvcm1TZWxlY3Qoe1xuICAgICAgaW9zOiAyMCwgLy8gV2lsbCBiZSBvdmVycmlkZGVuIGJ5IHNhZmUgYXJlYSBvbiBuZXdlciBkZXZpY2VzXG4gICAgICBhbmRyb2lkOiAyNCxcbiAgICAgIGRlZmF1bHQ6IDIwLFxuICAgIH0pLFxuXG4gIC8vIE5hdmlnYXRpb24gYmFyIGhlaWdodFxuICBuYXZpZ2F0aW9uQmFySGVpZ2h0OiAoKSA9PlxuICAgIHNhZmVQbGF0Zm9ybVNlbGVjdCh7XG4gICAgICBpb3M6IDQ0LFxuICAgICAgYW5kcm9pZDogNTYsXG4gICAgICBkZWZhdWx0OiA0NCxcbiAgICB9KSxcblxuICAvLyBUYWIgYmFyIGhlaWdodFxuICB0YWJCYXJIZWlnaHQ6ICgpID0+XG4gICAgc2FmZVBsYXRmb3JtU2VsZWN0KHtcbiAgICAgIGlvczogNDksXG4gICAgICBhbmRyb2lkOiA1NixcbiAgICAgIGRlZmF1bHQ6IDQ5LFxuICAgIH0pLFxufTtcblxuLy8gUGxhdGZvcm0tc3BlY2lmaWMgY29uc3RhbnRzXG5leHBvcnQgY29uc3QgUGxhdGZvcm1Db25zdGFudHMgPSB7XG4gIGlzSU9TUGxhdGZvcm06IGlzSU9TLFxuICBpc0FuZHJvaWRQbGF0Zm9ybTogaXNBbmRyb2lkLFxuICBpc1dlYlBsYXRmb3JtOiBpc1dlYixcblxuICAvLyBNaW5pbXVtIHRvdWNoIHRhcmdldHNcbiAgbWluVG91Y2hUYXJnZXQ6IHBsYXRmb3JtU3R5bGVzLnRvdWNoVGFyZ2V0KCksXG5cbiAgLy8gRGVmYXVsdCBzcGFjaW5nXG4gIGRlZmF1bHRTcGFjaW5nOiB7XG4gICAgeHM6IDQsXG4gICAgc206IDgsXG4gICAgbWQ6IDE2LFxuICAgIGxnOiAyNCxcbiAgICB4bDogMzIsXG4gIH0sXG5cbiAgLy8gQW5pbWF0aW9uIGR1cmF0aW9uc1xuICBhbmltYXRpb25EdXJhdGlvbjoge1xuICAgIGZhc3Q6IDIwMCxcbiAgICBub3JtYWw6IDMwMCxcbiAgICBzbG93OiA1MDAsXG4gIH0sXG5cbiAgLy8gWi1pbmRleCBsYXllcnNcbiAgekluZGV4OiB7XG4gICAgbW9kYWw6IDEwMDAsXG4gICAgb3ZlcmxheTogOTAwLFxuICAgIGRyb3Bkb3duOiA4MDAsXG4gICAgaGVhZGVyOiA3MDAsXG4gICAgZmFiOiA2MDAsXG4gIH0sXG59O1xuXG4vLyBQbGF0Zm9ybS1zcGVjaWZpYyBjb21wb25lbnQgcHJvcHNcbmV4cG9ydCBjb25zdCBnZXRQbGF0Zm9ybVByb3BzID0gKFxuICBjb21wb25lbnQ6ICdTdGF0dXNCYXInIHwgJ1Njcm9sbFZpZXcnIHwgJ1RleHRJbnB1dCcsXG4pID0+IHtcbiAgc3dpdGNoIChjb21wb25lbnQpIHtcbiAgICBjYXNlICdTdGF0dXNCYXInOlxuICAgICAgcmV0dXJuIHNhZmVQbGF0Zm9ybVNlbGVjdCh7XG4gICAgICAgIGlvczoge1xuICAgICAgICAgIGJhclN0eWxlOiAnZGFyay1jb250ZW50JyxcbiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICd0cmFuc3BhcmVudCcsXG4gICAgICAgIH0sXG4gICAgICAgIGFuZHJvaWQ6IHtcbiAgICAgICAgICBiYXJTdHlsZTogJ2RhcmstY29udGVudCcsXG4gICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnI0ZGRkZGRicsXG4gICAgICAgICAgdHJhbnNsdWNlbnQ6IGZhbHNlLFxuICAgICAgICB9LFxuICAgICAgICBkZWZhdWx0OiB7XG4gICAgICAgICAgYmFyU3R5bGU6ICdkYXJrLWNvbnRlbnQnLFxuICAgICAgICB9LFxuICAgICAgfSk7XG5cbiAgICBjYXNlICdTY3JvbGxWaWV3JzpcbiAgICAgIHJldHVybiBzYWZlUGxhdGZvcm1TZWxlY3Qoe1xuICAgICAgICBpb3M6IHtcbiAgICAgICAgICBib3VuY2VzOiB0cnVlLFxuICAgICAgICAgIHNob3dzVmVydGljYWxTY3JvbGxJbmRpY2F0b3I6IGZhbHNlLFxuICAgICAgICAgIHNob3dzSG9yaXpvbnRhbFNjcm9sbEluZGljYXRvcjogZmFsc2UsXG4gICAgICAgIH0sXG4gICAgICAgIGFuZHJvaWQ6IHtcbiAgICAgICAgICBib3VuY2VzOiBmYWxzZSxcbiAgICAgICAgICBzaG93c1ZlcnRpY2FsU2Nyb2xsSW5kaWNhdG9yOiBmYWxzZSxcbiAgICAgICAgICBzaG93c0hvcml6b250YWxTY3JvbGxJbmRpY2F0b3I6IGZhbHNlLFxuICAgICAgICAgIG92ZXJTY3JvbGxNb2RlOiAnbmV2ZXInLFxuICAgICAgICB9LFxuICAgICAgICBkZWZhdWx0OiB7XG4gICAgICAgICAgYm91bmNlczogdHJ1ZSxcbiAgICAgICAgICBzaG93c1ZlcnRpY2FsU2Nyb2xsSW5kaWNhdG9yOiBmYWxzZSxcbiAgICAgICAgICBzaG93c0hvcml6b250YWxTY3JvbGxJbmRpY2F0b3I6IGZhbHNlLFxuICAgICAgICB9LFxuICAgICAgfSk7XG5cbiAgICBjYXNlICdUZXh0SW5wdXQnOlxuICAgICAgcmV0dXJuIHNhZmVQbGF0Zm9ybVNlbGVjdCh7XG4gICAgICAgIGlvczoge1xuICAgICAgICAgIGNsZWFyQnV0dG9uTW9kZTogJ3doaWxlLWVkaXRpbmcnLFxuICAgICAgICAgIGVuYWJsZXNSZXR1cm5LZXlBdXRvbWF0aWNhbGx5OiB0cnVlLFxuICAgICAgICB9LFxuICAgICAgICBhbmRyb2lkOiB7XG4gICAgICAgICAgdW5kZXJsaW5lQ29sb3JBbmRyb2lkOiAndHJhbnNwYXJlbnQnLFxuICAgICAgICAgIHRleHRBbGlnblZlcnRpY2FsOiAnY2VudGVyJyxcbiAgICAgICAgfSxcbiAgICAgICAgZGVmYXVsdDoge30sXG4gICAgICB9KTtcblxuICAgIGRlZmF1bHQ6XG4gICAgICByZXR1cm4ge307XG4gIH1cbn07XG5cbi8vIFBsYXRmb3JtLXNwZWNpZmljIGJlaGF2aW9yIGhlbHBlcnNcbmV4cG9ydCBjb25zdCBwbGF0Zm9ybUJlaGF2aW9ycyA9IHtcbiAgLy8gSGFwdGljIGZlZWRiYWNrXG4gIHN1cHBvcnRzSGFwdGljczogKCkgPT4gaXNJT1MsXG5cbiAgLy8gS2V5Ym9hcmQgYmVoYXZpb3JcbiAga2V5Ym9hcmRCZWhhdmlvcjogKCkgPT5cbiAgICBzYWZlUGxhdGZvcm1TZWxlY3Qoe1xuICAgICAgaW9zOiAncGFkZGluZycsXG4gICAgICBhbmRyb2lkOiAnaGVpZ2h0JyxcbiAgICAgIGRlZmF1bHQ6ICdwYWRkaW5nJyxcbiAgICB9KSxcblxuICAvLyBNb2RhbCBwcmVzZW50YXRpb25cbiAgbW9kYWxQcmVzZW50YXRpb25TdHlsZTogKCkgPT5cbiAgICBzYWZlUGxhdGZvcm1TZWxlY3Qoe1xuICAgICAgaW9zOiAncGFnZVNoZWV0JyxcbiAgICAgIGFuZHJvaWQ6ICdub25lJyxcbiAgICAgIGRlZmF1bHQ6ICdub25lJyxcbiAgICB9KSxcblxuICAvLyBOYXZpZ2F0aW9uIGdlc3R1cmVzXG4gIGdlc3R1cmVFbmFibGVkOiAoKSA9PlxuICAgIHNhZmVQbGF0Zm9ybVNlbGVjdCh7XG4gICAgICBpb3M6IHRydWUsXG4gICAgICBhbmRyb2lkOiBmYWxzZSxcbiAgICAgIGRlZmF1bHQ6IHRydWUsXG4gICAgfSksXG59O1xuXG4vLyBFeHBvcnQgdXRpbGl0eSBmdW5jdGlvbiBmb3IgZWFzeSBhY2Nlc3NcbmV4cG9ydCBjb25zdCBwbGF0Zm9ybSA9IHtcbiAgc2VsZWN0OiBzYWZlUGxhdGZvcm1TZWxlY3QsXG4gIHN0eWxlczogcGxhdGZvcm1TdHlsZXMsXG4gIGNvbnN0YW50czogUGxhdGZvcm1Db25zdGFudHMsXG4gIHByb3BzOiBnZXRQbGF0Zm9ybVByb3BzLFxuICBiZWhhdmlvcnM6IHBsYXRmb3JtQmVoYXZpb3JzLFxuICBpczoge1xuICAgIGlvczogaXNJT1MsXG4gICAgYW5kcm9pZDogaXNBbmRyb2lkLFxuICAgIHdlYjogaXNXZWIsXG4gIH0sXG59O1xuIl0sIm1hcHBpbmdzIjoiOzs7O0FBVUEsSUFBQUEsWUFBQSxHQUFBQyxPQUFBO0FBR08sSUFBTUMsS0FBSyxHQUFBQyxPQUFBLENBQUFELEtBQUEsR0FBR0UscUJBQVEsQ0FBQ0MsRUFBRSxLQUFLLEtBQUs7QUFDbkMsSUFBTUMsU0FBUyxHQUFBSCxPQUFBLENBQUFHLFNBQUEsR0FBR0YscUJBQVEsQ0FBQ0MsRUFBRSxLQUFLLFNBQVM7QUFDM0MsSUFBTUUsS0FBSyxHQUFBSixPQUFBLENBQUFJLEtBQUEsR0FBR0gscUJBQVEsQ0FBQ0MsRUFBRSxLQUFLLEtBQUs7QUFHbkMsSUFBTUcsa0JBQWtCLEdBQUFMLE9BQUEsQ0FBQUssa0JBQUEsR0FBRyxTQUFyQkEsa0JBQWtCQSxDQUFPQyxTQUtyQyxFQUFRO0VBQ1AsSUFBSVAsS0FBSyxJQUFJTyxTQUFTLENBQUNDLEdBQUcsS0FBS0MsU0FBUyxFQUFFO0lBQ3hDLE9BQU9GLFNBQVMsQ0FBQ0MsR0FBRztFQUN0QjtFQUVBLElBQUlKLFNBQVMsSUFBSUcsU0FBUyxDQUFDRyxPQUFPLEtBQUtELFNBQVMsRUFBRTtJQUNoRCxPQUFPRixTQUFTLENBQUNHLE9BQU87RUFDMUI7RUFFQSxJQUFJTCxLQUFLLElBQUlFLFNBQVMsQ0FBQ0ksR0FBRyxLQUFLRixTQUFTLEVBQUU7SUFDeEMsT0FBT0YsU0FBUyxDQUFDSSxHQUFHO0VBQ3RCO0VBRUEsSUFBSUosU0FBUyxDQUFDSyxPQUFPLEtBQUtILFNBQVMsRUFBRTtJQUNuQyxPQUFPRixTQUFTLENBQUNLLE9BQU87RUFDMUI7RUFHQSxJQUFJTCxTQUFTLENBQUNDLEdBQUcsS0FBS0MsU0FBUyxFQUFFO0lBQy9CLE9BQU9GLFNBQVMsQ0FBQ0MsR0FBRztFQUN0QjtFQUVBLElBQUlELFNBQVMsQ0FBQ0csT0FBTyxLQUFLRCxTQUFTLEVBQUU7SUFDbkMsT0FBT0YsU0FBUyxDQUFDRyxPQUFPO0VBQzFCO0VBRUEsTUFBTSxJQUFJRyxLQUFLLENBQ2Isb0VBQ0YsQ0FBQztBQUNILENBQUM7QUFHTSxJQUFNQyxjQUFjLEdBQUFiLE9BQUEsQ0FBQWEsY0FBQSxHQUFHO0VBRTVCQyxNQUFNLEVBQUUsU0FBUkEsTUFBTUEsQ0FBQTtJQUFBLElBQUdDLFNBQWlCLEdBQUFDLFNBQUEsQ0FBQUMsTUFBQSxRQUFBRCxTQUFBLFFBQUFSLFNBQUEsR0FBQVEsU0FBQSxNQUFHLENBQUM7SUFBQSxPQUM1Qlgsa0JBQWtCLENBQUM7TUFDakJFLEdBQUcsRUFBRTtRQUNIVyxXQUFXLEVBQUUsTUFBTTtRQUNuQkMsWUFBWSxFQUFFO1VBQUVDLEtBQUssRUFBRSxDQUFDO1VBQUVDLE1BQU0sRUFBRU4sU0FBUyxHQUFHO1FBQUUsQ0FBQztRQUNqRE8sYUFBYSxFQUFFLEdBQUc7UUFDbEJDLFlBQVksRUFBRVI7TUFDaEIsQ0FBQztNQUNETixPQUFPLEVBQUU7UUFDUE0sU0FBUyxFQUFUQTtNQUNGLENBQUM7TUFDREosT0FBTyxFQUFFLENBQUM7SUFDWixDQUFDLENBQUM7RUFBQTtFQUdKYSxZQUFZLEVBQUUsU0FBZEEsWUFBWUEsQ0FBQSxFQUFxRDtJQUFBLElBQWxEQyxJQUFrQyxHQUFBVCxTQUFBLENBQUFDLE1BQUEsUUFBQUQsU0FBQSxRQUFBUixTQUFBLEdBQUFRLFNBQUEsTUFBRyxRQUFRO0lBQzFELElBQU1VLFNBQVMsR0FBRztNQUNoQkMsS0FBSyxFQUFFdEIsa0JBQWtCLENBQUM7UUFBRUUsR0FBRyxFQUFFLENBQUM7UUFBRUUsT0FBTyxFQUFFLENBQUM7UUFBRUUsT0FBTyxFQUFFO01BQUUsQ0FBQyxDQUFDO01BQzdEaUIsTUFBTSxFQUFFdkIsa0JBQWtCLENBQUM7UUFBRUUsR0FBRyxFQUFFLEVBQUU7UUFBRUUsT0FBTyxFQUFFLENBQUM7UUFBRUUsT0FBTyxFQUFFO01BQUcsQ0FBQyxDQUFDO01BQ2hFa0IsS0FBSyxFQUFFeEIsa0JBQWtCLENBQUM7UUFBRUUsR0FBRyxFQUFFLEVBQUU7UUFBRUUsT0FBTyxFQUFFLEVBQUU7UUFBRUUsT0FBTyxFQUFFO01BQUcsQ0FBQztJQUNqRSxDQUFDO0lBQ0QsT0FBT2UsU0FBUyxDQUFDRCxJQUFJLENBQUM7RUFDeEIsQ0FBQztFQUdESyxXQUFXLEVBQUUsU0FBYkEsV0FBV0EsQ0FBQTtJQUFBLE9BQ1R6QixrQkFBa0IsQ0FBQztNQUNqQkUsR0FBRyxFQUFFLEVBQUU7TUFDUEUsT0FBTyxFQUFFLEVBQUU7TUFDWEUsT0FBTyxFQUFFO0lBQ1gsQ0FBQyxDQUFDO0VBQUE7RUFHSm9CLGVBQWUsRUFBRSxTQUFqQkEsZUFBZUEsQ0FBQTtJQUFBLE9BQ2IxQixrQkFBa0IsQ0FBQztNQUNqQkUsR0FBRyxFQUFFLEVBQUU7TUFDUEUsT0FBTyxFQUFFLEVBQUU7TUFDWEUsT0FBTyxFQUFFO0lBQ1gsQ0FBQyxDQUFDO0VBQUE7RUFHSnFCLG1CQUFtQixFQUFFLFNBQXJCQSxtQkFBbUJBLENBQUE7SUFBQSxPQUNqQjNCLGtCQUFrQixDQUFDO01BQ2pCRSxHQUFHLEVBQUUsRUFBRTtNQUNQRSxPQUFPLEVBQUUsRUFBRTtNQUNYRSxPQUFPLEVBQUU7SUFDWCxDQUFDLENBQUM7RUFBQTtFQUdKc0IsWUFBWSxFQUFFLFNBQWRBLFlBQVlBLENBQUE7SUFBQSxPQUNWNUIsa0JBQWtCLENBQUM7TUFDakJFLEdBQUcsRUFBRSxFQUFFO01BQ1BFLE9BQU8sRUFBRSxFQUFFO01BQ1hFLE9BQU8sRUFBRTtJQUNYLENBQUMsQ0FBQztFQUFBO0FBQ04sQ0FBQztBQUdNLElBQU11QixpQkFBaUIsR0FBQWxDLE9BQUEsQ0FBQWtDLGlCQUFBLEdBQUc7RUFDL0JDLGFBQWEsRUFBRXBDLEtBQUs7RUFDcEJxQyxpQkFBaUIsRUFBRWpDLFNBQVM7RUFDNUJrQyxhQUFhLEVBQUVqQyxLQUFLO0VBR3BCa0MsY0FBYyxFQUFFekIsY0FBYyxDQUFDaUIsV0FBVyxDQUFDLENBQUM7RUFHNUNTLGNBQWMsRUFBRTtJQUNkQyxFQUFFLEVBQUUsQ0FBQztJQUNMQyxFQUFFLEVBQUUsQ0FBQztJQUNMQyxFQUFFLEVBQUUsRUFBRTtJQUNOQyxFQUFFLEVBQUUsRUFBRTtJQUNOQyxFQUFFLEVBQUU7RUFDTixDQUFDO0VBR0RDLGlCQUFpQixFQUFFO0lBQ2pCQyxJQUFJLEVBQUUsR0FBRztJQUNUQyxNQUFNLEVBQUUsR0FBRztJQUNYQyxJQUFJLEVBQUU7RUFDUixDQUFDO0VBR0RDLE1BQU0sRUFBRTtJQUNOQyxLQUFLLEVBQUUsSUFBSTtJQUNYQyxPQUFPLEVBQUUsR0FBRztJQUNaQyxRQUFRLEVBQUUsR0FBRztJQUNiQyxNQUFNLEVBQUUsR0FBRztJQUNYQyxHQUFHLEVBQUU7RUFDUDtBQUNGLENBQUM7QUFHTSxJQUFNQyxnQkFBZ0IsR0FBQXZELE9BQUEsQ0FBQXVELGdCQUFBLEdBQUcsU0FBbkJBLGdCQUFnQkEsQ0FDM0JDLFNBQW1ELEVBQ2hEO0VBQ0gsUUFBUUEsU0FBUztJQUNmLEtBQUssV0FBVztNQUNkLE9BQU9uRCxrQkFBa0IsQ0FBQztRQUN4QkUsR0FBRyxFQUFFO1VBQ0hrRCxRQUFRLEVBQUUsY0FBYztVQUN4QkMsZUFBZSxFQUFFO1FBQ25CLENBQUM7UUFDRGpELE9BQU8sRUFBRTtVQUNQZ0QsUUFBUSxFQUFFLGNBQWM7VUFDeEJDLGVBQWUsRUFBRSxTQUFTO1VBQzFCQyxXQUFXLEVBQUU7UUFDZixDQUFDO1FBQ0RoRCxPQUFPLEVBQUU7VUFDUDhDLFFBQVEsRUFBRTtRQUNaO01BQ0YsQ0FBQyxDQUFDO0lBRUosS0FBSyxZQUFZO01BQ2YsT0FBT3BELGtCQUFrQixDQUFDO1FBQ3hCRSxHQUFHLEVBQUU7VUFDSHFELE9BQU8sRUFBRSxJQUFJO1VBQ2JDLDRCQUE0QixFQUFFLEtBQUs7VUFDbkNDLDhCQUE4QixFQUFFO1FBQ2xDLENBQUM7UUFDRHJELE9BQU8sRUFBRTtVQUNQbUQsT0FBTyxFQUFFLEtBQUs7VUFDZEMsNEJBQTRCLEVBQUUsS0FBSztVQUNuQ0MsOEJBQThCLEVBQUUsS0FBSztVQUNyQ0MsY0FBYyxFQUFFO1FBQ2xCLENBQUM7UUFDRHBELE9BQU8sRUFBRTtVQUNQaUQsT0FBTyxFQUFFLElBQUk7VUFDYkMsNEJBQTRCLEVBQUUsS0FBSztVQUNuQ0MsOEJBQThCLEVBQUU7UUFDbEM7TUFDRixDQUFDLENBQUM7SUFFSixLQUFLLFdBQVc7TUFDZCxPQUFPekQsa0JBQWtCLENBQUM7UUFDeEJFLEdBQUcsRUFBRTtVQUNIeUQsZUFBZSxFQUFFLGVBQWU7VUFDaENDLDZCQUE2QixFQUFFO1FBQ2pDLENBQUM7UUFDRHhELE9BQU8sRUFBRTtVQUNQeUQscUJBQXFCLEVBQUUsYUFBYTtVQUNwQ0MsaUJBQWlCLEVBQUU7UUFDckIsQ0FBQztRQUNEeEQsT0FBTyxFQUFFLENBQUM7TUFDWixDQUFDLENBQUM7SUFFSjtNQUNFLE9BQU8sQ0FBQyxDQUFDO0VBQ2I7QUFDRixDQUFDO0FBR00sSUFBTXlELGlCQUFpQixHQUFBcEUsT0FBQSxDQUFBb0UsaUJBQUEsR0FBRztFQUUvQkMsZUFBZSxFQUFFLFNBQWpCQSxlQUFlQSxDQUFBO0lBQUEsT0FBUXRFLEtBQUs7RUFBQTtFQUc1QnVFLGdCQUFnQixFQUFFLFNBQWxCQSxnQkFBZ0JBLENBQUE7SUFBQSxPQUNkakUsa0JBQWtCLENBQUM7TUFDakJFLEdBQUcsRUFBRSxTQUFTO01BQ2RFLE9BQU8sRUFBRSxRQUFRO01BQ2pCRSxPQUFPLEVBQUU7SUFDWCxDQUFDLENBQUM7RUFBQTtFQUdKNEQsc0JBQXNCLEVBQUUsU0FBeEJBLHNCQUFzQkEsQ0FBQTtJQUFBLE9BQ3BCbEUsa0JBQWtCLENBQUM7TUFDakJFLEdBQUcsRUFBRSxXQUFXO01BQ2hCRSxPQUFPLEVBQUUsTUFBTTtNQUNmRSxPQUFPLEVBQUU7SUFDWCxDQUFDLENBQUM7RUFBQTtFQUdKNkQsY0FBYyxFQUFFLFNBQWhCQSxjQUFjQSxDQUFBO0lBQUEsT0FDWm5FLGtCQUFrQixDQUFDO01BQ2pCRSxHQUFHLEVBQUUsSUFBSTtNQUNURSxPQUFPLEVBQUUsS0FBSztNQUNkRSxPQUFPLEVBQUU7SUFDWCxDQUFDLENBQUM7RUFBQTtBQUNOLENBQUM7QUFHTSxJQUFNOEQsUUFBUSxHQUFBekUsT0FBQSxDQUFBeUUsUUFBQSxHQUFHO0VBQ3RCQyxNQUFNLEVBQUVyRSxrQkFBa0I7RUFDMUJzRSxNQUFNLEVBQUU5RCxjQUFjO0VBQ3RCK0QsU0FBUyxFQUFFMUMsaUJBQWlCO0VBQzVCMkMsS0FBSyxFQUFFdEIsZ0JBQWdCO0VBQ3ZCdUIsU0FBUyxFQUFFVixpQkFBaUI7RUFDNUJXLEVBQUUsRUFBRTtJQUNGeEUsR0FBRyxFQUFFUixLQUFLO0lBQ1ZVLE9BQU8sRUFBRU4sU0FBUztJQUNsQk8sR0FBRyxFQUFFTjtFQUNQO0FBQ0YsQ0FBQyIsImlnbm9yZUxpc3QiOltdfQ==