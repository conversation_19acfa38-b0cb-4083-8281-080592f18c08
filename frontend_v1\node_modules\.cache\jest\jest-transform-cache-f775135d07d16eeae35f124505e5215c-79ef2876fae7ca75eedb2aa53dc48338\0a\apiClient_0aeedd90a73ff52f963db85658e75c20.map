{"version": 3, "names": ["_asyncStorage", "_interopRequireDefault", "require", "_authSlice", "_performanceCacheService", "getApiBaseUrl", "__DEV__", "API_BASE_URL", "DEFAULT_TIMEOUT", "ApiClient", "baseURL", "arguments", "length", "undefined", "timeout", "_classCallCheck2", "default", "authToken", "defaultTimeout", "loadAuthToken", "_createClass2", "key", "value", "_loadAuthToken", "_asyncToGenerator2", "token", "AsyncStorage", "getItem", "error", "console", "warn", "apply", "setAuthToken", "setItem", "removeItem", "_refreshAuthToken", "refreshToken", "Error", "refreshResponse", "fetch", "method", "headers", "Accept", "body", "JSON", "stringify", "refresh", "ok", "errorData", "json", "detail", "refreshData", "access", "authState", "useAuthStore", "getState", "loginSuccess", "userRole", "log", "message", "logout", "refreshAuthToken", "buildUrl", "url", "params", "fullUrl", "startsWith", "Object", "keys", "urlParams", "URLSearchParams", "entries", "for<PERSON>ach", "_ref", "_ref2", "_slicedToArray2", "append", "String", "separator", "includes", "toString", "buildHeaders", "customHeaders", "requiresAuth", "assign", "Authorization", "_makeRequest", "config", "data", "_config$timeout", "_config$requiresAuth", "onProgress", "onStatusUpdate", "cache", "enabled", "cache<PERSON>ey", "cachedResponse", "performanceCacheService", "getCachedApiResponse", "loaded", "total", "percentage", "status", "statusText", "controller", "AbortController", "timeoutId", "setTimeout", "abort", "requestInit", "signal", "response", "clearTimeout", "responseData", "contentType", "get", "text", "retryHeaders", "retryResponse", "retryData", "fromEntries", "refreshError", "details", "cacheApiResponse", "ttl", "priority", "name", "makeRequest", "_x", "_get", "_cacheOptions$enabled", "cacheOptions", "_x2", "_x3", "_post", "post", "_x4", "_x5", "_put", "put", "_x6", "_x7", "_patch", "patch", "_x8", "_x9", "_delete2", "delete", "_x0", "apiClient", "exports", "_default"], "sources": ["apiClient.ts"], "sourcesContent": ["/**\n * API Client - HTTP Client for Backend Communication\n *\n * Component Contract:\n * - Provides centralized HTTP client for all API calls\n * - Handles authentication token management\n * - Implements request/response interceptors\n * - Provides error handling and retry logic\n * - Supports request cancellation and timeouts\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport AsyncStorage from '@react-native-async-storage/async-storage';\nimport { Platform } from 'react-native';\n\nimport { useAuthStore } from '../store/authSlice';\n\nimport { performanceCacheService } from './performanceCacheService';\n\n// Configuration\nconst getApiBaseUrl = () => {\n  if (!__DEV__) {\n    return 'https://api.vierla.com';\n  }\n\n  // In development, use the backend server address\n  return 'http://************:8000';\n};\n\nconst API_BASE_URL = getApiBaseUrl();\nconst DEFAULT_TIMEOUT = 10000; // 10 seconds\n\n// Types\nexport interface ApiResponse<T = any> {\n  data: T;\n  status: number;\n  statusText: string;\n  headers: Record<string, string>;\n}\n\nexport interface ApiError {\n  message: string;\n  status: number;\n  details?: any;\n}\n\nexport interface ApiProgress {\n  loaded: number;\n  total: number;\n  percentage: number;\n}\n\nexport interface RequestConfig {\n  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';\n  url: string;\n  data?: any;\n  params?: Record<string, any>;\n  headers?: Record<string, string>;\n  timeout?: number;\n  requiresAuth?: boolean;\n  onProgress?: (progress: ApiProgress) => void;\n  onStatusUpdate?: (status: string) => void;\n  cache?: {\n    enabled: boolean;\n    ttl?: number;\n    priority?: boolean;\n    key?: string;\n  };\n}\n\nclass ApiClient {\n  private baseURL: string;\n  private defaultTimeout: number;\n  private authToken: string | null = null;\n\n  constructor(\n    baseURL: string = API_BASE_URL,\n    timeout: number = DEFAULT_TIMEOUT,\n  ) {\n    this.baseURL = baseURL;\n    this.defaultTimeout = timeout;\n    this.loadAuthToken();\n  }\n\n  /**\n   * Load authentication token from storage\n   */\n  private async loadAuthToken(): Promise<void> {\n    try {\n      const token = await AsyncStorage.getItem('auth_token');\n      this.authToken = token;\n    } catch (error) {\n      console.warn('Failed to load auth token:', error);\n    }\n  }\n\n  /**\n   * Set authentication token\n   */\n  public setAuthToken(token: string | null): void {\n    this.authToken = token;\n    if (token) {\n      AsyncStorage.setItem('auth_token', token);\n    } else {\n      AsyncStorage.removeItem('auth_token');\n    }\n  }\n\n  /**\n   * Refresh authentication token\n   */\n  private async refreshAuthToken(): Promise<void> {\n    try {\n      // Get refresh token from AsyncStorage\n      const refreshToken = await AsyncStorage.getItem('refresh_token');\n\n      if (!refreshToken) {\n        throw new Error('No refresh token available');\n      }\n\n      // Make refresh request without auth header\n      const refreshResponse = await fetch(\n        `${this.baseURL}/api/auth/token/refresh/`,\n        {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            Accept: 'application/json',\n          },\n          body: JSON.stringify({ refresh: refreshToken }),\n        },\n      );\n\n      if (!refreshResponse.ok) {\n        const errorData = await refreshResponse.json();\n        throw new Error(errorData.detail || 'Token refresh failed');\n      }\n\n      const refreshData = await refreshResponse.json();\n\n      // Update tokens\n      if (refreshData.access) {\n        this.setAuthToken(refreshData.access);\n\n        // Update auth store\n        const authState = useAuthStore.getState();\n        authState.loginSuccess(\n          refreshData.access,\n          authState.userRole || 'customer',\n        );\n\n        console.log('✅ API: Token refreshed successfully');\n      }\n\n      // Store new refresh token if provided\n      if (refreshData.refresh) {\n        await AsyncStorage.setItem('refresh_token', refreshData.refresh);\n      }\n    } catch (error: any) {\n      console.error('❌ API: Token refresh failed:', error.message);\n\n      // Clear invalid tokens\n      this.setAuthToken(null);\n      await AsyncStorage.removeItem('refresh_token');\n\n      // Update auth store to logged out state\n      const authState = useAuthStore.getState();\n      authState.logout();\n\n      throw error;\n    }\n  }\n\n  /**\n   * Build full URL with query parameters\n   */\n  private buildUrl(url: string, params?: Record<string, any>): string {\n    const fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`;\n\n    if (!params || Object.keys(params).length === 0) {\n      return fullUrl;\n    }\n\n    const urlParams = new URLSearchParams();\n    Object.entries(params).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        urlParams.append(key, String(value));\n      }\n    });\n\n    const separator = fullUrl.includes('?') ? '&' : '?';\n    return `${fullUrl}${separator}${urlParams.toString()}`;\n  }\n\n  /**\n   * Build request headers\n   */\n  private buildHeaders(\n    customHeaders?: Record<string, string>,\n    requiresAuth: boolean = true,\n  ): Record<string, string> {\n    const headers: Record<string, string> = {\n      'Content-Type': 'application/json',\n      Accept: 'application/json',\n      ...customHeaders,\n    };\n\n    if (requiresAuth) {\n      // Get token from auth store first, fallback to instance token\n      const authState = useAuthStore.getState();\n      const token = authState.authToken || this.authToken;\n\n      if (token) {\n        headers.Authorization = `Bearer ${token}`;\n      }\n    }\n\n    return headers;\n  }\n\n  /**\n   * Make HTTP request\n   */\n  private async makeRequest<T>(config: RequestConfig): Promise<ApiResponse<T>> {\n    const {\n      method,\n      url,\n      data,\n      params,\n      headers: customHeaders,\n      timeout = this.defaultTimeout,\n      requiresAuth = true,\n      onProgress,\n      onStatusUpdate,\n      cache,\n    } = config;\n\n    // Check cache for GET requests\n    if (method === 'GET' && cache?.enabled) {\n      const cacheKey = cache.key || `${url}_${JSON.stringify(params || {})}`;\n      const cachedResponse =\n        await performanceCacheService.getCachedApiResponse<T>(cacheKey);\n\n      if (cachedResponse) {\n        onStatusUpdate?.('Loaded from cache');\n        onProgress?.({ loaded: 100, total: 100, percentage: 100 });\n\n        return {\n          data: cachedResponse,\n          status: 200,\n          statusText: 'OK (Cached)',\n          headers: {},\n        };\n      }\n    }\n\n    const fullUrl = this.buildUrl(url, params);\n    const headers = this.buildHeaders(customHeaders, requiresAuth);\n\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), timeout);\n\n    try {\n      // Notify request start\n      onStatusUpdate?.('Preparing request...');\n\n      const requestInit: RequestInit = {\n        method,\n        headers,\n        signal: controller.signal,\n      };\n\n      if (data && method !== 'GET') {\n        requestInit.body = JSON.stringify(data);\n      }\n\n      // Notify request sending\n      onStatusUpdate?.('Sending request...');\n      onProgress?.({ loaded: 0, total: 100, percentage: 0 });\n\n      const response = await fetch(fullUrl, requestInit);\n      clearTimeout(timeoutId);\n\n      // Notify response received\n      onStatusUpdate?.('Processing response...');\n      onProgress?.({ loaded: 50, total: 100, percentage: 50 });\n\n      let responseData: T;\n      const contentType = response.headers.get('content-type');\n\n      // Update progress for response parsing\n      onProgress?.({ loaded: 75, total: 100, percentage: 75 });\n\n      if (contentType && contentType.includes('application/json')) {\n        responseData = await response.json();\n      } else {\n        responseData = (await response.text()) as unknown as T;\n      }\n\n      if (!response.ok) {\n        onStatusUpdate?.('Request failed');\n\n        // Handle authentication errors with token refresh\n        if (response.status === 401 && requiresAuth) {\n          console.warn(\n            '🔐 API: Authentication failed, attempting token refresh...',\n          );\n\n          try {\n            // Attempt to refresh token\n            await this.refreshAuthToken();\n\n            // Retry the original request with new token\n            console.log('🔄 API: Retrying request with refreshed token...');\n            const retryHeaders = this.buildHeaders(customHeaders, requiresAuth);\n            const retryResponse = await fetch(fullUrl, {\n              ...requestInit,\n              headers: retryHeaders,\n            });\n\n            if (retryResponse.ok) {\n              const retryData = await retryResponse.json();\n              console.log('✅ API: Request succeeded after token refresh');\n              return {\n                data: retryData,\n                status: retryResponse.status,\n                statusText: retryResponse.statusText,\n                headers: Object.fromEntries(retryResponse.headers.entries()),\n              };\n            }\n          } catch (refreshError) {\n            console.error('❌ API: Token refresh failed:', refreshError);\n            // Fall through to throw original 401 error\n          }\n        }\n\n        throw {\n          message: responseData || response.statusText,\n          status: response.status,\n          details: responseData,\n        } as ApiError;\n      }\n\n      // Complete progress\n      onStatusUpdate?.('Request completed');\n      onProgress?.({ loaded: 100, total: 100, percentage: 100 });\n\n      // Cache successful GET responses\n      if (method === 'GET' && cache?.enabled && response.ok) {\n        const cacheKey = cache.key || `${url}_${JSON.stringify(params || {})}`;\n        await performanceCacheService.cacheApiResponse(cacheKey, responseData, {\n          ttl: cache.ttl,\n          priority: cache.priority,\n        });\n      }\n\n      return {\n        data: responseData,\n        status: response.status,\n        statusText: response.statusText,\n        headers: Object.fromEntries(response.headers.entries()),\n      };\n    } catch (error: any) {\n      clearTimeout(timeoutId);\n\n      if (error.name === 'AbortError') {\n        throw {\n          message: 'Request timeout',\n          status: 408,\n        } as ApiError;\n      }\n\n      if (error.status) {\n        throw error as ApiError;\n      }\n\n      throw {\n        message: error.message || 'Network error',\n        status: 0,\n        details: error,\n      } as ApiError;\n    }\n  }\n\n  /**\n   * GET request with caching\n   */\n  async get<T>(\n    url: string,\n    params?: Record<string, any>,\n    requiresAuth: boolean = true,\n    cacheOptions?: { enabled?: boolean; ttl?: number; priority?: boolean },\n  ): Promise<ApiResponse<T>> {\n    return this.makeRequest<T>({\n      method: 'GET',\n      url,\n      params,\n      requiresAuth,\n      cache: {\n        enabled: cacheOptions?.enabled ?? true, // Enable caching by default for GET requests\n        ttl: cacheOptions?.ttl,\n        priority: cacheOptions?.priority,\n      },\n    });\n  }\n\n  /**\n   * POST request\n   */\n  async post<T>(\n    url: string,\n    data?: any,\n    requiresAuth: boolean = true,\n  ): Promise<ApiResponse<T>> {\n    return this.makeRequest<T>({ method: 'POST', url, data, requiresAuth });\n  }\n\n  /**\n   * PUT request\n   */\n  async put<T>(\n    url: string,\n    data?: any,\n    requiresAuth: boolean = true,\n  ): Promise<ApiResponse<T>> {\n    return this.makeRequest<T>({ method: 'PUT', url, data, requiresAuth });\n  }\n\n  /**\n   * PATCH request\n   */\n  async patch<T>(\n    url: string,\n    data?: any,\n    requiresAuth: boolean = true,\n  ): Promise<ApiResponse<T>> {\n    return this.makeRequest<T>({ method: 'PATCH', url, data, requiresAuth });\n  }\n\n  /**\n   * DELETE request\n   */\n  async delete<T>(\n    url: string,\n    requiresAuth: boolean = true,\n  ): Promise<ApiResponse<T>> {\n    return this.makeRequest<T>({ method: 'DELETE', url, requiresAuth });\n  }\n}\n\n// Create and export singleton instance\nexport const apiClient = new ApiClient();\nexport default apiClient;\n"], "mappings": ";;;;;;;;;AAcA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AAGA,IAAAC,UAAA,GAAAD,OAAA;AAEA,IAAAE,wBAAA,GAAAF,OAAA;AAGA,IAAMG,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;EAC1B,IAAI,CAACC,OAAO,EAAE;IACZ,OAAO,wBAAwB;EACjC;EAGA,OAAO,0BAA0B;AACnC,CAAC;AAED,IAAMC,YAAY,GAAGF,aAAa,CAAC,CAAC;AACpC,IAAMG,eAAe,GAAG,KAAK;AAAC,IAwCxBC,SAAS;EAKb,SAAAA,UAAA,EAGE;IAAA,IAFAC,OAAe,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGJ,YAAY;IAAA,IAC9BO,OAAe,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGH,eAAe;IAAA,IAAAO,gBAAA,CAAAC,OAAA,QAAAP,SAAA;IAAA,KAJ3BQ,SAAS,GAAkB,IAAI;IAMrC,IAAI,CAACP,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACQ,cAAc,GAAGJ,OAAO;IAC7B,IAAI,CAACK,aAAa,CAAC,CAAC;EACtB;EAAC,WAAAC,aAAA,CAAAJ,OAAA,EAAAP,SAAA;IAAAY,GAAA;IAAAC,KAAA;MAAA,IAAAC,cAAA,OAAAC,kBAAA,CAAAR,OAAA,EAKD,aAA6C;QAC3C,IAAI;UACF,IAAMS,KAAK,SAASC,qBAAY,CAACC,OAAO,CAAC,YAAY,CAAC;UACtD,IAAI,CAACV,SAAS,GAAGQ,KAAK;QACxB,CAAC,CAAC,OAAOG,KAAK,EAAE;UACdC,OAAO,CAACC,IAAI,CAAC,4BAA4B,EAAEF,KAAK,CAAC;QACnD;MACF,CAAC;MAAA,SAPaT,aAAaA,CAAA;QAAA,OAAAI,cAAA,CAAAQ,KAAA,OAAApB,SAAA;MAAA;MAAA,OAAbQ,aAAa;IAAA;EAAA;IAAAE,GAAA;IAAAC,KAAA,EAY3B,SAAOU,YAAYA,CAACP,KAAoB,EAAQ;MAC9C,IAAI,CAACR,SAAS,GAAGQ,KAAK;MACtB,IAAIA,KAAK,EAAE;QACTC,qBAAY,CAACO,OAAO,CAAC,YAAY,EAAER,KAAK,CAAC;MAC3C,CAAC,MAAM;QACLC,qBAAY,CAACQ,UAAU,CAAC,YAAY,CAAC;MACvC;IACF;EAAC;IAAAb,GAAA;IAAAC,KAAA;MAAA,IAAAa,iBAAA,OAAAX,kBAAA,CAAAR,OAAA,EAKD,aAAgD;QAC9C,IAAI;UAEF,IAAMoB,YAAY,SAASV,qBAAY,CAACC,OAAO,CAAC,eAAe,CAAC;UAEhE,IAAI,CAACS,YAAY,EAAE;YACjB,MAAM,IAAIC,KAAK,CAAC,4BAA4B,CAAC;UAC/C;UAGA,IAAMC,eAAe,SAASC,KAAK,CACjC,GAAG,IAAI,CAAC7B,OAAO,0BAA0B,EACzC;YACE8B,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE,kBAAkB;cAClCC,MAAM,EAAE;YACV,CAAC;YACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cAAEC,OAAO,EAAEV;YAAa,CAAC;UAChD,CACF,CAAC;UAED,IAAI,CAACE,eAAe,CAACS,EAAE,EAAE;YACvB,IAAMC,SAAS,SAASV,eAAe,CAACW,IAAI,CAAC,CAAC;YAC9C,MAAM,IAAIZ,KAAK,CAACW,SAAS,CAACE,MAAM,IAAI,sBAAsB,CAAC;UAC7D;UAEA,IAAMC,WAAW,SAASb,eAAe,CAACW,IAAI,CAAC,CAAC;UAGhD,IAAIE,WAAW,CAACC,MAAM,EAAE;YACtB,IAAI,CAACpB,YAAY,CAACmB,WAAW,CAACC,MAAM,CAAC;YAGrC,IAAMC,SAAS,GAAGC,uBAAY,CAACC,QAAQ,CAAC,CAAC;YACzCF,SAAS,CAACG,YAAY,CACpBL,WAAW,CAACC,MAAM,EAClBC,SAAS,CAACI,QAAQ,IAAI,UACxB,CAAC;YAED5B,OAAO,CAAC6B,GAAG,CAAC,qCAAqC,CAAC;UACpD;UAGA,IAAIP,WAAW,CAACL,OAAO,EAAE;YACvB,MAAMpB,qBAAY,CAACO,OAAO,CAAC,eAAe,EAAEkB,WAAW,CAACL,OAAO,CAAC;UAClE;QACF,CAAC,CAAC,OAAOlB,KAAU,EAAE;UACnBC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC+B,OAAO,CAAC;UAG5D,IAAI,CAAC3B,YAAY,CAAC,IAAI,CAAC;UACvB,MAAMN,qBAAY,CAACQ,UAAU,CAAC,eAAe,CAAC;UAG9C,IAAMmB,UAAS,GAAGC,uBAAY,CAACC,QAAQ,CAAC,CAAC;UACzCF,UAAS,CAACO,MAAM,CAAC,CAAC;UAElB,MAAMhC,KAAK;QACb;MACF,CAAC;MAAA,SA5DaiC,gBAAgBA,CAAA;QAAA,OAAA1B,iBAAA,CAAAJ,KAAA,OAAApB,SAAA;MAAA;MAAA,OAAhBkD,gBAAgB;IAAA;EAAA;IAAAxC,GAAA;IAAAC,KAAA,EAiE9B,SAAQwC,QAAQA,CAACC,GAAW,EAAEC,MAA4B,EAAU;MAClE,IAAMC,OAAO,GAAGF,GAAG,CAACG,UAAU,CAAC,MAAM,CAAC,GAAGH,GAAG,GAAG,GAAG,IAAI,CAACrD,OAAO,GAAGqD,GAAG,EAAE;MAEtE,IAAI,CAACC,MAAM,IAAIG,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACpD,MAAM,KAAK,CAAC,EAAE;QAC/C,OAAOqD,OAAO;MAChB;MAEA,IAAMI,SAAS,GAAG,IAAIC,eAAe,CAAC,CAAC;MACvCH,MAAM,CAACI,OAAO,CAACP,MAAM,CAAC,CAACQ,OAAO,CAAC,UAAAC,IAAA,EAAkB;QAAA,IAAAC,KAAA,OAAAC,eAAA,CAAA3D,OAAA,EAAAyD,IAAA;UAAhBpD,GAAG,GAAAqD,KAAA;UAAEpD,KAAK,GAAAoD,KAAA;QACzC,IAAIpD,KAAK,KAAKT,SAAS,IAAIS,KAAK,KAAK,IAAI,EAAE;UACzC+C,SAAS,CAACO,MAAM,CAACvD,GAAG,EAAEwD,MAAM,CAACvD,KAAK,CAAC,CAAC;QACtC;MACF,CAAC,CAAC;MAEF,IAAMwD,SAAS,GAAGb,OAAO,CAACc,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;MACnD,OAAO,GAAGd,OAAO,GAAGa,SAAS,GAAGT,SAAS,CAACW,QAAQ,CAAC,CAAC,EAAE;IACxD;EAAC;IAAA3D,GAAA;IAAAC,KAAA,EAKD,SAAQ2D,YAAYA,CAClBC,aAAsC,EAEd;MAAA,IADxBC,YAAqB,GAAAxE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MAE5B,IAAM8B,OAA+B,GAAA0B,MAAA,CAAAiB,MAAA;QACnC,cAAc,EAAE,kBAAkB;QAClC1C,MAAM,EAAE;MAAkB,GACvBwC,aAAa,CACjB;MAED,IAAIC,YAAY,EAAE;QAEhB,IAAM9B,SAAS,GAAGC,uBAAY,CAACC,QAAQ,CAAC,CAAC;QACzC,IAAM9B,KAAK,GAAG4B,SAAS,CAACpC,SAAS,IAAI,IAAI,CAACA,SAAS;QAEnD,IAAIQ,KAAK,EAAE;UACTgB,OAAO,CAAC4C,aAAa,GAAG,UAAU5D,KAAK,EAAE;QAC3C;MACF;MAEA,OAAOgB,OAAO;IAChB;EAAC;IAAApB,GAAA;IAAAC,KAAA;MAAA,IAAAgE,YAAA,OAAA9D,kBAAA,CAAAR,OAAA,EAKD,WAA6BuE,MAAqB,EAA2B;QAC3E,IACE/C,MAAM,GAUJ+C,MAAM,CAVR/C,MAAM;UACNuB,GAAG,GASDwB,MAAM,CATRxB,GAAG;UACHyB,IAAI,GAQFD,MAAM,CARRC,IAAI;UACJxB,MAAM,GAOJuB,MAAM,CAPRvB,MAAM;UACGkB,aAAa,GAMpBK,MAAM,CANR9C,OAAO;UAAAgD,eAAA,GAMLF,MAAM,CALRzE,OAAO;UAAPA,OAAO,GAAA2E,eAAA,cAAG,IAAI,CAACvE,cAAc,GAAAuE,eAAA;UAAAC,oBAAA,GAK3BH,MAAM,CAJRJ,YAAY;UAAZA,YAAY,GAAAO,oBAAA,cAAG,IAAI,GAAAA,oBAAA;UACnBC,UAAU,GAGRJ,MAAM,CAHRI,UAAU;UACVC,cAAc,GAEZL,MAAM,CAFRK,cAAc;UACdC,KAAK,GACHN,MAAM,CADRM,KAAK;QAIP,IAAIrD,MAAM,KAAK,KAAK,IAAIqD,KAAK,YAALA,KAAK,CAAEC,OAAO,EAAE;UACtC,IAAMC,QAAQ,GAAGF,KAAK,CAACxE,GAAG,IAAI,GAAG0C,GAAG,IAAInB,IAAI,CAACC,SAAS,CAACmB,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE;UACtE,IAAMgC,cAAc,SACZC,gDAAuB,CAACC,oBAAoB,CAAIH,QAAQ,CAAC;UAEjE,IAAIC,cAAc,EAAE;YAClBJ,cAAc,YAAdA,cAAc,CAAG,mBAAmB,CAAC;YACrCD,UAAU,YAAVA,UAAU,CAAG;cAAEQ,MAAM,EAAE,GAAG;cAAEC,KAAK,EAAE,GAAG;cAAEC,UAAU,EAAE;YAAI,CAAC,CAAC;YAE1D,OAAO;cACLb,IAAI,EAAEQ,cAAc;cACpBM,MAAM,EAAE,GAAG;cACXC,UAAU,EAAE,aAAa;cACzB9D,OAAO,EAAE,CAAC;YACZ,CAAC;UACH;QACF;QAEA,IAAMwB,OAAO,GAAG,IAAI,CAACH,QAAQ,CAACC,GAAG,EAAEC,MAAM,CAAC;QAC1C,IAAMvB,OAAO,GAAG,IAAI,CAACwC,YAAY,CAACC,aAAa,EAAEC,YAAY,CAAC;QAE9D,IAAMqB,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;QACxC,IAAMC,SAAS,GAAGC,UAAU,CAAC;UAAA,OAAMH,UAAU,CAACI,KAAK,CAAC,CAAC;QAAA,GAAE9F,OAAO,CAAC;QAE/D,IAAI;UAEF8E,cAAc,YAAdA,cAAc,CAAG,sBAAsB,CAAC;UAExC,IAAMiB,WAAwB,GAAG;YAC/BrE,MAAM,EAANA,MAAM;YACNC,OAAO,EAAPA,OAAO;YACPqE,MAAM,EAAEN,UAAU,CAACM;UACrB,CAAC;UAED,IAAItB,IAAI,IAAIhD,MAAM,KAAK,KAAK,EAAE;YAC5BqE,WAAW,CAAClE,IAAI,GAAGC,IAAI,CAACC,SAAS,CAAC2C,IAAI,CAAC;UACzC;UAGAI,cAAc,YAAdA,cAAc,CAAG,oBAAoB,CAAC;UACtCD,UAAU,YAAVA,UAAU,CAAG;YAAEQ,MAAM,EAAE,CAAC;YAAEC,KAAK,EAAE,GAAG;YAAEC,UAAU,EAAE;UAAE,CAAC,CAAC;UAEtD,IAAMU,QAAQ,SAASxE,KAAK,CAAC0B,OAAO,EAAE4C,WAAW,CAAC;UAClDG,YAAY,CAACN,SAAS,CAAC;UAGvBd,cAAc,YAAdA,cAAc,CAAG,wBAAwB,CAAC;UAC1CD,UAAU,YAAVA,UAAU,CAAG;YAAEQ,MAAM,EAAE,EAAE;YAAEC,KAAK,EAAE,GAAG;YAAEC,UAAU,EAAE;UAAG,CAAC,CAAC;UAExD,IAAIY,YAAe;UACnB,IAAMC,WAAW,GAAGH,QAAQ,CAACtE,OAAO,CAAC0E,GAAG,CAAC,cAAc,CAAC;UAGxDxB,UAAU,YAAVA,UAAU,CAAG;YAAEQ,MAAM,EAAE,EAAE;YAAEC,KAAK,EAAE,GAAG;YAAEC,UAAU,EAAE;UAAG,CAAC,CAAC;UAExD,IAAIa,WAAW,IAAIA,WAAW,CAACnC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;YAC3DkC,YAAY,SAASF,QAAQ,CAAC9D,IAAI,CAAC,CAAC;UACtC,CAAC,MAAM;YACLgE,YAAY,SAAUF,QAAQ,CAACK,IAAI,CAAC,CAAkB;UACxD;UAEA,IAAI,CAACL,QAAQ,CAAChE,EAAE,EAAE;YAChB6C,cAAc,YAAdA,cAAc,CAAG,gBAAgB,CAAC;YAGlC,IAAImB,QAAQ,CAACT,MAAM,KAAK,GAAG,IAAInB,YAAY,EAAE;cAC3CtD,OAAO,CAACC,IAAI,CACV,4DACF,CAAC;cAED,IAAI;gBAEF,MAAM,IAAI,CAAC+B,gBAAgB,CAAC,CAAC;gBAG7BhC,OAAO,CAAC6B,GAAG,CAAC,kDAAkD,CAAC;gBAC/D,IAAM2D,YAAY,GAAG,IAAI,CAACpC,YAAY,CAACC,aAAa,EAAEC,YAAY,CAAC;gBACnE,IAAMmC,aAAa,SAAS/E,KAAK,CAAC0B,OAAO,EAAAE,MAAA,CAAAiB,MAAA,KACpCyB,WAAW;kBACdpE,OAAO,EAAE4E;gBAAY,EACtB,CAAC;gBAEF,IAAIC,aAAa,CAACvE,EAAE,EAAE;kBACpB,IAAMwE,SAAS,SAASD,aAAa,CAACrE,IAAI,CAAC,CAAC;kBAC5CpB,OAAO,CAAC6B,GAAG,CAAC,8CAA8C,CAAC;kBAC3D,OAAO;oBACL8B,IAAI,EAAE+B,SAAS;oBACfjB,MAAM,EAAEgB,aAAa,CAAChB,MAAM;oBAC5BC,UAAU,EAAEe,aAAa,CAACf,UAAU;oBACpC9D,OAAO,EAAE0B,MAAM,CAACqD,WAAW,CAACF,aAAa,CAAC7E,OAAO,CAAC8B,OAAO,CAAC,CAAC;kBAC7D,CAAC;gBACH;cACF,CAAC,CAAC,OAAOkD,YAAY,EAAE;gBACrB5F,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAE6F,YAAY,CAAC;cAE7D;YACF;YAEA,MAAM;cACJ9D,OAAO,EAAEsD,YAAY,IAAIF,QAAQ,CAACR,UAAU;cAC5CD,MAAM,EAAES,QAAQ,CAACT,MAAM;cACvBoB,OAAO,EAAET;YACX,CAAC;UACH;UAGArB,cAAc,YAAdA,cAAc,CAAG,mBAAmB,CAAC;UACrCD,UAAU,YAAVA,UAAU,CAAG;YAAEQ,MAAM,EAAE,GAAG;YAAEC,KAAK,EAAE,GAAG;YAAEC,UAAU,EAAE;UAAI,CAAC,CAAC;UAG1D,IAAI7D,MAAM,KAAK,KAAK,IAAIqD,KAAK,YAALA,KAAK,CAAEC,OAAO,IAAIiB,QAAQ,CAAChE,EAAE,EAAE;YACrD,IAAMgD,SAAQ,GAAGF,KAAK,CAACxE,GAAG,IAAI,GAAG0C,GAAG,IAAInB,IAAI,CAACC,SAAS,CAACmB,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE;YACtE,MAAMiC,gDAAuB,CAAC0B,gBAAgB,CAAC5B,SAAQ,EAAEkB,YAAY,EAAE;cACrEW,GAAG,EAAE/B,KAAK,CAAC+B,GAAG;cACdC,QAAQ,EAAEhC,KAAK,CAACgC;YAClB,CAAC,CAAC;UACJ;UAEA,OAAO;YACLrC,IAAI,EAAEyB,YAAY;YAClBX,MAAM,EAAES,QAAQ,CAACT,MAAM;YACvBC,UAAU,EAAEQ,QAAQ,CAACR,UAAU;YAC/B9D,OAAO,EAAE0B,MAAM,CAACqD,WAAW,CAACT,QAAQ,CAACtE,OAAO,CAAC8B,OAAO,CAAC,CAAC;UACxD,CAAC;QACH,CAAC,CAAC,OAAO3C,KAAU,EAAE;UACnBoF,YAAY,CAACN,SAAS,CAAC;UAEvB,IAAI9E,KAAK,CAACkG,IAAI,KAAK,YAAY,EAAE;YAC/B,MAAM;cACJnE,OAAO,EAAE,iBAAiB;cAC1B2C,MAAM,EAAE;YACV,CAAC;UACH;UAEA,IAAI1E,KAAK,CAAC0E,MAAM,EAAE;YAChB,MAAM1E,KAAK;UACb;UAEA,MAAM;YACJ+B,OAAO,EAAE/B,KAAK,CAAC+B,OAAO,IAAI,eAAe;YACzC2C,MAAM,EAAE,CAAC;YACToB,OAAO,EAAE9F;UACX,CAAC;QACH;MACF,CAAC;MAAA,SA/JamG,WAAWA,CAAAC,EAAA;QAAA,OAAA1C,YAAA,CAAAvD,KAAA,OAAApB,SAAA;MAAA;MAAA,OAAXoH,WAAW;IAAA;EAAA;IAAA1G,GAAA;IAAAC,KAAA;MAAA,IAAA2G,IAAA,OAAAzG,kBAAA,CAAAR,OAAA,EAoKzB,WACE+C,GAAW,EACXC,MAA4B,EAGH;QAAA,IAAAkE,qBAAA;QAAA,IAFzB/C,YAAqB,GAAAxE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;QAAA,IAC5BwH,YAAsE,GAAAxH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;QAEtE,OAAO,IAAI,CAACkH,WAAW,CAAI;UACzBvF,MAAM,EAAE,KAAK;UACbuB,GAAG,EAAHA,GAAG;UACHC,MAAM,EAANA,MAAM;UACNmB,YAAY,EAAZA,YAAY;UACZU,KAAK,EAAE;YACLC,OAAO,GAAAoC,qBAAA,GAAEC,YAAY,oBAAZA,YAAY,CAAErC,OAAO,YAAAoC,qBAAA,GAAI,IAAI;YACtCN,GAAG,EAAEO,YAAY,oBAAZA,YAAY,CAAEP,GAAG;YACtBC,QAAQ,EAAEM,YAAY,oBAAZA,YAAY,CAAEN;UAC1B;QACF,CAAC,CAAC;MACJ,CAAC;MAAA,SAjBKV,GAAGA,CAAAiB,GAAA,EAAAC,GAAA;QAAA,OAAAJ,IAAA,CAAAlG,KAAA,OAAApB,SAAA;MAAA;MAAA,OAAHwG,GAAG;IAAA;EAAA;IAAA9F,GAAA;IAAAC,KAAA;MAAA,IAAAgH,KAAA,OAAA9G,kBAAA,CAAAR,OAAA,EAsBT,WACE+C,GAAW,EACXyB,IAAU,EAEe;QAAA,IADzBL,YAAqB,GAAAxE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;QAE5B,OAAO,IAAI,CAACoH,WAAW,CAAI;UAAEvF,MAAM,EAAE,MAAM;UAAEuB,GAAG,EAAHA,GAAG;UAAEyB,IAAI,EAAJA,IAAI;UAAEL,YAAY,EAAZA;QAAa,CAAC,CAAC;MACzE,CAAC;MAAA,SANKoD,IAAIA,CAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAH,KAAA,CAAAvG,KAAA,OAAApB,SAAA;MAAA;MAAA,OAAJ4H,IAAI;IAAA;EAAA;IAAAlH,GAAA;IAAAC,KAAA;MAAA,IAAAoH,IAAA,OAAAlH,kBAAA,CAAAR,OAAA,EAWV,WACE+C,GAAW,EACXyB,IAAU,EAEe;QAAA,IADzBL,YAAqB,GAAAxE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;QAE5B,OAAO,IAAI,CAACoH,WAAW,CAAI;UAAEvF,MAAM,EAAE,KAAK;UAAEuB,GAAG,EAAHA,GAAG;UAAEyB,IAAI,EAAJA,IAAI;UAAEL,YAAY,EAAZA;QAAa,CAAC,CAAC;MACxE,CAAC;MAAA,SANKwD,GAAGA,CAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAH,IAAA,CAAA3G,KAAA,OAAApB,SAAA;MAAA;MAAA,OAAHgI,GAAG;IAAA;EAAA;IAAAtH,GAAA;IAAAC,KAAA;MAAA,IAAAwH,MAAA,OAAAtH,kBAAA,CAAAR,OAAA,EAWT,WACE+C,GAAW,EACXyB,IAAU,EAEe;QAAA,IADzBL,YAAqB,GAAAxE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;QAE5B,OAAO,IAAI,CAACoH,WAAW,CAAI;UAAEvF,MAAM,EAAE,OAAO;UAAEuB,GAAG,EAAHA,GAAG;UAAEyB,IAAI,EAAJA,IAAI;UAAEL,YAAY,EAAZA;QAAa,CAAC,CAAC;MAC1E,CAAC;MAAA,SANK4D,KAAKA,CAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAH,MAAA,CAAA/G,KAAA,OAAApB,SAAA;MAAA;MAAA,OAALoI,KAAK;IAAA;EAAA;IAAA1H,GAAA;IAAAC,KAAA;MAAA,IAAA4H,QAAA,OAAA1H,kBAAA,CAAAR,OAAA,EAWX,WACE+C,GAAW,EAEc;QAAA,IADzBoB,YAAqB,GAAAxE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;QAE5B,OAAO,IAAI,CAACoH,WAAW,CAAI;UAAEvF,MAAM,EAAE,QAAQ;UAAEuB,GAAG,EAAHA,GAAG;UAAEoB,YAAY,EAAZA;QAAa,CAAC,CAAC;MACrE,CAAC;MAAA,SALKgE,OAAMA,CAAAC,GAAA;QAAA,OAAAF,QAAA,CAAAnH,KAAA,OAAApB,SAAA;MAAA;MAAA,OAANwI,OAAM;IAAA;EAAA;AAAA;AASP,IAAME,SAAS,GAAAC,OAAA,CAAAD,SAAA,GAAG,IAAI5I,SAAS,CAAC,CAAC;AAAC,IAAA8I,QAAA,GAAAD,OAAA,CAAAtI,OAAA,GAC1BqI,SAAS", "ignoreList": []}