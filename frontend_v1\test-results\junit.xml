<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="655" failures="118" errors="0" time="72.419">
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-08-02T23:07:34" time="0.301" tests="48">
    <testcase classname="Test Setup" name="should configure test environment" time="0.003">
    </testcase>
    <testcase classname="Accessibility Utilities › WCAG_CONSTANTS" name="defines correct contrast ratios" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › WCAG_CONSTANTS" name="defines correct touch target sizes" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › WCAG_CONSTANTS" name="defines correct animation timing" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ScreenReaderUtils › generateFormFieldLabel" name="generates basic label" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › ScreenReaderUtils › generateFormFieldLabel" name="adds required indicator" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ScreenReaderUtils › generateFormFieldLabel" name="adds error message" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ScreenReaderUtils › generateFormFieldLabel" name="combines required and error" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ScreenReaderUtils › generateInteractionHint" name="generates basic hint" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › ScreenReaderUtils › generateInteractionHint" name="adds additional info" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › getRelativeLuminance" name="calculates luminance for white" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › getRelativeLuminance" name="calculates luminance for black" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › getRelativeLuminance" name="calculates luminance for gray" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › getContrastRatio" name="calculates maximum contrast ratio" time="0.002">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › getContrastRatio" name="calculates minimum contrast ratio" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › getContrastRatio" name="calculates intermediate contrast ratio" time="0.005">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › meetsWCAGAA" name="passes for high contrast combinations" time="0.002">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › meetsWCAGAA" name="fails for low contrast combinations" time="0.002">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › meetsWCAGAA" name="uses different thresholds for large text" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › suggestAccessibleColor" name="returns null for already accessible combinations" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › suggestAccessibleColor" name="suggests darker color for low contrast" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateHeadingProps" name="generates heading props" time="0.002">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateHeadingProps" name="handles different heading levels" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateListProps" name="generates list props" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateListItemProps" name="generates list item props" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateButtonProps" name="generates basic button props" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateButtonProps" name="includes action hint" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateButtonProps" name="includes state" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateInputProps" name="generates basic input props" time="0.003">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateInputProps" name="includes value" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateInputProps" name="includes required and error info" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityTestUtils › validateAccessibilityProps" name="passes for well-formed interactive element" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityTestUtils › validateAccessibilityProps" name="flags missing accessibility role" time="0.002">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityTestUtils › validateAccessibilityProps" name="flags missing accessibility label" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityTestUtils › validateAccessibilityProps" name="flags small touch targets" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityTestUtils › validateAccessibilityProps" name="passes for adequate touch targets" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityTestUtils › generateAccessibilityReport" name="generates report for component tree" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityTestUtils › generateAccessibilityReport" name="handles empty component tree" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AdvancedAccessibilityUtils › announceLiveRegion" name="should announce with polite priority" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AdvancedAccessibilityUtils › announceLiveRegion" name="should announce with assertive priority" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AdvancedAccessibilityUtils › announceContentChange" name="should announce content changes" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AdvancedAccessibilityUtils › announceFormValidation" name="should announce valid form field" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AdvancedAccessibilityUtils › announceFormValidation" name="should announce invalid form field" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AdvancedAccessibilityUtils › announceProgress" name="should announce progress updates" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AdvancedAccessibilityUtils › announceLoadingState" name="should announce loading start" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AdvancedAccessibilityUtils › announceLoadingState" name="should announce loading complete" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityMonitoringUtils › trackAccessibilityUsage" name="should track accessibility usage statistics" time="0.015">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityMonitoringUtils › validateCompliance" name="should validate accessibility compliance" time="0.003">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="15" skipped="0" timestamp="2025-08-02T23:07:35" time="0.514" tests="16">
    <testcase classname="Test Setup" name="should configure test environment" time="0.042">
    </testcase>
    <testcase classname="Customer Home Flow Integration › Complete User Journey" name="loads home screen and displays all data correctly" time="0.026">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:137:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Complete User Journey" name="handles category selection flow" time="0.013">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:177:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Complete User Journey" name="handles provider selection flow" time="0.004">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:214:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Error Handling Integration" name="handles service failures gracefully" time="0.004">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:245:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Error Handling Integration" name="handles partial service failures" time="0.005">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:269:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Error Handling Integration" name="handles retry functionality" time="0.007">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:296:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Performance Integration" name="tracks performance metrics during normal flow" time="0.005">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:322:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Performance Integration" name="handles performance under load" time="0.004">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:362:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Cache Integration" name="uses cached data when available" time="0.005">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:402:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Cache Integration" name="falls back to API when cache is expired" time="0.033">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:433:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Cache Integration" name="updates cache after successful API calls" time="0.004">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:448:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Refresh Integration" name="handles pull-to-refresh correctly" time="0.004">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:473:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Refresh Integration" name="shows refreshing state during refresh" time="0.003">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:506:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Memory and Resource Management" name="cleans up resources on unmount" time="0.005">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:536:33)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Memory and Resource Management" name="handles multiple rapid re-renders efficiently" time="0.003">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:556:34)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-08-02T23:07:36" time="0.237" tests="24">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="PasswordlessAuthService › Magic Link Authentication › sendMagicLink" name="should send magic link successfully" time="0.006">
    </testcase>
    <testcase classname="PasswordlessAuthService › Magic Link Authentication › sendMagicLink" name="should reject invalid email format" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Magic Link Authentication › sendMagicLink" name="should enforce rate limiting" time="0.004">
    </testcase>
    <testcase classname="PasswordlessAuthService › Magic Link Authentication › verifyMagicLink" name="should verify valid magic link" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Magic Link Authentication › verifyMagicLink" name="should reject expired magic link" time="0.002">
    </testcase>
    <testcase classname="PasswordlessAuthService › Magic Link Authentication › verifyMagicLink" name="should reject already used magic link" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Magic Link Authentication › verifyMagicLink" name="should reject invalid token" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › SMS OTP Authentication › sendOTP" name="should send OTP successfully" time="0.002">
    </testcase>
    <testcase classname="PasswordlessAuthService › SMS OTP Authentication › sendOTP" name="should reject invalid phone format" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › SMS OTP Authentication › sendOTP" name="should enforce rate limiting for phone numbers" time="0.004">
    </testcase>
    <testcase classname="PasswordlessAuthService › SMS OTP Authentication › verifyOTP" name="should verify correct OTP" time="0.002">
    </testcase>
    <testcase classname="PasswordlessAuthService › SMS OTP Authentication › verifyOTP" name="should reject incorrect OTP with attempt tracking" time="0.002">
    </testcase>
    <testcase classname="PasswordlessAuthService › SMS OTP Authentication › verifyOTP" name="should reject expired OTP" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › SMS OTP Authentication › verifyOTP" name="should block after max attempts" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Biometric Authentication › authenticateWithBiometrics" name="should authenticate successfully with biometrics" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Biometric Authentication › authenticateWithBiometrics" name="should fail when no biometric hardware available" time="0">
    </testcase>
    <testcase classname="PasswordlessAuthService › Biometric Authentication › authenticateWithBiometrics" name="should fail when no biometric credentials enrolled" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Biometric Authentication › authenticateWithBiometrics" name="should fail when biometric authentication fails" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Biometric Authentication › authenticateWithBiometrics" name="should fail when no biometric user data found" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Biometric Authentication › setupBiometricAuth" name="should setup biometric authentication successfully" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Biometric Authentication › isBiometricSetup" name="should return true when biometric is setup" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Biometric Authentication › isBiometricSetup" name="should return false when biometric is not setup" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Utility Methods › clearAuthData" name="should clear all authentication data" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="23" skipped="0" timestamp="2025-08-02T23:07:36" time="1.536" tests="26">
    <testcase classname="Test Setup" name="should configure test environment" time="0.003">
    </testcase>
    <testcase classname="EnhancedTouchTarget › Basic Rendering" name="should render children correctly" time="0.055">
      <failure>Error: Unable to find an element with text: Touch me
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\EnhancedTouchTarget.test.tsx:51:14)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="EnhancedTouchTarget › Basic Rendering" name="should apply custom styles" time="0.003">
      <failure>Error: Unable to find an element with testID: touch-target
    at Object.getByTestId (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\EnhancedTouchTarget.test.tsx:65:27)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="EnhancedTouchTarget › Basic Rendering" name="should be disabled when disabled prop is true" time="0.002">
      <failure>Error: Unable to find an element with testID: touch-target
    at Object.getByTestId (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\EnhancedTouchTarget.test.tsx:85:27)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="EnhancedTouchTarget › Touch Target Size Enforcement" name="should enforce minimum size when enforceMinimumSize is true" time="0.002">
      <failure>Error: Unable to find an element with testID: touch-target
    at Object.getByTestId (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\EnhancedTouchTarget.test.tsx:105:27)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="EnhancedTouchTarget › Touch Target Size Enforcement" name="should use custom minimum size" time="0.002">
      <failure>Error: Unable to find an element with testID: touch-target
    at Object.getByTestId (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\EnhancedTouchTarget.test.tsx:130:27)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="EnhancedTouchTarget › Touch Target Size Enforcement" name="should not enforce minimum size when enforceMinimumSize is false" time="0.001">
      <failure>Error: Unable to find an element with testID: touch-target
    at Object.getByTestId (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\EnhancedTouchTarget.test.tsx:150:27)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="EnhancedTouchTarget › Touch Feedback" name="should show touch feedback when enabled" time="0.002">
      <failure>Error: Unable to find an element with testID: touch-target
    at Object.getByTestId (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\EnhancedTouchTarget.test.tsx:175:27)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="EnhancedTouchTarget › Touch Feedback" name="should not show touch feedback when disabled" time="0.002">
      <failure>Error: Unable to find an element with testID: touch-target
    at Object.getByTestId (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\EnhancedTouchTarget.test.tsx:192:27)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="EnhancedTouchTarget › Touch Feedback" name="should use custom feedback color" time="0.006">
      <failure>Error: Unable to find an element with testID: touch-target
    at Object.getByTestId (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\EnhancedTouchTarget.test.tsx:213:27)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="EnhancedTouchTarget › Press Handling" name="should call onPress when pressed" time="0.002">
      <failure>Error: Unable to find an element with testID: touch-target
    at Object.getByTestId (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\EnhancedTouchTarget.test.tsx:235:27)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="EnhancedTouchTarget › Press Handling" name="should call onLongPress when long pressed" time="0.001">
      <failure>Error: Unable to find an element with testID: touch-target
    at Object.getByTestId (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\EnhancedTouchTarget.test.tsx:254:27)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="EnhancedTouchTarget › Press Handling" name="should call onPressIn and onPressOut" time="0.001">
      <failure>Error: Unable to find an element with testID: touch-target
    at Object.getByTestId (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\EnhancedTouchTarget.test.tsx:275:27)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="EnhancedTouchTarget › Accessibility" name="should have proper accessibility attributes" time="0.001">
      <failure>Error: Unable to find an element with testID: touch-target
    at Object.getByTestId (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\EnhancedTouchTarget.test.tsx:298:27)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="EnhancedTouchTarget › Accessibility" name="should be accessible by default" time="0.001">
      <failure>Error: Unable to find an element with testID: touch-target
    at Object.getByTestId (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\EnhancedTouchTarget.test.tsx:313:27)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="EnhancedTouchTarget › Accessibility" name="should handle focus states" time="0.001">
      <failure>Error: Unable to find an element with testID: touch-target
    at Object.getByTestId (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\EnhancedTouchTarget.test.tsx:326:27)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="EnhancedTouchTarget › Swipe Gestures" name="should handle swipe gestures when enabled" time="0.001">
      <failure>Error: Unable to find an element with testID: touch-target
    at Object.getByTestId (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\EnhancedTouchTarget.test.tsx:356:27)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="EnhancedTouchTarget › Swipe Gestures" name="should not handle swipe gestures when disabled" time="0.003">
      <failure>Error: Unable to find an element with testID: touch-target
    at Object.getByTestId (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\EnhancedTouchTarget.test.tsx:381:27)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="EnhancedTouchTarget › Auto Focus" name="should auto focus when autoFocus is true" time="1.009">
      <failure>Error: Unable to find an element with testID: touch-target
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\EnhancedTouchTarget.test.tsx:398:20)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="EnhancedTouchTarget › Development Mode" name="should show size indicator in development mode" time="0.002">
      <failure>Error: Unable to find an element with testID: size-indicator
    at Object.getByTestId (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\EnhancedTouchTarget.test.tsx:424:14)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="EnhancedTouchTarget › Development Mode" name="should not show size indicator in production mode" time="0.002">
    </testcase>
    <testcase classname="AccessibleIconButton › Rendering" name="should render icon and label correctly" time="0.002">
      <failure>Error: Unable to find an element with text: Home
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\EnhancedTouchTarget.test.tsx:462:14)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AccessibleIconButton › Rendering" name="should not show label when showLabel is false" time="0.002">
    </testcase>
    <testcase classname="AccessibleIconButton › Rendering" name="should position label correctly" time="0.002">
      <failure>Error: Unable to find an element with text: Home
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\EnhancedTouchTarget.test.tsx:492:21)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AccessibleIconButton › Accessibility" name="should use label as accessibility label" time="0.005">
      <failure>Error: Unable to find an element with testID: icon-button
    at Object.getByTestId (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\EnhancedTouchTarget.test.tsx:509:22)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AccessibleIconButton › Accessibility" name="should use custom accessibility label when provided" time="0.002">
      <failure>Error: Unable to find an element with testID: icon-button
    at Object.getByTestId (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\EnhancedTouchTarget.test.tsx:525:22)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="4" skipped="0" timestamp="2025-08-02T23:07:39" time="40.306" tests="30">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="CacheService › Basic Cache Operations" name="sets and gets data from memory cache" time="0.012">
    </testcase>
    <testcase classname="CacheService › Basic Cache Operations" name="returns null for non-existent keys" time="0.001">
    </testcase>
    <testcase classname="CacheService › Basic Cache Operations" name="removes data from cache" time="0.002">
    </testcase>
    <testcase classname="CacheService › Basic Cache Operations" name="clears all cache data" time="0.001">
    </testcase>
    <testcase classname="CacheService › TTL and Expiration" name="respects TTL for cache entries" time="10.016">
      <failure>Error: thrown: &quot;Exceeded timeout of 10000 ms for a test.
Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout.&quot;
    at it (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\cacheService.test.ts:93:5)
    at _dispatchDescribe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\index.js:91:26)
    at describe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\index.js:55:5)
    at describe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\cacheService.test.ts:92:3)
    at _dispatchDescribe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\index.js:91:26)
    at describe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\index.js:55:5)
    at Object.describe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\cacheService.test.ts:33:1)
    at Runtime._execModule (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runtime\build\index.js:1439:24)
    at Runtime._loadModule (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runtime\build\index.js:1022:12)
    at Runtime.requireModule (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runtime\build\index.js:882:12)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:77:13)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="CacheService › TTL and Expiration" name="uses default TTL when not specified" time="0.002">
    </testcase>
    <testcase classname="CacheService › TTL and Expiration" name="updates TTL on cache hit" time="10.016">
      <failure>Error: thrown: &quot;Exceeded timeout of 10000 ms for a test.
Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout.&quot;
    at it (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\cacheService.test.ts:122:5)
    at _dispatchDescribe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\index.js:91:26)
    at describe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\index.js:55:5)
    at describe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\cacheService.test.ts:92:3)
    at _dispatchDescribe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\index.js:91:26)
    at describe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\index.js:55:5)
    at Object.describe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\cacheService.test.ts:33:1)
    at Runtime._execModule (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runtime\build\index.js:1439:24)
    at Runtime._loadModule (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runtime\build\index.js:1022:12)
    at Runtime.requireModule (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runtime\build\index.js:882:12)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:77:13)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="CacheService › Storage Cache Integration" name="falls back to storage cache when memory cache misses" time="0.002">
    </testcase>
    <testcase classname="CacheService › Storage Cache Integration" name="stores data in both memory and storage by default" time="0.003">
    </testcase>
    <testcase classname="CacheService › Storage Cache Integration" name="supports memory-only storage option" time="0.002">
    </testcase>
    <testcase classname="CacheService › Storage Cache Integration" name="supports storage-only option" time="0.001">
    </testcase>
    <testcase classname="CacheService › Memory Management" name="enforces memory limits" time="0.002">
    </testcase>
    <testcase classname="CacheService › Memory Management" name="uses LFU + LRU eviction strategy" time="10.005">
      <failure>Error: thrown: &quot;Exceeded timeout of 10000 ms for a test.
Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout.&quot;
    at it (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\cacheService.test.ts:227:5)
    at _dispatchDescribe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\index.js:91:26)
    at describe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\index.js:55:5)
    at describe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\cacheService.test.ts:208:3)
    at _dispatchDescribe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\index.js:91:26)
    at describe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\index.js:55:5)
    at Object.describe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\cacheService.test.ts:33:1)
    at Runtime._execModule (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runtime\build\index.js:1439:24)
    at Runtime._loadModule (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runtime\build\index.js:1022:12)
    at Runtime.requireModule (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runtime\build\index.js:882:12)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:77:13)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="CacheService › Cache Statistics" name="tracks cache hits and misses" time="0.003">
    </testcase>
    <testcase classname="CacheService › Cache Statistics" name="calculates hit rate correctly" time="0.002">
    </testcase>
    <testcase classname="CacheService › Cache Statistics" name="tracks entry count and total size" time="0.002">
    </testcase>
    <testcase classname="CacheService › Cache Preloading" name="preloads multiple entries" time="0.002">
    </testcase>
    <testcase classname="CacheService › Cache Preloading" name="handles preload failures gracefully" time="0.002">
    </testcase>
    <testcase classname="CacheService › Pattern-based Invalidation" name="invalidates entries matching pattern" time="0.001">
    </testcase>
    <testcase classname="CacheService › Pattern-based Invalidation" name="invalidates storage entries matching pattern" time="0.001">
    </testcase>
    <testcase classname="CacheService › Entry Information" name="provides entry metadata" time="0.002">
    </testcase>
    <testcase classname="CacheService › Entry Information" name="returns null for non-existent entries" time="0.001">
    </testcase>
    <testcase classname="CacheService › Entry Information" name="updates access count on cache hits" time="0">
    </testcase>
    <testcase classname="CacheService › Error Handling" name="handles AsyncStorage errors gracefully" time="0.003">
    </testcase>
    <testcase classname="CacheService › Error Handling" name="handles set errors gracefully" time="0.003">
    </testcase>
    <testcase classname="CacheService › Error Handling" name="handles remove errors gracefully" time="0.002">
    </testcase>
    <testcase classname="CacheService › Error Handling" name="handles clear errors gracefully" time="0.003">
    </testcase>
    <testcase classname="CacheService › Service Lifecycle" name="destroys service correctly" time="0.003">
    </testcase>
    <testcase classname="CacheService › Service Lifecycle" name="cleans up expired entries periodically" time="10.012">
      <failure>Error: thrown: &quot;Exceeded timeout of 10000 ms for a test.
Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout.&quot;
    at it (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\cacheService.test.ts:458:5)
    at _dispatchDescribe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\index.js:91:26)
    at describe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\index.js:55:5)
    at describe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\cacheService.test.ts:447:3)
    at _dispatchDescribe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\index.js:91:26)
    at describe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\index.js:55:5)
    at Object.describe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\cacheService.test.ts:33:1)
    at Runtime._execModule (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runtime\build\index.js:1439:24)
    at Runtime._loadModule (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runtime\build\index.js:1022:12)
    at Runtime.requireModule (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runtime\build\index.js:882:12)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:77:13)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="23" skipped="0" timestamp="2025-08-02T23:08:19" time="10.47" tests="26">
    <testcase classname="Test Setup" name="should configure test environment" time="0.003">
    </testcase>
    <testcase classname="useErrorHandling › Basic Error Handling" name="initializes with no error state" time="0.018">
    </testcase>
    <testcase classname="useErrorHandling › Basic Error Handling" name="handles string errors" time="0.008">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: &quot;Test error message&quot;
Received: undefined
    at Object.toBe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:71:45)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Basic Error Handling" name="handles Error objects" time="0.004">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: &quot;Test error object&quot;
Received: undefined
    at Object.toBe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:84:45)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Basic Error Handling" name="clears error state" time="0.003">
    </testcase>
    <testcase classname="useErrorHandling › Retry Logic" name="increments retry count on retry" time="10.006">
      <failure>Error: thrown: &quot;Exceeded timeout of 10000 ms for a test.
Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout.&quot;
    at it (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:108:5)
    at _dispatchDescribe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\index.js:91:26)
    at describe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\index.js:55:5)
    at describe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:107:3)
    at _dispatchDescribe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\index.js:91:26)
    at describe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\index.js:55:5)
    at Object.describe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:43:1)
    at Runtime._execModule (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runtime\build\index.js:1439:24)
    at Runtime._loadModule (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runtime\build\index.js:1022:12)
    at Runtime.requireModule (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runtime\build\index.js:882:12)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:77:13)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Retry Logic" name="respects max retries limit" time="0.001">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:126:36)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Retry Logic" name="uses progressive retry delays" time="0.003">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:157:36)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Retry Logic" name="uses fixed retry delay when progressive is disabled" time="0.001">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:184:36)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Error Classification" name="identifies network errors" time="0.001">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:211:36)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Error Classification" name="identifies server errors" time="0.001">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:223:36)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Error Classification" name="identifies authentication errors" time="0.001">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:235:36)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Error Classification" name="provides appropriate error messages" time="0.001">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:247:36)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Callbacks and Options" name="calls onError callback" time="0.001">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:279:36)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Callbacks and Options" name="calls onRetry callback" time="0.002">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:294:36)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Callbacks and Options" name="calls onMaxRetriesExceeded callback" time="0.001">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:310:36)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Callbacks and Options" name="respects error reporting setting" time="0.001">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:330:36)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Performance Tracking Integration" name="tracks error handling performance" time="0.002">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:346:36)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Performance Tracking Integration" name="tracks retry performance" time="0.002">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:363:36)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › App State Integration" name="retries on app focus when enabled" time="0.002">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:387:36)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › App State Integration" name="does not retry on app focus when disabled" time="0.001">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:420:36)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Cleanup" name="cleans up timeouts on unmount" time="0.002">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:440:45)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Cleanup" name="removes app state listeners on unmount" time="0.001">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:463:37)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Edge Cases" name="handles retry when no error exists" time="0.001">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:475:36)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Edge Cases" name="handles retry when max retries already exceeded" time="0.002">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:486:36)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Edge Cases" name="handles component unmount during retry delay" time="0.002">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:512:45)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-08-02T23:08:30" time="0.17" tests="16">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="Core Integration Tests › API Client Integration" name="should handle successful API requests" time="0.002">
    </testcase>
    <testcase classname="Core Integration Tests › API Client Integration" name="should handle API request failures" time="0.01">
    </testcase>
    <testcase classname="Core Integration Tests › API Client Integration" name="should handle different HTTP methods" time="0.001">
    </testcase>
    <testcase classname="Core Integration Tests › Booking Flow Integration" name="should complete booking creation flow" time="0.002">
    </testcase>
    <testcase classname="Core Integration Tests › Booking Flow Integration" name="should handle booking status updates" time="0.001">
    </testcase>
    <testcase classname="Core Integration Tests › Payment Integration" name="should handle payment intent creation" time="0.001">
    </testcase>
    <testcase classname="Core Integration Tests › Payment Integration" name="should handle payment confirmation" time="0">
    </testcase>
    <testcase classname="Core Integration Tests › Payment Integration" name="should handle payment method management" time="0.005">
    </testcase>
    <testcase classname="Core Integration Tests › Provider Service Management" name="should handle service creation" time="0.002">
    </testcase>
    <testcase classname="Core Integration Tests › Provider Service Management" name="should handle provider booking management" time="0.001">
    </testcase>
    <testcase classname="Core Integration Tests › Error Handling and Recovery" name="should handle network errors gracefully" time="0.002">
    </testcase>
    <testcase classname="Core Integration Tests › Error Handling and Recovery" name="should handle authentication errors" time="0.001">
    </testcase>
    <testcase classname="Core Integration Tests › Error Handling and Recovery" name="should handle validation errors" time="0.001">
    </testcase>
    <testcase classname="Core Integration Tests › Error Handling and Recovery" name="should handle server errors" time="0.001">
    </testcase>
    <testcase classname="Core Integration Tests › Data Flow Integration" name="should handle complete customer journey data flow" time="0.002">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-08-02T23:08:30" time="0.224" tests="33">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="TestAccountsService › Test Mode Management" name="should return true for test mode in development" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Test Mode Management" name="should return false for test mode in production" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Test Mode Management" name="should set test mode enabled state" time="0.003">
    </testcase>
    <testcase classname="TestAccountsService › Test Mode Management" name="should not set test mode in production" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Account Retrieval" name="should return all test accounts" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Account Retrieval" name="should return customer accounts only" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Account Retrieval" name="should return service provider accounts only" time="0.003">
    </testcase>
    <testcase classname="TestAccountsService › Account Retrieval" name="should return providers by category" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Account Retrieval" name="should return random account" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Account Retrieval" name="should return random account by role" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Account Retrieval" name="should find account by email" time="0">
    </testcase>
    <testcase classname="TestAccountsService › Account Retrieval" name="should return undefined for non-existent email" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Quick Access Functions" name="should return quick login accounts" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Quick Access Functions" name="should return account statistics" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Login Functionality" name="should successfully login with test account" time="0.002">
    </testcase>
    <testcase classname="TestAccountsService › Login Functionality" name="should handle login failure" time="0">
    </testcase>
    <testcase classname="TestAccountsService › Login Functionality" name="should not login when test mode is disabled" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Login Functionality" name="should perform quick login" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Login Functionality" name="should login with random account" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Storage Management" name="should store last test account" time="0">
    </testcase>
    <testcase classname="TestAccountsService › Storage Management" name="should retrieve last test account" time="0">
    </testcase>
    <testcase classname="TestAccountsService › Storage Management" name="should return null when no last account stored" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Storage Management" name="should clear test account data" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Account Validation" name="should validate correct test account credentials" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Account Validation" name="should return null for invalid credentials" time="0">
    </testcase>
    <testcase classname="TestAccountsService › Account Validation" name="should return null for correct email but wrong password" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Scenario-based Account Selection" name="should return appropriate accounts for booking scenario" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Scenario-based Account Selection" name="should return appropriate accounts for messaging scenario" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Scenario-based Account Selection" name="should return appropriate accounts for payments scenario" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Development Helpers" name="should generate test account credentials for UI" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Development Helpers" name="should log test accounts summary in development" time="0">
    </testcase>
    <testcase classname="TestAccountsService › Development Helpers" name="should not log in production" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-08-02T23:08:30" time="0.291" tests="27">
    <testcase classname="Test Setup" name="should configure test environment" time="0.003">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › REC-TEST-001 through REC-TEST-008: Complete Testing Infrastructure" name="should initialize with default configuration" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › REC-TEST-001 through REC-TEST-008: Complete Testing Infrastructure" name="should support custom configuration" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › REC-TEST-001 through REC-TEST-008: Complete Testing Infrastructure" name="should validate default configuration values" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Performance Testing" name="should validate performance metrics" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Performance Testing" name="should calculate performance scores correctly" time="0.004">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Accessibility Testing" name="should validate accessibility for elements with labels" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Accessibility Testing" name="should detect accessibility issues" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Accessibility Testing" name="should calculate accessibility scores" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Code Quality Testing" name="should detect code quality issues" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Code Quality Testing" name="should pass clean code validation" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Code Quality Testing" name="should calculate code quality scores" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Test Data Factories" name="should create user test data" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Test Data Factories" name="should create user test data with overrides" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Test Data Factories" name="should create service test data" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Test Data Factories" name="should create booking test data" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Test Data Factories" name="should create provider test data" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Enhanced Assertions" name="should provide enhanced assertion helpers" time="0.003">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Enhanced Assertions" name="should validate test coverage expectations" time="0.013">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Test Suite Utilities" name="should provide test suite creation utilities" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Test Suite Utilities" name="should create comprehensive test suites" time="0.022">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Quality Reporting" name="should generate quality reports" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Quality Reporting" name="should provide current metrics" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Singleton Instance" name="should provide singleton instance" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Singleton Instance" name="should export utility functions" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Error Handling" name="should handle missing performance object gracefully" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Error Handling" name="should handle invalid container in accessibility validation" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-08-02T23:08:30" time="0.186" tests="18">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Alt-Text Generation" name="should generate appropriate alt-text for different image types" time="0.007">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Alt-Text Generation" name="should use custom description when provided" time="0.001">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Alt-Text Generation" name="should handle missing entity names gracefully" time="0.007">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Props Generation" name="should generate comprehensive accessibility props for informative images" time="0.002">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Props Generation" name="should generate proper props for functional images" time="0.001">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Props Generation" name="should handle decorative images correctly" time="0.002">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Props Generation" name="should use custom alt-text when provided" time="0.002">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Validation" name="should validate informative images correctly" time="0.001">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Validation" name="should identify missing accessibility labels" time="0.002">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Validation" name="should warn about overly long accessibility labels" time="0.001">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Validation" name="should validate decorative images correctly" time="0.002">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Predefined Image Contexts" name="should provide correct context for app logo" time="0.001">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Predefined Image Contexts" name="should provide correct context for user avatar" time="0.001">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Predefined Image Contexts" name="should provide correct context for functional icons" time="0.002">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Predefined Image Contexts" name="should provide correct context for decorative images" time="0.001">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Batch Validation" name="should validate multiple images and provide comprehensive results" time="0.004">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › WCAG 2.1 AA Compliance Summary" name="should meet all image accessibility requirements" time="0.009">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="4" skipped="0" timestamp="2025-08-02T23:08:31" time="0.277" tests="26">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="PerformanceMonitorService › Initialization" name="starts monitoring correctly" time="0.005">
    </testcase>
    <testcase classname="PerformanceMonitorService › Initialization" name="stops monitoring correctly" time="0.003">
    </testcase>
    <testcase classname="PerformanceMonitorService › Initialization" name="prevents double initialization" time="0.003">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)

Expected number of calls: 1
Received number of calls: 0
    at Object.toHaveBeenCalledTimes (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\performanceMonitor.test.ts:45:26)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="PerformanceMonitorService › Render Performance Tracking" name="tracks component render times" time="0.006">
    </testcase>
    <testcase classname="PerformanceMonitorService › Render Performance Tracking" name="calculates average render times for components" time="0.005">
    </testcase>
    <testcase classname="PerformanceMonitorService › Render Performance Tracking" name="warns about slow renders" time="0.004">
    </testcase>
    <testcase classname="PerformanceMonitorService › Render Performance Tracking" name="tracks render metadata correctly" time="0.005">
    </testcase>
    <testcase classname="PerformanceMonitorService › Network Performance Tracking" name="tracks network request performance" time="0.003">
    </testcase>
    <testcase classname="PerformanceMonitorService › Network Performance Tracking" name="warns about slow network requests" time="0.003">
    </testcase>
    <testcase classname="PerformanceMonitorService › Network Performance Tracking" name="tracks cached vs non-cached requests" time="0.003">
    </testcase>
    <testcase classname="PerformanceMonitorService › Network Performance Tracking" name="limits stored network metrics" time="0.008">
    </testcase>
    <testcase classname="PerformanceMonitorService › User Interaction Tracking" name="tracks user interaction performance" time="0.004">
    </testcase>
    <testcase classname="PerformanceMonitorService › User Interaction Tracking" name="warns about slow interactions" time="0.003">
    </testcase>
    <testcase classname="PerformanceMonitorService › Navigation Performance Tracking" name="tracks navigation performance" time="0.005">
    </testcase>
    <testcase classname="PerformanceMonitorService › Performance Reports" name="generates comprehensive performance report" time="0.006">
    </testcase>
    <testcase classname="PerformanceMonitorService › Performance Reports" name="calculates cache hit rate correctly" time="0.005">
    </testcase>
    <testcase classname="PerformanceMonitorService › Performance Reports" name="identifies slow components" time="0.008">
    </testcase>
    <testcase classname="PerformanceMonitorService › Performance Reports" name="identifies slow network requests" time="0.006">
    </testcase>
    <testcase classname="PerformanceMonitorService › Performance Reports" name="generates performance recommendations" time="0.007">
      <failure>Error: expect(received).toContain(expected) // indexOf

Expected value: StringContaining &quot;Optimize slow components&quot;
Received array: [&quot;Consider optimizing component renders with React.memo or useMemo&quot;, &quot;Optimize slow components: SlowComponent&quot;, &quot;Consider implementing request caching or optimizing API endpoints&quot;]

Looks like you wanted to test for object/array equality with the stricter `toContain` matcher. You probably need to use `toContainEqual` instead.
    at Object.toContain (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\performanceMonitor.test.ts:304:38)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="PerformanceMonitorService › Memory Monitoring" name="collects memory metrics when available" time="0.003">
    </testcase>
    <testcase classname="PerformanceMonitorService › Memory Monitoring" name="detects potential memory leaks" time="0.003">
    </testcase>
    <testcase classname="PerformanceMonitorService › Memory Monitoring" name="detects excessive re-renders" time="0.003">
      <failure>Error: expect(received).toBeDefined()

Received: undefined
    at Object.toBeDefined (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\performanceMonitor.test.ts:361:28)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="PerformanceMonitorService › Cleanup and Maintenance" name="clears all metrics" time="0.005">
    </testcase>
    <testcase classname="PerformanceMonitorService › Cleanup and Maintenance" name="cleans up old metrics automatically" time="0.004">
    </testcase>
    <testcase classname="PerformanceMonitorService › Cleanup and Maintenance" name="destroys service correctly" time="0.003">
      <failure>TypeError: _performanceMonitor.performanceMonitor.destroy is not a function
    at Object.destroy (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\performanceMonitor.test.ts:401:26)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-08-02T23:08:31" time="0.217" tests="23">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="Performance Utilities › PerformanceMonitor" name="creates singleton instance" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › PerformanceMonitor" name="records metrics correctly" time="0.004">
    </testcase>
    <testcase classname="Performance Utilities › PerformanceMonitor" name="gets all metrics when no name specified" time="0.002">
    </testcase>
    <testcase classname="Performance Utilities › PerformanceMonitor" name="generates performance summary" time="0.003">
    </testcase>
    <testcase classname="Performance Utilities › PerformanceMonitor" name="limits metrics to prevent memory leaks" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › PerformanceMonitor" name="clears metrics" time="0.002">
    </testcase>
    <testcase classname="Performance Utilities › PerformanceTimer" name="measures timing correctly" time="0.002">
    </testcase>
    <testcase classname="Performance Utilities › PerformanceTimer" name="includes tags in metrics" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › CriticalCSSOptimizer" name="sets and gets critical CSS" time="0.004">
    </testcase>
    <testcase classname="Performance Utilities › CriticalCSSOptimizer" name="loads non-critical CSS asynchronously" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › CriticalCSSOptimizer" name="preloads critical resources" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › CodeSplittingUtils" name="imports module with performance tracking" time="0.002">
    </testcase>
    <testcase classname="Performance Utilities › CodeSplittingUtils" name="handles import errors" time="0.009">
    </testcase>
    <testcase classname="Performance Utilities › CodeSplittingUtils" name="preloads chunks" time="0.002">
    </testcase>
    <testcase classname="Performance Utilities › CodeSplittingUtils" name="tracks loaded chunks" time="0.002">
    </testcase>
    <testcase classname="Performance Utilities › MemoryMonitor" name="gets memory usage information" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › MemoryMonitor" name="returns null when memory API not available" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › MemoryMonitor" name="starts memory monitoring" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › Utility Functions" name="measurePerformance creates PerformanceTimer" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › Utility Functions" name="withPerformanceTracking wraps synchronous functions" time="0.002">
    </testcase>
    <testcase classname="Performance Utilities › Utility Functions" name="withPerformanceTracking wraps asynchronous functions" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › Utility Functions" name="withPerformanceTracking handles errors" time="0.002">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="20" skipped="0" timestamp="2025-08-02T23:08:31" time="0.432" tests="22">
    <testcase classname="Test Setup" name="should configure test environment" time="0.004">
    </testcase>
    <testcase classname="AccessibilityAudit › Rendering" name="should render audit component correctly" time="0.008">
      <failure>Error: Unable to find an element with text: Accessibility Audit (WCAG AA)
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\AccessibilityAudit.test.tsx:48:14)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AccessibilityAudit › Rendering" name="should not render when showDetailedReport is false" time="0.002">
    </testcase>
    <testcase classname="AccessibilityAudit › Rendering" name="should render with different compliance levels" time="0.002">
      <failure>Error: Unable to find an element with text: Accessibility Audit (WCAG AAA)
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\AccessibilityAudit.test.tsx:69:14)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AccessibilityAudit › Audit Execution" name="should run audit when button is pressed" time="0.002">
      <failure>Error: Unable to find an element with text: Run Audit
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\AccessibilityAudit.test.tsx:86:25)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AccessibilityAudit › Audit Execution" name="should show progress during audit" time="0.001">
      <failure>Error: Unable to find an element with text: Run Audit
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\AccessibilityAudit.test.tsx:103:25)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AccessibilityAudit › Audit Execution" name="should call onIssueFound when issues are detected" time="0.003">
      <failure>Error: Unable to find an element with text: Run Audit
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\AccessibilityAudit.test.tsx:126:25)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AccessibilityAudit › Real-time Auditing" name="should start real-time auditing when enabled" time="0.002">
      <failure>Error: Unable to find an element with text: Auditing...
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\AccessibilityAudit.test.tsx:157:14)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AccessibilityAudit › Real-time Auditing" name="should stop real-time auditing when component unmounts" time="0.002">
      <failure>Error: expect(jest.fn()).toHaveBeenCalled()

Expected number of calls: &gt;= 1
Received number of calls:    0
    at Object.toHaveBeenCalled (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\AccessibilityAudit.test.tsx:177:32)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AccessibilityAudit › Audit Results Display" name="should display audit results after completion" time="0.002">
      <failure>Error: Unable to find an element with text: Run Audit
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\AccessibilityAudit.test.tsx:191:25)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AccessibilityAudit › Audit Results Display" name="should show pass status for passing criteria" time="0.002">
      <failure>Error: Unable to find an element with text: Run Audit
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\AccessibilityAudit.test.tsx:208:25)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AccessibilityAudit › Audit Results Display" name="should show fail status for failing criteria" time="0.002">
      <failure>Error: Unable to find an element with text: Run Audit
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\AccessibilityAudit.test.tsx:228:25)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AccessibilityAudit › Audit Results Display" name="should display impact badges correctly" time="0.002">
      <failure>Error: Unable to find an element with text: Run Audit
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\AccessibilityAudit.test.tsx:244:25)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AccessibilityAudit › Audit Results Display" name="should show recommendations for each criterion" time="0.001">
      <failure>Error: Unable to find an element with text: Run Audit
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\AccessibilityAudit.test.tsx:259:25)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AccessibilityAudit › Compliance Level Filtering" name="should filter criteria based on compliance level A" time="0.003">
      <failure>Error: Unable to find an element with text: Run Audit
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\AccessibilityAudit.test.tsx:279:25)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AccessibilityAudit › Compliance Level Filtering" name="should include all criteria for AAA compliance level" time="0.001">
      <failure>Error: Unable to find an element with text: Run Audit
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\AccessibilityAudit.test.tsx:297:25)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AccessibilityAudit › Color Contrast Testing" name="should test color contrast correctly" time="0.001">
      <failure>Error: Unable to find an element with text: Run Audit
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\AccessibilityAudit.test.tsx:316:25)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AccessibilityAudit › Color Contrast Testing" name="should display contrast ratio in results" time="0.001">
      <failure>Error: Unable to find an element with text: Run Audit
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\AccessibilityAudit.test.tsx:334:25)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AccessibilityAudit › Touch Target Testing" name="should validate touch target sizes" time="0.002">
      <failure>Error: Unable to find an element with text: Run Audit
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\AccessibilityAudit.test.tsx:351:25)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AccessibilityAudit › Accessibility" name="should have proper accessibility attributes" time="0.001">
      <failure>Error: Unable to find an element with role: button
    at Object.getByRole (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\AccessibilityAudit.test.tsx:370:25)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AccessibilityAudit › Accessibility" name="should be keyboard accessible" time="0.001">
      <failure>Error: Unable to find an element with text: Run Audit
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\AccessibilityAudit.test.tsx:381:25)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AccessibilityAudit › Error Handling" name="should handle audit errors gracefully" time="0.001">
      <failure>Error: Unable to find an element with text: Run Audit
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\accessibility\__tests__\AccessibilityAudit.test.tsx:399:25)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-08-02T23:08:32" time="0.677" tests="28">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="Input Component › Basic Rendering" name="should render with default props" time="0.377">
    </testcase>
    <testcase classname="Input Component › Basic Rendering" name="should render with custom placeholder" time="0.002">
    </testcase>
    <testcase classname="Input Component › Basic Rendering" name="should render with label" time="0.003">
    </testcase>
    <testcase classname="Input Component › Value and Text Handling" name="should display initial value" time="0.002">
    </testcase>
    <testcase classname="Input Component › Value and Text Handling" name="should call onChangeText when text changes" time="0.003">
    </testcase>
    <testcase classname="Input Component › Value and Text Handling" name="should handle controlled input pattern" time="0.003">
    </testcase>
    <testcase classname="Input Component › Input States" name="should handle disabled state" time="0.002">
    </testcase>
    <testcase classname="Input Component › Input States" name="should handle error state with message" time="0.003">
    </testcase>
    <testcase classname="Input Component › Input States" name="should handle success state with message" time="0.004">
    </testcase>
    <testcase classname="Input Component › Input States" name="should show helper text when provided" time="0.002">
    </testcase>
    <testcase classname="Input Component › Input States" name="should handle focus state" time="0.002">
    </testcase>
    <testcase classname="Input Component › Input Variants" name="should render default variant by default" time="0.002">
    </testcase>
    <testcase classname="Input Component › Input Variants" name="should render outline variant correctly" time="0.002">
    </testcase>
    <testcase classname="Input Component › Input Variants" name="should render minimal variant correctly" time="0.002">
    </testcase>
    <testcase classname="Input Component › Input Sizes" name="should render medium size by default" time="0.002">
    </testcase>
    <testcase classname="Input Component › Input Sizes" name="should render small size correctly" time="0.002">
    </testcase>
    <testcase classname="Input Component › Input Sizes" name="should render large size correctly" time="0.003">
    </testcase>
    <testcase classname="Input Component › Input Types" name="should handle secure text entry" time="0.002">
    </testcase>
    <testcase classname="Input Component › Input Types" name="should handle multiline input" time="0.002">
    </testcase>
    <testcase classname="Input Component › Input Types" name="should handle keyboard type" time="0.002">
    </testcase>
    <testcase classname="Input Component › Required Field" name="should show required indicator when required" time="0.002">
    </testcase>
    <testcase classname="Input Component › Required Field" name="should not show required indicator by default" time="0.001">
    </testcase>
    <testcase classname="Input Component › Accessibility" name="should support custom accessibility label" time="0.002">
    </testcase>
    <testcase classname="Input Component › Accessibility" name="should support accessibility hint" time="0.003">
    </testcase>
    <testcase classname="Input Component › Accessibility" name="should have proper accessibility setup for text input" time="0.002">
    </testcase>
    <testcase classname="Input Component › Custom Styling" name="should apply custom styles" time="0.002">
    </testcase>
    <testcase classname="Input Component › Component Contract Compliance" name="should handle complex prop combinations" time="0.003">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="4" skipped="0" timestamp="2025-08-02T23:08:32" time="0.354" tests="5">
    <testcase classname="Test Setup" name="should configure test environment" time="0.001">
    </testcase>
    <testcase classname="End-to-End Integration Tests › Complete Customer Booking Journey" name="should complete full booking flow from search to payment confirmation" time="0.006">
      <failure>Error: expect(received).toHaveLength(expected)

Expected length: 1
Received length: 0
Received array:  []
    at Object.toHaveLength (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\tests\e2e\EndToEndIntegration.test.ts:116:38)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)</failure>
    </testcase>
    <testcase classname="End-to-End Integration Tests › Provider Service Management Journey" name="should complete provider service creation and booking management" time="0.001">
      <failure>TypeError: _bookingService.bookingService.createService is not a function
    at Object.createService (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\tests\e2e\EndToEndIntegration.test.ts:248:44)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="End-to-End Integration Tests › Error Handling and Recovery" name="should handle booking creation failure and retry" time="0.008">
      <failure>Error: expect(received).rejects.toThrow()

Received promise resolved instead of rejected
Resolved to value: {&quot;base_price&quot;: 50, &quot;booking_number&quot;: &quot;BK113191&quot;, &quot;created_at&quot;: &quot;2025-08-02T23:08:33.191Z&quot;, &quot;customer_id&quot;: &quot;customer_1&quot;, &quot;duration_minutes&quot;: 60, &quot;id&quot;: &quot;booking_1754176113191&quot;, &quot;notes&quot;: &quot;&quot;, &quot;payment_status&quot;: &quot;pending&quot;, &quot;provider_id&quot;: &quot;provider123&quot;, &quot;provider_name&quot;: &quot;Offline Provider&quot;, &quot;scheduled_datetime&quot;: &quot;undefinedTundefined:00Z&quot;, &quot;service_category&quot;: &quot;Beauty&quot;, &quot;service_id&quot;: &quot;service123&quot;, &quot;service_name&quot;: &quot;Offline Service&quot;, &quot;status&quot;: &quot;pending&quot;, &quot;total_amount&quot;: 50, &quot;updated_at&quot;: &quot;2025-08-02T23:08:33.191Z&quot;}
    at expect (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@jest\expect\node_modules\expect\build\index.js:113:15)
    at Object.expect (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\tests\e2e\EndToEndIntegration.test.ts:314:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="End-to-End Integration Tests › Error Handling and Recovery" name="should handle payment failure and provide recovery options" time="0.014">
      <failure>Error: expect(received).rejects.toThrow(expected)

Expected substring: &quot;Payment failed: Insufficient funds&quot;
Received message:   &quot;Failed to confirm payment&quot;

      170 |     } catch (error) {
      171 |       console.error(&apos;Failed to confirm payment:&apos;, error);
    &gt; 172 |       throw new Error(&apos;Failed to confirm payment&apos;);
          |             ^
      173 |     }
      174 |   }
      175 |

      at PaymentService.&lt;anonymous&gt; (src/services/paymentService.ts:172:13)
          at Generator.throw (&lt;anonymous&gt;)
      at asyncGeneratorStep (node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
      at _throw (node_modules/@babel/runtime/helpers/asyncToGenerator.js:20:9)
    at Object.toThrow (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@jest\expect\node_modules\expect\build\index.js:218:22)
    at Object.toThrow (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\tests\e2e\EndToEndIntegration.test.ts:352:17)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-08-02T23:08:33" time="0.187" tests="14">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Form Field Accessibility Props Generation" name="should generate comprehensive accessibility props for text input" time="0.005">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Form Field Accessibility Props Generation" name="should generate error-specific accessibility props" time="0.002">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Form Field Accessibility Props Generation" name="should handle checkbox accessibility props" time="0.003">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Label Generation" name="should generate proper labels for different field types" time="0.003">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Hint Generation" name="should generate appropriate hints for different field types" time="0.003">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Error Message Accessibility" name="should generate proper error message props" time="0.001">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Helper Text Accessibility" name="should generate proper helper text props" time="0.001">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Label Accessibility" name="should generate proper label props" time="0.001">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Label Accessibility" name="should handle non-required labels" time="0.001">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Form Accessibility Validation" name="should validate complete form accessibility" time="0.002">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Form Accessibility Validation" name="should identify accessibility issues" time="0.002">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Field Type Specific Accessibility" name="should handle different field types correctly" time="0.009">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › WCAG 2.1 AA Compliance Summary" name="should meet all form accessibility requirements" time="0.009">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-08-02T23:08:33" time="1.866" tests="27">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="UnifiedButton Component › Basic Rendering" name="should render with default props" time="1.44">
    </testcase>
    <testcase classname="UnifiedButton Component › Basic Rendering" name="should render with custom children" time="0.003">
    </testcase>
    <testcase classname="UnifiedButton Component › Basic Rendering" name="should have correct accessibility role" time="0.003">
    </testcase>
    <testcase classname="UnifiedButton Component › Button Variants" name="should render primary variant by default" time="0.004">
    </testcase>
    <testcase classname="UnifiedButton Component › Button Variants" name="should render secondary variant correctly" time="0.004">
    </testcase>
    <testcase classname="UnifiedButton Component › Button Variants" name="should render outline variant correctly" time="0.003">
    </testcase>
    <testcase classname="UnifiedButton Component › Button Variants" name="should render ghost variant correctly" time="0.003">
    </testcase>
    <testcase classname="UnifiedButton Component › Button Variants" name="should render destructive variant correctly" time="0.005">
    </testcase>
    <testcase classname="UnifiedButton Component › Button Variants" name="should render success variant correctly" time="0.004">
    </testcase>
    <testcase classname="UnifiedButton Component › Button Variants" name="should render minimal variant correctly" time="0.003">
    </testcase>
    <testcase classname="UnifiedButton Component › Button Sizes" name="should render small size correctly" time="0.003">
    </testcase>
    <testcase classname="UnifiedButton Component › Button Sizes" name="should render medium size by default" time="0.003">
    </testcase>
    <testcase classname="UnifiedButton Component › Button Sizes" name="should render large size correctly" time="0.006">
    </testcase>
    <testcase classname="UnifiedButton Component › Button States" name="should handle disabled state" time="0.003">
    </testcase>
    <testcase classname="UnifiedButton Component › Button States" name="should handle loading state" time="0.096">
    </testcase>
    <testcase classname="UnifiedButton Component › Button States" name="should call onPress when pressed" time="0.002">
    </testcase>
    <testcase classname="UnifiedButton Component › Button States" name="should not call onPress when loading" time="0.002">
    </testcase>
    <testcase classname="UnifiedButton Component › Icon Support" name="should render with left icon" time="0.004">
    </testcase>
    <testcase classname="UnifiedButton Component › Icon Support" name="should render with right icon" time="0.003">
    </testcase>
    <testcase classname="UnifiedButton Component › Icon Support" name="should render icon-only button" time="0.003">
    </testcase>
    <testcase classname="UnifiedButton Component › Accessibility" name="should support custom accessibility label" time="0.003">
    </testcase>
    <testcase classname="UnifiedButton Component › Accessibility" name="should support accessibility hint" time="0.002">
    </testcase>
    <testcase classname="UnifiedButton Component › Accessibility" name="should have proper accessibility state for disabled button" time="0.002">
    </testcase>
    <testcase classname="UnifiedButton Component › Accessibility" name="should have proper accessibility state for loading button" time="0.003">
    </testcase>
    <testcase classname="UnifiedButton Component › Custom Styling" name="should apply custom styles" time="0.01">
    </testcase>
    <testcase classname="UnifiedButton Component › Custom Styling" name="should apply custom text styles" time="0.004">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="11" skipped="0" timestamp="2025-08-02T23:08:35" time="0.529" tests="15">
    <testcase classname="Test Setup" name="should configure test environment" time="0.003">
    </testcase>
    <testcase classname="ErrorBoundary › Error Handling" name="catches errors in child components" time="0.08">
    </testcase>
    <testcase classname="ErrorBoundary › Error Handling" name="renders children normally when no errors occur" time="0.003">
    </testcase>
    <testcase classname="ErrorBoundary › Error Handling" name="calls onError callback when an error occurs" time="0.014">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: ObjectContaining {&quot;message&quot;: &quot;Test error&quot;}
Received: [Error: Test error], {&quot;componentStack&quot;: &quot;
    at ErrorComponent (C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\components\\error\\__tests__\\ErrorBoundary.test.tsx:31:9)
    at ErrorBoundary (C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\components\\error\\ErrorBoundary.tsx:36:42)&quot;}

Number of calls: 1
    at Object.toHaveBeenCalledWith (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\error\__tests__\ErrorBoundary.test.tsx:106:23)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ErrorBoundary › Error Handling" name="tracks errors with performance monitor" time="0.008">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: &quot;error_boundary_catch&quot;, 0, ObjectContaining {&quot;componentStack&quot;: Any&lt;String&gt;, &quot;error&quot;: &quot;Test error&quot;}

Number of calls: 0
    at Object.toHaveBeenCalledWith (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\error\__tests__\ErrorBoundary.test.tsx:120:59)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ErrorBoundary › Fallback UI" name="renders default fallback UI" time="0.008">
      <failure>Error: Unable to find an element with text: /We&apos;re sorry, but an error occurred/

&lt;View
  accessibilityRole=&quot;alert&quot;
  testID=&quot;error-boundary-container&quot;
&gt;
  &lt;View&gt;
    &lt;Text&gt;
      Something went wrong
    &lt;/Text&gt;
    &lt;Text&gt;
      Test error
    &lt;/Text&gt;
    &lt;View
      accessible={true}
      testID=&quot;error-boundary-retry&quot;
    &gt;
      &lt;Text&gt;
        Try Again
      &lt;/Text&gt;
    &lt;/View&gt;
  &lt;/View&gt;
&lt;/View&gt;
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\error\__tests__\ErrorBoundary.test.tsx:140:14)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ErrorBoundary › Fallback UI" name="renders custom fallback UI when provided" time="0.003">
      <failure>Error: Unable to find an element with testID: custom-fallback
    at Object.getByTestId (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\error\__tests__\ErrorBoundary.test.tsx:167:14)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ErrorBoundary › Fallback UI" name="shows technical details in development mode" time="0.007">
      <failure>Error: Unable to find an element with text: /Technical Details/

&lt;View
  accessibilityRole=&quot;alert&quot;
  testID=&quot;error-boundary-container&quot;
&gt;
  &lt;View&gt;
    &lt;Text&gt;
      Something went wrong
    &lt;/Text&gt;
    &lt;Text&gt;
      Test error
    &lt;/Text&gt;
    &lt;View
      accessible={true}
      testID=&quot;error-boundary-retry&quot;
    &gt;
      &lt;Text&gt;
        Try Again
      &lt;/Text&gt;
    &lt;/View&gt;
  &lt;/View&gt;
&lt;/View&gt;
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\error\__tests__\ErrorBoundary.test.tsx:182:14)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ErrorBoundary › Reset Functionality" name="resets error state when retry button is pressed" time="0.014">
    </testcase>
    <testcase classname="ErrorBoundary › Reset Functionality" name="respects maxRetries limit" time="0.005">
      <failure>ReferenceError: screen is not defined
    at Object.screen (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\error\__tests__\ErrorBoundary.test.tsx:235:25)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ErrorBoundary › Reset Functionality" name="resets on props change when resetOnPropsChange is true" time="0.085">
      <failure>ReferenceError: screen is not defined
    at Object.screen (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\error\__tests__\ErrorBoundary.test.tsx:266:14)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ErrorBoundary › Dynamic Error Handling" name="catches runtime errors from user interactions" time="0.002">
      <failure>ReferenceError: screen is not defined
    at Object.screen (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\error\__tests__\ErrorBoundary.test.tsx:287:14)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ErrorBoundary › Dynamic Error Handling" name="handles nested error boundaries correctly" time="0.002">
      <failure>ReferenceError: screen is not defined
    at Object.screen (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\error\__tests__\ErrorBoundary.test.tsx:320:14)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ErrorBoundary › Accessibility" name="announces errors to screen readers" time="0.004">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: StringContaining &quot;error occurred&quot;

Number of calls: 0
    at Object.toHaveBeenCalledWith (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\error\__tests__\ErrorBoundary.test.tsx:348:28)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ErrorBoundary › Accessibility" name="provides proper accessibility props on fallback UI" time="0.008">
      <failure>ReferenceError: screen is not defined
    at Object.screen (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\error\__tests__\ErrorBoundary.test.tsx:360:30)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-08-02T23:08:35" time="0.306" tests="31">
    <testcase classname="Test Setup" name="should configure test environment" time="0.003">
    </testcase>
    <testcase classname="Text Component › Basic Rendering" name="should render with default props" time="0.039">
    </testcase>
    <testcase classname="Text Component › Basic Rendering" name="should render with custom children" time="0.003">
    </testcase>
    <testcase classname="Text Component › Basic Rendering" name="should render string content" time="0.005">
    </testcase>
    <testcase classname="Text Component › Typography Variants" name="should apply body variant by default" time="0.002">
    </testcase>
    <testcase classname="Text Component › Typography Variants" name="should apply heading variant correctly" time="0.002">
    </testcase>
    <testcase classname="Text Component › Typography Variants" name="should apply caption variant correctly" time="0.002">
    </testcase>
    <testcase classname="Text Component › Typography Variants" name="should apply label variant correctly" time="0.002">
    </testcase>
    <testcase classname="Text Component › Typography Variants" name="should apply display variant correctly" time="0.002">
    </testcase>
    <testcase classname="Text Component › Text Sizes" name="should apply xs size correctly" time="0.002">
    </testcase>
    <testcase classname="Text Component › Text Sizes" name="should apply sm size correctly" time="0.002">
    </testcase>
    <testcase classname="Text Component › Text Sizes" name="should apply base size by default" time="0.009">
    </testcase>
    <testcase classname="Text Component › Text Sizes" name="should apply lg size correctly" time="0.002">
    </testcase>
    <testcase classname="Text Component › Text Sizes" name="should apply xl size correctly" time="0.002">
    </testcase>
    <testcase classname="Text Component › Text Colors" name="should apply primary color by default" time="0.002">
    </testcase>
    <testcase classname="Text Component › Text Colors" name="should apply secondary color correctly" time="0.003">
    </testcase>
    <testcase classname="Text Component › Text Colors" name="should apply error color correctly" time="0.002">
    </testcase>
    <testcase classname="Text Component › Text Colors" name="should apply success color correctly" time="0.002">
    </testcase>
    <testcase classname="Text Component › Text Weights" name="should apply normal weight by default" time="0.002">
    </testcase>
    <testcase classname="Text Component › Text Weights" name="should apply bold weight correctly" time="0.002">
    </testcase>
    <testcase classname="Text Component › Text Weights" name="should apply medium weight correctly" time="0.003">
    </testcase>
    <testcase classname="Text Component › Text Alignment" name="should apply left alignment by default" time="0.002">
    </testcase>
    <testcase classname="Text Component › Text Alignment" name="should apply center alignment correctly" time="0.002">
    </testcase>
    <testcase classname="Text Component › Text Alignment" name="should apply right alignment correctly" time="0.001">
    </testcase>
    <testcase classname="Text Component › Accessibility" name="should support custom accessibility label" time="0.001">
    </testcase>
    <testcase classname="Text Component › Accessibility" name="should support accessibility hint" time="0.002">
    </testcase>
    <testcase classname="Text Component › Accessibility" name="should have proper accessibility role for text" time="0.002">
    </testcase>
    <testcase classname="Text Component › Custom Styling" name="should apply custom styles" time="0.006">
    </testcase>
    <testcase classname="Text Component › Custom Styling" name="should merge custom styles with component styles" time="0.003">
    </testcase>
    <testcase classname="Text Component › Component Contract Compliance" name="should handle text content properly" time="0.001">
    </testcase>
    <testcase classname="Text Component › Component Contract Compliance" name="should support complex prop combinations" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="5" skipped="0" timestamp="2025-08-02T23:08:36" time="0.214" tests="12">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="Payment System Integration › Payment Configuration" name="should retrieve payment configuration successfully" time="0.004">
    </testcase>
    <testcase classname="Payment System Integration › Payment Configuration" name="should handle payment configuration errors" time="0.016">
    </testcase>
    <testcase classname="Payment System Integration › Payment Methods Management" name="should load payment methods successfully" time="0.009">
      <failure>Error: expect(received).toHaveLength(expected)

Expected length: 1
Received length: 0
Received array:  []
    at Object.toHaveLength (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\tests\payment\PaymentSystemIntegration.test.ts:99:36)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)</failure>
    </testcase>
    <testcase classname="Payment System Integration › Payment Methods Management" name="should add new payment method successfully" time="0.002">
    </testcase>
    <testcase classname="Payment System Integration › Payment Methods Management" name="should delete payment method successfully" time="0.005">
    </testcase>
    <testcase classname="Payment System Integration › Payment Methods Management" name="should set default payment method successfully" time="0.002">
    </testcase>
    <testcase classname="Payment System Integration › Payment Processing" name="should create payment intent successfully" time="0.002">
    </testcase>
    <testcase classname="Payment System Integration › Transaction History" name="should load transaction history successfully" time="0.004">
      <failure>Error: expect(received).toHaveLength(expected)

Expected length: 1
Received length: 0
Received array:  []
    at Object.toHaveLength (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\tests\payment\PaymentSystemIntegration.test.ts:238:34)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)</failure>
    </testcase>
    <testcase classname="Payment System Integration › Refund Processing" name="should request refund successfully" time="0.003">
      <failure>Error: expect(received).toHaveLength(expected)

Expected length: 1
Received length: 0
Received array:  []
    at Object.toHaveLength (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\tests\payment\PaymentSystemIntegration.test.ts:298:45)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)</failure>
    </testcase>
    <testcase classname="Payment System Integration › Error Handling" name="should handle payment method loading errors" time="0.005">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: &quot;Network error&quot;
Received: null
    at Object.toBe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\tests\payment\PaymentSystemIntegration.test.ts:315:27)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)</failure>
    </testcase>
    <testcase classname="Payment System Integration › Error Handling" name="should handle transaction loading errors" time="0.004">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: &quot;Server error&quot;
Received: null
    at Object.toBe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\tests\payment\PaymentSystemIntegration.test.ts:327:27)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-08-02T23:08:36" time="0.201" tests="16">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="Enhanced Form Validation System › FormValidator Class" name="should validate individual fields correctly" time="0.001">
    </testcase>
    <testcase classname="Enhanced Form Validation System › FormValidator Class" name="should detect invalid fields" time="0.001">
    </testcase>
    <testcase classname="Enhanced Form Validation System › FormValidator Class" name="should validate entire form" time="0.005">
    </testcase>
    <testcase classname="Enhanced Form Validation System › FormValidator Class" name="should detect form validation errors" time="0.002">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Service Form Validation" name="should validate service name correctly" time="0.001">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Service Form Validation" name="should validate service description" time="0.001">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Service Form Validation" name="should validate price correctly" time="0.001">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Service Form Validation" name="should validate duration correctly" time="0">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Profile Form Validation" name="should validate profile data correctly" time="0.001">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Profile Form Validation" name="should handle optional fields correctly" time="0">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Profile Form Validation" name="should validate date of birth format" time="0.004">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Real-time Validation Scenarios" name="should provide immediate feedback for email validation" time="0.001">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Real-time Validation Scenarios" name="should handle password confirmation validation" time="0.001">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Error Message Quality" name="should provide clear, actionable error messages" time="0.001">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Error Message Quality" name="should provide specific validation feedback" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-08-02T23:08:36" time="0.171" tests="17">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Thickness" name="should meet WCAG 2.1 AA minimum thickness requirement (2px)" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Thickness" name="should use recommended thickness for better visibility" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Thickness" name="should enforce minimum thickness even when smaller width is requested" time="0.001">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Visibility" name="should have high z-index to prevent obscuring by sticky elements" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Visibility" name="should have visible overflow to show focus ring" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Visibility" name="should have shadow for enhanced visibility" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Platform-Specific Focus Indicators" name="should provide web-specific outline properties" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Platform-Specific Focus Indicators" name="should provide Android-specific elevation" time="0.001">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Colors" name="should support different focus indicator colors" time="0.005">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Colors" name="should provide subtle background highlight" time="0.005">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Obscuring Prevention" name="should ensure focus is not obscured by sticky elements" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Obscuring Prevention" name="should handle empty sticky elements array" time="0.001">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Obscuring Prevention" name="should preserve existing high z-index if already sufficient" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus State Management" name="should return empty style when not focused" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus State Management" name="should merge with base styles when focused" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › WCAG Standards Compliance" name="should meet all WCAG 2.1 AA focus indicator requirements" time="0.006">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-08-02T23:08:36" time="0.166" tests="14">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="AuthSlice › Initial State" name="should have correct initial state" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Login Actions" name="should handle loginStart" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Login Actions" name="should handle loginSuccess" time="0.004">
    </testcase>
    <testcase classname="AuthSlice › Login Actions" name="should handle loginFailure" time="0.002">
    </testcase>
    <testcase classname="AuthSlice › Registration Actions" name="should handle registerStart" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Registration Actions" name="should handle registerSuccess" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Registration Actions" name="should handle registerFailure" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Profile Management" name="should handle updateProfile" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Profile Management" name="should handle updateTokens" time="0.002">
    </testcase>
    <testcase classname="AuthSlice › Logout" name="should handle logout" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Authentication Status Check" name="should load stored authentication data" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Authentication Status Check" name="should handle missing stored data" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Authentication Status Check" name="should handle corrupted stored user data" time="0.01">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="2" skipped="0" timestamp="2025-08-02T23:08:37" time="0.177" tests="16">
    <testcase classname="Test Setup" name="should configure test environment" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Initial State" name="should have correct initial state" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Login Actions" name="should handle login start" time="0">
    </testcase>
    <testcase classname="AuthSlice › Login Actions" name="should handle login success" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Login Actions" name="should handle login failure" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Registration Actions" name="should handle registration start" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Registration Actions" name="should handle registration success" time="0.002">
    </testcase>
    <testcase classname="AuthSlice › Registration Actions" name="should handle registration failure" time="0">
    </testcase>
    <testcase classname="AuthSlice › Profile Management" name="should update profile information" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Profile Management" name="should update tokens" time="0.003">
    </testcase>
    <testcase classname="AuthSlice › Logout and Reset" name="should handle logout" time="0.003">
    </testcase>
    <testcase classname="AuthSlice › Logout and Reset" name="should handle reset" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Token Validation" name="should validate valid token" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: StringContaining &quot;/api/auth/validate/&quot;, ObjectContaining {&quot;headers&quot;: ObjectContaining {&quot;Authorization&quot;: &quot;Bearer valid-token&quot;}}

Number of calls: 0
    at Object.toHaveBeenCalledWith (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\store\__tests__\authSlice.test.ts:236:21)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)</failure>
    </testcase>
    <testcase classname="AuthSlice › Token Validation" name="should handle invalid token" time="0.002">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: false
Received: true
    at Object.toBe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\store\__tests__\authSlice.test.ts:258:23)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)</failure>
    </testcase>
    <testcase classname="AuthSlice › Dual Role Support" name="should handle customer role" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Dual Role Support" name="should handle provider role" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-08-02T23:08:37" time="0.188" tests="10">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Primary Button Contrast" name="should meet WCAG AA standards for primary button text" time="0.004">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Primary Button Contrast" name="should have appropriate disabled state contrast" time="0.002">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Secondary Button Contrast" name="should meet WCAG AA standards for secondary button text on white background" time="0.002">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Secondary Button Contrast" name="should meet WCAG AA standards for secondary button border" time="0.001">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Destructive Button Contrast" name="should meet WCAG AA standards for destructive button text" time="0.004">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Ghost Button Contrast" name="should meet WCAG AA standards for ghost button text on white background" time="0.003">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Focus States Contrast" name="should meet WCAG AA standards for focus indicators" time="0.004">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Comprehensive Color Combinations" name="should validate all interactive color combinations" time="0.007">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Large Text Contrast" name="should meet WCAG AA standards for large text (18pt+)" time="0.002">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-08-02T23:08:37" time="1.835" tests="25">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="ErrorHandler › handleError" name="should handle basic errors correctly" time="0.019">
    </testcase>
    <testcase classname="ErrorHandler › handleError" name="should handle errors with context" time="0.012">
    </testcase>
    <testcase classname="ErrorHandler › handleError" name="should preserve AppError properties" time="0.012">
    </testcase>
    <testcase classname="ErrorHandler › handleNetworkError" name="should handle network errors correctly" time="0.009">
    </testcase>
    <testcase classname="ErrorHandler › error type detection" name="should detect network errors" time="0.007">
    </testcase>
    <testcase classname="ErrorHandler › error type detection" name="should detect authentication errors" time="0.011">
    </testcase>
    <testcase classname="ErrorHandler › error type detection" name="should detect authorization errors" time="0.02">
    </testcase>
    <testcase classname="ErrorHandler › error type detection" name="should detect not found errors" time="0.018">
    </testcase>
    <testcase classname="ErrorHandler › error type detection" name="should detect server errors" time="0.009">
    </testcase>
    <testcase classname="ErrorHandler › severity detection" name="should detect critical errors" time="0.012">
    </testcase>
    <testcase classname="ErrorHandler › severity detection" name="should detect high severity errors" time="0.008">
    </testcase>
    <testcase classname="ErrorHandler › severity detection" name="should detect medium severity errors" time="0.007">
    </testcase>
    <testcase classname="ErrorHandler › severity detection" name="should default to low severity" time="0.009">
    </testcase>
    <testcase classname="ErrorHandler › user message generation" name="should generate appropriate network error messages" time="0.011">
    </testcase>
    <testcase classname="ErrorHandler › user message generation" name="should generate appropriate auth error messages" time="0.007">
    </testcase>
    <testcase classname="ErrorHandler › user message generation" name="should generate appropriate authorization error messages" time="0.008">
    </testcase>
    <testcase classname="ErrorHandler › user message generation" name="should generate appropriate not found error messages" time="0.008">
    </testcase>
    <testcase classname="ErrorHandler › user message generation" name="should generate appropriate server error messages" time="0.47">
    </testcase>
    <testcase classname="ErrorHandler › user message generation" name="should generate default error messages" time="0.012">
    </testcase>
    <testcase classname="ErrorHandler › error statistics" name="should track error statistics correctly" time="0.029">
    </testcase>
    <testcase classname="ErrorHandler › error statistics" name="should clear errors correctly" time="0.013">
    </testcase>
    <testcase classname="ErrorHandler › error queue management" name="should maintain queue size limit" time="0.906">
    </testcase>
    <testcase classname="ErrorHandler › error ID generation" name="should generate unique error IDs" time="0.018">
    </testcase>
    <testcase classname="ErrorHandler › error ID generation" name="should generate IDs with correct format" time="0.01">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-08-02T23:08:39" time="0.381" tests="17">
    <testcase classname="Test Setup" name="should configure test environment" time="0.004">
    </testcase>
    <testcase classname="Card Component › Basic Rendering" name="should render with children" time="0.054">
    </testcase>
    <testcase classname="Card Component › Basic Rendering" name="should render with text children" time="0.003">
    </testcase>
    <testcase classname="Card Component › Basic Rendering" name="should render with complex children" time="0.004">
    </testcase>
    <testcase classname="Card Component › Styling and Props" name="should apply default card styling" time="0.002">
    </testcase>
    <testcase classname="Card Component › Styling and Props" name="should accept custom styles through Box props" time="0.002">
    </testcase>
    <testcase classname="Card Component › Styling and Props" name="should override default props when provided" time="0.006">
    </testcase>
    <testcase classname="Card Component › Styling and Props" name="should support custom border radius" time="0.004">
    </testcase>
    <testcase classname="Card Component › Accessibility" name="should support accessibility props" time="0.002">
    </testcase>
    <testcase classname="Card Component › Accessibility" name="should support accessibility hint" time="0.003">
    </testcase>
    <testcase classname="Card Component › Accessibility" name="should support accessibility role" time="0.003">
    </testcase>
    <testcase classname="Card Component › Box Integration" name="should inherit all Box component capabilities" time="0.003">
    </testcase>
    <testcase classname="Card Component › Box Integration" name="should support Box spacing props" time="0.002">
    </testcase>
    <testcase classname="Card Component › Box Integration" name="should support Box layout props" time="0.002">
    </testcase>
    <testcase classname="Card Component › Component Contract Compliance" name="should render as a styled container" time="0.002">
    </testcase>
    <testcase classname="Card Component › Component Contract Compliance" name="should provide consistent card appearance" time="0.004">
    </testcase>
    <testcase classname="Card Component › Component Contract Compliance" name="should handle complex content structures" time="0.003">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="7" skipped="0" timestamp="2025-08-02T23:08:39" time="0.239" tests="12">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="ApiClient › Authentication Token Management" name="should load auth token from AsyncStorage on initialization" time="0.001">
      <failure>TypeError: require(...).ApiClient is not a constructor
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\apiClient.test.ts:55:22)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ApiClient › Authentication Token Management" name="should set auth token and save to AsyncStorage" time="0.001">
    </testcase>
    <testcase classname="ApiClient › Authentication Token Management" name="should remove auth token from AsyncStorage when set to null" time="0">
    </testcase>
    <testcase classname="ApiClient › HTTP Request Methods" name="should make GET request with correct parameters" time="0.001">
      <failure>TypeError: _performanceCacheService.performanceCacheService.getCachedApiResponse is not a function
    at ApiClient.getCachedApiResponse (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\apiClient.ts:244:39)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at ApiClient.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at ApiClient.apply [as makeRequest] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\apiClient.ts:226:28)
    at ApiClient.makeRequest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\apiClient.ts:396:17)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at ApiClient.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at ApiClient.apply [as get] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\apiClient.ts:390:12)
    at Object.get (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\apiClient.test.ts:96:23)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ApiClient › HTTP Request Methods" name="should make POST request with data" time="0.002">
    </testcase>
    <testcase classname="ApiClient › HTTP Request Methods" name="should include auth token in headers when available" time="0.001">
      <failure>TypeError: _performanceCacheService.performanceCacheService.getCachedApiResponse is not a function
    at ApiClient.getCachedApiResponse (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\apiClient.ts:244:39)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at ApiClient.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at ApiClient.apply [as makeRequest] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\apiClient.ts:226:28)
    at ApiClient.makeRequest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\apiClient.ts:396:17)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at ApiClient.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at ApiClient.apply [as get] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\apiClient.ts:390:12)
    at Object.get (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\apiClient.test.ts:133:23)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ApiClient › Error Handling" name="should handle network errors" time="0.006">
      <failure>Error: expect(received).rejects.toThrow(expected)

Expected substring: &quot;Network error&quot;
Received message:   &quot;_performanceCacheService.performanceCacheService.getCachedApiResponse is not a function&quot;

      242 |       const cacheKey = cache.key || `${url}_${JSON.stringify(params || {})}`;
      243 |       const cachedResponse =
    &gt; 244 |         await performanceCacheService.getCachedApiResponse&lt;T&gt;(cacheKey);
          |                                       ^
      245 |
      246 |       if (cachedResponse) {
      247 |         onStatusUpdate?.(&apos;Loaded from cache&apos;);

      at ApiClient.getCachedApiResponse (src/services/apiClient.ts:244:39)
      at asyncGeneratorStep (node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
      at _next (node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
      at node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
      at ApiClient.&lt;anonymous&gt; (node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
      at ApiClient.apply [as makeRequest] (src/services/apiClient.ts:226:28)
      at ApiClient.makeRequest (src/services/apiClient.ts:396:17)
      at asyncGeneratorStep (node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
      at _next (node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
      at node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
      at ApiClient.&lt;anonymous&gt; (node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
      at ApiClient.apply [as get] (src/services/apiClient.ts:390:12)
      at Object.get (src/services/__tests__/apiClient.test.ts:150:30)
      at asyncGeneratorStep (node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
      at _next (node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
      at node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
      at Object.&lt;anonymous&gt; (node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
    at Object.toThrow (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@jest\expect\node_modules\expect\build\index.js:218:22)
    at Object.toThrow (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\apiClient.test.ts:150:56)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ApiClient › Error Handling" name="should handle HTTP error responses" time="0.001">
    </testcase>
    <testcase classname="ApiClient › Error Handling" name="should handle 401 unauthorized and attempt token refresh" time="0.001">
      <failure>TypeError: _performanceCacheService.performanceCacheService.getCachedApiResponse is not a function
    at ApiClient.getCachedApiResponse (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\apiClient.ts:244:39)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at ApiClient.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at ApiClient.apply [as makeRequest] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\apiClient.ts:226:28)
    at ApiClient.makeRequest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\apiClient.ts:396:17)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at ApiClient.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at ApiClient.apply [as get] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\apiClient.ts:390:12)
    at Object.get (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\apiClient.test.ts:189:38)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ApiClient › Request Configuration" name="should respect custom timeout" time="0.001">
      <failure>TypeError: _performanceCacheService.performanceCacheService.getCachedApiResponse is not a function
    at ApiClient.getCachedApiResponse (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\apiClient.ts:244:39)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at ApiClient.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at ApiClient.apply [as makeRequest] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\apiClient.ts:226:28)
    at ApiClient.makeRequest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\apiClient.ts:396:17)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at ApiClient.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at ApiClient.apply [as get] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\apiClient.ts:390:12)
    at Object.get (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\apiClient.test.ts:200:23)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ApiClient › Request Configuration" name="should handle custom headers" time="0.001">
      <failure>TypeError: _performanceCacheService.performanceCacheService.getCachedApiResponse is not a function
    at ApiClient.getCachedApiResponse (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\apiClient.ts:244:39)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at ApiClient.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at ApiClient.apply [as makeRequest] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\apiClient.ts:226:28)
    at ApiClient.makeRequest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\apiClient.ts:396:17)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at ApiClient.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at ApiClient.apply [as get] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\apiClient.ts:390:12)
    at Object.get (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\apiClient.test.ts:209:23)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-08-02T23:08:40" time="0.283" tests="10">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="AuthService › login" name="should successfully login with valid credentials" time="0.01">
    </testcase>
    <testcase classname="AuthService › login" name="should handle login failure with error message" time="0.015">
    </testcase>
    <testcase classname="AuthService › login" name="should handle network errors" time="0.014">
    </testcase>
    <testcase classname="AuthService › register" name="should successfully register a new customer" time="0.007">
    </testcase>
    <testcase classname="AuthService › register" name="should successfully register a new service provider" time="0.008">
    </testcase>
    <testcase classname="AuthService › register" name="should handle registration validation errors" time="0.006">
    </testcase>
    <testcase classname="AuthService › refreshToken" name="should successfully refresh access token" time="0.009">
    </testcase>
    <testcase classname="AuthService › logout" name="should successfully logout" time="0.005">
    </testcase>
    <testcase classname="AuthService › logout" name="should handle logout errors gracefully" time="0.005">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-08-02T23:08:40" time="0.17" tests="20">
    <testcase classname="Test Setup" name="should configure test environment" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › enhancePrimaryCTAContrast" name="should validate compliant colors" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › enhancePrimaryCTAContrast" name="should enhance non-compliant colors" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › enhancePrimaryCTAContrast" name="should identify optimal colors (AAA)" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › validateInteractiveColors" name="should validate all interactive colors" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › generateContrastReport" name="should generate comprehensive contrast report" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › generateContrastReport" name="should have high compliance rate" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › getBestTextColor" name="should return white for dark backgrounds" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › getBestTextColor" name="should return black for light backgrounds" time="0">
    </testcase>
    <testcase classname="Contrast Enhancer › getBestTextColor" name="should return appropriate color for sage green" time="0">
    </testcase>
    <testcase classname="Contrast Enhancer › ensureMinimumContrast" name="should return original color if contrast is sufficient" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › ensureMinimumContrast" name="should return high contrast alternative if insufficient" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › EnhancedCTAColors" name="should have all required color constants" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › EnhancedCTAColors" name="should have WCAG compliant primary colors" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › EnhancedCTAColors" name="should have WCAG compliant secondary colors" time="0">
    </testcase>
    <testcase classname="Contrast Enhancer › Color contrast ratios" name="should meet WCAG AA standards for Primary CTA" time="0">
    </testcase>
    <testcase classname="Contrast Enhancer › Color contrast ratios" name="should meet WCAG AA standards for Primary CTA Hover" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › Color contrast ratios" name="should meet WCAG AA standards for Primary CTA Pressed" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › Color contrast ratios" name="should meet WCAG AA standards for Secondary Button" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › Color contrast ratios" name="should meet WCAG AA standards for Error Button" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-08-02T23:08:40" time="1.67" tests="7">
    <testcase classname="Test Setup" name="should configure test environment" time="0.005">
    </testcase>
    <testcase classname="useProvidersStore › initial state" name="should have correct initial state" time="0.011">
    </testcase>
    <testcase classname="useProvidersStore › fetchProviders" name="should fetch providers successfully" time="0.81">
    </testcase>
    <testcase classname="useProvidersStore › fetchProviders" name="should handle loading error" time="0.002">
    </testcase>
    <testcase classname="useProvidersStore › updateFilters" name="should update filters correctly" time="0.002">
    </testcase>
    <testcase classname="useProvidersStore › clearFilters" name="should clear all filters" time="0.004">
    </testcase>
    <testcase classname="useProvidersStore › searchProviders" name="should search providers with filters" time="0.612">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-08-02T23:08:42" time="0.111" tests="6">
    <testcase classname="Test Setup" name="should configure test environment" time="0.001">
    </testcase>
    <testcase classname="Navigation Types › Type Definitions" name="should define RootStackParamList correctly" time="0.001">
    </testcase>
    <testcase classname="Navigation Types › Type Definitions" name="should define AuthStackParamList correctly" time="0.001">
    </testcase>
    <testcase classname="Navigation Types › Type Definitions" name="should define CustomerTabParamList correctly" time="0.001">
    </testcase>
    <testcase classname="Navigation Types › Type Definitions" name="should define ProviderTabParamList correctly" time="0.004">
    </testcase>
    <testcase classname="Navigation Types › Type Safety" name="should enforce correct parameter types" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-08-02T23:08:42" time="0.111" tests="8">
    <testcase classname="Test Setup" name="should configure test environment" time="0.001">
    </testcase>
    <testcase classname="WCAG_STANDARDS Fix" name="should have correct TOUCH_TARGETS structure" time="0">
    </testcase>
    <testcase classname="WCAG_STANDARDS Fix" name="should have backward compatibility TARGET_SIZE alias" time="0">
    </testcase>
    <testcase classname="WCAG_STANDARDS Fix" name="should have correct FOCUS_INDICATORS structure" time="0.001">
    </testcase>
    <testcase classname="WCAG_STANDARDS Fix" name="should have correct CONTRAST_RATIOS structure" time="0.001">
    </testcase>
    <testcase classname="WCAG_STANDARDS Fix" name="should be frozen to prevent modification" time="0.004">
    </testcase>
    <testcase classname="WCAG_STANDARDS Fix" name="should not throw runtime errors when accessing properties" time="0.001">
    </testcase>
    <testcase classname="WCAG_STANDARDS Fix" name="should maintain consistency between TOUCH_TARGETS.MINIMUM_SIZE and TARGET_SIZE.MINIMUM" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-08-02T23:08:42" time="0.235" tests="6">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="AnimatedButton › Rendering" name="renders correctly with default props" time="0.075">
    </testcase>
    <testcase classname="AnimatedButton › Rendering" name="renders with different variants" time="0.003">
    </testcase>
    <testcase classname="AnimatedButton › Functionality" name="calls onPress when pressed" time="0.003">
    </testcase>
    <testcase classname="AnimatedButton › Functionality" name="does not call onPress when disabled" time="0.002">
    </testcase>
    <testcase classname="AnimatedButton › Accessibility" name="has proper accessibility properties" time="0.005">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="3" timestamp="2025-08-02T23:08:42" time="0.113" tests="4">
    <testcase classname="Test Setup" name="should configure test environment" time="0.001">
    </testcase>
    <testcase classname="AuthService Integration Tests › login integration" name="should successfully login with test credentials" time="0">
      <skipped/>
    </testcase>
    <testcase classname="AuthService Integration Tests › login integration" name="should fail with invalid credentials" time="0">
      <skipped/>
    </testcase>
    <testcase classname="AuthService Integration Tests › register integration" name="should handle registration attempt" time="0">
      <skipped/>
    </testcase>
  </testsuite>
</testsuites>