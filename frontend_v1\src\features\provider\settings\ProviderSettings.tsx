/**
 * Provider Settings - Settings Management Component
 *
 * Phase 1 MVP Implementation:
 * - Account settings and preferences
 * - Notification settings
 * - Privacy settings
 * - Payment settings
 * - Support and help options
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  Text,
  TouchableOpacity,
  StyleSheet,
  Switch,
  Alert,
} from 'react-native';

import { Button } from '../../../components/ui/Button';
import { Card } from '../../../components/ui/Card';
import { Icon } from '../../../components/ui/Icon';
import { useI18n } from '../../../contexts/I18nContext';
import { useTheme } from '../../../contexts/ThemeContext';

// Settings interfaces
interface NotificationSettings {
  newJobRequests: boolean;
  jobUpdates: boolean;
  paymentNotifications: boolean;
  marketingEmails: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
}

interface PrivacySettings {
  profileVisibility: 'public' | 'limited' | 'private';
  showContactInfo: boolean;
  showRatings: boolean;
  allowDirectMessages: boolean;
}

interface PaymentSettings {
  preferredPayoutMethod: 'bank_transfer' | 'paypal' | 'stripe';
  payoutSchedule: 'weekly' | 'biweekly' | 'monthly';
  minimumPayoutAmount: number;
}

interface ProviderSettings {
  notifications: NotificationSettings;
  privacy: PrivacySettings;
  payment: PaymentSettings;
  language: string;
  timezone: string;
  autoAcceptJobs: boolean;
  workingRadius: number; // in kilometers
}

export const ProviderSettings: React.FC = () => {
  const { colors } = useTheme();
  const { t } = useI18n();

  // State
  const [settings, setSettings] = useState<ProviderSettings | null>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Load settings data
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setIsLoading(true);

      // Mock data for Phase 1 MVP
      const mockSettings: ProviderSettings = {
        notifications: {
          newJobRequests: true,
          jobUpdates: true,
          paymentNotifications: true,
          marketingEmails: false,
          pushNotifications: true,
          smsNotifications: false,
        },
        privacy: {
          profileVisibility: 'public',
          showContactInfo: true,
          showRatings: true,
          allowDirectMessages: true,
        },
        payment: {
          preferredPayoutMethod: 'bank_transfer',
          payoutSchedule: 'weekly',
          minimumPayoutAmount: 50,
        },
        language: 'en',
        timezone: 'America/Toronto',
        autoAcceptJobs: false,
        workingRadius: 25,
      };

      setSettings(mockSettings);
    } catch (error) {
      console.error('Error loading settings:', error);
      Alert.alert(t('common.error'), t('provider.settings.loadError'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveSettings = async () => {
    if (!settings) return;

    try {
      setIsSaving(true);

      // Save settings changes
      console.log('Saving settings:', settings);

      Alert.alert(t('common.success'), t('provider.settings.saveSuccess'));

      setHasChanges(false);
    } catch (error) {
      console.error('Error saving settings:', error);
      Alert.alert(t('common.error'), t('provider.settings.saveError'));
    } finally {
      setIsSaving(false);
    }
  };

  const handleNotificationChange = (
    key: keyof NotificationSettings,
    value: boolean,
  ) => {
    if (!settings) return;

    setSettings(prev => ({
      ...prev!,
      notifications: {
        ...prev!.notifications,
        [key]: value,
      },
    }));
    setHasChanges(true);
  };

  const handlePrivacyChange = (key: keyof PrivacySettings, value: any) => {
    if (!settings) return;

    setSettings(prev => ({
      ...prev!,
      privacy: {
        ...prev!.privacy,
        [key]: value,
      },
    }));
    setHasChanges(true);
  };

  const handlePaymentChange = (key: keyof PaymentSettings, value: any) => {
    if (!settings) return;

    setSettings(prev => ({
      ...prev!,
      payment: {
        ...prev!.payment,
        [key]: value,
      },
    }));
    setHasChanges(true);
  };

  const handleGeneralChange = (key: keyof ProviderSettings, value: any) => {
    if (!settings) return;

    setSettings(prev => ({
      ...prev!,
      [key]: value,
    }));
    setHasChanges(true);
  };

  const renderSettingRow = (
    title: string,
    description: string,
    value: boolean,
    onValueChange: (value: boolean) => void,
    icon?: string,
  ) => (
    <View style={styles.settingRow}>
      <View style={styles.settingInfo}>
        {icon && (
          <Icon
            name={icon}
            size={20}
            color={colors.text.secondary}
            style={styles.settingIcon}
          />
        )}
        <View style={styles.settingText}>
          <Text style={[styles.settingTitle, { color: colors.text.primary }]}>
            {title}
          </Text>
          <Text
            style={[
              styles.settingDescription,
              { color: colors.text.secondary },
            ]}>
            {description}
          </Text>
        </View>
      </View>
      <Switch
        value={value}
        onValueChange={onValueChange}
        trackColor={{
          false: colors.gray[300],
          true: colors.primary[200],
        }}
        thumbColor={value ? colors.primary[500] : colors.gray[500]}
      />
    </View>
  );

  const renderOptionRow = (
    title: string,
    description: string,
    currentValue: string,
    options: { label: string; value: string }[],
    onValueChange: (value: string) => void,
    icon?: string,
  ) => (
    <TouchableOpacity
      style={styles.settingRow}
      onPress={() => {
        Alert.alert(
          title,
          description,
          options.map(option => ({
            text: option.label,
            onPress: () => onValueChange(option.value),
            style: option.value === currentValue ? 'default' : 'cancel',
          })),
        );
      }}>
      <View style={styles.settingInfo}>
        {icon && (
          <Icon
            name={icon}
            size={20}
            color={colors.text.secondary}
            style={styles.settingIcon}
          />
        )}
        <View style={styles.settingText}>
          <Text style={[styles.settingTitle, { color: colors.text.primary }]}>
            {title}
          </Text>
          <Text
            style={[
              styles.settingDescription,
              { color: colors.text.secondary },
            ]}>
            {description}
          </Text>
        </View>
      </View>
      <View style={styles.settingValue}>
        <Text
          style={[styles.settingValueText, { color: colors.text.secondary }]}>
          {options.find(opt => opt.value === currentValue)?.label}
        </Text>
        <Icon name="chevron-right" size={16} color={colors.text.secondary} />
      </View>
    </TouchableOpacity>
  );

  const renderActionRow = (
    title: string,
    description: string,
    onPress: () => void,
    icon?: string,
    destructive?: boolean,
  ) => (
    <TouchableOpacity style={styles.settingRow} onPress={onPress}>
      <View style={styles.settingInfo}>
        {icon && (
          <Icon
            name={icon}
            size={20}
            color={destructive ? colors.error[500] : colors.text.secondary}
            style={styles.settingIcon}
          />
        )}
        <View style={styles.settingText}>
          <Text
            style={[
              styles.settingTitle,
              { color: destructive ? colors.error[500] : colors.text.primary },
            ]}>
            {title}
          </Text>
          <Text
            style={[
              styles.settingDescription,
              { color: colors.text.secondary },
            ]}>
            {description}
          </Text>
        </View>
      </View>
      <Icon
        name="chevron-right"
        size={16}
        color={destructive ? colors.error[500] : colors.text.secondary}
      />
    </TouchableOpacity>
  );

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={[styles.loadingText, { color: colors.text.secondary }]}>
          {t('provider.settings.loading')}
        </Text>
      </View>
    );
  }

  if (!settings) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={[styles.errorText, { color: colors.error[500] }]}>
          {t('provider.settings.loadError')}
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background?.primary || '#FFFFFF' }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text.primary }]}>
          {t('provider.settings.title')}
        </Text>
        {hasChanges && (
          <Button
            title={t('common.save')}
            variant="primary"
            size="small"
            onPress={handleSaveSettings}
            loading={isSaving}
          />
        )}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Notifications */}
        <Card style={styles.settingsCard}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            {t('provider.settings.notifications')}
          </Text>

          {renderSettingRow(
            t('provider.settings.newJobRequests'),
            t('provider.settings.newJobRequestsDesc'),
            settings.notifications.newJobRequests,
            value => handleNotificationChange('newJobRequests', value),
            'bell',
          )}

          {renderSettingRow(
            t('provider.settings.jobUpdates'),
            t('provider.settings.jobUpdatesDesc'),
            settings.notifications.jobUpdates,
            value => handleNotificationChange('jobUpdates', value),
            'refresh-cw',
          )}

          {renderSettingRow(
            t('provider.settings.paymentNotifications'),
            t('provider.settings.paymentNotificationsDesc'),
            settings.notifications.paymentNotifications,
            value => handleNotificationChange('paymentNotifications', value),
            'credit-card',
          )}

          {renderSettingRow(
            t('provider.settings.pushNotifications'),
            t('provider.settings.pushNotificationsDesc'),
            settings.notifications.pushNotifications,
            value => handleNotificationChange('pushNotifications', value),
            'smartphone',
          )}
        </Card>

        {/* Privacy */}
        <Card style={styles.settingsCard}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            {t('provider.settings.privacy')}
          </Text>

          {renderOptionRow(
            t('provider.settings.profileVisibility'),
            t('provider.settings.profileVisibilityDesc'),
            settings.privacy.profileVisibility,
            [
              { label: t('provider.settings.public'), value: 'public' },
              { label: t('provider.settings.limited'), value: 'limited' },
              { label: t('provider.settings.private'), value: 'private' },
            ],
            value => handlePrivacyChange('profileVisibility', value),
            'eye',
          )}

          {renderSettingRow(
            t('provider.settings.showContactInfo'),
            t('provider.settings.showContactInfoDesc'),
            settings.privacy.showContactInfo,
            value => handlePrivacyChange('showContactInfo', value),
            'phone',
          )}

          {renderSettingRow(
            t('provider.settings.allowDirectMessages'),
            t('provider.settings.allowDirectMessagesDesc'),
            settings.privacy.allowDirectMessages,
            value => handlePrivacyChange('allowDirectMessages', value),
            'message-circle',
          )}
        </Card>

        {/* Payment */}
        <Card style={styles.settingsCard}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            {t('provider.settings.payment')}
          </Text>

          {renderOptionRow(
            t('provider.settings.payoutMethod'),
            t('provider.settings.payoutMethodDesc'),
            settings.payment.preferredPayoutMethod,
            [
              {
                label: t('provider.settings.bankTransfer'),
                value: 'bank_transfer',
              },
              { label: t('provider.settings.paypal'), value: 'paypal' },
              { label: t('provider.settings.stripe'), value: 'stripe' },
            ],
            value => handlePaymentChange('preferredPayoutMethod', value),
            'credit-card',
          )}

          {renderOptionRow(
            t('provider.settings.payoutSchedule'),
            t('provider.settings.payoutScheduleDesc'),
            settings.payment.payoutSchedule,
            [
              { label: t('provider.settings.weekly'), value: 'weekly' },
              { label: t('provider.settings.biweekly'), value: 'biweekly' },
              { label: t('provider.settings.monthly'), value: 'monthly' },
            ],
            value => handlePaymentChange('payoutSchedule', value),
            'calendar',
          )}
        </Card>

        {/* General */}
        <Card style={styles.settingsCard}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            {t('provider.settings.general')}
          </Text>

          {renderSettingRow(
            t('provider.settings.autoAcceptJobs'),
            t('provider.settings.autoAcceptJobsDesc'),
            settings.autoAcceptJobs,
            value => handleGeneralChange('autoAcceptJobs', value),
            'zap',
          )}

          {renderOptionRow(
            t('provider.settings.language'),
            t('provider.settings.languageDesc'),
            settings.language,
            [
              { label: 'English', value: 'en' },
              { label: 'Français', value: 'fr' },
            ],
            value => handleGeneralChange('language', value),
            'globe',
          )}
        </Card>

        {/* Support & Help */}
        <Card style={styles.settingsCard}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            {t('provider.settings.support')}
          </Text>

          {renderActionRow(
            t('provider.settings.helpCenter'),
            t('provider.settings.helpCenterDesc'),
            () => console.log('Open help center'),
            'help-circle',
          )}

          {renderActionRow(
            t('provider.settings.contactSupport'),
            t('provider.settings.contactSupportDesc'),
            () => console.log('Contact support'),
            'message-square',
          )}

          {renderActionRow(
            t('provider.settings.reportIssue'),
            t('provider.settings.reportIssueDesc'),
            () => console.log('Report issue'),
            'flag',
          )}
        </Card>

        {/* Account Actions */}
        <Card style={styles.settingsCard}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            {t('provider.settings.account')}
          </Text>

          {renderActionRow(
            t('provider.settings.changePassword'),
            t('provider.settings.changePasswordDesc'),
            () => console.log('Change password'),
            'lock',
          )}

          {renderActionRow(
            t('provider.settings.deleteAccount'),
            t('provider.settings.deleteAccountDesc'),
            () => {
              Alert.alert(
                t('provider.settings.deleteAccount'),
                t('provider.settings.deleteAccountConfirm'),
                [
                  { text: t('common.cancel'), style: 'cancel' },
                  {
                    text: t('common.delete'),
                    style: 'destructive',
                    onPress: () => console.log('Delete account'),
                  },
                ],
              );
            },
            'trash-2',
            true,
          )}
        </Card>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  errorText: {
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    paddingBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  settingsCard: {
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  settingInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingIcon: {
    marginRight: 12,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
    lineHeight: 18,
  },
  settingValue: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingValueText: {
    fontSize: 14,
    marginRight: 8,
  },
});
