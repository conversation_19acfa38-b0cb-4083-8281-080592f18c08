{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_get2", "_inherits2", "_AnimatedValue", "_AnimatedWithChildren2", "_invariant", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "_uniqueId", "AnimatedValueXY", "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "valueIn", "config", "_this", "x", "y", "AnimatedValue", "invariant", "_listeners", "useNativeDriver", "__makeNative", "key", "setValue", "setOffset", "offset", "flattenOffset", "extractOffset", "__getValue", "resetAnimation", "callback", "stopAnimation", "addListener", "_this2", "id", "String", "jointCallback", "_ref", "number", "removeListener", "removeAllListeners", "getLayout", "left", "top", "getTranslateTransform", "translateX", "translateY", "__attach", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d", "platformConfig", "AnimatedWithChildren"], "sources": ["AnimatedValueXY.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n */\n\n'use strict';\n\nimport type {PlatformConfig} from '../AnimatedPlatformConfig';\nimport type {AnimatedNodeConfig} from './AnimatedNode';\n\nimport AnimatedValue from './AnimatedValue';\nimport AnimatedWithChildren from './AnimatedWithChildren';\nimport invariant from 'invariant';\n\nexport type AnimatedValueXYConfig = $ReadOnly<{\n  ...AnimatedNodeConfig,\n  useNativeDriver: boolean,\n}>;\ntype ValueXYListenerCallback = (value: {x: number, y: number, ...}) => mixed;\n\nlet _uniqueId = 1;\n\n/**\n * 2D Value for driving 2D animations, such as pan gestures. Almost identical\n * API to normal `Animated.Value`, but multiplexed.\n *\n * See https://reactnative.dev/docs/animatedvaluexy\n */\nexport default class AnimatedValueXY extends AnimatedWithChildren {\n  x: AnimatedValue;\n  y: AnimatedValue;\n  _listeners: {\n    [key: string]: {\n      x: string,\n      y: string,\n      ...\n    },\n    ...\n  };\n\n  constructor(\n    valueIn?: ?{\n      +x: number | AnimatedValue,\n      +y: number | AnimatedValue,\n      ...\n    },\n    config?: ?AnimatedValueXYConfig,\n  ) {\n    super(config);\n    const value: any = valueIn || {x: 0, y: 0}; // @flowfixme: shouldn't need `: any`\n    if (typeof value.x === 'number' && typeof value.y === 'number') {\n      this.x = new AnimatedValue(value.x);\n      this.y = new AnimatedValue(value.y);\n    } else {\n      invariant(\n        value.x instanceof AnimatedValue && value.y instanceof AnimatedValue,\n        'AnimatedValueXY must be initialized with an object of numbers or ' +\n          'AnimatedValues.',\n      );\n      this.x = value.x;\n      this.y = value.y;\n    }\n    this._listeners = {};\n    if (config && config.useNativeDriver) {\n      this.__makeNative();\n    }\n  }\n\n  /**\n   * Directly set the value. This will stop any animations running on the value\n   * and update all the bound properties.\n   *\n   * See https://reactnative.dev/docs/animatedvaluexy#setvalue\n   */\n  setValue(value: {x: number, y: number, ...}) {\n    this.x.setValue(value.x);\n    this.y.setValue(value.y);\n  }\n\n  /**\n   * Sets an offset that is applied on top of whatever value is set, whether\n   * via `setValue`, an animation, or `Animated.event`. Useful for compensating\n   * things like the start of a pan gesture.\n   *\n   * See https://reactnative.dev/docs/animatedvaluexy#setoffset\n   */\n  setOffset(offset: {x: number, y: number, ...}) {\n    this.x.setOffset(offset.x);\n    this.y.setOffset(offset.y);\n  }\n\n  /**\n   * Merges the offset value into the base value and resets the offset to zero.\n   * The final output of the value is unchanged.\n   *\n   * See https://reactnative.dev/docs/animatedvaluexy#flattenoffset\n   */\n  flattenOffset(): void {\n    this.x.flattenOffset();\n    this.y.flattenOffset();\n  }\n\n  /**\n   * Sets the offset value to the base value, and resets the base value to\n   * zero. The final output of the value is unchanged.\n   *\n   * See https://reactnative.dev/docs/animatedvaluexy#extractoffset\n   */\n  extractOffset(): void {\n    this.x.extractOffset();\n    this.y.extractOffset();\n  }\n\n  __getValue(): {\n    x: number,\n    y: number,\n    ...\n  } {\n    return {\n      x: this.x.__getValue(),\n      y: this.y.__getValue(),\n    };\n  }\n\n  /**\n   * Stops any animation and resets the value to its original.\n   *\n   * See https://reactnative.dev/docs/animatedvaluexy#resetanimation\n   */\n  resetAnimation(\n    callback?: (value: {x: number, y: number, ...}) => void,\n  ): void {\n    this.x.resetAnimation();\n    this.y.resetAnimation();\n    callback && callback(this.__getValue());\n  }\n\n  /**\n   * Stops any running animation or tracking. `callback` is invoked with the\n   * final value after stopping the animation, which is useful for updating\n   * state to match the animation position with layout.\n   *\n   * See https://reactnative.dev/docs/animatedvaluexy#stopanimation\n   */\n  stopAnimation(callback?: (value: {x: number, y: number, ...}) => void): void {\n    this.x.stopAnimation();\n    this.y.stopAnimation();\n    callback && callback(this.__getValue());\n  }\n\n  /**\n   * Adds an asynchronous listener to the value so you can observe updates from\n   * animations.  This is useful because there is no way to synchronously read\n   * the value because it might be driven natively.\n   *\n   * Returns a string that serves as an identifier for the listener.\n   *\n   * See https://reactnative.dev/docs/animatedvaluexy#addlistener\n   */\n  addListener(callback: ValueXYListenerCallback): string {\n    const id = String(_uniqueId++);\n    const jointCallback = ({value: number}: any) => {\n      callback(this.__getValue());\n    };\n    this._listeners[id] = {\n      x: this.x.addListener(jointCallback),\n      y: this.y.addListener(jointCallback),\n    };\n    return id;\n  }\n\n  /**\n   * Unregister a listener. The `id` param shall match the identifier\n   * previously returned by `addListener()`.\n   *\n   * See https://reactnative.dev/docs/animatedvaluexy#removelistener\n   */\n  removeListener(id: string): void {\n    this.x.removeListener(this._listeners[id].x);\n    this.y.removeListener(this._listeners[id].y);\n    delete this._listeners[id];\n  }\n\n  /**\n   * Remove all registered listeners.\n   *\n   * See https://reactnative.dev/docs/animatedvaluexy#removealllisteners\n   */\n  removeAllListeners(): void {\n    this.x.removeAllListeners();\n    this.y.removeAllListeners();\n    this._listeners = {};\n  }\n\n  /**\n   * Converts `{x, y}` into `{left, top}` for use in style.\n   *\n   * See https://reactnative.dev/docs/animatedvaluexy#getlayout\n   */\n  getLayout(): {[key: string]: AnimatedValue, ...} {\n    return {\n      left: this.x,\n      top: this.y,\n    };\n  }\n\n  /**\n   * Converts `{x, y}` into a useable translation transform.\n   *\n   * See https://reactnative.dev/docs/animatedvaluexy#gettranslatetransform\n   */\n  getTranslateTransform(): Array<{[key: string]: AnimatedValue, ...}> {\n    return [{translateX: this.x}, {translateY: this.y}];\n  }\n\n  __attach(): void {\n    this.x.__addChild(this);\n    this.y.__addChild(this);\n    super.__attach();\n  }\n\n  __detach(): void {\n    this.x.__removeChild(this);\n    this.y.__removeChild(this);\n    super.__detach();\n  }\n\n  __makeNative(platformConfig: ?PlatformConfig) {\n    this.x.__makeNative(platformConfig);\n    this.y.__makeNative(platformConfig);\n    super.__makeNative(platformConfig);\n  }\n}\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,2BAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,gBAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,KAAA,GAAAX,sBAAA,CAAAC,OAAA;AAAA,IAAAW,UAAA,GAAAZ,sBAAA,CAAAC,OAAA;AAKb,IAAAY,cAAA,GAAAb,sBAAA,CAAAC,OAAA;AACA,IAAAa,sBAAA,GAAAd,sBAAA,CAAAC,OAAA;AACA,IAAAc,UAAA,GAAAf,sBAAA,CAAAC,OAAA;AAAkC,SAAAe,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAR,gBAAA,CAAAJ,OAAA,EAAAY,CAAA,OAAAT,2BAAA,CAAAH,OAAA,EAAAW,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,YAAAT,gBAAA,CAAAJ,OAAA,EAAAW,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAAA,SAAAY,cAAAZ,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAW,CAAA,QAAAC,CAAA,OAAApB,KAAA,CAAAL,OAAA,MAAAI,gBAAA,CAAAJ,OAAA,MAAAwB,CAAA,GAAAb,CAAA,CAAAS,SAAA,GAAAT,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAW,CAAA,yBAAAC,CAAA,aAAAd,CAAA,WAAAc,CAAA,CAAAP,KAAA,CAAAL,CAAA,EAAAF,CAAA,OAAAc,CAAA;AAQlC,IAAIC,SAAS,GAAG,CAAC;AAAC,IAQGC,eAAe,GAAA7B,OAAA,CAAAE,OAAA,aAAA4B,qBAAA;EAYlC,SAAAD,gBACEE,OAIC,EACDC,MAA+B,EAC/B;IAAA,IAAAC,KAAA;IAAA,IAAA9B,gBAAA,CAAAD,OAAA,QAAA2B,eAAA;IACAI,KAAA,GAAArB,UAAA,OAAAiB,eAAA,GAAMG,MAAM;IACZ,IAAM/B,KAAU,GAAG8B,OAAO,IAAI;MAACG,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAC,CAAC;IAC1C,IAAI,OAAOlC,KAAK,CAACiC,CAAC,KAAK,QAAQ,IAAI,OAAOjC,KAAK,CAACkC,CAAC,KAAK,QAAQ,EAAE;MAC9DF,KAAA,CAAKC,CAAC,GAAG,IAAIE,sBAAa,CAACnC,KAAK,CAACiC,CAAC,CAAC;MACnCD,KAAA,CAAKE,CAAC,GAAG,IAAIC,sBAAa,CAACnC,KAAK,CAACkC,CAAC,CAAC;IACrC,CAAC,MAAM;MACL,IAAAE,kBAAS,EACPpC,KAAK,CAACiC,CAAC,YAAYE,sBAAa,IAAInC,KAAK,CAACkC,CAAC,YAAYC,sBAAa,EACpE,mEAAmE,GACjE,iBACJ,CAAC;MACDH,KAAA,CAAKC,CAAC,GAAGjC,KAAK,CAACiC,CAAC;MAChBD,KAAA,CAAKE,CAAC,GAAGlC,KAAK,CAACkC,CAAC;IAClB;IACAF,KAAA,CAAKK,UAAU,GAAG,CAAC,CAAC;IACpB,IAAIN,MAAM,IAAIA,MAAM,CAACO,eAAe,EAAE;MACpCN,KAAA,CAAKO,YAAY,CAAC,CAAC;IACrB;IAAC,OAAAP,KAAA;EACH;EAAC,IAAAzB,UAAA,CAAAN,OAAA,EAAA2B,eAAA,EAAAC,qBAAA;EAAA,WAAA1B,aAAA,CAAAF,OAAA,EAAA2B,eAAA;IAAAY,GAAA;IAAAxC,KAAA,EAQD,SAAAyC,QAAQA,CAACzC,KAAkC,EAAE;MAC3C,IAAI,CAACiC,CAAC,CAACQ,QAAQ,CAACzC,KAAK,CAACiC,CAAC,CAAC;MACxB,IAAI,CAACC,CAAC,CAACO,QAAQ,CAACzC,KAAK,CAACkC,CAAC,CAAC;IAC1B;EAAC;IAAAM,GAAA;IAAAxC,KAAA,EASD,SAAA0C,SAASA,CAACC,MAAmC,EAAE;MAC7C,IAAI,CAACV,CAAC,CAACS,SAAS,CAACC,MAAM,CAACV,CAAC,CAAC;MAC1B,IAAI,CAACC,CAAC,CAACQ,SAAS,CAACC,MAAM,CAACT,CAAC,CAAC;IAC5B;EAAC;IAAAM,GAAA;IAAAxC,KAAA,EAQD,SAAA4C,aAAaA,CAAA,EAAS;MACpB,IAAI,CAACX,CAAC,CAACW,aAAa,CAAC,CAAC;MACtB,IAAI,CAACV,CAAC,CAACU,aAAa,CAAC,CAAC;IACxB;EAAC;IAAAJ,GAAA;IAAAxC,KAAA,EAQD,SAAA6C,aAAaA,CAAA,EAAS;MACpB,IAAI,CAACZ,CAAC,CAACY,aAAa,CAAC,CAAC;MACtB,IAAI,CAACX,CAAC,CAACW,aAAa,CAAC,CAAC;IACxB;EAAC;IAAAL,GAAA;IAAAxC,KAAA,EAED,SAAA8C,UAAUA,CAAA,EAIR;MACA,OAAO;QACLb,CAAC,EAAE,IAAI,CAACA,CAAC,CAACa,UAAU,CAAC,CAAC;QACtBZ,CAAC,EAAE,IAAI,CAACA,CAAC,CAACY,UAAU,CAAC;MACvB,CAAC;IACH;EAAC;IAAAN,GAAA;IAAAxC,KAAA,EAOD,SAAA+C,cAAcA,CACZC,QAAuD,EACjD;MACN,IAAI,CAACf,CAAC,CAACc,cAAc,CAAC,CAAC;MACvB,IAAI,CAACb,CAAC,CAACa,cAAc,CAAC,CAAC;MACvBC,QAAQ,IAAIA,QAAQ,CAAC,IAAI,CAACF,UAAU,CAAC,CAAC,CAAC;IACzC;EAAC;IAAAN,GAAA;IAAAxC,KAAA,EASD,SAAAiD,aAAaA,CAACD,QAAuD,EAAQ;MAC3E,IAAI,CAACf,CAAC,CAACgB,aAAa,CAAC,CAAC;MACtB,IAAI,CAACf,CAAC,CAACe,aAAa,CAAC,CAAC;MACtBD,QAAQ,IAAIA,QAAQ,CAAC,IAAI,CAACF,UAAU,CAAC,CAAC,CAAC;IACzC;EAAC;IAAAN,GAAA;IAAAxC,KAAA,EAWD,SAAAkD,WAAWA,CAACF,QAAiC,EAAU;MAAA,IAAAG,MAAA;MACrD,IAAMC,EAAE,GAAGC,MAAM,CAAC1B,SAAS,EAAE,CAAC;MAC9B,IAAM2B,aAAa,GAAG,SAAhBA,aAAaA,CAAAC,IAAA,EAA6B;QAAA,IAAjBC,MAAM,GAAAD,IAAA,CAAbvD,KAAK;QAC3BgD,QAAQ,CAACG,MAAI,CAACL,UAAU,CAAC,CAAC,CAAC;MAC7B,CAAC;MACD,IAAI,CAACT,UAAU,CAACe,EAAE,CAAC,GAAG;QACpBnB,CAAC,EAAE,IAAI,CAACA,CAAC,CAACiB,WAAW,CAACI,aAAa,CAAC;QACpCpB,CAAC,EAAE,IAAI,CAACA,CAAC,CAACgB,WAAW,CAACI,aAAa;MACrC,CAAC;MACD,OAAOF,EAAE;IACX;EAAC;IAAAZ,GAAA;IAAAxC,KAAA,EAQD,SAAAyD,cAAcA,CAACL,EAAU,EAAQ;MAC/B,IAAI,CAACnB,CAAC,CAACwB,cAAc,CAAC,IAAI,CAACpB,UAAU,CAACe,EAAE,CAAC,CAACnB,CAAC,CAAC;MAC5C,IAAI,CAACC,CAAC,CAACuB,cAAc,CAAC,IAAI,CAACpB,UAAU,CAACe,EAAE,CAAC,CAAClB,CAAC,CAAC;MAC5C,OAAO,IAAI,CAACG,UAAU,CAACe,EAAE,CAAC;IAC5B;EAAC;IAAAZ,GAAA;IAAAxC,KAAA,EAOD,SAAA0D,kBAAkBA,CAAA,EAAS;MACzB,IAAI,CAACzB,CAAC,CAACyB,kBAAkB,CAAC,CAAC;MAC3B,IAAI,CAACxB,CAAC,CAACwB,kBAAkB,CAAC,CAAC;MAC3B,IAAI,CAACrB,UAAU,GAAG,CAAC,CAAC;IACtB;EAAC;IAAAG,GAAA;IAAAxC,KAAA,EAOD,SAAA2D,SAASA,CAAA,EAAwC;MAC/C,OAAO;QACLC,IAAI,EAAE,IAAI,CAAC3B,CAAC;QACZ4B,GAAG,EAAE,IAAI,CAAC3B;MACZ,CAAC;IACH;EAAC;IAAAM,GAAA;IAAAxC,KAAA,EAOD,SAAA8D,qBAAqBA,CAAA,EAA+C;MAClE,OAAO,CAAC;QAACC,UAAU,EAAE,IAAI,CAAC9B;MAAC,CAAC,EAAE;QAAC+B,UAAU,EAAE,IAAI,CAAC9B;MAAC,CAAC,CAAC;IACrD;EAAC;IAAAM,GAAA;IAAAxC,KAAA,EAED,SAAAiE,QAAQA,CAAA,EAAS;MACf,IAAI,CAAChC,CAAC,CAACiC,UAAU,CAAC,IAAI,CAAC;MACvB,IAAI,CAAChC,CAAC,CAACgC,UAAU,CAAC,IAAI,CAAC;MACvB1C,aAAA,CAAAI,eAAA;IACF;EAAC;IAAAY,GAAA;IAAAxC,KAAA,EAED,SAAAmE,QAAQA,CAAA,EAAS;MACf,IAAI,CAAClC,CAAC,CAACmC,aAAa,CAAC,IAAI,CAAC;MAC1B,IAAI,CAAClC,CAAC,CAACkC,aAAa,CAAC,IAAI,CAAC;MAC1B5C,aAAA,CAAAI,eAAA;IACF;EAAC;IAAAY,GAAA;IAAAxC,KAAA,EAED,SAAAuC,YAAYA,CAAC8B,cAA+B,EAAE;MAC5C,IAAI,CAACpC,CAAC,CAACM,YAAY,CAAC8B,cAAc,CAAC;MACnC,IAAI,CAACnC,CAAC,CAACK,YAAY,CAAC8B,cAAc,CAAC;MACnC7C,aAAA,CAAAI,eAAA,4BAAmByC,cAAc;IACnC;EAAC;AAAA,EA3M0CC,8BAAoB", "ignoreList": []}