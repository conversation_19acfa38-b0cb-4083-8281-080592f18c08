{"version": 3, "names": ["_getJest<PERSON>bj", "mock", "testAccountsService", "getAccountsByRole", "jest", "fn", "getProvidersByCategory", "_interopRequireDefault", "require", "_asyncToGenerator2", "_reactNative", "_testAccountsService", "_providersSlice", "_require", "mockTestAccountsService", "mockTestAccount", "id", "email", "password", "firstName", "lastName", "role", "category", "city", "description", "mockProvider", "created_at", "updated_at", "user", "business_name", "business_phone", "business_email", "website", "undefined", "instagram_handle", "address", "state", "zip_code", "latitude", "longitude", "cover_image", "profile_image", "rating", "review_count", "is_verified", "is_featured", "is_active", "categories", "operating_hours", "distance", "price_range", "describe", "beforeEach", "mockClear", "it", "_renderHook", "renderHook", "useProvidersStore", "result", "expect", "current", "providers", "toEqual", "loading", "toBe", "error", "toBeNull", "filters", "default", "mockReturnValue", "_renderHook2", "act", "fetchProviders", "toHave<PERSON>ength", "_result$current$error", "errorMessage", "mockImplementation", "Error", "_renderHook3", "message", "_renderHook4", "newFilters", "rating_min", "price_max", "updateFilters", "_renderHook5", "clearFilters", "_renderHook6", "searchProviders"], "sources": ["providersSlice.test.ts"], "sourcesContent": ["/**\n * Providers Store Tests - TDD Implementation\n * Following Red-Green-Refactor methodology\n * Testing Zustand store implementation\n */\n\nimport { act, renderHook } from '@testing-library/react-native';\n\nimport { testAccountsService } from '../../services/testAccountsService';\nimport { useProvidersStore } from '../providersSlice';\n\n// Mock the test accounts service\njest.mock('../../services/testAccountsService', () => ({\n  testAccountsService: {\n    getAccountsByRole: jest.fn(),\n    getProvidersByCategory: jest.fn(),\n  },\n}));\n\nconst mockTestAccountsService = testAccountsService as jest.Mocked<\n  typeof testAccountsService\n>;\n\n// Mock data\nconst mockTestAccount = {\n  id: '1',\n  email: '<EMAIL>',\n  password: 'TestPass123!',\n  firstName: 'John',\n  lastName: 'Doe',\n  role: 'service_provider' as const,\n  category: 'Hair Services',\n  city: 'Toronto',\n  description: 'Professional hair stylist with 10 years experience',\n};\n\nconst mockProvider = {\n  id: 'provider_1',\n  created_at: '2024-01-01T00:00:00Z',\n  updated_at: '2024-01-01T00:00:00Z',\n  user: 'user_1',\n  business_name: 'John Doe Hair Services',\n  description: 'Professional hair stylist with 10 years experience',\n  business_phone: '555-0123',\n  business_email: '<EMAIL>',\n  website: undefined,\n  instagram_handle: undefined,\n  address: '123 Main St',\n  city: 'Toronto',\n  state: 'ON',\n  zip_code: '12345',\n  latitude: 43.6532,\n  longitude: -79.3832,\n  cover_image: undefined,\n  profile_image: undefined,\n  rating: 4.5,\n  review_count: 25,\n  is_verified: true,\n  is_featured: false,\n  is_active: true,\n  categories: ['Hair Services'],\n  operating_hours: undefined,\n  distance: '2.5 km',\n  price_range: '$50-150',\n};\n\ndescribe('useProvidersStore', () => {\n  beforeEach(() => {\n    // Reset mocks\n    mockTestAccountsService.getAccountsByRole.mockClear();\n    mockTestAccountsService.getProvidersByCategory.mockClear();\n  });\n\n  describe('initial state', () => {\n    it('should have correct initial state', () => {\n      const { result } = renderHook(() => useProvidersStore());\n\n      expect(result.current.providers).toEqual([]);\n      expect(result.current.loading).toBe(false);\n      expect(result.current.error).toBeNull();\n      expect(result.current.filters).toEqual({});\n    });\n  });\n\n  describe('fetchProviders', () => {\n    it('should fetch providers successfully', async () => {\n      mockTestAccountsService.getAccountsByRole.mockReturnValue([\n        mockTestAccount,\n      ]);\n\n      const { result } = renderHook(() => useProvidersStore());\n\n      await act(async () => {\n        await result.current.fetchProviders();\n      });\n\n      expect(result.current.loading).toBe(false);\n      expect(result.current.providers).toHaveLength(1);\n      expect(result.current.providers[0].business_name).toBe('John Doe');\n      expect(result.current.error).toBeNull();\n    });\n\n    it('should handle loading error', async () => {\n      const errorMessage = 'Failed to load providers';\n      mockTestAccountsService.getAccountsByRole.mockImplementation(() => {\n        throw new Error(errorMessage);\n      });\n\n      const { result } = renderHook(() => useProvidersStore());\n\n      await act(async () => {\n        await result.current.fetchProviders();\n      });\n\n      expect(result.current.loading).toBe(false);\n      expect(result.current.providers).toEqual([]);\n      expect(result.current.error?.message).toBe(errorMessage);\n    });\n  });\n\n  describe('updateFilters', () => {\n    it('should update filters correctly', () => {\n      const { result } = renderHook(() => useProvidersStore());\n\n      const newFilters = {\n        category: 'Hair Services',\n        rating_min: 4.0,\n        price_max: 100,\n      };\n\n      act(() => {\n        result.current.updateFilters(newFilters);\n      });\n\n      expect(result.current.filters).toEqual(newFilters);\n    });\n  });\n\n  describe('clearFilters', () => {\n    it('should clear all filters', () => {\n      const { result } = renderHook(() => useProvidersStore());\n\n      // Set some filters first\n      act(() => {\n        result.current.updateFilters({\n          category: 'Hair Services',\n          rating_min: 4.0,\n        });\n      });\n\n      // Clear filters\n      act(() => {\n        result.current.clearFilters();\n      });\n\n      expect(result.current.filters).toEqual({});\n    });\n  });\n\n  describe('searchProviders', () => {\n    it('should search providers with filters', async () => {\n      mockTestAccountsService.getAccountsByRole.mockReturnValue([\n        mockTestAccount,\n      ]);\n      mockTestAccountsService.getProvidersByCategory.mockReturnValue([\n        mockTestAccount,\n      ]);\n\n      const { result } = renderHook(() => useProvidersStore());\n\n      const filters = {\n        category: 'Hair Services',\n        rating_min: 4.0,\n      };\n\n      await act(async () => {\n        await result.current.searchProviders(filters);\n      });\n\n      expect(result.current.loading).toBe(false);\n      expect(result.current.filters).toEqual(filters);\n      expect(result.current.providers).toHaveLength(1);\n      expect(result.current.error).toBeNull();\n    });\n  });\n});\n"], "mappings": "AAYAA,WAAA,GAAKC,IAAI,uCAAuC;EAAA,OAAO;IACrDC,mBAAmB,EAAE;MACnBC,iBAAiB,EAAEC,IAAI,CAACC,EAAE,CAAC,CAAC;MAC5BC,sBAAsB,EAAEF,IAAI,CAACC,EAAE,CAAC;IAClC;EACF,CAAC;AAAA,CAAC,CAAC;AAAC,IAAAE,sBAAA,GAAAC,OAAA;AAAA,IAAAC,kBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAXJ,IAAAE,YAAA,GAAAF,OAAA;AAEA,IAAAG,oBAAA,GAAAH,OAAA;AACA,IAAAI,eAAA,GAAAJ,OAAA;AAAsD,SAAAR,YAAA;EAAA,IAAAa,QAAA,GAAAL,OAAA;IAAAJ,IAAA,GAAAS,QAAA,CAAAT,IAAA;EAAAJ,WAAA,YAAAA,YAAA;IAAA,OAAAI,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAUtD,IAAMU,uBAAuB,GAAGZ,wCAE/B;AAGD,IAAMa,eAAe,GAAG;EACtBC,EAAE,EAAE,GAAG;EACPC,KAAK,EAAE,mBAAmB;EAC1BC,QAAQ,EAAE,cAAc;EACxBC,SAAS,EAAE,MAAM;EACjBC,QAAQ,EAAE,KAAK;EACfC,IAAI,EAAE,kBAA2B;EACjCC,QAAQ,EAAE,eAAe;EACzBC,IAAI,EAAE,SAAS;EACfC,WAAW,EAAE;AACf,CAAC;AAED,IAAMC,YAAY,GAAG;EACnBT,EAAE,EAAE,YAAY;EAChBU,UAAU,EAAE,sBAAsB;EAClCC,UAAU,EAAE,sBAAsB;EAClCC,IAAI,EAAE,QAAQ;EACdC,aAAa,EAAE,wBAAwB;EACvCL,WAAW,EAAE,oDAAoD;EACjEM,cAAc,EAAE,UAAU;EAC1BC,cAAc,EAAE,mBAAmB;EACnCC,OAAO,EAAEC,SAAS;EAClBC,gBAAgB,EAAED,SAAS;EAC3BE,OAAO,EAAE,aAAa;EACtBZ,IAAI,EAAE,SAAS;EACfa,KAAK,EAAE,IAAI;EACXC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE,OAAO;EACjBC,SAAS,EAAE,CAAC,OAAO;EACnBC,WAAW,EAAEP,SAAS;EACtBQ,aAAa,EAAER,SAAS;EACxBS,MAAM,EAAE,GAAG;EACXC,YAAY,EAAE,EAAE;EAChBC,WAAW,EAAE,IAAI;EACjBC,WAAW,EAAE,KAAK;EAClBC,SAAS,EAAE,IAAI;EACfC,UAAU,EAAE,CAAC,eAAe,CAAC;EAC7BC,eAAe,EAAEf,SAAS;EAC1BgB,QAAQ,EAAE,QAAQ;EAClBC,WAAW,EAAE;AACf,CAAC;AAEDC,QAAQ,CAAC,mBAAmB,EAAE,YAAM;EAClCC,UAAU,CAAC,YAAM;IAEftC,uBAAuB,CAACX,iBAAiB,CAACkD,SAAS,CAAC,CAAC;IACrDvC,uBAAuB,CAACR,sBAAsB,CAAC+C,SAAS,CAAC,CAAC;EAC5D,CAAC,CAAC;EAEFF,QAAQ,CAAC,eAAe,EAAE,YAAM;IAC9BG,EAAE,CAAC,mCAAmC,EAAE,YAAM;MAC5C,IAAAC,WAAA,GAAmB,IAAAC,uBAAU,EAAC;UAAA,OAAM,IAAAC,iCAAiB,EAAC,CAAC;QAAA,EAAC;QAAhDC,MAAM,GAAAH,WAAA,CAANG,MAAM;MAEdC,MAAM,CAACD,MAAM,CAACE,OAAO,CAACC,SAAS,CAAC,CAACC,OAAO,CAAC,EAAE,CAAC;MAC5CH,MAAM,CAACD,MAAM,CAACE,OAAO,CAACG,OAAO,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MAC1CL,MAAM,CAACD,MAAM,CAACE,OAAO,CAACK,KAAK,CAAC,CAACC,QAAQ,CAAC,CAAC;MACvCP,MAAM,CAACD,MAAM,CAACE,OAAO,CAACO,OAAO,CAAC,CAACL,OAAO,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFX,QAAQ,CAAC,gBAAgB,EAAE,YAAM;IAC/BG,EAAE,CAAC,qCAAqC,MAAA7C,kBAAA,CAAA2D,OAAA,EAAE,aAAY;MACpDtD,uBAAuB,CAACX,iBAAiB,CAACkE,eAAe,CAAC,CACxDtD,eAAe,CAChB,CAAC;MAEF,IAAAuD,YAAA,GAAmB,IAAAd,uBAAU,EAAC;UAAA,OAAM,IAAAC,iCAAiB,EAAC,CAAC;QAAA,EAAC;QAAhDC,MAAM,GAAAY,YAAA,CAANZ,MAAM;MAEd,MAAM,IAAAa,gBAAG,MAAA9D,kBAAA,CAAA2D,OAAA,EAAC,aAAY;QACpB,MAAMV,MAAM,CAACE,OAAO,CAACY,cAAc,CAAC,CAAC;MACvC,CAAC,EAAC;MAEFb,MAAM,CAACD,MAAM,CAACE,OAAO,CAACG,OAAO,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MAC1CL,MAAM,CAACD,MAAM,CAACE,OAAO,CAACC,SAAS,CAAC,CAACY,YAAY,CAAC,CAAC,CAAC;MAChDd,MAAM,CAACD,MAAM,CAACE,OAAO,CAACC,SAAS,CAAC,CAAC,CAAC,CAAChC,aAAa,CAAC,CAACmC,IAAI,CAAC,UAAU,CAAC;MAClEL,MAAM,CAACD,MAAM,CAACE,OAAO,CAACK,KAAK,CAAC,CAACC,QAAQ,CAAC,CAAC;IACzC,CAAC,EAAC;IAEFZ,EAAE,CAAC,6BAA6B,MAAA7C,kBAAA,CAAA2D,OAAA,EAAE,aAAY;MAAA,IAAAM,qBAAA;MAC5C,IAAMC,YAAY,GAAG,0BAA0B;MAC/C7D,uBAAuB,CAACX,iBAAiB,CAACyE,kBAAkB,CAAC,YAAM;QACjE,MAAM,IAAIC,KAAK,CAACF,YAAY,CAAC;MAC/B,CAAC,CAAC;MAEF,IAAAG,YAAA,GAAmB,IAAAtB,uBAAU,EAAC;UAAA,OAAM,IAAAC,iCAAiB,EAAC,CAAC;QAAA,EAAC;QAAhDC,MAAM,GAAAoB,YAAA,CAANpB,MAAM;MAEd,MAAM,IAAAa,gBAAG,MAAA9D,kBAAA,CAAA2D,OAAA,EAAC,aAAY;QACpB,MAAMV,MAAM,CAACE,OAAO,CAACY,cAAc,CAAC,CAAC;MACvC,CAAC,EAAC;MAEFb,MAAM,CAACD,MAAM,CAACE,OAAO,CAACG,OAAO,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MAC1CL,MAAM,CAACD,MAAM,CAACE,OAAO,CAACC,SAAS,CAAC,CAACC,OAAO,CAAC,EAAE,CAAC;MAC5CH,MAAM,EAAAe,qBAAA,GAAChB,MAAM,CAACE,OAAO,CAACK,KAAK,qBAApBS,qBAAA,CAAsBK,OAAO,CAAC,CAACf,IAAI,CAACW,YAAY,CAAC;IAC1D,CAAC,EAAC;EACJ,CAAC,CAAC;EAEFxB,QAAQ,CAAC,eAAe,EAAE,YAAM;IAC9BG,EAAE,CAAC,iCAAiC,EAAE,YAAM;MAC1C,IAAA0B,YAAA,GAAmB,IAAAxB,uBAAU,EAAC;UAAA,OAAM,IAAAC,iCAAiB,EAAC,CAAC;QAAA,EAAC;QAAhDC,MAAM,GAAAsB,YAAA,CAANtB,MAAM;MAEd,IAAMuB,UAAU,GAAG;QACjB3D,QAAQ,EAAE,eAAe;QACzB4D,UAAU,EAAE,GAAG;QACfC,SAAS,EAAE;MACb,CAAC;MAED,IAAAZ,gBAAG,EAAC,YAAM;QACRb,MAAM,CAACE,OAAO,CAACwB,aAAa,CAACH,UAAU,CAAC;MAC1C,CAAC,CAAC;MAEFtB,MAAM,CAACD,MAAM,CAACE,OAAO,CAACO,OAAO,CAAC,CAACL,OAAO,CAACmB,UAAU,CAAC;IACpD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF9B,QAAQ,CAAC,cAAc,EAAE,YAAM;IAC7BG,EAAE,CAAC,0BAA0B,EAAE,YAAM;MACnC,IAAA+B,YAAA,GAAmB,IAAA7B,uBAAU,EAAC;UAAA,OAAM,IAAAC,iCAAiB,EAAC,CAAC;QAAA,EAAC;QAAhDC,MAAM,GAAA2B,YAAA,CAAN3B,MAAM;MAGd,IAAAa,gBAAG,EAAC,YAAM;QACRb,MAAM,CAACE,OAAO,CAACwB,aAAa,CAAC;UAC3B9D,QAAQ,EAAE,eAAe;UACzB4D,UAAU,EAAE;QACd,CAAC,CAAC;MACJ,CAAC,CAAC;MAGF,IAAAX,gBAAG,EAAC,YAAM;QACRb,MAAM,CAACE,OAAO,CAAC0B,YAAY,CAAC,CAAC;MAC/B,CAAC,CAAC;MAEF3B,MAAM,CAACD,MAAM,CAACE,OAAO,CAACO,OAAO,CAAC,CAACL,OAAO,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFX,QAAQ,CAAC,iBAAiB,EAAE,YAAM;IAChCG,EAAE,CAAC,sCAAsC,MAAA7C,kBAAA,CAAA2D,OAAA,EAAE,aAAY;MACrDtD,uBAAuB,CAACX,iBAAiB,CAACkE,eAAe,CAAC,CACxDtD,eAAe,CAChB,CAAC;MACFD,uBAAuB,CAACR,sBAAsB,CAAC+D,eAAe,CAAC,CAC7DtD,eAAe,CAChB,CAAC;MAEF,IAAAwE,YAAA,GAAmB,IAAA/B,uBAAU,EAAC;UAAA,OAAM,IAAAC,iCAAiB,EAAC,CAAC;QAAA,EAAC;QAAhDC,MAAM,GAAA6B,YAAA,CAAN7B,MAAM;MAEd,IAAMS,OAAO,GAAG;QACd7C,QAAQ,EAAE,eAAe;QACzB4D,UAAU,EAAE;MACd,CAAC;MAED,MAAM,IAAAX,gBAAG,MAAA9D,kBAAA,CAAA2D,OAAA,EAAC,aAAY;QACpB,MAAMV,MAAM,CAACE,OAAO,CAAC4B,eAAe,CAACrB,OAAO,CAAC;MAC/C,CAAC,EAAC;MAEFR,MAAM,CAACD,MAAM,CAACE,OAAO,CAACG,OAAO,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MAC1CL,MAAM,CAACD,MAAM,CAACE,OAAO,CAACO,OAAO,CAAC,CAACL,OAAO,CAACK,OAAO,CAAC;MAC/CR,MAAM,CAACD,MAAM,CAACE,OAAO,CAACC,SAAS,CAAC,CAACY,YAAY,CAAC,CAAC,CAAC;MAChDd,MAAM,CAACD,MAAM,CAACE,OAAO,CAACK,KAAK,CAAC,CAACC,QAAQ,CAAC,CAAC;IACzC,CAAC,EAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}