3ce2ff382c1c057d3e602432345e7f07
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.isValidAccessibilityRole = exports.getSafeAccessibilityRole = exports.WCAG_CONSTANTS = exports.VALID_ACCESSIBILITY_ROLES = exports.SemanticMarkupUtils = exports.SemanticMarkupUtils = exports.ScreenReaderUtils = exports.ScreenReaderUtils = exports.FocusManagementUtils = exports.FocusManagementUtils = exports.ColorContrastUtils = exports.ColorContrastUtils = exports.AdvancedAccessibilityUtils = exports.AdvancedAccessibilityUtils = exports.AccessibilityTestUtils = exports.AccessibilityTestUtils = exports.AccessibilityMonitoringUtils = exports.AccessibilityMonitoringUtils = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _reactNative = require("react-native");
var WCAG_CONSTANTS = exports.WCAG_CONSTANTS = {
  CONTRAST_RATIOS: {
    NORMAL_TEXT: 4.5,
    LARGE_TEXT: 3.0,
    NON_TEXT: 3.0
  },
  TOUCH_TARGET: {
    MIN_SIZE: 44,
    RECOMMENDED_SIZE: 48
  },
  ANIMATION: {
    MAX_DURATION: 5000,
    REDUCED_MOTION_DURATION: 200
  },
  TEXT: {
    MIN_SIZE: 12,
    LARGE_TEXT_THRESHOLD: 18
  }
};
var ScreenReaderUtils = exports.ScreenReaderUtils = exports.ScreenReaderUtils = function () {
  function ScreenReaderUtils() {
    (0, _classCallCheck2.default)(this, ScreenReaderUtils);
  }
  return (0, _createClass2.default)(ScreenReaderUtils, null, [{
    key: "isScreenReaderEnabled",
    value: (function () {
      var _isScreenReaderEnabled = (0, _asyncToGenerator2.default)(function* () {
        try {
          return yield _reactNative.AccessibilityInfo.isScreenReaderEnabled();
        } catch (error) {
          console.warn('Failed to check screen reader status:', error);
          return false;
        }
      });
      function isScreenReaderEnabled() {
        return _isScreenReaderEnabled.apply(this, arguments);
      }
      return isScreenReaderEnabled;
    }())
  }, {
    key: "announceForAccessibility",
    value: function announceForAccessibility(message) {
      if (_reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'android') {
        _reactNative.AccessibilityInfo.announceForAccessibility(message);
      }
    }
  }, {
    key: "setAccessibilityFocus",
    value: function setAccessibilityFocus(reactTag) {
      if (_reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'android') {
        _reactNative.AccessibilityInfo.setAccessibilityFocus(reactTag);
      }
    }
  }, {
    key: "generateFormFieldLabel",
    value: function generateFormFieldLabel(label) {
      var required = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
      var error = arguments.length > 2 ? arguments[2] : undefined;
      var accessibleLabel = label;
      if (required) {
        accessibleLabel += ', required';
      }
      if (error) {
        accessibleLabel += `, error: ${error}`;
      }
      return accessibleLabel;
    }
  }, {
    key: "generateInteractionHint",
    value: function generateInteractionHint(action, additionalInfo) {
      var hint = `Double tap to ${action}`;
      if (additionalInfo) {
        hint += `. ${additionalInfo}`;
      }
      return hint;
    }
  }]);
}();
var ColorContrastUtils = exports.ColorContrastUtils = exports.ColorContrastUtils = function () {
  function ColorContrastUtils() {
    (0, _classCallCheck2.default)(this, ColorContrastUtils);
  }
  return (0, _createClass2.default)(ColorContrastUtils, null, [{
    key: "getRelativeLuminance",
    value: function getRelativeLuminance(color) {
      var hex = color.replace('#', '');
      var r = parseInt(hex.substr(0, 2), 16) / 255;
      var g = parseInt(hex.substr(2, 2), 16) / 255;
      var b = parseInt(hex.substr(4, 2), 16) / 255;
      var sRGB = [r, g, b].map(function (c) {
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
      });
      return 0.2126 * sRGB[0] + 0.7152 * sRGB[1] + 0.0722 * sRGB[2];
    }
  }, {
    key: "getContrastRatio",
    value: function getContrastRatio(color1, color2) {
      var lum1 = this.getRelativeLuminance(color1);
      var lum2 = this.getRelativeLuminance(color2);
      var lighter = Math.max(lum1, lum2);
      var darker = Math.min(lum1, lum2);
      return (lighter + 0.05) / (darker + 0.05);
    }
  }, {
    key: "meetsWCAGAA",
    value: function meetsWCAGAA(foreground, background) {
      var isLargeText = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
      var ratio = this.getContrastRatio(foreground, background);
      var requiredRatio = isLargeText ? WCAG_CONSTANTS.CONTRAST_RATIOS.LARGE_TEXT : WCAG_CONSTANTS.CONTRAST_RATIOS.NORMAL_TEXT;
      return ratio >= requiredRatio;
    }
  }, {
    key: "suggestAccessibleColor",
    value: function suggestAccessibleColor(foreground, background) {
      var isLargeText = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
      if (this.meetsWCAGAA(foreground, background, isLargeText)) {
        return null;
      }
      var hex = foreground.replace('#', '');
      var r = parseInt(hex.substr(0, 2), 16);
      var g = parseInt(hex.substr(2, 2), 16);
      var b = parseInt(hex.substr(4, 2), 16);
      var factors = [0.7, 0.5, 0.3, 0.1];
      for (var factor of factors) {
        var newR = Math.floor(r * factor);
        var newG = Math.floor(g * factor);
        var newB = Math.floor(b * factor);
        var newColor = `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
        if (this.meetsWCAGAA(newColor, background, isLargeText)) {
          return newColor;
        }
      }
      return this.meetsWCAGAA('#000000', background, isLargeText) ? '#000000' : null;
    }
  }]);
}();
var FocusManagementUtils = exports.FocusManagementUtils = exports.FocusManagementUtils = function () {
  function FocusManagementUtils() {
    (0, _classCallCheck2.default)(this, FocusManagementUtils);
  }
  return (0, _createClass2.default)(FocusManagementUtils, null, [{
    key: "pushFocus",
    value: function pushFocus(reactTag) {
      this.focusStack.push(reactTag);
      ScreenReaderUtils.setAccessibilityFocus(reactTag);
    }
  }, {
    key: "popFocus",
    value: function popFocus() {
      this.focusStack.pop();
      var previousFocus = this.focusStack[this.focusStack.length - 1];
      if (previousFocus) {
        ScreenReaderUtils.setAccessibilityFocus(previousFocus);
      }
    }
  }, {
    key: "clearFocusStack",
    value: function clearFocusStack() {
      this.focusStack = [];
    }
  }, {
    key: "createFocusTrap",
    value: function createFocusTrap(firstElement, lastElement) {
      return {
        onKeyPress: function onKeyPress(event) {
          if (event.key === 'Tab') {
            if (event.shiftKey) {
              if (event.target === firstElement) {
                event.preventDefault();
                ScreenReaderUtils.setAccessibilityFocus(lastElement);
              }
            } else {
              if (event.target === lastElement) {
                event.preventDefault();
                ScreenReaderUtils.setAccessibilityFocus(firstElement);
              }
            }
          }
        }
      };
    }
  }]);
}();
FocusManagementUtils.focusStack = [];
var SemanticMarkupUtils = exports.SemanticMarkupUtils = exports.SemanticMarkupUtils = function () {
  function SemanticMarkupUtils() {
    (0, _classCallCheck2.default)(this, SemanticMarkupUtils);
  }
  return (0, _createClass2.default)(SemanticMarkupUtils, null, [{
    key: "generateHeadingProps",
    value: function generateHeadingProps(level, text) {
      return {
        accessibilityRole: 'header',
        accessibilityLevel: level,
        accessibilityLabel: text
      };
    }
  }, {
    key: "generateListProps",
    value: function generateListProps(itemCount) {
      return {
        accessibilityRole: 'list',
        accessibilityLabel: `List with ${itemCount} items`
      };
    }
  }, {
    key: "generateListItemProps",
    value: function generateListItemProps(index, total, content) {
      return {
        accessibilityRole: 'listitem',
        accessibilityLabel: `${content}, ${index + 1} of ${total}`
      };
    }
  }, {
    key: "generateButtonProps",
    value: function generateButtonProps(label, action, state) {
      return {
        accessibilityRole: 'button',
        accessibilityLabel: label,
        accessibilityHint: action ? ScreenReaderUtils.generateInteractionHint(action) : undefined,
        accessibilityState: state
      };
    }
  }, {
    key: "generateInputProps",
    value: function generateInputProps(label, value) {
      var required = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
      var error = arguments.length > 3 ? arguments[3] : undefined;
      return {
        accessibilityLabel: ScreenReaderUtils.generateFormFieldLabel(label, required, error),
        accessibilityValue: value ? {
          text: value
        } : undefined,
        accessibilityState: {
          disabled: false
        }
      };
    }
  }]);
}();
var VALID_ACCESSIBILITY_ROLES = exports.VALID_ACCESSIBILITY_ROLES = ['button', 'link', 'search', 'image', 'keyboardkey', 'text', 'adjustable', 'imagebutton', 'header', 'summary', 'alert', 'checkbox', 'combobox', 'menu', 'menubar', 'menuitem', 'progressbar', 'radio', 'radiogroup', 'scrollbar', 'spinbutton', 'switch', 'tab', 'tablist', 'timer', 'toolbar', 'grid', 'list', 'listitem', 'none'];
var isValidAccessibilityRole = exports.isValidAccessibilityRole = function isValidAccessibilityRole(role) {
  return VALID_ACCESSIBILITY_ROLES.includes(role);
};
var getSafeAccessibilityRole = exports.getSafeAccessibilityRole = function getSafeAccessibilityRole(role) {
  return isValidAccessibilityRole(role) ? role : 'none';
};
var AccessibilityTestUtils = exports.AccessibilityTestUtils = exports.AccessibilityTestUtils = function () {
  function AccessibilityTestUtils() {
    (0, _classCallCheck2.default)(this, AccessibilityTestUtils);
  }
  return (0, _createClass2.default)(AccessibilityTestUtils, null, [{
    key: "validateAccessibilityProps",
    value: function validateAccessibilityProps(props) {
      var _props$style, _props$style2;
      var issues = [];
      if (!props.accessibilityRole && props.onPress) {
        issues.push('Interactive element missing accessibilityRole');
      }
      if (props.accessibilityRole && !isValidAccessibilityRole(props.accessibilityRole)) {
        issues.push(`Invalid accessibility role: ${props.accessibilityRole}. Use one of: ${VALID_ACCESSIBILITY_ROLES.join(', ')}`);
      }
      if (!props.accessibilityLabel && !props.children) {
        issues.push('Element missing accessibilityLabel or text content');
      }
      if ((_props$style = props.style) != null && _props$style.width && (_props$style2 = props.style) != null && _props$style2.height) {
        var width = props.style.width;
        var height = props.style.height;
        if (width < WCAG_CONSTANTS.TOUCH_TARGET.MIN_SIZE || height < WCAG_CONSTANTS.TOUCH_TARGET.MIN_SIZE) {
          issues.push(`Touch target too small: ${width}x${height}. Minimum: ${WCAG_CONSTANTS.TOUCH_TARGET.MIN_SIZE}x${WCAG_CONSTANTS.TOUCH_TARGET.MIN_SIZE}`);
        }
      }
      return issues;
    }
  }, {
    key: "generateAccessibilityReport",
    value: function generateAccessibilityReport(componentTree) {
      var _this = this;
      var passed = 0;
      var failed = 0;
      var issues = [];
      componentTree.forEach(function (component, index) {
        var componentIssues = _this.validateAccessibilityProps(component.props);
        if (componentIssues.length === 0) {
          passed++;
        } else {
          failed++;
          issues.push({
            component: component.type || `Component ${index}`,
            issues: componentIssues
          });
        }
      });
      return {
        passed: passed,
        failed: failed,
        issues: issues
      };
    }
  }]);
}();
var AdvancedAccessibilityUtils = exports.AdvancedAccessibilityUtils = exports.AdvancedAccessibilityUtils = {
  announceLiveRegion: function announceLiveRegion(message) {
    var priority = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'polite';
    if (_reactNative.Platform.OS === 'ios') {
      _reactNative.AccessibilityInfo.announceForAccessibility(message);
    } else {
      setTimeout(function () {
        _reactNative.AccessibilityInfo.announceForAccessibility(message);
      }, priority === 'assertive' ? 0 : 100);
    }
  },
  announceContentChange: function announceContentChange(element, changeType, details) {
    var message = `${element} ${changeType}${details ? `: ${details}` : ''}`;
    AdvancedAccessibilityUtils.announceLiveRegion(message, 'polite');
  },
  announceFormValidation: function announceFormValidation(fieldName, isValid, errorMessage) {
    if (isValid) {
      AdvancedAccessibilityUtils.announceLiveRegion(`${fieldName} is valid`, 'polite');
    } else {
      AdvancedAccessibilityUtils.announceLiveRegion(`${fieldName} error: ${errorMessage || 'Invalid input'}`, 'assertive');
    }
  },
  announceProgress: function announceProgress(current, total) {
    var label = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'Progress';
    var percentage = Math.round(current / total * 100);
    AdvancedAccessibilityUtils.announceLiveRegion(`${label}: ${percentage}% complete, ${current} of ${total}`, 'polite');
  },
  announceLoadingState: function announceLoadingState(isLoading) {
    var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'Content';
    if (isLoading) {
      AdvancedAccessibilityUtils.announceLiveRegion(`${context} loading`, 'polite');
    } else {
      AdvancedAccessibilityUtils.announceLiveRegion(`${context} loaded`, 'polite');
    }
  }
};
var AccessibilityMonitoringUtils = exports.AccessibilityMonitoringUtils = exports.AccessibilityMonitoringUtils = {
  trackAccessibilityUsage: function () {
    var _trackAccessibilityUsage = (0, _asyncToGenerator2.default)(function* () {
      var stats = {
        screenReaderUsage: 0,
        keyboardNavigation: 0,
        voiceControlUsage: 0,
        reducedMotionPreference: false
      };
      try {
        stats.screenReaderUsage = (yield _reactNative.AccessibilityInfo.isScreenReaderEnabled()) ? 1 : 0;
        if (_reactNative.Platform.OS === 'ios') {
          stats.reducedMotionPreference = yield _reactNative.AccessibilityInfo.isReduceMotionEnabled();
        }
      } catch (error) {
        console.warn('Accessibility usage tracking failed:', error);
      }
      return stats;
    });
    function trackAccessibilityUsage() {
      return _trackAccessibilityUsage.apply(this, arguments);
    }
    return trackAccessibilityUsage;
  }(),
  validateCompliance: function () {
    var _validateCompliance = (0, _asyncToGenerator2.default)(function* () {
      var compliance = {
        screenReaderSupport: false,
        keyboardNavigation: true,
        colorContrast: true,
        touchTargets: true,
        textScaling: true,
        reducedMotion: false
      };
      try {
        compliance.screenReaderSupport = yield _reactNative.AccessibilityInfo.isScreenReaderEnabled();
        if (_reactNative.Platform.OS === 'ios') {
          compliance.reducedMotion = yield _reactNative.AccessibilityInfo.isReduceMotionEnabled();
        }
      } catch (error) {
        console.warn('Accessibility compliance check failed:', error);
      }
      return compliance;
    });
    function validateCompliance() {
      return _validateCompliance.apply(this, arguments);
    }
    return validateCompliance;
  }()
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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