{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "processFontVariant", "fontVariant", "Array", "isArray", "match", "split", "filter", "Boolean", "_default"], "sources": ["processFontVariant.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\n'use strict';\n\nimport type {____FontVariantArray_Internal} from './StyleSheetTypes';\n\nfunction processFontVariant(\n  fontVariant: ____FontVariantArray_Internal | string,\n): ?____FontVariantArray_Internal {\n  if (Array.isArray(fontVariant)) {\n    return fontVariant;\n  }\n\n  // $FlowFixMe[incompatible-type]\n  const match: ?____FontVariantArray_Internal = fontVariant\n    .split(' ')\n    .filter(Boolean);\n\n  return match;\n}\n\nexport default processFontVariant;\n"], "mappings": "AAUA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAIb,SAASC,kBAAkBA,CACzBC,WAAmD,EACnB;EAChC,IAAIC,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;IAC9B,OAAOA,WAAW;EACpB;EAGA,IAAMG,KAAqC,GAAGH,WAAW,CACtDI,KAAK,CAAC,GAAG,CAAC,CACVC,MAAM,CAACC,OAAO,CAAC;EAElB,OAAOH,KAAK;AACd;AAAC,IAAAI,QAAA,GAAAX,OAAA,CAAAE,OAAA,GAEcC,kBAAkB", "ignoreList": []}