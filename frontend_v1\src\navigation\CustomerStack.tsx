/**
 * Customer Stack - Customer Role Stack Navigation
 *
 * Component Contract:
 * - Provides stack navigation for customer users
 * - Includes bottom tabs and modal screens
 * - Handles navigation to service details
 * - Follows React Navigation v6+ patterns
 * - Supports type-safe navigation
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { createStackNavigator } from '@react-navigation/stack';
import React from 'react';

// Import RealTimeDemo directly for now
import {
  LazyProviderDetailsScreen,
  LazyCheckoutScreen,
  LazyPaymentScreen,
  LazyBookingScreen,
  LazyBookingConfirmationScreen,
  LazyBookingDetailsScreen,
  LazyBookingRescheduleScreen,
  LazyAccountSettingsScreen,
  LazyConversationScreen,
  LazyEditProfileScreen,
  LazyLeaveReviewScreen,
  LazyRescheduleBookingScreen,
  LazyNotificationsScreen,
} from '../components/lazy/LazyScreens';
import { ServiceDetailsScreen } from '../features/service-discovery/ServiceDetailsScreen';
import { RealTimeDemo } from '../components/realtime/RealTimeDemo';

import { CustomerTabs } from './CustomerTabs';
import type { CustomerStackParamList } from './types';

const Stack = createStackNavigator<CustomerStackParamList>();

export const CustomerStack: React.FC = () => {
  return (
    <Stack.Navigator
      initialRouteName="CustomerTabs"
      screenOptions={{
        headerShown: false,
      }}>
      <Stack.Screen
        name="CustomerTabs"
        component={CustomerTabs}
        options={{
          headerShown: false,
        }}
      />

      <Stack.Screen
        name="ProviderDetails"
        component={LazyProviderDetailsScreen}
        options={{
          headerShown: false, // Using custom header in component
        }}
      />

      <Stack.Screen
        name="ServiceDetails"
        component={ServiceDetailsScreen}
        options={{
          headerShown: true,
          title: 'Service Details',
          headerBackTitleVisible: false,
        }}
      />

      <Stack.Screen
        name="BookingScreen"
        component={LazyBookingScreen}
        options={{
          headerShown: true,
          title: 'Book Service',
          headerBackTitleVisible: false,
        }}
      />

      <Stack.Screen
        name="BookingConfirmation"
        component={LazyBookingConfirmationScreen}
        options={{
          headerShown: true,
          title: 'Booking Confirmed',
          headerBackTitleVisible: false,
        }}
      />

      <Stack.Screen
        name="BookingDetails"
        component={LazyBookingDetailsScreen}
        options={{
          headerShown: true,
          title: 'Booking Details',
          headerBackTitleVisible: false,
        }}
      />

      <Stack.Screen
        name="BookingReschedule"
        component={LazyBookingRescheduleScreen}
        options={{
          headerShown: true,
          title: 'Reschedule Booking',
          headerBackTitleVisible: false,
        }}
      />

      {/* BookingManagement screen temporarily disabled due to import issues */}

      <Stack.Screen
        name="Checkout"
        component={LazyCheckoutScreen}
        options={{
          headerShown: true,
          title: 'Checkout',
          headerBackTitleVisible: false,
        }}
      />

      <Stack.Screen
        name="Payment"
        component={LazyPaymentScreen}
        options={{
          headerShown: true,
          title: 'Payment',
          headerBackTitleVisible: false,
        }}
      />

      <Stack.Screen
        name="Conversation"
        component={LazyConversationScreen}
        options={{
          headerShown: false, // Using custom header in component
        }}
      />

      <Stack.Screen
        name="RescheduleBooking"
        component={LazyRescheduleBookingScreen}
        options={{
          headerShown: false, // Using custom header in component
        }}
      />

      <Stack.Screen
        name="LeaveReview"
        component={LazyLeaveReviewScreen}
        options={{
          headerShown: false, // Using custom header in component
        }}
      />

      <Stack.Screen
        name="EditProfile"
        component={LazyEditProfileScreen}
        options={{
          headerShown: false, // Using custom header in component
        }}
      />

      <Stack.Screen
        name="AccountSettings"
        component={LazyAccountSettingsScreen}
        options={{
          headerShown: false, // Using custom header in component
        }}
      />

      <Stack.Screen
        name="Notifications"
        component={LazyNotificationsScreen}
        options={{
          headerShown: false, // Using custom header in component
        }}
      />

      <Stack.Screen
        name="RealTimeDemo"
        component={RealTimeDemo}
        options={{
          headerShown: false, // Using custom header in component
        }}
      />
    </Stack.Navigator>
  );
};
