{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_classCallCheck2", "_createClass2", "infoLog", "invariant", "DEBUG", "TaskQueue", "_ref", "onMoreTasks", "_onMoreTasks", "_queueStack", "tasks", "popable", "key", "enqueue", "task", "_getCurrentQueue", "push", "enqueueTasks", "_this", "for<PERSON>ach", "cancelTasks", "tasksToCancel", "map", "queue", "assign", "filter", "indexOf", "idx", "length", "hasTasksToProcess", "processNext", "shift", "gen", "name", "_genPromise", "run", "JSON", "stringify", "e", "message", "stackIdx", "pop", "queueStackSize", "_this2", "stackItem", "then", "catch", "ex", "setTimeout", "_default"], "sources": ["TaskQueue.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict\n */\n\n'use strict';\n\nconst infoLog = require('../Utilities/infoLog').default;\nconst invariant = require('invariant');\n\nexport type SimpleTask = {\n  name: string,\n  run: () => void,\n};\nexport type PromiseTask = {\n  name: string,\n  gen: () => Promise<void>,\n};\nexport type Task = SimpleTask | PromiseTask | (() => void);\n\nconst DEBUG: false = false;\n\n/**\n * TaskQueue - A system for queueing and executing a mix of simple callbacks and\n * trees of dependent tasks based on Promises. No tasks are executed unless\n * `processNext` is called.\n *\n * `enqueue` takes a Task object with either a simple `run` callback, or a\n * `gen` function that returns a `Promise` and puts it in the queue.  If a gen\n * function is supplied, then the promise it returns will block execution of\n * tasks already in the queue until it resolves. This can be used to make sure\n * the first task is fully resolved (including asynchronous dependencies that\n * also schedule more tasks via `enqueue`) before starting on the next task.\n * The `onMoreTasks` constructor argument is used to inform the owner that an\n * async task has resolved and that the queue should be processed again.\n *\n * Note: Tasks are only actually executed with explicit calls to `processNext`.\n */\nclass TaskQueue {\n  /**\n   * TaskQueue instances are self contained and independent, so multiple tasks\n   * of varying semantics and priority can operate together.\n   *\n   * `onMoreTasks` is invoked when `PromiseTask`s resolve if there are more\n   * tasks to process.\n   */\n  constructor({onMoreTasks}: {onMoreTasks: () => void, ...}) {\n    this._onMoreTasks = onMoreTasks;\n    this._queueStack = [{tasks: [], popable: false}];\n  }\n\n  /**\n   * Add a task to the queue.  It is recommended to name your tasks for easier\n   * async debugging. Tasks will not be executed until `processNext` is called\n   * explicitly.\n   */\n  enqueue(task: Task): void {\n    this._getCurrentQueue().push(task);\n  }\n\n  enqueueTasks(tasks: Array<Task>): void {\n    tasks.forEach(task => this.enqueue(task));\n  }\n\n  cancelTasks(tasksToCancel: Array<Task>): void {\n    // search through all tasks and remove them.\n    this._queueStack = this._queueStack\n      .map(queue => ({\n        ...queue,\n        tasks: queue.tasks.filter(task => tasksToCancel.indexOf(task) === -1),\n      }))\n      .filter((queue, idx) => queue.tasks.length > 0 || idx === 0);\n  }\n\n  /**\n   * Check to see if `processNext` should be called.\n   *\n   * @returns {boolean} Returns true if there are tasks that are ready to be\n   * processed with `processNext`, or returns false if there are no more tasks\n   * to be processed right now, although there may be tasks in the queue that\n   * are blocked by earlier `PromiseTask`s that haven't resolved yet.\n   * `onMoreTasks` will be called after each `PromiseTask` resolves if there are\n   * tasks ready to run at that point.\n   */\n  hasTasksToProcess(): boolean {\n    return this._getCurrentQueue().length > 0;\n  }\n\n  /**\n   * Executes the next task in the queue.\n   */\n  processNext(): void {\n    const queue = this._getCurrentQueue();\n    if (queue.length) {\n      const task = queue.shift();\n      try {\n        if (typeof task === 'object' && task.gen) {\n          DEBUG && infoLog('TaskQueue: genPromise for task ' + task.name);\n          this._genPromise(task);\n        } else if (typeof task === 'object' && task.run) {\n          DEBUG && infoLog('TaskQueue: run task ' + task.name);\n          task.run();\n        } else {\n          invariant(\n            typeof task === 'function',\n            'Expected Function, SimpleTask, or PromiseTask, but got:\\n' +\n              JSON.stringify(task, null, 2),\n          );\n          DEBUG && infoLog('TaskQueue: run anonymous task');\n          task();\n        }\n      } catch (e) {\n        e.message =\n          // $FlowFixMe[incompatible-type]\n          'TaskQueue: Error with task ' + (task.name || '') + ': ' + e.message;\n        throw e;\n      }\n    }\n  }\n\n  _queueStack: Array<{\n    tasks: Array<Task>,\n    popable: boolean,\n    ...\n  }>;\n  _onMoreTasks: () => void;\n\n  _getCurrentQueue(): Array<Task> {\n    const stackIdx = this._queueStack.length - 1;\n    const queue = this._queueStack[stackIdx];\n    if (\n      queue.popable &&\n      queue.tasks.length === 0 &&\n      this._queueStack.length > 1\n    ) {\n      this._queueStack.pop();\n      DEBUG &&\n        infoLog('TaskQueue: popped queue: ', {\n          stackIdx,\n          queueStackSize: this._queueStack.length,\n        });\n      return this._getCurrentQueue();\n    } else {\n      return queue.tasks;\n    }\n  }\n\n  _genPromise(task: PromiseTask) {\n    // Each async task pushes it's own queue onto the queue stack. This\n    // effectively defers execution of previously queued tasks until the promise\n    // resolves, at which point we allow the new queue to be popped, which\n    // happens once it is fully processed.\n    this._queueStack.push({tasks: [], popable: false});\n    const stackIdx = this._queueStack.length - 1;\n    const stackItem = this._queueStack[stackIdx];\n    DEBUG && infoLog('TaskQueue: push new queue: ', {stackIdx});\n    DEBUG && infoLog('TaskQueue: exec gen task ' + task.name);\n    task\n      .gen()\n      .then(() => {\n        DEBUG &&\n          infoLog('TaskQueue: onThen for gen task ' + task.name, {\n            stackIdx,\n            queueStackSize: this._queueStack.length,\n          });\n        stackItem.popable = true;\n        this.hasTasksToProcess() && this._onMoreTasks();\n      })\n      .catch(ex => {\n        setTimeout(() => {\n          ex.message = `TaskQueue: Error resolving Promise in task ${task.name}: ${ex.message}`;\n          throw ex;\n        }, 0);\n      });\n  }\n}\n\nexport default TaskQueue;\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AAEb,IAAMQ,OAAO,GAAGR,OAAO,uBAAuB,CAAC,CAACK,OAAO;AACvD,IAAMI,SAAS,GAAGT,OAAO,CAAC,WAAW,CAAC;AAYtC,IAAMU,KAAY,GAAG,KAAK;AAAC,IAkBrBC,SAAS;EAQb,SAAAA,UAAAC,IAAA,EAA2D;IAAA,IAA9CC,WAAW,GAAAD,IAAA,CAAXC,WAAW;IAAA,IAAAP,gBAAA,CAAAD,OAAA,QAAAM,SAAA;IACtB,IAAI,CAACG,YAAY,GAAGD,WAAW;IAC/B,IAAI,CAACE,WAAW,GAAG,CAAC;MAACC,KAAK,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;EAClD;EAAC,WAAAV,aAAA,CAAAF,OAAA,EAAAM,SAAA;IAAAO,GAAA;IAAAd,KAAA,EAOD,SAAAe,OAAOA,CAACC,IAAU,EAAQ;MACxB,IAAI,CAACC,gBAAgB,CAAC,CAAC,CAACC,IAAI,CAACF,IAAI,CAAC;IACpC;EAAC;IAAAF,GAAA;IAAAd,KAAA,EAED,SAAAmB,YAAYA,CAACP,KAAkB,EAAQ;MAAA,IAAAQ,KAAA;MACrCR,KAAK,CAACS,OAAO,CAAC,UAAAL,IAAI;QAAA,OAAII,KAAI,CAACL,OAAO,CAACC,IAAI,CAAC;MAAA,EAAC;IAC3C;EAAC;IAAAF,GAAA;IAAAd,KAAA,EAED,SAAAsB,WAAWA,CAACC,aAA0B,EAAQ;MAE5C,IAAI,CAACZ,WAAW,GAAG,IAAI,CAACA,WAAW,CAChCa,GAAG,CAAC,UAAAC,KAAK;QAAA,OAAA5B,MAAA,CAAA6B,MAAA,KACLD,KAAK;UACRb,KAAK,EAAEa,KAAK,CAACb,KAAK,CAACe,MAAM,CAAC,UAAAX,IAAI;YAAA,OAAIO,aAAa,CAACK,OAAO,CAACZ,IAAI,CAAC,KAAK,CAAC,CAAC;UAAA;QAAC;MAAA,CACrE,CAAC,CACFW,MAAM,CAAC,UAACF,KAAK,EAAEI,GAAG;QAAA,OAAKJ,KAAK,CAACb,KAAK,CAACkB,MAAM,GAAG,CAAC,IAAID,GAAG,KAAK,CAAC;MAAA,EAAC;IAChE;EAAC;IAAAf,GAAA;IAAAd,KAAA,EAYD,SAAA+B,iBAAiBA,CAAA,EAAY;MAC3B,OAAO,IAAI,CAACd,gBAAgB,CAAC,CAAC,CAACa,MAAM,GAAG,CAAC;IAC3C;EAAC;IAAAhB,GAAA;IAAAd,KAAA,EAKD,SAAAgC,WAAWA,CAAA,EAAS;MAClB,IAAMP,KAAK,GAAG,IAAI,CAACR,gBAAgB,CAAC,CAAC;MACrC,IAAIQ,KAAK,CAACK,MAAM,EAAE;QAChB,IAAMd,IAAI,GAAGS,KAAK,CAACQ,KAAK,CAAC,CAAC;QAC1B,IAAI;UACF,IAAI,OAAOjB,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACkB,GAAG,EAAE;YACxC5B,KAAK,IAAIF,OAAO,CAAC,iCAAiC,GAAGY,IAAI,CAACmB,IAAI,CAAC;YAC/D,IAAI,CAACC,WAAW,CAACpB,IAAI,CAAC;UACxB,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACqB,GAAG,EAAE;YAC/C/B,KAAK,IAAIF,OAAO,CAAC,sBAAsB,GAAGY,IAAI,CAACmB,IAAI,CAAC;YACpDnB,IAAI,CAACqB,GAAG,CAAC,CAAC;UACZ,CAAC,MAAM;YACLhC,SAAS,CACP,OAAOW,IAAI,KAAK,UAAU,EAC1B,2DAA2D,GACzDsB,IAAI,CAACC,SAAS,CAACvB,IAAI,EAAE,IAAI,EAAE,CAAC,CAChC,CAAC;YACDV,KAAK,IAAIF,OAAO,CAAC,+BAA+B,CAAC;YACjDY,IAAI,CAAC,CAAC;UACR;QACF,CAAC,CAAC,OAAOwB,CAAC,EAAE;UACVA,CAAC,CAACC,OAAO,GAEP,6BAA6B,IAAIzB,IAAI,CAACmB,IAAI,IAAI,EAAE,CAAC,GAAG,IAAI,GAAGK,CAAC,CAACC,OAAO;UACtE,MAAMD,CAAC;QACT;MACF;IACF;EAAC;IAAA1B,GAAA;IAAAd,KAAA,EASD,SAAAiB,gBAAgBA,CAAA,EAAgB;MAC9B,IAAMyB,QAAQ,GAAG,IAAI,CAAC/B,WAAW,CAACmB,MAAM,GAAG,CAAC;MAC5C,IAAML,KAAK,GAAG,IAAI,CAACd,WAAW,CAAC+B,QAAQ,CAAC;MACxC,IACEjB,KAAK,CAACZ,OAAO,IACbY,KAAK,CAACb,KAAK,CAACkB,MAAM,KAAK,CAAC,IACxB,IAAI,CAACnB,WAAW,CAACmB,MAAM,GAAG,CAAC,EAC3B;QACA,IAAI,CAACnB,WAAW,CAACgC,GAAG,CAAC,CAAC;QACtBrC,KAAK,IACHF,OAAO,CAAC,2BAA2B,EAAE;UACnCsC,QAAQ,EAARA,QAAQ;UACRE,cAAc,EAAE,IAAI,CAACjC,WAAW,CAACmB;QACnC,CAAC,CAAC;QACJ,OAAO,IAAI,CAACb,gBAAgB,CAAC,CAAC;MAChC,CAAC,MAAM;QACL,OAAOQ,KAAK,CAACb,KAAK;MACpB;IACF;EAAC;IAAAE,GAAA;IAAAd,KAAA,EAED,SAAAoC,WAAWA,CAACpB,IAAiB,EAAE;MAAA,IAAA6B,MAAA;MAK7B,IAAI,CAAClC,WAAW,CAACO,IAAI,CAAC;QAACN,KAAK,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;MAClD,IAAM6B,QAAQ,GAAG,IAAI,CAAC/B,WAAW,CAACmB,MAAM,GAAG,CAAC;MAC5C,IAAMgB,SAAS,GAAG,IAAI,CAACnC,WAAW,CAAC+B,QAAQ,CAAC;MAC5CpC,KAAK,IAAIF,OAAO,CAAC,6BAA6B,EAAE;QAACsC,QAAQ,EAARA;MAAQ,CAAC,CAAC;MAC3DpC,KAAK,IAAIF,OAAO,CAAC,2BAA2B,GAAGY,IAAI,CAACmB,IAAI,CAAC;MACzDnB,IAAI,CACDkB,GAAG,CAAC,CAAC,CACLa,IAAI,CAAC,YAAM;QACVzC,KAAK,IACHF,OAAO,CAAC,iCAAiC,GAAGY,IAAI,CAACmB,IAAI,EAAE;UACrDO,QAAQ,EAARA,QAAQ;UACRE,cAAc,EAAEC,MAAI,CAAClC,WAAW,CAACmB;QACnC,CAAC,CAAC;QACJgB,SAAS,CAACjC,OAAO,GAAG,IAAI;QACxBgC,MAAI,CAACd,iBAAiB,CAAC,CAAC,IAAIc,MAAI,CAACnC,YAAY,CAAC,CAAC;MACjD,CAAC,CAAC,CACDsC,KAAK,CAAC,UAAAC,EAAE,EAAI;QACXC,UAAU,CAAC,YAAM;UACfD,EAAE,CAACR,OAAO,GAAG,8CAA8CzB,IAAI,CAACmB,IAAI,KAAKc,EAAE,CAACR,OAAO,EAAE;UACrF,MAAMQ,EAAE;QACV,CAAC,EAAE,CAAC,CAAC;MACP,CAAC,CAAC;IACN;EAAC;AAAA;AAAA,IAAAE,QAAA,GAAApD,OAAA,CAAAE,OAAA,GAGYM,SAAS", "ignoreList": []}