{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_get2", "_inherits2", "_AnimatedInterpolation", "_AnimatedNode", "_AnimatedValue", "_AnimatedWithChildren2", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "AnimatedDivision", "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "a", "b", "config", "_this", "_warnedAboutDivideByZero", "AnimatedNode", "__getValue", "console", "error", "_a", "AnimatedValue", "_b", "key", "__makeNative", "platformConfig", "interpolate", "AnimatedInterpolation", "__attach", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d", "__getNativeConfig", "type", "input", "__getNativeTag", "debugID", "__getDebugID", "AnimatedWithChildren"], "sources": ["AnimatedDivision.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n */\n\n'use strict';\n\nimport type {PlatformConfig} from '../AnimatedPlatformConfig';\nimport type {InterpolationConfigType} from './AnimatedInterpolation';\nimport type {AnimatedNodeConfig} from './AnimatedNode';\n\nimport AnimatedInterpolation from './AnimatedInterpolation';\nimport AnimatedNode from './AnimatedNode';\nimport AnimatedValue from './AnimatedValue';\nimport AnimatedWithChildren from './AnimatedWithChildren';\n\nexport default class AnimatedDivision extends AnimatedWithChildren {\n  _a: AnimatedNode;\n  _b: AnimatedNode;\n  _warnedAboutDivideByZero: boolean = false;\n\n  constructor(\n    a: AnimatedNode | number,\n    b: AnimatedNode | number,\n    config?: ?AnimatedNodeConfig,\n  ) {\n    super(config);\n    if (b === 0 || (b instanceof AnimatedNode && b.__getValue() === 0)) {\n      console.error('Detected potential division by zero in AnimatedDivision');\n    }\n    this._a = typeof a === 'number' ? new AnimatedValue(a) : a;\n    this._b = typeof b === 'number' ? new AnimatedValue(b) : b;\n  }\n\n  __makeNative(platformConfig: ?PlatformConfig) {\n    this._a.__makeNative(platformConfig);\n    this._b.__makeNative(platformConfig);\n    super.__makeNative(platformConfig);\n  }\n\n  __getValue(): number {\n    const a = this._a.__getValue();\n    const b = this._b.__getValue();\n    if (b === 0) {\n      // Prevent spamming the console/LogBox\n      if (!this._warnedAboutDivideByZero) {\n        console.error('Detected division by zero in AnimatedDivision');\n        this._warnedAboutDivideByZero = true;\n      }\n      // Passing infinity/NaN to Fabric will cause a native crash\n      return 0;\n    }\n    this._warnedAboutDivideByZero = false;\n    return a / b;\n  }\n\n  interpolate<OutputT: number | string>(\n    config: InterpolationConfigType<OutputT>,\n  ): AnimatedInterpolation<OutputT> {\n    return new AnimatedInterpolation(this, config);\n  }\n\n  __attach(): void {\n    this._a.__addChild(this);\n    this._b.__addChild(this);\n    super.__attach();\n  }\n\n  __detach(): void {\n    this._a.__removeChild(this);\n    this._b.__removeChild(this);\n    super.__detach();\n  }\n\n  __getNativeConfig(): any {\n    return {\n      type: 'division',\n      input: [this._a.__getNativeTag(), this._b.__getNativeTag()],\n      debugID: this.__getDebugID(),\n    };\n  }\n}\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,2BAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,gBAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,KAAA,GAAAX,sBAAA,CAAAC,OAAA;AAAA,IAAAW,UAAA,GAAAZ,sBAAA,CAAAC,OAAA;AAMb,IAAAY,sBAAA,GAAAb,sBAAA,CAAAC,OAAA;AACA,IAAAa,aAAA,GAAAd,sBAAA,CAAAC,OAAA;AACA,IAAAc,cAAA,GAAAf,sBAAA,CAAAC,OAAA;AACA,IAAAe,sBAAA,GAAAhB,sBAAA,CAAAC,OAAA;AAA0D,SAAAgB,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAT,gBAAA,CAAAJ,OAAA,EAAAa,CAAA,OAAAV,2BAAA,CAAAH,OAAA,EAAAY,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,YAAAV,gBAAA,CAAAJ,OAAA,EAAAY,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAAA,SAAAY,cAAAZ,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAW,CAAA,QAAAC,CAAA,OAAArB,KAAA,CAAAL,OAAA,MAAAI,gBAAA,CAAAJ,OAAA,MAAAyB,CAAA,GAAAb,CAAA,CAAAS,SAAA,GAAAT,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAW,CAAA,yBAAAC,CAAA,aAAAd,CAAA,WAAAc,CAAA,CAAAP,KAAA,CAAAL,CAAA,EAAAF,CAAA,OAAAc,CAAA;AAAA,IAErCC,gBAAgB,GAAA7B,OAAA,CAAAE,OAAA,aAAA4B,qBAAA;EAKnC,SAAAD,iBACEE,CAAwB,EACxBC,CAAwB,EACxBC,MAA4B,EAC5B;IAAA,IAAAC,KAAA;IAAA,IAAA/B,gBAAA,CAAAD,OAAA,QAAA2B,gBAAA;IACAK,KAAA,GAAArB,UAAA,OAAAgB,gBAAA,GAAMI,MAAM;IAAEC,KAAA,CAPhBC,wBAAwB,GAAY,KAAK;IAQvC,IAAIH,CAAC,KAAK,CAAC,IAAKA,CAAC,YAAYI,qBAAY,IAAIJ,CAAC,CAACK,UAAU,CAAC,CAAC,KAAK,CAAE,EAAE;MAClEC,OAAO,CAACC,KAAK,CAAC,yDAAyD,CAAC;IAC1E;IACAL,KAAA,CAAKM,EAAE,GAAG,OAAOT,CAAC,KAAK,QAAQ,GAAG,IAAIU,sBAAa,CAACV,CAAC,CAAC,GAAGA,CAAC;IAC1DG,KAAA,CAAKQ,EAAE,GAAG,OAAOV,CAAC,KAAK,QAAQ,GAAG,IAAIS,sBAAa,CAACT,CAAC,CAAC,GAAGA,CAAC;IAAC,OAAAE,KAAA;EAC7D;EAAC,IAAA1B,UAAA,CAAAN,OAAA,EAAA2B,gBAAA,EAAAC,qBAAA;EAAA,WAAA1B,aAAA,CAAAF,OAAA,EAAA2B,gBAAA;IAAAc,GAAA;IAAA1C,KAAA,EAED,SAAA2C,YAAYA,CAACC,cAA+B,EAAE;MAC5C,IAAI,CAACL,EAAE,CAACI,YAAY,CAACC,cAAc,CAAC;MACpC,IAAI,CAACH,EAAE,CAACE,YAAY,CAACC,cAAc,CAAC;MACpCnB,aAAA,CAAAG,gBAAA,4BAAmBgB,cAAc;IACnC;EAAC;IAAAF,GAAA;IAAA1C,KAAA,EAED,SAAAoC,UAAUA,CAAA,EAAW;MACnB,IAAMN,CAAC,GAAG,IAAI,CAACS,EAAE,CAACH,UAAU,CAAC,CAAC;MAC9B,IAAML,CAAC,GAAG,IAAI,CAACU,EAAE,CAACL,UAAU,CAAC,CAAC;MAC9B,IAAIL,CAAC,KAAK,CAAC,EAAE;QAEX,IAAI,CAAC,IAAI,CAACG,wBAAwB,EAAE;UAClCG,OAAO,CAACC,KAAK,CAAC,+CAA+C,CAAC;UAC9D,IAAI,CAACJ,wBAAwB,GAAG,IAAI;QACtC;QAEA,OAAO,CAAC;MACV;MACA,IAAI,CAACA,wBAAwB,GAAG,KAAK;MACrC,OAAOJ,CAAC,GAAGC,CAAC;IACd;EAAC;IAAAW,GAAA;IAAA1C,KAAA,EAED,SAAA6C,WAAWA,CACTb,MAAwC,EACR;MAChC,OAAO,IAAIc,8BAAqB,CAAC,IAAI,EAAEd,MAAM,CAAC;IAChD;EAAC;IAAAU,GAAA;IAAA1C,KAAA,EAED,SAAA+C,QAAQA,CAAA,EAAS;MACf,IAAI,CAACR,EAAE,CAACS,UAAU,CAAC,IAAI,CAAC;MACxB,IAAI,CAACP,EAAE,CAACO,UAAU,CAAC,IAAI,CAAC;MACxBvB,aAAA,CAAAG,gBAAA;IACF;EAAC;IAAAc,GAAA;IAAA1C,KAAA,EAED,SAAAiD,QAAQA,CAAA,EAAS;MACf,IAAI,CAACV,EAAE,CAACW,aAAa,CAAC,IAAI,CAAC;MAC3B,IAAI,CAACT,EAAE,CAACS,aAAa,CAAC,IAAI,CAAC;MAC3BzB,aAAA,CAAAG,gBAAA;IACF;EAAC;IAAAc,GAAA;IAAA1C,KAAA,EAED,SAAAmD,iBAAiBA,CAAA,EAAQ;MACvB,OAAO;QACLC,IAAI,EAAE,UAAU;QAChBC,KAAK,EAAE,CAAC,IAAI,CAACd,EAAE,CAACe,cAAc,CAAC,CAAC,EAAE,IAAI,CAACb,EAAE,CAACa,cAAc,CAAC,CAAC,CAAC;QAC3DC,OAAO,EAAE,IAAI,CAACC,YAAY,CAAC;MAC7B,CAAC;IACH;EAAC;AAAA,EAhE2CC,8BAAoB", "ignoreList": []}