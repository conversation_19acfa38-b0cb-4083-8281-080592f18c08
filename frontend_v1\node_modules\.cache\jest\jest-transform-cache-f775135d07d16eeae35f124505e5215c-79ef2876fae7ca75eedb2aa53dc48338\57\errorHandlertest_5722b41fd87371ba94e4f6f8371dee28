be4c53da4bcb0b9187cc6a0297eae1ed
_getJestObj().mock('expo-haptics', function () {
  return {
    notificationAsync: jest.fn(),
    NotificationFeedbackType: {
      Warning: 'warning',
      Error: 'error'
    }
  };
});
_getJestObj().mock('react-native', function () {
  return {
    Alert: {
      alert: jest.fn()
    }
  };
});
var _errorHandler = require("../errorHandler");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
describe('ErrorHandler', function () {
  beforeEach(function () {
    jest.clearAllMocks();
    _errorHandler.errorHandler.clearErrors();
  });
  describe('handleError', function () {
    it('should handle basic errors correctly', function () {
      var error = new Error('Test error');
      var result = (0, _errorHandler.handleError)(error);
      expect(result).toMatchObject({
        type: _errorHandler.ErrorType.UNKNOWN,
        message: 'Test error',
        severity: _errorHandler.ErrorSeverity.LOW
      });
      expect(result.id).toBeDefined();
      expect(result.timestamp).toBeInstanceOf(Date);
    });
    it('should handle errors with context', function () {
      var error = new Error('Test error');
      var context = {
        component: 'TestComponent',
        action: 'testAction'
      };
      var result = (0, _errorHandler.handleError)(error, context);
      expect(result.context).toEqual(context);
    });
    it('should preserve AppError properties', function () {
      var appError = {
        id: 'test-id',
        type: _errorHandler.ErrorType.VALIDATION,
        severity: _errorHandler.ErrorSeverity.HIGH,
        message: 'Validation error',
        userMessage: 'Please fix the form',
        timestamp: new Date()
      };
      var result = (0, _errorHandler.handleError)(appError);
      expect(result).toMatchObject(appError);
    });
  });
  describe('handleNetworkError', function () {
    it('should handle network errors correctly', function () {
      var error = new Error('Network request failed');
      var result = (0, _errorHandler.handleNetworkError)(error);
      expect(result).toMatchObject({
        type: _errorHandler.ErrorType.NETWORK,
        severity: _errorHandler.ErrorSeverity.MEDIUM,
        message: 'Network request failed',
        userMessage: 'Network connection issue. Please check your internet connection and try again.'
      });
    });
  });
  describe('error type detection', function () {
    it('should detect network errors', function () {
      var error = new Error('fetch failed');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.type).toBe(_errorHandler.ErrorType.NETWORK);
    });
    it('should detect authentication errors', function () {
      var error = new Error('unauthorized access');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.type).toBe(_errorHandler.ErrorType.AUTHENTICATION);
    });
    it('should detect authorization errors', function () {
      var error = new Error('forbidden resource');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.type).toBe(_errorHandler.ErrorType.AUTHORIZATION);
    });
    it('should detect not found errors', function () {
      var error = new Error('resource not found');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.type).toBe(_errorHandler.ErrorType.NOT_FOUND);
    });
    it('should detect server errors', function () {
      var error = new Error('internal server error');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.type).toBe(_errorHandler.ErrorType.SERVER);
    });
  });
  describe('severity detection', function () {
    it('should detect critical errors', function () {
      var error = new Error('critical system failure');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.severity).toBe(_errorHandler.ErrorSeverity.CRITICAL);
    });
    it('should detect high severity errors', function () {
      var error = new Error('unauthorized access');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.severity).toBe(_errorHandler.ErrorSeverity.HIGH);
    });
    it('should detect medium severity errors', function () {
      var error = new Error('network timeout');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.severity).toBe(_errorHandler.ErrorSeverity.MEDIUM);
    });
    it('should default to low severity', function () {
      var error = new Error('minor issue');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.severity).toBe(_errorHandler.ErrorSeverity.LOW);
    });
  });
  describe('user message generation', function () {
    it('should generate appropriate network error messages', function () {
      var error = new Error('fetch failed');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.userMessage).toBe('Please check your internet connection and try again.');
    });
    it('should generate appropriate auth error messages', function () {
      var error = new Error('unauthorized');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.userMessage).toBe('Please log in to continue.');
    });
    it('should generate appropriate authorization error messages', function () {
      var error = new Error('forbidden');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.userMessage).toBe("You don't have permission to perform this action.");
    });
    it('should generate appropriate not found error messages', function () {
      var error = new Error('not found');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.userMessage).toBe('The requested item could not be found.');
    });
    it('should generate appropriate server error messages', function () {
      var error = new Error('server error');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.userMessage).toBe('Server is temporarily unavailable. Please try again later.');
    });
    it('should generate default error messages', function () {
      var error = new Error('unknown error');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.userMessage).toBe('An unexpected error occurred. Please try again.');
    });
  });
  describe('error statistics', function () {
    it('should track error statistics correctly', function () {
      (0, _errorHandler.handleError)(new Error('network error'));
      (0, _errorHandler.handleError)(new Error('validation error'));
      (0, _errorHandler.handleError)(new Error('critical error'));
      var stats = _errorHandler.errorHandler.getErrorStats();
      expect(stats.total).toBe(3);
      expect(stats.byType[_errorHandler.ErrorType.NETWORK]).toBe(1);
      expect(stats.byType[_errorHandler.ErrorType.UNKNOWN]).toBe(2);
      expect(stats.bySeverity[_errorHandler.ErrorSeverity.MEDIUM]).toBe(1);
      expect(stats.bySeverity[_errorHandler.ErrorSeverity.CRITICAL]).toBe(1);
      expect(stats.bySeverity[_errorHandler.ErrorSeverity.LOW]).toBe(1);
    });
    it('should clear errors correctly', function () {
      (0, _errorHandler.handleError)(new Error('test error'));
      expect(_errorHandler.errorHandler.getErrorStats().total).toBe(1);
      _errorHandler.errorHandler.clearErrors();
      expect(_errorHandler.errorHandler.getErrorStats().total).toBe(0);
    });
  });
  describe('error queue management', function () {
    it('should maintain queue size limit', function () {
      for (var i = 0; i < 105; i++) {
        (0, _errorHandler.handleError)(new Error(`Error ${i}`));
      }
      var stats = _errorHandler.errorHandler.getErrorStats();
      expect(stats.total).toBe(100);
    });
  });
  describe('error ID generation', function () {
    it('should generate unique error IDs', function () {
      var error1 = (0, _errorHandler.handleError)(new Error('Error 1'));
      var error2 = (0, _errorHandler.handleError)(new Error('Error 2'));
      expect(error1.id).toBeDefined();
      expect(error2.id).toBeDefined();
      expect(error1.id).not.toBe(error2.id);
    });
    it('should generate IDs with correct format', function () {
      var error = (0, _errorHandler.handleError)(new Error('Test error'));
      expect(error.id).toMatch(/^error_\d+_[a-z0-9]+$/);
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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