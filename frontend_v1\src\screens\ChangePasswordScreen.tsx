/**
 * Change Password Screen - Secure Password Management
 *
 * Component Contract:
 * - Provides secure password change functionality
 * - Validates current password before allowing change
 * - Enforces strong password requirements
 * - Integrates with backend authentication service
 * - Follows security best practices and accessibility guidelines
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Platform,
  TextInput,
  Alert,
  KeyboardAvoidingView,
} from 'react-native';

import { Button } from '../components/atoms/Button';
import { SafeAreaWrapper } from '../components/ui/SafeAreaWrapper';
import { useTheme } from '../contexts/ThemeContext';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../utils/responsiveUtils';

type ChangePasswordScreenNavigationProp = StackNavigationProp<
  any,
  'ChangePassword'
>;

interface PasswordForm {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export const ChangePasswordScreen: React.FC = () => {
  const { colors } = useTheme();
  const navigation = useNavigation<ChangePasswordScreenNavigationProp>();
  const styles = createStyles(colors);

  const [form, setForm] = useState<PasswordForm>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Partial<PasswordForm>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<PasswordForm> = {};

    if (!form.currentPassword) {
      newErrors.currentPassword = 'Current password is required';
    }

    if (!form.newPassword) {
      newErrors.newPassword = 'New password is required';
    } else if (form.newPassword.length < 6) {
      newErrors.newPassword = 'Password must be at least 6 characters';
    }

    if (!form.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your new password';
    } else if (form.newPassword !== form.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChangePassword = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      // Import authService dynamically to avoid circular dependencies
      const { authService } = await import('../services/authService');

      await authService.changePassword({
        old_password: formData.currentPassword,
        new_password: formData.newPassword,
      });

      Alert.alert(
        'Success',
        'Your password has been changed successfully.',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error: any) {
      console.error('❌ Change password failed:', error);

      const errorMessage = error.message || 'Failed to change password. Please try again.';
      Alert.alert('Error', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const updateForm = (field: keyof PasswordForm, value: string) => {
    setForm(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const togglePasswordVisibility = (field: 'current' | 'new' | 'confirm') => {
    setShowPasswords(prev => ({ ...prev, [field]: !prev[field] }));
  };

  const renderPasswordField = (
    label: string,
    field: keyof PasswordForm,
    visibilityField: 'current' | 'new' | 'confirm',
    placeholder: string,
  ) => (
    <View style={styles.formField}>
      <Text style={styles.fieldLabel}>{label}</Text>
      <View style={styles.passwordContainer}>
        <TextInput
          style={[styles.passwordInput, errors[field] && styles.inputError]}
          value={form[field]}
          onChangeText={value => updateForm(field, value)}
          placeholder={placeholder}
          placeholderTextColor={colors.text.tertiary}
          secureTextEntry={!showPasswords[visibilityField]}
          autoCapitalize="none"
          autoCorrect={false}
          testID={`${field}-input`}
        />
        <TouchableOpacity
          style={styles.eyeButton}
          onPress={() => togglePasswordVisibility(visibilityField)}
          testID={`${field}-visibility-toggle`}>
          <Ionicons
            name={showPasswords[visibilityField] ? 'eye-off' : 'eye'}
            size={20}
            color={colors.text.tertiary}
          />
        </TouchableOpacity>
      </View>
      {errors[field] && <Text style={styles.errorText}>{errors[field]}</Text>}
    </View>
  );



  return (
    <SafeAreaWrapper backgroundColor={colors.background.primary}>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.backButton}
            testID="back-button">
            <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Change Password</Text>
          <View style={styles.placeholder} />
        </View>

        <View style={styles.content}>
          <Text style={styles.formTitle}>Update Your Password</Text>
          <Text style={styles.formSubtitle}>
            Enter your current password and choose a new secure password.
          </Text>

          {renderPasswordField(
            'Current Password',
            'currentPassword',
            'current',
            'Enter your current password',
          )}

          {renderPasswordField(
            'New Password',
            'newPassword',
            'new',
            'Enter your new password',
          )}

          {renderPasswordField(
            'Confirm New Password',
            'confirmPassword',
            'confirm',
            'Confirm your new password',
          )}

          <Button
            onPress={handleChangePassword}
            loading={loading}
            style={styles.changeButton}
            testID="change-password-button">
            Change Password
          </Button>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaWrapper>
  );
};

const createStyles = (colors: any) => ({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(12),
    borderBottomWidth: 1,
    borderBottomColor: colors.border.light,
    backgroundColor: colors.surface.primary,
  },
  backButton: {
    padding: getResponsiveSpacing(8),
  },
  headerTitle: {
    flex: 1,
    fontSize: getResponsiveFontSize(18),
    fontWeight: '600',
    color: colors.text.primary,
    textAlign: 'center' as const,
  },
  placeholder: {
    width: getResponsiveSpacing(40),
  },
  content: {
    flex: 1,
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(24),
  },
  formTitle: {
    fontSize: getResponsiveFontSize(24),
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: getResponsiveSpacing(8),
    textAlign: 'center' as const,
  },
  formSubtitle: {
    fontSize: getResponsiveFontSize(16),
    color: colors.text.secondary,
    textAlign: 'center' as const,
    marginBottom: getResponsiveSpacing(32),
  },
  formField: {
    marginBottom: getResponsiveSpacing(20),
  },
  fieldLabel: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '500',
    color: colors.text.primary,
    marginBottom: getResponsiveSpacing(8),
  },
  passwordContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    borderWidth: 1,
    borderColor: colors.border.light,
    borderRadius: getResponsiveSpacing(8),
    backgroundColor: colors.surface.primary,
  },
  passwordInput: {
    flex: 1,
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(12),
    fontSize: getResponsiveFontSize(16),
    color: colors.text.primary,
  },
  eyeButton: {
    padding: getResponsiveSpacing(12),
  },
  inputError: {
    borderColor: colors.error?.text || '#EF4444',
  },
  errorText: {
    fontSize: getResponsiveFontSize(12),
    color: colors.error?.text || '#EF4444',
    marginTop: getResponsiveSpacing(4),
  },
  changeButton: {
    marginTop: getResponsiveSpacing(24),
    width: '100%',
  },
});
