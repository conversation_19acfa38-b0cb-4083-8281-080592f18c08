/**
 * Service Discovery Types
 *
 * TypeScript interfaces and types for service discovery functionality
 * Aligned with backend API structure and responsive design requirements
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

// Base types
export interface BaseEntity {
  id: string;
  created_at: string;
  updated_at: string;
}

// Location and address types
export interface Location {
  address: string;
  city: string;
  state: string;
  zip_code: string;
  latitude?: number;
  longitude?: number;
}

export interface OperatingHours {
  day:
    | 'monday'
    | 'tuesday'
    | 'wednesday'
    | 'thursday'
    | 'friday'
    | 'saturday'
    | 'sunday';
  is_open: boolean;
  open_time: string;
  close_time: string;
}

// Service Category
export interface ServiceCategory extends BaseEntity {
  name: string;
  description: string;
  icon: string;
  color: string;
  service_count: number;
  is_popular: boolean;
  is_active: boolean;
  parent?: string;
}

// Service Provider
export interface ServiceProvider extends BaseEntity {
  user: string;
  business_name: string;
  description: string;
  business_phone: string;
  business_email: string;
  website?: string;
  instagram_handle?: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  latitude?: number;
  longitude?: number;
  cover_image?: string;
  profile_image?: string;
  rating: number;
  review_count: number;
  is_verified: boolean;
  is_featured: boolean;
  is_active: boolean;
  categories: string[];
  operating_hours?: OperatingHours[];
  distance?: string;
  price_range?: string;
}

// Service
export interface Service extends BaseEntity {
  provider: string;
  provider_details?: ServiceProvider;
  category: string;
  category_details?: ServiceCategory;
  name: string;
  description: string;
  short_description?: string;
  mobile_description?: string;
  base_price: number;
  price_type: 'fixed' | 'hourly' | 'custom';
  max_price?: number;
  duration: number;
  buffer_time?: number;
  requirements?: string[];
  preparation_instructions?: string;
  image?: string;
  images?: string[];
  is_active: boolean;
  is_available: boolean;
  is_popular: boolean;
  booking_count: number;
  average_rating?: number;
  review_count?: number;
}

// Service with full provider details (for service details screen)
export interface ServiceDetails extends Service {
  provider_details: ServiceProvider;
  category_details: ServiceCategory;
  reviews: Review[];
  availability: TimeSlot[];
  gallery: ServiceImage[];
}

// Review
export interface Review extends BaseEntity {
  customer: string;
  customer_name: string;
  provider: string;
  provider_name: string;
  service?: string;
  booking: string;
  rating: number;
  comment: string;
  images?: Record<string, any>;
  is_verified: boolean;
}

// Time slot for availability
export interface TimeSlot {
  id: string;
  start_time: string;
  end_time: string;
  is_available: boolean;
  date: string;
}

// Service image/gallery
export interface ServiceImage {
  id: string;
  service: string;
  image: string;
  caption?: string;
  is_primary: boolean;
  order: number;
}

// Search and filter types
export interface SearchFilters {
  query?: string;
  category?: string;
  location?: {
    latitude: number;
    longitude: number;
    radius?: number; // in kilometers
    address?: string;
  };
  price_min?: number;
  price_max?: number;
  rating_min?: number;
  sort_by?: 'relevance' | 'price' | 'rating' | 'distance';
  availability?: boolean;
  distance_max?: number;
  is_popular?: boolean;
  is_featured?: boolean;
  page?: number;
  page_size?: number;
}

// Pagination
export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// API Response types
export interface SearchResponse {
  providers: PaginatedResponse<ServiceProvider>;
  services: PaginatedResponse<Service>;
  categories: ServiceCategory[];
  total_results: number;
}

// Search state for components
export interface SearchState {
  query: string;
  filters: SearchFilters;
  results: {
    providers: ServiceProvider[];
    services: Service[];
    categories: ServiceCategory[];
  };
  isLoading: boolean;
  error: string | null;
  hasMore: boolean;
  page: number;
}

// Service details state
export interface ServiceDetailsState {
  service: ServiceDetails | null;
  isLoading: boolean;
  error: string | null;
  isBooking: boolean;
}

// Navigation types
export interface ServiceDiscoveryNavigationParams {
  Search: undefined;
  ServiceDetails: {
    serviceId: string;
    providerId?: string;
  };
  ProviderDetails: {
    providerId: string;
  };
  BookingFlow: {
    serviceId: string;
    providerId: string;
  };
}

// Component props types
export interface SearchScreenProps {
  initialQuery?: string;
  initialFilters?: Partial<SearchFilters>;
}

export interface ServiceDetailsScreenProps {
  serviceId: string;
  providerId?: string;
}

// Responsive design types
export interface ResponsiveStyles {
  fontSize: number;
  spacing: number;
  isTablet: boolean;
  isSmallScreen: boolean;
  screenWidth: number;
  screenHeight: number;
}

// Error types
export interface ServiceDiscoveryError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

// API configuration
export interface ServiceDiscoveryApiConfig {
  baseUrl: string;
  timeout: number;
  retryAttempts: number;
  cacheTimeout: number;
}
