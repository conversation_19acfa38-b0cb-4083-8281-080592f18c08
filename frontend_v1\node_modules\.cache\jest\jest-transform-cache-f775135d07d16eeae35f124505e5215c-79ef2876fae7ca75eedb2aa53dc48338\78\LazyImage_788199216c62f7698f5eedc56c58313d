03965eb52b339e0f68257de1011209fa
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.LazyImage = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _ThemeContext = require("../../contexts/ThemeContext");
var _imageAccessibilityUtils = require("../../utils/imageAccessibilityUtils");
var _Text = require("../atoms/Text");
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["source", "placeholder", "fallback", "width", "height", "aspectRatio", "lazy", "threshold", "fadeInDuration", "onLoadStart", "onLoadEnd", "onError", "containerStyle", "imageStyle", "testID", "accessibilityLabel", "accessibilityHint", "accessibilityRole", "alt", "isDecorative", "imageContext", "validateAccessibility"];
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var LazyImage = exports.LazyImage = function LazyImage(_ref) {
  var source = _ref.source,
    placeholder = _ref.placeholder,
    fallback = _ref.fallback,
    width = _ref.width,
    height = _ref.height,
    aspectRatio = _ref.aspectRatio,
    _ref$lazy = _ref.lazy,
    lazy = _ref$lazy === void 0 ? true : _ref$lazy,
    _ref$threshold = _ref.threshold,
    threshold = _ref$threshold === void 0 ? 0.1 : _ref$threshold,
    _ref$fadeInDuration = _ref.fadeInDuration,
    fadeInDuration = _ref$fadeInDuration === void 0 ? 300 : _ref$fadeInDuration,
    onLoadStart = _ref.onLoadStart,
    onLoadEnd = _ref.onLoadEnd,
    onError = _ref.onError,
    containerStyle = _ref.containerStyle,
    imageStyle = _ref.imageStyle,
    _ref$testID = _ref.testID,
    testID = _ref$testID === void 0 ? 'lazy-image' : _ref$testID,
    accessibilityLabel = _ref.accessibilityLabel,
    accessibilityHint = _ref.accessibilityHint,
    _ref$accessibilityRol = _ref.accessibilityRole,
    accessibilityRole = _ref$accessibilityRol === void 0 ? 'image' : _ref$accessibilityRol,
    alt = _ref.alt,
    _ref$isDecorative = _ref.isDecorative,
    isDecorative = _ref$isDecorative === void 0 ? false : _ref$isDecorative,
    imageContext = _ref.imageContext,
    _ref$validateAccessib = _ref.validateAccessibility,
    validateAccessibility = _ref$validateAccessib === void 0 ? __DEV__ : _ref$validateAccessib,
    imageProps = (0, _objectWithoutProperties2.default)(_ref, _excluded);
  var _useTheme = (0, _ThemeContext.useTheme)(),
    isDark = _useTheme.isDark,
    colors = _useTheme.colors;
  var enhancedAccessibilityProps = _react.default.useMemo(function () {
    if (imageContext) {
      return (0, _imageAccessibilityUtils.generateImageAccessibilityProps)(imageContext, alt || accessibilityLabel);
    }
    return {
      accessibilityLabel: alt || accessibilityLabel,
      accessibilityHint: accessibilityHint,
      accessibilityRole: accessibilityRole,
      accessible: !isDecorative,
      importantForAccessibility: isDecorative ? 'no' : 'yes'
    };
  }, [imageContext, alt, accessibilityLabel, accessibilityHint, accessibilityRole, isDecorative]);
  var _useState = (0, _react.useState)(!lazy),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    isInView = _useState2[0],
    setIsInView = _useState2[1];
  var _useState3 = (0, _react.useState)(false),
    _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
    isLoading = _useState4[0],
    setIsLoading = _useState4[1];
  var _useState5 = (0, _react.useState)(false),
    _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
    isLoaded = _useState6[0],
    setIsLoaded = _useState6[1];
  var _useState7 = (0, _react.useState)(false),
    _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
    hasError = _useState8[0],
    setHasError = _useState8[1];
  var _useState9 = (0, _react.useState)(null),
    _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
    metrics = _useState0[0],
    setMetrics = _useState0[1];
  var containerRef = (0, _react.useRef)(null);
  var fadeAnim = (0, _react.useRef)(new _reactNative.Animated.Value(0)).current;
  var observerRef = (0, _react.useRef)(null);
  var screenWidth = _reactNative.Dimensions.get('window').width;
  var calculatedWidth = width || screenWidth;
  var calculatedHeight = height || (aspectRatio ? calculatedWidth / aspectRatio : calculatedWidth);
  (0, _react.useEffect)(function () {
    if (!lazy) {
      setIsInView(true);
      return;
    }
    var immediateLoadTimer = setTimeout(function () {
      setIsInView(true);
    }, 100);
    var preloadTimer = setTimeout(function () {
      if (typeof source === 'object' && source.uri) {
        _reactNative.Image.prefetch(source.uri).catch(function () {});
      }
    }, 500);
    return function () {
      clearTimeout(immediateLoadTimer);
      clearTimeout(preloadTimer);
    };
  }, [lazy, source]);
  var handleLayout = (0, _react.useCallback)(function (event) {
    if (!lazy || isInView) return;
    var y = event.nativeEvent.layout.y;
    var screenHeight = _reactNative.Dimensions.get('window').height;
    if (y < screenHeight * (1 + threshold)) {
      setIsInView(true);
    }
  }, [lazy, isInView, threshold]);
  var handleLoadStart = (0, _react.useCallback)(function () {
    setIsLoading(true);
    setHasError(false);
    var startTime = performance.now();
    setMetrics({
      startTime: startTime,
      wasLazy: lazy
    });
    onLoadStart == null || onLoadStart();
  }, [lazy, onLoadStart]);
  var handleLoadEnd = (0, _react.useCallback)(function () {
    setIsLoading(false);
    setIsLoaded(true);
    var endTime = performance.now();
    setMetrics(function (prev) {
      return prev ? Object.assign({}, prev, {
        endTime: endTime,
        loadTime: endTime - prev.startTime
      }) : null;
    });
    _reactNative.Animated.timing(fadeAnim, {
      toValue: 1,
      duration: fadeInDuration,
      useNativeDriver: true
    }).start();
    onLoadEnd == null || onLoadEnd();
  }, [fadeAnim, fadeInDuration, onLoadEnd]);
  var handleError = (0, _react.useCallback)(function (error) {
    setIsLoading(false);
    setHasError(true);
    var endTime = performance.now();
    setMetrics(function (prev) {
      return prev ? Object.assign({}, prev, {
        endTime: endTime,
        loadTime: endTime - prev.startTime
      }) : null;
    });
    onError == null || onError(error);
  }, [onError]);
  var getImageSource = function getImageSource() {
    if (hasError && fallback) {
      return fallback;
    }
    if (!isInView && placeholder) {
      return placeholder;
    }
    return source;
  };
  var containerStyles = [styles.container, {
    width: calculatedWidth,
    height: calculatedHeight,
    backgroundColor: colors.gray[100]
  }, containerStyle].filter(Boolean);
  var imageStyles = [styles.image, {
    width: calculatedWidth,
    height: calculatedHeight
  }, imageStyle].filter(Boolean);
  var renderPlaceholder = function renderPlaceholder() {
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: [styles.placeholder, {
        backgroundColor: colors.gray[100]
      }],
      children: (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [styles.placeholderContent, {
          backgroundColor: colors.gray[200]
        }],
        children: (0, _jsxRuntime.jsx)(_Text.Text, {
          style: [styles.placeholderText, {
            color: colors.gray[500]
          }],
          children: isLoading ? 'Loading...' : hasError ? 'Failed to load' : 'Image'
        })
      })
    });
  };
  var renderError = function renderError() {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: [styles.errorContainer, {
        backgroundColor: colors.gray[100]
      }],
      children: [(0, _jsxRuntime.jsx)(_Text.Text, {
        style: [styles.errorText, {
          color: colors.semantic.error
        }],
        children: "Failed to load image"
      }), fallback && (0, _jsxRuntime.jsx)(_reactNative.Image, Object.assign({
        source: fallback,
        style: imageStyles,
        onLoadStart: handleLoadStart,
        onLoadEnd: handleLoadEnd,
        onError: handleError
      }, enhancedAccessibilityProps, imageProps))]
    });
  };
  return (0, _jsxRuntime.jsxs)(_reactNative.View, {
    ref: containerRef,
    style: containerStyles,
    testID: testID,
    accessibilityRole: "image",
    accessibilityLabel: imageProps.accessibilityLabel || 'Image',
    onLayout: handleLayout,
    children: [(!isInView || isLoading || !isLoaded && !hasError) && renderPlaceholder(), hasError && !fallback && renderError(), isInView && !hasError && (0, _jsxRuntime.jsx)(_reactNative.Animated.View, {
      style: [styles.imageContainer, {
        opacity: fadeAnim
      }],
      children: (0, _jsxRuntime.jsx)(_reactNative.Image, Object.assign({
        source: getImageSource(),
        style: imageStyles,
        onLoadStart: handleLoadStart,
        onLoadEnd: handleLoadEnd,
        onError: handleError
      }, enhancedAccessibilityProps, imageProps))
    }), __DEV__ && metrics && metrics.loadTime && (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.metricsOverlay,
      children: (0, _jsxRuntime.jsxs)(_Text.Text, {
        style: styles.metricsText,
        children: [metrics.loadTime.toFixed(0), "ms", metrics.wasLazy ? ' (lazy)' : '']
      })
    })]
  });
};
var styles = _reactNative.StyleSheet.create({
  container: {
    position: 'relative',
    overflow: 'hidden',
    borderRadius: 8
  },
  image: {
    resizeMode: 'cover'
  },
  imageContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0
  },
  placeholder: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center'
  },
  placeholderContent: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6
  },
  placeholderText: {
    fontSize: 13,
    fontWeight: '500'
  },
  errorContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center'
  },
  errorText: {
    fontSize: 13,
    fontWeight: '500',
    textAlign: 'center'
  },
  metricsOverlay: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4
  },
  metricsText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '500'
  }
});
var _default = exports.default = LazyImage;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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