dfc19e47c76dcdafde2f68cae0df16b4
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.validateTouchTargetSize = exports.prefersReducedMotion = exports.meetsWCAGAA = exports.hexToRgb = exports.getWCAGCompliantColorLocal = exports.getWCAGCompliantColor = exports.getTouchTargetStyle = exports.getResponsiveSpacing = exports.getResponsiveFontSize = exports.getRelativeLuminance = exports.getRecommendedTouchTarget = exports.getMinimumTouchTarget = exports.getImageAccessibilityProps = exports.getFocusIndicatorStyle = exports.getContrastRatio = exports.enhanceAccessibilityProps = exports.default = exports.accessibilityUtils = exports.WCAG_STANDARDS = exports.WCAG_STANDARDS = exports.VoiceControlUtils = exports.TouchTargetUtils = exports.TouchTargetUtils = exports.SinglePointerUtils = exports.ScreenReaderUtils = exports.ScreenReaderUtils = exports.ImageAccessibilityUtils = exports.GestureAccessibilityUtils = exports.FocusUtils = exports.FocusUtils = exports.FocusManagementUtils = exports.ColorContrastUtils = exports.ColorContrastUtils = exports.CognitiveAccessibilityUtils = exports.AccessibilityUtils = exports.AccessibilityTestUtils = exports.AccessibilityTestUtils = void 0;
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _reactNative = require("react-native");
var _colorContrastAudit = require("./colorContrastAudit");
var WCAG_STANDARDS = exports.WCAG_STANDARDS = exports.WCAG_STANDARDS = {
  CONTRAST_RATIOS: {
    AA_NORMAL: 4.5,
    AA_LARGE: 3.0,
    AAA_NORMAL: 7.0,
    AAA_LARGE: 4.5
  },
  TOUCH_TARGETS: {
    MINIMUM_SIZE: 44,
    RECOMMENDED_SIZE: 48,
    SPACING: 8
  },
  TARGET_SIZE: {
    MINIMUM: 44
  },
  FOCUS_INDICATORS: {
    MIN_WIDTH: 2,
    RECOMMENDED_WIDTH: 3,
    OFFSET: 2,
    Z_INDEX_BASE: 9999,
    SHADOW_OPACITY: 0.4,
    SHADOW_RADIUS: 6
  }
};
if (typeof global !== 'undefined') {
  global.WCAG_STANDARDS = WCAG_STANDARDS;
}
var hexToRgb = exports.hexToRgb = function hexToRgb(hex) {
  var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
};
var getRelativeLuminance = exports.getRelativeLuminance = function getRelativeLuminance(r, g, b) {
  var _map = [r, g, b].map(function (c) {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    }),
    _map2 = (0, _slicedToArray2.default)(_map, 3),
    rs = _map2[0],
    gs = _map2[1],
    bs = _map2[2];
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
};
var getContrastRatio = exports.getContrastRatio = function getContrastRatio(color1, color2) {
  var rgb1 = hexToRgb(color1);
  var rgb2 = hexToRgb(color2);
  if (!rgb1 || !rgb2) return 1;
  var lum1 = getRelativeLuminance(rgb1.r, rgb1.g, rgb1.b);
  var lum2 = getRelativeLuminance(rgb2.r, rgb2.g, rgb2.b);
  var brightest = Math.max(lum1, lum2);
  var darkest = Math.min(lum1, lum2);
  return (brightest + 0.05) / (darkest + 0.05);
};
var meetsWCAGAA = exports.meetsWCAGAA = function meetsWCAGAA(foreground, background) {
  var isLargeText = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
  var ratio = getContrastRatio(foreground, background);
  var requiredRatio = isLargeText ? WCAG_STANDARDS.CONTRAST_RATIOS.AA_LARGE : WCAG_STANDARDS.CONTRAST_RATIOS.AA_NORMAL;
  return ratio >= requiredRatio;
};
var getWCAGCompliantColorLocal = exports.getWCAGCompliantColorLocal = function getWCAGCompliantColorLocal(baseColor, backgroundColor) {
  var isLargeText = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
  if (meetsWCAGAA(baseColor, backgroundColor, isLargeText)) {
    return baseColor;
  }
  var compliantColors = {
    sage: {
      light: '#4A6B52',
      medium: '#3A5B42',
      dark: '#2A4B32',
      darker: '#1F3A26'
    },
    neutral: {
      dark: '#374151',
      darker: '#1F2937'
    }
  };
  if (backgroundColor === '#FFFFFF' || backgroundColor === '#F9FAFB') {
    return isLargeText ? compliantColors.sage.light : compliantColors.sage.medium;
  }
  return baseColor;
};
var getFocusIndicatorStyle = exports.getFocusIndicatorStyle = function getFocusIndicatorStyle() {
  var baseColor = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '#3B82F6';
  return {
    borderWidth: WCAG_STANDARDS.FOCUS_INDICATORS.RECOMMENDED_WIDTH,
    borderColor: baseColor,
    borderStyle: 'solid',
    shadowColor: baseColor,
    shadowOffset: {
      width: 0,
      height: 0
    },
    shadowOpacity: WCAG_STANDARDS.FOCUS_INDICATORS.SHADOW_OPACITY,
    shadowRadius: WCAG_STANDARDS.FOCUS_INDICATORS.SHADOW_RADIUS,
    elevation: _reactNative.Platform.OS === 'android' ? 8 : 0,
    zIndex: WCAG_STANDARDS.FOCUS_INDICATORS.Z_INDEX_BASE
  };
};
var FocusUtils = exports.FocusUtils = exports.FocusUtils = {
  ensureFocusVisible: function ensureFocusVisible(element) {
    if (_reactNative.Platform.OS === 'web') {
      if (element && element.focus) {
        element.focus();
        element.scrollIntoView == null || element.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'nearest'
        });
      }
    }
  },
  getEnhancedFocusStyle: function getEnhancedFocusStyle(color) {
    var focusColor = color || '#3B82F6';
    return Object.assign({}, getFocusIndicatorStyle(focusColor), {
      outlineWidth: WCAG_STANDARDS.FOCUS_INDICATORS.RECOMMENDED_WIDTH,
      outlineColor: focusColor,
      outlineStyle: 'solid',
      outlineOffset: WCAG_STANDARDS.FOCUS_INDICATORS.OFFSET
    });
  },
  checkFocusObscured: function checkFocusObscured(elementY) {
    var stickyFooterHeight = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 80;
    var screenHeight = _reactNative.Platform.OS === 'web' ? window.innerHeight : 800;
    var focusAreaBottom = elementY + WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE;
    var stickyFooterTop = screenHeight - stickyFooterHeight;
    return focusAreaBottom > stickyFooterTop;
  },
  getFocusIndicatorStyle: function getFocusIndicatorStyle(isFocused) {
    var baseStyle = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
    var _options$color = options.color,
      color = _options$color === void 0 ? '#3B82F6' : _options$color,
      _options$width = options.width,
      requestedWidth = _options$width === void 0 ? WCAG_STANDARDS.FOCUS_INDICATORS.RECOMMENDED_WIDTH : _options$width,
      _options$offset = options.offset,
      offset = _options$offset === void 0 ? WCAG_STANDARDS.FOCUS_INDICATORS.OFFSET : _options$offset,
      _options$preventObscu = options.preventObscuring,
      preventObscuring = _options$preventObscu === void 0 ? true : _options$preventObscu;
    var width = Math.max(WCAG_STANDARDS.FOCUS_INDICATORS.MIN_WIDTH, requestedWidth);
    if (!isFocused) {
      return baseStyle;
    }
    var baseStyles = Object.assign({}, baseStyle, {
      borderWidth: width,
      borderColor: color,
      borderStyle: 'solid',
      shadowColor: color,
      shadowOffset: {
        width: 0,
        height: 0
      },
      shadowOpacity: WCAG_STANDARDS.FOCUS_INDICATORS.SHADOW_OPACITY,
      shadowRadius: WCAG_STANDARDS.FOCUS_INDICATORS.SHADOW_RADIUS,
      overflow: 'visible',
      zIndex: preventObscuring ? WCAG_STANDARDS.FOCUS_INDICATORS.Z_INDEX_BASE : undefined,
      backgroundColor: `${color}10`,
      borderRadius: Math.max(baseStyle.borderRadius || 0, 4)
    });
    var platformStyles = _reactNative.Platform.select({
      web: {
        outlineWidth: width,
        outlineColor: color,
        outlineStyle: 'solid',
        outlineOffset: offset
      },
      android: {
        elevation: 6
      },
      ios: {
        elevation: 0
      },
      default: {}
    });
    return Object.assign({}, baseStyles, platformStyles);
  },
  ensureFocusNotObscured: function ensureFocusNotObscured(focusedElementStyle) {
    var stickyElements = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];
    var maxStickyZIndex = stickyElements.reduce(function (max, element) {
      if (element.position === 'sticky' || element.position === 'fixed') {
        return Math.max(max, element.zIndex || 0);
      }
      return max;
    }, 0);
    return Object.assign({}, focusedElementStyle, {
      zIndex: Math.max(focusedElementStyle.zIndex || 0, maxStickyZIndex + 1, WCAG_STANDARDS.FOCUS_INDICATORS.Z_INDEX_BASE)
    });
  },
  createFocusManager: function createFocusManager() {
    var currentFocusedElement = null;
    return {
      setFocus: function setFocus(element) {
        currentFocusedElement = element;
      },
      getCurrentFocus: function getCurrentFocus() {
        return currentFocusedElement;
      },
      clearFocus: function clearFocus() {
        currentFocusedElement = null;
      },
      moveFocus: function moveFocus(direction) {
        console.log(`Moving focus ${direction}`);
      }
    };
  }
};
var getMinimumTouchTarget = exports.getMinimumTouchTarget = function getMinimumTouchTarget() {
  return WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE;
};
var getRecommendedTouchTarget = exports.getRecommendedTouchTarget = function getRecommendedTouchTarget() {
  return WCAG_STANDARDS.TOUCH_TARGETS.RECOMMENDED_SIZE;
};
var validateTouchTargetSize = exports.validateTouchTargetSize = function validateTouchTargetSize(width, height) {
  var minSize = WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE;
  return width >= minSize && height >= minSize;
};
var getTouchTargetStyle = exports.getTouchTargetStyle = function getTouchTargetStyle(customSize) {
  var size = customSize || WCAG_STANDARDS.TOUCH_TARGETS.RECOMMENDED_SIZE;
  return {
    minWidth: size,
    minHeight: size,
    paddingHorizontal: Math.max(0, (size - 24) / 2),
    paddingVertical: Math.max(0, (size - 24) / 2)
  };
};
var TouchTargetUtils = exports.TouchTargetUtils = exports.TouchTargetUtils = {
  getSpacing: function getSpacing() {
    return WCAG_STANDARDS.TOUCH_TARGETS.SPACING;
  },
  getToolbarIconStyle: function getToolbarIconStyle() {
    return Object.assign({}, getTouchTargetStyle(WCAG_STANDARDS.TOUCH_TARGETS.RECOMMENDED_SIZE), {
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 8
    });
  },
  validate: validateTouchTargetSize,
  validateTouchTarget: function validateTouchTarget(width, height) {
    var minSize = WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE;
    var issues = [];
    if (width < minSize) {
      issues.push(`Width ${width}px is below minimum ${minSize}px`);
    }
    if (height < minSize) {
      issues.push(`Height ${height}px is below minimum ${minSize}px`);
    }
    return {
      isValid: issues.length === 0,
      issues: issues
    };
  },
  getMinSize: getMinimumTouchTarget,
  getRecommendedSize: getRecommendedTouchTarget,
  getPlatformTouchTarget: function getPlatformTouchTarget() {
    return _reactNative.Platform.select({
      ios: 44,
      android: 48,
      default: 44
    });
  },
  calculateHitSlop: function calculateHitSlop(targetSize) {
    var minSize = WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE;
    var deficit = Math.max(0, minSize - targetSize);
    var slop = Math.ceil(deficit / 2);
    return {
      top: slop,
      bottom: slop,
      left: slop,
      right: slop
    };
  }
};
var getImageAccessibilityProps = exports.getImageAccessibilityProps = function getImageAccessibilityProps(type, description, action) {
  switch (type) {
    case 'decorative':
      return {
        accessibilityLabel: '',
        accessibilityRole: 'image',
        accessible: false,
        importantForAccessibility: 'no'
      };
    case 'informative':
      return {
        accessibilityLabel: description || 'Informative image',
        accessibilityRole: 'image',
        accessible: true,
        importantForAccessibility: 'yes'
      };
    case 'functional':
      return {
        accessibilityLabel: description || 'Interactive image',
        accessibilityHint: action ? `Double tap to ${action}` : undefined,
        accessibilityRole: 'imagebutton',
        accessible: true,
        importantForAccessibility: 'yes'
      };
    default:
      return {
        accessibilityLabel: description || 'Image',
        accessibilityRole: 'image',
        accessible: true,
        importantForAccessibility: 'yes'
      };
  }
};
var ImageAccessibilityUtils = exports.ImageAccessibilityUtils = {
  getDecorative: function getDecorative() {
    return getImageAccessibilityProps('decorative');
  },
  getInformative: function getInformative(description) {
    return getImageAccessibilityProps('informative', description);
  },
  getFunctional: function getFunctional(description, action) {
    return getImageAccessibilityProps('functional', description, action);
  },
  generateAltText: function generateAltText(type, entityName, action) {
    switch (type) {
      case 'logo':
        return entityName ? `${entityName} logo` : 'Company logo';
      case 'avatar':
        return entityName ? `${entityName} profile picture` : 'User profile picture';
      case 'service':
        return entityName ? `${entityName} service image` : 'Service image';
      case 'store':
        return entityName ? `${entityName} store image` : 'Store image';
      case 'icon':
        return action ? `${action} icon` : entityName || 'Icon';
      default:
        return entityName || 'Image';
    }
  }
};
var SinglePointerUtils = exports.SinglePointerUtils = {
  getDragAlternative: function getDragAlternative(onMove) {
    return {
      onMoveUp: function onMoveUp() {
        return onMove('up');
      },
      onMoveDown: function onMoveDown() {
        return onMove('down');
      },
      accessibilityActions: [{
        name: 'increment',
        label: 'Move up'
      }, {
        name: 'decrement',
        label: 'Move down'
      }],
      onAccessibilityAction: function onAccessibilityAction(event) {
        switch (event.nativeEvent.actionName) {
          case 'increment':
            onMove('up');
            break;
          case 'decrement':
            onMove('down');
            break;
        }
      }
    };
  },
  getSwipeAlternative: function getSwipeAlternative(onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown) {
    return {
      buttons: [onSwipeLeft && {
        label: 'Previous',
        onPress: onSwipeLeft
      }, onSwipeRight && {
        label: 'Next',
        onPress: onSwipeRight
      }, onSwipeUp && {
        label: 'Up',
        onPress: onSwipeUp
      }, onSwipeDown && {
        label: 'Down',
        onPress: onSwipeDown
      }].filter(Boolean)
    };
  },
  getMultiTouchAlternative: function getMultiTouchAlternative(onPinch, onRotate) {
    return {
      zoomIn: onPinch ? function () {
        return onPinch(1.2);
      } : undefined,
      zoomOut: onPinch ? function () {
        return onPinch(0.8);
      } : undefined,
      rotateLeft: onRotate ? function () {
        return onRotate(-15);
      } : undefined,
      rotateRight: onRotate ? function () {
        return onRotate(15);
      } : undefined
    };
  }
};
var ScreenReaderUtils = exports.ScreenReaderUtils = exports.ScreenReaderUtils = {
  announceForAccessibility: function announceForAccessibility(message) {
    if (_reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'android') {
      _reactNative.AccessibilityInfo.announceForAccessibility(message);
    }
  },
  isScreenReaderEnabled: function () {
    var _isScreenReaderEnabled = (0, _asyncToGenerator2.default)(function* () {
      try {
        return yield _reactNative.AccessibilityInfo.isScreenReaderEnabled();
      } catch (_unused) {
        return false;
      }
    });
    function isScreenReaderEnabled() {
      return _isScreenReaderEnabled.apply(this, arguments);
    }
    return isScreenReaderEnabled;
  }(),
  getAccessibleLabel: function getAccessibleLabel(primary, secondary, state, position) {
    var parts = [primary];
    if (secondary) parts.push(secondary);
    if (state) parts.push(state);
    if (position) parts.push(position);
    return parts.join(', ');
  },
  generateAccessibleLabel: function generateAccessibleLabel(primary, state, context, hint) {
    var parts = [primary];
    if (state) parts.push(state);
    if (context) parts.push(context);
    if (hint) parts.push(hint);
    return parts.join(', ');
  },
  getSemanticRole: function getSemanticRole(componentType) {
    var roleMap = {
      button: 'button',
      input: 'text',
      checkbox: 'checkbox',
      radio: 'radio',
      link: 'link',
      image: 'image',
      text: 'text',
      header: 'header',
      list: 'list',
      listitem: 'listitem'
    };
    return roleMap[componentType] || 'none';
  }
};
var prefersReducedMotion = exports.prefersReducedMotion = function () {
  var _ref = (0, _asyncToGenerator2.default)(function* () {
    try {
      return yield _reactNative.AccessibilityInfo.isReduceMotionEnabled();
    } catch (_unused2) {
      return false;
    }
  });
  return function prefersReducedMotion() {
    return _ref.apply(this, arguments);
  };
}();
var getResponsiveSpacing = exports.getResponsiveSpacing = function getResponsiveSpacing(baseSpacing) {
  return Math.max(baseSpacing, WCAG_STANDARDS.TOUCH_TARGETS.SPACING);
};
var getResponsiveFontSize = exports.getResponsiveFontSize = function getResponsiveFontSize(baseFontSize) {
  return Math.max(baseFontSize, 12);
};
var enhanceAccessibilityProps = exports.enhanceAccessibilityProps = function enhanceAccessibilityProps(props) {
  return Object.assign({}, props, {
    accessible: props.accessible !== false,
    accessibilityRole: props.accessibilityRole || 'button',
    importantForAccessibility: props.importantForAccessibility || 'yes'
  });
};
var AccessibilityUtils = exports.AccessibilityUtils = {
  WCAG_STANDARDS: WCAG_STANDARDS,
  hexToRgb: hexToRgb,
  getRelativeLuminance: getRelativeLuminance,
  getContrastRatio: getContrastRatio,
  meetsWCAGAA: meetsWCAGAA,
  getWCAGCompliantColor: getWCAGCompliantColorLocal,
  FocusUtils: FocusUtils,
  getFocusIndicatorStyle: getFocusIndicatorStyle,
  TouchTargetUtils: TouchTargetUtils,
  getMinimumTouchTarget: getMinimumTouchTarget,
  getRecommendedTouchTarget: getRecommendedTouchTarget,
  validateTouchTargetSize: validateTouchTargetSize,
  getTouchTargetStyle: getTouchTargetStyle,
  ImageAccessibilityUtils: ImageAccessibilityUtils,
  getImageAccessibilityProps: getImageAccessibilityProps,
  SinglePointerUtils: SinglePointerUtils,
  ScreenReaderUtils: ScreenReaderUtils,
  prefersReducedMotion: prefersReducedMotion,
  getResponsiveSpacing: getResponsiveSpacing,
  getResponsiveFontSize: getResponsiveFontSize,
  enhanceAccessibilityProps: enhanceAccessibilityProps
};
Object.freeze(WCAG_STANDARDS);
Object.freeze(WCAG_STANDARDS.CONTRAST_RATIOS);
Object.freeze(WCAG_STANDARDS.TOUCH_TARGETS);
Object.freeze(WCAG_STANDARDS.TARGET_SIZE);
Object.freeze(WCAG_STANDARDS.FOCUS_INDICATORS);
var ColorContrastUtils = exports.ColorContrastUtils = exports.ColorContrastUtils = {
  hexToRgb: function hexToRgb(hex) {
    var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  },
  getRelativeLuminance: function getRelativeLuminance(hex) {
    var rgb = ColorContrastUtils.hexToRgb(hex);
    if (!rgb) return 0;
    var r = rgb.r,
      g = rgb.g,
      b = rgb.b;
    var _map3 = [r, g, b].map(function (c) {
        c = c / 255;
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
      }),
      _map4 = (0, _slicedToArray2.default)(_map3, 3),
      rs = _map4[0],
      gs = _map4[1],
      bs = _map4[2];
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  },
  getContrastRatio: function getContrastRatio(color1, color2) {
    var l1 = ColorContrastUtils.getRelativeLuminance(color1);
    var l2 = ColorContrastUtils.getRelativeLuminance(color2);
    var lighter = Math.max(l1, l2);
    var darker = Math.min(l1, l2);
    return (lighter + 0.05) / (darker + 0.05);
  },
  validateContrast: function validateContrast(foreground, background) {
    var level = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'AA';
    var isLargeText = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;
    var ratio = ColorContrastUtils.getContrastRatio(foreground, background);
    var requiredRatio;
    if (level === 'AAA') {
      requiredRatio = isLargeText ? 4.5 : 7.0;
    } else {
      requiredRatio = isLargeText ? 3.0 : 4.5;
    }
    var isCompliant = ratio >= requiredRatio;
    var recommendation = '';
    if (!isCompliant) {
      var improvement = (requiredRatio / ratio).toFixed(2);
      recommendation = `Increase contrast by ${improvement}x to meet ${level} standards`;
    } else {
      recommendation = `Meets ${level} standards (${ratio.toFixed(2)}:1)`;
    }
    return {
      ratio: Math.round(ratio * 100) / 100,
      isCompliant: isCompliant,
      requiredRatio: requiredRatio,
      recommendation: recommendation
    };
  },
  getAccessibleTextColor: function getAccessibleTextColor(backgroundColor) {
    var whiteContrast = ColorContrastUtils.getContrastRatio('#FFFFFF', backgroundColor);
    var blackContrast = ColorContrastUtils.getContrastRatio('#000000', backgroundColor);
    return whiteContrast > blackContrast ? '#FFFFFF' : '#000000';
  },
  enhanceColorContrast: function enhanceColorContrast(color, targetBackground) {
    var targetRatio = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 4.5;
    var currentRatio = ColorContrastUtils.getContrastRatio(color, targetBackground);
    if (currentRatio >= targetRatio) {
      return color;
    }
    var rgb = ColorContrastUtils.hexToRgb(color);
    if (!rgb) return color;
    var backgroundLuminance = ColorContrastUtils.getRelativeLuminance(targetBackground);
    var enhanced;
    if (backgroundLuminance > 0.5) {
      var factor = 0.3;
      enhanced = {
        r: Math.max(0, Math.round(rgb.r * factor)),
        g: Math.max(0, Math.round(rgb.g * factor)),
        b: Math.max(0, Math.round(rgb.b * factor))
      };
    } else {
      var _factor = 2.5;
      enhanced = {
        r: Math.min(255, Math.round(rgb.r * _factor)),
        g: Math.min(255, Math.round(rgb.g * _factor)),
        b: Math.min(255, Math.round(rgb.b * _factor))
      };
    }
    var enhancedHex = `#${enhanced.r.toString(16).padStart(2, '0')}${enhanced.g.toString(16).padStart(2, '0')}${enhanced.b.toString(16).padStart(2, '0')}`;
    var newRatio = ColorContrastUtils.getContrastRatio(enhancedHex, targetBackground);
    if (newRatio >= targetRatio) {
      return enhancedHex;
    }
    return backgroundLuminance > 0.5 ? '#000000' : '#FFFFFF';
  }
};
var AccessibilityTestUtils = exports.AccessibilityTestUtils = exports.AccessibilityTestUtils = {
  auditComponent: function auditComponent(componentProps) {
    var _componentProps$style, _componentProps$style2;
    var issues = [];
    var warnings = [];
    var recommendations = [];
    if (!componentProps.accessibilityLabel && !componentProps.children) {
      issues.push('Missing accessibility label');
      recommendations.push('Add accessibilityLabel prop');
    }
    if (!componentProps.accessibilityRole) {
      warnings.push('Missing accessibility role');
      recommendations.push('Add appropriate accessibilityRole');
    }
    if ((_componentProps$style = componentProps.style) != null && _componentProps$style.width && (_componentProps$style2 = componentProps.style) != null && _componentProps$style2.height) {
      var validation = TouchTargetUtils.validateTouchTarget(componentProps.style.width, componentProps.style.height);
      if (!validation.isValid) {
        issues.push.apply(issues, (0, _toConsumableArray2.default)(validation.issues));
        recommendations.push.apply(recommendations, (0, _toConsumableArray2.default)(validation.recommendations));
      }
    }
    return {
      issues: issues,
      warnings: warnings,
      recommendations: recommendations
    };
  },
  generateAccessibilityReport: function generateAccessibilityReport(components) {
    var details = components.map(function (component, index) {
      return {
        componentIndex: index,
        audit: AccessibilityTestUtils.auditComponent(component)
      };
    });
    var totalIssues = details.reduce(function (sum, detail) {
      return sum + detail.audit.issues.length;
    }, 0);
    var totalWarnings = details.reduce(function (sum, detail) {
      return sum + detail.audit.warnings.length;
    }, 0);
    var complianceScore = Math.max(0, 100 - totalIssues * 10 - totalWarnings * 5);
    return {
      totalComponents: components.length,
      issuesFound: totalIssues,
      warningsFound: totalWarnings,
      complianceScore: complianceScore,
      details: details
    };
  }
};
var VoiceControlUtils = exports.VoiceControlUtils = {
  isVoiceControlAvailable: function isVoiceControlAvailable() {
    return _reactNative.Platform.OS === 'ios' && _reactNative.Platform.Version >= '13.0';
  },
  generateVoiceLabel: function generateVoiceLabel(text, context) {
    var cleanText = text.replace(/[^\w\s]/gi, '').toLowerCase();
    return context ? `${context} ${cleanText}` : cleanText;
  },
  createVoiceHints: function createVoiceHints(actions) {
    return `Available actions: ${actions.join(', ')}`;
  }
};
var GestureAccessibilityUtils = exports.GestureAccessibilityUtils = {
  needsAlternativeGestures: function () {
    var _needsAlternativeGestures = (0, _asyncToGenerator2.default)(function* () {
      try {
        var isReduceMotionEnabled = yield _reactNative.AccessibilityInfo.isReduceMotionEnabled();
        return isReduceMotionEnabled;
      } catch (_unused3) {
        return false;
      }
    });
    function needsAlternativeGestures() {
      return _needsAlternativeGestures.apply(this, arguments);
    }
    return needsAlternativeGestures;
  }(),
  getGestureAlternatives: function getGestureAlternatives(gestureType) {
    var alternatives = {
      swipe: ['double tap to activate', 'use navigation buttons'],
      pinch: ['use zoom controls', 'double tap to zoom'],
      longPress: ['use context menu button', 'double tap and hold'],
      drag: ['use move buttons', 'select and use arrow keys']
    };
    return alternatives[gestureType] || ['use alternative controls'];
  },
  createGestureInstructions: function createGestureInstructions(gesture, alternative) {
    return `Gesture: ${gesture}. Alternative: ${alternative}`;
  }
};
var CognitiveAccessibilityUtils = exports.CognitiveAccessibilityUtils = {
  simplifyText: function simplifyText(text) {
    var level = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'intermediate';
    if (level === 'basic') {
      return text.replace(/utilize/gi, 'use').replace(/facilitate/gi, 'help').replace(/approximately/gi, 'about').replace(/subsequently/gi, 'then');
    }
    return text;
  },
  estimateReadingTime: function estimateReadingTime(text) {
    var wordsPerMinute = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 200;
    var wordCount = text.split(/\s+/).length;
    var minutes = Math.ceil(wordCount / wordsPerMinute);
    return `Reading time: ${minutes} minute${minutes !== 1 ? 's' : ''}`;
  },
  createProgressIndicator: function createProgressIndicator(currentStep, totalSteps) {
    return `Step ${currentStep} of ${totalSteps}`;
  }
};
var FocusManagementUtils = exports.FocusManagementUtils = FocusUtils;
var accessibilityUtils = exports.accessibilityUtils = AccessibilityUtils;
var _default = exports.default = {
  WCAG_STANDARDS: WCAG_STANDARDS,
  ColorContrastUtils: ColorContrastUtils,
  TouchTargetUtils: TouchTargetUtils,
  FocusUtils: FocusUtils,
  FocusManagementUtils: FocusManagementUtils,
  ScreenReaderUtils: ScreenReaderUtils,
  AccessibilityTestUtils: AccessibilityTestUtils,
  VoiceControlUtils: VoiceControlUtils,
  GestureAccessibilityUtils: GestureAccessibilityUtils,
  CognitiveAccessibilityUtils: CognitiveAccessibilityUtils,
  getWCAGCompliantColor: getWCAGCompliantColorLocal,
  checkColorContrast: function checkColorContrast(foreground, background) {
    var level = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'AA';
    var textSize = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'normal';
    var ratio = (0, _colorContrastAudit.calculateContrastRatio)(foreground, background);
    var requiredRatio = level === 'AAA' ? textSize === 'large' ? 4.5 : 7.0 : textSize === 'large' ? 3.0 : 4.5;
    return ratio >= requiredRatio;
  }
};
var getWCAGCompliantColor = exports.getWCAGCompliantColor = getWCAGCompliantColorLocal;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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