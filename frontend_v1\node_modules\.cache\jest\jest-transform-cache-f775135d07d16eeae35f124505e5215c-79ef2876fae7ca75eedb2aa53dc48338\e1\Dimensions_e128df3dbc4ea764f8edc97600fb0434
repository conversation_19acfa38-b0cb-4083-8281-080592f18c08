f5402fa3bf53e35c57b5bb58a732bd37
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _RCTDeviceEventEmitter = _interopRequireDefault(require("../EventEmitter/RCTDeviceEventEmitter"));
var _EventEmitter = _interopRequireDefault(require("../vendor/emitter/EventEmitter"));
var _NativeDeviceInfo = _interopRequireDefault(require("./NativeDeviceInfo"));
var _invariant = _interopRequireDefault(require("invariant"));
var eventEmitter = new _EventEmitter.default();
var dimensionsInitialized = false;
var dimensions;
var Dimensions = function () {
  function Dimensions() {
    (0, _classCallCheck2.default)(this, Dimensions);
  }
  return (0, _createClass2.default)(Dimensions, null, [{
    key: "get",
    value: function get(dim) {
      (0, _invariant.default)(dimensions[dim], 'No dimension set for key ' + dim);
      return dimensions[dim];
    }
  }, {
    key: "set",
    value: function set(dims) {
      var screen = dims.screen,
        window = dims.window;
      var windowPhysicalPixels = dims.windowPhysicalPixels;
      if (windowPhysicalPixels) {
        window = {
          width: windowPhysicalPixels.width / windowPhysicalPixels.scale,
          height: windowPhysicalPixels.height / windowPhysicalPixels.scale,
          scale: windowPhysicalPixels.scale,
          fontScale: windowPhysicalPixels.fontScale
        };
      }
      var screenPhysicalPixels = dims.screenPhysicalPixels;
      if (screenPhysicalPixels) {
        screen = {
          width: screenPhysicalPixels.width / screenPhysicalPixels.scale,
          height: screenPhysicalPixels.height / screenPhysicalPixels.scale,
          scale: screenPhysicalPixels.scale,
          fontScale: screenPhysicalPixels.fontScale
        };
      } else if (screen == null) {
        screen = window;
      }
      dimensions = {
        window: window,
        screen: screen
      };
      if (dimensionsInitialized) {
        eventEmitter.emit('change', dimensions);
      } else {
        dimensionsInitialized = true;
      }
    }
  }, {
    key: "addEventListener",
    value: function addEventListener(type, handler) {
      (0, _invariant.default)(type === 'change', 'Trying to subscribe to unknown event: "%s"', type);
      return eventEmitter.addListener(type, handler);
    }
  }]);
}();
_RCTDeviceEventEmitter.default.addListener('didUpdateDimensions', function (update) {
  Dimensions.set(update);
});
Dimensions.set(_NativeDeviceInfo.default.getConstants().Dimensions);
var _default = exports.default = Dimensions;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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