{"version": 3, "names": ["_getJest<PERSON>bj", "mock", "_interopRequireDefault", "require", "_asyncToGenerator2", "_slicedToArray2", "_reactNative", "_react", "_reactNative2", "_performanceMonitor", "_ErrorBoundary", "_jsxRuntime", "_require", "jest", "mockPerformanceMonitor", "performanceMonitor", "ErrorComponent", "Error", "ButtonThatThrows", "_ref", "onError", "_React$useState", "React", "useState", "_React$useState2", "default", "shouldThrow", "setShouldThrow", "error", "jsx", "<PERSON><PERSON>", "title", "onPress", "testID", "WorkingComponent", "View", "children", "Text", "describe", "beforeEach", "clearAllMocks", "spyOn", "console", "mockImplementation", "after<PERSON>ach", "restoreAllMocks", "it", "_render", "render", "Error<PERSON>ou<PERSON><PERSON>", "getByText", "expect", "toBeTruthy", "_render2", "getByTestId", "fn", "toHaveBeenCalledWith", "objectContaining", "message", "trackUserInteraction", "componentStack", "any", "String", "_render3", "CustomFallback", "_ref2", "retry", "jsxs", "_render4", "fallback", "global", "__DEV__", "_render5", "TestComponent", "_ref4", "reset<PERSON>ey", "_render6", "resetKeys", "resetOnPropsChange", "rerender", "fireEvent", "press", "waitFor", "maxRetries", "i", "screen", "retryButton", "props", "disabled", "toBe", "TestWrapper", "_ref7", "_React$useState3", "_React$useState4", "key", "<PERSON><PERSON><PERSON>", "k", "_render7", "getByTestID", "OuterFallback", "InnerFallback", "queryByText", "toBeNull", "rn", "requireActual", "Object", "assign", "AccessibilityInfo", "announceForAccessibility", "mockAnnounce", "stringContaining", "<PERSON><PERSON><PERSON><PERSON>", "accessibilityRole", "accessibilityLabel"], "sources": ["ErrorBoundary.test.tsx"], "sourcesContent": ["/**\n * ErrorBoundary Component Tests\n *\n * Test Coverage:\n * - Error catching and handling\n * - Fallback UI rendering\n * - Reset functionality\n * - Retry mechanism\n * - Integration with performance monitoring\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport { render, fireEvent, waitFor } from '@testing-library/react-native';\nimport React from 'react';\nimport { Text, View, Button } from 'react-native';\n\nimport { performanceMonitor } from '../../../services/performanceMonitor';\nimport { ErrorBoundary } from '../ErrorBoundary';\n\n// Mock dependencies\njest.mock('../../../services/performanceMonitor');\n\nconst mockPerformanceMonitor = performanceMonitor as jest.Mocked<\n  typeof performanceMonitor\n>;\n\n// Test components\nconst ErrorComponent: React.FC = () => {\n  throw new Error('Test error');\n  return null;\n};\n\nconst ButtonThatThrows: React.FC<{ onError?: () => void }> = ({ onError }) => {\n  const [shouldThrow, setShouldThrow] = React.useState(false);\n\n  if (shouldThrow) {\n    try {\n      throw new Error('Button error');\n    } catch (error) {\n      if (onError) onError();\n      throw error;\n    }\n  }\n\n  return (\n    <Button\n      title=\"Throw Error\"\n      onPress={() => setShouldThrow(true)}\n      testID=\"throw-button\"\n    />\n  );\n};\n\nconst WorkingComponent: React.FC = () => (\n  <View testID=\"working-component\">\n    <Text>Working Component</Text>\n  </View>\n);\n\ndescribe('ErrorBoundary', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n\n    // Suppress React error boundary warnings in tests\n    jest.spyOn(console, 'error').mockImplementation(() => {});\n  });\n\n  afterEach(() => {\n    jest.restoreAllMocks();\n  });\n\n  describe('Error Handling', () => {\n    it('catches errors in child components', () => {\n      const { getByText } = render(\n        <ErrorBoundary>\n          <ErrorComponent />\n        </ErrorBoundary>,\n      );\n\n      // Should render fallback UI instead of crashing\n      expect(getByText('Something went wrong')).toBeTruthy();\n    });\n\n    it('renders children normally when no errors occur', () => {\n      const { getByTestId, getByText } = render(\n        <ErrorBoundary>\n          <WorkingComponent />\n        </ErrorBoundary>,\n      );\n\n      expect(getByTestId('working-component')).toBeTruthy();\n      expect(getByText('Working Component')).toBeTruthy();\n    });\n\n    it('calls onError callback when an error occurs', () => {\n      const onError = jest.fn();\n\n      render(\n        <ErrorBoundary onError={onError}>\n          <ErrorComponent />\n        </ErrorBoundary>,\n      );\n\n      expect(onError).toHaveBeenCalledWith(\n        expect.objectContaining({\n          message: 'Test error',\n        }),\n      );\n    });\n\n    it('tracks errors with performance monitor', () => {\n      render(\n        <ErrorBoundary>\n          <ErrorComponent />\n        </ErrorBoundary>,\n      );\n\n      expect(mockPerformanceMonitor.trackUserInteraction).toHaveBeenCalledWith(\n        'error_boundary_catch',\n        0,\n        expect.objectContaining({\n          error: 'Test error',\n          componentStack: expect.any(String),\n        }),\n      );\n    });\n  });\n\n  describe('Fallback UI', () => {\n    it('renders default fallback UI', () => {\n      const { getByText } = render(\n        <ErrorBoundary>\n          <ErrorComponent />\n        </ErrorBoundary>,\n      );\n\n      expect(getByText('Something went wrong')).toBeTruthy();\n      expect(getByText(/We're sorry, but an error occurred/)).toBeTruthy();\n      expect(getByText('Try Again')).toBeTruthy();\n    });\n\n    it('renders custom fallback UI when provided', () => {\n      const CustomFallback = ({\n        error,\n        retry,\n      }: {\n        error: Error;\n        retry: () => void;\n      }) => (\n        <View testID=\"custom-fallback\">\n          <Text>Custom Error: {error.message}</Text>\n          <Button title=\"Custom Retry\" onPress={retry} testID=\"custom-retry\" />\n        </View>\n      );\n\n      const { getByTestId, getByText } = render(\n        <ErrorBoundary\n          fallback={(error, retry) => (\n            <CustomFallback error={error} retry={retry} />\n          )}>\n          <ErrorComponent />\n        </ErrorBoundary>,\n      );\n\n      expect(getByTestId('custom-fallback')).toBeTruthy();\n      expect(getByText('Custom Error: Test error')).toBeTruthy();\n      expect(getByText('Custom Retry')).toBeTruthy();\n    });\n\n    it('shows technical details in development mode', () => {\n      // Mock __DEV__ to be true\n      global.__DEV__ = true;\n\n      const { getByText } = render(\n        <ErrorBoundary>\n          <ErrorComponent />\n        </ErrorBoundary>,\n      );\n\n      expect(getByText(/Technical Details/)).toBeTruthy();\n      expect(getByText('Test error')).toBeTruthy();\n\n      // Reset __DEV__\n      global.__DEV__ = false;\n    });\n  });\n\n  describe('Reset Functionality', () => {\n    it('resets error state when retry button is pressed', async () => {\n      const TestComponent = ({ resetKey }: { resetKey: number }) => {\n        if (resetKey === 1) {\n          throw new Error('Initial error');\n        }\n        return <Text>Recovered Component</Text>;\n      };\n\n      const { getByText, rerender } = render(\n        <ErrorBoundary resetKeys={[1]} resetOnPropsChange={true}>\n          <TestComponent resetKey={1} />\n        </ErrorBoundary>,\n      );\n\n      // Initially shows error\n      expect(getByText('Something went wrong')).toBeTruthy();\n\n      // Press retry button\n      fireEvent.press(getByText('Try Again'));\n\n      // Change resetKey to trigger reset\n      rerender(\n        <ErrorBoundary resetKeys={[2]} resetOnPropsChange={true}>\n          <TestComponent resetKey={2} />\n        </ErrorBoundary>,\n      );\n\n      // Should recover and render component\n      await waitFor(() => {\n        expect(getByText('Recovered Component')).toBeTruthy();\n      });\n    });\n\n    it('respects maxRetries limit', async () => {\n      const maxRetries = 2;\n\n      render(\n        <ErrorBoundary maxRetries={maxRetries}>\n          <ErrorComponent />\n        </ErrorBoundary>,\n      );\n\n      // Press retry button multiple times\n      for (let i = 0; i < maxRetries; i++) {\n        fireEvent.press(screen.getByText('Try Again'));\n      }\n\n      // After max retries, button should be disabled\n      const retryButton = screen.getByText('Try Again');\n      expect(retryButton.props.disabled).toBe(true);\n    });\n\n    it('resets on props change when resetOnPropsChange is true', async () => {\n      const TestWrapper = ({ children }: { children: React.ReactNode }) => {\n        const [key, setKey] = React.useState(1);\n\n        return (\n          <View>\n            <ErrorBoundary resetOnPropsChange>{children}</ErrorBoundary>\n            <Button\n              title=\"Change Props\"\n              onPress={() => setKey(k => k + 1)}\n              testID=\"change-props\"\n            />\n          </View>\n        );\n      };\n\n      const { rerender } = render(\n        <TestWrapper>\n          <ErrorComponent />\n        </TestWrapper>,\n      );\n\n      // Initially shows error\n      expect(screen.getByText('Something went wrong')).toBeTruthy();\n\n      // Change props\n      fireEvent.press(screen.getByTestID('change-props'));\n\n      // Should attempt to re-render, but still error\n      expect(screen.getByText('Something went wrong')).toBeTruthy();\n    });\n  });\n\n  describe('Dynamic Error Handling', () => {\n    it('catches runtime errors from user interactions', async () => {\n      const onError = jest.fn();\n\n      render(\n        <ErrorBoundary onError={onError}>\n          <ButtonThatThrows onError={onError} />\n        </ErrorBoundary>,\n      );\n\n      // Initially renders without error\n      expect(screen.getByTestID('throw-button')).toBeTruthy();\n\n      // Trigger error\n      fireEvent.press(screen.getByTestID('throw-button'));\n\n      // Should show error boundary fallback\n      await waitFor(() => {\n        expect(screen.getByText('Something went wrong')).toBeTruthy();\n      });\n\n      expect(onError).toHaveBeenCalledWith(\n        expect.objectContaining({\n          message: 'Button error',\n        }),\n      );\n    });\n\n    it('handles nested error boundaries correctly', () => {\n      const OuterFallback = () => <Text>Outer Error</Text>;\n      const InnerFallback = () => <Text>Inner Error</Text>;\n\n      render(\n        <ErrorBoundary fallback={() => <OuterFallback />}>\n          <View>\n            <Text>Outer Content</Text>\n            <ErrorBoundary fallback={() => <InnerFallback />}>\n              <ErrorComponent />\n            </ErrorBoundary>\n          </View>\n        </ErrorBoundary>,\n      );\n\n      // Inner error boundary should catch the error\n      expect(screen.getByText('Inner Error')).toBeTruthy();\n      expect(screen.getByText('Outer Content')).toBeTruthy();\n      expect(screen.queryByText('Outer Error')).toBeNull();\n    });\n  });\n\n  describe('Accessibility', () => {\n    it('announces errors to screen readers', () => {\n      const mockAnnounce = jest.fn();\n\n      // Mock AccessibilityInfo\n      jest.mock('react-native', () => {\n        const rn = jest.requireActual('react-native');\n        return {\n          ...rn,\n          AccessibilityInfo: {\n            ...rn.AccessibilityInfo,\n            announceForAccessibility: mockAnnounce,\n          },\n        };\n      });\n\n      render(\n        <ErrorBoundary>\n          <ErrorComponent />\n        </ErrorBoundary>,\n      );\n\n      expect(mockAnnounce).toHaveBeenCalledWith(\n        expect.stringContaining('error occurred'),\n      );\n    });\n\n    it('provides proper accessibility props on fallback UI', () => {\n      render(\n        <ErrorBoundary>\n          <ErrorComponent />\n        </ErrorBoundary>,\n      );\n\n      const errorContainer = screen.getByTestID('error-boundary-container');\n      expect(errorContainer.props.accessibilityRole).toBe('alert');\n\n      const retryButton = screen.getByText('Try Again');\n      expect(retryButton.props.accessibilityRole).toBe('button');\n      expect(retryButton.props.accessibilityLabel).toBeTruthy();\n    });\n  });\n});\n"], "mappings": "AAsBAA,WAAA,GAAKC,IAAI,uCAAuC,CAAC;AAAC,IAAAC,sBAAA,GAAAC,OAAA;AAAA,IAAAC,kBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAA,IAAAE,eAAA,GAAAH,sBAAA,CAAAC,OAAA;AARlD,IAAAG,YAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,aAAA,GAAAL,OAAA;AAEA,IAAAM,mBAAA,GAAAN,OAAA;AACA,IAAAO,cAAA,GAAAP,OAAA;AAAiD,IAAAQ,WAAA,GAAAR,OAAA;AAAA,SAAAH,YAAA;EAAA,IAAAY,QAAA,GAAAT,OAAA;IAAAU,IAAA,GAAAD,QAAA,CAAAC,IAAA;EAAAb,WAAA,YAAAA,YAAA;IAAA,OAAAa,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAKjD,IAAMC,sBAAsB,GAAGC,sCAE9B;AAGD,IAAMC,cAAwB,GAAG,SAA3BA,cAAwBA,CAAA,EAAS;EACrC,MAAM,IAAIC,KAAK,CAAC,YAAY,CAAC;EAC7B,OAAO,IAAI;AACb,CAAC;AAED,IAAMC,gBAAoD,GAAG,SAAvDA,gBAAoDA,CAAAC,IAAA,EAAoB;EAAA,IAAdC,OAAO,GAAAD,IAAA,CAAPC,OAAO;EACrE,IAAAC,eAAA,GAAsCC,cAAK,CAACC,QAAQ,CAAC,KAAK,CAAC;IAAAC,gBAAA,OAAAnB,eAAA,CAAAoB,OAAA,EAAAJ,eAAA;IAApDK,WAAW,GAAAF,gBAAA;IAAEG,cAAc,GAAAH,gBAAA;EAElC,IAAIE,WAAW,EAAE;IACf,IAAI;MACF,MAAM,IAAIT,KAAK,CAAC,cAAc,CAAC;IACjC,CAAC,CAAC,OAAOW,KAAK,EAAE;MACd,IAAIR,OAAO,EAAEA,OAAO,CAAC,CAAC;MACtB,MAAMQ,KAAK;IACb;EACF;EAEA,OACE,IAAAjB,WAAA,CAAAkB,GAAA,EAACrB,aAAA,CAAAsB,MAAM;IACLC,KAAK,EAAC,aAAa;IACnBC,OAAO,EAAE,SAATA,OAAOA,CAAA;MAAA,OAAQL,cAAc,CAAC,IAAI,CAAC;IAAA,CAAC;IACpCM,MAAM,EAAC;EAAc,CACtB,CAAC;AAEN,CAAC;AAED,IAAMC,gBAA0B,GAAG,SAA7BA,gBAA0BA,CAAA;EAAA,OAC9B,IAAAvB,WAAA,CAAAkB,GAAA,EAACrB,aAAA,CAAA2B,IAAI;IAACF,MAAM,EAAC,mBAAmB;IAAAG,QAAA,EAC9B,IAAAzB,WAAA,CAAAkB,GAAA,EAACrB,aAAA,CAAA6B,IAAI;MAAAD,QAAA,EAAC;IAAiB,CAAM;EAAC,CAC1B,CAAC;AAAA,CACR;AAEDE,QAAQ,CAAC,eAAe,EAAE,YAAM;EAC9BC,UAAU,CAAC,YAAM;IACf1B,IAAI,CAAC2B,aAAa,CAAC,CAAC;IAGpB3B,IAAI,CAAC4B,KAAK,CAACC,OAAO,EAAE,OAAO,CAAC,CAACC,kBAAkB,CAAC,YAAM,CAAC,CAAC,CAAC;EAC3D,CAAC,CAAC;EAEFC,SAAS,CAAC,YAAM;IACd/B,IAAI,CAACgC,eAAe,CAAC,CAAC;EACxB,CAAC,CAAC;EAEFP,QAAQ,CAAC,gBAAgB,EAAE,YAAM;IAC/BQ,EAAE,CAAC,oCAAoC,EAAE,YAAM;MAC7C,IAAAC,OAAA,GAAsB,IAAAC,mBAAM,EAC1B,IAAArC,WAAA,CAAAkB,GAAA,EAACnB,cAAA,CAAAuC,aAAa;UAAAb,QAAA,EACZ,IAAAzB,WAAA,CAAAkB,GAAA,EAACb,cAAc,IAAE;QAAC,CACL,CACjB,CAAC;QAJOkC,SAAS,GAAAH,OAAA,CAATG,SAAS;MAOjBC,MAAM,CAACD,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC;IACxD,CAAC,CAAC;IAEFN,EAAE,CAAC,gDAAgD,EAAE,YAAM;MACzD,IAAAO,QAAA,GAAmC,IAAAL,mBAAM,EACvC,IAAArC,WAAA,CAAAkB,GAAA,EAACnB,cAAA,CAAAuC,aAAa;UAAAb,QAAA,EACZ,IAAAzB,WAAA,CAAAkB,GAAA,EAACK,gBAAgB,IAAE;QAAC,CACP,CACjB,CAAC;QAJOoB,WAAW,GAAAD,QAAA,CAAXC,WAAW;QAAEJ,SAAS,GAAAG,QAAA,CAATH,SAAS;MAM9BC,MAAM,CAACG,WAAW,CAAC,mBAAmB,CAAC,CAAC,CAACF,UAAU,CAAC,CAAC;MACrDD,MAAM,CAACD,SAAS,CAAC,mBAAmB,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC;IACrD,CAAC,CAAC;IAEFN,EAAE,CAAC,6CAA6C,EAAE,YAAM;MACtD,IAAM1B,OAAO,GAAGP,IAAI,CAAC0C,EAAE,CAAC,CAAC;MAEzB,IAAAP,mBAAM,EACJ,IAAArC,WAAA,CAAAkB,GAAA,EAACnB,cAAA,CAAAuC,aAAa;QAAC7B,OAAO,EAAEA,OAAQ;QAAAgB,QAAA,EAC9B,IAAAzB,WAAA,CAAAkB,GAAA,EAACb,cAAc,IAAE;MAAC,CACL,CACjB,CAAC;MAEDmC,MAAM,CAAC/B,OAAO,CAAC,CAACoC,oBAAoB,CAClCL,MAAM,CAACM,gBAAgB,CAAC;QACtBC,OAAO,EAAE;MACX,CAAC,CACH,CAAC;IACH,CAAC,CAAC;IAEFZ,EAAE,CAAC,wCAAwC,EAAE,YAAM;MACjD,IAAAE,mBAAM,EACJ,IAAArC,WAAA,CAAAkB,GAAA,EAACnB,cAAA,CAAAuC,aAAa;QAAAb,QAAA,EACZ,IAAAzB,WAAA,CAAAkB,GAAA,EAACb,cAAc,IAAE;MAAC,CACL,CACjB,CAAC;MAEDmC,MAAM,CAACrC,sBAAsB,CAAC6C,oBAAoB,CAAC,CAACH,oBAAoB,CACtE,sBAAsB,EACtB,CAAC,EACDL,MAAM,CAACM,gBAAgB,CAAC;QACtB7B,KAAK,EAAE,YAAY;QACnBgC,cAAc,EAAET,MAAM,CAACU,GAAG,CAACC,MAAM;MACnC,CAAC,CACH,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFxB,QAAQ,CAAC,aAAa,EAAE,YAAM;IAC5BQ,EAAE,CAAC,6BAA6B,EAAE,YAAM;MACtC,IAAAiB,QAAA,GAAsB,IAAAf,mBAAM,EAC1B,IAAArC,WAAA,CAAAkB,GAAA,EAACnB,cAAA,CAAAuC,aAAa;UAAAb,QAAA,EACZ,IAAAzB,WAAA,CAAAkB,GAAA,EAACb,cAAc,IAAE;QAAC,CACL,CACjB,CAAC;QAJOkC,SAAS,GAAAa,QAAA,CAATb,SAAS;MAMjBC,MAAM,CAACD,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC;MACtDD,MAAM,CAACD,SAAS,CAAC,oCAAoC,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC;MACpED,MAAM,CAACD,SAAS,CAAC,WAAW,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC;IAC7C,CAAC,CAAC;IAEFN,EAAE,CAAC,0CAA0C,EAAE,YAAM;MACnD,IAAMkB,cAAc,GAAG,SAAjBA,cAAcA,CAAAC,KAAA;QAAA,IAClBrC,KAAK,GAAAqC,KAAA,CAALrC,KAAK;UACLsC,KAAK,GAAAD,KAAA,CAALC,KAAK;QAAA,OAKL,IAAAvD,WAAA,CAAAwD,IAAA,EAAC3D,aAAA,CAAA2B,IAAI;UAACF,MAAM,EAAC,iBAAiB;UAAAG,QAAA,GAC5B,IAAAzB,WAAA,CAAAwD,IAAA,EAAC3D,aAAA,CAAA6B,IAAI;YAAAD,QAAA,GAAC,gBAAc,EAACR,KAAK,CAAC8B,OAAO;UAAA,CAAO,CAAC,EAC1C,IAAA/C,WAAA,CAAAkB,GAAA,EAACrB,aAAA,CAAAsB,MAAM;YAACC,KAAK,EAAC,cAAc;YAACC,OAAO,EAAEkC,KAAM;YAACjC,MAAM,EAAC;UAAc,CAAE,CAAC;QAAA,CACjE,CAAC;MAAA,CACR;MAED,IAAAmC,QAAA,GAAmC,IAAApB,mBAAM,EACvC,IAAArC,WAAA,CAAAkB,GAAA,EAACnB,cAAA,CAAAuC,aAAa;UACZoB,QAAQ,EAAE,SAAVA,QAAQA,CAAGzC,KAAK,EAAEsC,KAAK;YAAA,OACrB,IAAAvD,WAAA,CAAAkB,GAAA,EAACmC,cAAc;cAACpC,KAAK,EAAEA,KAAM;cAACsC,KAAK,EAAEA;YAAM,CAAE,CAAC;UAAA,CAC9C;UAAA9B,QAAA,EACF,IAAAzB,WAAA,CAAAkB,GAAA,EAACb,cAAc,IAAE;QAAC,CACL,CACjB,CAAC;QAPOsC,WAAW,GAAAc,QAAA,CAAXd,WAAW;QAAEJ,SAAS,GAAAkB,QAAA,CAATlB,SAAS;MAS9BC,MAAM,CAACG,WAAW,CAAC,iBAAiB,CAAC,CAAC,CAACF,UAAU,CAAC,CAAC;MACnDD,MAAM,CAACD,SAAS,CAAC,0BAA0B,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC;MAC1DD,MAAM,CAACD,SAAS,CAAC,cAAc,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC;IAChD,CAAC,CAAC;IAEFN,EAAE,CAAC,6CAA6C,EAAE,YAAM;MAEtDwB,MAAM,CAACC,OAAO,GAAG,IAAI;MAErB,IAAAC,QAAA,GAAsB,IAAAxB,mBAAM,EAC1B,IAAArC,WAAA,CAAAkB,GAAA,EAACnB,cAAA,CAAAuC,aAAa;UAAAb,QAAA,EACZ,IAAAzB,WAAA,CAAAkB,GAAA,EAACb,cAAc,IAAE;QAAC,CACL,CACjB,CAAC;QAJOkC,SAAS,GAAAsB,QAAA,CAATtB,SAAS;MAMjBC,MAAM,CAACD,SAAS,CAAC,mBAAmB,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC;MACnDD,MAAM,CAACD,SAAS,CAAC,YAAY,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC;MAG5CkB,MAAM,CAACC,OAAO,GAAG,KAAK;IACxB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjC,QAAQ,CAAC,qBAAqB,EAAE,YAAM;IACpCQ,EAAE,CAAC,iDAAiD,MAAA1C,kBAAA,CAAAqB,OAAA,EAAE,aAAY;MAChE,IAAMgD,aAAa,GAAG,SAAhBA,aAAaA,CAAAC,KAAA,EAA2C;QAAA,IAArCC,QAAQ,GAAAD,KAAA,CAARC,QAAQ;QAC/B,IAAIA,QAAQ,KAAK,CAAC,EAAE;UAClB,MAAM,IAAI1D,KAAK,CAAC,eAAe,CAAC;QAClC;QACA,OAAO,IAAAN,WAAA,CAAAkB,GAAA,EAACrB,aAAA,CAAA6B,IAAI;UAAAD,QAAA,EAAC;QAAmB,CAAM,CAAC;MACzC,CAAC;MAED,IAAAwC,QAAA,GAAgC,IAAA5B,mBAAM,EACpC,IAAArC,WAAA,CAAAkB,GAAA,EAACnB,cAAA,CAAAuC,aAAa;UAAC4B,SAAS,EAAE,CAAC,CAAC,CAAE;UAACC,kBAAkB,EAAE,IAAK;UAAA1C,QAAA,EACtD,IAAAzB,WAAA,CAAAkB,GAAA,EAAC4C,aAAa;YAACE,QAAQ,EAAE;UAAE,CAAE;QAAC,CACjB,CACjB,CAAC;QAJOzB,SAAS,GAAA0B,QAAA,CAAT1B,SAAS;QAAE6B,QAAQ,GAAAH,QAAA,CAARG,QAAQ;MAO3B5B,MAAM,CAACD,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC;MAGtD4B,sBAAS,CAACC,KAAK,CAAC/B,SAAS,CAAC,WAAW,CAAC,CAAC;MAGvC6B,QAAQ,CACN,IAAApE,WAAA,CAAAkB,GAAA,EAACnB,cAAA,CAAAuC,aAAa;QAAC4B,SAAS,EAAE,CAAC,CAAC,CAAE;QAACC,kBAAkB,EAAE,IAAK;QAAA1C,QAAA,EACtD,IAAAzB,WAAA,CAAAkB,GAAA,EAAC4C,aAAa;UAACE,QAAQ,EAAE;QAAE,CAAE;MAAC,CACjB,CACjB,CAAC;MAGD,MAAM,IAAAO,oBAAO,EAAC,YAAM;QAClB/B,MAAM,CAACD,SAAS,CAAC,qBAAqB,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC;MACvD,CAAC,CAAC;IACJ,CAAC,EAAC;IAEFN,EAAE,CAAC,2BAA2B,MAAA1C,kBAAA,CAAAqB,OAAA,EAAE,aAAY;MAC1C,IAAM0D,UAAU,GAAG,CAAC;MAEpB,IAAAnC,mBAAM,EACJ,IAAArC,WAAA,CAAAkB,GAAA,EAACnB,cAAA,CAAAuC,aAAa;QAACkC,UAAU,EAAEA,UAAW;QAAA/C,QAAA,EACpC,IAAAzB,WAAA,CAAAkB,GAAA,EAACb,cAAc,IAAE;MAAC,CACL,CACjB,CAAC;MAGD,KAAK,IAAIoE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,EAAEC,CAAC,EAAE,EAAE;QACnCJ,sBAAS,CAACC,KAAK,CAACI,MAAM,CAACnC,SAAS,CAAC,WAAW,CAAC,CAAC;MAChD;MAGA,IAAMoC,WAAW,GAAGD,MAAM,CAACnC,SAAS,CAAC,WAAW,CAAC;MACjDC,MAAM,CAACmC,WAAW,CAACC,KAAK,CAACC,QAAQ,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IAC/C,CAAC,EAAC;IAEF3C,EAAE,CAAC,wDAAwD,MAAA1C,kBAAA,CAAAqB,OAAA,EAAE,aAAY;MACvE,IAAMiE,WAAW,GAAG,SAAdA,WAAWA,CAAAC,KAAA,EAAoD;QAAA,IAA9CvD,QAAQ,GAAAuD,KAAA,CAARvD,QAAQ;QAC7B,IAAAwD,gBAAA,GAAsBtE,cAAK,CAACC,QAAQ,CAAC,CAAC,CAAC;UAAAsE,gBAAA,OAAAxF,eAAA,CAAAoB,OAAA,EAAAmE,gBAAA;UAAhCE,GAAG,GAAAD,gBAAA;UAAEE,MAAM,GAAAF,gBAAA;QAElB,OACE,IAAAlF,WAAA,CAAAwD,IAAA,EAAC3D,aAAA,CAAA2B,IAAI;UAAAC,QAAA,GACH,IAAAzB,WAAA,CAAAkB,GAAA,EAACnB,cAAA,CAAAuC,aAAa;YAAC6B,kBAAkB;YAAA1C,QAAA,EAAEA;UAAQ,CAAgB,CAAC,EAC5D,IAAAzB,WAAA,CAAAkB,GAAA,EAACrB,aAAA,CAAAsB,MAAM;YACLC,KAAK,EAAC,cAAc;YACpBC,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQ+D,MAAM,CAAC,UAAAC,CAAC;gBAAA,OAAIA,CAAC,GAAG,CAAC;cAAA,EAAC;YAAA,CAAC;YAClC/D,MAAM,EAAC;UAAc,CACtB,CAAC;QAAA,CACE,CAAC;MAEX,CAAC;MAED,IAAAgE,QAAA,GAAqB,IAAAjD,mBAAM,EACzB,IAAArC,WAAA,CAAAkB,GAAA,EAAC6D,WAAW;UAAAtD,QAAA,EACV,IAAAzB,WAAA,CAAAkB,GAAA,EAACb,cAAc,IAAE;QAAC,CACP,CACf,CAAC;QAJO+D,QAAQ,GAAAkB,QAAA,CAARlB,QAAQ;MAOhB5B,MAAM,CAACkC,MAAM,CAACnC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC;MAG7D4B,sBAAS,CAACC,KAAK,CAACI,MAAM,CAACa,WAAW,CAAC,cAAc,CAAC,CAAC;MAGnD/C,MAAM,CAACkC,MAAM,CAACnC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC;IAC/D,CAAC,EAAC;EACJ,CAAC,CAAC;EAEFd,QAAQ,CAAC,wBAAwB,EAAE,YAAM;IACvCQ,EAAE,CAAC,+CAA+C,MAAA1C,kBAAA,CAAAqB,OAAA,EAAE,aAAY;MAC9D,IAAML,OAAO,GAAGP,IAAI,CAAC0C,EAAE,CAAC,CAAC;MAEzB,IAAAP,mBAAM,EACJ,IAAArC,WAAA,CAAAkB,GAAA,EAACnB,cAAA,CAAAuC,aAAa;QAAC7B,OAAO,EAAEA,OAAQ;QAAAgB,QAAA,EAC9B,IAAAzB,WAAA,CAAAkB,GAAA,EAACX,gBAAgB;UAACE,OAAO,EAAEA;QAAQ,CAAE;MAAC,CACzB,CACjB,CAAC;MAGD+B,MAAM,CAACkC,MAAM,CAACa,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC9C,UAAU,CAAC,CAAC;MAGvD4B,sBAAS,CAACC,KAAK,CAACI,MAAM,CAACa,WAAW,CAAC,cAAc,CAAC,CAAC;MAGnD,MAAM,IAAAhB,oBAAO,EAAC,YAAM;QAClB/B,MAAM,CAACkC,MAAM,CAACnC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC;MAC/D,CAAC,CAAC;MAEFD,MAAM,CAAC/B,OAAO,CAAC,CAACoC,oBAAoB,CAClCL,MAAM,CAACM,gBAAgB,CAAC;QACtBC,OAAO,EAAE;MACX,CAAC,CACH,CAAC;IACH,CAAC,EAAC;IAEFZ,EAAE,CAAC,2CAA2C,EAAE,YAAM;MACpD,IAAMqD,aAAa,GAAG,SAAhBA,aAAaA,CAAA;QAAA,OAAS,IAAAxF,WAAA,CAAAkB,GAAA,EAACrB,aAAA,CAAA6B,IAAI;UAAAD,QAAA,EAAC;QAAW,CAAM,CAAC;MAAA;MACpD,IAAMgE,aAAa,GAAG,SAAhBA,aAAaA,CAAA;QAAA,OAAS,IAAAzF,WAAA,CAAAkB,GAAA,EAACrB,aAAA,CAAA6B,IAAI;UAAAD,QAAA,EAAC;QAAW,CAAM,CAAC;MAAA;MAEpD,IAAAY,mBAAM,EACJ,IAAArC,WAAA,CAAAkB,GAAA,EAACnB,cAAA,CAAAuC,aAAa;QAACoB,QAAQ,EAAE,SAAVA,QAAQA,CAAA;UAAA,OAAQ,IAAA1D,WAAA,CAAAkB,GAAA,EAACsE,aAAa,IAAE,CAAC;QAAA,CAAC;QAAA/D,QAAA,EAC/C,IAAAzB,WAAA,CAAAwD,IAAA,EAAC3D,aAAA,CAAA2B,IAAI;UAAAC,QAAA,GACH,IAAAzB,WAAA,CAAAkB,GAAA,EAACrB,aAAA,CAAA6B,IAAI;YAAAD,QAAA,EAAC;UAAa,CAAM,CAAC,EAC1B,IAAAzB,WAAA,CAAAkB,GAAA,EAACnB,cAAA,CAAAuC,aAAa;YAACoB,QAAQ,EAAE,SAAVA,QAAQA,CAAA;cAAA,OAAQ,IAAA1D,WAAA,CAAAkB,GAAA,EAACuE,aAAa,IAAE,CAAC;YAAA,CAAC;YAAAhE,QAAA,EAC/C,IAAAzB,WAAA,CAAAkB,GAAA,EAACb,cAAc,IAAE;UAAC,CACL,CAAC;QAAA,CACZ;MAAC,CACM,CACjB,CAAC;MAGDmC,MAAM,CAACkC,MAAM,CAACnC,SAAS,CAAC,aAAa,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC;MACpDD,MAAM,CAACkC,MAAM,CAACnC,SAAS,CAAC,eAAe,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC;MACtDD,MAAM,CAACkC,MAAM,CAACgB,WAAW,CAAC,aAAa,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;IACtD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFhE,QAAQ,CAAC,eAAe,EAAE,YAAM;IAC9BQ,EAAE,CAAC,oCAAoC,EAAE,YAAM;MAI7C9C,WAAA,GAAKC,IAAI,CAAC,cAAc,EAAE,YAAM;QAC9B,IAAMsG,EAAE,GAAG1F,IAAI,CAAC2F,aAAa,CAAC,cAAc,CAAC;QAC7C,OAAAC,MAAA,CAAAC,MAAA,KACKH,EAAE;UACLI,iBAAiB,EAAAF,MAAA,CAAAC,MAAA,KACZH,EAAE,CAACI,iBAAiB;YACvBC,wBAAwB,EAAEC;UAAY;QACvC;MAEL,CAAC,CAAC;MAZF,IAAMA,YAAY,GAAGhG,IAAI,CAAC0C,EAAE,CAAC,CAAC;MAc9B,IAAAP,mBAAM,EACJ,IAAArC,WAAA,CAAAkB,GAAA,EAACnB,cAAA,CAAAuC,aAAa;QAAAb,QAAA,EACZ,IAAAzB,WAAA,CAAAkB,GAAA,EAACb,cAAc,IAAE;MAAC,CACL,CACjB,CAAC;MAEDmC,MAAM,CAAC0D,YAAY,CAAC,CAACrD,oBAAoB,CACvCL,MAAM,CAAC2D,gBAAgB,CAAC,gBAAgB,CAC1C,CAAC;IACH,CAAC,CAAC;IAEFhE,EAAE,CAAC,oDAAoD,EAAE,YAAM;MAC7D,IAAAE,mBAAM,EACJ,IAAArC,WAAA,CAAAkB,GAAA,EAACnB,cAAA,CAAAuC,aAAa;QAAAb,QAAA,EACZ,IAAAzB,WAAA,CAAAkB,GAAA,EAACb,cAAc,IAAE;MAAC,CACL,CACjB,CAAC;MAED,IAAM+F,cAAc,GAAG1B,MAAM,CAACa,WAAW,CAAC,0BAA0B,CAAC;MACrE/C,MAAM,CAAC4D,cAAc,CAACxB,KAAK,CAACyB,iBAAiB,CAAC,CAACvB,IAAI,CAAC,OAAO,CAAC;MAE5D,IAAMH,WAAW,GAAGD,MAAM,CAACnC,SAAS,CAAC,WAAW,CAAC;MACjDC,MAAM,CAACmC,WAAW,CAACC,KAAK,CAACyB,iBAAiB,CAAC,CAACvB,IAAI,CAAC,QAAQ,CAAC;MAC1DtC,MAAM,CAACmC,WAAW,CAACC,KAAK,CAAC0B,kBAAkB,CAAC,CAAC7D,UAAU,CAAC,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}