58664aa48537e5ad8c9f73e753eae6ac
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var ReactNativeFeatureFlags = _interopRequireWildcard(require("../../../src/private/featureflags/ReactNativeFeatureFlags"));
var _usePressability2 = _interopRequireDefault(require("../../Pressability/usePressability"));
var _flattenStyle = _interopRequireDefault(require("../../StyleSheet/flattenStyle"));
var _StyleSheet = _interopRequireDefault(require("../../StyleSheet/StyleSheet"));
var _Text = _interopRequireDefault(require("../../Text/Text"));
var _TextAncestor = _interopRequireDefault(require("../../Text/TextAncestor"));
var _Platform = _interopRequireDefault(require("../../Utilities/Platform"));
var _useMergeRefs = _interopRequireDefault(require("../../Utilities/useMergeRefs"));
var _TextInputState = _interopRequireDefault(require("./TextInputState"));
var _invariant = _interopRequireDefault(require("invariant"));
var _nullthrows = _interopRequireDefault(require("nullthrows"));
var _react = _interopRequireWildcard(require("react"));
var React = _react;
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["aria-busy", "aria-checked", "aria-disabled", "aria-expanded", "aria-selected", "accessibilityState", "id", "tabIndex", "selection", "selectionColor", "selectionHandleColor", "cursorColor"],
  _excluded2 = ["onBlur", "onFocus"],
  _excluded3 = ["allowFontScaling", "rejectResponderTermination", "underlineColorAndroid", "autoComplete", "textContentType", "readOnly", "editable", "enterKeyHint", "returnKeyType", "inputMode", "showSoftInputOnFocus", "keyboardType"];
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var AndroidTextInput;
var AndroidTextInputCommands;
var RCTSinglelineTextInputView;
var RCTSinglelineTextInputNativeCommands;
var RCTMultilineTextInputView;
var RCTMultilineTextInputNativeCommands;
if (_Platform.default.OS === 'android') {
  AndroidTextInput = require("./AndroidTextInputNativeComponent").default;
  AndroidTextInputCommands = require("./AndroidTextInputNativeComponent").Commands;
} else if (_Platform.default.OS === 'ios') {
  RCTSinglelineTextInputView = require("./RCTSingelineTextInputNativeComponent").default;
  RCTSinglelineTextInputNativeCommands = require("./RCTSingelineTextInputNativeComponent").Commands;
  RCTMultilineTextInputView = require("./RCTMultilineTextInputNativeComponent").default;
  RCTMultilineTextInputNativeCommands = require("./RCTMultilineTextInputNativeComponent").Commands;
}
var emptyFunctionThatReturnsTrue = function emptyFunctionThatReturnsTrue() {
  return true;
};
function useTextInputStateSynchronization_STATE(_ref) {
  var props = _ref.props,
    mostRecentEventCount = _ref.mostRecentEventCount,
    selection = _ref.selection,
    inputRef = _ref.inputRef,
    text = _ref.text,
    viewCommands = _ref.viewCommands;
  var _useState = (0, _react.useState)(props.value),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    lastNativeText = _useState2[0],
    setLastNativeText = _useState2[1];
  var _useState3 = (0, _react.useState)({
      selection: {
        start: -1,
        end: -1
      },
      mostRecentEventCount: mostRecentEventCount
    }),
    _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
    lastNativeSelectionState = _useState4[0],
    setLastNativeSelection = _useState4[1];
  var lastNativeSelection = lastNativeSelectionState.selection;
  (0, _react.useLayoutEffect)(function () {
    var nativeUpdate = {};
    if (lastNativeText !== props.value && typeof props.value === 'string') {
      nativeUpdate.text = props.value;
      setLastNativeText(props.value);
    }
    if (selection && lastNativeSelection && (lastNativeSelection.start !== selection.start || lastNativeSelection.end !== selection.end)) {
      nativeUpdate.selection = selection;
      setLastNativeSelection({
        selection: selection,
        mostRecentEventCount: mostRecentEventCount
      });
    }
    if (Object.keys(nativeUpdate).length === 0) {
      return;
    }
    if (inputRef.current != null) {
      var _selection$start, _selection$end;
      viewCommands.setTextAndSelection(inputRef.current, mostRecentEventCount, text, (_selection$start = selection == null ? void 0 : selection.start) != null ? _selection$start : -1, (_selection$end = selection == null ? void 0 : selection.end) != null ? _selection$end : -1);
    }
  }, [mostRecentEventCount, inputRef, props.value, props.defaultValue, lastNativeText, selection, lastNativeSelection, text, viewCommands]);
  return {
    setLastNativeText: setLastNativeText,
    setLastNativeSelection: setLastNativeSelection
  };
}
function useTextInputStateSynchronization_REFS(_ref2) {
  var props = _ref2.props,
    mostRecentEventCount = _ref2.mostRecentEventCount,
    selection = _ref2.selection,
    inputRef = _ref2.inputRef,
    text = _ref2.text,
    viewCommands = _ref2.viewCommands;
  var lastNativeTextRef = (0, _react.useRef)(props.value);
  var lastNativeSelectionRef = (0, _react.useRef)({
    selection: {
      start: -1,
      end: -1
    },
    mostRecentEventCount: mostRecentEventCount
  });
  (0, _react.useLayoutEffect)(function () {
    var nativeUpdate = {};
    var lastNativeSelection = lastNativeSelectionRef.current.selection;
    if (lastNativeTextRef.current !== props.value && typeof props.value === 'string') {
      nativeUpdate.text = props.value;
      lastNativeTextRef.current = props.value;
    }
    if (selection && lastNativeSelection && (lastNativeSelection.start !== selection.start || lastNativeSelection.end !== selection.end)) {
      nativeUpdate.selection = selection;
      lastNativeSelectionRef.current = {
        selection: selection,
        mostRecentEventCount: mostRecentEventCount
      };
    }
    if (Object.keys(nativeUpdate).length === 0) {
      return;
    }
    if (inputRef.current != null) {
      var _selection$start2, _selection$end2;
      viewCommands.setTextAndSelection(inputRef.current, mostRecentEventCount, text, (_selection$start2 = selection == null ? void 0 : selection.start) != null ? _selection$start2 : -1, (_selection$end2 = selection == null ? void 0 : selection.end) != null ? _selection$end2 : -1);
    }
  }, [mostRecentEventCount, inputRef, props.value, props.defaultValue, selection, text, viewCommands]);
  return {
    setLastNativeText: function setLastNativeText(lastNativeText) {
      lastNativeTextRef.current = lastNativeText;
    },
    setLastNativeSelection: function setLastNativeSelection(lastNativeSelection) {
      lastNativeSelectionRef.current = lastNativeSelection;
    }
  };
}
function InternalTextInput(props) {
  var _propsSelection$end, _props$multiline;
  var ariaBusy = props['aria-busy'],
    ariaChecked = props['aria-checked'],
    ariaDisabled = props['aria-disabled'],
    ariaExpanded = props['aria-expanded'],
    ariaSelected = props['aria-selected'],
    accessibilityState = props.accessibilityState,
    id = props.id,
    tabIndex = props.tabIndex,
    propsSelection = props.selection,
    selectionColor = props.selectionColor,
    selectionHandleColor = props.selectionHandleColor,
    cursorColor = props.cursorColor,
    otherProps = (0, _objectWithoutProperties2.default)(props, _excluded);
  var inputRef = (0, _react.useRef)(null);
  var selection = propsSelection == null ? null : {
    start: propsSelection.start,
    end: (_propsSelection$end = propsSelection.end) != null ? _propsSelection$end : propsSelection.start
  };
  var text = typeof props.value === 'string' ? props.value : typeof props.defaultValue === 'string' ? props.defaultValue : undefined;
  var viewCommands = AndroidTextInputCommands || (props.multiline === true ? RCTMultilineTextInputNativeCommands : RCTSinglelineTextInputNativeCommands);
  var _useState5 = (0, _react.useState)(0),
    _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
    mostRecentEventCount = _useState6[0],
    setMostRecentEventCount = _useState6[1];
  var useTextInputStateSynchronization = ReactNativeFeatureFlags.useRefsForTextInputState() ? useTextInputStateSynchronization_REFS : useTextInputStateSynchronization_STATE;
  var _useTextInputStateSyn = useTextInputStateSynchronization({
      props: props,
      inputRef: inputRef,
      mostRecentEventCount: mostRecentEventCount,
      selection: selection,
      text: text,
      viewCommands: viewCommands
    }),
    setLastNativeText = _useTextInputStateSyn.setLastNativeText,
    setLastNativeSelection = _useTextInputStateSyn.setLastNativeSelection;
  (0, _react.useLayoutEffect)(function () {
    var inputRefValue = inputRef.current;
    if (inputRefValue != null) {
      _TextInputState.default.registerInput(inputRefValue);
      return function () {
        _TextInputState.default.unregisterInput(inputRefValue);
        if (_TextInputState.default.currentlyFocusedInput() === inputRefValue) {
          (0, _nullthrows.default)(inputRefValue).blur();
        }
      };
    }
  }, []);
  var setLocalRef = (0, _react.useCallback)(function (instance) {
    inputRef.current = instance;
    if (instance != null) {
      Object.assign(instance, {
        clear: function clear() {
          if (inputRef.current != null) {
            viewCommands.setTextAndSelection(inputRef.current, mostRecentEventCount, '', 0, 0);
          }
        },
        isFocused: function isFocused() {
          return _TextInputState.default.currentlyFocusedInput() === inputRef.current;
        },
        getNativeRef: function getNativeRef() {
          return inputRef.current;
        },
        setSelection: function setSelection(start, end) {
          if (inputRef.current != null) {
            viewCommands.setTextAndSelection(inputRef.current, mostRecentEventCount, null, start, end);
          }
        }
      });
    }
  }, [mostRecentEventCount, viewCommands]);
  var ref = (0, _useMergeRefs.default)(setLocalRef, props.forwardedRef);
  var _onChange = function _onChange(event) {
    var currentText = event.nativeEvent.text;
    props.onChange && props.onChange(event);
    props.onChangeText && props.onChangeText(currentText);
    if (inputRef.current == null) {
      return;
    }
    setLastNativeText(currentText);
    setMostRecentEventCount(event.nativeEvent.eventCount);
  };
  var _onSelectionChange = function _onSelectionChange(event) {
    props.onSelectionChange && props.onSelectionChange(event);
    if (inputRef.current == null) {
      return;
    }
    setLastNativeSelection({
      selection: event.nativeEvent.selection,
      mostRecentEventCount: mostRecentEventCount
    });
  };
  var _onFocus = function _onFocus(event) {
    _TextInputState.default.focusInput(inputRef.current);
    if (props.onFocus) {
      props.onFocus(event);
    }
  };
  var _onBlur = function _onBlur(event) {
    _TextInputState.default.blurInput(inputRef.current);
    if (props.onBlur) {
      props.onBlur(event);
    }
  };
  var _onScroll = function _onScroll(event) {
    props.onScroll && props.onScroll(event);
  };
  var textInput = null;
  var multiline = (_props$multiline = props.multiline) != null ? _props$multiline : false;
  var submitBehavior;
  if (props.submitBehavior != null) {
    if (!multiline && props.submitBehavior === 'newline') {
      submitBehavior = 'blurAndSubmit';
    } else {
      submitBehavior = props.submitBehavior;
    }
  } else if (multiline) {
    if (props.blurOnSubmit === true) {
      submitBehavior = 'blurAndSubmit';
    } else {
      submitBehavior = 'newline';
    }
  } else {
    if (props.blurOnSubmit !== false) {
      submitBehavior = 'blurAndSubmit';
    } else {
      submitBehavior = 'submit';
    }
  }
  var accessible = props.accessible !== false;
  var focusable = props.focusable !== false;
  var editable = props.editable,
    hitSlop = props.hitSlop,
    _onPress = props.onPress,
    onPressIn = props.onPressIn,
    onPressOut = props.onPressOut,
    rejectResponderTermination = props.rejectResponderTermination;
  var config = React.useMemo(function () {
    return {
      hitSlop: hitSlop,
      onPress: function onPress(event) {
        _onPress == null || _onPress(event);
        if (editable !== false) {
          if (inputRef.current != null) {
            inputRef.current.focus();
          }
        }
      },
      onPressIn: onPressIn,
      onPressOut: onPressOut,
      cancelable: _Platform.default.OS === 'ios' ? !rejectResponderTermination : null
    };
  }, [editable, hitSlop, _onPress, onPressIn, onPressOut, rejectResponderTermination]);
  var caretHidden = props.caretHidden;
  if (_Platform.default.isTesting) {
    caretHidden = true;
  }
  var _usePressability = (0, _usePressability2.default)(config),
    onBlur = _usePressability.onBlur,
    onFocus = _usePressability.onFocus,
    eventHandlers = (0, _objectWithoutProperties2.default)(_usePressability, _excluded2);
  var _accessibilityState;
  if (accessibilityState != null || ariaBusy != null || ariaChecked != null || ariaDisabled != null || ariaExpanded != null || ariaSelected != null) {
    _accessibilityState = {
      busy: ariaBusy != null ? ariaBusy : accessibilityState == null ? void 0 : accessibilityState.busy,
      checked: ariaChecked != null ? ariaChecked : accessibilityState == null ? void 0 : accessibilityState.checked,
      disabled: ariaDisabled != null ? ariaDisabled : accessibilityState == null ? void 0 : accessibilityState.disabled,
      expanded: ariaExpanded != null ? ariaExpanded : accessibilityState == null ? void 0 : accessibilityState.expanded,
      selected: ariaSelected != null ? ariaSelected : accessibilityState == null ? void 0 : accessibilityState.selected
    };
  }
  var _style = props.style;
  var flattenedStyle = (0, _flattenStyle.default)(props.style);
  if (flattenedStyle != null) {
    var overrides = null;
    if (typeof (flattenedStyle == null ? void 0 : flattenedStyle.fontWeight) === 'number') {
      overrides = overrides || {};
      overrides.fontWeight = flattenedStyle.fontWeight.toString();
    }
    if (flattenedStyle.verticalAlign != null) {
      overrides = overrides || {};
      overrides.textAlignVertical = verticalAlignToTextAlignVerticalMap[flattenedStyle.verticalAlign];
      overrides.verticalAlign = undefined;
    }
    if (overrides != null) {
      _style = [_style, overrides];
    }
  }
  if (_Platform.default.OS === 'ios') {
    var _props$rows;
    var RCTTextInputView = props.multiline === true ? RCTMultilineTextInputView : RCTSinglelineTextInputView;
    var useMultilineDefaultStyle = props.multiline === true && (flattenedStyle == null || flattenedStyle.padding == null && flattenedStyle.paddingVertical == null && flattenedStyle.paddingTop == null);
    textInput = (0, _jsxRuntime.jsx)(RCTTextInputView, Object.assign({
      ref: ref
    }, otherProps, eventHandlers, {
      accessibilityState: _accessibilityState,
      accessible: accessible,
      submitBehavior: submitBehavior,
      caretHidden: caretHidden,
      dataDetectorTypes: props.dataDetectorTypes,
      focusable: tabIndex !== undefined ? !tabIndex : focusable,
      mostRecentEventCount: mostRecentEventCount,
      nativeID: id != null ? id : props.nativeID,
      numberOfLines: (_props$rows = props.rows) != null ? _props$rows : props.numberOfLines,
      onBlur: _onBlur,
      onChange: _onChange,
      onContentSizeChange: props.onContentSizeChange,
      onFocus: _onFocus,
      onScroll: _onScroll,
      onSelectionChange: _onSelectionChange,
      onSelectionChangeShouldSetResponder: emptyFunctionThatReturnsTrue,
      selection: selection,
      selectionColor: selectionColor,
      style: _StyleSheet.default.compose(useMultilineDefaultStyle ? styles.multilineDefault : null, _style),
      text: text
    }));
  } else if (_Platform.default.OS === 'android') {
    var _props$ariaLabelledb, _props$placeholder, _props$rows2;
    var autoCapitalize = props.autoCapitalize || 'sentences';
    var _accessibilityLabelledBy = (_props$ariaLabelledb = props == null ? void 0 : props['aria-labelledby']) != null ? _props$ariaLabelledb : props == null ? void 0 : props.accessibilityLabelledBy;
    var placeholder = (_props$placeholder = props.placeholder) != null ? _props$placeholder : '';
    var children = props.children;
    var childCount = React.Children.count(children);
    (0, _invariant.default)(!(props.value != null && childCount), 'Cannot specify both value and children.');
    if (childCount > 1) {
      children = (0, _jsxRuntime.jsx)(_Text.default, {
        children: children
      });
    }
    var colorProps = {
      selectionColor: selectionColor,
      selectionHandleColor: selectionHandleColor === undefined ? selectionColor : selectionHandleColor,
      cursorColor: cursorColor === undefined ? selectionColor : cursorColor
    };
    textInput = (0, _jsxRuntime.jsx)(AndroidTextInput, Object.assign({
      ref: ref
    }, otherProps, colorProps, eventHandlers, {
      accessibilityState: _accessibilityState,
      accessibilityLabelledBy: _accessibilityLabelledBy,
      accessible: accessible,
      autoCapitalize: autoCapitalize,
      submitBehavior: submitBehavior,
      caretHidden: caretHidden,
      children: children,
      disableFullscreenUI: props.disableFullscreenUI,
      focusable: tabIndex !== undefined ? !tabIndex : focusable,
      mostRecentEventCount: mostRecentEventCount,
      nativeID: id != null ? id : props.nativeID,
      numberOfLines: (_props$rows2 = props.rows) != null ? _props$rows2 : props.numberOfLines,
      onBlur: _onBlur,
      onChange: _onChange,
      onFocus: _onFocus,
      onScroll: _onScroll,
      onSelectionChange: _onSelectionChange,
      placeholder: placeholder,
      style: _style,
      text: text,
      textBreakStrategy: props.textBreakStrategy
    }));
  }
  return (0, _jsxRuntime.jsx)(_TextAncestor.default.Provider, {
    value: true,
    children: textInput
  });
}
var enterKeyHintToReturnTypeMap = {
  enter: 'default',
  done: 'done',
  go: 'go',
  next: 'next',
  previous: 'previous',
  search: 'search',
  send: 'send'
};
var inputModeToKeyboardTypeMap = {
  none: 'default',
  text: 'default',
  decimal: 'decimal-pad',
  numeric: 'number-pad',
  tel: 'phone-pad',
  search: _Platform.default.OS === 'ios' ? 'web-search' : 'default',
  email: 'email-address',
  url: 'url'
};
var autoCompleteWebToAutoCompleteAndroidMap = {
  'address-line1': 'postal-address-region',
  'address-line2': 'postal-address-locality',
  bday: 'birthdate-full',
  'bday-day': 'birthdate-day',
  'bday-month': 'birthdate-month',
  'bday-year': 'birthdate-year',
  'cc-csc': 'cc-csc',
  'cc-exp': 'cc-exp',
  'cc-exp-month': 'cc-exp-month',
  'cc-exp-year': 'cc-exp-year',
  'cc-number': 'cc-number',
  country: 'postal-address-country',
  'current-password': 'password',
  email: 'email',
  'honorific-prefix': 'name-prefix',
  'honorific-suffix': 'name-suffix',
  name: 'name',
  'additional-name': 'name-middle',
  'family-name': 'name-family',
  'given-name': 'name-given',
  'new-password': 'password-new',
  off: 'off',
  'one-time-code': 'sms-otp',
  'postal-code': 'postal-code',
  sex: 'gender',
  'street-address': 'street-address',
  tel: 'tel',
  'tel-country-code': 'tel-country-code',
  'tel-national': 'tel-national',
  username: 'username'
};
var autoCompleteWebToTextContentTypeMap = {
  'address-line1': 'streetAddressLine1',
  'address-line2': 'streetAddressLine2',
  bday: 'birthdate',
  'bday-day': 'birthdateDay',
  'bday-month': 'birthdateMonth',
  'bday-year': 'birthdateYear',
  'cc-csc': 'creditCardSecurityCode',
  'cc-exp-month': 'creditCardExpirationMonth',
  'cc-exp-year': 'creditCardExpirationYear',
  'cc-exp': 'creditCardExpiration',
  'cc-given-name': 'creditCardGivenName',
  'cc-additional-name': 'creditCardMiddleName',
  'cc-family-name': 'creditCardFamilyName',
  'cc-name': 'creditCardName',
  'cc-number': 'creditCardNumber',
  'cc-type': 'creditCardType',
  'current-password': 'password',
  country: 'countryName',
  email: 'emailAddress',
  name: 'name',
  'additional-name': 'middleName',
  'family-name': 'familyName',
  'given-name': 'givenName',
  nickname: 'nickname',
  'honorific-prefix': 'namePrefix',
  'honorific-suffix': 'nameSuffix',
  'new-password': 'newPassword',
  off: 'none',
  'one-time-code': 'oneTimeCode',
  organization: 'organizationName',
  'organization-title': 'jobTitle',
  'postal-code': 'postalCode',
  'street-address': 'fullStreetAddress',
  tel: 'telephoneNumber',
  url: 'URL',
  username: 'username'
};
var ExportedForwardRef = React.forwardRef(function TextInput(_ref3, forwardedRef) {
  var _autoCompleteWebToAut;
  var _ref3$allowFontScalin = _ref3.allowFontScaling,
    allowFontScaling = _ref3$allowFontScalin === void 0 ? true : _ref3$allowFontScalin,
    _ref3$rejectResponder = _ref3.rejectResponderTermination,
    rejectResponderTermination = _ref3$rejectResponder === void 0 ? true : _ref3$rejectResponder,
    _ref3$underlineColorA = _ref3.underlineColorAndroid,
    underlineColorAndroid = _ref3$underlineColorA === void 0 ? 'transparent' : _ref3$underlineColorA,
    autoComplete = _ref3.autoComplete,
    textContentType = _ref3.textContentType,
    readOnly = _ref3.readOnly,
    editable = _ref3.editable,
    enterKeyHint = _ref3.enterKeyHint,
    returnKeyType = _ref3.returnKeyType,
    inputMode = _ref3.inputMode,
    showSoftInputOnFocus = _ref3.showSoftInputOnFocus,
    keyboardType = _ref3.keyboardType,
    restProps = (0, _objectWithoutProperties2.default)(_ref3, _excluded3);
  return (0, _jsxRuntime.jsx)(InternalTextInput, Object.assign({
    allowFontScaling: allowFontScaling,
    rejectResponderTermination: rejectResponderTermination,
    underlineColorAndroid: underlineColorAndroid,
    editable: readOnly !== undefined ? !readOnly : editable,
    returnKeyType: enterKeyHint ? enterKeyHintToReturnTypeMap[enterKeyHint] : returnKeyType,
    keyboardType: inputMode ? inputModeToKeyboardTypeMap[inputMode] : keyboardType,
    showSoftInputOnFocus: inputMode == null ? showSoftInputOnFocus : inputMode !== 'none',
    autoComplete: _Platform.default.OS === 'android' ? (_autoCompleteWebToAut = autoCompleteWebToAutoCompleteAndroidMap[autoComplete]) != null ? _autoCompleteWebToAut : autoComplete : undefined,
    textContentType: textContentType != null ? textContentType : _Platform.default.OS === 'ios' && autoComplete && autoComplete in autoCompleteWebToTextContentTypeMap ? autoCompleteWebToTextContentTypeMap[autoComplete] : textContentType
  }, restProps, {
    forwardedRef: forwardedRef
  }));
});
ExportedForwardRef.displayName = 'TextInput';
ExportedForwardRef.State = {
  currentlyFocusedInput: _TextInputState.default.currentlyFocusedInput,
  currentlyFocusedField: _TextInputState.default.currentlyFocusedField,
  focusTextInput: _TextInputState.default.focusTextInput,
  blurTextInput: _TextInputState.default.blurTextInput
};
var styles = _StyleSheet.default.create({
  multilineDefault: {
    paddingTop: 5
  }
});
var verticalAlignToTextAlignVerticalMap = {
  auto: 'auto',
  top: 'top',
  bottom: 'bottom',
  middle: 'center'
};
var _default = exports.default = ExportedForwardRef;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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