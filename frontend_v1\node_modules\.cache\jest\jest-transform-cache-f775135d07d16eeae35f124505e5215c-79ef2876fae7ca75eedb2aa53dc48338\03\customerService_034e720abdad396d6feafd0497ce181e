91dc3577af1335941e136d8512eb63c0
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _apiClient = require("./apiClient");
var _cachingService = require("./cachingService");
var CustomerService = function () {
  function CustomerService() {
    (0, _classCallCheck2.default)(this, CustomerService);
  }
  return (0, _createClass2.default)(CustomerService, [{
    key: "getServiceCategories",
    value: (function () {
      var _getServiceCategories = (0, _asyncToGenerator2.default)(function* () {
        try {
          var response = yield _apiClient.apiClient.get('/api/catalog/categories/', undefined, false);
          if (response.data && Array.isArray(response.data.results)) {
            return response.data.results;
          } else if (Array.isArray(response.data)) {
            return response.data;
          } else {
            console.warn('Unexpected response format for categories:', response.data);
            return this.getFallbackCategories();
          }
        } catch (error) {
          console.error('Failed to fetch service categories:', error);
          return this.getFallbackCategories();
        }
      });
      function getServiceCategories() {
        return _getServiceCategories.apply(this, arguments);
      }
      return getServiceCategories;
    }())
  }, {
    key: "getFeaturedProviders",
    value: (function () {
      var _getFeaturedProviders = (0, _asyncToGenerator2.default)(function* () {
        var limit = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 10;
        try {
          var cachedData = yield _cachingService.cachingService.getCachedApiResponse('/api/catalog/providers/featured/', {
            limit: limit
          });
          if (cachedData) {
            return cachedData;
          }
          var response = yield _apiClient.apiClient.get('/api/catalog/providers/featured/', {
            limit: limit
          }, false, {
            enabled: true,
            ttl: 5 * 60 * 1000
          });
          var transformedData = response.data.map(function (provider) {
            return Object.assign({}, provider, {
              rating: typeof provider.rating === 'string' ? parseFloat(provider.rating) || 0 : provider.rating || 0,
              reviewCount: provider.review_count || provider.reviewCount || 0
            });
          });
          yield _cachingService.cachingService.cacheApiResponse('/api/catalog/providers/featured/', {
            limit: limit
          }, transformedData, 5 * 60 * 1000);
          return transformedData;
        } catch (error) {
          console.error('Failed to fetch featured providers:', error);
          return this.getFallbackFeaturedProviders();
        }
      });
      function getFeaturedProviders() {
        return _getFeaturedProviders.apply(this, arguments);
      }
      return getFeaturedProviders;
    }())
  }, {
    key: "getNearbyProviders",
    value: (function () {
      var _getNearbyProviders = (0, _asyncToGenerator2.default)(function* (latitude, longitude) {
        var radius = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 10;
        var limit = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 10;
        try {
          var response = yield _apiClient.apiClient.get('/api/v1/customer/nearby/providers/', {
            lat: latitude,
            lng: longitude,
            radius: radius,
            limit: limit
          });
          return response.data.results;
        } catch (error) {
          console.error('Failed to fetch nearby providers:', error);
          return [];
        }
      });
      function getNearbyProviders(_x, _x2) {
        return _getNearbyProviders.apply(this, arguments);
      }
      return getNearbyProviders;
    }())
  }, {
    key: "getCustomerDashboard",
    value: (function () {
      var _getCustomerDashboard = (0, _asyncToGenerator2.default)(function* () {
        try {
          var response = yield _apiClient.apiClient.get('/api/v1/customer/dashboard/');
          return response.data;
        } catch (error) {
          console.error('Failed to fetch customer dashboard:', error);
          return this.getFallbackDashboard();
        }
      });
      function getCustomerDashboard() {
        return _getCustomerDashboard.apply(this, arguments);
      }
      return getCustomerDashboard;
    }())
  }, {
    key: "getCustomerProfile",
    value: (function () {
      var _getCustomerProfile = (0, _asyncToGenerator2.default)(function* () {
        try {
          var response = yield _apiClient.apiClient.get('/api/v1/customer/profile/');
          return response.data;
        } catch (error) {
          console.error('Failed to fetch customer profile:', error);
          throw error;
        }
      });
      function getCustomerProfile() {
        return _getCustomerProfile.apply(this, arguments);
      }
      return getCustomerProfile;
    }())
  }, {
    key: "getPersonalizedRecommendations",
    value: (function () {
      var _getPersonalizedRecommendations = (0, _asyncToGenerator2.default)(function* () {
        try {
          var response = yield _apiClient.apiClient.get('/api/v1/customer/recommendations/personalized/');
          return response.data.results;
        } catch (error) {
          console.error('Failed to fetch personalized recommendations:', error);
          return [];
        }
      });
      function getPersonalizedRecommendations() {
        return _getPersonalizedRecommendations.apply(this, arguments);
      }
      return getPersonalizedRecommendations;
    }())
  }, {
    key: "getFavoriteProviders",
    value: (function () {
      var _getFavoriteProviders = (0, _asyncToGenerator2.default)(function* () {
        try {
          var response = yield _apiClient.apiClient.get('/api/v1/customer/favorites/');
          return response.data.results;
        } catch (error) {
          console.error('Failed to fetch favorite providers:', error);
          return [];
        }
      });
      function getFavoriteProviders() {
        return _getFavoriteProviders.apply(this, arguments);
      }
      return getFavoriteProviders;
    }())
  }, {
    key: "createQuickBooking",
    value: (function () {
      var _createQuickBooking = (0, _asyncToGenerator2.default)(function* (bookingData) {
        try {
          var response = yield _apiClient.apiClient.post('/api/v1/customer/bookings/quick-book/', bookingData);
          return response.data;
        } catch (error) {
          console.error('Failed to create quick booking:', error);
          throw error;
        }
      });
      function createQuickBooking(_x3) {
        return _createQuickBooking.apply(this, arguments);
      }
      return createQuickBooking;
    }())
  }, {
    key: "getFallbackCategories",
    value: function getFallbackCategories() {
      return [{
        id: '1',
        name: 'Barber',
        slug: 'barber',
        description: 'Professional barber services',
        icon: 'cut-outline',
        color: '#5A7A63',
        serviceCount: 12,
        isActive: true,
        displayOrder: 1
      }, {
        id: '2',
        name: 'Salon',
        slug: 'salon',
        description: 'Hair salon services',
        icon: 'brush-outline',
        color: '#6B8A74',
        serviceCount: 8,
        isActive: true,
        displayOrder: 2
      }, {
        id: '3',
        name: 'Nail Services',
        slug: 'nail-services',
        description: 'Professional nail care',
        icon: 'hand-left-outline',
        color: '#5A7A63',
        serviceCount: 15,
        isActive: true,
        displayOrder: 3
      }, {
        id: '4',
        name: 'Lash Services',
        slug: 'lash-services',
        description: 'Eyelash extensions and care',
        icon: 'eye-outline',
        color: '#4A6B52',
        serviceCount: 6,
        isActive: true,
        displayOrder: 4
      }, {
        id: '5',
        name: 'Braiding',
        slug: 'braiding',
        description: 'Hair braiding services',
        icon: 'flower-outline',
        color: '#3A5B42',
        serviceCount: 10,
        isActive: true,
        displayOrder: 5
      }, {
        id: '6',
        name: 'Skincare',
        slug: 'skincare',
        description: 'Facial and skincare treatments',
        icon: 'heart-outline',
        color: '#6B8A74',
        serviceCount: 7,
        isActive: true,
        displayOrder: 6
      }, {
        id: '7',
        name: 'Massage',
        slug: 'massage',
        description: 'Therapeutic massage services',
        icon: 'hand-right-outline',
        color: '#5A7A63',
        serviceCount: 8,
        isActive: true,
        displayOrder: 7
      }];
    }
  }, {
    key: "getFallbackFeaturedProviders",
    value: function getFallbackFeaturedProviders() {
      return [{
        id: 'fallback_1',
        name: 'Bella Beauty Studio',
        businessName: 'Bella Beauty Studio',
        description: 'Professional beauty services',
        avatar: null,
        rating: 4.8,
        reviewCount: 127,
        isVerified: true,
        categories: ['Hair', 'Nails'],
        location: {
          address: '123 Main St',
          city: 'Toronto',
          distance: 2.5
        },
        services: [{
          id: 'service_1',
          name: 'Hair Cut & Style',
          price: 65,
          duration: 60
        }],
        isOnline: true,
        responseTime: '< 1 hour'
      }];
    }
  }, {
    key: "getFallbackDashboard",
    value: function getFallbackDashboard() {
      return {
        greeting: 'Good morning',
        upcomingBookings: 0,
        favoriteProviders: 0,
        recentActivity: [],
        recommendations: []
      };
    }
  }]);
}();
var customerService = new CustomerService();
var _default = exports.default = customerService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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