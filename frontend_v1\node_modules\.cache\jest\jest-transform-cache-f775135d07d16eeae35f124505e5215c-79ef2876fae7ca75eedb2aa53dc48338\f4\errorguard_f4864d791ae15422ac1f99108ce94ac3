5e5c95e08734cdd745602b31534c6fba
var _inGuard = 0;
var _globalHandler = global.RN$useAlwaysAvailableJSErrorHandling === true ? global.RN$handleException : function (e, isFatal) {
  throw e;
};
var ErrorUtils = {
  setGlobalHandler: function setGlobalHandler(fun) {
    _globalHandler = fun;
  },
  getGlobalHandler: function getGlobalHandler() {
    return _globalHandler;
  },
  reportError: function reportError(error) {
    _globalHandler && _globalHandler(error, false);
  },
  reportFatalError: function reportFatalError(error) {
    _globalHandler && _globalHandler(error, true);
  },
  applyWithGuard: function applyWithGuard(fun, context, args, unused_onError, unused_name) {
    try {
      _inGuard++;
      return fun.apply(context, args);
    } catch (e) {
      ErrorUtils.reportError(e);
    } finally {
      _inGuard--;
    }
    return null;
  },
  applyWithGuardIfNeeded: function applyWithGuardIfNeeded(fun, context, args) {
    if (ErrorUtils.inGuard()) {
      return fun.apply(context, args);
    } else {
      ErrorUtils.applyWithGuard(fun, context, args);
    }
    return null;
  },
  inGuard: function inGuard() {
    return !!_inGuard;
  },
  guard: function guard(fun, name, context) {
    var _ref;
    if (typeof fun !== 'function') {
      console.warn('A function must be passed to ErrorUtils.guard, got ', fun);
      return null;
    }
    var guardName = (_ref = name != null ? name : fun.name) != null ? _ref : '<generated guard>';
    function guarded() {
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      return ErrorUtils.applyWithGuard(fun, context != null ? context : this, args, null, guardName);
    }
    return guarded;
  }
};
global.ErrorUtils = ErrorUtils;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************