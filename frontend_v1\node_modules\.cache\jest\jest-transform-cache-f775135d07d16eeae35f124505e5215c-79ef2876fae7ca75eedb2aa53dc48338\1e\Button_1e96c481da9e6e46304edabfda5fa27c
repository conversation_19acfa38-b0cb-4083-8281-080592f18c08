b605ba0c4f6f6e67a260572d95e29e7a
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Button = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _ThemeContext = require("../../contexts/ThemeContext");
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["children", "title", "variant", "size", "style", "onPress", "disabled", "loading", "fullWidth", "leftIcon", "rightIcon", "accessibilityLabel", "accessibilityHint"];
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var Button = exports.Button = function Button(_ref) {
  var _colors$primary, _colors$primary2;
  var children = _ref.children,
    title = _ref.title,
    _ref$variant = _ref.variant,
    variant = _ref$variant === void 0 ? 'primary' : _ref$variant,
    _ref$size = _ref.size,
    size = _ref$size === void 0 ? 'md' : _ref$size,
    style = _ref.style,
    onPress = _ref.onPress,
    _ref$disabled = _ref.disabled,
    disabled = _ref$disabled === void 0 ? false : _ref$disabled,
    _ref$loading = _ref.loading,
    loading = _ref$loading === void 0 ? false : _ref$loading,
    _ref$fullWidth = _ref.fullWidth,
    fullWidth = _ref$fullWidth === void 0 ? false : _ref$fullWidth,
    leftIcon = _ref.leftIcon,
    rightIcon = _ref.rightIcon,
    accessibilityLabel = _ref.accessibilityLabel,
    accessibilityHint = _ref.accessibilityHint,
    props = (0, _objectWithoutProperties2.default)(_ref, _excluded);
  var _useTheme = (0, _ThemeContext.useTheme)(),
    colors = _useTheme.colors;
  var _useState = (0, _react.useState)(false),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    isFocused = _useState2[0],
    setIsFocused = _useState2[1];
  var styles = createStyles(colors);
  var buttonContent = children || title;
  var buttonStyleArray = [styles.base, styles[variant], styles[size], fullWidth && styles.fullWidth, disabled && styles.disabled, loading && styles.loading, isFocused && styles.focused, style].filter(Boolean);
  var textStyleArray = [styles.text, styles[`${variant}Text`], styles[`${size}Text`], disabled && styles.disabledText].filter(Boolean);
  var isInteractionDisabled = disabled || loading;
  return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, Object.assign({
    style: buttonStyleArray,
    onPress: isInteractionDisabled ? undefined : onPress,
    disabled: isInteractionDisabled,
    onFocus: function onFocus() {
      return setIsFocused(true);
    },
    onBlur: function onBlur() {
      return setIsFocused(false);
    },
    accessibilityRole: "button",
    accessibilityState: {
      disabled: isInteractionDisabled,
      busy: loading
    },
    accessibilityLabel: accessibilityLabel || (typeof buttonContent === 'string' ? buttonContent : 'Button'),
    accessibilityHint: accessibilityHint
  }, props, {
    children: [loading && (0, _jsxRuntime.jsx)(_reactNative.ActivityIndicator, {
      size: "small",
      color: variant === 'primary' ? ((_colors$primary = colors.primary) == null ? void 0 : _colors$primary.contrast) || '#FFFFFF' : ((_colors$primary2 = colors.primary) == null ? void 0 : _colors$primary2.main) || '#4A6B52',
      style: styles.loadingIndicator
    }), !loading && leftIcon && (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: leftIcon
    }), buttonContent && (0, _jsxRuntime.jsx)(_reactNative.Text, {
      style: textStyleArray,
      numberOfLines: 1,
      children: buttonContent
    }), !loading && rightIcon && (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: rightIcon
    })]
  }));
};
var createStyles = function createStyles(colors) {
  var _colors$primary3, _colors$surface, _colors$primary4, _colors$primary5, _colors$primary6, _colors$primary7, _colors$text, _colors$primary8, _colors$primary9, _colors$text2;
  return _reactNative.StyleSheet.create({
    base: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 8,
      minHeight: 44,
      minWidth: 44,
      gap: 8
    },
    primary: {
      backgroundColor: ((_colors$primary3 = colors.primary) == null ? void 0 : _colors$primary3.default) || '#4A6B52',
      paddingHorizontal: 16,
      paddingVertical: 12
    },
    secondary: {
      backgroundColor: ((_colors$surface = colors.surface) == null ? void 0 : _colors$surface.secondary) || '#F9FAFB',
      borderWidth: 1,
      borderColor: ((_colors$primary4 = colors.primary) == null ? void 0 : _colors$primary4.default) || '#4A6B52',
      paddingHorizontal: 16,
      paddingVertical: 12
    },
    outline: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: ((_colors$primary5 = colors.primary) == null ? void 0 : _colors$primary5.default) || '#4A6B52',
      paddingHorizontal: 16,
      paddingVertical: 12
    },
    minimal: {
      backgroundColor: 'transparent',
      paddingHorizontal: 8,
      paddingVertical: 8
    },
    sm: {
      height: 36,
      paddingHorizontal: 12,
      paddingVertical: 8
    },
    md: {
      height: 44,
      paddingHorizontal: 16,
      paddingVertical: 12
    },
    lg: {
      height: 52,
      paddingHorizontal: 20,
      paddingVertical: 16
    },
    disabled: {
      opacity: 0.5
    },
    loading: {
      opacity: 0.8
    },
    focused: {
      borderWidth: 2,
      borderColor: ((_colors$primary6 = colors.primary) == null ? void 0 : _colors$primary6.light) || '#6B8A74'
    },
    fullWidth: {
      width: '100%'
    },
    text: {
      fontFamily: 'System',
      fontWeight: '500',
      textAlign: 'center',
      fontSize: 16,
      lineHeight: 24
    },
    primaryText: {
      color: ((_colors$primary7 = colors.primary) == null ? void 0 : _colors$primary7.contrast) || '#FFFFFF'
    },
    secondaryText: {
      color: ((_colors$text = colors.text) == null ? void 0 : _colors$text.primary) || '#1A1A1A'
    },
    outlineText: {
      color: ((_colors$primary8 = colors.primary) == null ? void 0 : _colors$primary8.default) || '#4A6B52'
    },
    minimalText: {
      color: ((_colors$primary9 = colors.primary) == null ? void 0 : _colors$primary9.default) || '#4A6B52'
    },
    disabledText: {
      color: ((_colors$text2 = colors.text) == null ? void 0 : _colors$text2.tertiary) || '#9CA3AF'
    },
    smText: {
      fontSize: 14
    },
    mdText: {
      fontSize: 16
    },
    lgText: {
      fontSize: 18
    },
    loadingIndicator: {
      marginRight: 8
    }
  });
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfcmVhY3QiLCJfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZCIsInJlcXVpcmUiLCJfcmVhY3ROYXRpdmUiLCJfVGhlbWVDb250ZXh0IiwiX2pzeFJ1bnRpbWUiLCJfZXhjbHVkZWQiLCJlIiwidCIsIldlYWtNYXAiLCJyIiwibiIsIl9fZXNNb2R1bGUiLCJvIiwiaSIsImYiLCJfX3Byb3RvX18iLCJkZWZhdWx0IiwiaGFzIiwiZ2V0Iiwic2V0IiwiX3QiLCJoYXNPd25Qcm9wZXJ0eSIsImNhbGwiLCJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImdldE93blByb3BlcnR5RGVzY3JpcHRvciIsIkJ1dHRvbiIsImV4cG9ydHMiLCJfcmVmIiwiX2NvbG9ycyRwcmltYXJ5IiwiX2NvbG9ycyRwcmltYXJ5MiIsImNoaWxkcmVuIiwidGl0bGUiLCJfcmVmJHZhcmlhbnQiLCJ2YXJpYW50IiwiX3JlZiRzaXplIiwic2l6ZSIsInN0eWxlIiwib25QcmVzcyIsIl9yZWYkZGlzYWJsZWQiLCJkaXNhYmxlZCIsIl9yZWYkbG9hZGluZyIsImxvYWRpbmciLCJfcmVmJGZ1bGxXaWR0aCIsImZ1bGxXaWR0aCIsImxlZnRJY29uIiwicmlnaHRJY29uIiwiYWNjZXNzaWJpbGl0eUxhYmVsIiwiYWNjZXNzaWJpbGl0eUhpbnQiLCJwcm9wcyIsIl9vYmplY3RXaXRob3V0UHJvcGVydGllczIiLCJfdXNlVGhlbWUiLCJ1c2VUaGVtZSIsImNvbG9ycyIsIl91c2VTdGF0ZSIsInVzZVN0YXRlIiwiX3VzZVN0YXRlMiIsIl9zbGljZWRUb0FycmF5MiIsImlzRm9jdXNlZCIsInNldElzRm9jdXNlZCIsInN0eWxlcyIsImNyZWF0ZVN0eWxlcyIsImJ1dHRvbkNvbnRlbnQiLCJidXR0b25TdHlsZUFycmF5IiwiYmFzZSIsImZvY3VzZWQiLCJmaWx0ZXIiLCJCb29sZWFuIiwidGV4dFN0eWxlQXJyYXkiLCJ0ZXh0IiwiZGlzYWJsZWRUZXh0IiwiaXNJbnRlcmFjdGlvbkRpc2FibGVkIiwianN4cyIsIlRvdWNoYWJsZU9wYWNpdHkiLCJhc3NpZ24iLCJ1bmRlZmluZWQiLCJvbkZvY3VzIiwib25CbHVyIiwiYWNjZXNzaWJpbGl0eVJvbGUiLCJhY2Nlc3NpYmlsaXR5U3RhdGUiLCJidXN5IiwianN4IiwiQWN0aXZpdHlJbmRpY2F0b3IiLCJjb2xvciIsInByaW1hcnkiLCJjb250cmFzdCIsIm1haW4iLCJsb2FkaW5nSW5kaWNhdG9yIiwiRnJhZ21lbnQiLCJUZXh0IiwibnVtYmVyT2ZMaW5lcyIsIl9jb2xvcnMkcHJpbWFyeTMiLCJfY29sb3JzJHN1cmZhY2UiLCJfY29sb3JzJHByaW1hcnk0IiwiX2NvbG9ycyRwcmltYXJ5NSIsIl9jb2xvcnMkcHJpbWFyeTYiLCJfY29sb3JzJHByaW1hcnk3IiwiX2NvbG9ycyR0ZXh0IiwiX2NvbG9ycyRwcmltYXJ5OCIsIl9jb2xvcnMkcHJpbWFyeTkiLCJfY29sb3JzJHRleHQyIiwiU3R5bGVTaGVldCIsImNyZWF0ZSIsImZsZXhEaXJlY3Rpb24iLCJhbGlnbkl0ZW1zIiwianVzdGlmeUNvbnRlbnQiLCJib3JkZXJSYWRpdXMiLCJtaW5IZWlnaHQiLCJtaW5XaWR0aCIsImdhcCIsImJhY2tncm91bmRDb2xvciIsInBhZGRpbmdIb3Jpem9udGFsIiwicGFkZGluZ1ZlcnRpY2FsIiwic2Vjb25kYXJ5Iiwic3VyZmFjZSIsImJvcmRlcldpZHRoIiwiYm9yZGVyQ29sb3IiLCJvdXRsaW5lIiwibWluaW1hbCIsInNtIiwiaGVpZ2h0IiwibWQiLCJsZyIsIm9wYWNpdHkiLCJsaWdodCIsIndpZHRoIiwiZm9udEZhbWlseSIsImZvbnRXZWlnaHQiLCJ0ZXh0QWxpZ24iLCJmb250U2l6ZSIsImxpbmVIZWlnaHQiLCJwcmltYXJ5VGV4dCIsInNlY29uZGFyeVRleHQiLCJvdXRsaW5lVGV4dCIsIm1pbmltYWxUZXh0IiwidGVydGlhcnkiLCJzbVRleHQiLCJtZFRleHQiLCJsZ1RleHQiLCJtYXJnaW5SaWdodCJdLCJzb3VyY2VzIjpbIkJ1dHRvbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBCdXR0b24gQ29tcG9uZW50IC0gRW5oYW5jZWQgRm91bmRhdGlvbmFsIFVJIEF0b21cbiAqXG4gKiBDb21wb25lbnQgQ29udHJhY3Q6XG4gKiAtIFNvdXJjZXMgYWxsIHN0eWxlcyBmcm9tIHVuaWZpZWQgZGVzaWduIHRva2Vuc1xuICogLSBTdXBwb3J0cyBtdWx0aXBsZSB2YXJpYW50cyAocHJpbWFyeSwgc2Vjb25kYXJ5LCBvdXRsaW5lLCBtaW5pbWFsKVxuICogLSBIYW5kbGVzIGRpc2FibGVkIGFuZCBsb2FkaW5nIHN0YXRlcyB3aXRoIHByb3BlciBhY2Nlc3NpYmlsaXR5XG4gKiAtIE1lZXRzIG1pbmltdW0gdG91Y2ggdGFyZ2V0IHJlcXVpcmVtZW50cyAoNDR4NDQpXG4gKiAtIFByb3ZpZGVzIGNvbXByZWhlbnNpdmUgYWNjZXNzaWJpbGl0eSBzdXBwb3J0XG4gKiAtIEZvbGxvd3MgUmVhY3QgTmF0aXZlIGJlc3QgcHJhY3RpY2VzIGZvciBwZXJmb3JtYW5jZVxuICogLSBTdXBwb3J0cyB0aGVtZSBzd2l0Y2hpbmcgKGxpZ2h0L2RhcmsgbW9kZSlcbiAqXG4gKiBAdmVyc2lvbiAyLjAuMFxuICogQGF1dGhvciBWaWVybGEgRGV2ZWxvcG1lbnQgVGVhbVxuICovXG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7XG4gIFRvdWNoYWJsZU9wYWNpdHksXG4gIFRleHQsXG4gIEFjdGl2aXR5SW5kaWNhdG9yLFxuICBTdHlsZVNoZWV0LFxuICBWaWV3U3R5bGUsXG4gIFRleHRTdHlsZSxcbiAgVG91Y2hhYmxlT3BhY2l0eVByb3BzLFxufSBmcm9tICdyZWFjdC1uYXRpdmUnO1xuXG5pbXBvcnQgeyB1c2VUaGVtZSB9IGZyb20gJy4uLy4uL2NvbnRleHRzL1RoZW1lQ29udGV4dCc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgQnV0dG9uUHJvcHMgZXh0ZW5kcyBPbWl0PFRvdWNoYWJsZU9wYWNpdHlQcm9wcywgJ3N0eWxlJz4ge1xuICAvKiogQnV0dG9uIGNvbnRlbnQgKi9cbiAgY2hpbGRyZW4/OiBSZWFjdC5SZWFjdE5vZGU7XG4gIC8qKiBCdXR0b24gdGl0bGUgKGFsdGVybmF0aXZlIHRvIGNoaWxkcmVuKSAqL1xuICB0aXRsZT86IHN0cmluZztcbiAgLyoqIEJ1dHRvbiB2YXJpYW50ICovXG4gIHZhcmlhbnQ/OiAncHJpbWFyeScgfCAnc2Vjb25kYXJ5JyB8ICdvdXRsaW5lJyB8ICdtaW5pbWFsJztcbiAgLyoqIEJ1dHRvbiBzaXplICovXG4gIHNpemU/OiAnc20nIHwgJ21kJyB8ICdsZyc7XG4gIC8qKiBDdXN0b20gc3R5bGUgb3ZlcnJpZGUgKi9cbiAgc3R5bGU/OiBWaWV3U3R5bGU7XG4gIC8qKiBQcmVzcyBoYW5kbGVyICovXG4gIG9uUHJlc3M6ICgpID0+IHZvaWQ7XG4gIC8qKiBEaXNhYmxlZCBzdGF0ZSAqL1xuICBkaXNhYmxlZD86IGJvb2xlYW47XG4gIC8qKiBMb2FkaW5nIHN0YXRlICovXG4gIGxvYWRpbmc/OiBib29sZWFuO1xuICAvKiogRnVsbCB3aWR0aCBidXR0b24gKi9cbiAgZnVsbFdpZHRoPzogYm9vbGVhbjtcbiAgLyoqIEljb24gdG8gZGlzcGxheSBiZWZvcmUgdGV4dCAqL1xuICBsZWZ0SWNvbj86IFJlYWN0LlJlYWN0Tm9kZTtcbiAgLyoqIEljb24gdG8gZGlzcGxheSBhZnRlciB0ZXh0ICovXG4gIHJpZ2h0SWNvbj86IFJlYWN0LlJlYWN0Tm9kZTtcbn1cblxuZXhwb3J0IGNvbnN0IEJ1dHRvbjogUmVhY3QuRkM8QnV0dG9uUHJvcHM+ID0gKHtcbiAgY2hpbGRyZW4sXG4gIHRpdGxlLFxuICB2YXJpYW50ID0gJ3ByaW1hcnknLFxuICBzaXplID0gJ21kJyxcbiAgc3R5bGUsXG4gIG9uUHJlc3MsXG4gIGRpc2FibGVkID0gZmFsc2UsXG4gIGxvYWRpbmcgPSBmYWxzZSxcbiAgZnVsbFdpZHRoID0gZmFsc2UsXG4gIGxlZnRJY29uLFxuICByaWdodEljb24sXG4gIGFjY2Vzc2liaWxpdHlMYWJlbCxcbiAgYWNjZXNzaWJpbGl0eUhpbnQsXG4gIC4uLnByb3BzXG59KSA9PiB7XG4gIGNvbnN0IHsgY29sb3JzIH0gPSB1c2VUaGVtZSgpO1xuICBjb25zdCBbaXNGb2N1c2VkLCBzZXRJc0ZvY3VzZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIGNvbnN0IHN0eWxlcyA9IGNyZWF0ZVN0eWxlcyhjb2xvcnMpO1xuXG4gIC8vIERldGVybWluZSBidXR0b24gY29udGVudCAtIGNoaWxkcmVuIHRha2VzIHByaW9yaXR5IG92ZXIgdGl0bGVcbiAgY29uc3QgYnV0dG9uQ29udGVudCA9IGNoaWxkcmVuIHx8IHRpdGxlO1xuXG4gIC8vIENyZWF0ZSBzdHlsZSBhcnJheXMgYmFzZWQgb24gcHJvcHNcbiAgY29uc3QgYnV0dG9uU3R5bGVBcnJheSA9IFtcbiAgICBzdHlsZXMuYmFzZSxcbiAgICBzdHlsZXNbdmFyaWFudF0sXG4gICAgc3R5bGVzW3NpemVdLFxuICAgIGZ1bGxXaWR0aCAmJiBzdHlsZXMuZnVsbFdpZHRoLFxuICAgIGRpc2FibGVkICYmIHN0eWxlcy5kaXNhYmxlZCxcbiAgICBsb2FkaW5nICYmIHN0eWxlcy5sb2FkaW5nLFxuICAgIGlzRm9jdXNlZCAmJiBzdHlsZXMuZm9jdXNlZCxcbiAgICBzdHlsZSxcbiAgXS5maWx0ZXIoQm9vbGVhbik7XG5cbiAgY29uc3QgdGV4dFN0eWxlQXJyYXkgPSBbXG4gICAgc3R5bGVzLnRleHQsXG4gICAgc3R5bGVzW2Ake3ZhcmlhbnR9VGV4dGBdLFxuICAgIHN0eWxlc1tgJHtzaXplfVRleHRgXSxcbiAgICBkaXNhYmxlZCAmJiBzdHlsZXMuZGlzYWJsZWRUZXh0LFxuICBdLmZpbHRlcihCb29sZWFuKTtcblxuICBjb25zdCBpc0ludGVyYWN0aW9uRGlzYWJsZWQgPSBkaXNhYmxlZCB8fCBsb2FkaW5nO1xuXG4gIHJldHVybiAoXG4gICAgPFRvdWNoYWJsZU9wYWNpdHlcbiAgICAgIHN0eWxlPXtidXR0b25TdHlsZUFycmF5fVxuICAgICAgb25QcmVzcz17aXNJbnRlcmFjdGlvbkRpc2FibGVkID8gdW5kZWZpbmVkIDogb25QcmVzc31cbiAgICAgIGRpc2FibGVkPXtpc0ludGVyYWN0aW9uRGlzYWJsZWR9XG4gICAgICBvbkZvY3VzPXsoKSA9PiBzZXRJc0ZvY3VzZWQodHJ1ZSl9XG4gICAgICBvbkJsdXI9eygpID0+IHNldElzRm9jdXNlZChmYWxzZSl9XG4gICAgICBhY2Nlc3NpYmlsaXR5Um9sZT1cImJ1dHRvblwiXG4gICAgICBhY2Nlc3NpYmlsaXR5U3RhdGU9e3tcbiAgICAgICAgZGlzYWJsZWQ6IGlzSW50ZXJhY3Rpb25EaXNhYmxlZCxcbiAgICAgICAgYnVzeTogbG9hZGluZyxcbiAgICAgIH19XG4gICAgICBhY2Nlc3NpYmlsaXR5TGFiZWw9e1xuICAgICAgICBhY2Nlc3NpYmlsaXR5TGFiZWwgfHxcbiAgICAgICAgKHR5cGVvZiBidXR0b25Db250ZW50ID09PSAnc3RyaW5nJyA/IGJ1dHRvbkNvbnRlbnQgOiAnQnV0dG9uJylcbiAgICAgIH1cbiAgICAgIGFjY2Vzc2liaWxpdHlIaW50PXthY2Nlc3NpYmlsaXR5SGludH1cbiAgICAgIHsuLi5wcm9wc30+XG4gICAgICB7bG9hZGluZyAmJiAoXG4gICAgICAgIDxBY3Rpdml0eUluZGljYXRvclxuICAgICAgICAgIHNpemU9XCJzbWFsbFwiXG4gICAgICAgICAgY29sb3I9e1xuICAgICAgICAgICAgdmFyaWFudCA9PT0gJ3ByaW1hcnknXG4gICAgICAgICAgICAgID8gY29sb3JzLnByaW1hcnk/LmNvbnRyYXN0IHx8ICcjRkZGRkZGJ1xuICAgICAgICAgICAgICA6IGNvbG9ycy5wcmltYXJ5Py5tYWluIHx8ICcjNEE2QjUyJ1xuICAgICAgICAgIH1cbiAgICAgICAgICBzdHlsZT17c3R5bGVzLmxvYWRpbmdJbmRpY2F0b3J9XG4gICAgICAgIC8+XG4gICAgICApfVxuXG4gICAgICB7IWxvYWRpbmcgJiYgbGVmdEljb24gJiYgPD57bGVmdEljb259PC8+fVxuXG4gICAgICB7YnV0dG9uQ29udGVudCAmJiAoXG4gICAgICAgIDxUZXh0IHN0eWxlPXt0ZXh0U3R5bGVBcnJheX0gbnVtYmVyT2ZMaW5lcz17MX0+XG4gICAgICAgICAge2J1dHRvbkNvbnRlbnR9XG4gICAgICAgIDwvVGV4dD5cbiAgICAgICl9XG5cbiAgICAgIHshbG9hZGluZyAmJiByaWdodEljb24gJiYgPD57cmlnaHRJY29ufTwvPn1cbiAgICA8L1RvdWNoYWJsZU9wYWNpdHk+XG4gICk7XG59O1xuXG4vLyBUaGVtZS1iYXNlZCBzdHlsZXMgZmFjdG9yeVxuY29uc3QgY3JlYXRlU3R5bGVzID0gKGNvbG9yczogYW55KSA9PlxuICBTdHlsZVNoZWV0LmNyZWF0ZSh7XG4gICAgYmFzZToge1xuICAgICAgZmxleERpcmVjdGlvbjogJ3JvdycsXG4gICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgIGJvcmRlclJhZGl1czogOCxcbiAgICAgIG1pbkhlaWdodDogNDQsXG4gICAgICBtaW5XaWR0aDogNDQsXG4gICAgICBnYXA6IDgsXG4gICAgfSxcblxuICAgIC8vIFZhcmlhbnRzXG4gICAgcHJpbWFyeToge1xuICAgICAgYmFja2dyb3VuZENvbG9yOiBjb2xvcnMucHJpbWFyeT8uZGVmYXVsdCB8fCAnIzRBNkI1MicsXG4gICAgICBwYWRkaW5nSG9yaXpvbnRhbDogMTYsXG4gICAgICBwYWRkaW5nVmVydGljYWw6IDEyLFxuICAgIH0sXG4gICAgc2Vjb25kYXJ5OiB7XG4gICAgICBiYWNrZ3JvdW5kQ29sb3I6IGNvbG9ycy5zdXJmYWNlPy5zZWNvbmRhcnkgfHwgJyNGOUZBRkInLFxuICAgICAgYm9yZGVyV2lkdGg6IDEsXG4gICAgICBib3JkZXJDb2xvcjogY29sb3JzLnByaW1hcnk/LmRlZmF1bHQgfHwgJyM0QTZCNTInLFxuICAgICAgcGFkZGluZ0hvcml6b250YWw6IDE2LFxuICAgICAgcGFkZGluZ1ZlcnRpY2FsOiAxMixcbiAgICB9LFxuICAgIG91dGxpbmU6IHtcbiAgICAgIGJhY2tncm91bmRDb2xvcjogJ3RyYW5zcGFyZW50JyxcbiAgICAgIGJvcmRlcldpZHRoOiAxLFxuICAgICAgYm9yZGVyQ29sb3I6IGNvbG9ycy5wcmltYXJ5Py5kZWZhdWx0IHx8ICcjNEE2QjUyJyxcbiAgICAgIHBhZGRpbmdIb3Jpem9udGFsOiAxNixcbiAgICAgIHBhZGRpbmdWZXJ0aWNhbDogMTIsXG4gICAgfSxcbiAgICBtaW5pbWFsOiB7XG4gICAgICBiYWNrZ3JvdW5kQ29sb3I6ICd0cmFuc3BhcmVudCcsXG4gICAgICBwYWRkaW5nSG9yaXpvbnRhbDogOCxcbiAgICAgIHBhZGRpbmdWZXJ0aWNhbDogOCxcbiAgICB9LFxuXG4gICAgLy8gU2l6ZXNcbiAgICBzbToge1xuICAgICAgaGVpZ2h0OiAzNixcbiAgICAgIHBhZGRpbmdIb3Jpem9udGFsOiAxMixcbiAgICAgIHBhZGRpbmdWZXJ0aWNhbDogOCxcbiAgICB9LFxuICAgIG1kOiB7XG4gICAgICBoZWlnaHQ6IDQ0LFxuICAgICAgcGFkZGluZ0hvcml6b250YWw6IDE2LFxuICAgICAgcGFkZGluZ1ZlcnRpY2FsOiAxMixcbiAgICB9LFxuICAgIGxnOiB7XG4gICAgICBoZWlnaHQ6IDUyLFxuICAgICAgcGFkZGluZ0hvcml6b250YWw6IDIwLFxuICAgICAgcGFkZGluZ1ZlcnRpY2FsOiAxNixcbiAgICB9LFxuXG4gICAgLy8gU3RhdGVzXG4gICAgZGlzYWJsZWQ6IHtcbiAgICAgIG9wYWNpdHk6IDAuNSxcbiAgICB9LFxuICAgIGxvYWRpbmc6IHtcbiAgICAgIG9wYWNpdHk6IDAuOCxcbiAgICB9LFxuICAgIGZvY3VzZWQ6IHtcbiAgICAgIGJvcmRlcldpZHRoOiAyLFxuICAgICAgYm9yZGVyQ29sb3I6IGNvbG9ycy5wcmltYXJ5Py5saWdodCB8fCAnIzZCOEE3NCcsXG4gICAgfSxcbiAgICBmdWxsV2lkdGg6IHtcbiAgICAgIHdpZHRoOiAnMTAwJScsXG4gICAgfSxcblxuICAgIC8vIFRleHQgc3R5bGVzXG4gICAgdGV4dDoge1xuICAgICAgZm9udEZhbWlseTogJ1N5c3RlbScsXG4gICAgICBmb250V2VpZ2h0OiAnNTAwJyxcbiAgICAgIHRleHRBbGlnbjogJ2NlbnRlcicsXG4gICAgICBmb250U2l6ZTogMTYsXG4gICAgICBsaW5lSGVpZ2h0OiAyNCxcbiAgICB9LFxuICAgIHByaW1hcnlUZXh0OiB7XG4gICAgICBjb2xvcjogY29sb3JzLnByaW1hcnk/LmNvbnRyYXN0IHx8ICcjRkZGRkZGJyxcbiAgICB9LFxuICAgIHNlY29uZGFyeVRleHQ6IHtcbiAgICAgIGNvbG9yOiBjb2xvcnMudGV4dD8ucHJpbWFyeSB8fCAnIzFBMUExQScsXG4gICAgfSxcbiAgICBvdXRsaW5lVGV4dDoge1xuICAgICAgY29sb3I6IGNvbG9ycy5wcmltYXJ5Py5kZWZhdWx0IHx8ICcjNEE2QjUyJyxcbiAgICB9LFxuICAgIG1pbmltYWxUZXh0OiB7XG4gICAgICBjb2xvcjogY29sb3JzLnByaW1hcnk/LmRlZmF1bHQgfHwgJyM0QTZCNTInLFxuICAgIH0sXG4gICAgZGlzYWJsZWRUZXh0OiB7XG4gICAgICBjb2xvcjogY29sb3JzLnRleHQ/LnRlcnRpYXJ5IHx8ICcjOUNBM0FGJyxcbiAgICB9LFxuXG4gICAgLy8gU2l6ZS1zcGVjaWZpYyB0ZXh0XG4gICAgc21UZXh0OiB7XG4gICAgICBmb250U2l6ZTogMTQsXG4gICAgfSxcbiAgICBtZFRleHQ6IHtcbiAgICAgIGZvbnRTaXplOiAxNixcbiAgICB9LFxuICAgIGxnVGV4dDoge1xuICAgICAgZm9udFNpemU6IDE4LFxuICAgIH0sXG5cbiAgICAvLyBMb2FkaW5nIGluZGljYXRvclxuICAgIGxvYWRpbmdJbmRpY2F0b3I6IHtcbiAgICAgIG1hcmdpblJpZ2h0OiA4LFxuICAgIH0sXG4gIH0pO1xuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7O0FBZ0JBLElBQUFBLE1BQUEsR0FBQUMsdUJBQUEsQ0FBQUMsT0FBQTtBQUNBLElBQUFDLFlBQUEsR0FBQUQsT0FBQTtBQVVBLElBQUFFLGFBQUEsR0FBQUYsT0FBQTtBQUF1RCxJQUFBRyxXQUFBLEdBQUFILE9BQUE7QUFBQSxJQUFBSSxTQUFBO0FBQUEsU0FBQUwsd0JBQUFNLENBQUEsRUFBQUMsQ0FBQSw2QkFBQUMsT0FBQSxNQUFBQyxDQUFBLE9BQUFELE9BQUEsSUFBQUUsQ0FBQSxPQUFBRixPQUFBLFlBQUFSLHVCQUFBLFlBQUFBLHdCQUFBTSxDQUFBLEVBQUFDLENBQUEsU0FBQUEsQ0FBQSxJQUFBRCxDQUFBLElBQUFBLENBQUEsQ0FBQUssVUFBQSxTQUFBTCxDQUFBLE1BQUFNLENBQUEsRUFBQUMsQ0FBQSxFQUFBQyxDQUFBLEtBQUFDLFNBQUEsUUFBQUMsT0FBQSxFQUFBVixDQUFBLGlCQUFBQSxDQUFBLHVCQUFBQSxDQUFBLHlCQUFBQSxDQUFBLFNBQUFRLENBQUEsTUFBQUYsQ0FBQSxHQUFBTCxDQUFBLEdBQUFHLENBQUEsR0FBQUQsQ0FBQSxRQUFBRyxDQUFBLENBQUFLLEdBQUEsQ0FBQVgsQ0FBQSxVQUFBTSxDQUFBLENBQUFNLEdBQUEsQ0FBQVosQ0FBQSxHQUFBTSxDQUFBLENBQUFPLEdBQUEsQ0FBQWIsQ0FBQSxFQUFBUSxDQUFBLGNBQUFNLEVBQUEsSUFBQWQsQ0FBQSxnQkFBQWMsRUFBQSxPQUFBQyxjQUFBLENBQUFDLElBQUEsQ0FBQWhCLENBQUEsRUFBQWMsRUFBQSxPQUFBUCxDQUFBLElBQUFELENBQUEsR0FBQVcsTUFBQSxDQUFBQyxjQUFBLEtBQUFELE1BQUEsQ0FBQUUsd0JBQUEsQ0FBQW5CLENBQUEsRUFBQWMsRUFBQSxPQUFBUCxDQUFBLENBQUFLLEdBQUEsSUFBQUwsQ0FBQSxDQUFBTSxHQUFBLElBQUFQLENBQUEsQ0FBQUUsQ0FBQSxFQUFBTSxFQUFBLEVBQUFQLENBQUEsSUFBQUMsQ0FBQSxDQUFBTSxFQUFBLElBQUFkLENBQUEsQ0FBQWMsRUFBQSxXQUFBTixDQUFBLEtBQUFSLENBQUEsRUFBQUMsQ0FBQTtBQTJCaEQsSUFBTW1CLE1BQTZCLEdBQUFDLE9BQUEsQ0FBQUQsTUFBQSxHQUFHLFNBQWhDQSxNQUE2QkEsQ0FBQUUsSUFBQSxFQWVwQztFQUFBLElBQUFDLGVBQUEsRUFBQUMsZ0JBQUE7RUFBQSxJQWRKQyxRQUFRLEdBQUFILElBQUEsQ0FBUkcsUUFBUTtJQUNSQyxLQUFLLEdBQUFKLElBQUEsQ0FBTEksS0FBSztJQUFBQyxZQUFBLEdBQUFMLElBQUEsQ0FDTE0sT0FBTztJQUFQQSxPQUFPLEdBQUFELFlBQUEsY0FBRyxTQUFTLEdBQUFBLFlBQUE7SUFBQUUsU0FBQSxHQUFBUCxJQUFBLENBQ25CUSxJQUFJO0lBQUpBLElBQUksR0FBQUQsU0FBQSxjQUFHLElBQUksR0FBQUEsU0FBQTtJQUNYRSxLQUFLLEdBQUFULElBQUEsQ0FBTFMsS0FBSztJQUNMQyxPQUFPLEdBQUFWLElBQUEsQ0FBUFUsT0FBTztJQUFBQyxhQUFBLEdBQUFYLElBQUEsQ0FDUFksUUFBUTtJQUFSQSxRQUFRLEdBQUFELGFBQUEsY0FBRyxLQUFLLEdBQUFBLGFBQUE7SUFBQUUsWUFBQSxHQUFBYixJQUFBLENBQ2hCYyxPQUFPO0lBQVBBLE9BQU8sR0FBQUQsWUFBQSxjQUFHLEtBQUssR0FBQUEsWUFBQTtJQUFBRSxjQUFBLEdBQUFmLElBQUEsQ0FDZmdCLFNBQVM7SUFBVEEsU0FBUyxHQUFBRCxjQUFBLGNBQUcsS0FBSyxHQUFBQSxjQUFBO0lBQ2pCRSxRQUFRLEdBQUFqQixJQUFBLENBQVJpQixRQUFRO0lBQ1JDLFNBQVMsR0FBQWxCLElBQUEsQ0FBVGtCLFNBQVM7SUFDVEMsa0JBQWtCLEdBQUFuQixJQUFBLENBQWxCbUIsa0JBQWtCO0lBQ2xCQyxpQkFBaUIsR0FBQXBCLElBQUEsQ0FBakJvQixpQkFBaUI7SUFDZEMsS0FBSyxPQUFBQyx5QkFBQSxDQUFBbEMsT0FBQSxFQUFBWSxJQUFBLEVBQUF2QixTQUFBO0VBRVIsSUFBQThDLFNBQUEsR0FBbUIsSUFBQUMsc0JBQVEsRUFBQyxDQUFDO0lBQXJCQyxNQUFNLEdBQUFGLFNBQUEsQ0FBTkUsTUFBTTtFQUNkLElBQUFDLFNBQUEsR0FBa0MsSUFBQUMsZUFBUSxFQUFDLEtBQUssQ0FBQztJQUFBQyxVQUFBLE9BQUFDLGVBQUEsQ0FBQXpDLE9BQUEsRUFBQXNDLFNBQUE7SUFBMUNJLFNBQVMsR0FBQUYsVUFBQTtJQUFFRyxZQUFZLEdBQUFILFVBQUE7RUFFOUIsSUFBTUksTUFBTSxHQUFHQyxZQUFZLENBQUNSLE1BQU0sQ0FBQztFQUduQyxJQUFNUyxhQUFhLEdBQUcvQixRQUFRLElBQUlDLEtBQUs7RUFHdkMsSUFBTStCLGdCQUFnQixHQUFHLENBQ3ZCSCxNQUFNLENBQUNJLElBQUksRUFDWEosTUFBTSxDQUFDMUIsT0FBTyxDQUFDLEVBQ2YwQixNQUFNLENBQUN4QixJQUFJLENBQUMsRUFDWlEsU0FBUyxJQUFJZ0IsTUFBTSxDQUFDaEIsU0FBUyxFQUM3QkosUUFBUSxJQUFJb0IsTUFBTSxDQUFDcEIsUUFBUSxFQUMzQkUsT0FBTyxJQUFJa0IsTUFBTSxDQUFDbEIsT0FBTyxFQUN6QmdCLFNBQVMsSUFBSUUsTUFBTSxDQUFDSyxPQUFPLEVBQzNCNUIsS0FBSyxDQUNOLENBQUM2QixNQUFNLENBQUNDLE9BQU8sQ0FBQztFQUVqQixJQUFNQyxjQUFjLEdBQUcsQ0FDckJSLE1BQU0sQ0FBQ1MsSUFBSSxFQUNYVCxNQUFNLENBQUMsR0FBRzFCLE9BQU8sTUFBTSxDQUFDLEVBQ3hCMEIsTUFBTSxDQUFDLEdBQUd4QixJQUFJLE1BQU0sQ0FBQyxFQUNyQkksUUFBUSxJQUFJb0IsTUFBTSxDQUFDVSxZQUFZLENBQ2hDLENBQUNKLE1BQU0sQ0FBQ0MsT0FBTyxDQUFDO0VBRWpCLElBQU1JLHFCQUFxQixHQUFHL0IsUUFBUSxJQUFJRSxPQUFPO0VBRWpELE9BQ0UsSUFBQXRDLFdBQUEsQ0FBQW9FLElBQUEsRUFBQ3RFLFlBQUEsQ0FBQXVFLGdCQUFnQixFQUFBbEQsTUFBQSxDQUFBbUQsTUFBQTtJQUNmckMsS0FBSyxFQUFFMEIsZ0JBQWlCO0lBQ3hCekIsT0FBTyxFQUFFaUMscUJBQXFCLEdBQUdJLFNBQVMsR0FBR3JDLE9BQVE7SUFDckRFLFFBQVEsRUFBRStCLHFCQUFzQjtJQUNoQ0ssT0FBTyxFQUFFLFNBQVRBLE9BQU9BLENBQUE7TUFBQSxPQUFRakIsWUFBWSxDQUFDLElBQUksQ0FBQztJQUFBLENBQUM7SUFDbENrQixNQUFNLEVBQUUsU0FBUkEsTUFBTUEsQ0FBQTtNQUFBLE9BQVFsQixZQUFZLENBQUMsS0FBSyxDQUFDO0lBQUEsQ0FBQztJQUNsQ21CLGlCQUFpQixFQUFDLFFBQVE7SUFDMUJDLGtCQUFrQixFQUFFO01BQ2xCdkMsUUFBUSxFQUFFK0IscUJBQXFCO01BQy9CUyxJQUFJLEVBQUV0QztJQUNSLENBQUU7SUFDRkssa0JBQWtCLEVBQ2hCQSxrQkFBa0IsS0FDakIsT0FBT2UsYUFBYSxLQUFLLFFBQVEsR0FBR0EsYUFBYSxHQUFHLFFBQVEsQ0FDOUQ7SUFDRGQsaUJBQWlCLEVBQUVBO0VBQWtCLEdBQ2pDQyxLQUFLO0lBQUFsQixRQUFBLEdBQ1JXLE9BQU8sSUFDTixJQUFBdEMsV0FBQSxDQUFBNkUsR0FBQSxFQUFDL0UsWUFBQSxDQUFBZ0YsaUJBQWlCO01BQ2hCOUMsSUFBSSxFQUFDLE9BQU87TUFDWitDLEtBQUssRUFDSGpELE9BQU8sS0FBSyxTQUFTLEdBQ2pCLEVBQUFMLGVBQUEsR0FBQXdCLE1BQU0sQ0FBQytCLE9BQU8scUJBQWR2RCxlQUFBLENBQWdCd0QsUUFBUSxLQUFJLFNBQVMsR0FDckMsRUFBQXZELGdCQUFBLEdBQUF1QixNQUFNLENBQUMrQixPQUFPLHFCQUFkdEQsZ0JBQUEsQ0FBZ0J3RCxJQUFJLEtBQUksU0FDN0I7TUFDRGpELEtBQUssRUFBRXVCLE1BQU0sQ0FBQzJCO0lBQWlCLENBQ2hDLENBQ0YsRUFFQSxDQUFDN0MsT0FBTyxJQUFJRyxRQUFRLElBQUksSUFBQXpDLFdBQUEsQ0FBQTZFLEdBQUEsRUFBQTdFLFdBQUEsQ0FBQW9GLFFBQUE7TUFBQXpELFFBQUEsRUFBR2M7SUFBUSxDQUFHLENBQUMsRUFFdkNpQixhQUFhLElBQ1osSUFBQTFELFdBQUEsQ0FBQTZFLEdBQUEsRUFBQy9FLFlBQUEsQ0FBQXVGLElBQUk7TUFBQ3BELEtBQUssRUFBRStCLGNBQWU7TUFBQ3NCLGFBQWEsRUFBRSxDQUFFO01BQUEzRCxRQUFBLEVBQzNDK0I7SUFBYSxDQUNWLENBQ1AsRUFFQSxDQUFDcEIsT0FBTyxJQUFJSSxTQUFTLElBQUksSUFBQTFDLFdBQUEsQ0FBQTZFLEdBQUEsRUFBQTdFLFdBQUEsQ0FBQW9GLFFBQUE7TUFBQXpELFFBQUEsRUFBR2U7SUFBUyxDQUFHLENBQUM7RUFBQSxFQUMxQixDQUFDO0FBRXZCLENBQUM7QUFHRCxJQUFNZSxZQUFZLEdBQUcsU0FBZkEsWUFBWUEsQ0FBSVIsTUFBVztFQUFBLElBQUFzQyxnQkFBQSxFQUFBQyxlQUFBLEVBQUFDLGdCQUFBLEVBQUFDLGdCQUFBLEVBQUFDLGdCQUFBLEVBQUFDLGdCQUFBLEVBQUFDLFlBQUEsRUFBQUMsZ0JBQUEsRUFBQUMsZ0JBQUEsRUFBQUMsYUFBQTtFQUFBLE9BQy9CQyx1QkFBVSxDQUFDQyxNQUFNLENBQUM7SUFDaEJ0QyxJQUFJLEVBQUU7TUFDSnVDLGFBQWEsRUFBRSxLQUFLO01BQ3BCQyxVQUFVLEVBQUUsUUFBUTtNQUNwQkMsY0FBYyxFQUFFLFFBQVE7TUFDeEJDLFlBQVksRUFBRSxDQUFDO01BQ2ZDLFNBQVMsRUFBRSxFQUFFO01BQ2JDLFFBQVEsRUFBRSxFQUFFO01BQ1pDLEdBQUcsRUFBRTtJQUNQLENBQUM7SUFHRHpCLE9BQU8sRUFBRTtNQUNQMEIsZUFBZSxFQUFFLEVBQUFuQixnQkFBQSxHQUFBdEMsTUFBTSxDQUFDK0IsT0FBTyxxQkFBZE8sZ0JBQUEsQ0FBZ0IzRSxPQUFPLEtBQUksU0FBUztNQUNyRCtGLGlCQUFpQixFQUFFLEVBQUU7TUFDckJDLGVBQWUsRUFBRTtJQUNuQixDQUFDO0lBQ0RDLFNBQVMsRUFBRTtNQUNUSCxlQUFlLEVBQUUsRUFBQWxCLGVBQUEsR0FBQXZDLE1BQU0sQ0FBQzZELE9BQU8scUJBQWR0QixlQUFBLENBQWdCcUIsU0FBUyxLQUFJLFNBQVM7TUFDdkRFLFdBQVcsRUFBRSxDQUFDO01BQ2RDLFdBQVcsRUFBRSxFQUFBdkIsZ0JBQUEsR0FBQXhDLE1BQU0sQ0FBQytCLE9BQU8scUJBQWRTLGdCQUFBLENBQWdCN0UsT0FBTyxLQUFJLFNBQVM7TUFDakQrRixpQkFBaUIsRUFBRSxFQUFFO01BQ3JCQyxlQUFlLEVBQUU7SUFDbkIsQ0FBQztJQUNESyxPQUFPLEVBQUU7TUFDUFAsZUFBZSxFQUFFLGFBQWE7TUFDOUJLLFdBQVcsRUFBRSxDQUFDO01BQ2RDLFdBQVcsRUFBRSxFQUFBdEIsZ0JBQUEsR0FBQXpDLE1BQU0sQ0FBQytCLE9BQU8scUJBQWRVLGdCQUFBLENBQWdCOUUsT0FBTyxLQUFJLFNBQVM7TUFDakQrRixpQkFBaUIsRUFBRSxFQUFFO01BQ3JCQyxlQUFlLEVBQUU7SUFDbkIsQ0FBQztJQUNETSxPQUFPLEVBQUU7TUFDUFIsZUFBZSxFQUFFLGFBQWE7TUFDOUJDLGlCQUFpQixFQUFFLENBQUM7TUFDcEJDLGVBQWUsRUFBRTtJQUNuQixDQUFDO0lBR0RPLEVBQUUsRUFBRTtNQUNGQyxNQUFNLEVBQUUsRUFBRTtNQUNWVCxpQkFBaUIsRUFBRSxFQUFFO01BQ3JCQyxlQUFlLEVBQUU7SUFDbkIsQ0FBQztJQUNEUyxFQUFFLEVBQUU7TUFDRkQsTUFBTSxFQUFFLEVBQUU7TUFDVlQsaUJBQWlCLEVBQUUsRUFBRTtNQUNyQkMsZUFBZSxFQUFFO0lBQ25CLENBQUM7SUFDRFUsRUFBRSxFQUFFO01BQ0ZGLE1BQU0sRUFBRSxFQUFFO01BQ1ZULGlCQUFpQixFQUFFLEVBQUU7TUFDckJDLGVBQWUsRUFBRTtJQUNuQixDQUFDO0lBR0R4RSxRQUFRLEVBQUU7TUFDUm1GLE9BQU8sRUFBRTtJQUNYLENBQUM7SUFDRGpGLE9BQU8sRUFBRTtNQUNQaUYsT0FBTyxFQUFFO0lBQ1gsQ0FBQztJQUNEMUQsT0FBTyxFQUFFO01BQ1BrRCxXQUFXLEVBQUUsQ0FBQztNQUNkQyxXQUFXLEVBQUUsRUFBQXJCLGdCQUFBLEdBQUExQyxNQUFNLENBQUMrQixPQUFPLHFCQUFkVyxnQkFBQSxDQUFnQjZCLEtBQUssS0FBSTtJQUN4QyxDQUFDO0lBQ0RoRixTQUFTLEVBQUU7TUFDVGlGLEtBQUssRUFBRTtJQUNULENBQUM7SUFHRHhELElBQUksRUFBRTtNQUNKeUQsVUFBVSxFQUFFLFFBQVE7TUFDcEJDLFVBQVUsRUFBRSxLQUFLO01BQ2pCQyxTQUFTLEVBQUUsUUFBUTtNQUNuQkMsUUFBUSxFQUFFLEVBQUU7TUFDWkMsVUFBVSxFQUFFO0lBQ2QsQ0FBQztJQUNEQyxXQUFXLEVBQUU7TUFDWGhELEtBQUssRUFBRSxFQUFBYSxnQkFBQSxHQUFBM0MsTUFBTSxDQUFDK0IsT0FBTyxxQkFBZFksZ0JBQUEsQ0FBZ0JYLFFBQVEsS0FBSTtJQUNyQyxDQUFDO0lBQ0QrQyxhQUFhLEVBQUU7TUFDYmpELEtBQUssRUFBRSxFQUFBYyxZQUFBLEdBQUE1QyxNQUFNLENBQUNnQixJQUFJLHFCQUFYNEIsWUFBQSxDQUFhYixPQUFPLEtBQUk7SUFDakMsQ0FBQztJQUNEaUQsV0FBVyxFQUFFO01BQ1hsRCxLQUFLLEVBQUUsRUFBQWUsZ0JBQUEsR0FBQTdDLE1BQU0sQ0FBQytCLE9BQU8scUJBQWRjLGdCQUFBLENBQWdCbEYsT0FBTyxLQUFJO0lBQ3BDLENBQUM7SUFDRHNILFdBQVcsRUFBRTtNQUNYbkQsS0FBSyxFQUFFLEVBQUFnQixnQkFBQSxHQUFBOUMsTUFBTSxDQUFDK0IsT0FBTyxxQkFBZGUsZ0JBQUEsQ0FBZ0JuRixPQUFPLEtBQUk7SUFDcEMsQ0FBQztJQUNEc0QsWUFBWSxFQUFFO01BQ1phLEtBQUssRUFBRSxFQUFBaUIsYUFBQSxHQUFBL0MsTUFBTSxDQUFDZ0IsSUFBSSxxQkFBWCtCLGFBQUEsQ0FBYW1DLFFBQVEsS0FBSTtJQUNsQyxDQUFDO0lBR0RDLE1BQU0sRUFBRTtNQUNOUCxRQUFRLEVBQUU7SUFDWixDQUFDO0lBQ0RRLE1BQU0sRUFBRTtNQUNOUixRQUFRLEVBQUU7SUFDWixDQUFDO0lBQ0RTLE1BQU0sRUFBRTtNQUNOVCxRQUFRLEVBQUU7SUFDWixDQUFDO0lBR0QxQyxnQkFBZ0IsRUFBRTtNQUNoQm9ELFdBQVcsRUFBRTtJQUNmO0VBQ0YsQ0FBQyxDQUFDO0FBQUEiLCJpZ25vcmVMaXN0IjpbXX0=