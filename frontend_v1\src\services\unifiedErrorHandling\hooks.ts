/**
 * Unified Error Handling Hooks
 * 
 * React hooks for the unified error handling system that provide
 * consistent error handling patterns across all components.
 * 
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

import { useCallback, useEffect, useRef, useState } from 'react';
import { AppState } from 'react-native';

import {
  UnifiedError,
  ErrorReport,
  ErrorContext,
  ErrorType,
  ErrorSeverity,
  ErrorListener
} from './types';
// Import service directly to avoid circular dependency
import { unifiedErrorHandlingService } from './UnifiedErrorHandlingService';

// Hook Options
export interface UseUnifiedErrorHandlingOptions {
  component?: string;
  screen?: string;
  enableAutoRecovery?: boolean;
  enableUserFeedback?: boolean;
  maxRetries?: number;
  retryDelay?: number;
  onError?: (error: ErrorReport) => void;
  onRecovery?: (error: ErrorReport) => void;
}

// Hook Return Type
export interface UseUnifiedErrorHandlingResult {
  // Error State
  error: ErrorReport | null;
  isError: boolean;
  hasRecovered: boolean;
  retryCount: number;
  
  // Error Handling Methods
  handleError: (error: Error | string, context?: Partial<ErrorContext>) => Promise<ErrorReport>;
  handleNetworkError: (error: Error, context?: Partial<ErrorContext>) => Promise<ErrorReport>;
  handleAuthError: (error: Error, context?: Partial<ErrorContext>) => Promise<ErrorReport>;
  handleValidationError: (error: Error, context?: Partial<ErrorContext>) => Promise<ErrorReport>;
  handleWebSocketError: (error: Error, context?: Partial<ErrorContext>) => Promise<ErrorReport>;
  
  // Async Operation Wrapper
  withErrorHandling: <T>(
    operation: () => Promise<T>,
    context?: Partial<ErrorContext>
  ) => Promise<{ data: T | null; error: ErrorReport | null }>;
  
  // Recovery Methods
  retry: () => Promise<void>;
  clearError: () => void;
  
  // Utility Methods
  addBreadcrumb: (message: string, category?: string, data?: Record<string, any>) => void;
}

/**
 * Main unified error handling hook
 */
export const useUnifiedErrorHandling = (
  options: UseUnifiedErrorHandlingOptions = {}
): UseUnifiedErrorHandlingResult => {
  const {
    component,
    screen,
    enableAutoRecovery = true,
    enableUserFeedback = true,
    maxRetries = 3,
    retryDelay = 2000,
    onError,
    onRecovery
  } = options;

  // State
  const [error, setError] = useState<ErrorReport | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [hasRecovered, setHasRecovered] = useState(false);
  
  // Refs
  const lastOperationRef = useRef<(() => Promise<any>) | null>(null);
  const contextRef = useRef<Partial<ErrorContext>>({
    component,
    screen
  });

  // Update context when options change
  useEffect(() => {
    contextRef.current = {
      component,
      screen
    };
  }, [component, screen]);

  // Error listener for auto-recovery
  useEffect(() => {
    if (!enableAutoRecovery) return;

    const unsubscribe = unifiedErrorHandlingService.addErrorListener((errorReport) => {
      if (errorReport.recovered && errorReport.id === error?.id) {
        setHasRecovered(true);
        setError(null);
        
        if (onRecovery) {
          onRecovery(errorReport);
        }
      }
    });

    return unsubscribe;
  }, [enableAutoRecovery, error?.id, onRecovery]);

  // Main error handler
  const handleError = useCallback(async (
    errorInput: Error | string,
    context: Partial<ErrorContext> = {}
  ): Promise<ErrorReport> => {
    const fullContext = {
      ...contextRef.current,
      ...context
    };

    const errorReport = await unifiedErrorHandlingService.handleError(
      errorInput,
      fullContext
    );

    setError(errorReport);
    setHasRecovered(false);

    if (onError) {
      onError(errorReport);
    }

    return errorReport;
  }, [onError]);

  // Specific error handlers
  const handleNetworkError = useCallback(async (
    error: Error,
    context: Partial<ErrorContext> = {}
  ): Promise<ErrorReport> => {
    return handleError(error, { ...context, type: ErrorType.NETWORK });
  }, [handleError]);

  const handleAuthError = useCallback(async (
    error: Error,
    context: Partial<ErrorContext> = {}
  ): Promise<ErrorReport> => {
    return handleError(error, { ...context, type: ErrorType.AUTHENTICATION });
  }, [handleError]);

  const handleValidationError = useCallback(async (
    error: Error,
    context: Partial<ErrorContext> = {}
  ): Promise<ErrorReport> => {
    return handleError(error, { ...context, type: ErrorType.VALIDATION });
  }, [handleError]);

  const handleWebSocketError = useCallback(async (
    error: Error,
    context: Partial<ErrorContext> = {}
  ): Promise<ErrorReport> => {
    return handleError(error, { ...context, type: ErrorType.WEBSOCKET });
  }, [handleError]);

  // Async operation wrapper
  const withErrorHandling = useCallback(async <T>(
    operation: () => Promise<T>,
    context: Partial<ErrorContext> = {}
  ): Promise<{ data: T | null; error: ErrorReport | null }> => {
    try {
      // Store operation for retry
      lastOperationRef.current = operation;
      
      const data = await operation();
      
      // Clear any previous errors on success
      if (error) {
        setError(null);
        setRetryCount(0);
        setHasRecovered(false);
      }
      
      return { data, error: null };
    } catch (err) {
      const errorReport = await handleError(err as Error, context);
      return { data: null, error: errorReport };
    }
  }, [error, handleError]);

  // Retry mechanism
  const retry = useCallback(async (): Promise<void> => {
    if (!lastOperationRef.current || retryCount >= maxRetries) {
      return;
    }

    try {
      setRetryCount(prev => prev + 1);
      
      // Progressive delay
      const delay = retryDelay * retryCount;
      await new Promise(resolve => setTimeout(resolve, delay));
      
      await lastOperationRef.current();
      
      // Success - clear error
      setError(null);
      setRetryCount(0);
      setHasRecovered(true);
    } catch (err) {
      // Retry failed - update error
      await handleError(err as Error, { action: 'retry' });
    }
  }, [retryCount, maxRetries, retryDelay, handleError]);

  // Clear error
  const clearError = useCallback((): void => {
    setError(null);
    setRetryCount(0);
    setHasRecovered(false);
    lastOperationRef.current = null;
  }, []);

  // Add breadcrumb
  const addBreadcrumb = useCallback((
    message: string,
    category: string = 'user_action',
    data?: Record<string, any>
  ): void => {
    unifiedErrorHandlingService.addBreadcrumb({
      timestamp: Date.now(),
      category: category as any,
      message,
      level: 'info',
      data: {
        component,
        screen,
        ...data
      }
    });
  }, [component, screen]);

  return {
    // State
    error,
    isError: !!error,
    hasRecovered,
    retryCount,
    
    // Methods
    handleError,
    handleNetworkError,
    handleAuthError,
    handleValidationError,
    handleWebSocketError,
    withErrorHandling,
    retry,
    clearError,
    addBreadcrumb
  };
};

/**
 * Hook for error metrics and monitoring
 */
export const useErrorMetrics = () => {
  const [metrics, setMetrics] = useState(unifiedErrorHandlingService.getMetrics());

  useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(unifiedErrorHandlingService.getMetrics());
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return metrics;
};

/**
 * Hook for global error listening
 */
export const useGlobalErrorListener = (listener: ErrorListener) => {
  useEffect(() => {
    const unsubscribe = unifiedErrorHandlingService.addErrorListener(listener);
    return unsubscribe;
  }, [listener]);
};

/**
 * Hook for error boundary integration
 */
export const useErrorBoundary = (componentName: string) => {
  const { handleError } = useUnifiedErrorHandling({
    component: componentName
  });

  const onError = useCallback((error: Error, errorInfo: any) => {
    handleError(error, {
      component: componentName,
      action: 'component_error',
      additionalData: {
        componentStack: errorInfo.componentStack
      }
    });
  }, [handleError, componentName]);

  return { onError };
};

/**
 * Hook for API error handling
 */
export const useApiErrorHandling = (apiName?: string) => {
  const { handleError, handleNetworkError, handleAuthError } = useUnifiedErrorHandling({
    component: 'ApiClient',
    screen: apiName
  });

  const handleApiError = useCallback(async (
    error: any,
    endpoint?: string,
    method?: string
  ): Promise<ErrorReport> => {
    const context = {
      endpoint,
      method,
      action: 'api_call',
      additionalData: {
        statusCode: error?.response?.status,
        responseData: error?.response?.data
      }
    };

    // Handle specific HTTP status codes
    if (error?.response?.status === 401) {
      return handleAuthError(new Error('Unauthorized'), context);
    }

    if (error?.response?.status >= 500) {
      return handleError(new Error('Server Error'), {
        ...context,
        type: ErrorType.SERVER_ERROR
      });
    }

    if (!error?.response || error?.code === 'NETWORK_ERROR') {
      return handleNetworkError(new Error('Network Error'), context);
    }

    return handleError(error, context);
  }, [handleError, handleNetworkError, handleAuthError]);

  return { handleApiError };
};

/**
 * Hook for form validation error handling
 */
export const useFormErrorHandling = (formName: string) => {
  const { handleValidationError } = useUnifiedErrorHandling({
    component: 'Form',
    screen: formName
  });

  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});

  const handleFieldError = useCallback(async (
    field: string,
    error: string | Error
  ): Promise<void> => {
    const errorMessage = typeof error === 'string' ? error : error.message;
    
    setFieldErrors(prev => ({
      ...prev,
      [field]: errorMessage
    }));

    await handleValidationError(
      new Error(`Validation error in field: ${field}`),
      {
        action: 'field_validation',
        additionalData: { field, errorMessage }
      }
    );
  }, [handleValidationError]);

  const clearFieldError = useCallback((field: string): void => {
    setFieldErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  const clearAllErrors = useCallback((): void => {
    setFieldErrors({});
  }, []);

  return {
    fieldErrors,
    handleFieldError,
    clearFieldError,
    clearAllErrors
  };
};

/**
 * Hook for WebSocket error handling
 */
export const useWebSocketErrorHandling = (connectionName: string) => {
  const { handleWebSocketError } = useUnifiedErrorHandling({
    component: 'WebSocket',
    screen: connectionName
  });

  const handleConnectionError = useCallback(async (error: Error): Promise<ErrorReport> => {
    return handleWebSocketError(error, {
      action: 'connection_error',
      additionalData: { connectionName }
    });
  }, [handleWebSocketError, connectionName]);

  const handleMessageError = useCallback(async (error: Error, message?: any): Promise<ErrorReport> => {
    return handleWebSocketError(error, {
      action: 'message_error',
      additionalData: { connectionName, message }
    });
  }, [handleWebSocketError, connectionName]);

  return {
    handleConnectionError,
    handleMessageError
  };
};
