08df2801d7d2374b33a7e90a4d95c044
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _processColor = _interopRequireDefault(require("../../StyleSheet/processColor"));
var _Platform = _interopRequireDefault(require("../../Utilities/Platform"));
var _NativeStatusBarManagerAndroid = _interopRequireDefault(require("./NativeStatusBarManagerAndroid"));
var _NativeStatusBarManagerIOS = _interopRequireDefault(require("./NativeStatusBarManagerIOS"));
var _invariant = _interopRequireDefault(require("invariant"));
var React = _interopRequireWildcard(require("react"));
var _StatusBar, _NativeStatusBarManag;
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function mergePropsStack(propsStack, defaultValues) {
  return propsStack.reduce(function (prev, cur) {
    for (var prop in cur) {
      if (cur[prop] != null) {
        prev[prop] = cur[prop];
      }
    }
    return prev;
  }, Object.assign({}, defaultValues));
}
function createStackEntry(props) {
  var _props$animated, _props$showHideTransi;
  var animated = (_props$animated = props.animated) != null ? _props$animated : false;
  var showHideTransition = (_props$showHideTransi = props.showHideTransition) != null ? _props$showHideTransi : 'fade';
  return {
    backgroundColor: props.backgroundColor != null ? {
      value: props.backgroundColor,
      animated: animated
    } : null,
    barStyle: props.barStyle != null ? {
      value: props.barStyle,
      animated: animated
    } : null,
    translucent: props.translucent,
    hidden: props.hidden != null ? {
      value: props.hidden,
      animated: animated,
      transition: showHideTransition
    } : null,
    networkActivityIndicatorVisible: props.networkActivityIndicatorVisible
  };
}
var StatusBar = function (_React$Component) {
  function StatusBar() {
    var _this;
    (0, _classCallCheck2.default)(this, StatusBar);
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _callSuper(this, StatusBar, [].concat(args));
    _this._stackEntry = null;
    return _this;
  }
  (0, _inherits2.default)(StatusBar, _React$Component);
  return (0, _createClass2.default)(StatusBar, [{
    key: "componentDidMount",
    value: function componentDidMount() {
      this._stackEntry = StatusBar.pushStackEntry(this.props);
    }
  }, {
    key: "componentWillUnmount",
    value: function componentWillUnmount() {
      if (this._stackEntry != null) {
        StatusBar.popStackEntry(this._stackEntry);
      }
    }
  }, {
    key: "componentDidUpdate",
    value: function componentDidUpdate() {
      if (this._stackEntry != null) {
        this._stackEntry = StatusBar.replaceStackEntry(this._stackEntry, this.props);
      }
    }
  }, {
    key: "render",
    value: function render() {
      return null;
    }
  }], [{
    key: "setHidden",
    value: function setHidden(hidden, animation) {
      animation = animation || 'none';
      StatusBar._defaultProps.hidden.value = hidden;
      if (_Platform.default.OS === 'ios') {
        _NativeStatusBarManagerIOS.default.setHidden(hidden, animation);
      } else if (_Platform.default.OS === 'android') {
        _NativeStatusBarManagerAndroid.default.setHidden(hidden);
      }
    }
  }, {
    key: "setBarStyle",
    value: function setBarStyle(style, animated) {
      animated = animated || false;
      StatusBar._defaultProps.barStyle.value = style;
      if (_Platform.default.OS === 'ios') {
        _NativeStatusBarManagerIOS.default.setStyle(style, animated);
      } else if (_Platform.default.OS === 'android') {
        _NativeStatusBarManagerAndroid.default.setStyle(style);
      }
    }
  }, {
    key: "setNetworkActivityIndicatorVisible",
    value: function setNetworkActivityIndicatorVisible(visible) {
      if (_Platform.default.OS !== 'ios') {
        console.warn('`setNetworkActivityIndicatorVisible` is only available on iOS');
        return;
      }
      StatusBar._defaultProps.networkActivityIndicatorVisible = visible;
      _NativeStatusBarManagerIOS.default.setNetworkActivityIndicatorVisible(visible);
    }
  }, {
    key: "setBackgroundColor",
    value: function setBackgroundColor(color, animated) {
      if (_Platform.default.OS !== 'android') {
        console.warn('`setBackgroundColor` is only available on Android');
        return;
      }
      animated = animated || false;
      StatusBar._defaultProps.backgroundColor.value = color;
      var processedColor = (0, _processColor.default)(color);
      if (processedColor == null) {
        console.warn(`\`StatusBar.setBackgroundColor\`: Color ${String(color)} parsed to null or undefined`);
        return;
      }
      (0, _invariant.default)(typeof processedColor === 'number', 'Unexpected color given for StatusBar.setBackgroundColor');
      _NativeStatusBarManagerAndroid.default.setColor(processedColor, animated);
    }
  }, {
    key: "setTranslucent",
    value: function setTranslucent(translucent) {
      if (_Platform.default.OS !== 'android') {
        console.warn('`setTranslucent` is only available on Android');
        return;
      }
      StatusBar._defaultProps.translucent = translucent;
      _NativeStatusBarManagerAndroid.default.setTranslucent(translucent);
    }
  }, {
    key: "pushStackEntry",
    value: function pushStackEntry(props) {
      var entry = createStackEntry(props);
      StatusBar._propsStack.push(entry);
      StatusBar._updatePropsStack();
      return entry;
    }
  }, {
    key: "popStackEntry",
    value: function popStackEntry(entry) {
      var index = StatusBar._propsStack.indexOf(entry);
      if (index !== -1) {
        StatusBar._propsStack.splice(index, 1);
      }
      StatusBar._updatePropsStack();
    }
  }, {
    key: "replaceStackEntry",
    value: function replaceStackEntry(entry, props) {
      var newEntry = createStackEntry(props);
      var index = StatusBar._propsStack.indexOf(entry);
      if (index !== -1) {
        StatusBar._propsStack[index] = newEntry;
      }
      StatusBar._updatePropsStack();
      return newEntry;
    }
  }]);
}(React.Component);
_StatusBar = StatusBar;
StatusBar._propsStack = [];
StatusBar._defaultProps = createStackEntry({
  backgroundColor: _Platform.default.OS === 'android' ? (_NativeStatusBarManag = _NativeStatusBarManagerAndroid.default.getConstants().DEFAULT_BACKGROUND_COLOR) != null ? _NativeStatusBarManag : 'black' : 'black',
  barStyle: 'default',
  translucent: false,
  hidden: false,
  networkActivityIndicatorVisible: false
});
StatusBar._updateImmediate = null;
StatusBar._currentValues = null;
StatusBar.currentHeight = _Platform.default.OS === 'android' ? _NativeStatusBarManagerAndroid.default.getConstants().HEIGHT : null;
StatusBar._updatePropsStack = function () {
  clearImmediate(_StatusBar._updateImmediate);
  _StatusBar._updateImmediate = setImmediate(function () {
    var oldProps = _StatusBar._currentValues;
    var mergedProps = mergePropsStack(_StatusBar._propsStack, _StatusBar._defaultProps);
    if (_Platform.default.OS === 'ios') {
      var _oldProps$barStyle, _oldProps$hidden;
      if (!oldProps || ((_oldProps$barStyle = oldProps.barStyle) == null ? void 0 : _oldProps$barStyle.value) !== mergedProps.barStyle.value) {
        _NativeStatusBarManagerIOS.default.setStyle(mergedProps.barStyle.value, mergedProps.barStyle.animated || false);
      }
      if (!oldProps || ((_oldProps$hidden = oldProps.hidden) == null ? void 0 : _oldProps$hidden.value) !== mergedProps.hidden.value) {
        _NativeStatusBarManagerIOS.default.setHidden(mergedProps.hidden.value, mergedProps.hidden.animated ? mergedProps.hidden.transition : 'none');
      }
      if (!oldProps || oldProps.networkActivityIndicatorVisible !== mergedProps.networkActivityIndicatorVisible) {
        _NativeStatusBarManagerIOS.default.setNetworkActivityIndicatorVisible(mergedProps.networkActivityIndicatorVisible);
      }
    } else if (_Platform.default.OS === 'android') {
      var _oldProps$hidden2;
      _NativeStatusBarManagerAndroid.default.setStyle(mergedProps.barStyle.value);
      var processedColor = (0, _processColor.default)(mergedProps.backgroundColor.value);
      if (processedColor == null) {
        console.warn(`\`StatusBar._updatePropsStack\`: Color ${mergedProps.backgroundColor.value} parsed to null or undefined`);
      } else {
        (0, _invariant.default)(typeof processedColor === 'number', 'Unexpected color given in StatusBar._updatePropsStack');
        _NativeStatusBarManagerAndroid.default.setColor(processedColor, mergedProps.backgroundColor.animated);
      }
      if (!oldProps || ((_oldProps$hidden2 = oldProps.hidden) == null ? void 0 : _oldProps$hidden2.value) !== mergedProps.hidden.value) {
        _NativeStatusBarManagerAndroid.default.setHidden(mergedProps.hidden.value);
      }
      if (!oldProps || oldProps.translucent !== mergedProps.translucent || mergedProps.translucent) {
        _NativeStatusBarManagerAndroid.default.setTranslucent(mergedProps.translucent);
      }
    }
    _StatusBar._currentValues = mergedProps;
  });
};
var _default = exports.default = StatusBar;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfcHJvY2Vzc0NvbG9yIiwiX2ludGVyb3BSZXF1aXJlRGVmYXVsdCIsInJlcXVpcmUiLCJfUGxhdGZvcm0iLCJfTmF0aXZlU3RhdHVzQmFyTWFuYWdlckFuZHJvaWQiLCJfTmF0aXZlU3RhdHVzQmFyTWFuYWdlcklPUyIsIl9pbnZhcmlhbnQiLCJSZWFjdCIsIl9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkIiwiX1N0YXR1c0JhciIsIl9OYXRpdmVTdGF0dXNCYXJNYW5hZyIsImUiLCJ0IiwiV2Vha01hcCIsInIiLCJuIiwiX19lc01vZHVsZSIsIm8iLCJpIiwiZiIsIl9fcHJvdG9fXyIsImRlZmF1bHQiLCJoYXMiLCJnZXQiLCJzZXQiLCJfdCIsImhhc093blByb3BlcnR5IiwiY2FsbCIsIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yIiwiX2NhbGxTdXBlciIsIl9nZXRQcm90b3R5cGVPZjIiLCJfcG9zc2libGVDb25zdHJ1Y3RvclJldHVybjIiLCJfaXNOYXRpdmVSZWZsZWN0Q29uc3RydWN0IiwiUmVmbGVjdCIsImNvbnN0cnVjdCIsImNvbnN0cnVjdG9yIiwiYXBwbHkiLCJCb29sZWFuIiwicHJvdG90eXBlIiwidmFsdWVPZiIsIm1lcmdlUHJvcHNTdGFjayIsInByb3BzU3RhY2siLCJkZWZhdWx0VmFsdWVzIiwicmVkdWNlIiwicHJldiIsImN1ciIsInByb3AiLCJhc3NpZ24iLCJjcmVhdGVTdGFja0VudHJ5IiwicHJvcHMiLCJfcHJvcHMkYW5pbWF0ZWQiLCJfcHJvcHMkc2hvd0hpZGVUcmFuc2kiLCJhbmltYXRlZCIsInNob3dIaWRlVHJhbnNpdGlvbiIsImJhY2tncm91bmRDb2xvciIsInZhbHVlIiwiYmFyU3R5bGUiLCJ0cmFuc2x1Y2VudCIsImhpZGRlbiIsInRyYW5zaXRpb24iLCJuZXR3b3JrQWN0aXZpdHlJbmRpY2F0b3JWaXNpYmxlIiwiU3RhdHVzQmFyIiwiX1JlYWN0JENvbXBvbmVudCIsIl90aGlzIiwiX2NsYXNzQ2FsbENoZWNrMiIsIl9sZW4iLCJhcmd1bWVudHMiLCJsZW5ndGgiLCJhcmdzIiwiQXJyYXkiLCJfa2V5IiwiY29uY2F0IiwiX3N0YWNrRW50cnkiLCJfaW5oZXJpdHMyIiwiX2NyZWF0ZUNsYXNzMiIsImtleSIsImNvbXBvbmVudERpZE1vdW50IiwicHVzaFN0YWNrRW50cnkiLCJjb21wb25lbnRXaWxsVW5tb3VudCIsInBvcFN0YWNrRW50cnkiLCJjb21wb25lbnREaWRVcGRhdGUiLCJyZXBsYWNlU3RhY2tFbnRyeSIsInJlbmRlciIsInNldEhpZGRlbiIsImFuaW1hdGlvbiIsIl9kZWZhdWx0UHJvcHMiLCJQbGF0Zm9ybSIsIk9TIiwiTmF0aXZlU3RhdHVzQmFyTWFuYWdlcklPUyIsIk5hdGl2ZVN0YXR1c0Jhck1hbmFnZXJBbmRyb2lkIiwic2V0QmFyU3R5bGUiLCJzdHlsZSIsInNldFN0eWxlIiwic2V0TmV0d29ya0FjdGl2aXR5SW5kaWNhdG9yVmlzaWJsZSIsInZpc2libGUiLCJjb25zb2xlIiwid2FybiIsInNldEJhY2tncm91bmRDb2xvciIsImNvbG9yIiwicHJvY2Vzc2VkQ29sb3IiLCJwcm9jZXNzQ29sb3IiLCJTdHJpbmciLCJpbnZhcmlhbnQiLCJzZXRDb2xvciIsInNldFRyYW5zbHVjZW50IiwiZW50cnkiLCJfcHJvcHNTdGFjayIsInB1c2giLCJfdXBkYXRlUHJvcHNTdGFjayIsImluZGV4IiwiaW5kZXhPZiIsInNwbGljZSIsIm5ld0VudHJ5IiwiQ29tcG9uZW50IiwiZ2V0Q29uc3RhbnRzIiwiREVGQVVMVF9CQUNLR1JPVU5EX0NPTE9SIiwiX3VwZGF0ZUltbWVkaWF0ZSIsIl9jdXJyZW50VmFsdWVzIiwiY3VycmVudEhlaWdodCIsIkhFSUdIVCIsImNsZWFySW1tZWRpYXRlIiwic2V0SW1tZWRpYXRlIiwib2xkUHJvcHMiLCJtZXJnZWRQcm9wcyIsIl9vbGRQcm9wcyRiYXJTdHlsZSIsIl9vbGRQcm9wcyRoaWRkZW4iLCJfb2xkUHJvcHMkaGlkZGVuMiIsIl9kZWZhdWx0IiwiZXhwb3J0cyJdLCJzb3VyY2VzIjpbIlN0YXR1c0Jhci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICogQGZvcm1hdFxuICogQGZsb3dcbiAqL1xuXG5pbXBvcnQgdHlwZSB7Q29sb3JWYWx1ZX0gZnJvbSAnLi4vLi4vU3R5bGVTaGVldC9TdHlsZVNoZWV0JztcblxuaW1wb3J0IHByb2Nlc3NDb2xvciBmcm9tICcuLi8uLi9TdHlsZVNoZWV0L3Byb2Nlc3NDb2xvcic7XG5pbXBvcnQgUGxhdGZvcm0gZnJvbSAnLi4vLi4vVXRpbGl0aWVzL1BsYXRmb3JtJztcbmltcG9ydCBOYXRpdmVTdGF0dXNCYXJNYW5hZ2VyQW5kcm9pZCBmcm9tICcuL05hdGl2ZVN0YXR1c0Jhck1hbmFnZXJBbmRyb2lkJztcbmltcG9ydCBOYXRpdmVTdGF0dXNCYXJNYW5hZ2VySU9TIGZyb20gJy4vTmF0aXZlU3RhdHVzQmFyTWFuYWdlcklPUyc7XG5pbXBvcnQgaW52YXJpYW50IGZyb20gJ2ludmFyaWFudCc7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbi8qKlxuICogU3RhdHVzIGJhciBzdHlsZVxuICovXG5leHBvcnQgdHlwZSBTdGF0dXNCYXJTdHlsZSA9ICRLZXlzPHtcbiAgLyoqXG4gICAqIERlZmF1bHQgc3RhdHVzIGJhciBzdHlsZSAoZGFyayBmb3IgaU9TLCBsaWdodCBmb3IgQW5kcm9pZClcbiAgICovXG4gIGRlZmF1bHQ6IHN0cmluZyxcbiAgLyoqXG4gICAqIERhcmsgYmFja2dyb3VuZCwgd2hpdGUgdGV4dHMgYW5kIGljb25zXG4gICAqL1xuICAnbGlnaHQtY29udGVudCc6IHN0cmluZyxcbiAgLyoqXG4gICAqIExpZ2h0IGJhY2tncm91bmQsIGRhcmsgdGV4dHMgYW5kIGljb25zXG4gICAqL1xuICAnZGFyay1jb250ZW50Jzogc3RyaW5nLFxuICAuLi5cbn0+O1xuXG4vKipcbiAqIFN0YXR1cyBiYXIgYW5pbWF0aW9uXG4gKi9cbmV4cG9ydCB0eXBlIFN0YXR1c0JhckFuaW1hdGlvbiA9ICRLZXlzPHtcbiAgLyoqXG4gICAqIE5vIGFuaW1hdGlvblxuICAgKi9cbiAgbm9uZTogc3RyaW5nLFxuICAvKipcbiAgICogRmFkZSBhbmltYXRpb25cbiAgICovXG4gIGZhZGU6IHN0cmluZyxcbiAgLyoqXG4gICAqIFNsaWRlIGFuaW1hdGlvblxuICAgKi9cbiAgc2xpZGU6IHN0cmluZyxcbiAgLi4uXG59PjtcblxuZXhwb3J0IHR5cGUgU3RhdHVzQmFyUHJvcHNBbmRyb2lkID0gJFJlYWRPbmx5PHtcbiAgLyoqXG4gICAqIFRoZSBiYWNrZ3JvdW5kIGNvbG9yIG9mIHRoZSBzdGF0dXMgYmFyLlxuICAgKiBAcGxhdGZvcm0gYW5kcm9pZFxuICAgKi9cbiAgYmFja2dyb3VuZENvbG9yPzogP0NvbG9yVmFsdWUsXG4gIC8qKlxuICAgKiBJZiB0aGUgc3RhdHVzIGJhciBpcyB0cmFuc2x1Y2VudC5cbiAgICogV2hlbiB0cmFuc2x1Y2VudCBpcyBzZXQgdG8gdHJ1ZSwgdGhlIGFwcCB3aWxsIGRyYXcgdW5kZXIgdGhlIHN0YXR1cyBiYXIuXG4gICAqIFRoaXMgaXMgdXNlZnVsIHdoZW4gdXNpbmcgYSBzZW1pIHRyYW5zcGFyZW50IHN0YXR1cyBiYXIgY29sb3IuXG4gICAqXG4gICAqIEBwbGF0Zm9ybSBhbmRyb2lkXG4gICAqL1xuICB0cmFuc2x1Y2VudD86ID9ib29sZWFuLFxufT47XG5cbmV4cG9ydCB0eXBlIFN0YXR1c0JhclByb3BzSU9TID0gJFJlYWRPbmx5PHtcbiAgLyoqXG4gICAqIElmIHRoZSBuZXR3b3JrIGFjdGl2aXR5IGluZGljYXRvciBzaG91bGQgYmUgdmlzaWJsZS5cbiAgICpcbiAgICogQHBsYXRmb3JtIGlvc1xuICAgKi9cbiAgbmV0d29ya0FjdGl2aXR5SW5kaWNhdG9yVmlzaWJsZT86ID9ib29sZWFuLFxuICAvKipcbiAgICogVGhlIHRyYW5zaXRpb24gZWZmZWN0IHdoZW4gc2hvd2luZyBhbmQgaGlkaW5nIHRoZSBzdGF0dXMgYmFyIHVzaW5nIHRoZSBgaGlkZGVuYFxuICAgKiBwcm9wLiBEZWZhdWx0cyB0byAnZmFkZScuXG4gICAqXG4gICAqIEBwbGF0Zm9ybSBpb3NcbiAgICovXG4gIHNob3dIaWRlVHJhbnNpdGlvbj86ID8oJ2ZhZGUnIHwgJ3NsaWRlJyB8ICdub25lJyksXG59PjtcblxudHlwZSBTdGF0dXNCYXJCYXNlUHJvcHMgPSAkUmVhZE9ubHk8e1xuICAvKipcbiAgICogSWYgdGhlIHN0YXR1cyBiYXIgaXMgaGlkZGVuLlxuICAgKi9cbiAgaGlkZGVuPzogP2Jvb2xlYW4sXG4gIC8qKlxuICAgKiBJZiB0aGUgdHJhbnNpdGlvbiBiZXR3ZWVuIHN0YXR1cyBiYXIgcHJvcGVydHkgY2hhbmdlcyBzaG91bGQgYmUgYW5pbWF0ZWQuXG4gICAqIFN1cHBvcnRlZCBmb3IgYmFja2dyb3VuZENvbG9yLCBiYXJTdHlsZSBhbmQgaGlkZGVuLlxuICAgKi9cbiAgYW5pbWF0ZWQ/OiA/Ym9vbGVhbixcbiAgLyoqXG4gICAqIFNldHMgdGhlIGNvbG9yIG9mIHRoZSBzdGF0dXMgYmFyIHRleHQuXG4gICAqL1xuICBiYXJTdHlsZT86ID8oJ2RlZmF1bHQnIHwgJ2xpZ2h0LWNvbnRlbnQnIHwgJ2RhcmstY29udGVudCcpLFxufT47XG5cbmV4cG9ydCB0eXBlIFN0YXR1c0JhclByb3BzID0gJFJlYWRPbmx5PHtcbiAgLi4uU3RhdHVzQmFyUHJvcHNBbmRyb2lkLFxuICAuLi5TdGF0dXNCYXJQcm9wc0lPUyxcbiAgLi4uU3RhdHVzQmFyQmFzZVByb3BzLFxufT47XG5cbnR5cGUgU3RhY2tQcm9wcyA9IHtcbiAgYmFja2dyb3VuZENvbG9yOiA/e1xuICAgIHZhbHVlOiBTdGF0dXNCYXJQcm9wc1snYmFja2dyb3VuZENvbG9yJ10sXG4gICAgYW5pbWF0ZWQ6IGJvb2xlYW4sXG4gIH0sXG4gIGJhclN0eWxlOiA/e1xuICAgIHZhbHVlOiBTdGF0dXNCYXJQcm9wc1snYmFyU3R5bGUnXSxcbiAgICBhbmltYXRlZDogYm9vbGVhbixcbiAgfSxcbiAgdHJhbnNsdWNlbnQ6IFN0YXR1c0JhclByb3BzWyd0cmFuc2x1Y2VudCddLFxuICBoaWRkZW46ID97XG4gICAgdmFsdWU6IGJvb2xlYW4sXG4gICAgYW5pbWF0ZWQ6IGJvb2xlYW4sXG4gICAgdHJhbnNpdGlvbjogU3RhdHVzQmFyUHJvcHNbJ3Nob3dIaWRlVHJhbnNpdGlvbiddLFxuICB9LFxuICBuZXR3b3JrQWN0aXZpdHlJbmRpY2F0b3JWaXNpYmxlOiBTdGF0dXNCYXJQcm9wc1snbmV0d29ya0FjdGl2aXR5SW5kaWNhdG9yVmlzaWJsZSddLFxufTtcblxuLyoqXG4gKiBNZXJnZXMgdGhlIHByb3Agc3RhY2sgd2l0aCB0aGUgZGVmYXVsdCB2YWx1ZXMuXG4gKi9cbmZ1bmN0aW9uIG1lcmdlUHJvcHNTdGFjayhcbiAgcHJvcHNTdGFjazogQXJyYXk8T2JqZWN0PixcbiAgZGVmYXVsdFZhbHVlczogT2JqZWN0LFxuKTogT2JqZWN0IHtcbiAgcmV0dXJuIHByb3BzU3RhY2sucmVkdWNlKFxuICAgIChwcmV2LCBjdXIpID0+IHtcbiAgICAgIGZvciAoY29uc3QgcHJvcCBpbiBjdXIpIHtcbiAgICAgICAgaWYgKGN1cltwcm9wXSAhPSBudWxsKSB7XG4gICAgICAgICAgcHJldltwcm9wXSA9IGN1cltwcm9wXTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgcmV0dXJuIHByZXY7XG4gICAgfSxcbiAgICB7Li4uZGVmYXVsdFZhbHVlc30sXG4gICk7XG59XG5cbi8qKlxuICogUmV0dXJucyBhbiBvYmplY3QgdG8gaW5zZXJ0IGluIHRoZSBwcm9wcyBzdGFjayBmcm9tIHRoZSBwcm9wc1xuICogYW5kIHRoZSB0cmFuc2l0aW9uL2FuaW1hdGlvbiBpbmZvLlxuICovXG5mdW5jdGlvbiBjcmVhdGVTdGFja0VudHJ5KHByb3BzOiBTdGF0dXNCYXJQcm9wcyk6IFN0YWNrUHJvcHMge1xuICBjb25zdCBhbmltYXRlZCA9IHByb3BzLmFuaW1hdGVkID8/IGZhbHNlO1xuICBjb25zdCBzaG93SGlkZVRyYW5zaXRpb24gPSBwcm9wcy5zaG93SGlkZVRyYW5zaXRpb24gPz8gJ2ZhZGUnO1xuICByZXR1cm4ge1xuICAgIGJhY2tncm91bmRDb2xvcjpcbiAgICAgIHByb3BzLmJhY2tncm91bmRDb2xvciAhPSBudWxsXG4gICAgICAgID8ge1xuICAgICAgICAgICAgdmFsdWU6IHByb3BzLmJhY2tncm91bmRDb2xvcixcbiAgICAgICAgICAgIGFuaW1hdGVkLFxuICAgICAgICAgIH1cbiAgICAgICAgOiBudWxsLFxuICAgIGJhclN0eWxlOlxuICAgICAgcHJvcHMuYmFyU3R5bGUgIT0gbnVsbFxuICAgICAgICA/IHtcbiAgICAgICAgICAgIHZhbHVlOiBwcm9wcy5iYXJTdHlsZSxcbiAgICAgICAgICAgIGFuaW1hdGVkLFxuICAgICAgICAgIH1cbiAgICAgICAgOiBudWxsLFxuICAgIHRyYW5zbHVjZW50OiBwcm9wcy50cmFuc2x1Y2VudCxcbiAgICBoaWRkZW46XG4gICAgICBwcm9wcy5oaWRkZW4gIT0gbnVsbFxuICAgICAgICA/IHtcbiAgICAgICAgICAgIHZhbHVlOiBwcm9wcy5oaWRkZW4sXG4gICAgICAgICAgICBhbmltYXRlZCxcbiAgICAgICAgICAgIHRyYW5zaXRpb246IHNob3dIaWRlVHJhbnNpdGlvbixcbiAgICAgICAgICB9XG4gICAgICAgIDogbnVsbCxcbiAgICBuZXR3b3JrQWN0aXZpdHlJbmRpY2F0b3JWaXNpYmxlOiBwcm9wcy5uZXR3b3JrQWN0aXZpdHlJbmRpY2F0b3JWaXNpYmxlLFxuICB9O1xufVxuXG4vKipcbiAqIENvbXBvbmVudCB0byBjb250cm9sIHRoZSBhcHAgc3RhdHVzIGJhci5cbiAqXG4gKiBJdCBpcyBwb3NzaWJsZSB0byBoYXZlIG11bHRpcGxlIGBTdGF0dXNCYXJgIGNvbXBvbmVudHMgbW91bnRlZCBhdCB0aGUgc2FtZVxuICogdGltZS4gVGhlIHByb3BzIHdpbGwgYmUgbWVyZ2VkIGluIHRoZSBvcmRlciB0aGUgYFN0YXR1c0JhcmAgY29tcG9uZW50cyB3ZXJlXG4gKiBtb3VudGVkLlxuICpcbiAqICMjIyBJbXBlcmF0aXZlIEFQSVxuICpcbiAqIEZvciBjYXNlcyB3aGVyZSB1c2luZyBhIGNvbXBvbmVudCBpcyBub3QgaWRlYWwsIHRoZXJlIGFyZSBzdGF0aWMgbWV0aG9kc1xuICogdG8gbWFuaXB1bGF0ZSB0aGUgYFN0YXR1c0JhcmAgZGlzcGxheSBzdGFjay4gVGhlc2UgbWV0aG9kcyBoYXZlIHRoZSBzYW1lXG4gKiBiZWhhdmlvciBhcyBtb3VudGluZyBhbmQgdW5tb3VudGluZyBhIGBTdGF0dXNCYXJgIGNvbXBvbmVudC5cbiAqXG4gKiBGb3IgZXhhbXBsZSwgeW91IGNhbiBjYWxsIGBTdGF0dXNCYXIucHVzaFN0YWNrRW50cnlgIHRvIHVwZGF0ZSB0aGUgc3RhdHVzIGJhclxuICogYmVmb3JlIGxhdW5jaGluZyBhIHRoaXJkLXBhcnR5IG5hdGl2ZSBVSSBjb21wb25lbnQsIGFuZCB0aGVuIGNhbGxcbiAqIGBTdGF0dXNCYXIucG9wU3RhY2tFbnRyeWAgd2hlbiBjb21wbGV0ZWQuXG4gKlxuICogYGBgXG4gKiBjb25zdCBvcGVuVGhpcmRQYXJ0eUJ1Z1JlcG9ydGVyID0gYXN5bmMgKCkgPT4ge1xuICogICAvLyBUaGUgYnVnIHJlcG9ydGVyIGhhcyBhIGRhcmsgYmFja2dyb3VuZCwgc28gd2UgcHVzaCBhIG5ldyBzdGF0dXMgYmFyIHN0eWxlLlxuICogICBjb25zdCBzdGFja0VudHJ5ID0gU3RhdHVzQmFyLnB1c2hTdGFja0VudHJ5KHtiYXJTdHlsZTogJ2xpZ2h0LWNvbnRlbnQnfSk7XG4gKlxuICogICAvLyBgb3BlbmAgcmV0dXJucyBhIHByb21pc2UgdGhhdCByZXNvbHZlcyB3aGVuIHRoZSBVSSBpcyBkaXNtaXNzZWQuXG4gKiAgIGF3YWl0IEJ1Z1JlcG9ydGVyLm9wZW4oKTtcbiAqXG4gKiAgIC8vIERvbid0IGZvcmdldCB0byBjYWxsIGBwb3BTdGFja0VudHJ5YCB3aGVuIHlvdSdyZSBkb25lLlxuICogICBTdGF0dXNCYXIucG9wU3RhY2tFbnRyeShzdGFja0VudHJ5KTtcbiAqIH07XG4gKiBgYGBcbiAqXG4gKiBUaGVyZSBpcyBhIGxlZ2FjeSBpbXBlcmF0aXZlIEFQSSB0aGF0IGVuYWJsZXMgeW91IHRvIG1hbnVhbGx5IHVwZGF0ZSB0aGVcbiAqIHN0YXR1cyBiYXIgc3R5bGVzLiBIb3dldmVyLCB0aGUgbGVnYWN5IEFQSSBkb2VzIG5vdCB1cGRhdGUgdGhlIGludGVybmFsXG4gKiBgU3RhdHVzQmFyYCBkaXNwbGF5IHN0YWNrLCB3aGljaCBtZWFucyB0aGF0IGFueSBjaGFuZ2VzIHdpbGwgYmUgb3ZlcnJpZGRlblxuICogd2hlbmV2ZXIgYSBgU3RhdHVzQmFyYCBjb21wb25lbnQgaXMgbW91bnRlZCBvciB1bm1vdW50ZWQuXG4gKlxuICogSXQgaXMgc3Ryb25nbHkgYWR2aXNlZCB0aGF0IHlvdSB1c2UgYHB1c2hTdGFja0VudHJ5YCwgYHBvcFN0YWNrRW50cnlgLCBvclxuICogYHJlcGxhY2VTdGFja0VudHJ5YCBpbnN0ZWFkIG9mIHRoZSBzdGF0aWMgbWV0aG9kcyBiZWdpbm5pbmcgd2l0aCBgc2V0YC5cbiAqXG4gKiAjIyMgQ29uc3RhbnRzXG4gKlxuICogYGN1cnJlbnRIZWlnaHRgIChBbmRyb2lkIG9ubHkpIFRoZSBoZWlnaHQgb2YgdGhlIHN0YXR1cyBiYXIuXG4gKi9cbmNsYXNzIFN0YXR1c0JhciBleHRlbmRzIFJlYWN0LkNvbXBvbmVudDxTdGF0dXNCYXJQcm9wcz4ge1xuICBzdGF0aWMgX3Byb3BzU3RhY2s6IEFycmF5PFN0YWNrUHJvcHM+ID0gW107XG5cbiAgc3RhdGljIF9kZWZhdWx0UHJvcHM6IGFueSA9IGNyZWF0ZVN0YWNrRW50cnkoe1xuICAgIGJhY2tncm91bmRDb2xvcjpcbiAgICAgIFBsYXRmb3JtLk9TID09PSAnYW5kcm9pZCdcbiAgICAgICAgPyBOYXRpdmVTdGF0dXNCYXJNYW5hZ2VyQW5kcm9pZC5nZXRDb25zdGFudHMoKVxuICAgICAgICAgICAgLkRFRkFVTFRfQkFDS0dST1VORF9DT0xPUiA/PyAnYmxhY2snXG4gICAgICAgIDogJ2JsYWNrJyxcbiAgICBiYXJTdHlsZTogJ2RlZmF1bHQnLFxuICAgIHRyYW5zbHVjZW50OiBmYWxzZSxcbiAgICBoaWRkZW46IGZhbHNlLFxuICAgIG5ldHdvcmtBY3Rpdml0eUluZGljYXRvclZpc2libGU6IGZhbHNlLFxuICB9KTtcblxuICAvLyBUaW1lciBmb3IgdXBkYXRpbmcgdGhlIG5hdGl2ZSBtb2R1bGUgdmFsdWVzIGF0IHRoZSBlbmQgb2YgdGhlIGZyYW1lLlxuICBzdGF0aWMgX3VwZGF0ZUltbWVkaWF0ZTogP251bWJlciA9IG51bGw7XG5cbiAgLy8gVGhlIGN1cnJlbnQgbWVyZ2VkIHZhbHVlcyBmcm9tIHRoZSBwcm9wcyBzdGFjay5cbiAgc3RhdGljIF9jdXJyZW50VmFsdWVzOiA/U3RhY2tQcm9wcyA9IG51bGw7XG5cbiAgLy8gVE9ETyhqYW5pYyk6IFByb3ZpZGUgYSByZWFsIEFQSSB0byBkZWFsIHdpdGggc3RhdHVzIGJhciBoZWlnaHQuIFNlZSB0aGVcbiAgLy8gZGlzY3Vzc2lvbiBpbiAjNjE5NS5cbiAgLyoqXG4gICAqIFRoZSBjdXJyZW50IGhlaWdodCBvZiB0aGUgc3RhdHVzIGJhciBvbiB0aGUgZGV2aWNlLlxuICAgKlxuICAgKiBAcGxhdGZvcm0gYW5kcm9pZFxuICAgKi9cbiAgc3RhdGljIGN1cnJlbnRIZWlnaHQ6ID9udW1iZXIgPVxuICAgIFBsYXRmb3JtLk9TID09PSAnYW5kcm9pZCdcbiAgICAgID8gTmF0aXZlU3RhdHVzQmFyTWFuYWdlckFuZHJvaWQuZ2V0Q29uc3RhbnRzKCkuSEVJR0hUXG4gICAgICA6IG51bGw7XG5cbiAgLy8gUHJvdmlkZSBhbiBpbXBlcmF0aXZlIEFQSSBhcyBzdGF0aWMgZnVuY3Rpb25zIG9mIHRoZSBjb21wb25lbnQuXG4gIC8vIFNlZSB0aGUgY29ycmVzcG9uZGluZyBwcm9wIGZvciBtb3JlIGRldGFpbC5cblxuICAvKipcbiAgICogU2hvdyBvciBoaWRlIHRoZSBzdGF0dXMgYmFyXG4gICAqIEBwYXJhbSBoaWRkZW4gSGlkZSB0aGUgc3RhdHVzIGJhci5cbiAgICogQHBhcmFtIGFuaW1hdGlvbiBPcHRpb25hbCBhbmltYXRpb24gd2hlblxuICAgKiAgICBjaGFuZ2luZyB0aGUgc3RhdHVzIGJhciBoaWRkZW4gcHJvcGVydHkuXG4gICAqL1xuICBzdGF0aWMgc2V0SGlkZGVuKGhpZGRlbjogYm9vbGVhbiwgYW5pbWF0aW9uPzogU3RhdHVzQmFyQW5pbWF0aW9uKSB7XG4gICAgYW5pbWF0aW9uID0gYW5pbWF0aW9uIHx8ICdub25lJztcbiAgICBTdGF0dXNCYXIuX2RlZmF1bHRQcm9wcy5oaWRkZW4udmFsdWUgPSBoaWRkZW47XG4gICAgaWYgKFBsYXRmb3JtLk9TID09PSAnaW9zJykge1xuICAgICAgTmF0aXZlU3RhdHVzQmFyTWFuYWdlcklPUy5zZXRIaWRkZW4oaGlkZGVuLCBhbmltYXRpb24pO1xuICAgIH0gZWxzZSBpZiAoUGxhdGZvcm0uT1MgPT09ICdhbmRyb2lkJykge1xuICAgICAgTmF0aXZlU3RhdHVzQmFyTWFuYWdlckFuZHJvaWQuc2V0SGlkZGVuKGhpZGRlbik7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFNldCB0aGUgc3RhdHVzIGJhciBzdHlsZVxuICAgKiBAcGFyYW0gc3R5bGUgU3RhdHVzIGJhciBzdHlsZSB0byBzZXRcbiAgICogQHBhcmFtIGFuaW1hdGVkIEFuaW1hdGUgdGhlIHN0eWxlIGNoYW5nZS5cbiAgICovXG4gIHN0YXRpYyBzZXRCYXJTdHlsZShzdHlsZTogU3RhdHVzQmFyU3R5bGUsIGFuaW1hdGVkPzogYm9vbGVhbikge1xuICAgIGFuaW1hdGVkID0gYW5pbWF0ZWQgfHwgZmFsc2U7XG4gICAgU3RhdHVzQmFyLl9kZWZhdWx0UHJvcHMuYmFyU3R5bGUudmFsdWUgPSBzdHlsZTtcbiAgICBpZiAoUGxhdGZvcm0uT1MgPT09ICdpb3MnKSB7XG4gICAgICBOYXRpdmVTdGF0dXNCYXJNYW5hZ2VySU9TLnNldFN0eWxlKHN0eWxlLCBhbmltYXRlZCk7XG4gICAgfSBlbHNlIGlmIChQbGF0Zm9ybS5PUyA9PT0gJ2FuZHJvaWQnKSB7XG4gICAgICBOYXRpdmVTdGF0dXNCYXJNYW5hZ2VyQW5kcm9pZC5zZXRTdHlsZShzdHlsZSk7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIERFUFJFQ0FURUQgLSBUaGUgc3RhdHVzIGJhciBuZXR3b3JrIGFjdGl2aXR5IGluZGljYXRvciBpcyBub3Qgc3VwcG9ydGVkIGluIGlPUyAxMyBhbmQgbGF0ZXIuIFRoaXMgd2lsbCBiZSByZW1vdmVkIGluIGEgZnV0dXJlIHJlbGVhc2UuXG4gICAqIEBwYXJhbSB2aXNpYmxlIFNob3cgdGhlIGluZGljYXRvci5cbiAgICpcbiAgICogQGRlcHJlY2F0ZWRcbiAgICovXG4gIHN0YXRpYyBzZXROZXR3b3JrQWN0aXZpdHlJbmRpY2F0b3JWaXNpYmxlKHZpc2libGU6IGJvb2xlYW4pIHtcbiAgICBpZiAoUGxhdGZvcm0uT1MgIT09ICdpb3MnKSB7XG4gICAgICBjb25zb2xlLndhcm4oXG4gICAgICAgICdgc2V0TmV0d29ya0FjdGl2aXR5SW5kaWNhdG9yVmlzaWJsZWAgaXMgb25seSBhdmFpbGFibGUgb24gaU9TJyxcbiAgICAgICk7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIFN0YXR1c0Jhci5fZGVmYXVsdFByb3BzLm5ldHdvcmtBY3Rpdml0eUluZGljYXRvclZpc2libGUgPSB2aXNpYmxlO1xuICAgIE5hdGl2ZVN0YXR1c0Jhck1hbmFnZXJJT1Muc2V0TmV0d29ya0FjdGl2aXR5SW5kaWNhdG9yVmlzaWJsZSh2aXNpYmxlKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBTZXQgdGhlIGJhY2tncm91bmQgY29sb3IgZm9yIHRoZSBzdGF0dXMgYmFyXG4gICAqIEBwYXJhbSBjb2xvciBCYWNrZ3JvdW5kIGNvbG9yLlxuICAgKiBAcGFyYW0gYW5pbWF0ZWQgQW5pbWF0ZSB0aGUgc3R5bGUgY2hhbmdlLlxuICAgKi9cbiAgc3RhdGljIHNldEJhY2tncm91bmRDb2xvcihjb2xvcjogQ29sb3JWYWx1ZSwgYW5pbWF0ZWQ/OiBib29sZWFuKTogdm9pZCB7XG4gICAgaWYgKFBsYXRmb3JtLk9TICE9PSAnYW5kcm9pZCcpIHtcbiAgICAgIGNvbnNvbGUud2FybignYHNldEJhY2tncm91bmRDb2xvcmAgaXMgb25seSBhdmFpbGFibGUgb24gQW5kcm9pZCcpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBhbmltYXRlZCA9IGFuaW1hdGVkIHx8IGZhbHNlO1xuICAgIFN0YXR1c0Jhci5fZGVmYXVsdFByb3BzLmJhY2tncm91bmRDb2xvci52YWx1ZSA9IGNvbG9yO1xuXG4gICAgY29uc3QgcHJvY2Vzc2VkQ29sb3IgPSBwcm9jZXNzQ29sb3IoY29sb3IpO1xuICAgIGlmIChwcm9jZXNzZWRDb2xvciA9PSBudWxsKSB7XG4gICAgICBjb25zb2xlLndhcm4oXG4gICAgICAgIGBcXGBTdGF0dXNCYXIuc2V0QmFja2dyb3VuZENvbG9yXFxgOiBDb2xvciAke1N0cmluZyhjb2xvcil9IHBhcnNlZCB0byBudWxsIG9yIHVuZGVmaW5lZGAsXG4gICAgICApO1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBpbnZhcmlhbnQoXG4gICAgICB0eXBlb2YgcHJvY2Vzc2VkQ29sb3IgPT09ICdudW1iZXInLFxuICAgICAgJ1VuZXhwZWN0ZWQgY29sb3IgZ2l2ZW4gZm9yIFN0YXR1c0Jhci5zZXRCYWNrZ3JvdW5kQ29sb3InLFxuICAgICk7XG5cbiAgICBOYXRpdmVTdGF0dXNCYXJNYW5hZ2VyQW5kcm9pZC5zZXRDb2xvcihwcm9jZXNzZWRDb2xvciwgYW5pbWF0ZWQpO1xuICB9XG5cbiAgLyoqXG4gICAqIENvbnRyb2wgdGhlIHRyYW5zbHVjZW5jeSBvZiB0aGUgc3RhdHVzIGJhclxuICAgKiBAcGFyYW0gdHJhbnNsdWNlbnQgU2V0IGFzIHRyYW5zbHVjZW50LlxuICAgKi9cbiAgc3RhdGljIHNldFRyYW5zbHVjZW50KHRyYW5zbHVjZW50OiBib29sZWFuKSB7XG4gICAgaWYgKFBsYXRmb3JtLk9TICE9PSAnYW5kcm9pZCcpIHtcbiAgICAgIGNvbnNvbGUud2FybignYHNldFRyYW5zbHVjZW50YCBpcyBvbmx5IGF2YWlsYWJsZSBvbiBBbmRyb2lkJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIFN0YXR1c0Jhci5fZGVmYXVsdFByb3BzLnRyYW5zbHVjZW50ID0gdHJhbnNsdWNlbnQ7XG4gICAgTmF0aXZlU3RhdHVzQmFyTWFuYWdlckFuZHJvaWQuc2V0VHJhbnNsdWNlbnQodHJhbnNsdWNlbnQpO1xuICB9XG5cbiAgLyoqXG4gICAqIFB1c2ggYSBTdGF0dXNCYXIgZW50cnkgb250byB0aGUgc3RhY2suXG4gICAqIFRoZSByZXR1cm4gdmFsdWUgc2hvdWxkIGJlIHBhc3NlZCB0byBgcG9wU3RhY2tFbnRyeWAgd2hlbiBjb21wbGV0ZS5cbiAgICpcbiAgICogQHBhcmFtIHByb3BzIE9iamVjdCBjb250YWluaW5nIHRoZSBTdGF0dXNCYXIgcHJvcHMgdG8gdXNlIGluIHRoZSBzdGFjayBlbnRyeS5cbiAgICovXG4gIHN0YXRpYyBwdXNoU3RhY2tFbnRyeShwcm9wczogU3RhdHVzQmFyUHJvcHMpOiBTdGFja1Byb3BzIHtcbiAgICBjb25zdCBlbnRyeSA9IGNyZWF0ZVN0YWNrRW50cnkocHJvcHMpO1xuICAgIFN0YXR1c0Jhci5fcHJvcHNTdGFjay5wdXNoKGVudHJ5KTtcbiAgICBTdGF0dXNCYXIuX3VwZGF0ZVByb3BzU3RhY2soKTtcbiAgICByZXR1cm4gZW50cnk7XG4gIH1cblxuICAvKipcbiAgICogUG9wIGEgU3RhdHVzQmFyIGVudHJ5IGZyb20gdGhlIHN0YWNrLlxuICAgKlxuICAgKiBAcGFyYW0gZW50cnkgRW50cnkgcmV0dXJuZWQgZnJvbSBgcHVzaFN0YWNrRW50cnlgLlxuICAgKi9cbiAgc3RhdGljIHBvcFN0YWNrRW50cnkoZW50cnk6IFN0YWNrUHJvcHMpIHtcbiAgICBjb25zdCBpbmRleCA9IFN0YXR1c0Jhci5fcHJvcHNTdGFjay5pbmRleE9mKGVudHJ5KTtcbiAgICBpZiAoaW5kZXggIT09IC0xKSB7XG4gICAgICBTdGF0dXNCYXIuX3Byb3BzU3RhY2suc3BsaWNlKGluZGV4LCAxKTtcbiAgICB9XG4gICAgU3RhdHVzQmFyLl91cGRhdGVQcm9wc1N0YWNrKCk7XG4gIH1cblxuICAvKipcbiAgICogUmVwbGFjZSBhbiBleGlzdGluZyBTdGF0dXNCYXIgc3RhY2sgZW50cnkgd2l0aCBuZXcgcHJvcHMuXG4gICAqXG4gICAqIEBwYXJhbSBlbnRyeSBFbnRyeSByZXR1cm5lZCBmcm9tIGBwdXNoU3RhY2tFbnRyeWAgdG8gcmVwbGFjZS5cbiAgICogQHBhcmFtIHByb3BzIE9iamVjdCBjb250YWluaW5nIHRoZSBTdGF0dXNCYXIgcHJvcHMgdG8gdXNlIGluIHRoZSByZXBsYWNlbWVudCBzdGFjayBlbnRyeS5cbiAgICovXG4gIHN0YXRpYyByZXBsYWNlU3RhY2tFbnRyeShcbiAgICBlbnRyeTogU3RhY2tQcm9wcyxcbiAgICBwcm9wczogU3RhdHVzQmFyUHJvcHMsXG4gICk6IFN0YWNrUHJvcHMge1xuICAgIGNvbnN0IG5ld0VudHJ5ID0gY3JlYXRlU3RhY2tFbnRyeShwcm9wcyk7XG4gICAgY29uc3QgaW5kZXggPSBTdGF0dXNCYXIuX3Byb3BzU3RhY2suaW5kZXhPZihlbnRyeSk7XG4gICAgaWYgKGluZGV4ICE9PSAtMSkge1xuICAgICAgU3RhdHVzQmFyLl9wcm9wc1N0YWNrW2luZGV4XSA9IG5ld0VudHJ5O1xuICAgIH1cbiAgICBTdGF0dXNCYXIuX3VwZGF0ZVByb3BzU3RhY2soKTtcbiAgICByZXR1cm4gbmV3RW50cnk7XG4gIH1cblxuICBfc3RhY2tFbnRyeTogP1N0YWNrUHJvcHMgPSBudWxsO1xuXG4gIGNvbXBvbmVudERpZE1vdW50KCkge1xuICAgIC8vIEV2ZXJ5IHRpbWUgYSBTdGF0dXNCYXIgY29tcG9uZW50IGlzIG1vdW50ZWQsIHdlIHB1c2ggaXQncyBwcm9wIHRvIGEgc3RhY2tcbiAgICAvLyBhbmQgYWx3YXlzIHVwZGF0ZSB0aGUgbmF0aXZlIHN0YXR1cyBiYXIgd2l0aCB0aGUgcHJvcHMgZnJvbSB0aGUgdG9wIG9mIHRoZW5cbiAgICAvLyBzdGFjay4gVGhpcyBhbGxvd3MgaGF2aW5nIG11bHRpcGxlIFN0YXR1c0JhciBjb21wb25lbnRzIGFuZCB0aGUgb25lIHRoYXQgaXNcbiAgICAvLyBhZGRlZCBsYXN0IG9yIGlzIGRlZXBlciBpbiB0aGUgdmlldyBoaWVyYXJjaHkgd2lsbCBoYXZlIHByaW9yaXR5LlxuICAgIHRoaXMuX3N0YWNrRW50cnkgPSBTdGF0dXNCYXIucHVzaFN0YWNrRW50cnkodGhpcy5wcm9wcyk7XG4gIH1cblxuICBjb21wb25lbnRXaWxsVW5tb3VudCgpIHtcbiAgICAvLyBXaGVuIGEgU3RhdHVzQmFyIGlzIHVubW91bnRlZCwgcmVtb3ZlIGl0c2VsZiBmcm9tIHRoZSBzdGFjayBhbmQgdXBkYXRlXG4gICAgLy8gdGhlIG5hdGl2ZSBiYXIgd2l0aCB0aGUgbmV4dCBwcm9wcy5cbiAgICBpZiAodGhpcy5fc3RhY2tFbnRyeSAhPSBudWxsKSB7XG4gICAgICBTdGF0dXNCYXIucG9wU3RhY2tFbnRyeSh0aGlzLl9zdGFja0VudHJ5KTtcbiAgICB9XG4gIH1cblxuICBjb21wb25lbnREaWRVcGRhdGUoKSB7XG4gICAgaWYgKHRoaXMuX3N0YWNrRW50cnkgIT0gbnVsbCkge1xuICAgICAgdGhpcy5fc3RhY2tFbnRyeSA9IFN0YXR1c0Jhci5yZXBsYWNlU3RhY2tFbnRyeShcbiAgICAgICAgdGhpcy5fc3RhY2tFbnRyeSxcbiAgICAgICAgdGhpcy5wcm9wcyxcbiAgICAgICk7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFVwZGF0ZXMgdGhlIG5hdGl2ZSBzdGF0dXMgYmFyIHdpdGggdGhlIHByb3BzIGZyb20gdGhlIHN0YWNrLlxuICAgKi9cbiAgc3RhdGljIF91cGRhdGVQcm9wc1N0YWNrID0gKCkgPT4ge1xuICAgIC8vIFNlbmQgdGhlIHVwZGF0ZSB0byB0aGUgbmF0aXZlIG1vZHVsZSBvbmx5IG9uY2UgYXQgdGhlIGVuZCBvZiB0aGUgZnJhbWUuXG4gICAgY2xlYXJJbW1lZGlhdGUoU3RhdHVzQmFyLl91cGRhdGVJbW1lZGlhdGUpO1xuICAgIFN0YXR1c0Jhci5fdXBkYXRlSW1tZWRpYXRlID0gc2V0SW1tZWRpYXRlKCgpID0+IHtcbiAgICAgIGNvbnN0IG9sZFByb3BzID0gU3RhdHVzQmFyLl9jdXJyZW50VmFsdWVzO1xuICAgICAgY29uc3QgbWVyZ2VkUHJvcHMgPSBtZXJnZVByb3BzU3RhY2soXG4gICAgICAgIFN0YXR1c0Jhci5fcHJvcHNTdGFjayxcbiAgICAgICAgU3RhdHVzQmFyLl9kZWZhdWx0UHJvcHMsXG4gICAgICApO1xuXG4gICAgICAvLyBVcGRhdGUgdGhlIHByb3BzIHRoYXQgaGF2ZSBjaGFuZ2VkIHVzaW5nIHRoZSBtZXJnZWQgdmFsdWVzIGZyb20gdGhlIHByb3BzIHN0YWNrLlxuICAgICAgaWYgKFBsYXRmb3JtLk9TID09PSAnaW9zJykge1xuICAgICAgICBpZiAoXG4gICAgICAgICAgIW9sZFByb3BzIHx8XG4gICAgICAgICAgb2xkUHJvcHMuYmFyU3R5bGU/LnZhbHVlICE9PSBtZXJnZWRQcm9wcy5iYXJTdHlsZS52YWx1ZVxuICAgICAgICApIHtcbiAgICAgICAgICBOYXRpdmVTdGF0dXNCYXJNYW5hZ2VySU9TLnNldFN0eWxlKFxuICAgICAgICAgICAgbWVyZ2VkUHJvcHMuYmFyU3R5bGUudmFsdWUsXG4gICAgICAgICAgICBtZXJnZWRQcm9wcy5iYXJTdHlsZS5hbmltYXRlZCB8fCBmYWxzZSxcbiAgICAgICAgICApO1xuICAgICAgICB9XG4gICAgICAgIGlmICghb2xkUHJvcHMgfHwgb2xkUHJvcHMuaGlkZGVuPy52YWx1ZSAhPT0gbWVyZ2VkUHJvcHMuaGlkZGVuLnZhbHVlKSB7XG4gICAgICAgICAgTmF0aXZlU3RhdHVzQmFyTWFuYWdlcklPUy5zZXRIaWRkZW4oXG4gICAgICAgICAgICBtZXJnZWRQcm9wcy5oaWRkZW4udmFsdWUsXG4gICAgICAgICAgICBtZXJnZWRQcm9wcy5oaWRkZW4uYW5pbWF0ZWRcbiAgICAgICAgICAgICAgPyBtZXJnZWRQcm9wcy5oaWRkZW4udHJhbnNpdGlvblxuICAgICAgICAgICAgICA6ICdub25lJyxcbiAgICAgICAgICApO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKFxuICAgICAgICAgICFvbGRQcm9wcyB8fFxuICAgICAgICAgIG9sZFByb3BzLm5ldHdvcmtBY3Rpdml0eUluZGljYXRvclZpc2libGUgIT09XG4gICAgICAgICAgICBtZXJnZWRQcm9wcy5uZXR3b3JrQWN0aXZpdHlJbmRpY2F0b3JWaXNpYmxlXG4gICAgICAgICkge1xuICAgICAgICAgIE5hdGl2ZVN0YXR1c0Jhck1hbmFnZXJJT1Muc2V0TmV0d29ya0FjdGl2aXR5SW5kaWNhdG9yVmlzaWJsZShcbiAgICAgICAgICAgIG1lcmdlZFByb3BzLm5ldHdvcmtBY3Rpdml0eUluZGljYXRvclZpc2libGUsXG4gICAgICAgICAgKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIGlmIChQbGF0Zm9ybS5PUyA9PT0gJ2FuZHJvaWQnKSB7XG4gICAgICAgIC8vdG9kbyhUNjA2ODQ3ODcpOiBBZGQgYmFjayBvcHRpbWl6YXRpb24gdG8gb25seSB1cGRhdGUgYmFyIHN0eWxlIGFuZFxuICAgICAgICAvL2JhY2tncm91bmQgY29sb3IgaWYgdGhlIG5ldyB2YWx1ZSBpcyBkaWZmZXJlbnQgZnJvbSB0aGUgb2xkIHZhbHVlLlxuICAgICAgICBOYXRpdmVTdGF0dXNCYXJNYW5hZ2VyQW5kcm9pZC5zZXRTdHlsZShtZXJnZWRQcm9wcy5iYXJTdHlsZS52YWx1ZSk7XG4gICAgICAgIGNvbnN0IHByb2Nlc3NlZENvbG9yID0gcHJvY2Vzc0NvbG9yKG1lcmdlZFByb3BzLmJhY2tncm91bmRDb2xvci52YWx1ZSk7XG4gICAgICAgIGlmIChwcm9jZXNzZWRDb2xvciA9PSBudWxsKSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKFxuICAgICAgICAgICAgYFxcYFN0YXR1c0Jhci5fdXBkYXRlUHJvcHNTdGFja1xcYDogQ29sb3IgJHttZXJnZWRQcm9wcy5iYWNrZ3JvdW5kQ29sb3IudmFsdWV9IHBhcnNlZCB0byBudWxsIG9yIHVuZGVmaW5lZGAsXG4gICAgICAgICAgKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBpbnZhcmlhbnQoXG4gICAgICAgICAgICB0eXBlb2YgcHJvY2Vzc2VkQ29sb3IgPT09ICdudW1iZXInLFxuICAgICAgICAgICAgJ1VuZXhwZWN0ZWQgY29sb3IgZ2l2ZW4gaW4gU3RhdHVzQmFyLl91cGRhdGVQcm9wc1N0YWNrJyxcbiAgICAgICAgICApO1xuICAgICAgICAgIE5hdGl2ZVN0YXR1c0Jhck1hbmFnZXJBbmRyb2lkLnNldENvbG9yKFxuICAgICAgICAgICAgcHJvY2Vzc2VkQ29sb3IsXG4gICAgICAgICAgICBtZXJnZWRQcm9wcy5iYWNrZ3JvdW5kQ29sb3IuYW5pbWF0ZWQsXG4gICAgICAgICAgKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoIW9sZFByb3BzIHx8IG9sZFByb3BzLmhpZGRlbj8udmFsdWUgIT09IG1lcmdlZFByb3BzLmhpZGRlbi52YWx1ZSkge1xuICAgICAgICAgIE5hdGl2ZVN0YXR1c0Jhck1hbmFnZXJBbmRyb2lkLnNldEhpZGRlbihtZXJnZWRQcm9wcy5oaWRkZW4udmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIC8vIEFjdGl2aXRpZXMgYXJlIG5vdCB0cmFuc2x1Y2VudCBieSBkZWZhdWx0LCBzbyBhbHdheXMgc2V0IGlmIHRydWUuXG4gICAgICAgIGlmIChcbiAgICAgICAgICAhb2xkUHJvcHMgfHxcbiAgICAgICAgICBvbGRQcm9wcy50cmFuc2x1Y2VudCAhPT0gbWVyZ2VkUHJvcHMudHJhbnNsdWNlbnQgfHxcbiAgICAgICAgICBtZXJnZWRQcm9wcy50cmFuc2x1Y2VudFxuICAgICAgICApIHtcbiAgICAgICAgICBOYXRpdmVTdGF0dXNCYXJNYW5hZ2VyQW5kcm9pZC5zZXRUcmFuc2x1Y2VudChtZXJnZWRQcm9wcy50cmFuc2x1Y2VudCk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIC8vIFVwZGF0ZSB0aGUgY3VycmVudCBwcm9wIHZhbHVlcy5cbiAgICAgIFN0YXR1c0Jhci5fY3VycmVudFZhbHVlcyA9IG1lcmdlZFByb3BzO1xuICAgIH0pO1xuICB9O1xuXG4gIHJlbmRlcigpOiBSZWFjdC5Ob2RlIHtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxufVxuXG5leHBvcnQgZGVmYXVsdCBTdGF0dXNCYXI7XG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFZQSxJQUFBQSxhQUFBLEdBQUFDLHNCQUFBLENBQUFDLE9BQUE7QUFDQSxJQUFBQyxTQUFBLEdBQUFGLHNCQUFBLENBQUFDLE9BQUE7QUFDQSxJQUFBRSw4QkFBQSxHQUFBSCxzQkFBQSxDQUFBQyxPQUFBO0FBQ0EsSUFBQUcsMEJBQUEsR0FBQUosc0JBQUEsQ0FBQUMsT0FBQTtBQUNBLElBQUFJLFVBQUEsR0FBQUwsc0JBQUEsQ0FBQUMsT0FBQTtBQUNBLElBQUFLLEtBQUEsR0FBQUMsdUJBQUEsQ0FBQU4sT0FBQTtBQUErQixJQUFBTyxVQUFBLEVBQUFDLHFCQUFBO0FBQUEsU0FBQUYsd0JBQUFHLENBQUEsRUFBQUMsQ0FBQSw2QkFBQUMsT0FBQSxNQUFBQyxDQUFBLE9BQUFELE9BQUEsSUFBQUUsQ0FBQSxPQUFBRixPQUFBLFlBQUFMLHVCQUFBLFlBQUFBLHdCQUFBRyxDQUFBLEVBQUFDLENBQUEsU0FBQUEsQ0FBQSxJQUFBRCxDQUFBLElBQUFBLENBQUEsQ0FBQUssVUFBQSxTQUFBTCxDQUFBLE1BQUFNLENBQUEsRUFBQUMsQ0FBQSxFQUFBQyxDQUFBLEtBQUFDLFNBQUEsUUFBQUMsT0FBQSxFQUFBVixDQUFBLGlCQUFBQSxDQUFBLHVCQUFBQSxDQUFBLHlCQUFBQSxDQUFBLFNBQUFRLENBQUEsTUFBQUYsQ0FBQSxHQUFBTCxDQUFBLEdBQUFHLENBQUEsR0FBQUQsQ0FBQSxRQUFBRyxDQUFBLENBQUFLLEdBQUEsQ0FBQVgsQ0FBQSxVQUFBTSxDQUFBLENBQUFNLEdBQUEsQ0FBQVosQ0FBQSxHQUFBTSxDQUFBLENBQUFPLEdBQUEsQ0FBQWIsQ0FBQSxFQUFBUSxDQUFBLGNBQUFNLEVBQUEsSUFBQWQsQ0FBQSxnQkFBQWMsRUFBQSxPQUFBQyxjQUFBLENBQUFDLElBQUEsQ0FBQWhCLENBQUEsRUFBQWMsRUFBQSxPQUFBUCxDQUFBLElBQUFELENBQUEsR0FBQVcsTUFBQSxDQUFBQyxjQUFBLEtBQUFELE1BQUEsQ0FBQUUsd0JBQUEsQ0FBQW5CLENBQUEsRUFBQWMsRUFBQSxPQUFBUCxDQUFBLENBQUFLLEdBQUEsSUFBQUwsQ0FBQSxDQUFBTSxHQUFBLElBQUFQLENBQUEsQ0FBQUUsQ0FBQSxFQUFBTSxFQUFBLEVBQUFQLENBQUEsSUFBQUMsQ0FBQSxDQUFBTSxFQUFBLElBQUFkLENBQUEsQ0FBQWMsRUFBQSxXQUFBTixDQUFBLEtBQUFSLENBQUEsRUFBQUMsQ0FBQTtBQUFBLFNBQUFtQixXQUFBbkIsQ0FBQSxFQUFBSyxDQUFBLEVBQUFOLENBQUEsV0FBQU0sQ0FBQSxPQUFBZSxnQkFBQSxDQUFBWCxPQUFBLEVBQUFKLENBQUEsT0FBQWdCLDJCQUFBLENBQUFaLE9BQUEsRUFBQVQsQ0FBQSxFQUFBc0IseUJBQUEsS0FBQUMsT0FBQSxDQUFBQyxTQUFBLENBQUFuQixDQUFBLEVBQUFOLENBQUEsWUFBQXFCLGdCQUFBLENBQUFYLE9BQUEsRUFBQVQsQ0FBQSxFQUFBeUIsV0FBQSxJQUFBcEIsQ0FBQSxDQUFBcUIsS0FBQSxDQUFBMUIsQ0FBQSxFQUFBRCxDQUFBO0FBQUEsU0FBQXVCLDBCQUFBLGNBQUF0QixDQUFBLElBQUEyQixPQUFBLENBQUFDLFNBQUEsQ0FBQUMsT0FBQSxDQUFBZCxJQUFBLENBQUFRLE9BQUEsQ0FBQUMsU0FBQSxDQUFBRyxPQUFBLGlDQUFBM0IsQ0FBQSxhQUFBc0IseUJBQUEsWUFBQUEsMEJBQUEsYUFBQXRCLENBQUE7QUFtSC9CLFNBQVM4QixlQUFlQSxDQUN0QkMsVUFBeUIsRUFDekJDLGFBQXFCLEVBQ2I7RUFDUixPQUFPRCxVQUFVLENBQUNFLE1BQU0sQ0FDdEIsVUFBQ0MsSUFBSSxFQUFFQyxHQUFHLEVBQUs7SUFDYixLQUFLLElBQU1DLElBQUksSUFBSUQsR0FBRyxFQUFFO01BQ3RCLElBQUlBLEdBQUcsQ0FBQ0MsSUFBSSxDQUFDLElBQUksSUFBSSxFQUFFO1FBQ3JCRixJQUFJLENBQUNFLElBQUksQ0FBQyxHQUFHRCxHQUFHLENBQUNDLElBQUksQ0FBQztNQUN4QjtJQUNGO0lBQ0EsT0FBT0YsSUFBSTtFQUNiLENBQUMsRUFBQWxCLE1BQUEsQ0FBQXFCLE1BQUEsS0FDR0wsYUFBYSxDQUNuQixDQUFDO0FBQ0g7QUFNQSxTQUFTTSxnQkFBZ0JBLENBQUNDLEtBQXFCLEVBQWM7RUFBQSxJQUFBQyxlQUFBLEVBQUFDLHFCQUFBO0VBQzNELElBQU1DLFFBQVEsSUFBQUYsZUFBQSxHQUFHRCxLQUFLLENBQUNHLFFBQVEsWUFBQUYsZUFBQSxHQUFJLEtBQUs7RUFDeEMsSUFBTUcsa0JBQWtCLElBQUFGLHFCQUFBLEdBQUdGLEtBQUssQ0FBQ0ksa0JBQWtCLFlBQUFGLHFCQUFBLEdBQUksTUFBTTtFQUM3RCxPQUFPO0lBQ0xHLGVBQWUsRUFDYkwsS0FBSyxDQUFDSyxlQUFlLElBQUksSUFBSSxHQUN6QjtNQUNFQyxLQUFLLEVBQUVOLEtBQUssQ0FBQ0ssZUFBZTtNQUM1QkYsUUFBUSxFQUFSQTtJQUNGLENBQUMsR0FDRCxJQUFJO0lBQ1ZJLFFBQVEsRUFDTlAsS0FBSyxDQUFDTyxRQUFRLElBQUksSUFBSSxHQUNsQjtNQUNFRCxLQUFLLEVBQUVOLEtBQUssQ0FBQ08sUUFBUTtNQUNyQkosUUFBUSxFQUFSQTtJQUNGLENBQUMsR0FDRCxJQUFJO0lBQ1ZLLFdBQVcsRUFBRVIsS0FBSyxDQUFDUSxXQUFXO0lBQzlCQyxNQUFNLEVBQ0pULEtBQUssQ0FBQ1MsTUFBTSxJQUFJLElBQUksR0FDaEI7TUFDRUgsS0FBSyxFQUFFTixLQUFLLENBQUNTLE1BQU07TUFDbkJOLFFBQVEsRUFBUkEsUUFBUTtNQUNSTyxVQUFVLEVBQUVOO0lBQ2QsQ0FBQyxHQUNELElBQUk7SUFDVk8sK0JBQStCLEVBQUVYLEtBQUssQ0FBQ1c7RUFDekMsQ0FBQztBQUNIO0FBQUMsSUE0Q0tDLFNBQVMsYUFBQUMsZ0JBQUE7RUFBQSxTQUFBRCxVQUFBO0lBQUEsSUFBQUUsS0FBQTtJQUFBLElBQUFDLGdCQUFBLENBQUE3QyxPQUFBLFFBQUEwQyxTQUFBO0lBQUEsU0FBQUksSUFBQSxHQUFBQyxTQUFBLENBQUFDLE1BQUEsRUFBQUMsSUFBQSxPQUFBQyxLQUFBLENBQUFKLElBQUEsR0FBQUssSUFBQSxNQUFBQSxJQUFBLEdBQUFMLElBQUEsRUFBQUssSUFBQTtNQUFBRixJQUFBLENBQUFFLElBQUEsSUFBQUosU0FBQSxDQUFBSSxJQUFBO0lBQUE7SUFBQVAsS0FBQSxHQUFBbEMsVUFBQSxPQUFBZ0MsU0FBQSxLQUFBVSxNQUFBLENBQUFILElBQUE7SUFBQUwsS0FBQSxDQTBLYlMsV0FBVyxHQUFnQixJQUFJO0lBQUEsT0FBQVQsS0FBQTtFQUFBO0VBQUEsSUFBQVUsVUFBQSxDQUFBdEQsT0FBQSxFQUFBMEMsU0FBQSxFQUFBQyxnQkFBQTtFQUFBLFdBQUFZLGFBQUEsQ0FBQXZELE9BQUEsRUFBQTBDLFNBQUE7SUFBQWMsR0FBQTtJQUFBcEIsS0FBQSxFQUUvQixTQUFBcUIsaUJBQWlCQSxDQUFBLEVBQUc7TUFLbEIsSUFBSSxDQUFDSixXQUFXLEdBQUdYLFNBQVMsQ0FBQ2dCLGNBQWMsQ0FBQyxJQUFJLENBQUM1QixLQUFLLENBQUM7SUFDekQ7RUFBQztJQUFBMEIsR0FBQTtJQUFBcEIsS0FBQSxFQUVELFNBQUF1QixvQkFBb0JBLENBQUEsRUFBRztNQUdyQixJQUFJLElBQUksQ0FBQ04sV0FBVyxJQUFJLElBQUksRUFBRTtRQUM1QlgsU0FBUyxDQUFDa0IsYUFBYSxDQUFDLElBQUksQ0FBQ1AsV0FBVyxDQUFDO01BQzNDO0lBQ0Y7RUFBQztJQUFBRyxHQUFBO0lBQUFwQixLQUFBLEVBRUQsU0FBQXlCLGtCQUFrQkEsQ0FBQSxFQUFHO01BQ25CLElBQUksSUFBSSxDQUFDUixXQUFXLElBQUksSUFBSSxFQUFFO1FBQzVCLElBQUksQ0FBQ0EsV0FBVyxHQUFHWCxTQUFTLENBQUNvQixpQkFBaUIsQ0FDNUMsSUFBSSxDQUFDVCxXQUFXLEVBQ2hCLElBQUksQ0FBQ3ZCLEtBQ1AsQ0FBQztNQUNIO0lBQ0Y7RUFBQztJQUFBMEIsR0FBQTtJQUFBcEIsS0FBQSxFQWdGRCxTQUFBMkIsTUFBTUEsQ0FBQSxFQUFlO01BQ25CLE9BQU8sSUFBSTtJQUNiO0VBQUM7SUFBQVAsR0FBQTtJQUFBcEIsS0FBQSxFQTNPRCxTQUFPNEIsU0FBU0EsQ0FBQ3pCLE1BQWUsRUFBRTBCLFNBQThCLEVBQUU7TUFDaEVBLFNBQVMsR0FBR0EsU0FBUyxJQUFJLE1BQU07TUFDL0J2QixTQUFTLENBQUN3QixhQUFhLENBQUMzQixNQUFNLENBQUNILEtBQUssR0FBR0csTUFBTTtNQUM3QyxJQUFJNEIsaUJBQVEsQ0FBQ0MsRUFBRSxLQUFLLEtBQUssRUFBRTtRQUN6QkMsa0NBQXlCLENBQUNMLFNBQVMsQ0FBQ3pCLE1BQU0sRUFBRTBCLFNBQVMsQ0FBQztNQUN4RCxDQUFDLE1BQU0sSUFBSUUsaUJBQVEsQ0FBQ0MsRUFBRSxLQUFLLFNBQVMsRUFBRTtRQUNwQ0Usc0NBQTZCLENBQUNOLFNBQVMsQ0FBQ3pCLE1BQU0sQ0FBQztNQUNqRDtJQUNGO0VBQUM7SUFBQWlCLEdBQUE7SUFBQXBCLEtBQUEsRUFPRCxTQUFPbUMsV0FBV0EsQ0FBQ0MsS0FBcUIsRUFBRXZDLFFBQWtCLEVBQUU7TUFDNURBLFFBQVEsR0FBR0EsUUFBUSxJQUFJLEtBQUs7TUFDNUJTLFNBQVMsQ0FBQ3dCLGFBQWEsQ0FBQzdCLFFBQVEsQ0FBQ0QsS0FBSyxHQUFHb0MsS0FBSztNQUM5QyxJQUFJTCxpQkFBUSxDQUFDQyxFQUFFLEtBQUssS0FBSyxFQUFFO1FBQ3pCQyxrQ0FBeUIsQ0FBQ0ksUUFBUSxDQUFDRCxLQUFLLEVBQUV2QyxRQUFRLENBQUM7TUFDckQsQ0FBQyxNQUFNLElBQUlrQyxpQkFBUSxDQUFDQyxFQUFFLEtBQUssU0FBUyxFQUFFO1FBQ3BDRSxzQ0FBNkIsQ0FBQ0csUUFBUSxDQUFDRCxLQUFLLENBQUM7TUFDL0M7SUFDRjtFQUFDO0lBQUFoQixHQUFBO0lBQUFwQixLQUFBLEVBUUQsU0FBT3NDLGtDQUFrQ0EsQ0FBQ0MsT0FBZ0IsRUFBRTtNQUMxRCxJQUFJUixpQkFBUSxDQUFDQyxFQUFFLEtBQUssS0FBSyxFQUFFO1FBQ3pCUSxPQUFPLENBQUNDLElBQUksQ0FDViwrREFDRixDQUFDO1FBQ0Q7TUFDRjtNQUNBbkMsU0FBUyxDQUFDd0IsYUFBYSxDQUFDekIsK0JBQStCLEdBQUdrQyxPQUFPO01BQ2pFTixrQ0FBeUIsQ0FBQ0ssa0NBQWtDLENBQUNDLE9BQU8sQ0FBQztJQUN2RTtFQUFDO0lBQUFuQixHQUFBO0lBQUFwQixLQUFBLEVBT0QsU0FBTzBDLGtCQUFrQkEsQ0FBQ0MsS0FBaUIsRUFBRTlDLFFBQWtCLEVBQVE7TUFDckUsSUFBSWtDLGlCQUFRLENBQUNDLEVBQUUsS0FBSyxTQUFTLEVBQUU7UUFDN0JRLE9BQU8sQ0FBQ0MsSUFBSSxDQUFDLG1EQUFtRCxDQUFDO1FBQ2pFO01BQ0Y7TUFDQTVDLFFBQVEsR0FBR0EsUUFBUSxJQUFJLEtBQUs7TUFDNUJTLFNBQVMsQ0FBQ3dCLGFBQWEsQ0FBQy9CLGVBQWUsQ0FBQ0MsS0FBSyxHQUFHMkMsS0FBSztNQUVyRCxJQUFNQyxjQUFjLEdBQUcsSUFBQUMscUJBQVksRUFBQ0YsS0FBSyxDQUFDO01BQzFDLElBQUlDLGNBQWMsSUFBSSxJQUFJLEVBQUU7UUFDMUJKLE9BQU8sQ0FBQ0MsSUFBSSxDQUNWLDJDQUEyQ0ssTUFBTSxDQUFDSCxLQUFLLENBQUMsOEJBQzFELENBQUM7UUFDRDtNQUNGO01BQ0EsSUFBQUksa0JBQVMsRUFDUCxPQUFPSCxjQUFjLEtBQUssUUFBUSxFQUNsQyx5REFDRixDQUFDO01BRURWLHNDQUE2QixDQUFDYyxRQUFRLENBQUNKLGNBQWMsRUFBRS9DLFFBQVEsQ0FBQztJQUNsRTtFQUFDO0lBQUF1QixHQUFBO0lBQUFwQixLQUFBLEVBTUQsU0FBT2lELGNBQWNBLENBQUMvQyxXQUFvQixFQUFFO01BQzFDLElBQUk2QixpQkFBUSxDQUFDQyxFQUFFLEtBQUssU0FBUyxFQUFFO1FBQzdCUSxPQUFPLENBQUNDLElBQUksQ0FBQywrQ0FBK0MsQ0FBQztRQUM3RDtNQUNGO01BQ0FuQyxTQUFTLENBQUN3QixhQUFhLENBQUM1QixXQUFXLEdBQUdBLFdBQVc7TUFDakRnQyxzQ0FBNkIsQ0FBQ2UsY0FBYyxDQUFDL0MsV0FBVyxDQUFDO0lBQzNEO0VBQUM7SUFBQWtCLEdBQUE7SUFBQXBCLEtBQUEsRUFRRCxTQUFPc0IsY0FBY0EsQ0FBQzVCLEtBQXFCLEVBQWM7TUFDdkQsSUFBTXdELEtBQUssR0FBR3pELGdCQUFnQixDQUFDQyxLQUFLLENBQUM7TUFDckNZLFNBQVMsQ0FBQzZDLFdBQVcsQ0FBQ0MsSUFBSSxDQUFDRixLQUFLLENBQUM7TUFDakM1QyxTQUFTLENBQUMrQyxpQkFBaUIsQ0FBQyxDQUFDO01BQzdCLE9BQU9ILEtBQUs7SUFDZDtFQUFDO0lBQUE5QixHQUFBO0lBQUFwQixLQUFBLEVBT0QsU0FBT3dCLGFBQWFBLENBQUMwQixLQUFpQixFQUFFO01BQ3RDLElBQU1JLEtBQUssR0FBR2hELFNBQVMsQ0FBQzZDLFdBQVcsQ0FBQ0ksT0FBTyxDQUFDTCxLQUFLLENBQUM7TUFDbEQsSUFBSUksS0FBSyxLQUFLLENBQUMsQ0FBQyxFQUFFO1FBQ2hCaEQsU0FBUyxDQUFDNkMsV0FBVyxDQUFDSyxNQUFNLENBQUNGLEtBQUssRUFBRSxDQUFDLENBQUM7TUFDeEM7TUFDQWhELFNBQVMsQ0FBQytDLGlCQUFpQixDQUFDLENBQUM7SUFDL0I7RUFBQztJQUFBakMsR0FBQTtJQUFBcEIsS0FBQSxFQVFELFNBQU8wQixpQkFBaUJBLENBQ3RCd0IsS0FBaUIsRUFDakJ4RCxLQUFxQixFQUNUO01BQ1osSUFBTStELFFBQVEsR0FBR2hFLGdCQUFnQixDQUFDQyxLQUFLLENBQUM7TUFDeEMsSUFBTTRELEtBQUssR0FBR2hELFNBQVMsQ0FBQzZDLFdBQVcsQ0FBQ0ksT0FBTyxDQUFDTCxLQUFLLENBQUM7TUFDbEQsSUFBSUksS0FBSyxLQUFLLENBQUMsQ0FBQyxFQUFFO1FBQ2hCaEQsU0FBUyxDQUFDNkMsV0FBVyxDQUFDRyxLQUFLLENBQUMsR0FBR0csUUFBUTtNQUN6QztNQUNBbkQsU0FBUyxDQUFDK0MsaUJBQWlCLENBQUMsQ0FBQztNQUM3QixPQUFPSSxRQUFRO0lBQ2pCO0VBQUM7QUFBQSxFQXhLcUIzRyxLQUFLLENBQUM0RyxTQUFTO0FBQUExRyxVQUFBLEdBQWpDc0QsU0FBUztBQUFUQSxTQUFTLENBQ042QyxXQUFXLEdBQXNCLEVBQUU7QUFEdEM3QyxTQUFTLENBR053QixhQUFhLEdBQVFyQyxnQkFBZ0IsQ0FBQztFQUMzQ00sZUFBZSxFQUNiZ0MsaUJBQVEsQ0FBQ0MsRUFBRSxLQUFLLFNBQVMsSUFBQS9FLHFCQUFBLEdBQ3JCaUYsc0NBQTZCLENBQUN5QixZQUFZLENBQUMsQ0FBQyxDQUN6Q0Msd0JBQXdCLFlBQUEzRyxxQkFBQSxHQUFJLE9BQU8sR0FDdEMsT0FBTztFQUNiZ0QsUUFBUSxFQUFFLFNBQVM7RUFDbkJDLFdBQVcsRUFBRSxLQUFLO0VBQ2xCQyxNQUFNLEVBQUUsS0FBSztFQUNiRSwrQkFBK0IsRUFBRTtBQUNuQyxDQUFDLENBQUM7QUFiRUMsU0FBUyxDQWdCTnVELGdCQUFnQixHQUFZLElBQUk7QUFoQm5DdkQsU0FBUyxDQW1CTndELGNBQWMsR0FBZ0IsSUFBSTtBQW5CckN4RCxTQUFTLENBNEJOeUQsYUFBYSxHQUNsQmhDLGlCQUFRLENBQUNDLEVBQUUsS0FBSyxTQUFTLEdBQ3JCRSxzQ0FBNkIsQ0FBQ3lCLFlBQVksQ0FBQyxDQUFDLENBQUNLLE1BQU0sR0FDbkQsSUFBSTtBQS9CTjFELFNBQVMsQ0F3TU4rQyxpQkFBaUIsR0FBRyxZQUFNO0VBRS9CWSxjQUFjLENBQUMzRCxVQUFTLENBQUN1RCxnQkFBZ0IsQ0FBQztFQUMxQ3ZELFVBQVMsQ0FBQ3VELGdCQUFnQixHQUFHSyxZQUFZLENBQUMsWUFBTTtJQUM5QyxJQUFNQyxRQUFRLEdBQUc3RCxVQUFTLENBQUN3RCxjQUFjO0lBQ3pDLElBQU1NLFdBQVcsR0FBR25GLGVBQWUsQ0FDakNxQixVQUFTLENBQUM2QyxXQUFXLEVBQ3JCN0MsVUFBUyxDQUFDd0IsYUFDWixDQUFDO0lBR0QsSUFBSUMsaUJBQVEsQ0FBQ0MsRUFBRSxLQUFLLEtBQUssRUFBRTtNQUFBLElBQUFxQyxrQkFBQSxFQUFBQyxnQkFBQTtNQUN6QixJQUNFLENBQUNILFFBQVEsSUFDVCxFQUFBRSxrQkFBQSxHQUFBRixRQUFRLENBQUNsRSxRQUFRLHFCQUFqQm9FLGtCQUFBLENBQW1CckUsS0FBSyxNQUFLb0UsV0FBVyxDQUFDbkUsUUFBUSxDQUFDRCxLQUFLLEVBQ3ZEO1FBQ0FpQyxrQ0FBeUIsQ0FBQ0ksUUFBUSxDQUNoQytCLFdBQVcsQ0FBQ25FLFFBQVEsQ0FBQ0QsS0FBSyxFQUMxQm9FLFdBQVcsQ0FBQ25FLFFBQVEsQ0FBQ0osUUFBUSxJQUFJLEtBQ25DLENBQUM7TUFDSDtNQUNBLElBQUksQ0FBQ3NFLFFBQVEsSUFBSSxFQUFBRyxnQkFBQSxHQUFBSCxRQUFRLENBQUNoRSxNQUFNLHFCQUFmbUUsZ0JBQUEsQ0FBaUJ0RSxLQUFLLE1BQUtvRSxXQUFXLENBQUNqRSxNQUFNLENBQUNILEtBQUssRUFBRTtRQUNwRWlDLGtDQUF5QixDQUFDTCxTQUFTLENBQ2pDd0MsV0FBVyxDQUFDakUsTUFBTSxDQUFDSCxLQUFLLEVBQ3hCb0UsV0FBVyxDQUFDakUsTUFBTSxDQUFDTixRQUFRLEdBQ3ZCdUUsV0FBVyxDQUFDakUsTUFBTSxDQUFDQyxVQUFVLEdBQzdCLE1BQ04sQ0FBQztNQUNIO01BRUEsSUFDRSxDQUFDK0QsUUFBUSxJQUNUQSxRQUFRLENBQUM5RCwrQkFBK0IsS0FDdEMrRCxXQUFXLENBQUMvRCwrQkFBK0IsRUFDN0M7UUFDQTRCLGtDQUF5QixDQUFDSyxrQ0FBa0MsQ0FDMUQ4QixXQUFXLENBQUMvRCwrQkFDZCxDQUFDO01BQ0g7SUFDRixDQUFDLE1BQU0sSUFBSTBCLGlCQUFRLENBQUNDLEVBQUUsS0FBSyxTQUFTLEVBQUU7TUFBQSxJQUFBdUMsaUJBQUE7TUFHcENyQyxzQ0FBNkIsQ0FBQ0csUUFBUSxDQUFDK0IsV0FBVyxDQUFDbkUsUUFBUSxDQUFDRCxLQUFLLENBQUM7TUFDbEUsSUFBTTRDLGNBQWMsR0FBRyxJQUFBQyxxQkFBWSxFQUFDdUIsV0FBVyxDQUFDckUsZUFBZSxDQUFDQyxLQUFLLENBQUM7TUFDdEUsSUFBSTRDLGNBQWMsSUFBSSxJQUFJLEVBQUU7UUFDMUJKLE9BQU8sQ0FBQ0MsSUFBSSxDQUNWLDBDQUEwQzJCLFdBQVcsQ0FBQ3JFLGVBQWUsQ0FBQ0MsS0FBSyw4QkFDN0UsQ0FBQztNQUNILENBQUMsTUFBTTtRQUNMLElBQUErQyxrQkFBUyxFQUNQLE9BQU9ILGNBQWMsS0FBSyxRQUFRLEVBQ2xDLHVEQUNGLENBQUM7UUFDRFYsc0NBQTZCLENBQUNjLFFBQVEsQ0FDcENKLGNBQWMsRUFDZHdCLFdBQVcsQ0FBQ3JFLGVBQWUsQ0FBQ0YsUUFDOUIsQ0FBQztNQUNIO01BQ0EsSUFBSSxDQUFDc0UsUUFBUSxJQUFJLEVBQUFJLGlCQUFBLEdBQUFKLFFBQVEsQ0FBQ2hFLE1BQU0scUJBQWZvRSxpQkFBQSxDQUFpQnZFLEtBQUssTUFBS29FLFdBQVcsQ0FBQ2pFLE1BQU0sQ0FBQ0gsS0FBSyxFQUFFO1FBQ3BFa0Msc0NBQTZCLENBQUNOLFNBQVMsQ0FBQ3dDLFdBQVcsQ0FBQ2pFLE1BQU0sQ0FBQ0gsS0FBSyxDQUFDO01BQ25FO01BRUEsSUFDRSxDQUFDbUUsUUFBUSxJQUNUQSxRQUFRLENBQUNqRSxXQUFXLEtBQUtrRSxXQUFXLENBQUNsRSxXQUFXLElBQ2hEa0UsV0FBVyxDQUFDbEUsV0FBVyxFQUN2QjtRQUNBZ0Msc0NBQTZCLENBQUNlLGNBQWMsQ0FBQ21CLFdBQVcsQ0FBQ2xFLFdBQVcsQ0FBQztNQUN2RTtJQUNGO0lBRUFJLFVBQVMsQ0FBQ3dELGNBQWMsR0FBR00sV0FBVztFQUN4QyxDQUFDLENBQUM7QUFDSixDQUFDO0FBQUEsSUFBQUksUUFBQSxHQUFBQyxPQUFBLENBQUE3RyxPQUFBLEdBT1kwQyxTQUFTIiwiaWdub3JlTGlzdCI6W119