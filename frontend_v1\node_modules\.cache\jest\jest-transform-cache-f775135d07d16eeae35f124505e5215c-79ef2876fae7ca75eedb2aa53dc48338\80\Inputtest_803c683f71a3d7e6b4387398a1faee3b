fb458293ff71d85730e6a83c8f52ac78
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _reactNative = require("@testing-library/react-native");
var _react = _interopRequireDefault(require("react"));
var _ThemeContext = require("../../../contexts/ThemeContext");
var _HyperMinimalistTheme = require("../../../design-system/HyperMinimalistTheme");
var _Input = require("../Input");
var _jsxRuntime = require("react/jsx-runtime");
var TestWrapper = function TestWrapper(_ref) {
  var children = _ref.children;
  return (0, _jsxRuntime.jsx)(_ThemeContext.ThemeProvider, {
    theme: _HyperMinimalistTheme.HyperMinimalistTheme,
    children: children
  });
};
describe('Input Component', function () {
  var defaultProps = {
    placeholder: 'Enter text'
  };
  beforeEach(function () {
    jest.clearAllMocks();
  });
  describe('Basic Rendering', function () {
    it('should render with default props', function () {
      var _render = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps))
        })),
        getByPlaceholderText = _render.getByPlaceholderText;
      expect(getByPlaceholderText('Enter text')).toBeTruthy();
    });
    it('should render with custom placeholder', function () {
      var _render2 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps, {
            placeholder: "Custom placeholder"
          }))
        })),
        getByPlaceholderText = _render2.getByPlaceholderText;
      expect(getByPlaceholderText('Custom placeholder')).toBeTruthy();
    });
    it('should render with label', function () {
      var _render3 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps, {
            label: "Input Label"
          }))
        })),
        getByText = _render3.getByText,
        getByPlaceholderText = _render3.getByPlaceholderText;
      expect(getByText('Input Label')).toBeTruthy();
      expect(getByPlaceholderText('Enter text')).toBeTruthy();
    });
  });
  describe('Value and Text Handling', function () {
    it('should display initial value', function () {
      var _render4 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps, {
            value: "Initial value"
          }))
        })),
        getByDisplayValue = _render4.getByDisplayValue;
      expect(getByDisplayValue('Initial value')).toBeTruthy();
    });
    it('should call onChangeText when text changes', function () {
      var onChangeText = jest.fn();
      var _render5 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps, {
            onChangeText: onChangeText
          }))
        })),
        getByPlaceholderText = _render5.getByPlaceholderText;
      var input = getByPlaceholderText('Enter text');
      _reactNative.fireEvent.changeText(input, 'New text');
      expect(onChangeText).toHaveBeenCalledWith('New text');
    });
    it('should handle controlled input pattern', function () {
      var onChangeText = jest.fn();
      var _render6 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps, {
            value: "Controlled value",
            onChangeText: onChangeText
          }))
        })),
        getByDisplayValue = _render6.getByDisplayValue;
      expect(getByDisplayValue('Controlled value')).toBeTruthy();
    });
  });
  describe('Input States', function () {
    it('should handle disabled state', function () {
      var onChangeText = jest.fn();
      var _render7 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps, {
            onChangeText: onChangeText,
            disabled: true
          }))
        })),
        getByPlaceholderText = _render7.getByPlaceholderText;
      var input = getByPlaceholderText('Enter text');
      expect(input.props.editable).toBe(false);
    });
    it('should handle error state with message', function () {
      var _render8 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps, {
            error: "This field is required"
          }))
        })),
        getByPlaceholderText = _render8.getByPlaceholderText,
        getByText = _render8.getByText;
      var input = getByPlaceholderText('Enter text');
      expect(input).toBeTruthy();
      expect(getByText('This field is required')).toBeTruthy();
    });
    it('should handle success state with message', function () {
      var _render9 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps, {
            success: "Input is valid"
          }))
        })),
        getByPlaceholderText = _render9.getByPlaceholderText,
        getByText = _render9.getByText;
      var input = getByPlaceholderText('Enter text');
      expect(input).toBeTruthy();
      expect(getByText('Input is valid')).toBeTruthy();
    });
    it('should show helper text when provided', function () {
      var _render0 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps, {
            helperText: "This is helper text"
          }))
        })),
        getByText = _render0.getByText;
      expect(getByText('This is helper text')).toBeTruthy();
    });
    it('should handle focus state', function () {
      var _render1 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps))
        })),
        getByPlaceholderText = _render1.getByPlaceholderText;
      var input = getByPlaceholderText('Enter text');
      (0, _reactNative.fireEvent)(input, 'focus');
      expect(input).toBeTruthy();
    });
  });
  describe('Input Variants', function () {
    it('should render default variant by default', function () {
      var _render10 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps))
        })),
        getByPlaceholderText = _render10.getByPlaceholderText;
      var input = getByPlaceholderText('Enter text');
      expect(input).toBeTruthy();
    });
    it('should render outline variant correctly', function () {
      var _render11 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps, {
            variant: "outline"
          }))
        })),
        getByPlaceholderText = _render11.getByPlaceholderText;
      var input = getByPlaceholderText('Enter text');
      expect(input).toBeTruthy();
    });
    it('should render minimal variant correctly', function () {
      var _render12 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps, {
            variant: "minimal"
          }))
        })),
        getByPlaceholderText = _render12.getByPlaceholderText;
      var input = getByPlaceholderText('Enter text');
      expect(input).toBeTruthy();
    });
  });
  describe('Input Sizes', function () {
    it('should render medium size by default', function () {
      var _render13 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps))
        })),
        getByPlaceholderText = _render13.getByPlaceholderText;
      var input = getByPlaceholderText('Enter text');
      expect(input).toBeTruthy();
    });
    it('should render small size correctly', function () {
      var _render14 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps, {
            size: "sm"
          }))
        })),
        getByPlaceholderText = _render14.getByPlaceholderText;
      var input = getByPlaceholderText('Enter text');
      expect(input).toBeTruthy();
    });
    it('should render large size correctly', function () {
      var _render15 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps, {
            size: "lg"
          }))
        })),
        getByPlaceholderText = _render15.getByPlaceholderText;
      var input = getByPlaceholderText('Enter text');
      expect(input).toBeTruthy();
    });
  });
  describe('Input Types', function () {
    it('should handle secure text entry', function () {
      var _render16 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps, {
            secureTextEntry: true
          }))
        })),
        getByPlaceholderText = _render16.getByPlaceholderText;
      var input = getByPlaceholderText('Enter text');
      expect(input.props.secureTextEntry).toBe(true);
    });
    it('should handle multiline input', function () {
      var _render17 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps, {
            multiline: true
          }))
        })),
        getByPlaceholderText = _render17.getByPlaceholderText;
      var input = getByPlaceholderText('Enter text');
      expect(input.props.multiline).toBe(true);
    });
    it('should handle keyboard type', function () {
      var _render18 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps, {
            keyboardType: "email-address"
          }))
        })),
        getByPlaceholderText = _render18.getByPlaceholderText;
      var input = getByPlaceholderText('Enter text');
      expect(input.props.keyboardType).toBe('email-address');
    });
  });
  describe('Required Field', function () {
    it('should show required indicator when required', function () {
      var _render19 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps, {
            label: "Required Field",
            required: true
          }))
        })),
        getByText = _render19.getByText;
      expect(getByText('Required Field', {
        exact: false
      })).toBeTruthy();
    });
    it('should not show required indicator by default', function () {
      var _render20 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps, {
            label: "Optional Field"
          }))
        })),
        getByText = _render20.getByText;
      expect(getByText('Optional Field')).toBeTruthy();
    });
  });
  describe('Accessibility', function () {
    it('should support custom accessibility label', function () {
      var _render21 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps, {
            accessibilityLabel: "Custom Input Label"
          }))
        })),
        getByLabelText = _render21.getByLabelText;
      expect(getByLabelText('Custom Input Label')).toBeTruthy();
    });
    it('should support accessibility hint', function () {
      var _render22 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps, {
            accessibilityHint: "Enter your email address"
          }))
        })),
        getByPlaceholderText = _render22.getByPlaceholderText;
      var input = getByPlaceholderText('Enter text');
      expect(input.props.accessibilityHint).toBe('Enter your email address');
    });
    it('should have proper accessibility setup for text input', function () {
      var _render23 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps))
        })),
        getByPlaceholderText = _render23.getByPlaceholderText;
      var input = getByPlaceholderText('Enter text');
      expect(input).toBeTruthy();
    });
  });
  describe('Custom Styling', function () {
    it('should apply custom styles', function () {
      var customStyle = {
        marginTop: 20
      };
      var _render24 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, Object.assign({}, defaultProps, {
            style: customStyle
          }))
        })),
        getByPlaceholderText = _render24.getByPlaceholderText;
      var input = getByPlaceholderText('Enter text');
      expect(input).toHaveStyle(customStyle);
    });
  });
  describe('Component Contract Compliance', function () {
    it('should handle complex prop combinations', function () {
      var _render25 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Input.Input, {
            label: "Complex Input",
            placeholder: "Enter complex data",
            helperText: "This is a complex input example",
            size: "lg",
            variant: "outline",
            required: true
          })
        })),
        getByPlaceholderText = _render25.getByPlaceholderText,
        getByText = _render25.getByText;
      expect(getByText(/Complex Input/)).toBeTruthy();
      expect(getByPlaceholderText('Enter complex data')).toBeTruthy();
      expect(getByText('This is a complex input example')).toBeTruthy();
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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