{"version": 3, "names": ["_vectorIcons", "require", "_react", "_interopRequireWildcard", "_reactNative", "_ThemeContext", "_imageAccessibilityUtils", "_responsiveUtils", "_storeImages", "_LazyImage", "_jsxRuntime", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "SIZE_CONFIG", "small", "width", "height", "borderRadius", "fontSize", "iconSize", "medium", "large", "StoreImage", "exports", "_ref", "providerId", "providerName", "category", "imageUrl", "_ref$size", "size", "style", "imageStyle", "_ref$showFallbackIcon", "showFallbackIcon", "testID", "_useTheme", "useTheme", "colors", "_useState", "useState", "_useState2", "_slicedToArray2", "isLoading", "setIsLoading", "_useState3", "_useState4", "<PERSON><PERSON><PERSON><PERSON>", "setHasError", "sizeConfig", "styles", "createStyles", "finalImageUrl", "getStoreImage", "handleImageLoad", "handleImageError", "renderFallback", "jsx", "Ionicons", "name", "color", "text", "onPrimary", "Text", "fallbackText", "children", "char<PERSON>t", "toUpperCase", "jsxs", "View", "container", "LazyImage", "source", "uri", "fallback", "getFallbackImage", "lazy", "threshold", "fadeInDuration", "onLoadStart", "onLoadEnd", "onError", "containerStyle", "image", "imageContext", "ImageContexts", "storeImage", "resizeMode", "fall<PERSON><PERSON><PERSON><PERSON>", "StyleSheet", "create", "getResponsiveSpacing", "overflow", "backgroundColor", "surface", "secondary", "borderWidth", "borderColor", "border", "light", "position", "loadingContainer", "top", "left", "right", "bottom", "justifyContent", "alignItems", "primary", "getResponsiveFontSize", "fontWeight", "textAlign", "_default"], "sources": ["StoreImage.tsx"], "sourcesContent": ["/**\n * StoreImage Component\n *\n * A reusable component for displaying store/provider images with fallback support.\n * Handles loading states, error states, and provides consistent styling.\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport { Ionicons } from '@expo/vector-icons';\nimport React, { useState } from 'react';\nimport {\n  View,\n  Text,\n  StyleSheet,\n  ActivityIndicator,\n  ViewStyle,\n  ImageStyle,\n  TextStyle,\n} from 'react-native';\n\nimport { useTheme } from '../../contexts/ThemeContext';\nimport { ImageContexts } from '../../utils/imageAccessibilityUtils';\nimport {\n  getResponsiveSpacing,\n  getResponsiveFontSize,\n} from '../../utils/responsiveUtils';\nimport { getStoreImage, getFallbackImage } from '../../utils/storeImages';\nimport { LazyImage } from '../ui/LazyImage';\n\ninterface StoreImageProps {\n  providerId: string;\n  providerName: string;\n  category?: string;\n  imageUrl?: string;\n  size?: 'small' | 'medium' | 'large';\n  style?: ViewStyle;\n  imageStyle?: ImageStyle;\n  showFallbackIcon?: boolean;\n  testID?: string;\n}\n\nconst SIZE_CONFIG = {\n  small: {\n    width: 40,\n    height: 40,\n    borderRadius: 20,\n    fontSize: 16,\n    iconSize: 20,\n  },\n  medium: {\n    width: 60,\n    height: 60,\n    borderRadius: 30,\n    fontSize: 24,\n    iconSize: 30,\n  },\n  large: {\n    width: 80,\n    height: 80,\n    borderRadius: 40,\n    fontSize: 32,\n    iconSize: 40,\n  },\n};\n\nexport const StoreImage: React.FC<StoreImageProps> = ({\n  providerId,\n  providerName,\n  category,\n  imageUrl,\n  size = 'medium',\n  style,\n  imageStyle,\n  showFallbackIcon = true,\n  testID,\n}) => {\n  const { colors } = useTheme();\n  const [isLoading, setIsLoading] = useState(true);\n  const [hasError, setHasError] = useState(false);\n\n  const sizeConfig = SIZE_CONFIG[size];\n  const styles = createStyles(colors, sizeConfig);\n\n  // Determine the image URL to use\n  const finalImageUrl = imageUrl || getStoreImage(providerId, category);\n\n  const handleImageLoad = () => {\n    setIsLoading(false);\n    setHasError(false);\n  };\n\n  const handleImageError = () => {\n    setIsLoading(false);\n    setHasError(true);\n  };\n\n  const renderFallback = () => {\n    if (showFallbackIcon) {\n      return (\n        <Ionicons\n          name=\"storefront-outline\"\n          size={sizeConfig.iconSize}\n          color={colors.text.onPrimary}\n        />\n      );\n    }\n\n    // Show first letter of provider name\n    return (\n      <Text style={styles.fallbackText}>\n        {(providerName || 'P').charAt(0).toUpperCase()}\n      </Text>\n    );\n  };\n\n  return (\n    <View style={[styles.container, style]} testID={testID}>\n      <LazyImage\n        source={{ uri: finalImageUrl }}\n        fallback={{ uri: getFallbackImage(providerName) }}\n        width={sizeConfig.width}\n        height={sizeConfig.height}\n        lazy={true}\n        threshold={0.1}\n        fadeInDuration={300}\n        onLoadStart={() => setIsLoading(true)}\n        onLoadEnd={handleImageLoad}\n        onError={handleImageError}\n        containerStyle={styles.image}\n        imageStyle={[styles.image, imageStyle]}\n        testID={`${testID}-lazy-image`}\n        // Enhanced WCAG-compliant accessibility\n        imageContext={ImageContexts.storeImage(providerName)}\n        resizeMode=\"cover\"\n      />\n\n      {/* Fallback when image fails to load */}\n      {hasError && (\n        <View style={styles.fallbackContainer}>{renderFallback()}</View>\n      )}\n    </View>\n  );\n};\n\nconst createStyles = (colors: any, sizeConfig: any) =>\n  StyleSheet.create({\n    container: {\n      width: getResponsiveSpacing(sizeConfig.width),\n      height: getResponsiveSpacing(sizeConfig.height),\n      borderRadius: getResponsiveSpacing(sizeConfig.borderRadius),\n      overflow: 'hidden',\n      backgroundColor: colors.surface.secondary,\n      borderWidth: 1,\n      borderColor: colors.border.light,\n      position: 'relative',\n    },\n    image: {\n      width: '100%',\n      height: '100%',\n    },\n    loadingContainer: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      justifyContent: 'center',\n      alignItems: 'center',\n      backgroundColor: colors.surface.secondary,\n    },\n    fallbackContainer: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      justifyContent: 'center',\n      alignItems: 'center',\n      backgroundColor: colors.primary.default,\n    },\n    fallbackText: {\n      fontSize: getResponsiveFontSize(sizeConfig.fontSize),\n      fontWeight: '700',\n      color: colors.text.onPrimary,\n      textAlign: 'center',\n    },\n  });\n\nexport default StoreImage;\n"], "mappings": ";;;;;;AAUA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,YAAA,GAAAH,OAAA;AAUA,IAAAI,aAAA,GAAAJ,OAAA;AACA,IAAAK,wBAAA,GAAAL,OAAA;AACA,IAAAM,gBAAA,GAAAN,OAAA;AAIA,IAAAO,YAAA,GAAAP,OAAA;AACA,IAAAQ,UAAA,GAAAR,OAAA;AAA4C,IAAAS,WAAA,GAAAT,OAAA;AAAA,SAAAE,wBAAAQ,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAV,uBAAA,YAAAA,wBAAAQ,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAc5C,IAAMmB,WAAW,GAAG;EAClBC,KAAK,EAAE;IACLC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC;EACDC,MAAM,EAAE;IACNL,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC;EACDE,KAAK,EAAE;IACLN,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ;AACF,CAAC;AAEM,IAAMG,UAAqC,GAAAC,OAAA,CAAAD,UAAA,GAAG,SAAxCA,UAAqCA,CAAAE,IAAA,EAU5C;EAAA,IATJC,UAAU,GAAAD,IAAA,CAAVC,UAAU;IACVC,YAAY,GAAAF,IAAA,CAAZE,YAAY;IACZC,QAAQ,GAAAH,IAAA,CAARG,QAAQ;IACRC,QAAQ,GAAAJ,IAAA,CAARI,QAAQ;IAAAC,SAAA,GAAAL,IAAA,CACRM,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG,QAAQ,GAAAA,SAAA;IACfE,KAAK,GAAAP,IAAA,CAALO,KAAK;IACLC,UAAU,GAAAR,IAAA,CAAVQ,UAAU;IAAAC,qBAAA,GAAAT,IAAA,CACVU,gBAAgB;IAAhBA,gBAAgB,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IACvBE,MAAM,GAAAX,IAAA,CAANW,MAAM;EAEN,IAAAC,SAAA,GAAmB,IAAAC,sBAAQ,EAAC,CAAC;IAArBC,MAAM,GAAAF,SAAA,CAANE,MAAM;EACd,IAAAC,SAAA,GAAkC,IAAAC,eAAQ,EAAC,IAAI,CAAC;IAAAC,UAAA,OAAAC,eAAA,CAAAvC,OAAA,EAAAoC,SAAA;IAAzCI,SAAS,GAAAF,UAAA;IAAEG,YAAY,GAAAH,UAAA;EAC9B,IAAAI,UAAA,GAAgC,IAAAL,eAAQ,EAAC,KAAK,CAAC;IAAAM,UAAA,OAAAJ,eAAA,CAAAvC,OAAA,EAAA0C,UAAA;IAAxCE,QAAQ,GAAAD,UAAA;IAAEE,WAAW,GAAAF,UAAA;EAE5B,IAAMG,UAAU,GAAGpC,WAAW,CAACiB,IAAI,CAAC;EACpC,IAAMoB,MAAM,GAAGC,YAAY,CAACb,MAAM,EAAEW,UAAU,CAAC;EAG/C,IAAMG,aAAa,GAAGxB,QAAQ,IAAI,IAAAyB,0BAAa,EAAC5B,UAAU,EAAEE,QAAQ,CAAC;EAErE,IAAM2B,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;IAC5BV,YAAY,CAAC,KAAK,CAAC;IACnBI,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,IAAMO,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;IAC7BX,YAAY,CAAC,KAAK,CAAC;IACnBI,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,IAAMQ,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAC3B,IAAItB,gBAAgB,EAAE;MACpB,OACE,IAAA1C,WAAA,CAAAiE,GAAA,EAAC3E,YAAA,CAAA4E,QAAQ;QACPC,IAAI,EAAC,oBAAoB;QACzB7B,IAAI,EAAEmB,UAAU,CAAC9B,QAAS;QAC1ByC,KAAK,EAAEtB,MAAM,CAACuB,IAAI,CAACC;MAAU,CAC9B,CAAC;IAEN;IAGA,OACE,IAAAtE,WAAA,CAAAiE,GAAA,EAACvE,YAAA,CAAA6E,IAAI;MAAChC,KAAK,EAAEmB,MAAM,CAACc,YAAa;MAAAC,QAAA,EAC9B,CAACvC,YAAY,IAAI,GAAG,EAAEwC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;IAAC,CAC1C,CAAC;EAEX,CAAC;EAED,OACE,IAAA3E,WAAA,CAAA4E,IAAA,EAAClF,YAAA,CAAAmF,IAAI;IAACtC,KAAK,EAAE,CAACmB,MAAM,CAACoB,SAAS,EAAEvC,KAAK,CAAE;IAACI,MAAM,EAAEA,MAAO;IAAA8B,QAAA,GACrD,IAAAzE,WAAA,CAAAiE,GAAA,EAAClE,UAAA,CAAAgF,SAAS;MACRC,MAAM,EAAE;QAAEC,GAAG,EAAErB;MAAc,CAAE;MAC/BsB,QAAQ,EAAE;QAAED,GAAG,EAAE,IAAAE,6BAAgB,EAACjD,YAAY;MAAE,CAAE;MAClDX,KAAK,EAAEkC,UAAU,CAAClC,KAAM;MACxBC,MAAM,EAAEiC,UAAU,CAACjC,MAAO;MAC1B4D,IAAI,EAAE,IAAK;MACXC,SAAS,EAAE,GAAI;MACfC,cAAc,EAAE,GAAI;MACpBC,WAAW,EAAE,SAAbA,WAAWA,CAAA;QAAA,OAAQnC,YAAY,CAAC,IAAI,CAAC;MAAA,CAAC;MACtCoC,SAAS,EAAE1B,eAAgB;MAC3B2B,OAAO,EAAE1B,gBAAiB;MAC1B2B,cAAc,EAAEhC,MAAM,CAACiC,KAAM;MAC7BnD,UAAU,EAAE,CAACkB,MAAM,CAACiC,KAAK,EAAEnD,UAAU,CAAE;MACvCG,MAAM,EAAE,GAAGA,MAAM,aAAc;MAE/BiD,YAAY,EAAEC,sCAAa,CAACC,UAAU,CAAC5D,YAAY,CAAE;MACrD6D,UAAU,EAAC;IAAO,CACnB,CAAC,EAGDxC,QAAQ,IACP,IAAAvD,WAAA,CAAAiE,GAAA,EAACvE,YAAA,CAAAmF,IAAI;MAACtC,KAAK,EAAEmB,MAAM,CAACsC,iBAAkB;MAAAvB,QAAA,EAAET,cAAc,CAAC;IAAC,CAAO,CAChE;EAAA,CACG,CAAC;AAEX,CAAC;AAED,IAAML,YAAY,GAAG,SAAfA,YAAYA,CAAIb,MAAW,EAAEW,UAAe;EAAA,OAChDwC,uBAAU,CAACC,MAAM,CAAC;IAChBpB,SAAS,EAAE;MACTvD,KAAK,EAAE,IAAA4E,qCAAoB,EAAC1C,UAAU,CAAClC,KAAK,CAAC;MAC7CC,MAAM,EAAE,IAAA2E,qCAAoB,EAAC1C,UAAU,CAACjC,MAAM,CAAC;MAC/CC,YAAY,EAAE,IAAA0E,qCAAoB,EAAC1C,UAAU,CAAChC,YAAY,CAAC;MAC3D2E,QAAQ,EAAE,QAAQ;MAClBC,eAAe,EAAEvD,MAAM,CAACwD,OAAO,CAACC,SAAS;MACzCC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE3D,MAAM,CAAC4D,MAAM,CAACC,KAAK;MAChCC,QAAQ,EAAE;IACZ,CAAC;IACDjB,KAAK,EAAE;MACLpE,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE;IACV,CAAC;IACDqF,gBAAgB,EAAE;MAChBD,QAAQ,EAAE,UAAU;MACpBE,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBd,eAAe,EAAEvD,MAAM,CAACwD,OAAO,CAACC;IAClC,CAAC;IACDP,iBAAiB,EAAE;MACjBY,QAAQ,EAAE,UAAU;MACpBE,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBd,eAAe,EAAEvD,MAAM,CAACsE,OAAO,CAACzG;IAClC,CAAC;IACD6D,YAAY,EAAE;MACZ9C,QAAQ,EAAE,IAAA2F,sCAAqB,EAAC5D,UAAU,CAAC/B,QAAQ,CAAC;MACpD4F,UAAU,EAAE,KAAK;MACjBlD,KAAK,EAAEtB,MAAM,CAACuB,IAAI,CAACC,SAAS;MAC5BiD,SAAS,EAAE;IACb;EACF,CAAC,CAAC;AAAA;AAAC,IAAAC,QAAA,GAAAzF,OAAA,CAAApB,OAAA,GAEUmB,UAAU", "ignoreList": []}