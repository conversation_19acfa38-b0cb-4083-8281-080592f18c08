356542c8d66abdb37e4db458b4b57d87
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.validateForm = exports.validateField = exports.createDebouncedValidator = exports.ValidationRules = exports.FormValidator = exports.CommonValidations = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var ValidationRules = exports.ValidationRules = {
  required: function required(value) {
    var isValid = value !== null && value !== undefined && String(value).trim().length > 0;
    return {
      isValid: isValid,
      error: isValid ? undefined : 'Please fill in this field to continue'
    };
  },
  email: function email(value) {
    if (!value) return {
      isValid: true
    };
    var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    var isValid = emailRegex.test(value);
    return {
      isValid: isValid,
      error: isValid ? undefined : 'Please enter your email in <NAME_EMAIL>'
    };
  },
  phone: function phone(value) {
    if (!value) return {
      isValid: true
    };
    var cleanPhone = value.replace(/[\s\-\(\)]/g, '');
    var phoneRegex = /^[\+]?[1-9][\d]{9,14}$/;
    var isValid = phoneRegex.test(cleanPhone);
    return {
      isValid: isValid,
      error: isValid ? undefined : 'Please enter your phone number with area code (e.g., ************)'
    };
  },
  password: function password(value) {
    if (!value) return {
      isValid: true
    };
    var minLength = 8;
    var hasUpperCase = /[A-Z]/.test(value);
    var hasLowerCase = /[a-z]/.test(value);
    var hasNumbers = /\d/.test(value);
    var hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);
    if (value.length < minLength) {
      return {
        isValid: false,
        error: `Your password needs at least ${minLength} characters. Please add ${minLength - value.length} more character${minLength - value.length > 1 ? 's' : ''}.`
      };
    }
    if (!hasUpperCase) {
      return {
        isValid: false,
        error: 'Your password needs at least one uppercase letter (A-Z). Please add one to continue.'
      };
    }
    if (!hasLowerCase) {
      return {
        isValid: false,
        error: 'Your password needs at least one lowercase letter (a-z). Please add one to continue.'
      };
    }
    if (!hasNumbers) {
      return {
        isValid: false,
        error: 'Your password needs at least one number (0-9). Please add one to continue.'
      };
    }
    return {
      isValid: true
    };
  },
  name: function name(value) {
    if (!value) return {
      isValid: true
    };
    var nameRegex = /^[a-zA-Z\s\-']{2,50}$/;
    var isValid = nameRegex.test(value.trim());
    return {
      isValid: isValid,
      error: isValid ? undefined : 'Please enter your name using only letters, spaces, hyphens, and apostrophes (2-50 characters)'
    };
  },
  url: function url(value) {
    if (!value) return {
      isValid: true
    };
    try {
      new URL(value);
      return {
        isValid: true
      };
    } catch (_unused) {
      return {
        isValid: false,
        error: 'Please enter a valid URL'
      };
    }
  },
  number: function number(value) {
    if (!value) return {
      isValid: true
    };
    var isValid = !isNaN(Number(value)) && isFinite(Number(value));
    return {
      isValid: isValid,
      error: isValid ? undefined : 'Please enter a valid number'
    };
  },
  date: function date(value) {
    if (!value) return {
      isValid: true
    };
    var date = new Date(value);
    var isValid = date instanceof Date && !isNaN(date.getTime());
    return {
      isValid: isValid,
      error: isValid ? undefined : 'Please enter a valid date'
    };
  },
  minLength: function minLength(value, _minLength) {
    if (!value) return {
      isValid: true
    };
    var isValid = value.length >= _minLength;
    return {
      isValid: isValid,
      error: isValid ? undefined : `Must be at least ${_minLength} characters long`
    };
  },
  maxLength: function maxLength(value, _maxLength) {
    if (!value) return {
      isValid: true
    };
    var isValid = value.length <= _maxLength;
    return {
      isValid: isValid,
      error: isValid ? undefined : `Must be no more than ${_maxLength} characters long`
    };
  },
  pattern: function pattern(value, _pattern, errorMessage) {
    if (!value) return {
      isValid: true
    };
    var isValid = _pattern.test(value);
    return {
      isValid: isValid,
      error: isValid ? undefined : errorMessage || 'Invalid format'
    };
  },
  confirmPassword: function confirmPassword(value, originalPassword) {
    var isValid = value === originalPassword;
    return {
      isValid: isValid,
      error: isValid ? undefined : 'Passwords do not match'
    };
  }
};
var _validateField = exports.validateField = function validateField(value, validation, formData) {
  if (validation.required) {
    var requiredResult = ValidationRules.required(value);
    if (!requiredResult.isValid) {
      return requiredResult;
    }
  }
  if (!value && !validation.required) {
    return {
      isValid: true
    };
  }
  if (validation.email) {
    var emailResult = ValidationRules.email(value);
    if (!emailResult.isValid) {
      return emailResult;
    }
  }
  if (validation.phone) {
    var phoneResult = ValidationRules.phone(value);
    if (!phoneResult.isValid) {
      return phoneResult;
    }
  }
  if (validation.password) {
    var passwordResult = ValidationRules.password(value);
    if (!passwordResult.isValid) {
      return passwordResult;
    }
  }
  if (validation.name) {
    var nameResult = ValidationRules.name(value);
    if (!nameResult.isValid) {
      return nameResult;
    }
  }
  if (validation.url) {
    var urlResult = ValidationRules.url(value);
    if (!urlResult.isValid) {
      return urlResult;
    }
  }
  if (validation.number) {
    var numberResult = ValidationRules.number(value);
    if (!numberResult.isValid) {
      return numberResult;
    }
  }
  if (validation.date) {
    var dateResult = ValidationRules.date(value);
    if (!dateResult.isValid) {
      return dateResult;
    }
  }
  if (validation.minLength !== undefined) {
    var minLengthResult = ValidationRules.minLength(value, validation.minLength);
    if (!minLengthResult.isValid) {
      return minLengthResult;
    }
  }
  if (validation.maxLength !== undefined) {
    var maxLengthResult = ValidationRules.maxLength(value, validation.maxLength);
    if (!maxLengthResult.isValid) {
      return maxLengthResult;
    }
  }
  if (validation.pattern) {
    var patternResult = ValidationRules.pattern(value, validation.pattern);
    if (!patternResult.isValid) {
      return patternResult;
    }
  }
  if (validation.confirmPassword && formData) {
    var originalPassword = formData[validation.confirmPassword];
    var confirmResult = ValidationRules.confirmPassword(value, originalPassword);
    if (!confirmResult.isValid) {
      return confirmResult;
    }
  }
  if (validation.custom) {
    var customResult = validation.custom(value);
    if (!customResult.isValid) {
      return customResult;
    }
  }
  return {
    isValid: true
  };
};
var _validateForm = exports.validateForm = function validateForm(formData, validationConfig) {
  var errors = {};
  var isValid = true;
  Object.keys(validationConfig).forEach(function (fieldName) {
    var fieldValue = formData[fieldName];
    var fieldValidation = validationConfig[fieldName];
    var result = _validateField(fieldValue, fieldValidation, formData);
    if (!result.isValid) {
      errors[fieldName] = result.error;
      isValid = false;
    }
  });
  return {
    isValid: isValid,
    errors: errors
  };
};
var createDebouncedValidator = exports.createDebouncedValidator = function createDebouncedValidator(validationFn) {
  var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 300;
  var timeoutId;
  return function () {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(validationFn, delay);
  };
};
var CommonValidations = exports.CommonValidations = {
  email: {
    required: true,
    email: true
  },
  password: {
    required: true,
    password: true,
    minLength: 8
  },
  confirmPassword: function confirmPassword(passwordFieldName) {
    return {
      required: true,
      confirmPassword: passwordFieldName
    };
  },
  name: {
    required: true,
    name: true,
    minLength: 2,
    maxLength: 50
  },
  phone: {
    required: true,
    phone: true
  },
  required: {
    required: true
  },
  optional: {}
};
var FormValidator = exports.FormValidator = function () {
  function FormValidator(validationConfig) {
    (0, _classCallCheck2.default)(this, FormValidator);
    this.errors = {};
    this.touched = {};
    this.validationConfig = validationConfig;
  }
  return (0, _createClass2.default)(FormValidator, [{
    key: "validateField",
    value: function validateField(fieldName, value, formData) {
      var result = _validateField(value, this.validationConfig[fieldName], formData);
      if (result.isValid) {
        delete this.errors[fieldName];
      } else {
        this.errors[fieldName] = result.error;
      }
      return result;
    }
  }, {
    key: "validateForm",
    value: function validateForm(formData) {
      var result = _validateForm(formData, this.validationConfig);
      this.errors = result.errors;
      return result;
    }
  }, {
    key: "setFieldTouched",
    value: function setFieldTouched(fieldName) {
      var touched = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
      this.touched[fieldName] = touched;
    }
  }, {
    key: "getFieldError",
    value: function getFieldError(fieldName) {
      return this.touched[fieldName] ? this.errors[fieldName] : undefined;
    }
  }, {
    key: "getErrors",
    value: function getErrors() {
      return this.errors;
    }
  }, {
    key: "isFieldTouched",
    value: function isFieldTouched(fieldName) {
      return this.touched[fieldName] || false;
    }
  }, {
    key: "reset",
    value: function reset() {
      this.errors = {};
      this.touched = {};
    }
  }]);
}();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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