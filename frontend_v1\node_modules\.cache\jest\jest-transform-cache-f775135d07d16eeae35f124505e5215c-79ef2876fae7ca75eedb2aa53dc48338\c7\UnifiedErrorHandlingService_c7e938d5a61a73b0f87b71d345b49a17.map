{"version": 3, "names": ["_asyncStorage", "_interopRequireDefault", "require", "_netinfo", "_types", "_UserFeedbackService", "_AnalyticsIntegrationService", "_ErrorMonitoringService", "UnifiedErrorHandlingService", "_classCallCheck2", "default", "errorQueue", "errorListeners", "recoveryStrategies", "Map", "breadcrumbs", "isInitialized", "config", "getDefaultConfig", "metrics", "initializeMetrics", "setupRecoveryStrategies", "_createClass2", "key", "value", "_initialize", "_asyncToGenerator2", "userFeedbackService", "initialize", "analyticsIntegrationService", "errorMonitoringService", "loadOfflineErrors", "setupNetworkMonitoring", "setupErrorQueueFlush", "console", "log", "error", "apply", "arguments", "_handleError", "context", "length", "undefined", "userMessage", "unifiedError", "normalizeError", "errorReport", "createErrorReport", "addBreadcrumb", "timestamp", "Date", "now", "category", "message", "type", "level", "data", "errorId", "id", "processError", "handleError", "_x", "_handleNetworkError", "networkContext", "Object", "assign", "ErrorType", "NETWORK", "severity", "ErrorSeverity", "MEDIUM", "handleNetworkError", "_x2", "_handleAuthError", "authContext", "AUTHENTICATION", "HIGH", "handleAuthError", "_x3", "_handleValidationError", "validationContext", "VALIDATION", "LOW", "handleValidationError", "_x4", "_handleWebSocketError", "wsContext", "WEBSOCKET", "handleWebSocketError", "_x5", "breadcrumb", "push", "slice", "addErrorListener", "listener", "_this", "index", "indexOf", "splice", "addRecoveryStrategy", "strategy", "set", "getMetrics", "clearErrorQueue", "AsyncStorage", "removeItem", "updateConfig", "newConfig", "UnifiedError", "UNKNOWN", "errorType", "determineErrorType", "determineSeverity", "toLowerCase", "includes", "AUTHORIZATION", "NOT_FOUND", "TIMEOUT", "THEME", "LAZY_LOADING", "SERVER_ERROR", "SYSTEM", "CRITICAL", "_createErrorReport", "report", "technicalMessage", "stack", "_toConsumableArray2", "recoveryStrategy", "recoveryAttempts", "recovered", "reported", "userFeedback", "createUserFeedbackConfig", "analyticsData", "createAnalyticsData", "_x6", "_x7", "shouldShow", "enableUserFeedback", "showToUser", "title", "getUserFeedbackTitle", "variant", "defaultFeedbackVariant", "dismissible", "autoHide", "<PERSON><PERSON><PERSON><PERSON>", "titles", "_defineProperty2", "_error$context$device", "_error$context$device2", "errorCategory", "component", "screen", "action", "platform", "deviceInfo", "networkStatus", "enableLogging", "enableReporting", "enableRecovery", "enableAnalytics", "userFeedbackThreshold", "maxR<PERSON><PERSON>y<PERSON>tte<PERSON>s", "defaultRetryDelay", "enableProgressiveRetry", "reportingThreshold", "enableOfflineStorage", "maxOfflineErrors", "maxErrorQueueSize", "errorQueueFlushInterval", "enableDebugMode", "__DEV__", "enableStackTrace", "totalErrors", "errorsByType", "errorsBySeverity", "errorsByCategory", "recoverySuccessRate", "averageRecoveryTime", "name", "description", "canRecover", "recover", "_recover", "netInfo", "NetInfo", "fetch", "isConnected", "maxAttempts", "retry<PERSON><PERSON><PERSON>", "progressiveDelay", "_recover2", "_processError", "_errorReport$userFeed", "shift", "updateMetrics", "logError", "attemptRecovery", "showFeedback", "notifyListeners", "storeOfflineError", "reportError", "recordError", "_x8", "logLevel", "getLogLevel", "logMessage", "_attemptRecovery", "_this2", "strategies", "Array", "from", "values", "filter", "_loop", "delay", "Promise", "resolve", "setTimeout", "recoveryError", "warn", "_ret", "_x9", "for<PERSON>ach", "_storeOfflineError", "stored", "getItem", "errors", "JSON", "parse", "setItem", "stringify", "_x0", "_loadOfflineErrors", "_this$errorQueue", "_this3", "addEventListener", "state", "_this4", "setInterval", "unreported", "getMonitoringMetrics", "getErrorMetrics", "getSystemHealth", "getErrorTrends", "days", "_exportErrorData", "format", "exportErrorData", "_x1", "_clearMonitoringHistory", "clearErrorHistory", "clearMonitoringHistory", "_serviceInstance", "unifiedErrorHandlingService", "exports", "_default"], "sources": ["UnifiedErrorHandlingService.ts"], "sourcesContent": ["/**\n * Unified Error Handling Service\n * \n * Central service that consolidates all error handling functionality\n * into a single, consistent system. Replaces multiple overlapping\n * error handling services with a unified approach.\n * \n * @version 2.0.0\n * <AUTHOR> Development Team\n */\n\nimport AsyncStorage from '@react-native-async-storage/async-storage';\nimport { Alert } from 'react-native';\nimport NetInfo from '@react-native-community/netinfo';\n\nimport {\n  UnifiedError,\n  ErrorReport,\n  ErrorContext,\n  ErrorType,\n  ErrorSeverity,\n  ErrorCategory,\n  RecoveryStrategy,\n  UserFeedbackConfig,\n  UnifiedErrorHandlingConfig,\n  ErrorListener,\n  ErrorMetrics,\n  ErrorBreadcrumb\n} from './types';\nimport { userFeedbackService } from './UserFeedbackService';\nimport { analyticsIntegrationService } from './AnalyticsIntegrationService';\nimport { errorMonitoringService } from './ErrorMonitoringService';\n\nclass UnifiedErrorHandlingService {\n  private config: UnifiedErrorHandlingConfig;\n  private errorQueue: ErrorReport[] = [];\n  private errorListeners: ErrorListener[] = [];\n  private recoveryStrategies: Map<string, RecoveryStrategy> = new Map();\n  private breadcrumbs: ErrorBreadcrumb[] = [];\n  private metrics: ErrorMetrics;\n  private isInitialized = false;\n\n  constructor() {\n    this.config = this.getDefaultConfig();\n    this.metrics = this.initializeMetrics();\n    this.setupRecoveryStrategies();\n  }\n\n  /**\n   * Initialize the error handling service\n   */\n  async initialize(): Promise<void> {\n    if (this.isInitialized) return;\n\n    try {\n      // Initialize sub-services\n      await userFeedbackService.initialize?.();\n      await analyticsIntegrationService.initialize();\n      await errorMonitoringService.initialize();\n\n      // Load offline errors\n      await this.loadOfflineErrors();\n\n      // Setup network monitoring\n      this.setupNetworkMonitoring();\n\n      // Setup periodic error queue flush\n      this.setupErrorQueueFlush();\n\n      this.isInitialized = true;\n      console.log('✅ UnifiedErrorHandlingService initialized');\n    } catch (error) {\n      console.error('❌ Failed to initialize UnifiedErrorHandlingService:', error);\n    }\n  }\n\n  /**\n   * Main error handling method\n   */\n  async handleError(\n    error: Error | UnifiedError | string,\n    context: Partial<ErrorContext> = {},\n    userMessage?: string\n  ): Promise<ErrorReport> {\n    // Convert to UnifiedError if needed\n    const unifiedError = this.normalizeError(error, context);\n    \n    // Create error report\n    const errorReport = await this.createErrorReport(unifiedError, userMessage);\n    \n    // Add to breadcrumbs\n    this.addBreadcrumb({\n      timestamp: Date.now(),\n      category: 'system_event',\n      message: `Error handled: ${unifiedError.type}`,\n      level: 'error',\n      data: { errorId: unifiedError.id, type: unifiedError.type }\n    });\n    \n    // Process error through pipeline\n    await this.processError(errorReport);\n    \n    return errorReport;\n  }\n\n  /**\n   * Handle specific error types with optimized processing\n   */\n  async handleNetworkError(error: Error, context: Partial<ErrorContext> = {}): Promise<ErrorReport> {\n    const networkContext = {\n      ...context,\n      type: ErrorType.NETWORK,\n      severity: ErrorSeverity.MEDIUM\n    };\n    \n    return this.handleError(error, networkContext);\n  }\n\n  async handleAuthError(error: Error, context: Partial<ErrorContext> = {}): Promise<ErrorReport> {\n    const authContext = {\n      ...context,\n      type: ErrorType.AUTHENTICATION,\n      severity: ErrorSeverity.HIGH\n    };\n    \n    return this.handleError(error, authContext);\n  }\n\n  async handleValidationError(error: Error, context: Partial<ErrorContext> = {}): Promise<ErrorReport> {\n    const validationContext = {\n      ...context,\n      type: ErrorType.VALIDATION,\n      severity: ErrorSeverity.LOW\n    };\n    \n    return this.handleError(error, validationContext);\n  }\n\n  async handleWebSocketError(error: Error, context: Partial<ErrorContext> = {}): Promise<ErrorReport> {\n    const wsContext = {\n      ...context,\n      type: ErrorType.WEBSOCKET,\n      severity: ErrorSeverity.MEDIUM\n    };\n    \n    return this.handleError(error, wsContext);\n  }\n\n  /**\n   * Add breadcrumb for error tracking\n   */\n  addBreadcrumb(breadcrumb: ErrorBreadcrumb): void {\n    this.breadcrumbs.push(breadcrumb);\n    \n    // Keep only last 50 breadcrumbs\n    if (this.breadcrumbs.length > 50) {\n      this.breadcrumbs = this.breadcrumbs.slice(-50);\n    }\n  }\n\n  /**\n   * Add error listener\n   */\n  addErrorListener(listener: ErrorListener): () => void {\n    this.errorListeners.push(listener);\n    \n    // Return unsubscribe function\n    return () => {\n      const index = this.errorListeners.indexOf(listener);\n      if (index > -1) {\n        this.errorListeners.splice(index, 1);\n      }\n    };\n  }\n\n  /**\n   * Add recovery strategy\n   */\n  addRecoveryStrategy(strategy: RecoveryStrategy): void {\n    this.recoveryStrategies.set(strategy.id, strategy);\n  }\n\n  /**\n   * Get error metrics\n   */\n  getMetrics(): ErrorMetrics {\n    return { ...this.metrics };\n  }\n\n  /**\n   * Clear error queue\n   */\n  clearErrorQueue(): void {\n    this.errorQueue = [];\n    AsyncStorage.removeItem('unified_error_queue');\n  }\n\n  /**\n   * Update configuration\n   */\n  updateConfig(newConfig: Partial<UnifiedErrorHandlingConfig>): void {\n    this.config = { ...this.config, ...newConfig };\n  }\n\n  // Private Methods\n\n  private normalizeError(\n    error: Error | UnifiedError | string,\n    context: Partial<ErrorContext>\n  ): UnifiedError {\n    if (error instanceof UnifiedError) {\n      return error;\n    }\n\n    if (typeof error === 'string') {\n      return new UnifiedError(\n        error,\n        context.type as ErrorType || ErrorType.UNKNOWN,\n        context.severity as ErrorSeverity || ErrorSeverity.MEDIUM,\n        context\n      );\n    }\n\n    // Determine error type from error properties\n    const errorType = this.determineErrorType(error, context);\n    const severity = this.determineSeverity(error, context);\n\n    return new UnifiedError(\n      error.message,\n      errorType,\n      severity,\n      context,\n      error\n    );\n  }\n\n  private determineErrorType(error: Error, context: Partial<ErrorContext>): ErrorType {\n    // Check context first\n    if (context.type) return context.type as ErrorType;\n\n    // Check error message patterns\n    const message = error.message.toLowerCase();\n    \n    if (message.includes('network') || message.includes('fetch')) {\n      return ErrorType.NETWORK;\n    }\n    if (message.includes('unauthorized') || message.includes('401')) {\n      return ErrorType.AUTHENTICATION;\n    }\n    if (message.includes('forbidden') || message.includes('403')) {\n      return ErrorType.AUTHORIZATION;\n    }\n    if (message.includes('not found') || message.includes('404')) {\n      return ErrorType.NOT_FOUND;\n    }\n    if (message.includes('timeout')) {\n      return ErrorType.TIMEOUT;\n    }\n    if (message.includes('websocket') || message.includes('ws')) {\n      return ErrorType.WEBSOCKET;\n    }\n    if (message.includes('theme') || message.includes('colors')) {\n      return ErrorType.THEME;\n    }\n    if (message.includes('lazy') || message.includes('loading')) {\n      return ErrorType.LAZY_LOADING;\n    }\n\n    return ErrorType.UNKNOWN;\n  }\n\n  private determineSeverity(error: Error, context: Partial<ErrorContext>): ErrorSeverity {\n    if (context.severity) return context.severity as ErrorSeverity;\n\n    const errorType = this.determineErrorType(error, context);\n    \n    switch (errorType) {\n      case ErrorType.AUTHENTICATION:\n      case ErrorType.AUTHORIZATION:\n      case ErrorType.SERVER_ERROR:\n        return ErrorSeverity.HIGH;\n      case ErrorType.SYSTEM:\n      case ErrorType.THEME:\n        return ErrorSeverity.CRITICAL;\n      case ErrorType.VALIDATION:\n      case ErrorType.NOT_FOUND:\n        return ErrorSeverity.LOW;\n      default:\n        return ErrorSeverity.MEDIUM;\n    }\n  }\n\n  private async createErrorReport(\n    error: UnifiedError,\n    userMessage?: string\n  ): Promise<ErrorReport> {\n    const report: ErrorReport = {\n      id: error.id,\n      type: error.type,\n      category: error.category,\n      severity: error.severity,\n      message: error.message,\n      technicalMessage: error.technicalMessage,\n      userMessage: userMessage || error.userMessage,\n      stack: error.stack,\n      context: {\n        ...error.context,\n        breadcrumbs: [...this.breadcrumbs]\n      },\n      timestamp: error.timestamp,\n      recoveryStrategy: undefined,\n      recoveryAttempts: 0,\n      recovered: false,\n      reported: false,\n      userFeedback: this.createUserFeedbackConfig(error),\n      analyticsData: this.createAnalyticsData(error)\n    };\n\n    return report;\n  }\n\n  private createUserFeedbackConfig(error: UnifiedError): UserFeedbackConfig {\n    const shouldShow = error.severity !== ErrorSeverity.LOW && this.config.enableUserFeedback;\n    \n    return {\n      showToUser: shouldShow,\n      title: this.getUserFeedbackTitle(error.type),\n      message: error.userMessage,\n      variant: this.config.defaultFeedbackVariant,\n      dismissible: error.severity !== ErrorSeverity.CRITICAL,\n      autoHide: error.severity === ErrorSeverity.LOW,\n      hideDelay: 5000\n    };\n  }\n\n  private getUserFeedbackTitle(type: ErrorType): string {\n    const titles = {\n      [ErrorType.NETWORK]: 'Connection Problem',\n      [ErrorType.AUTHENTICATION]: 'Sign In Required',\n      [ErrorType.AUTHORIZATION]: 'Access Denied',\n      [ErrorType.VALIDATION]: 'Input Error',\n      [ErrorType.NOT_FOUND]: 'Not Found',\n      [ErrorType.SERVER_ERROR]: 'Server Error',\n      [ErrorType.TIMEOUT]: 'Request Timeout',\n      [ErrorType.WEBSOCKET]: 'Connection Lost',\n      [ErrorType.THEME]: 'Display Issue',\n      [ErrorType.LAZY_LOADING]: 'Loading Error'\n    };\n    \n    return titles[type] || 'Error';\n  }\n\n  private createAnalyticsData(error: UnifiedError): Record<string, any> {\n    return {\n      errorType: error.type,\n      errorCategory: error.category,\n      severity: error.severity,\n      component: error.context.component,\n      screen: error.context.screen,\n      action: error.context.action,\n      timestamp: error.timestamp,\n      platform: error.context.deviceInfo?.platform,\n      networkStatus: error.context.deviceInfo?.networkStatus\n    };\n  }\n\n  private getDefaultConfig(): UnifiedErrorHandlingConfig {\n    return {\n      enableLogging: true,\n      enableReporting: true,\n      enableUserFeedback: true,\n      enableRecovery: true,\n      enableAnalytics: true,\n      userFeedbackThreshold: ErrorSeverity.MEDIUM,\n      defaultFeedbackVariant: 'toast',\n      maxRecoveryAttempts: 3,\n      defaultRetryDelay: 2000,\n      enableProgressiveRetry: true,\n      reportingThreshold: ErrorSeverity.MEDIUM,\n      enableOfflineStorage: true,\n      maxOfflineErrors: 100,\n      maxErrorQueueSize: 50,\n      errorQueueFlushInterval: 30000,\n      enableDebugMode: __DEV__,\n      enableStackTrace: __DEV__\n    };\n  }\n\n  private initializeMetrics(): ErrorMetrics {\n    return {\n      totalErrors: 0,\n      errorsByType: {} as Record<ErrorType, number>,\n      errorsBySeverity: {} as Record<ErrorSeverity, number>,\n      errorsByCategory: {} as Record<ErrorCategory, number>,\n      recoverySuccessRate: 0,\n      averageRecoveryTime: 0\n    };\n  }\n\n  private setupRecoveryStrategies(): void {\n    // Network retry strategy\n    this.addRecoveryStrategy({\n      id: 'network_retry',\n      name: 'Network Retry',\n      description: 'Retry network requests after connection is restored',\n      canRecover: (error) => error.type === ErrorType.NETWORK,\n      recover: async () => {\n        // Wait for network connection\n        const netInfo = await NetInfo.fetch();\n        return netInfo.isConnected || false;\n      },\n      maxAttempts: 3,\n      retryDelay: 2000,\n      progressiveDelay: true\n    });\n\n    // WebSocket reconnection strategy\n    this.addRecoveryStrategy({\n      id: 'websocket_reconnect',\n      name: 'WebSocket Reconnect',\n      description: 'Reconnect WebSocket connections',\n      canRecover: (error) => error.type === ErrorType.WEBSOCKET,\n      recover: async () => {\n        // Implementation will be added when integrating with WebSocket services\n        return true;\n      },\n      maxAttempts: 5,\n      retryDelay: 1000,\n      progressiveDelay: true\n    });\n  }\n\n  private async processError(errorReport: ErrorReport): Promise<void> {\n    // Add to queue\n    this.errorQueue.push(errorReport);\n    \n    // Maintain queue size\n    if (this.errorQueue.length > this.config.maxErrorQueueSize) {\n      this.errorQueue.shift();\n    }\n\n    // Update metrics\n    this.updateMetrics(errorReport);\n\n    // Log error\n    if (this.config.enableLogging) {\n      this.logError(errorReport);\n    }\n\n    // Attempt recovery\n    if (this.config.enableRecovery) {\n      await this.attemptRecovery(errorReport);\n    }\n\n    // Show user feedback\n    if (this.config.enableUserFeedback && errorReport.userFeedback?.showToUser) {\n      await userFeedbackService.showFeedback(errorReport);\n    }\n\n    // Notify listeners\n    this.notifyListeners(errorReport);\n\n    // Store offline if needed\n    if (this.config.enableOfflineStorage) {\n      await this.storeOfflineError(errorReport);\n    }\n\n    // Report to analytics\n    if (this.config.enableReporting && errorReport.severity >= this.config.reportingThreshold) {\n      await analyticsIntegrationService.reportError(errorReport);\n    }\n\n    // Record in monitoring service\n    await errorMonitoringService.recordError(errorReport);\n  }\n\n  private updateMetrics(errorReport: ErrorReport): void {\n    this.metrics.totalErrors++;\n    \n    // Update by type\n    this.metrics.errorsByType[errorReport.type] = \n      (this.metrics.errorsByType[errorReport.type] || 0) + 1;\n    \n    // Update by severity\n    this.metrics.errorsBySeverity[errorReport.severity] = \n      (this.metrics.errorsBySeverity[errorReport.severity] || 0) + 1;\n    \n    // Update by category\n    this.metrics.errorsByCategory[errorReport.category] = \n      (this.metrics.errorsByCategory[errorReport.category] || 0) + 1;\n  }\n\n  private logError(errorReport: ErrorReport): void {\n    const logLevel = this.getLogLevel(errorReport.severity);\n    const logMessage = `[UnifiedErrorHandling] ${errorReport.type}: ${errorReport.message}`;\n    \n    console[logLevel](logMessage, {\n      id: errorReport.id,\n      type: errorReport.type,\n      severity: errorReport.severity,\n      context: errorReport.context,\n      stack: this.config.enableStackTrace ? errorReport.stack : undefined\n    });\n  }\n\n  private getLogLevel(severity: ErrorSeverity): 'log' | 'warn' | 'error' {\n    switch (severity) {\n      case ErrorSeverity.LOW:\n        return 'log';\n      case ErrorSeverity.MEDIUM:\n        return 'warn';\n      case ErrorSeverity.HIGH:\n      case ErrorSeverity.CRITICAL:\n        return 'error';\n      default:\n        return 'warn';\n    }\n  }\n\n  private async attemptRecovery(errorReport: ErrorReport): Promise<void> {\n    // Find applicable recovery strategies\n    const strategies = Array.from(this.recoveryStrategies.values())\n      .filter(strategy => strategy.canRecover(errorReport as any, errorReport.context));\n\n    for (const strategy of strategies) {\n      if (errorReport.recoveryAttempts >= strategy.maxAttempts) continue;\n\n      try {\n        errorReport.recoveryAttempts++;\n        \n        // Calculate delay\n        const delay = strategy.progressiveDelay \n          ? strategy.retryDelay! * errorReport.recoveryAttempts\n          : strategy.retryDelay || this.config.defaultRetryDelay;\n\n        // Wait before retry\n        await new Promise(resolve => setTimeout(resolve, delay));\n\n        // Attempt recovery\n        const recovered = await strategy.recover(errorReport as any, errorReport.context);\n        \n        if (recovered) {\n          errorReport.recovered = true;\n          errorReport.recoveryStrategy = strategy.name;\n          \n          console.log(`✅ Error recovered using strategy: ${strategy.name}`);\n          break;\n        }\n      } catch (recoveryError) {\n        console.warn(`❌ Recovery strategy failed: ${strategy.name}`, recoveryError);\n      }\n    }\n  }\n\n  // Removed showUserFeedback method - now handled by UserFeedbackService\n\n  private notifyListeners(errorReport: ErrorReport): void {\n    this.errorListeners.forEach(listener => {\n      try {\n        listener(errorReport);\n      } catch (error) {\n        console.warn('Error listener failed:', error);\n      }\n    });\n  }\n\n  private async storeOfflineError(errorReport: ErrorReport): Promise<void> {\n    try {\n      const stored = await AsyncStorage.getItem('unified_error_queue');\n      const errors = stored ? JSON.parse(stored) : [];\n      \n      errors.push(errorReport);\n      \n      // Maintain max offline errors\n      if (errors.length > this.config.maxOfflineErrors) {\n        errors.splice(0, errors.length - this.config.maxOfflineErrors);\n      }\n      \n      await AsyncStorage.setItem('unified_error_queue', JSON.stringify(errors));\n    } catch (error) {\n      console.warn('Failed to store offline error:', error);\n    }\n  }\n\n  private async loadOfflineErrors(): Promise<void> {\n    try {\n      const stored = await AsyncStorage.getItem('unified_error_queue');\n      if (stored) {\n        const errors = JSON.parse(stored);\n        this.errorQueue.push(...errors);\n        \n        // Clear offline storage\n        await AsyncStorage.removeItem('unified_error_queue');\n      }\n    } catch (error) {\n      console.warn('Failed to load offline errors:', error);\n    }\n  }\n\n  // Removed reportError method - now handled by AnalyticsIntegrationService\n\n  private setupNetworkMonitoring(): void {\n    NetInfo.addEventListener(state => {\n      // Update device info for all future errors\n      if (this.config.enableLogging) {\n        console.log('📶 Network status changed:', state.isConnected ? 'online' : 'offline');\n      }\n    });\n  }\n\n  private setupErrorQueueFlush(): void {\n    setInterval(() => {\n      if (this.errorQueue.length > 0 && this.config.enableReporting) {\n        // Process any unreported errors\n        const unreported = this.errorQueue.filter(error => !error.reported);\n        unreported.forEach(error => analyticsIntegrationService.reportError(error));\n      }\n    }, this.config.errorQueueFlushInterval);\n  }\n\n  /**\n   * Get comprehensive error monitoring metrics\n   */\n  getMonitoringMetrics(): ErrorMetrics {\n    return errorMonitoringService.getErrorMetrics();\n  }\n\n  /**\n   * Get system health assessment\n   */\n  getSystemHealth() {\n    return errorMonitoringService.getSystemHealth();\n  }\n\n  /**\n   * Get error trends over time\n   */\n  getErrorTrends(days?: number) {\n    return errorMonitoringService.getErrorTrends(days);\n  }\n\n  /**\n   * Export error data for analysis\n   */\n  async exportErrorData(format?: 'json' | 'csv') {\n    return errorMonitoringService.exportErrorData(format);\n  }\n\n  /**\n   * Clear error monitoring history\n   */\n  async clearMonitoringHistory() {\n    return errorMonitoringService.clearErrorHistory();\n  }\n}\n\n// Export singleton instance with Hermes compatibility\nlet _serviceInstance: UnifiedErrorHandlingService | null = null;\n\nexport const unifiedErrorHandlingService = (() => {\n  if (!_serviceInstance) {\n    _serviceInstance = new UnifiedErrorHandlingService();\n  }\n  return _serviceInstance;\n})();\n\nexport default unifiedErrorHandlingService;\n"], "mappings": ";;;;;;;;;;AAWA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,QAAA,GAAAF,sBAAA,CAAAC,OAAA;AAEA,IAAAE,MAAA,GAAAF,OAAA;AAcA,IAAAG,oBAAA,GAAAH,OAAA;AACA,IAAAI,4BAAA,GAAAJ,OAAA;AACA,IAAAK,uBAAA,GAAAL,OAAA;AAAkE,IAE5DM,2BAA2B;EAS/B,SAAAA,4BAAA,EAAc;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAF,2BAAA;IAAA,KAPNG,UAAU,GAAkB,EAAE;IAAA,KAC9BC,cAAc,GAAoB,EAAE;IAAA,KACpCC,kBAAkB,GAAkC,IAAIC,GAAG,CAAC,CAAC;IAAA,KAC7DC,WAAW,GAAsB,EAAE;IAAA,KAEnCC,aAAa,GAAG,KAAK;IAG3B,IAAI,CAACC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACrC,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACvC,IAAI,CAACC,uBAAuB,CAAC,CAAC;EAChC;EAAC,WAAAC,aAAA,CAAAZ,OAAA,EAAAF,2BAAA;IAAAe,GAAA;IAAAC,KAAA;MAAA,IAAAC,WAAA,OAAAC,kBAAA,CAAAhB,OAAA,EAKD,aAAkC;QAChC,IAAI,IAAI,CAACM,aAAa,EAAE;QAExB,IAAI;UAEF,MAAMW,wCAAmB,CAACC,UAAU,oBAA9BD,wCAAmB,CAACC,UAAU,CAAG,CAAC;UACxC,MAAMC,wDAA2B,CAACD,UAAU,CAAC,CAAC;UAC9C,MAAME,8CAAsB,CAACF,UAAU,CAAC,CAAC;UAGzC,MAAM,IAAI,CAACG,iBAAiB,CAAC,CAAC;UAG9B,IAAI,CAACC,sBAAsB,CAAC,CAAC;UAG7B,IAAI,CAACC,oBAAoB,CAAC,CAAC;UAE3B,IAAI,CAACjB,aAAa,GAAG,IAAI;UACzBkB,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;QAC1D,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,qDAAqD,EAAEA,KAAK,CAAC;QAC7E;MACF,CAAC;MAAA,SAvBKR,UAAUA,CAAA;QAAA,OAAAH,WAAA,CAAAY,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAVV,UAAU;IAAA;EAAA;IAAAL,GAAA;IAAAC,KAAA;MAAA,IAAAe,YAAA,OAAAb,kBAAA,CAAAhB,OAAA,EA4BhB,WACE0B,KAAoC,EAGd;QAAA,IAFtBI,OAA8B,GAAAF,SAAA,CAAAG,MAAA,QAAAH,SAAA,QAAAI,SAAA,GAAAJ,SAAA,MAAG,CAAC,CAAC;QAAA,IACnCK,WAAoB,GAAAL,SAAA,CAAAG,MAAA,OAAAH,SAAA,MAAAI,SAAA;QAGpB,IAAME,YAAY,GAAG,IAAI,CAACC,cAAc,CAACT,KAAK,EAAEI,OAAO,CAAC;QAGxD,IAAMM,WAAW,SAAS,IAAI,CAACC,iBAAiB,CAACH,YAAY,EAAED,WAAW,CAAC;QAG3E,IAAI,CAACK,aAAa,CAAC;UACjBC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;UACrBC,QAAQ,EAAE,cAAc;UACxBC,OAAO,EAAE,kBAAkBT,YAAY,CAACU,IAAI,EAAE;UAC9CC,KAAK,EAAE,OAAO;UACdC,IAAI,EAAE;YAAEC,OAAO,EAAEb,YAAY,CAACc,EAAE;YAAEJ,IAAI,EAAEV,YAAY,CAACU;UAAK;QAC5D,CAAC,CAAC;QAGF,MAAM,IAAI,CAACK,YAAY,CAACb,WAAW,CAAC;QAEpC,OAAOA,WAAW;MACpB,CAAC;MAAA,SAxBKc,WAAWA,CAAAC,EAAA;QAAA,OAAAtB,YAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAXsB,WAAW;IAAA;EAAA;IAAArC,GAAA;IAAAC,KAAA;MAAA,IAAAsC,mBAAA,OAAApC,kBAAA,CAAAhB,OAAA,EA6BjB,WAAyB0B,KAAY,EAA6D;QAAA,IAA3DI,OAA8B,GAAAF,SAAA,CAAAG,MAAA,QAAAH,SAAA,QAAAI,SAAA,GAAAJ,SAAA,MAAG,CAAC,CAAC;QACxE,IAAMyB,cAAc,GAAAC,MAAA,CAAAC,MAAA,KACfzB,OAAO;UACVc,IAAI,EAAEY,gBAAS,CAACC,OAAO;UACvBC,QAAQ,EAAEC,oBAAa,CAACC;QAAM,EAC/B;QAED,OAAO,IAAI,CAACV,WAAW,CAACxB,KAAK,EAAE2B,cAAc,CAAC;MAChD,CAAC;MAAA,SARKQ,kBAAkBA,CAAAC,GAAA;QAAA,OAAAV,mBAAA,CAAAzB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAlBiC,kBAAkB;IAAA;EAAA;IAAAhD,GAAA;IAAAC,KAAA;MAAA,IAAAiD,gBAAA,OAAA/C,kBAAA,CAAAhB,OAAA,EAUxB,WAAsB0B,KAAY,EAA6D;QAAA,IAA3DI,OAA8B,GAAAF,SAAA,CAAAG,MAAA,QAAAH,SAAA,QAAAI,SAAA,GAAAJ,SAAA,MAAG,CAAC,CAAC;QACrE,IAAMoC,WAAW,GAAAV,MAAA,CAAAC,MAAA,KACZzB,OAAO;UACVc,IAAI,EAAEY,gBAAS,CAACS,cAAc;UAC9BP,QAAQ,EAAEC,oBAAa,CAACO;QAAI,EAC7B;QAED,OAAO,IAAI,CAAChB,WAAW,CAACxB,KAAK,EAAEsC,WAAW,CAAC;MAC7C,CAAC;MAAA,SARKG,eAAeA,CAAAC,GAAA;QAAA,OAAAL,gBAAA,CAAApC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAfuC,eAAe;IAAA;EAAA;IAAAtD,GAAA;IAAAC,KAAA;MAAA,IAAAuD,sBAAA,OAAArD,kBAAA,CAAAhB,OAAA,EAUrB,WAA4B0B,KAAY,EAA6D;QAAA,IAA3DI,OAA8B,GAAAF,SAAA,CAAAG,MAAA,QAAAH,SAAA,QAAAI,SAAA,GAAAJ,SAAA,MAAG,CAAC,CAAC;QAC3E,IAAM0C,iBAAiB,GAAAhB,MAAA,CAAAC,MAAA,KAClBzB,OAAO;UACVc,IAAI,EAAEY,gBAAS,CAACe,UAAU;UAC1Bb,QAAQ,EAAEC,oBAAa,CAACa;QAAG,EAC5B;QAED,OAAO,IAAI,CAACtB,WAAW,CAACxB,KAAK,EAAE4C,iBAAiB,CAAC;MACnD,CAAC;MAAA,SARKG,qBAAqBA,CAAAC,GAAA;QAAA,OAAAL,sBAAA,CAAA1C,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArB6C,qBAAqB;IAAA;EAAA;IAAA5D,GAAA;IAAAC,KAAA;MAAA,IAAA6D,qBAAA,OAAA3D,kBAAA,CAAAhB,OAAA,EAU3B,WAA2B0B,KAAY,EAA6D;QAAA,IAA3DI,OAA8B,GAAAF,SAAA,CAAAG,MAAA,QAAAH,SAAA,QAAAI,SAAA,GAAAJ,SAAA,MAAG,CAAC,CAAC;QAC1E,IAAMgD,SAAS,GAAAtB,MAAA,CAAAC,MAAA,KACVzB,OAAO;UACVc,IAAI,EAAEY,gBAAS,CAACqB,SAAS;UACzBnB,QAAQ,EAAEC,oBAAa,CAACC;QAAM,EAC/B;QAED,OAAO,IAAI,CAACV,WAAW,CAACxB,KAAK,EAAEkD,SAAS,CAAC;MAC3C,CAAC;MAAA,SARKE,oBAAoBA,CAAAC,GAAA;QAAA,OAAAJ,qBAAA,CAAAhD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBkD,oBAAoB;IAAA;EAAA;IAAAjE,GAAA;IAAAC,KAAA,EAa1B,SAAAwB,aAAaA,CAAC0C,UAA2B,EAAQ;MAC/C,IAAI,CAAC3E,WAAW,CAAC4E,IAAI,CAACD,UAAU,CAAC;MAGjC,IAAI,IAAI,CAAC3E,WAAW,CAAC0B,MAAM,GAAG,EAAE,EAAE;QAChC,IAAI,CAAC1B,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC6E,KAAK,CAAC,CAAC,EAAE,CAAC;MAChD;IACF;EAAC;IAAArE,GAAA;IAAAC,KAAA,EAKD,SAAAqE,gBAAgBA,CAACC,QAAuB,EAAc;MAAA,IAAAC,KAAA;MACpD,IAAI,CAACnF,cAAc,CAAC+E,IAAI,CAACG,QAAQ,CAAC;MAGlC,OAAO,YAAM;QACX,IAAME,KAAK,GAAGD,KAAI,CAACnF,cAAc,CAACqF,OAAO,CAACH,QAAQ,CAAC;QACnD,IAAIE,KAAK,GAAG,CAAC,CAAC,EAAE;UACdD,KAAI,CAACnF,cAAc,CAACsF,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QACtC;MACF,CAAC;IACH;EAAC;IAAAzE,GAAA;IAAAC,KAAA,EAKD,SAAA2E,mBAAmBA,CAACC,QAA0B,EAAQ;MACpD,IAAI,CAACvF,kBAAkB,CAACwF,GAAG,CAACD,QAAQ,CAAC1C,EAAE,EAAE0C,QAAQ,CAAC;IACpD;EAAC;IAAA7E,GAAA;IAAAC,KAAA,EAKD,SAAA8E,UAAUA,CAAA,EAAiB;MACzB,OAAAtC,MAAA,CAAAC,MAAA,KAAY,IAAI,CAAC9C,OAAO;IAC1B;EAAC;IAAAI,GAAA;IAAAC,KAAA,EAKD,SAAA+E,eAAeA,CAAA,EAAS;MACtB,IAAI,CAAC5F,UAAU,GAAG,EAAE;MACpB6F,qBAAY,CAACC,UAAU,CAAC,qBAAqB,CAAC;IAChD;EAAC;IAAAlF,GAAA;IAAAC,KAAA,EAKD,SAAAkF,YAAYA,CAACC,SAA8C,EAAQ;MACjE,IAAI,CAAC1F,MAAM,GAAA+C,MAAA,CAAAC,MAAA,KAAQ,IAAI,CAAChD,MAAM,EAAK0F,SAAS,CAAE;IAChD;EAAC;IAAApF,GAAA;IAAAC,KAAA,EAID,SAAQqB,cAAcA,CACpBT,KAAoC,EACpCI,OAA8B,EAChB;MACd,IAAIJ,KAAK,YAAYwE,mBAAY,EAAE;QACjC,OAAOxE,KAAK;MACd;MAEA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAO,IAAIwE,mBAAY,CACrBxE,KAAK,EACLI,OAAO,CAACc,IAAI,IAAiBY,gBAAS,CAAC2C,OAAO,EAC9CrE,OAAO,CAAC4B,QAAQ,IAAqBC,oBAAa,CAACC,MAAM,EACzD9B,OACF,CAAC;MACH;MAGA,IAAMsE,SAAS,GAAG,IAAI,CAACC,kBAAkB,CAAC3E,KAAK,EAAEI,OAAO,CAAC;MACzD,IAAM4B,QAAQ,GAAG,IAAI,CAAC4C,iBAAiB,CAAC5E,KAAK,EAAEI,OAAO,CAAC;MAEvD,OAAO,IAAIoE,mBAAY,CACrBxE,KAAK,CAACiB,OAAO,EACbyD,SAAS,EACT1C,QAAQ,EACR5B,OAAO,EACPJ,KACF,CAAC;IACH;EAAC;IAAAb,GAAA;IAAAC,KAAA,EAED,SAAQuF,kBAAkBA,CAAC3E,KAAY,EAAEI,OAA8B,EAAa;MAElF,IAAIA,OAAO,CAACc,IAAI,EAAE,OAAOd,OAAO,CAACc,IAAI;MAGrC,IAAMD,OAAO,GAAGjB,KAAK,CAACiB,OAAO,CAAC4D,WAAW,CAAC,CAAC;MAE3C,IAAI5D,OAAO,CAAC6D,QAAQ,CAAC,SAAS,CAAC,IAAI7D,OAAO,CAAC6D,QAAQ,CAAC,OAAO,CAAC,EAAE;QAC5D,OAAOhD,gBAAS,CAACC,OAAO;MAC1B;MACA,IAAId,OAAO,CAAC6D,QAAQ,CAAC,cAAc,CAAC,IAAI7D,OAAO,CAAC6D,QAAQ,CAAC,KAAK,CAAC,EAAE;QAC/D,OAAOhD,gBAAS,CAACS,cAAc;MACjC;MACA,IAAItB,OAAO,CAAC6D,QAAQ,CAAC,WAAW,CAAC,IAAI7D,OAAO,CAAC6D,QAAQ,CAAC,KAAK,CAAC,EAAE;QAC5D,OAAOhD,gBAAS,CAACiD,aAAa;MAChC;MACA,IAAI9D,OAAO,CAAC6D,QAAQ,CAAC,WAAW,CAAC,IAAI7D,OAAO,CAAC6D,QAAQ,CAAC,KAAK,CAAC,EAAE;QAC5D,OAAOhD,gBAAS,CAACkD,SAAS;MAC5B;MACA,IAAI/D,OAAO,CAAC6D,QAAQ,CAAC,SAAS,CAAC,EAAE;QAC/B,OAAOhD,gBAAS,CAACmD,OAAO;MAC1B;MACA,IAAIhE,OAAO,CAAC6D,QAAQ,CAAC,WAAW,CAAC,IAAI7D,OAAO,CAAC6D,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC3D,OAAOhD,gBAAS,CAACqB,SAAS;MAC5B;MACA,IAAIlC,OAAO,CAAC6D,QAAQ,CAAC,OAAO,CAAC,IAAI7D,OAAO,CAAC6D,QAAQ,CAAC,QAAQ,CAAC,EAAE;QAC3D,OAAOhD,gBAAS,CAACoD,KAAK;MACxB;MACA,IAAIjE,OAAO,CAAC6D,QAAQ,CAAC,MAAM,CAAC,IAAI7D,OAAO,CAAC6D,QAAQ,CAAC,SAAS,CAAC,EAAE;QAC3D,OAAOhD,gBAAS,CAACqD,YAAY;MAC/B;MAEA,OAAOrD,gBAAS,CAAC2C,OAAO;IAC1B;EAAC;IAAAtF,GAAA;IAAAC,KAAA,EAED,SAAQwF,iBAAiBA,CAAC5E,KAAY,EAAEI,OAA8B,EAAiB;MACrF,IAAIA,OAAO,CAAC4B,QAAQ,EAAE,OAAO5B,OAAO,CAAC4B,QAAQ;MAE7C,IAAM0C,SAAS,GAAG,IAAI,CAACC,kBAAkB,CAAC3E,KAAK,EAAEI,OAAO,CAAC;MAEzD,QAAQsE,SAAS;QACf,KAAK5C,gBAAS,CAACS,cAAc;QAC7B,KAAKT,gBAAS,CAACiD,aAAa;QAC5B,KAAKjD,gBAAS,CAACsD,YAAY;UACzB,OAAOnD,oBAAa,CAACO,IAAI;QAC3B,KAAKV,gBAAS,CAACuD,MAAM;QACrB,KAAKvD,gBAAS,CAACoD,KAAK;UAClB,OAAOjD,oBAAa,CAACqD,QAAQ;QAC/B,KAAKxD,gBAAS,CAACe,UAAU;QACzB,KAAKf,gBAAS,CAACkD,SAAS;UACtB,OAAO/C,oBAAa,CAACa,GAAG;QAC1B;UACE,OAAOb,oBAAa,CAACC,MAAM;MAC/B;IACF;EAAC;IAAA/C,GAAA;IAAAC,KAAA;MAAA,IAAAmG,kBAAA,OAAAjG,kBAAA,CAAAhB,OAAA,EAED,WACE0B,KAAmB,EACnBO,WAAoB,EACE;QACtB,IAAMiF,MAAmB,GAAG;UAC1BlE,EAAE,EAAEtB,KAAK,CAACsB,EAAE;UACZJ,IAAI,EAAElB,KAAK,CAACkB,IAAI;UAChBF,QAAQ,EAAEhB,KAAK,CAACgB,QAAQ;UACxBgB,QAAQ,EAAEhC,KAAK,CAACgC,QAAQ;UACxBf,OAAO,EAAEjB,KAAK,CAACiB,OAAO;UACtBwE,gBAAgB,EAAEzF,KAAK,CAACyF,gBAAgB;UACxClF,WAAW,EAAEA,WAAW,IAAIP,KAAK,CAACO,WAAW;UAC7CmF,KAAK,EAAE1F,KAAK,CAAC0F,KAAK;UAClBtF,OAAO,EAAAwB,MAAA,CAAAC,MAAA,KACF7B,KAAK,CAACI,OAAO;YAChBzB,WAAW,MAAAgH,mBAAA,CAAArH,OAAA,EAAM,IAAI,CAACK,WAAW;UAAC,EACnC;UACDkC,SAAS,EAAEb,KAAK,CAACa,SAAS;UAC1B+E,gBAAgB,EAAEtF,SAAS;UAC3BuF,gBAAgB,EAAE,CAAC;UACnBC,SAAS,EAAE,KAAK;UAChBC,QAAQ,EAAE,KAAK;UACfC,YAAY,EAAE,IAAI,CAACC,wBAAwB,CAACjG,KAAK,CAAC;UAClDkG,aAAa,EAAE,IAAI,CAACC,mBAAmB,CAACnG,KAAK;QAC/C,CAAC;QAED,OAAOwF,MAAM;MACf,CAAC;MAAA,SA3Ba7E,iBAAiBA,CAAAyF,GAAA,EAAAC,GAAA;QAAA,OAAAd,kBAAA,CAAAtF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjBS,iBAAiB;IAAA;EAAA;IAAAxB,GAAA;IAAAC,KAAA,EA6B/B,SAAQ6G,wBAAwBA,CAACjG,KAAmB,EAAsB;MACxE,IAAMsG,UAAU,GAAGtG,KAAK,CAACgC,QAAQ,KAAKC,oBAAa,CAACa,GAAG,IAAI,IAAI,CAACjE,MAAM,CAAC0H,kBAAkB;MAEzF,OAAO;QACLC,UAAU,EAAEF,UAAU;QACtBG,KAAK,EAAE,IAAI,CAACC,oBAAoB,CAAC1G,KAAK,CAACkB,IAAI,CAAC;QAC5CD,OAAO,EAAEjB,KAAK,CAACO,WAAW;QAC1BoG,OAAO,EAAE,IAAI,CAAC9H,MAAM,CAAC+H,sBAAsB;QAC3CC,WAAW,EAAE7G,KAAK,CAACgC,QAAQ,KAAKC,oBAAa,CAACqD,QAAQ;QACtDwB,QAAQ,EAAE9G,KAAK,CAACgC,QAAQ,KAAKC,oBAAa,CAACa,GAAG;QAC9CiE,SAAS,EAAE;MACb,CAAC;IACH;EAAC;IAAA5H,GAAA;IAAAC,KAAA,EAED,SAAQsH,oBAAoBA,CAACxF,IAAe,EAAU;MACpD,IAAM8F,MAAM,OAAAC,gBAAA,CAAA3I,OAAA,MAAA2I,gBAAA,CAAA3I,OAAA,MAAA2I,gBAAA,CAAA3I,OAAA,MAAA2I,gBAAA,CAAA3I,OAAA,MAAA2I,gBAAA,CAAA3I,OAAA,MAAA2I,gBAAA,CAAA3I,OAAA,MAAA2I,gBAAA,CAAA3I,OAAA,MAAA2I,gBAAA,CAAA3I,OAAA,MAAA2I,gBAAA,CAAA3I,OAAA,MAAA2I,gBAAA,CAAA3I,OAAA,MACTwD,gBAAS,CAACC,OAAO,EAAG,oBAAoB,GACxCD,gBAAS,CAACS,cAAc,EAAG,kBAAkB,GAC7CT,gBAAS,CAACiD,aAAa,EAAG,eAAe,GACzCjD,gBAAS,CAACe,UAAU,EAAG,aAAa,GACpCf,gBAAS,CAACkD,SAAS,EAAG,WAAW,GACjClD,gBAAS,CAACsD,YAAY,EAAG,cAAc,GACvCtD,gBAAS,CAACmD,OAAO,EAAG,iBAAiB,GACrCnD,gBAAS,CAACqB,SAAS,EAAG,iBAAiB,GACvCrB,gBAAS,CAACoD,KAAK,EAAG,eAAe,GACjCpD,gBAAS,CAACqD,YAAY,EAAG,eAAe,CAC1C;MAED,OAAO6B,MAAM,CAAC9F,IAAI,CAAC,IAAI,OAAO;IAChC;EAAC;IAAA/B,GAAA;IAAAC,KAAA,EAED,SAAQ+G,mBAAmBA,CAACnG,KAAmB,EAAuB;MAAA,IAAAkH,qBAAA,EAAAC,sBAAA;MACpE,OAAO;QACLzC,SAAS,EAAE1E,KAAK,CAACkB,IAAI;QACrBkG,aAAa,EAAEpH,KAAK,CAACgB,QAAQ;QAC7BgB,QAAQ,EAAEhC,KAAK,CAACgC,QAAQ;QACxBqF,SAAS,EAAErH,KAAK,CAACI,OAAO,CAACiH,SAAS;QAClCC,MAAM,EAAEtH,KAAK,CAACI,OAAO,CAACkH,MAAM;QAC5BC,MAAM,EAAEvH,KAAK,CAACI,OAAO,CAACmH,MAAM;QAC5B1G,SAAS,EAAEb,KAAK,CAACa,SAAS;QAC1B2G,QAAQ,GAAAN,qBAAA,GAAElH,KAAK,CAACI,OAAO,CAACqH,UAAU,qBAAxBP,qBAAA,CAA0BM,QAAQ;QAC5CE,aAAa,GAAAP,sBAAA,GAAEnH,KAAK,CAACI,OAAO,CAACqH,UAAU,qBAAxBN,sBAAA,CAA0BO;MAC3C,CAAC;IACH;EAAC;IAAAvI,GAAA;IAAAC,KAAA,EAED,SAAQN,gBAAgBA,CAAA,EAA+B;MACrD,OAAO;QACL6I,aAAa,EAAE,IAAI;QACnBC,eAAe,EAAE,IAAI;QACrBrB,kBAAkB,EAAE,IAAI;QACxBsB,cAAc,EAAE,IAAI;QACpBC,eAAe,EAAE,IAAI;QACrBC,qBAAqB,EAAE9F,oBAAa,CAACC,MAAM;QAC3C0E,sBAAsB,EAAE,OAAO;QAC/BoB,mBAAmB,EAAE,CAAC;QACtBC,iBAAiB,EAAE,IAAI;QACvBC,sBAAsB,EAAE,IAAI;QAC5BC,kBAAkB,EAAElG,oBAAa,CAACC,MAAM;QACxCkG,oBAAoB,EAAE,IAAI;QAC1BC,gBAAgB,EAAE,GAAG;QACrBC,iBAAiB,EAAE,EAAE;QACrBC,uBAAuB,EAAE,KAAK;QAC9BC,eAAe,EAAEC,OAAO;QACxBC,gBAAgB,EAAED;MACpB,CAAC;IACH;EAAC;IAAAtJ,GAAA;IAAAC,KAAA,EAED,SAAQJ,iBAAiBA,CAAA,EAAiB;MACxC,OAAO;QACL2J,WAAW,EAAE,CAAC;QACdC,YAAY,EAAE,CAAC,CAA8B;QAC7CC,gBAAgB,EAAE,CAAC,CAAkC;QACrDC,gBAAgB,EAAE,CAAC,CAAkC;QACrDC,mBAAmB,EAAE,CAAC;QACtBC,mBAAmB,EAAE;MACvB,CAAC;IACH;EAAC;IAAA7J,GAAA;IAAAC,KAAA,EAED,SAAQH,uBAAuBA,CAAA,EAAS;MAEtC,IAAI,CAAC8E,mBAAmB,CAAC;QACvBzC,EAAE,EAAE,eAAe;QACnB2H,IAAI,EAAE,eAAe;QACrBC,WAAW,EAAE,qDAAqD;QAClEC,UAAU,EAAE,SAAZA,UAAUA,CAAGnJ,KAAK;UAAA,OAAKA,KAAK,CAACkB,IAAI,KAAKY,gBAAS,CAACC,OAAO;QAAA;QACvDqH,OAAO;UAAA,IAAAC,QAAA,OAAA/J,kBAAA,CAAAhB,OAAA,EAAE,aAAY;YAEnB,IAAMgL,OAAO,SAASC,gBAAO,CAACC,KAAK,CAAC,CAAC;YACrC,OAAOF,OAAO,CAACG,WAAW,IAAI,KAAK;UACrC,CAAC;UAAA,SAJDL,OAAOA,CAAA;YAAA,OAAAC,QAAA,CAAApJ,KAAA,OAAAC,SAAA;UAAA;UAAA,OAAPkJ,OAAO;QAAA,GAIN;QACDM,WAAW,EAAE,CAAC;QACdC,UAAU,EAAE,IAAI;QAChBC,gBAAgB,EAAE;MACpB,CAAC,CAAC;MAGF,IAAI,CAAC7F,mBAAmB,CAAC;QACvBzC,EAAE,EAAE,qBAAqB;QACzB2H,IAAI,EAAE,qBAAqB;QAC3BC,WAAW,EAAE,iCAAiC;QAC9CC,UAAU,EAAE,SAAZA,UAAUA,CAAGnJ,KAAK;UAAA,OAAKA,KAAK,CAACkB,IAAI,KAAKY,gBAAS,CAACqB,SAAS;QAAA;QACzDiG,OAAO;UAAA,IAAAS,SAAA,OAAAvK,kBAAA,CAAAhB,OAAA,EAAE,aAAY;YAEnB,OAAO,IAAI;UACb,CAAC;UAAA,SAHD8K,OAAOA,CAAA;YAAA,OAAAS,SAAA,CAAA5J,KAAA,OAAAC,SAAA;UAAA;UAAA,OAAPkJ,OAAO;QAAA,GAGN;QACDM,WAAW,EAAE,CAAC;QACdC,UAAU,EAAE,IAAI;QAChBC,gBAAgB,EAAE;MACpB,CAAC,CAAC;IACJ;EAAC;IAAAzK,GAAA;IAAAC,KAAA;MAAA,IAAA0K,aAAA,OAAAxK,kBAAA,CAAAhB,OAAA,EAED,WAA2BoC,WAAwB,EAAiB;QAAA,IAAAqJ,qBAAA;QAElE,IAAI,CAACxL,UAAU,CAACgF,IAAI,CAAC7C,WAAW,CAAC;QAGjC,IAAI,IAAI,CAACnC,UAAU,CAAC8B,MAAM,GAAG,IAAI,CAACxB,MAAM,CAACyJ,iBAAiB,EAAE;UAC1D,IAAI,CAAC/J,UAAU,CAACyL,KAAK,CAAC,CAAC;QACzB;QAGA,IAAI,CAACC,aAAa,CAACvJ,WAAW,CAAC;QAG/B,IAAI,IAAI,CAAC7B,MAAM,CAAC8I,aAAa,EAAE;UAC7B,IAAI,CAACuC,QAAQ,CAACxJ,WAAW,CAAC;QAC5B;QAGA,IAAI,IAAI,CAAC7B,MAAM,CAACgJ,cAAc,EAAE;UAC9B,MAAM,IAAI,CAACsC,eAAe,CAACzJ,WAAW,CAAC;QACzC;QAGA,IAAI,IAAI,CAAC7B,MAAM,CAAC0H,kBAAkB,KAAAwD,qBAAA,GAAIrJ,WAAW,CAACsF,YAAY,aAAxB+D,qBAAA,CAA0BvD,UAAU,EAAE;UAC1E,MAAMjH,wCAAmB,CAAC6K,YAAY,CAAC1J,WAAW,CAAC;QACrD;QAGA,IAAI,CAAC2J,eAAe,CAAC3J,WAAW,CAAC;QAGjC,IAAI,IAAI,CAAC7B,MAAM,CAACuJ,oBAAoB,EAAE;UACpC,MAAM,IAAI,CAACkC,iBAAiB,CAAC5J,WAAW,CAAC;QAC3C;QAGA,IAAI,IAAI,CAAC7B,MAAM,CAAC+I,eAAe,IAAIlH,WAAW,CAACsB,QAAQ,IAAI,IAAI,CAACnD,MAAM,CAACsJ,kBAAkB,EAAE;UACzF,MAAM1I,wDAA2B,CAAC8K,WAAW,CAAC7J,WAAW,CAAC;QAC5D;QAGA,MAAMhB,8CAAsB,CAAC8K,WAAW,CAAC9J,WAAW,CAAC;MACvD,CAAC;MAAA,SA1Caa,YAAYA,CAAAkJ,GAAA;QAAA,OAAAX,aAAA,CAAA7J,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAZqB,YAAY;IAAA;EAAA;IAAApC,GAAA;IAAAC,KAAA,EA4C1B,SAAQ6K,aAAaA,CAACvJ,WAAwB,EAAQ;MACpD,IAAI,CAAC3B,OAAO,CAAC4J,WAAW,EAAE;MAG1B,IAAI,CAAC5J,OAAO,CAAC6J,YAAY,CAAClI,WAAW,CAACQ,IAAI,CAAC,GACzC,CAAC,IAAI,CAACnC,OAAO,CAAC6J,YAAY,CAAClI,WAAW,CAACQ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;MAGxD,IAAI,CAACnC,OAAO,CAAC8J,gBAAgB,CAACnI,WAAW,CAACsB,QAAQ,CAAC,GACjD,CAAC,IAAI,CAACjD,OAAO,CAAC8J,gBAAgB,CAACnI,WAAW,CAACsB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;MAGhE,IAAI,CAACjD,OAAO,CAAC+J,gBAAgB,CAACpI,WAAW,CAACM,QAAQ,CAAC,GACjD,CAAC,IAAI,CAACjC,OAAO,CAAC+J,gBAAgB,CAACpI,WAAW,CAACM,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;IAClE;EAAC;IAAA7B,GAAA;IAAAC,KAAA,EAED,SAAQ8K,QAAQA,CAACxJ,WAAwB,EAAQ;MAC/C,IAAMgK,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACjK,WAAW,CAACsB,QAAQ,CAAC;MACvD,IAAM4I,UAAU,GAAG,0BAA0BlK,WAAW,CAACQ,IAAI,KAAKR,WAAW,CAACO,OAAO,EAAE;MAEvFnB,OAAO,CAAC4K,QAAQ,CAAC,CAACE,UAAU,EAAE;QAC5BtJ,EAAE,EAAEZ,WAAW,CAACY,EAAE;QAClBJ,IAAI,EAAER,WAAW,CAACQ,IAAI;QACtBc,QAAQ,EAAEtB,WAAW,CAACsB,QAAQ;QAC9B5B,OAAO,EAAEM,WAAW,CAACN,OAAO;QAC5BsF,KAAK,EAAE,IAAI,CAAC7G,MAAM,CAAC6J,gBAAgB,GAAGhI,WAAW,CAACgF,KAAK,GAAGpF;MAC5D,CAAC,CAAC;IACJ;EAAC;IAAAnB,GAAA;IAAAC,KAAA,EAED,SAAQuL,WAAWA,CAAC3I,QAAuB,EAA4B;MACrE,QAAQA,QAAQ;QACd,KAAKC,oBAAa,CAACa,GAAG;UACpB,OAAO,KAAK;QACd,KAAKb,oBAAa,CAACC,MAAM;UACvB,OAAO,MAAM;QACf,KAAKD,oBAAa,CAACO,IAAI;QACvB,KAAKP,oBAAa,CAACqD,QAAQ;UACzB,OAAO,OAAO;QAChB;UACE,OAAO,MAAM;MACjB;IACF;EAAC;IAAAnG,GAAA;IAAAC,KAAA;MAAA,IAAAyL,gBAAA,OAAAvL,kBAAA,CAAAhB,OAAA,EAED,WAA8BoC,WAAwB,EAAiB;QAAA,IAAAoK,MAAA;QAErE,IAAMC,UAAU,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACxM,kBAAkB,CAACyM,MAAM,CAAC,CAAC,CAAC,CAC5DC,MAAM,CAAC,UAAAnH,QAAQ;UAAA,OAAIA,QAAQ,CAACmF,UAAU,CAACzI,WAAW,EAASA,WAAW,CAACN,OAAO,CAAC;QAAA,EAAC;QAAC,IAAAgL,KAAA,aAAAA,MAAA,EAEjD;YACjC,IAAI1K,WAAW,CAACmF,gBAAgB,IAAI7B,QAAQ,CAAC0F,WAAW;YAExD,IAAI;cACFhJ,WAAW,CAACmF,gBAAgB,EAAE;cAG9B,IAAMwF,KAAK,GAAGrH,QAAQ,CAAC4F,gBAAgB,GACnC5F,QAAQ,CAAC2F,UAAU,GAAIjJ,WAAW,CAACmF,gBAAgB,GACnD7B,QAAQ,CAAC2F,UAAU,IAAImB,MAAI,CAACjM,MAAM,CAACoJ,iBAAiB;cAGxD,MAAM,IAAIqD,OAAO,CAAC,UAAAC,OAAO;gBAAA,OAAIC,UAAU,CAACD,OAAO,EAAEF,KAAK,CAAC;cAAA,EAAC;cAGxD,IAAMvF,SAAS,SAAS9B,QAAQ,CAACoF,OAAO,CAAC1I,WAAW,EAASA,WAAW,CAACN,OAAO,CAAC;cAEjF,IAAI0F,SAAS,EAAE;gBACbpF,WAAW,CAACoF,SAAS,GAAG,IAAI;gBAC5BpF,WAAW,CAACkF,gBAAgB,GAAG5B,QAAQ,CAACiF,IAAI;gBAE5CnJ,OAAO,CAACC,GAAG,CAAC,qCAAqCiE,QAAQ,CAACiF,IAAI,EAAE,CAAC;gBAAC;cAEpE;YACF,CAAC,CAAC,OAAOwC,aAAa,EAAE;cACtB3L,OAAO,CAAC4L,IAAI,CAAC,+BAA+B1H,QAAQ,CAACiF,IAAI,EAAE,EAAEwC,aAAa,CAAC;YAC7E;UACF,CAAC;UAAAE,IAAA;QA3BD,KAAK,IAAM3H,QAAQ,IAAI+G,UAAU;UAAAY,IAAA,UAAAP,KAAA;UAAA,IAAAO,IAAA,QAC2B;UAAS,IAAAA,IAAA,QAqB/D;QAAM;MAMd,CAAC;MAAA,SAjCaxB,eAAeA,CAAAyB,GAAA;QAAA,OAAAf,gBAAA,CAAA5K,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAfiK,eAAe;IAAA;EAAA;IAAAhL,GAAA;IAAAC,KAAA,EAqC7B,SAAQiL,eAAeA,CAAC3J,WAAwB,EAAQ;MACtD,IAAI,CAAClC,cAAc,CAACqN,OAAO,CAAC,UAAAnI,QAAQ,EAAI;QACtC,IAAI;UACFA,QAAQ,CAAChD,WAAW,CAAC;QACvB,CAAC,CAAC,OAAOV,KAAK,EAAE;UACdF,OAAO,CAAC4L,IAAI,CAAC,wBAAwB,EAAE1L,KAAK,CAAC;QAC/C;MACF,CAAC,CAAC;IACJ;EAAC;IAAAb,GAAA;IAAAC,KAAA;MAAA,IAAA0M,kBAAA,OAAAxM,kBAAA,CAAAhB,OAAA,EAED,WAAgCoC,WAAwB,EAAiB;QACvE,IAAI;UACF,IAAMqL,MAAM,SAAS3H,qBAAY,CAAC4H,OAAO,CAAC,qBAAqB,CAAC;UAChE,IAAMC,MAAM,GAAGF,MAAM,GAAGG,IAAI,CAACC,KAAK,CAACJ,MAAM,CAAC,GAAG,EAAE;UAE/CE,MAAM,CAAC1I,IAAI,CAAC7C,WAAW,CAAC;UAGxB,IAAIuL,MAAM,CAAC5L,MAAM,GAAG,IAAI,CAACxB,MAAM,CAACwJ,gBAAgB,EAAE;YAChD4D,MAAM,CAACnI,MAAM,CAAC,CAAC,EAAEmI,MAAM,CAAC5L,MAAM,GAAG,IAAI,CAACxB,MAAM,CAACwJ,gBAAgB,CAAC;UAChE;UAEA,MAAMjE,qBAAY,CAACgI,OAAO,CAAC,qBAAqB,EAAEF,IAAI,CAACG,SAAS,CAACJ,MAAM,CAAC,CAAC;QAC3E,CAAC,CAAC,OAAOjM,KAAK,EAAE;UACdF,OAAO,CAAC4L,IAAI,CAAC,gCAAgC,EAAE1L,KAAK,CAAC;QACvD;MACF,CAAC;MAAA,SAhBasK,iBAAiBA,CAAAgC,GAAA;QAAA,OAAAR,kBAAA,CAAA7L,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjBoK,iBAAiB;IAAA;EAAA;IAAAnL,GAAA;IAAAC,KAAA;MAAA,IAAAmN,kBAAA,OAAAjN,kBAAA,CAAAhB,OAAA,EAkB/B,aAAiD;QAC/C,IAAI;UACF,IAAMyN,MAAM,SAAS3H,qBAAY,CAAC4H,OAAO,CAAC,qBAAqB,CAAC;UAChE,IAAID,MAAM,EAAE;YAAA,IAAAS,gBAAA;YACV,IAAMP,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACJ,MAAM,CAAC;YACjC,CAAAS,gBAAA,OAAI,CAACjO,UAAU,EAACgF,IAAI,CAAAtD,KAAA,CAAAuM,gBAAA,MAAA7G,mBAAA,CAAArH,OAAA,EAAI2N,MAAM,EAAC;YAG/B,MAAM7H,qBAAY,CAACC,UAAU,CAAC,qBAAqB,CAAC;UACtD;QACF,CAAC,CAAC,OAAOrE,KAAK,EAAE;UACdF,OAAO,CAAC4L,IAAI,CAAC,gCAAgC,EAAE1L,KAAK,CAAC;QACvD;MACF,CAAC;MAAA,SAbaL,iBAAiBA,CAAA;QAAA,OAAA4M,kBAAA,CAAAtM,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjBP,iBAAiB;IAAA;EAAA;IAAAR,GAAA;IAAAC,KAAA,EAiB/B,SAAQQ,sBAAsBA,CAAA,EAAS;MAAA,IAAA6M,MAAA;MACrClD,gBAAO,CAACmD,gBAAgB,CAAC,UAAAC,KAAK,EAAI;QAEhC,IAAIF,MAAI,CAAC5N,MAAM,CAAC8I,aAAa,EAAE;UAC7B7H,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE4M,KAAK,CAAClD,WAAW,GAAG,QAAQ,GAAG,SAAS,CAAC;QACrF;MACF,CAAC,CAAC;IACJ;EAAC;IAAAtK,GAAA;IAAAC,KAAA,EAED,SAAQS,oBAAoBA,CAAA,EAAS;MAAA,IAAA+M,MAAA;MACnCC,WAAW,CAAC,YAAM;QAChB,IAAID,MAAI,CAACrO,UAAU,CAAC8B,MAAM,GAAG,CAAC,IAAIuM,MAAI,CAAC/N,MAAM,CAAC+I,eAAe,EAAE;UAE7D,IAAMkF,UAAU,GAAGF,MAAI,CAACrO,UAAU,CAAC4M,MAAM,CAAC,UAAAnL,KAAK;YAAA,OAAI,CAACA,KAAK,CAAC+F,QAAQ;UAAA,EAAC;UACnE+G,UAAU,CAACjB,OAAO,CAAC,UAAA7L,KAAK;YAAA,OAAIP,wDAA2B,CAAC8K,WAAW,CAACvK,KAAK,CAAC;UAAA,EAAC;QAC7E;MACF,CAAC,EAAE,IAAI,CAACnB,MAAM,CAAC0J,uBAAuB,CAAC;IACzC;EAAC;IAAApJ,GAAA;IAAAC,KAAA,EAKD,SAAA2N,oBAAoBA,CAAA,EAAiB;MACnC,OAAOrN,8CAAsB,CAACsN,eAAe,CAAC,CAAC;IACjD;EAAC;IAAA7N,GAAA;IAAAC,KAAA,EAKD,SAAA6N,eAAeA,CAAA,EAAG;MAChB,OAAOvN,8CAAsB,CAACuN,eAAe,CAAC,CAAC;IACjD;EAAC;IAAA9N,GAAA;IAAAC,KAAA,EAKD,SAAA8N,cAAcA,CAACC,IAAa,EAAE;MAC5B,OAAOzN,8CAAsB,CAACwN,cAAc,CAACC,IAAI,CAAC;IACpD;EAAC;IAAAhO,GAAA;IAAAC,KAAA;MAAA,IAAAgO,gBAAA,OAAA9N,kBAAA,CAAAhB,OAAA,EAKD,WAAsB+O,MAAuB,EAAE;QAC7C,OAAO3N,8CAAsB,CAAC4N,eAAe,CAACD,MAAM,CAAC;MACvD,CAAC;MAAA,SAFKC,eAAeA,CAAAC,GAAA;QAAA,OAAAH,gBAAA,CAAAnN,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAfoN,eAAe;IAAA;EAAA;IAAAnO,GAAA;IAAAC,KAAA;MAAA,IAAAoO,uBAAA,OAAAlO,kBAAA,CAAAhB,OAAA,EAOrB,aAA+B;QAC7B,OAAOoB,8CAAsB,CAAC+N,iBAAiB,CAAC,CAAC;MACnD,CAAC;MAAA,SAFKC,sBAAsBA,CAAA;QAAA,OAAAF,uBAAA,CAAAvN,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAtBwN,sBAAsB;IAAA;EAAA;AAAA;AAM9B,IAAIC,gBAAoD,GAAG,IAAI;AAExD,IAAMC,2BAA2B,GAAAC,OAAA,CAAAD,2BAAA,GAAI,YAAM;EAChD,IAAI,CAACD,gBAAgB,EAAE;IACrBA,gBAAgB,GAAG,IAAIvP,2BAA2B,CAAC,CAAC;EACtD;EACA,OAAOuP,gBAAgB;AACzB,CAAC,CAAE,CAAC;AAAC,IAAAG,QAAA,GAAAD,OAAA,CAAAvP,OAAA,GAEUsP,2BAA2B", "ignoreList": []}