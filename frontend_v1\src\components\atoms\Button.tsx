/**
 * Button Component - Enhanced Foundational UI Atom
 *
 * Component Contract:
 * - Sources all styles from unified design tokens
 * - Supports multiple variants (primary, secondary, outline, minimal)
 * - <PERSON><PERSON> disabled and loading states with proper accessibility
 * - Meets minimum touch target requirements (44x44)
 * - Provides comprehensive accessibility support
 * - Follows React Native best practices for performance
 * - Supports theme switching (light/dark mode)
 *
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

import React, { useState } from 'react';
import {
  TouchableOpacity,
  Text,
  ActivityIndicator,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TouchableOpacityProps,
} from 'react-native';

import { useTheme } from '../../contexts/ThemeContext';

export interface ButtonProps extends Omit<TouchableOpacityProps, 'style'> {
  /** Button content */
  children?: React.ReactNode;
  /** Button title (alternative to children) */
  title?: string;
  /** Button variant */
  variant?: 'primary' | 'secondary' | 'outline' | 'minimal';
  /** Button size */
  size?: 'sm' | 'md' | 'lg';
  /** Custom style override */
  style?: ViewStyle;
  /** Press handler */
  onPress: () => void;
  /** Disabled state */
  disabled?: boolean;
  /** Loading state */
  loading?: boolean;
  /** Full width button */
  fullWidth?: boolean;
  /** Icon to display before text */
  leftIcon?: React.ReactNode;
  /** Icon to display after text */
  rightIcon?: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  children,
  title,
  variant = 'primary',
  size = 'md',
  style,
  onPress,
  disabled = false,
  loading = false,
  fullWidth = false,
  leftIcon,
  rightIcon,
  accessibilityLabel,
  accessibilityHint,
  ...props
}) => {
  const { colors } = useTheme();
  const [isFocused, setIsFocused] = useState(false);

  const styles = createStyles(colors);

  // Determine button content - children takes priority over title
  const buttonContent = children || title;

  // Create style arrays based on props
  const buttonStyleArray = [
    styles.base,
    styles[variant],
    styles[size],
    fullWidth && styles.fullWidth,
    disabled && styles.disabled,
    loading && styles.loading,
    isFocused && styles.focused,
    style,
  ].filter(Boolean);

  const textStyleArray = [
    styles.text,
    styles[`${variant}Text`],
    styles[`${size}Text`],
    disabled && styles.disabledText,
  ].filter(Boolean);

  const isInteractionDisabled = disabled || loading;

  return (
    <TouchableOpacity
      style={buttonStyleArray}
      onPress={isInteractionDisabled ? undefined : onPress}
      disabled={isInteractionDisabled}
      onFocus={() => setIsFocused(true)}
      onBlur={() => setIsFocused(false)}
      accessibilityRole="button"
      accessibilityState={{
        disabled: isInteractionDisabled,
        busy: loading,
      }}
      accessibilityLabel={
        accessibilityLabel ||
        (typeof buttonContent === 'string' ? buttonContent : 'Button')
      }
      accessibilityHint={accessibilityHint}
      {...props}>
      {loading && (
        <ActivityIndicator
          size="small"
          color={
            variant === 'primary'
              ? colors.primary?.contrast || '#FFFFFF'
              : colors.primary?.default || '#4A6B52'
          }
          style={styles.loadingIndicator}
        />
      )}

      {!loading && leftIcon && <>{leftIcon}</>}

      {buttonContent && (
        <Text style={textStyleArray} numberOfLines={1}>
          {buttonContent}
        </Text>
      )}

      {!loading && rightIcon && <>{rightIcon}</>}
    </TouchableOpacity>
  );
};

// Theme-based styles factory
const createStyles = (colors: any) =>
  StyleSheet.create({
    base: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 8,
      minHeight: 44,
      minWidth: 44,
      gap: 8,
    },

    // Variants
    primary: {
      backgroundColor: colors.primary?.default || '#4A6B52',
      paddingHorizontal: 16,
      paddingVertical: 12,
    },
    secondary: {
      backgroundColor: colors.surface?.secondary || '#F9FAFB',
      borderWidth: 1,
      borderColor: colors.primary?.default || '#4A6B52',
      paddingHorizontal: 16,
      paddingVertical: 12,
    },
    outline: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: colors.primary?.default || '#4A6B52',
      paddingHorizontal: 16,
      paddingVertical: 12,
    },
    minimal: {
      backgroundColor: 'transparent',
      paddingHorizontal: 8,
      paddingVertical: 8,
    },

    // Sizes
    sm: {
      height: 36,
      paddingHorizontal: 12,
      paddingVertical: 8,
    },
    md: {
      height: 44,
      paddingHorizontal: 16,
      paddingVertical: 12,
    },
    lg: {
      height: 52,
      paddingHorizontal: 20,
      paddingVertical: 16,
    },

    // States
    disabled: {
      opacity: 0.5,
    },
    loading: {
      opacity: 0.8,
    },
    focused: {
      borderWidth: 2,
      borderColor: colors.primary?.light || '#6B8A74',
    },
    fullWidth: {
      width: '100%',
    },

    // Text styles
    text: {
      fontFamily: 'System',
      fontWeight: '500',
      textAlign: 'center',
      fontSize: 16,
      lineHeight: 24,
    },
    primaryText: {
      color: colors.primary?.contrast || '#FFFFFF',
    },
    secondaryText: {
      color: colors.text?.primary || '#1A1A1A',
    },
    outlineText: {
      color: colors.primary?.default || '#4A6B52',
    },
    minimalText: {
      color: colors.primary?.default || '#4A6B52',
    },
    disabledText: {
      color: colors.text?.tertiary || '#9CA3AF',
    },

    // Size-specific text
    smText: {
      fontSize: 14,
    },
    mdText: {
      fontSize: 16,
    },
    lgText: {
      fontSize: 18,
    },

    // Loading indicator
    loadingIndicator: {
      marginRight: 8,
    },
  });
