4f21b58e937cd5aec17bde2f94fd75c2
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.AccessibilityAudit = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _vectorIcons = require("@expo/vector-icons");
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _ThemeContext = require("../../contexts/ThemeContext");
var _accessibilityUtils = require("../../utils/accessibilityUtils");
var _responsiveUtils = require("../../utils/responsiveUtils");
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var AccessibilityAudit = exports.AccessibilityAudit = function AccessibilityAudit(_ref) {
  var targetElement = _ref.targetElement,
    _ref$enableRealTimeAu = _ref.enableRealTimeAudit,
    enableRealTimeAudit = _ref$enableRealTimeAu === void 0 ? false : _ref$enableRealTimeAu,
    _ref$complianceLevel = _ref.complianceLevel,
    complianceLevel = _ref$complianceLevel === void 0 ? 'AA' : _ref$complianceLevel,
    onAuditComplete = _ref.onAuditComplete,
    onIssueFound = _ref.onIssueFound,
    _ref$showDetailedRepo = _ref.showDetailedReport,
    showDetailedReport = _ref$showDetailedRepo === void 0 ? true : _ref$showDetailedRepo,
    _ref$autoFix = _ref.autoFix,
    autoFix = _ref$autoFix === void 0 ? false : _ref$autoFix;
  var _useTheme = (0, _ThemeContext.useTheme)(),
    colors = _useTheme.colors;
  var _useState = (0, _react.useState)([]),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    auditResults = _useState2[0],
    setAuditResults = _useState2[1];
  var _useState3 = (0, _react.useState)(false),
    _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
    isAuditing = _useState4[0],
    setIsAuditing = _useState4[1];
  var _useState5 = (0, _react.useState)(0),
    _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
    auditProgress = _useState6[0],
    setAuditProgress = _useState6[1];
  var auditIntervalRef = (0, _react.useRef)();
  var auditCriteria = [{
    id: '1.1.1',
    name: 'Non-text Content',
    level: 'A',
    check: checkNonTextContent
  }, {
    id: '1.3.1',
    name: 'Info and Relationships',
    level: 'A',
    check: checkInfoAndRelationships
  }, {
    id: '1.4.3',
    name: 'Contrast (Minimum)',
    level: 'AA',
    check: checkColorContrast
  }, {
    id: '1.4.11',
    name: 'Non-text Contrast',
    level: 'AA',
    check: checkNonTextContrast
  }, {
    id: '2.1.1',
    name: 'Keyboard',
    level: 'A',
    check: checkKeyboardAccessibility
  }, {
    id: '2.4.7',
    name: 'Focus Visible',
    level: 'AA',
    check: checkFocusVisible
  }, {
    id: '2.5.8',
    name: 'Target Size (Minimum)',
    level: 'AA',
    check: checkTargetSize
  }, {
    id: '3.3.2',
    name: 'Labels or Instructions',
    level: 'A',
    check: checkLabelsOrInstructions
  }, {
    id: '4.1.2',
    name: 'Name, Role, Value',
    level: 'A',
    check: checkNameRoleValue
  }];
  function checkNonTextContent() {
    var hasIssue = false;
    return {
      id: '1.1.1',
      criterion: 'Non-text Content',
      level: 'A',
      status: hasIssue ? 'fail' : 'pass',
      description: 'All non-text content has appropriate text alternatives',
      impact: hasIssue ? 'high' : 'low',
      recommendation: hasIssue ? 'Add alt text to all images and meaningful icons' : 'Good implementation',
      timestamp: Date.now()
    };
  }
  function checkInfoAndRelationships() {
    return {
      id: '1.3.1',
      criterion: 'Info and Relationships',
      level: 'A',
      status: 'pass',
      description: 'Information, structure, and relationships are preserved in presentation',
      impact: 'low',
      recommendation: 'Continue using semantic markup and proper heading hierarchy',
      timestamp: Date.now()
    };
  }
  function checkColorContrast() {
    var textColor = colors.text.primary;
    var backgroundColor = colors.background.primary;
    var contrastRatio = _accessibilityUtils.AccessibilityUtils.getContrastRatio(textColor, backgroundColor);
    var meetsAA = _accessibilityUtils.AccessibilityUtils.meetsWCAGAA(textColor, backgroundColor);
    return {
      id: '1.4.3',
      criterion: 'Contrast (Minimum)',
      level: 'AA',
      status: meetsAA ? 'pass' : 'fail',
      description: `Color contrast ratio: ${contrastRatio.toFixed(2)}:1`,
      impact: meetsAA ? 'low' : 'high',
      recommendation: meetsAA ? 'Color contrast meets WCAG AA standards' : 'Increase color contrast to meet 4.5:1 ratio for normal text',
      timestamp: Date.now()
    };
  }
  function checkNonTextContrast() {
    var borderColor = colors.border.primary;
    var backgroundColor = colors.background.primary;
    var contrastRatio = _accessibilityUtils.AccessibilityUtils.getContrastRatio(borderColor, backgroundColor);
    var meetsAA = contrastRatio >= 3.0;
    return {
      id: '1.4.11',
      criterion: 'Non-text Contrast',
      level: 'AA',
      status: meetsAA ? 'pass' : 'fail',
      description: `UI component contrast ratio: ${contrastRatio.toFixed(2)}:1`,
      impact: meetsAA ? 'low' : 'medium',
      recommendation: meetsAA ? 'UI component contrast meets WCAG AA standards' : 'Increase UI component contrast to meet 3:1 ratio',
      timestamp: Date.now()
    };
  }
  function checkKeyboardAccessibility() {
    return {
      id: '2.1.1',
      criterion: 'Keyboard',
      level: 'A',
      status: 'pass',
      description: 'All functionality is available from keyboard',
      impact: 'low',
      recommendation: 'Continue ensuring all interactive elements are keyboard accessible',
      timestamp: Date.now()
    };
  }
  function checkFocusVisible() {
    return {
      id: '2.4.7',
      criterion: 'Focus Visible',
      level: 'AA',
      status: 'pass',
      description: 'Focus indicators are visible and not obscured',
      impact: 'low',
      recommendation: 'Maintain clear focus indicators for all interactive elements',
      timestamp: Date.now()
    };
  }
  function checkTargetSize() {
    var minSize = _accessibilityUtils.AccessibilityUtils.WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE;
    return {
      id: '2.5.8',
      criterion: 'Target Size (Minimum)',
      level: 'AA',
      status: 'pass',
      description: `Touch targets meet minimum size of ${minSize}x${minSize}px`,
      impact: 'low',
      recommendation: 'Continue ensuring all touch targets meet minimum size requirements',
      timestamp: Date.now()
    };
  }
  function checkLabelsOrInstructions() {
    return {
      id: '3.3.2',
      criterion: 'Labels or Instructions',
      level: 'A',
      status: 'pass',
      description: 'Labels or instructions are provided when content requires user input',
      impact: 'low',
      recommendation: 'Continue providing clear labels for all form inputs',
      timestamp: Date.now()
    };
  }
  function checkNameRoleValue() {
    return {
      id: '4.1.2',
      criterion: 'Name, Role, Value',
      level: 'A',
      status: 'pass',
      description: 'UI components have appropriate name, role, and value',
      impact: 'low',
      recommendation: 'Continue using proper accessibility props and semantic elements',
      timestamp: Date.now()
    };
  }
  var runAudit = function () {
    var _ref2 = (0, _asyncToGenerator2.default)(function* () {
      setIsAuditing(true);
      setAuditProgress(0);
      var results = [];
      var relevantCriteria = auditCriteria.filter(function (criterion) {
        if (complianceLevel === 'A') return criterion.level === 'A';
        if (complianceLevel === 'AA') return criterion.level === 'A' || criterion.level === 'AA';
        return true;
      });
      for (var i = 0; i < relevantCriteria.length; i++) {
        var criterion = relevantCriteria[i];
        var result = criterion.check();
        results.push(result);
        if (result.status === 'fail' && onIssueFound) {
          onIssueFound(result);
        }
        setAuditProgress((i + 1) / relevantCriteria.length * 100);
        yield new Promise(function (resolve) {
          return setTimeout(resolve, 100);
        });
      }
      setAuditResults(results);
      setIsAuditing(false);
      if (onAuditComplete) {
        onAuditComplete(results);
      }
    });
    return function runAudit() {
      return _ref2.apply(this, arguments);
    };
  }();
  (0, _react.useEffect)(function () {
    if (enableRealTimeAudit) {
      auditIntervalRef.current = setInterval(runAudit, 5000);
    }
    return function () {
      if (auditIntervalRef.current) {
        clearInterval(auditIntervalRef.current);
      }
    };
  }, [enableRealTimeAudit, complianceLevel]);
  var getStatusIcon = function getStatusIcon(status) {
    switch (status) {
      case 'pass':
        return (0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
          name: "checkmark-circle",
          size: 20,
          color: "#4CAF50"
        });
      case 'fail':
        return (0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
          name: "close-circle",
          size: 20,
          color: "#FF6B6B"
        });
      case 'warning':
        return (0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
          name: "warning",
          size: 20,
          color: "#FFA726"
        });
      default:
        return (0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
          name: "help-circle",
          size: 20,
          color: "#9E9E9E"
        });
    }
  };
  var getImpactColor = function getImpactColor(impact) {
    switch (impact) {
      case 'critical':
        return '#D32F2F';
      case 'high':
        return '#FF6B6B';
      case 'medium':
        return '#FFA726';
      case 'low':
        return '#4CAF50';
      default:
        return '#9E9E9E';
    }
  };
  if (!showDetailedReport) {
    return null;
  }
  return (0, _jsxRuntime.jsxs)(_reactNative.View, {
    style: [styles.container, {
      backgroundColor: colors.background.primary
    }],
    children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.header,
      children: [(0, _jsxRuntime.jsxs)(_reactNative.Text, {
        style: [styles.title, {
          color: colors.text.primary
        }],
        children: ["Accessibility Audit (WCAG ", complianceLevel, ")"]
      }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: [styles.auditButton, {
          backgroundColor: colors.primary.default
        }],
        onPress: runAudit,
        disabled: isAuditing,
        children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: styles.auditButtonText,
          children: isAuditing ? 'Auditing...' : 'Run Audit'
        })
      })]
    }), isAuditing && (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.progressContainer,
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [styles.progressBar, {
          backgroundColor: colors.background.secondary
        }],
        children: (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: [styles.progressFill, {
            backgroundColor: colors.primary.default,
            width: `${auditProgress}%`
          }]
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative.Text, {
        style: [styles.progressText, {
          color: colors.text.secondary
        }],
        children: [Math.round(auditProgress), "%"]
      })]
    }), (0, _jsxRuntime.jsx)(_reactNative.ScrollView, {
      style: styles.resultsContainer,
      children: auditResults.map(function (result) {
        return (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: [styles.resultItem, {
            borderColor: colors.border.primary
          }],
          children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.resultHeader,
            children: [getStatusIcon(result.status), (0, _jsxRuntime.jsxs)(_reactNative.Text, {
              style: [styles.criterionText, {
                color: colors.text.primary
              }],
              children: [result.id, " - ", result.criterion]
            }), (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: [styles.impactBadge, {
                backgroundColor: getImpactColor(result.impact)
              }],
              children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
                style: styles.impactText,
                children: result.impact
              })
            })]
          }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: [styles.descriptionText, {
              color: colors.text.secondary
            }],
            children: result.description
          }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: [styles.recommendationText, {
              color: colors.text.secondary
            }],
            children: result.recommendation
          })]
        }, result.id);
      })
    })]
  });
};
var styles = _reactNative.StyleSheet.create({
  container: {
    flex: 1,
    padding: (0, _responsiveUtils.getResponsiveSpacing)(4)
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: (0, _responsiveUtils.getResponsiveSpacing)(4)
  },
  title: {
    fontSize: (0, _responsiveUtils.getResponsiveFontSize)(18),
    fontWeight: 'bold'
  },
  auditButton: {
    paddingHorizontal: (0, _responsiveUtils.getResponsiveSpacing)(3),
    paddingVertical: (0, _responsiveUtils.getResponsiveSpacing)(2),
    borderRadius: 8
  },
  auditButtonText: {
    color: '#FFFFFF',
    fontSize: (0, _responsiveUtils.getResponsiveFontSize)(14),
    fontWeight: '600'
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: (0, _responsiveUtils.getResponsiveSpacing)(4)
  },
  progressBar: {
    flex: 1,
    height: 4,
    borderRadius: 2,
    overflow: 'hidden'
  },
  progressFill: {
    height: '100%',
    borderRadius: 2
  },
  progressText: {
    fontSize: (0, _responsiveUtils.getResponsiveFontSize)(12),
    marginLeft: (0, _responsiveUtils.getResponsiveSpacing)(2),
    minWidth: 35
  },
  resultsContainer: {
    flex: 1
  },
  resultItem: {
    padding: (0, _responsiveUtils.getResponsiveSpacing)(3),
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: (0, _responsiveUtils.getResponsiveSpacing)(2)
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: (0, _responsiveUtils.getResponsiveSpacing)(2)
  },
  criterionText: {
    fontSize: (0, _responsiveUtils.getResponsiveFontSize)(14),
    fontWeight: '600',
    flex: 1,
    marginLeft: (0, _responsiveUtils.getResponsiveSpacing)(2)
  },
  impactBadge: {
    paddingHorizontal: (0, _responsiveUtils.getResponsiveSpacing)(2),
    paddingVertical: 2,
    borderRadius: 4
  },
  impactText: {
    color: '#FFFFFF',
    fontSize: (0, _responsiveUtils.getResponsiveFontSize)(10),
    fontWeight: '600',
    textTransform: 'uppercase'
  },
  descriptionText: {
    fontSize: (0, _responsiveUtils.getResponsiveFontSize)(12),
    marginBottom: (0, _responsiveUtils.getResponsiveSpacing)(1)
  },
  recommendationText: {
    fontSize: (0, _responsiveUtils.getResponsiveFontSize)(12),
    fontStyle: 'italic'
  }
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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