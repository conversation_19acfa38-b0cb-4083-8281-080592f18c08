{"version": 3, "names": ["_apiClient", "require", "_unifiedErrorHandling", "MessagingService", "_classCallCheck2", "default", "baseUrl", "_createClass2", "key", "value", "_getConversations", "_asyncToGenerator2", "page", "arguments", "length", "undefined", "limit", "archived", "response", "apiClient", "get", "params", "conversations", "Array", "isArray", "data", "totalCount", "error", "console", "Error", "getConversations", "apply", "_getConversation", "conversationId", "getConversation", "_x", "_createConversation", "participantIds", "type", "title", "post", "participant_ids", "createConversation", "_x2", "_getMessages", "filters", "Object", "assign", "getMessages", "_x3", "_sendMessage", "content", "attachments", "replyTo", "formData", "FormData", "append", "for<PERSON>ach", "file", "index", "headers", "unifiedErrorHandlingService", "handleNetworkError", "action", "additionalData", "substring", "sendMessage", "_x4", "_x5", "_editMessage", "messageId", "patch", "editMessage", "_x6", "_x7", "_deleteMessage", "delete", "deleteMessage", "_x8", "_markMessagesAsRead", "messageIds", "message_ids", "markMessagesAsRead", "_x9", "_x0", "_addReaction", "emoji", "addReaction", "_x1", "_x10", "_removeReaction", "reactionId", "removeReaction", "_x11", "_x12", "_searchMessages", "query", "q", "conversation_id", "messages", "searchMessages", "_x13", "_x14", "_uploadAttachment", "uploadAttachment", "_x15", "_getConversationAnalytics", "getConversationAnalytics", "_x16", "formatMessageTime", "timestamp", "messageDate", "Date", "now", "diffInMinutes", "Math", "floor", "getTime", "toLocaleDateString", "formatFileSize", "bytes", "k", "sizes", "i", "log", "parseFloat", "pow", "toFixed", "validateMessage", "trim", "<PERSON><PERSON><PERSON><PERSON>", "includes", "messagingService", "exports"], "sources": ["messagingService.ts"], "sourcesContent": ["/**\n * Messaging Service - Advanced Real-time Communication\n *\n * Service Contract:\n * - Provides comprehensive messaging functionality\n * - Handles real-time WebSocket communication\n * - Manages message delivery and read receipts\n * - Supports file attachments and media sharing\n * - Implements message encryption and security\n * - Handles offline message queuing\n * - Integrates with unified error handling system\n *\n * @version 2.0.0\n * <AUTHOR> Development Team\n */\n\nimport { apiClient } from './apiClient';\nimport { unifiedErrorHandlingService } from './unifiedErrorHandling';\n\nexport interface Message {\n  id: string;\n  conversationId: string;\n  senderId: string;\n  senderName: string;\n  senderAvatar?: string;\n  content: string;\n  type: 'text' | 'image' | 'file' | 'location' | 'booking_update' | 'system';\n  attachments?: MessageAttachment[];\n  replyTo?: string;\n  isEdited: boolean;\n  editedAt?: string;\n  deliveryStatus: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';\n  readBy: MessageReadReceipt[];\n  reactions: MessageReaction[];\n  metadata?: Record<string, any>;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface MessageAttachment {\n  id: string;\n  type: 'image' | 'file' | 'audio' | 'video';\n  url: string;\n  thumbnailUrl?: string;\n  fileName: string;\n  fileSize: number;\n  mimeType: string;\n  duration?: number; // for audio/video\n  dimensions?: { width: number; height: number }; // for images/videos\n}\n\nexport interface MessageReadReceipt {\n  userId: string;\n  userName: string;\n  readAt: string;\n}\n\nexport interface MessageReaction {\n  userId: string;\n  userName: string;\n  emoji: string;\n  createdAt: string;\n}\n\nexport interface Conversation {\n  id: string;\n  type: 'direct' | 'group' | 'support';\n  participants: ConversationParticipant[];\n  title?: string;\n  description?: string;\n  avatar?: string;\n  lastMessage?: Message;\n  unreadCount: number;\n  isArchived: boolean;\n  isMuted: boolean;\n  isPinned: boolean;\n  settings: ConversationSettings;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface ConversationParticipant {\n  userId: string;\n  userName: string;\n  userAvatar?: string;\n  role: 'admin' | 'member' | 'guest';\n  isOnline: boolean;\n  lastSeen?: string;\n  joinedAt: string;\n}\n\nexport interface ConversationSettings {\n  allowFileSharing: boolean;\n  allowLocationSharing: boolean;\n  messageRetention: number; // days\n  encryptionEnabled: boolean;\n  notificationsEnabled: boolean;\n}\n\nexport interface TypingIndicator {\n  conversationId: string;\n  userId: string;\n  userName: string;\n  isTyping: boolean;\n  timestamp: string;\n}\n\nexport interface MessageFilters {\n  conversationId?: string;\n  senderId?: string;\n  type?: Message['type'];\n  hasAttachments?: boolean;\n  dateFrom?: string;\n  dateTo?: string;\n  searchQuery?: string;\n}\n\nclass MessagingService {\n  private readonly baseUrl = '/api/messaging';\n\n  /**\n   * Get conversations for current user\n   */\n  async getConversations(\n    page: number = 1,\n    limit: number = 20,\n    archived: boolean = false,\n  ): Promise<{ conversations: Conversation[]; totalCount: number }> {\n    try {\n      const response = await apiClient.get(`${this.baseUrl}/conversations/`, {\n        params: { page, limit, archived },\n      });\n\n      // The backend returns an array directly, not an object with conversations property\n      const conversations = Array.isArray(response.data) ? response.data : [];\n\n      return {\n        conversations,\n        totalCount: conversations.length,\n      };\n    } catch (error) {\n      console.error('Failed to get conversations:', error);\n      throw new Error('Failed to get conversations');\n    }\n  }\n\n  /**\n   * Get conversation by ID\n   */\n  async getConversation(conversationId: string): Promise<Conversation> {\n    try {\n      const response = await apiClient.get(\n        `${this.baseUrl}/conversations/${conversationId}/`,\n      );\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get conversation:', error);\n      throw new Error('Failed to get conversation');\n    }\n  }\n\n  /**\n   * Create new conversation\n   */\n  async createConversation(\n    participantIds: string[],\n    type: Conversation['type'] = 'direct',\n    title?: string,\n  ): Promise<Conversation> {\n    try {\n      const response = await apiClient.post(`${this.baseUrl}/conversations/`, {\n        participant_ids: participantIds,\n        type,\n        title,\n      });\n\n      return response.data;\n    } catch (error) {\n      console.error('Failed to create conversation:', error);\n      throw new Error('Failed to create conversation');\n    }\n  }\n\n  /**\n   * Get messages for conversation\n   */\n  async getMessages(\n    conversationId: string,\n    page: number = 1,\n    limit: number = 50,\n    filters?: MessageFilters,\n  ): Promise<{ messages: Message[]; totalCount: number }> {\n    try {\n      const params = {\n        page,\n        limit,\n        ...filters,\n      };\n\n      const response = await apiClient.get(\n        `${this.baseUrl}/conversations/${conversationId}/messages/`,\n        { params },\n      );\n\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get messages:', error);\n      throw new Error('Failed to get messages');\n    }\n  }\n\n  /**\n   * Send message\n   */\n  async sendMessage(\n    conversationId: string,\n    content: string,\n    type: Message['type'] = 'text',\n    attachments?: File[],\n    replyTo?: string,\n  ): Promise<Message> {\n    try {\n      const formData = new FormData();\n      formData.append('content', content);\n      formData.append('type', type);\n\n      if (replyTo) {\n        formData.append('reply_to', replyTo);\n      }\n\n      if (attachments && attachments.length > 0) {\n        attachments.forEach((file, index) => {\n          formData.append(`attachments[${index}]`, file);\n        });\n      }\n\n      const response = await apiClient.post(\n        `${this.baseUrl}/conversations/${conversationId}/send_message/`,\n        formData,\n        {\n          headers: {\n            'Content-Type': 'multipart/form-data',\n          },\n        },\n      );\n\n      return response.data;\n    } catch (error) {\n      console.error('Failed to send message:', error);\n      await unifiedErrorHandlingService.handleNetworkError(error as Error, {\n        action: 'send_message',\n        additionalData: { conversationId, content: content.substring(0, 50) + '...' }\n      });\n      throw error;\n    }\n  }\n\n  /**\n   * Edit message\n   */\n  async editMessage(messageId: string, content: string): Promise<Message> {\n    try {\n      const response = await apiClient.patch(\n        `${this.baseUrl}/messages/${messageId}/`,\n        {\n          content,\n        },\n      );\n\n      return response.data;\n    } catch (error) {\n      console.error('Failed to edit message:', error);\n      await unifiedErrorHandlingService.handleNetworkError(error as Error, {\n        action: 'edit_message',\n        additionalData: { messageId, content: content.substring(0, 50) + '...' }\n      });\n      throw error;\n    }\n  }\n\n  /**\n   * Delete message\n   */\n  async deleteMessage(messageId: string): Promise<void> {\n    try {\n      await apiClient.delete(`${this.baseUrl}/messages/${messageId}/`);\n    } catch (error) {\n      console.error('Failed to delete message:', error);\n      throw new Error('Failed to delete message');\n    }\n  }\n\n  /**\n   * Mark messages as read\n   */\n  async markMessagesAsRead(\n    conversationId: string,\n    messageIds: string[],\n  ): Promise<void> {\n    try {\n      await apiClient.post(\n        `${this.baseUrl}/conversations/${conversationId}/mark-read/`,\n        {\n          message_ids: messageIds,\n        },\n      );\n    } catch (error) {\n      console.error('Failed to mark messages as read:', error);\n      throw new Error('Failed to mark messages as read');\n    }\n  }\n\n  /**\n   * Add reaction to message\n   */\n  async addReaction(\n    messageId: string,\n    emoji: string,\n  ): Promise<MessageReaction> {\n    try {\n      const response = await apiClient.post(\n        `${this.baseUrl}/messages/${messageId}/reactions/`,\n        {\n          emoji,\n        },\n      );\n\n      return response.data;\n    } catch (error) {\n      console.error('Failed to add reaction:', error);\n      throw new Error('Failed to add reaction');\n    }\n  }\n\n  /**\n   * Remove reaction from message\n   */\n  async removeReaction(messageId: string, reactionId: string): Promise<void> {\n    try {\n      await apiClient.delete(\n        `${this.baseUrl}/messages/${messageId}/reactions/${reactionId}/`,\n      );\n    } catch (error) {\n      console.error('Failed to remove reaction:', error);\n      throw new Error('Failed to remove reaction');\n    }\n  }\n\n  /**\n   * Search messages\n   */\n  async searchMessages(\n    query: string,\n    conversationId?: string,\n    limit: number = 20,\n  ): Promise<Message[]> {\n    try {\n      const params = {\n        q: query,\n        conversation_id: conversationId,\n        limit,\n      };\n\n      const response = await apiClient.get(`${this.baseUrl}/search/`, {\n        params,\n      });\n      return response.data.messages;\n    } catch (error) {\n      console.error('Failed to search messages:', error);\n      throw new Error('Failed to search messages');\n    }\n  }\n\n  /**\n   * Upload file attachment\n   */\n  async uploadAttachment(file: File): Promise<MessageAttachment> {\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n\n      const response = await apiClient.post(\n        `${this.baseUrl}/attachments/`,\n        formData,\n        {\n          headers: {\n            'Content-Type': 'multipart/form-data',\n          },\n        },\n      );\n\n      return response.data;\n    } catch (error) {\n      console.error('Failed to upload attachment:', error);\n      throw new Error('Failed to upload attachment');\n    }\n  }\n\n  /**\n   * Get conversation analytics\n   */\n  async getConversationAnalytics(conversationId: string): Promise<{\n    messageCount: number;\n    participantActivity: {\n      userId: string;\n      messageCount: number;\n      lastActive: string;\n    }[];\n    peakHours: { hour: number; messageCount: number }[];\n    responseTime: number;\n  }> {\n    try {\n      const response = await apiClient.get(\n        `${this.baseUrl}/conversations/${conversationId}/analytics/`,\n      );\n\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get conversation analytics:', error);\n      throw new Error('Failed to get conversation analytics');\n    }\n  }\n\n  /**\n   * Format message timestamp\n   */\n  formatMessageTime(timestamp: string): string {\n    const messageDate = new Date(timestamp);\n    const now = new Date();\n    const diffInMinutes = Math.floor(\n      (now.getTime() - messageDate.getTime()) / (1000 * 60),\n    );\n\n    if (diffInMinutes < 1) {\n      return 'Just now';\n    } else if (diffInMinutes < 60) {\n      return `${diffInMinutes}m ago`;\n    } else if (diffInMinutes < 1440) {\n      return `${Math.floor(diffInMinutes / 60)}h ago`;\n    } else {\n      return messageDate.toLocaleDateString();\n    }\n  }\n\n  /**\n   * Get file size display\n   */\n  formatFileSize(bytes: number): string {\n    if (bytes === 0) return '0 Bytes';\n\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  /**\n   * Validate message content\n   */\n  validateMessage(\n    content: string,\n    type: Message['type'],\n  ): { isValid: boolean; error?: string } {\n    if (!content.trim()) {\n      return { isValid: false, error: 'Message cannot be empty' };\n    }\n\n    if (content.length > 5000) {\n      return {\n        isValid: false,\n        error: 'Message is too long (max 5000 characters)',\n      };\n    }\n\n    if (type === 'text' && content.includes('<script>')) {\n      return { isValid: false, error: 'Invalid content detected' };\n    }\n\n    return { isValid: true };\n  }\n}\n\nexport const messagingService = new MessagingService();\n"], "mappings": ";;;;;;;;AAgBA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,qBAAA,GAAAD,OAAA;AAAqE,IAoG/DE,gBAAgB;EAAA,SAAAA,iBAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAF,gBAAA;IAAA,KACHG,OAAO,GAAG,gBAAgB;EAAA;EAAA,WAAAC,aAAA,CAAAF,OAAA,EAAAF,gBAAA;IAAAK,GAAA;IAAAC,KAAA;MAAA,IAAAC,iBAAA,OAAAC,kBAAA,CAAAN,OAAA,EAK3C,aAIkE;QAAA,IAHhEO,IAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;QAAA,IAChBG,KAAa,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;QAAA,IAClBI,QAAiB,GAAAJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;QAEzB,IAAI;UACF,IAAMK,QAAQ,SAASC,oBAAS,CAACC,GAAG,CAAC,GAAG,IAAI,CAACd,OAAO,iBAAiB,EAAE;YACrEe,MAAM,EAAE;cAAET,IAAI,EAAJA,IAAI;cAAEI,KAAK,EAALA,KAAK;cAAEC,QAAQ,EAARA;YAAS;UAClC,CAAC,CAAC;UAGF,IAAMK,aAAa,GAAGC,KAAK,CAACC,OAAO,CAACN,QAAQ,CAACO,IAAI,CAAC,GAAGP,QAAQ,CAACO,IAAI,GAAG,EAAE;UAEvE,OAAO;YACLH,aAAa,EAAbA,aAAa;YACbI,UAAU,EAAEJ,aAAa,CAACR;UAC5B,CAAC;QACH,CAAC,CAAC,OAAOa,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;UACpD,MAAM,IAAIE,KAAK,CAAC,6BAA6B,CAAC;QAChD;MACF,CAAC;MAAA,SArBKC,gBAAgBA,CAAA;QAAA,OAAApB,iBAAA,CAAAqB,KAAA,OAAAlB,SAAA;MAAA;MAAA,OAAhBiB,gBAAgB;IAAA;EAAA;IAAAtB,GAAA;IAAAC,KAAA;MAAA,IAAAuB,gBAAA,OAAArB,kBAAA,CAAAN,OAAA,EA0BtB,WAAsB4B,cAAsB,EAAyB;QACnE,IAAI;UACF,IAAMf,QAAQ,SAASC,oBAAS,CAACC,GAAG,CAClC,GAAG,IAAI,CAACd,OAAO,kBAAkB2B,cAAc,GACjD,CAAC;UACD,OAAOf,QAAQ,CAACO,IAAI;QACtB,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;UACnD,MAAM,IAAIE,KAAK,CAAC,4BAA4B,CAAC;QAC/C;MACF,CAAC;MAAA,SAVKK,eAAeA,CAAAC,EAAA;QAAA,OAAAH,gBAAA,CAAAD,KAAA,OAAAlB,SAAA;MAAA;MAAA,OAAfqB,eAAe;IAAA;EAAA;IAAA1B,GAAA;IAAAC,KAAA;MAAA,IAAA2B,mBAAA,OAAAzB,kBAAA,CAAAN,OAAA,EAerB,WACEgC,cAAwB,EAGD;QAAA,IAFvBC,IAA0B,GAAAzB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,QAAQ;QAAA,IACrC0B,KAAc,GAAA1B,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;QAEd,IAAI;UACF,IAAMG,QAAQ,SAASC,oBAAS,CAACqB,IAAI,CAAC,GAAG,IAAI,CAAClC,OAAO,iBAAiB,EAAE;YACtEmC,eAAe,EAAEJ,cAAc;YAC/BC,IAAI,EAAJA,IAAI;YACJC,KAAK,EAALA;UACF,CAAC,CAAC;UAEF,OAAOrB,QAAQ,CAACO,IAAI;QACtB,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UACtD,MAAM,IAAIE,KAAK,CAAC,+BAA+B,CAAC;QAClD;MACF,CAAC;MAAA,SAjBKa,kBAAkBA,CAAAC,GAAA;QAAA,OAAAP,mBAAA,CAAAL,KAAA,OAAAlB,SAAA;MAAA;MAAA,OAAlB6B,kBAAkB;IAAA;EAAA;IAAAlC,GAAA;IAAAC,KAAA;MAAA,IAAAmC,YAAA,OAAAjC,kBAAA,CAAAN,OAAA,EAsBxB,WACE4B,cAAsB,EAIgC;QAAA,IAHtDrB,IAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;QAAA,IAChBG,KAAa,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;QAAA,IAClBgC,OAAwB,GAAAhC,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;QAExB,IAAI;UACF,IAAMM,MAAM,GAAAyB,MAAA,CAAAC,MAAA;YACVnC,IAAI,EAAJA,IAAI;YACJI,KAAK,EAALA;UAAK,GACF6B,OAAO,CACX;UAED,IAAM3B,QAAQ,SAASC,oBAAS,CAACC,GAAG,CAClC,GAAG,IAAI,CAACd,OAAO,kBAAkB2B,cAAc,YAAY,EAC3D;YAAEZ,MAAM,EAANA;UAAO,CACX,CAAC;UAED,OAAOH,QAAQ,CAACO,IAAI;QACtB,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C,MAAM,IAAIE,KAAK,CAAC,wBAAwB,CAAC;QAC3C;MACF,CAAC;MAAA,SAvBKmB,WAAWA,CAAAC,GAAA;QAAA,OAAAL,YAAA,CAAAb,KAAA,OAAAlB,SAAA;MAAA;MAAA,OAAXmC,WAAW;IAAA;EAAA;IAAAxC,GAAA;IAAAC,KAAA;MAAA,IAAAyC,YAAA,OAAAvC,kBAAA,CAAAN,OAAA,EA4BjB,WACE4B,cAAsB,EACtBkB,OAAe,EAIG;QAAA,IAHlBb,IAAqB,GAAAzB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,MAAM;QAAA,IAC9BuC,WAAoB,GAAAvC,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;QAAA,IACpBsC,OAAgB,GAAAxC,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;QAEhB,IAAI;UACF,IAAMuC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;UAC/BD,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEL,OAAO,CAAC;UACnCG,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAElB,IAAI,CAAC;UAE7B,IAAIe,OAAO,EAAE;YACXC,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEH,OAAO,CAAC;UACtC;UAEA,IAAID,WAAW,IAAIA,WAAW,CAACtC,MAAM,GAAG,CAAC,EAAE;YACzCsC,WAAW,CAACK,OAAO,CAAC,UAACC,IAAI,EAAEC,KAAK,EAAK;cACnCL,QAAQ,CAACE,MAAM,CAAC,eAAeG,KAAK,GAAG,EAAED,IAAI,CAAC;YAChD,CAAC,CAAC;UACJ;UAEA,IAAMxC,QAAQ,SAASC,oBAAS,CAACqB,IAAI,CACnC,GAAG,IAAI,CAAClC,OAAO,kBAAkB2B,cAAc,gBAAgB,EAC/DqB,QAAQ,EACR;YACEM,OAAO,EAAE;cACP,cAAc,EAAE;YAClB;UACF,CACF,CAAC;UAED,OAAO1C,QAAQ,CAACO,IAAI;QACtB,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C,MAAMkC,iDAA2B,CAACC,kBAAkB,CAACnC,KAAK,EAAW;YACnEoC,MAAM,EAAE,cAAc;YACtBC,cAAc,EAAE;cAAE/B,cAAc,EAAdA,cAAc;cAAEkB,OAAO,EAAEA,OAAO,CAACc,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG;YAAM;UAC9E,CAAC,CAAC;UACF,MAAMtC,KAAK;QACb;MACF,CAAC;MAAA,SAzCKuC,WAAWA,CAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAlB,YAAA,CAAAnB,KAAA,OAAAlB,SAAA;MAAA;MAAA,OAAXqD,WAAW;IAAA;EAAA;IAAA1D,GAAA;IAAAC,KAAA;MAAA,IAAA4D,YAAA,OAAA1D,kBAAA,CAAAN,OAAA,EA8CjB,WAAkBiE,SAAiB,EAAEnB,OAAe,EAAoB;QACtE,IAAI;UACF,IAAMjC,QAAQ,SAASC,oBAAS,CAACoD,KAAK,CACpC,GAAG,IAAI,CAACjE,OAAO,aAAagE,SAAS,GAAG,EACxC;YACEnB,OAAO,EAAPA;UACF,CACF,CAAC;UAED,OAAOjC,QAAQ,CAACO,IAAI;QACtB,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C,MAAMkC,iDAA2B,CAACC,kBAAkB,CAACnC,KAAK,EAAW;YACnEoC,MAAM,EAAE,cAAc;YACtBC,cAAc,EAAE;cAAEM,SAAS,EAATA,SAAS;cAAEnB,OAAO,EAAEA,OAAO,CAACc,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG;YAAM;UACzE,CAAC,CAAC;UACF,MAAMtC,KAAK;QACb;MACF,CAAC;MAAA,SAlBK6C,WAAWA,CAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAL,YAAA,CAAAtC,KAAA,OAAAlB,SAAA;MAAA;MAAA,OAAX2D,WAAW;IAAA;EAAA;IAAAhE,GAAA;IAAAC,KAAA;MAAA,IAAAkE,cAAA,OAAAhE,kBAAA,CAAAN,OAAA,EAuBjB,WAAoBiE,SAAiB,EAAiB;QACpD,IAAI;UACF,MAAMnD,oBAAS,CAACyD,MAAM,CAAC,GAAG,IAAI,CAACtE,OAAO,aAAagE,SAAS,GAAG,CAAC;QAClE,CAAC,CAAC,OAAO3C,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjD,MAAM,IAAIE,KAAK,CAAC,0BAA0B,CAAC;QAC7C;MACF,CAAC;MAAA,SAPKgD,aAAaA,CAAAC,GAAA;QAAA,OAAAH,cAAA,CAAA5C,KAAA,OAAAlB,SAAA;MAAA;MAAA,OAAbgE,aAAa;IAAA;EAAA;IAAArE,GAAA;IAAAC,KAAA;MAAA,IAAAsE,mBAAA,OAAApE,kBAAA,CAAAN,OAAA,EAYnB,WACE4B,cAAsB,EACtB+C,UAAoB,EACL;QACf,IAAI;UACF,MAAM7D,oBAAS,CAACqB,IAAI,CAClB,GAAG,IAAI,CAAClC,OAAO,kBAAkB2B,cAAc,aAAa,EAC5D;YACEgD,WAAW,EAAED;UACf,CACF,CAAC;QACH,CAAC,CAAC,OAAOrD,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;UACxD,MAAM,IAAIE,KAAK,CAAC,iCAAiC,CAAC;QACpD;MACF,CAAC;MAAA,SAfKqD,kBAAkBA,CAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAL,mBAAA,CAAAhD,KAAA,OAAAlB,SAAA;MAAA;MAAA,OAAlBqE,kBAAkB;IAAA;EAAA;IAAA1E,GAAA;IAAAC,KAAA;MAAA,IAAA4E,YAAA,OAAA1E,kBAAA,CAAAN,OAAA,EAoBxB,WACEiE,SAAiB,EACjBgB,KAAa,EACa;QAC1B,IAAI;UACF,IAAMpE,QAAQ,SAASC,oBAAS,CAACqB,IAAI,CACnC,GAAG,IAAI,CAAClC,OAAO,aAAagE,SAAS,aAAa,EAClD;YACEgB,KAAK,EAALA;UACF,CACF,CAAC;UAED,OAAOpE,QAAQ,CAACO,IAAI;QACtB,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C,MAAM,IAAIE,KAAK,CAAC,wBAAwB,CAAC;QAC3C;MACF,CAAC;MAAA,SAjBK0D,WAAWA,CAAAC,GAAA,EAAAC,IAAA;QAAA,OAAAJ,YAAA,CAAAtD,KAAA,OAAAlB,SAAA;MAAA;MAAA,OAAX0E,WAAW;IAAA;EAAA;IAAA/E,GAAA;IAAAC,KAAA;MAAA,IAAAiF,eAAA,OAAA/E,kBAAA,CAAAN,OAAA,EAsBjB,WAAqBiE,SAAiB,EAAEqB,UAAkB,EAAiB;QACzE,IAAI;UACF,MAAMxE,oBAAS,CAACyD,MAAM,CACpB,GAAG,IAAI,CAACtE,OAAO,aAAagE,SAAS,cAAcqB,UAAU,GAC/D,CAAC;QACH,CAAC,CAAC,OAAOhE,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UAClD,MAAM,IAAIE,KAAK,CAAC,2BAA2B,CAAC;QAC9C;MACF,CAAC;MAAA,SATK+D,cAAcA,CAAAC,IAAA,EAAAC,IAAA;QAAA,OAAAJ,eAAA,CAAA3D,KAAA,OAAAlB,SAAA;MAAA;MAAA,OAAd+E,cAAc;IAAA;EAAA;IAAApF,GAAA;IAAAC,KAAA;MAAA,IAAAsF,eAAA,OAAApF,kBAAA,CAAAN,OAAA,EAcpB,WACE2F,KAAa,EACb/D,cAAuB,EAEH;QAAA,IADpBjB,KAAa,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;QAElB,IAAI;UACF,IAAMQ,MAAM,GAAG;YACb4E,CAAC,EAAED,KAAK;YACRE,eAAe,EAAEjE,cAAc;YAC/BjB,KAAK,EAALA;UACF,CAAC;UAED,IAAME,QAAQ,SAASC,oBAAS,CAACC,GAAG,CAAC,GAAG,IAAI,CAACd,OAAO,UAAU,EAAE;YAC9De,MAAM,EAANA;UACF,CAAC,CAAC;UACF,OAAOH,QAAQ,CAACO,IAAI,CAAC0E,QAAQ;QAC/B,CAAC,CAAC,OAAOxE,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UAClD,MAAM,IAAIE,KAAK,CAAC,2BAA2B,CAAC;QAC9C;MACF,CAAC;MAAA,SApBKuE,cAAcA,CAAAC,IAAA,EAAAC,IAAA;QAAA,OAAAP,eAAA,CAAAhE,KAAA,OAAAlB,SAAA;MAAA;MAAA,OAAduF,cAAc;IAAA;EAAA;IAAA5F,GAAA;IAAAC,KAAA;MAAA,IAAA8F,iBAAA,OAAA5F,kBAAA,CAAAN,OAAA,EAyBpB,WAAuBqD,IAAU,EAA8B;QAC7D,IAAI;UACF,IAAMJ,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;UAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEE,IAAI,CAAC;UAE7B,IAAMxC,QAAQ,SAASC,oBAAS,CAACqB,IAAI,CACnC,GAAG,IAAI,CAAClC,OAAO,eAAe,EAC9BgD,QAAQ,EACR;YACEM,OAAO,EAAE;cACP,cAAc,EAAE;YAClB;UACF,CACF,CAAC;UAED,OAAO1C,QAAQ,CAACO,IAAI;QACtB,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;UACpD,MAAM,IAAIE,KAAK,CAAC,6BAA6B,CAAC;QAChD;MACF,CAAC;MAAA,SApBK2E,gBAAgBA,CAAAC,IAAA;QAAA,OAAAF,iBAAA,CAAAxE,KAAA,OAAAlB,SAAA;MAAA;MAAA,OAAhB2F,gBAAgB;IAAA;EAAA;IAAAhG,GAAA;IAAAC,KAAA;MAAA,IAAAiG,yBAAA,OAAA/F,kBAAA,CAAAN,OAAA,EAyBtB,WAA+B4B,cAAsB,EASlD;QACD,IAAI;UACF,IAAMf,QAAQ,SAASC,oBAAS,CAACC,GAAG,CAClC,GAAG,IAAI,CAACd,OAAO,kBAAkB2B,cAAc,aACjD,CAAC;UAED,OAAOf,QAAQ,CAACO,IAAI;QACtB,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;UAC7D,MAAM,IAAIE,KAAK,CAAC,sCAAsC,CAAC;QACzD;MACF,CAAC;MAAA,SApBK8E,wBAAwBA,CAAAC,IAAA;QAAA,OAAAF,yBAAA,CAAA3E,KAAA,OAAAlB,SAAA;MAAA;MAAA,OAAxB8F,wBAAwB;IAAA;EAAA;IAAAnG,GAAA;IAAAC,KAAA,EAyB9B,SAAAoG,iBAAiBA,CAACC,SAAiB,EAAU;MAC3C,IAAMC,WAAW,GAAG,IAAIC,IAAI,CAACF,SAAS,CAAC;MACvC,IAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;MACtB,IAAME,aAAa,GAAGC,IAAI,CAACC,KAAK,CAC9B,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC,GAAGN,WAAW,CAACM,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,CACtD,CAAC;MAED,IAAIH,aAAa,GAAG,CAAC,EAAE;QACrB,OAAO,UAAU;MACnB,CAAC,MAAM,IAAIA,aAAa,GAAG,EAAE,EAAE;QAC7B,OAAO,GAAGA,aAAa,OAAO;MAChC,CAAC,MAAM,IAAIA,aAAa,GAAG,IAAI,EAAE;QAC/B,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC,OAAO;MACjD,CAAC,MAAM;QACL,OAAOH,WAAW,CAACO,kBAAkB,CAAC,CAAC;MACzC;IACF;EAAC;IAAA9G,GAAA;IAAAC,KAAA,EAKD,SAAA8G,cAAcA,CAACC,KAAa,EAAU;MACpC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;MAEjC,IAAMC,CAAC,GAAG,IAAI;MACd,IAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MACzC,IAAMC,CAAC,GAAGR,IAAI,CAACC,KAAK,CAACD,IAAI,CAACS,GAAG,CAACJ,KAAK,CAAC,GAAGL,IAAI,CAACS,GAAG,CAACH,CAAC,CAAC,CAAC;MAEnD,OAAOI,UAAU,CAAC,CAACL,KAAK,GAAGL,IAAI,CAACW,GAAG,CAACL,CAAC,EAAEE,CAAC,CAAC,EAAEI,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGL,KAAK,CAACC,CAAC,CAAC;IACzE;EAAC;IAAAnH,GAAA;IAAAC,KAAA,EAKD,SAAAuH,eAAeA,CACb7E,OAAe,EACfb,IAAqB,EACiB;MACtC,IAAI,CAACa,OAAO,CAAC8E,IAAI,CAAC,CAAC,EAAE;QACnB,OAAO;UAAEC,OAAO,EAAE,KAAK;UAAEvG,KAAK,EAAE;QAA0B,CAAC;MAC7D;MAEA,IAAIwB,OAAO,CAACrC,MAAM,GAAG,IAAI,EAAE;QACzB,OAAO;UACLoH,OAAO,EAAE,KAAK;UACdvG,KAAK,EAAE;QACT,CAAC;MACH;MAEA,IAAIW,IAAI,KAAK,MAAM,IAAIa,OAAO,CAACgF,QAAQ,CAAC,UAAU,CAAC,EAAE;QACnD,OAAO;UAAED,OAAO,EAAE,KAAK;UAAEvG,KAAK,EAAE;QAA2B,CAAC;MAC9D;MAEA,OAAO;QAAEuG,OAAO,EAAE;MAAK,CAAC;IAC1B;EAAC;AAAA;AAGI,IAAME,gBAAgB,GAAAC,OAAA,CAAAD,gBAAA,GAAG,IAAIjI,gBAAgB,CAAC,CAAC", "ignoreList": []}