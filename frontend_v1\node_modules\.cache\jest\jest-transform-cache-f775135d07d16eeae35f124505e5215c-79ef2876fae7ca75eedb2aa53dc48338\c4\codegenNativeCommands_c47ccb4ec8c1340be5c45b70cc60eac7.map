{"version": 3, "names": ["_require", "require", "dispatchCommand", "codegenNativeCommands", "options", "commandObj", "supportedCommands", "for<PERSON>ach", "command", "ref", "_len", "arguments", "length", "args", "Array", "_key", "_default", "exports", "default"], "sources": ["codegenNativeCommands.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow\n */\n\nconst {dispatchCommand} = require('../ReactNative/RendererProxy');\n\ntype Options<T = string> = $ReadOnly<{\n  supportedCommands: $ReadOnlyArray<T>,\n}>;\n\nfunction codegenNativeCommands<T: interface {}>(options: Options<$Keys<T>>): T {\n  const commandObj: {[$Keys<T>]: (...$ReadOnlyArray<mixed>) => void} = {};\n\n  options.supportedCommands.forEach(command => {\n    // $FlowFixMe[missing-local-annot]\n    commandObj[command] = (ref, ...args) => {\n      // $FlowFixMe[incompatible-call]\n      dispatchCommand(ref, command, args);\n    };\n  });\n\n  return ((commandObj: any): T);\n}\n\nexport default codegenNativeCommands;\n"], "mappings": ";;;;AAUA,IAAAA,QAAA,GAA0BC,OAAO,+BAA+B,CAAC;EAA1DC,eAAe,GAAAF,QAAA,CAAfE,eAAe;AAMtB,SAASC,qBAAqBA,CAAkBC,OAA0B,EAAK;EAC7E,IAAMC,UAA4D,GAAG,CAAC,CAAC;EAEvED,OAAO,CAACE,iBAAiB,CAACC,OAAO,CAAC,UAAAC,OAAO,EAAI;IAE3CH,UAAU,CAACG,OAAO,CAAC,GAAG,UAACC,GAAG,EAAc;MAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAATC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;QAAJF,IAAI,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;MAAA;MAEjCb,eAAe,CAACO,GAAG,EAAED,OAAO,EAAEK,IAAI,CAAC;IACrC,CAAC;EACH,CAAC,CAAC;EAEF,OAASR,UAAU;AACrB;AAAC,IAAAW,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEcf,qBAAqB", "ignoreList": []}