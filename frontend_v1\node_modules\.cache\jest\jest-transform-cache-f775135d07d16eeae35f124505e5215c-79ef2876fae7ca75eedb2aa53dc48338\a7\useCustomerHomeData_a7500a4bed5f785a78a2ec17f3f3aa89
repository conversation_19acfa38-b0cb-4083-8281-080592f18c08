84ea82be42015b48277be326af895584
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useCustomerHomeData = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _react = require("react");
var _cacheService = _interopRequireDefault(require("../services/cacheService"));
var _customerService = _interopRequireDefault(require("../services/customerService"));
var _performanceMonitor = require("../services/performanceMonitor");
var _authSlice = require("../store/authSlice");
var initialData = {
  categories: [],
  featuredProviders: [],
  favoriteProviders: [],
  nearbyProviders: [],
  dashboard: null,
  recommendations: [],
  recentBookings: [],
  unreadNotifications: 0
};
var initialLoading = {
  categories: false,
  featuredProviders: false,
  favoriteProviders: false,
  nearbyProviders: false,
  dashboard: false,
  recommendations: false,
  recentBookings: false,
  overall: false
};
var initialError = {
  categories: null,
  featuredProviders: null,
  favoriteProviders: null,
  nearbyProviders: null,
  dashboard: null,
  recommendations: null,
  recentBookings: null,
  overall: null
};
var useCustomerHomeData = exports.useCustomerHomeData = function useCustomerHomeData() {
  var _useAuthStore = (0, _authSlice.useAuthStore)(),
    isAuthenticated = _useAuthStore.isAuthenticated,
    user = _useAuthStore.user;
  var _useState = (0, _react.useState)({
      data: initialData,
      loading: initialLoading,
      error: initialError,
      lastUpdated: null,
      refreshing: false
    }),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    state = _useState2[0],
    setState = _useState2[1];
  var abortControllerRef = (0, _react.useRef)(null);
  var cacheRef = (0, _react.useRef)({});
  var CACHE_DURATION = 5 * 60 * 1000;
  var isCacheValid = (0, _react.useCallback)(function (key) {
    var cached = cacheRef.current[key];
    if (!cached) return false;
    return Date.now() - cached.timestamp < CACHE_DURATION;
  }, []);
  var getCachedData = (0, _react.useCallback)(function (key) {
    var cached = cacheRef.current[key];
    return cached ? cached.data : null;
  }, []);
  var setCache = (0, _react.useCallback)(function (key, data) {
    cacheRef.current[key] = {
      data: data,
      timestamp: Date.now()
    };
  }, []);
  var setLoading = (0, _react.useCallback)(function (key, value) {
    setState(function (prev) {
      return Object.assign({}, prev, {
        loading: Object.assign({}, prev.loading, (0, _defineProperty2.default)((0, _defineProperty2.default)({}, key, value), "overall", value || Object.values(Object.assign({}, prev.loading, (0, _defineProperty2.default)({}, key, value))).some(Boolean)))
      });
    });
  }, []);
  var setError = (0, _react.useCallback)(function (key, value) {
    setState(function (prev) {
      return Object.assign({}, prev, {
        error: Object.assign({}, prev.error, (0, _defineProperty2.default)({}, key, value))
      });
    });
  }, []);
  var setData = (0, _react.useCallback)(function (key, value) {
    setState(function (prev) {
      return Object.assign({}, prev, {
        data: Object.assign({}, prev.data, (0, _defineProperty2.default)({}, key, value)),
        lastUpdated: new Date()
      });
    });
  }, []);
  var fetchCategories = (0, _react.useCallback)((0, _asyncToGenerator2.default)(function* () {
    var cacheKey = 'customer_home_categories';
    var startTime = Date.now();
    setLoading('categories', true);
    setError('categories', null);
    try {
      var cachedData = yield _cacheService.default.get(cacheKey);
      if (cachedData) {
        setData('categories', cachedData);
        setLoading('categories', false);
        _performanceMonitor.performanceMonitor.trackNetworkRequest('/api/catalog/categories/', 'GET', Date.now() - startTime, 200, 0, 0, true);
        return;
      }
      var categories = yield _customerService.default.getServiceCategories();
      _performanceMonitor.performanceMonitor.trackNetworkRequest('/api/catalog/categories/', 'GET', Date.now() - startTime, 200, 0, 0, false);
      yield _cacheService.default.set(cacheKey, categories, 5 * 60 * 1000);
      setData('categories', categories);
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      setError('categories', error.message || 'Failed to load categories');
      _performanceMonitor.performanceMonitor.trackNetworkRequest('/api/catalog/categories/', 'GET', Date.now() - startTime, error.status || 500, 0, 0, false);
    } finally {
      setLoading('categories', false);
    }
  }), [setData, setLoading, setError]);
  var fetchFeaturedProviders = (0, _react.useCallback)((0, _asyncToGenerator2.default)(function* () {
    var cacheKey = 'customer_home_featured_providers';
    var startTime = Date.now();
    setLoading('featuredProviders', true);
    setError('featuredProviders', null);
    try {
      var cachedData = yield _cacheService.default.get(cacheKey);
      if (cachedData) {
        setData('featuredProviders', cachedData);
        setLoading('featuredProviders', false);
        _performanceMonitor.performanceMonitor.trackNetworkRequest('/api/catalog/providers/featured/', 'GET', Date.now() - startTime, 200, 0, 0, true);
        return;
      }
      var providers = yield _customerService.default.getFeaturedProviders(10);
      _performanceMonitor.performanceMonitor.trackNetworkRequest('/api/catalog/providers/featured/', 'GET', Date.now() - startTime, 200, 0, 0, false);
      yield _cacheService.default.set(cacheKey, providers, 10 * 60 * 1000);
      setData('featuredProviders', providers);
    } catch (error) {
      console.error('Failed to fetch featured providers:', error);
      setError('featuredProviders', error.message || 'Failed to load featured providers');
      _performanceMonitor.performanceMonitor.trackNetworkRequest('/api/catalog/providers/featured/', 'GET', Date.now() - startTime, error.status || 500, 0, 0, false);
    } finally {
      setLoading('featuredProviders', false);
    }
  }), [setData, setLoading, setError]);
  var fetchFavoriteProviders = (0, _react.useCallback)((0, _asyncToGenerator2.default)(function* () {
    if (!isAuthenticated) return;
    var cacheKey = 'favoriteProviders';
    if (isCacheValid(cacheKey)) {
      setData('favoriteProviders', getCachedData(cacheKey));
      return;
    }
    setLoading('favoriteProviders', true);
    setError('favoriteProviders', null);
    try {
      var providers = yield _customerService.default.getFavoriteProviders();
      setData('favoriteProviders', providers);
      setCache(cacheKey, providers);
    } catch (error) {
      console.error('Failed to fetch favorite providers:', error);
      setError('favoriteProviders', error.message || 'Failed to load favorite providers');
    } finally {
      setLoading('favoriteProviders', false);
    }
  }), [isAuthenticated, isCacheValid, getCachedData, setData, setCache, setLoading, setError]);
  var fetchNearbyProviders = (0, _react.useCallback)((0, _asyncToGenerator2.default)(function* () {
    var defaultLat = 45.4215;
    var defaultLng = -75.6972;
    var cacheKey = 'nearbyProviders';
    if (isCacheValid(cacheKey)) {
      setData('nearbyProviders', getCachedData(cacheKey));
      return;
    }
    setLoading('nearbyProviders', true);
    setError('nearbyProviders', null);
    try {
      var providers = yield _customerService.default.getNearbyProviders(defaultLat, defaultLng, 10, 10);
      setData('nearbyProviders', providers);
      setCache(cacheKey, providers);
    } catch (error) {
      console.error('Failed to fetch nearby providers:', error);
      setError('nearbyProviders', error.message || 'Failed to load nearby providers');
    } finally {
      setLoading('nearbyProviders', false);
    }
  }), [isCacheValid, getCachedData, setData, setCache, setLoading, setError]);
  var fetchDashboard = (0, _react.useCallback)((0, _asyncToGenerator2.default)(function* () {
    if (!isAuthenticated) return;
    var cacheKey = 'dashboard';
    if (isCacheValid(cacheKey)) {
      setData('dashboard', getCachedData(cacheKey));
      return;
    }
    setLoading('dashboard', true);
    setError('dashboard', null);
    try {
      var dashboard = yield _customerService.default.getCustomerDashboard();
      setData('dashboard', dashboard);
      setCache(cacheKey, dashboard);
    } catch (error) {
      console.error('Failed to fetch dashboard:', error);
      setError('dashboard', error.message || 'Failed to load dashboard');
    } finally {
      setLoading('dashboard', false);
    }
  }), [isAuthenticated, isCacheValid, getCachedData, setData, setCache, setLoading, setError]);
  var fetchRecommendations = (0, _react.useCallback)((0, _asyncToGenerator2.default)(function* () {
    if (!isAuthenticated) return;
    var cacheKey = 'recommendations';
    if (isCacheValid(cacheKey)) {
      setData('recommendations', getCachedData(cacheKey));
      return;
    }
    setLoading('recommendations', true);
    setError('recommendations', null);
    try {
      var recommendations = yield _customerService.default.getPersonalizedRecommendations();
      setData('recommendations', recommendations);
      setCache(cacheKey, recommendations);
    } catch (error) {
      console.error('Failed to fetch recommendations:', error);
      setError('recommendations', error.message || 'Failed to load recommendations');
    } finally {
      setLoading('recommendations', false);
    }
  }), [isAuthenticated, isCacheValid, getCachedData, setData, setCache, setLoading, setError]);
  var loadAllData = (0, _react.useCallback)((0, _asyncToGenerator2.default)(function* () {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    abortControllerRef.current = new AbortController();
    yield Promise.allSettled([fetchCategories(), fetchFeaturedProviders(), fetchFavoriteProviders(), fetchNearbyProviders(), fetchDashboard(), fetchRecommendations()]);
  }), [fetchCategories, fetchFeaturedProviders, fetchFavoriteProviders, fetchNearbyProviders, fetchDashboard, fetchRecommendations]);
  var refresh = (0, _react.useCallback)((0, _asyncToGenerator2.default)(function* () {
    setState(function (prev) {
      return Object.assign({}, prev, {
        refreshing: true
      });
    });
    cacheRef.current = {};
    try {
      yield loadAllData();
    } finally {
      setState(function (prev) {
        return Object.assign({}, prev, {
          refreshing: false
        });
      });
    }
  }), [loadAllData]);
  (0, _react.useEffect)(function () {
    var loadData = function () {
      var _ref9 = (0, _asyncToGenerator2.default)(function* () {
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }
        abortControllerRef.current = new AbortController();
        yield Promise.allSettled([fetchCategories(), fetchFeaturedProviders(), fetchFavoriteProviders(), fetchNearbyProviders(), fetchDashboard(), fetchRecommendations()]);
      });
      return function loadData() {
        return _ref9.apply(this, arguments);
      };
    }();
    loadData();
    return function () {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);
  return Object.assign({}, state, {
    refresh: refresh,
    loadAllData: loadAllData,
    fetchCategories: fetchCategories,
    fetchFeaturedProviders: fetchFeaturedProviders,
    fetchFavoriteProviders: fetchFavoriteProviders,
    fetchNearbyProviders: fetchNearbyProviders,
    fetchDashboard: fetchDashboard,
    fetchRecommendations: fetchRecommendations
  });
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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