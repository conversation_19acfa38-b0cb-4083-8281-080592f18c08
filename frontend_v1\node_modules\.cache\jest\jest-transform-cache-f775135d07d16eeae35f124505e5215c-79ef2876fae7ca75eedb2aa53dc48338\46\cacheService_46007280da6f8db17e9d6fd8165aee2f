65765bcac6241087483a10b6036eb06d
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.cacheService = exports.CacheService = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _asyncStorage = _interopRequireDefault(require("@react-native-async-storage/async-storage"));
var CacheService = exports.CacheService = function () {
  function CacheService(config) {
    (0, _classCallCheck2.default)(this, CacheService);
    this.memoryCache = new Map();
    this.CACHE_VERSION = '1.0.0';
    this.STORAGE_PREFIX = '@vierla_cache_';
    this.config = Object.assign({
      defaultTTL: 5 * 60 * 1000,
      maxMemorySize: 50 * 1024 * 1024,
      maxStorageSize: 100 * 1024 * 1024,
      compressionThreshold: 1024,
      enableEncryption: false,
      enableAnalytics: true,
      cleanupInterval: 60 * 1000
    }, config);
    this.stats = {
      memoryHits: 0,
      memoryMisses: 0,
      storageHits: 0,
      storageMisses: 0,
      totalSize: 0,
      entryCount: 0,
      hitRate: 0,
      averageAccessTime: 0
    };
    this.startCleanupTimer();
  }
  return (0, _createClass2.default)(CacheService, [{
    key: "get",
    value: (function () {
      var _get = (0, _asyncToGenerator2.default)(function* (key) {
        var startTime = Date.now();
        try {
          var memoryEntry = this.memoryCache.get(key);
          if (memoryEntry && this.isValidEntry(memoryEntry)) {
            this.updateStats('memoryHit', Date.now() - startTime);
            this.updateEntryAccess(key, memoryEntry);
            return memoryEntry.data;
          }
          if (memoryEntry) {
            this.memoryCache.delete(key);
          }
          var storageEntry = yield this.getFromStorage(key);
          if (storageEntry && this.isValidEntry(storageEntry)) {
            this.updateStats('storageHit', Date.now() - startTime);
            this.updateEntryAccess(key, storageEntry);
            this.memoryCache.set(key, storageEntry);
            return storageEntry.data;
          }
          this.updateStats('miss', Date.now() - startTime);
          return null;
        } catch (error) {
          console.error('Cache get error:', error);
          return null;
        }
      });
      function get(_x) {
        return _get.apply(this, arguments);
      }
      return get;
    }())
  }, {
    key: "set",
    value: (function () {
      var _set = (0, _asyncToGenerator2.default)(function* (key, data, ttl, options) {
        var entry = {
          data: data,
          timestamp: Date.now(),
          ttl: ttl || this.config.defaultTTL,
          version: this.CACHE_VERSION,
          accessCount: 0,
          lastAccessed: Date.now()
        };
        try {
          var dataSize = this.estimateSize(data);
          var shouldCompress = (options == null ? void 0 : options.compress) || dataSize > this.config.compressionThreshold && !(options != null && options.memoryOnly);
          if (shouldCompress) {
            entry.compressed = true;
          }
          if (!(options != null && options.storageOnly)) {
            this.memoryCache.set(key, entry);
            this.enforceMemoryLimit();
          }
          if (!(options != null && options.memoryOnly)) {
            yield this.setInStorage(key, entry);
          }
          this.updateCacheStats();
        } catch (error) {
          console.error('Cache set error:', error);
        }
      });
      function set(_x2, _x3, _x4, _x5) {
        return _set.apply(this, arguments);
      }
      return set;
    }())
  }, {
    key: "remove",
    value: (function () {
      var _remove = (0, _asyncToGenerator2.default)(function* (key) {
        try {
          this.memoryCache.delete(key);
          yield _asyncStorage.default.removeItem(this.STORAGE_PREFIX + key);
          this.updateCacheStats();
        } catch (error) {
          console.error('Cache remove error:', error);
        }
      });
      function remove(_x6) {
        return _remove.apply(this, arguments);
      }
      return remove;
    }())
  }, {
    key: "clear",
    value: (function () {
      var _clear = (0, _asyncToGenerator2.default)(function* () {
        var _this = this;
        try {
          this.memoryCache.clear();
          var keys = yield _asyncStorage.default.getAllKeys();
          var cacheKeys = keys.filter(function (key) {
            return key.startsWith(_this.STORAGE_PREFIX);
          });
          yield _asyncStorage.default.multiRemove(cacheKeys);
          this.resetStats();
        } catch (error) {
          console.error('Cache clear error:', error);
        }
      });
      function clear() {
        return _clear.apply(this, arguments);
      }
      return clear;
    }())
  }, {
    key: "preload",
    value: (function () {
      var _preload = (0, _asyncToGenerator2.default)(function* (entries) {
        var _this2 = this;
        var promises = entries.map(function (_ref) {
          var key = _ref.key,
            data = _ref.data,
            ttl = _ref.ttl;
          return _this2.set(key, data, ttl);
        });
        yield Promise.allSettled(promises);
      });
      function preload(_x7) {
        return _preload.apply(this, arguments);
      }
      return preload;
    }())
  }, {
    key: "getStats",
    value: function getStats() {
      return Object.assign({}, this.stats);
    }
  }, {
    key: "getEntryInfo",
    value: function getEntryInfo(key) {
      var entry = this.memoryCache.get(key);
      if (!entry) return null;
      return {
        timestamp: entry.timestamp,
        ttl: entry.ttl,
        version: entry.version,
        accessCount: entry.accessCount,
        lastAccessed: entry.lastAccessed,
        compressed: entry.compressed,
        encrypted: entry.encrypted
      };
    }
  }, {
    key: "invalidatePattern",
    value: (function () {
      var _invalidatePattern = (0, _asyncToGenerator2.default)(function* (pattern) {
        var _this3 = this;
        for (var key of this.memoryCache.keys()) {
          if (pattern.test(key)) {
            this.memoryCache.delete(key);
          }
        }
        try {
          var keys = yield _asyncStorage.default.getAllKeys();
          var cacheKeys = keys.filter(function (key) {
            return key.startsWith(_this3.STORAGE_PREFIX);
          }).map(function (key) {
            return key.replace(_this3.STORAGE_PREFIX, '');
          }).filter(function (key) {
            return pattern.test(key);
          });
          var storageKeys = cacheKeys.map(function (key) {
            return _this3.STORAGE_PREFIX + key;
          });
          yield _asyncStorage.default.multiRemove(storageKeys);
        } catch (error) {
          console.error('Cache pattern invalidation error:', error);
        }
        this.updateCacheStats();
      });
      function invalidatePattern(_x8) {
        return _invalidatePattern.apply(this, arguments);
      }
      return invalidatePattern;
    }())
  }, {
    key: "getFromStorage",
    value: (function () {
      var _getFromStorage = (0, _asyncToGenerator2.default)(function* (key) {
        try {
          var stored = yield _asyncStorage.default.getItem(this.STORAGE_PREFIX + key);
          if (!stored) return null;
          var entry = JSON.parse(stored);
          if (entry.compressed) {}
          return entry;
        } catch (error) {
          console.error('Storage get error:', error);
          return null;
        }
      });
      function getFromStorage(_x9) {
        return _getFromStorage.apply(this, arguments);
      }
      return getFromStorage;
    }())
  }, {
    key: "setInStorage",
    value: (function () {
      var _setInStorage = (0, _asyncToGenerator2.default)(function* (key, entry) {
        try {
          var serialized = JSON.stringify(entry);
          yield _asyncStorage.default.setItem(this.STORAGE_PREFIX + key, serialized);
        } catch (error) {
          console.error('Storage set error:', error);
        }
      });
      function setInStorage(_x0, _x1) {
        return _setInStorage.apply(this, arguments);
      }
      return setInStorage;
    }())
  }, {
    key: "isValidEntry",
    value: function isValidEntry(entry) {
      var now = Date.now();
      return now - entry.timestamp < entry.ttl;
    }
  }, {
    key: "updateEntryAccess",
    value: function updateEntryAccess(key, entry) {
      entry.accessCount++;
      entry.lastAccessed = Date.now();
      this.memoryCache.set(key, entry);
    }
  }, {
    key: "estimateSize",
    value: function estimateSize(data) {
      return JSON.stringify(data).length * 2;
    }
  }, {
    key: "enforceMemoryLimit",
    value: function enforceMemoryLimit() {
      var _this4 = this;
      var entries = Array.from(this.memoryCache.entries());
      var totalSize = entries.reduce(function (sum, _ref2) {
        var _ref3 = (0, _slicedToArray2.default)(_ref2, 2),
          entry = _ref3[1];
        return sum + _this4.estimateSize(entry.data);
      }, 0);
      if (totalSize <= this.config.maxMemorySize) return;
      entries.sort(function (_ref4, _ref5) {
        var _ref6 = (0, _slicedToArray2.default)(_ref4, 2),
          a = _ref6[1];
        var _ref7 = (0, _slicedToArray2.default)(_ref5, 2),
          b = _ref7[1];
        var scoreA = a.accessCount / (Date.now() - a.lastAccessed);
        var scoreB = b.accessCount / (Date.now() - b.lastAccessed);
        return scoreA - scoreB;
      });
      while (totalSize > this.config.maxMemorySize && entries.length > 0) {
        var _ref8 = entries.shift(),
          _ref9 = (0, _slicedToArray2.default)(_ref8, 2),
          key = _ref9[0],
          entry = _ref9[1];
        this.memoryCache.delete(key);
        totalSize -= this.estimateSize(entry.data);
      }
    }
  }, {
    key: "updateStats",
    value: function updateStats(type, accessTime) {
      if (!this.config.enableAnalytics) return;
      switch (type) {
        case 'memoryHit':
          this.stats.memoryHits++;
          break;
        case 'storageHit':
          this.stats.storageHits++;
          break;
        case 'miss':
          this.stats.memoryMisses++;
          this.stats.storageMisses++;
          break;
      }
      var totalRequests = this.stats.memoryHits + this.stats.storageHits + this.stats.memoryMisses;
      this.stats.hitRate = totalRequests > 0 ? (this.stats.memoryHits + this.stats.storageHits) / totalRequests : 0;
      this.stats.averageAccessTime = this.stats.averageAccessTime * 0.9 + accessTime * 0.1;
    }
  }, {
    key: "updateCacheStats",
    value: function updateCacheStats() {
      var _this5 = this;
      this.stats.entryCount = this.memoryCache.size;
      this.stats.totalSize = Array.from(this.memoryCache.values()).reduce(function (sum, entry) {
        return sum + _this5.estimateSize(entry.data);
      }, 0);
    }
  }, {
    key: "resetStats",
    value: function resetStats() {
      this.stats = {
        memoryHits: 0,
        memoryMisses: 0,
        storageHits: 0,
        storageMisses: 0,
        totalSize: 0,
        entryCount: 0,
        hitRate: 0,
        averageAccessTime: 0
      };
    }
  }, {
    key: "startCleanupTimer",
    value: function startCleanupTimer() {
      var _this6 = this;
      this.cleanupTimer = setInterval(function () {
        _this6.cleanup();
      }, this.config.cleanupInterval);
    }
  }, {
    key: "cleanup",
    value: function cleanup() {
      var now = Date.now();
      for (var _ref0 of this.memoryCache.entries()) {
        var _ref1 = (0, _slicedToArray2.default)(_ref0, 2);
        var key = _ref1[0];
        var entry = _ref1[1];
        if (!this.isValidEntry(entry)) {
          this.memoryCache.delete(key);
        }
      }
      this.updateCacheStats();
    }
  }, {
    key: "destroy",
    value: function destroy() {
      if (this.cleanupTimer) {
        clearInterval(this.cleanupTimer);
      }
      this.memoryCache.clear();
    }
  }]);
}();
var cacheService = exports.cacheService = new CacheService();
var _default = exports.default = cacheService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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