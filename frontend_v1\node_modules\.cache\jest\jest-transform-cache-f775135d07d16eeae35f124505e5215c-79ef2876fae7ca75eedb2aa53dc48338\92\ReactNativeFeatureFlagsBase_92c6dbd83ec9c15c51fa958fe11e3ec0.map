{"version": 3, "names": ["_NativeReactNativeFeatureFlags", "_interopRequireDefault", "require", "accessedFeatureFlags", "Set", "overrides", "createGetter", "config<PERSON><PERSON>", "customValueGetter", "defaultValue", "cachedValue", "_customValueGetter", "createJavaScriptFlagGetter", "_overrides", "_overrides$configName", "add", "call", "createNativeFlagGetter", "skipUnavailableNativeModuleError", "arguments", "length", "undefined", "_NativeReactNativeFea", "maybeLogUnavailableNativeModuleError", "NativeReactNativeFeatureFlags", "getOverrides", "setOverrides", "newOverrides", "Error", "size", "accessedFeatureFlagsStr", "Array", "from", "join", "reportedConfigNames", "has", "console", "error"], "sources": ["ReactNativeFeatureFlagsBase.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\nimport type {\n  ReactNativeFeatureFlagsJsOnly,\n  ReactNativeFeatureFlagsJsOnlyOverrides,\n} from './ReactNativeFeatureFlags';\n\nimport NativeReactNativeFeatureFlags from './specs/NativeReactNativeFeatureFlags';\n\nconst accessedFeatureFlags: Set<string> = new Set();\nlet overrides: ?ReactNativeFeatureFlagsJsOnlyOverrides;\n\nexport type Getter<T> = () => T;\n\n// This defines the types for the overrides object, whose methods also receive\n// the default value as a parameter.\nexport type OverridesFor<T> = Partial<{\n  [key in keyof T]: (ReturnType<T[key]>) => ReturnType<T[key]>,\n}>;\n\nfunction createGetter<T: boolean | number | string>(\n  configName: string,\n  customValueGetter: Getter<?T>,\n  defaultValue: T,\n): Getter<T> {\n  let cachedValue: ?T;\n\n  return () => {\n    if (cachedValue == null) {\n      cachedValue = customValueGetter() ?? defaultValue;\n    }\n    return cachedValue;\n  };\n}\n\nexport function createJavaScriptFlagGetter<\n  K: $Keys<ReactNativeFeatureFlagsJsOnly>,\n>(\n  configName: K,\n  defaultValue: ReturnType<ReactNativeFeatureFlagsJsOnly[K]>,\n): Getter<ReturnType<ReactNativeFeatureFlagsJsOnly[K]>> {\n  return createGetter(\n    configName,\n    () => {\n      accessedFeatureFlags.add(configName);\n      return overrides?.[configName]?.(defaultValue);\n    },\n    defaultValue,\n  );\n}\n\ntype NativeFeatureFlags = $NonMaybeType<typeof NativeReactNativeFeatureFlags>;\n\nexport function createNativeFlagGetter<K: $Keys<NativeFeatureFlags>>(\n  configName: K,\n  defaultValue: ReturnType<$NonMaybeType<NativeFeatureFlags[K]>>,\n  skipUnavailableNativeModuleError: boolean = false,\n): Getter<ReturnType<$NonMaybeType<NativeFeatureFlags[K]>>> {\n  return createGetter(\n    configName,\n    () => {\n      maybeLogUnavailableNativeModuleError(configName);\n      return NativeReactNativeFeatureFlags?.[configName]?.();\n    },\n    defaultValue,\n  );\n}\n\nexport function getOverrides(): ?ReactNativeFeatureFlagsJsOnlyOverrides {\n  return overrides;\n}\n\nexport function setOverrides(\n  newOverrides: ReactNativeFeatureFlagsJsOnlyOverrides,\n): void {\n  if (overrides != null) {\n    throw new Error('Feature flags cannot be overridden more than once');\n  }\n\n  if (accessedFeatureFlags.size > 0) {\n    const accessedFeatureFlagsStr = Array.from(accessedFeatureFlags).join(', ');\n    throw new Error(\n      `Feature flags were accessed before being overridden: ${accessedFeatureFlagsStr}`,\n    );\n  }\n\n  overrides = newOverrides;\n}\n\nconst reportedConfigNames: Set<string> = new Set();\n\nfunction maybeLogUnavailableNativeModuleError(configName: string): void {\n  if (!NativeReactNativeFeatureFlags && !reportedConfigNames.has(configName)) {\n    reportedConfigNames.add(configName);\n    console.error(\n      `Could not access feature flag '${configName}' because native module method was not available`,\n    );\n  }\n}\n"], "mappings": ";;;;;;;;AAeA,IAAAA,8BAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAMC,oBAAiC,GAAG,IAAIC,GAAG,CAAC,CAAC;AACnD,IAAIC,SAAkD;AAUtD,SAASC,YAAYA,CACnBC,UAAkB,EAClBC,iBAA6B,EAC7BC,YAAe,EACJ;EACX,IAAIC,WAAe;EAEnB,OAAO,YAAM;IACX,IAAIA,WAAW,IAAI,IAAI,EAAE;MAAA,IAAAC,kBAAA;MACvBD,WAAW,IAAAC,kBAAA,GAAGH,iBAAiB,CAAC,CAAC,YAAAG,kBAAA,GAAIF,YAAY;IACnD;IACA,OAAOC,WAAW;EACpB,CAAC;AACH;AAEO,SAASE,0BAA0BA,CAGxCL,UAAa,EACbE,YAA0D,EACJ;EACtD,OAAOH,YAAY,CACjBC,UAAU,EACV,YAAM;IAAA,IAAAM,UAAA,EAAAC,qBAAA;IACJX,oBAAoB,CAACY,GAAG,CAACR,UAAU,CAAC;IACpC,QAAAM,UAAA,GAAOR,SAAS,cAAAS,qBAAA,GAATD,UAAA,CAAYN,UAAU,CAAC,qBAAvBO,qBAAA,CAAAE,IAAA,CAAAH,UAAA,EAA0BJ,YAAY,CAAC;EAChD,CAAC,EACDA,YACF,CAAC;AACH;AAIO,SAASQ,sBAAsBA,CACpCV,UAAa,EACbE,YAA8D,EAEJ;EAAA,IAD1DS,gCAAyC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAEjD,OAAOb,YAAY,CACjBC,UAAU,EACV,YAAM;IAAA,IAAAe,qBAAA;IACJC,oCAAoC,CAAChB,UAAU,CAAC;IAChD,OAAOiB,sCAA6B,aAAAF,qBAAA,GAA7BE,sCAA6B,CAAGjB,UAAU,CAAC,qBAA3Ce,qBAAA,CAAAN,IAAA,CAAAQ,sCAA8C,CAAC;EACxD,CAAC,EACDf,YACF,CAAC;AACH;AAEO,SAASgB,YAAYA,CAAA,EAA4C;EACtE,OAAOpB,SAAS;AAClB;AAEO,SAASqB,YAAYA,CAC1BC,YAAoD,EAC9C;EACN,IAAItB,SAAS,IAAI,IAAI,EAAE;IACrB,MAAM,IAAIuB,KAAK,CAAC,mDAAmD,CAAC;EACtE;EAEA,IAAIzB,oBAAoB,CAAC0B,IAAI,GAAG,CAAC,EAAE;IACjC,IAAMC,uBAAuB,GAAGC,KAAK,CAACC,IAAI,CAAC7B,oBAAoB,CAAC,CAAC8B,IAAI,CAAC,IAAI,CAAC;IAC3E,MAAM,IAAIL,KAAK,CACb,wDAAwDE,uBAAuB,EACjF,CAAC;EACH;EAEAzB,SAAS,GAAGsB,YAAY;AAC1B;AAEA,IAAMO,mBAAgC,GAAG,IAAI9B,GAAG,CAAC,CAAC;AAElD,SAASmB,oCAAoCA,CAAChB,UAAkB,EAAQ;EACtE,IAAI,CAACiB,sCAA6B,IAAI,CAACU,mBAAmB,CAACC,GAAG,CAAC5B,UAAU,CAAC,EAAE;IAC1E2B,mBAAmB,CAACnB,GAAG,CAACR,UAAU,CAAC;IACnC6B,OAAO,CAACC,KAAK,CACX,kCAAkC9B,UAAU,kDAC9C,CAAC;EACH;AACF", "ignoreList": []}