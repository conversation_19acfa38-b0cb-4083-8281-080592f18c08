5777df5a54bec6a2eb5a9471801a1f09
_getJestObj().mock("../../../utils/hapticPatterns", function () {
  return {
    HapticPatterns: {
      buttonPress: jest.fn(),
      lightImpact: jest.fn(),
      warningPress: jest.fn(),
      successPress: jest.fn()
    }
  };
});
_getJestObj().mock("../../../utils/accessibilityUtils", function () {
  return {
    AccessibilityUtils: {
      announceForAccessibility: jest.fn(),
      getAccessibilityLabel: jest.fn(function (label) {
        return label;
      })
    }
  };
});
_getJestObj().mock("../../../utils/responsiveUtils", function () {
  return {
    getResponsiveSpacing: jest.fn(function (spacing) {
      return spacing;
    }),
    getResponsiveFontSize: jest.fn(function (size) {
      return size;
    })
  };
});
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _reactNative = require("@testing-library/react-native");
var _react = _interopRequireDefault(require("react"));
var _ThemeContext = require("../../../contexts/ThemeContext");
var _HyperMinimalistTheme = require("../../../design-system/HyperMinimalistTheme");
var _UnifiedButton = require("../UnifiedButton");
var _jsxRuntime = require("react/jsx-runtime");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var TestWrapper = function TestWrapper(_ref) {
  var children = _ref.children;
  return (0, _jsxRuntime.jsx)(_ThemeContext.ThemeProvider, {
    theme: _HyperMinimalistTheme.HyperMinimalistTheme,
    children: children
  });
};
describe('UnifiedButton Component', function () {
  var defaultProps = {
    children: 'Test Button',
    onPress: jest.fn()
  };
  beforeEach(function () {
    jest.clearAllMocks();
  });
  describe('Basic Rendering', function () {
    it('should render with default props', function () {
      var _render = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps))
        })),
        getByText = _render.getByText,
        getByRole = _render.getByRole;
      expect(getByText('Test Button')).toBeTruthy();
      expect(getByRole('button')).toBeTruthy();
    });
    it('should render with custom children', function () {
      var _render2 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps, {
            children: "Custom Text"
          }))
        })),
        getByText = _render2.getByText;
      expect(getByText('Custom Text')).toBeTruthy();
    });
    it('should have correct accessibility role', function () {
      var _render3 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps))
        })),
        getByRole = _render3.getByRole;
      var button = getByRole('button');
      expect(button).toBeTruthy();
    });
  });
  describe('Button Variants', function () {
    it('should render primary variant by default', function () {
      var _render4 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps))
        })),
        getByRole = _render4.getByRole;
      var button = getByRole('button');
      expect(button).toBeTruthy();
    });
    it('should render secondary variant correctly', function () {
      var _render5 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps, {
            variant: "secondary"
          }))
        })),
        getByRole = _render5.getByRole;
      var button = getByRole('button');
      expect(button).toBeTruthy();
    });
    it('should render outline variant correctly', function () {
      var _render6 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps, {
            variant: "outline"
          }))
        })),
        getByRole = _render6.getByRole;
      var button = getByRole('button');
      expect(button).toBeTruthy();
    });
    it('should render ghost variant correctly', function () {
      var _render7 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps, {
            variant: "ghost"
          }))
        })),
        getByRole = _render7.getByRole;
      var button = getByRole('button');
      expect(button).toBeTruthy();
    });
    it('should render destructive variant correctly', function () {
      var _render8 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps, {
            variant: "destructive"
          }))
        })),
        getByRole = _render8.getByRole;
      var button = getByRole('button');
      expect(button).toBeTruthy();
    });
    it('should render success variant correctly', function () {
      var _render9 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps, {
            variant: "success"
          }))
        })),
        getByRole = _render9.getByRole;
      var button = getByRole('button');
      expect(button).toBeTruthy();
    });
    it('should render minimal variant correctly', function () {
      var _render0 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps, {
            variant: "minimal"
          }))
        })),
        getByRole = _render0.getByRole;
      var button = getByRole('button');
      expect(button).toBeTruthy();
    });
  });
  describe('Button Sizes', function () {
    it('should render small size correctly', function () {
      var _render1 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps, {
            size: "small"
          }))
        })),
        getByRole = _render1.getByRole;
      var button = getByRole('button');
      expect(button).toBeTruthy();
    });
    it('should render medium size by default', function () {
      var _render10 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps))
        })),
        getByRole = _render10.getByRole;
      var button = getByRole('button');
      expect(button).toBeTruthy();
    });
    it('should render large size correctly', function () {
      var _render11 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps, {
            size: "large"
          }))
        })),
        getByRole = _render11.getByRole;
      var button = getByRole('button');
      expect(button).toBeTruthy();
    });
  });
  describe('Button States', function () {
    it('should handle disabled state', function () {
      var onPress = jest.fn();
      var _render12 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps, {
            onPress: onPress,
            disabled: true
          }))
        })),
        getByRole = _render12.getByRole;
      var button = getByRole('button');
      _reactNative.fireEvent.press(button);
      expect(onPress).not.toHaveBeenCalled();
    });
    it('should handle loading state', function () {
      var _button$props$accessi;
      var _render13 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps, {
            loading: true
          }))
        })),
        getByRole = _render13.getByRole;
      var button = getByRole('button');
      expect(button).toBeTruthy();
      expect((_button$props$accessi = button.props.accessibilityState) == null ? void 0 : _button$props$accessi.busy).toBe(true);
    });
    it('should call onPress when pressed', function () {
      var onPress = jest.fn();
      var _render14 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps, {
            onPress: onPress
          }))
        })),
        getByRole = _render14.getByRole;
      var button = getByRole('button');
      _reactNative.fireEvent.press(button);
      expect(onPress).toHaveBeenCalledTimes(1);
    });
    it('should not call onPress when loading', function () {
      var onPress = jest.fn();
      var _render15 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps, {
            onPress: onPress,
            loading: true
          }))
        })),
        getByRole = _render15.getByRole;
      var button = getByRole('button');
      _reactNative.fireEvent.press(button);
      expect(onPress).not.toHaveBeenCalled();
    });
  });
  describe('Icon Support', function () {
    it('should render with left icon', function () {
      var _render16 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps, {
            iconLeft: "add"
          }))
        })),
        getByRole = _render16.getByRole;
      var button = getByRole('button');
      expect(button).toBeTruthy();
    });
    it('should render with right icon', function () {
      var _render17 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps, {
            iconRight: "arrow-forward"
          }))
        })),
        getByRole = _render17.getByRole;
      var button = getByRole('button');
      expect(button).toBeTruthy();
    });
    it('should render icon-only button', function () {
      var _render18 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, {
            onPress: defaultProps.onPress,
            iconLeft: "add"
          })
        })),
        getByRole = _render18.getByRole;
      var button = getByRole('button');
      expect(button).toBeTruthy();
    });
  });
  describe('Accessibility', function () {
    it('should support custom accessibility label', function () {
      var _render19 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps, {
            accessibilityLabel: "Custom Button Label"
          }))
        })),
        getByLabelText = _render19.getByLabelText;
      expect(getByLabelText('Custom Button Label')).toBeTruthy();
    });
    it('should support accessibility hint', function () {
      var _render20 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps, {
            accessibilityHint: "Tap to submit form"
          }))
        })),
        getByRole = _render20.getByRole;
      var button = getByRole('button');
      expect(button.props.accessibilityHint).toBe('Tap to submit form');
    });
    it('should have proper accessibility state for disabled button', function () {
      var _button$props$accessi2;
      var _render21 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps, {
            disabled: true
          }))
        })),
        getByRole = _render21.getByRole;
      var button = getByRole('button');
      expect((_button$props$accessi2 = button.props.accessibilityState) == null ? void 0 : _button$props$accessi2.disabled).toBe(true);
    });
    it('should have proper accessibility state for loading button', function () {
      var _button$props$accessi3;
      var _render22 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps, {
            loading: true
          }))
        })),
        getByRole = _render22.getByRole;
      var button = getByRole('button');
      expect((_button$props$accessi3 = button.props.accessibilityState) == null ? void 0 : _button$props$accessi3.busy).toBe(true);
    });
  });
  describe('Custom Styling', function () {
    it('should apply custom styles', function () {
      var customStyle = {
        marginTop: 20
      };
      var _render23 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps, {
            style: customStyle
          }))
        })),
        getByRole = _render23.getByRole;
      var button = getByRole('button');
      expect(button).toHaveStyle(customStyle);
    });
    it('should apply custom text styles', function () {
      var customTextStyle = {
        fontSize: 18
      };
      var _render24 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_UnifiedButton.UnifiedButton, Object.assign({}, defaultProps, {
            textStyle: customTextStyle
          }))
        })),
        getByText = _render24.getByText;
      var text = getByText('Test Button');
      expect(text).toHaveStyle(customTextStyle);
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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