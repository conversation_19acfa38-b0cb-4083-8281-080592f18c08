/**
 * Service List Screen - Provider Service Management
 *
 * Component Contract:
 * - Displays list of services offered by the service provider
 * - Provides CRUD operations for service management
 * - Supports filtering and searching services
 * - Integrates with backend API for service data
 * - Follows TDD methodology with comprehensive test coverage
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { useNavigation } from '@react-navigation/native';
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  Alert,
} from 'react-native';

import { Box } from '../../components/atoms/Box';
import { Button } from '../../components/atoms/Button';
import { Input } from '../../components/atoms/Input';

// Types
interface Service {
  id: string;
  name: string;
  description: string;
  category: string;
  price: number;
  duration: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export const ServiceListScreen: React.FC = () => {
  const navigation = useNavigation();

  // State
  const [services, setServices] = useState<Service[]>([]);
  const [filteredServices, setFilteredServices] = useState<Service[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Mock data - replace with actual API call
  const mockServices: Service[] = [
    {
      id: '1',
      name: 'Hair Cut & Style',
      description: 'Professional haircut and styling service',
      category: 'Hair',
      price: 45,
      duration: 60,
      is_active: true,
      created_at: '2024-01-15T10:00:00Z',
      updated_at: '2024-01-15T10:00:00Z',
    },
    {
      id: '2',
      name: 'Facial Treatment',
      description: 'Relaxing facial treatment with organic products',
      category: 'Skincare',
      price: 75,
      duration: 90,
      is_active: true,
      created_at: '2024-01-14T14:30:00Z',
      updated_at: '2024-01-14T14:30:00Z',
    },
    {
      id: '3',
      name: 'Manicure',
      description: 'Professional nail care service',
      category: 'Nails',
      price: 25,
      duration: 30,
      is_active: false,
      created_at: '2024-01-13T09:15:00Z',
      updated_at: '2024-01-13T09:15:00Z',
    },
  ];

  // Load services on component mount
  useEffect(() => {
    loadServices();
  }, []);

  // Filter services when search query changes
  useEffect(() => {
    filterServices();
  }, [searchQuery, services]);

  const loadServices = async () => {
    setIsLoading(true);
    try {
      // Import the provider API service
      const { providerApiService } = await import('../../services/providerApi');

      // Fetch real services data from backend
      const servicesResponse = await providerApiService.getServices();

      // Transform backend services to frontend format
      const transformedServices = servicesResponse.services.map(service => ({
        id: service.id,
        name: service.name,
        description: service.description,
        price: service.base_price,
        duration: service.duration,
        category: service.category || 'General',
        isActive: service.is_active,
        bookingsCount: 0, // Will be populated from analytics
        lastBooking: null, // Will be populated from bookings
      }));

      setServices(transformedServices);
    } catch (error) {
      console.error('Failed to load services:', error);
      Alert.alert('Error', 'Failed to load services');
    } finally {
      setIsLoading(false);
    }
  };

  const filterServices = () => {
    let filtered = services;

    if (searchQuery.trim()) {
      filtered = filtered.filter(
        service =>
          service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          service.description
            .toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          service.category.toLowerCase().includes(searchQuery.toLowerCase()),
      );
    }

    setFilteredServices(filtered);
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadServices();
    setIsRefreshing(false);
  };

  const handleAddService = () => {
    navigation.navigate('AddService' as never);
  };

  const handleEditService = (service: Service) => {
    navigation.navigate(
      'EditService' as never,
      { serviceId: service.id } as never,
    );
  };

  const handleToggleService = async (service: Service) => {
    try {
      // Simulate API call to toggle service status
      const updatedServices = services.map(s =>
        s.id === service.id ? { ...s, is_active: !s.is_active } : s,
      );
      setServices(updatedServices);

      Alert.alert(
        'Success',
        `Service ${service.is_active ? 'deactivated' : 'activated'} successfully`,
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to update service status');
    }
  };

  const handleDeleteService = (service: Service) => {
    Alert.alert(
      'Delete Service',
      `Are you sure you want to delete "${service.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // Simulate API call to delete service
              const updatedServices = services.filter(s => s.id !== service.id);
              setServices(updatedServices);
              Alert.alert('Success', 'Service deleted successfully');
            } catch (error) {
              Alert.alert('Error', 'Failed to delete service');
            }
          },
        },
      ],
    );
  };

  const renderServiceItem = ({ item }: { item: Service }) => (
    <TouchableOpacity
      style={[styles.serviceCard, !item.is_active && styles.inactiveCard]}
      onPress={() => handleEditService(item)}
      testID={`service-item-${item.id}`}>
      <View style={styles.serviceHeader}>
        <Text style={styles.serviceName}>{item.name}</Text>
        <View style={styles.serviceActions}>
          <TouchableOpacity
            style={[styles.actionButton, styles.toggleButton]}
            onPress={() => handleToggleService(item)}
            testID={`toggle-service-${item.id}`}>
            <Text style={styles.actionButtonText}>
              {item.is_active ? 'Deactivate' : 'Activate'}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionButton, styles.deleteButton]}
            onPress={() => handleDeleteService(item)}
            testID={`delete-service-${item.id}`}>
            <Text style={styles.actionButtonText}>Delete</Text>
          </TouchableOpacity>
        </View>
      </View>

      <Text style={styles.serviceDescription}>{item.description}</Text>
      <Text style={styles.serviceCategory}>{item.category}</Text>

      <View style={styles.serviceDetails}>
        <Text style={styles.servicePrice}>${item.price}</Text>
        <Text style={styles.serviceDuration}>{item.duration} min</Text>
        <Text
          style={[
            styles.serviceStatus,
            item.is_active ? styles.activeStatus : styles.inactiveStatus,
          ]}>
          {item.is_active ? 'Active' : 'Inactive'}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container} testID="service-list-screen">
      {/* Header */}
      <Box style={styles.header}>
        <Text style={styles.title}>My Services</Text>
        <Button
          testID="add-service-button"
          onPress={handleAddService}
          style={styles.addButton}>
          Add Service
        </Button>
      </Box>

      {/* Search */}
      <Box style={styles.searchContainer}>
        <Input
          testID="search-input"
          placeholder="Search services..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          style={styles.searchInput}
        />
      </Box>

      {/* Services List */}
      <View style={styles.listContainer}>
        {isLoading ? (
          <View style={styles.loadingContainer} testID="loading-indicator">
            <ActivityIndicator size="large" color="#2A4B32" />
            <Text style={styles.loadingText}>Loading services...</Text>
          </View>
        ) : (
          <FlatList
            data={filteredServices}
            renderItem={renderServiceItem}
            keyExtractor={item => item.id}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.servicesList}
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            testID="services-list"
            ListEmptyComponent={
              <View style={styles.emptyContainer} testID="empty-state">
                <Text style={styles.emptyText}>No services found</Text>
                <Text style={styles.emptySubtext}>
                  {searchQuery
                    ? 'Try adjusting your search'
                    : 'Add your first service to get started'}
                </Text>
              </View>
            }
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  addButton: {
    minWidth: 120,
  },
  searchContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  searchInput: {
    marginBottom: 0,
  },
  listContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
  servicesList: {
    padding: 16,
  },
  serviceCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  inactiveCard: {
    opacity: 0.6,
    backgroundColor: '#F9FAFB',
  },
  serviceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  serviceName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    flex: 1,
    marginRight: 12,
  },
  serviceActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    minWidth: 70,
    alignItems: 'center',
  },
  toggleButton: {
    backgroundColor: '#2A4B32',
  },
  deleteButton: {
    backgroundColor: '#EF4444',
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
  },
  serviceDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 8,
  },
  serviceCategory: {
    fontSize: 12,
    color: '#2A4B32',
    fontWeight: '500',
    marginBottom: 12,
  },
  serviceDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  servicePrice: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  serviceDuration: {
    fontSize: 14,
    color: '#6B7280',
  },
  serviceStatus: {
    fontSize: 12,
    fontWeight: '500',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  activeStatus: {
    backgroundColor: '#D1FAE5',
    color: '#065F46',
  },
  inactiveStatus: {
    backgroundColor: '#FEE2E2',
    color: '#991B1B',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    minHeight: 200,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
  },
});
