c004cd7b29bcbd4c1ad1491b60223f1d
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useProvidersStore = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _zustand = require("zustand");
var _middleware = require("zustand/middleware");
var _testAccountsService = require("../services/testAccountsService");
var convertTestAccountToProvider = function convertTestAccountToProvider(account, index) {
  return {
    id: `provider_${index + 1}`,
    business_name: account.businessName || `${account.firstName} ${account.lastName}`,
    description: account.description || 'Professional beauty services',
    rating: 4.2 + Math.random() * 0.8,
    review_count: Math.floor(Math.random() * 50) + 10,
    is_active: true,
    is_verified: Math.random() > 0.3,
    city: account.city || 'Ottawa',
    state: 'ON',
    distance: `${(Math.random() * 10 + 1).toFixed(1)} km`,
    avatar: null,
    services: [account.category || 'Beauty Services'],
    price_range: {
      min: 30 + Math.floor(Math.random() * 20),
      max: 80 + Math.floor(Math.random() * 120)
    },
    category: account.category,
    email: account.email,
    firstName: account.firstName,
    lastName: account.lastName
  };
};
var useProvidersStore = exports.useProvidersStore = (0, _zustand.create)()((0, _middleware.devtools)(function (set, get) {
  return {
    providers: [],
    loading: false,
    error: null,
    filters: {},
    fetchProviders: function () {
      var _fetchProviders = (0, _asyncToGenerator2.default)(function* () {
        set({
          loading: true,
          error: null
        });
        try {
          var testAccounts = _testAccountsService.testAccountsService.getAccountsByRole('service_provider');
          var providers = testAccounts.map(function (account, index) {
            return convertTestAccountToProvider(account, index);
          });
          yield new Promise(function (resolve) {
            return setTimeout(resolve, 800);
          });
          set({
            providers: providers,
            loading: false
          });
        } catch (error) {
          set({
            error: error,
            loading: false,
            providers: []
          });
        }
      });
      function fetchProviders() {
        return _fetchProviders.apply(this, arguments);
      }
      return fetchProviders;
    }(),
    searchProviders: function () {
      var _searchProviders = (0, _asyncToGenerator2.default)(function* (filters) {
        set({
          loading: true,
          error: null,
          filters: filters
        });
        try {
          var testAccounts = _testAccountsService.testAccountsService.getAccountsByRole('service_provider');
          if (filters.category && filters.category !== 'all') {
            testAccounts = _testAccountsService.testAccountsService.getProvidersByCategory(filters.category);
          }
          if (filters.location) {
            testAccounts = testAccounts.filter(function (account) {
              var _account$city;
              return (_account$city = account.city) == null ? void 0 : _account$city.toLowerCase().includes(filters.location.toLowerCase());
            });
          }
          var providers = testAccounts.map(function (account, index) {
            return convertTestAccountToProvider(account, index);
          });
          if (filters.rating_min) {
            providers = providers.filter(function (provider) {
              return provider.rating >= filters.rating_min;
            });
          }
          if (filters.is_verified !== undefined) {
            providers = providers.filter(function (provider) {
              return provider.is_verified === filters.is_verified;
            });
          }
          yield new Promise(function (resolve) {
            return setTimeout(resolve, 600);
          });
          set({
            providers: providers,
            loading: false
          });
        } catch (error) {
          set({
            error: error,
            loading: false,
            providers: []
          });
        }
      });
      function searchProviders(_x) {
        return _searchProviders.apply(this, arguments);
      }
      return searchProviders;
    }(),
    getProviderById: function () {
      var _getProviderById = (0, _asyncToGenerator2.default)(function* (providerId) {
        console.log('🔍 providersSlice.getProviderById called with:', providerId);
        set({
          loading: true,
          error: null
        });
        try {
          var _get = get(),
            providers = _get.providers;
          console.log('🔍 providersSlice: providers type:', typeof providers);
          console.log('🔍 providersSlice: providers length:', providers == null ? void 0 : providers.length);
          var provider = providers == null ? void 0 : providers.find(function (p) {
            return p.id === providerId;
          });
          if (!provider) {
            var testAccounts = _testAccountsService.testAccountsService.getAllTestAccounts();
            var testAccount = testAccounts.find(function (account) {
              var _account$businessName;
              return account.email.includes(providerId) || ((_account$businessName = account.businessName) == null ? void 0 : _account$businessName.includes(providerId));
            });
            if (testAccount) {
              provider = convertTestAccountToProvider(testAccount, 0);
            }
          }
          yield new Promise(function (resolve) {
            return setTimeout(resolve, 300);
          });
          set({
            loading: false
          });
          return provider || null;
        } catch (error) {
          console.error('❌ providersSlice.getProviderById error:', error);
          set({
            error: error,
            loading: false
          });
          return null;
        }
      });
      function getProviderById(_x2) {
        return _getProviderById.apply(this, arguments);
      }
      return getProviderById;
    }(),
    updateFilters: function updateFilters(newFilters) {
      var _get2 = get(),
        filters = _get2.filters;
      set({
        filters: Object.assign({}, filters, newFilters)
      });
    },
    clearFilters: function clearFilters() {
      set({
        filters: {}
      });
    }
  };
}, {
  name: 'providers-store'
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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