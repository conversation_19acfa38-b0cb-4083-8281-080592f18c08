c4046e897c611ba181079dfba1564cf8
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
exports.flushValue = flushValue;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _classPrivateFieldLooseBase2 = _interopRequireDefault(require("@babel/runtime/helpers/classPrivateFieldLooseBase"));
var _classPrivateFieldLooseKey2 = _interopRequireDefault(require("@babel/runtime/helpers/classPrivateFieldLooseKey"));
var _NativeAnimatedHelper = _interopRequireDefault(require("../../../src/private/animated/NativeAnimatedHelper"));
var _InteractionManager = _interopRequireDefault(require("../../Interaction/InteractionManager"));
var _AnimatedInterpolation = _interopRequireDefault(require("./AnimatedInterpolation"));
var _AnimatedWithChildren2 = _interopRequireDefault(require("./AnimatedWithChildren"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var NativeAnimatedAPI = _NativeAnimatedHelper.default.API;
function flushValue(rootNode) {
  var leaves = new Set();
  function findAnimatedStyles(node) {
    if (typeof node.update === 'function') {
      leaves.add(node);
    } else {
      node.__getChildren().forEach(findAnimatedStyles);
    }
  }
  findAnimatedStyles(rootNode);
  leaves.forEach(function (leaf) {
    return leaf.update();
  });
}
function _executeAsAnimatedBatch(id, operation) {
  NativeAnimatedAPI.setWaitingForIdentifier(id);
  operation();
  NativeAnimatedAPI.unsetWaitingForIdentifier(id);
}
var _listenerCount = (0, _classPrivateFieldLooseKey2.default)("listenerCount");
var _updateSubscription = (0, _classPrivateFieldLooseKey2.default)("updateSubscription");
var _ensureUpdateSubscriptionExists = (0, _classPrivateFieldLooseKey2.default)("ensureUpdateSubscriptionExists");
var AnimatedValue = exports.default = function (_AnimatedWithChildren) {
  function AnimatedValue(value, config) {
    var _this;
    (0, _classCallCheck2.default)(this, AnimatedValue);
    _this = _callSuper(this, AnimatedValue, [config]);
    Object.defineProperty(_this, _ensureUpdateSubscriptionExists, {
      value: _ensureUpdateSubscriptionExists2
    });
    Object.defineProperty(_this, _listenerCount, {
      writable: true,
      value: 0
    });
    Object.defineProperty(_this, _updateSubscription, {
      writable: true,
      value: null
    });
    if (typeof value !== 'number') {
      throw new Error('AnimatedValue: Attempting to set value to undefined');
    }
    _this._startingValue = _this._value = value;
    _this._offset = 0;
    _this._animation = null;
    if (config && config.useNativeDriver) {
      _this.__makeNative();
    }
    return _this;
  }
  (0, _inherits2.default)(AnimatedValue, _AnimatedWithChildren);
  return (0, _createClass2.default)(AnimatedValue, [{
    key: "__detach",
    value: function __detach() {
      var _this2 = this;
      if (this.__isNative) {
        NativeAnimatedAPI.getValue(this.__getNativeTag(), function (value) {
          _this2._value = value - _this2._offset;
        });
      }
      this.stopAnimation();
      _superPropGet(AnimatedValue, "__detach", this, 3)([]);
    }
  }, {
    key: "__getValue",
    value: function __getValue() {
      return this._value + this._offset;
    }
  }, {
    key: "__makeNative",
    value: function __makeNative(platformConfig) {
      _superPropGet(AnimatedValue, "__makeNative", this, 3)([platformConfig]);
      if ((0, _classPrivateFieldLooseBase2.default)(this, _listenerCount)[_listenerCount] > 0) {
        (0, _classPrivateFieldLooseBase2.default)(this, _ensureUpdateSubscriptionExists)[_ensureUpdateSubscriptionExists]();
      }
    }
  }, {
    key: "addListener",
    value: function addListener(callback) {
      var id = _superPropGet(AnimatedValue, "addListener", this, 3)([callback]);
      (0, _classPrivateFieldLooseBase2.default)(this, _listenerCount)[_listenerCount]++;
      if (this.__isNative) {
        (0, _classPrivateFieldLooseBase2.default)(this, _ensureUpdateSubscriptionExists)[_ensureUpdateSubscriptionExists]();
      }
      return id;
    }
  }, {
    key: "removeListener",
    value: function removeListener(id) {
      _superPropGet(AnimatedValue, "removeListener", this, 3)([id]);
      (0, _classPrivateFieldLooseBase2.default)(this, _listenerCount)[_listenerCount]--;
      if (this.__isNative && (0, _classPrivateFieldLooseBase2.default)(this, _listenerCount)[_listenerCount] === 0) {
        var _classPrivateFieldLoo;
        (_classPrivateFieldLoo = (0, _classPrivateFieldLooseBase2.default)(this, _updateSubscription)[_updateSubscription]) == null || _classPrivateFieldLoo.remove();
      }
    }
  }, {
    key: "removeAllListeners",
    value: function removeAllListeners() {
      _superPropGet(AnimatedValue, "removeAllListeners", this, 3)([]);
      (0, _classPrivateFieldLooseBase2.default)(this, _listenerCount)[_listenerCount] = 0;
      if (this.__isNative) {
        var _classPrivateFieldLoo2;
        (_classPrivateFieldLoo2 = (0, _classPrivateFieldLooseBase2.default)(this, _updateSubscription)[_updateSubscription]) == null || _classPrivateFieldLoo2.remove();
      }
    }
  }, {
    key: "setValue",
    value: function setValue(value) {
      var _this3 = this;
      if (this._animation) {
        this._animation.stop();
        this._animation = null;
      }
      this._updateValue(value, !this.__isNative);
      if (this.__isNative) {
        _executeAsAnimatedBatch(this.__getNativeTag().toString(), function () {
          return NativeAnimatedAPI.setAnimatedNodeValue(_this3.__getNativeTag(), value);
        });
      }
    }
  }, {
    key: "setOffset",
    value: function setOffset(offset) {
      this._offset = offset;
      if (this.__isNative) {
        NativeAnimatedAPI.setAnimatedNodeOffset(this.__getNativeTag(), offset);
      }
    }
  }, {
    key: "flattenOffset",
    value: function flattenOffset() {
      this._value += this._offset;
      this._offset = 0;
      if (this.__isNative) {
        NativeAnimatedAPI.flattenAnimatedNodeOffset(this.__getNativeTag());
      }
    }
  }, {
    key: "extractOffset",
    value: function extractOffset() {
      this._offset += this._value;
      this._value = 0;
      if (this.__isNative) {
        NativeAnimatedAPI.extractAnimatedNodeOffset(this.__getNativeTag());
      }
    }
  }, {
    key: "stopAnimation",
    value: function stopAnimation(callback) {
      this.stopTracking();
      this._animation && this._animation.stop();
      this._animation = null;
      if (callback) {
        if (this.__isNative) {
          NativeAnimatedAPI.getValue(this.__getNativeTag(), callback);
        } else {
          callback(this.__getValue());
        }
      }
    }
  }, {
    key: "resetAnimation",
    value: function resetAnimation(callback) {
      this.stopAnimation(callback);
      this._value = this._startingValue;
      if (this.__isNative) {
        NativeAnimatedAPI.setAnimatedNodeValue(this.__getNativeTag(), this._startingValue);
      }
    }
  }, {
    key: "__onAnimatedValueUpdateReceived",
    value: function __onAnimatedValueUpdateReceived(value) {
      this._updateValue(value, false);
    }
  }, {
    key: "interpolate",
    value: function interpolate(config) {
      return new _AnimatedInterpolation.default(this, config);
    }
  }, {
    key: "animate",
    value: function animate(animation, callback) {
      var _this4 = this;
      var handle = null;
      if (animation.__isInteraction) {
        handle = _InteractionManager.default.createInteractionHandle();
      }
      var previousAnimation = this._animation;
      this._animation && this._animation.stop();
      this._animation = animation;
      animation.start(this._value, function (value) {
        _this4._updateValue(value, true);
      }, function (result) {
        _this4._animation = null;
        if (handle !== null) {
          _InteractionManager.default.clearInteractionHandle(handle);
        }
        callback && callback(result);
      }, previousAnimation, this);
    }
  }, {
    key: "stopTracking",
    value: function stopTracking() {
      this._tracking && this._tracking.__detach();
      this._tracking = null;
    }
  }, {
    key: "track",
    value: function track(tracking) {
      this.stopTracking();
      this._tracking = tracking;
      this._tracking && this._tracking.update();
    }
  }, {
    key: "_updateValue",
    value: function _updateValue(value, flush) {
      if (value === undefined) {
        throw new Error('AnimatedValue: Attempting to set value to undefined');
      }
      this._value = value;
      if (flush) {
        flushValue(this);
      }
      this.__callListeners(this.__getValue());
    }
  }, {
    key: "__getNativeConfig",
    value: function __getNativeConfig() {
      return {
        type: 'value',
        value: this._value,
        offset: this._offset,
        debugID: this.__getDebugID()
      };
    }
  }]);
}(_AnimatedWithChildren2.default);
function _ensureUpdateSubscriptionExists2() {
  var _this5 = this;
  if ((0, _classPrivateFieldLooseBase2.default)(this, _updateSubscription)[_updateSubscription] != null) {
    return;
  }
  var nativeTag = this.__getNativeTag();
  NativeAnimatedAPI.startListeningToAnimatedNodeValue(nativeTag);
  var subscription = _NativeAnimatedHelper.default.nativeEventEmitter.addListener('onAnimatedValueUpdate', function (data) {
    if (data.tag === nativeTag) {
      _this5.__onAnimatedValueUpdateReceived(data.value);
    }
  });
  (0, _classPrivateFieldLooseBase2.default)(this, _updateSubscription)[_updateSubscription] = {
    remove: function remove() {
      if ((0, _classPrivateFieldLooseBase2.default)(_this5, _updateSubscription)[_updateSubscription] == null) {
        return;
      }
      (0, _classPrivateFieldLooseBase2.default)(_this5, _updateSubscription)[_updateSubscription] = null;
      subscription.remove();
      NativeAnimatedAPI.stopListeningToAnimatedNodeValue(nativeTag);
    }
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfTmF0aXZlQW5pbWF0ZWRIZWxwZXIiLCJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0IiwicmVxdWlyZSIsIl9JbnRlcmFjdGlvbk1hbmFnZXIiLCJfQW5pbWF0ZWRJbnRlcnBvbGF0aW9uIiwiX0FuaW1hdGVkV2l0aENoaWxkcmVuMiIsIl9jYWxsU3VwZXIiLCJ0IiwibyIsImUiLCJfZ2V0UHJvdG90eXBlT2YyIiwiZGVmYXVsdCIsIl9wb3NzaWJsZUNvbnN0cnVjdG9yUmV0dXJuMiIsIl9pc05hdGl2ZVJlZmxlY3RDb25zdHJ1Y3QiLCJSZWZsZWN0IiwiY29uc3RydWN0IiwiY29uc3RydWN0b3IiLCJhcHBseSIsIkJvb2xlYW4iLCJwcm90b3R5cGUiLCJ2YWx1ZU9mIiwiY2FsbCIsIl9zdXBlclByb3BHZXQiLCJyIiwicCIsIl9nZXQyIiwiTmF0aXZlQW5pbWF0ZWRBUEkiLCJOYXRpdmVBbmltYXRlZEhlbHBlciIsIkFQSSIsImZsdXNoVmFsdWUiLCJyb290Tm9kZSIsImxlYXZlcyIsIlNldCIsImZpbmRBbmltYXRlZFN0eWxlcyIsIm5vZGUiLCJ1cGRhdGUiLCJhZGQiLCJfX2dldENoaWxkcmVuIiwiZm9yRWFjaCIsImxlYWYiLCJfZXhlY3V0ZUFzQW5pbWF0ZWRCYXRjaCIsImlkIiwib3BlcmF0aW9uIiwic2V0V2FpdGluZ0ZvcklkZW50aWZpZXIiLCJ1bnNldFdhaXRpbmdGb3JJZGVudGlmaWVyIiwiX2xpc3RlbmVyQ291bnQiLCJfY2xhc3NQcml2YXRlRmllbGRMb29zZUtleTIiLCJfdXBkYXRlU3Vic2NyaXB0aW9uIiwiX2Vuc3VyZVVwZGF0ZVN1YnNjcmlwdGlvbkV4aXN0cyIsIkFuaW1hdGVkVmFsdWUiLCJleHBvcnRzIiwiX0FuaW1hdGVkV2l0aENoaWxkcmVuIiwidmFsdWUiLCJjb25maWciLCJfdGhpcyIsIl9jbGFzc0NhbGxDaGVjazIiLCJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsIl9lbnN1cmVVcGRhdGVTdWJzY3JpcHRpb25FeGlzdHMyIiwid3JpdGFibGUiLCJFcnJvciIsIl9zdGFydGluZ1ZhbHVlIiwiX3ZhbHVlIiwiX29mZnNldCIsIl9hbmltYXRpb24iLCJ1c2VOYXRpdmVEcml2ZXIiLCJfX21ha2VOYXRpdmUiLCJfaW5oZXJpdHMyIiwiX2NyZWF0ZUNsYXNzMiIsImtleSIsIl9fZGV0YWNoIiwiX3RoaXMyIiwiX19pc05hdGl2ZSIsImdldFZhbHVlIiwiX19nZXROYXRpdmVUYWciLCJzdG9wQW5pbWF0aW9uIiwiX19nZXRWYWx1ZSIsInBsYXRmb3JtQ29uZmlnIiwiX2NsYXNzUHJpdmF0ZUZpZWxkTG9vc2VCYXNlMiIsImFkZExpc3RlbmVyIiwiY2FsbGJhY2siLCJyZW1vdmVMaXN0ZW5lciIsIl9jbGFzc1ByaXZhdGVGaWVsZExvbyIsInJlbW92ZSIsInJlbW92ZUFsbExpc3RlbmVycyIsIl9jbGFzc1ByaXZhdGVGaWVsZExvbzIiLCJzZXRWYWx1ZSIsIl90aGlzMyIsInN0b3AiLCJfdXBkYXRlVmFsdWUiLCJ0b1N0cmluZyIsInNldEFuaW1hdGVkTm9kZVZhbHVlIiwic2V0T2Zmc2V0Iiwib2Zmc2V0Iiwic2V0QW5pbWF0ZWROb2RlT2Zmc2V0IiwiZmxhdHRlbk9mZnNldCIsImZsYXR0ZW5BbmltYXRlZE5vZGVPZmZzZXQiLCJleHRyYWN0T2Zmc2V0IiwiZXh0cmFjdEFuaW1hdGVkTm9kZU9mZnNldCIsInN0b3BUcmFja2luZyIsInJlc2V0QW5pbWF0aW9uIiwiX19vbkFuaW1hdGVkVmFsdWVVcGRhdGVSZWNlaXZlZCIsImludGVycG9sYXRlIiwiQW5pbWF0ZWRJbnRlcnBvbGF0aW9uIiwiYW5pbWF0ZSIsImFuaW1hdGlvbiIsIl90aGlzNCIsImhhbmRsZSIsIl9faXNJbnRlcmFjdGlvbiIsIkludGVyYWN0aW9uTWFuYWdlciIsImNyZWF0ZUludGVyYWN0aW9uSGFuZGxlIiwicHJldmlvdXNBbmltYXRpb24iLCJzdGFydCIsInJlc3VsdCIsImNsZWFySW50ZXJhY3Rpb25IYW5kbGUiLCJfdHJhY2tpbmciLCJ0cmFjayIsInRyYWNraW5nIiwiZmx1c2giLCJ1bmRlZmluZWQiLCJfX2NhbGxMaXN0ZW5lcnMiLCJfX2dldE5hdGl2ZUNvbmZpZyIsInR5cGUiLCJkZWJ1Z0lEIiwiX19nZXREZWJ1Z0lEIiwiQW5pbWF0ZWRXaXRoQ2hpbGRyZW4iLCJfdGhpczUiLCJuYXRpdmVUYWciLCJzdGFydExpc3RlbmluZ1RvQW5pbWF0ZWROb2RlVmFsdWUiLCJzdWJzY3JpcHRpb24iLCJuYXRpdmVFdmVudEVtaXR0ZXIiLCJkYXRhIiwidGFnIiwic3RvcExpc3RlbmluZ1RvQW5pbWF0ZWROb2RlVmFsdWUiXSwic291cmNlcyI6WyJBbmltYXRlZFZhbHVlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqXG4gKiBAZmxvd1xuICogQGZvcm1hdFxuICovXG5cbmltcG9ydCB0eXBlIHtFdmVudFN1YnNjcmlwdGlvbn0gZnJvbSAnLi4vLi4vdmVuZG9yL2VtaXR0ZXIvRXZlbnRFbWl0dGVyJztcbmltcG9ydCB0eXBlIHtQbGF0Zm9ybUNvbmZpZ30gZnJvbSAnLi4vQW5pbWF0ZWRQbGF0Zm9ybUNvbmZpZyc7XG5pbXBvcnQgdHlwZSBBbmltYXRpb24sIHtFbmRDYWxsYmFja30gZnJvbSAnLi4vYW5pbWF0aW9ucy9BbmltYXRpb24nO1xuaW1wb3J0IHR5cGUge0ludGVycG9sYXRpb25Db25maWdUeXBlfSBmcm9tICcuL0FuaW1hdGVkSW50ZXJwb2xhdGlvbic7XG5pbXBvcnQgdHlwZSBBbmltYXRlZE5vZGUgZnJvbSAnLi9BbmltYXRlZE5vZGUnO1xuaW1wb3J0IHR5cGUge0FuaW1hdGVkTm9kZUNvbmZpZ30gZnJvbSAnLi9BbmltYXRlZE5vZGUnO1xuaW1wb3J0IHR5cGUgQW5pbWF0ZWRUcmFja2luZyBmcm9tICcuL0FuaW1hdGVkVHJhY2tpbmcnO1xuXG5pbXBvcnQgTmF0aXZlQW5pbWF0ZWRIZWxwZXIgZnJvbSAnLi4vLi4vLi4vc3JjL3ByaXZhdGUvYW5pbWF0ZWQvTmF0aXZlQW5pbWF0ZWRIZWxwZXInO1xuaW1wb3J0IEludGVyYWN0aW9uTWFuYWdlciBmcm9tICcuLi8uLi9JbnRlcmFjdGlvbi9JbnRlcmFjdGlvbk1hbmFnZXInO1xuaW1wb3J0IEFuaW1hdGVkSW50ZXJwb2xhdGlvbiBmcm9tICcuL0FuaW1hdGVkSW50ZXJwb2xhdGlvbic7XG5pbXBvcnQgQW5pbWF0ZWRXaXRoQ2hpbGRyZW4gZnJvbSAnLi9BbmltYXRlZFdpdGhDaGlsZHJlbic7XG5cbmV4cG9ydCB0eXBlIEFuaW1hdGVkVmFsdWVDb25maWcgPSAkUmVhZE9ubHk8e1xuICAuLi5BbmltYXRlZE5vZGVDb25maWcsXG4gIHVzZU5hdGl2ZURyaXZlcjogYm9vbGVhbixcbn0+O1xuXG5jb25zdCBOYXRpdmVBbmltYXRlZEFQSSA9IE5hdGl2ZUFuaW1hdGVkSGVscGVyLkFQSTtcblxuLyoqXG4gKiBBbmltYXRlZCB3b3JrcyBieSBidWlsZGluZyBhIGRpcmVjdGVkIGFjeWNsaWMgZ3JhcGggb2YgZGVwZW5kZW5jaWVzXG4gKiB0cmFuc3BhcmVudGx5IHdoZW4geW91IHJlbmRlciB5b3VyIEFuaW1hdGVkIGNvbXBvbmVudHMuXG4gKlxuICogICAgICAgICAgICAgICBuZXcgQW5pbWF0ZWQuVmFsdWUoMClcbiAqICAgICAuaW50ZXJwb2xhdGUoKSAgICAgICAgLmludGVycG9sYXRlKCkgICAgbmV3IEFuaW1hdGVkLlZhbHVlKDEpXG4gKiAgICAgICAgIG9wYWNpdHkgICAgICAgICAgICAgICB0cmFuc2xhdGVZICAgICAgc2NhbGVcbiAqICAgICAgICAgIHN0eWxlICAgICAgICAgICAgICAgICAgICAgICAgIHRyYW5zZm9ybVxuICogICAgICAgICBWaWV3IzIzNCAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZVxuICogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFZpZXcjMTIzXG4gKlxuICogQSkgVG9wIERvd24gcGhhc2VcbiAqIFdoZW4gYW4gQW5pbWF0ZWQuVmFsdWUgaXMgdXBkYXRlZCwgd2UgcmVjdXJzaXZlbHkgZ28gZG93biB0aHJvdWdoIHRoaXNcbiAqIGdyYXBoIGluIG9yZGVyIHRvIGZpbmQgbGVhZiBub2RlczogdGhlIHZpZXdzIHRoYXQgd2UgZmxhZyBhcyBuZWVkaW5nXG4gKiBhbiB1cGRhdGUuXG4gKlxuICogQikgQm90dG9tIFVwIHBoYXNlXG4gKiBXaGVuIGEgdmlldyBpcyBmbGFnZ2VkIGFzIG5lZWRpbmcgYW4gdXBkYXRlLCB3ZSByZWN1cnNpdmVseSBnbyBiYWNrIHVwXG4gKiBpbiBvcmRlciB0byBidWlsZCB0aGUgbmV3IHZhbHVlIHRoYXQgaXQgbmVlZHMuIFRoZSByZWFzb24gd2h5IHdlIG5lZWRcbiAqIHRoaXMgdHdvLXBoYXNlcyBwcm9jZXNzIGlzIHRvIGRlYWwgd2l0aCBjb21wb3NpdGUgcHJvcHMgc3VjaCBhc1xuICogdHJhbnNmb3JtIHdoaWNoIGNhbiByZWNlaXZlIHZhbHVlcyBmcm9tIG11bHRpcGxlIHBhcmVudHMuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBmbHVzaFZhbHVlKHJvb3ROb2RlOiBBbmltYXRlZE5vZGUpOiB2b2lkIHtcbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIGZ1bmMtY2FsbC1zcGFjaW5nXG4gIGNvbnN0IGxlYXZlcyA9IG5ldyBTZXQ8e3VwZGF0ZTogKCkgPT4gdm9pZCwgLi4ufT4oKTtcbiAgZnVuY3Rpb24gZmluZEFuaW1hdGVkU3R5bGVzKG5vZGU6IEFuaW1hdGVkTm9kZSkge1xuICAgIC8vICRGbG93Rml4TWVbcHJvcC1taXNzaW5nXVxuICAgIGlmICh0eXBlb2Ygbm9kZS51cGRhdGUgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgIGxlYXZlcy5hZGQoKG5vZGU6IGFueSkpO1xuICAgIH0gZWxzZSB7XG4gICAgICBub2RlLl9fZ2V0Q2hpbGRyZW4oKS5mb3JFYWNoKGZpbmRBbmltYXRlZFN0eWxlcyk7XG4gICAgfVxuICB9XG4gIGZpbmRBbmltYXRlZFN0eWxlcyhyb290Tm9kZSk7XG4gIGxlYXZlcy5mb3JFYWNoKGxlYWYgPT4gbGVhZi51cGRhdGUoKSk7XG59XG5cbi8qKlxuICogU29tZSBvcGVyYXRpb25zIGFyZSBleGVjdXRlZCBvbmx5IG9uIGJhdGNoIGVuZCwgd2hpY2ggaXMgX21vc3RseV8gc2NoZWR1bGVkIHdoZW5cbiAqIEFuaW1hdGVkIGNvbXBvbmVudCBwcm9wcyBjaGFuZ2UuIEZvciBzb21lIG9mIHRoZSBjaGFuZ2VzIHdoaWNoIHJlcXVpcmUgaW1tZWRpYXRlIGV4ZWN1dGlvblxuICogKGUuZy4gc2V0VmFsdWUpLCB3ZSBjcmVhdGUgYSBzZXBhcmF0ZSBiYXRjaCBpbiBjYXNlIG5vbmUgaXMgc2NoZWR1bGVkLlxuICovXG5mdW5jdGlvbiBfZXhlY3V0ZUFzQW5pbWF0ZWRCYXRjaChpZDogc3RyaW5nLCBvcGVyYXRpb246ICgpID0+IHZvaWQpIHtcbiAgTmF0aXZlQW5pbWF0ZWRBUEkuc2V0V2FpdGluZ0ZvcklkZW50aWZpZXIoaWQpO1xuICBvcGVyYXRpb24oKTtcbiAgTmF0aXZlQW5pbWF0ZWRBUEkudW5zZXRXYWl0aW5nRm9ySWRlbnRpZmllcihpZCk7XG59XG5cbi8qKlxuICogU3RhbmRhcmQgdmFsdWUgZm9yIGRyaXZpbmcgYW5pbWF0aW9ucy4gIE9uZSBgQW5pbWF0ZWQuVmFsdWVgIGNhbiBkcml2ZVxuICogbXVsdGlwbGUgcHJvcGVydGllcyBpbiBhIHN5bmNocm9uaXplZCBmYXNoaW9uLCBidXQgY2FuIG9ubHkgYmUgZHJpdmVuIGJ5IG9uZVxuICogbWVjaGFuaXNtIGF0IGEgdGltZS4gIFVzaW5nIGEgbmV3IG1lY2hhbmlzbSAoZS5nLiBzdGFydGluZyBhIG5ldyBhbmltYXRpb24sXG4gKiBvciBjYWxsaW5nIGBzZXRWYWx1ZWApIHdpbGwgc3RvcCBhbnkgcHJldmlvdXMgb25lcy5cbiAqXG4gKiBTZWUgaHR0cHM6Ly9yZWFjdG5hdGl2ZS5kZXYvZG9jcy9hbmltYXRlZHZhbHVlXG4gKi9cbmV4cG9ydCBkZWZhdWx0IGNsYXNzIEFuaW1hdGVkVmFsdWUgZXh0ZW5kcyBBbmltYXRlZFdpdGhDaGlsZHJlbiB7XG4gICNsaXN0ZW5lckNvdW50OiBudW1iZXIgPSAwO1xuICAjdXBkYXRlU3Vic2NyaXB0aW9uOiA/RXZlbnRTdWJzY3JpcHRpb24gPSBudWxsO1xuXG4gIF92YWx1ZTogbnVtYmVyO1xuICBfc3RhcnRpbmdWYWx1ZTogbnVtYmVyO1xuICBfb2Zmc2V0OiBudW1iZXI7XG4gIF9hbmltYXRpb246ID9BbmltYXRpb247XG4gIF90cmFja2luZzogP0FuaW1hdGVkVHJhY2tpbmc7XG5cbiAgY29uc3RydWN0b3IodmFsdWU6IG51bWJlciwgY29uZmlnPzogP0FuaW1hdGVkVmFsdWVDb25maWcpIHtcbiAgICBzdXBlcihjb25maWcpO1xuICAgIGlmICh0eXBlb2YgdmFsdWUgIT09ICdudW1iZXInKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0FuaW1hdGVkVmFsdWU6IEF0dGVtcHRpbmcgdG8gc2V0IHZhbHVlIHRvIHVuZGVmaW5lZCcpO1xuICAgIH1cbiAgICB0aGlzLl9zdGFydGluZ1ZhbHVlID0gdGhpcy5fdmFsdWUgPSB2YWx1ZTtcbiAgICB0aGlzLl9vZmZzZXQgPSAwO1xuICAgIHRoaXMuX2FuaW1hdGlvbiA9IG51bGw7XG4gICAgaWYgKGNvbmZpZyAmJiBjb25maWcudXNlTmF0aXZlRHJpdmVyKSB7XG4gICAgICB0aGlzLl9fbWFrZU5hdGl2ZSgpO1xuICAgIH1cbiAgfVxuXG4gIF9fZGV0YWNoKCkge1xuICAgIGlmICh0aGlzLl9faXNOYXRpdmUpIHtcbiAgICAgIE5hdGl2ZUFuaW1hdGVkQVBJLmdldFZhbHVlKHRoaXMuX19nZXROYXRpdmVUYWcoKSwgdmFsdWUgPT4ge1xuICAgICAgICB0aGlzLl92YWx1ZSA9IHZhbHVlIC0gdGhpcy5fb2Zmc2V0O1xuICAgICAgfSk7XG4gICAgfVxuICAgIHRoaXMuc3RvcEFuaW1hdGlvbigpO1xuICAgIHN1cGVyLl9fZGV0YWNoKCk7XG4gIH1cblxuICBfX2dldFZhbHVlKCk6IG51bWJlciB7XG4gICAgcmV0dXJuIHRoaXMuX3ZhbHVlICsgdGhpcy5fb2Zmc2V0O1xuICB9XG5cbiAgX19tYWtlTmF0aXZlKHBsYXRmb3JtQ29uZmlnOiA/UGxhdGZvcm1Db25maWcpOiB2b2lkIHtcbiAgICBzdXBlci5fX21ha2VOYXRpdmUocGxhdGZvcm1Db25maWcpO1xuICAgIGlmICh0aGlzLiNsaXN0ZW5lckNvdW50ID4gMCkge1xuICAgICAgdGhpcy4jZW5zdXJlVXBkYXRlU3Vic2NyaXB0aW9uRXhpc3RzKCk7XG4gICAgfVxuICB9XG5cbiAgYWRkTGlzdGVuZXIoY2FsbGJhY2s6ICh2YWx1ZTogYW55KSA9PiBtaXhlZCk6IHN0cmluZyB7XG4gICAgY29uc3QgaWQgPSBzdXBlci5hZGRMaXN0ZW5lcihjYWxsYmFjayk7XG4gICAgdGhpcy4jbGlzdGVuZXJDb3VudCsrO1xuICAgIGlmICh0aGlzLl9faXNOYXRpdmUpIHtcbiAgICAgIHRoaXMuI2Vuc3VyZVVwZGF0ZVN1YnNjcmlwdGlvbkV4aXN0cygpO1xuICAgIH1cbiAgICByZXR1cm4gaWQ7XG4gIH1cblxuICByZW1vdmVMaXN0ZW5lcihpZDogc3RyaW5nKTogdm9pZCB7XG4gICAgc3VwZXIucmVtb3ZlTGlzdGVuZXIoaWQpO1xuICAgIHRoaXMuI2xpc3RlbmVyQ291bnQtLTtcbiAgICBpZiAodGhpcy5fX2lzTmF0aXZlICYmIHRoaXMuI2xpc3RlbmVyQ291bnQgPT09IDApIHtcbiAgICAgIHRoaXMuI3VwZGF0ZVN1YnNjcmlwdGlvbj8ucmVtb3ZlKCk7XG4gICAgfVxuICB9XG5cbiAgcmVtb3ZlQWxsTGlzdGVuZXJzKCk6IHZvaWQge1xuICAgIHN1cGVyLnJlbW92ZUFsbExpc3RlbmVycygpO1xuICAgIHRoaXMuI2xpc3RlbmVyQ291bnQgPSAwO1xuICAgIGlmICh0aGlzLl9faXNOYXRpdmUpIHtcbiAgICAgIHRoaXMuI3VwZGF0ZVN1YnNjcmlwdGlvbj8ucmVtb3ZlKCk7XG4gICAgfVxuICB9XG5cbiAgI2Vuc3VyZVVwZGF0ZVN1YnNjcmlwdGlvbkV4aXN0cygpOiB2b2lkIHtcbiAgICBpZiAodGhpcy4jdXBkYXRlU3Vic2NyaXB0aW9uICE9IG51bGwpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgY29uc3QgbmF0aXZlVGFnID0gdGhpcy5fX2dldE5hdGl2ZVRhZygpO1xuICAgIE5hdGl2ZUFuaW1hdGVkQVBJLnN0YXJ0TGlzdGVuaW5nVG9BbmltYXRlZE5vZGVWYWx1ZShuYXRpdmVUYWcpO1xuICAgIGNvbnN0IHN1YnNjcmlwdGlvbjogRXZlbnRTdWJzY3JpcHRpb24gPVxuICAgICAgTmF0aXZlQW5pbWF0ZWRIZWxwZXIubmF0aXZlRXZlbnRFbWl0dGVyLmFkZExpc3RlbmVyKFxuICAgICAgICAnb25BbmltYXRlZFZhbHVlVXBkYXRlJyxcbiAgICAgICAgZGF0YSA9PiB7XG4gICAgICAgICAgaWYgKGRhdGEudGFnID09PSBuYXRpdmVUYWcpIHtcbiAgICAgICAgICAgIHRoaXMuX19vbkFuaW1hdGVkVmFsdWVVcGRhdGVSZWNlaXZlZChkYXRhLnZhbHVlKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICApO1xuXG4gICAgdGhpcy4jdXBkYXRlU3Vic2NyaXB0aW9uID0ge1xuICAgICAgcmVtb3ZlOiAoKSA9PiB7XG4gICAgICAgIC8vIE9ubHkgdGhpcyBmdW5jdGlvbiBhc3NpZ25zIHRvIGB0aGlzLiN1cGRhdGVTdWJzY3JpcHRpb25gLlxuICAgICAgICBpZiAodGhpcy4jdXBkYXRlU3Vic2NyaXB0aW9uID09IG51bGwpIHtcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy4jdXBkYXRlU3Vic2NyaXB0aW9uID0gbnVsbDtcbiAgICAgICAgc3Vic2NyaXB0aW9uLnJlbW92ZSgpO1xuICAgICAgICBOYXRpdmVBbmltYXRlZEFQSS5zdG9wTGlzdGVuaW5nVG9BbmltYXRlZE5vZGVWYWx1ZShuYXRpdmVUYWcpO1xuICAgICAgfSxcbiAgICB9O1xuICB9XG5cbiAgLyoqXG4gICAqIERpcmVjdGx5IHNldCB0aGUgdmFsdWUuICBUaGlzIHdpbGwgc3RvcCBhbnkgYW5pbWF0aW9ucyBydW5uaW5nIG9uIHRoZSB2YWx1ZVxuICAgKiBhbmQgdXBkYXRlIGFsbCB0aGUgYm91bmQgcHJvcGVydGllcy5cbiAgICpcbiAgICogU2VlIGh0dHBzOi8vcmVhY3RuYXRpdmUuZGV2L2RvY3MvYW5pbWF0ZWR2YWx1ZSNzZXR2YWx1ZVxuICAgKi9cbiAgc2V0VmFsdWUodmFsdWU6IG51bWJlcik6IHZvaWQge1xuICAgIGlmICh0aGlzLl9hbmltYXRpb24pIHtcbiAgICAgIHRoaXMuX2FuaW1hdGlvbi5zdG9wKCk7XG4gICAgICB0aGlzLl9hbmltYXRpb24gPSBudWxsO1xuICAgIH1cbiAgICB0aGlzLl91cGRhdGVWYWx1ZShcbiAgICAgIHZhbHVlLFxuICAgICAgIXRoaXMuX19pc05hdGl2ZSAvKiBkb24ndCBwZXJmb3JtIGEgZmx1c2ggZm9yIG5hdGl2ZWx5IGRyaXZlbiB2YWx1ZXMgKi8sXG4gICAgKTtcbiAgICBpZiAodGhpcy5fX2lzTmF0aXZlKSB7XG4gICAgICBfZXhlY3V0ZUFzQW5pbWF0ZWRCYXRjaCh0aGlzLl9fZ2V0TmF0aXZlVGFnKCkudG9TdHJpbmcoKSwgKCkgPT5cbiAgICAgICAgTmF0aXZlQW5pbWF0ZWRBUEkuc2V0QW5pbWF0ZWROb2RlVmFsdWUodGhpcy5fX2dldE5hdGl2ZVRhZygpLCB2YWx1ZSksXG4gICAgICApO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBTZXRzIGFuIG9mZnNldCB0aGF0IGlzIGFwcGxpZWQgb24gdG9wIG9mIHdoYXRldmVyIHZhbHVlIGlzIHNldCwgd2hldGhlciB2aWFcbiAgICogYHNldFZhbHVlYCwgYW4gYW5pbWF0aW9uLCBvciBgQW5pbWF0ZWQuZXZlbnRgLiAgVXNlZnVsIGZvciBjb21wZW5zYXRpbmdcbiAgICogdGhpbmdzIGxpa2UgdGhlIHN0YXJ0IG9mIGEgcGFuIGdlc3R1cmUuXG4gICAqXG4gICAqIFNlZSBodHRwczovL3JlYWN0bmF0aXZlLmRldi9kb2NzL2FuaW1hdGVkdmFsdWUjc2V0b2Zmc2V0XG4gICAqL1xuICBzZXRPZmZzZXQob2Zmc2V0OiBudW1iZXIpOiB2b2lkIHtcbiAgICB0aGlzLl9vZmZzZXQgPSBvZmZzZXQ7XG4gICAgaWYgKHRoaXMuX19pc05hdGl2ZSkge1xuICAgICAgTmF0aXZlQW5pbWF0ZWRBUEkuc2V0QW5pbWF0ZWROb2RlT2Zmc2V0KHRoaXMuX19nZXROYXRpdmVUYWcoKSwgb2Zmc2V0KTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogTWVyZ2VzIHRoZSBvZmZzZXQgdmFsdWUgaW50byB0aGUgYmFzZSB2YWx1ZSBhbmQgcmVzZXRzIHRoZSBvZmZzZXQgdG8gemVyby5cbiAgICogVGhlIGZpbmFsIG91dHB1dCBvZiB0aGUgdmFsdWUgaXMgdW5jaGFuZ2VkLlxuICAgKlxuICAgKiBTZWUgaHR0cHM6Ly9yZWFjdG5hdGl2ZS5kZXYvZG9jcy9hbmltYXRlZHZhbHVlI2ZsYXR0ZW5vZmZzZXRcbiAgICovXG4gIGZsYXR0ZW5PZmZzZXQoKTogdm9pZCB7XG4gICAgdGhpcy5fdmFsdWUgKz0gdGhpcy5fb2Zmc2V0O1xuICAgIHRoaXMuX29mZnNldCA9IDA7XG4gICAgaWYgKHRoaXMuX19pc05hdGl2ZSkge1xuICAgICAgTmF0aXZlQW5pbWF0ZWRBUEkuZmxhdHRlbkFuaW1hdGVkTm9kZU9mZnNldCh0aGlzLl9fZ2V0TmF0aXZlVGFnKCkpO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBTZXRzIHRoZSBvZmZzZXQgdmFsdWUgdG8gdGhlIGJhc2UgdmFsdWUsIGFuZCByZXNldHMgdGhlIGJhc2UgdmFsdWUgdG8gemVyby5cbiAgICogVGhlIGZpbmFsIG91dHB1dCBvZiB0aGUgdmFsdWUgaXMgdW5jaGFuZ2VkLlxuICAgKlxuICAgKiBTZWUgaHR0cHM6Ly9yZWFjdG5hdGl2ZS5kZXYvZG9jcy9hbmltYXRlZHZhbHVlI2V4dHJhY3RvZmZzZXRcbiAgICovXG4gIGV4dHJhY3RPZmZzZXQoKTogdm9pZCB7XG4gICAgdGhpcy5fb2Zmc2V0ICs9IHRoaXMuX3ZhbHVlO1xuICAgIHRoaXMuX3ZhbHVlID0gMDtcbiAgICBpZiAodGhpcy5fX2lzTmF0aXZlKSB7XG4gICAgICBOYXRpdmVBbmltYXRlZEFQSS5leHRyYWN0QW5pbWF0ZWROb2RlT2Zmc2V0KHRoaXMuX19nZXROYXRpdmVUYWcoKSk7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFN0b3BzIGFueSBydW5uaW5nIGFuaW1hdGlvbiBvciB0cmFja2luZy4gYGNhbGxiYWNrYCBpcyBpbnZva2VkIHdpdGggdGhlXG4gICAqIGZpbmFsIHZhbHVlIGFmdGVyIHN0b3BwaW5nIHRoZSBhbmltYXRpb24sIHdoaWNoIGlzIHVzZWZ1bCBmb3IgdXBkYXRpbmdcbiAgICogc3RhdGUgdG8gbWF0Y2ggdGhlIGFuaW1hdGlvbiBwb3NpdGlvbiB3aXRoIGxheW91dC5cbiAgICpcbiAgICogU2VlIGh0dHBzOi8vcmVhY3RuYXRpdmUuZGV2L2RvY3MvYW5pbWF0ZWR2YWx1ZSNzdG9wYW5pbWF0aW9uXG4gICAqL1xuICBzdG9wQW5pbWF0aW9uKGNhbGxiYWNrPzogPyh2YWx1ZTogbnVtYmVyKSA9PiB2b2lkKTogdm9pZCB7XG4gICAgdGhpcy5zdG9wVHJhY2tpbmcoKTtcbiAgICB0aGlzLl9hbmltYXRpb24gJiYgdGhpcy5fYW5pbWF0aW9uLnN0b3AoKTtcbiAgICB0aGlzLl9hbmltYXRpb24gPSBudWxsO1xuICAgIGlmIChjYWxsYmFjaykge1xuICAgICAgaWYgKHRoaXMuX19pc05hdGl2ZSkge1xuICAgICAgICBOYXRpdmVBbmltYXRlZEFQSS5nZXRWYWx1ZSh0aGlzLl9fZ2V0TmF0aXZlVGFnKCksIGNhbGxiYWNrKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNhbGxiYWNrKHRoaXMuX19nZXRWYWx1ZSgpKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogU3RvcHMgYW55IGFuaW1hdGlvbiBhbmQgcmVzZXRzIHRoZSB2YWx1ZSB0byBpdHMgb3JpZ2luYWwuXG4gICAqXG4gICAqIFNlZSBodHRwczovL3JlYWN0bmF0aXZlLmRldi9kb2NzL2FuaW1hdGVkdmFsdWUjcmVzZXRhbmltYXRpb25cbiAgICovXG4gIHJlc2V0QW5pbWF0aW9uKGNhbGxiYWNrPzogPyh2YWx1ZTogbnVtYmVyKSA9PiB2b2lkKTogdm9pZCB7XG4gICAgdGhpcy5zdG9wQW5pbWF0aW9uKGNhbGxiYWNrKTtcbiAgICB0aGlzLl92YWx1ZSA9IHRoaXMuX3N0YXJ0aW5nVmFsdWU7XG4gICAgaWYgKHRoaXMuX19pc05hdGl2ZSkge1xuICAgICAgTmF0aXZlQW5pbWF0ZWRBUEkuc2V0QW5pbWF0ZWROb2RlVmFsdWUoXG4gICAgICAgIHRoaXMuX19nZXROYXRpdmVUYWcoKSxcbiAgICAgICAgdGhpcy5fc3RhcnRpbmdWYWx1ZSxcbiAgICAgICk7XG4gICAgfVxuICB9XG5cbiAgX19vbkFuaW1hdGVkVmFsdWVVcGRhdGVSZWNlaXZlZCh2YWx1ZTogbnVtYmVyKTogdm9pZCB7XG4gICAgdGhpcy5fdXBkYXRlVmFsdWUodmFsdWUsIGZhbHNlIC8qZmx1c2gqLyk7XG4gIH1cblxuICAvKipcbiAgICogSW50ZXJwb2xhdGVzIHRoZSB2YWx1ZSBiZWZvcmUgdXBkYXRpbmcgdGhlIHByb3BlcnR5LCBlLmcuIG1hcHBpbmcgMC0xIHRvXG4gICAqIDAtMTAuXG4gICAqL1xuICBpbnRlcnBvbGF0ZTxPdXRwdXRUOiBudW1iZXIgfCBzdHJpbmc+KFxuICAgIGNvbmZpZzogSW50ZXJwb2xhdGlvbkNvbmZpZ1R5cGU8T3V0cHV0VD4sXG4gICk6IEFuaW1hdGVkSW50ZXJwb2xhdGlvbjxPdXRwdXRUPiB7XG4gICAgcmV0dXJuIG5ldyBBbmltYXRlZEludGVycG9sYXRpb24odGhpcywgY29uZmlnKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBUeXBpY2FsbHkgb25seSB1c2VkIGludGVybmFsbHksIGJ1dCBjb3VsZCBiZSB1c2VkIGJ5IGEgY3VzdG9tIEFuaW1hdGlvblxuICAgKiBjbGFzcy5cbiAgICpcbiAgICogU2VlIGh0dHBzOi8vcmVhY3RuYXRpdmUuZGV2L2RvY3MvYW5pbWF0ZWR2YWx1ZSNhbmltYXRlXG4gICAqL1xuICBhbmltYXRlKGFuaW1hdGlvbjogQW5pbWF0aW9uLCBjYWxsYmFjazogP0VuZENhbGxiYWNrKTogdm9pZCB7XG4gICAgbGV0IGhhbmRsZSA9IG51bGw7XG4gICAgaWYgKGFuaW1hdGlvbi5fX2lzSW50ZXJhY3Rpb24pIHtcbiAgICAgIGhhbmRsZSA9IEludGVyYWN0aW9uTWFuYWdlci5jcmVhdGVJbnRlcmFjdGlvbkhhbmRsZSgpO1xuICAgIH1cbiAgICBjb25zdCBwcmV2aW91c0FuaW1hdGlvbiA9IHRoaXMuX2FuaW1hdGlvbjtcbiAgICB0aGlzLl9hbmltYXRpb24gJiYgdGhpcy5fYW5pbWF0aW9uLnN0b3AoKTtcbiAgICB0aGlzLl9hbmltYXRpb24gPSBhbmltYXRpb247XG4gICAgYW5pbWF0aW9uLnN0YXJ0KFxuICAgICAgdGhpcy5fdmFsdWUsXG4gICAgICB2YWx1ZSA9PiB7XG4gICAgICAgIC8vIE5hdGl2ZWx5IGRyaXZlbiBhbmltYXRpb25zIHdpbGwgbmV2ZXIgY2FsbCBpbnRvIHRoYXQgY2FsbGJhY2ssIHRoZXJlZm9yZSB3ZSBjYW4gYWx3YXlzXG4gICAgICAgIC8vIHBhc3MgZmx1c2ggPSB0cnVlIHRvIGFsbG93IHRoZSB1cGRhdGVkIHZhbHVlIHRvIHByb3BhZ2F0ZSB0byBuYXRpdmUgd2l0aCBzZXROYXRpdmVQcm9wc1xuICAgICAgICB0aGlzLl91cGRhdGVWYWx1ZSh2YWx1ZSwgdHJ1ZSAvKiBmbHVzaCAqLyk7XG4gICAgICB9LFxuICAgICAgcmVzdWx0ID0+IHtcbiAgICAgICAgdGhpcy5fYW5pbWF0aW9uID0gbnVsbDtcbiAgICAgICAgaWYgKGhhbmRsZSAhPT0gbnVsbCkge1xuICAgICAgICAgIEludGVyYWN0aW9uTWFuYWdlci5jbGVhckludGVyYWN0aW9uSGFuZGxlKGhhbmRsZSk7XG4gICAgICAgIH1cbiAgICAgICAgY2FsbGJhY2sgJiYgY2FsbGJhY2socmVzdWx0KTtcbiAgICAgIH0sXG4gICAgICBwcmV2aW91c0FuaW1hdGlvbixcbiAgICAgIHRoaXMsXG4gICAgKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBUeXBpY2FsbHkgb25seSB1c2VkIGludGVybmFsbHkuXG4gICAqL1xuICBzdG9wVHJhY2tpbmcoKTogdm9pZCB7XG4gICAgdGhpcy5fdHJhY2tpbmcgJiYgdGhpcy5fdHJhY2tpbmcuX19kZXRhY2goKTtcbiAgICB0aGlzLl90cmFja2luZyA9IG51bGw7XG4gIH1cblxuICAvKipcbiAgICogVHlwaWNhbGx5IG9ubHkgdXNlZCBpbnRlcm5hbGx5LlxuICAgKi9cbiAgdHJhY2sodHJhY2tpbmc6IEFuaW1hdGVkVHJhY2tpbmcpOiB2b2lkIHtcbiAgICB0aGlzLnN0b3BUcmFja2luZygpO1xuICAgIHRoaXMuX3RyYWNraW5nID0gdHJhY2tpbmc7XG4gICAgLy8gTWFrZSBzdXJlIHRoYXQgdGhlIHRyYWNraW5nIGFuaW1hdGlvbiBzdGFydHMgZXhlY3V0aW5nXG4gICAgdGhpcy5fdHJhY2tpbmcgJiYgdGhpcy5fdHJhY2tpbmcudXBkYXRlKCk7XG4gIH1cblxuICBfdXBkYXRlVmFsdWUodmFsdWU6IG51bWJlciwgZmx1c2g6IGJvb2xlYW4pOiB2b2lkIHtcbiAgICBpZiAodmFsdWUgPT09IHVuZGVmaW5lZCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdBbmltYXRlZFZhbHVlOiBBdHRlbXB0aW5nIHRvIHNldCB2YWx1ZSB0byB1bmRlZmluZWQnKTtcbiAgICB9XG5cbiAgICB0aGlzLl92YWx1ZSA9IHZhbHVlO1xuICAgIGlmIChmbHVzaCkge1xuICAgICAgZmx1c2hWYWx1ZSh0aGlzKTtcbiAgICB9XG4gICAgdGhpcy5fX2NhbGxMaXN0ZW5lcnModGhpcy5fX2dldFZhbHVlKCkpO1xuICB9XG5cbiAgX19nZXROYXRpdmVDb25maWcoKTogT2JqZWN0IHtcbiAgICByZXR1cm4ge1xuICAgICAgdHlwZTogJ3ZhbHVlJyxcbiAgICAgIHZhbHVlOiB0aGlzLl92YWx1ZSxcbiAgICAgIG9mZnNldDogdGhpcy5fb2Zmc2V0LFxuICAgICAgZGVidWdJRDogdGhpcy5fX2dldERlYnVnSUQoKSxcbiAgICB9O1xuICB9XG59XG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBa0JBLElBQUFBLHFCQUFBLEdBQUFDLHNCQUFBLENBQUFDLE9BQUE7QUFDQSxJQUFBQyxtQkFBQSxHQUFBRixzQkFBQSxDQUFBQyxPQUFBO0FBQ0EsSUFBQUUsc0JBQUEsR0FBQUgsc0JBQUEsQ0FBQUMsT0FBQTtBQUNBLElBQUFHLHNCQUFBLEdBQUFKLHNCQUFBLENBQUFDLE9BQUE7QUFBMEQsU0FBQUksV0FBQUMsQ0FBQSxFQUFBQyxDQUFBLEVBQUFDLENBQUEsV0FBQUQsQ0FBQSxPQUFBRSxnQkFBQSxDQUFBQyxPQUFBLEVBQUFILENBQUEsT0FBQUksMkJBQUEsQ0FBQUQsT0FBQSxFQUFBSixDQUFBLEVBQUFNLHlCQUFBLEtBQUFDLE9BQUEsQ0FBQUMsU0FBQSxDQUFBUCxDQUFBLEVBQUFDLENBQUEsWUFBQUMsZ0JBQUEsQ0FBQUMsT0FBQSxFQUFBSixDQUFBLEVBQUFTLFdBQUEsSUFBQVIsQ0FBQSxDQUFBUyxLQUFBLENBQUFWLENBQUEsRUFBQUUsQ0FBQTtBQUFBLFNBQUFJLDBCQUFBLGNBQUFOLENBQUEsSUFBQVcsT0FBQSxDQUFBQyxTQUFBLENBQUFDLE9BQUEsQ0FBQUMsSUFBQSxDQUFBUCxPQUFBLENBQUFDLFNBQUEsQ0FBQUcsT0FBQSxpQ0FBQVgsQ0FBQSxhQUFBTSx5QkFBQSxZQUFBQSwwQkFBQSxhQUFBTixDQUFBO0FBQUEsU0FBQWUsY0FBQWYsQ0FBQSxFQUFBQyxDQUFBLEVBQUFDLENBQUEsRUFBQWMsQ0FBQSxRQUFBQyxDQUFBLE9BQUFDLEtBQUEsQ0FBQWQsT0FBQSxNQUFBRCxnQkFBQSxDQUFBQyxPQUFBLE1BQUFZLENBQUEsR0FBQWhCLENBQUEsQ0FBQVksU0FBQSxHQUFBWixDQUFBLEdBQUFDLENBQUEsRUFBQUMsQ0FBQSxjQUFBYyxDQUFBLHlCQUFBQyxDQUFBLGFBQUFqQixDQUFBLFdBQUFpQixDQUFBLENBQUFQLEtBQUEsQ0FBQVIsQ0FBQSxFQUFBRixDQUFBLE9BQUFpQixDQUFBO0FBTzFELElBQU1FLGlCQUFpQixHQUFHQyw2QkFBb0IsQ0FBQ0MsR0FBRztBQXdCM0MsU0FBU0MsVUFBVUEsQ0FBQ0MsUUFBc0IsRUFBUTtFQUV2RCxJQUFNQyxNQUFNLEdBQUcsSUFBSUMsR0FBRyxDQUE0QixDQUFDO0VBQ25ELFNBQVNDLGtCQUFrQkEsQ0FBQ0MsSUFBa0IsRUFBRTtJQUU5QyxJQUFJLE9BQU9BLElBQUksQ0FBQ0MsTUFBTSxLQUFLLFVBQVUsRUFBRTtNQUNyQ0osTUFBTSxDQUFDSyxHQUFHLENBQUVGLElBQVUsQ0FBQztJQUN6QixDQUFDLE1BQU07TUFDTEEsSUFBSSxDQUFDRyxhQUFhLENBQUMsQ0FBQyxDQUFDQyxPQUFPLENBQUNMLGtCQUFrQixDQUFDO0lBQ2xEO0VBQ0Y7RUFDQUEsa0JBQWtCLENBQUNILFFBQVEsQ0FBQztFQUM1QkMsTUFBTSxDQUFDTyxPQUFPLENBQUMsVUFBQUMsSUFBSTtJQUFBLE9BQUlBLElBQUksQ0FBQ0osTUFBTSxDQUFDLENBQUM7RUFBQSxFQUFDO0FBQ3ZDO0FBT0EsU0FBU0ssdUJBQXVCQSxDQUFDQyxFQUFVLEVBQUVDLFNBQXFCLEVBQUU7RUFDbEVoQixpQkFBaUIsQ0FBQ2lCLHVCQUF1QixDQUFDRixFQUFFLENBQUM7RUFDN0NDLFNBQVMsQ0FBQyxDQUFDO0VBQ1hoQixpQkFBaUIsQ0FBQ2tCLHlCQUF5QixDQUFDSCxFQUFFLENBQUM7QUFDakQ7QUFBQyxJQUFBSSxjQUFBLE9BQUFDLDJCQUFBLENBQUFuQyxPQUFBO0FBQUEsSUFBQW9DLG1CQUFBLE9BQUFELDJCQUFBLENBQUFuQyxPQUFBO0FBQUEsSUFBQXFDLCtCQUFBLE9BQUFGLDJCQUFBLENBQUFuQyxPQUFBO0FBQUEsSUFVb0JzQyxhQUFhLEdBQUFDLE9BQUEsQ0FBQXZDLE9BQUEsYUFBQXdDLHFCQUFBO0VBVWhDLFNBQUFGLGNBQVlHLEtBQWEsRUFBRUMsTUFBNkIsRUFBRTtJQUFBLElBQUFDLEtBQUE7SUFBQSxJQUFBQyxnQkFBQSxDQUFBNUMsT0FBQSxRQUFBc0MsYUFBQTtJQUN4REssS0FBQSxHQUFBaEQsVUFBQSxPQUFBMkMsYUFBQSxHQUFNSSxNQUFNO0lBQUVHLE1BQUEsQ0FBQUMsY0FBQSxDQUFBSCxLQUFBLEVBQUFOLCtCQUFBO01BQUFJLEtBQUEsRUFBQU07SUFBQTtJQUFBRixNQUFBLENBQUFDLGNBQUEsQ0FBQUgsS0FBQSxFQUFBVCxjQUFBO01BQUFjLFFBQUE7TUFBQVAsS0FBQSxFQVZTO0lBQUM7SUFBQUksTUFBQSxDQUFBQyxjQUFBLENBQUFILEtBQUEsRUFBQVAsbUJBQUE7TUFBQVksUUFBQTtNQUFBUCxLQUFBLEVBQ2dCO0lBQUk7SUFVNUMsSUFBSSxPQUFPQSxLQUFLLEtBQUssUUFBUSxFQUFFO01BQzdCLE1BQU0sSUFBSVEsS0FBSyxDQUFDLHFEQUFxRCxDQUFDO0lBQ3hFO0lBQ0FOLEtBQUEsQ0FBS08sY0FBYyxHQUFHUCxLQUFBLENBQUtRLE1BQU0sR0FBR1YsS0FBSztJQUN6Q0UsS0FBQSxDQUFLUyxPQUFPLEdBQUcsQ0FBQztJQUNoQlQsS0FBQSxDQUFLVSxVQUFVLEdBQUcsSUFBSTtJQUN0QixJQUFJWCxNQUFNLElBQUlBLE1BQU0sQ0FBQ1ksZUFBZSxFQUFFO01BQ3BDWCxLQUFBLENBQUtZLFlBQVksQ0FBQyxDQUFDO0lBQ3JCO0lBQUMsT0FBQVosS0FBQTtFQUNIO0VBQUMsSUFBQWEsVUFBQSxDQUFBeEQsT0FBQSxFQUFBc0MsYUFBQSxFQUFBRSxxQkFBQTtFQUFBLFdBQUFpQixhQUFBLENBQUF6RCxPQUFBLEVBQUFzQyxhQUFBO0lBQUFvQixHQUFBO0lBQUFqQixLQUFBLEVBRUQsU0FBQWtCLFFBQVFBLENBQUEsRUFBRztNQUFBLElBQUFDLE1BQUE7TUFDVCxJQUFJLElBQUksQ0FBQ0MsVUFBVSxFQUFFO1FBQ25COUMsaUJBQWlCLENBQUMrQyxRQUFRLENBQUMsSUFBSSxDQUFDQyxjQUFjLENBQUMsQ0FBQyxFQUFFLFVBQUF0QixLQUFLLEVBQUk7VUFDekRtQixNQUFJLENBQUNULE1BQU0sR0FBR1YsS0FBSyxHQUFHbUIsTUFBSSxDQUFDUixPQUFPO1FBQ3BDLENBQUMsQ0FBQztNQUNKO01BQ0EsSUFBSSxDQUFDWSxhQUFhLENBQUMsQ0FBQztNQUNwQnJELGFBQUEsQ0FBQTJCLGFBQUE7SUFDRjtFQUFDO0lBQUFvQixHQUFBO0lBQUFqQixLQUFBLEVBRUQsU0FBQXdCLFVBQVVBLENBQUEsRUFBVztNQUNuQixPQUFPLElBQUksQ0FBQ2QsTUFBTSxHQUFHLElBQUksQ0FBQ0MsT0FBTztJQUNuQztFQUFDO0lBQUFNLEdBQUE7SUFBQWpCLEtBQUEsRUFFRCxTQUFBYyxZQUFZQSxDQUFDVyxjQUErQixFQUFRO01BQ2xEdkQsYUFBQSxDQUFBMkIsYUFBQSw0QkFBbUI0QixjQUFjO01BQ2pDLElBQUksSUFBQUMsNEJBQUEsQ0FBQW5FLE9BQUEsTUFBSSxFQUFBa0MsY0FBQSxFQUFBQSxjQUFBLElBQWtCLENBQUMsRUFBRTtRQUMzQixJQUFBaUMsNEJBQUEsQ0FBQW5FLE9BQUEsTUFBSSxFQUFBcUMsK0JBQUEsRUFBQUEsK0JBQUE7TUFDTjtJQUNGO0VBQUM7SUFBQXFCLEdBQUE7SUFBQWpCLEtBQUEsRUFFRCxTQUFBMkIsV0FBV0EsQ0FBQ0MsUUFBK0IsRUFBVTtNQUNuRCxJQUFNdkMsRUFBRSxHQUFBbkIsYUFBQSxDQUFBMkIsYUFBQSwyQkFBcUIrQixRQUFRLEVBQUM7TUFDdEMsSUFBQUYsNEJBQUEsQ0FBQW5FLE9BQUEsTUFBSSxFQUFBa0MsY0FBQSxFQUFBQSxjQUFBLEdBQWlCO01BQ3JCLElBQUksSUFBSSxDQUFDMkIsVUFBVSxFQUFFO1FBQ25CLElBQUFNLDRCQUFBLENBQUFuRSxPQUFBLE1BQUksRUFBQXFDLCtCQUFBLEVBQUFBLCtCQUFBO01BQ047TUFDQSxPQUFPUCxFQUFFO0lBQ1g7RUFBQztJQUFBNEIsR0FBQTtJQUFBakIsS0FBQSxFQUVELFNBQUE2QixjQUFjQSxDQUFDeEMsRUFBVSxFQUFRO01BQy9CbkIsYUFBQSxDQUFBMkIsYUFBQSw4QkFBcUJSLEVBQUU7TUFDdkIsSUFBQXFDLDRCQUFBLENBQUFuRSxPQUFBLE1BQUksRUFBQWtDLGNBQUEsRUFBQUEsY0FBQSxHQUFpQjtNQUNyQixJQUFJLElBQUksQ0FBQzJCLFVBQVUsSUFBSSxJQUFBTSw0QkFBQSxDQUFBbkUsT0FBQSxNQUFJLEVBQUFrQyxjQUFBLEVBQUFBLGNBQUEsTUFBb0IsQ0FBQyxFQUFFO1FBQUEsSUFBQXFDLHFCQUFBO1FBQ2hELENBQUFBLHFCQUFBLE9BQUFKLDRCQUFBLENBQUFuRSxPQUFBLE1BQUksRUFBQW9DLG1CQUFBLEVBQUFBLG1CQUFBLGNBQUptQyxxQkFBQSxDQUEwQkMsTUFBTSxDQUFDLENBQUM7TUFDcEM7SUFDRjtFQUFDO0lBQUFkLEdBQUE7SUFBQWpCLEtBQUEsRUFFRCxTQUFBZ0Msa0JBQWtCQSxDQUFBLEVBQVM7TUFDekI5RCxhQUFBLENBQUEyQixhQUFBO01BQ0EsSUFBQTZCLDRCQUFBLENBQUFuRSxPQUFBLE1BQUksRUFBQWtDLGNBQUEsRUFBQUEsY0FBQSxJQUFrQixDQUFDO01BQ3ZCLElBQUksSUFBSSxDQUFDMkIsVUFBVSxFQUFFO1FBQUEsSUFBQWEsc0JBQUE7UUFDbkIsQ0FBQUEsc0JBQUEsT0FBQVAsNEJBQUEsQ0FBQW5FLE9BQUEsTUFBSSxFQUFBb0MsbUJBQUEsRUFBQUEsbUJBQUEsY0FBSnNDLHNCQUFBLENBQTBCRixNQUFNLENBQUMsQ0FBQztNQUNwQztJQUNGO0VBQUM7SUFBQWQsR0FBQTtJQUFBakIsS0FBQSxFQXFDRCxTQUFBa0MsUUFBUUEsQ0FBQ2xDLEtBQWEsRUFBUTtNQUFBLElBQUFtQyxNQUFBO01BQzVCLElBQUksSUFBSSxDQUFDdkIsVUFBVSxFQUFFO1FBQ25CLElBQUksQ0FBQ0EsVUFBVSxDQUFDd0IsSUFBSSxDQUFDLENBQUM7UUFDdEIsSUFBSSxDQUFDeEIsVUFBVSxHQUFHLElBQUk7TUFDeEI7TUFDQSxJQUFJLENBQUN5QixZQUFZLENBQ2ZyQyxLQUFLLEVBQ0wsQ0FBQyxJQUFJLENBQUNvQixVQUNSLENBQUM7TUFDRCxJQUFJLElBQUksQ0FBQ0EsVUFBVSxFQUFFO1FBQ25CaEMsdUJBQXVCLENBQUMsSUFBSSxDQUFDa0MsY0FBYyxDQUFDLENBQUMsQ0FBQ2dCLFFBQVEsQ0FBQyxDQUFDLEVBQUU7VUFBQSxPQUN4RGhFLGlCQUFpQixDQUFDaUUsb0JBQW9CLENBQUNKLE1BQUksQ0FBQ2IsY0FBYyxDQUFDLENBQUMsRUFBRXRCLEtBQUssQ0FBQztRQUFBLENBQ3RFLENBQUM7TUFDSDtJQUNGO0VBQUM7SUFBQWlCLEdBQUE7SUFBQWpCLEtBQUEsRUFTRCxTQUFBd0MsU0FBU0EsQ0FBQ0MsTUFBYyxFQUFRO01BQzlCLElBQUksQ0FBQzlCLE9BQU8sR0FBRzhCLE1BQU07TUFDckIsSUFBSSxJQUFJLENBQUNyQixVQUFVLEVBQUU7UUFDbkI5QyxpQkFBaUIsQ0FBQ29FLHFCQUFxQixDQUFDLElBQUksQ0FBQ3BCLGNBQWMsQ0FBQyxDQUFDLEVBQUVtQixNQUFNLENBQUM7TUFDeEU7SUFDRjtFQUFDO0lBQUF4QixHQUFBO0lBQUFqQixLQUFBLEVBUUQsU0FBQTJDLGFBQWFBLENBQUEsRUFBUztNQUNwQixJQUFJLENBQUNqQyxNQUFNLElBQUksSUFBSSxDQUFDQyxPQUFPO01BQzNCLElBQUksQ0FBQ0EsT0FBTyxHQUFHLENBQUM7TUFDaEIsSUFBSSxJQUFJLENBQUNTLFVBQVUsRUFBRTtRQUNuQjlDLGlCQUFpQixDQUFDc0UseUJBQXlCLENBQUMsSUFBSSxDQUFDdEIsY0FBYyxDQUFDLENBQUMsQ0FBQztNQUNwRTtJQUNGO0VBQUM7SUFBQUwsR0FBQTtJQUFBakIsS0FBQSxFQVFELFNBQUE2QyxhQUFhQSxDQUFBLEVBQVM7TUFDcEIsSUFBSSxDQUFDbEMsT0FBTyxJQUFJLElBQUksQ0FBQ0QsTUFBTTtNQUMzQixJQUFJLENBQUNBLE1BQU0sR0FBRyxDQUFDO01BQ2YsSUFBSSxJQUFJLENBQUNVLFVBQVUsRUFBRTtRQUNuQjlDLGlCQUFpQixDQUFDd0UseUJBQXlCLENBQUMsSUFBSSxDQUFDeEIsY0FBYyxDQUFDLENBQUMsQ0FBQztNQUNwRTtJQUNGO0VBQUM7SUFBQUwsR0FBQTtJQUFBakIsS0FBQSxFQVNELFNBQUF1QixhQUFhQSxDQUFDSyxRQUFtQyxFQUFRO01BQ3ZELElBQUksQ0FBQ21CLFlBQVksQ0FBQyxDQUFDO01BQ25CLElBQUksQ0FBQ25DLFVBQVUsSUFBSSxJQUFJLENBQUNBLFVBQVUsQ0FBQ3dCLElBQUksQ0FBQyxDQUFDO01BQ3pDLElBQUksQ0FBQ3hCLFVBQVUsR0FBRyxJQUFJO01BQ3RCLElBQUlnQixRQUFRLEVBQUU7UUFDWixJQUFJLElBQUksQ0FBQ1IsVUFBVSxFQUFFO1VBQ25COUMsaUJBQWlCLENBQUMrQyxRQUFRLENBQUMsSUFBSSxDQUFDQyxjQUFjLENBQUMsQ0FBQyxFQUFFTSxRQUFRLENBQUM7UUFDN0QsQ0FBQyxNQUFNO1VBQ0xBLFFBQVEsQ0FBQyxJQUFJLENBQUNKLFVBQVUsQ0FBQyxDQUFDLENBQUM7UUFDN0I7TUFDRjtJQUNGO0VBQUM7SUFBQVAsR0FBQTtJQUFBakIsS0FBQSxFQU9ELFNBQUFnRCxjQUFjQSxDQUFDcEIsUUFBbUMsRUFBUTtNQUN4RCxJQUFJLENBQUNMLGFBQWEsQ0FBQ0ssUUFBUSxDQUFDO01BQzVCLElBQUksQ0FBQ2xCLE1BQU0sR0FBRyxJQUFJLENBQUNELGNBQWM7TUFDakMsSUFBSSxJQUFJLENBQUNXLFVBQVUsRUFBRTtRQUNuQjlDLGlCQUFpQixDQUFDaUUsb0JBQW9CLENBQ3BDLElBQUksQ0FBQ2pCLGNBQWMsQ0FBQyxDQUFDLEVBQ3JCLElBQUksQ0FBQ2IsY0FDUCxDQUFDO01BQ0g7SUFDRjtFQUFDO0lBQUFRLEdBQUE7SUFBQWpCLEtBQUEsRUFFRCxTQUFBaUQsK0JBQStCQSxDQUFDakQsS0FBYSxFQUFRO01BQ25ELElBQUksQ0FBQ3FDLFlBQVksQ0FBQ3JDLEtBQUssRUFBRSxLQUFlLENBQUM7SUFDM0M7RUFBQztJQUFBaUIsR0FBQTtJQUFBakIsS0FBQSxFQU1ELFNBQUFrRCxXQUFXQSxDQUNUakQsTUFBd0MsRUFDUjtNQUNoQyxPQUFPLElBQUlrRCw4QkFBcUIsQ0FBQyxJQUFJLEVBQUVsRCxNQUFNLENBQUM7SUFDaEQ7RUFBQztJQUFBZ0IsR0FBQTtJQUFBakIsS0FBQSxFQVFELFNBQUFvRCxPQUFPQSxDQUFDQyxTQUFvQixFQUFFekIsUUFBc0IsRUFBUTtNQUFBLElBQUEwQixNQUFBO01BQzFELElBQUlDLE1BQU0sR0FBRyxJQUFJO01BQ2pCLElBQUlGLFNBQVMsQ0FBQ0csZUFBZSxFQUFFO1FBQzdCRCxNQUFNLEdBQUdFLDJCQUFrQixDQUFDQyx1QkFBdUIsQ0FBQyxDQUFDO01BQ3ZEO01BQ0EsSUFBTUMsaUJBQWlCLEdBQUcsSUFBSSxDQUFDL0MsVUFBVTtNQUN6QyxJQUFJLENBQUNBLFVBQVUsSUFBSSxJQUFJLENBQUNBLFVBQVUsQ0FBQ3dCLElBQUksQ0FBQyxDQUFDO01BQ3pDLElBQUksQ0FBQ3hCLFVBQVUsR0FBR3lDLFNBQVM7TUFDM0JBLFNBQVMsQ0FBQ08sS0FBSyxDQUNiLElBQUksQ0FBQ2xELE1BQU0sRUFDWCxVQUFBVixLQUFLLEVBQUk7UUFHUHNELE1BQUksQ0FBQ2pCLFlBQVksQ0FBQ3JDLEtBQUssRUFBRSxJQUFnQixDQUFDO01BQzVDLENBQUMsRUFDRCxVQUFBNkQsTUFBTSxFQUFJO1FBQ1JQLE1BQUksQ0FBQzFDLFVBQVUsR0FBRyxJQUFJO1FBQ3RCLElBQUkyQyxNQUFNLEtBQUssSUFBSSxFQUFFO1VBQ25CRSwyQkFBa0IsQ0FBQ0ssc0JBQXNCLENBQUNQLE1BQU0sQ0FBQztRQUNuRDtRQUNBM0IsUUFBUSxJQUFJQSxRQUFRLENBQUNpQyxNQUFNLENBQUM7TUFDOUIsQ0FBQyxFQUNERixpQkFBaUIsRUFDakIsSUFDRixDQUFDO0lBQ0g7RUFBQztJQUFBMUMsR0FBQTtJQUFBakIsS0FBQSxFQUtELFNBQUErQyxZQUFZQSxDQUFBLEVBQVM7TUFDbkIsSUFBSSxDQUFDZ0IsU0FBUyxJQUFJLElBQUksQ0FBQ0EsU0FBUyxDQUFDN0MsUUFBUSxDQUFDLENBQUM7TUFDM0MsSUFBSSxDQUFDNkMsU0FBUyxHQUFHLElBQUk7SUFDdkI7RUFBQztJQUFBOUMsR0FBQTtJQUFBakIsS0FBQSxFQUtELFNBQUFnRSxLQUFLQSxDQUFDQyxRQUEwQixFQUFRO01BQ3RDLElBQUksQ0FBQ2xCLFlBQVksQ0FBQyxDQUFDO01BQ25CLElBQUksQ0FBQ2dCLFNBQVMsR0FBR0UsUUFBUTtNQUV6QixJQUFJLENBQUNGLFNBQVMsSUFBSSxJQUFJLENBQUNBLFNBQVMsQ0FBQ2hGLE1BQU0sQ0FBQyxDQUFDO0lBQzNDO0VBQUM7SUFBQWtDLEdBQUE7SUFBQWpCLEtBQUEsRUFFRCxTQUFBcUMsWUFBWUEsQ0FBQ3JDLEtBQWEsRUFBRWtFLEtBQWMsRUFBUTtNQUNoRCxJQUFJbEUsS0FBSyxLQUFLbUUsU0FBUyxFQUFFO1FBQ3ZCLE1BQU0sSUFBSTNELEtBQUssQ0FBQyxxREFBcUQsQ0FBQztNQUN4RTtNQUVBLElBQUksQ0FBQ0UsTUFBTSxHQUFHVixLQUFLO01BQ25CLElBQUlrRSxLQUFLLEVBQUU7UUFDVHpGLFVBQVUsQ0FBQyxJQUFJLENBQUM7TUFDbEI7TUFDQSxJQUFJLENBQUMyRixlQUFlLENBQUMsSUFBSSxDQUFDNUMsVUFBVSxDQUFDLENBQUMsQ0FBQztJQUN6QztFQUFDO0lBQUFQLEdBQUE7SUFBQWpCLEtBQUEsRUFFRCxTQUFBcUUsaUJBQWlCQSxDQUFBLEVBQVc7TUFDMUIsT0FBTztRQUNMQyxJQUFJLEVBQUUsT0FBTztRQUNidEUsS0FBSyxFQUFFLElBQUksQ0FBQ1UsTUFBTTtRQUNsQitCLE1BQU0sRUFBRSxJQUFJLENBQUM5QixPQUFPO1FBQ3BCNEQsT0FBTyxFQUFFLElBQUksQ0FBQ0MsWUFBWSxDQUFDO01BQzdCLENBQUM7SUFDSDtFQUFDO0FBQUEsRUExUndDQyw4QkFBb0I7QUFBQSxTQUFBbkUsaUNBQUEsRUFxRXJCO0VBQUEsSUFBQW9FLE1BQUE7RUFDdEMsSUFBSSxJQUFBaEQsNEJBQUEsQ0FBQW5FLE9BQUEsTUFBSSxFQUFBb0MsbUJBQUEsRUFBQUEsbUJBQUEsS0FBd0IsSUFBSSxFQUFFO0lBQ3BDO0VBQ0Y7RUFDQSxJQUFNZ0YsU0FBUyxHQUFHLElBQUksQ0FBQ3JELGNBQWMsQ0FBQyxDQUFDO0VBQ3ZDaEQsaUJBQWlCLENBQUNzRyxpQ0FBaUMsQ0FBQ0QsU0FBUyxDQUFDO0VBQzlELElBQU1FLFlBQStCLEdBQ25DdEcsNkJBQW9CLENBQUN1RyxrQkFBa0IsQ0FBQ25ELFdBQVcsQ0FDakQsdUJBQXVCLEVBQ3ZCLFVBQUFvRCxJQUFJLEVBQUk7SUFDTixJQUFJQSxJQUFJLENBQUNDLEdBQUcsS0FBS0wsU0FBUyxFQUFFO01BQzFCRCxNQUFJLENBQUN6QiwrQkFBK0IsQ0FBQzhCLElBQUksQ0FBQy9FLEtBQUssQ0FBQztJQUNsRDtFQUNGLENBQ0YsQ0FBQztFQUVILElBQUEwQiw0QkFBQSxDQUFBbkUsT0FBQSxNQUFJLEVBQUFvQyxtQkFBQSxFQUFBQSxtQkFBQSxJQUF1QjtJQUN6Qm9DLE1BQU0sRUFBRSxTQUFSQSxNQUFNQSxDQUFBLEVBQVE7TUFFWixJQUFJLElBQUFMLDRCQUFBLENBQUFuRSxPQUFBLEVBQUFtSCxNQUFJLEVBQUEvRSxtQkFBQSxFQUFBQSxtQkFBQSxLQUF3QixJQUFJLEVBQUU7UUFDcEM7TUFDRjtNQUNBLElBQUErQiw0QkFBQSxDQUFBbkUsT0FBQSxFQUFBbUgsTUFBSSxFQUFBL0UsbUJBQUEsRUFBQUEsbUJBQUEsSUFBdUIsSUFBSTtNQUMvQmtGLFlBQVksQ0FBQzlDLE1BQU0sQ0FBQyxDQUFDO01BQ3JCekQsaUJBQWlCLENBQUMyRyxnQ0FBZ0MsQ0FBQ04sU0FBUyxDQUFDO0lBQy9EO0VBQ0YsQ0FBQztBQUNIIiwiaWdub3JlTGlzdCI6W119