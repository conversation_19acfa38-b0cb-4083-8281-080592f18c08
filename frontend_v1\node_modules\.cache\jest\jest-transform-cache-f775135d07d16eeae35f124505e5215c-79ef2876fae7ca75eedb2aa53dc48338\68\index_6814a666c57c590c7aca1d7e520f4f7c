3c7cde1b47112c8196647333967c180b
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "ComponentErrorBoundary", {
  enumerable: true,
  get: function get() {
    return _UnifiedErrorBoundary.ComponentErrorBoundary;
  }
});
Object.defineProperty(exports, "ErrorCategory", {
  enumerable: true,
  get: function get() {
    return _types.ErrorCategory;
  }
});
Object.defineProperty(exports, "ErrorSeverity", {
  enumerable: true,
  get: function get() {
    return _types.ErrorSeverity;
  }
});
Object.defineProperty(exports, "ErrorType", {
  enumerable: true,
  get: function get() {
    return _types.ErrorType;
  }
});
Object.defineProperty(exports, "FeatureErrorBoundary", {
  enumerable: true,
  get: function get() {
    return _UnifiedErrorBoundary.FeatureErrorBoundary;
  }
});
Object.defineProperty(exports, "ScreenErrorBoundary", {
  enumerable: true,
  get: function get() {
    return _UnifiedErrorBoundary.ScreenErrorBoundary;
  }
});
Object.defineProperty(exports, "UnifiedError", {
  enumerable: true,
  get: function get() {
    return _types.UnifiedError;
  }
});
Object.defineProperty(exports, "UnifiedErrorBoundary", {
  enumerable: true,
  get: function get() {
    return _UnifiedErrorBoundary.UnifiedErrorBoundary;
  }
});
exports.addRecoveryStrategy = exports.addErrorListener = void 0;
Object.defineProperty(exports, "analyticsIntegrationService", {
  enumerable: true,
  get: function get() {
    return _AnalyticsIntegrationService.analyticsIntegrationService;
  }
});
exports.handleWebSocketError = exports.handleValidationError = exports.handleNetworkError = exports.handleError = exports.handleAuthError = exports.getSystemHealth = exports.getMonitoringMetrics = exports.getErrorTrends = exports.getErrorMetrics = exports.exportErrorData = exports.default = exports.createMigrationWrapper = exports.clearMonitoringHistory = exports.clearErrorQueue = void 0;
Object.defineProperty(exports, "legacyEnhancedErrorHandlingService", {
  enumerable: true,
  get: function get() {
    return _migrationHelper.legacyEnhancedErrorHandlingService;
  }
});
Object.defineProperty(exports, "legacyErrorHandlingService", {
  enumerable: true,
  get: function get() {
    return _migrationHelper.legacyErrorHandlingService;
  }
});
Object.defineProperty(exports, "legacyErrorMonitoringService", {
  enumerable: true,
  get: function get() {
    return _migrationHelper.legacyErrorMonitoringService;
  }
});
Object.defineProperty(exports, "legacyErrorReportingService", {
  enumerable: true,
  get: function get() {
    return _migrationHelper.legacyErrorReportingService;
  }
});
Object.defineProperty(exports, "legacyRuntimeErrorHandler", {
  enumerable: true,
  get: function get() {
    return _migrationHelper.legacyRuntimeErrorHandler;
  }
});
Object.defineProperty(exports, "migrationUtils", {
  enumerable: true,
  get: function get() {
    return _migrationHelper.migrationUtils;
  }
});
exports.updateErrorConfig = exports.unifiedErrorHandlingService = void 0;
Object.defineProperty(exports, "useApiErrorHandling", {
  enumerable: true,
  get: function get() {
    return _hooks.useApiErrorHandling;
  }
});
Object.defineProperty(exports, "useErrorBoundary", {
  enumerable: true,
  get: function get() {
    return _hooks.useErrorBoundary;
  }
});
Object.defineProperty(exports, "useErrorMetrics", {
  enumerable: true,
  get: function get() {
    return _hooks.useErrorMetrics;
  }
});
Object.defineProperty(exports, "useFormErrorHandling", {
  enumerable: true,
  get: function get() {
    return _hooks.useFormErrorHandling;
  }
});
Object.defineProperty(exports, "useGlobalErrorListener", {
  enumerable: true,
  get: function get() {
    return _hooks.useGlobalErrorListener;
  }
});
Object.defineProperty(exports, "useUnifiedErrorHandling", {
  enumerable: true,
  get: function get() {
    return _hooks.useUnifiedErrorHandling;
  }
});
Object.defineProperty(exports, "useWebSocketErrorHandling", {
  enumerable: true,
  get: function get() {
    return _hooks.useWebSocketErrorHandling;
  }
});
Object.defineProperty(exports, "userFeedbackService", {
  enumerable: true,
  get: function get() {
    return _UserFeedbackService.userFeedbackService;
  }
});
var _UnifiedErrorHandlingService = require("./UnifiedErrorHandlingService");
var _UserFeedbackService = require("./UserFeedbackService");
var _AnalyticsIntegrationService = require("./AnalyticsIntegrationService");
var _types = require("./types");
var _hooks = require("./hooks");
var _UnifiedErrorBoundary = require("./UnifiedErrorBoundary");
var _migrationHelper = require("./migrationHelper");
if (!_UnifiedErrorHandlingService.unifiedErrorHandlingService) {
  console.error('[UnifiedErrorHandling] Service instance is undefined - this indicates a module loading issue');
  throw new Error('UnifiedErrorHandlingService failed to initialize properly');
}
var unifiedErrorHandlingService = exports.unifiedErrorHandlingService = _UnifiedErrorHandlingService.unifiedErrorHandlingService;
var _default = exports.default = unifiedErrorHandlingService;
var handleError = exports.handleError = function handleError(error, context) {
  return _UnifiedErrorHandlingService.unifiedErrorHandlingService.handleError(error, context);
};
var handleNetworkError = exports.handleNetworkError = function handleNetworkError(error, context) {
  return _UnifiedErrorHandlingService.unifiedErrorHandlingService.handleNetworkError(error, context);
};
var handleAuthError = exports.handleAuthError = function handleAuthError(error, context) {
  return _UnifiedErrorHandlingService.unifiedErrorHandlingService.handleAuthError(error, context);
};
var handleValidationError = exports.handleValidationError = function handleValidationError(error, context) {
  return _UnifiedErrorHandlingService.unifiedErrorHandlingService.handleValidationError(error, context);
};
var handleWebSocketError = exports.handleWebSocketError = function handleWebSocketError(error, context) {
  return _UnifiedErrorHandlingService.unifiedErrorHandlingService.handleWebSocketError(error, context);
};
var addErrorListener = exports.addErrorListener = function addErrorListener(listener) {
  return _UnifiedErrorHandlingService.unifiedErrorHandlingService.addErrorListener(listener);
};
var addRecoveryStrategy = exports.addRecoveryStrategy = function addRecoveryStrategy(strategy) {
  return _UnifiedErrorHandlingService.unifiedErrorHandlingService.addRecoveryStrategy(strategy);
};
var getErrorMetrics = exports.getErrorMetrics = function getErrorMetrics() {
  return _UnifiedErrorHandlingService.unifiedErrorHandlingService.getMetrics();
};
var clearErrorQueue = exports.clearErrorQueue = function clearErrorQueue() {
  return _UnifiedErrorHandlingService.unifiedErrorHandlingService.clearErrorQueue();
};
var updateErrorConfig = exports.updateErrorConfig = function updateErrorConfig(config) {
  return _UnifiedErrorHandlingService.unifiedErrorHandlingService.updateConfig(config);
};
var getMonitoringMetrics = exports.getMonitoringMetrics = function getMonitoringMetrics() {
  return _UnifiedErrorHandlingService.unifiedErrorHandlingService.getMonitoringMetrics();
};
var getSystemHealth = exports.getSystemHealth = function getSystemHealth() {
  return _UnifiedErrorHandlingService.unifiedErrorHandlingService.getSystemHealth();
};
var getErrorTrends = exports.getErrorTrends = function getErrorTrends(days) {
  return _UnifiedErrorHandlingService.unifiedErrorHandlingService.getErrorTrends(days);
};
var exportErrorData = exports.exportErrorData = function exportErrorData(format) {
  return _UnifiedErrorHandlingService.unifiedErrorHandlingService.exportErrorData(format);
};
var clearMonitoringHistory = exports.clearMonitoringHistory = function clearMonitoringHistory() {
  return _UnifiedErrorHandlingService.unifiedErrorHandlingService.clearMonitoringHistory();
};
var createMigrationWrapper = exports.createMigrationWrapper = function createMigrationWrapper() {
  return {
    handleError: function handleError(error) {
      var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      var userMessage = arguments.length > 2 ? arguments[2] : undefined;
      return _UnifiedErrorHandlingService.unifiedErrorHandlingService.handleError(error, context, userMessage);
    },
    handleErrorWithRecovery: function handleErrorWithRecovery(error) {
      var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      return _UnifiedErrorHandlingService.unifiedErrorHandlingService.handleError(error, context);
    },
    reportError: function reportError(error) {
      var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      var severity = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'medium';
      return _UnifiedErrorHandlingService.unifiedErrorHandlingService.handleError(error, Object.assign({}, context, {
        severity: severity
      }));
    },
    reportErrorWithBreadcrumbs: function reportErrorWithBreadcrumbs(error) {
      var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      return _UnifiedErrorHandlingService.unifiedErrorHandlingService.handleError(error, context);
    },
    handleRuntimeError: function handleRuntimeError(error) {
      var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      return _UnifiedErrorHandlingService.unifiedErrorHandlingService.handleError(error, context);
    }
  };
};
unifiedErrorHandlingService.initialize().catch(function (error) {
  console.error('Failed to initialize unified error handling service:', error);
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfVW5pZmllZEVycm9ySGFuZGxpbmdTZXJ2aWNlIiwicmVxdWlyZSIsIl9Vc2VyRmVlZGJhY2tTZXJ2aWNlIiwiX0FuYWx5dGljc0ludGVncmF0aW9uU2VydmljZSIsIl90eXBlcyIsIl9ob29rcyIsIl9VbmlmaWVkRXJyb3JCb3VuZGFyeSIsIl9taWdyYXRpb25IZWxwZXIiLCJfdW5pZmllZEVycm9ySGFuZGxpbmdTZXJ2aWNlIiwiY29uc29sZSIsImVycm9yIiwiRXJyb3IiLCJ1bmlmaWVkRXJyb3JIYW5kbGluZ1NlcnZpY2UiLCJleHBvcnRzIiwiX2RlZmF1bHQiLCJkZWZhdWx0IiwiaGFuZGxlRXJyb3IiLCJjb250ZXh0IiwiaGFuZGxlTmV0d29ya0Vycm9yIiwiaGFuZGxlQXV0aEVycm9yIiwiaGFuZGxlVmFsaWRhdGlvbkVycm9yIiwiaGFuZGxlV2ViU29ja2V0RXJyb3IiLCJhZGRFcnJvckxpc3RlbmVyIiwibGlzdGVuZXIiLCJhZGRSZWNvdmVyeVN0cmF0ZWd5Iiwic3RyYXRlZ3kiLCJnZXRFcnJvck1ldHJpY3MiLCJnZXRNZXRyaWNzIiwiY2xlYXJFcnJvclF1ZXVlIiwidXBkYXRlRXJyb3JDb25maWciLCJjb25maWciLCJ1cGRhdGVDb25maWciLCJnZXRNb25pdG9yaW5nTWV0cmljcyIsImdldFN5c3RlbUhlYWx0aCIsImdldEVycm9yVHJlbmRzIiwiZGF5cyIsImV4cG9ydEVycm9yRGF0YSIsImZvcm1hdCIsImNsZWFyTW9uaXRvcmluZ0hpc3RvcnkiLCJjcmVhdGVNaWdyYXRpb25XcmFwcGVyIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwidW5kZWZpbmVkIiwidXNlck1lc3NhZ2UiLCJoYW5kbGVFcnJvcldpdGhSZWNvdmVyeSIsInJlcG9ydEVycm9yIiwic2V2ZXJpdHkiLCJPYmplY3QiLCJhc3NpZ24iLCJyZXBvcnRFcnJvcldpdGhCcmVhZGNydW1icyIsImhhbmRsZVJ1bnRpbWVFcnJvciIsImluaXRpYWxpemUiLCJjYXRjaCJdLCJzb3VyY2VzIjpbImluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogVW5pZmllZCBFcnJvciBIYW5kbGluZyBTeXN0ZW0gLSBNYWluIEV4cG9ydFxuICogXG4gKiBDZW50cmFsIGV4cG9ydCBwb2ludCBmb3IgdGhlIHVuaWZpZWQgZXJyb3IgaGFuZGxpbmcgc3lzdGVtLlxuICogVGhpcyByZXBsYWNlcyBhbGwgZXhpc3RpbmcgZXJyb3IgaGFuZGxpbmcgc2VydmljZXMgYW5kIHByb3ZpZGVzXG4gKiBhIHNpbmdsZSwgY29uc2lzdGVudCBBUEkgZm9yIGVycm9yIGhhbmRsaW5nIGFjcm9zcyB0aGUgYXBwbGljYXRpb24uXG4gKiBcbiAqIEB2ZXJzaW9uIDIuMC4wXG4gKiBAYXV0aG9yIFZpZXJsYSBEZXZlbG9wbWVudCBUZWFtXG4gKi9cblxuLy8gQ29yZSBTZXJ2aWNlIC0gSW1wb3J0IGFuZCByZS1leHBvcnQgdG8gYXZvaWQgY2lyY3VsYXIgZGVwZW5kZW5jaWVzXG5pbXBvcnQgeyB1bmlmaWVkRXJyb3JIYW5kbGluZ1NlcnZpY2UgYXMgX3VuaWZpZWRFcnJvckhhbmRsaW5nU2VydmljZSB9IGZyb20gJy4vVW5pZmllZEVycm9ySGFuZGxpbmdTZXJ2aWNlJztcblxuLy8gQWRkIHJ1bnRpbWUgdmFsaWRhdGlvbiBmb3IgSGVybWVzIGNvbXBhdGliaWxpdHlcbmlmICghX3VuaWZpZWRFcnJvckhhbmRsaW5nU2VydmljZSkge1xuICBjb25zb2xlLmVycm9yKCdbVW5pZmllZEVycm9ySGFuZGxpbmddIFNlcnZpY2UgaW5zdGFuY2UgaXMgdW5kZWZpbmVkIC0gdGhpcyBpbmRpY2F0ZXMgYSBtb2R1bGUgbG9hZGluZyBpc3N1ZScpO1xuICB0aHJvdyBuZXcgRXJyb3IoJ1VuaWZpZWRFcnJvckhhbmRsaW5nU2VydmljZSBmYWlsZWQgdG8gaW5pdGlhbGl6ZSBwcm9wZXJseScpO1xufVxuXG5leHBvcnQgY29uc3QgdW5pZmllZEVycm9ySGFuZGxpbmdTZXJ2aWNlID0gX3VuaWZpZWRFcnJvckhhbmRsaW5nU2VydmljZTtcbmV4cG9ydCBkZWZhdWx0IHVuaWZpZWRFcnJvckhhbmRsaW5nU2VydmljZTtcblxuLy8gU3ViLXNlcnZpY2VzXG5leHBvcnQge1xuICB1c2VyRmVlZGJhY2tTZXJ2aWNlXG59IGZyb20gJy4vVXNlckZlZWRiYWNrU2VydmljZSc7XG5cbmV4cG9ydCB7XG4gIGFuYWx5dGljc0ludGVncmF0aW9uU2VydmljZVxufSBmcm9tICcuL0FuYWx5dGljc0ludGVncmF0aW9uU2VydmljZSc7XG5cbi8vIFR5cGVzXG5leHBvcnQgdHlwZSB7XG4gIEVycm9yVHlwZSxcbiAgRXJyb3JTZXZlcml0eSxcbiAgRXJyb3JDYXRlZ29yeSxcbiAgRXJyb3JDb250ZXh0LFxuICBFcnJvckJyZWFkY3J1bWIsXG4gIFJlY292ZXJ5U3RyYXRlZ3ksXG4gIFVzZXJGZWVkYmFja0NvbmZpZyxcbiAgRXJyb3JSZXBvcnQsXG4gIFVuaWZpZWRFcnJvckhhbmRsaW5nQ29uZmlnLFxuICBFcnJvckxpc3RlbmVyLFxuICBFcnJvck1ldHJpY3Ncbn0gZnJvbSAnLi90eXBlcyc7XG5cbmV4cG9ydCB7IFxuICBVbmlmaWVkRXJyb3IsXG4gIEVycm9yVHlwZSxcbiAgRXJyb3JTZXZlcml0eSxcbiAgRXJyb3JDYXRlZ29yeVxufSBmcm9tICcuL3R5cGVzJztcblxuLy8gSG9va3NcbmV4cG9ydCB7XG4gIHVzZVVuaWZpZWRFcnJvckhhbmRsaW5nLFxuICB1c2VFcnJvck1ldHJpY3MsXG4gIHVzZUdsb2JhbEVycm9yTGlzdGVuZXIsXG4gIHVzZUVycm9yQm91bmRhcnksXG4gIHVzZUFwaUVycm9ySGFuZGxpbmcsXG4gIHVzZUZvcm1FcnJvckhhbmRsaW5nLFxuICB1c2VXZWJTb2NrZXRFcnJvckhhbmRsaW5nXG59IGZyb20gJy4vaG9va3MnO1xuXG5leHBvcnQgdHlwZSB7XG4gIFVzZVVuaWZpZWRFcnJvckhhbmRsaW5nT3B0aW9ucyxcbiAgVXNlVW5pZmllZEVycm9ySGFuZGxpbmdSZXN1bHRcbn0gZnJvbSAnLi9ob29rcyc7XG5cbi8vIENvbXBvbmVudHNcbmV4cG9ydCB7XG4gIFVuaWZpZWRFcnJvckJvdW5kYXJ5LFxuICBTY3JlZW5FcnJvckJvdW5kYXJ5LFxuICBGZWF0dXJlRXJyb3JCb3VuZGFyeSxcbiAgQ29tcG9uZW50RXJyb3JCb3VuZGFyeVxufSBmcm9tICcuL1VuaWZpZWRFcnJvckJvdW5kYXJ5JztcblxuLy8gQ29udmVuaWVuY2UgRnVuY3Rpb25zXG5leHBvcnQgY29uc3QgaGFuZGxlRXJyb3IgPSAoXG4gIGVycm9yOiBFcnJvciB8IHN0cmluZyxcbiAgY29udGV4dD86IFBhcnRpYWw8RXJyb3JDb250ZXh0PlxuKSA9PiBfdW5pZmllZEVycm9ySGFuZGxpbmdTZXJ2aWNlLmhhbmRsZUVycm9yKGVycm9yLCBjb250ZXh0KTtcblxuZXhwb3J0IGNvbnN0IGhhbmRsZU5ldHdvcmtFcnJvciA9IChcbiAgZXJyb3I6IEVycm9yLFxuICBjb250ZXh0PzogUGFydGlhbDxFcnJvckNvbnRleHQ+XG4pID0+IF91bmlmaWVkRXJyb3JIYW5kbGluZ1NlcnZpY2UuaGFuZGxlTmV0d29ya0Vycm9yKGVycm9yLCBjb250ZXh0KTtcblxuZXhwb3J0IGNvbnN0IGhhbmRsZUF1dGhFcnJvciA9IChcbiAgZXJyb3I6IEVycm9yLFxuICBjb250ZXh0PzogUGFydGlhbDxFcnJvckNvbnRleHQ+XG4pID0+IF91bmlmaWVkRXJyb3JIYW5kbGluZ1NlcnZpY2UuaGFuZGxlQXV0aEVycm9yKGVycm9yLCBjb250ZXh0KTtcblxuZXhwb3J0IGNvbnN0IGhhbmRsZVZhbGlkYXRpb25FcnJvciA9IChcbiAgZXJyb3I6IEVycm9yLFxuICBjb250ZXh0PzogUGFydGlhbDxFcnJvckNvbnRleHQ+XG4pID0+IF91bmlmaWVkRXJyb3JIYW5kbGluZ1NlcnZpY2UuaGFuZGxlVmFsaWRhdGlvbkVycm9yKGVycm9yLCBjb250ZXh0KTtcblxuZXhwb3J0IGNvbnN0IGhhbmRsZVdlYlNvY2tldEVycm9yID0gKFxuICBlcnJvcjogRXJyb3IsXG4gIGNvbnRleHQ/OiBQYXJ0aWFsPEVycm9yQ29udGV4dD5cbikgPT4gX3VuaWZpZWRFcnJvckhhbmRsaW5nU2VydmljZS5oYW5kbGVXZWJTb2NrZXRFcnJvcihlcnJvciwgY29udGV4dCk7XG5cbmV4cG9ydCBjb25zdCBhZGRFcnJvckxpc3RlbmVyID0gKGxpc3RlbmVyOiBFcnJvckxpc3RlbmVyKSA9PlxuICBfdW5pZmllZEVycm9ySGFuZGxpbmdTZXJ2aWNlLmFkZEVycm9yTGlzdGVuZXIobGlzdGVuZXIpO1xuXG5leHBvcnQgY29uc3QgYWRkUmVjb3ZlcnlTdHJhdGVneSA9IChzdHJhdGVneTogUmVjb3ZlcnlTdHJhdGVneSkgPT5cbiAgX3VuaWZpZWRFcnJvckhhbmRsaW5nU2VydmljZS5hZGRSZWNvdmVyeVN0cmF0ZWd5KHN0cmF0ZWd5KTtcblxuZXhwb3J0IGNvbnN0IGdldEVycm9yTWV0cmljcyA9ICgpID0+XG4gIF91bmlmaWVkRXJyb3JIYW5kbGluZ1NlcnZpY2UuZ2V0TWV0cmljcygpO1xuXG5leHBvcnQgY29uc3QgY2xlYXJFcnJvclF1ZXVlID0gKCkgPT5cbiAgX3VuaWZpZWRFcnJvckhhbmRsaW5nU2VydmljZS5jbGVhckVycm9yUXVldWUoKTtcblxuZXhwb3J0IGNvbnN0IHVwZGF0ZUVycm9yQ29uZmlnID0gKGNvbmZpZzogUGFydGlhbDxVbmlmaWVkRXJyb3JIYW5kbGluZ0NvbmZpZz4pID0+XG4gIF91bmlmaWVkRXJyb3JIYW5kbGluZ1NlcnZpY2UudXBkYXRlQ29uZmlnKGNvbmZpZyk7XG5cbi8vIEVycm9yIE1vbml0b3JpbmcgRnVuY3Rpb25zXG5leHBvcnQgY29uc3QgZ2V0TW9uaXRvcmluZ01ldHJpY3MgPSAoKSA9PlxuICBfdW5pZmllZEVycm9ySGFuZGxpbmdTZXJ2aWNlLmdldE1vbml0b3JpbmdNZXRyaWNzKCk7XG5cbmV4cG9ydCBjb25zdCBnZXRTeXN0ZW1IZWFsdGggPSAoKSA9PlxuICBfdW5pZmllZEVycm9ySGFuZGxpbmdTZXJ2aWNlLmdldFN5c3RlbUhlYWx0aCgpO1xuXG5leHBvcnQgY29uc3QgZ2V0RXJyb3JUcmVuZHMgPSAoZGF5cz86IG51bWJlcikgPT5cbiAgX3VuaWZpZWRFcnJvckhhbmRsaW5nU2VydmljZS5nZXRFcnJvclRyZW5kcyhkYXlzKTtcblxuZXhwb3J0IGNvbnN0IGV4cG9ydEVycm9yRGF0YSA9IChmb3JtYXQ/OiAnanNvbicgfCAnY3N2JykgPT5cbiAgX3VuaWZpZWRFcnJvckhhbmRsaW5nU2VydmljZS5leHBvcnRFcnJvckRhdGEoZm9ybWF0KTtcblxuZXhwb3J0IGNvbnN0IGNsZWFyTW9uaXRvcmluZ0hpc3RvcnkgPSAoKSA9PlxuICBfdW5pZmllZEVycm9ySGFuZGxpbmdTZXJ2aWNlLmNsZWFyTW9uaXRvcmluZ0hpc3RvcnkoKTtcblxuLy8gTWlncmF0aW9uIEhlbHBlcnNcbmV4cG9ydCB7XG4gIG1pZ3JhdGlvblV0aWxzLFxuICBsZWdhY3lFcnJvckhhbmRsaW5nU2VydmljZSxcbiAgbGVnYWN5RW5oYW5jZWRFcnJvckhhbmRsaW5nU2VydmljZSxcbiAgbGVnYWN5RXJyb3JNb25pdG9yaW5nU2VydmljZSxcbiAgbGVnYWN5RXJyb3JSZXBvcnRpbmdTZXJ2aWNlLFxuICBsZWdhY3lSdW50aW1lRXJyb3JIYW5kbGVyXG59IGZyb20gJy4vbWlncmF0aW9uSGVscGVyJztcbi8qKlxuICogTWlncmF0aW9uIGhlbHBlciB0byByZXBsYWNlIGV4aXN0aW5nIGVycm9yIGhhbmRsaW5nIHNlcnZpY2VzXG4gKiBUaGlzIHByb3ZpZGVzIGJhY2t3YXJkIGNvbXBhdGliaWxpdHkgd2hpbGUgdHJhbnNpdGlvbmluZyB0byB0aGUgdW5pZmllZCBzeXN0ZW1cbiAqL1xuZXhwb3J0IGNvbnN0IGNyZWF0ZU1pZ3JhdGlvbldyYXBwZXIgPSAoKSA9PiB7XG4gIHJldHVybiB7XG4gICAgLy8gTGVnYWN5IGVycm9ySGFuZGxpbmdTZXJ2aWNlIGNvbXBhdGliaWxpdHlcbiAgICBoYW5kbGVFcnJvcjogKGVycm9yOiBhbnksIGNvbnRleHQ6IGFueSA9IHt9LCB1c2VyTWVzc2FnZT86IHN0cmluZykgPT5cbiAgICAgIF91bmlmaWVkRXJyb3JIYW5kbGluZ1NlcnZpY2UuaGFuZGxlRXJyb3IoZXJyb3IsIGNvbnRleHQsIHVzZXJNZXNzYWdlKSxcblxuICAgIC8vIExlZ2FjeSBlbmhhbmNlZEVycm9ySGFuZGxpbmdTZXJ2aWNlIGNvbXBhdGliaWxpdHlcbiAgICBoYW5kbGVFcnJvcldpdGhSZWNvdmVyeTogKGVycm9yOiBhbnksIGNvbnRleHQ6IGFueSA9IHt9KSA9PlxuICAgICAgX3VuaWZpZWRFcnJvckhhbmRsaW5nU2VydmljZS5oYW5kbGVFcnJvcihlcnJvciwgY29udGV4dCksXG5cbiAgICAvLyBMZWdhY3kgZXJyb3JNb25pdG9yaW5nU2VydmljZSBjb21wYXRpYmlsaXR5XG4gICAgcmVwb3J0RXJyb3I6IChlcnJvcjogYW55LCBjb250ZXh0OiBhbnkgPSB7fSwgc2V2ZXJpdHk6IGFueSA9ICdtZWRpdW0nKSA9PlxuICAgICAgX3VuaWZpZWRFcnJvckhhbmRsaW5nU2VydmljZS5oYW5kbGVFcnJvcihlcnJvciwgeyAuLi5jb250ZXh0LCBzZXZlcml0eSB9KSxcblxuICAgIC8vIExlZ2FjeSBFcnJvclJlcG9ydGluZ1NlcnZpY2UgY29tcGF0aWJpbGl0eVxuICAgIHJlcG9ydEVycm9yV2l0aEJyZWFkY3J1bWJzOiAoZXJyb3I6IGFueSwgY29udGV4dDogYW55ID0ge30pID0+XG4gICAgICBfdW5pZmllZEVycm9ySGFuZGxpbmdTZXJ2aWNlLmhhbmRsZUVycm9yKGVycm9yLCBjb250ZXh0KSxcblxuICAgIC8vIExlZ2FjeSBydW50aW1lRXJyb3JIYW5kbGVyIGNvbXBhdGliaWxpdHlcbiAgICBoYW5kbGVSdW50aW1lRXJyb3I6IChlcnJvcjogYW55LCBjb250ZXh0OiBhbnkgPSB7fSkgPT5cbiAgICAgIF91bmlmaWVkRXJyb3JIYW5kbGluZ1NlcnZpY2UuaGFuZGxlRXJyb3IoZXJyb3IsIGNvbnRleHQpXG4gIH07XG59O1xuXG4vLyBJbml0aWFsaXplIHNlcnZpY2Ugb24gaW1wb3J0XG51bmlmaWVkRXJyb3JIYW5kbGluZ1NlcnZpY2UuaW5pdGlhbGl6ZSgpLmNhdGNoKGVycm9yID0+IHtcbiAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGluaXRpYWxpemUgdW5pZmllZCBlcnJvciBoYW5kbGluZyBzZXJ2aWNlOicsIGVycm9yKTtcbn0pO1xuXG4vKipcbiAqIFVzYWdlIEV4YW1wbGVzOlxuICogXG4gKiAvLyBCYXNpYyBlcnJvciBoYW5kbGluZ1xuICogaW1wb3J0IHsgaGFuZGxlRXJyb3IgfSBmcm9tICcuL3NlcnZpY2VzL3VuaWZpZWRFcnJvckhhbmRsaW5nJztcbiAqIFxuICogdHJ5IHtcbiAqICAgYXdhaXQgc29tZU9wZXJhdGlvbigpO1xuICogfSBjYXRjaCAoZXJyb3IpIHtcbiAqICAgYXdhaXQgaGFuZGxlRXJyb3IoZXJyb3IsIHsgY29tcG9uZW50OiAnTXlDb21wb25lbnQnLCBhY3Rpb246ICdzb21lT3BlcmF0aW9uJyB9KTtcbiAqIH1cbiAqIFxuICogLy8gVXNpbmcgaG9va3MgaW4gY29tcG9uZW50c1xuICogaW1wb3J0IHsgdXNlVW5pZmllZEVycm9ySGFuZGxpbmcgfSBmcm9tICcuL3NlcnZpY2VzL3VuaWZpZWRFcnJvckhhbmRsaW5nJztcbiAqIFxuICogY29uc3QgTXlDb21wb25lbnQgPSAoKSA9PiB7XG4gKiAgIGNvbnN0IHsgaGFuZGxlRXJyb3IsIHdpdGhFcnJvckhhbmRsaW5nIH0gPSB1c2VVbmlmaWVkRXJyb3JIYW5kbGluZyh7XG4gKiAgICAgY29tcG9uZW50OiAnTXlDb21wb25lbnQnXG4gKiAgIH0pO1xuICogXG4gKiAgIGNvbnN0IGxvYWREYXRhID0gYXN5bmMgKCkgPT4ge1xuICogICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHdpdGhFcnJvckhhbmRsaW5nKFxuICogICAgICAgKCkgPT4gYXBpQ2xpZW50LmdldERhdGEoKSxcbiAqICAgICAgIHsgYWN0aW9uOiAnbG9hZERhdGEnIH1cbiAqICAgICApO1xuICogICAgIFxuICogICAgIGlmIChkYXRhKSB7XG4gKiAgICAgICBzZXREYXRhKGRhdGEpO1xuICogICAgIH1cbiAqICAgfTtcbiAqIFxuICogICByZXR1cm4gPGRpdj4uLi48L2Rpdj47XG4gKiB9O1xuICogXG4gKiAvLyBVc2luZyBlcnJvciBib3VuZGFyaWVzXG4gKiBpbXBvcnQgeyBTY3JlZW5FcnJvckJvdW5kYXJ5IH0gZnJvbSAnLi9zZXJ2aWNlcy91bmlmaWVkRXJyb3JIYW5kbGluZyc7XG4gKiBcbiAqIGNvbnN0IE15U2NyZWVuID0gKCkgPT4gKFxuICogICA8U2NyZWVuRXJyb3JCb3VuZGFyeSBzY3JlZW5OYW1lPVwiTXlTY3JlZW5cIj5cbiAqICAgICA8TXlTY3JlZW5Db250ZW50IC8+XG4gKiAgIDwvU2NyZWVuRXJyb3JCb3VuZGFyeT5cbiAqICk7XG4gKiBcbiAqIC8vIEFQSSBlcnJvciBoYW5kbGluZ1xuICogaW1wb3J0IHsgdXNlQXBpRXJyb3JIYW5kbGluZyB9IGZyb20gJy4vc2VydmljZXMvdW5pZmllZEVycm9ySGFuZGxpbmcnO1xuICogXG4gKiBjb25zdCBhcGlTZXJ2aWNlID0ge1xuICogICBhc3luYyBnZXREYXRhKCkge1xuICogICAgIGNvbnN0IHsgaGFuZGxlQXBpRXJyb3IgfSA9IHVzZUFwaUVycm9ySGFuZGxpbmcoJ0RhdGFTZXJ2aWNlJyk7XG4gKiAgICAgXG4gKiAgICAgdHJ5IHtcbiAqICAgICAgIHJldHVybiBhd2FpdCBmZXRjaCgnL2FwaS9kYXRhJyk7XG4gKiAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAqICAgICAgIGF3YWl0IGhhbmRsZUFwaUVycm9yKGVycm9yLCAnL2FwaS9kYXRhJywgJ0dFVCcpO1xuICogICAgICAgdGhyb3cgZXJyb3I7XG4gKiAgICAgfVxuICogICB9XG4gKiB9O1xuICovXG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVlBLElBQUFBLDRCQUFBLEdBQUFDLE9BQUE7QUFZQSxJQUFBQyxvQkFBQSxHQUFBRCxPQUFBO0FBSUEsSUFBQUUsNEJBQUEsR0FBQUYsT0FBQTtBQW1CQSxJQUFBRyxNQUFBLEdBQUFILE9BQUE7QUFRQSxJQUFBSSxNQUFBLEdBQUFKLE9BQUE7QUFnQkEsSUFBQUsscUJBQUEsR0FBQUwsT0FBQTtBQWlFQSxJQUFBTSxnQkFBQSxHQUFBTixPQUFBO0FBekhBLElBQUksQ0FBQ08sd0RBQTRCLEVBQUU7RUFDakNDLE9BQU8sQ0FBQ0MsS0FBSyxDQUFDLDhGQUE4RixDQUFDO0VBQzdHLE1BQU0sSUFBSUMsS0FBSyxDQUFDLDJEQUEyRCxDQUFDO0FBQzlFO0FBRU8sSUFBTUMsMkJBQTJCLEdBQUFDLE9BQUEsQ0FBQUQsMkJBQUEsR0FBR0osd0RBQTRCO0FBQUMsSUFBQU0sUUFBQSxHQUFBRCxPQUFBLENBQUFFLE9BQUEsR0FDekRILDJCQUEyQjtBQTBEbkMsSUFBTUksV0FBVyxHQUFBSCxPQUFBLENBQUFHLFdBQUEsR0FBRyxTQUFkQSxXQUFXQSxDQUN0Qk4sS0FBcUIsRUFDckJPLE9BQStCO0VBQUEsT0FDNUJULHdEQUE0QixDQUFDUSxXQUFXLENBQUNOLEtBQUssRUFBRU8sT0FBTyxDQUFDO0FBQUE7QUFFdEQsSUFBTUMsa0JBQWtCLEdBQUFMLE9BQUEsQ0FBQUssa0JBQUEsR0FBRyxTQUFyQkEsa0JBQWtCQSxDQUM3QlIsS0FBWSxFQUNaTyxPQUErQjtFQUFBLE9BQzVCVCx3REFBNEIsQ0FBQ1Usa0JBQWtCLENBQUNSLEtBQUssRUFBRU8sT0FBTyxDQUFDO0FBQUE7QUFFN0QsSUFBTUUsZUFBZSxHQUFBTixPQUFBLENBQUFNLGVBQUEsR0FBRyxTQUFsQkEsZUFBZUEsQ0FDMUJULEtBQVksRUFDWk8sT0FBK0I7RUFBQSxPQUM1QlQsd0RBQTRCLENBQUNXLGVBQWUsQ0FBQ1QsS0FBSyxFQUFFTyxPQUFPLENBQUM7QUFBQTtBQUUxRCxJQUFNRyxxQkFBcUIsR0FBQVAsT0FBQSxDQUFBTyxxQkFBQSxHQUFHLFNBQXhCQSxxQkFBcUJBLENBQ2hDVixLQUFZLEVBQ1pPLE9BQStCO0VBQUEsT0FDNUJULHdEQUE0QixDQUFDWSxxQkFBcUIsQ0FBQ1YsS0FBSyxFQUFFTyxPQUFPLENBQUM7QUFBQTtBQUVoRSxJQUFNSSxvQkFBb0IsR0FBQVIsT0FBQSxDQUFBUSxvQkFBQSxHQUFHLFNBQXZCQSxvQkFBb0JBLENBQy9CWCxLQUFZLEVBQ1pPLE9BQStCO0VBQUEsT0FDNUJULHdEQUE0QixDQUFDYSxvQkFBb0IsQ0FBQ1gsS0FBSyxFQUFFTyxPQUFPLENBQUM7QUFBQTtBQUUvRCxJQUFNSyxnQkFBZ0IsR0FBQVQsT0FBQSxDQUFBUyxnQkFBQSxHQUFHLFNBQW5CQSxnQkFBZ0JBLENBQUlDLFFBQXVCO0VBQUEsT0FDdERmLHdEQUE0QixDQUFDYyxnQkFBZ0IsQ0FBQ0MsUUFBUSxDQUFDO0FBQUE7QUFFbEQsSUFBTUMsbUJBQW1CLEdBQUFYLE9BQUEsQ0FBQVcsbUJBQUEsR0FBRyxTQUF0QkEsbUJBQW1CQSxDQUFJQyxRQUEwQjtFQUFBLE9BQzVEakIsd0RBQTRCLENBQUNnQixtQkFBbUIsQ0FBQ0MsUUFBUSxDQUFDO0FBQUE7QUFFckQsSUFBTUMsZUFBZSxHQUFBYixPQUFBLENBQUFhLGVBQUEsR0FBRyxTQUFsQkEsZUFBZUEsQ0FBQTtFQUFBLE9BQzFCbEIsd0RBQTRCLENBQUNtQixVQUFVLENBQUMsQ0FBQztBQUFBO0FBRXBDLElBQU1DLGVBQWUsR0FBQWYsT0FBQSxDQUFBZSxlQUFBLEdBQUcsU0FBbEJBLGVBQWVBLENBQUE7RUFBQSxPQUMxQnBCLHdEQUE0QixDQUFDb0IsZUFBZSxDQUFDLENBQUM7QUFBQTtBQUV6QyxJQUFNQyxpQkFBaUIsR0FBQWhCLE9BQUEsQ0FBQWdCLGlCQUFBLEdBQUcsU0FBcEJBLGlCQUFpQkEsQ0FBSUMsTUFBMkM7RUFBQSxPQUMzRXRCLHdEQUE0QixDQUFDdUIsWUFBWSxDQUFDRCxNQUFNLENBQUM7QUFBQTtBQUc1QyxJQUFNRSxvQkFBb0IsR0FBQW5CLE9BQUEsQ0FBQW1CLG9CQUFBLEdBQUcsU0FBdkJBLG9CQUFvQkEsQ0FBQTtFQUFBLE9BQy9CeEIsd0RBQTRCLENBQUN3QixvQkFBb0IsQ0FBQyxDQUFDO0FBQUE7QUFFOUMsSUFBTUMsZUFBZSxHQUFBcEIsT0FBQSxDQUFBb0IsZUFBQSxHQUFHLFNBQWxCQSxlQUFlQSxDQUFBO0VBQUEsT0FDMUJ6Qix3REFBNEIsQ0FBQ3lCLGVBQWUsQ0FBQyxDQUFDO0FBQUE7QUFFekMsSUFBTUMsY0FBYyxHQUFBckIsT0FBQSxDQUFBcUIsY0FBQSxHQUFHLFNBQWpCQSxjQUFjQSxDQUFJQyxJQUFhO0VBQUEsT0FDMUMzQix3REFBNEIsQ0FBQzBCLGNBQWMsQ0FBQ0MsSUFBSSxDQUFDO0FBQUE7QUFFNUMsSUFBTUMsZUFBZSxHQUFBdkIsT0FBQSxDQUFBdUIsZUFBQSxHQUFHLFNBQWxCQSxlQUFlQSxDQUFJQyxNQUF1QjtFQUFBLE9BQ3JEN0Isd0RBQTRCLENBQUM0QixlQUFlLENBQUNDLE1BQU0sQ0FBQztBQUFBO0FBRS9DLElBQU1DLHNCQUFzQixHQUFBekIsT0FBQSxDQUFBeUIsc0JBQUEsR0FBRyxTQUF6QkEsc0JBQXNCQSxDQUFBO0VBQUEsT0FDakM5Qix3REFBNEIsQ0FBQzhCLHNCQUFzQixDQUFDLENBQUM7QUFBQTtBQWVoRCxJQUFNQyxzQkFBc0IsR0FBQTFCLE9BQUEsQ0FBQTBCLHNCQUFBLEdBQUcsU0FBekJBLHNCQUFzQkEsQ0FBQSxFQUFTO0VBQzFDLE9BQU87SUFFTHZCLFdBQVcsRUFBRSxTQUFiQSxXQUFXQSxDQUFHTixLQUFVO01BQUEsSUFBRU8sT0FBWSxHQUFBdUIsU0FBQSxDQUFBQyxNQUFBLFFBQUFELFNBQUEsUUFBQUUsU0FBQSxHQUFBRixTQUFBLE1BQUcsQ0FBQyxDQUFDO01BQUEsSUFBRUcsV0FBb0IsR0FBQUgsU0FBQSxDQUFBQyxNQUFBLE9BQUFELFNBQUEsTUFBQUUsU0FBQTtNQUFBLE9BQy9EbEMsd0RBQTRCLENBQUNRLFdBQVcsQ0FBQ04sS0FBSyxFQUFFTyxPQUFPLEVBQUUwQixXQUFXLENBQUM7SUFBQTtJQUd2RUMsdUJBQXVCLEVBQUUsU0FBekJBLHVCQUF1QkEsQ0FBR2xDLEtBQVU7TUFBQSxJQUFFTyxPQUFZLEdBQUF1QixTQUFBLENBQUFDLE1BQUEsUUFBQUQsU0FBQSxRQUFBRSxTQUFBLEdBQUFGLFNBQUEsTUFBRyxDQUFDLENBQUM7TUFBQSxPQUNyRGhDLHdEQUE0QixDQUFDUSxXQUFXLENBQUNOLEtBQUssRUFBRU8sT0FBTyxDQUFDO0lBQUE7SUFHMUQ0QixXQUFXLEVBQUUsU0FBYkEsV0FBV0EsQ0FBR25DLEtBQVU7TUFBQSxJQUFFTyxPQUFZLEdBQUF1QixTQUFBLENBQUFDLE1BQUEsUUFBQUQsU0FBQSxRQUFBRSxTQUFBLEdBQUFGLFNBQUEsTUFBRyxDQUFDLENBQUM7TUFBQSxJQUFFTSxRQUFhLEdBQUFOLFNBQUEsQ0FBQUMsTUFBQSxRQUFBRCxTQUFBLFFBQUFFLFNBQUEsR0FBQUYsU0FBQSxNQUFHLFFBQVE7TUFBQSxPQUNuRWhDLHdEQUE0QixDQUFDUSxXQUFXLENBQUNOLEtBQUssRUFBQXFDLE1BQUEsQ0FBQUMsTUFBQSxLQUFPL0IsT0FBTztRQUFFNkIsUUFBUSxFQUFSQTtNQUFRLEVBQUUsQ0FBQztJQUFBO0lBRzNFRywwQkFBMEIsRUFBRSxTQUE1QkEsMEJBQTBCQSxDQUFHdkMsS0FBVTtNQUFBLElBQUVPLE9BQVksR0FBQXVCLFNBQUEsQ0FBQUMsTUFBQSxRQUFBRCxTQUFBLFFBQUFFLFNBQUEsR0FBQUYsU0FBQSxNQUFHLENBQUMsQ0FBQztNQUFBLE9BQ3hEaEMsd0RBQTRCLENBQUNRLFdBQVcsQ0FBQ04sS0FBSyxFQUFFTyxPQUFPLENBQUM7SUFBQTtJQUcxRGlDLGtCQUFrQixFQUFFLFNBQXBCQSxrQkFBa0JBLENBQUd4QyxLQUFVO01BQUEsSUFBRU8sT0FBWSxHQUFBdUIsU0FBQSxDQUFBQyxNQUFBLFFBQUFELFNBQUEsUUFBQUUsU0FBQSxHQUFBRixTQUFBLE1BQUcsQ0FBQyxDQUFDO01BQUEsT0FDaERoQyx3REFBNEIsQ0FBQ1EsV0FBVyxDQUFDTixLQUFLLEVBQUVPLE9BQU8sQ0FBQztJQUFBO0VBQzVELENBQUM7QUFDSCxDQUFDO0FBR0RMLDJCQUEyQixDQUFDdUMsVUFBVSxDQUFDLENBQUMsQ0FBQ0MsS0FBSyxDQUFDLFVBQUExQyxLQUFLLEVBQUk7RUFDdERELE9BQU8sQ0FBQ0MsS0FBSyxDQUFDLHNEQUFzRCxFQUFFQSxLQUFLLENBQUM7QUFDOUUsQ0FBQyxDQUFDIiwiaWdub3JlTGlzdCI6W119