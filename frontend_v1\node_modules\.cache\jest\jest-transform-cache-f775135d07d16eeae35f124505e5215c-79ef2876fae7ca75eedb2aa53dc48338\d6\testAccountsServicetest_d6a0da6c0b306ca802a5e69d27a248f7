5f737f95f2cb458938d79aec78259bb4
_getJestObj().mock('@react-native-async-storage/async-storage', function () {
  return {
    getItem: jest.fn(),
    setItem: jest.fn(),
    multiRemove: jest.fn()
  };
});
_getJestObj().mock("../authService", function () {
  return {
    authService: {
      login: jest.fn()
    }
  };
});
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _asyncStorage = _interopRequireDefault(require("@react-native-async-storage/async-storage"));
var _testAccounts = require("../../config/testAccounts");
var _authService = require("../authService");
var _testAccountsService = require("../testAccountsService");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var mockAsyncStorage = _asyncStorage.default;
var mockAuthService = _authService.authService;
describe('TestAccountsService', function () {
  beforeEach(function () {
    jest.clearAllMocks();
    global.__DEV__ = true;
  });
  describe('Test Mode Management', function () {
    it('should return true for test mode in development', (0, _asyncToGenerator2.default)(function* () {
      mockAsyncStorage.getItem.mockResolvedValue('true');
      var isActive = yield _testAccountsService.testAccountsService.isTestModeActive();
      expect(isActive).toBe(true);
    }));
    it('should return false for test mode in production', (0, _asyncToGenerator2.default)(function* () {
      global.__DEV__ = false;
      var isActive = yield _testAccountsService.testAccountsService.isTestModeActive();
      expect(isActive).toBe(false);
    }));
    it('should set test mode enabled state', (0, _asyncToGenerator2.default)(function* () {
      yield _testAccountsService.testAccountsService.setTestMode(true);
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith('@vierla/test_mode_enabled', 'true');
    }));
    it('should not set test mode in production', (0, _asyncToGenerator2.default)(function* () {
      global.__DEV__ = false;
      yield _testAccountsService.testAccountsService.setTestMode(true);
      expect(mockAsyncStorage.setItem).not.toHaveBeenCalled();
    }));
  });
  describe('Account Retrieval', function () {
    it('should return all test accounts', function () {
      var accounts = _testAccountsService.testAccountsService.getAllTestAccounts();
      expect(accounts).toEqual(_testAccounts.ALL_TEST_ACCOUNTS);
      expect(accounts.length).toBeGreaterThan(0);
    });
    it('should return customer accounts only', function () {
      var customers = _testAccountsService.testAccountsService.getAccountsByRole('customer');
      expect(customers.length).toBeGreaterThan(0);
      customers.forEach(function (account) {
        expect(account.role).toBe('customer');
      });
    });
    it('should return service provider accounts only', function () {
      var providers = _testAccountsService.testAccountsService.getAccountsByRole('service_provider');
      expect(providers.length).toBeGreaterThan(0);
      providers.forEach(function (account) {
        expect(account.role).toBe('service_provider');
      });
    });
    it('should return providers by category', function () {
      var salonProviders = _testAccountsService.testAccountsService.getProvidersByCategory('Salon');
      expect(salonProviders.length).toBeGreaterThan(0);
      salonProviders.forEach(function (provider) {
        expect(provider.category).toBe('Salon');
        expect(provider.role).toBe('service_provider');
      });
    });
    it('should return random account', function () {
      var randomAccount = _testAccountsService.testAccountsService.getRandomAccount();
      expect(randomAccount).toBeDefined();
      expect(randomAccount.email).toBeDefined();
      expect(randomAccount.password).toBeDefined();
    });
    it('should return random account by role', function () {
      var randomCustomer = _testAccountsService.testAccountsService.getRandomAccount('customer');
      expect(randomCustomer.role).toBe('customer');
    });
    it('should find account by email', function () {
      var testEmail = _testAccounts.QUICK_LOGIN_ACCOUNTS.CUSTOMER.email;
      var account = _testAccountsService.testAccountsService.findAccountByEmail(testEmail);
      expect(account).toBeDefined();
      expect(account == null ? void 0 : account.email).toBe(testEmail);
    });
    it('should return undefined for non-existent email', function () {
      var account = _testAccountsService.testAccountsService.findAccountByEmail('<EMAIL>');
      expect(account).toBeUndefined();
    });
  });
  describe('Quick Access Functions', function () {
    it('should return quick login accounts', function () {
      var quickAccounts = _testAccountsService.testAccountsService.getQuickLoginAccounts();
      expect(quickAccounts).toEqual(_testAccounts.QUICK_LOGIN_ACCOUNTS);
      expect(quickAccounts.CUSTOMER).toBeDefined();
      expect(quickAccounts.SALON_PROVIDER).toBeDefined();
    });
    it('should return account statistics', function () {
      var stats = _testAccountsService.testAccountsService.getAccountsStats();
      expect(stats.totalAccounts).toBeGreaterThan(0);
      expect(stats.customerAccounts).toBeGreaterThan(0);
      expect(stats.providerAccounts).toBeGreaterThan(0);
      expect(stats.categoriesBreakdown).toBeDefined();
      expect(stats.citiesBreakdown).toBeDefined();
    });
  });
  describe('Login Functionality', function () {
    it('should successfully login with test account', (0, _asyncToGenerator2.default)(function* () {
      var mockAuthResponse = {
        access: 'mock-access-token',
        refresh: 'mock-refresh-token',
        user: {
          id: '1',
          email: '<EMAIL>',
          first_name: 'Test',
          last_name: 'User',
          role: 'customer',
          is_verified: true
        }
      };
      mockAuthService.login.mockResolvedValue(mockAuthResponse);
      mockAsyncStorage.setItem.mockResolvedValue();
      var testAccount = _testAccounts.QUICK_LOGIN_ACCOUNTS.CUSTOMER;
      var result = yield _testAccountsService.testAccountsService.loginWithTestAccount(testAccount);
      expect(result.success).toBe(true);
      expect(result.account).toEqual(testAccount);
      expect(result.authResponse).toEqual(mockAuthResponse);
      expect(mockAuthService.login).toHaveBeenCalledWith({
        email: testAccount.email,
        password: testAccount.password
      });
    }));
    it('should handle login failure', (0, _asyncToGenerator2.default)(function* () {
      mockAuthService.login.mockRejectedValue(new Error('Invalid credentials'));
      var testAccount = _testAccounts.QUICK_LOGIN_ACCOUNTS.CUSTOMER;
      var result = yield _testAccountsService.testAccountsService.loginWithTestAccount(testAccount);
      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid credentials');
      expect(result.account).toEqual(testAccount);
    }));
    it('should not login when test mode is disabled', (0, _asyncToGenerator2.default)(function* () {
      global.__DEV__ = false;
      var testAccount = _testAccounts.QUICK_LOGIN_ACCOUNTS.CUSTOMER;
      var result = yield _testAccountsService.testAccountsService.loginWithTestAccount(testAccount);
      expect(result.success).toBe(false);
      expect(result.error).toBe('Test mode is not enabled');
      expect(mockAuthService.login).not.toHaveBeenCalled();
    }));
    it('should perform quick login', (0, _asyncToGenerator2.default)(function* () {
      var mockAuthResponse = {
        access: 'mock-access-token',
        refresh: 'mock-refresh-token',
        user: {
          id: '1',
          email: '<EMAIL>',
          first_name: 'Test',
          last_name: 'User',
          role: 'customer',
          is_verified: true
        }
      };
      mockAuthService.login.mockResolvedValue(mockAuthResponse);
      mockAsyncStorage.setItem.mockResolvedValue();
      var result = yield _testAccountsService.testAccountsService.quickLogin('CUSTOMER');
      expect(result.success).toBe(true);
      expect(result.account).toEqual(_testAccounts.QUICK_LOGIN_ACCOUNTS.CUSTOMER);
    }));
    it('should login with random account', (0, _asyncToGenerator2.default)(function* () {
      var _result$account;
      var mockAuthResponse = {
        access: 'mock-access-token',
        refresh: 'mock-refresh-token',
        user: {
          id: '1',
          email: '<EMAIL>',
          first_name: 'Test',
          last_name: 'User',
          role: 'customer',
          is_verified: true
        }
      };
      mockAuthService.login.mockResolvedValue(mockAuthResponse);
      mockAsyncStorage.setItem.mockResolvedValue();
      var result = yield _testAccountsService.testAccountsService.loginWithRandomAccount('customer');
      expect(result.success).toBe(true);
      expect((_result$account = result.account) == null ? void 0 : _result$account.role).toBe('customer');
    }));
  });
  describe('Storage Management', function () {
    it('should store last test account', (0, _asyncToGenerator2.default)(function* () {
      var mockAuthResponse = {
        access: 'mock-access-token',
        refresh: 'mock-refresh-token',
        user: {
          id: '1',
          email: '<EMAIL>',
          first_name: 'Test',
          last_name: 'User',
          role: 'customer',
          is_verified: true
        }
      };
      mockAuthService.login.mockResolvedValue(mockAuthResponse);
      mockAsyncStorage.setItem.mockResolvedValue();
      var testAccount = _testAccounts.QUICK_LOGIN_ACCOUNTS.CUSTOMER;
      yield _testAccountsService.testAccountsService.loginWithTestAccount(testAccount);
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith('@vierla/last_test_account', JSON.stringify(testAccount));
    }));
    it('should retrieve last test account', (0, _asyncToGenerator2.default)(function* () {
      var testAccount = _testAccounts.QUICK_LOGIN_ACCOUNTS.CUSTOMER;
      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify(testAccount));
      var lastAccount = yield _testAccountsService.testAccountsService.getLastTestAccount();
      expect(lastAccount).toEqual(testAccount);
      expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('@vierla/last_test_account');
    }));
    it('should return null when no last account stored', (0, _asyncToGenerator2.default)(function* () {
      mockAsyncStorage.getItem.mockResolvedValue(null);
      var lastAccount = yield _testAccountsService.testAccountsService.getLastTestAccount();
      expect(lastAccount).toBeNull();
    }));
    it('should clear test account data', (0, _asyncToGenerator2.default)(function* () {
      mockAsyncStorage.multiRemove.mockResolvedValue();
      yield _testAccountsService.testAccountsService.clearTestAccountData();
      expect(mockAsyncStorage.multiRemove).toHaveBeenCalledWith(['@vierla/last_test_account', '@vierla/preferred_test_accounts']);
    }));
  });
  describe('Account Validation', function () {
    it('should validate correct test account credentials', function () {
      var testAccount = _testAccounts.QUICK_LOGIN_ACCOUNTS.CUSTOMER;
      var validatedAccount = _testAccountsService.testAccountsService.validateTestAccount(testAccount.email, testAccount.password);
      expect(validatedAccount).toEqual(testAccount);
    });
    it('should return null for invalid credentials', function () {
      var validatedAccount = _testAccountsService.testAccountsService.validateTestAccount('<EMAIL>', 'wrongpassword');
      expect(validatedAccount).toBeNull();
    });
    it('should return null for correct email but wrong password', function () {
      var testAccount = _testAccounts.QUICK_LOGIN_ACCOUNTS.CUSTOMER;
      var validatedAccount = _testAccountsService.testAccountsService.validateTestAccount(testAccount.email, 'wrongpassword');
      expect(validatedAccount).toBeNull();
    });
  });
  describe('Scenario-based Account Selection', function () {
    it('should return appropriate accounts for booking scenario', function () {
      var accounts = _testAccountsService.testAccountsService.getAccountsForScenario('booking');
      expect(accounts.customer.role).toBe('customer');
      expect(accounts.provider.role).toBe('service_provider');
      expect(accounts.provider.category).toBe('Barber');
    });
    it('should return appropriate accounts for messaging scenario', function () {
      var accounts = _testAccountsService.testAccountsService.getAccountsForScenario('messaging');
      expect(accounts.customer.role).toBe('customer');
      expect(accounts.provider.role).toBe('service_provider');
      expect(accounts.provider.category).toBe('Nail Services');
    });
    it('should return appropriate accounts for payments scenario', function () {
      var accounts = _testAccountsService.testAccountsService.getAccountsForScenario('payments');
      expect(accounts.customer.role).toBe('customer');
      expect(accounts.provider.role).toBe('service_provider');
      expect(accounts.provider.category).toBe('Lash Services');
    });
  });
  describe('Development Helpers', function () {
    it('should generate test account credentials for UI', function () {
      var credentials = _testAccountsService.testAccountsService.getTestAccountCredentials();
      expect(credentials.length).toBeGreaterThan(0);
      credentials.forEach(function (cred) {
        expect(cred.label).toBeDefined();
        expect(cred.email).toBeDefined();
        expect(cred.password).toBeDefined();
        expect(cred.role).toBeDefined();
      });
    });
    it('should log test accounts summary in development', function () {
      var consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      _testAccountsService.testAccountsService.logTestAccountsSummary();
      expect(consoleSpy).toHaveBeenCalledWith('🧪 Vierla Test Accounts Summary');
      consoleSpy.mockRestore();
    });
    it('should not log in production', function () {
      global.__DEV__ = false;
      var consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      _testAccountsService.testAccountsService.logTestAccountsSummary();
      expect(consoleSpy).not.toHaveBeenCalled();
      consoleSpy.mockRestore();
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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