{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_platformUtils", "_responsiveUtils", "_jsxRuntime", "SafeAreaWrapper", "exports", "_ref", "_forceInsets$top", "_forceInsets$bottom", "_forceInsets$left", "_forceInsets$right", "children", "style", "_ref$edges", "edges", "_ref$mode", "mode", "_ref$backgroundColor", "backgroundColor", "forceInsets", "testID", "_ref$respectNotch", "respectNotch", "_ref$respectGestures", "respectGestures", "_ref$statusBarStyle", "statusBarStyle", "statusBarBackgroundColor", "customTopPadding", "customBottomPadding", "insets", "getSafeAreaInsets", "getEnhancedTopInset", "undefined", "isIPhoneWithNotch", "hasDynamicIsland", "Math", "max", "top", "hasNotch", "getEnhancedBottomInset", "bottom", "isAndroidWithGestures", "finalInsets", "left", "right", "dynamicStyle", "for<PERSON>ach", "edge", "insetValue", "char<PERSON>t", "toUpperCase", "slice", "jsxs", "View", "styles", "container", "Platform", "OS", "jsx", "StatusBar", "barStyle", "translucent", "EnhancedSafeAreaView", "_ref2", "_ref2$edges", "_ref2$backgroundColor", "paddingStyle", "paddingTop", "includes", "paddingBottom", "paddingLeft", "paddingRight", "SafeAreaScreen", "_ref3", "_ref3$backgroundColor", "_ref3$includeStatusBa", "includeStatusBar", "_ref3$includeTabBar", "includeTabBar", "_ref3$statusBarStyle", "_ref3$respectNotch", "_ref3$respectGestures", "push", "screen", "SafeAreaModal", "_ref4", "_ref4$backgroundColor", "modal", "SafeAreaHeader", "_ref5", "_ref5$backgroundColor", "header", "useCustomSafeAreaInsets", "useSafeAreaTop", "getSafeAreaTop", "useSafeAreaBottom", "getSafeAreaBottom", "StyleSheet", "create", "flex", "Object", "assign", "safePlatformSelect", "ios", "borderTopLeftRadius", "borderTopRightRadius", "android", "default", "shadowColor", "shadowOffset", "width", "height", "shadowOpacity", "shadowRadius", "elevation"], "sources": ["SafeAreaWrapper.tsx"], "sourcesContent": ["/**\n * SafeAreaWrapper - Enhanced Safe Area Management\n *\n * Component Contract:\n * - Provides consistent safe area handling across iOS and Android\n * - Respects device notches, status bars, and navigation bars\n * - Offers flexible configuration for different screen types\n * - Integrates with responsive utilities for optimal spacing\n * - Supports custom edge configurations\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport React from 'react';\nimport { View, Platform, ViewStyle, StyleSheet, StatusBar } from 'react-native';\n\nimport { safePlatformSelect } from '../../utils/platformUtils';\nimport {\n  getSafeAreaInsets,\n  getSafeAreaTop,\n  getSafeAreaBottom,\n  isIPhoneWithNotch,\n  isAndroidWithGestures,\n  hasNotch,\n  hasDynamicIsland,\n} from '../../utils/responsiveUtils';\n\ninterface SafeAreaWrapperProps {\n  children: React.ReactNode;\n  style?: ViewStyle;\n  edges?: ('top' | 'bottom' | 'left' | 'right')[];\n  mode?: 'padding' | 'margin';\n  backgroundColor?: string;\n  forceInsets?: {\n    top?: number;\n    bottom?: number;\n    left?: number;\n    right?: number;\n  };\n  testID?: string;\n  // Enhanced device compatibility props\n  respectNotch?: boolean;\n  respectGestures?: boolean;\n  statusBarStyle?: 'light-content' | 'dark-content';\n  statusBarBackgroundColor?: string;\n  customTopPadding?: number;\n  customBottomPadding?: number;\n}\n\nexport const SafeAreaWrapper: React.FC<SafeAreaWrapperProps> = ({\n  children,\n  style,\n  edges = ['top', 'bottom'],\n  mode = 'padding',\n  backgroundColor = 'transparent',\n  forceInsets,\n  testID,\n  respectNotch = true,\n  respectGestures = true,\n  statusBarStyle = 'dark-content',\n  statusBarBackgroundColor,\n  customTopPadding,\n  customBottomPadding,\n}) => {\n  // Get safe area insets with enhanced device detection\n  const insets = getSafeAreaInsets();\n\n  // Enhanced device-specific calculations\n  const getEnhancedTopInset = () => {\n    if (customTopPadding !== undefined) return customTopPadding;\n\n    if (respectNotch && isIPhoneWithNotch()) {\n      // Ensure minimum padding for iPhone notch/Dynamic Island\n      if (hasDynamicIsland()) return Math.max(insets.top, 59);\n      if (hasNotch()) return Math.max(insets.top, 44);\n    }\n\n    return insets.top;\n  };\n\n  const getEnhancedBottomInset = () => {\n    if (customBottomPadding !== undefined) return customBottomPadding;\n\n    if (respectGestures) {\n      if (isIPhoneWithNotch()) return Math.max(insets.bottom, 34);\n      if (isAndroidWithGestures()) return Math.max(insets.bottom, 16);\n    }\n\n    return insets.bottom;\n  };\n\n  // Apply forced insets if provided, otherwise use enhanced calculations\n  const finalInsets = {\n    top: forceInsets?.top ?? getEnhancedTopInset(),\n    bottom: forceInsets?.bottom ?? getEnhancedBottomInset(),\n    left: forceInsets?.left ?? insets.left,\n    right: forceInsets?.right ?? insets.right,\n  };\n\n  // Create dynamic styles based on edges and mode\n  const dynamicStyle: ViewStyle = {};\n\n  edges.forEach(edge => {\n    const insetValue = finalInsets[edge];\n    if (insetValue > 0) {\n      if (mode === 'padding') {\n        dynamicStyle[\n          `padding${edge.charAt(0).toUpperCase() + edge.slice(1)}` as keyof ViewStyle\n        ] = insetValue;\n      } else {\n        dynamicStyle[\n          `margin${edge.charAt(0).toUpperCase() + edge.slice(1)}` as keyof ViewStyle\n        ] = insetValue;\n      }\n    }\n  });\n\n  return (\n    <View\n      style={[styles.container, { backgroundColor }, dynamicStyle, style]}\n      testID={testID}>\n      {Platform.OS !== 'web' && (\n        <StatusBar\n          barStyle={statusBarStyle}\n          backgroundColor={statusBarBackgroundColor || backgroundColor}\n          translucent={Platform.OS === 'android'}\n        />\n      )}\n      {children}\n    </View>\n  );\n};\n\n// Alternative component using native View with calculated safe areas\nexport const EnhancedSafeAreaView: React.FC<SafeAreaWrapperProps> = ({\n  children,\n  style,\n  edges = ['top', 'bottom'],\n  backgroundColor = 'transparent',\n  testID,\n}) => {\n  const insets = getSafeAreaInsets();\n\n  const paddingStyle = {\n    paddingTop: edges.includes('top') ? insets.top : 0,\n    paddingBottom: edges.includes('bottom') ? insets.bottom : 0,\n    paddingLeft: edges.includes('left') ? insets.left : 0,\n    paddingRight: edges.includes('right') ? insets.right : 0,\n  };\n\n  return (\n    <View\n      style={[styles.container, { backgroundColor }, paddingStyle, style]}\n      testID={testID}>\n      {children}\n    </View>\n  );\n};\n\n// Specialized components for common use cases\nexport const SafeAreaScreen: React.FC<{\n  children: React.ReactNode;\n  style?: ViewStyle;\n  backgroundColor?: string;\n  includeStatusBar?: boolean;\n  includeTabBar?: boolean;\n  statusBarStyle?: 'light-content' | 'dark-content';\n  respectNotch?: boolean;\n  respectGestures?: boolean;\n  testID?: string;\n}> = ({\n  children,\n  style,\n  backgroundColor = '#FFFFFF',\n  includeStatusBar = true,\n  includeTabBar = true,\n  statusBarStyle = 'dark-content',\n  respectNotch = true,\n  respectGestures = true,\n  testID,\n}) => {\n  const edges: ('top' | 'bottom' | 'left' | 'right')[] = [];\n\n  if (includeStatusBar) edges.push('top');\n  if (includeTabBar) edges.push('bottom');\n\n  return (\n    <SafeAreaWrapper\n      edges={edges}\n      backgroundColor={backgroundColor}\n      statusBarStyle={statusBarStyle}\n      respectNotch={respectNotch}\n      respectGestures={respectGestures}\n      style={[styles.screen, style]}\n      testID={testID}>\n      {children}\n    </SafeAreaWrapper>\n  );\n};\n\n// Modal safe area component\nexport const SafeAreaModal: React.FC<{\n  children: React.ReactNode;\n  style?: ViewStyle;\n  backgroundColor?: string;\n  testID?: string;\n}> = ({ children, style, backgroundColor = '#FFFFFF', testID }) => {\n  return (\n    <SafeAreaWrapper\n      edges={['top', 'bottom']}\n      backgroundColor={backgroundColor}\n      style={[styles.modal, style]}\n      testID={testID}>\n      {children}\n    </SafeAreaWrapper>\n  );\n};\n\n// Header safe area component\nexport const SafeAreaHeader: React.FC<{\n  children: React.ReactNode;\n  style?: ViewStyle;\n  backgroundColor?: string;\n  testID?: string;\n}> = ({ children, style, backgroundColor = '#FFFFFF', testID }) => {\n  return (\n    <SafeAreaWrapper\n      edges={['top']}\n      backgroundColor={backgroundColor}\n      style={[styles.header, style]}\n      testID={testID}>\n      {children}\n    </SafeAreaWrapper>\n  );\n};\n\n// Utility hooks for safe area values\nexport const useCustomSafeAreaInsets = () => {\n  return getSafeAreaInsets();\n};\n\nexport const useSafeAreaTop = () => {\n  return getSafeAreaTop();\n};\n\nexport const useSafeAreaBottom = () => {\n  return getSafeAreaBottom();\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n  screen: {\n    flex: 1,\n  },\n  modal: {\n    flex: 1,\n    ...safePlatformSelect({\n      ios: {\n        borderTopLeftRadius: 12,\n        borderTopRightRadius: 12,\n      },\n      android: {\n        borderTopLeftRadius: 8,\n        borderTopRightRadius: 8,\n      },\n      default: {},\n    }),\n  },\n  header: {\n    ...safePlatformSelect({\n      ios: {\n        shadowColor: '#000',\n        shadowOffset: { width: 0, height: 1 },\n        shadowOpacity: 0.1,\n        shadowRadius: 2,\n      },\n      android: {\n        elevation: 2,\n      },\n      default: {},\n    }),\n  },\n});\n"], "mappings": ";;;;;AAcA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,cAAA,GAAAF,OAAA;AACA,IAAAG,gBAAA,GAAAH,OAAA;AAQqC,IAAAI,WAAA,GAAAJ,OAAA;AAwB9B,IAAMK,eAA+C,GAAAC,OAAA,CAAAD,eAAA,GAAG,SAAlDA,eAA+CA,CAAAE,IAAA,EActD;EAAA,IAAAC,gBAAA,EAAAC,mBAAA,EAAAC,iBAAA,EAAAC,kBAAA;EAAA,IAbJC,QAAQ,GAAAL,IAAA,CAARK,QAAQ;IACRC,KAAK,GAAAN,IAAA,CAALM,KAAK;IAAAC,UAAA,GAAAP,IAAA,CACLQ,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAAA,UAAA;IAAAE,SAAA,GAAAT,IAAA,CACzBU,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG,SAAS,GAAAA,SAAA;IAAAE,oBAAA,GAAAX,IAAA,CAChBY,eAAe;IAAfA,eAAe,GAAAD,oBAAA,cAAG,aAAa,GAAAA,oBAAA;IAC/BE,WAAW,GAAAb,IAAA,CAAXa,WAAW;IACXC,MAAM,GAAAd,IAAA,CAANc,MAAM;IAAAC,iBAAA,GAAAf,IAAA,CACNgB,YAAY;IAAZA,YAAY,GAAAD,iBAAA,cAAG,IAAI,GAAAA,iBAAA;IAAAE,oBAAA,GAAAjB,IAAA,CACnBkB,eAAe;IAAfA,eAAe,GAAAD,oBAAA,cAAG,IAAI,GAAAA,oBAAA;IAAAE,mBAAA,GAAAnB,IAAA,CACtBoB,cAAc;IAAdA,cAAc,GAAAD,mBAAA,cAAG,cAAc,GAAAA,mBAAA;IAC/BE,wBAAwB,GAAArB,IAAA,CAAxBqB,wBAAwB;IACxBC,gBAAgB,GAAAtB,IAAA,CAAhBsB,gBAAgB;IAChBC,mBAAmB,GAAAvB,IAAA,CAAnBuB,mBAAmB;EAGnB,IAAMC,MAAM,GAAG,IAAAC,kCAAiB,EAAC,CAAC;EAGlC,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAAS;IAChC,IAAIJ,gBAAgB,KAAKK,SAAS,EAAE,OAAOL,gBAAgB;IAE3D,IAAIN,YAAY,IAAI,IAAAY,kCAAiB,EAAC,CAAC,EAAE;MAEvC,IAAI,IAAAC,iCAAgB,EAAC,CAAC,EAAE,OAAOC,IAAI,CAACC,GAAG,CAACP,MAAM,CAACQ,GAAG,EAAE,EAAE,CAAC;MACvD,IAAI,IAAAC,yBAAQ,EAAC,CAAC,EAAE,OAAOH,IAAI,CAACC,GAAG,CAACP,MAAM,CAACQ,GAAG,EAAE,EAAE,CAAC;IACjD;IAEA,OAAOR,MAAM,CAACQ,GAAG;EACnB,CAAC;EAED,IAAME,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAA,EAAS;IACnC,IAAIX,mBAAmB,KAAKI,SAAS,EAAE,OAAOJ,mBAAmB;IAEjE,IAAIL,eAAe,EAAE;MACnB,IAAI,IAAAU,kCAAiB,EAAC,CAAC,EAAE,OAAOE,IAAI,CAACC,GAAG,CAACP,MAAM,CAACW,MAAM,EAAE,EAAE,CAAC;MAC3D,IAAI,IAAAC,sCAAqB,EAAC,CAAC,EAAE,OAAON,IAAI,CAACC,GAAG,CAACP,MAAM,CAACW,MAAM,EAAE,EAAE,CAAC;IACjE;IAEA,OAAOX,MAAM,CAACW,MAAM;EACtB,CAAC;EAGD,IAAME,WAAW,GAAG;IAClBL,GAAG,GAAA/B,gBAAA,GAAEY,WAAW,oBAAXA,WAAW,CAAEmB,GAAG,YAAA/B,gBAAA,GAAIyB,mBAAmB,CAAC,CAAC;IAC9CS,MAAM,GAAAjC,mBAAA,GAAEW,WAAW,oBAAXA,WAAW,CAAEsB,MAAM,YAAAjC,mBAAA,GAAIgC,sBAAsB,CAAC,CAAC;IACvDI,IAAI,GAAAnC,iBAAA,GAAEU,WAAW,oBAAXA,WAAW,CAAEyB,IAAI,YAAAnC,iBAAA,GAAIqB,MAAM,CAACc,IAAI;IACtCC,KAAK,GAAAnC,kBAAA,GAAES,WAAW,oBAAXA,WAAW,CAAE0B,KAAK,YAAAnC,kBAAA,GAAIoB,MAAM,CAACe;EACtC,CAAC;EAGD,IAAMC,YAAuB,GAAG,CAAC,CAAC;EAElChC,KAAK,CAACiC,OAAO,CAAC,UAAAC,IAAI,EAAI;IACpB,IAAMC,UAAU,GAAGN,WAAW,CAACK,IAAI,CAAC;IACpC,IAAIC,UAAU,GAAG,CAAC,EAAE;MAClB,IAAIjC,IAAI,KAAK,SAAS,EAAE;QACtB8B,YAAY,CACV,UAAUE,IAAI,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGH,IAAI,CAACI,KAAK,CAAC,CAAC,CAAC,EAAE,CACzD,GAAGH,UAAU;MAChB,CAAC,MAAM;QACLH,YAAY,CACV,SAASE,IAAI,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGH,IAAI,CAACI,KAAK,CAAC,CAAC,CAAC,EAAE,CACxD,GAAGH,UAAU;MAChB;IACF;EACF,CAAC,CAAC;EAEF,OACE,IAAA9C,WAAA,CAAAkD,IAAA,EAACrD,YAAA,CAAAsD,IAAI;IACH1C,KAAK,EAAE,CAAC2C,MAAM,CAACC,SAAS,EAAE;MAAEtC,eAAe,EAAfA;IAAgB,CAAC,EAAE4B,YAAY,EAAElC,KAAK,CAAE;IACpEQ,MAAM,EAAEA,MAAO;IAAAT,QAAA,GACd8C,qBAAQ,CAACC,EAAE,KAAK,KAAK,IACpB,IAAAvD,WAAA,CAAAwD,GAAA,EAAC3D,YAAA,CAAA4D,SAAS;MACRC,QAAQ,EAAEnC,cAAe;MACzBR,eAAe,EAAES,wBAAwB,IAAIT,eAAgB;MAC7D4C,WAAW,EAAEL,qBAAQ,CAACC,EAAE,KAAK;IAAU,CACxC,CACF,EACA/C,QAAQ;EAAA,CACL,CAAC;AAEX,CAAC;AAGM,IAAMoD,oBAAoD,GAAA1D,OAAA,CAAA0D,oBAAA,GAAG,SAAvDA,oBAAoDA,CAAAC,KAAA,EAM3D;EAAA,IALJrD,QAAQ,GAAAqD,KAAA,CAARrD,QAAQ;IACRC,KAAK,GAAAoD,KAAA,CAALpD,KAAK;IAAAqD,WAAA,GAAAD,KAAA,CACLlD,KAAK;IAALA,KAAK,GAAAmD,WAAA,cAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAAA,WAAA;IAAAC,qBAAA,GAAAF,KAAA,CACzB9C,eAAe;IAAfA,eAAe,GAAAgD,qBAAA,cAAG,aAAa,GAAAA,qBAAA;IAC/B9C,MAAM,GAAA4C,KAAA,CAAN5C,MAAM;EAEN,IAAMU,MAAM,GAAG,IAAAC,kCAAiB,EAAC,CAAC;EAElC,IAAMoC,YAAY,GAAG;IACnBC,UAAU,EAAEtD,KAAK,CAACuD,QAAQ,CAAC,KAAK,CAAC,GAAGvC,MAAM,CAACQ,GAAG,GAAG,CAAC;IAClDgC,aAAa,EAAExD,KAAK,CAACuD,QAAQ,CAAC,QAAQ,CAAC,GAAGvC,MAAM,CAACW,MAAM,GAAG,CAAC;IAC3D8B,WAAW,EAAEzD,KAAK,CAACuD,QAAQ,CAAC,MAAM,CAAC,GAAGvC,MAAM,CAACc,IAAI,GAAG,CAAC;IACrD4B,YAAY,EAAE1D,KAAK,CAACuD,QAAQ,CAAC,OAAO,CAAC,GAAGvC,MAAM,CAACe,KAAK,GAAG;EACzD,CAAC;EAED,OACE,IAAA1C,WAAA,CAAAwD,GAAA,EAAC3D,YAAA,CAAAsD,IAAI;IACH1C,KAAK,EAAE,CAAC2C,MAAM,CAACC,SAAS,EAAE;MAAEtC,eAAe,EAAfA;IAAgB,CAAC,EAAEiD,YAAY,EAAEvD,KAAK,CAAE;IACpEQ,MAAM,EAAEA,MAAO;IAAAT,QAAA,EACdA;EAAQ,CACL,CAAC;AAEX,CAAC;AAGM,IAAM8D,cAUX,GAAApE,OAAA,CAAAoE,cAAA,GAAG,SAVQA,cAUXA,CAAAC,KAAA,EAUI;EAAA,IATJ/D,QAAQ,GAAA+D,KAAA,CAAR/D,QAAQ;IACRC,KAAK,GAAA8D,KAAA,CAAL9D,KAAK;IAAA+D,qBAAA,GAAAD,KAAA,CACLxD,eAAe;IAAfA,eAAe,GAAAyD,qBAAA,cAAG,SAAS,GAAAA,qBAAA;IAAAC,qBAAA,GAAAF,KAAA,CAC3BG,gBAAgB;IAAhBA,gBAAgB,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IAAAE,mBAAA,GAAAJ,KAAA,CACvBK,aAAa;IAAbA,aAAa,GAAAD,mBAAA,cAAG,IAAI,GAAAA,mBAAA;IAAAE,oBAAA,GAAAN,KAAA,CACpBhD,cAAc;IAAdA,cAAc,GAAAsD,oBAAA,cAAG,cAAc,GAAAA,oBAAA;IAAAC,kBAAA,GAAAP,KAAA,CAC/BpD,YAAY;IAAZA,YAAY,GAAA2D,kBAAA,cAAG,IAAI,GAAAA,kBAAA;IAAAC,qBAAA,GAAAR,KAAA,CACnBlD,eAAe;IAAfA,eAAe,GAAA0D,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IACtB9D,MAAM,GAAAsD,KAAA,CAANtD,MAAM;EAEN,IAAMN,KAA8C,GAAG,EAAE;EAEzD,IAAI+D,gBAAgB,EAAE/D,KAAK,CAACqE,IAAI,CAAC,KAAK,CAAC;EACvC,IAAIJ,aAAa,EAAEjE,KAAK,CAACqE,IAAI,CAAC,QAAQ,CAAC;EAEvC,OACE,IAAAhF,WAAA,CAAAwD,GAAA,EAACvD,eAAe;IACdU,KAAK,EAAEA,KAAM;IACbI,eAAe,EAAEA,eAAgB;IACjCQ,cAAc,EAAEA,cAAe;IAC/BJ,YAAY,EAAEA,YAAa;IAC3BE,eAAe,EAAEA,eAAgB;IACjCZ,KAAK,EAAE,CAAC2C,MAAM,CAAC6B,MAAM,EAAExE,KAAK,CAAE;IAC9BQ,MAAM,EAAEA,MAAO;IAAAT,QAAA,EACdA;EAAQ,CACM,CAAC;AAEtB,CAAC;AAGM,IAAM0E,aAKX,GAAAhF,OAAA,CAAAgF,aAAA,GAAG,SALQA,aAKXA,CAAAC,KAAA,EAAiE;EAAA,IAA3D3E,QAAQ,GAAA2E,KAAA,CAAR3E,QAAQ;IAAEC,KAAK,GAAA0E,KAAA,CAAL1E,KAAK;IAAA2E,qBAAA,GAAAD,KAAA,CAAEpE,eAAe;IAAfA,eAAe,GAAAqE,qBAAA,cAAG,SAAS,GAAAA,qBAAA;IAAEnE,MAAM,GAAAkE,KAAA,CAANlE,MAAM;EAC1D,OACE,IAAAjB,WAAA,CAAAwD,GAAA,EAACvD,eAAe;IACdU,KAAK,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAE;IACzBI,eAAe,EAAEA,eAAgB;IACjCN,KAAK,EAAE,CAAC2C,MAAM,CAACiC,KAAK,EAAE5E,KAAK,CAAE;IAC7BQ,MAAM,EAAEA,MAAO;IAAAT,QAAA,EACdA;EAAQ,CACM,CAAC;AAEtB,CAAC;AAGM,IAAM8E,cAKX,GAAApF,OAAA,CAAAoF,cAAA,GAAG,SALQA,cAKXA,CAAAC,KAAA,EAAiE;EAAA,IAA3D/E,QAAQ,GAAA+E,KAAA,CAAR/E,QAAQ;IAAEC,KAAK,GAAA8E,KAAA,CAAL9E,KAAK;IAAA+E,qBAAA,GAAAD,KAAA,CAAExE,eAAe;IAAfA,eAAe,GAAAyE,qBAAA,cAAG,SAAS,GAAAA,qBAAA;IAAEvE,MAAM,GAAAsE,KAAA,CAANtE,MAAM;EAC1D,OACE,IAAAjB,WAAA,CAAAwD,GAAA,EAACvD,eAAe;IACdU,KAAK,EAAE,CAAC,KAAK,CAAE;IACfI,eAAe,EAAEA,eAAgB;IACjCN,KAAK,EAAE,CAAC2C,MAAM,CAACqC,MAAM,EAAEhF,KAAK,CAAE;IAC9BQ,MAAM,EAAEA,MAAO;IAAAT,QAAA,EACdA;EAAQ,CACM,CAAC;AAEtB,CAAC;AAGM,IAAMkF,uBAAuB,GAAAxF,OAAA,CAAAwF,uBAAA,GAAG,SAA1BA,uBAAuBA,CAAA,EAAS;EAC3C,OAAO,IAAA9D,kCAAiB,EAAC,CAAC;AAC5B,CAAC;AAEM,IAAM+D,cAAc,GAAAzF,OAAA,CAAAyF,cAAA,GAAG,SAAjBA,cAAcA,CAAA,EAAS;EAClC,OAAO,IAAAC,+BAAc,EAAC,CAAC;AACzB,CAAC;AAEM,IAAMC,iBAAiB,GAAA3F,OAAA,CAAA2F,iBAAA,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;EACrC,OAAO,IAAAC,kCAAiB,EAAC,CAAC;AAC5B,CAAC;AAED,IAAM1C,MAAM,GAAG2C,uBAAU,CAACC,MAAM,CAAC;EAC/B3C,SAAS,EAAE;IACT4C,IAAI,EAAE;EACR,CAAC;EACDhB,MAAM,EAAE;IACNgB,IAAI,EAAE;EACR,CAAC;EACDZ,KAAK,EAAAa,MAAA,CAAAC,MAAA;IACHF,IAAI,EAAE;EAAC,GACJ,IAAAG,iCAAkB,EAAC;IACpBC,GAAG,EAAE;MACHC,mBAAmB,EAAE,EAAE;MACvBC,oBAAoB,EAAE;IACxB,CAAC;IACDC,OAAO,EAAE;MACPF,mBAAmB,EAAE,CAAC;MACtBC,oBAAoB,EAAE;IACxB,CAAC;IACDE,OAAO,EAAE,CAAC;EACZ,CAAC,CAAC,CACH;EACDhB,MAAM,EAAAS,MAAA,CAAAC,MAAA,KACD,IAAAC,iCAAkB,EAAC;IACpBC,GAAG,EAAE;MACHK,WAAW,EAAE,MAAM;MACnBC,YAAY,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MACrCC,aAAa,EAAE,GAAG;MAClBC,YAAY,EAAE;IAChB,CAAC;IACDP,OAAO,EAAE;MACPQ,SAAS,EAAE;IACb,CAAC;IACDP,OAAO,EAAE,CAAC;EACZ,CAAC,CAAC;AAEN,CAAC,CAAC", "ignoreList": []}