{"version": 3, "names": ["_zustand", "require", "_middleware", "_testAccountsService", "convertTestAccountToProvider", "account", "index", "id", "business_name", "businessName", "firstName", "lastName", "description", "rating", "Math", "random", "review_count", "floor", "is_active", "is_verified", "city", "state", "distance", "toFixed", "avatar", "services", "category", "price_range", "min", "max", "email", "useProvidersStore", "exports", "create", "devtools", "set", "get", "providers", "loading", "error", "filters", "fetchProviders", "_fetchProviders", "_asyncToGenerator2", "default", "testAccounts", "testAccountsService", "getAccountsByRole", "map", "Promise", "resolve", "setTimeout", "apply", "arguments", "searchProviders", "_searchProviders", "getProvidersByCategory", "location", "filter", "_account$city", "toLowerCase", "includes", "rating_min", "provider", "undefined", "_x", "getProviderById", "_getProviderById", "providerId", "console", "log", "_get", "length", "find", "p", "getAllTestAccounts", "testAccount", "_account$businessName", "_x2", "updateFilters", "newFilters", "_get2", "Object", "assign", "clearFilters", "name"], "sources": ["providersSlice.ts"], "sourcesContent": ["/**\n * Providers Store Slice - Service Provider State Management\n *\n * Store Contract:\n * - Manages service provider data with Zustand\n * - Integrates with backend test accounts for development\n * - Handles provider search, filtering, and selection\n * - Supports location-based provider discovery\n * - Provides real test data from backend accounts\n *\n * @version 2.0.0\n * <AUTHOR> Development Team\n */\n\nimport { create } from 'zustand';\nimport { devtools } from 'zustand/middleware';\n\nimport { testAccountsService } from '../services/testAccountsService';\n\n// Types\nexport interface ServiceProvider {\n  id: string;\n  business_name: string;\n  description: string;\n  rating: number;\n  review_count: number;\n  is_active: boolean;\n  is_verified: boolean;\n  city: string;\n  state?: string;\n  distance?: string;\n  avatar?: string | null;\n  services?: string[];\n  price_range?: {\n    min: number;\n    max: number;\n  };\n  category?: string;\n  email?: string;\n  firstName?: string;\n  lastName?: string;\n}\n\ninterface SearchFilters {\n  category?: string;\n  location?: string;\n  price_min?: number;\n  price_max?: number;\n  rating_min?: number;\n  distance_max?: number;\n  is_verified?: boolean;\n}\n\ninterface ProvidersState {\n  providers: ServiceProvider[];\n  loading: boolean;\n  error: Error | null;\n  filters: SearchFilters;\n\n  // Actions\n  fetchProviders: () => Promise<void>;\n  searchProviders: (filters: SearchFilters) => Promise<void>;\n  getProviderById: (providerId: string) => Promise<ServiceProvider | null>;\n  updateFilters: (filters: Partial<SearchFilters>) => void;\n  clearFilters: () => void;\n}\n\n// Helper function to convert test account to service provider\nconst convertTestAccountToProvider = (\n  account: any,\n  index: number,\n): ServiceProvider => {\n  return {\n    id: `provider_${index + 1}`,\n    business_name:\n      account.businessName || `${account.firstName} ${account.lastName}`,\n    description: account.description || 'Professional beauty services',\n    rating: 4.2 + Math.random() * 0.8, // Random rating between 4.2-5.0\n    review_count: Math.floor(Math.random() * 50) + 10, // Random reviews 10-60\n    is_active: true,\n    is_verified: Math.random() > 0.3, // 70% verified\n    city: account.city || 'Ottawa',\n    state: 'ON',\n    distance: `${(Math.random() * 10 + 1).toFixed(1)} km`,\n    avatar: null,\n    services: [account.category || 'Beauty Services'],\n    price_range: {\n      min: 30 + Math.floor(Math.random() * 20),\n      max: 80 + Math.floor(Math.random() * 120),\n    },\n    category: account.category,\n    email: account.email,\n    firstName: account.firstName,\n    lastName: account.lastName,\n  };\n};\n\nexport const useProvidersStore = create<ProvidersState>()(\n  devtools(\n    (set, get) => ({\n      providers: [],\n      loading: false,\n      error: null,\n      filters: {},\n\n      fetchProviders: async () => {\n        set({ loading: true, error: null });\n\n        try {\n          // Get all service provider test accounts\n          const testAccounts =\n            testAccountsService.getAccountsByRole('service_provider');\n\n          // Convert test accounts to service providers\n          const providers = testAccounts.map((account, index) =>\n            convertTestAccountToProvider(account, index),\n          );\n\n          // Simulate API delay\n          await new Promise(resolve => setTimeout(resolve, 800));\n\n          set({ providers, loading: false });\n        } catch (error) {\n          set({\n            error: error as Error,\n            loading: false,\n            providers: [],\n          });\n        }\n      },\n\n      searchProviders: async (filters: SearchFilters) => {\n        set({ loading: true, error: null, filters });\n\n        try {\n          // Get all service provider test accounts\n          let testAccounts =\n            testAccountsService.getAccountsByRole('service_provider');\n\n          // Apply category filter\n          if (filters.category && filters.category !== 'all') {\n            testAccounts = testAccountsService.getProvidersByCategory(\n              filters.category,\n            );\n          }\n\n          // Apply location filter\n          if (filters.location) {\n            testAccounts = testAccounts.filter(account =>\n              account.city\n                ?.toLowerCase()\n                .includes(filters.location!.toLowerCase()),\n            );\n          }\n\n          // Convert to service providers\n          let providers = testAccounts.map((account, index) =>\n            convertTestAccountToProvider(account, index),\n          );\n\n          // Apply rating filter\n          if (filters.rating_min) {\n            providers = providers.filter(\n              provider => provider.rating >= filters.rating_min!,\n            );\n          }\n\n          // Apply verification filter\n          if (filters.is_verified !== undefined) {\n            providers = providers.filter(\n              provider => provider.is_verified === filters.is_verified,\n            );\n          }\n\n          // Simulate API delay\n          await new Promise(resolve => setTimeout(resolve, 600));\n\n          set({ providers, loading: false });\n        } catch (error) {\n          set({\n            error: error as Error,\n            loading: false,\n            providers: [],\n          });\n        }\n      },\n\n      getProviderById: async (providerId: string) => {\n        console.log(\n          '🔍 providersSlice.getProviderById called with:',\n          providerId,\n        );\n        set({ loading: true, error: null });\n\n        try {\n          const { providers } = get();\n          console.log('🔍 providersSlice: providers type:', typeof providers);\n          console.log(\n            '🔍 providersSlice: providers length:',\n            providers?.length,\n          );\n\n          let provider = providers?.find(p => p.id === providerId);\n\n          if (!provider) {\n            // If not in current list, try to find from test accounts\n            const testAccounts = testAccountsService.getAllTestAccounts();\n            const testAccount = testAccounts.find(\n              account =>\n                account.email.includes(providerId) ||\n                account.businessName?.includes(providerId),\n            );\n\n            if (testAccount) {\n              provider = convertTestAccountToProvider(testAccount, 0);\n            }\n          }\n\n          // Simulate API delay\n          await new Promise(resolve => setTimeout(resolve, 300));\n\n          set({ loading: false });\n          return provider || null;\n        } catch (error) {\n          console.error('❌ providersSlice.getProviderById error:', error);\n          set({\n            error: error as Error,\n            loading: false,\n          });\n          return null;\n        }\n      },\n\n      updateFilters: (newFilters: Partial<SearchFilters>) => {\n        const { filters } = get();\n        set({ filters: { ...filters, ...newFilters } });\n      },\n\n      clearFilters: () => {\n        set({ filters: {} });\n      },\n    }),\n    {\n      name: 'providers-store',\n    },\n  ),\n);\n"], "mappings": ";;;;;;AAcA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AAEA,IAAAE,oBAAA,GAAAF,OAAA;AAmDA,IAAMG,4BAA4B,GAAG,SAA/BA,4BAA4BA,CAChCC,OAAY,EACZC,KAAa,EACO;EACpB,OAAO;IACLC,EAAE,EAAE,YAAYD,KAAK,GAAG,CAAC,EAAE;IAC3BE,aAAa,EACXH,OAAO,CAACI,YAAY,IAAI,GAAGJ,OAAO,CAACK,SAAS,IAAIL,OAAO,CAACM,QAAQ,EAAE;IACpEC,WAAW,EAAEP,OAAO,CAACO,WAAW,IAAI,8BAA8B;IAClEC,MAAM,EAAE,GAAG,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;IACjCC,YAAY,EAAEF,IAAI,CAACG,KAAK,CAACH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE;IACjDG,SAAS,EAAE,IAAI;IACfC,WAAW,EAAEL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;IAChCK,IAAI,EAAEf,OAAO,CAACe,IAAI,IAAI,QAAQ;IAC9BC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,GAAG,CAACR,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAEQ,OAAO,CAAC,CAAC,CAAC,KAAK;IACrDC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,CAACpB,OAAO,CAACqB,QAAQ,IAAI,iBAAiB,CAAC;IACjDC,WAAW,EAAE;MACXC,GAAG,EAAE,EAAE,GAAGd,IAAI,CAACG,KAAK,CAACH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;MACxCc,GAAG,EAAE,EAAE,GAAGf,IAAI,CAACG,KAAK,CAACH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;IAC1C,CAAC;IACDW,QAAQ,EAAErB,OAAO,CAACqB,QAAQ;IAC1BI,KAAK,EAAEzB,OAAO,CAACyB,KAAK;IACpBpB,SAAS,EAAEL,OAAO,CAACK,SAAS;IAC5BC,QAAQ,EAAEN,OAAO,CAACM;EACpB,CAAC;AACH,CAAC;AAEM,IAAMoB,iBAAiB,GAAAC,OAAA,CAAAD,iBAAA,GAAG,IAAAE,eAAM,EAAiB,CAAC,CACvD,IAAAC,oBAAQ,EACN,UAACC,GAAG,EAAEC,GAAG;EAAA,OAAM;IACbC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAE,CAAC,CAAC;IAEXC,cAAc;MAAA,IAAAC,eAAA,OAAAC,kBAAA,CAAAC,OAAA,EAAE,aAAY;QAC1BT,GAAG,CAAC;UAAEG,OAAO,EAAE,IAAI;UAAEC,KAAK,EAAE;QAAK,CAAC,CAAC;QAEnC,IAAI;UAEF,IAAMM,YAAY,GAChBC,wCAAmB,CAACC,iBAAiB,CAAC,kBAAkB,CAAC;UAG3D,IAAMV,SAAS,GAAGQ,YAAY,CAACG,GAAG,CAAC,UAAC3C,OAAO,EAAEC,KAAK;YAAA,OAChDF,4BAA4B,CAACC,OAAO,EAAEC,KAAK,CAAC;UAAA,CAC9C,CAAC;UAGD,MAAM,IAAI2C,OAAO,CAAC,UAAAC,OAAO;YAAA,OAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC;UAAA,EAAC;UAEtDf,GAAG,CAAC;YAAEE,SAAS,EAATA,SAAS;YAAEC,OAAO,EAAE;UAAM,CAAC,CAAC;QACpC,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdJ,GAAG,CAAC;YACFI,KAAK,EAAEA,KAAc;YACrBD,OAAO,EAAE,KAAK;YACdD,SAAS,EAAE;UACb,CAAC,CAAC;QACJ;MACF,CAAC;MAAA,SAxBDI,cAAcA,CAAA;QAAA,OAAAC,eAAA,CAAAU,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAdZ,cAAc;IAAA,GAwBb;IAEDa,eAAe;MAAA,IAAAC,gBAAA,OAAAZ,kBAAA,CAAAC,OAAA,EAAE,WAAOJ,OAAsB,EAAK;QACjDL,GAAG,CAAC;UAAEG,OAAO,EAAE,IAAI;UAAEC,KAAK,EAAE,IAAI;UAAEC,OAAO,EAAPA;QAAQ,CAAC,CAAC;QAE5C,IAAI;UAEF,IAAIK,YAAY,GACdC,wCAAmB,CAACC,iBAAiB,CAAC,kBAAkB,CAAC;UAG3D,IAAIP,OAAO,CAACd,QAAQ,IAAIc,OAAO,CAACd,QAAQ,KAAK,KAAK,EAAE;YAClDmB,YAAY,GAAGC,wCAAmB,CAACU,sBAAsB,CACvDhB,OAAO,CAACd,QACV,CAAC;UACH;UAGA,IAAIc,OAAO,CAACiB,QAAQ,EAAE;YACpBZ,YAAY,GAAGA,YAAY,CAACa,MAAM,CAAC,UAAArD,OAAO;cAAA,IAAAsD,aAAA;cAAA,QAAAA,aAAA,GACxCtD,OAAO,CAACe,IAAI,qBAAZuC,aAAA,CACIC,WAAW,CAAC,CAAC,CACdC,QAAQ,CAACrB,OAAO,CAACiB,QAAQ,CAAEG,WAAW,CAAC,CAAC,CAAC;YAAA,CAC9C,CAAC;UACH;UAGA,IAAIvB,SAAS,GAAGQ,YAAY,CAACG,GAAG,CAAC,UAAC3C,OAAO,EAAEC,KAAK;YAAA,OAC9CF,4BAA4B,CAACC,OAAO,EAAEC,KAAK,CAAC;UAAA,CAC9C,CAAC;UAGD,IAAIkC,OAAO,CAACsB,UAAU,EAAE;YACtBzB,SAAS,GAAGA,SAAS,CAACqB,MAAM,CAC1B,UAAAK,QAAQ;cAAA,OAAIA,QAAQ,CAAClD,MAAM,IAAI2B,OAAO,CAACsB,UAAW;YAAA,CACpD,CAAC;UACH;UAGA,IAAItB,OAAO,CAACrB,WAAW,KAAK6C,SAAS,EAAE;YACrC3B,SAAS,GAAGA,SAAS,CAACqB,MAAM,CAC1B,UAAAK,QAAQ;cAAA,OAAIA,QAAQ,CAAC5C,WAAW,KAAKqB,OAAO,CAACrB,WAAW;YAAA,CAC1D,CAAC;UACH;UAGA,MAAM,IAAI8B,OAAO,CAAC,UAAAC,OAAO;YAAA,OAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC;UAAA,EAAC;UAEtDf,GAAG,CAAC;YAAEE,SAAS,EAATA,SAAS;YAAEC,OAAO,EAAE;UAAM,CAAC,CAAC;QACpC,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdJ,GAAG,CAAC;YACFI,KAAK,EAAEA,KAAc;YACrBD,OAAO,EAAE,KAAK;YACdD,SAAS,EAAE;UACb,CAAC,CAAC;QACJ;MACF,CAAC;MAAA,SAtDDiB,eAAeA,CAAAW,EAAA;QAAA,OAAAV,gBAAA,CAAAH,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAfC,eAAe;IAAA,GAsDd;IAEDY,eAAe;MAAA,IAAAC,gBAAA,OAAAxB,kBAAA,CAAAC,OAAA,EAAE,WAAOwB,UAAkB,EAAK;QAC7CC,OAAO,CAACC,GAAG,CACT,gDAAgD,EAChDF,UACF,CAAC;QACDjC,GAAG,CAAC;UAAEG,OAAO,EAAE,IAAI;UAAEC,KAAK,EAAE;QAAK,CAAC,CAAC;QAEnC,IAAI;UACF,IAAAgC,IAAA,GAAsBnC,GAAG,CAAC,CAAC;YAAnBC,SAAS,GAAAkC,IAAA,CAATlC,SAAS;UACjBgC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE,OAAOjC,SAAS,CAAC;UACnEgC,OAAO,CAACC,GAAG,CACT,sCAAsC,EACtCjC,SAAS,oBAATA,SAAS,CAAEmC,MACb,CAAC;UAED,IAAIT,QAAQ,GAAG1B,SAAS,oBAATA,SAAS,CAAEoC,IAAI,CAAC,UAAAC,CAAC;YAAA,OAAIA,CAAC,CAACnE,EAAE,KAAK6D,UAAU;UAAA,EAAC;UAExD,IAAI,CAACL,QAAQ,EAAE;YAEb,IAAMlB,YAAY,GAAGC,wCAAmB,CAAC6B,kBAAkB,CAAC,CAAC;YAC7D,IAAMC,WAAW,GAAG/B,YAAY,CAAC4B,IAAI,CACnC,UAAApE,OAAO;cAAA,IAAAwE,qBAAA;cAAA,OACLxE,OAAO,CAACyB,KAAK,CAAC+B,QAAQ,CAACO,UAAU,CAAC,MAAAS,qBAAA,GAClCxE,OAAO,CAACI,YAAY,qBAApBoE,qBAAA,CAAsBhB,QAAQ,CAACO,UAAU,CAAC;YAAA,CAC9C,CAAC;YAED,IAAIQ,WAAW,EAAE;cACfb,QAAQ,GAAG3D,4BAA4B,CAACwE,WAAW,EAAE,CAAC,CAAC;YACzD;UACF;UAGA,MAAM,IAAI3B,OAAO,CAAC,UAAAC,OAAO;YAAA,OAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC;UAAA,EAAC;UAEtDf,GAAG,CAAC;YAAEG,OAAO,EAAE;UAAM,CAAC,CAAC;UACvB,OAAOyB,QAAQ,IAAI,IAAI;QACzB,CAAC,CAAC,OAAOxB,KAAK,EAAE;UACd8B,OAAO,CAAC9B,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;UAC/DJ,GAAG,CAAC;YACFI,KAAK,EAAEA,KAAc;YACrBD,OAAO,EAAE;UACX,CAAC,CAAC;UACF,OAAO,IAAI;QACb;MACF,CAAC;MAAA,SA5CD4B,eAAeA,CAAAY,GAAA;QAAA,OAAAX,gBAAA,CAAAf,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAfa,eAAe;IAAA,GA4Cd;IAEDa,aAAa,EAAE,SAAfA,aAAaA,CAAGC,UAAkC,EAAK;MACrD,IAAAC,KAAA,GAAoB7C,GAAG,CAAC,CAAC;QAAjBI,OAAO,GAAAyC,KAAA,CAAPzC,OAAO;MACfL,GAAG,CAAC;QAAEK,OAAO,EAAA0C,MAAA,CAAAC,MAAA,KAAO3C,OAAO,EAAKwC,UAAU;MAAG,CAAC,CAAC;IACjD,CAAC;IAEDI,YAAY,EAAE,SAAdA,YAAYA,CAAA,EAAQ;MAClBjD,GAAG,CAAC;QAAEK,OAAO,EAAE,CAAC;MAAE,CAAC,CAAC;IACtB;EACF,CAAC;AAAA,CAAC,EACF;EACE6C,IAAI,EAAE;AACR,CACF,CACF,CAAC", "ignoreList": []}