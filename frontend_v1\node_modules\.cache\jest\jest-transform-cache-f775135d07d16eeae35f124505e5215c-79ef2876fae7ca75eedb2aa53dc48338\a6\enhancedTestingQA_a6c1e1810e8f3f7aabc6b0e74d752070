598892f2d891033fee1eef83a1c333e2
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.validatePerformance = exports.validateCodeQuality = exports.validateAccessibility = exports.testSuiteUtils = exports.testDataFactories = exports.renderWithEnhancedProviders = exports.enhancedTestingQA = exports.enhancedAssertions = exports.default = exports.EnhancedTestingQA = exports.DEFAULT_ENHANCED_TEST_CONFIG = void 0;
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _reactNative = require("@testing-library/react-native");
var DEFAULT_ENHANCED_TEST_CONFIG = exports.DEFAULT_ENHANCED_TEST_CONFIG = {
  enablePerformanceTesting: true,
  enableAccessibilityTesting: true,
  enableVisualRegression: false,
  enableCodeQualityChecks: true,
  testTimeout: 10000,
  coverageThreshold: {
    statements: 85,
    branches: 80,
    functions: 85,
    lines: 85
  },
  performanceBudget: {
    renderTime: 16,
    memoryUsage: 50,
    bundleSize: 500
  }
};
var EnhancedTestingQA = exports.EnhancedTestingQA = (0, _createClass2.default)(function EnhancedTestingQA() {
  var _this = this;
  var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  (0, _classCallCheck2.default)(this, EnhancedTestingQA);
  this.renderWithEnhancedProviders = function (ui) {
    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    var startTime = performance.now();
    var result = (0, _reactNative.render)(ui, options);
    if (options.enablePerformanceMonitoring && _this.config.enablePerformanceTesting) {
      var renderTime = performance.now() - startTime;
      _this.validatePerformance({
        renderTime: renderTime
      });
    }
    if (options.enableAccessibilityChecks && _this.config.enableAccessibilityTesting) {
      _this.validateAccessibility(result.container);
    }
    return result;
  };
  this.validatePerformance = function (metrics) {
    var renderTime = metrics.renderTime,
      memoryUsage = metrics.memoryUsage;
    var budget = _this.config.performanceBudget;
    if (renderTime > budget.renderTime) {
      console.warn(`⚠️ Performance Warning: Render time ${renderTime.toFixed(2)}ms exceeds budget ${budget.renderTime}ms`);
    }
    if (memoryUsage && memoryUsage > budget.memoryUsage) {
      console.warn(`⚠️ Performance Warning: Memory usage ${memoryUsage}MB exceeds budget ${budget.memoryUsage}MB`);
    }
    var renderRatio = renderTime / budget.renderTime;
    var renderScore = Math.max(0, 100 - (renderRatio - 1) * 100);
    var memoryScore = memoryUsage ? Math.max(0, 100 - (memoryUsage / budget.memoryUsage - 1) * 100) : 100;
    _this.metrics.performanceScore = (renderScore + memoryScore) / 2;
  };
  this.validateAccessibility = function (container) {
    var accessibilityIssues = [];
    try {
      var interactiveElements = container.querySelectorAll('button, input, select, textarea, [role="button"]');
      interactiveElements.forEach(function (element, index) {
        var hasLabel = element.accessibilityLabel || element.getAttribute && element.getAttribute('aria-label');
        if (!hasLabel) {
          accessibilityIssues.push(`Interactive element ${index} missing accessibility label`);
        }
      });
      var headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6, [role="heading"]');
      if (headings.length === 0) {
        accessibilityIssues.push('No heading elements found - consider adding semantic headings');
      }
      var maxIssues = Math.max(1, interactiveElements.length);
      _this.metrics.accessibilityScore = Math.max(0, 100 - accessibilityIssues.length / maxIssues * 100);
      if (accessibilityIssues.length > 0) {
        console.warn('♿ Accessibility Issues:', accessibilityIssues);
      }
    } catch (error) {
      console.warn('Accessibility validation failed:', error);
      _this.metrics.accessibilityScore = 0;
    }
    return accessibilityIssues;
  };
  this.validateCodeQuality = function (componentCode) {
    var qualityIssues = [];
    if (componentCode.includes('console.log')) {
      qualityIssues.push('Console.log statements found - remove before production');
    }
    var todoCount = (componentCode.match(/TODO|FIXME|HACK/gi) || []).length;
    if (todoCount > 0) {
      qualityIssues.push(`${todoCount} TODO/FIXME comments found`);
    }
    if (componentCode.includes(': any')) {
      qualityIssues.push('Avoid using "any" type - use specific types instead');
    }
    _this.metrics.codeQualityScore = Math.max(0, 100 - qualityIssues.length * 10);
    return qualityIssues;
  };
  this.createTestDataFactory = function (template) {
    return function () {
      var overrides = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      return Object.assign({}, template, overrides);
    };
  };
  this.testDataFactories = {
    user: this.createTestDataFactory({
      id: '1',
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      phone: '+**********',
      isActive: true,
      createdAt: new Date().toISOString()
    }),
    service: this.createTestDataFactory({
      id: '1',
      name: 'Test Service',
      description: 'A test service for testing purposes',
      price: 50,
      duration: 60,
      category: 'Test Category',
      isActive: true
    }),
    booking: this.createTestDataFactory({
      id: '1',
      userId: '1',
      serviceId: '1',
      providerId: '1',
      date: new Date().toISOString(),
      status: 'confirmed',
      totalAmount: 50
    }),
    provider: this.createTestDataFactory({
      id: '1',
      businessName: 'Test Provider',
      description: 'A test provider for testing',
      rating: 4.5,
      reviewCount: 10,
      isVerified: true,
      location: {
        address: '123 Test St',
        city: 'Test City',
        state: 'TS',
        zipCode: '12345'
      }
    })
  };
  this.enhancedAssertions = {
    expectToRenderWithoutErrors: function expectToRenderWithoutErrors(component) {
      expect(function () {
        return _this.renderWithEnhancedProviders(component);
      }).not.toThrow();
    },
    expectToBeAccessible: function expectToBeAccessible(component) {
      var _this$renderWithEnhan = _this.renderWithEnhancedProviders(component, {
          enableAccessibilityChecks: true
        }),
        container = _this$renderWithEnhan.container;
      var issues = _this.validateAccessibility(container);
      expect(issues).toHaveLength(0);
    },
    expectToMeetPerformanceBudget: function expectToMeetPerformanceBudget(component) {
      var startTime = performance.now();
      _this.renderWithEnhancedProviders(component, {
        enablePerformanceMonitoring: true
      });
      var renderTime = performance.now() - startTime;
      expect(renderTime).toBeLessThan(_this.config.performanceBudget.renderTime);
    },
    expectToHaveTestCoverage: function expectToHaveTestCoverage(testResults) {
      var _coverage$statements, _coverage$branches, _coverage$functions, _coverage$lines;
      var coverage = testResults.coverage || {};
      expect(((_coverage$statements = coverage.statements) == null ? void 0 : _coverage$statements.pct) || 0).toBeGreaterThanOrEqual(_this.config.coverageThreshold.statements);
      expect(((_coverage$branches = coverage.branches) == null ? void 0 : _coverage$branches.pct) || 0).toBeGreaterThanOrEqual(_this.config.coverageThreshold.branches);
      expect(((_coverage$functions = coverage.functions) == null ? void 0 : _coverage$functions.pct) || 0).toBeGreaterThanOrEqual(_this.config.coverageThreshold.functions);
      expect(((_coverage$lines = coverage.lines) == null ? void 0 : _coverage$lines.pct) || 0).toBeGreaterThanOrEqual(_this.config.coverageThreshold.lines);
    }
  };
  this.testSuiteUtils = {
    createComprehensiveTestSuite: function createComprehensiveTestSuite(componentName, component, testCases) {
      return {
        suiteName: `${componentName} - Comprehensive Test Suite`,
        tests: [{
          name: 'should render without errors',
          test: function test() {
            return _this.enhancedAssertions.expectToRenderWithoutErrors(component);
          }
        }, {
          name: 'should be accessible',
          test: function test() {
            return _this.enhancedAssertions.expectToBeAccessible(component);
          }
        }, {
          name: 'should meet performance budget',
          test: function test() {
            return _this.enhancedAssertions.expectToMeetPerformanceBudget(component);
          }
        }].concat((0, _toConsumableArray2.default)(testCases))
      };
    },
    createIntegrationTestSuite: function createIntegrationTestSuite(suiteName, integrationTests) {
      return {
        suiteName: `${suiteName} - Integration Tests`,
        tests: integrationTests.map(function (_ref) {
          var name = _ref.name,
            test = _ref.test;
          return {
            name: name,
            test: test,
            timeout: _this.config.testTimeout
          };
        })
      };
    }
  };
  this.getMetrics = function () {
    return Object.assign({}, _this.metrics);
  };
  this.generateQualityReport = function () {
    var metrics = _this.getMetrics();
    var overallScore = (metrics.performanceScore + metrics.accessibilityScore + metrics.codeQualityScore) / 3;
    return `
📊 Test Quality Report
=====================
Total Tests: ${metrics.totalTests}
Passing: ${metrics.passingTests}
Failing: ${metrics.failingTests}
Skipped: ${metrics.skippedTests}

📈 Quality Scores
Performance: ${metrics.performanceScore.toFixed(1)}/100
Accessibility: ${metrics.accessibilityScore.toFixed(1)}/100
Code Quality: ${metrics.codeQualityScore.toFixed(1)}/100
Overall: ${overallScore.toFixed(1)}/100

⏱️ Execution Time: ${metrics.testExecutionTime.toFixed(2)}ms
📊 Coverage: ${metrics.coveragePercentage.toFixed(1)}%
    `.trim();
  };
  this.config = Object.assign({}, DEFAULT_ENHANCED_TEST_CONFIG, config);
  this.metrics = {
    totalTests: 0,
    passingTests: 0,
    failingTests: 0,
    skippedTests: 0,
    coveragePercentage: 0,
    performanceScore: 0,
    accessibilityScore: 0,
    codeQualityScore: 0,
    testExecutionTime: 0
  };
});
var enhancedTestingQA = exports.enhancedTestingQA = new EnhancedTestingQA();
var renderWithEnhancedProviders = exports.renderWithEnhancedProviders = enhancedTestingQA.renderWithEnhancedProviders,
  validatePerformance = exports.validatePerformance = enhancedTestingQA.validatePerformance,
  validateAccessibility = exports.validateAccessibility = enhancedTestingQA.validateAccessibility,
  validateCodeQuality = exports.validateCodeQuality = enhancedTestingQA.validateCodeQuality,
  testDataFactories = exports.testDataFactories = enhancedTestingQA.testDataFactories,
  enhancedAssertions = exports.enhancedAssertions = enhancedTestingQA.enhancedAssertions,
  testSuiteUtils = exports.testSuiteUtils = enhancedTestingQA.testSuiteUtils;
var _default = exports.default = enhancedTestingQA;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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