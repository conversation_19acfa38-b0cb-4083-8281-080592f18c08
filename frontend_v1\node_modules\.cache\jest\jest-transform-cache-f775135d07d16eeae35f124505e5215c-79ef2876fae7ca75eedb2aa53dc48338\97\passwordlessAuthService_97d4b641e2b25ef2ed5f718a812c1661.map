{"version": 3, "names": ["_asyncStorage", "_interopRequireDefault", "require", "Crypto", "_interopRequireWildcard", "LocalAuthentication", "_authService", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "PasswordlessAuthService", "_classCallCheck2", "MAGIC_LINK_EXPIRY", "OTP_EXPIRY", "MAX_OTP_ATTEMPTS", "RATE_LIMIT_WINDOW", "MAX_REQUESTS_PER_WINDOW", "requestCounts", "Map", "_createClass2", "key", "value", "checkRateLimit", "identifier", "now", "Date", "record", "resetTime", "count", "_generateSecureToken", "_asyncToGenerator2", "randomBytes", "getRandomBytesAsync", "Array", "from", "byte", "toString", "padStart", "join", "generateSecureToken", "apply", "arguments", "generateOTPCode", "Math", "floor", "random", "_sendMagicLink", "email", "success", "error", "emailRegex", "test", "token", "expiresAt", "magicLinkData", "used", "AsyncStorage", "setItem", "JSON", "stringify", "console", "log", "requiresVerification", "verificationId", "sendMagicLink", "_x", "_verifyMagicLink", "storedData", "getItem", "parse", "removeItem", "authResult", "authService", "authenticateWithEmail", "verifyMagicLink", "_x2", "_sendOTP", "phone", "phoneRegex", "code", "otpData", "attempts", "maxAttempts", "sendOTP", "_x3", "_verifyOTP", "remainingAttempts", "authenticateWithPhone", "verifyOTP", "_x4", "_x5", "_authenticateWithBiometrics", "options", "length", "undefined", "hasHardware", "hasHardwareAsync", "isEnrolled", "isEnrolledAsync", "result", "authenticateAsync", "promptMessage", "fallback<PERSON><PERSON><PERSON>", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storedUser", "userData", "authenticateWithBiometric", "authenticateWithBiometrics", "_setupBiometricAuth", "user", "setupBiometricAuth", "_x6", "_isBiometricSetup", "isBiometricSetup", "_clearAuthData", "keys", "getAllKeys", "auth<PERSON><PERSON><PERSON>", "filter", "startsWith", "multiRemove", "clearAuthData", "passwordlessAuthService", "exports"], "sources": ["passwordlessAuthService.ts"], "sourcesContent": ["/**\n * Passwordless Authentication Service\n *\n * Comprehensive service for handling passwordless authentication methods:\n * - Email magic links\n * - SMS OTP verification\n * - Biometric authentication\n * - Social login integration\n *\n * Features:\n * - Secure token generation and validation\n * - Rate limiting and security measures\n * - Multi-factor authentication support\n * - Session management\n * - Error handling and recovery\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport AsyncStorage from '@react-native-async-storage/async-storage';\nimport * as Crypto from 'expo-crypto';\nimport * as LocalAuthentication from 'expo-local-authentication';\n\nimport { authService } from './authService';\n\n// Authentication method types\nexport type AuthMethod = 'email' | 'phone' | 'biometric' | 'social';\n\n// Authentication result interface\nexport interface AuthResult {\n  success: boolean;\n  user?: any;\n  token?: string;\n  refreshToken?: string;\n  error?: string;\n  requiresVerification?: boolean;\n  verificationId?: string;\n}\n\n// Magic link data interface\nexport interface MagicLinkData {\n  email: string;\n  token: string;\n  expiresAt: number;\n  used: boolean;\n}\n\n// OTP data interface\nexport interface OTPData {\n  phone: string;\n  code: string;\n  expiresAt: number;\n  attempts: number;\n  maxAttempts: number;\n}\n\n// Biometric authentication options\nexport interface BiometricOptions {\n  promptMessage?: string;\n  fallbackLabel?: string;\n  disableDeviceFallback?: boolean;\n}\n\nclass PasswordlessAuthService {\n  private readonly MAGIC_LINK_EXPIRY = 15 * 60 * 1000; // 15 minutes\n  private readonly OTP_EXPIRY = 5 * 60 * 1000; // 5 minutes\n  private readonly MAX_OTP_ATTEMPTS = 3;\n  private readonly RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute\n  private readonly MAX_REQUESTS_PER_WINDOW = 5;\n\n  private requestCounts: Map<string, { count: number; resetTime: number }> =\n    new Map();\n\n  /**\n   * Check if rate limit is exceeded for a given identifier\n   */\n  private checkRateLimit(identifier: string): boolean {\n    const now = Date.now();\n    const record = this.requestCounts.get(identifier);\n\n    if (!record || now > record.resetTime) {\n      this.requestCounts.set(identifier, {\n        count: 1,\n        resetTime: now + this.RATE_LIMIT_WINDOW,\n      });\n      return false;\n    }\n\n    if (record.count >= this.MAX_REQUESTS_PER_WINDOW) {\n      return true;\n    }\n\n    record.count++;\n    return false;\n  }\n\n  /**\n   * Generate secure random token\n   */\n  private async generateSecureToken(): Promise<string> {\n    const randomBytes = await Crypto.getRandomBytesAsync(32);\n    return Array.from(randomBytes, byte =>\n      byte.toString(16).padStart(2, '0'),\n    ).join('');\n  }\n\n  /**\n   * Generate 6-digit OTP code\n   */\n  private generateOTPCode(): string {\n    return Math.floor(100000 + Math.random() * 900000).toString();\n  }\n\n  /**\n   * Send magic link to email\n   */\n  async sendMagicLink(email: string): Promise<AuthResult> {\n    try {\n      // Check rate limiting\n      if (this.checkRateLimit(email)) {\n        return {\n          success: false,\n          error: 'Too many requests. Please wait before trying again.',\n        };\n      }\n\n      // Validate email format\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      if (!emailRegex.test(email)) {\n        return {\n          success: false,\n          error: 'Invalid email address format.',\n        };\n      }\n\n      // Generate secure token\n      const token = await this.generateSecureToken();\n      const expiresAt = Date.now() + this.MAGIC_LINK_EXPIRY;\n\n      // Store magic link data\n      const magicLinkData: MagicLinkData = {\n        email,\n        token,\n        expiresAt,\n        used: false,\n      };\n\n      await AsyncStorage.setItem(\n        `magic_link_${token}`,\n        JSON.stringify(magicLinkData),\n      );\n\n      // In a real implementation, this would send an email\n      // For demo purposes, we'll log the magic link\n      console.log(\n        `🔗 Magic Link for ${email}: vierla://auth/magic?token=${token}`,\n      );\n\n      return {\n        success: true,\n        requiresVerification: true,\n        verificationId: token,\n      };\n    } catch (error) {\n      console.error('Error sending magic link:', error);\n      return {\n        success: false,\n        error: 'Failed to send magic link. Please try again.',\n      };\n    }\n  }\n\n  /**\n   * Verify magic link token\n   */\n  async verifyMagicLink(token: string): Promise<AuthResult> {\n    try {\n      const storedData = await AsyncStorage.getItem(`magic_link_${token}`);\n      if (!storedData) {\n        return {\n          success: false,\n          error: 'Invalid or expired magic link.',\n        };\n      }\n\n      const magicLinkData: MagicLinkData = JSON.parse(storedData);\n\n      // Check if token is expired\n      if (Date.now() > magicLinkData.expiresAt) {\n        await AsyncStorage.removeItem(`magic_link_${token}`);\n        return {\n          success: false,\n          error: 'Magic link has expired. Please request a new one.',\n        };\n      }\n\n      // Check if token has been used\n      if (magicLinkData.used) {\n        return {\n          success: false,\n          error: 'Magic link has already been used.',\n        };\n      }\n\n      // Mark token as used\n      magicLinkData.used = true;\n      await AsyncStorage.setItem(\n        `magic_link_${token}`,\n        JSON.stringify(magicLinkData),\n      );\n\n      // Authenticate user with email\n      const authResult = await authService.authenticateWithEmail(\n        magicLinkData.email,\n      );\n\n      // Clean up token\n      await AsyncStorage.removeItem(`magic_link_${token}`);\n\n      return authResult;\n    } catch (error) {\n      console.error('Error verifying magic link:', error);\n      return {\n        success: false,\n        error: 'Failed to verify magic link. Please try again.',\n      };\n    }\n  }\n\n  /**\n   * Send OTP to phone number\n   */\n  async sendOTP(phone: string): Promise<AuthResult> {\n    try {\n      // Check rate limiting\n      if (this.checkRateLimit(phone)) {\n        return {\n          success: false,\n          error: 'Too many requests. Please wait before trying again.',\n        };\n      }\n\n      // Validate phone format\n      const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]{10,}$/;\n      if (!phoneRegex.test(phone)) {\n        return {\n          success: false,\n          error: 'Invalid phone number format.',\n        };\n      }\n\n      // Generate OTP code\n      const code = this.generateOTPCode();\n      const expiresAt = Date.now() + this.OTP_EXPIRY;\n\n      // Store OTP data\n      const otpData: OTPData = {\n        phone,\n        code,\n        expiresAt,\n        attempts: 0,\n        maxAttempts: this.MAX_OTP_ATTEMPTS,\n      };\n\n      await AsyncStorage.setItem(`otp_${phone}`, JSON.stringify(otpData));\n\n      // In a real implementation, this would send an SMS\n      // For demo purposes, we'll log the OTP\n      console.log(`📱 OTP for ${phone}: ${code}`);\n\n      return {\n        success: true,\n        requiresVerification: true,\n        verificationId: phone,\n      };\n    } catch (error) {\n      console.error('Error sending OTP:', error);\n      return {\n        success: false,\n        error: 'Failed to send OTP. Please try again.',\n      };\n    }\n  }\n\n  /**\n   * Verify OTP code\n   */\n  async verifyOTP(phone: string, code: string): Promise<AuthResult> {\n    try {\n      const storedData = await AsyncStorage.getItem(`otp_${phone}`);\n      if (!storedData) {\n        return {\n          success: false,\n          error: 'No OTP found for this phone number.',\n        };\n      }\n\n      const otpData: OTPData = JSON.parse(storedData);\n\n      // Check if OTP is expired\n      if (Date.now() > otpData.expiresAt) {\n        await AsyncStorage.removeItem(`otp_${phone}`);\n        return {\n          success: false,\n          error: 'OTP has expired. Please request a new one.',\n        };\n      }\n\n      // Check attempts\n      if (otpData.attempts >= otpData.maxAttempts) {\n        await AsyncStorage.removeItem(`otp_${phone}`);\n        return {\n          success: false,\n          error: 'Too many failed attempts. Please request a new OTP.',\n        };\n      }\n\n      // Verify code\n      if (otpData.code !== code) {\n        otpData.attempts++;\n        await AsyncStorage.setItem(`otp_${phone}`, JSON.stringify(otpData));\n\n        const remainingAttempts = otpData.maxAttempts - otpData.attempts;\n        return {\n          success: false,\n          error: `Invalid OTP. ${remainingAttempts} attempts remaining.`,\n        };\n      }\n\n      // Authenticate user with phone\n      const authResult = await authService.authenticateWithPhone(phone);\n\n      // Clean up OTP\n      await AsyncStorage.removeItem(`otp_${phone}`);\n\n      return authResult;\n    } catch (error) {\n      console.error('Error verifying OTP:', error);\n      return {\n        success: false,\n        error: 'Failed to verify OTP. Please try again.',\n      };\n    }\n  }\n\n  /**\n   * Authenticate with biometrics\n   */\n  async authenticateWithBiometrics(\n    options: BiometricOptions = {},\n  ): Promise<AuthResult> {\n    try {\n      // Check if biometric authentication is available\n      const hasHardware = await LocalAuthentication.hasHardwareAsync();\n      if (!hasHardware) {\n        return {\n          success: false,\n          error: 'Biometric authentication is not available on this device.',\n        };\n      }\n\n      const isEnrolled = await LocalAuthentication.isEnrolledAsync();\n      if (!isEnrolled) {\n        return {\n          success: false,\n          error: 'No biometric credentials are enrolled on this device.',\n        };\n      }\n\n      // Perform biometric authentication\n      const result = await LocalAuthentication.authenticateAsync({\n        promptMessage: options.promptMessage || 'Authenticate to sign in',\n        fallbackLabel: options.fallbackLabel || 'Use passcode',\n        disableDeviceFallback: options.disableDeviceFallback || false,\n      });\n\n      if (!result.success) {\n        return {\n          success: false,\n          error: result.error || 'Biometric authentication failed.',\n        };\n      }\n\n      // Get stored user data for biometric authentication\n      const storedUser = await AsyncStorage.getItem('biometric_user');\n      if (!storedUser) {\n        return {\n          success: false,\n          error:\n            'No biometric user data found. Please set up biometric authentication.',\n        };\n      }\n\n      const userData = JSON.parse(storedUser);\n      const authResult = await authService.authenticateWithBiometric(userData);\n\n      return authResult;\n    } catch (error) {\n      console.error('Error with biometric authentication:', error);\n      return {\n        success: false,\n        error: 'Biometric authentication failed. Please try again.',\n      };\n    }\n  }\n\n  /**\n   * Set up biometric authentication for a user\n   */\n  async setupBiometricAuth(user: any): Promise<boolean> {\n    try {\n      await AsyncStorage.setItem('biometric_user', JSON.stringify(user));\n      return true;\n    } catch (error) {\n      console.error('Error setting up biometric auth:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Check if biometric authentication is set up\n   */\n  async isBiometricSetup(): Promise<boolean> {\n    try {\n      const storedUser = await AsyncStorage.getItem('biometric_user');\n      return !!storedUser;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  /**\n   * Clear all stored authentication data\n   */\n  async clearAuthData(): Promise<void> {\n    try {\n      const keys = await AsyncStorage.getAllKeys();\n      const authKeys = keys.filter(\n        key =>\n          key.startsWith('magic_link_') ||\n          key.startsWith('otp_') ||\n          key === 'biometric_user',\n      );\n\n      if (authKeys.length > 0) {\n        await AsyncStorage.multiRemove(authKeys);\n      }\n    } catch (error) {\n      console.error('Error clearing auth data:', error);\n    }\n  }\n}\n\nexport const passwordlessAuthService = new PasswordlessAuthService();\n"], "mappings": ";;;;;;;;AAoBA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,mBAAA,GAAAD,uBAAA,CAAAF,OAAA;AAEA,IAAAI,YAAA,GAAAJ,OAAA;AAA4C,SAAAE,wBAAAG,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAL,uBAAA,YAAAA,wBAAAG,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,IAwCtCmB,uBAAuB;EAAA,SAAAA,wBAAA;IAAA,IAAAC,gBAAA,CAAAX,OAAA,QAAAU,uBAAA;IAAA,KACVE,iBAAiB,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;IAAA,KAClCC,UAAU,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI;IAAA,KAC1BC,gBAAgB,GAAG,CAAC;IAAA,KACpBC,iBAAiB,GAAG,EAAE,GAAG,IAAI;IAAA,KAC7BC,uBAAuB,GAAG,CAAC;IAAA,KAEpCC,aAAa,GACnB,IAAIC,GAAG,CAAC,CAAC;EAAA;EAAA,WAAAC,aAAA,CAAAnB,OAAA,EAAAU,uBAAA;IAAAU,GAAA;IAAAC,KAAA,EAKX,SAAQC,cAAcA,CAACC,UAAkB,EAAW;MAClD,IAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;MACtB,IAAME,MAAM,GAAG,IAAI,CAACT,aAAa,CAACf,GAAG,CAACqB,UAAU,CAAC;MAEjD,IAAI,CAACG,MAAM,IAAIF,GAAG,GAAGE,MAAM,CAACC,SAAS,EAAE;QACrC,IAAI,CAACV,aAAa,CAACd,GAAG,CAACoB,UAAU,EAAE;UACjCK,KAAK,EAAE,CAAC;UACRD,SAAS,EAAEH,GAAG,GAAG,IAAI,CAACT;QACxB,CAAC,CAAC;QACF,OAAO,KAAK;MACd;MAEA,IAAIW,MAAM,CAACE,KAAK,IAAI,IAAI,CAACZ,uBAAuB,EAAE;QAChD,OAAO,IAAI;MACb;MAEAU,MAAM,CAACE,KAAK,EAAE;MACd,OAAO,KAAK;IACd;EAAC;IAAAR,GAAA;IAAAC,KAAA;MAAA,IAAAQ,oBAAA,OAAAC,kBAAA,CAAA9B,OAAA,EAKD,aAAqD;QACnD,IAAM+B,WAAW,SAAS7C,MAAM,CAAC8C,mBAAmB,CAAC,EAAE,CAAC;QACxD,OAAOC,KAAK,CAACC,IAAI,CAACH,WAAW,EAAE,UAAAI,IAAI;UAAA,OACjCA,IAAI,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QAAA,CACpC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;MACZ,CAAC;MAAA,SALaC,mBAAmBA,CAAA;QAAA,OAAAV,oBAAA,CAAAW,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAnBF,mBAAmB;IAAA;EAAA;IAAAnB,GAAA;IAAAC,KAAA,EAUjC,SAAQqB,eAAeA,CAAA,EAAW;MAChC,OAAOC,IAAI,CAACC,KAAK,CAAC,MAAM,GAAGD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAACT,QAAQ,CAAC,CAAC;IAC/D;EAAC;IAAAhB,GAAA;IAAAC,KAAA;MAAA,IAAAyB,cAAA,OAAAhB,kBAAA,CAAA9B,OAAA,EAKD,WAAoB+C,KAAa,EAAuB;QACtD,IAAI;UAEF,IAAI,IAAI,CAACzB,cAAc,CAACyB,KAAK,CAAC,EAAE;YAC9B,OAAO;cACLC,OAAO,EAAE,KAAK;cACdC,KAAK,EAAE;YACT,CAAC;UACH;UAGA,IAAMC,UAAU,GAAG,4BAA4B;UAC/C,IAAI,CAACA,UAAU,CAACC,IAAI,CAACJ,KAAK,CAAC,EAAE;YAC3B,OAAO;cACLC,OAAO,EAAE,KAAK;cACdC,KAAK,EAAE;YACT,CAAC;UACH;UAGA,IAAMG,KAAK,SAAS,IAAI,CAACb,mBAAmB,CAAC,CAAC;UAC9C,IAAMc,SAAS,GAAG5B,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG,IAAI,CAACZ,iBAAiB;UAGrD,IAAM0C,aAA4B,GAAG;YACnCP,KAAK,EAALA,KAAK;YACLK,KAAK,EAALA,KAAK;YACLC,SAAS,EAATA,SAAS;YACTE,IAAI,EAAE;UACR,CAAC;UAED,MAAMC,qBAAY,CAACC,OAAO,CACxB,cAAcL,KAAK,EAAE,EACrBM,IAAI,CAACC,SAAS,CAACL,aAAa,CAC9B,CAAC;UAIDM,OAAO,CAACC,GAAG,CACT,qBAAqBd,KAAK,+BAA+BK,KAAK,EAChE,CAAC;UAED,OAAO;YACLJ,OAAO,EAAE,IAAI;YACbc,oBAAoB,EAAE,IAAI;YAC1BC,cAAc,EAAEX;UAClB,CAAC;QACH,CAAC,CAAC,OAAOH,KAAK,EAAE;UACdW,OAAO,CAACX,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjD,OAAO;YACLD,OAAO,EAAE,KAAK;YACdC,KAAK,EAAE;UACT,CAAC;QACH;MACF,CAAC;MAAA,SAtDKe,aAAaA,CAAAC,EAAA;QAAA,OAAAnB,cAAA,CAAAN,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAbuB,aAAa;IAAA;EAAA;IAAA5C,GAAA;IAAAC,KAAA;MAAA,IAAA6C,gBAAA,OAAApC,kBAAA,CAAA9B,OAAA,EA2DnB,WAAsBoD,KAAa,EAAuB;QACxD,IAAI;UACF,IAAMe,UAAU,SAASX,qBAAY,CAACY,OAAO,CAAC,cAAchB,KAAK,EAAE,CAAC;UACpE,IAAI,CAACe,UAAU,EAAE;YACf,OAAO;cACLnB,OAAO,EAAE,KAAK;cACdC,KAAK,EAAE;YACT,CAAC;UACH;UAEA,IAAMK,aAA4B,GAAGI,IAAI,CAACW,KAAK,CAACF,UAAU,CAAC;UAG3D,IAAI1C,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG8B,aAAa,CAACD,SAAS,EAAE;YACxC,MAAMG,qBAAY,CAACc,UAAU,CAAC,cAAclB,KAAK,EAAE,CAAC;YACpD,OAAO;cACLJ,OAAO,EAAE,KAAK;cACdC,KAAK,EAAE;YACT,CAAC;UACH;UAGA,IAAIK,aAAa,CAACC,IAAI,EAAE;YACtB,OAAO;cACLP,OAAO,EAAE,KAAK;cACdC,KAAK,EAAE;YACT,CAAC;UACH;UAGAK,aAAa,CAACC,IAAI,GAAG,IAAI;UACzB,MAAMC,qBAAY,CAACC,OAAO,CACxB,cAAcL,KAAK,EAAE,EACrBM,IAAI,CAACC,SAAS,CAACL,aAAa,CAC9B,CAAC;UAGD,IAAMiB,UAAU,SAASC,wBAAW,CAACC,qBAAqB,CACxDnB,aAAa,CAACP,KAChB,CAAC;UAGD,MAAMS,qBAAY,CAACc,UAAU,CAAC,cAAclB,KAAK,EAAE,CAAC;UAEpD,OAAOmB,UAAU;QACnB,CAAC,CAAC,OAAOtB,KAAK,EAAE;UACdW,OAAO,CAACX,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;UACnD,OAAO;YACLD,OAAO,EAAE,KAAK;YACdC,KAAK,EAAE;UACT,CAAC;QACH;MACF,CAAC;MAAA,SApDKyB,eAAeA,CAAAC,GAAA;QAAA,OAAAT,gBAAA,CAAA1B,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAfiC,eAAe;IAAA;EAAA;IAAAtD,GAAA;IAAAC,KAAA;MAAA,IAAAuD,QAAA,OAAA9C,kBAAA,CAAA9B,OAAA,EAyDrB,WAAc6E,KAAa,EAAuB;QAChD,IAAI;UAEF,IAAI,IAAI,CAACvD,cAAc,CAACuD,KAAK,CAAC,EAAE;YAC9B,OAAO;cACL7B,OAAO,EAAE,KAAK;cACdC,KAAK,EAAE;YACT,CAAC;UACH;UAGA,IAAM6B,UAAU,GAAG,wBAAwB;UAC3C,IAAI,CAACA,UAAU,CAAC3B,IAAI,CAAC0B,KAAK,CAAC,EAAE;YAC3B,OAAO;cACL7B,OAAO,EAAE,KAAK;cACdC,KAAK,EAAE;YACT,CAAC;UACH;UAGA,IAAM8B,IAAI,GAAG,IAAI,CAACrC,eAAe,CAAC,CAAC;UACnC,IAAMW,SAAS,GAAG5B,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG,IAAI,CAACX,UAAU;UAG9C,IAAMmE,OAAgB,GAAG;YACvBH,KAAK,EAALA,KAAK;YACLE,IAAI,EAAJA,IAAI;YACJ1B,SAAS,EAATA,SAAS;YACT4B,QAAQ,EAAE,CAAC;YACXC,WAAW,EAAE,IAAI,CAACpE;UACpB,CAAC;UAED,MAAM0C,qBAAY,CAACC,OAAO,CAAC,OAAOoB,KAAK,EAAE,EAAEnB,IAAI,CAACC,SAAS,CAACqB,OAAO,CAAC,CAAC;UAInEpB,OAAO,CAACC,GAAG,CAAC,cAAcgB,KAAK,KAAKE,IAAI,EAAE,CAAC;UAE3C,OAAO;YACL/B,OAAO,EAAE,IAAI;YACbc,oBAAoB,EAAE,IAAI;YAC1BC,cAAc,EAAEc;UAClB,CAAC;QACH,CAAC,CAAC,OAAO5B,KAAK,EAAE;UACdW,OAAO,CAACX,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;UAC1C,OAAO;YACLD,OAAO,EAAE,KAAK;YACdC,KAAK,EAAE;UACT,CAAC;QACH;MACF,CAAC;MAAA,SAlDKkC,OAAOA,CAAAC,GAAA;QAAA,OAAAR,QAAA,CAAApC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAP0C,OAAO;IAAA;EAAA;IAAA/D,GAAA;IAAAC,KAAA;MAAA,IAAAgE,UAAA,OAAAvD,kBAAA,CAAA9B,OAAA,EAuDb,WAAgB6E,KAAa,EAAEE,IAAY,EAAuB;QAChE,IAAI;UACF,IAAMZ,UAAU,SAASX,qBAAY,CAACY,OAAO,CAAC,OAAOS,KAAK,EAAE,CAAC;UAC7D,IAAI,CAACV,UAAU,EAAE;YACf,OAAO;cACLnB,OAAO,EAAE,KAAK;cACdC,KAAK,EAAE;YACT,CAAC;UACH;UAEA,IAAM+B,OAAgB,GAAGtB,IAAI,CAACW,KAAK,CAACF,UAAU,CAAC;UAG/C,IAAI1C,IAAI,CAACD,GAAG,CAAC,CAAC,GAAGwD,OAAO,CAAC3B,SAAS,EAAE;YAClC,MAAMG,qBAAY,CAACc,UAAU,CAAC,OAAOO,KAAK,EAAE,CAAC;YAC7C,OAAO;cACL7B,OAAO,EAAE,KAAK;cACdC,KAAK,EAAE;YACT,CAAC;UACH;UAGA,IAAI+B,OAAO,CAACC,QAAQ,IAAID,OAAO,CAACE,WAAW,EAAE;YAC3C,MAAM1B,qBAAY,CAACc,UAAU,CAAC,OAAOO,KAAK,EAAE,CAAC;YAC7C,OAAO;cACL7B,OAAO,EAAE,KAAK;cACdC,KAAK,EAAE;YACT,CAAC;UACH;UAGA,IAAI+B,OAAO,CAACD,IAAI,KAAKA,IAAI,EAAE;YACzBC,OAAO,CAACC,QAAQ,EAAE;YAClB,MAAMzB,qBAAY,CAACC,OAAO,CAAC,OAAOoB,KAAK,EAAE,EAAEnB,IAAI,CAACC,SAAS,CAACqB,OAAO,CAAC,CAAC;YAEnE,IAAMM,iBAAiB,GAAGN,OAAO,CAACE,WAAW,GAAGF,OAAO,CAACC,QAAQ;YAChE,OAAO;cACLjC,OAAO,EAAE,KAAK;cACdC,KAAK,EAAE,gBAAgBqC,iBAAiB;YAC1C,CAAC;UACH;UAGA,IAAMf,UAAU,SAASC,wBAAW,CAACe,qBAAqB,CAACV,KAAK,CAAC;UAGjE,MAAMrB,qBAAY,CAACc,UAAU,CAAC,OAAOO,KAAK,EAAE,CAAC;UAE7C,OAAON,UAAU;QACnB,CAAC,CAAC,OAAOtB,KAAK,EAAE;UACdW,OAAO,CAACX,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,OAAO;YACLD,OAAO,EAAE,KAAK;YACdC,KAAK,EAAE;UACT,CAAC;QACH;MACF,CAAC;MAAA,SAxDKuC,SAASA,CAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAL,UAAA,CAAA7C,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAT+C,SAAS;IAAA;EAAA;IAAApE,GAAA;IAAAC,KAAA;MAAA,IAAAsE,2BAAA,OAAA7D,kBAAA,CAAA9B,OAAA,EA6Df,aAEuB;QAAA,IADrB4F,OAAyB,GAAAnD,SAAA,CAAAoD,MAAA,QAAApD,SAAA,QAAAqD,SAAA,GAAArD,SAAA,MAAG,CAAC,CAAC;QAE9B,IAAI;UAEF,IAAMsD,WAAW,SAAS3G,mBAAmB,CAAC4G,gBAAgB,CAAC,CAAC;UAChE,IAAI,CAACD,WAAW,EAAE;YAChB,OAAO;cACL/C,OAAO,EAAE,KAAK;cACdC,KAAK,EAAE;YACT,CAAC;UACH;UAEA,IAAMgD,UAAU,SAAS7G,mBAAmB,CAAC8G,eAAe,CAAC,CAAC;UAC9D,IAAI,CAACD,UAAU,EAAE;YACf,OAAO;cACLjD,OAAO,EAAE,KAAK;cACdC,KAAK,EAAE;YACT,CAAC;UACH;UAGA,IAAMkD,MAAM,SAAS/G,mBAAmB,CAACgH,iBAAiB,CAAC;YACzDC,aAAa,EAAET,OAAO,CAACS,aAAa,IAAI,yBAAyB;YACjEC,aAAa,EAAEV,OAAO,CAACU,aAAa,IAAI,cAAc;YACtDC,qBAAqB,EAAEX,OAAO,CAACW,qBAAqB,IAAI;UAC1D,CAAC,CAAC;UAEF,IAAI,CAACJ,MAAM,CAACnD,OAAO,EAAE;YACnB,OAAO;cACLA,OAAO,EAAE,KAAK;cACdC,KAAK,EAAEkD,MAAM,CAAClD,KAAK,IAAI;YACzB,CAAC;UACH;UAGA,IAAMuD,UAAU,SAAShD,qBAAY,CAACY,OAAO,CAAC,gBAAgB,CAAC;UAC/D,IAAI,CAACoC,UAAU,EAAE;YACf,OAAO;cACLxD,OAAO,EAAE,KAAK;cACdC,KAAK,EACH;YACJ,CAAC;UACH;UAEA,IAAMwD,QAAQ,GAAG/C,IAAI,CAACW,KAAK,CAACmC,UAAU,CAAC;UACvC,IAAMjC,UAAU,SAASC,wBAAW,CAACkC,yBAAyB,CAACD,QAAQ,CAAC;UAExE,OAAOlC,UAAU;QACnB,CAAC,CAAC,OAAOtB,KAAK,EAAE;UACdW,OAAO,CAACX,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;UAC5D,OAAO;YACLD,OAAO,EAAE,KAAK;YACdC,KAAK,EAAE;UACT,CAAC;QACH;MACF,CAAC;MAAA,SAxDK0D,0BAA0BA,CAAA;QAAA,OAAAhB,2BAAA,CAAAnD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAA1BkE,0BAA0B;IAAA;EAAA;IAAAvF,GAAA;IAAAC,KAAA;MAAA,IAAAuF,mBAAA,OAAA9E,kBAAA,CAAA9B,OAAA,EA6DhC,WAAyB6G,IAAS,EAAoB;QACpD,IAAI;UACF,MAAMrD,qBAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEC,IAAI,CAACC,SAAS,CAACkD,IAAI,CAAC,CAAC;UAClE,OAAO,IAAI;QACb,CAAC,CAAC,OAAO5D,KAAK,EAAE;UACdW,OAAO,CAACX,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;UACxD,OAAO,KAAK;QACd;MACF,CAAC;MAAA,SARK6D,kBAAkBA,CAAAC,GAAA;QAAA,OAAAH,mBAAA,CAAApE,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAlBqE,kBAAkB;IAAA;EAAA;IAAA1F,GAAA;IAAAC,KAAA;MAAA,IAAA2F,iBAAA,OAAAlF,kBAAA,CAAA9B,OAAA,EAaxB,aAA2C;QACzC,IAAI;UACF,IAAMwG,UAAU,SAAShD,qBAAY,CAACY,OAAO,CAAC,gBAAgB,CAAC;UAC/D,OAAO,CAAC,CAACoC,UAAU;QACrB,CAAC,CAAC,OAAOvD,KAAK,EAAE;UACd,OAAO,KAAK;QACd;MACF,CAAC;MAAA,SAPKgE,gBAAgBA,CAAA;QAAA,OAAAD,iBAAA,CAAAxE,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAhBwE,gBAAgB;IAAA;EAAA;IAAA7F,GAAA;IAAAC,KAAA;MAAA,IAAA6F,cAAA,OAAApF,kBAAA,CAAA9B,OAAA,EAYtB,aAAqC;QACnC,IAAI;UACF,IAAMmH,IAAI,SAAS3D,qBAAY,CAAC4D,UAAU,CAAC,CAAC;UAC5C,IAAMC,QAAQ,GAAGF,IAAI,CAACG,MAAM,CAC1B,UAAAlG,GAAG;YAAA,OACDA,GAAG,CAACmG,UAAU,CAAC,aAAa,CAAC,IAC7BnG,GAAG,CAACmG,UAAU,CAAC,MAAM,CAAC,IACtBnG,GAAG,KAAK,gBAAgB;UAAA,CAC5B,CAAC;UAED,IAAIiG,QAAQ,CAACxB,MAAM,GAAG,CAAC,EAAE;YACvB,MAAMrC,qBAAY,CAACgE,WAAW,CAACH,QAAQ,CAAC;UAC1C;QACF,CAAC,CAAC,OAAOpE,KAAK,EAAE;UACdW,OAAO,CAACX,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACnD;MACF,CAAC;MAAA,SAhBKwE,aAAaA,CAAA;QAAA,OAAAP,cAAA,CAAA1E,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAbgF,aAAa;IAAA;EAAA;AAAA;AAmBd,IAAMC,uBAAuB,GAAAC,OAAA,CAAAD,uBAAA,GAAG,IAAIhH,uBAAuB,CAAC,CAAC", "ignoreList": []}