{"version": 3, "names": ["_UnifiedErrorHandlingService", "require", "_UserFeedbackService", "_AnalyticsIntegrationService", "_types", "_hooks", "_UnifiedErrorBoundary", "_migration<PERSON><PERSON>per", "_unifiedErrorHandlingService", "console", "error", "Error", "unifiedErrorHandlingService", "exports", "_default", "default", "handleError", "context", "handleNetworkError", "handleAuthError", "handleValidationError", "handleWebSocketError", "addErrorListener", "listener", "addRecoveryStrategy", "strategy", "getErrorMetrics", "getMetrics", "clearErrorQueue", "updateErrorConfig", "config", "updateConfig", "getMonitoringMetrics", "getSystemHealth", "getErrorTrends", "days", "exportErrorData", "format", "clearMonitoringHistory", "createMigrationWrapper", "arguments", "length", "undefined", "userMessage", "handleErrorWithRecovery", "reportError", "severity", "Object", "assign", "reportErrorWithBreadcrumbs", "handleRuntimeError", "initialize", "catch"], "sources": ["index.ts"], "sourcesContent": ["/**\n * Unified Error Handling System - Main Export\n * \n * Central export point for the unified error handling system.\n * This replaces all existing error handling services and provides\n * a single, consistent API for error handling across the application.\n * \n * @version 2.0.0\n * <AUTHOR> Development Team\n */\n\n// Core Service - Import and re-export to avoid circular dependencies\nimport { unifiedErrorHandlingService as _unifiedErrorHandlingService } from './UnifiedErrorHandlingService';\n\n// Add runtime validation for Hermes compatibility\nif (!_unifiedErrorHandlingService) {\n  console.error('[UnifiedErrorHandling] Service instance is undefined - this indicates a module loading issue');\n  throw new Error('UnifiedErrorHandlingService failed to initialize properly');\n}\n\nexport const unifiedErrorHandlingService = _unifiedErrorHandlingService;\nexport default unifiedErrorHandlingService;\n\n// Sub-services\nexport {\n  userFeedbackService\n} from './UserFeedbackService';\n\nexport {\n  analyticsIntegrationService\n} from './AnalyticsIntegrationService';\n\n// Types\nexport type {\n  ErrorType,\n  ErrorSeverity,\n  ErrorCategory,\n  ErrorContext,\n  ErrorBreadcrumb,\n  RecoveryStrategy,\n  UserFeedbackConfig,\n  ErrorReport,\n  UnifiedErrorHandlingConfig,\n  ErrorListener,\n  ErrorMetrics\n} from './types';\n\nexport { \n  UnifiedError,\n  ErrorType,\n  ErrorSeverity,\n  ErrorCategory\n} from './types';\n\n// Hooks\nexport {\n  useUnifiedErrorHandling,\n  useErrorMetrics,\n  useGlobalErrorListener,\n  useErrorBoundary,\n  useApiErrorHandling,\n  useFormErrorHandling,\n  useWebSocketErrorHandling\n} from './hooks';\n\nexport type {\n  UseUnifiedErrorHandlingOptions,\n  UseUnifiedErrorHandlingResult\n} from './hooks';\n\n// Components\nexport {\n  UnifiedErrorBoundary,\n  ScreenErrorBoundary,\n  FeatureErrorBoundary,\n  ComponentErrorBoundary\n} from './UnifiedErrorBoundary';\n\n// Convenience Functions\nexport const handleError = (\n  error: Error | string,\n  context?: Partial<ErrorContext>\n) => _unifiedErrorHandlingService.handleError(error, context);\n\nexport const handleNetworkError = (\n  error: Error,\n  context?: Partial<ErrorContext>\n) => _unifiedErrorHandlingService.handleNetworkError(error, context);\n\nexport const handleAuthError = (\n  error: Error,\n  context?: Partial<ErrorContext>\n) => _unifiedErrorHandlingService.handleAuthError(error, context);\n\nexport const handleValidationError = (\n  error: Error,\n  context?: Partial<ErrorContext>\n) => _unifiedErrorHandlingService.handleValidationError(error, context);\n\nexport const handleWebSocketError = (\n  error: Error,\n  context?: Partial<ErrorContext>\n) => _unifiedErrorHandlingService.handleWebSocketError(error, context);\n\nexport const addErrorListener = (listener: ErrorListener) =>\n  _unifiedErrorHandlingService.addErrorListener(listener);\n\nexport const addRecoveryStrategy = (strategy: RecoveryStrategy) =>\n  _unifiedErrorHandlingService.addRecoveryStrategy(strategy);\n\nexport const getErrorMetrics = () =>\n  _unifiedErrorHandlingService.getMetrics();\n\nexport const clearErrorQueue = () =>\n  _unifiedErrorHandlingService.clearErrorQueue();\n\nexport const updateErrorConfig = (config: Partial<UnifiedErrorHandlingConfig>) =>\n  _unifiedErrorHandlingService.updateConfig(config);\n\n// Error Monitoring Functions\nexport const getMonitoringMetrics = () =>\n  _unifiedErrorHandlingService.getMonitoringMetrics();\n\nexport const getSystemHealth = () =>\n  _unifiedErrorHandlingService.getSystemHealth();\n\nexport const getErrorTrends = (days?: number) =>\n  _unifiedErrorHandlingService.getErrorTrends(days);\n\nexport const exportErrorData = (format?: 'json' | 'csv') =>\n  _unifiedErrorHandlingService.exportErrorData(format);\n\nexport const clearMonitoringHistory = () =>\n  _unifiedErrorHandlingService.clearMonitoringHistory();\n\n// Migration Helpers\nexport {\n  migrationUtils,\n  legacyErrorHandlingService,\n  legacyEnhancedErrorHandlingService,\n  legacyErrorMonitoringService,\n  legacyErrorReportingService,\n  legacyRuntimeErrorHandler\n} from './migrationHelper';\n/**\n * Migration helper to replace existing error handling services\n * This provides backward compatibility while transitioning to the unified system\n */\nexport const createMigrationWrapper = () => {\n  return {\n    // Legacy errorHandlingService compatibility\n    handleError: (error: any, context: any = {}, userMessage?: string) =>\n      _unifiedErrorHandlingService.handleError(error, context, userMessage),\n\n    // Legacy enhancedErrorHandlingService compatibility\n    handleErrorWithRecovery: (error: any, context: any = {}) =>\n      _unifiedErrorHandlingService.handleError(error, context),\n\n    // Legacy errorMonitoringService compatibility\n    reportError: (error: any, context: any = {}, severity: any = 'medium') =>\n      _unifiedErrorHandlingService.handleError(error, { ...context, severity }),\n\n    // Legacy ErrorReportingService compatibility\n    reportErrorWithBreadcrumbs: (error: any, context: any = {}) =>\n      _unifiedErrorHandlingService.handleError(error, context),\n\n    // Legacy runtimeErrorHandler compatibility\n    handleRuntimeError: (error: any, context: any = {}) =>\n      _unifiedErrorHandlingService.handleError(error, context)\n  };\n};\n\n// Initialize service on import\nunifiedErrorHandlingService.initialize().catch(error => {\n  console.error('Failed to initialize unified error handling service:', error);\n});\n\n/**\n * Usage Examples:\n * \n * // Basic error handling\n * import { handleError } from './services/unifiedErrorHandling';\n * \n * try {\n *   await someOperation();\n * } catch (error) {\n *   await handleError(error, { component: 'MyComponent', action: 'someOperation' });\n * }\n * \n * // Using hooks in components\n * import { useUnifiedErrorHandling } from './services/unifiedErrorHandling';\n * \n * const MyComponent = () => {\n *   const { handleError, withErrorHandling } = useUnifiedErrorHandling({\n *     component: 'MyComponent'\n *   });\n * \n *   const loadData = async () => {\n *     const { data, error } = await withErrorHandling(\n *       () => apiClient.getData(),\n *       { action: 'loadData' }\n *     );\n *     \n *     if (data) {\n *       setData(data);\n *     }\n *   };\n * \n *   return <div>...</div>;\n * };\n * \n * // Using error boundaries\n * import { ScreenErrorBoundary } from './services/unifiedErrorHandling';\n * \n * const MyScreen = () => (\n *   <ScreenErrorBoundary screenName=\"MyScreen\">\n *     <MyScreenContent />\n *   </ScreenErrorBoundary>\n * );\n * \n * // API error handling\n * import { useApiErrorHandling } from './services/unifiedErrorHandling';\n * \n * const apiService = {\n *   async getData() {\n *     const { handleApiError } = useApiErrorHandling('DataService');\n *     \n *     try {\n *       return await fetch('/api/data');\n *     } catch (error) {\n *       await handleApiError(error, '/api/data', 'GET');\n *       throw error;\n *     }\n *   }\n * };\n */\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,IAAAA,4BAAA,GAAAC,OAAA;AAYA,IAAAC,oBAAA,GAAAD,OAAA;AAIA,IAAAE,4BAAA,GAAAF,OAAA;AAmBA,IAAAG,MAAA,GAAAH,OAAA;AAQA,IAAAI,MAAA,GAAAJ,OAAA;AAgBA,IAAAK,qBAAA,GAAAL,OAAA;AAiEA,IAAAM,gBAAA,GAAAN,OAAA;AAzHA,IAAI,CAACO,wDAA4B,EAAE;EACjCC,OAAO,CAACC,KAAK,CAAC,8FAA8F,CAAC;EAC7G,MAAM,IAAIC,KAAK,CAAC,2DAA2D,CAAC;AAC9E;AAEO,IAAMC,2BAA2B,GAAAC,OAAA,CAAAD,2BAAA,GAAGJ,wDAA4B;AAAC,IAAAM,QAAA,GAAAD,OAAA,CAAAE,OAAA,GACzDH,2BAA2B;AA0DnC,IAAMI,WAAW,GAAAH,OAAA,CAAAG,WAAA,GAAG,SAAdA,WAAWA,CACtBN,KAAqB,EACrBO,OAA+B;EAAA,OAC5BT,wDAA4B,CAACQ,WAAW,CAACN,KAAK,EAAEO,OAAO,CAAC;AAAA;AAEtD,IAAMC,kBAAkB,GAAAL,OAAA,CAAAK,kBAAA,GAAG,SAArBA,kBAAkBA,CAC7BR,KAAY,EACZO,OAA+B;EAAA,OAC5BT,wDAA4B,CAACU,kBAAkB,CAACR,KAAK,EAAEO,OAAO,CAAC;AAAA;AAE7D,IAAME,eAAe,GAAAN,OAAA,CAAAM,eAAA,GAAG,SAAlBA,eAAeA,CAC1BT,KAAY,EACZO,OAA+B;EAAA,OAC5BT,wDAA4B,CAACW,eAAe,CAACT,KAAK,EAAEO,OAAO,CAAC;AAAA;AAE1D,IAAMG,qBAAqB,GAAAP,OAAA,CAAAO,qBAAA,GAAG,SAAxBA,qBAAqBA,CAChCV,KAAY,EACZO,OAA+B;EAAA,OAC5BT,wDAA4B,CAACY,qBAAqB,CAACV,KAAK,EAAEO,OAAO,CAAC;AAAA;AAEhE,IAAMI,oBAAoB,GAAAR,OAAA,CAAAQ,oBAAA,GAAG,SAAvBA,oBAAoBA,CAC/BX,KAAY,EACZO,OAA+B;EAAA,OAC5BT,wDAA4B,CAACa,oBAAoB,CAACX,KAAK,EAAEO,OAAO,CAAC;AAAA;AAE/D,IAAMK,gBAAgB,GAAAT,OAAA,CAAAS,gBAAA,GAAG,SAAnBA,gBAAgBA,CAAIC,QAAuB;EAAA,OACtDf,wDAA4B,CAACc,gBAAgB,CAACC,QAAQ,CAAC;AAAA;AAElD,IAAMC,mBAAmB,GAAAX,OAAA,CAAAW,mBAAA,GAAG,SAAtBA,mBAAmBA,CAAIC,QAA0B;EAAA,OAC5DjB,wDAA4B,CAACgB,mBAAmB,CAACC,QAAQ,CAAC;AAAA;AAErD,IAAMC,eAAe,GAAAb,OAAA,CAAAa,eAAA,GAAG,SAAlBA,eAAeA,CAAA;EAAA,OAC1BlB,wDAA4B,CAACmB,UAAU,CAAC,CAAC;AAAA;AAEpC,IAAMC,eAAe,GAAAf,OAAA,CAAAe,eAAA,GAAG,SAAlBA,eAAeA,CAAA;EAAA,OAC1BpB,wDAA4B,CAACoB,eAAe,CAAC,CAAC;AAAA;AAEzC,IAAMC,iBAAiB,GAAAhB,OAAA,CAAAgB,iBAAA,GAAG,SAApBA,iBAAiBA,CAAIC,MAA2C;EAAA,OAC3EtB,wDAA4B,CAACuB,YAAY,CAACD,MAAM,CAAC;AAAA;AAG5C,IAAME,oBAAoB,GAAAnB,OAAA,CAAAmB,oBAAA,GAAG,SAAvBA,oBAAoBA,CAAA;EAAA,OAC/BxB,wDAA4B,CAACwB,oBAAoB,CAAC,CAAC;AAAA;AAE9C,IAAMC,eAAe,GAAApB,OAAA,CAAAoB,eAAA,GAAG,SAAlBA,eAAeA,CAAA;EAAA,OAC1BzB,wDAA4B,CAACyB,eAAe,CAAC,CAAC;AAAA;AAEzC,IAAMC,cAAc,GAAArB,OAAA,CAAAqB,cAAA,GAAG,SAAjBA,cAAcA,CAAIC,IAAa;EAAA,OAC1C3B,wDAA4B,CAAC0B,cAAc,CAACC,IAAI,CAAC;AAAA;AAE5C,IAAMC,eAAe,GAAAvB,OAAA,CAAAuB,eAAA,GAAG,SAAlBA,eAAeA,CAAIC,MAAuB;EAAA,OACrD7B,wDAA4B,CAAC4B,eAAe,CAACC,MAAM,CAAC;AAAA;AAE/C,IAAMC,sBAAsB,GAAAzB,OAAA,CAAAyB,sBAAA,GAAG,SAAzBA,sBAAsBA,CAAA;EAAA,OACjC9B,wDAA4B,CAAC8B,sBAAsB,CAAC,CAAC;AAAA;AAehD,IAAMC,sBAAsB,GAAA1B,OAAA,CAAA0B,sBAAA,GAAG,SAAzBA,sBAAsBA,CAAA,EAAS;EAC1C,OAAO;IAELvB,WAAW,EAAE,SAAbA,WAAWA,CAAGN,KAAU;MAAA,IAAEO,OAAY,GAAAuB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,IAAEG,WAAoB,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;MAAA,OAC/DlC,wDAA4B,CAACQ,WAAW,CAACN,KAAK,EAAEO,OAAO,EAAE0B,WAAW,CAAC;IAAA;IAGvEC,uBAAuB,EAAE,SAAzBA,uBAAuBA,CAAGlC,KAAU;MAAA,IAAEO,OAAY,GAAAuB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,OACrDhC,wDAA4B,CAACQ,WAAW,CAACN,KAAK,EAAEO,OAAO,CAAC;IAAA;IAG1D4B,WAAW,EAAE,SAAbA,WAAWA,CAAGnC,KAAU;MAAA,IAAEO,OAAY,GAAAuB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,IAAEM,QAAa,GAAAN,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,QAAQ;MAAA,OACnEhC,wDAA4B,CAACQ,WAAW,CAACN,KAAK,EAAAqC,MAAA,CAAAC,MAAA,KAAO/B,OAAO;QAAE6B,QAAQ,EAARA;MAAQ,EAAE,CAAC;IAAA;IAG3EG,0BAA0B,EAAE,SAA5BA,0BAA0BA,CAAGvC,KAAU;MAAA,IAAEO,OAAY,GAAAuB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,OACxDhC,wDAA4B,CAACQ,WAAW,CAACN,KAAK,EAAEO,OAAO,CAAC;IAAA;IAG1DiC,kBAAkB,EAAE,SAApBA,kBAAkBA,CAAGxC,KAAU;MAAA,IAAEO,OAAY,GAAAuB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,OAChDhC,wDAA4B,CAACQ,WAAW,CAACN,KAAK,EAAEO,OAAO,CAAC;IAAA;EAC5D,CAAC;AACH,CAAC;AAGDL,2BAA2B,CAACuC,UAAU,CAAC,CAAC,CAACC,KAAK,CAAC,UAAA1C,KAAK,EAAI;EACtDD,OAAO,CAACC,KAAK,CAAC,sDAAsD,EAAEA,KAAK,CAAC;AAC9E,CAAC,CAAC", "ignoreList": []}