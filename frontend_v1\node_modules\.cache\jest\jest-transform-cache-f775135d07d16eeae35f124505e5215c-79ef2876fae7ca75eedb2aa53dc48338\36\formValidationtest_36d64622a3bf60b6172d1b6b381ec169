05e083b221bd9a825b7fd5c6bb54af16
var _formValidation = require("../formValidation");
describe('Enhanced Form Validation System', function () {
  describe('FormValidator Class', function () {
    var validator;
    beforeEach(function () {
      validator = new _formValidation.FormValidator({
        name: _formValidation.CommonValidations.name,
        email: _formValidation.CommonValidations.email,
        phone: _formValidation.CommonValidations.phone,
        password: _formValidation.CommonValidations.password,
        confirmPassword: _formValidation.CommonValidations.confirmPassword('password')
      });
    });
    it('should validate individual fields correctly', function () {
      var result = validator.validateField('name', '<PERSON>', {});
      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });
    it('should detect invalid fields', function () {
      var result = validator.validateField('email', 'invalid-email', {});
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Please enter your email in <NAME_EMAIL>');
    });
    it('should validate entire form', function () {
      var formData = {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        password: 'SecurePass123!',
        confirmPassword: 'SecurePass123!'
      };
      var result = validator.validateForm(formData);
      expect(result.isValid).toBe(true);
      expect(Object.keys(result.errors)).toHaveLength(0);
    });
    it('should detect form validation errors', function () {
      var formData = {
        name: '',
        email: 'invalid-email',
        phone: '123',
        password: 'weak',
        confirmPassword: 'different'
      };
      var result = validator.validateForm(formData);
      expect(result.isValid).toBe(false);
      expect(Object.keys(result.errors).length).toBeGreaterThan(0);
    });
  });
  describe('Service Form Validation', function () {
    var serviceValidator;
    beforeEach(function () {
      serviceValidator = new _formValidation.FormValidator({
        name: {
          required: true,
          minLength: 3,
          maxLength: 100
        },
        description: {
          required: true,
          minLength: 10,
          maxLength: 500
        },
        price: {
          required: true,
          number: true,
          custom: function custom(value) {
            var numValue = Number(value);
            if (isNaN(numValue) || numValue <= 0) {
              return {
                isValid: false,
                error: 'Price must be greater than 0'
              };
            }
            return {
              isValid: true
            };
          }
        },
        duration: {
          required: true,
          number: true,
          custom: function custom(value) {
            var numValue = Number(value);
            if (isNaN(numValue) || numValue <= 0) {
              return {
                isValid: false,
                error: 'Duration must be greater than 0'
              };
            }
            return {
              isValid: true
            };
          }
        }
      });
    });
    it('should validate service name correctly', function () {
      var validResult = serviceValidator.validateField('name', 'Hair Cut & Style', {});
      expect(validResult.isValid).toBe(true);
      var invalidResult = serviceValidator.validateField('name', 'Hi', {});
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.error).toContain('at least 3 characters');
    });
    it('should validate service description', function () {
      var validResult = serviceValidator.validateField('description', 'Professional hair cutting and styling service', {});
      expect(validResult.isValid).toBe(true);
      var invalidResult = serviceValidator.validateField('description', 'Short', {});
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.error).toContain('at least 10 characters');
    });
    it('should validate price correctly', function () {
      var validResult = serviceValidator.validateField('price', '50.00', {});
      expect(validResult.isValid).toBe(true);
      var invalidResult = serviceValidator.validateField('price', '-10', {});
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.error).toContain('greater than 0');
    });
    it('should validate duration correctly', function () {
      var validResult = serviceValidator.validateField('duration', '60', {});
      expect(validResult.isValid).toBe(true);
      var invalidResult = serviceValidator.validateField('duration', '0', {});
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.error).toContain('greater than 0');
    });
  });
  describe('Profile Form Validation', function () {
    var profileValidator;
    beforeEach(function () {
      profileValidator = new _formValidation.FormValidator({
        firstName: _formValidation.CommonValidations.name,
        lastName: _formValidation.CommonValidations.name,
        email: _formValidation.CommonValidations.email,
        phone: {
          required: false,
          phone: true
        },
        dateOfBirth: {
          required: false,
          date: true,
          custom: function custom(value) {
            if (!value) return {
              isValid: true
            };
            var dateRegex = /^\d{4}-\d{2}-\d{2}$/;
            if (!dateRegex.test(value)) {
              return {
                isValid: false,
                error: 'Date must be in YYYY-MM-DD format'
              };
            }
            var date = new Date(value);
            var today = new Date();
            if (date > today) {
              return {
                isValid: false,
                error: 'Date of birth cannot be in the future'
              };
            }
            return {
              isValid: true
            };
          }
        }
      });
    });
    it('should validate profile data correctly', function () {
      var formData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        dateOfBirth: '1990-01-01'
      };
      var result = profileValidator.validateForm(formData);
      expect(result.isValid).toBe(true);
    });
    it('should handle optional fields correctly', function () {
      var formData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '',
        dateOfBirth: ''
      };
      var result = profileValidator.validateForm(formData);
      expect(result.isValid).toBe(true);
    });
    it('should validate date of birth format', function () {
      var invalidResult = profileValidator.validateField('dateOfBirth', '01/01/1990', {});
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.error).toContain('YYYY-MM-DD format');
      var futureResult = profileValidator.validateField('dateOfBirth', '2030-01-01', {});
      expect(futureResult.isValid).toBe(false);
      expect(futureResult.error).toContain('cannot be in the future');
    });
  });
  describe('Real-time Validation Scenarios', function () {
    it('should provide immediate feedback for email validation', function () {
      var emailSteps = ['j', 'jo', 'john', 'john@', 'john@ex', '<EMAIL>'];
      var results = emailSteps.map(function (step) {
        return (0, _formValidation.validateField)(step, _formValidation.CommonValidations.email, {});
      });
      expect(results[results.length - 1].isValid).toBe(true);
      results.slice(0, -1).forEach(function (result) {
        expect(result.isValid).toBe(false);
      });
    });
    it('should handle password confirmation validation', function () {
      var formData = {
        password: 'SecurePass123!'
      };
      var matchingResult = (0, _formValidation.validateField)('SecurePass123!', _formValidation.CommonValidations.confirmPassword('password'), formData);
      expect(matchingResult.isValid).toBe(true);
      var nonMatchingResult = (0, _formValidation.validateField)('DifferentPass123!', _formValidation.CommonValidations.confirmPassword('password'), formData);
      expect(nonMatchingResult.isValid).toBe(false);
      expect(nonMatchingResult.error).toContain('do not match');
    });
  });
  describe('Error Message Quality', function () {
    it('should provide clear, actionable error messages', function () {
      var nameResult = (0, _formValidation.validateField)('', _formValidation.CommonValidations.name, {});
      expect(nameResult.error).toBe('Please fill in this field to continue');
      var emailResult = (0, _formValidation.validateField)('invalid', _formValidation.CommonValidations.email, {});
      expect(emailResult.error).toBe('Please enter your email in <NAME_EMAIL>');
      var phoneResult = (0, _formValidation.validateField)('123', _formValidation.CommonValidations.phone, {});
      expect(phoneResult.error).toBe('Please enter your phone number with area code (e.g., ************)');
    });
    it('should provide specific validation feedback', function () {
      var shortNameResult = (0, _formValidation.validateField)('A', _formValidation.CommonValidations.name, {});
      expect(shortNameResult.error).toContain('2-50 characters');
      var weakPasswordResult = (0, _formValidation.validateField)('123', _formValidation.CommonValidations.password, {});
      expect(weakPasswordResult.error).toContain('at least 8 characters');
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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