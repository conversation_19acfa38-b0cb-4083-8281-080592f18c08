/**
 * Service Catalog Slice - Zustand Store for Service Discovery and Search
 *
 * Store Contract:
 * - Manages service catalog data and search functionality
 * - Handles service filtering, sorting, and categorization
 * - Provides geolocation-based service discovery
 * - Maintains search history and preferences
 * - Supports real-time availability checking
 * - Implements intelligent recommendations
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

export interface Service {
  id: string;
  name: string;
  description: string;
  longDescription?: string;
  category: string;
  subcategory: string;
  tags: string[];
  price: number;
  currency: string;
  duration: number; // in minutes
  images: string[];
  provider: ServiceProvider;
  availability: ServiceAvailability;
  rating: number;
  reviewCount: number;
  isPopular: boolean;
  isFeatured: boolean;
  isAvailable: boolean;
  distance?: number; // in kilometers
  createdAt: string;
  updatedAt: string;
}

export interface ServiceProvider {
  id: string;
  businessName: string;
  displayName: string;
  avatar?: string;
  rating: number;
  reviewCount: number;
  isVerified: boolean;
  responseTime: number; // in minutes
  location: {
    address: string;
    city: string;
    state: string;
    coordinates: {
      latitude: number;
      longitude: number;
    };
  };
  amenities: string[];
  certifications: string[];
}

export interface ServiceAvailability {
  nextAvailable: string | null;
  slotsToday: number;
  slotsThisWeek: number;
  isBookable: boolean;
  advanceBooking: {
    min: number; // hours
    max: number; // days
  };
}

export interface SearchFilters {
  category?: string;
  subcategory?: string;
  priceRange?: {
    min: number;
    max: number;
  };
  rating?: number;
  distance?: number;
  availability?: 'today' | 'this_week' | 'anytime';
  amenities?: string[];
  sortBy?:
    | 'relevance'
    | 'price_low'
    | 'price_high'
    | 'rating'
    | 'distance'
    | 'popularity';
  isVerified?: boolean;
  isPopular?: boolean;
  isFeatured?: boolean;
}

export interface SearchQuery {
  query: string;
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  filters: SearchFilters;
  page: number;
  limit: number;
}

export interface SearchResult {
  services: Service[];
  totalCount: number;
  hasMore: boolean;
  suggestions: string[];
  categories: CategorySummary[];
  priceRanges: PriceRange[];
  popularFilters: FilterOption[];
}

export interface CategorySummary {
  category: string;
  count: number;
  subcategories: Array<{
    name: string;
    count: number;
  }>;
}

export interface PriceRange {
  label: string;
  min: number;
  max: number;
  count: number;
}

export interface FilterOption {
  key: string;
  label: string;
  count: number;
}

export interface SearchHistory {
  id: string;
  query: string;
  filters: SearchFilters;
  timestamp: string;
  resultCount: number;
}

export interface ServiceRecommendation {
  service: Service;
  reason: string;
  score: number;
}

export interface ServiceCatalogState {
  // Search state
  searchQuery: string;
  searchFilters: SearchFilters;
  searchResults: SearchResult | null;
  isSearching: boolean;
  searchError: string | null;

  // Service data
  featuredServices: Service[];
  popularServices: Service[];
  nearbyServices: Service[];
  recommendations: ServiceRecommendation[];

  // Categories
  categories: string[];
  subcategories: Record<string, string[]>;

  // User preferences
  searchHistory: SearchHistory[];
  favoriteServices: string[];
  recentlyViewed: string[];

  // UI state
  isLoading: boolean;
  error: string | null;

  // Location
  userLocation: {
    latitude: number;
    longitude: number;
    address?: string;
  } | null;

  // Actions
  searchServices: (query: SearchQuery) => Promise<void>;
  loadMoreResults: () => Promise<void>;
  updateFilters: (filters: Partial<SearchFilters>) => void;
  clearSearch: () => void;
  loadFeaturedServices: () => Promise<void>;
  loadPopularServices: () => Promise<void>;
  loadNearbyServices: (location: {
    latitude: number;
    longitude: number;
  }) => Promise<void>;
  loadRecommendations: (userId: string) => Promise<void>;
  addToFavorites: (serviceId: string) => void;
  removeFromFavorites: (serviceId: string) => void;
  addToRecentlyViewed: (serviceId: string) => void;
  saveSearchToHistory: (query: SearchQuery, resultCount: number) => void;
  clearSearchHistory: () => void;
  setUserLocation: (location: {
    latitude: number;
    longitude: number;
    address?: string;
  }) => void;
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  searchQuery: '',
  searchFilters: {},
  searchResults: null,
  isSearching: false,
  searchError: null,
  featuredServices: [],
  popularServices: [],
  nearbyServices: [],
  recommendations: [],
  categories: [],
  subcategories: {},
  searchHistory: [],
  favoriteServices: [],
  recentlyViewed: [],
  isLoading: false,
  error: null,
  userLocation: null,
};

export const useServiceCatalogStore = create<ServiceCatalogState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        ...initialState,

        // Search services
        searchServices: async (query: SearchQuery) => {
          set(
            state => ({
              ...state,
              isSearching: true,
              searchError: null,
              searchQuery: query.query,
              searchFilters: query.filters,
            }),
            false,
            'serviceCatalog/searchServices/start',
          );

          try {
            // Import the service discovery API
            const { serviceDiscoveryApi } = await import('../features/service-discovery/services/serviceDiscoveryApi');

            // Use real backend search API
            const searchResponse = await serviceDiscoveryApi.search(query.query, query.filters);

            // Transform backend services to frontend format
            const transformedServices: Service[] = searchResponse.services.results.map(service => ({
              id: service.id,
              name: service.name,
              description: service.description,
              category: service.category || 'General',
              subcategory: service.subcategory || '',
              tags: service.tags || [],
              price: service.base_price || service.price,
              currency: 'CAD',
              duration: service.duration,
              images: service.images || [],
              provider: {
                id: service.provider,
                businessName: service.provider_name || 'Unknown Provider',
                displayName: service.provider_name || 'Unknown Provider',
                rating: 4.5, // Default rating, will be populated from provider data
                reviewCount: 0, // Will be populated from provider data
                isVerified: false, // Will be populated from provider data
                responseTime: 15, // Default response time
                location: {
                  address: service.provider_address || '',
                  city: service.provider_city || '',
                  state: service.provider_state || '',
                  coordinates: { latitude: 43.6532, longitude: -79.3832 }, // Default coordinates
                },
                amenities: [],
                certifications: [],
              },
              availability: {
                nextAvailable: new Date(
                  Date.now() + 2 * 60 * 60 * 1000,
                ).toISOString(),
                slotsToday: 3,
                slotsThisWeek: 15,
                isBookable: true,
                advanceBooking: { min: 2, max: 30 },
              },
              rating: service.rating || 4.5,
              reviewCount: service.review_count || 0,
              isPopular: service.is_popular || false,
              isFeatured: service.is_featured || false,
              isAvailable: service.is_active,
              distance: service.distance || 0,
              createdAt: service.created_at || new Date().toISOString(),
              updatedAt: service.updated_at || new Date().toISOString(),
            }));

            // Create real search result from backend data
            const realResult: SearchResult = {
              services: transformedServices,
              totalCount: searchResponse.services.count,
              hasMore: searchResponse.services.next !== null,
              suggestions: searchResponse.suggestions || [],
              categories: searchResponse.categories || [],
              priceRanges: searchResponse.price_ranges || [
                { label: '$0 - $50', min: 0, max: 50, count: 0 },
                { label: '$50 - $100', min: 50, max: 100, count: 0 },
                { label: '$100+', min: 100, max: 999, count: 0 },
              ],
              popularFilters: searchResponse.popular_filters || [],
            };

            // Save search to history
            get().saveSearchToHistory(query, realResult.totalCount);

            set(
              state => ({
                ...state,
                searchResults: realResult,
                isSearching: false,
                searchError: null,
              }),
              false,
              'serviceCatalog/searchServices/success',
            );
          } catch (error) {
            set(
              state => ({
                ...state,
                isSearching: false,
                searchError:
                  error instanceof Error ? error.message : 'Search failed',
              }),
              false,
              'serviceCatalog/searchServices/error',
            );
          }
        },

        // Load more search results
        loadMoreResults: async () => {
          const currentResults = get().searchResults;
          if (!currentResults || !currentResults.hasMore) return;

          set(
            state => ({ ...state, isSearching: true }),
            false,
            'serviceCatalog/loadMore/start',
          );

          try {
            await new Promise(resolve => setTimeout(resolve, 500));

            // Mock additional results
            const additionalServices: Service[] = [];

            set(
              state => ({
                ...state,
                searchResults: state.searchResults
                  ? {
                      ...state.searchResults,
                      services: [
                        ...state.searchResults.services,
                        ...additionalServices,
                      ],
                      hasMore: false,
                    }
                  : null,
                isSearching: false,
              }),
              false,
              'serviceCatalog/loadMore/success',
            );
          } catch (error) {
            set(
              state => ({
                ...state,
                isSearching: false,
                searchError:
                  error instanceof Error
                    ? error.message
                    : 'Failed to load more results',
              }),
              false,
              'serviceCatalog/loadMore/error',
            );
          }
        },

        // Update search filters
        updateFilters: (filters: Partial<SearchFilters>) => {
          set(
            state => ({
              ...state,
              searchFilters: { ...state.searchFilters, ...filters },
            }),
            false,
            'serviceCatalog/updateFilters',
          );
        },

        // Clear search
        clearSearch: () => {
          set(
            state => ({
              ...state,
              searchQuery: '',
              searchFilters: {},
              searchResults: null,
              searchError: null,
            }),
            false,
            'serviceCatalog/clearSearch',
          );
        },

        // Load featured services
        loadFeaturedServices: async () => {
          set(
            state => ({ ...state, isLoading: true, error: null }),
            false,
            'serviceCatalog/loadFeatured/start',
          );

          try {
            await new Promise(resolve => setTimeout(resolve, 500));

            // Mock featured services
            const featuredServices: Service[] = [];

            set(
              state => ({
                ...state,
                featuredServices,
                isLoading: false,
                error: null,
              }),
              false,
              'serviceCatalog/loadFeatured/success',
            );
          } catch (error) {
            set(
              state => ({
                ...state,
                isLoading: false,
                error:
                  error instanceof Error
                    ? error.message
                    : 'Failed to load featured services',
              }),
              false,
              'serviceCatalog/loadFeatured/error',
            );
          }
        },

        // Load popular services
        loadPopularServices: async () => {
          set(
            state => ({ ...state, isLoading: true, error: null }),
            false,
            'serviceCatalog/loadPopular/start',
          );

          try {
            await new Promise(resolve => setTimeout(resolve, 500));

            const popularServices: Service[] = [];

            set(
              state => ({
                ...state,
                popularServices,
                isLoading: false,
                error: null,
              }),
              false,
              'serviceCatalog/loadPopular/success',
            );
          } catch (error) {
            set(
              state => ({
                ...state,
                isLoading: false,
                error:
                  error instanceof Error
                    ? error.message
                    : 'Failed to load popular services',
              }),
              false,
              'serviceCatalog/loadPopular/error',
            );
          }
        },

        // Load nearby services
        loadNearbyServices: async (location: {
          latitude: number;
          longitude: number;
        }) => {
          set(
            state => ({ ...state, isLoading: true, error: null }),
            false,
            'serviceCatalog/loadNearby/start',
          );

          try {
            await new Promise(resolve => setTimeout(resolve, 500));

            const nearbyServices: Service[] = [];

            set(
              state => ({
                ...state,
                nearbyServices,
                isLoading: false,
                error: null,
              }),
              false,
              'serviceCatalog/loadNearby/success',
            );
          } catch (error) {
            set(
              state => ({
                ...state,
                isLoading: false,
                error:
                  error instanceof Error
                    ? error.message
                    : 'Failed to load nearby services',
              }),
              false,
              'serviceCatalog/loadNearby/error',
            );
          }
        },

        // Load recommendations
        loadRecommendations: async (userId: string) => {
          set(
            state => ({ ...state, isLoading: true, error: null }),
            false,
            'serviceCatalog/loadRecommendations/start',
          );

          try {
            await new Promise(resolve => setTimeout(resolve, 500));

            const recommendations: ServiceRecommendation[] = [];

            set(
              state => ({
                ...state,
                recommendations,
                isLoading: false,
                error: null,
              }),
              false,
              'serviceCatalog/loadRecommendations/success',
            );
          } catch (error) {
            set(
              state => ({
                ...state,
                isLoading: false,
                error:
                  error instanceof Error
                    ? error.message
                    : 'Failed to load recommendations',
              }),
              false,
              'serviceCatalog/loadRecommendations/error',
            );
          }
        },

        // Add to favorites
        addToFavorites: (serviceId: string) => {
          set(
            state => ({
              ...state,
              favoriteServices: [
                ...state.favoriteServices.filter(id => id !== serviceId),
                serviceId,
              ],
            }),
            false,
            'serviceCatalog/addToFavorites',
          );
        },

        // Remove from favorites
        removeFromFavorites: (serviceId: string) => {
          set(
            state => ({
              ...state,
              favoriteServices: state.favoriteServices.filter(
                id => id !== serviceId,
              ),
            }),
            false,
            'serviceCatalog/removeFromFavorites',
          );
        },

        // Add to recently viewed
        addToRecentlyViewed: (serviceId: string) => {
          set(
            state => ({
              ...state,
              recentlyViewed: [
                serviceId,
                ...state.recentlyViewed.filter(id => id !== serviceId),
              ].slice(0, 10), // Keep only last 10
            }),
            false,
            'serviceCatalog/addToRecentlyViewed',
          );
        },

        // Save search to history
        saveSearchToHistory: (query: SearchQuery, resultCount: number) => {
          const historyItem: SearchHistory = {
            id: `search_${Date.now()}`,
            query: query.query,
            filters: query.filters,
            timestamp: new Date().toISOString(),
            resultCount,
          };

          set(
            state => ({
              ...state,
              searchHistory: [
                historyItem,
                ...state.searchHistory.filter(
                  item => item.query !== query.query,
                ),
              ].slice(0, 20), // Keep only last 20 searches
            }),
            false,
            'serviceCatalog/saveSearchToHistory',
          );
        },

        // Clear search history
        clearSearchHistory: () => {
          set(
            state => ({ ...state, searchHistory: [] }),
            false,
            'serviceCatalog/clearSearchHistory',
          );
        },

        // Set user location
        setUserLocation: (location: {
          latitude: number;
          longitude: number;
          address?: string;
        }) => {
          set(
            state => ({ ...state, userLocation: location }),
            false,
            'serviceCatalog/setUserLocation',
          );
        },

        // Clear error
        clearError: () => {
          set(
            state => ({ ...state, error: null, searchError: null }),
            false,
            'serviceCatalog/clearError',
          );
        },

        // Reset store
        reset: () => {
          set(() => ({ ...initialState }), false, 'serviceCatalog/reset');
        },
      }),
      {
        name: 'service-catalog-store',
        storage: {
          getItem: async (name: string) => {
            try {
              const value = await AsyncStorage.getItem(name);
              return value ? JSON.parse(value) : null;
            } catch (error) {
              console.error('Failed to load service catalog state:', error);
              return null;
            }
          },
          setItem: async (name: string, value: any) => {
            try {
              await AsyncStorage.setItem(name, JSON.stringify(value));
            } catch (error) {
              console.error('Failed to save service catalog state:', error);
            }
          },
          removeItem: async (name: string) => {
            try {
              await AsyncStorage.removeItem(name);
            } catch (error) {
              console.error('Failed to remove service catalog state:', error);
            }
          },
        },
        partialize: state => ({
          searchHistory: state.searchHistory,
          favoriteServices: state.favoriteServices,
          recentlyViewed: state.recentlyViewed,
          userLocation: state.userLocation,
        }),
      },
    ),
  ),
);
