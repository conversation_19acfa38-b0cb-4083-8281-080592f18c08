{"version": 3, "names": ["errorCount", "lastErrorTime", "MAX_ERRORS_PER_MINUTE", "FALLBACK_THEME", "colors", "primary", "default", "light", "dark", "contrast", "text", "secondary", "tertiary", "inverse", "disabled", "onPrimary", "onSecondary", "link", "linkHover", "background", "elevated", "overlay", "sage", "surface", "border", "medium", "focus", "error", "success", "isDark", "THEME_ERROR_PATTERNS", "isThemeError", "message", "some", "pattern", "test", "shouldHandleError", "now", "Date", "globalErrorHandler", "isFatal", "arguments", "length", "undefined", "console", "warn", "stack", "global", "__VIERLA_FALLBACK_THEME__", "log", "recoveryError", "installGlobalErrorInterceptor", "exports", "_global$ErrorUtils", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "originalReportError", "reportError", "originalReportFatalError", "reportFatalError", "call", "setGlobalHandler", "<PERSON><PERSON><PERSON><PERSON>", "getGlobalHandler", "window", "originalOnError", "onerror", "source", "lineno", "colno", "getFallbackTheme", "hasRecentThemeErrors", "resetErrorTracking", "getErrorStats", "hasRecentErrors"], "sources": ["globalErrorInterceptor.ts"], "sourcesContent": ["/**\n * Global Error Interceptor for Hermes Engine\n *\n * Intercepts and handles the specific \"Cannot read property 'primary' of undefined\" error\n * that occurs in Hermes engine when theme objects are not properly initialized\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\n// Global error tracking\nlet errorCount = 0;\nlet lastErrorTime = 0;\nconst MAX_ERRORS_PER_MINUTE = 10;\n\n// Fallback theme object\nconst FALLBACK_THEME = {\n  colors: {\n    primary: {\n      default: '#4A6B52',\n      light: '#6B8A74',\n      dark: '#2A4B32',\n      contrast: '#FFFFFF',\n    },\n    text: {\n      primary: '#1A1A1A',\n      secondary: '#6B7280',\n      tertiary: '#9CA3AF',\n      inverse: '#FFFFFF',\n      disabled: '#9CA3AF',\n      onPrimary: '#FFFFFF',\n      onSecondary: '#FFFFFF',\n      link: '#4A6B52',\n      linkHover: '#2A4B32',\n    },\n    background: {\n      primary: '#FFFFFF',\n      secondary: '#F9FAFB',\n      tertiary: '#F3F4F6',\n      elevated: '#FFFFFF',\n      overlay: 'rgba(0, 0, 0, 0.5)',\n      sage: '#F4F7F5',\n    },\n    surface: {\n      primary: '#FFFFFF',\n      secondary: '#F9FAFB',\n      tertiary: '#F3F4F6',\n      inverse: '#1A1A1A',\n      disabled: '#F3F4F6',\n    },\n    border: {\n      light: '#E5E7EB',\n      medium: '#D1D5DB',\n      dark: '#9CA3AF',\n      focus: '#4A6B52',\n      error: '#EF4444',\n      success: '#10B981',\n    },\n  },\n  isDark: false,\n};\n\n// Error pattern detection\nconst THEME_ERROR_PATTERNS = [\n  /Cannot read property 'primary' of undefined/,\n  /Cannot read property 'text' of undefined/,\n  /Cannot read property 'background' of undefined/,\n  /Cannot read property 'surface' of undefined/,\n  /Cannot read property 'colors' of undefined/,\n  /Cannot read properties of undefined \\(reading 'primary'\\)/,\n  /Cannot read properties of undefined \\(reading 'text'\\)/,\n  /Cannot read properties of undefined \\(reading 'background'\\)/,\n  /Cannot read properties of undefined \\(reading 'surface'\\)/,\n  /Cannot read properties of undefined \\(reading 'colors'\\)/,\n];\n\n// Check if error is theme-related\nconst isThemeError = (error: Error): boolean => {\n  if (!error || !error.message) return false;\n\n  return THEME_ERROR_PATTERNS.some(pattern => pattern.test(error.message));\n};\n\n// Rate limiting for error handling\nconst shouldHandleError = (): boolean => {\n  const now = Date.now();\n\n  // Reset counter if more than a minute has passed\n  if (now - lastErrorTime > 60000) {\n    errorCount = 0;\n  }\n\n  lastErrorTime = now;\n  errorCount++;\n\n  return errorCount <= MAX_ERRORS_PER_MINUTE;\n};\n\n// Global error handler\nconst globalErrorHandler = (error: Error, isFatal: boolean = false): void => {\n  if (!isThemeError(error)) {\n    // Not a theme error, let it propagate normally\n    return;\n  }\n\n  if (!shouldHandleError()) {\n    console.warn(\n      '[GlobalErrorInterceptor] Too many theme errors, throttling...',\n    );\n    return;\n  }\n\n  console.error(\n    '[GlobalErrorInterceptor] 🛡️ Theme error intercepted:',\n    error.message,\n  );\n  console.error('[GlobalErrorInterceptor] Stack trace:', error.stack);\n\n  // Attempt to provide fallback theme\n  try {\n    // Check if we can access global theme context\n    if (typeof global !== 'undefined') {\n      // Store fallback theme in global for emergency access\n      global.__VIERLA_FALLBACK_THEME__ = FALLBACK_THEME;\n      console.log(\n        '[GlobalErrorInterceptor] ✅ Fallback theme stored in global',\n      );\n    }\n\n    // Log recovery attempt\n    console.log('[GlobalErrorInterceptor] 🔄 Attempting graceful recovery...');\n\n    // Don't let theme errors crash the app\n    if (!isFatal) {\n      console.log(\n        '[GlobalErrorInterceptor] ✅ Non-fatal theme error handled gracefully',\n      );\n      return;\n    }\n\n    // For fatal errors, still try to prevent crash\n    console.warn(\n      '[GlobalErrorInterceptor] ⚠️ Fatal theme error detected, attempting recovery...',\n    );\n  } catch (recoveryError) {\n    console.error(\n      '[GlobalErrorInterceptor] ❌ Recovery attempt failed:',\n      recoveryError,\n    );\n  }\n};\n\n// Install global error interceptor\nexport const installGlobalErrorInterceptor = (): void => {\n  console.log(\n    '[GlobalErrorInterceptor] Installing global error interceptor...',\n  );\n\n  try {\n    // Install on ErrorUtils if available\n    if (global && global.ErrorUtils) {\n      const originalReportError = global.ErrorUtils.reportError;\n      const originalReportFatalError = global.ErrorUtils.reportFatalError;\n\n      global.ErrorUtils.reportError = function (error: Error) {\n        globalErrorHandler(error, false);\n        if (originalReportError && !isThemeError(error)) {\n          originalReportError.call(this, error);\n        }\n      };\n\n      global.ErrorUtils.reportFatalError = function (error: Error) {\n        globalErrorHandler(error, true);\n        if (originalReportFatalError && !isThemeError(error)) {\n          originalReportFatalError.call(this, error);\n        }\n      };\n\n      console.log('[GlobalErrorInterceptor] ✅ Installed on ErrorUtils');\n    }\n\n    // Install on global error handler\n    if (global && typeof global.ErrorUtils?.setGlobalHandler === 'function') {\n      const originalHandler = global.ErrorUtils.getGlobalHandler?.() || null;\n\n      global.ErrorUtils.setGlobalHandler((error: Error, isFatal?: boolean) => {\n        globalErrorHandler(error, isFatal || false);\n\n        // Call original handler for non-theme errors\n        if (originalHandler && !isThemeError(error)) {\n          originalHandler(error, isFatal);\n        }\n      });\n\n      console.log('[GlobalErrorInterceptor] ✅ Installed global handler');\n    }\n\n    // Install on window.onerror if available (for web compatibility)\n    if (typeof window !== 'undefined') {\n      const originalOnError = window.onerror;\n\n      window.onerror = function (message, source, lineno, colno, error) {\n        if (error && isThemeError(error)) {\n          globalErrorHandler(error, false);\n          return true; // Prevent default error handling\n        }\n\n        if (originalOnError) {\n          return originalOnError.call(\n            this,\n            message,\n            source,\n            lineno,\n            colno,\n            error,\n          );\n        }\n\n        return false;\n      };\n\n      console.log('[GlobalErrorInterceptor] ✅ Installed on window.onerror');\n    }\n\n    console.log(\n      '[GlobalErrorInterceptor] ✅ Global error interceptor installed successfully',\n    );\n  } catch (error) {\n    console.error(\n      '[GlobalErrorInterceptor] ❌ Failed to install global error interceptor:',\n      error,\n    );\n  }\n};\n\n// Get fallback theme\nexport const getFallbackTheme = () => FALLBACK_THEME;\n\n// Check if theme error occurred recently\nexport const hasRecentThemeErrors = (): boolean => {\n  return errorCount > 0 && Date.now() - lastErrorTime < 60000;\n};\n\n// Reset error tracking\nexport const resetErrorTracking = (): void => {\n  errorCount = 0;\n  lastErrorTime = 0;\n  console.log('[GlobalErrorInterceptor] Error tracking reset');\n};\n\n// Get error statistics\nexport const getErrorStats = () => ({\n  errorCount,\n  lastErrorTime,\n  hasRecentErrors: hasRecentThemeErrors(),\n});\n"], "mappings": ";;;;AAWA,IAAIA,UAAU,GAAG,CAAC;AAClB,IAAIC,aAAa,GAAG,CAAC;AACrB,IAAMC,qBAAqB,GAAG,EAAE;AAGhC,IAAMC,cAAc,GAAG;EACrBC,MAAM,EAAE;IACNC,OAAO,EAAE;MACPC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE;IACZ,CAAC;IACDC,IAAI,EAAE;MACJL,OAAO,EAAE,SAAS;MAClBM,SAAS,EAAE,SAAS;MACpBC,QAAQ,EAAE,SAAS;MACnBC,OAAO,EAAE,SAAS;MAClBC,QAAQ,EAAE,SAAS;MACnBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,SAAS;MACtBC,IAAI,EAAE,SAAS;MACfC,SAAS,EAAE;IACb,CAAC;IACDC,UAAU,EAAE;MACVd,OAAO,EAAE,SAAS;MAClBM,SAAS,EAAE,SAAS;MACpBC,QAAQ,EAAE,SAAS;MACnBQ,QAAQ,EAAE,SAAS;MACnBC,OAAO,EAAE,oBAAoB;MAC7BC,IAAI,EAAE;IACR,CAAC;IACDC,OAAO,EAAE;MACPlB,OAAO,EAAE,SAAS;MAClBM,SAAS,EAAE,SAAS;MACpBC,QAAQ,EAAE,SAAS;MACnBC,OAAO,EAAE,SAAS;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDU,MAAM,EAAE;MACNjB,KAAK,EAAE,SAAS;MAChBkB,MAAM,EAAE,SAAS;MACjBjB,IAAI,EAAE,SAAS;MACfkB,KAAK,EAAE,SAAS;MAChBC,KAAK,EAAE,SAAS;MAChBC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,MAAM,EAAE;AACV,CAAC;AAGD,IAAMC,oBAAoB,GAAG,CAC3B,6CAA6C,EAC7C,0CAA0C,EAC1C,gDAAgD,EAChD,6CAA6C,EAC7C,4CAA4C,EAC5C,2DAA2D,EAC3D,wDAAwD,EACxD,8DAA8D,EAC9D,2DAA2D,EAC3D,0DAA0D,CAC3D;AAGD,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAIJ,KAAY,EAAc;EAC9C,IAAI,CAACA,KAAK,IAAI,CAACA,KAAK,CAACK,OAAO,EAAE,OAAO,KAAK;EAE1C,OAAOF,oBAAoB,CAACG,IAAI,CAAC,UAAAC,OAAO;IAAA,OAAIA,OAAO,CAACC,IAAI,CAACR,KAAK,CAACK,OAAO,CAAC;EAAA,EAAC;AAC1E,CAAC;AAGD,IAAMI,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAkB;EACvC,IAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;EAGtB,IAAIA,GAAG,GAAGpC,aAAa,GAAG,KAAK,EAAE;IAC/BD,UAAU,GAAG,CAAC;EAChB;EAEAC,aAAa,GAAGoC,GAAG;EACnBrC,UAAU,EAAE;EAEZ,OAAOA,UAAU,IAAIE,qBAAqB;AAC5C,CAAC;AAGD,IAAMqC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIZ,KAAY,EAAqC;EAAA,IAAnCa,OAAgB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAChE,IAAI,CAACV,YAAY,CAACJ,KAAK,CAAC,EAAE;IAExB;EACF;EAEA,IAAI,CAACS,iBAAiB,CAAC,CAAC,EAAE;IACxBQ,OAAO,CAACC,IAAI,CACV,+DACF,CAAC;IACD;EACF;EAEAD,OAAO,CAACjB,KAAK,CACX,uDAAuD,EACvDA,KAAK,CAACK,OACR,CAAC;EACDY,OAAO,CAACjB,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAACmB,KAAK,CAAC;EAGnE,IAAI;IAEF,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;MAEjCA,MAAM,CAACC,yBAAyB,GAAG7C,cAAc;MACjDyC,OAAO,CAACK,GAAG,CACT,4DACF,CAAC;IACH;IAGAL,OAAO,CAACK,GAAG,CAAC,6DAA6D,CAAC;IAG1E,IAAI,CAACT,OAAO,EAAE;MACZI,OAAO,CAACK,GAAG,CACT,qEACF,CAAC;MACD;IACF;IAGAL,OAAO,CAACC,IAAI,CACV,gFACF,CAAC;EACH,CAAC,CAAC,OAAOK,aAAa,EAAE;IACtBN,OAAO,CAACjB,KAAK,CACX,qDAAqD,EACrDuB,aACF,CAAC;EACH;AACF,CAAC;AAGM,IAAMC,6BAA6B,GAAAC,OAAA,CAAAD,6BAAA,GAAG,SAAhCA,6BAA6BA,CAAA,EAAe;EACvDP,OAAO,CAACK,GAAG,CACT,iEACF,CAAC;EAED,IAAI;IAAA,IAAAI,kBAAA;IAEF,IAAIN,MAAM,IAAIA,MAAM,CAACO,UAAU,EAAE;MAC/B,IAAMC,mBAAmB,GAAGR,MAAM,CAACO,UAAU,CAACE,WAAW;MACzD,IAAMC,wBAAwB,GAAGV,MAAM,CAACO,UAAU,CAACI,gBAAgB;MAEnEX,MAAM,CAACO,UAAU,CAACE,WAAW,GAAG,UAAU7B,KAAY,EAAE;QACtDY,kBAAkB,CAACZ,KAAK,EAAE,KAAK,CAAC;QAChC,IAAI4B,mBAAmB,IAAI,CAACxB,YAAY,CAACJ,KAAK,CAAC,EAAE;UAC/C4B,mBAAmB,CAACI,IAAI,CAAC,IAAI,EAAEhC,KAAK,CAAC;QACvC;MACF,CAAC;MAEDoB,MAAM,CAACO,UAAU,CAACI,gBAAgB,GAAG,UAAU/B,KAAY,EAAE;QAC3DY,kBAAkB,CAACZ,KAAK,EAAE,IAAI,CAAC;QAC/B,IAAI8B,wBAAwB,IAAI,CAAC1B,YAAY,CAACJ,KAAK,CAAC,EAAE;UACpD8B,wBAAwB,CAACE,IAAI,CAAC,IAAI,EAAEhC,KAAK,CAAC;QAC5C;MACF,CAAC;MAEDiB,OAAO,CAACK,GAAG,CAAC,oDAAoD,CAAC;IACnE;IAGA,IAAIF,MAAM,IAAI,SAAAM,kBAAA,GAAON,MAAM,CAACO,UAAU,qBAAjBD,kBAAA,CAAmBO,gBAAgB,MAAK,UAAU,EAAE;MACvE,IAAMC,eAAe,GAAG,CAAAd,MAAM,CAACO,UAAU,CAACQ,gBAAgB,oBAAlCf,MAAM,CAACO,UAAU,CAACQ,gBAAgB,CAAG,CAAC,KAAI,IAAI;MAEtEf,MAAM,CAACO,UAAU,CAACM,gBAAgB,CAAC,UAACjC,KAAY,EAAEa,OAAiB,EAAK;QACtED,kBAAkB,CAACZ,KAAK,EAAEa,OAAO,IAAI,KAAK,CAAC;QAG3C,IAAIqB,eAAe,IAAI,CAAC9B,YAAY,CAACJ,KAAK,CAAC,EAAE;UAC3CkC,eAAe,CAAClC,KAAK,EAAEa,OAAO,CAAC;QACjC;MACF,CAAC,CAAC;MAEFI,OAAO,CAACK,GAAG,CAAC,qDAAqD,CAAC;IACpE;IAGA,IAAI,OAAOc,MAAM,KAAK,WAAW,EAAE;MACjC,IAAMC,eAAe,GAAGD,MAAM,CAACE,OAAO;MAEtCF,MAAM,CAACE,OAAO,GAAG,UAAUjC,OAAO,EAAEkC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEzC,KAAK,EAAE;QAChE,IAAIA,KAAK,IAAII,YAAY,CAACJ,KAAK,CAAC,EAAE;UAChCY,kBAAkB,CAACZ,KAAK,EAAE,KAAK,CAAC;UAChC,OAAO,IAAI;QACb;QAEA,IAAIqC,eAAe,EAAE;UACnB,OAAOA,eAAe,CAACL,IAAI,CACzB,IAAI,EACJ3B,OAAO,EACPkC,MAAM,EACNC,MAAM,EACNC,KAAK,EACLzC,KACF,CAAC;QACH;QAEA,OAAO,KAAK;MACd,CAAC;MAEDiB,OAAO,CAACK,GAAG,CAAC,wDAAwD,CAAC;IACvE;IAEAL,OAAO,CAACK,GAAG,CACT,4EACF,CAAC;EACH,CAAC,CAAC,OAAOtB,KAAK,EAAE;IACdiB,OAAO,CAACjB,KAAK,CACX,wEAAwE,EACxEA,KACF,CAAC;EACH;AACF,CAAC;AAGM,IAAM0C,gBAAgB,GAAAjB,OAAA,CAAAiB,gBAAA,GAAG,SAAnBA,gBAAgBA,CAAA;EAAA,OAASlE,cAAc;AAAA;AAG7C,IAAMmE,oBAAoB,GAAAlB,OAAA,CAAAkB,oBAAA,GAAG,SAAvBA,oBAAoBA,CAAA,EAAkB;EACjD,OAAOtE,UAAU,GAAG,CAAC,IAAIsC,IAAI,CAACD,GAAG,CAAC,CAAC,GAAGpC,aAAa,GAAG,KAAK;AAC7D,CAAC;AAGM,IAAMsE,kBAAkB,GAAAnB,OAAA,CAAAmB,kBAAA,GAAG,SAArBA,kBAAkBA,CAAA,EAAe;EAC5CvE,UAAU,GAAG,CAAC;EACdC,aAAa,GAAG,CAAC;EACjB2C,OAAO,CAACK,GAAG,CAAC,+CAA+C,CAAC;AAC9D,CAAC;AAGM,IAAMuB,aAAa,GAAApB,OAAA,CAAAoB,aAAA,GAAG,SAAhBA,aAAaA,CAAA;EAAA,OAAU;IAClCxE,UAAU,EAAVA,UAAU;IACVC,aAAa,EAAbA,aAAa;IACbwE,eAAe,EAAEH,oBAAoB,CAAC;EACxC,CAAC;AAAA,CAAC", "ignoreList": []}