{"version": 3, "names": ["_processColor", "_interopRequireDefault", "require", "_Platform", "_NativeStatusBarManagerAndroid", "_NativeStatusBarManagerIOS", "_invariant", "React", "_interopRequireWildcard", "_StatusBar", "_NativeStatusBarManag", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_callSuper", "_getPrototypeOf2", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "mergePropsStack", "propsStack", "defaultValues", "reduce", "prev", "cur", "prop", "assign", "createStackEntry", "props", "_props$animated", "_props$showHideTransi", "animated", "showHideTransition", "backgroundColor", "value", "barStyle", "translucent", "hidden", "transition", "networkActivityIndicatorVisible", "StatusBar", "_React$Component", "_this", "_classCallCheck2", "_len", "arguments", "length", "args", "Array", "_key", "concat", "_stackEntry", "_inherits2", "_createClass2", "key", "componentDidMount", "pushStackEntry", "componentWillUnmount", "popStackEntry", "componentDidUpdate", "replaceStackEntry", "render", "setHidden", "animation", "_defaultProps", "Platform", "OS", "NativeStatusBarManagerIOS", "NativeStatusBarManagerAndroid", "setBarStyle", "style", "setStyle", "setNetworkActivityIndicatorVisible", "visible", "console", "warn", "setBackgroundColor", "color", "processedColor", "processColor", "String", "invariant", "setColor", "setTranslucent", "entry", "_propsStack", "push", "_updatePropsStack", "index", "indexOf", "splice", "newEntry", "Component", "getConstants", "DEFAULT_BACKGROUND_COLOR", "_updateImmediate", "_currentValues", "currentHeight", "HEIGHT", "clearImmediate", "setImmediate", "oldProps", "mergedProps", "_oldProps$barStyle", "_oldProps$hidden", "_oldProps$hidden2", "_default", "exports"], "sources": ["StatusBar.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow\n */\n\nimport type {ColorValue} from '../../StyleSheet/StyleSheet';\n\nimport processColor from '../../StyleSheet/processColor';\nimport Platform from '../../Utilities/Platform';\nimport NativeStatusBarManagerAndroid from './NativeStatusBarManagerAndroid';\nimport NativeStatusBarManagerIOS from './NativeStatusBarManagerIOS';\nimport invariant from 'invariant';\nimport * as React from 'react';\n\n/**\n * Status bar style\n */\nexport type StatusBarStyle = $Keys<{\n  /**\n   * Default status bar style (dark for iOS, light for Android)\n   */\n  default: string,\n  /**\n   * Dark background, white texts and icons\n   */\n  'light-content': string,\n  /**\n   * Light background, dark texts and icons\n   */\n  'dark-content': string,\n  ...\n}>;\n\n/**\n * Status bar animation\n */\nexport type StatusBarAnimation = $Keys<{\n  /**\n   * No animation\n   */\n  none: string,\n  /**\n   * Fade animation\n   */\n  fade: string,\n  /**\n   * Slide animation\n   */\n  slide: string,\n  ...\n}>;\n\nexport type StatusBarPropsAndroid = $ReadOnly<{\n  /**\n   * The background color of the status bar.\n   * @platform android\n   */\n  backgroundColor?: ?ColorValue,\n  /**\n   * If the status bar is translucent.\n   * When translucent is set to true, the app will draw under the status bar.\n   * This is useful when using a semi transparent status bar color.\n   *\n   * @platform android\n   */\n  translucent?: ?boolean,\n}>;\n\nexport type StatusBarPropsIOS = $ReadOnly<{\n  /**\n   * If the network activity indicator should be visible.\n   *\n   * @platform ios\n   */\n  networkActivityIndicatorVisible?: ?boolean,\n  /**\n   * The transition effect when showing and hiding the status bar using the `hidden`\n   * prop. Defaults to 'fade'.\n   *\n   * @platform ios\n   */\n  showHideTransition?: ?('fade' | 'slide' | 'none'),\n}>;\n\ntype StatusBarBaseProps = $ReadOnly<{\n  /**\n   * If the status bar is hidden.\n   */\n  hidden?: ?boolean,\n  /**\n   * If the transition between status bar property changes should be animated.\n   * Supported for backgroundColor, barStyle and hidden.\n   */\n  animated?: ?boolean,\n  /**\n   * Sets the color of the status bar text.\n   */\n  barStyle?: ?('default' | 'light-content' | 'dark-content'),\n}>;\n\nexport type StatusBarProps = $ReadOnly<{\n  ...StatusBarPropsAndroid,\n  ...StatusBarPropsIOS,\n  ...StatusBarBaseProps,\n}>;\n\ntype StackProps = {\n  backgroundColor: ?{\n    value: StatusBarProps['backgroundColor'],\n    animated: boolean,\n  },\n  barStyle: ?{\n    value: StatusBarProps['barStyle'],\n    animated: boolean,\n  },\n  translucent: StatusBarProps['translucent'],\n  hidden: ?{\n    value: boolean,\n    animated: boolean,\n    transition: StatusBarProps['showHideTransition'],\n  },\n  networkActivityIndicatorVisible: StatusBarProps['networkActivityIndicatorVisible'],\n};\n\n/**\n * Merges the prop stack with the default values.\n */\nfunction mergePropsStack(\n  propsStack: Array<Object>,\n  defaultValues: Object,\n): Object {\n  return propsStack.reduce(\n    (prev, cur) => {\n      for (const prop in cur) {\n        if (cur[prop] != null) {\n          prev[prop] = cur[prop];\n        }\n      }\n      return prev;\n    },\n    {...defaultValues},\n  );\n}\n\n/**\n * Returns an object to insert in the props stack from the props\n * and the transition/animation info.\n */\nfunction createStackEntry(props: StatusBarProps): StackProps {\n  const animated = props.animated ?? false;\n  const showHideTransition = props.showHideTransition ?? 'fade';\n  return {\n    backgroundColor:\n      props.backgroundColor != null\n        ? {\n            value: props.backgroundColor,\n            animated,\n          }\n        : null,\n    barStyle:\n      props.barStyle != null\n        ? {\n            value: props.barStyle,\n            animated,\n          }\n        : null,\n    translucent: props.translucent,\n    hidden:\n      props.hidden != null\n        ? {\n            value: props.hidden,\n            animated,\n            transition: showHideTransition,\n          }\n        : null,\n    networkActivityIndicatorVisible: props.networkActivityIndicatorVisible,\n  };\n}\n\n/**\n * Component to control the app status bar.\n *\n * It is possible to have multiple `StatusBar` components mounted at the same\n * time. The props will be merged in the order the `StatusBar` components were\n * mounted.\n *\n * ### Imperative API\n *\n * For cases where using a component is not ideal, there are static methods\n * to manipulate the `StatusBar` display stack. These methods have the same\n * behavior as mounting and unmounting a `StatusBar` component.\n *\n * For example, you can call `StatusBar.pushStackEntry` to update the status bar\n * before launching a third-party native UI component, and then call\n * `StatusBar.popStackEntry` when completed.\n *\n * ```\n * const openThirdPartyBugReporter = async () => {\n *   // The bug reporter has a dark background, so we push a new status bar style.\n *   const stackEntry = StatusBar.pushStackEntry({barStyle: 'light-content'});\n *\n *   // `open` returns a promise that resolves when the UI is dismissed.\n *   await BugReporter.open();\n *\n *   // Don't forget to call `popStackEntry` when you're done.\n *   StatusBar.popStackEntry(stackEntry);\n * };\n * ```\n *\n * There is a legacy imperative API that enables you to manually update the\n * status bar styles. However, the legacy API does not update the internal\n * `StatusBar` display stack, which means that any changes will be overridden\n * whenever a `StatusBar` component is mounted or unmounted.\n *\n * It is strongly advised that you use `pushStackEntry`, `popStackEntry`, or\n * `replaceStackEntry` instead of the static methods beginning with `set`.\n *\n * ### Constants\n *\n * `currentHeight` (Android only) The height of the status bar.\n */\nclass StatusBar extends React.Component<StatusBarProps> {\n  static _propsStack: Array<StackProps> = [];\n\n  static _defaultProps: any = createStackEntry({\n    backgroundColor:\n      Platform.OS === 'android'\n        ? NativeStatusBarManagerAndroid.getConstants()\n            .DEFAULT_BACKGROUND_COLOR ?? 'black'\n        : 'black',\n    barStyle: 'default',\n    translucent: false,\n    hidden: false,\n    networkActivityIndicatorVisible: false,\n  });\n\n  // Timer for updating the native module values at the end of the frame.\n  static _updateImmediate: ?number = null;\n\n  // The current merged values from the props stack.\n  static _currentValues: ?StackProps = null;\n\n  // TODO(janic): Provide a real API to deal with status bar height. See the\n  // discussion in #6195.\n  /**\n   * The current height of the status bar on the device.\n   *\n   * @platform android\n   */\n  static currentHeight: ?number =\n    Platform.OS === 'android'\n      ? NativeStatusBarManagerAndroid.getConstants().HEIGHT\n      : null;\n\n  // Provide an imperative API as static functions of the component.\n  // See the corresponding prop for more detail.\n\n  /**\n   * Show or hide the status bar\n   * @param hidden Hide the status bar.\n   * @param animation Optional animation when\n   *    changing the status bar hidden property.\n   */\n  static setHidden(hidden: boolean, animation?: StatusBarAnimation) {\n    animation = animation || 'none';\n    StatusBar._defaultProps.hidden.value = hidden;\n    if (Platform.OS === 'ios') {\n      NativeStatusBarManagerIOS.setHidden(hidden, animation);\n    } else if (Platform.OS === 'android') {\n      NativeStatusBarManagerAndroid.setHidden(hidden);\n    }\n  }\n\n  /**\n   * Set the status bar style\n   * @param style Status bar style to set\n   * @param animated Animate the style change.\n   */\n  static setBarStyle(style: StatusBarStyle, animated?: boolean) {\n    animated = animated || false;\n    StatusBar._defaultProps.barStyle.value = style;\n    if (Platform.OS === 'ios') {\n      NativeStatusBarManagerIOS.setStyle(style, animated);\n    } else if (Platform.OS === 'android') {\n      NativeStatusBarManagerAndroid.setStyle(style);\n    }\n  }\n\n  /**\n   * DEPRECATED - The status bar network activity indicator is not supported in iOS 13 and later. This will be removed in a future release.\n   * @param visible Show the indicator.\n   *\n   * @deprecated\n   */\n  static setNetworkActivityIndicatorVisible(visible: boolean) {\n    if (Platform.OS !== 'ios') {\n      console.warn(\n        '`setNetworkActivityIndicatorVisible` is only available on iOS',\n      );\n      return;\n    }\n    StatusBar._defaultProps.networkActivityIndicatorVisible = visible;\n    NativeStatusBarManagerIOS.setNetworkActivityIndicatorVisible(visible);\n  }\n\n  /**\n   * Set the background color for the status bar\n   * @param color Background color.\n   * @param animated Animate the style change.\n   */\n  static setBackgroundColor(color: ColorValue, animated?: boolean): void {\n    if (Platform.OS !== 'android') {\n      console.warn('`setBackgroundColor` is only available on Android');\n      return;\n    }\n    animated = animated || false;\n    StatusBar._defaultProps.backgroundColor.value = color;\n\n    const processedColor = processColor(color);\n    if (processedColor == null) {\n      console.warn(\n        `\\`StatusBar.setBackgroundColor\\`: Color ${String(color)} parsed to null or undefined`,\n      );\n      return;\n    }\n    invariant(\n      typeof processedColor === 'number',\n      'Unexpected color given for StatusBar.setBackgroundColor',\n    );\n\n    NativeStatusBarManagerAndroid.setColor(processedColor, animated);\n  }\n\n  /**\n   * Control the translucency of the status bar\n   * @param translucent Set as translucent.\n   */\n  static setTranslucent(translucent: boolean) {\n    if (Platform.OS !== 'android') {\n      console.warn('`setTranslucent` is only available on Android');\n      return;\n    }\n    StatusBar._defaultProps.translucent = translucent;\n    NativeStatusBarManagerAndroid.setTranslucent(translucent);\n  }\n\n  /**\n   * Push a StatusBar entry onto the stack.\n   * The return value should be passed to `popStackEntry` when complete.\n   *\n   * @param props Object containing the StatusBar props to use in the stack entry.\n   */\n  static pushStackEntry(props: StatusBarProps): StackProps {\n    const entry = createStackEntry(props);\n    StatusBar._propsStack.push(entry);\n    StatusBar._updatePropsStack();\n    return entry;\n  }\n\n  /**\n   * Pop a StatusBar entry from the stack.\n   *\n   * @param entry Entry returned from `pushStackEntry`.\n   */\n  static popStackEntry(entry: StackProps) {\n    const index = StatusBar._propsStack.indexOf(entry);\n    if (index !== -1) {\n      StatusBar._propsStack.splice(index, 1);\n    }\n    StatusBar._updatePropsStack();\n  }\n\n  /**\n   * Replace an existing StatusBar stack entry with new props.\n   *\n   * @param entry Entry returned from `pushStackEntry` to replace.\n   * @param props Object containing the StatusBar props to use in the replacement stack entry.\n   */\n  static replaceStackEntry(\n    entry: StackProps,\n    props: StatusBarProps,\n  ): StackProps {\n    const newEntry = createStackEntry(props);\n    const index = StatusBar._propsStack.indexOf(entry);\n    if (index !== -1) {\n      StatusBar._propsStack[index] = newEntry;\n    }\n    StatusBar._updatePropsStack();\n    return newEntry;\n  }\n\n  _stackEntry: ?StackProps = null;\n\n  componentDidMount() {\n    // Every time a StatusBar component is mounted, we push it's prop to a stack\n    // and always update the native status bar with the props from the top of then\n    // stack. This allows having multiple StatusBar components and the one that is\n    // added last or is deeper in the view hierarchy will have priority.\n    this._stackEntry = StatusBar.pushStackEntry(this.props);\n  }\n\n  componentWillUnmount() {\n    // When a StatusBar is unmounted, remove itself from the stack and update\n    // the native bar with the next props.\n    if (this._stackEntry != null) {\n      StatusBar.popStackEntry(this._stackEntry);\n    }\n  }\n\n  componentDidUpdate() {\n    if (this._stackEntry != null) {\n      this._stackEntry = StatusBar.replaceStackEntry(\n        this._stackEntry,\n        this.props,\n      );\n    }\n  }\n\n  /**\n   * Updates the native status bar with the props from the stack.\n   */\n  static _updatePropsStack = () => {\n    // Send the update to the native module only once at the end of the frame.\n    clearImmediate(StatusBar._updateImmediate);\n    StatusBar._updateImmediate = setImmediate(() => {\n      const oldProps = StatusBar._currentValues;\n      const mergedProps = mergePropsStack(\n        StatusBar._propsStack,\n        StatusBar._defaultProps,\n      );\n\n      // Update the props that have changed using the merged values from the props stack.\n      if (Platform.OS === 'ios') {\n        if (\n          !oldProps ||\n          oldProps.barStyle?.value !== mergedProps.barStyle.value\n        ) {\n          NativeStatusBarManagerIOS.setStyle(\n            mergedProps.barStyle.value,\n            mergedProps.barStyle.animated || false,\n          );\n        }\n        if (!oldProps || oldProps.hidden?.value !== mergedProps.hidden.value) {\n          NativeStatusBarManagerIOS.setHidden(\n            mergedProps.hidden.value,\n            mergedProps.hidden.animated\n              ? mergedProps.hidden.transition\n              : 'none',\n          );\n        }\n\n        if (\n          !oldProps ||\n          oldProps.networkActivityIndicatorVisible !==\n            mergedProps.networkActivityIndicatorVisible\n        ) {\n          NativeStatusBarManagerIOS.setNetworkActivityIndicatorVisible(\n            mergedProps.networkActivityIndicatorVisible,\n          );\n        }\n      } else if (Platform.OS === 'android') {\n        //todo(T60684787): Add back optimization to only update bar style and\n        //background color if the new value is different from the old value.\n        NativeStatusBarManagerAndroid.setStyle(mergedProps.barStyle.value);\n        const processedColor = processColor(mergedProps.backgroundColor.value);\n        if (processedColor == null) {\n          console.warn(\n            `\\`StatusBar._updatePropsStack\\`: Color ${mergedProps.backgroundColor.value} parsed to null or undefined`,\n          );\n        } else {\n          invariant(\n            typeof processedColor === 'number',\n            'Unexpected color given in StatusBar._updatePropsStack',\n          );\n          NativeStatusBarManagerAndroid.setColor(\n            processedColor,\n            mergedProps.backgroundColor.animated,\n          );\n        }\n        if (!oldProps || oldProps.hidden?.value !== mergedProps.hidden.value) {\n          NativeStatusBarManagerAndroid.setHidden(mergedProps.hidden.value);\n        }\n        // Activities are not translucent by default, so always set if true.\n        if (\n          !oldProps ||\n          oldProps.translucent !== mergedProps.translucent ||\n          mergedProps.translucent\n        ) {\n          NativeStatusBarManagerAndroid.setTranslucent(mergedProps.translucent);\n        }\n      }\n      // Update the current prop values.\n      StatusBar._currentValues = mergedProps;\n    });\n  };\n\n  render(): React.Node {\n    return null;\n  }\n}\n\nexport default StatusBar;\n"], "mappings": ";;;;;;;;;;AAYA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,8BAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,0BAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,UAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,KAAA,GAAAC,uBAAA,CAAAN,OAAA;AAA+B,IAAAO,UAAA,EAAAC,qBAAA;AAAA,SAAAF,wBAAAG,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAL,uBAAA,YAAAA,wBAAAG,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,SAAAmB,WAAAnB,CAAA,EAAAK,CAAA,EAAAN,CAAA,WAAAM,CAAA,OAAAe,gBAAA,CAAAX,OAAA,EAAAJ,CAAA,OAAAgB,2BAAA,CAAAZ,OAAA,EAAAT,CAAA,EAAAsB,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAnB,CAAA,EAAAN,CAAA,YAAAqB,gBAAA,CAAAX,OAAA,EAAAT,CAAA,EAAAyB,WAAA,IAAApB,CAAA,CAAAqB,KAAA,CAAA1B,CAAA,EAAAD,CAAA;AAAA,SAAAuB,0BAAA,cAAAtB,CAAA,IAAA2B,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAd,IAAA,CAAAQ,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAA3B,CAAA,aAAAsB,yBAAA,YAAAA,0BAAA,aAAAtB,CAAA;AAmH/B,SAAS8B,eAAeA,CACtBC,UAAyB,EACzBC,aAAqB,EACb;EACR,OAAOD,UAAU,CAACE,MAAM,CACtB,UAACC,IAAI,EAAEC,GAAG,EAAK;IACb,KAAK,IAAMC,IAAI,IAAID,GAAG,EAAE;MACtB,IAAIA,GAAG,CAACC,IAAI,CAAC,IAAI,IAAI,EAAE;QACrBF,IAAI,CAACE,IAAI,CAAC,GAAGD,GAAG,CAACC,IAAI,CAAC;MACxB;IACF;IACA,OAAOF,IAAI;EACb,CAAC,EAAAlB,MAAA,CAAAqB,MAAA,KACGL,aAAa,CACnB,CAAC;AACH;AAMA,SAASM,gBAAgBA,CAACC,KAAqB,EAAc;EAAA,IAAAC,eAAA,EAAAC,qBAAA;EAC3D,IAAMC,QAAQ,IAAAF,eAAA,GAAGD,KAAK,CAACG,QAAQ,YAAAF,eAAA,GAAI,KAAK;EACxC,IAAMG,kBAAkB,IAAAF,qBAAA,GAAGF,KAAK,CAACI,kBAAkB,YAAAF,qBAAA,GAAI,MAAM;EAC7D,OAAO;IACLG,eAAe,EACbL,KAAK,CAACK,eAAe,IAAI,IAAI,GACzB;MACEC,KAAK,EAAEN,KAAK,CAACK,eAAe;MAC5BF,QAAQ,EAARA;IACF,CAAC,GACD,IAAI;IACVI,QAAQ,EACNP,KAAK,CAACO,QAAQ,IAAI,IAAI,GAClB;MACED,KAAK,EAAEN,KAAK,CAACO,QAAQ;MACrBJ,QAAQ,EAARA;IACF,CAAC,GACD,IAAI;IACVK,WAAW,EAAER,KAAK,CAACQ,WAAW;IAC9BC,MAAM,EACJT,KAAK,CAACS,MAAM,IAAI,IAAI,GAChB;MACEH,KAAK,EAAEN,KAAK,CAACS,MAAM;MACnBN,QAAQ,EAARA,QAAQ;MACRO,UAAU,EAAEN;IACd,CAAC,GACD,IAAI;IACVO,+BAA+B,EAAEX,KAAK,CAACW;EACzC,CAAC;AACH;AAAC,IA4CKC,SAAS,aAAAC,gBAAA;EAAA,SAAAD,UAAA;IAAA,IAAAE,KAAA;IAAA,IAAAC,gBAAA,CAAA7C,OAAA,QAAA0C,SAAA;IAAA,SAAAI,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAAAP,KAAA,GAAAlC,UAAA,OAAAgC,SAAA,KAAAU,MAAA,CAAAH,IAAA;IAAAL,KAAA,CA0KbS,WAAW,GAAgB,IAAI;IAAA,OAAAT,KAAA;EAAA;EAAA,IAAAU,UAAA,CAAAtD,OAAA,EAAA0C,SAAA,EAAAC,gBAAA;EAAA,WAAAY,aAAA,CAAAvD,OAAA,EAAA0C,SAAA;IAAAc,GAAA;IAAApB,KAAA,EAE/B,SAAAqB,iBAAiBA,CAAA,EAAG;MAKlB,IAAI,CAACJ,WAAW,GAAGX,SAAS,CAACgB,cAAc,CAAC,IAAI,CAAC5B,KAAK,CAAC;IACzD;EAAC;IAAA0B,GAAA;IAAApB,KAAA,EAED,SAAAuB,oBAAoBA,CAAA,EAAG;MAGrB,IAAI,IAAI,CAACN,WAAW,IAAI,IAAI,EAAE;QAC5BX,SAAS,CAACkB,aAAa,CAAC,IAAI,CAACP,WAAW,CAAC;MAC3C;IACF;EAAC;IAAAG,GAAA;IAAApB,KAAA,EAED,SAAAyB,kBAAkBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAACR,WAAW,IAAI,IAAI,EAAE;QAC5B,IAAI,CAACA,WAAW,GAAGX,SAAS,CAACoB,iBAAiB,CAC5C,IAAI,CAACT,WAAW,EAChB,IAAI,CAACvB,KACP,CAAC;MACH;IACF;EAAC;IAAA0B,GAAA;IAAApB,KAAA,EAgFD,SAAA2B,MAAMA,CAAA,EAAe;MACnB,OAAO,IAAI;IACb;EAAC;IAAAP,GAAA;IAAApB,KAAA,EA3OD,SAAO4B,SAASA,CAACzB,MAAe,EAAE0B,SAA8B,EAAE;MAChEA,SAAS,GAAGA,SAAS,IAAI,MAAM;MAC/BvB,SAAS,CAACwB,aAAa,CAAC3B,MAAM,CAACH,KAAK,GAAGG,MAAM;MAC7C,IAAI4B,iBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;QACzBC,kCAAyB,CAACL,SAAS,CAACzB,MAAM,EAAE0B,SAAS,CAAC;MACxD,CAAC,MAAM,IAAIE,iBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;QACpCE,sCAA6B,CAACN,SAAS,CAACzB,MAAM,CAAC;MACjD;IACF;EAAC;IAAAiB,GAAA;IAAApB,KAAA,EAOD,SAAOmC,WAAWA,CAACC,KAAqB,EAAEvC,QAAkB,EAAE;MAC5DA,QAAQ,GAAGA,QAAQ,IAAI,KAAK;MAC5BS,SAAS,CAACwB,aAAa,CAAC7B,QAAQ,CAACD,KAAK,GAAGoC,KAAK;MAC9C,IAAIL,iBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;QACzBC,kCAAyB,CAACI,QAAQ,CAACD,KAAK,EAAEvC,QAAQ,CAAC;MACrD,CAAC,MAAM,IAAIkC,iBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;QACpCE,sCAA6B,CAACG,QAAQ,CAACD,KAAK,CAAC;MAC/C;IACF;EAAC;IAAAhB,GAAA;IAAApB,KAAA,EAQD,SAAOsC,kCAAkCA,CAACC,OAAgB,EAAE;MAC1D,IAAIR,iBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;QACzBQ,OAAO,CAACC,IAAI,CACV,+DACF,CAAC;QACD;MACF;MACAnC,SAAS,CAACwB,aAAa,CAACzB,+BAA+B,GAAGkC,OAAO;MACjEN,kCAAyB,CAACK,kCAAkC,CAACC,OAAO,CAAC;IACvE;EAAC;IAAAnB,GAAA;IAAApB,KAAA,EAOD,SAAO0C,kBAAkBA,CAACC,KAAiB,EAAE9C,QAAkB,EAAQ;MACrE,IAAIkC,iBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;QAC7BQ,OAAO,CAACC,IAAI,CAAC,mDAAmD,CAAC;QACjE;MACF;MACA5C,QAAQ,GAAGA,QAAQ,IAAI,KAAK;MAC5BS,SAAS,CAACwB,aAAa,CAAC/B,eAAe,CAACC,KAAK,GAAG2C,KAAK;MAErD,IAAMC,cAAc,GAAG,IAAAC,qBAAY,EAACF,KAAK,CAAC;MAC1C,IAAIC,cAAc,IAAI,IAAI,EAAE;QAC1BJ,OAAO,CAACC,IAAI,CACV,2CAA2CK,MAAM,CAACH,KAAK,CAAC,8BAC1D,CAAC;QACD;MACF;MACA,IAAAI,kBAAS,EACP,OAAOH,cAAc,KAAK,QAAQ,EAClC,yDACF,CAAC;MAEDV,sCAA6B,CAACc,QAAQ,CAACJ,cAAc,EAAE/C,QAAQ,CAAC;IAClE;EAAC;IAAAuB,GAAA;IAAApB,KAAA,EAMD,SAAOiD,cAAcA,CAAC/C,WAAoB,EAAE;MAC1C,IAAI6B,iBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;QAC7BQ,OAAO,CAACC,IAAI,CAAC,+CAA+C,CAAC;QAC7D;MACF;MACAnC,SAAS,CAACwB,aAAa,CAAC5B,WAAW,GAAGA,WAAW;MACjDgC,sCAA6B,CAACe,cAAc,CAAC/C,WAAW,CAAC;IAC3D;EAAC;IAAAkB,GAAA;IAAApB,KAAA,EAQD,SAAOsB,cAAcA,CAAC5B,KAAqB,EAAc;MACvD,IAAMwD,KAAK,GAAGzD,gBAAgB,CAACC,KAAK,CAAC;MACrCY,SAAS,CAAC6C,WAAW,CAACC,IAAI,CAACF,KAAK,CAAC;MACjC5C,SAAS,CAAC+C,iBAAiB,CAAC,CAAC;MAC7B,OAAOH,KAAK;IACd;EAAC;IAAA9B,GAAA;IAAApB,KAAA,EAOD,SAAOwB,aAAaA,CAAC0B,KAAiB,EAAE;MACtC,IAAMI,KAAK,GAAGhD,SAAS,CAAC6C,WAAW,CAACI,OAAO,CAACL,KAAK,CAAC;MAClD,IAAII,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBhD,SAAS,CAAC6C,WAAW,CAACK,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACxC;MACAhD,SAAS,CAAC+C,iBAAiB,CAAC,CAAC;IAC/B;EAAC;IAAAjC,GAAA;IAAApB,KAAA,EAQD,SAAO0B,iBAAiBA,CACtBwB,KAAiB,EACjBxD,KAAqB,EACT;MACZ,IAAM+D,QAAQ,GAAGhE,gBAAgB,CAACC,KAAK,CAAC;MACxC,IAAM4D,KAAK,GAAGhD,SAAS,CAAC6C,WAAW,CAACI,OAAO,CAACL,KAAK,CAAC;MAClD,IAAII,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBhD,SAAS,CAAC6C,WAAW,CAACG,KAAK,CAAC,GAAGG,QAAQ;MACzC;MACAnD,SAAS,CAAC+C,iBAAiB,CAAC,CAAC;MAC7B,OAAOI,QAAQ;IACjB;EAAC;AAAA,EAxKqB3G,KAAK,CAAC4G,SAAS;AAAA1G,UAAA,GAAjCsD,SAAS;AAATA,SAAS,CACN6C,WAAW,GAAsB,EAAE;AADtC7C,SAAS,CAGNwB,aAAa,GAAQrC,gBAAgB,CAAC;EAC3CM,eAAe,EACbgC,iBAAQ,CAACC,EAAE,KAAK,SAAS,IAAA/E,qBAAA,GACrBiF,sCAA6B,CAACyB,YAAY,CAAC,CAAC,CACzCC,wBAAwB,YAAA3G,qBAAA,GAAI,OAAO,GACtC,OAAO;EACbgD,QAAQ,EAAE,SAAS;EACnBC,WAAW,EAAE,KAAK;EAClBC,MAAM,EAAE,KAAK;EACbE,+BAA+B,EAAE;AACnC,CAAC,CAAC;AAbEC,SAAS,CAgBNuD,gBAAgB,GAAY,IAAI;AAhBnCvD,SAAS,CAmBNwD,cAAc,GAAgB,IAAI;AAnBrCxD,SAAS,CA4BNyD,aAAa,GAClBhC,iBAAQ,CAACC,EAAE,KAAK,SAAS,GACrBE,sCAA6B,CAACyB,YAAY,CAAC,CAAC,CAACK,MAAM,GACnD,IAAI;AA/BN1D,SAAS,CAwMN+C,iBAAiB,GAAG,YAAM;EAE/BY,cAAc,CAAC3D,UAAS,CAACuD,gBAAgB,CAAC;EAC1CvD,UAAS,CAACuD,gBAAgB,GAAGK,YAAY,CAAC,YAAM;IAC9C,IAAMC,QAAQ,GAAG7D,UAAS,CAACwD,cAAc;IACzC,IAAMM,WAAW,GAAGnF,eAAe,CACjCqB,UAAS,CAAC6C,WAAW,EACrB7C,UAAS,CAACwB,aACZ,CAAC;IAGD,IAAIC,iBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;MAAA,IAAAqC,kBAAA,EAAAC,gBAAA;MACzB,IACE,CAACH,QAAQ,IACT,EAAAE,kBAAA,GAAAF,QAAQ,CAAClE,QAAQ,qBAAjBoE,kBAAA,CAAmBrE,KAAK,MAAKoE,WAAW,CAACnE,QAAQ,CAACD,KAAK,EACvD;QACAiC,kCAAyB,CAACI,QAAQ,CAChC+B,WAAW,CAACnE,QAAQ,CAACD,KAAK,EAC1BoE,WAAW,CAACnE,QAAQ,CAACJ,QAAQ,IAAI,KACnC,CAAC;MACH;MACA,IAAI,CAACsE,QAAQ,IAAI,EAAAG,gBAAA,GAAAH,QAAQ,CAAChE,MAAM,qBAAfmE,gBAAA,CAAiBtE,KAAK,MAAKoE,WAAW,CAACjE,MAAM,CAACH,KAAK,EAAE;QACpEiC,kCAAyB,CAACL,SAAS,CACjCwC,WAAW,CAACjE,MAAM,CAACH,KAAK,EACxBoE,WAAW,CAACjE,MAAM,CAACN,QAAQ,GACvBuE,WAAW,CAACjE,MAAM,CAACC,UAAU,GAC7B,MACN,CAAC;MACH;MAEA,IACE,CAAC+D,QAAQ,IACTA,QAAQ,CAAC9D,+BAA+B,KACtC+D,WAAW,CAAC/D,+BAA+B,EAC7C;QACA4B,kCAAyB,CAACK,kCAAkC,CAC1D8B,WAAW,CAAC/D,+BACd,CAAC;MACH;IACF,CAAC,MAAM,IAAI0B,iBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;MAAA,IAAAuC,iBAAA;MAGpCrC,sCAA6B,CAACG,QAAQ,CAAC+B,WAAW,CAACnE,QAAQ,CAACD,KAAK,CAAC;MAClE,IAAM4C,cAAc,GAAG,IAAAC,qBAAY,EAACuB,WAAW,CAACrE,eAAe,CAACC,KAAK,CAAC;MACtE,IAAI4C,cAAc,IAAI,IAAI,EAAE;QAC1BJ,OAAO,CAACC,IAAI,CACV,0CAA0C2B,WAAW,CAACrE,eAAe,CAACC,KAAK,8BAC7E,CAAC;MACH,CAAC,MAAM;QACL,IAAA+C,kBAAS,EACP,OAAOH,cAAc,KAAK,QAAQ,EAClC,uDACF,CAAC;QACDV,sCAA6B,CAACc,QAAQ,CACpCJ,cAAc,EACdwB,WAAW,CAACrE,eAAe,CAACF,QAC9B,CAAC;MACH;MACA,IAAI,CAACsE,QAAQ,IAAI,EAAAI,iBAAA,GAAAJ,QAAQ,CAAChE,MAAM,qBAAfoE,iBAAA,CAAiBvE,KAAK,MAAKoE,WAAW,CAACjE,MAAM,CAACH,KAAK,EAAE;QACpEkC,sCAA6B,CAACN,SAAS,CAACwC,WAAW,CAACjE,MAAM,CAACH,KAAK,CAAC;MACnE;MAEA,IACE,CAACmE,QAAQ,IACTA,QAAQ,CAACjE,WAAW,KAAKkE,WAAW,CAAClE,WAAW,IAChDkE,WAAW,CAAClE,WAAW,EACvB;QACAgC,sCAA6B,CAACe,cAAc,CAACmB,WAAW,CAAClE,WAAW,CAAC;MACvE;IACF;IAEAI,UAAS,CAACwD,cAAc,GAAGM,WAAW;EACxC,CAAC,CAAC;AACJ,CAAC;AAAA,IAAAI,QAAA,GAAAC,OAAA,CAAA7G,OAAA,GAOY0C,SAAS", "ignoreList": []}