{"version": 3, "names": ["_View", "_interopRequireDefault", "require", "_Pressability", "_PressabilityDebug", "_RendererProxy", "_processColor", "_Platform", "_ViewNativeComponent", "_invariant", "React", "_interopRequireWildcard", "_jsxRuntime", "_excluded", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_callSuper", "_getPrototypeOf2", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "TouchableNativeFeedback", "_React$Component", "_this", "_classCallCheck2", "_len", "arguments", "length", "args", "Array", "_key", "concat", "state", "pressability", "Pressability", "_createPressabilityConfig", "_inherits2", "_createClass2", "key", "value", "_this$props$ariaDisa", "_this$props$accessibi", "_this2", "accessibilityStateDisabled", "props", "accessibilityState", "disabled", "cancelable", "rejectResponderTermination", "hitSlop", "delayLongPress", "delayPressIn", "delayPressOut", "minPressDuration", "pressRectOffset", "pressRetentionOffset", "android_disableSound", "touchSoundDisabled", "onLongPress", "onPress", "onPressIn", "event", "Platform", "OS", "_dispatchHotspotUpdate", "_dispatchPressedStateChange", "onPressMove", "onPressOut", "pressed", "hostComponentRef", "findHostInstance_DEPRECATED", "console", "warn", "Commands", "setPressed", "_event$nativeEvent", "nativeEvent", "locationX", "locationY", "hotspotUpdate", "render", "_this$props$ariaBusy", "_this$props$accessibi2", "_this$props$ariaChec", "_this$props$accessibi3", "_this$props$ariaDisa2", "_this$props$accessibi4", "_this$props$ariaExpa", "_this$props$accessibi5", "_this$props$ariaSele", "_this$props$accessibi6", "_this$props$ariaValu", "_this$props$accessibi7", "_this$props$ariaValu2", "_this$props$accessibi8", "_this$props$ariaValu3", "_this$props$accessibi9", "_this$props$ariaValu4", "_this$props$accessibi0", "_this$props$ariaLive", "_this$props$ariaLabe", "_this$props$ariaModa", "_this$props$ariaHidd", "_this$props$id", "element", "Children", "only", "children", "__DEV__", "type", "View", "push", "jsx", "PressabilityDebugView", "color", "_this$state$pressabil", "getEventHandlers", "onBlur", "onFocus", "eventHandlersWithoutBlurAndFocus", "_objectWithoutProperties2", "_accessibilityState", "busy", "checked", "expanded", "selected", "assign", "accessibilityValue", "max", "min", "now", "text", "accessibilityLiveRegion", "accessibilityLabel", "cloneElement", "getBackgroundProp", "background", "undefined", "SelectableBackground", "useForeground", "accessible", "accessibilityHint", "accessibilityLanguage", "accessibilityRole", "accessibilityActions", "onAccessibilityAction", "importantForAccessibility", "accessibilityViewIsModal", "accessibilityElementsHidden", "hasTVPreferredFocus", "focusable", "nativeID", "id", "nextFocusDown", "nextFocusForward", "nextFocusLeft", "nextFocusRight", "nextFocusUp", "onLayout", "testID", "componentDidUpdate", "prevProps", "prevState", "configure", "componentDidMount", "componentWillUnmount", "reset", "Component", "rippleRadius", "attribute", "SelectableBackgroundBorderless", "<PERSON><PERSON><PERSON>", "borderless", "processedColor", "processColor", "invariant", "canUseNativeForeground", "nativeForegroundAndroid", "nativeBackgroundAndroid", "displayName", "_default", "exports"], "sources": ["TouchableNativeFeedback.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {GestureResponderEvent} from '../../Types/CoreEventTypes';\nimport type {TouchableWithoutFeedbackProps} from './TouchableWithoutFeedback';\n\nimport View from '../../Components/View/View';\nimport Pressability, {\n  type PressabilityConfig,\n} from '../../Pressability/Pressability';\nimport {PressabilityDebugView} from '../../Pressability/PressabilityDebug';\nimport {findHostInstance_DEPRECATED} from '../../ReactNative/RendererProxy';\nimport processColor from '../../StyleSheet/processColor';\nimport Platform from '../../Utilities/Platform';\nimport {Commands} from '../View/ViewNativeComponent';\nimport invariant from 'invariant';\nimport * as React from 'react';\n\ntype TVProps = {\n  /**\n   * *(Apple TV only)* TV preferred focus (see documentation for the View component).\n   *\n   * @platform ios\n   */\n  hasTVPreferredFocus?: ?boolean,\n\n  /**\n   * Designates the next view to receive focus when the user navigates down. See the Android documentation.\n   *\n   * @platform android\n   */\n  nextFocusDown?: ?number,\n\n  /**\n   * Designates the next view to receive focus when the user navigates forward. See the Android documentation.\n   *\n   * @platform android\n   */\n  nextFocusForward?: ?number,\n\n  /**\n   * Designates the next view to receive focus when the user navigates left. See the Android documentation.\n   *\n   * @platform android\n   */\n  nextFocusLeft?: ?number,\n\n  /**\n   * Designates the next view to receive focus when the user navigates right. See the Android documentation.\n   *\n   * @platform android\n   */\n  nextFocusRight?: ?number,\n\n  /**\n   * Designates the next view to receive focus when the user navigates up. See the Android documentation.\n   *\n   * @platform android\n   */\n  nextFocusUp?: ?number,\n};\n\nexport type TouchableNativeFeedbackProps = $ReadOnly<{\n  ...TouchableWithoutFeedbackProps,\n  ...TVProps,\n  /**\n   * Determines the type of background drawable that's going to be used to display feedback.\n   * It takes an object with type property and extra data depending on the type.\n   * It's recommended to use one of the following static methods to generate that dictionary:\n   *      1) TouchableNativeFeedback.SelectableBackground() - will create object that represents android theme's\n   *         default background for selectable elements (?android:attr/selectableItemBackground)\n   *      2) TouchableNativeFeedback.SelectableBackgroundBorderless() - will create object that represent android\n   *         theme's default background for borderless selectable elements\n   *         (?android:attr/selectableItemBackgroundBorderless). Available on android API level 21+\n   *      3) TouchableNativeFeedback.Ripple(color, borderless) - will create object that represents ripple drawable\n   *         with specified color (as a string). If property borderless evaluates to true the ripple will render\n   *         outside of the view bounds (see native actionbar buttons as an example of that behavior). This background\n   *         type is available on Android API level 21+\n   */\n  background?: ?(\n    | $ReadOnly<{\n        type: 'ThemeAttrAndroid',\n        attribute:\n          | 'selectableItemBackground'\n          | 'selectableItemBackgroundBorderless',\n        rippleRadius: ?number,\n      }>\n    | $ReadOnly<{\n        type: 'RippleAndroid',\n        color: ?number,\n        borderless: boolean,\n        rippleRadius: ?number,\n      }>\n  ),\n  /**\n   * Set to true to add the ripple effect to the foreground of the view, instead\n   * of the background. This is useful if one of your child views has a\n   * background of its own, or you're e.g. displaying images, and you don't want\n   * the ripple to be covered by them.\n   *\n   * Check TouchableNativeFeedback.canUseNativeForeground() first, as this is\n   * only available on Android 6.0 and above. If you try to use this on older\n   * versions, this will fallback to background.\n   */\n  useForeground?: ?boolean,\n}>;\n\ntype State = $ReadOnly<{\n  pressability: Pressability,\n}>;\n\n/**\n * A wrapper for making views respond properly to touches (Android only).\n * On Android this component uses native state drawable to display touch feedback.\n * At the moment it only supports having a single View instance as a child node,\n * as it's implemented by replacing that View with another instance of RCTView node with some additional properties set.\n *\n * Background drawable of native feedback touchable can be customized with background property.\n *\n * @see https://reactnative.dev/docs/touchablenativefeedback#content\n */\nclass TouchableNativeFeedback extends React.Component<\n  TouchableNativeFeedbackProps,\n  State,\n> {\n  /**\n   * Creates an object that represents android theme's default background for\n   * selectable elements (?android:attr/selectableItemBackground).\n   *\n   * @param rippleRadius The radius of ripple effect\n   */\n  static SelectableBackground: (rippleRadius?: ?number) => $ReadOnly<{\n    attribute: 'selectableItemBackground',\n    type: 'ThemeAttrAndroid',\n    rippleRadius: ?number,\n  }> = (rippleRadius?: ?number) => ({\n    type: 'ThemeAttrAndroid',\n    attribute: 'selectableItemBackground',\n    rippleRadius,\n  });\n\n  /**\n   * Creates an object that represent android theme's default background for borderless\n   * selectable elements (?android:attr/selectableItemBackgroundBorderless).\n   * Available on android API level 21+.\n   *\n   * @param rippleRadius The radius of ripple effect\n   */\n  static SelectableBackgroundBorderless: (rippleRadius?: ?number) => $ReadOnly<{\n    attribute: 'selectableItemBackgroundBorderless',\n    type: 'ThemeAttrAndroid',\n    rippleRadius: ?number,\n  }> = (rippleRadius?: ?number) => ({\n    type: 'ThemeAttrAndroid',\n    attribute: 'selectableItemBackgroundBorderless',\n    rippleRadius,\n  });\n\n  /**\n   * Creates an object that represents ripple drawable with specified color (as a\n   * string). If property `borderless` evaluates to true the ripple will\n   * render outside of the view bounds (see native actionbar buttons as an\n   * example of that behavior). This background type is available on Android\n   * API level 21+.\n   *\n   * @param color The ripple color\n   * @param borderless If the ripple can render outside it's bounds\n   * @param rippleRadius The radius of ripple effect\n   */\n  static Ripple: (\n    color: string,\n    borderless: boolean,\n    rippleRadius?: ?number,\n  ) => $ReadOnly<{\n    borderless: boolean,\n    color: ?number,\n    rippleRadius: ?number,\n    type: 'RippleAndroid',\n  }> = (color: string, borderless: boolean, rippleRadius?: ?number) => {\n    const processedColor = processColor(color);\n    invariant(\n      processedColor == null || typeof processedColor === 'number',\n      'Unexpected color given for Ripple color',\n    );\n    return {\n      type: 'RippleAndroid',\n      // $FlowFixMe[incompatible-type]\n      color: processedColor,\n      borderless,\n      rippleRadius,\n    };\n  };\n\n  /**\n   * Whether `useForeground` is supported.\n   */\n  static canUseNativeForeground: () => boolean = () =>\n    Platform.OS === 'android';\n\n  state: State = {\n    pressability: new Pressability(this._createPressabilityConfig()),\n  };\n\n  _createPressabilityConfig(): PressabilityConfig {\n    const accessibilityStateDisabled =\n      this.props['aria-disabled'] ?? this.props.accessibilityState?.disabled;\n    return {\n      cancelable: !this.props.rejectResponderTermination,\n      disabled:\n        this.props.disabled != null\n          ? this.props.disabled\n          : accessibilityStateDisabled,\n      hitSlop: this.props.hitSlop,\n      delayLongPress: this.props.delayLongPress,\n      delayPressIn: this.props.delayPressIn,\n      delayPressOut: this.props.delayPressOut,\n      minPressDuration: 0,\n      pressRectOffset: this.props.pressRetentionOffset,\n      android_disableSound: this.props.touchSoundDisabled,\n      onLongPress: this.props.onLongPress,\n      onPress: this.props.onPress,\n      onPressIn: event => {\n        if (Platform.OS === 'android') {\n          this._dispatchHotspotUpdate(event);\n          this._dispatchPressedStateChange(true);\n        }\n        if (this.props.onPressIn != null) {\n          this.props.onPressIn(event);\n        }\n      },\n      onPressMove: event => {\n        if (Platform.OS === 'android') {\n          this._dispatchHotspotUpdate(event);\n        }\n      },\n      onPressOut: event => {\n        if (Platform.OS === 'android') {\n          this._dispatchPressedStateChange(false);\n        }\n        if (this.props.onPressOut != null) {\n          this.props.onPressOut(event);\n        }\n      },\n    };\n  }\n\n  _dispatchPressedStateChange(pressed: boolean): void {\n    if (Platform.OS === 'android') {\n      const hostComponentRef = findHostInstance_DEPRECATED(this);\n      if (hostComponentRef == null) {\n        console.warn(\n          'Touchable: Unable to find HostComponent instance. ' +\n            'Has your Touchable component been unmounted?',\n        );\n      } else {\n        Commands.setPressed(hostComponentRef, pressed);\n      }\n    }\n  }\n\n  _dispatchHotspotUpdate(event: GestureResponderEvent): void {\n    if (Platform.OS === 'android') {\n      const {locationX, locationY} = event.nativeEvent;\n      const hostComponentRef = findHostInstance_DEPRECATED(this);\n      if (hostComponentRef == null) {\n        console.warn(\n          'Touchable: Unable to find HostComponent instance. ' +\n            'Has your Touchable component been unmounted?',\n        );\n      } else {\n        Commands.hotspotUpdate(\n          hostComponentRef,\n          locationX ?? 0,\n          locationY ?? 0,\n        );\n      }\n    }\n  }\n\n  render(): React.Node {\n    const element = React.Children.only<$FlowFixMe>(this.props.children);\n    const children: Array<React.Node> = [element.props.children];\n    if (__DEV__) {\n      if (element.type === View) {\n        children.push(\n          <PressabilityDebugView color=\"brown\" hitSlop={this.props.hitSlop} />,\n        );\n      }\n    }\n\n    // BACKWARD-COMPATIBILITY: Focus and blur events were never supported before\n    // adopting `Pressability`, so preserve that behavior.\n    const {onBlur, onFocus, ...eventHandlersWithoutBlurAndFocus} =\n      this.state.pressability.getEventHandlers();\n\n    let _accessibilityState = {\n      busy: this.props['aria-busy'] ?? this.props.accessibilityState?.busy,\n      checked:\n        this.props['aria-checked'] ?? this.props.accessibilityState?.checked,\n      disabled:\n        this.props['aria-disabled'] ?? this.props.accessibilityState?.disabled,\n      expanded:\n        this.props['aria-expanded'] ?? this.props.accessibilityState?.expanded,\n      selected:\n        this.props['aria-selected'] ?? this.props.accessibilityState?.selected,\n    };\n\n    _accessibilityState =\n      this.props.disabled != null\n        ? {\n            ..._accessibilityState,\n            disabled: this.props.disabled,\n          }\n        : _accessibilityState;\n\n    const accessibilityValue = {\n      max: this.props['aria-valuemax'] ?? this.props.accessibilityValue?.max,\n      min: this.props['aria-valuemin'] ?? this.props.accessibilityValue?.min,\n      now: this.props['aria-valuenow'] ?? this.props.accessibilityValue?.now,\n      text: this.props['aria-valuetext'] ?? this.props.accessibilityValue?.text,\n    };\n\n    const accessibilityLiveRegion =\n      this.props['aria-live'] === 'off'\n        ? 'none'\n        : this.props['aria-live'] ?? this.props.accessibilityLiveRegion;\n\n    const accessibilityLabel =\n      this.props['aria-label'] ?? this.props.accessibilityLabel;\n    return React.cloneElement(\n      element,\n      {\n        ...eventHandlersWithoutBlurAndFocus,\n        ...getBackgroundProp(\n          this.props.background === undefined\n            ? TouchableNativeFeedback.SelectableBackground()\n            : this.props.background,\n          this.props.useForeground === true,\n        ),\n        accessible: this.props.accessible !== false,\n        accessibilityHint: this.props.accessibilityHint,\n        accessibilityLanguage: this.props.accessibilityLanguage,\n        accessibilityLabel: accessibilityLabel,\n        accessibilityRole: this.props.accessibilityRole,\n        accessibilityState: _accessibilityState,\n        accessibilityActions: this.props.accessibilityActions,\n        onAccessibilityAction: this.props.onAccessibilityAction,\n        accessibilityValue: accessibilityValue,\n        importantForAccessibility:\n          this.props['aria-hidden'] === true\n            ? 'no-hide-descendants'\n            : this.props.importantForAccessibility,\n        accessibilityViewIsModal:\n          this.props['aria-modal'] ?? this.props.accessibilityViewIsModal,\n        accessibilityLiveRegion: accessibilityLiveRegion,\n        accessibilityElementsHidden:\n          this.props['aria-hidden'] ?? this.props.accessibilityElementsHidden,\n        hasTVPreferredFocus: this.props.hasTVPreferredFocus,\n        hitSlop: this.props.hitSlop,\n        focusable:\n          this.props.focusable !== false &&\n          this.props.onPress !== undefined &&\n          !this.props.disabled,\n        nativeID: this.props.id ?? this.props.nativeID,\n        nextFocusDown: this.props.nextFocusDown,\n        nextFocusForward: this.props.nextFocusForward,\n        nextFocusLeft: this.props.nextFocusLeft,\n        nextFocusRight: this.props.nextFocusRight,\n        nextFocusUp: this.props.nextFocusUp,\n        onLayout: this.props.onLayout,\n        testID: this.props.testID,\n      },\n      ...children,\n    );\n  }\n\n  componentDidUpdate(\n    prevProps: TouchableNativeFeedbackProps,\n    prevState: State,\n  ) {\n    this.state.pressability.configure(this._createPressabilityConfig());\n  }\n\n  componentDidMount(): mixed {\n    this.state.pressability.configure(this._createPressabilityConfig());\n  }\n\n  componentWillUnmount(): void {\n    this.state.pressability.reset();\n  }\n}\n\nconst getBackgroundProp =\n  Platform.OS === 'android'\n    ? /* $FlowFixMe[missing-local-annot] The type annotation(s) required by\n       * Flow's LTI update could not be added via codemod */\n      (background, useForeground: boolean) =>\n        useForeground && TouchableNativeFeedback.canUseNativeForeground()\n          ? {nativeForegroundAndroid: background}\n          : {nativeBackgroundAndroid: background}\n    : /* $FlowFixMe[missing-local-annot] The type annotation(s) required by\n       * Flow's LTI update could not be added via codemod */\n      (background, useForeground: boolean) => null;\n\nTouchableNativeFeedback.displayName = 'TouchableNativeFeedback';\n\nexport default TouchableNativeFeedback;\n"], "mappings": ";;;;;;;;;;;AAaA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAF,sBAAA,CAAAC,OAAA;AAGA,IAAAE,kBAAA,GAAAF,OAAA;AACA,IAAAG,cAAA,GAAAH,OAAA;AACA,IAAAI,aAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,SAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,oBAAA,GAAAN,OAAA;AACA,IAAAO,UAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,KAAA,GAAAC,uBAAA,CAAAT,OAAA;AAA+B,IAAAU,WAAA,GAAAV,OAAA;AAAA,IAAAW,SAAA;AAAA,SAAAF,wBAAAG,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAL,uBAAA,YAAAA,wBAAAG,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,SAAAmB,WAAAnB,CAAA,EAAAK,CAAA,EAAAN,CAAA,WAAAM,CAAA,OAAAe,gBAAA,CAAAX,OAAA,EAAAJ,CAAA,OAAAgB,2BAAA,CAAAZ,OAAA,EAAAT,CAAA,EAAAsB,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAnB,CAAA,EAAAN,CAAA,YAAAqB,gBAAA,CAAAX,OAAA,EAAAT,CAAA,EAAAyB,WAAA,IAAApB,CAAA,CAAAqB,KAAA,CAAA1B,CAAA,EAAAD,CAAA;AAAA,SAAAuB,0BAAA,cAAAtB,CAAA,IAAA2B,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAd,IAAA,CAAAQ,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAA3B,CAAA,aAAAsB,yBAAA,YAAAA,0BAAA,aAAAtB,CAAA;AAAA,IAyGzB8B,uBAAuB,aAAAC,gBAAA;EAAA,SAAAD,wBAAA;IAAA,IAAAE,KAAA;IAAA,IAAAC,gBAAA,CAAAxB,OAAA,QAAAqB,uBAAA;IAAA,SAAAI,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAAAP,KAAA,GAAAb,UAAA,OAAAW,uBAAA,KAAAU,MAAA,CAAAH,IAAA;IAAAL,KAAA,CA8E3BS,KAAK,GAAU;MACbC,YAAY,EAAE,IAAIC,qBAAY,CAACX,KAAA,CAAKY,yBAAyB,CAAC,CAAC;IACjE,CAAC;IAAA,OAAAZ,KAAA;EAAA;EAAA,IAAAa,UAAA,CAAApC,OAAA,EAAAqB,uBAAA,EAAAC,gBAAA;EAAA,WAAAe,aAAA,CAAArC,OAAA,EAAAqB,uBAAA;IAAAiB,GAAA;IAAAC,KAAA,EAED,SAAAJ,yBAAyBA,CAAA,EAAuB;MAAA,IAAAK,oBAAA;QAAAC,qBAAA;QAAAC,MAAA;MAC9C,IAAMC,0BAA0B,IAAAH,oBAAA,GAC9B,IAAI,CAACI,KAAK,CAAC,eAAe,CAAC,YAAAJ,oBAAA,IAAAC,qBAAA,GAAI,IAAI,CAACG,KAAK,CAACC,kBAAkB,qBAA7BJ,qBAAA,CAA+BK,QAAQ;MACxE,OAAO;QACLC,UAAU,EAAE,CAAC,IAAI,CAACH,KAAK,CAACI,0BAA0B;QAClDF,QAAQ,EACN,IAAI,CAACF,KAAK,CAACE,QAAQ,IAAI,IAAI,GACvB,IAAI,CAACF,KAAK,CAACE,QAAQ,GACnBH,0BAA0B;QAChCM,OAAO,EAAE,IAAI,CAACL,KAAK,CAACK,OAAO;QAC3BC,cAAc,EAAE,IAAI,CAACN,KAAK,CAACM,cAAc;QACzCC,YAAY,EAAE,IAAI,CAACP,KAAK,CAACO,YAAY;QACrCC,aAAa,EAAE,IAAI,CAACR,KAAK,CAACQ,aAAa;QACvCC,gBAAgB,EAAE,CAAC;QACnBC,eAAe,EAAE,IAAI,CAACV,KAAK,CAACW,oBAAoB;QAChDC,oBAAoB,EAAE,IAAI,CAACZ,KAAK,CAACa,kBAAkB;QACnDC,WAAW,EAAE,IAAI,CAACd,KAAK,CAACc,WAAW;QACnCC,OAAO,EAAE,IAAI,CAACf,KAAK,CAACe,OAAO;QAC3BC,SAAS,EAAE,SAAXA,SAASA,CAAEC,KAAK,EAAI;UAClB,IAAIC,iBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;YAC7BrB,MAAI,CAACsB,sBAAsB,CAACH,KAAK,CAAC;YAClCnB,MAAI,CAACuB,2BAA2B,CAAC,IAAI,CAAC;UACxC;UACA,IAAIvB,MAAI,CAACE,KAAK,CAACgB,SAAS,IAAI,IAAI,EAAE;YAChClB,MAAI,CAACE,KAAK,CAACgB,SAAS,CAACC,KAAK,CAAC;UAC7B;QACF,CAAC;QACDK,WAAW,EAAE,SAAbA,WAAWA,CAAEL,KAAK,EAAI;UACpB,IAAIC,iBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;YAC7BrB,MAAI,CAACsB,sBAAsB,CAACH,KAAK,CAAC;UACpC;QACF,CAAC;QACDM,UAAU,EAAE,SAAZA,UAAUA,CAAEN,KAAK,EAAI;UACnB,IAAIC,iBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;YAC7BrB,MAAI,CAACuB,2BAA2B,CAAC,KAAK,CAAC;UACzC;UACA,IAAIvB,MAAI,CAACE,KAAK,CAACuB,UAAU,IAAI,IAAI,EAAE;YACjCzB,MAAI,CAACE,KAAK,CAACuB,UAAU,CAACN,KAAK,CAAC;UAC9B;QACF;MACF,CAAC;IACH;EAAC;IAAAvB,GAAA;IAAAC,KAAA,EAED,SAAA0B,2BAA2BA,CAACG,OAAgB,EAAQ;MAClD,IAAIN,iBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;QAC7B,IAAMM,gBAAgB,GAAG,IAAAC,0CAA2B,EAAC,IAAI,CAAC;QAC1D,IAAID,gBAAgB,IAAI,IAAI,EAAE;UAC5BE,OAAO,CAACC,IAAI,CACV,oDAAoD,GAClD,8CACJ,CAAC;QACH,CAAC,MAAM;UACLC,6BAAQ,CAACC,UAAU,CAACL,gBAAgB,EAAED,OAAO,CAAC;QAChD;MACF;IACF;EAAC;IAAA9B,GAAA;IAAAC,KAAA,EAED,SAAAyB,sBAAsBA,CAACH,KAA4B,EAAQ;MACzD,IAAIC,iBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;QAC7B,IAAAY,kBAAA,GAA+Bd,KAAK,CAACe,WAAW;UAAzCC,SAAS,GAAAF,kBAAA,CAATE,SAAS;UAAEC,SAAS,GAAAH,kBAAA,CAATG,SAAS;QAC3B,IAAMT,gBAAgB,GAAG,IAAAC,0CAA2B,EAAC,IAAI,CAAC;QAC1D,IAAID,gBAAgB,IAAI,IAAI,EAAE;UAC5BE,OAAO,CAACC,IAAI,CACV,oDAAoD,GAClD,8CACJ,CAAC;QACH,CAAC,MAAM;UACLC,6BAAQ,CAACM,aAAa,CACpBV,gBAAgB,EAChBQ,SAAS,WAATA,SAAS,GAAI,CAAC,EACdC,SAAS,WAATA,SAAS,GAAI,CACf,CAAC;QACH;MACF;IACF;EAAC;IAAAxC,GAAA;IAAAC,KAAA,EAED,SAAAyC,MAAMA,CAAA,EAAe;MAAA,IAAAC,oBAAA,EAAAC,sBAAA,EAAAC,oBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,oBAAA,EAAAC,sBAAA,EAAAC,oBAAA,EAAAC,sBAAA,EAAAC,oBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,cAAA;MACnB,IAAMC,OAAO,GAAGtH,KAAK,CAACuH,QAAQ,CAACC,IAAI,CAAa,IAAI,CAAC9D,KAAK,CAAC+D,QAAQ,CAAC;MACpE,IAAMA,QAA2B,GAAG,CAACH,OAAO,CAAC5D,KAAK,CAAC+D,QAAQ,CAAC;MAC5D,IAAIC,OAAO,EAAE;QACX,IAAIJ,OAAO,CAACK,IAAI,KAAKC,aAAI,EAAE;UACzBH,QAAQ,CAACI,IAAI,CACX,IAAA3H,WAAA,CAAA4H,GAAA,EAACpI,kBAAA,CAAAqI,qBAAqB;YAACC,KAAK,EAAC,OAAO;YAACjE,OAAO,EAAE,IAAI,CAACL,KAAK,CAACK;UAAQ,CAAE,CACrE,CAAC;QACH;MACF;MAIA,IAAAkE,qBAAA,GACE,IAAI,CAACnF,KAAK,CAACC,YAAY,CAACmF,gBAAgB,CAAC,CAAC;QADrCC,MAAM,GAAAF,qBAAA,CAANE,MAAM;QAAEC,OAAO,GAAAH,qBAAA,CAAPG,OAAO;QAAKC,gCAAgC,OAAAC,yBAAA,CAAAxH,OAAA,EAAAmH,qBAAA,EAAA9H,SAAA;MAG3D,IAAIoI,mBAAmB,GAAG;QACxBC,IAAI,GAAAzC,oBAAA,GAAE,IAAI,CAACrC,KAAK,CAAC,WAAW,CAAC,YAAAqC,oBAAA,IAAAC,sBAAA,GAAI,IAAI,CAACtC,KAAK,CAACC,kBAAkB,qBAA7BqC,sBAAA,CAA+BwC,IAAI;QACpEC,OAAO,GAAAxC,oBAAA,GACL,IAAI,CAACvC,KAAK,CAAC,cAAc,CAAC,YAAAuC,oBAAA,IAAAC,sBAAA,GAAI,IAAI,CAACxC,KAAK,CAACC,kBAAkB,qBAA7BuC,sBAAA,CAA+BuC,OAAO;QACtE7E,QAAQ,GAAAuC,qBAAA,GACN,IAAI,CAACzC,KAAK,CAAC,eAAe,CAAC,YAAAyC,qBAAA,IAAAC,sBAAA,GAAI,IAAI,CAAC1C,KAAK,CAACC,kBAAkB,qBAA7ByC,sBAAA,CAA+BxC,QAAQ;QACxE8E,QAAQ,GAAArC,oBAAA,GACN,IAAI,CAAC3C,KAAK,CAAC,eAAe,CAAC,YAAA2C,oBAAA,IAAAC,sBAAA,GAAI,IAAI,CAAC5C,KAAK,CAACC,kBAAkB,qBAA7B2C,sBAAA,CAA+BoC,QAAQ;QACxEC,QAAQ,GAAApC,oBAAA,GACN,IAAI,CAAC7C,KAAK,CAAC,eAAe,CAAC,YAAA6C,oBAAA,IAAAC,sBAAA,GAAI,IAAI,CAAC9C,KAAK,CAACC,kBAAkB,qBAA7B6C,sBAAA,CAA+BmC;MAClE,CAAC;MAEDJ,mBAAmB,GACjB,IAAI,CAAC7E,KAAK,CAACE,QAAQ,IAAI,IAAI,GAAAvC,MAAA,CAAAuH,MAAA,KAElBL,mBAAmB;QACtB3E,QAAQ,EAAE,IAAI,CAACF,KAAK,CAACE;MAAQ,KAE/B2E,mBAAmB;MAEzB,IAAMM,kBAAkB,GAAG;QACzBC,GAAG,GAAArC,oBAAA,GAAE,IAAI,CAAC/C,KAAK,CAAC,eAAe,CAAC,YAAA+C,oBAAA,IAAAC,sBAAA,GAAI,IAAI,CAAChD,KAAK,CAACmF,kBAAkB,qBAA7BnC,sBAAA,CAA+BoC,GAAG;QACtEC,GAAG,GAAApC,qBAAA,GAAE,IAAI,CAACjD,KAAK,CAAC,eAAe,CAAC,YAAAiD,qBAAA,IAAAC,sBAAA,GAAI,IAAI,CAAClD,KAAK,CAACmF,kBAAkB,qBAA7BjC,sBAAA,CAA+BmC,GAAG;QACtEC,GAAG,GAAAnC,qBAAA,GAAE,IAAI,CAACnD,KAAK,CAAC,eAAe,CAAC,YAAAmD,qBAAA,IAAAC,sBAAA,GAAI,IAAI,CAACpD,KAAK,CAACmF,kBAAkB,qBAA7B/B,sBAAA,CAA+BkC,GAAG;QACtEC,IAAI,GAAAlC,qBAAA,GAAE,IAAI,CAACrD,KAAK,CAAC,gBAAgB,CAAC,YAAAqD,qBAAA,IAAAC,sBAAA,GAAI,IAAI,CAACtD,KAAK,CAACmF,kBAAkB,qBAA7B7B,sBAAA,CAA+BiC;MACvE,CAAC;MAED,IAAMC,uBAAuB,GAC3B,IAAI,CAACxF,KAAK,CAAC,WAAW,CAAC,KAAK,KAAK,GAC7B,MAAM,IAAAuD,oBAAA,GACN,IAAI,CAACvD,KAAK,CAAC,WAAW,CAAC,YAAAuD,oBAAA,GAAI,IAAI,CAACvD,KAAK,CAACwF,uBAAuB;MAEnE,IAAMC,kBAAkB,IAAAjC,oBAAA,GACtB,IAAI,CAACxD,KAAK,CAAC,YAAY,CAAC,YAAAwD,oBAAA,GAAI,IAAI,CAACxD,KAAK,CAACyF,kBAAkB;MAC3D,OAAOnJ,KAAK,CAACoJ,YAAY,CAAArH,KAAA,CAAlB/B,KAAK,GACVsH,OAAO,EAAAjG,MAAA,CAAAuH,MAAA,KAEFP,gCAAgC,EAChCgB,iBAAiB,CAClB,IAAI,CAAC3F,KAAK,CAAC4F,UAAU,KAAKC,SAAS,GAC/BpH,uBAAuB,CAACqH,oBAAoB,CAAC,CAAC,GAC9C,IAAI,CAAC9F,KAAK,CAAC4F,UAAU,EACzB,IAAI,CAAC5F,KAAK,CAAC+F,aAAa,KAAK,IAC/B,CAAC;QACDC,UAAU,EAAE,IAAI,CAAChG,KAAK,CAACgG,UAAU,KAAK,KAAK;QAC3CC,iBAAiB,EAAE,IAAI,CAACjG,KAAK,CAACiG,iBAAiB;QAC/CC,qBAAqB,EAAE,IAAI,CAAClG,KAAK,CAACkG,qBAAqB;QACvDT,kBAAkB,EAAEA,kBAAkB;QACtCU,iBAAiB,EAAE,IAAI,CAACnG,KAAK,CAACmG,iBAAiB;QAC/ClG,kBAAkB,EAAE4E,mBAAmB;QACvCuB,oBAAoB,EAAE,IAAI,CAACpG,KAAK,CAACoG,oBAAoB;QACrDC,qBAAqB,EAAE,IAAI,CAACrG,KAAK,CAACqG,qBAAqB;QACvDlB,kBAAkB,EAAEA,kBAAkB;QACtCmB,yBAAyB,EACvB,IAAI,CAACtG,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,GAC9B,qBAAqB,GACrB,IAAI,CAACA,KAAK,CAACsG,yBAAyB;QAC1CC,wBAAwB,GAAA9C,oBAAA,GACtB,IAAI,CAACzD,KAAK,CAAC,YAAY,CAAC,YAAAyD,oBAAA,GAAI,IAAI,CAACzD,KAAK,CAACuG,wBAAwB;QACjEf,uBAAuB,EAAEA,uBAAuB;QAChDgB,2BAA2B,GAAA9C,oBAAA,GACzB,IAAI,CAAC1D,KAAK,CAAC,aAAa,CAAC,YAAA0D,oBAAA,GAAI,IAAI,CAAC1D,KAAK,CAACwG,2BAA2B;QACrEC,mBAAmB,EAAE,IAAI,CAACzG,KAAK,CAACyG,mBAAmB;QACnDpG,OAAO,EAAE,IAAI,CAACL,KAAK,CAACK,OAAO;QAC3BqG,SAAS,EACP,IAAI,CAAC1G,KAAK,CAAC0G,SAAS,KAAK,KAAK,IAC9B,IAAI,CAAC1G,KAAK,CAACe,OAAO,KAAK8E,SAAS,IAChC,CAAC,IAAI,CAAC7F,KAAK,CAACE,QAAQ;QACtByG,QAAQ,GAAAhD,cAAA,GAAE,IAAI,CAAC3D,KAAK,CAAC4G,EAAE,YAAAjD,cAAA,GAAI,IAAI,CAAC3D,KAAK,CAAC2G,QAAQ;QAC9CE,aAAa,EAAE,IAAI,CAAC7G,KAAK,CAAC6G,aAAa;QACvCC,gBAAgB,EAAE,IAAI,CAAC9G,KAAK,CAAC8G,gBAAgB;QAC7CC,aAAa,EAAE,IAAI,CAAC/G,KAAK,CAAC+G,aAAa;QACvCC,cAAc,EAAE,IAAI,CAAChH,KAAK,CAACgH,cAAc;QACzCC,WAAW,EAAE,IAAI,CAACjH,KAAK,CAACiH,WAAW;QACnCC,QAAQ,EAAE,IAAI,CAAClH,KAAK,CAACkH,QAAQ;QAC7BC,MAAM,EAAE,IAAI,CAACnH,KAAK,CAACmH;MAAM,IAAAhI,MAAA,CAExB4E,QAAQ,CACb,CAAC;IACH;EAAC;IAAArE,GAAA;IAAAC,KAAA,EAED,SAAAyH,kBAAkBA,CAChBC,SAAuC,EACvCC,SAAgB,EAChB;MACA,IAAI,CAAClI,KAAK,CAACC,YAAY,CAACkI,SAAS,CAAC,IAAI,CAAChI,yBAAyB,CAAC,CAAC,CAAC;IACrE;EAAC;IAAAG,GAAA;IAAAC,KAAA,EAED,SAAA6H,iBAAiBA,CAAA,EAAU;MACzB,IAAI,CAACpI,KAAK,CAACC,YAAY,CAACkI,SAAS,CAAC,IAAI,CAAChI,yBAAyB,CAAC,CAAC,CAAC;IACrE;EAAC;IAAAG,GAAA;IAAAC,KAAA,EAED,SAAA8H,oBAAoBA,CAAA,EAAS;MAC3B,IAAI,CAACrI,KAAK,CAACC,YAAY,CAACqI,KAAK,CAAC,CAAC;IACjC;EAAC;AAAA,EA5QmCpL,KAAK,CAACqL,SAAS;AAA/ClJ,uBAAuB,CAUpBqH,oBAAoB,GAItB,UAAC8B,YAAsB;EAAA,OAAM;IAChC3D,IAAI,EAAE,kBAAkB;IACxB4D,SAAS,EAAE,0BAA0B;IACrCD,YAAY,EAAZA;EACF,CAAC;AAAA,CAAC;AAlBEnJ,uBAAuB,CA2BpBqJ,8BAA8B,GAIhC,UAACF,YAAsB;EAAA,OAAM;IAChC3D,IAAI,EAAE,kBAAkB;IACxB4D,SAAS,EAAE,oCAAoC;IAC/CD,YAAY,EAAZA;EACF,CAAC;AAAA,CAAC;AAnCEnJ,uBAAuB,CAgDpBsJ,MAAM,GASR,UAACzD,KAAa,EAAE0D,UAAmB,EAAEJ,YAAsB,EAAK;EACnE,IAAMK,cAAc,GAAG,IAAAC,qBAAY,EAAC5D,KAAK,CAAC;EAC1C,IAAA6D,kBAAS,EACPF,cAAc,IAAI,IAAI,IAAI,OAAOA,cAAc,KAAK,QAAQ,EAC5D,yCACF,CAAC;EACD,OAAO;IACLhE,IAAI,EAAE,eAAe;IAErBK,KAAK,EAAE2D,cAAc;IACrBD,UAAU,EAAVA,UAAU;IACVJ,YAAY,EAAZA;EACF,CAAC;AACH,CAAC;AAtEGnJ,uBAAuB,CA2EpB2J,sBAAsB,GAAkB;EAAA,OAC7ClH,iBAAQ,CAACC,EAAE,KAAK,SAAS;AAAA;AAmM7B,IAAMwE,iBAAiB,GACrBzE,iBAAQ,CAACC,EAAE,KAAK,SAAS,GAGrB,UAACyE,UAAU,EAAEG,aAAsB;EAAA,OACjCA,aAAa,IAAItH,uBAAuB,CAAC2J,sBAAsB,CAAC,CAAC,GAC7D;IAACC,uBAAuB,EAAEzC;EAAU,CAAC,GACrC;IAAC0C,uBAAuB,EAAE1C;EAAU,CAAC;AAAA,IAG3C,UAACA,UAAU,EAAEG,aAAsB;EAAA,OAAK,IAAI;AAAA;AAElDtH,uBAAuB,CAAC8J,WAAW,GAAG,yBAAyB;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAArL,OAAA,GAEjDqB,uBAAuB", "ignoreList": []}