{"version": 3, "names": ["_invariant", "_interopRequireDefault", "require", "NativeModules", "default", "turboModuleProxy", "global", "__turboModuleProxy", "requireModule", "name", "module", "RN$Bridgeless", "RN$TurboInterop", "RN$UnifiedNativeModuleProxy", "legacyModule", "get", "getEnforcing", "invariant"], "sources": ["TurboModuleRegistry.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\nimport type {TurboModule} from './RCTExport';\n\nimport invariant from 'invariant';\n\nconst NativeModules = require('../BatchedBridge/NativeModules').default;\n\nconst turboModuleProxy = global.__turboModuleProxy;\n\nfunction requireModule<T: TurboModule>(name: string): ?T {\n  if (turboModuleProxy != null) {\n    const module: ?T = turboModuleProxy(name);\n    if (module != null) {\n      return module;\n    }\n  }\n\n  if (\n    global.RN$Bridgeless !== true ||\n    global.RN$TurboInterop === true ||\n    global.RN$UnifiedNativeModuleProxy === true\n  ) {\n    const legacyModule: ?T = NativeModules[name];\n    if (legacyModule != null) {\n      return legacyModule;\n    }\n  }\n\n  return null;\n}\n\nexport function get<T: TurboModule>(name: string): ?T {\n  return requireModule<T>(name);\n}\n\nexport function getEnforcing<T: TurboModule>(name: string): T {\n  const module = requireModule<T>(name);\n  invariant(\n    module != null,\n    `TurboModuleRegistry.getEnforcing(...): '${name}' could not be found. ` +\n      'Verify that a module by this name is registered in the native binary.',\n  );\n  return module;\n}\n"], "mappings": ";;;;;;AAYA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAMC,aAAa,GAAGD,OAAO,iCAAiC,CAAC,CAACE,OAAO;AAEvE,IAAMC,gBAAgB,GAAGC,MAAM,CAACC,kBAAkB;AAElD,SAASC,aAAaA,CAAiBC,IAAY,EAAM;EACvD,IAAIJ,gBAAgB,IAAI,IAAI,EAAE;IAC5B,IAAMK,MAAU,GAAGL,gBAAgB,CAACI,IAAI,CAAC;IACzC,IAAIC,MAAM,IAAI,IAAI,EAAE;MAClB,OAAOA,MAAM;IACf;EACF;EAEA,IACEJ,MAAM,CAACK,aAAa,KAAK,IAAI,IAC7BL,MAAM,CAACM,eAAe,KAAK,IAAI,IAC/BN,MAAM,CAACO,2BAA2B,KAAK,IAAI,EAC3C;IACA,IAAMC,YAAgB,GAAGX,aAAa,CAACM,IAAI,CAAC;IAC5C,IAAIK,YAAY,IAAI,IAAI,EAAE;MACxB,OAAOA,YAAY;IACrB;EACF;EAEA,OAAO,IAAI;AACb;AAEO,SAASC,GAAGA,CAAiBN,IAAY,EAAM;EACpD,OAAOD,aAAa,CAAIC,IAAI,CAAC;AAC/B;AAEO,SAASO,YAAYA,CAAiBP,IAAY,EAAK;EAC5D,IAAMC,MAAM,GAAGF,aAAa,CAAIC,IAAI,CAAC;EACrC,IAAAQ,kBAAS,EACPP,MAAM,IAAI,IAAI,EACd,2CAA2CD,IAAI,wBAAwB,GACrE,uEACJ,CAAC;EACD,OAAOC,MAAM;AACf", "ignoreList": []}