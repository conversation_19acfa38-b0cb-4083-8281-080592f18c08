b51e744ec403a81b9f2609bea5de5129
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useTurboModules = exports.useTurboModuleInterop = exports.useShadowNodeStateOnClone = exports.useRefsForTextInputState = exports.useRawPropsJsiValue = exports.useOptimizedEventBatchingOnAndroid = exports.useNativeViewConfigsInBridgelessMode = exports.useFabricInterop = exports.useAlwaysAvailableJSErrorHandling = exports.updateRuntimeShadowNodeReferencesOnCommit = exports.traceTurboModulePromiseRejectionsOnAndroid = exports.throwExceptionInsteadOfDeadlockOnTurboModuleSetupDuringSyncRenderIOS = exports.shouldUseSetNativePropsInFabric = exports.shouldUseRemoveClippedSubviewsAsDefaultOnIOS = exports.shouldUseAnimatedObjectForTransform = exports.scheduleAnimatedCleanupInMicrotask = exports.removeTurboModuleManagerDelegateMutex = exports.override = exports.lazyAnimationCallbacks = exports.jsOnlyTestFlag = exports.isLayoutAnimationEnabled = exports.fuseboxNetworkInspectionEnabled = exports.fuseboxEnabledRelease = exports.fixVirtualizeListCollapseWindowSize = exports.fixMountingCoordinatorReportedPendingTransactionsOnAndroid = exports.fixMappingOfEventPrioritiesBetweenFabricAndReact = exports.fixDifferentiatorEmittingUpdatesWithWrongParentTag = exports.excludeYogaFromRawProps = exports.enableViewRecyclingForView = exports.enableViewRecyclingForText = exports.enableViewRecycling = exports.enableViewCulling = exports.enableUIConsistency = exports.enableSynchronousStateUpdates = exports.enableReportEventPaintTime = exports.enablePropsUpdateReconciliationAndroid = exports.enablePreciseSchedulingForPremountItemsOnAndroid = exports.enableNewBackgroundAndBorderDrawables = exports.enableNativeCSSParsing = exports.enableLongTaskAPI = exports.enableLayoutAnimationsOnIOS = exports.enableLayoutAnimationsOnAndroid = exports.enableJSRuntimeGCOnMemoryPressureOnIOS = exports.enableImagePrefetchingAndroid = exports.enableIOSViewClipToPaddingBox = exports.enableFabricRenderer = exports.enableFabricLogs = exports.enableEagerRootViewAttachment = exports.enableDOMDocumentAPI = exports.enableCppPropsIteratorSetter = exports.enableBridgelessArchitecture = exports.enableAnimatedClearImmediateFix = exports.enableAccumulatedUpdatesInRawPropsAndroid = exports.enableAccessToHostTreeInFabric = exports.disableMountItemReorderingAndroid = exports.disableInteractionManager = exports.commonTestFlagWithoutNativeImplementation = exports.commonTestFlag = exports.avoidStateUpdateInAnimatedPropsMemo = exports.animatedShouldUseSingleOp = exports.animatedShouldDebounceQueueFlush = void 0;
var _ReactNativeFeatureFlagsBase = require("./ReactNativeFeatureFlagsBase");
var jsOnlyTestFlag = exports.jsOnlyTestFlag = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('jsOnlyTestFlag', false);
var animatedShouldDebounceQueueFlush = exports.animatedShouldDebounceQueueFlush = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('animatedShouldDebounceQueueFlush', false);
var animatedShouldUseSingleOp = exports.animatedShouldUseSingleOp = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('animatedShouldUseSingleOp', false);
var avoidStateUpdateInAnimatedPropsMemo = exports.avoidStateUpdateInAnimatedPropsMemo = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('avoidStateUpdateInAnimatedPropsMemo', false);
var disableInteractionManager = exports.disableInteractionManager = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('disableInteractionManager', false);
var enableAccessToHostTreeInFabric = exports.enableAccessToHostTreeInFabric = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('enableAccessToHostTreeInFabric', false);
var enableAnimatedClearImmediateFix = exports.enableAnimatedClearImmediateFix = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('enableAnimatedClearImmediateFix', true);
var enableDOMDocumentAPI = exports.enableDOMDocumentAPI = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('enableDOMDocumentAPI', false);
var fixVirtualizeListCollapseWindowSize = exports.fixVirtualizeListCollapseWindowSize = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('fixVirtualizeListCollapseWindowSize', false);
var isLayoutAnimationEnabled = exports.isLayoutAnimationEnabled = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('isLayoutAnimationEnabled', true);
var scheduleAnimatedCleanupInMicrotask = exports.scheduleAnimatedCleanupInMicrotask = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('scheduleAnimatedCleanupInMicrotask', false);
var shouldUseAnimatedObjectForTransform = exports.shouldUseAnimatedObjectForTransform = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('shouldUseAnimatedObjectForTransform', false);
var shouldUseRemoveClippedSubviewsAsDefaultOnIOS = exports.shouldUseRemoveClippedSubviewsAsDefaultOnIOS = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('shouldUseRemoveClippedSubviewsAsDefaultOnIOS', false);
var shouldUseSetNativePropsInFabric = exports.shouldUseSetNativePropsInFabric = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('shouldUseSetNativePropsInFabric', true);
var useRefsForTextInputState = exports.useRefsForTextInputState = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('useRefsForTextInputState', false);
var commonTestFlag = exports.commonTestFlag = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('commonTestFlag', false);
var commonTestFlagWithoutNativeImplementation = exports.commonTestFlagWithoutNativeImplementation = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('commonTestFlagWithoutNativeImplementation', false);
var disableMountItemReorderingAndroid = exports.disableMountItemReorderingAndroid = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('disableMountItemReorderingAndroid', false);
var enableAccumulatedUpdatesInRawPropsAndroid = exports.enableAccumulatedUpdatesInRawPropsAndroid = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableAccumulatedUpdatesInRawPropsAndroid', false);
var enableBridgelessArchitecture = exports.enableBridgelessArchitecture = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableBridgelessArchitecture', false);
var enableCppPropsIteratorSetter = exports.enableCppPropsIteratorSetter = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableCppPropsIteratorSetter', false);
var enableEagerRootViewAttachment = exports.enableEagerRootViewAttachment = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableEagerRootViewAttachment', false);
var enableFabricLogs = exports.enableFabricLogs = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableFabricLogs', false);
var enableFabricRenderer = exports.enableFabricRenderer = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableFabricRenderer', false);
var enableIOSViewClipToPaddingBox = exports.enableIOSViewClipToPaddingBox = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableIOSViewClipToPaddingBox', false);
var enableImagePrefetchingAndroid = exports.enableImagePrefetchingAndroid = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableImagePrefetchingAndroid', false);
var enableJSRuntimeGCOnMemoryPressureOnIOS = exports.enableJSRuntimeGCOnMemoryPressureOnIOS = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableJSRuntimeGCOnMemoryPressureOnIOS', false);
var enableLayoutAnimationsOnAndroid = exports.enableLayoutAnimationsOnAndroid = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableLayoutAnimationsOnAndroid', false);
var enableLayoutAnimationsOnIOS = exports.enableLayoutAnimationsOnIOS = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableLayoutAnimationsOnIOS', true);
var enableLongTaskAPI = exports.enableLongTaskAPI = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableLongTaskAPI', false);
var enableNativeCSSParsing = exports.enableNativeCSSParsing = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableNativeCSSParsing', false);
var enableNewBackgroundAndBorderDrawables = exports.enableNewBackgroundAndBorderDrawables = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableNewBackgroundAndBorderDrawables', false);
var enablePreciseSchedulingForPremountItemsOnAndroid = exports.enablePreciseSchedulingForPremountItemsOnAndroid = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enablePreciseSchedulingForPremountItemsOnAndroid', false);
var enablePropsUpdateReconciliationAndroid = exports.enablePropsUpdateReconciliationAndroid = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enablePropsUpdateReconciliationAndroid', false);
var enableReportEventPaintTime = exports.enableReportEventPaintTime = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableReportEventPaintTime', false);
var enableSynchronousStateUpdates = exports.enableSynchronousStateUpdates = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableSynchronousStateUpdates', false);
var enableUIConsistency = exports.enableUIConsistency = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableUIConsistency', false);
var enableViewCulling = exports.enableViewCulling = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableViewCulling', false);
var enableViewRecycling = exports.enableViewRecycling = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableViewRecycling', false);
var enableViewRecyclingForText = exports.enableViewRecyclingForText = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableViewRecyclingForText', true);
var enableViewRecyclingForView = exports.enableViewRecyclingForView = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableViewRecyclingForView', true);
var excludeYogaFromRawProps = exports.excludeYogaFromRawProps = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('excludeYogaFromRawProps', false);
var fixDifferentiatorEmittingUpdatesWithWrongParentTag = exports.fixDifferentiatorEmittingUpdatesWithWrongParentTag = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('fixDifferentiatorEmittingUpdatesWithWrongParentTag', true);
var fixMappingOfEventPrioritiesBetweenFabricAndReact = exports.fixMappingOfEventPrioritiesBetweenFabricAndReact = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('fixMappingOfEventPrioritiesBetweenFabricAndReact', false);
var fixMountingCoordinatorReportedPendingTransactionsOnAndroid = exports.fixMountingCoordinatorReportedPendingTransactionsOnAndroid = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('fixMountingCoordinatorReportedPendingTransactionsOnAndroid', false);
var fuseboxEnabledRelease = exports.fuseboxEnabledRelease = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('fuseboxEnabledRelease', false);
var fuseboxNetworkInspectionEnabled = exports.fuseboxNetworkInspectionEnabled = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('fuseboxNetworkInspectionEnabled', false);
var lazyAnimationCallbacks = exports.lazyAnimationCallbacks = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('lazyAnimationCallbacks', false);
var removeTurboModuleManagerDelegateMutex = exports.removeTurboModuleManagerDelegateMutex = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('removeTurboModuleManagerDelegateMutex', false);
var throwExceptionInsteadOfDeadlockOnTurboModuleSetupDuringSyncRenderIOS = exports.throwExceptionInsteadOfDeadlockOnTurboModuleSetupDuringSyncRenderIOS = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('throwExceptionInsteadOfDeadlockOnTurboModuleSetupDuringSyncRenderIOS', false);
var traceTurboModulePromiseRejectionsOnAndroid = exports.traceTurboModulePromiseRejectionsOnAndroid = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('traceTurboModulePromiseRejectionsOnAndroid', false);
var updateRuntimeShadowNodeReferencesOnCommit = exports.updateRuntimeShadowNodeReferencesOnCommit = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('updateRuntimeShadowNodeReferencesOnCommit', false);
var useAlwaysAvailableJSErrorHandling = exports.useAlwaysAvailableJSErrorHandling = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('useAlwaysAvailableJSErrorHandling', false);
var useFabricInterop = exports.useFabricInterop = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('useFabricInterop', false);
var useNativeViewConfigsInBridgelessMode = exports.useNativeViewConfigsInBridgelessMode = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('useNativeViewConfigsInBridgelessMode', false);
var useOptimizedEventBatchingOnAndroid = exports.useOptimizedEventBatchingOnAndroid = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('useOptimizedEventBatchingOnAndroid', false);
var useRawPropsJsiValue = exports.useRawPropsJsiValue = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('useRawPropsJsiValue', false);
var useShadowNodeStateOnClone = exports.useShadowNodeStateOnClone = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('useShadowNodeStateOnClone', false);
var useTurboModuleInterop = exports.useTurboModuleInterop = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('useTurboModuleInterop', false);
var useTurboModules = exports.useTurboModules = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('useTurboModules', false);
var override = exports.override = _ReactNativeFeatureFlagsBase.setOverrides;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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