{"version": 3, "names": ["_vectorIcons", "require", "_native", "_react", "_interopRequireWildcard", "_reactNative", "_SafeAreaWrapper", "_messagingService", "_ThemeContext", "_responsiveUtils", "_jsxRuntime", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "getAvatarColor", "name", "avatarColors", "index", "charCodeAt", "length", "formatLastMessageTime", "timestamp", "now", "Date", "messageTime", "diffInMinutes", "Math", "floor", "getTime", "diffInHours", "diffInDays", "toLocaleDateString", "MessagesScreen", "exports", "_useTheme", "useTheme", "colors", "styles", "createStyles", "navigation", "useNavigation", "_useState", "useState", "_useState2", "_slicedToArray2", "conversations", "setConversations", "_useState3", "_useState4", "refreshing", "setRefreshing", "_useState5", "_useState6", "loading", "setLoading", "_useState7", "_useState8", "error", "setError", "useEffect", "loadConversations", "_ref", "_asyncToGenerator2", "response", "messagingService", "getConversations", "transformedConversations", "map", "conv", "_conv$last_message", "_conv$last_message2", "id", "toString", "participantName", "participants", "filter", "p", "current_user_id", "first_name", "last_name", "join", "lastMessage", "last_message", "content", "created_at", "updated_at", "unreadCount", "unread_count", "err", "console", "apply", "arguments", "handleRefresh", "_ref2", "handleConversationPress", "conversation", "prev", "assign", "navigate", "conversationId", "renderEmptyState", "jsxs", "View", "style", "emptyState", "children", "jsx", "Ionicons", "size", "color", "sage200", "Text", "emptyStateText", "emptyStateSubtext", "renderConversation", "_ref3", "item", "hasUnread", "isOnline", "random", "TouchableOpacity", "onPress", "testID", "accessibilityLabel", "accessibilityHint", "conversationItem", "unreadConversation", "activeOpacity", "conversationContent", "avatar<PERSON><PERSON><PERSON>", "avatar", "backgroundColor", "avatarText", "char<PERSON>t", "toUpperCase", "onlineIndicator", "conversationInfo", "<PERSON><PERSON><PERSON><PERSON>", "unreadText", "numberOfLines", "lastMessageContainer", "unreadBadge", "SafeAreaScreen", "background", "primary", "statusBarStyle", "respectNotch", "respectGestures", "header", "title", "newMessageButton", "accessibilityRole", "sage400", "loadingContainer", "ActivityIndicator", "loadingText", "FlatList", "data", "renderItem", "keyExtractor", "conversationsList", "contentContainerStyle", "conversationsContent", "showsVerticalScrollIndicator", "refreshControl", "RefreshControl", "onRefresh", "tintColor", "ListEmptyComponent", "<PERSON><PERSON><PERSON><PERSON>", "errorText", "StyleSheet", "create", "flexDirection", "justifyContent", "alignItems", "paddingHorizontal", "getResponsiveSpacing", "paddingVertical", "borderBottomWidth", "borderBottomColor", "sage100", "fontSize", "getResponsiveFontSize", "fontWeight", "text", "padding", "borderRadius", "flex", "marginTop", "secondary", "sage50", "sage25", "position", "marginRight", "width", "getMinimumTouchTarget", "height", "bottom", "right", "borderWidth", "borderColor", "marginBottom", "tertiary", "min<PERSON><PERSON><PERSON>", "textAlign", "lineHeight"], "sources": ["MessagesScreen.tsx"], "sourcesContent": ["/**\n * Messages Screen - Customer Communication\n *\n * Component Contract:\n * - Displays customer's message conversations with providers\n * - Shows conversation list with last message and unread counts\n * - Supports navigation to individual chat screens\n * - Integrates with backend messaging API\n * - Follows responsive design and accessibility guidelines\n *\n * @version 2.0.0 - Redesigned to match frontend_v0 patterns\n * <AUTHOR> Development Team\n */\n\nimport { Ionicons } from '@expo/vector-icons';\nimport { useNavigation } from '@react-navigation/native';\nimport { StackNavigationProp } from '@react-navigation/stack';\nimport React, { useState, useEffect } from 'react';\nimport {\n  View,\n  Text,\n  StyleSheet,\n  FlatList,\n  TouchableOpacity,\n  ActivityIndicator,\n  RefreshControl,\n} from 'react-native';\n\nimport { SafeAreaScreen } from '../components/ui/SafeAreaWrapper';\nimport { messagingService } from '../services/messagingService';\n\nimport { useTheme } from '../contexts/ThemeContext';\nimport type { CustomerStackParamList } from '../navigation/types';\nimport {\n  getResponsiveSpacing,\n  getResponsiveFontSize,\n  getMinimumTouchTarget,\n} from '../utils/responsiveUtils';\n\n// Mock conversation data for now\ninterface MockConversation {\n  id: string;\n  participantName: string;\n  lastMessage: string;\n  timestamp: string;\n  unreadCount: number;\n  avatar?: string;\n}\n\n// Helper function to generate avatar colors\nconst getAvatarColor = (name: string): string => {\n  const avatarColors = [\n    '#5A7A63', // Primary sage\n    '#4A6B52', // Medium sage\n    '#3A5B42', // Darker sage\n    '#10B981', // Success green\n    '#F59E0B', // Warning orange\n    '#3B82F6', // Info blue\n  ];\n  const index = name.charCodeAt(0) % avatarColors.length;\n  return avatarColors[index];\n};\n\n// Helper function to format last message time\nconst formatLastMessageTime = (timestamp: string): string => {\n  const now = new Date();\n  const messageTime = new Date(timestamp);\n  const diffInMinutes = Math.floor(\n    (now.getTime() - messageTime.getTime()) / (1000 * 60),\n  );\n\n  if (diffInMinutes < 1) return 'Just now';\n  if (diffInMinutes < 60) return `${diffInMinutes}m`;\n\n  const diffInHours = Math.floor(diffInMinutes / 60);\n  if (diffInHours < 24) return `${diffInHours}h`;\n\n  const diffInDays = Math.floor(diffInHours / 24);\n  if (diffInDays < 7) return `${diffInDays}d`;\n\n  return messageTime.toLocaleDateString();\n};\n\ntype MessagesScreenNavigationProp = StackNavigationProp<CustomerStackParamList>;\n\nexport const MessagesScreen: React.FC = () => {\n  const { colors } = useTheme();\n  const styles = createStyles(colors);\n  const navigation = useNavigation<MessagesScreenNavigationProp>();\n\n  const [conversations, setConversations] = useState<MockConversation[]>([]);\n  const [refreshing, setRefreshing] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  useEffect(() => {\n    loadConversations();\n  }, []);\n\n  const loadConversations = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      // Load conversations from backend API\n      const response = await messagingService.getConversations();\n\n      // Transform backend data to match frontend interface\n      const transformedConversations: MockConversation[] = response.conversations.map((conv: any) => ({\n        id: conv.id.toString(),\n        participantName: conv.participants\n          .filter((p: any) => p.id !== conv.current_user_id)\n          .map((p: any) => `${p.first_name} ${p.last_name}`)\n          .join(', ') || 'Unknown',\n        lastMessage: conv.last_message?.content || 'No messages yet',\n        timestamp: conv.last_message?.created_at || conv.updated_at,\n        unreadCount: conv.unread_count || 0,\n      }));\n\n      setConversations(transformedConversations);\n    } catch (err) {\n      console.error('Error loading conversations:', err);\n      setError('Failed to load conversations. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    setError(null);\n    await loadConversations();\n    setRefreshing(false);\n  };\n\n  const handleConversationPress = (conversation: MockConversation) => {\n    // Mark as read locally\n    setConversations(prev =>\n      prev.map(conv =>\n        conv.id === conversation.id ? { ...conv, unreadCount: 0 } : conv,\n      ),\n    );\n\n    // Navigate to conversation screen\n    navigation.navigate('Conversation', {\n      conversationId: conversation.id,\n      participantName: conversation.participantName,\n    });\n  };\n\n  const renderEmptyState = () => (\n    <View style={styles.emptyState}>\n      <Ionicons name=\"chatbubbles-outline\" size={64} color={colors.sage200} />\n      <Text style={styles.emptyStateText}>No conversations yet</Text>\n      <Text style={styles.emptyStateSubtext}>\n        Your messages with providers will appear here\n      </Text>\n    </View>\n  );\n\n  const renderConversation = ({ item }: { item: MockConversation }) => {\n    const hasUnread = item.unreadCount > 0;\n    const isOnline = Math.random() > 0.5; // Mock online status\n\n    return (\n      <TouchableOpacity\n        onPress={() => handleConversationPress(item)}\n        testID={`conversation-${item.id}`}\n        accessibilityLabel={`Conversation with ${item.participantName}`}\n        accessibilityHint=\"Tap to open chat\"\n        style={[styles.conversationItem, hasUnread && styles.unreadConversation]}\n        activeOpacity={0.7}>\n        <View style={styles.conversationContent}>\n          {/* Avatar */}\n          <View style={styles.avatarContainer}>\n            <View style={[styles.avatar, { backgroundColor: getAvatarColor(item.participantName) }]}>\n              <Text style={styles.avatarText}>\n                {item.participantName.charAt(0).toUpperCase()}\n              </Text>\n            </View>\n            {/* Online indicator */}\n            {isOnline && <View style={styles.onlineIndicator} />}\n          </View>\n\n          {/* Conversation Info */}\n          <View style={styles.conversationInfo}>\n            <View style={styles.conversationHeader}>\n              <Text\n                style={[\n                  styles.participantName,\n                  hasUnread && styles.unreadText,\n                ]}\n                numberOfLines={1}>\n                {item.participantName}\n              </Text>\n              <Text style={styles.timestamp}>\n                {formatLastMessageTime(item.timestamp)}\n              </Text>\n            </View>\n\n            <View style={styles.lastMessageContainer}>\n              <Text\n                style={[styles.lastMessage, hasUnread && styles.unreadText]}\n                numberOfLines={2}>\n                {item.lastMessage}\n              </Text>\n              {hasUnread && (\n                <View style={styles.unreadBadge}>\n                  <Text style={styles.unreadCount}>\n                    {item.unreadCount > 99 ? '99+' : item.unreadCount}\n                  </Text>\n                </View>\n              )}\n            </View>\n          </View>\n        </View>\n      </TouchableOpacity>\n    );\n  };\n\n  return (\n    <SafeAreaScreen\n      backgroundColor={colors.background.primary}\n      statusBarStyle=\"dark-content\"\n      respectNotch={true}\n      respectGestures={true}\n      testID=\"messages-screen\">\n      {/* Header */}\n      <View style={styles.header}>\n        <Text style={styles.title}>Messages</Text>\n        <TouchableOpacity\n          style={styles.newMessageButton}\n          testID=\"new-message-button\"\n          accessibilityLabel=\"Start new conversation\"\n          accessibilityRole=\"button\">\n          <Ionicons name=\"create-outline\" size={24} color={colors.sage400} />\n        </TouchableOpacity>\n      </View>\n\n      {/* Conversations List */}\n      {loading && conversations.length === 0 ? (\n        <View style={styles.loadingContainer}>\n          <ActivityIndicator size=\"large\" color={colors.sage400} />\n          <Text style={styles.loadingText}>Loading conversations...</Text>\n        </View>\n      ) : (\n        <FlatList\n          data={conversations}\n          renderItem={renderConversation}\n          keyExtractor={item => item.id}\n          style={styles.conversationsList}\n          contentContainerStyle={styles.conversationsContent}\n          showsVerticalScrollIndicator={false}\n          refreshControl={\n            <RefreshControl\n              refreshing={refreshing}\n              onRefresh={handleRefresh}\n              colors={[colors.sage400]}\n              tintColor={colors.sage400}\n            />\n          }\n          ListEmptyComponent={renderEmptyState}\n        />\n      )}\n\n      {error && (\n        <View style={styles.errorContainer}>\n          <Text style={styles.errorText}>\n            Error loading conversations. Please try again.\n          </Text>\n        </View>\n      )}\n    </SafeAreaScreen>\n  );\n};\n\nconst createStyles = (colors: any) =>\n  StyleSheet.create({\n    header: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      paddingHorizontal: getResponsiveSpacing(20),\n      paddingVertical: getResponsiveSpacing(16),\n      backgroundColor: colors.background.primary,\n      borderBottomWidth: 1,\n      borderBottomColor: colors.sage100,\n    },\n    title: {\n      fontSize: getResponsiveFontSize(24),\n      fontWeight: '700',\n      color: colors.text.primary,\n    },\n    newMessageButton: {\n      padding: getResponsiveSpacing(8),\n      borderRadius: getResponsiveSpacing(8),\n    },\n    loadingContainer: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      paddingVertical: getResponsiveSpacing(40),\n    },\n    loadingText: {\n      marginTop: getResponsiveSpacing(12),\n      fontSize: getResponsiveFontSize(16),\n      color: colors.text.secondary,\n    },\n    conversationsList: {\n      flex: 1,\n    },\n    conversationsContent: {\n      paddingVertical: getResponsiveSpacing(8),\n    },\n    conversationItem: {\n      backgroundColor: colors.background.primary,\n      paddingHorizontal: getResponsiveSpacing(20),\n      paddingVertical: getResponsiveSpacing(16),\n      borderBottomWidth: 1,\n      borderBottomColor: colors.sage50,\n    },\n    unreadConversation: {\n      backgroundColor: colors.sage25,\n    },\n    conversationContent: {\n      flexDirection: 'row',\n      alignItems: 'center',\n    },\n    avatarContainer: {\n      position: 'relative',\n      marginRight: getResponsiveSpacing(12),\n    },\n    avatar: {\n      width: getMinimumTouchTarget(48),\n      height: getMinimumTouchTarget(48),\n      borderRadius: getMinimumTouchTarget(24),\n      justifyContent: 'center',\n      alignItems: 'center',\n    },\n    avatarText: {\n      fontSize: getResponsiveFontSize(18),\n      fontWeight: '600',\n      color: '#FFFFFF',\n    },\n    onlineIndicator: {\n      position: 'absolute',\n      bottom: 2,\n      right: 2,\n      width: 12,\n      height: 12,\n      borderRadius: 6,\n      backgroundColor: '#10B981',\n      borderWidth: 2,\n      borderColor: colors.background.primary,\n    },\n    conversationInfo: {\n      flex: 1,\n    },\n    conversationHeader: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      marginBottom: getResponsiveSpacing(4),\n    },\n    participantName: {\n      fontSize: getResponsiveFontSize(16),\n      fontWeight: '600',\n      color: colors.text.primary,\n      flex: 1,\n      marginRight: getResponsiveSpacing(8),\n    },\n    timestamp: {\n      fontSize: getResponsiveFontSize(12),\n      color: colors.text.tertiary,\n    },\n    lastMessageContainer: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      justifyContent: 'space-between',\n    },\n    lastMessage: {\n      fontSize: getResponsiveFontSize(14),\n      color: colors.text.secondary,\n      flex: 1,\n      marginRight: getResponsiveSpacing(8),\n    },\n    unreadText: {\n      fontWeight: '600',\n      color: colors.text.primary,\n    },\n    unreadBadge: {\n      backgroundColor: colors.sage400,\n      borderRadius: 10,\n      minWidth: 20,\n      height: 20,\n      justifyContent: 'center',\n      alignItems: 'center',\n      paddingHorizontal: 6,\n    },\n    unreadCount: {\n      fontSize: getResponsiveFontSize(12),\n      fontWeight: '600',\n      color: '#FFFFFF',\n    },\n    emptyState: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      paddingHorizontal: getResponsiveSpacing(40),\n      paddingVertical: getResponsiveSpacing(60),\n    },\n    emptyStateText: {\n      fontSize: getResponsiveFontSize(18),\n      fontWeight: '600',\n      color: colors.text.primary,\n      marginTop: getResponsiveSpacing(16),\n      marginBottom: getResponsiveSpacing(8),\n    },\n    emptyStateSubtext: {\n      fontSize: getResponsiveFontSize(14),\n      color: colors.text.secondary,\n      textAlign: 'center',\n      lineHeight: 20,\n    },\n    errorContainer: {\n      padding: getResponsiveSpacing(20),\n      alignItems: 'center',\n    },\n    errorText: {\n      fontSize: getResponsiveFontSize(16),\n      color: colors.error,\n      textAlign: 'center',\n    },\n  });\n"], "mappings": ";;;;;;;AAcA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AAEA,IAAAE,MAAA,GAAAC,uBAAA,CAAAH,OAAA;AACA,IAAAI,YAAA,GAAAJ,OAAA;AAUA,IAAAK,gBAAA,GAAAL,OAAA;AACA,IAAAM,iBAAA,GAAAN,OAAA;AAEA,IAAAO,aAAA,GAAAP,OAAA;AAEA,IAAAQ,gBAAA,GAAAR,OAAA;AAIkC,IAAAS,WAAA,GAAAT,OAAA;AAAA,SAAAG,wBAAAO,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAT,uBAAA,YAAAA,wBAAAO,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAalC,IAAMmB,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,IAAY,EAAa;EAC/C,IAAMC,YAAY,GAAG,CACnB,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;EACD,IAAMC,KAAK,GAAGF,IAAI,CAACG,UAAU,CAAC,CAAC,CAAC,GAAGF,YAAY,CAACG,MAAM;EACtD,OAAOH,YAAY,CAACC,KAAK,CAAC;AAC5B,CAAC;AAGD,IAAMG,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAIC,SAAiB,EAAa;EAC3D,IAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;EACtB,IAAMC,WAAW,GAAG,IAAID,IAAI,CAACF,SAAS,CAAC;EACvC,IAAMI,aAAa,GAAGC,IAAI,CAACC,KAAK,CAC9B,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC,GAAGJ,WAAW,CAACI,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,CACtD,CAAC;EAED,IAAIH,aAAa,GAAG,CAAC,EAAE,OAAO,UAAU;EACxC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;EAElD,IAAMI,WAAW,GAAGH,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC;EAClD,IAAII,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;EAE9C,IAAMC,UAAU,GAAGJ,IAAI,CAACC,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;EAC/C,IAAIC,UAAU,GAAG,CAAC,EAAE,OAAO,GAAGA,UAAU,GAAG;EAE3C,OAAON,WAAW,CAACO,kBAAkB,CAAC,CAAC;AACzC,CAAC;AAIM,IAAMC,cAAwB,GAAAC,OAAA,CAAAD,cAAA,GAAG,SAA3BA,cAAwBA,CAAA,EAAS;EAC5C,IAAAE,SAAA,GAAmB,IAAAC,sBAAQ,EAAC,CAAC;IAArBC,MAAM,GAAAF,SAAA,CAANE,MAAM;EACd,IAAMC,MAAM,GAAGC,YAAY,CAACF,MAAM,CAAC;EACnC,IAAMG,UAAU,GAAG,IAAAC,qBAAa,EAA+B,CAAC;EAEhE,IAAAC,SAAA,GAA0C,IAAAC,eAAQ,EAAqB,EAAE,CAAC;IAAAC,UAAA,OAAAC,eAAA,CAAAxC,OAAA,EAAAqC,SAAA;IAAnEI,aAAa,GAAAF,UAAA;IAAEG,gBAAgB,GAAAH,UAAA;EACtC,IAAAI,UAAA,GAAoC,IAAAL,eAAQ,EAAC,KAAK,CAAC;IAAAM,UAAA,OAAAJ,eAAA,CAAAxC,OAAA,EAAA2C,UAAA;IAA5CE,UAAU,GAAAD,UAAA;IAAEE,aAAa,GAAAF,UAAA;EAChC,IAAAG,UAAA,GAA8B,IAAAT,eAAQ,EAAC,IAAI,CAAC;IAAAU,UAAA,OAAAR,eAAA,CAAAxC,OAAA,EAAA+C,UAAA;IAArCE,OAAO,GAAAD,UAAA;IAAEE,UAAU,GAAAF,UAAA;EAC1B,IAAAG,UAAA,GAA0B,IAAAb,eAAQ,EAAgB,IAAI,CAAC;IAAAc,UAAA,OAAAZ,eAAA,CAAAxC,OAAA,EAAAmD,UAAA;IAAhDE,KAAK,GAAAD,UAAA;IAAEE,QAAQ,GAAAF,UAAA;EACtB,IAAAG,gBAAS,EAAC,YAAM;IACdC,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMA,iBAAiB;IAAA,IAAAC,IAAA,OAAAC,kBAAA,CAAA1D,OAAA,EAAG,aAAY;MACpCkD,UAAU,CAAC,IAAI,CAAC;MAChBI,QAAQ,CAAC,IAAI,CAAC;MACd,IAAI;QAEF,IAAMK,QAAQ,SAASC,kCAAgB,CAACC,gBAAgB,CAAC,CAAC;QAG1D,IAAMC,wBAA4C,GAAGH,QAAQ,CAAClB,aAAa,CAACsB,GAAG,CAAC,UAACC,IAAS;UAAA,IAAAC,kBAAA,EAAAC,mBAAA;UAAA,OAAM;YAC9FC,EAAE,EAAEH,IAAI,CAACG,EAAE,CAACC,QAAQ,CAAC,CAAC;YACtBC,eAAe,EAAEL,IAAI,CAACM,YAAY,CAC/BC,MAAM,CAAC,UAACC,CAAM;cAAA,OAAKA,CAAC,CAACL,EAAE,KAAKH,IAAI,CAACS,eAAe;YAAA,EAAC,CACjDV,GAAG,CAAC,UAACS,CAAM;cAAA,OAAK,GAAGA,CAAC,CAACE,UAAU,IAAIF,CAAC,CAACG,SAAS,EAAE;YAAA,EAAC,CACjDC,IAAI,CAAC,IAAI,CAAC,IAAI,SAAS;YAC1BC,WAAW,EAAE,EAAAZ,kBAAA,GAAAD,IAAI,CAACc,YAAY,qBAAjBb,kBAAA,CAAmBc,OAAO,KAAI,iBAAiB;YAC5D9D,SAAS,EAAE,EAAAiD,mBAAA,GAAAF,IAAI,CAACc,YAAY,qBAAjBZ,mBAAA,CAAmBc,UAAU,KAAIhB,IAAI,CAACiB,UAAU;YAC3DC,WAAW,EAAElB,IAAI,CAACmB,YAAY,IAAI;UACpC,CAAC;QAAA,CAAC,CAAC;QAEHzC,gBAAgB,CAACoB,wBAAwB,CAAC;MAC5C,CAAC,CAAC,OAAOsB,GAAG,EAAE;QACZC,OAAO,CAAChC,KAAK,CAAC,8BAA8B,EAAE+B,GAAG,CAAC;QAClD9B,QAAQ,CAAC,iDAAiD,CAAC;MAC7D,CAAC,SAAS;QACRJ,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAAA,gBA1BKM,iBAAiBA,CAAA;MAAA,OAAAC,IAAA,CAAA6B,KAAA,OAAAC,SAAA;IAAA;EAAA,GA0BtB;EAED,IAAMC,aAAa;IAAA,IAAAC,KAAA,OAAA/B,kBAAA,CAAA1D,OAAA,EAAG,aAAY;MAChC8C,aAAa,CAAC,IAAI,CAAC;MACnBQ,QAAQ,CAAC,IAAI,CAAC;MACd,MAAME,iBAAiB,CAAC,CAAC;MACzBV,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC;IAAA,gBALK0C,aAAaA,CAAA;MAAA,OAAAC,KAAA,CAAAH,KAAA,OAAAC,SAAA;IAAA;EAAA,GAKlB;EAED,IAAMG,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAIC,YAA8B,EAAK;IAElEjD,gBAAgB,CAAC,UAAAkD,IAAI;MAAA,OACnBA,IAAI,CAAC7B,GAAG,CAAC,UAAAC,IAAI;QAAA,OACXA,IAAI,CAACG,EAAE,KAAKwB,YAAY,CAACxB,EAAE,GAAA5D,MAAA,CAAAsF,MAAA,KAAQ7B,IAAI;UAAEkB,WAAW,EAAE;QAAC,KAAKlB,IAAI;MAAA,CAClE,CAAC;IAAA,CACH,CAAC;IAGD7B,UAAU,CAAC2D,QAAQ,CAAC,cAAc,EAAE;MAClCC,cAAc,EAAEJ,YAAY,CAACxB,EAAE;MAC/BE,eAAe,EAAEsB,YAAY,CAACtB;IAChC,CAAC,CAAC;EACJ,CAAC;EAED,IAAM2B,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA;IAAA,OACpB,IAAA3G,WAAA,CAAA4G,IAAA,EAACjH,YAAA,CAAAkH,IAAI;MAACC,KAAK,EAAElE,MAAM,CAACmE,UAAW;MAAAC,QAAA,GAC7B,IAAAhH,WAAA,CAAAiH,GAAA,EAAC3H,YAAA,CAAA4H,QAAQ;QAAC5F,IAAI,EAAC,qBAAqB;QAAC6F,IAAI,EAAE,EAAG;QAACC,KAAK,EAAEzE,MAAM,CAAC0E;MAAQ,CAAE,CAAC,EACxE,IAAArH,WAAA,CAAAiH,GAAA,EAACtH,YAAA,CAAA2H,IAAI;QAACR,KAAK,EAAElE,MAAM,CAAC2E,cAAe;QAAAP,QAAA,EAAC;MAAoB,CAAM,CAAC,EAC/D,IAAAhH,WAAA,CAAAiH,GAAA,EAACtH,YAAA,CAAA2H,IAAI;QAACR,KAAK,EAAElE,MAAM,CAAC4E,iBAAkB;QAAAR,QAAA,EAAC;MAEvC,CAAM,CAAC;IAAA,CACH,CAAC;EAAA,CACR;EAED,IAAMS,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAAC,KAAA,EAA6C;IAAA,IAAvCC,IAAI,GAAAD,KAAA,CAAJC,IAAI;IAChC,IAAMC,SAAS,GAAGD,IAAI,CAAC9B,WAAW,GAAG,CAAC;IACtC,IAAMgC,QAAQ,GAAG5F,IAAI,CAAC6F,MAAM,CAAC,CAAC,GAAG,GAAG;IAEpC,OACE,IAAA9H,WAAA,CAAAiH,GAAA,EAACtH,YAAA,CAAAoI,gBAAgB;MACfC,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQ3B,uBAAuB,CAACsB,IAAI,CAAC;MAAA,CAAC;MAC7CM,MAAM,EAAE,gBAAgBN,IAAI,CAAC7C,EAAE,EAAG;MAClCoD,kBAAkB,EAAE,qBAAqBP,IAAI,CAAC3C,eAAe,EAAG;MAChEmD,iBAAiB,EAAC,kBAAkB;MACpCrB,KAAK,EAAE,CAAClE,MAAM,CAACwF,gBAAgB,EAAER,SAAS,IAAIhF,MAAM,CAACyF,kBAAkB,CAAE;MACzEC,aAAa,EAAE,GAAI;MAAAtB,QAAA,EACnB,IAAAhH,WAAA,CAAA4G,IAAA,EAACjH,YAAA,CAAAkH,IAAI;QAACC,KAAK,EAAElE,MAAM,CAAC2F,mBAAoB;QAAAvB,QAAA,GAEtC,IAAAhH,WAAA,CAAA4G,IAAA,EAACjH,YAAA,CAAAkH,IAAI;UAACC,KAAK,EAAElE,MAAM,CAAC4F,eAAgB;UAAAxB,QAAA,GAClC,IAAAhH,WAAA,CAAAiH,GAAA,EAACtH,YAAA,CAAAkH,IAAI;YAACC,KAAK,EAAE,CAAClE,MAAM,CAAC6F,MAAM,EAAE;cAAEC,eAAe,EAAErH,cAAc,CAACsG,IAAI,CAAC3C,eAAe;YAAE,CAAC,CAAE;YAAAgC,QAAA,EACtF,IAAAhH,WAAA,CAAAiH,GAAA,EAACtH,YAAA,CAAA2H,IAAI;cAACR,KAAK,EAAElE,MAAM,CAAC+F,UAAW;cAAA3B,QAAA,EAC5BW,IAAI,CAAC3C,eAAe,CAAC4D,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;YAAC,CACzC;UAAC,CACH,CAAC,EAENhB,QAAQ,IAAI,IAAA7H,WAAA,CAAAiH,GAAA,EAACtH,YAAA,CAAAkH,IAAI;YAACC,KAAK,EAAElE,MAAM,CAACkG;UAAgB,CAAE,CAAC;QAAA,CAChD,CAAC,EAGP,IAAA9I,WAAA,CAAA4G,IAAA,EAACjH,YAAA,CAAAkH,IAAI;UAACC,KAAK,EAAElE,MAAM,CAACmG,gBAAiB;UAAA/B,QAAA,GACnC,IAAAhH,WAAA,CAAA4G,IAAA,EAACjH,YAAA,CAAAkH,IAAI;YAACC,KAAK,EAAElE,MAAM,CAACoG,kBAAmB;YAAAhC,QAAA,GACrC,IAAAhH,WAAA,CAAAiH,GAAA,EAACtH,YAAA,CAAA2H,IAAI;cACHR,KAAK,EAAE,CACLlE,MAAM,CAACoC,eAAe,EACtB4C,SAAS,IAAIhF,MAAM,CAACqG,UAAU,CAC9B;cACFC,aAAa,EAAE,CAAE;cAAAlC,QAAA,EAChBW,IAAI,CAAC3C;YAAe,CACjB,CAAC,EACP,IAAAhF,WAAA,CAAAiH,GAAA,EAACtH,YAAA,CAAA2H,IAAI;cAACR,KAAK,EAAElE,MAAM,CAAChB,SAAU;cAAAoF,QAAA,EAC3BrF,qBAAqB,CAACgG,IAAI,CAAC/F,SAAS;YAAC,CAClC,CAAC;UAAA,CACH,CAAC,EAEP,IAAA5B,WAAA,CAAA4G,IAAA,EAACjH,YAAA,CAAAkH,IAAI;YAACC,KAAK,EAAElE,MAAM,CAACuG,oBAAqB;YAAAnC,QAAA,GACvC,IAAAhH,WAAA,CAAAiH,GAAA,EAACtH,YAAA,CAAA2H,IAAI;cACHR,KAAK,EAAE,CAAClE,MAAM,CAAC4C,WAAW,EAAEoC,SAAS,IAAIhF,MAAM,CAACqG,UAAU,CAAE;cAC5DC,aAAa,EAAE,CAAE;cAAAlC,QAAA,EAChBW,IAAI,CAACnC;YAAW,CACb,CAAC,EACNoC,SAAS,IACR,IAAA5H,WAAA,CAAAiH,GAAA,EAACtH,YAAA,CAAAkH,IAAI;cAACC,KAAK,EAAElE,MAAM,CAACwG,WAAY;cAAApC,QAAA,EAC9B,IAAAhH,WAAA,CAAAiH,GAAA,EAACtH,YAAA,CAAA2H,IAAI;gBAACR,KAAK,EAAElE,MAAM,CAACiD,WAAY;gBAAAmB,QAAA,EAC7BW,IAAI,CAAC9B,WAAW,GAAG,EAAE,GAAG,KAAK,GAAG8B,IAAI,CAAC9B;cAAW,CAC7C;YAAC,CACH,CACP;UAAA,CACG,CAAC;QAAA,CACH,CAAC;MAAA,CACH;IAAC,CACS,CAAC;EAEvB,CAAC;EAED,OACE,IAAA7F,WAAA,CAAA4G,IAAA,EAAChH,gBAAA,CAAAyJ,cAAc;IACbX,eAAe,EAAE/F,MAAM,CAAC2G,UAAU,CAACC,OAAQ;IAC3CC,cAAc,EAAC,cAAc;IAC7BC,YAAY,EAAE,IAAK;IACnBC,eAAe,EAAE,IAAK;IACtBzB,MAAM,EAAC,iBAAiB;IAAAjB,QAAA,GAExB,IAAAhH,WAAA,CAAA4G,IAAA,EAACjH,YAAA,CAAAkH,IAAI;MAACC,KAAK,EAAElE,MAAM,CAAC+G,MAAO;MAAA3C,QAAA,GACzB,IAAAhH,WAAA,CAAAiH,GAAA,EAACtH,YAAA,CAAA2H,IAAI;QAACR,KAAK,EAAElE,MAAM,CAACgH,KAAM;QAAA5C,QAAA,EAAC;MAAQ,CAAM,CAAC,EAC1C,IAAAhH,WAAA,CAAAiH,GAAA,EAACtH,YAAA,CAAAoI,gBAAgB;QACfjB,KAAK,EAAElE,MAAM,CAACiH,gBAAiB;QAC/B5B,MAAM,EAAC,oBAAoB;QAC3BC,kBAAkB,EAAC,wBAAwB;QAC3C4B,iBAAiB,EAAC,QAAQ;QAAA9C,QAAA,EAC1B,IAAAhH,WAAA,CAAAiH,GAAA,EAAC3H,YAAA,CAAA4H,QAAQ;UAAC5F,IAAI,EAAC,gBAAgB;UAAC6F,IAAI,EAAE,EAAG;UAACC,KAAK,EAAEzE,MAAM,CAACoH;QAAQ,CAAE;MAAC,CACnD,CAAC;IAAA,CACf,CAAC,EAGNnG,OAAO,IAAIR,aAAa,CAAC1B,MAAM,KAAK,CAAC,GACpC,IAAA1B,WAAA,CAAA4G,IAAA,EAACjH,YAAA,CAAAkH,IAAI;MAACC,KAAK,EAAElE,MAAM,CAACoH,gBAAiB;MAAAhD,QAAA,GACnC,IAAAhH,WAAA,CAAAiH,GAAA,EAACtH,YAAA,CAAAsK,iBAAiB;QAAC9C,IAAI,EAAC,OAAO;QAACC,KAAK,EAAEzE,MAAM,CAACoH;MAAQ,CAAE,CAAC,EACzD,IAAA/J,WAAA,CAAAiH,GAAA,EAACtH,YAAA,CAAA2H,IAAI;QAACR,KAAK,EAAElE,MAAM,CAACsH,WAAY;QAAAlD,QAAA,EAAC;MAAwB,CAAM,CAAC;IAAA,CAC5D,CAAC,GAEP,IAAAhH,WAAA,CAAAiH,GAAA,EAACtH,YAAA,CAAAwK,QAAQ;MACPC,IAAI,EAAEhH,aAAc;MACpBiH,UAAU,EAAE5C,kBAAmB;MAC/B6C,YAAY,EAAE,SAAdA,YAAYA,CAAE3C,IAAI;QAAA,OAAIA,IAAI,CAAC7C,EAAE;MAAA,CAAC;MAC9BgC,KAAK,EAAElE,MAAM,CAAC2H,iBAAkB;MAChCC,qBAAqB,EAAE5H,MAAM,CAAC6H,oBAAqB;MACnDC,4BAA4B,EAAE,KAAM;MACpCC,cAAc,EACZ,IAAA3K,WAAA,CAAAiH,GAAA,EAACtH,YAAA,CAAAiL,cAAc;QACbpH,UAAU,EAAEA,UAAW;QACvBqH,SAAS,EAAE1E,aAAc;QACzBxD,MAAM,EAAE,CAACA,MAAM,CAACoH,OAAO,CAAE;QACzBe,SAAS,EAAEnI,MAAM,CAACoH;MAAQ,CAC3B,CACF;MACDgB,kBAAkB,EAAEpE;IAAiB,CACtC,CACF,EAEA3C,KAAK,IACJ,IAAAhE,WAAA,CAAAiH,GAAA,EAACtH,YAAA,CAAAkH,IAAI;MAACC,KAAK,EAAElE,MAAM,CAACoI,cAAe;MAAAhE,QAAA,EACjC,IAAAhH,WAAA,CAAAiH,GAAA,EAACtH,YAAA,CAAA2H,IAAI;QAACR,KAAK,EAAElE,MAAM,CAACqI,SAAU;QAAAjE,QAAA,EAAC;MAE/B,CAAM;IAAC,CACH,CACP;EAAA,CACa,CAAC;AAErB,CAAC;AAED,IAAMnE,YAAY,GAAG,SAAfA,YAAYA,CAAIF,MAAW;EAAA,OAC/BuI,uBAAU,CAACC,MAAM,CAAC;IAChBxB,MAAM,EAAE;MACNyB,aAAa,EAAE,KAAK;MACpBC,cAAc,EAAE,eAAe;MAC/BC,UAAU,EAAE,QAAQ;MACpBC,iBAAiB,EAAE,IAAAC,qCAAoB,EAAC,EAAE,CAAC;MAC3CC,eAAe,EAAE,IAAAD,qCAAoB,EAAC,EAAE,CAAC;MACzC9C,eAAe,EAAE/F,MAAM,CAAC2G,UAAU,CAACC,OAAO;MAC1CmC,iBAAiB,EAAE,CAAC;MACpBC,iBAAiB,EAAEhJ,MAAM,CAACiJ;IAC5B,CAAC;IACDhC,KAAK,EAAE;MACLiC,QAAQ,EAAE,IAAAC,sCAAqB,EAAC,EAAE,CAAC;MACnCC,UAAU,EAAE,KAAK;MACjB3E,KAAK,EAAEzE,MAAM,CAACqJ,IAAI,CAACzC;IACrB,CAAC;IACDM,gBAAgB,EAAE;MAChBoC,OAAO,EAAE,IAAAT,qCAAoB,EAAC,CAAC,CAAC;MAChCU,YAAY,EAAE,IAAAV,qCAAoB,EAAC,CAAC;IACtC,CAAC;IACDxB,gBAAgB,EAAE;MAChBmC,IAAI,EAAE,CAAC;MACPd,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBG,eAAe,EAAE,IAAAD,qCAAoB,EAAC,EAAE;IAC1C,CAAC;IACDtB,WAAW,EAAE;MACXkC,SAAS,EAAE,IAAAZ,qCAAoB,EAAC,EAAE,CAAC;MACnCK,QAAQ,EAAE,IAAAC,sCAAqB,EAAC,EAAE,CAAC;MACnC1E,KAAK,EAAEzE,MAAM,CAACqJ,IAAI,CAACK;IACrB,CAAC;IACD9B,iBAAiB,EAAE;MACjB4B,IAAI,EAAE;IACR,CAAC;IACD1B,oBAAoB,EAAE;MACpBgB,eAAe,EAAE,IAAAD,qCAAoB,EAAC,CAAC;IACzC,CAAC;IACDpD,gBAAgB,EAAE;MAChBM,eAAe,EAAE/F,MAAM,CAAC2G,UAAU,CAACC,OAAO;MAC1CgC,iBAAiB,EAAE,IAAAC,qCAAoB,EAAC,EAAE,CAAC;MAC3CC,eAAe,EAAE,IAAAD,qCAAoB,EAAC,EAAE,CAAC;MACzCE,iBAAiB,EAAE,CAAC;MACpBC,iBAAiB,EAAEhJ,MAAM,CAAC2J;IAC5B,CAAC;IACDjE,kBAAkB,EAAE;MAClBK,eAAe,EAAE/F,MAAM,CAAC4J;IAC1B,CAAC;IACDhE,mBAAmB,EAAE;MACnB6C,aAAa,EAAE,KAAK;MACpBE,UAAU,EAAE;IACd,CAAC;IACD9C,eAAe,EAAE;MACfgE,QAAQ,EAAE,UAAU;MACpBC,WAAW,EAAE,IAAAjB,qCAAoB,EAAC,EAAE;IACtC,CAAC;IACD/C,MAAM,EAAE;MACNiE,KAAK,EAAE,IAAAC,sCAAqB,EAAC,EAAE,CAAC;MAChCC,MAAM,EAAE,IAAAD,sCAAqB,EAAC,EAAE,CAAC;MACjCT,YAAY,EAAE,IAAAS,sCAAqB,EAAC,EAAE,CAAC;MACvCtB,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE;IACd,CAAC;IACD3C,UAAU,EAAE;MACVkD,QAAQ,EAAE,IAAAC,sCAAqB,EAAC,EAAE,CAAC;MACnCC,UAAU,EAAE,KAAK;MACjB3E,KAAK,EAAE;IACT,CAAC;IACD0B,eAAe,EAAE;MACf0D,QAAQ,EAAE,UAAU;MACpBK,MAAM,EAAE,CAAC;MACTC,KAAK,EAAE,CAAC;MACRJ,KAAK,EAAE,EAAE;MACTE,MAAM,EAAE,EAAE;MACVV,YAAY,EAAE,CAAC;MACfxD,eAAe,EAAE,SAAS;MAC1BqE,WAAW,EAAE,CAAC;MACdC,WAAW,EAAErK,MAAM,CAAC2G,UAAU,CAACC;IACjC,CAAC;IACDR,gBAAgB,EAAE;MAChBoD,IAAI,EAAE;IACR,CAAC;IACDnD,kBAAkB,EAAE;MAClBoC,aAAa,EAAE,KAAK;MACpBC,cAAc,EAAE,eAAe;MAC/BC,UAAU,EAAE,QAAQ;MACpB2B,YAAY,EAAE,IAAAzB,qCAAoB,EAAC,CAAC;IACtC,CAAC;IACDxG,eAAe,EAAE;MACf6G,QAAQ,EAAE,IAAAC,sCAAqB,EAAC,EAAE,CAAC;MACnCC,UAAU,EAAE,KAAK;MACjB3E,KAAK,EAAEzE,MAAM,CAACqJ,IAAI,CAACzC,OAAO;MAC1B4C,IAAI,EAAE,CAAC;MACPM,WAAW,EAAE,IAAAjB,qCAAoB,EAAC,CAAC;IACrC,CAAC;IACD5J,SAAS,EAAE;MACTiK,QAAQ,EAAE,IAAAC,sCAAqB,EAAC,EAAE,CAAC;MACnC1E,KAAK,EAAEzE,MAAM,CAACqJ,IAAI,CAACkB;IACrB,CAAC;IACD/D,oBAAoB,EAAE;MACpBiC,aAAa,EAAE,KAAK;MACpBE,UAAU,EAAE,QAAQ;MACpBD,cAAc,EAAE;IAClB,CAAC;IACD7F,WAAW,EAAE;MACXqG,QAAQ,EAAE,IAAAC,sCAAqB,EAAC,EAAE,CAAC;MACnC1E,KAAK,EAAEzE,MAAM,CAACqJ,IAAI,CAACK,SAAS;MAC5BF,IAAI,EAAE,CAAC;MACPM,WAAW,EAAE,IAAAjB,qCAAoB,EAAC,CAAC;IACrC,CAAC;IACDvC,UAAU,EAAE;MACV8C,UAAU,EAAE,KAAK;MACjB3E,KAAK,EAAEzE,MAAM,CAACqJ,IAAI,CAACzC;IACrB,CAAC;IACDH,WAAW,EAAE;MACXV,eAAe,EAAE/F,MAAM,CAACoH,OAAO;MAC/BmC,YAAY,EAAE,EAAE;MAChBiB,QAAQ,EAAE,EAAE;MACZP,MAAM,EAAE,EAAE;MACVvB,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,iBAAiB,EAAE;IACrB,CAAC;IACD1F,WAAW,EAAE;MACXgG,QAAQ,EAAE,IAAAC,sCAAqB,EAAC,EAAE,CAAC;MACnCC,UAAU,EAAE,KAAK;MACjB3E,KAAK,EAAE;IACT,CAAC;IACDL,UAAU,EAAE;MACVoF,IAAI,EAAE,CAAC;MACPd,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,iBAAiB,EAAE,IAAAC,qCAAoB,EAAC,EAAE,CAAC;MAC3CC,eAAe,EAAE,IAAAD,qCAAoB,EAAC,EAAE;IAC1C,CAAC;IACDjE,cAAc,EAAE;MACdsE,QAAQ,EAAE,IAAAC,sCAAqB,EAAC,EAAE,CAAC;MACnCC,UAAU,EAAE,KAAK;MACjB3E,KAAK,EAAEzE,MAAM,CAACqJ,IAAI,CAACzC,OAAO;MAC1B6C,SAAS,EAAE,IAAAZ,qCAAoB,EAAC,EAAE,CAAC;MACnCyB,YAAY,EAAE,IAAAzB,qCAAoB,EAAC,CAAC;IACtC,CAAC;IACDhE,iBAAiB,EAAE;MACjBqE,QAAQ,EAAE,IAAAC,sCAAqB,EAAC,EAAE,CAAC;MACnC1E,KAAK,EAAEzE,MAAM,CAACqJ,IAAI,CAACK,SAAS;MAC5Be,SAAS,EAAE,QAAQ;MACnBC,UAAU,EAAE;IACd,CAAC;IACDrC,cAAc,EAAE;MACdiB,OAAO,EAAE,IAAAT,qCAAoB,EAAC,EAAE,CAAC;MACjCF,UAAU,EAAE;IACd,CAAC;IACDL,SAAS,EAAE;MACTY,QAAQ,EAAE,IAAAC,sCAAqB,EAAC,EAAE,CAAC;MACnC1E,KAAK,EAAEzE,MAAM,CAACqB,KAAK;MACnBoJ,SAAS,EAAE;IACb;EACF,CAAC,CAAC;AAAA", "ignoreList": []}