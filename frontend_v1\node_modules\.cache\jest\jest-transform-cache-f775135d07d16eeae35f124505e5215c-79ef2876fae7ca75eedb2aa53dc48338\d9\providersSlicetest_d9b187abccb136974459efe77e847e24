d12ca642524dfda1fd999a4197e4f3f4
_getJestObj().mock("../../services/testAccountsService", function () {
  return {
    testAccountsService: {
      getAccountsByRole: jest.fn(),
      getProvidersByCategory: jest.fn()
    }
  };
});
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _reactNative = require("@testing-library/react-native");
var _testAccountsService = require("../../services/testAccountsService");
var _providersSlice = require("../providersSlice");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var mockTestAccountsService = _testAccountsService.testAccountsService;
var mockTestAccount = {
  id: '1',
  email: '<EMAIL>',
  password: 'TestPass123!',
  firstName: 'John',
  lastName: 'Doe',
  role: 'service_provider',
  category: 'Hair Services',
  city: 'Toronto',
  description: 'Professional hair stylist with 10 years experience'
};
var mockProvider = {
  id: 'provider_1',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  user: 'user_1',
  business_name: 'John Doe Hair Services',
  description: 'Professional hair stylist with 10 years experience',
  business_phone: '555-0123',
  business_email: '<EMAIL>',
  website: undefined,
  instagram_handle: undefined,
  address: '123 Main St',
  city: 'Toronto',
  state: 'ON',
  zip_code: '12345',
  latitude: 43.6532,
  longitude: -79.3832,
  cover_image: undefined,
  profile_image: undefined,
  rating: 4.5,
  review_count: 25,
  is_verified: true,
  is_featured: false,
  is_active: true,
  categories: ['Hair Services'],
  operating_hours: undefined,
  distance: '2.5 km',
  price_range: '$50-150'
};
describe('useProvidersStore', function () {
  beforeEach(function () {
    mockTestAccountsService.getAccountsByRole.mockClear();
    mockTestAccountsService.getProvidersByCategory.mockClear();
  });
  describe('initial state', function () {
    it('should have correct initial state', function () {
      var _renderHook = (0, _reactNative.renderHook)(function () {
          return (0, _providersSlice.useProvidersStore)();
        }),
        result = _renderHook.result;
      expect(result.current.providers).toEqual([]);
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBeNull();
      expect(result.current.filters).toEqual({});
    });
  });
  describe('fetchProviders', function () {
    it('should fetch providers successfully', (0, _asyncToGenerator2.default)(function* () {
      mockTestAccountsService.getAccountsByRole.mockReturnValue([mockTestAccount]);
      var _renderHook2 = (0, _reactNative.renderHook)(function () {
          return (0, _providersSlice.useProvidersStore)();
        }),
        result = _renderHook2.result;
      yield (0, _reactNative.act)((0, _asyncToGenerator2.default)(function* () {
        yield result.current.fetchProviders();
      }));
      expect(result.current.loading).toBe(false);
      expect(result.current.providers).toHaveLength(1);
      expect(result.current.providers[0].business_name).toBe('John Doe');
      expect(result.current.error).toBeNull();
    }));
    it('should handle loading error', (0, _asyncToGenerator2.default)(function* () {
      var _result$current$error;
      var errorMessage = 'Failed to load providers';
      mockTestAccountsService.getAccountsByRole.mockImplementation(function () {
        throw new Error(errorMessage);
      });
      var _renderHook3 = (0, _reactNative.renderHook)(function () {
          return (0, _providersSlice.useProvidersStore)();
        }),
        result = _renderHook3.result;
      yield (0, _reactNative.act)((0, _asyncToGenerator2.default)(function* () {
        yield result.current.fetchProviders();
      }));
      expect(result.current.loading).toBe(false);
      expect(result.current.providers).toEqual([]);
      expect((_result$current$error = result.current.error) == null ? void 0 : _result$current$error.message).toBe(errorMessage);
    }));
  });
  describe('updateFilters', function () {
    it('should update filters correctly', function () {
      var _renderHook4 = (0, _reactNative.renderHook)(function () {
          return (0, _providersSlice.useProvidersStore)();
        }),
        result = _renderHook4.result;
      var newFilters = {
        category: 'Hair Services',
        rating_min: 4.0,
        price_max: 100
      };
      (0, _reactNative.act)(function () {
        result.current.updateFilters(newFilters);
      });
      expect(result.current.filters).toEqual(newFilters);
    });
  });
  describe('clearFilters', function () {
    it('should clear all filters', function () {
      var _renderHook5 = (0, _reactNative.renderHook)(function () {
          return (0, _providersSlice.useProvidersStore)();
        }),
        result = _renderHook5.result;
      (0, _reactNative.act)(function () {
        result.current.updateFilters({
          category: 'Hair Services',
          rating_min: 4.0
        });
      });
      (0, _reactNative.act)(function () {
        result.current.clearFilters();
      });
      expect(result.current.filters).toEqual({});
    });
  });
  describe('searchProviders', function () {
    it('should search providers with filters', (0, _asyncToGenerator2.default)(function* () {
      mockTestAccountsService.getAccountsByRole.mockReturnValue([mockTestAccount]);
      mockTestAccountsService.getProvidersByCategory.mockReturnValue([mockTestAccount]);
      var _renderHook6 = (0, _reactNative.renderHook)(function () {
          return (0, _providersSlice.useProvidersStore)();
        }),
        result = _renderHook6.result;
      var filters = {
        category: 'Hair Services',
        rating_min: 4.0
      };
      yield (0, _reactNative.act)((0, _asyncToGenerator2.default)(function* () {
        yield result.current.searchProviders(filters);
      }));
      expect(result.current.loading).toBe(false);
      expect(result.current.filters).toEqual(filters);
      expect(result.current.providers).toHaveLength(1);
      expect(result.current.error).toBeNull();
    }));
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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