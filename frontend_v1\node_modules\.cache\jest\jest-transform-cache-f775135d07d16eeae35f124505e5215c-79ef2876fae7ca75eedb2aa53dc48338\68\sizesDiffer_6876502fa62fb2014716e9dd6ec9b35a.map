{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "dummySize", "width", "undefined", "height", "<PERSON><PERSON><PERSON><PERSON>", "one", "two", "defaultedOne", "defaultedTwo", "_default"], "sources": ["sizesDiffer.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\n'use strict';\n\nconst dummySize = {width: undefined, height: undefined};\ntype Size = {width: ?number, height: ?number};\n\nfunction sizesDiffer(one: Size, two: Size): boolean {\n  const defaultedOne = one || dummySize;\n  const defaultedTwo = two || dummySize;\n  return (\n    defaultedOne !== defaultedTwo &&\n    (defaultedOne.width !== defaultedTwo.width ||\n      defaultedOne.height !== defaultedTwo.height)\n  );\n}\n\nexport default sizesDiffer;\n"], "mappings": "AAUA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAEb,IAAMC,SAAS,GAAG;EAACC,KAAK,EAAEC,SAAS;EAAEC,MAAM,EAAED;AAAS,CAAC;AAGvD,SAASE,WAAWA,CAACC,GAAS,EAAEC,GAAS,EAAW;EAClD,IAAMC,YAAY,GAAGF,GAAG,IAAIL,SAAS;EACrC,IAAMQ,YAAY,GAAGF,GAAG,IAAIN,SAAS;EACrC,OACEO,YAAY,KAAKC,YAAY,KAC5BD,YAAY,CAACN,KAAK,KAAKO,YAAY,CAACP,KAAK,IACxCM,YAAY,CAACJ,MAAM,KAAKK,YAAY,CAACL,MAAM,CAAC;AAElD;AAAC,IAAAM,QAAA,GAAAZ,OAAA,CAAAE,OAAA,GAEcK,WAAW", "ignoreList": []}