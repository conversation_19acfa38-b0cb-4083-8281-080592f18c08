fe30d80ea9c12a4706a3172c32890d2e
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.withPerformanceTracking = exports.performanceMonitor = exports.measurePerformance = exports.PerformanceTimer = exports.PerformanceMonitor = exports.MemoryMonitor = exports.CriticalCSSOptimizer = exports.CodeSplittingUtils = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var PerformanceMonitor = exports.PerformanceMonitor = function () {
  function PerformanceMonitor() {
    (0, _classCallCheck2.default)(this, PerformanceMonitor);
    this.metrics = [];
    this.observers = new Map();
    this.slowRenderCallbacks = [];
    this.slowNetworkCallbacks = [];
    this.initializeObservers();
  }
  return (0, _createClass2.default)(PerformanceMonitor, [{
    key: "initializeObservers",
    value: function initializeObservers() {
      var _this = this;
      if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
        return;
      }
      try {
        var navObserver = new PerformanceObserver(function (list) {
          list.getEntries().forEach(function (entry) {
            if (entry.entryType === 'navigation') {
              _this.recordNavigationTiming(entry);
            }
          });
        });
        navObserver.observe({
          entryTypes: ['navigation']
        });
        this.observers.set('navigation', navObserver);
      } catch (error) {
        console.warn('Navigation timing observer not supported:', error);
      }
      try {
        var paintObserver = new PerformanceObserver(function (list) {
          list.getEntries().forEach(function (entry) {
            _this.recordMetric({
              name: entry.name,
              value: entry.startTime,
              timestamp: Date.now(),
              type: 'timing',
              tags: {
                type: 'paint'
              }
            });
          });
        });
        paintObserver.observe({
          entryTypes: ['paint']
        });
        this.observers.set('paint', paintObserver);
      } catch (error) {
        console.warn('Paint timing observer not supported:', error);
      }
      try {
        var lcpObserver = new PerformanceObserver(function (list) {
          var entries = list.getEntries();
          var lastEntry = entries[entries.length - 1];
          _this.recordMetric({
            name: 'largest-contentful-paint',
            value: lastEntry.startTime,
            timestamp: Date.now(),
            type: 'timing',
            tags: {
              type: 'lcp'
            }
          });
        });
        lcpObserver.observe({
          entryTypes: ['largest-contentful-paint']
        });
        this.observers.set('lcp', lcpObserver);
      } catch (error) {
        console.warn('LCP observer not supported:', error);
      }
      try {
        var fidObserver = new PerformanceObserver(function (list) {
          list.getEntries().forEach(function (entry) {
            _this.recordMetric({
              name: 'first-input-delay',
              value: entry.processingStart - entry.startTime,
              timestamp: Date.now(),
              type: 'timing',
              tags: {
                type: 'fid'
              }
            });
          });
        });
        fidObserver.observe({
          entryTypes: ['first-input']
        });
        this.observers.set('fid', fidObserver);
      } catch (error) {
        console.warn('FID observer not supported:', error);
      }
    }
  }, {
    key: "recordNavigationTiming",
    value: function recordNavigationTiming(entry) {
      var timing = {
        navigationStart: entry.navigationStart,
        domContentLoadedEventEnd: entry.domContentLoadedEventEnd,
        loadEventEnd: entry.loadEventEnd
      };
      var domContentLoaded = timing.domContentLoadedEventEnd - timing.navigationStart;
      var pageLoad = timing.loadEventEnd - timing.navigationStart;
      this.recordMetric({
        name: 'dom-content-loaded',
        value: domContentLoaded,
        timestamp: Date.now(),
        type: 'timing',
        tags: {
          type: 'navigation'
        }
      });
      this.recordMetric({
        name: 'page-load',
        value: pageLoad,
        timestamp: Date.now(),
        type: 'timing',
        tags: {
          type: 'navigation'
        }
      });
    }
  }, {
    key: "recordMetric",
    value: function recordMetric(metric) {
      this.metrics.push(metric);
      if (this.metrics.length > 1000) {
        this.metrics = this.metrics.slice(-1000);
      }
      if (__DEV__) {
        console.log(`[Performance] ${metric.name}: ${metric.value}ms`, metric.tags);
      }
    }
  }, {
    key: "getMetrics",
    value: function getMetrics(name) {
      if (name) {
        return this.metrics.filter(function (metric) {
          return metric.name === name;
        });
      }
      return (0, _toConsumableArray2.default)(this.metrics);
    }
  }, {
    key: "getSummary",
    value: function getSummary() {
      var summary = {};
      this.metrics.forEach(function (metric) {
        if (!summary[metric.name]) {
          summary[metric.name] = {
            avg: 0,
            min: Infinity,
            max: -Infinity,
            count: 0
          };
        }
        var stat = summary[metric.name];
        stat.count++;
        stat.min = Math.min(stat.min, metric.value);
        stat.max = Math.max(stat.max, metric.value);
        stat.avg = (stat.avg * (stat.count - 1) + metric.value) / stat.count;
      });
      return summary;
    }
  }, {
    key: "clearMetrics",
    value: function clearMetrics() {
      this.metrics = [];
    }
  }, {
    key: "dispose",
    value: function dispose() {
      this.observers.forEach(function (observer) {
        return observer.disconnect();
      });
      this.observers.clear();
      this.metrics = [];
    }
  }], [{
    key: "getInstance",
    value: function getInstance() {
      if (!PerformanceMonitor.instance) {
        PerformanceMonitor.instance = new PerformanceMonitor();
      }
      return PerformanceMonitor.instance;
    }
  }]);
}();
var PerformanceTimer = exports.PerformanceTimer = function () {
  function PerformanceTimer(name) {
    (0, _classCallCheck2.default)(this, PerformanceTimer);
    this.name = name;
    this.startTime = performance.now();
  }
  return (0, _createClass2.default)(PerformanceTimer, [{
    key: "end",
    value: function end(tags) {
      var duration = performance.now() - this.startTime;
      PerformanceMonitor.getInstance().recordMetric({
        name: this.name,
        value: duration,
        timestamp: Date.now(),
        type: 'timing',
        tags: tags
      });
      return duration;
    }
  }]);
}();
var CriticalCSSOptimizer = exports.CriticalCSSOptimizer = function () {
  function CriticalCSSOptimizer() {
    (0, _classCallCheck2.default)(this, CriticalCSSOptimizer);
  }
  return (0, _createClass2.default)(CriticalCSSOptimizer, null, [{
    key: "setCriticalCSS",
    value: function setCriticalCSS(css) {
      this.criticalCSS = css;
    }
  }, {
    key: "addNonCriticalCSS",
    value: function addNonCriticalCSS(cssUrl) {
      this.nonCriticalCSS.push(cssUrl);
      this.loadCSSAsync(cssUrl);
    }
  }, {
    key: "loadCSSAsync",
    value: function loadCSSAsync(cssUrl) {
      if (typeof document === 'undefined') return;
      var link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = cssUrl;
      link.media = 'print';
      link.onload = function () {
        link.media = 'all';
      };
      document.head.appendChild(link);
    }
  }, {
    key: "getCriticalCSS",
    value: function getCriticalCSS() {
      return this.criticalCSS;
    }
  }, {
    key: "preloadCriticalResources",
    value: function preloadCriticalResources(resources) {
      if (typeof document === 'undefined') return;
      resources.forEach(function (resource) {
        var link = document.createElement('link');
        link.rel = 'preload';
        link.href = resource.href;
        link.as = resource.as;
        if (resource.type) {
          link.type = resource.type;
        }
        document.head.appendChild(link);
      });
    }
  }]);
}();
CriticalCSSOptimizer.criticalCSS = '';
CriticalCSSOptimizer.nonCriticalCSS = [];
var CodeSplittingUtils = exports.CodeSplittingUtils = function () {
  function CodeSplittingUtils() {
    (0, _classCallCheck2.default)(this, CodeSplittingUtils);
  }
  return (0, _createClass2.default)(CodeSplittingUtils, null, [{
    key: "importModule",
    value: (function () {
      var _importModule = (0, _asyncToGenerator2.default)(function* (importFn, chunkName) {
        var timer = new PerformanceTimer(`chunk-load-${chunkName}`);
        try {
          var module = yield importFn();
          timer.end({
            chunk: chunkName,
            status: 'success'
          });
          this.loadedChunks.add(chunkName);
          return module;
        } catch (error) {
          timer.end({
            chunk: chunkName,
            status: 'error'
          });
          throw error;
        }
      });
      function importModule(_x, _x2) {
        return _importModule.apply(this, arguments);
      }
      return importModule;
    }())
  }, {
    key: "preloadChunk",
    value: function preloadChunk(importFn, chunkName) {
      var _this2 = this;
      if (this.loadedChunks.has(chunkName)) return;
      if ('requestIdleCallback' in window) {
        requestIdleCallback(function () {
          _this2.importModule(importFn, chunkName).catch(function () {});
        });
      } else {
        setTimeout(function () {
          _this2.importModule(importFn, chunkName).catch(function () {});
        }, 100);
      }
    }
  }, {
    key: "getLoadedChunks",
    value: function getLoadedChunks() {
      return Array.from(this.loadedChunks);
    }
  }]);
}();
CodeSplittingUtils.loadedChunks = new Set();
var MemoryMonitor = exports.MemoryMonitor = function () {
  function MemoryMonitor() {
    (0, _classCallCheck2.default)(this, MemoryMonitor);
  }
  return (0, _createClass2.default)(MemoryMonitor, [{
    key: "onSlowRender",
    value: function onSlowRender(callback) {
      this.slowRenderCallbacks.push(callback);
    }
  }, {
    key: "onSlowNetwork",
    value: function onSlowNetwork(callback) {
      this.slowNetworkCallbacks.push(callback);
    }
  }, {
    key: "offSlowRender",
    value: function offSlowRender(callback) {
      var index = this.slowRenderCallbacks.indexOf(callback);
      if (index > -1) {
        this.slowRenderCallbacks.splice(index, 1);
      }
    }
  }, {
    key: "offSlowNetwork",
    value: function offSlowNetwork(callback) {
      var index = this.slowNetworkCallbacks.indexOf(callback);
      if (index > -1) {
        this.slowNetworkCallbacks.splice(index, 1);
      }
    }
  }], [{
    key: "getMemoryUsage",
    value: function getMemoryUsage() {
      if (typeof window === 'undefined' || !('performance' in window) || !performance.memory) {
        return null;
      }
      var memory = performance.memory;
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        percentage: memory.usedJSHeapSize / memory.totalJSHeapSize * 100
      };
    }
  }, {
    key: "startMemoryMonitoring",
    value: function startMemoryMonitoring() {
      var _this3 = this;
      var threshold = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 80;
      if (typeof window === 'undefined') return;
      var checkMemory = function checkMemory() {
        var usage = _this3.getMemoryUsage();
        if (usage && usage.percentage > threshold) {
          console.warn(`High memory usage: ${usage.percentage.toFixed(1)}%`);
          PerformanceMonitor.getInstance().recordMetric({
            name: 'memory-usage-high',
            value: usage.percentage,
            timestamp: Date.now(),
            type: 'gauge',
            tags: {
              threshold: threshold.toString()
            }
          });
        }
      };
      setInterval(checkMemory, 30000);
    }
  }]);
}();
var performanceMonitor = exports.performanceMonitor = PerformanceMonitor.getInstance();
var measurePerformance = exports.measurePerformance = function measurePerformance(name) {
  return new PerformanceTimer(name);
};
var withPerformanceTracking = exports.withPerformanceTracking = function withPerformanceTracking(fn, name) {
  return function () {
    var timer = new PerformanceTimer(name);
    try {
      var result = fn.apply(void 0, arguments);
      if (result && typeof result.then === 'function') {
        return result.finally(function () {
          return timer.end();
        });
      }
      timer.end();
      return result;
    } catch (error) {
      timer.end({
        status: 'error'
      });
      throw error;
    }
  };
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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