9c15b8ffea6df2728446ce4ea4d07e4e
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.__INTERNAL_VIEW_CONFIG = exports.Commands = void 0;
var NativeComponentRegistry = _interopRequireWildcard(require("../../NativeComponent/NativeComponentRegistry"));
var _codegenNativeCommands = _interopRequireDefault(require("../../Utilities/codegenNativeCommands"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var Commands = exports.Commands = (0, _codegenNativeCommands.default)({
  supportedCommands: ['focus', 'blur', 'setTextAndSelection']
});
var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {
  uiViewClassName: 'AndroidTextInput',
  bubblingEventTypes: {
    topBlur: {
      phasedRegistrationNames: {
        bubbled: 'onBlur',
        captured: 'onBlurCapture'
      }
    },
    topEndEditing: {
      phasedRegistrationNames: {
        bubbled: 'onEndEditing',
        captured: 'onEndEditingCapture'
      }
    },
    topFocus: {
      phasedRegistrationNames: {
        bubbled: 'onFocus',
        captured: 'onFocusCapture'
      }
    },
    topKeyPress: {
      phasedRegistrationNames: {
        bubbled: 'onKeyPress',
        captured: 'onKeyPressCapture'
      }
    },
    topSubmitEditing: {
      phasedRegistrationNames: {
        bubbled: 'onSubmitEditing',
        captured: 'onSubmitEditingCapture'
      }
    }
  },
  directEventTypes: {
    topScroll: {
      registrationName: 'onScroll'
    }
  },
  validAttributes: {
    maxFontSizeMultiplier: true,
    adjustsFontSizeToFit: true,
    minimumFontScale: true,
    autoFocus: true,
    placeholder: true,
    inlineImagePadding: true,
    contextMenuHidden: true,
    textShadowColor: {
      process: require("../../StyleSheet/processColor").default
    },
    maxLength: true,
    selectTextOnFocus: true,
    textShadowRadius: true,
    underlineColorAndroid: {
      process: require("../../StyleSheet/processColor").default
    },
    textDecorationLine: true,
    submitBehavior: true,
    textAlignVertical: true,
    fontStyle: true,
    textShadowOffset: true,
    selectionColor: {
      process: require("../../StyleSheet/processColor").default
    },
    selectionHandleColor: {
      process: require("../../StyleSheet/processColor").default
    },
    placeholderTextColor: {
      process: require("../../StyleSheet/processColor").default
    },
    importantForAutofill: true,
    lineHeight: true,
    textTransform: true,
    returnKeyType: true,
    keyboardType: true,
    multiline: true,
    color: {
      process: require("../../StyleSheet/processColor").default
    },
    autoComplete: true,
    numberOfLines: true,
    letterSpacing: true,
    returnKeyLabel: true,
    fontSize: true,
    onKeyPress: true,
    cursorColor: {
      process: require("../../StyleSheet/processColor").default
    },
    text: true,
    showSoftInputOnFocus: true,
    textAlign: true,
    autoCapitalize: true,
    autoCorrect: true,
    caretHidden: true,
    secureTextEntry: true,
    textBreakStrategy: true,
    onScroll: true,
    onContentSizeChange: true,
    disableFullscreenUI: true,
    includeFontPadding: true,
    fontWeight: true,
    fontFamily: true,
    allowFontScaling: true,
    onSelectionChange: true,
    mostRecentEventCount: true,
    inlineImageLeft: true,
    editable: true,
    fontVariant: true,
    borderBottomRightRadius: true,
    borderBottomColor: {
      process: require("../../StyleSheet/processColor").default
    },
    borderRadius: true,
    borderRightColor: {
      process: require("../../StyleSheet/processColor").default
    },
    borderColor: {
      process: require("../../StyleSheet/processColor").default
    },
    borderTopRightRadius: true,
    borderStyle: true,
    borderBottomLeftRadius: true,
    borderLeftColor: {
      process: require("../../StyleSheet/processColor").default
    },
    borderTopLeftRadius: true,
    borderTopColor: {
      process: require("../../StyleSheet/processColor").default
    }
  }
};
var AndroidTextInputNativeComponent = NativeComponentRegistry.get('AndroidTextInput', function () {
  return __INTERNAL_VIEW_CONFIG;
});
var _default = exports.default = AndroidTextInputNativeComponent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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