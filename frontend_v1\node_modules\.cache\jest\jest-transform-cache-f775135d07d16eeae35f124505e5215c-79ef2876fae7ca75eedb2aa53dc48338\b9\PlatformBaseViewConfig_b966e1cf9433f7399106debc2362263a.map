{"version": 3, "names": ["_BaseViewConfig", "_interopRequireDefault", "require", "PlatformBaseViewConfig", "BaseViewConfig", "_default", "exports", "default"], "sources": ["PlatformBaseViewConfig.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\nimport type {PartialViewConfig} from '../Renderer/shims/ReactNativeTypes';\n\nimport BaseViewConfig from './BaseViewConfig';\n\nexport type PartialViewConfigWithoutName = $Rest<\n  PartialViewConfig,\n  {uiViewClassName: string},\n>;\n\nconst PlatformBaseViewConfig: PartialViewConfigWithoutName = BaseViewConfig;\n\n// In Wilde/FB4A, use RNHostComponentListRoute in Bridge mode to verify\n// whether the JS props defined here match the native props defined\n// in RCTViewManagers in iOS, and ViewManagers in Android.\nexport default PlatformBaseViewConfig;\n"], "mappings": ";;;;;AAYA,IAAAA,eAAA,GAAAC,sBAAA,CAAAC,OAAA;AAOA,IAAMC,sBAAoD,GAAGC,uBAAc;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAK7DJ,sBAAsB", "ignoreList": []}