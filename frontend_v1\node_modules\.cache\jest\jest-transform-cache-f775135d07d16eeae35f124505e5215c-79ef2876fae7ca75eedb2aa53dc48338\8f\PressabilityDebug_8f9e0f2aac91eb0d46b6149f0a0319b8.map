{"version": 3, "names": ["_View", "_interopRequireDefault", "require", "_normalizeColor", "_Rect", "React", "_interopRequireWildcard", "_jsxRuntime", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "PressabilityDebugView", "props", "__DEV__", "isEnabled", "_hitSlop$bottom", "_hitSlop$left", "_hitSlop$right", "_hitSlop$top", "normalizedColor", "normalizeColor", "color", "baseColor", "toString", "padStart", "hitSlop", "normalizeRect", "jsx", "pointerEvents", "style", "backgroundColor", "slice", "borderColor", "borderStyle", "borderWidth", "bottom", "left", "position", "right", "top", "isDebugEnabled", "setEnabled", "value"], "sources": ["PressabilityDebug.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {ColorValue} from '../StyleSheet/StyleSheet';\n\nimport View from '../Components/View/View';\nimport normalizeColor from '../StyleSheet/normalizeColor';\nimport {type RectOrSize, normalizeRect} from '../StyleSheet/Rect';\nimport * as React from 'react';\n\ntype Props = $ReadOnly<{\n  color: ColorValue,\n  hitSlop: ?RectOrSize,\n}>;\n\n/**\n * Displays a debug overlay to visualize press targets when enabled via the\n * React Native Inspector. Calls to this module should be guarded by `__DEV__`,\n * for example:\n *\n *   return (\n *     <View>\n *       {children}\n *       {__DEV__ ? (\n *         <PressabilityDebugView color=\"...\" hitSlop={props.hitSlop} />\n *       ) : null}\n *     </View>\n *   );\n *\n */\nexport function PressabilityDebugView(props: Props): React.Node {\n  if (__DEV__) {\n    if (isEnabled()) {\n      const normalizedColor = normalizeColor(props.color);\n      if (typeof normalizedColor !== 'number') {\n        return null;\n      }\n      const baseColor =\n        '#' + (normalizedColor ?? 0).toString(16).padStart(8, '0');\n      const hitSlop = normalizeRect(props.hitSlop);\n      return (\n        <View\n          pointerEvents=\"none\"\n          style={\n            // eslint-disable-next-line react-native/no-inline-styles\n            {\n              backgroundColor: baseColor.slice(0, -2) + '0F', // 15%\n              borderColor: baseColor.slice(0, -2) + '55', // 85%\n              borderStyle: 'dashed',\n              borderWidth: 1,\n              bottom: -(hitSlop?.bottom ?? 0),\n              left: -(hitSlop?.left ?? 0),\n              position: 'absolute',\n              right: -(hitSlop?.right ?? 0),\n              top: -(hitSlop?.top ?? 0),\n            }\n          }\n        />\n      );\n    }\n  }\n  return null;\n}\n\nlet isDebugEnabled = false;\n\nexport function isEnabled(): boolean {\n  if (__DEV__) {\n    return isDebugEnabled;\n  }\n  return false;\n}\n\nexport function setEnabled(value: boolean): void {\n  if (__DEV__) {\n    isDebugEnabled = value;\n  }\n}\n"], "mappings": ";;;;;;;AAYA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,eAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAC,uBAAA,CAAAJ,OAAA;AAA+B,IAAAK,WAAA,GAAAL,OAAA;AAAA,SAAAI,wBAAAE,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAJ,uBAAA,YAAAA,wBAAAE,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAsBxB,SAASmB,qBAAqBA,CAACC,KAAY,EAAc;EAC9D,IAAIC,OAAO,EAAE;IACX,IAAIC,SAAS,CAAC,CAAC,EAAE;MAAA,IAAAC,eAAA,EAAAC,aAAA,EAAAC,cAAA,EAAAC,YAAA;MACf,IAAMC,eAAe,GAAG,IAAAC,uBAAc,EAACR,KAAK,CAACS,KAAK,CAAC;MACnD,IAAI,OAAOF,eAAe,KAAK,QAAQ,EAAE;QACvC,OAAO,IAAI;MACb;MACA,IAAMG,SAAS,GACb,GAAG,GAAG,CAACH,eAAe,WAAfA,eAAe,GAAI,CAAC,EAAEI,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC5D,IAAMC,OAAO,GAAG,IAAAC,mBAAa,EAACd,KAAK,CAACa,OAAO,CAAC;MAC5C,OACE,IAAAnC,WAAA,CAAAqC,GAAA,EAAC5C,KAAA,CAAAkB,OAAI;QACH2B,aAAa,EAAC,MAAM;QACpBC,KAAK,EAEH;UACEC,eAAe,EAAER,SAAS,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI;UAC9CC,WAAW,EAAEV,SAAS,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI;UAC1CE,WAAW,EAAE,QAAQ;UACrBC,WAAW,EAAE,CAAC;UACdC,MAAM,EAAE,GAAApB,eAAA,GAAEU,OAAO,oBAAPA,OAAO,CAAEU,MAAM,YAAApB,eAAA,GAAI,CAAC,CAAC;UAC/BqB,IAAI,EAAE,GAAApB,aAAA,GAAES,OAAO,oBAAPA,OAAO,CAAEW,IAAI,YAAApB,aAAA,GAAI,CAAC,CAAC;UAC3BqB,QAAQ,EAAE,UAAU;UACpBC,KAAK,EAAE,GAAArB,cAAA,GAAEQ,OAAO,oBAAPA,OAAO,CAAEa,KAAK,YAAArB,cAAA,GAAI,CAAC,CAAC;UAC7BsB,GAAG,EAAE,GAAArB,YAAA,GAAEO,OAAO,oBAAPA,OAAO,CAAEc,GAAG,YAAArB,YAAA,GAAI,CAAC;QAC1B;MACD,CACF,CAAC;IAEN;EACF;EACA,OAAO,IAAI;AACb;AAEA,IAAIsB,cAAc,GAAG,KAAK;AAEnB,SAAS1B,SAASA,CAAA,EAAY;EACnC,IAAID,OAAO,EAAE;IACX,OAAO2B,cAAc;EACvB;EACA,OAAO,KAAK;AACd;AAEO,SAASC,UAAUA,CAACC,KAAc,EAAQ;EAC/C,IAAI7B,OAAO,EAAE;IACX2B,cAAc,GAAGE,KAAK;EACxB;AACF", "ignoreList": []}