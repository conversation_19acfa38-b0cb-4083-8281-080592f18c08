3cfc361caad859a158d5dbe0e6e60237
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _AndroidTextInputNativeComponent = require("../../Components/TextInput/AndroidTextInputNativeComponent");
var _RCTSingelineTextInputNativeComponent = require("../../Components/TextInput/RCTSingelineTextInputNativeComponent");
var _require = require("../../ReactNative/RendererProxy"),
  findNodeHandle = _require.findNodeHandle;
var Platform = require("../../Utilities/Platform").default;
var currentlyFocusedInputRef = null;
var inputs = new Set();
function currentlyFocusedInput() {
  return currentlyFocusedInputRef;
}
function currentlyFocusedField() {
  if (__DEV__) {
    console.error('currentlyFocusedField is deprecated and will be removed in a future release. Use currentlyFocusedInput');
  }
  return findNodeHandle(currentlyFocusedInputRef);
}
function focusInput(textField) {
  if (currentlyFocusedInputRef !== textField && textField != null) {
    currentlyFocusedInputRef = textField;
  }
}
function blurInput(textField) {
  if (currentlyFocusedInputRef === textField && textField != null) {
    currentlyFocusedInputRef = null;
  }
}
function focusField(textFieldID) {
  if (__DEV__) {
    console.error('focusField no longer works. Use focusInput');
  }
  return;
}
function blurField(textFieldID) {
  if (__DEV__) {
    console.error('blurField no longer works. Use blurInput');
  }
  return;
}
function focusTextInput(textField) {
  if (typeof textField === 'number') {
    if (__DEV__) {
      console.error('focusTextInput must be called with a host component. Passing a react tag is deprecated.');
    }
    return;
  }
  if (textField != null) {
    var _textField$currentPro;
    var fieldCanBeFocused = currentlyFocusedInputRef !== textField && ((_textField$currentPro = textField.currentProps) == null ? void 0 : _textField$currentPro.editable) !== false;
    if (!fieldCanBeFocused) {
      return;
    }
    focusInput(textField);
    if (Platform.OS === 'ios') {
      _RCTSingelineTextInputNativeComponent.Commands.focus(textField);
    } else if (Platform.OS === 'android') {
      _AndroidTextInputNativeComponent.Commands.focus(textField);
    }
  }
}
function blurTextInput(textField) {
  if (typeof textField === 'number') {
    if (__DEV__) {
      console.error('blurTextInput must be called with a host component. Passing a react tag is deprecated.');
    }
    return;
  }
  if (currentlyFocusedInputRef === textField && textField != null) {
    blurInput(textField);
    if (Platform.OS === 'ios') {
      _RCTSingelineTextInputNativeComponent.Commands.blur(textField);
    } else if (Platform.OS === 'android') {
      _AndroidTextInputNativeComponent.Commands.blur(textField);
    }
  }
}
function registerInput(textField) {
  if (typeof textField === 'number') {
    if (__DEV__) {
      console.error('registerInput must be called with a host component. Passing a react tag is deprecated.');
    }
    return;
  }
  inputs.add(textField);
}
function unregisterInput(textField) {
  if (typeof textField === 'number') {
    if (__DEV__) {
      console.error('unregisterInput must be called with a host component. Passing a react tag is deprecated.');
    }
    return;
  }
  inputs.delete(textField);
}
function isTextInput(textField) {
  if (typeof textField === 'number') {
    if (__DEV__) {
      console.error('isTextInput must be called with a host component. Passing a react tag is deprecated.');
    }
    return false;
  }
  return inputs.has(textField);
}
var TextInputState = {
  currentlyFocusedInput: currentlyFocusedInput,
  focusInput: focusInput,
  blurInput: blurInput,
  currentlyFocusedField: currentlyFocusedField,
  focusField: focusField,
  blurField: blurField,
  focusTextInput: focusTextInput,
  blurTextInput: blurTextInput,
  registerInput: registerInput,
  unregisterInput: unregisterInput,
  isTextInput: isTextInput
};
var _default = exports.default = TextInputState;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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