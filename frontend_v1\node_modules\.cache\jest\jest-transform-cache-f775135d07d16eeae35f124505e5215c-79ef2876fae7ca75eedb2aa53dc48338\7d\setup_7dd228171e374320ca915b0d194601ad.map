{"version": 3, "names": ["require", "after<PERSON>ach", "jest", "clearAllMocks", "describe", "it", "expect", "toBeDefined"], "sources": ["setup.ts"], "sourcesContent": ["/**\n * Jest Test Setup\n *\n * Global test setup and configuration for the Vierla Frontend v1 test suite.\n * Includes mocks, polyfills, and test utilities.\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport '@testing-library/jest-native/extend-expect';\n\n// Cleanup after each test\nafterEach(() => {\n  jest.clearAllMocks();\n});\n\n// Simple test to prevent \"no tests\" error\ndescribe('Test Setup', () => {\n  it('should configure test environment', () => {\n    expect(jest).toBeDefined();\n    expect(afterEach).toBeDefined();\n  });\n});\n\nexport {};\n"], "mappings": ";;;AAUAA,OAAA;AAGAC,SAAS,CAAC,YAAM;EACdC,IAAI,CAACC,aAAa,CAAC,CAAC;AACtB,CAAC,CAAC;AAGFC,QAAQ,CAAC,YAAY,EAAE,YAAM;EAC3BC,EAAE,CAAC,mCAAmC,EAAE,YAAM;IAC5CC,MAAM,CAACJ,IAAI,CAAC,CAACK,WAAW,CAAC,CAAC;IAC1BD,MAAM,CAACL,SAAS,CAAC,CAACM,WAAW,CAAC,CAAC;EACjC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}