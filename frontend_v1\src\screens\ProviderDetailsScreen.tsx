/**
 * Provider Details Screen - Comprehensive Provider Information Display
 *
 * Component Contract:
 * - Displays detailed provider information including services, contact info, and reviews
 * - Supports booking services and contacting providers
 * - Implements modern UI/UX with responsive design
 * - Follows accessibility guidelines and testing standards
 * - Integrates with navigation and state management
 *
 * @version 2.0.0 - Rebuilt for Vierla Frontend v2 based on frontend_v0
 * <AUTHOR> Development Team
 */

import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  Linking,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';

// Atomic Design Components
import { Box } from '../components/atoms/Box';
import { Button } from '../components/atoms/Button';
import { IconButton } from '../components/atoms/IconButton';
// Error Handling
import {
  DataLoadingFallback as ErrorBoundary,
  DataLoadingFallback,
} from '../components/error/DataLoadingFallback';
import { unifiedErrorHandlingService } from '../services/unifiedErrorHandling';

// UI Components
import { SafeAreaWrapper } from '../components/ui/SafeAreaWrapper';

// Theme and Navigation
import { useTheme } from '../contexts/ThemeContext';
import type { CustomerStackParamList } from '../navigation/types';

// Services
import { bookingService } from '../services/bookingService';
import { messagingService } from '../services/messagingService';

// Utils
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../utils/responsive';

// Types
interface ServiceProvider {
  id: string;
  business_name: string;
  description: string;
  avatar?: string;
  rating: number;
  review_count: number;
  city: string;
  state: string;
  distance: string;
  address: string;
  business_phone: string;
  business_email: string;
  is_verified: boolean;
  categories: string[];
}

interface Service {
  id: string;
  name: string;
  description: string;
  price: number;
  duration: number;
  category: string;
}

interface Review {
  id: string;
  customer_name: string;
  rating: number;
  comment: string;
  created_at: string;
}

// Navigation Types
type ProviderDetailsScreenNavigationProp = StackNavigationProp<
  CustomerStackParamList,
  'ProviderDetails'
>;

type ProviderDetailsScreenRouteProp = RouteProp<
  CustomerStackParamList,
  'ProviderDetails'
>;

// Services
import providerService, {
  Provider,
  ProviderService as ProviderServiceType,
  ProviderReview,
} from '../services/providerService';

// Helper function to render star ratings
const renderStars = (rating: number | null | undefined) => {
  const stars = [];
  const safeRating = rating || 0;
  const fullStars = Math.floor(safeRating);
  const hasHalfStar = safeRating % 1 !== 0;

  for (let i = 0; i < fullStars; i++) {
    stars.push(
      <Text key={i} style={{ fontSize: 18, color: '#FFD700' }}>
        ★
      </Text>,
    );
  }

  if (hasHalfStar) {
    stars.push(
      <Text key="half" style={{ fontSize: 18, color: '#FFD700' }}>
        ☆
      </Text>,
    );
  }

  const emptyStars = 5 - Math.ceil(rating);
  for (let i = 0; i < emptyStars; i++) {
    stars.push(
      <Text key={`empty-${i}`} style={{ fontSize: 18, color: '#E0E0E0' }}>
        ☆
      </Text>,
    );
  }

  return stars;
};

export const ProviderDetailsScreen: React.FC = () => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const navigation = useNavigation<ProviderDetailsScreenNavigationProp>();
  const route = useRoute<ProviderDetailsScreenRouteProp>();
  const { providerId } = route.params;

  // State management - simplified frontend_v0 style
  const [provider, setProvider] = useState<ServiceProvider | null>(null);
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [isFavorite, setIsFavorite] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  useEffect(() => {
    console.log('🔍 useEffect called with providerId:', providerId);
    loadProviderDetails();
  }, [providerId]);

  const loadProviderDetails = async () => {
    try {
      setLoading(true);

      // Load provider details from backend
      const response = await fetch(
        `http://************:8000/api/catalog/providers/${providerId}/`,
      );
      if (!response.ok) {
        throw new Error('Failed to load provider details');
      }

      const providerData = await response.json();

      // Transform backend data to match our interface
      const transformedProvider: ServiceProvider = {
        id: providerData.id,
        business_name: providerData.business_name,
        description:
          providerData.description || 'Professional service provider',
        avatar: providerData.avatar,
        rating: typeof providerData.rating === 'string'
          ? parseFloat(providerData.rating) || 4.5
          : providerData.rating || 4.5,
        review_count: providerData.review_count || 0,
        city: providerData.city || 'Toronto',
        state: providerData.state || 'ON',
        distance: providerData.distance || '2.5 km',
        address: providerData.address || 'Address not available',
        business_phone: providerData.business_phone || 'Phone not available',
        business_email: providerData.business_email || 'Email not available',
        is_verified: providerData.is_verified || false,
        categories: providerData.categories || [],
      };

      setProvider(transformedProvider);

      // Load services
      const servicesResponse = await fetch(
        `http://************:8000/api/catalog/providers/${providerId}/services/`,
      );
      if (servicesResponse.ok) {
        const servicesData = await servicesResponse.json();

        // Handle paginated response - extract results array
        const servicesArray = servicesData.results || servicesData;

        // Ensure we have an array before calling map
        if (Array.isArray(servicesArray)) {
          const transformedServices: Service[] = servicesArray.map(
            (service: any) => ({
              id: service.id,
              name: service.name,
              description: service.description || 'Professional service',
              price: service.base_price || service.price || 50,
              duration: service.duration || 60,
              category: service.category?.name || service.category || 'General',
            }),
          );
          setServices(transformedServices);
        } else {
          console.warn('Services data is not an array:', servicesArray);
          setServices([]);
        }
      }
    } catch (error) {
      console.error('Failed to load provider details:', error);

      // Use unified error handling
      await unifiedErrorHandlingService.handleNetworkError(error as Error, {
        action: 'load_provider_details',
        additionalData: { providerId }
      });

      Alert.alert(
        'Error',
        'Failed to load provider details. Please try again.',
      );
    } finally {
      setLoading(false);
    }
  };

  // Handler functions following frontend_v0 pattern
  const handleCallProvider = () => {
    if (provider?.business_phone) {
      Linking.openURL(`tel:${provider.business_phone}`);
    } else {
      Alert.alert(
        'Phone Not Available',
        'Phone number is not available for this provider.',
      );
    }
  };

  const handleEmailProvider = () => {
    if (provider?.business_email) {
      Linking.openURL(`mailto:${provider.business_email}`);
    } else {
      Alert.alert(
        'Email Not Available',
        'Email address is not available for this provider.',
      );
    }
  };

  const handleToggleFavorite = () => {
    setIsFavorite(!isFavorite);
    // TODO: Implement favorites API call
  };

  const handleBookService = (service: Service) => {
    console.log('Booking service:', service);
    navigation.navigate('ServiceDetails', {
      serviceId: service.id,
      providerId: provider?.id || '',
    });
  };

  // Filter services by category
  const filteredServices =
    selectedCategory === 'all'
      ? services
      : services.filter(service => service.category === selectedCategory);

  // Get unique categories
  const categories = [
    'all',
    ...Array.from(new Set(services.map(service => service.category))),
  ];

  if (loading) {
    return (
      <SafeAreaWrapper>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.sage600} />
          <Text style={styles.loadingText}>Loading provider details...</Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  if (!provider) {
    return (
      <SafeAreaWrapper>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Provider not found</Text>
          <Button
            title="Go Back"
            onPress={() => navigation.goBack()}
            variant="primary"
          />
        </View>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      <View style={styles.container}>
        {/* Header with back button and favorite */}
        <View style={styles.header}>
          <IconButton
            icon="arrow-back"
            onPress={() => navigation.goBack()}
            style={styles.backButton}
            testID="back-button"
            accessibilityLabel="Go back"
          />
          <IconButton
            icon={isFavorite ? 'heart' : 'heart-outline'}
            onPress={handleToggleFavorite}
            style={styles.favoriteButton}
            testID="favorite-button"
            accessibilityLabel={
              isFavorite ? 'Remove from favorites' : 'Add to favorites'
            }
          />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Provider Header */}
          <Box style={styles.providerHeader}>
            <View style={styles.providerImageContainer}>
              {provider.avatar ? (
                <Image
                  source={{ uri: provider.avatar }}
                  style={styles.providerImage}
                />
              ) : (
                <View style={styles.placeholderImage}>
                  <Text style={styles.placeholderText}>
                    {provider.business_name.charAt(0)}
                  </Text>
                </View>
              )}
              {provider.is_verified && (
                <View style={styles.verifiedBadge}>
                  <Text style={styles.verifiedText}>✓</Text>
                </View>
              )}
            </View>

            <View style={styles.providerInfo}>
              <Text style={styles.businessName} testID="business-name">
                {provider.business_name}
              </Text>
              <Text style={styles.description} testID="description">
                {provider.description}
              </Text>

              <View style={styles.ratingContainer}>
                <View style={styles.stars}>{renderStars(provider.rating)}</View>
                <Text style={styles.ratingText} testID="rating">
                  {provider.rating ? provider.rating.toFixed(1) : '0.0'} ({provider.review_count} reviews)
                </Text>
              </View>

              <Text style={styles.location} testID="location">
                📍 {provider.city}, {provider.state} • {provider.distance}
              </Text>
            </View>
          </Box>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <Button
              title="Call"
              onPress={handleCallProvider}
              variant="outline"
              style={styles.actionButton}
              testID="call-button"
              accessibilityLabel={`Call ${provider.business_name}`}
            />
            <Button
              title="Email"
              onPress={handleEmailProvider}
              variant="outline"
              style={styles.actionButton}
              testID="email-button"
              accessibilityLabel={`Email ${provider.business_name}`}
            />
          </View>

          {/* Contact Information */}
          <Box style={styles.section}>
            <Text style={styles.sectionTitle}>Contact Information</Text>

            <View style={styles.contactItem}>
              <Text style={styles.contactIcon}>📍</Text>
              <Text style={styles.contactText} testID="address">
                {provider.address}
              </Text>
            </View>

            <View style={styles.contactItem}>
              <Text style={styles.contactIcon}>📞</Text>
              <Text style={styles.contactText} testID="phone">
                {provider.business_phone}
              </Text>
            </View>

            <View style={styles.contactItem}>
              <Text style={styles.contactIcon}>✉️</Text>
              <Text style={styles.contactText} testID="email">
                {provider.business_email}
              </Text>
            </View>
          </Box>

          {/* Services Section */}
          <Box style={styles.section}>
            <Text style={styles.sectionTitle}>Services</Text>

            {/* Category Filter */}
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.categoryFilter}
              contentContainerStyle={styles.categoryFilterContent}>
              {categories.map(category => (
                <TouchableOpacity
                  key={category}
                  style={[
                    styles.categoryButton,
                    selectedCategory === category &&
                      styles.categoryButtonActive,
                  ]}
                  onPress={() => setSelectedCategory(category)}
                  testID={`category-${category}`}>
                  <Text
                    style={[
                      styles.categoryButtonText,
                      selectedCategory === category &&
                        styles.categoryButtonTextActive,
                    ]}>
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>

            {/* Services List */}
            {filteredServices.map(service => (
              <View
                key={service.id}
                style={styles.serviceCard}
                testID={`service-${service.id}`}>
                <View style={styles.serviceInfo}>
                  <Text style={styles.serviceName}>{service.name}</Text>
                  <Text style={styles.serviceDescription}>
                    {service.description}
                  </Text>
                  <View style={styles.serviceDetails}>
                    <Text style={styles.servicePrice}>${service.price}</Text>
                    <Text style={styles.serviceDuration}>
                      {service.duration} min
                    </Text>
                  </View>
                </View>
                <Button
                  title="Book"
                  onPress={() => handleBookService(service)}
                  variant="primary"
                  style={styles.bookButton}
                  testID={`book-service-${service.id}`}
                  accessibilityLabel={`Book ${service.name}`}
                />
              </View>
            ))}
          </Box>
        </ScrollView>
      </View>
    </SafeAreaWrapper>
  );
};

// Default export
export default ProviderDetailsScreen;

// Styles
const createStyles = (colors: any) => ({
  container: {
    flex: 1,
    backgroundColor: colors?.background?.primary || '#FFFFFF',
  },
  header: {
    position: 'absolute',
    top: 50,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    zIndex: 10,
  },
  backButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  favoriteButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    paddingTop: 100,
  },
  providerHeader: {
    padding: 20,
    alignItems: 'center',
  },
  providerImageContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  providerImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#F0F0F0',
  },
  placeholderImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#E0E0E0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#666',
  },
  verifiedBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#4CAF50',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  verifiedText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  providerInfo: {
    alignItems: 'center',
  },
  businessName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors?.text?.primary || '#000000',
    textAlign: 'center',
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    color: colors?.text?.secondary || '#666666',
    textAlign: 'center',
    marginBottom: 12,
    paddingHorizontal: 20,
  },
  ratingContainer: {
    alignItems: 'center',
    marginBottom: 8,
  },
  stars: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  ratingText: {
    fontSize: 14,
    color: colors?.text?.secondary || '#666666',
  },
  location: {
    fontSize: 14,
    color: colors?.text?.secondary || '#666666',
    textAlign: 'center',
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  section: {
    margin: 16,
    padding: 16,
    backgroundColor: colors?.background?.secondary || '#F8F8F8',
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors?.text?.primary || '#000000',
    marginBottom: 16,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  contactIcon: {
    fontSize: 16,
    marginRight: 12,
    width: 20,
  },
  contactText: {
    fontSize: 16,
    color: colors?.text?.primary || '#000000',
    flex: 1,
  },
  categoryFilter: {
    marginBottom: 16,
  },
  categoryFilterContent: {
    paddingHorizontal: 4,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 4,
    backgroundColor: colors?.background?.tertiary || '#E0E0E0',
    borderRadius: 20,
  },
  categoryButtonActive: {
    backgroundColor: colors?.primary?.main || '#007AFF',
  },
  categoryButtonText: {
    fontSize: 14,
    color: colors?.text?.primary || '#000000',
  },
  categoryButtonTextActive: {
    color: '#FFFFFF',
  },
  serviceCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginBottom: 12,
    backgroundColor: colors?.background?.primary || '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors?.border?.primary || '#E0E0E0',
  },
  serviceInfo: {
    flex: 1,
    marginRight: 12,
  },
  serviceName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors?.text?.primary || '#000000',
    marginBottom: 4,
  },
  serviceDescription: {
    fontSize: 14,
    color: colors?.text?.secondary || '#666666',
    marginBottom: 8,
  },
  serviceDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  servicePrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors?.primary?.main || '#007AFF',
    marginRight: 12,
  },
  serviceDuration: {
    fontSize: 14,
    color: colors?.text?.secondary || '#666666',
  },
  bookButton: {
    minWidth: 80,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: colors?.text?.primary || '#000000',
    textAlign: 'center',
    marginBottom: 20,
  },
});

const styles = createStyles({});
