{"version": 3, "names": ["React", "require", "vanilla", "identity", "arg", "useStore", "api", "selector", "arguments", "length", "undefined", "slice", "useSyncExternalStore", "subscribe", "getState", "getInitialState", "useDebugValue", "createImpl", "createState", "createStore", "useBoundStore", "Object", "assign", "create", "exports"], "sources": ["react.js"], "sourcesContent": ["'use strict';\n\nvar React = require('react');\nvar vanilla = require('zustand/vanilla');\n\nconst identity = (arg) => arg;\nfunction useStore(api, selector = identity) {\n  const slice = React.useSyncExternalStore(\n    api.subscribe,\n    () => selector(api.getState()),\n    () => selector(api.getInitialState())\n  );\n  React.useDebugValue(slice);\n  return slice;\n}\nconst createImpl = (createState) => {\n  const api = vanilla.createStore(createState);\n  const useBoundStore = (selector) => useStore(api, selector);\n  Object.assign(useBoundStore, api);\n  return useBoundStore;\n};\nconst create = (createState) => createState ? createImpl(createState) : createImpl;\n\nexports.create = create;\nexports.useStore = useStore;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAC5B,IAAIC,OAAO,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AAExC,IAAME,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,GAAG;EAAA,OAAKA,GAAG;AAAA;AAC7B,SAASC,QAAQA,CAACC,GAAG,EAAuB;EAAA,IAArBC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGL,QAAQ;EACxC,IAAMQ,KAAK,GAAGX,KAAK,CAACY,oBAAoB,CACtCN,GAAG,CAACO,SAAS,EACb;IAAA,OAAMN,QAAQ,CAACD,GAAG,CAACQ,QAAQ,CAAC,CAAC,CAAC;EAAA,GAC9B;IAAA,OAAMP,QAAQ,CAACD,GAAG,CAACS,eAAe,CAAC,CAAC,CAAC;EAAA,CACvC,CAAC;EACDf,KAAK,CAACgB,aAAa,CAACL,KAAK,CAAC;EAC1B,OAAOA,KAAK;AACd;AACA,IAAMM,UAAU,GAAG,SAAbA,UAAUA,CAAIC,WAAW,EAAK;EAClC,IAAMZ,GAAG,GAAGJ,OAAO,CAACiB,WAAW,CAACD,WAAW,CAAC;EAC5C,IAAME,aAAa,GAAG,SAAhBA,aAAaA,CAAIb,QAAQ;IAAA,OAAKF,QAAQ,CAACC,GAAG,EAAEC,QAAQ,CAAC;EAAA;EAC3Dc,MAAM,CAACC,MAAM,CAACF,aAAa,EAAEd,GAAG,CAAC;EACjC,OAAOc,aAAa;AACtB,CAAC;AACD,IAAMG,MAAM,GAAG,SAATA,MAAMA,CAAIL,WAAW;EAAA,OAAKA,WAAW,GAAGD,UAAU,CAACC,WAAW,CAAC,GAAGD,UAAU;AAAA;AAElFO,OAAO,CAACD,MAAM,GAAGA,MAAM;AACvBC,OAAO,CAACnB,QAAQ,GAAGA,QAAQ", "ignoreList": []}