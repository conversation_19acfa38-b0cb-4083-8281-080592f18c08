# Search Architecture Design - Frontend V1 Rebuild

## Overview
Complete redesign of search functionality based on frontend_v0 reference with full backend integration and enhanced user experience.

## Architecture Components

### 1. API Layer Redesign

#### New API Configuration
```typescript
// Updated config.ts
export const apiConfig = {
  baseUrl: 'http://************:8000/api/v1/customer',
  endpoints: {
    search: '/search/search/',
    suggestions: '/search/suggestions/',
    categories: '/categories/',
    providers: '/providers/',
    services: '/services/'
  }
}
```

#### Search API Service
- **Primary Endpoint**: `/api/v1/customer/search/search/`
- **Parameters**: `q`, `type`, `location`, `category`, `price_min`, `price_max`, `rating_min`
- **Authentication**: JWT token in Authorization header
- **Response Format**: `{ services: [], providers: [] }`

### 2. Component Architecture

#### Core Components
1. **SearchScreen** (Main container)
2. **SearchHeader** (Search input + filters toggle)
3. **SearchFilters** (Advanced filtering panel)
4. **MapViewComponent** (Google Maps integration)
5. **SearchResults** (Results list with tabs)
6. **SearchSuggestions** (Real-time suggestions)

#### Component Hierarchy
```
SearchScreen
├── SearchHeader
│   ├── SearchInput
│   └── FilterToggle
├── SearchFilters (collapsible)
│   ├── CategoryFilter
│   ├── PriceRangeFilter
│   ├── RatingFilter
│   └── LocationFilter
├── ViewToggle (List/Map)
├── MapViewComponent (conditional)
└── SearchResults
    ├── ResultsTabs (Services/Providers)
    ├── ServicesList
    └── ProvidersList
```

### 3. State Management

#### Search State Structure
```typescript
interface SearchState {
  // Query and filters
  query: string;
  filters: SearchFilters;
  activeTab: 'services' | 'providers';
  viewMode: 'list' | 'map';
  
  // Results
  results: {
    services: Service[];
    providers: ServiceProvider[];
  };
  
  // UI State
  isLoading: boolean;
  isRefreshing: boolean;
  error: string | null;
  hasMore: boolean;
  page: number;
  totalResults: number;
  
  // Suggestions
  suggestions: SearchSuggestion[];
  recentSearches: string[];
  showSuggestions: boolean;
}
```

#### Filter State
```typescript
interface SearchFilters {
  category?: string;
  location?: {
    latitude: number;
    longitude: number;
    radius: number; // in km
  };
  priceRange?: {
    min: number;
    max: number;
  };
  rating?: number;
  sortBy?: 'relevance' | 'price' | 'rating' | 'distance';
  availability?: 'today' | 'tomorrow' | 'this_week';
}
```

### 4. Google Maps Integration

#### MapViewComponent Features
- **Provider Markers**: Show all providers in search results
- **User Location**: Current location marker
- **Clustering**: Group nearby providers
- **Info Windows**: Provider details on marker tap
- **Search Area**: Visual radius indicator
- **Navigation**: Directions to selected provider

#### Map State Management
```typescript
interface MapState {
  region: Region;
  markers: MapMarker[];
  selectedMarker?: string;
  userLocation?: Location;
  showUserLocation: boolean;
  searchRadius: number;
}
```

### 5. Search Flow Design

#### Search Process
1. **Input Handling**: Debounced search input (300ms)
2. **Suggestion Display**: Show suggestions while typing
3. **API Call**: Search with current query and filters
4. **Results Processing**: Transform and cache results
5. **UI Update**: Display results in list/map view
6. **History**: Save successful searches

#### Error Handling Flow
1. **Network Errors**: Show retry option with offline fallback
2. **No Results**: Display helpful suggestions
3. **API Errors**: Use unified error handling system
4. **Location Errors**: Graceful degradation without location

### 6. Performance Optimizations

#### Caching Strategy
- **Search Results**: Cache for 5 minutes
- **Provider Details**: Cache for 30 minutes
- **Categories**: Cache for 1 hour
- **User Location**: Cache for 10 minutes

#### Lazy Loading
- **Map Component**: Load only when map view is selected
- **Images**: Lazy load provider/service images
- **Pagination**: Load more results on scroll

#### Debouncing
- **Search Input**: 300ms debounce
- **Filter Changes**: 500ms debounce
- **Map Region Changes**: 1000ms debounce

### 7. Responsive Design

#### Breakpoints
- **Mobile**: < 768px (default)
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px (if applicable)

#### Adaptive Features
- **Filter Panel**: Slide-up modal on mobile, sidebar on tablet
- **Map View**: Full screen on mobile, split view on tablet
- **Results Grid**: 1 column mobile, 2 columns tablet

### 8. Accessibility Features

#### Screen Reader Support
- **Search Input**: Proper labels and hints
- **Filter Controls**: Accessible form elements
- **Results**: Structured navigation
- **Map**: Alternative text descriptions

#### Keyboard Navigation
- **Search Input**: Tab navigation
- **Filters**: Keyboard shortcuts
- **Results**: Arrow key navigation

### 9. Integration Points

#### Backend Integration
- **Authentication**: JWT token validation
- **Search API**: CustomerSearchViewSet
- **Location Services**: Geolocation API
- **Analytics**: Search tracking

#### Frontend Integration
- **Navigation**: React Navigation integration
- **State Management**: Zustand store
- **Error Handling**: Unified error system
- **Performance**: Performance monitoring

### 10. Testing Strategy

#### Unit Tests
- **Search Service**: API calls and data transformation
- **Components**: Render and interaction tests
- **Utilities**: Helper function tests

#### Integration Tests
- **Search Flow**: End-to-end search process
- **Map Integration**: Location and marker tests
- **Filter Functionality**: Combined filter tests

#### Performance Tests
- **Load Times**: Search response times
- **Memory Usage**: Component memory leaks
- **Network**: API call optimization

## Implementation Phases

### Phase 1: Core API Integration (Priority 1)
1. Fix API configuration and endpoints
2. Implement basic search functionality
3. Add authentication headers
4. Test backend connectivity

### Phase 2: Enhanced Search Features (Priority 2)
1. Add Google Maps integration
2. Implement advanced filters
3. Add search suggestions
4. Implement responsive design

### Phase 3: UX Enhancements (Priority 3)
1. Add loading states and animations
2. Implement offline support
3. Add search analytics
4. Performance optimizations

## Success Metrics
- **Functionality**: All search features working
- **Performance**: < 2s search response time
- **UX**: Smooth interactions and transitions
- **Accessibility**: WCAG 2.1 AA compliance
- **Backend Integration**: 100% API connectivity
