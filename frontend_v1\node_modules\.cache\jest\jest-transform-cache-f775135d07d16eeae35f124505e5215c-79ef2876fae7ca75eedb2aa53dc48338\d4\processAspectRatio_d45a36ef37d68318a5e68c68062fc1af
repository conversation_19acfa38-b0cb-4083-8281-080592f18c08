655bae6b356377c05f741e99ebf5f66c
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var invariant = require('invariant');
function processAspectRatio(aspectRatio) {
  if (typeof aspectRatio === 'number') {
    return aspectRatio;
  }
  if (typeof aspectRatio !== 'string') {
    if (__DEV__) {
      invariant(!aspectRatio, 'aspectRatio must either be a number, a ratio string or `auto`. You passed: %s', aspectRatio);
    }
    return;
  }
  var matches = aspectRatio.split('/').map(function (s) {
    return s.trim();
  });
  if (matches.includes('auto')) {
    if (__DEV__) {
      invariant(matches.length, 'aspectRatio does not support `auto <ratio>`. You passed: %s', aspectRatio);
    }
    return;
  }
  var hasNonNumericValues = matches.some(function (n) {
    return Number.isNaN(Number(n));
  });
  if (__DEV__) {
    invariant(!hasNonNumericValues && (matches.length === 1 || matches.length === 2), 'aspectRatio must either be a number, a ratio string or `auto`. You passed: %s', aspectRatio);
  }
  if (hasNonNumericValues) {
    return;
  }
  if (matches.length === 2) {
    return Number(matches[0]) / Number(matches[1]);
  }
  return Number(matches[0]);
}
var _default = exports.default = processAspectRatio;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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