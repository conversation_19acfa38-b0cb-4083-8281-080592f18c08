/**
 * Provider Slice - Zustand Store for Service Provider Management
 *
 * Store Contract:
 * - Manages service provider profile and business data
 * - Handles provider registration, verification, and portfolio management
 * - Provides availability settings and scheduling functionality
 * - Maintains provider analytics and performance metrics
 * - Supports real-time updates and synchronization
 * - Follows immutable state update patterns
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

export interface ProviderProfile {
  id: string;
  businessName: string;
  description: string;
  category: string;
  subcategories: string[];
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  contact: {
    phone: string;
    email: string;
    website?: string;
    socialMedia?: {
      facebook?: string;
      instagram?: string;
      twitter?: string;
    };
  };
  businessHours: {
    [key: string]: {
      isOpen: boolean;
      openTime: string;
      closeTime: string;
      breaks?: Array<{
        startTime: string;
        endTime: string;
      }>;
    };
  };
  services: ProviderService[];
  portfolio: PortfolioItem[];
  certifications: Certification[];
  amenities: string[];
  policies: {
    cancellation: string;
    rescheduling: string;
    noShow: string;
    payment: string;
  };
  verification: {
    status: 'pending' | 'verified' | 'rejected';
    documents: VerificationDocument[];
    verifiedAt?: string;
    verifiedBy?: string;
  };
  analytics: ProviderAnalytics;
  settings: ProviderSettings;
  createdAt: string;
  updatedAt: string;
}

export interface ProviderService {
  id: string;
  name: string;
  description: string;
  category: string;
  duration: number; // in minutes
  price: number;
  currency: string;
  isActive: boolean;
  images: string[];
  requirements?: string[];
  addOns?: ServiceAddOn[];
  availability?: ServiceAvailability;
}

export interface ServiceAddOn {
  id: string;
  name: string;
  description: string;
  price: number;
  duration?: number;
}

export interface ServiceAvailability {
  daysOfWeek: number[]; // 0-6 (Sunday-Saturday)
  timeSlots: TimeSlot[];
  blackoutDates: string[];
  advanceBooking: {
    min: number; // hours
    max: number; // days
  };
}

export interface TimeSlot {
  startTime: string;
  endTime: string;
  isAvailable: boolean;
}

export interface PortfolioItem {
  id: string;
  title: string;
  description: string;
  images: string[];
  serviceId?: string;
  tags: string[];
  createdAt: string;
}

export interface Certification {
  id: string;
  name: string;
  issuer: string;
  issueDate: string;
  expiryDate?: string;
  certificateNumber: string;
  documentUrl?: string;
  isVerified: boolean;
}

export interface VerificationDocument {
  id: string;
  type: 'license' | 'insurance' | 'certification' | 'identity';
  name: string;
  url: string;
  status: 'pending' | 'approved' | 'rejected';
  uploadedAt: string;
  reviewedAt?: string;
  reviewNotes?: string;
}

export interface ProviderAnalytics {
  totalBookings: number;
  completedBookings: number;
  cancelledBookings: number;
  totalRevenue: number;
  averageRating: number;
  totalReviews: number;
  responseTime: number; // average in minutes
  completionRate: number; // percentage
  repeatCustomers: number;
  monthlyStats: {
    [month: string]: {
      bookings: number;
      revenue: number;
      newCustomers: number;
    };
  };
  popularServices: Array<{
    serviceId: string;
    serviceName: string;
    bookingCount: number;
    revenue: number;
  }>;
  peakHours: Array<{
    hour: number;
    bookingCount: number;
  }>;
}

export interface ProviderSettings {
  notifications: {
    newBookings: boolean;
    cancellations: boolean;
    reviews: boolean;
    promotions: boolean;
    systemUpdates: boolean;
  };
  availability: {
    autoAcceptBookings: boolean;
    requireDeposit: boolean;
    depositPercentage: number;
    bufferTime: number; // minutes between bookings
  };
  communication: {
    allowDirectMessages: boolean;
    autoResponders: boolean;
    businessPhone: boolean;
  };
  privacy: {
    showExactLocation: boolean;
    showPersonalInfo: boolean;
    allowReviews: boolean;
  };
}

export interface ProviderState {
  // Profile data
  profile: ProviderProfile | null;

  // UI state
  isLoading: boolean;
  error: string | null;

  // Form state
  isEditing: boolean;
  unsavedChanges: boolean;

  // Analytics
  analyticsLoading: boolean;
  analyticsError: string | null;

  // Actions
  loadProfile: (providerId: string) => Promise<void>;
  updateProfile: (updates: Partial<ProviderProfile>) => Promise<void>;
  addService: (service: Omit<ProviderService, 'id'>) => Promise<void>;
  updateService: (
    serviceId: string,
    updates: Partial<ProviderService>,
  ) => Promise<void>;
  deleteService: (serviceId: string) => Promise<void>;
  addPortfolioItem: (
    item: Omit<PortfolioItem, 'id' | 'createdAt'>,
  ) => Promise<void>;
  updatePortfolioItem: (
    itemId: string,
    updates: Partial<PortfolioItem>,
  ) => Promise<void>;
  deletePortfolioItem: (itemId: string) => Promise<void>;
  updateAvailability: (
    availability: Partial<ServiceAvailability>,
  ) => Promise<void>;
  updateSettings: (settings: Partial<ProviderSettings>) => Promise<void>;
  uploadVerificationDocument: (
    document: Omit<VerificationDocument, 'id' | 'uploadedAt' | 'status'>,
  ) => Promise<void>;
  refreshAnalytics: () => Promise<void>;
  setEditing: (editing: boolean) => void;
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  profile: null,
  isLoading: false,
  error: null,
  isEditing: false,
  unsavedChanges: false,
  analyticsLoading: false,
  analyticsError: null,
};

export const useProviderStore = create<ProviderState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        ...initialState,

        // Load provider profile
        loadProfile: async (providerId: string) => {
          set(
            state => ({ ...state, isLoading: true, error: null }),
            false,
            'provider/loadProfile/start',
          );

          try {
            // Import the provider API service
            const { providerApiService } = await import('../services/providerApi');

            // Fetch real profile data from backend
            const profile = await providerApiService.getProfile();



            set(
              state => ({
                ...state,
                profile,
                isLoading: false,
                error: null,
              }),
              false,
              'provider/loadProfile/success',
            );
          } catch (error) {
            set(
              state => ({
                ...state,
                isLoading: false,
                error:
                  error instanceof Error
                    ? error.message
                    : 'Failed to load profile',
              }),
              false,
              'provider/loadProfile/error',
            );
          }
        },

        // Update provider profile
        updateProfile: async (updates: Partial<ProviderProfile>) => {
          const currentProfile = get().profile;
          if (!currentProfile) {
            throw new Error('No profile loaded');
          }

          set(
            state => ({ ...state, isLoading: true, error: null }),
            false,
            'provider/updateProfile/start',
          );

          try {
            // Import the provider API service
            const { providerApiService } = await import('../services/providerApi');

            // Update profile via backend API
            const updatedProfile = await providerApiService.updateProfile(updates);

            set(
              state => ({
                ...state,
                profile: updatedProfile,
                isLoading: false,
                unsavedChanges: false,
                error: null,
              }),
              false,
              'provider/updateProfile/success',
            );
          } catch (error) {
            set(
              state => ({
                ...state,
                isLoading: false,
                error:
                  error instanceof Error
                    ? error.message
                    : 'Failed to update profile',
              }),
              false,
              'provider/updateProfile/error',
            );
          }
        },

        // Add service
        addService: async (service: Omit<ProviderService, 'id'>) => {
          const currentProfile = get().profile;
          if (!currentProfile) {
            throw new Error('No profile loaded');
          }

          set(
            state => ({ ...state, isLoading: true, error: null }),
            false,
            'provider/addService/start',
          );

          try {
            // Import the provider API service
            const { providerApiService } = await import('../services/providerApi');

            // Create service via backend API
            const newService = await providerApiService.createService({
              name: service.name,
              description: service.description,
              base_price: service.price,
              duration: service.duration,
              is_active: service.isActive,
            });

            // Transform backend service to frontend format
            const frontendService: ProviderService = {
              id: newService.id,
              name: newService.name,
              description: newService.description,
              price: newService.base_price,
              duration: newService.duration,
              category: service.category,
              isActive: newService.is_active,
              images: service.images || [],
            };

            const updatedProfile = {
              ...currentProfile,
              services: [...currentProfile.services, frontendService],
              updatedAt: new Date().toISOString(),
            };

            set(
              state => ({
                ...state,
                profile: updatedProfile,
                isLoading: false,
                error: null,
              }),
              false,
              'provider/addService/success',
            );
          } catch (error) {
            set(
              state => ({
                ...state,
                isLoading: false,
                error:
                  error instanceof Error
                    ? error.message
                    : 'Failed to add service',
              }),
              false,
              'provider/addService/error',
            );
          }
        },

        // Update service
        updateService: async (
          serviceId: string,
          updates: Partial<ProviderService>,
        ) => {
          const currentProfile = get().profile;
          if (!currentProfile) {
            throw new Error('No profile loaded');
          }

          set(
            state => ({ ...state, isLoading: true, error: null }),
            false,
            'provider/updateService/start',
          );

          try {
            // Import the provider API service
            const { providerApiService } = await import('../services/providerApi');

            // Update service via backend API using bulk update
            const serviceToUpdate = currentProfile.services.find(s => s.id === serviceId);
            if (!serviceToUpdate) {
              throw new Error('Service not found');
            }

            await providerApiService.bulkUpdateServices([{
              id: serviceId,
              name: updates.name,
              description: updates.description,
              base_price: updates.price,
              duration: updates.duration,
              is_active: updates.isActive,
            }]);

            const updatedServices = currentProfile.services.map(service =>
              service.id === serviceId ? { ...service, ...updates } : service,
            );

            const updatedProfile = {
              ...currentProfile,
              services: updatedServices,
              updatedAt: new Date().toISOString(),
            };

            set(
              state => ({
                ...state,
                profile: updatedProfile,
                isLoading: false,
                error: null,
              }),
              false,
              'provider/updateService/success',
            );
          } catch (error) {
            set(
              state => ({
                ...state,
                isLoading: false,
                error:
                  error instanceof Error
                    ? error.message
                    : 'Failed to update service',
              }),
              false,
              'provider/updateService/error',
            );
          }
        },

        // Delete service
        deleteService: async (serviceId: string) => {
          const currentProfile = get().profile;
          if (!currentProfile) {
            throw new Error('No profile loaded');
          }

          set(
            state => ({ ...state, isLoading: true, error: null }),
            false,
            'provider/deleteService/start',
          );

          try {
            await new Promise(resolve => setTimeout(resolve, 300));

            const updatedServices = currentProfile.services.filter(
              service => service.id !== serviceId,
            );

            const updatedProfile = {
              ...currentProfile,
              services: updatedServices,
              updatedAt: new Date().toISOString(),
            };

            set(
              state => ({
                ...state,
                profile: updatedProfile,
                isLoading: false,
                error: null,
              }),
              false,
              'provider/deleteService/success',
            );
          } catch (error) {
            set(
              state => ({
                ...state,
                isLoading: false,
                error:
                  error instanceof Error
                    ? error.message
                    : 'Failed to delete service',
              }),
              false,
              'provider/deleteService/error',
            );
          }
        },

        // Add portfolio item
        addPortfolioItem: async (
          item: Omit<PortfolioItem, 'id' | 'createdAt'>,
        ) => {
          const currentProfile = get().profile;
          if (!currentProfile) {
            throw new Error('No profile loaded');
          }

          set(
            state => ({ ...state, isLoading: true, error: null }),
            false,
            'provider/addPortfolioItem/start',
          );

          try {
            await new Promise(resolve => setTimeout(resolve, 300));

            const newItem: PortfolioItem = {
              ...item,
              id: `portfolio_${Date.now()}`,
              createdAt: new Date().toISOString(),
            };

            const updatedProfile = {
              ...currentProfile,
              portfolio: [...currentProfile.portfolio, newItem],
              updatedAt: new Date().toISOString(),
            };

            set(
              state => ({
                ...state,
                profile: updatedProfile,
                isLoading: false,
                error: null,
              }),
              false,
              'provider/addPortfolioItem/success',
            );
          } catch (error) {
            set(
              state => ({
                ...state,
                isLoading: false,
                error:
                  error instanceof Error
                    ? error.message
                    : 'Failed to add portfolio item',
              }),
              false,
              'provider/addPortfolioItem/error',
            );
          }
        },

        // Update portfolio item
        updatePortfolioItem: async (
          itemId: string,
          updates: Partial<PortfolioItem>,
        ) => {
          const currentProfile = get().profile;
          if (!currentProfile) {
            throw new Error('No profile loaded');
          }

          set(
            state => ({ ...state, isLoading: true, error: null }),
            false,
            'provider/updatePortfolioItem/start',
          );

          try {
            await new Promise(resolve => setTimeout(resolve, 300));

            const updatedPortfolio = currentProfile.portfolio.map(item =>
              item.id === itemId ? { ...item, ...updates } : item,
            );

            const updatedProfile = {
              ...currentProfile,
              portfolio: updatedPortfolio,
              updatedAt: new Date().toISOString(),
            };

            set(
              state => ({
                ...state,
                profile: updatedProfile,
                isLoading: false,
                error: null,
              }),
              false,
              'provider/updatePortfolioItem/success',
            );
          } catch (error) {
            set(
              state => ({
                ...state,
                isLoading: false,
                error:
                  error instanceof Error
                    ? error.message
                    : 'Failed to update portfolio item',
              }),
              false,
              'provider/updatePortfolioItem/error',
            );
          }
        },

        // Delete portfolio item
        deletePortfolioItem: async (itemId: string) => {
          const currentProfile = get().profile;
          if (!currentProfile) {
            throw new Error('No profile loaded');
          }

          set(
            state => ({ ...state, isLoading: true, error: null }),
            false,
            'provider/deletePortfolioItem/start',
          );

          try {
            await new Promise(resolve => setTimeout(resolve, 300));

            const updatedPortfolio = currentProfile.portfolio.filter(
              item => item.id !== itemId,
            );

            const updatedProfile = {
              ...currentProfile,
              portfolio: updatedPortfolio,
              updatedAt: new Date().toISOString(),
            };

            set(
              state => ({
                ...state,
                profile: updatedProfile,
                isLoading: false,
                error: null,
              }),
              false,
              'provider/deletePortfolioItem/success',
            );
          } catch (error) {
            set(
              state => ({
                ...state,
                isLoading: false,
                error:
                  error instanceof Error
                    ? error.message
                    : 'Failed to delete portfolio item',
              }),
              false,
              'provider/deletePortfolioItem/error',
            );
          }
        },

        // Update availability
        updateAvailability: async (
          availability: Partial<ServiceAvailability>,
        ) => {
          const currentProfile = get().profile;
          if (!currentProfile) {
            throw new Error('No profile loaded');
          }

          set(
            state => ({ ...state, isLoading: true, error: null }),
            false,
            'provider/updateAvailability/start',
          );

          try {
            await new Promise(resolve => setTimeout(resolve, 300));

            // Update availability for all services or specific service
            // This is a simplified implementation
            const updatedProfile = {
              ...currentProfile,
              updatedAt: new Date().toISOString(),
            };

            set(
              state => ({
                ...state,
                profile: updatedProfile,
                isLoading: false,
                error: null,
              }),
              false,
              'provider/updateAvailability/success',
            );
          } catch (error) {
            set(
              state => ({
                ...state,
                isLoading: false,
                error:
                  error instanceof Error
                    ? error.message
                    : 'Failed to update availability',
              }),
              false,
              'provider/updateAvailability/error',
            );
          }
        },

        // Update settings
        updateSettings: async (settings: Partial<ProviderSettings>) => {
          const currentProfile = get().profile;
          if (!currentProfile) {
            throw new Error('No profile loaded');
          }

          set(
            state => ({ ...state, isLoading: true, error: null }),
            false,
            'provider/updateSettings/start',
          );

          try {
            await new Promise(resolve => setTimeout(resolve, 300));

            const updatedProfile = {
              ...currentProfile,
              settings: {
                ...currentProfile.settings,
                ...settings,
              },
              updatedAt: new Date().toISOString(),
            };

            set(
              state => ({
                ...state,
                profile: updatedProfile,
                isLoading: false,
                error: null,
              }),
              false,
              'provider/updateSettings/success',
            );
          } catch (error) {
            set(
              state => ({
                ...state,
                isLoading: false,
                error:
                  error instanceof Error
                    ? error.message
                    : 'Failed to update settings',
              }),
              false,
              'provider/updateSettings/error',
            );
          }
        },

        // Upload verification document
        uploadVerificationDocument: async (
          document: Omit<VerificationDocument, 'id' | 'uploadedAt' | 'status'>,
        ) => {
          const currentProfile = get().profile;
          if (!currentProfile) {
            throw new Error('No profile loaded');
          }

          set(
            state => ({ ...state, isLoading: true, error: null }),
            false,
            'provider/uploadDocument/start',
          );

          try {
            await new Promise(resolve => setTimeout(resolve, 1000));

            const newDocument: VerificationDocument = {
              ...document,
              id: `doc_${Date.now()}`,
              uploadedAt: new Date().toISOString(),
              status: 'pending',
            };

            const updatedProfile = {
              ...currentProfile,
              verification: {
                ...currentProfile.verification,
                documents: [
                  ...currentProfile.verification.documents,
                  newDocument,
                ],
              },
              updatedAt: new Date().toISOString(),
            };

            set(
              state => ({
                ...state,
                profile: updatedProfile,
                isLoading: false,
                error: null,
              }),
              false,
              'provider/uploadDocument/success',
            );
          } catch (error) {
            set(
              state => ({
                ...state,
                isLoading: false,
                error:
                  error instanceof Error
                    ? error.message
                    : 'Failed to upload document',
              }),
              false,
              'provider/uploadDocument/error',
            );
          }
        },

        // Refresh analytics
        refreshAnalytics: async () => {
          set(
            state => ({
              ...state,
              analyticsLoading: true,
              analyticsError: null,
            }),
            false,
            'provider/refreshAnalytics/start',
          );

          try {
            await new Promise(resolve => setTimeout(resolve, 1000));

            const currentProfile = get().profile;
            if (!currentProfile) {
              throw new Error('No profile loaded');
            }

            // Import the provider API service
            const { providerApiService } = await import('../services/providerApi');

            // Fetch real dashboard data from backend
            const dashboardData = await providerApiService.getDashboard();

            // Transform dashboard data to analytics format
            const analytics: ProviderAnalytics = {
              totalBookings: dashboardData.total_bookings,
              completedBookings: dashboardData.total_bookings, // Assuming all are completed for now
              cancelledBookings: 0, // Will be calculated separately
              totalRevenue: dashboardData.monthly_revenue * 12, // Estimate annual from monthly
              averageRating: dashboardData.average_rating,
              totalReviews: 0, // Will be calculated from reviews
              responseTime: 12, // Default response time in minutes
              completionRate: 95.0,
              repeatCustomers: 0, // Will be calculated separately
              monthlyStats: {}, // Will be populated from detailed analytics
              popularServices: [], // Will be populated from services data
              peakHours: [], // Will be populated from booking patterns
            };

            const updatedProfile = {
              ...currentProfile,
              analytics,
              updatedAt: new Date().toISOString(),
            };

            set(
              state => ({
                ...state,
                profile: updatedProfile,
                analyticsLoading: false,
                analyticsError: null,
              }),
              false,
              'provider/refreshAnalytics/success',
            );
          } catch (error) {
            set(
              state => ({
                ...state,
                analyticsLoading: false,
                analyticsError:
                  error instanceof Error
                    ? error.message
                    : 'Failed to refresh analytics',
              }),
              false,
              'provider/refreshAnalytics/error',
            );
          }
        },

        // UI actions
        setEditing: (editing: boolean) => {
          set(
            state => ({ ...state, isEditing: editing }),
            false,
            'provider/setEditing',
          );
        },

        clearError: () => {
          set(
            state => ({ ...state, error: null, analyticsError: null }),
            false,
            'provider/clearError',
          );
        },

        reset: () => {
          set(() => ({ ...initialState }), false, 'provider/reset');
        },
      }),
      {
        name: 'provider-store',
        storage: {
          getItem: async (name: string) => {
            try {
              const value = await AsyncStorage.getItem(name);
              return value ? JSON.parse(value) : null;
            } catch (error) {
              console.error('Failed to load provider state:', error);
              return null;
            }
          },
          setItem: async (name: string, value: any) => {
            try {
              await AsyncStorage.setItem(name, JSON.stringify(value));
            } catch (error) {
              console.error('Failed to save provider state:', error);
            }
          },
          removeItem: async (name: string) => {
            try {
              await AsyncStorage.removeItem(name);
            } catch (error) {
              console.error('Failed to remove provider state:', error);
            }
          },
        },
        partialize: state => ({
          profile: state.profile,
        }),
      },
    ),
  ),
);
