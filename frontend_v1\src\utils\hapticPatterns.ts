/**
 * Haptic Patterns Utility
 *
 * Provides standardized haptic feedback patterns for consistent user experience.
 * Implements platform-specific haptic feedback with fallbacks.
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';

export interface HapticPattern {
  type:
    | 'light'
    | 'medium'
    | 'heavy'
    | 'success'
    | 'warning'
    | 'error'
    | 'selection';
  duration?: number;
  intensity?: number;
}

export class HapticPatterns {
  private static isHapticsAvailable =
    Platform.OS === 'ios' || Platform.OS === 'android';

  /**
   * Light haptic feedback for subtle interactions
   */
  static light(): void {
    if (!this.isHapticsAvailable) return;

    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } catch (error) {
      console.warn('Haptic feedback not available:', error);
    }
  }

  /**
   * Light impact haptic feedback (alias for light)
   */
  static lightImpact(): void {
    this.light();
  }

  /**
   * Medium haptic feedback for standard interactions
   */
  static medium(): void {
    if (!this.isHapticsAvailable) return;

    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    } catch (error) {
      console.warn('Haptic feedback not available:', error);
    }
  }

  /**
   * Heavy haptic feedback for important interactions
   */
  static heavy(): void {
    if (!this.isHapticsAvailable) return;

    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    } catch (error) {
      console.warn('Haptic feedback not available:', error);
    }
  }

  /**
   * Success press haptic feedback
   */
  static successPress(): void {
    this.success();
  }

  /**
   * Warning press haptic feedback
   */
  static warningPress(): void {
    this.warning();
  }

  /**
   * Success haptic feedback for positive actions
   */
  static success(): void {
    if (!this.isHapticsAvailable) return;

    try {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      console.warn('Haptic feedback not available:', error);
    }
  }

  /**
   * Warning haptic feedback for cautionary actions
   */
  static warning(): void {
    if (!this.isHapticsAvailable) return;

    try {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
    } catch (error) {
      console.warn('Haptic feedback not available:', error);
    }
  }

  /**
   * Error haptic feedback for failed actions
   */
  static error(): void {
    if (!this.isHapticsAvailable) return;

    try {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    } catch (error) {
      console.warn('Haptic feedback not available:', error);
    }
  }

  /**
   * Selection haptic feedback for UI element selection
   */
  static selection(): void {
    if (!this.isHapticsAvailable) return;

    try {
      Haptics.selectionAsync();
    } catch (error) {
      console.warn('Haptic feedback not available:', error);
    }
  }

  /**
   * Custom haptic pattern
   */
  static custom(pattern: HapticPattern): void {
    if (!this.isHapticsAvailable) return;

    try {
      switch (pattern.type) {
        case 'light':
          this.light();
          break;
        case 'medium':
          this.medium();
          break;
        case 'heavy':
          this.heavy();
          break;
        case 'success':
          this.success();
          break;
        case 'warning':
          this.warning();
          break;
        case 'error':
          this.error();
          break;
        case 'selection':
          this.selection();
          break;
        default:
          this.medium();
      }
    } catch (error) {
      console.warn('Haptic feedback not available:', error);
    }
  }

  /**
   * Undo action haptic feedback
   */
  static undo(): void {
    this.medium();
  }

  /**
   * Button press haptic feedback
   */
  static buttonPress(): void {
    this.light();
  }

  /**
   * Toggle switch haptic feedback
   */
  static toggle(): void {
    this.selection();
  }

  /**
   * Long press haptic feedback
   */
  static longPress(): void {
    this.heavy();
  }

  /**
   * Swipe action haptic feedback
   */
  static swipe(): void {
    this.light();
  }

  /**
   * Refresh action haptic feedback
   */
  static refresh(): void {
    this.medium();
  }

  /**
   * Delete action haptic feedback
   */
  static delete(): void {
    this.warning();
  }

  /**
   * Confirm action haptic feedback
   */
  static confirm(): void {
    this.success();
  }

  /**
   * Cancel action haptic feedback
   */
  static cancel(): void {
    this.light();
  }
}

// Export default patterns for common use cases
export const hapticPatterns = {
  light: () => HapticPatterns.light(),
  medium: () => HapticPatterns.medium(),
  heavy: () => HapticPatterns.heavy(),
  success: () => HapticPatterns.success(),
  warning: () => HapticPatterns.warning(),
  error: () => HapticPatterns.error(),
  selection: () => HapticPatterns.selection(),
  undo: () => HapticPatterns.undo(),
  buttonPress: () => HapticPatterns.buttonPress(),
  toggle: () => HapticPatterns.toggle(),
  longPress: () => HapticPatterns.longPress(),
  swipe: () => HapticPatterns.swipe(),
  refresh: () => HapticPatterns.refresh(),
  delete: () => HapticPatterns.delete(),
  confirm: () => HapticPatterns.confirm(),
  cancel: () => HapticPatterns.cancel(),
};

export default HapticPatterns;
