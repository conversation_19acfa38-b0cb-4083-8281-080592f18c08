{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_theme", "_jsxRuntime", "_excluded", "spacingMap", "xs", "parseInt", "SpacingSemanticXs", "sm", "SpacingSemanticSm", "md", "SpacingSemanticMd", "lg", "SpacingSemanticLg", "backgroundColorMap", "primary", "ColorBackgroundPrimary", "secondary", "ColorBackgroundSecondary", "tertiary", "ColorBackgroundTertiary", "borderRadiusMap", "none", "BorderRadiusNone", "BorderRadiusSm", "BorderRadiusMd", "BorderRadiusLg", "Box", "exports", "_ref", "children", "padding", "margin", "backgroundColor", "borderRadius", "style", "accessibilityLabel", "accessibilityHint", "props", "_objectWithoutProperties2", "default", "boxStyle", "Object", "assign", "jsx", "View", "accessibilityRole"], "sources": ["Box.tsx"], "sourcesContent": ["/**\n * Box Component - Foundational Layout Atom\n *\n * Component Contract:\n * - Sources all styles from design tokens exclusively\n * - Provides flexible layout container functionality\n * - Supports semantic spacing, colors, and border radius\n * - Handles accessibility for container elements\n * - Follows React Native best practices for performance\n * - Serves as base for other layout components\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport React from 'react';\nimport { View, ViewProps, ViewStyle } from 'react-native';\n\nimport {\n  ColorBackgroundPrimary,\n  ColorBackgroundSecondary,\n  ColorBackgroundTertiary,\n  SpacingSemanticXs,\n  SpacingSemanticSm,\n  SpacingSemanticMd,\n  SpacingSemanticLg,\n  BorderRadiusNone,\n  BorderRadiusSm,\n  BorderRadiusMd,\n  BorderRadiusLg,\n} from '../../core/theme/theme';\n\ntype SpacingSize = 'xs' | 'sm' | 'md' | 'lg';\ntype BackgroundColor = 'primary' | 'secondary' | 'tertiary';\ntype BorderRadiusSize = 'none' | 'sm' | 'md' | 'lg';\n\nexport interface BoxProps extends Omit<ViewProps, 'style'> {\n  /** Box content */\n  children?: React.ReactNode;\n  /** Padding size from design tokens */\n  padding?: SpacingSize;\n  /** Margin size from design tokens */\n  margin?: SpacingSize;\n  /** Background color from design tokens */\n  backgroundColor?: BackgroundColor;\n  /** Border radius from design tokens */\n  borderRadius?: BorderRadiusSize;\n  /** Custom style override */\n  style?: ViewStyle;\n}\n\nconst spacingMap = {\n  xs: parseInt(SpacingSemanticXs, 10),\n  sm: parseInt(SpacingSemanticSm, 10),\n  md: parseInt(SpacingSemanticMd, 10),\n  lg: parseInt(SpacingSemanticLg, 10),\n};\n\nconst backgroundColorMap = {\n  primary: ColorBackgroundPrimary,\n  secondary: ColorBackgroundSecondary,\n  tertiary: ColorBackgroundTertiary,\n};\n\nconst borderRadiusMap = {\n  none: parseInt(BorderRadiusNone, 10),\n  sm: parseInt(BorderRadiusSm, 10),\n  md: parseInt(BorderRadiusMd, 10),\n  lg: parseInt(BorderRadiusLg, 10),\n};\n\nexport const Box: React.FC<BoxProps> = ({\n  children,\n  padding,\n  margin,\n  backgroundColor,\n  borderRadius,\n  style,\n  accessibilityLabel,\n  accessibilityHint,\n  ...props\n}) => {\n  const boxStyle: ViewStyle = {\n    ...(padding && { padding: spacingMap[padding] }),\n    ...(margin && { margin: spacingMap[margin] }),\n    ...(backgroundColor && {\n      backgroundColor: backgroundColorMap[backgroundColor],\n    }),\n    ...(borderRadius && { borderRadius: borderRadiusMap[borderRadius] }),\n    ...style,\n  };\n\n  return (\n    <View\n      style={boxStyle}\n      accessibilityRole=\"none\"\n      accessibilityLabel={accessibilityLabel}\n      accessibilityHint={accessibilityHint}\n      {...props}>\n      {children}\n    </View>\n  );\n};\n"], "mappings": ";;;;;;AAeA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,MAAA,GAAAF,OAAA;AAYgC,IAAAG,WAAA,GAAAH,OAAA;AAAA,IAAAI,SAAA;AAqBhC,IAAMC,UAAU,GAAG;EACjBC,EAAE,EAAEC,QAAQ,CAACC,wBAAiB,EAAE,EAAE,CAAC;EACnCC,EAAE,EAAEF,QAAQ,CAACG,wBAAiB,EAAE,EAAE,CAAC;EACnCC,EAAE,EAAEJ,QAAQ,CAACK,wBAAiB,EAAE,EAAE,CAAC;EACnCC,EAAE,EAAEN,QAAQ,CAACO,wBAAiB,EAAE,EAAE;AACpC,CAAC;AAED,IAAMC,kBAAkB,GAAG;EACzBC,OAAO,EAAEC,6BAAsB;EAC/BC,SAAS,EAAEC,+BAAwB;EACnCC,QAAQ,EAAEC;AACZ,CAAC;AAED,IAAMC,eAAe,GAAG;EACtBC,IAAI,EAAEhB,QAAQ,CAACiB,uBAAgB,EAAE,EAAE,CAAC;EACpCf,EAAE,EAAEF,QAAQ,CAACkB,qBAAc,EAAE,EAAE,CAAC;EAChCd,EAAE,EAAEJ,QAAQ,CAACmB,qBAAc,EAAE,EAAE,CAAC;EAChCb,EAAE,EAAEN,QAAQ,CAACoB,qBAAc,EAAE,EAAE;AACjC,CAAC;AAEM,IAAMC,GAAuB,GAAAC,OAAA,CAAAD,GAAA,GAAG,SAA1BA,GAAuBA,CAAAE,IAAA,EAU9B;EAAA,IATJC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IACRC,OAAO,GAAAF,IAAA,CAAPE,OAAO;IACPC,MAAM,GAAAH,IAAA,CAANG,MAAM;IACNC,eAAe,GAAAJ,IAAA,CAAfI,eAAe;IACfC,YAAY,GAAAL,IAAA,CAAZK,YAAY;IACZC,KAAK,GAAAN,IAAA,CAALM,KAAK;IACLC,kBAAkB,GAAAP,IAAA,CAAlBO,kBAAkB;IAClBC,iBAAiB,GAAAR,IAAA,CAAjBQ,iBAAiB;IACdC,KAAK,OAAAC,yBAAA,CAAAC,OAAA,EAAAX,IAAA,EAAA1B,SAAA;EAER,IAAMsC,QAAmB,GAAAC,MAAA,CAAAC,MAAA,KACnBZ,OAAO,IAAI;IAAEA,OAAO,EAAE3B,UAAU,CAAC2B,OAAO;EAAE,CAAC,EAC3CC,MAAM,IAAI;IAAEA,MAAM,EAAE5B,UAAU,CAAC4B,MAAM;EAAE,CAAC,EACxCC,eAAe,IAAI;IACrBA,eAAe,EAAEnB,kBAAkB,CAACmB,eAAe;EACrD,CAAC,EACGC,YAAY,IAAI;IAAEA,YAAY,EAAEb,eAAe,CAACa,YAAY;EAAE,CAAC,EAChEC,KAAK,CACT;EAED,OACE,IAAAjC,WAAA,CAAA0C,GAAA,EAAC5C,YAAA,CAAA6C,IAAI,EAAAH,MAAA,CAAAC,MAAA;IACHR,KAAK,EAAEM,QAAS;IAChBK,iBAAiB,EAAC,MAAM;IACxBV,kBAAkB,EAAEA,kBAAmB;IACvCC,iBAAiB,EAAEA;EAAkB,GACjCC,KAAK;IAAAR,QAAA,EACRA;EAAQ,EACL,CAAC;AAEX,CAAC", "ignoreList": []}