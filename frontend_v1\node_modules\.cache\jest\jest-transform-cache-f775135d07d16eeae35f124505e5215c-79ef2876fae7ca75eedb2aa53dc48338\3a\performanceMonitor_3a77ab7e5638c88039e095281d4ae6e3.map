{"version": 3, "names": ["PerformanceMonitorService", "_classCallCheck2", "default", "metrics", "renderMetrics", "Map", "networkMetrics", "memoryMetrics", "frameDropCount", "isMonitoring", "slowRenderCallbacks", "slowNetworkCallbacks", "MAX_METRICS", "SLOW_RENDER_THRESHOLD", "SLOW_NETWORK_THRESHOLD", "MEMORY_LEAK_THRESHOLD", "_createClass2", "key", "value", "startMonitoring", "_this", "console", "log", "monitoringInterval", "setInterval", "collectMemoryMetrics", "detectMemoryLeaks", "cleanupOldMetrics", "startFrameMonitoring", "stopMonitoring", "clearInterval", "trackRender", "componentName", "renderTime", "metadata", "existing", "get", "reRenders", "lastRenderTime", "Date", "now", "propsCount", "stateUpdates", "set", "addMetric", "name", "timestamp", "category", "Object", "assign", "warn", "for<PERSON>ach", "callback", "error", "trackNetworkRequest", "url", "method", "responseTime", "statusCode", "requestSize", "arguments", "length", "undefined", "responseSize", "cached", "metric", "push", "slice", "trackUserInteraction", "interactionType", "trackNavigation", "fromScreen", "toScreen", "navigationTime", "trackError", "errorMessage", "message", "errorName", "errorStack", "stack", "level", "data", "tags", "source", "errorType", "extra", "performanceMetadata", "getPerformanceReport", "_this2", "recentMetrics", "filter", "m", "averageRenderTime", "reduce", "sum", "averageNetworkTime", "slowComponents", "Array", "from", "values", "c", "sort", "a", "b", "slowNetworkRequests", "n", "cachedRequests", "totalRequests", "cacheHitRate", "latestMemory", "memoryUsage", "usedJSHeapSize", "recommendations", "generateRecommendations", "summary", "frameDrops", "memoryLeaks", "getMetricsByCategory", "clearMetrics", "clear", "on<PERSON>lowRender", "onSlowNetwork", "offSlowRender", "index", "indexOf", "splice", "offSlowNetwork", "performance", "memory", "totalJSHeapSize", "jsHeapSizeLimit", "leaks", "recent", "growth", "Math", "round", "_ref", "entries", "_ref2", "_slicedToArray2", "__DEV__", "cutoff", "map", "join", "performanceMonitor", "exports", "_default"], "sources": ["performanceMonitor.ts"], "sourcesContent": ["/**\n * Enhanced Performance Monitor Service - Comprehensive Performance Analytics\n *\n * Service Contract:\n * - Monitors render performance, frame drops, and component lifecycle\n * - Tracks memory usage, garbage collection, and resource optimization\n * - Measures API response times, network performance, and caching efficiency\n * - Provides comprehensive performance analytics and intelligent alerts\n * - Implements performance optimization suggestions and automated improvements\n * - Integrates with crash reporting and user behavior analytics\n * - Supports real-time performance monitoring and historical analysis\n * - Provides performance budgets and threshold monitoring\n *\n * @version 3.0.0 - Enhanced with Comprehensive Performance Analytics\n * <AUTHOR> Development Team\n */\n\n// Sentry temporarily disabled due to bundling conflicts\n// import { captureError, addSentryBreadcrumb, SentryPerformance } from '../config/sentry';\n\ninterface PerformanceMetric {\n  name: string;\n  value: number;\n  timestamp: number;\n  category:\n    | 'render'\n    | 'network'\n    | 'memory'\n    | 'navigation'\n    | 'user_interaction'\n    | 'error';\n  metadata?: Record<string, any>;\n}\n\ninterface RenderMetrics {\n  componentName: string;\n  renderTime: number;\n  propsCount: number;\n  stateUpdates: number;\n  reRenders: number;\n  lastRenderTime: number;\n}\n\ninterface NetworkMetrics {\n  url: string;\n  method: string;\n  responseTime: number;\n  statusCode: number;\n  requestSize: number;\n  responseSize: number;\n  cached: boolean;\n  timestamp: number;\n}\n\ninterface MemoryMetrics {\n  usedJSHeapSize: number;\n  totalJSHeapSize: number;\n  jsHeapSizeLimit: number;\n  timestamp: number;\n}\n\ninterface PerformanceReport {\n  summary: {\n    averageRenderTime: number;\n    averageNetworkTime: number;\n    memoryUsage: number;\n    frameDrops: number;\n    cacheHitRate: number;\n  };\n  slowComponents: RenderMetrics[];\n  slowNetworkRequests: NetworkMetrics[];\n  memoryLeaks: string[];\n  recommendations: string[];\n}\n\nclass PerformanceMonitorService {\n  private metrics: PerformanceMetric[] = [];\n  private renderMetrics = new Map<string, RenderMetrics>();\n  private networkMetrics: NetworkMetrics[] = [];\n  private memoryMetrics: MemoryMetrics[] = [];\n  private frameDropCount = 0;\n  private isMonitoring = false;\n  private monitoringInterval?: NodeJS.Timeout;\n\n  // Callback handlers\n  private slowRenderCallbacks: Array<\n    (renderTime: number, componentName: string) => void\n  > = [];\n  private slowNetworkCallbacks: Array<\n    (responseTime: number, url: string) => void\n  > = [];\n\n  private readonly MAX_METRICS = 1000;\n  private readonly SLOW_RENDER_THRESHOLD = 16; // 16ms for 60fps\n  private readonly SLOW_NETWORK_THRESHOLD = 2000; // 2 seconds\n  private readonly MEMORY_LEAK_THRESHOLD = 50 * 1024 * 1024; // 50MB\n\n  /**\n   * Start performance monitoring\n   */\n  startMonitoring(): void {\n    if (this.isMonitoring) return;\n\n    this.isMonitoring = true;\n    console.log('📊 Performance Monitor: Started');\n\n    // Monitor memory usage periodically\n    this.monitoringInterval = setInterval(() => {\n      this.collectMemoryMetrics();\n      this.detectMemoryLeaks();\n      this.cleanupOldMetrics();\n    }, 5000); // Every 5 seconds\n\n    // Monitor frame drops (if available)\n    this.startFrameMonitoring();\n  }\n\n  /**\n   * Stop performance monitoring\n   */\n  stopMonitoring(): void {\n    if (!this.isMonitoring) return;\n\n    this.isMonitoring = false;\n    console.log('📊 Performance Monitor: Stopped');\n\n    if (this.monitoringInterval) {\n      clearInterval(this.monitoringInterval);\n    }\n  }\n\n  /**\n   * Track component render performance\n   */\n  trackRender(\n    componentName: string,\n    renderTime: number,\n    metadata?: Record<string, any>,\n  ): void {\n    const existing = this.renderMetrics.get(componentName);\n\n    if (existing) {\n      existing.renderTime = (existing.renderTime + renderTime) / 2; // Moving average\n      existing.reRenders++;\n      existing.lastRenderTime = Date.now();\n      if (metadata?.propsCount) existing.propsCount = metadata.propsCount;\n      if (metadata?.stateUpdates)\n        existing.stateUpdates += metadata.stateUpdates;\n    } else {\n      this.renderMetrics.set(componentName, {\n        componentName,\n        renderTime,\n        propsCount: metadata?.propsCount || 0,\n        stateUpdates: metadata?.stateUpdates || 0,\n        reRenders: 1,\n        lastRenderTime: Date.now(),\n      });\n    }\n\n    // Track as general metric\n    this.addMetric({\n      name: 'component_render',\n      value: renderTime,\n      timestamp: Date.now(),\n      category: 'render',\n      metadata: { componentName, ...metadata },\n    });\n\n    // Alert on slow renders\n    if (renderTime > this.SLOW_RENDER_THRESHOLD) {\n      console.warn(\n        `🐌 Slow render detected: ${componentName} took ${renderTime}ms`,\n      );\n      // Trigger slow render callbacks\n      this.slowRenderCallbacks.forEach(callback => {\n        try {\n          callback(renderTime, componentName);\n        } catch (error) {\n          console.error('Error in slow render callback:', error);\n        }\n      });\n    }\n  }\n\n  /**\n   * Track network request performance\n   */\n  trackNetworkRequest(\n    url: string,\n    method: string,\n    responseTime: number,\n    statusCode: number,\n    requestSize: number = 0,\n    responseSize: number = 0,\n    cached: boolean = false,\n  ): void {\n    const metric: NetworkMetrics = {\n      url,\n      method,\n      responseTime,\n      statusCode,\n      requestSize,\n      responseSize,\n      cached,\n      timestamp: Date.now(),\n    };\n\n    this.networkMetrics.push(metric);\n\n    // Track as general metric\n    this.addMetric({\n      name: 'network_request',\n      value: responseTime,\n      timestamp: Date.now(),\n      category: 'network',\n      metadata: { url, method, statusCode, cached },\n    });\n\n    // Alert on slow requests\n    if (responseTime > this.SLOW_NETWORK_THRESHOLD) {\n      console.warn(\n        `🐌 Slow network request: ${method} ${url} took ${responseTime}ms`,\n      );\n      // Trigger slow network callbacks\n      this.slowNetworkCallbacks.forEach(callback => {\n        try {\n          callback(responseTime, url);\n        } catch (error) {\n          console.error('Error in slow network callback:', error);\n        }\n      });\n    }\n\n    // Keep only recent network metrics\n    if (this.networkMetrics.length > this.MAX_METRICS) {\n      this.networkMetrics = this.networkMetrics.slice(-this.MAX_METRICS / 2);\n    }\n  }\n\n  /**\n   * Track user interaction performance\n   */\n  trackUserInteraction(\n    interactionType: string,\n    responseTime: number,\n    metadata?: Record<string, any>,\n  ): void {\n    this.addMetric({\n      name: 'user_interaction',\n      value: responseTime,\n      timestamp: Date.now(),\n      category: 'user_interaction',\n      metadata: { interactionType, ...metadata },\n    });\n\n    // Alert on slow interactions\n    if (responseTime > 100) {\n      // 100ms threshold for interactions\n      console.warn(\n        `🐌 Slow interaction: ${interactionType} took ${responseTime}ms`,\n      );\n    }\n  }\n\n  /**\n   * Track navigation performance\n   */\n  trackNavigation(\n    fromScreen: string,\n    toScreen: string,\n    navigationTime: number,\n    metadata?: Record<string, any>,\n  ): void {\n    this.addMetric({\n      name: 'navigation',\n      value: navigationTime,\n      timestamp: Date.now(),\n      category: 'navigation',\n      metadata: { fromScreen, toScreen, ...metadata },\n    });\n  }\n\n  /**\n   * Track error occurrence\n   */\n  trackError(error: Error, metadata?: Record<string, any>): void {\n    this.addMetric({\n      name: 'error',\n      value: 1, // Error count\n      timestamp: Date.now(),\n      category: 'error',\n      metadata: {\n        errorMessage: error.message,\n        errorName: error.name,\n        errorStack: error.stack,\n        ...metadata,\n      },\n    });\n\n    // Add breadcrumb for error context (console logging instead of Sentry)\n    console.warn('[PerformanceMonitor] Error breadcrumb:', {\n      message: `Performance Monitor: Error tracked - ${error.message}`,\n      category: 'performance',\n      level: 'error',\n      data: {\n        errorName: error.name,\n        ...metadata,\n      },\n    });\n\n    // Report error with performance context (console logging instead of Sentry)\n    console.error('[PerformanceMonitor] Error captured:', {\n      error: error.message,\n      stack: error.stack,\n      tags: {\n        source: 'performance_monitor',\n        errorType: error.name,\n      },\n      extra: {\n        performanceMetadata: metadata,\n        errorStack: error.stack,\n        timestamp: Date.now(),\n      },\n      level: 'error',\n    });\n\n    // Log error for debugging\n    console.warn('📊 Performance Monitor: Error tracked', {\n      error: error.message,\n      metadata,\n    });\n  }\n\n  /**\n   * Get performance report\n   */\n  getPerformanceReport(): PerformanceReport {\n    const now = Date.now();\n    const recentMetrics = this.metrics.filter(m => now - m.timestamp < 300000); // Last 5 minutes\n\n    // Calculate averages\n    const renderMetrics = recentMetrics.filter(m => m.category === 'render');\n    const networkMetrics = recentMetrics.filter(m => m.category === 'network');\n\n    const averageRenderTime =\n      renderMetrics.length > 0\n        ? renderMetrics.reduce((sum, m) => sum + m.value, 0) /\n          renderMetrics.length\n        : 0;\n\n    const averageNetworkTime =\n      networkMetrics.length > 0\n        ? networkMetrics.reduce((sum, m) => sum + m.value, 0) /\n          networkMetrics.length\n        : 0;\n\n    // Get slow components\n    const slowComponents = Array.from(this.renderMetrics.values())\n      .filter(c => c.renderTime > this.SLOW_RENDER_THRESHOLD)\n      .sort((a, b) => b.renderTime - a.renderTime)\n      .slice(0, 10);\n\n    // Get slow network requests\n    const slowNetworkRequests = this.networkMetrics\n      .filter(n => n.responseTime > this.SLOW_NETWORK_THRESHOLD)\n      .sort((a, b) => b.responseTime - a.responseTime)\n      .slice(0, 10);\n\n    // Calculate cache hit rate\n    const cachedRequests = this.networkMetrics.filter(n => n.cached).length;\n    const totalRequests = this.networkMetrics.length;\n    const cacheHitRate = totalRequests > 0 ? cachedRequests / totalRequests : 0;\n\n    // Get current memory usage\n    const latestMemory = this.memoryMetrics[this.memoryMetrics.length - 1];\n    const memoryUsage = latestMemory ? latestMemory.usedJSHeapSize : 0;\n\n    // Generate recommendations\n    const recommendations = this.generateRecommendations({\n      averageRenderTime,\n      averageNetworkTime,\n      slowComponents,\n      slowNetworkRequests,\n      cacheHitRate,\n      memoryUsage,\n    });\n\n    return {\n      summary: {\n        averageRenderTime,\n        averageNetworkTime,\n        memoryUsage,\n        frameDrops: this.frameDropCount,\n        cacheHitRate,\n      },\n      slowComponents,\n      slowNetworkRequests,\n      memoryLeaks: this.detectMemoryLeaks(),\n      recommendations,\n    };\n  }\n\n  /**\n   * Get metrics by category\n   */\n  getMetricsByCategory(\n    category: PerformanceMetric['category'],\n  ): PerformanceMetric[] {\n    return this.metrics.filter(m => m.category === category);\n  }\n\n  /**\n   * Clear all metrics\n   */\n  clearMetrics(): void {\n    this.metrics = [];\n    this.renderMetrics.clear();\n    this.networkMetrics = [];\n    this.memoryMetrics = [];\n    this.frameDropCount = 0;\n  }\n\n  /**\n   * Register callback for slow render events\n   */\n  onSlowRender(\n    callback: (renderTime: number, componentName: string) => void,\n  ): void {\n    this.slowRenderCallbacks.push(callback);\n  }\n\n  /**\n   * Register callback for slow network events\n   */\n  onSlowNetwork(callback: (responseTime: number, url: string) => void): void {\n    this.slowNetworkCallbacks.push(callback);\n  }\n\n  /**\n   * Remove slow render callback\n   */\n  offSlowRender(\n    callback: (renderTime: number, componentName: string) => void,\n  ): void {\n    const index = this.slowRenderCallbacks.indexOf(callback);\n    if (index > -1) {\n      this.slowRenderCallbacks.splice(index, 1);\n    }\n  }\n\n  /**\n   * Remove slow network callback\n   */\n  offSlowNetwork(callback: (responseTime: number, url: string) => void): void {\n    const index = this.slowNetworkCallbacks.indexOf(callback);\n    if (index > -1) {\n      this.slowNetworkCallbacks.splice(index, 1);\n    }\n  }\n\n  /**\n   * Add a performance metric\n   */\n  private addMetric(metric: PerformanceMetric): void {\n    this.metrics.push(metric);\n\n    // Keep only recent metrics\n    if (this.metrics.length > this.MAX_METRICS) {\n      this.metrics = this.metrics.slice(-this.MAX_METRICS / 2);\n    }\n  }\n\n  /**\n   * Collect memory metrics\n   */\n  private collectMemoryMetrics(): void {\n    // In React Native, memory metrics are limited\n    // This is a placeholder for when performance.memory is available\n    if (typeof performance !== 'undefined' && (performance as any).memory) {\n      const memory = (performance as any).memory;\n      this.memoryMetrics.push({\n        usedJSHeapSize: memory.usedJSHeapSize,\n        totalJSHeapSize: memory.totalJSHeapSize,\n        jsHeapSizeLimit: memory.jsHeapSizeLimit,\n        timestamp: Date.now(),\n      });\n\n      // Keep only recent memory metrics\n      if (this.memoryMetrics.length > 100) {\n        this.memoryMetrics = this.memoryMetrics.slice(-50);\n      }\n    }\n  }\n\n  /**\n   * Detect potential memory leaks\n   */\n  private detectMemoryLeaks(): string[] {\n    const leaks: string[] = [];\n\n    if (this.memoryMetrics.length < 10) return leaks;\n\n    // Check for consistent memory growth\n    const recent = this.memoryMetrics.slice(-10);\n    const growth =\n      recent[recent.length - 1].usedJSHeapSize - recent[0].usedJSHeapSize;\n\n    if (growth > this.MEMORY_LEAK_THRESHOLD) {\n      leaks.push(\n        `Memory usage increased by ${Math.round(growth / 1024 / 1024)}MB in recent measurements`,\n      );\n    }\n\n    // Check for components with excessive re-renders\n    for (const [name, metrics] of this.renderMetrics.entries()) {\n      if (metrics.reRenders > 100) {\n        leaks.push(`Component ${name} has ${metrics.reRenders} re-renders`);\n      }\n    }\n\n    return leaks;\n  }\n\n  /**\n   * Start frame monitoring (if available)\n   */\n  private startFrameMonitoring(): void {\n    // This would use platform-specific APIs to monitor frame drops\n    // For now, it's a placeholder\n    if (__DEV__) {\n      console.log('📊 Frame monitoring started (placeholder)');\n    }\n  }\n\n  /**\n   * Clean up old metrics\n   */\n  private cleanupOldMetrics(): void {\n    const cutoff = Date.now() - 600000; // 10 minutes ago\n    this.metrics = this.metrics.filter(m => m.timestamp > cutoff);\n  }\n\n  /**\n   * Generate performance recommendations\n   */\n  private generateRecommendations(data: {\n    averageRenderTime: number;\n    averageNetworkTime: number;\n    slowComponents: RenderMetrics[];\n    slowNetworkRequests: NetworkMetrics[];\n    cacheHitRate: number;\n    memoryUsage: number;\n  }): string[] {\n    const recommendations: string[] = [];\n\n    if (data.averageRenderTime > this.SLOW_RENDER_THRESHOLD) {\n      recommendations.push(\n        'Consider optimizing component renders with React.memo or useMemo',\n      );\n    }\n\n    if (data.slowComponents.length > 0) {\n      recommendations.push(\n        `Optimize slow components: ${data.slowComponents\n          .slice(0, 3)\n          .map(c => c.componentName)\n          .join(', ')}`,\n      );\n    }\n\n    if (data.averageNetworkTime > 1000) {\n      recommendations.push(\n        'Consider implementing request caching or optimizing API endpoints',\n      );\n    }\n\n    if (data.cacheHitRate < 0.5) {\n      recommendations.push(\n        'Improve cache hit rate by implementing better caching strategies',\n      );\n    }\n\n    if (data.memoryUsage > 100 * 1024 * 1024) {\n      // 100MB\n      recommendations.push(\n        'High memory usage detected - check for memory leaks',\n      );\n    }\n\n    if (data.slowNetworkRequests.length > 5) {\n      recommendations.push(\n        'Multiple slow network requests detected - consider request batching',\n      );\n    }\n\n    return recommendations;\n  }\n}\n\n// Export singleton instance\nexport const performanceMonitor = new PerformanceMonitorService();\nexport default performanceMonitor;\n"], "mappings": ";;;;;;;;IA2EMA,yBAAyB;EAAA,SAAAA,0BAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAF,yBAAA;IAAA,KACrBG,OAAO,GAAwB,EAAE;IAAA,KACjCC,aAAa,GAAG,IAAIC,GAAG,CAAwB,CAAC;IAAA,KAChDC,cAAc,GAAqB,EAAE;IAAA,KACrCC,aAAa,GAAoB,EAAE;IAAA,KACnCC,cAAc,GAAG,CAAC;IAAA,KAClBC,YAAY,GAAG,KAAK;IAAA,KAIpBC,mBAAmB,GAEvB,EAAE;IAAA,KACEC,oBAAoB,GAExB,EAAE;IAAA,KAEWC,WAAW,GAAG,IAAI;IAAA,KAClBC,qBAAqB,GAAG,EAAE;IAAA,KAC1BC,sBAAsB,GAAG,IAAI;IAAA,KAC7BC,qBAAqB,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI;EAAA;EAAA,WAAAC,aAAA,CAAAd,OAAA,EAAAF,yBAAA;IAAAiB,GAAA;IAAAC,KAAA,EAKzD,SAAAC,eAAeA,CAAA,EAAS;MAAA,IAAAC,KAAA;MACtB,IAAI,IAAI,CAACX,YAAY,EAAE;MAEvB,IAAI,CAACA,YAAY,GAAG,IAAI;MACxBY,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;MAG9C,IAAI,CAACC,kBAAkB,GAAGC,WAAW,CAAC,YAAM;QAC1CJ,KAAI,CAACK,oBAAoB,CAAC,CAAC;QAC3BL,KAAI,CAACM,iBAAiB,CAAC,CAAC;QACxBN,KAAI,CAACO,iBAAiB,CAAC,CAAC;MAC1B,CAAC,EAAE,IAAI,CAAC;MAGR,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC7B;EAAC;IAAAX,GAAA;IAAAC,KAAA,EAKD,SAAAW,cAAcA,CAAA,EAAS;MACrB,IAAI,CAAC,IAAI,CAACpB,YAAY,EAAE;MAExB,IAAI,CAACA,YAAY,GAAG,KAAK;MACzBY,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;MAE9C,IAAI,IAAI,CAACC,kBAAkB,EAAE;QAC3BO,aAAa,CAAC,IAAI,CAACP,kBAAkB,CAAC;MACxC;IACF;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAKD,SAAAa,WAAWA,CACTC,aAAqB,EACrBC,UAAkB,EAClBC,QAA8B,EACxB;MACN,IAAMC,QAAQ,GAAG,IAAI,CAAC/B,aAAa,CAACgC,GAAG,CAACJ,aAAa,CAAC;MAEtD,IAAIG,QAAQ,EAAE;QACZA,QAAQ,CAACF,UAAU,GAAG,CAACE,QAAQ,CAACF,UAAU,GAAGA,UAAU,IAAI,CAAC;QAC5DE,QAAQ,CAACE,SAAS,EAAE;QACpBF,QAAQ,CAACG,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;QACpC,IAAIN,QAAQ,YAARA,QAAQ,CAAEO,UAAU,EAAEN,QAAQ,CAACM,UAAU,GAAGP,QAAQ,CAACO,UAAU;QACnE,IAAIP,QAAQ,YAARA,QAAQ,CAAEQ,YAAY,EACxBP,QAAQ,CAACO,YAAY,IAAIR,QAAQ,CAACQ,YAAY;MAClD,CAAC,MAAM;QACL,IAAI,CAACtC,aAAa,CAACuC,GAAG,CAACX,aAAa,EAAE;UACpCA,aAAa,EAAbA,aAAa;UACbC,UAAU,EAAVA,UAAU;UACVQ,UAAU,EAAE,CAAAP,QAAQ,oBAARA,QAAQ,CAAEO,UAAU,KAAI,CAAC;UACrCC,YAAY,EAAE,CAAAR,QAAQ,oBAARA,QAAQ,CAAEQ,YAAY,KAAI,CAAC;UACzCL,SAAS,EAAE,CAAC;UACZC,cAAc,EAAEC,IAAI,CAACC,GAAG,CAAC;QAC3B,CAAC,CAAC;MACJ;MAGA,IAAI,CAACI,SAAS,CAAC;QACbC,IAAI,EAAE,kBAAkB;QACxB3B,KAAK,EAAEe,UAAU;QACjBa,SAAS,EAAEP,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBO,QAAQ,EAAE,QAAQ;QAClBb,QAAQ,EAAAc,MAAA,CAAAC,MAAA;UAAIjB,aAAa,EAAbA;QAAa,GAAKE,QAAQ;MACxC,CAAC,CAAC;MAGF,IAAID,UAAU,GAAG,IAAI,CAACpB,qBAAqB,EAAE;QAC3CQ,OAAO,CAAC6B,IAAI,CACV,4BAA4BlB,aAAa,SAASC,UAAU,IAC9D,CAAC;QAED,IAAI,CAACvB,mBAAmB,CAACyC,OAAO,CAAC,UAAAC,QAAQ,EAAI;UAC3C,IAAI;YACFA,QAAQ,CAACnB,UAAU,EAAED,aAAa,CAAC;UACrC,CAAC,CAAC,OAAOqB,KAAK,EAAE;YACdhC,OAAO,CAACgC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UACxD;QACF,CAAC,CAAC;MACJ;IACF;EAAC;IAAApC,GAAA;IAAAC,KAAA,EAKD,SAAAoC,mBAAmBA,CACjBC,GAAW,EACXC,MAAc,EACdC,YAAoB,EACpBC,UAAkB,EAIZ;MAAA,IAHNC,WAAmB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;MAAA,IACvBG,YAAoB,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;MAAA,IACxBI,MAAe,GAAAJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MAEvB,IAAMK,MAAsB,GAAG;QAC7BV,GAAG,EAAHA,GAAG;QACHC,MAAM,EAANA,MAAM;QACNC,YAAY,EAAZA,YAAY;QACZC,UAAU,EAAVA,UAAU;QACVC,WAAW,EAAXA,WAAW;QACXI,YAAY,EAAZA,YAAY;QACZC,MAAM,EAANA,MAAM;QACNlB,SAAS,EAAEP,IAAI,CAACC,GAAG,CAAC;MACtB,CAAC;MAED,IAAI,CAAClC,cAAc,CAAC4D,IAAI,CAACD,MAAM,CAAC;MAGhC,IAAI,CAACrB,SAAS,CAAC;QACbC,IAAI,EAAE,iBAAiB;QACvB3B,KAAK,EAAEuC,YAAY;QACnBX,SAAS,EAAEP,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBO,QAAQ,EAAE,SAAS;QACnBb,QAAQ,EAAE;UAAEqB,GAAG,EAAHA,GAAG;UAAEC,MAAM,EAANA,MAAM;UAAEE,UAAU,EAAVA,UAAU;UAAEM,MAAM,EAANA;QAAO;MAC9C,CAAC,CAAC;MAGF,IAAIP,YAAY,GAAG,IAAI,CAAC3C,sBAAsB,EAAE;QAC9CO,OAAO,CAAC6B,IAAI,CACV,4BAA4BM,MAAM,IAAID,GAAG,SAASE,YAAY,IAChE,CAAC;QAED,IAAI,CAAC9C,oBAAoB,CAACwC,OAAO,CAAC,UAAAC,QAAQ,EAAI;UAC5C,IAAI;YACFA,QAAQ,CAACK,YAAY,EAAEF,GAAG,CAAC;UAC7B,CAAC,CAAC,OAAOF,KAAK,EAAE;YACdhC,OAAO,CAACgC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACzD;QACF,CAAC,CAAC;MACJ;MAGA,IAAI,IAAI,CAAC/C,cAAc,CAACuD,MAAM,GAAG,IAAI,CAACjD,WAAW,EAAE;QACjD,IAAI,CAACN,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC6D,KAAK,CAAC,CAAC,IAAI,CAACvD,WAAW,GAAG,CAAC,CAAC;MACxE;IACF;EAAC;IAAAK,GAAA;IAAAC,KAAA,EAKD,SAAAkD,oBAAoBA,CAClBC,eAAuB,EACvBZ,YAAoB,EACpBvB,QAA8B,EACxB;MACN,IAAI,CAACU,SAAS,CAAC;QACbC,IAAI,EAAE,kBAAkB;QACxB3B,KAAK,EAAEuC,YAAY;QACnBX,SAAS,EAAEP,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBO,QAAQ,EAAE,kBAAkB;QAC5Bb,QAAQ,EAAAc,MAAA,CAAAC,MAAA;UAAIoB,eAAe,EAAfA;QAAe,GAAKnC,QAAQ;MAC1C,CAAC,CAAC;MAGF,IAAIuB,YAAY,GAAG,GAAG,EAAE;QAEtBpC,OAAO,CAAC6B,IAAI,CACV,wBAAwBmB,eAAe,SAASZ,YAAY,IAC9D,CAAC;MACH;IACF;EAAC;IAAAxC,GAAA;IAAAC,KAAA,EAKD,SAAAoD,eAAeA,CACbC,UAAkB,EAClBC,QAAgB,EAChBC,cAAsB,EACtBvC,QAA8B,EACxB;MACN,IAAI,CAACU,SAAS,CAAC;QACbC,IAAI,EAAE,YAAY;QAClB3B,KAAK,EAAEuD,cAAc;QACrB3B,SAAS,EAAEP,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBO,QAAQ,EAAE,YAAY;QACtBb,QAAQ,EAAAc,MAAA,CAAAC,MAAA;UAAIsB,UAAU,EAAVA,UAAU;UAAEC,QAAQ,EAARA;QAAQ,GAAKtC,QAAQ;MAC/C,CAAC,CAAC;IACJ;EAAC;IAAAjB,GAAA;IAAAC,KAAA,EAKD,SAAAwD,UAAUA,CAACrB,KAAY,EAAEnB,QAA8B,EAAQ;MAC7D,IAAI,CAACU,SAAS,CAAC;QACbC,IAAI,EAAE,OAAO;QACb3B,KAAK,EAAE,CAAC;QACR4B,SAAS,EAAEP,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBO,QAAQ,EAAE,OAAO;QACjBb,QAAQ,EAAAc,MAAA,CAAAC,MAAA;UACN0B,YAAY,EAAEtB,KAAK,CAACuB,OAAO;UAC3BC,SAAS,EAAExB,KAAK,CAACR,IAAI;UACrBiC,UAAU,EAAEzB,KAAK,CAAC0B;QAAK,GACpB7C,QAAQ;MAEf,CAAC,CAAC;MAGFb,OAAO,CAAC6B,IAAI,CAAC,wCAAwC,EAAE;QACrD0B,OAAO,EAAE,wCAAwCvB,KAAK,CAACuB,OAAO,EAAE;QAChE7B,QAAQ,EAAE,aAAa;QACvBiC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAAjC,MAAA,CAAAC,MAAA;UACF4B,SAAS,EAAExB,KAAK,CAACR;QAAI,GAClBX,QAAQ;MAEf,CAAC,CAAC;MAGFb,OAAO,CAACgC,KAAK,CAAC,sCAAsC,EAAE;QACpDA,KAAK,EAAEA,KAAK,CAACuB,OAAO;QACpBG,KAAK,EAAE1B,KAAK,CAAC0B,KAAK;QAClBG,IAAI,EAAE;UACJC,MAAM,EAAE,qBAAqB;UAC7BC,SAAS,EAAE/B,KAAK,CAACR;QACnB,CAAC;QACDwC,KAAK,EAAE;UACLC,mBAAmB,EAAEpD,QAAQ;UAC7B4C,UAAU,EAAEzB,KAAK,CAAC0B,KAAK;UACvBjC,SAAS,EAAEP,IAAI,CAACC,GAAG,CAAC;QACtB,CAAC;QACDwC,KAAK,EAAE;MACT,CAAC,CAAC;MAGF3D,OAAO,CAAC6B,IAAI,CAAC,uCAAuC,EAAE;QACpDG,KAAK,EAAEA,KAAK,CAACuB,OAAO;QACpB1C,QAAQ,EAARA;MACF,CAAC,CAAC;IACJ;EAAC;IAAAjB,GAAA;IAAAC,KAAA,EAKD,SAAAqE,oBAAoBA,CAAA,EAAsB;MAAA,IAAAC,MAAA;MACxC,IAAMhD,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;MACtB,IAAMiD,aAAa,GAAG,IAAI,CAACtF,OAAO,CAACuF,MAAM,CAAC,UAAAC,CAAC;QAAA,OAAInD,GAAG,GAAGmD,CAAC,CAAC7C,SAAS,GAAG,MAAM;MAAA,EAAC;MAG1E,IAAM1C,aAAa,GAAGqF,aAAa,CAACC,MAAM,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAAC5C,QAAQ,KAAK,QAAQ;MAAA,EAAC;MACxE,IAAMzC,cAAc,GAAGmF,aAAa,CAACC,MAAM,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAAC5C,QAAQ,KAAK,SAAS;MAAA,EAAC;MAE1E,IAAM6C,iBAAiB,GACrBxF,aAAa,CAACyD,MAAM,GAAG,CAAC,GACpBzD,aAAa,CAACyF,MAAM,CAAC,UAACC,GAAG,EAAEH,CAAC;QAAA,OAAKG,GAAG,GAAGH,CAAC,CAACzE,KAAK;MAAA,GAAE,CAAC,CAAC,GAClDd,aAAa,CAACyD,MAAM,GACpB,CAAC;MAEP,IAAMkC,kBAAkB,GACtBzF,cAAc,CAACuD,MAAM,GAAG,CAAC,GACrBvD,cAAc,CAACuF,MAAM,CAAC,UAACC,GAAG,EAAEH,CAAC;QAAA,OAAKG,GAAG,GAAGH,CAAC,CAACzE,KAAK;MAAA,GAAE,CAAC,CAAC,GACnDZ,cAAc,CAACuD,MAAM,GACrB,CAAC;MAGP,IAAMmC,cAAc,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC9F,aAAa,CAAC+F,MAAM,CAAC,CAAC,CAAC,CAC3DT,MAAM,CAAC,UAAAU,CAAC;QAAA,OAAIA,CAAC,CAACnE,UAAU,GAAGuD,MAAI,CAAC3E,qBAAqB;MAAA,EAAC,CACtDwF,IAAI,CAAC,UAACC,CAAC,EAAEC,CAAC;QAAA,OAAKA,CAAC,CAACtE,UAAU,GAAGqE,CAAC,CAACrE,UAAU;MAAA,EAAC,CAC3CkC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;MAGf,IAAMqC,mBAAmB,GAAG,IAAI,CAAClG,cAAc,CAC5CoF,MAAM,CAAC,UAAAe,CAAC;QAAA,OAAIA,CAAC,CAAChD,YAAY,GAAG+B,MAAI,CAAC1E,sBAAsB;MAAA,EAAC,CACzDuF,IAAI,CAAC,UAACC,CAAC,EAAEC,CAAC;QAAA,OAAKA,CAAC,CAAC9C,YAAY,GAAG6C,CAAC,CAAC7C,YAAY;MAAA,EAAC,CAC/CU,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;MAGf,IAAMuC,cAAc,GAAG,IAAI,CAACpG,cAAc,CAACoF,MAAM,CAAC,UAAAe,CAAC;QAAA,OAAIA,CAAC,CAACzC,MAAM;MAAA,EAAC,CAACH,MAAM;MACvE,IAAM8C,aAAa,GAAG,IAAI,CAACrG,cAAc,CAACuD,MAAM;MAChD,IAAM+C,YAAY,GAAGD,aAAa,GAAG,CAAC,GAAGD,cAAc,GAAGC,aAAa,GAAG,CAAC;MAG3E,IAAME,YAAY,GAAG,IAAI,CAACtG,aAAa,CAAC,IAAI,CAACA,aAAa,CAACsD,MAAM,GAAG,CAAC,CAAC;MACtE,IAAMiD,WAAW,GAAGD,YAAY,GAAGA,YAAY,CAACE,cAAc,GAAG,CAAC;MAGlE,IAAMC,eAAe,GAAG,IAAI,CAACC,uBAAuB,CAAC;QACnDrB,iBAAiB,EAAjBA,iBAAiB;QACjBG,kBAAkB,EAAlBA,kBAAkB;QAClBC,cAAc,EAAdA,cAAc;QACdQ,mBAAmB,EAAnBA,mBAAmB;QACnBI,YAAY,EAAZA,YAAY;QACZE,WAAW,EAAXA;MACF,CAAC,CAAC;MAEF,OAAO;QACLI,OAAO,EAAE;UACPtB,iBAAiB,EAAjBA,iBAAiB;UACjBG,kBAAkB,EAAlBA,kBAAkB;UAClBe,WAAW,EAAXA,WAAW;UACXK,UAAU,EAAE,IAAI,CAAC3G,cAAc;UAC/BoG,YAAY,EAAZA;QACF,CAAC;QACDZ,cAAc,EAAdA,cAAc;QACdQ,mBAAmB,EAAnBA,mBAAmB;QACnBY,WAAW,EAAE,IAAI,CAAC1F,iBAAiB,CAAC,CAAC;QACrCsF,eAAe,EAAfA;MACF,CAAC;IACH;EAAC;IAAA/F,GAAA;IAAAC,KAAA,EAKD,SAAAmG,oBAAoBA,CAClBtE,QAAuC,EAClB;MACrB,OAAO,IAAI,CAAC5C,OAAO,CAACuF,MAAM,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAAC5C,QAAQ,KAAKA,QAAQ;MAAA,EAAC;IAC1D;EAAC;IAAA9B,GAAA;IAAAC,KAAA,EAKD,SAAAoG,YAAYA,CAAA,EAAS;MACnB,IAAI,CAACnH,OAAO,GAAG,EAAE;MACjB,IAAI,CAACC,aAAa,CAACmH,KAAK,CAAC,CAAC;MAC1B,IAAI,CAACjH,cAAc,GAAG,EAAE;MACxB,IAAI,CAACC,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,cAAc,GAAG,CAAC;IACzB;EAAC;IAAAS,GAAA;IAAAC,KAAA,EAKD,SAAAsG,YAAYA,CACVpE,QAA6D,EACvD;MACN,IAAI,CAAC1C,mBAAmB,CAACwD,IAAI,CAACd,QAAQ,CAAC;IACzC;EAAC;IAAAnC,GAAA;IAAAC,KAAA,EAKD,SAAAuG,aAAaA,CAACrE,QAAqD,EAAQ;MACzE,IAAI,CAACzC,oBAAoB,CAACuD,IAAI,CAACd,QAAQ,CAAC;IAC1C;EAAC;IAAAnC,GAAA;IAAAC,KAAA,EAKD,SAAAwG,aAAaA,CACXtE,QAA6D,EACvD;MACN,IAAMuE,KAAK,GAAG,IAAI,CAACjH,mBAAmB,CAACkH,OAAO,CAACxE,QAAQ,CAAC;MACxD,IAAIuE,KAAK,GAAG,CAAC,CAAC,EAAE;QACd,IAAI,CAACjH,mBAAmB,CAACmH,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC3C;IACF;EAAC;IAAA1G,GAAA;IAAAC,KAAA,EAKD,SAAA4G,cAAcA,CAAC1E,QAAqD,EAAQ;MAC1E,IAAMuE,KAAK,GAAG,IAAI,CAAChH,oBAAoB,CAACiH,OAAO,CAACxE,QAAQ,CAAC;MACzD,IAAIuE,KAAK,GAAG,CAAC,CAAC,EAAE;QACd,IAAI,CAAChH,oBAAoB,CAACkH,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC5C;IACF;EAAC;IAAA1G,GAAA;IAAAC,KAAA,EAKD,SAAQ0B,SAASA,CAACqB,MAAyB,EAAQ;MACjD,IAAI,CAAC9D,OAAO,CAAC+D,IAAI,CAACD,MAAM,CAAC;MAGzB,IAAI,IAAI,CAAC9D,OAAO,CAAC0D,MAAM,GAAG,IAAI,CAACjD,WAAW,EAAE;QAC1C,IAAI,CAACT,OAAO,GAAG,IAAI,CAACA,OAAO,CAACgE,KAAK,CAAC,CAAC,IAAI,CAACvD,WAAW,GAAG,CAAC,CAAC;MAC1D;IACF;EAAC;IAAAK,GAAA;IAAAC,KAAA,EAKD,SAAQO,oBAAoBA,CAAA,EAAS;MAGnC,IAAI,OAAOsG,WAAW,KAAK,WAAW,IAAKA,WAAW,CAASC,MAAM,EAAE;QACrE,IAAMA,MAAM,GAAID,WAAW,CAASC,MAAM;QAC1C,IAAI,CAACzH,aAAa,CAAC2D,IAAI,CAAC;UACtB6C,cAAc,EAAEiB,MAAM,CAACjB,cAAc;UACrCkB,eAAe,EAAED,MAAM,CAACC,eAAe;UACvCC,eAAe,EAAEF,MAAM,CAACE,eAAe;UACvCpF,SAAS,EAAEP,IAAI,CAACC,GAAG,CAAC;QACtB,CAAC,CAAC;QAGF,IAAI,IAAI,CAACjC,aAAa,CAACsD,MAAM,GAAG,GAAG,EAAE;UACnC,IAAI,CAACtD,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC4D,KAAK,CAAC,CAAC,EAAE,CAAC;QACpD;MACF;IACF;EAAC;IAAAlD,GAAA;IAAAC,KAAA,EAKD,SAAQQ,iBAAiBA,CAAA,EAAa;MACpC,IAAMyG,KAAe,GAAG,EAAE;MAE1B,IAAI,IAAI,CAAC5H,aAAa,CAACsD,MAAM,GAAG,EAAE,EAAE,OAAOsE,KAAK;MAGhD,IAAMC,MAAM,GAAG,IAAI,CAAC7H,aAAa,CAAC4D,KAAK,CAAC,CAAC,EAAE,CAAC;MAC5C,IAAMkE,MAAM,GACVD,MAAM,CAACA,MAAM,CAACvE,MAAM,GAAG,CAAC,CAAC,CAACkD,cAAc,GAAGqB,MAAM,CAAC,CAAC,CAAC,CAACrB,cAAc;MAErE,IAAIsB,MAAM,GAAG,IAAI,CAACtH,qBAAqB,EAAE;QACvCoH,KAAK,CAACjE,IAAI,CACR,6BAA6BoE,IAAI,CAACC,KAAK,CAACF,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,2BAC/D,CAAC;MACH;MAGA,SAAAG,IAAA,IAA8B,IAAI,CAACpI,aAAa,CAACqI,OAAO,CAAC,CAAC,EAAE;QAAA,IAAAC,KAAA,OAAAC,eAAA,CAAAzI,OAAA,EAAAsI,IAAA;QAAA,IAAhD3F,IAAI,GAAA6F,KAAA;QAAA,IAAEvI,OAAO,GAAAuI,KAAA;QACvB,IAAIvI,OAAO,CAACkC,SAAS,GAAG,GAAG,EAAE;UAC3B8F,KAAK,CAACjE,IAAI,CAAC,aAAarB,IAAI,QAAQ1C,OAAO,CAACkC,SAAS,aAAa,CAAC;QACrE;MACF;MAEA,OAAO8F,KAAK;IACd;EAAC;IAAAlH,GAAA;IAAAC,KAAA,EAKD,SAAQU,oBAAoBA,CAAA,EAAS;MAGnC,IAAIgH,OAAO,EAAE;QACXvH,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MAC1D;IACF;EAAC;IAAAL,GAAA;IAAAC,KAAA,EAKD,SAAQS,iBAAiBA,CAAA,EAAS;MAChC,IAAMkH,MAAM,GAAGtG,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,MAAM;MAClC,IAAI,CAACrC,OAAO,GAAG,IAAI,CAACA,OAAO,CAACuF,MAAM,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAAC7C,SAAS,GAAG+F,MAAM;MAAA,EAAC;IAC/D;EAAC;IAAA5H,GAAA;IAAAC,KAAA,EAKD,SAAQ+F,uBAAuBA,CAAChC,IAO/B,EAAY;MACX,IAAM+B,eAAyB,GAAG,EAAE;MAEpC,IAAI/B,IAAI,CAACW,iBAAiB,GAAG,IAAI,CAAC/E,qBAAqB,EAAE;QACvDmG,eAAe,CAAC9C,IAAI,CAClB,kEACF,CAAC;MACH;MAEA,IAAIe,IAAI,CAACe,cAAc,CAACnC,MAAM,GAAG,CAAC,EAAE;QAClCmD,eAAe,CAAC9C,IAAI,CAClB,6BAA6Be,IAAI,CAACe,cAAc,CAC7C7B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACX2E,GAAG,CAAC,UAAA1C,CAAC;UAAA,OAAIA,CAAC,CAACpE,aAAa;QAAA,EAAC,CACzB+G,IAAI,CAAC,IAAI,CAAC,EACf,CAAC;MACH;MAEA,IAAI9D,IAAI,CAACc,kBAAkB,GAAG,IAAI,EAAE;QAClCiB,eAAe,CAAC9C,IAAI,CAClB,mEACF,CAAC;MACH;MAEA,IAAIe,IAAI,CAAC2B,YAAY,GAAG,GAAG,EAAE;QAC3BI,eAAe,CAAC9C,IAAI,CAClB,kEACF,CAAC;MACH;MAEA,IAAIe,IAAI,CAAC6B,WAAW,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,EAAE;QAExCE,eAAe,CAAC9C,IAAI,CAClB,qDACF,CAAC;MACH;MAEA,IAAIe,IAAI,CAACuB,mBAAmB,CAAC3C,MAAM,GAAG,CAAC,EAAE;QACvCmD,eAAe,CAAC9C,IAAI,CAClB,qEACF,CAAC;MACH;MAEA,OAAO8C,eAAe;IACxB;EAAC;AAAA;AAII,IAAMgC,kBAAkB,GAAAC,OAAA,CAAAD,kBAAA,GAAG,IAAIhJ,yBAAyB,CAAC,CAAC;AAAC,IAAAkJ,QAAA,GAAAD,OAAA,CAAA/I,OAAA,GACnD8I,kBAAkB", "ignoreList": []}