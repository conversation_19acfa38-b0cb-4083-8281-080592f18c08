b353108d72129f87902663e6c82cc864
var _contrastEnhancer = require("../contrastEnhancer");
describe('Contrast Enhancer', function () {
  describe('enhancePrimaryCTAContrast', function () {
    it('should validate compliant colors', function () {
      var result = (0, _contrastEnhancer.enhancePrimaryCTAContrast)('#5A7A63', '#FFFFFF');
      expect(result.isCompliant).toBe(true);
      expect(result.ratio).toBeGreaterThanOrEqual(4.5);
      expect(result.recommendation).toContain('Meets WCAG AA standards');
    });
    it('should enhance non-compliant colors', function () {
      var result = (0, _contrastEnhancer.enhancePrimaryCTAContrast)('#CCCCCC', '#FFFFFF');
      expect(result.isCompliant).toBe(false);
      expect(result.enhancedColor).toBeDefined();
      expect(result.recommendation).toContain('Enhanced');
    });
    it('should identify optimal colors (AAA)', function () {
      var result = (0, _contrastEnhancer.enhancePrimaryCTAContrast)('#000000', '#FFFFFF');
      expect(result.isOptimal).toBe(true);
      expect(result.ratio).toBeGreaterThanOrEqual(7.0);
      expect(result.recommendation).toContain('Exceeds WCAG AAA standards');
    });
  });
  describe('validateInteractiveColors', function () {
    it('should validate all interactive colors', function () {
      var results = (0, _contrastEnhancer.validateInteractiveColors)();
      expect(results.primary).toBeDefined();
      expect(results.primaryHover).toBeDefined();
      expect(results.primaryPressed).toBeDefined();
      expect(results.secondary).toBeDefined();
      expect(results.destructive).toBeDefined();
      expect(results.primary.isCompliant).toBe(true);
      expect(results.primaryHover.isCompliant).toBe(true);
      expect(results.primaryPressed.isCompliant).toBe(true);
    });
  });
  describe('generateContrastReport', function () {
    it('should generate comprehensive contrast report', function () {
      var report = (0, _contrastEnhancer.generateContrastReport)();
      expect(report.totalTests).toBeGreaterThan(0);
      expect(report.compliantCount).toBeGreaterThan(0);
      expect(report.complianceRate).toBeGreaterThanOrEqual(0);
      expect(report.results).toHaveLength(report.totalTests);
      expect(report.summary).toBeDefined();
      expect(report.summary.overallStatus).toMatch(/FULLY_COMPLIANT|NEEDS_IMPROVEMENT/);
    });
    it('should have high compliance rate', function () {
      var report = (0, _contrastEnhancer.generateContrastReport)();
      expect(report.complianceRate).toBeGreaterThanOrEqual(80);
    });
  });
  describe('getBestTextColor', function () {
    it('should return white for dark backgrounds', function () {
      var textColor = (0, _contrastEnhancer.getBestTextColor)('#000000');
      expect(textColor).toBe('#FFFFFF');
    });
    it('should return black for light backgrounds', function () {
      var textColor = (0, _contrastEnhancer.getBestTextColor)('#FFFFFF');
      expect(textColor).toBe('#000000');
    });
    it('should return appropriate color for sage green', function () {
      var textColor = (0, _contrastEnhancer.getBestTextColor)('#5A7A63');
      expect(textColor).toBe('#FFFFFF');
    });
  });
  describe('ensureMinimumContrast', function () {
    it('should return original color if contrast is sufficient', function () {
      var result = (0, _contrastEnhancer.ensureMinimumContrast)('#FFFFFF', '#000000', 4.5);
      expect(result).toBe('#FFFFFF');
    });
    it('should return high contrast alternative if insufficient', function () {
      var result = (0, _contrastEnhancer.ensureMinimumContrast)('#CCCCCC', '#FFFFFF', 4.5);
      expect(result).toBe('#000000');
    });
  });
  describe('EnhancedCTAColors', function () {
    it('should have all required color constants', function () {
      expect(_contrastEnhancer.EnhancedCTAColors.primary).toBeDefined();
      expect(_contrastEnhancer.EnhancedCTAColors.primaryHover).toBeDefined();
      expect(_contrastEnhancer.EnhancedCTAColors.primaryPressed).toBeDefined();
      expect(_contrastEnhancer.EnhancedCTAColors.primaryText).toBeDefined();
      expect(_contrastEnhancer.EnhancedCTAColors.secondary).toBeDefined();
      expect(_contrastEnhancer.EnhancedCTAColors.secondaryText).toBeDefined();
    });
    it('should have WCAG compliant primary colors', function () {
      var primaryResult = (0, _contrastEnhancer.enhancePrimaryCTAContrast)(_contrastEnhancer.EnhancedCTAColors.primary, _contrastEnhancer.EnhancedCTAColors.primaryText);
      expect(primaryResult.isCompliant).toBe(true);
      expect(primaryResult.ratio).toBeGreaterThanOrEqual(4.5);
    });
    it('should have WCAG compliant secondary colors', function () {
      var secondaryResult = (0, _contrastEnhancer.enhancePrimaryCTAContrast)(_contrastEnhancer.EnhancedCTAColors.secondary, _contrastEnhancer.EnhancedCTAColors.secondaryText);
      expect(secondaryResult.isCompliant).toBe(true);
      expect(secondaryResult.ratio).toBeGreaterThanOrEqual(4.5);
    });
  });
  describe('Color contrast ratios', function () {
    var testCases = [{
      name: 'Primary CTA',
      background: '#5A7A63',
      text: '#FFFFFF',
      expectedMinRatio: 4.5
    }, {
      name: 'Primary CTA Hover',
      background: '#4A6B52',
      text: '#FFFFFF',
      expectedMinRatio: 4.5
    }, {
      name: 'Primary CTA Pressed',
      background: '#3A5B42',
      text: '#FFFFFF',
      expectedMinRatio: 4.5
    }, {
      name: 'Secondary Button',
      background: '#E1EDE4',
      text: '#1F2937',
      expectedMinRatio: 4.5
    }, {
      name: 'Error Button',
      background: '#DC2626',
      text: '#FFFFFF',
      expectedMinRatio: 4.5
    }];
    testCases.forEach(function (_ref) {
      var name = _ref.name,
        background = _ref.background,
        text = _ref.text,
        expectedMinRatio = _ref.expectedMinRatio;
      it(`should meet WCAG AA standards for ${name}`, function () {
        var result = (0, _contrastEnhancer.enhancePrimaryCTAContrast)(background, text);
        expect(result.ratio).toBeGreaterThanOrEqual(expectedMinRatio);
        expect(result.isCompliant).toBe(true);
      });
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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