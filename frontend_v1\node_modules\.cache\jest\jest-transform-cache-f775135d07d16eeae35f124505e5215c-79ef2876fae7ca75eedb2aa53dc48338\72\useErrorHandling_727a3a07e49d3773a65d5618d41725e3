5d7e9c7add78e696f80ea2e3a4ba76c0
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useErrorHandling = exports.default = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _react = require("react");
var _reactNative = require("react-native");
var _performanceMonitor = require("../services/performanceMonitor");
var _errorHandlingUtils = require("../utils/errorHandlingUtils");
var useErrorHandling = exports.useErrorHandling = function useErrorHandling() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var _options$maxRetries = options.maxRetries,
    maxRetries = _options$maxRetries === void 0 ? 3 : _options$maxRetries,
    _options$retryDelay = options.retryDelay,
    retryDelay = _options$retryDelay === void 0 ? 2000 : _options$retryDelay,
    _options$progressiveR = options.progressiveRetryDelay,
    progressiveRetryDelay = _options$progressiveR === void 0 ? true : _options$progressiveR,
    _options$autoRetryOnA = options.autoRetryOnAppFocus,
    autoRetryOnAppFocus = _options$autoRetryOnA === void 0 ? true : _options$autoRetryOnA,
    _options$autoRetryOnC = options.autoRetryOnConnectionRestored,
    autoRetryOnConnectionRestored = _options$autoRetryOnC === void 0 ? true : _options$autoRetryOnC,
    _options$reportErrors = options.reportErrors,
    reportErrors = _options$reportErrors === void 0 ? true : _options$reportErrors,
    _options$errorContext = options.errorContext,
    errorContext = _options$errorContext === void 0 ? {} : _options$errorContext,
    onError = options.onError,
    onRetry = options.onRetry,
    onMaxRetriesExceeded = options.onMaxRetriesExceeded;
  var _useState = (0, _react.useState)({
      error: null,
      isError: false,
      retryCount: 0,
      lastRetryTime: null,
      canRetry: true
    }),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    state = _useState2[0],
    setState = _useState2[1];
  var appStateRef = (0, _react.useRef)(_reactNative.AppState.currentState);
  var retryTimeoutRef = (0, _react.useRef)(null);
  var componentMountedRef = (0, _react.useRef)(true);
  (0, _react.useEffect)(function () {
    return function () {
      componentMountedRef.current = false;
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);
  (0, _react.useEffect)(function () {
    if (!autoRetryOnAppFocus) return;
    var handleAppStateChange = function handleAppStateChange(nextAppState) {
      if (appStateRef.current.match(/inactive|background/) && nextAppState === 'active' && state.isError && state.retryCount < maxRetries) {
        retry();
      }
      appStateRef.current = nextAppState;
    };
    var subscription = _reactNative.AppState.addEventListener('change', handleAppStateChange);
    return function () {
      subscription.remove();
    };
  }, [state.isError, state.retryCount, maxRetries, autoRetryOnAppFocus]);
  var handleError = (0, _react.useCallback)(function (errorInput) {
    var error = typeof errorInput === 'string' ? new Error(errorInput) : errorInput;
    var appError = error instanceof _errorHandlingUtils.AppError ? error : (0, _errorHandlingUtils.createAppError)(error, {
      component: 'useErrorHandling',
      action: 'handleError',
      additionalData: errorContext
    });
    setState(function (prev) {
      return {
        error: appError,
        isError: true,
        retryCount: prev.retryCount,
        lastRetryTime: Date.now(),
        canRetry: prev.retryCount < maxRetries
      };
    });
    if (reportErrors) {
      (0, _errorHandlingUtils.logError)(appError);
    }
    _performanceMonitor.performanceMonitor.trackUserInteraction('error_handled', 0, {
      errorType: appError.name,
      errorMessage: appError.message,
      retryCount: state.retryCount,
      component: errorContext.component
    });
    if (onError) {
      onError(appError);
    }
    if (state.retryCount >= maxRetries && onMaxRetriesExceeded) {
      onMaxRetriesExceeded();
    }
  }, [errorContext, maxRetries, onError, onMaxRetriesExceeded, reportErrors, state.retryCount]);
  var clearError = (0, _react.useCallback)(function () {
    setState({
      error: null,
      isError: false,
      retryCount: 0,
      lastRetryTime: null,
      canRetry: true
    });
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }
  }, []);
  var retry = (0, _react.useCallback)((0, _asyncToGenerator2.default)(function* () {
    var _state$error, _state$error2;
    if (!state.isError || state.retryCount >= maxRetries) {
      return;
    }
    var currentRetryDelay = progressiveRetryDelay ? retryDelay * Math.pow(1.5, state.retryCount) : retryDelay;
    setState(function (prev) {
      return Object.assign({}, prev, {
        retryCount: prev.retryCount + 1,
        lastRetryTime: Date.now(),
        canRetry: prev.retryCount + 1 < maxRetries
      });
    });
    _performanceMonitor.performanceMonitor.trackUserInteraction('error_retry', 0, {
      errorType: ((_state$error = state.error) == null ? void 0 : _state$error.name) || 'Unknown',
      errorMessage: ((_state$error2 = state.error) == null ? void 0 : _state$error2.message) || 'Unknown error',
      retryCount: state.retryCount + 1,
      retryDelay: currentRetryDelay
    });
    if (onRetry) {
      onRetry(state.retryCount + 1);
    }
    yield new Promise(function (resolve) {
      retryTimeoutRef.current = setTimeout(function () {
        if (componentMountedRef.current) {
          resolve();
        }
      }, currentRetryDelay);
    });
    if (componentMountedRef.current) {
      clearError();
    }
  }), [state.isError, state.retryCount, state.error, maxRetries, progressiveRetryDelay, retryDelay, onRetry, clearError]);
  var getErrorMessage = (0, _react.useCallback)(function () {
    if (!state.error) return '';
    if (isNetworkError()) {
      return 'Unable to connect to the server. Please check your internet connection and try again.';
    }
    if (isServerError()) {
      return 'Our servers are experiencing issues. Please try again later.';
    }
    if (isAuthError()) {
      return 'Your session has expired. Please sign in again.';
    }
    return state.error.message || 'An unexpected error occurred. Please try again.';
  }, [state.error]);
  var isNetworkError = (0, _react.useCallback)(function () {
    if (!state.error) return false;
    var errorMessage = state.error.message.toLowerCase();
    return errorMessage.includes('network') || errorMessage.includes('connection') || errorMessage.includes('offline') || errorMessage.includes('internet') || errorMessage.includes('timeout') || errorMessage.includes('abort');
  }, [state.error]);
  var isServerError = (0, _react.useCallback)(function () {
    if (!state.error) return false;
    var errorMessage = state.error.message.toLowerCase();
    return errorMessage.includes('500') || errorMessage.includes('502') || errorMessage.includes('503') || errorMessage.includes('504') || errorMessage.includes('server error') || errorMessage.includes('internal server');
  }, [state.error]);
  var isAuthError = (0, _react.useCallback)(function () {
    if (!state.error) return false;
    var errorMessage = state.error.message.toLowerCase();
    return errorMessage.includes('401') || errorMessage.includes('403') || errorMessage.includes('unauthorized') || errorMessage.includes('forbidden') || errorMessage.includes('authentication') || errorMessage.includes('not authenticated') || errorMessage.includes('token') || errorMessage.includes('session expired');
  }, [state.error]);
  return {
    error: state.error,
    isError: state.isError,
    retryCount: state.retryCount,
    canRetry: state.canRetry,
    handleError: handleError,
    clearError: clearError,
    retry: retry,
    getErrorMessage: getErrorMessage,
    isNetworkError: isNetworkError,
    isServerError: isServerError,
    isAuthError: isAuthError
  };
};
var _default = exports.default = useErrorHandling;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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