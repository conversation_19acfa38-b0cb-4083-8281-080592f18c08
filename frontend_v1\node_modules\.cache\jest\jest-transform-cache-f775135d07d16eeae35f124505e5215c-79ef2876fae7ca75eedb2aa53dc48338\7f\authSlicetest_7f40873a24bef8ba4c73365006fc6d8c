983fe52a04a886d998ba0c98b4f9e870
_getJestObj().mock('@react-native-async-storage/async-storage', function () {
  return {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn()
  };
});
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _authSlice = require("../authSlice");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
global.fetch = jest.fn();
describe('AuthSlice', function () {
  var mockUser = {
    id: '123',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    role: 'customer',
    profileImage: 'https://example.com/avatar.jpg',
    phoneNumber: '+1234567890',
    isVerified: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  };
  beforeEach(function () {
    jest.clearAllMocks();
    _authSlice.useAuthStore.getState().reset();
  });
  describe('Initial State', function () {
    it('should have correct initial state', function () {
      var state = _authSlice.useAuthStore.getState();
      expect(state.authToken).toBeNull();
      expect(state.refreshToken).toBeNull();
      expect(state.user).toBeNull();
      expect(state.userRole).toBeNull();
      expect(state.status).toBe('idle');
      expect(state.error).toBeNull();
      expect(state.tokenExpiresAt).toBeNull();
      expect(state.isAuthenticated).toBe(false);
    });
  });
  describe('Login Actions', function () {
    it('should handle login start', function () {
      var _useAuthStore$getStat = _authSlice.useAuthStore.getState(),
        loginStart = _useAuthStore$getStat.loginStart;
      loginStart();
      var state = _authSlice.useAuthStore.getState();
      expect(state.status).toBe('loading');
      expect(state.error).toBeNull();
    });
    it('should handle login success', function () {
      var _useAuthStore$getStat2 = _authSlice.useAuthStore.getState(),
        loginSuccess = _useAuthStore$getStat2.loginSuccess;
      var token = 'test-token';
      var refreshToken = 'test-refresh-token';
      loginSuccess(token, refreshToken, mockUser);
      var state = _authSlice.useAuthStore.getState();
      expect(state.authToken).toBe(token);
      expect(state.refreshToken).toBe(refreshToken);
      expect(state.user).toEqual(mockUser);
      expect(state.userRole).toBe(mockUser.role);
      expect(state.status).toBe('success');
      expect(state.error).toBeNull();
      expect(state.isAuthenticated).toBe(true);
      expect(state.tokenExpiresAt).toBeGreaterThan(Date.now());
    });
    it('should handle login failure', function () {
      var _useAuthStore$getStat3 = _authSlice.useAuthStore.getState(),
        loginFailure = _useAuthStore$getStat3.loginFailure;
      var errorMessage = 'Invalid credentials';
      loginFailure(errorMessage);
      var state = _authSlice.useAuthStore.getState();
      expect(state.authToken).toBeNull();
      expect(state.userRole).toBeNull();
      expect(state.status).toBe('error');
      expect(state.error).toBe(errorMessage);
      expect(state.isAuthenticated).toBe(false);
    });
  });
  describe('Registration Actions', function () {
    it('should handle registration start', function () {
      var _useAuthStore$getStat4 = _authSlice.useAuthStore.getState(),
        registerStart = _useAuthStore$getStat4.registerStart;
      registerStart();
      var state = _authSlice.useAuthStore.getState();
      expect(state.status).toBe('loading');
      expect(state.error).toBeNull();
    });
    it('should handle registration success', function () {
      var _useAuthStore$getStat5 = _authSlice.useAuthStore.getState(),
        registerSuccess = _useAuthStore$getStat5.registerSuccess;
      var token = 'test-token';
      var refreshToken = 'test-refresh-token';
      registerSuccess(token, refreshToken, mockUser);
      var state = _authSlice.useAuthStore.getState();
      expect(state.authToken).toBe(token);
      expect(state.refreshToken).toBe(refreshToken);
      expect(state.user).toEqual(mockUser);
      expect(state.userRole).toBe(mockUser.role);
      expect(state.status).toBe('success');
      expect(state.error).toBeNull();
      expect(state.isAuthenticated).toBe(true);
    });
    it('should handle registration failure', function () {
      var _useAuthStore$getStat6 = _authSlice.useAuthStore.getState(),
        registerFailure = _useAuthStore$getStat6.registerFailure;
      var errorMessage = 'Email already exists';
      registerFailure(errorMessage);
      var state = _authSlice.useAuthStore.getState();
      expect(state.authToken).toBeNull();
      expect(state.userRole).toBeNull();
      expect(state.status).toBe('error');
      expect(state.error).toBe(errorMessage);
      expect(state.isAuthenticated).toBe(false);
    });
  });
  describe('Profile Management', function () {
    beforeEach(function () {
      var _useAuthStore$getStat7 = _authSlice.useAuthStore.getState(),
        loginSuccess = _useAuthStore$getStat7.loginSuccess;
      loginSuccess('token', 'refresh-token', mockUser);
    });
    it('should update profile information', function () {
      var _state$user, _state$user2, _state$user3, _state$user4;
      var _useAuthStore$getStat8 = _authSlice.useAuthStore.getState(),
        updateProfile = _useAuthStore$getStat8.updateProfile;
      var updates = {
        firstName: 'Jane',
        lastName: 'Smith',
        phoneNumber: '+0987654321'
      };
      updateProfile(updates);
      var state = _authSlice.useAuthStore.getState();
      expect((_state$user = state.user) == null ? void 0 : _state$user.firstName).toBe(updates.firstName);
      expect((_state$user2 = state.user) == null ? void 0 : _state$user2.lastName).toBe(updates.lastName);
      expect((_state$user3 = state.user) == null ? void 0 : _state$user3.phoneNumber).toBe(updates.phoneNumber);
      expect((_state$user4 = state.user) == null ? void 0 : _state$user4.email).toBe(mockUser.email);
    });
    it('should update tokens', function () {
      var _useAuthStore$getStat9 = _authSlice.useAuthStore.getState(),
        updateTokens = _useAuthStore$getStat9.updateTokens;
      var newToken = 'new-token';
      var newRefreshToken = 'new-refresh-token';
      updateTokens(newToken, newRefreshToken);
      var state = _authSlice.useAuthStore.getState();
      expect(state.authToken).toBe(newToken);
      expect(state.refreshToken).toBe(newRefreshToken);
      expect(state.tokenExpiresAt).toBeGreaterThan(Date.now());
    });
  });
  describe('Logout and Reset', function () {
    beforeEach(function () {
      var _useAuthStore$getStat0 = _authSlice.useAuthStore.getState(),
        loginSuccess = _useAuthStore$getStat0.loginSuccess;
      loginSuccess('token', 'refresh-token', mockUser);
    });
    it('should handle logout', function () {
      var _useAuthStore$getStat1 = _authSlice.useAuthStore.getState(),
        logout = _useAuthStore$getStat1.logout;
      logout();
      var state = _authSlice.useAuthStore.getState();
      expect(state.authToken).toBeNull();
      expect(state.refreshToken).toBeNull();
      expect(state.user).toBeNull();
      expect(state.userRole).toBeNull();
      expect(state.status).toBe('idle');
      expect(state.error).toBeNull();
      expect(state.tokenExpiresAt).toBeNull();
      expect(state.isAuthenticated).toBe(false);
    });
    it('should handle reset', function () {
      var _useAuthStore$getStat10 = _authSlice.useAuthStore.getState(),
        reset = _useAuthStore$getStat10.reset;
      reset();
      var state = _authSlice.useAuthStore.getState();
      expect(state.authToken).toBeNull();
      expect(state.refreshToken).toBeNull();
      expect(state.user).toBeNull();
      expect(state.userRole).toBeNull();
      expect(state.status).toBe('idle');
      expect(state.error).toBeNull();
      expect(state.tokenExpiresAt).toBeNull();
      expect(state.isAuthenticated).toBe(false);
    });
  });
  describe('Token Validation', function () {
    it('should validate valid token', (0, _asyncToGenerator2.default)(function* () {
      fetch.mockResolvedValue({
        ok: true,
        json: jest.fn().mockResolvedValue({
          valid: true
        })
      });
      var _useAuthStore$getStat11 = _authSlice.useAuthStore.getState(),
        validateToken = _useAuthStore$getStat11.validateToken,
        loginSuccess = _useAuthStore$getStat11.loginSuccess;
      loginSuccess('valid-token', 'refresh-token', mockUser);
      var isValid = yield validateToken();
      expect(isValid).toBe(true);
      expect(fetch).toHaveBeenCalledWith(expect.stringContaining('/api/auth/validate/'), expect.objectContaining({
        headers: expect.objectContaining({
          Authorization: 'Bearer valid-token'
        })
      }));
    }));
    it('should handle invalid token', (0, _asyncToGenerator2.default)(function* () {
      fetch.mockResolvedValue({
        ok: false,
        status: 401
      });
      var _useAuthStore$getStat12 = _authSlice.useAuthStore.getState(),
        validateToken = _useAuthStore$getStat12.validateToken,
        loginSuccess = _useAuthStore$getStat12.loginSuccess;
      loginSuccess('invalid-token', 'refresh-token', mockUser);
      var isValid = yield validateToken();
      expect(isValid).toBe(false);
    }));
  });
  describe('Dual Role Support', function () {
    it('should handle customer role', function () {
      var _state$user5;
      var customerUser = Object.assign({}, mockUser, {
        role: 'customer'
      });
      var _useAuthStore$getStat13 = _authSlice.useAuthStore.getState(),
        loginSuccess = _useAuthStore$getStat13.loginSuccess;
      loginSuccess('token', 'refresh-token', customerUser);
      var state = _authSlice.useAuthStore.getState();
      expect(state.userRole).toBe('customer');
      expect((_state$user5 = state.user) == null ? void 0 : _state$user5.role).toBe('customer');
    });
    it('should handle provider role', function () {
      var _state$user6;
      var providerUser = Object.assign({}, mockUser, {
        role: 'provider'
      });
      var _useAuthStore$getStat14 = _authSlice.useAuthStore.getState(),
        loginSuccess = _useAuthStore$getStat14.loginSuccess;
      loginSuccess('token', 'refresh-token', providerUser);
      var state = _authSlice.useAuthStore.getState();
      expect(state.userRole).toBe('provider');
      expect((_state$user6 = state.user) == null ? void 0 : _state$user6.role).toBe('provider');
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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