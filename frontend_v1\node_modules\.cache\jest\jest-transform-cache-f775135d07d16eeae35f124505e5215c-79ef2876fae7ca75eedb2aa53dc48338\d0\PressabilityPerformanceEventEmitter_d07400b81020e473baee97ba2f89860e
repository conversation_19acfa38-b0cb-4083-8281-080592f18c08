1597de54e0452083463842c088c23869
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var PressabilityPerformanceEventEmitter = function () {
  function PressabilityPerformanceEventEmitter() {
    (0, _classCallCheck2.default)(this, PressabilityPerformanceEventEmitter);
    this._listeners = [];
  }
  return (0, _createClass2.default)(PressabilityPerformanceEventEmitter, [{
    key: "addListener",
    value: function addListener(listener) {
      this._listeners.push(listener);
    }
  }, {
    key: "removeListener",
    value: function removeListener(listener) {
      var index = this._listeners.indexOf(listener);
      if (index > -1) {
        this._listeners.splice(index, 1);
      }
    }
  }, {
    key: "emitEvent",
    value: function emitEvent(constructEvent) {
      if (this._listeners.length === 0) {
        return;
      }
      var event = constructEvent();
      this._listeners.forEach(function (listener) {
        return listener(event);
      });
    }
  }]);
}();
var PressabilityPerformanceEventEmitterSingleton = new PressabilityPerformanceEventEmitter();
var _default = exports.default = PressabilityPerformanceEventEmitterSingleton;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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