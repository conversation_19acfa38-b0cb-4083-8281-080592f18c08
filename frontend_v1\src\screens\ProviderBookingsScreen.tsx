/**
 * Provider Bookings Screen - Provider Booking Management
 *
 * Component Contract:
 * - Shows incoming bookings and booking requests for service providers
 * - Allows providers to confirm, reschedule, or cancel bookings
 * - Provides booking analytics and calendar view
 * - Follows responsive design and accessibility standards
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
  RefreshControl,
  FlatList,
} from 'react-native';

import { FocusScrollManager } from '../components/accessibility/FocusScrollManager';
import { Box } from '../components/atoms/Box';
import { Button } from '../components/atoms/Button';
import { Card } from '../components/atoms/Card';
import { HeaderHelpButton } from '../components/help';
import { SafeAreaScreen } from '../components/ui/SafeAreaWrapper';
import { useTheme } from '../contexts/ThemeContext';
import { useAuthStore } from '../store/authSlice';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
  getMinimumTouchTarget,
} from '../utils/responsiveUtils';

interface ProviderBooking {
  id: string;
  customerName: string;
  customerAvatar?: string;
  serviceName: string;
  date: string;
  time: string;
  duration: number;
  price: number;
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  notes?: string;
  paymentStatus: 'pending' | 'paid' | 'refunded';
  isNewCustomer?: boolean;
}

interface BookingStats {
  totalToday: number;
  pendingRequests: number;
  confirmedToday: number;
  todayRevenue: number;
}

export const ProviderBookingsScreen: React.FC = () => {
  const { colors } = useTheme();
  const navigation = useNavigation<any>();
  const { user } = useAuthStore();
  const styles = createStyles(colors);

  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState<
    'all' | 'pending' | 'today' | 'upcoming'
  >('all');

  // Mock bookings data
  const [bookings, setBookings] = useState<ProviderBooking[]>([
    {
      id: '1',
      customerName: 'Sarah Johnson',
      serviceName: 'Hair Cut & Style',
      date: '2025-01-16',
      time: '10:00 AM',
      duration: 60,
      price: 45,
      status: 'pending',
      notes: 'First time customer, prefers shorter length',
      paymentStatus: 'pending',
      isNewCustomer: true,
    },
    {
      id: '2',
      customerName: 'Emily Chen',
      serviceName: 'Hair Color',
      date: '2025-01-16',
      time: '2:00 PM',
      duration: 120,
      price: 85,
      status: 'confirmed',
      paymentStatus: 'paid',
      isNewCustomer: false,
    },
    {
      id: '3',
      customerName: 'Maria Rodriguez',
      serviceName: 'Manicure',
      date: '2025-01-17',
      time: '11:00 AM',
      duration: 45,
      price: 35,
      status: 'confirmed',
      paymentStatus: 'paid',
      isNewCustomer: false,
    },
  ]);

  const [stats, setStats] = useState<BookingStats>({
    totalToday: 3,
    pendingRequests: 1,
    confirmedToday: 2,
    todayRevenue: 165,
  });

  useEffect(() => {
    loadBookings();
  }, []);

  const loadBookings = async () => {
    setLoading(true);
    try {
      // TODO: Implement API call to load provider bookings
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
    } catch (error) {
      console.error('Error loading bookings:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadBookings();
    setRefreshing(false);
  };

  const handleConfirmBooking = (bookingId: string) => {
    Alert.alert(
      'Confirm Booking',
      'Are you sure you want to confirm this booking?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Confirm',
          onPress: () => {
            setBookings(prev =>
              prev.map(booking =>
                booking.id === bookingId
                  ? { ...booking, status: 'confirmed' }
                  : booking,
              ),
            );
          },
        },
      ],
    );
  };

  const handleCancelBooking = (bookingId: string) => {
    Alert.alert(
      'Cancel Booking',
      'Are you sure you want to cancel this booking?',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: () => {
            setBookings(prev =>
              prev.map(booking =>
                booking.id === bookingId
                  ? { ...booking, status: 'cancelled' }
                  : booking,
              ),
            );
          },
        },
      ],
    );
  };

  const handleBookingDetails = (bookingId: string) => {
    // Navigate to booking details
    Alert.alert('Booking Details', 'Booking details screen coming soon!');
  };

  const getStatusColor = (status: ProviderBooking['status']) => {
    switch (status) {
      case 'pending':
        return '#F59E0B';
      case 'confirmed':
        return '#10B981';
      case 'in_progress':
        return '#3B82F6';
      case 'completed':
        return '#6B7280';
      case 'cancelled':
        return '#EF4444';
      default:
        return colors.sage400;
    }
  };

  const filteredBookings = bookings.filter(booking => {
    switch (selectedFilter) {
      case 'pending':
        return booking.status === 'pending';
      case 'today':
        return booking.date === '2025-01-16';
      case 'upcoming':
        return ['pending', 'confirmed'].includes(booking.status);
      default:
        return true;
    }
  });

  const renderStatsCard = () => (
    <Card style={styles.statsCard}>
      <Text style={styles.sectionTitle}>Today's Overview</Text>
      <View style={styles.statsGrid}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{stats.totalToday}</Text>
          <Text style={styles.statLabel}>Total Bookings</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statValue, { color: '#F59E0B' }]}>
            {stats.pendingRequests}
          </Text>
          <Text style={styles.statLabel}>Pending</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statValue, { color: '#10B981' }]}>
            {stats.confirmedToday}
          </Text>
          <Text style={styles.statLabel}>Confirmed</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statValue, { color: colors.sage400 }]}>
            ${stats.todayRevenue}
          </Text>
          <Text style={styles.statLabel}>Revenue</Text>
        </View>
      </View>
    </Card>
  );

  const renderFilterTabs = () => (
    <View style={styles.filterContainer}>
      {[
        { id: 'all', label: 'All' },
        { id: 'pending', label: 'Pending' },
        { id: 'today', label: 'Today' },
        { id: 'upcoming', label: 'Upcoming' },
      ].map(filter => (
        <TouchableOpacity
          key={filter.id}
          style={[
            styles.filterTab,
            selectedFilter === filter.id && styles.activeFilterTab,
          ]}
          onPress={() => setSelectedFilter(filter.id as any)}>
          <Text
            style={[
              styles.filterText,
              selectedFilter === filter.id && styles.activeFilterText,
            ]}>
            {filter.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderBookingItem = ({ item }: { item: ProviderBooking }) => (
    <Card style={styles.bookingCard}>
      <TouchableOpacity
        style={styles.bookingContent}
        onPress={() => handleBookingDetails(item.id)}
        accessibilityLabel={`Booking with ${item.customerName}`}>
        <View style={styles.bookingHeader}>
          <View style={styles.customerInfo}>
            <View style={styles.avatarPlaceholder}>
              <Ionicons
                name="person-outline"
                size={20}
                color={colors.sage400}
              />
            </View>
            <View style={styles.customerDetails}>
              <Text style={styles.customerName}>{item.customerName}</Text>
              {item.isNewCustomer && (
                <Text style={styles.newCustomerBadge}>New Customer</Text>
              )}
            </View>
          </View>
          <View
            style={[
              styles.statusBadge,
              { backgroundColor: getStatusColor(item.status) + '20' },
            ]}>
            <Text
              style={[
                styles.statusText,
                { color: getStatusColor(item.status) },
              ]}>
              {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
            </Text>
          </View>
        </View>

        <View style={styles.bookingDetails}>
          <Text style={styles.serviceName}>{item.serviceName}</Text>
          <Text style={styles.bookingTime}>
            {item.date} at {item.time} • {item.duration}min • ${item.price}
          </Text>
          {item.notes && (
            <Text style={styles.bookingNotes} numberOfLines={2}>
              Note: {item.notes}
            </Text>
          )}
        </View>

        {item.status === 'pending' && (
          <View style={styles.actionButtons}>
            <Button
              title="Confirm"
              onPress={() => handleConfirmBooking(item.id)}
              variant="primary"
              size="small"
              style={styles.actionButton}
            />
            <Button
              title="Cancel"
              onPress={() => handleCancelBooking(item.id)}
              variant="outline"
              size="small"
              style={styles.actionButton}
            />
          </View>
        )}
      </TouchableOpacity>
    </Card>
  );

  return (
    <SafeAreaScreen style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.headerTitle}>Bookings</Text>
          <Text style={styles.headerSubtitle}>Manage your appointments</Text>
        </View>
        <View style={styles.headerRight}>
          <HeaderHelpButton />
        </View>
      </View>

      <FocusScrollManager
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
        stickyFooterHeight={80} // Account for tab bar
        focusPadding={20}>
        {/* Stats Overview */}
        {renderStatsCard()}

        {/* Filter Tabs */}
        {renderFilterTabs()}

        {/* Bookings List */}
        <FlatList
          data={filteredBookings}
          renderItem={renderBookingItem}
          keyExtractor={item => item.id}
          scrollEnabled={false}
          ListEmptyComponent={
            <View style={styles.emptyState}>
              <Ionicons
                name="calendar-outline"
                size={64}
                color={colors.sage200}
              />
              <Text style={styles.emptyStateText}>No bookings found</Text>
              <Text style={styles.emptyStateSubtext}>
                Your bookings will appear here
              </Text>
            </View>
          }
        />
      </FocusScrollManager>
    </SafeAreaScreen>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background?.primary || '#FFFFFF',
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: getResponsiveSpacing(20),
      paddingVertical: getResponsiveSpacing(16),
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.sage100,
    },
    headerLeft: {
      flex: 1,
    },
    headerTitle: {
      fontSize: getResponsiveFontSize(24),
      fontWeight: '700',
      color: colors.text,
      marginBottom: 4,
    },
    headerSubtitle: {
      fontSize: getResponsiveFontSize(14),
      color: colors.sage400,
    },
    headerRight: {
      marginLeft: getResponsiveSpacing(16),
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      padding: getResponsiveSpacing(20),
      paddingBottom: getResponsiveSpacing(100),
    },
    statsCard: {
      marginBottom: getResponsiveSpacing(20),
    },
    sectionTitle: {
      fontSize: getResponsiveFontSize(18),
      fontWeight: '600',
      color: colors.text,
      marginBottom: getResponsiveSpacing(16),
    },
    statsGrid: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    statItem: {
      alignItems: 'center',
    },
    statValue: {
      fontSize: getResponsiveFontSize(24),
      fontWeight: '700',
      color: colors.text,
      marginBottom: 4,
    },
    statLabel: {
      fontSize: getResponsiveFontSize(12),
      color: colors.sage400,
      textAlign: 'center',
    },
    filterContainer: {
      flexDirection: 'row',
      marginBottom: getResponsiveSpacing(20),
      backgroundColor: colors.sage50,
      borderRadius: 12,
      padding: 4,
    },
    filterTab: {
      flex: 1,
      paddingVertical: getResponsiveSpacing(12),
      alignItems: 'center',
      borderRadius: 8,
    },
    activeFilterTab: {
      backgroundColor: colors.surface,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    filterText: {
      fontSize: getResponsiveFontSize(14),
      fontWeight: '500',
      color: colors.sage400,
    },
    activeFilterText: {
      color: colors.text,
      fontWeight: '600',
    },
    bookingCard: {
      marginBottom: getResponsiveSpacing(12),
    },
    bookingContent: {
      padding: getResponsiveSpacing(16),
    },
    bookingHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: getResponsiveSpacing(12),
    },
    customerInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    avatarPlaceholder: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.sage100,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: getResponsiveSpacing(12),
    },
    customerDetails: {
      flex: 1,
    },
    customerName: {
      fontSize: getResponsiveFontSize(16),
      fontWeight: '600',
      color: colors.text,
      marginBottom: 2,
    },
    newCustomerBadge: {
      fontSize: getResponsiveFontSize(12),
      color: '#10B981',
      fontWeight: '500',
    },
    statusBadge: {
      paddingHorizontal: getResponsiveSpacing(12),
      paddingVertical: getResponsiveSpacing(6),
      borderRadius: 16,
    },
    statusText: {
      fontSize: getResponsiveFontSize(12),
      fontWeight: '600',
    },
    bookingDetails: {
      marginBottom: getResponsiveSpacing(16),
    },
    serviceName: {
      fontSize: getResponsiveFontSize(16),
      fontWeight: '600',
      color: colors.text,
      marginBottom: 4,
    },
    bookingTime: {
      fontSize: getResponsiveFontSize(14),
      color: colors.sage400,
      marginBottom: 4,
    },
    bookingNotes: {
      fontSize: getResponsiveFontSize(14),
      color: colors.sage500,
      fontStyle: 'italic',
    },
    actionButtons: {
      flexDirection: 'row',
      gap: getResponsiveSpacing(12),
    },
    actionButton: {
      flex: 1,
    },
    emptyState: {
      alignItems: 'center',
      paddingVertical: getResponsiveSpacing(40),
    },
    emptyStateText: {
      fontSize: getResponsiveFontSize(18),
      fontWeight: '600',
      color: colors.sage400,
      marginTop: getResponsiveSpacing(16),
      marginBottom: 8,
    },
    emptyStateSubtext: {
      fontSize: getResponsiveFontSize(14),
      color: colors.sage300,
      textAlign: 'center',
    },
  });
