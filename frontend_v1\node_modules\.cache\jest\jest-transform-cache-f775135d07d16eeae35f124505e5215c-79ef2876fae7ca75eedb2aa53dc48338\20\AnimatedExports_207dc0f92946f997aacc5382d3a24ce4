74f23c755eb85e0fb97d3e97dc538857
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _Platform = _interopRequireDefault(require("../Utilities/Platform"));
var _AnimatedImplementation = _interopRequireDefault(require("./AnimatedImplementation"));
var _AnimatedMock = _interopRequireDefault(require("./AnimatedMock"));
var Animated = _Platform.default.isDisableAnimations ? _AnimatedMock.default : _AnimatedImplementation.default;
var _default = exports.default = Object.assign({
  get FlatList() {
    return require("./components/AnimatedFlatList").default;
  },
  get Image() {
    return require("./components/AnimatedImage").default;
  },
  get ScrollView() {
    return require("./components/AnimatedScrollView").default;
  },
  get SectionList() {
    return require("./components/AnimatedSectionList").default;
  },
  get Text() {
    return require("./components/AnimatedText").default;
  },
  get View() {
    return require("./components/AnimatedView").default;
  }
}, Animated);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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