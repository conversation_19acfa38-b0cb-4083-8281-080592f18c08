{"version": 3, "names": ["asyncStorageMock", "getItem", "jest", "fn", "Promise", "resolve", "setItem", "removeItem", "clear", "getAllKeys", "multiGet", "multiSet", "multiRemove", "_default", "exports", "default"], "sources": ["asyncStorage.js"], "sourcesContent": ["/**\n * AsyncStorage Mock - Mock implementation for testing\n */\n\nconst asyncStorageMock = {\n  getItem: jest.fn(() => Promise.resolve(null)),\n  setItem: jest.fn(() => Promise.resolve()),\n  removeItem: jest.fn(() => Promise.resolve()),\n  clear: jest.fn(() => Promise.resolve()),\n  getAllKeys: jest.fn(() => Promise.resolve([])),\n  multiGet: jest.fn(() => Promise.resolve([])),\n  multiSet: jest.fn(() => Promise.resolve()),\n  multiRemove: jest.fn(() => Promise.resolve()),\n};\n\nexport default asyncStorageMock;\n"], "mappings": ";;;;AAIA,IAAMA,gBAAgB,GAAG;EACvBC,OAAO,EAAEC,IAAI,CAACC,EAAE,CAAC;IAAA,OAAMC,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC;EAAA,EAAC;EAC7CC,OAAO,EAAEJ,IAAI,CAACC,EAAE,CAAC;IAAA,OAAMC,OAAO,CAACC,OAAO,CAAC,CAAC;EAAA,EAAC;EACzCE,UAAU,EAAEL,IAAI,CAACC,EAAE,CAAC;IAAA,OAAMC,OAAO,CAACC,OAAO,CAAC,CAAC;EAAA,EAAC;EAC5CG,KAAK,EAAEN,IAAI,CAACC,EAAE,CAAC;IAAA,OAAMC,OAAO,CAACC,OAAO,CAAC,CAAC;EAAA,EAAC;EACvCI,UAAU,EAAEP,IAAI,CAACC,EAAE,CAAC;IAAA,OAAMC,OAAO,CAACC,OAAO,CAAC,EAAE,CAAC;EAAA,EAAC;EAC9CK,QAAQ,EAAER,IAAI,CAACC,EAAE,CAAC;IAAA,OAAMC,OAAO,CAACC,OAAO,CAAC,EAAE,CAAC;EAAA,EAAC;EAC5CM,QAAQ,EAAET,IAAI,CAACC,EAAE,CAAC;IAAA,OAAMC,OAAO,CAACC,OAAO,CAAC,CAAC;EAAA,EAAC;EAC1CO,WAAW,EAAEV,IAAI,CAACC,EAAE,CAAC;IAAA,OAAMC,OAAO,CAACC,OAAO,CAAC,CAAC;EAAA;AAC9C,CAAC;AAAC,IAAAQ,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEaf,gBAAgB", "ignoreList": []}