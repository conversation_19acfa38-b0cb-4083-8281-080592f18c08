{"version": 3, "names": ["MockNativeMethods", "measure", "jest", "fn", "measureInWindow", "measureLayout", "setNativeProps", "focus", "blur", "module", "exports"], "sources": ["MockNativeMethods.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\n'use strict';\n\nconst MockNativeMethods = {\n  measure: jest.fn(),\n  measureInWindow: jest.fn(),\n  measureLayout: jest.fn(),\n  setNativeProps: jest.fn(),\n  focus: jest.fn(),\n  blur: jest.fn(),\n};\n\nmodule.exports = MockNativeMethods;\n"], "mappings": "AASA,YAAY;;AAEZ,IAAMA,iBAAiB,GAAG;EACxBC,OAAO,EAAEC,IAAI,CAACC,EAAE,CAAC,CAAC;EAClBC,eAAe,EAAEF,IAAI,CAACC,EAAE,CAAC,CAAC;EAC1BE,aAAa,EAAEH,IAAI,CAACC,EAAE,CAAC,CAAC;EACxBG,cAAc,EAAEJ,IAAI,CAACC,EAAE,CAAC,CAAC;EACzBI,KAAK,EAAEL,IAAI,CAACC,EAAE,CAAC,CAAC;EAChBK,IAAI,EAAEN,IAAI,CAACC,EAAE,CAAC;AAChB,CAAC;AAEDM,MAAM,CAACC,OAAO,GAAGV,iBAAiB", "ignoreList": []}