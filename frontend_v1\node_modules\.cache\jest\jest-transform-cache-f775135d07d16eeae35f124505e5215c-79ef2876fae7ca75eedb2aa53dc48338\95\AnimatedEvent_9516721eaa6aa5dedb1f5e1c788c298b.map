{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "AnimatedEvent", "attachNativeEvent", "_classCallCheck2", "_createClass2", "_NativeAnimatedHelper", "_RendererProxy", "_AnimatedValue", "_AnimatedValueXY", "_invariant", "viewRef", "eventName", "arg<PERSON><PERSON><PERSON>", "platformConfig", "eventMappings", "traverse", "path", "AnimatedValue", "__makeNative", "push", "nativeEventPath", "animatedValueTag", "__getNativeTag", "AnimatedValueXY", "x", "concat", "y", "key", "invariant", "nativeEvent", "viewTag", "findNodeHandle", "for<PERSON>ach", "mapping", "NativeAnimatedHelper", "API", "addAnimatedEventToView", "detach", "removeAnimatedEventFromView", "validateMapping", "args", "validate", "recMapping", "recEvt", "mappingKey", "length", "idx", "config", "_this", "default", "_listeners", "_callListeners", "_len", "arguments", "Array", "_key2", "listener", "apply", "_argMapping", "console", "warn", "useNativeDriver", "__addListener", "_attachedEvent", "__isNative", "shouldUseNativeDriver", "__platformConfig", "callback", "__removeListener", "filter", "__attach", "__detach", "__<PERSON><PERSON><PERSON><PERSON>", "_this2", "__DEV__", "validatedMapping", "_len2", "_key3", "_len3", "_key4", "setValue"], "sources": ["AnimatedEvent.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n */\n\n'use strict';\n\nimport type {PlatformConfig} from './AnimatedPlatformConfig';\n\nimport NativeAnimatedHelper from '../../src/private/animated/NativeAnimatedHelper';\nimport {findNodeHandle} from '../ReactNative/RendererProxy';\nimport AnimatedValue from './nodes/AnimatedValue';\nimport AnimatedValueXY from './nodes/AnimatedValueXY';\nimport invariant from 'invariant';\n\nexport type Mapping =\n  | {[key: string]: Mapping, ...}\n  | AnimatedValue\n  | AnimatedValueXY;\nexport type EventConfig = {\n  listener?: ?Function,\n  useNativeDriver: boolean,\n  platformConfig?: PlatformConfig,\n};\n\nexport function attachNativeEvent(\n  viewRef: any,\n  eventName: string,\n  argMapping: $ReadOnlyArray<?Mapping>,\n  platformConfig: ?PlatformConfig,\n): {detach: () => void} {\n  // Find animated values in `argMapping` and create an array representing their\n  // key path inside the `nativeEvent` object. Ex.: ['contentOffset', 'x'].\n  const eventMappings = [];\n\n  const traverse = (value: mixed, path: Array<string>) => {\n    if (value instanceof AnimatedValue) {\n      value.__makeNative(platformConfig);\n\n      eventMappings.push({\n        nativeEventPath: path,\n        animatedValueTag: value.__getNativeTag(),\n      });\n    } else if (value instanceof AnimatedValueXY) {\n      traverse(value.x, path.concat('x'));\n      traverse(value.y, path.concat('y'));\n    } else if (typeof value === 'object') {\n      for (const key in value) {\n        traverse(value[key], path.concat(key));\n      }\n    }\n  };\n\n  invariant(\n    argMapping[0] && argMapping[0].nativeEvent,\n    'Native driven events only support animated values contained inside `nativeEvent`.',\n  );\n\n  // Assume that the event containing `nativeEvent` is always the first argument.\n  traverse(argMapping[0].nativeEvent, []);\n\n  const viewTag = findNodeHandle(viewRef);\n  if (viewTag != null) {\n    eventMappings.forEach(mapping => {\n      NativeAnimatedHelper.API.addAnimatedEventToView(\n        viewTag,\n        eventName,\n        mapping,\n      );\n    });\n  }\n\n  return {\n    detach() {\n      if (viewTag != null) {\n        eventMappings.forEach(mapping => {\n          NativeAnimatedHelper.API.removeAnimatedEventFromView(\n            viewTag,\n            eventName,\n            // $FlowFixMe[incompatible-call]\n            mapping.animatedValueTag,\n          );\n        });\n      }\n    },\n  };\n}\n\nfunction validateMapping(argMapping: $ReadOnlyArray<?Mapping>, args: any) {\n  const validate = (recMapping: ?Mapping, recEvt: any, key: string) => {\n    if (recMapping instanceof AnimatedValue) {\n      invariant(\n        typeof recEvt === 'number',\n        'Bad mapping of event key ' +\n          key +\n          ', should be number but got ' +\n          typeof recEvt,\n      );\n      return;\n    }\n    if (recMapping instanceof AnimatedValueXY) {\n      invariant(\n        typeof recEvt.x === 'number' && typeof recEvt.y === 'number',\n        'Bad mapping of event key ' + key + ', should be XY but got ' + recEvt,\n      );\n      return;\n    }\n    if (typeof recEvt === 'number') {\n      invariant(\n        recMapping instanceof AnimatedValue,\n        'Bad mapping of type ' +\n          typeof recMapping +\n          ' for key ' +\n          key +\n          ', event value must map to AnimatedValue',\n      );\n      return;\n    }\n    invariant(\n      typeof recMapping === 'object',\n      'Bad mapping of type ' + typeof recMapping + ' for key ' + key,\n    );\n    invariant(\n      typeof recEvt === 'object',\n      'Bad event of type ' + typeof recEvt + ' for key ' + key,\n    );\n    for (const mappingKey in recMapping) {\n      validate(recMapping[mappingKey], recEvt[mappingKey], mappingKey);\n    }\n  };\n\n  invariant(\n    args.length >= argMapping.length,\n    'Event has less arguments than mapping',\n  );\n  argMapping.forEach((mapping, idx) => {\n    validate(mapping, args[idx], 'arg' + idx);\n  });\n}\n\nexport class AnimatedEvent {\n  _argMapping: $ReadOnlyArray<?Mapping>;\n  _listeners: Array<Function> = [];\n  _attachedEvent: ?{detach: () => void, ...};\n  __isNative: boolean;\n  __platformConfig: ?PlatformConfig;\n\n  constructor(argMapping: $ReadOnlyArray<?Mapping>, config: EventConfig) {\n    this._argMapping = argMapping;\n\n    if (config == null) {\n      console.warn('Animated.event now requires a second argument for options');\n      config = {useNativeDriver: false};\n    }\n\n    if (config.listener) {\n      this.__addListener(config.listener);\n    }\n    this._attachedEvent = null;\n    this.__isNative = NativeAnimatedHelper.shouldUseNativeDriver(config);\n    this.__platformConfig = config.platformConfig;\n  }\n\n  __addListener(callback: Function): void {\n    this._listeners.push(callback);\n  }\n\n  __removeListener(callback: Function): void {\n    this._listeners = this._listeners.filter(listener => listener !== callback);\n  }\n\n  __attach(viewRef: any, eventName: string): void {\n    invariant(\n      this.__isNative,\n      'Only native driven events need to be attached.',\n    );\n\n    this._attachedEvent = attachNativeEvent(\n      viewRef,\n      eventName,\n      this._argMapping,\n      this.__platformConfig,\n    );\n  }\n\n  __detach(viewTag: any, eventName: string): void {\n    invariant(\n      this.__isNative,\n      'Only native driven events need to be detached.',\n    );\n\n    this._attachedEvent && this._attachedEvent.detach();\n  }\n\n  __getHandler(): any | ((...args: any) => void) {\n    if (this.__isNative) {\n      if (__DEV__) {\n        let validatedMapping = false;\n        return (...args: any) => {\n          if (!validatedMapping) {\n            validateMapping(this._argMapping, args);\n            validatedMapping = true;\n          }\n          this._callListeners(...args);\n        };\n      } else {\n        return this._callListeners;\n      }\n    }\n\n    let validatedMapping = false;\n    return (...args: any) => {\n      if (__DEV__ && !validatedMapping) {\n        validateMapping(this._argMapping, args);\n        validatedMapping = true;\n      }\n\n      const traverse = (\n        recMapping: ?(Mapping | AnimatedValue),\n        recEvt: any,\n      ) => {\n        if (recMapping instanceof AnimatedValue) {\n          if (typeof recEvt === 'number') {\n            recMapping.setValue(recEvt);\n          }\n        } else if (recMapping instanceof AnimatedValueXY) {\n          if (typeof recEvt === 'object') {\n            traverse(recMapping.x, recEvt.x);\n            traverse(recMapping.y, recEvt.y);\n          }\n        } else if (typeof recMapping === 'object') {\n          for (const mappingKey in recMapping) {\n            /* $FlowFixMe[prop-missing] (>=0.120.0) This comment suppresses an\n             * error found when Flow v0.120 was deployed. To see the error,\n             * delete this comment and run Flow. */\n            traverse(recMapping[mappingKey], recEvt[mappingKey]);\n          }\n        }\n      };\n      this._argMapping.forEach((mapping, idx) => {\n        traverse(mapping, args[idx]);\n      });\n\n      this._callListeners(...args);\n    };\n  }\n\n  _callListeners = (...args: any) => {\n    this._listeners.forEach(listener => listener(...args));\n  };\n}\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,aAAA;AAAAF,OAAA,CAAAG,iBAAA,GAAAA,iBAAA;AAAA,IAAAC,gBAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,aAAA,GAAAT,sBAAA,CAAAC,OAAA;AAIb,IAAAS,qBAAA,GAAAV,sBAAA,CAAAC,OAAA;AACA,IAAAU,cAAA,GAAAV,OAAA;AACA,IAAAW,cAAA,GAAAZ,sBAAA,CAAAC,OAAA;AACA,IAAAY,gBAAA,GAAAb,sBAAA,CAAAC,OAAA;AACA,IAAAa,UAAA,GAAAd,sBAAA,CAAAC,OAAA;AAYO,SAASM,iBAAiBA,CAC/BQ,OAAY,EACZC,SAAiB,EACjBC,UAAoC,EACpCC,cAA+B,EACT;EAGtB,IAAMC,aAAa,GAAG,EAAE;EAExB,IAAMC,SAAQ,GAAG,SAAXA,QAAQA,CAAIf,KAAY,EAAEgB,IAAmB,EAAK;IACtD,IAAIhB,KAAK,YAAYiB,sBAAa,EAAE;MAClCjB,KAAK,CAACkB,YAAY,CAACL,cAAc,CAAC;MAElCC,aAAa,CAACK,IAAI,CAAC;QACjBC,eAAe,EAAEJ,IAAI;QACrBK,gBAAgB,EAAErB,KAAK,CAACsB,cAAc,CAAC;MACzC,CAAC,CAAC;IACJ,CAAC,MAAM,IAAItB,KAAK,YAAYuB,wBAAe,EAAE;MAC3CR,SAAQ,CAACf,KAAK,CAACwB,CAAC,EAAER,IAAI,CAACS,MAAM,CAAC,GAAG,CAAC,CAAC;MACnCV,SAAQ,CAACf,KAAK,CAAC0B,CAAC,EAAEV,IAAI,CAACS,MAAM,CAAC,GAAG,CAAC,CAAC;IACrC,CAAC,MAAM,IAAI,OAAOzB,KAAK,KAAK,QAAQ,EAAE;MACpC,KAAK,IAAM2B,IAAG,IAAI3B,KAAK,EAAE;QACvBe,SAAQ,CAACf,KAAK,CAAC2B,IAAG,CAAC,EAAEX,IAAI,CAACS,MAAM,CAACE,IAAG,CAAC,CAAC;MACxC;IACF;EACF,CAAC;EAED,IAAAC,kBAAS,EACPhB,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,CAACiB,WAAW,EAC1C,mFACF,CAAC;EAGDd,SAAQ,CAACH,UAAU,CAAC,CAAC,CAAC,CAACiB,WAAW,EAAE,EAAE,CAAC;EAEvC,IAAMC,OAAO,GAAG,IAAAC,6BAAc,EAACrB,OAAO,CAAC;EACvC,IAAIoB,OAAO,IAAI,IAAI,EAAE;IACnBhB,aAAa,CAACkB,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC/BC,6BAAoB,CAACC,GAAG,CAACC,sBAAsB,CAC7CN,OAAO,EACPnB,SAAS,EACTsB,OACF,CAAC;IACH,CAAC,CAAC;EACJ;EAEA,OAAO;IACLI,MAAM,WAANA,MAAMA,CAAA,EAAG;MACP,IAAIP,OAAO,IAAI,IAAI,EAAE;QACnBhB,aAAa,CAACkB,OAAO,CAAC,UAAAC,OAAO,EAAI;UAC/BC,6BAAoB,CAACC,GAAG,CAACG,2BAA2B,CAClDR,OAAO,EACPnB,SAAS,EAETsB,OAAO,CAACZ,gBACV,CAAC;QACH,CAAC,CAAC;MACJ;IACF;EACF,CAAC;AACH;AAEA,SAASkB,eAAeA,CAAC3B,UAAoC,EAAE4B,IAAS,EAAE;EACxE,IAAMC,SAAQ,GAAG,SAAXA,QAAQA,CAAIC,UAAoB,EAAEC,MAAW,EAAEhB,GAAW,EAAK;IACnE,IAAIe,UAAU,YAAYzB,sBAAa,EAAE;MACvC,IAAAW,kBAAS,EACP,OAAOe,MAAM,KAAK,QAAQ,EAC1B,2BAA2B,GACzBhB,GAAG,GACH,6BAA6B,GAC7B,OAAOgB,MACX,CAAC;MACD;IACF;IACA,IAAID,UAAU,YAAYnB,wBAAe,EAAE;MACzC,IAAAK,kBAAS,EACP,OAAOe,MAAM,CAACnB,CAAC,KAAK,QAAQ,IAAI,OAAOmB,MAAM,CAACjB,CAAC,KAAK,QAAQ,EAC5D,2BAA2B,GAAGC,GAAG,GAAG,yBAAyB,GAAGgB,MAClE,CAAC;MACD;IACF;IACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC9B,IAAAf,kBAAS,EACPc,UAAU,YAAYzB,sBAAa,EACnC,sBAAsB,GACpB,OAAOyB,UAAU,GACjB,WAAW,GACXf,GAAG,GACH,yCACJ,CAAC;MACD;IACF;IACA,IAAAC,kBAAS,EACP,OAAOc,UAAU,KAAK,QAAQ,EAC9B,sBAAsB,GAAG,OAAOA,UAAU,GAAG,WAAW,GAAGf,GAC7D,CAAC;IACD,IAAAC,kBAAS,EACP,OAAOe,MAAM,KAAK,QAAQ,EAC1B,oBAAoB,GAAG,OAAOA,MAAM,GAAG,WAAW,GAAGhB,GACvD,CAAC;IACD,KAAK,IAAMiB,UAAU,IAAIF,UAAU,EAAE;MACnCD,SAAQ,CAACC,UAAU,CAACE,UAAU,CAAC,EAAED,MAAM,CAACC,UAAU,CAAC,EAAEA,UAAU,CAAC;IAClE;EACF,CAAC;EAED,IAAAhB,kBAAS,EACPY,IAAI,CAACK,MAAM,IAAIjC,UAAU,CAACiC,MAAM,EAChC,uCACF,CAAC;EACDjC,UAAU,CAACoB,OAAO,CAAC,UAACC,OAAO,EAAEa,GAAG,EAAK;IACnCL,SAAQ,CAACR,OAAO,EAAEO,IAAI,CAACM,GAAG,CAAC,EAAE,KAAK,GAAGA,GAAG,CAAC;EAC3C,CAAC,CAAC;AACJ;AAAC,IAEY7C,aAAa,GAAAF,OAAA,CAAAE,aAAA;EAOxB,SAAAA,cAAYW,UAAoC,EAAEmC,MAAmB,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAA7C,gBAAA,CAAA8C,OAAA,QAAAhD,aAAA;IAAA,KALvEiD,UAAU,GAAoB,EAAE;IAAA,KAyGhCC,cAAc,GAAG,YAAkB;MAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAR,MAAA,EAAdL,IAAI,OAAAc,KAAA,CAAAF,IAAA,GAAAG,KAAA,MAAAA,KAAA,GAAAH,IAAA,EAAAG,KAAA;QAAJf,IAAI,CAAAe,KAAA,IAAAF,SAAA,CAAAE,KAAA;MAAA;MACvBP,KAAI,CAACE,UAAU,CAAClB,OAAO,CAAC,UAAAwB,QAAQ;QAAA,OAAIA,QAAQ,CAAAC,KAAA,SAAIjB,IAAI,CAAC;MAAA,EAAC;IACxD,CAAC;IArGC,IAAI,CAACkB,WAAW,GAAG9C,UAAU;IAE7B,IAAImC,MAAM,IAAI,IAAI,EAAE;MAClBY,OAAO,CAACC,IAAI,CAAC,2DAA2D,CAAC;MACzEb,MAAM,GAAG;QAACc,eAAe,EAAE;MAAK,CAAC;IACnC;IAEA,IAAId,MAAM,CAACS,QAAQ,EAAE;MACnB,IAAI,CAACM,aAAa,CAACf,MAAM,CAACS,QAAQ,CAAC;IACrC;IACA,IAAI,CAACO,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,UAAU,GAAG9B,6BAAoB,CAAC+B,qBAAqB,CAAClB,MAAM,CAAC;IACpE,IAAI,CAACmB,gBAAgB,GAAGnB,MAAM,CAAClC,cAAc;EAC/C;EAAC,WAAAT,aAAA,CAAA6C,OAAA,EAAAhD,aAAA;IAAA0B,GAAA;IAAA3B,KAAA,EAED,SAAA8D,aAAaA,CAACK,QAAkB,EAAQ;MACtC,IAAI,CAACjB,UAAU,CAAC/B,IAAI,CAACgD,QAAQ,CAAC;IAChC;EAAC;IAAAxC,GAAA;IAAA3B,KAAA,EAED,SAAAoE,gBAAgBA,CAACD,QAAkB,EAAQ;MACzC,IAAI,CAACjB,UAAU,GAAG,IAAI,CAACA,UAAU,CAACmB,MAAM,CAAC,UAAAb,QAAQ;QAAA,OAAIA,QAAQ,KAAKW,QAAQ;MAAA,EAAC;IAC7E;EAAC;IAAAxC,GAAA;IAAA3B,KAAA,EAED,SAAAsE,QAAQA,CAAC5D,OAAY,EAAEC,SAAiB,EAAQ;MAC9C,IAAAiB,kBAAS,EACP,IAAI,CAACoC,UAAU,EACf,gDACF,CAAC;MAED,IAAI,CAACD,cAAc,GAAG7D,iBAAiB,CACrCQ,OAAO,EACPC,SAAS,EACT,IAAI,CAAC+C,WAAW,EAChB,IAAI,CAACQ,gBACP,CAAC;IACH;EAAC;IAAAvC,GAAA;IAAA3B,KAAA,EAED,SAAAuE,QAAQA,CAACzC,OAAY,EAAEnB,SAAiB,EAAQ;MAC9C,IAAAiB,kBAAS,EACP,IAAI,CAACoC,UAAU,EACf,gDACF,CAAC;MAED,IAAI,CAACD,cAAc,IAAI,IAAI,CAACA,cAAc,CAAC1B,MAAM,CAAC,CAAC;IACrD;EAAC;IAAAV,GAAA;IAAA3B,KAAA,EAED,SAAAwE,YAAYA,CAAA,EAAmC;MAAA,IAAAC,MAAA;MAC7C,IAAI,IAAI,CAACT,UAAU,EAAE;QACnB,IAAIU,OAAO,EAAE;UACX,IAAIC,iBAAgB,GAAG,KAAK;UAC5B,OAAO,YAAkB;YAAA,SAAAC,KAAA,GAAAvB,SAAA,CAAAR,MAAA,EAAdL,IAAI,OAAAc,KAAA,CAAAsB,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;cAAJrC,IAAI,CAAAqC,KAAA,IAAAxB,SAAA,CAAAwB,KAAA;YAAA;YACb,IAAI,CAACF,iBAAgB,EAAE;cACrBpC,eAAe,CAACkC,MAAI,CAACf,WAAW,EAAElB,IAAI,CAAC;cACvCmC,iBAAgB,GAAG,IAAI;YACzB;YACAF,MAAI,CAACtB,cAAc,CAAAM,KAAA,CAAnBgB,MAAI,EAAmBjC,IAAI,CAAC;UAC9B,CAAC;QACH,CAAC,MAAM;UACL,OAAO,IAAI,CAACW,cAAc;QAC5B;MACF;MAEA,IAAIwB,gBAAgB,GAAG,KAAK;MAC5B,OAAO,YAAkB;QAAA,SAAAG,KAAA,GAAAzB,SAAA,CAAAR,MAAA,EAAdL,IAAI,OAAAc,KAAA,CAAAwB,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;UAAJvC,IAAI,CAAAuC,KAAA,IAAA1B,SAAA,CAAA0B,KAAA;QAAA;QACb,IAAIL,OAAO,IAAI,CAACC,gBAAgB,EAAE;UAChCpC,eAAe,CAACkC,MAAI,CAACf,WAAW,EAAElB,IAAI,CAAC;UACvCmC,gBAAgB,GAAG,IAAI;QACzB;QAEA,IAAM5D,UAAQ,GAAG,SAAXA,QAAQA,CACZ2B,UAAsC,EACtCC,MAAW,EACR;UACH,IAAID,UAAU,YAAYzB,sBAAa,EAAE;YACvC,IAAI,OAAO0B,MAAM,KAAK,QAAQ,EAAE;cAC9BD,UAAU,CAACsC,QAAQ,CAACrC,MAAM,CAAC;YAC7B;UACF,CAAC,MAAM,IAAID,UAAU,YAAYnB,wBAAe,EAAE;YAChD,IAAI,OAAOoB,MAAM,KAAK,QAAQ,EAAE;cAC9B5B,UAAQ,CAAC2B,UAAU,CAAClB,CAAC,EAAEmB,MAAM,CAACnB,CAAC,CAAC;cAChCT,UAAQ,CAAC2B,UAAU,CAAChB,CAAC,EAAEiB,MAAM,CAACjB,CAAC,CAAC;YAClC;UACF,CAAC,MAAM,IAAI,OAAOgB,UAAU,KAAK,QAAQ,EAAE;YACzC,KAAK,IAAME,UAAU,IAAIF,UAAU,EAAE;cAInC3B,UAAQ,CAAC2B,UAAU,CAACE,UAAU,CAAC,EAAED,MAAM,CAACC,UAAU,CAAC,CAAC;YACtD;UACF;QACF,CAAC;QACD6B,MAAI,CAACf,WAAW,CAAC1B,OAAO,CAAC,UAACC,OAAO,EAAEa,GAAG,EAAK;UACzC/B,UAAQ,CAACkB,OAAO,EAAEO,IAAI,CAACM,GAAG,CAAC,CAAC;QAC9B,CAAC,CAAC;QAEF2B,MAAI,CAACtB,cAAc,CAAAM,KAAA,CAAnBgB,MAAI,EAAmBjC,IAAI,CAAC;MAC9B,CAAC;IACH;EAAC;AAAA", "ignoreList": []}