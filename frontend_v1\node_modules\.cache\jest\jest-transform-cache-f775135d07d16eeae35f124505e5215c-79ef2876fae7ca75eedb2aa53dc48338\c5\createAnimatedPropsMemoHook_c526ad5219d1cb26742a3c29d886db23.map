{"version": 3, "names": ["_AnimatedEvent", "require", "_AnimatedNode", "_interopRequireDefault", "_AnimatedObject", "_flattenStyle", "ReactNativeFeatureFlags", "_interopRequireWildcard", "_nullthrows", "_react", "_Object$hasOwn", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "createAnimatedPropsMemoHook", "allowlist", "useAnimatedPropsMemo", "create", "props", "useAnimatedPropsImpl", "avoidStateUpdateInAnimatedPropsMemo", "useAnimatedPropsMemo_ref", "useAnimatedPropsMemo_state", "compositeKey", "useMemo", "createCompositeKeyForProps", "prevRef", "useRef", "prev", "current", "next", "areCompositeKeysEqual", "node", "useInsertionEffect", "_useState", "useState", "value", "_useState2", "_slicedToArray2", "state", "setState", "keys", "ii", "length", "key", "hasOwn", "compositeKeyComponent", "flatStyle", "flattenStyle", "createCompositeKeyForObject", "style", "AnimatedNode", "AnimatedEvent", "Array", "isArray", "createCompositeKeyForArray", "isPlainObject", "array", "fill", "object", "<PERSON><PERSON><PERSON><PERSON>", "maybeNext", "prevComponent", "nextComponent", "areCompositeKeyComponentsEqual", "nullthrows", "_hasOwnProp", "prototype", "obj", "prop"], "sources": ["createAnimatedPropsMemoHook.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n * @oncall react_native\n */\n\nimport type AnimatedProps from '../../../Libraries/Animated/nodes/AnimatedProps';\nimport type {AnimatedPropsAllowlist} from '../../../Libraries/Animated/nodes/AnimatedProps';\nimport type {AnimatedStyleAllowlist} from '../../../Libraries/Animated/nodes/AnimatedStyle';\n\nimport {AnimatedEvent} from '../../../Libraries/Animated/AnimatedEvent';\nimport AnimatedNode from '../../../Libraries/Animated/nodes/AnimatedNode';\nimport {isPlainObject} from '../../../Libraries/Animated/nodes/AnimatedObject';\nimport flattenStyle from '../../../Libraries/StyleSheet/flattenStyle';\nimport * as ReactNativeFeatureFlags from '../featureflags/ReactNativeFeatureFlags';\nimport nullthrows from 'nullthrows';\nimport {useInsertionEffect, useMemo, useRef, useState} from 'react';\n\ntype CompositeKey = {\n  style?: {[string]: CompositeKeyComponent},\n  [string]:\n    | CompositeKeyComponent\n    | AnimatedEvent\n    | $ReadOnlyArray<mixed>\n    | $ReadOnly<{[string]: mixed}>,\n};\n\ntype CompositeKeyComponent =\n  | AnimatedNode\n  | Array<CompositeKeyComponent | null>\n  | {[string]: CompositeKeyComponent};\n\ntype $ReadOnlyCompositeKey = $ReadOnly<{\n  style?: $ReadOnly<{[string]: CompositeKeyComponent}>,\n  [string]:\n    | $ReadOnlyCompositeKeyComponent\n    | AnimatedEvent\n    | $ReadOnlyArray<mixed>\n    | $ReadOnly<{[string]: mixed}>,\n}>;\n\ntype $ReadOnlyCompositeKeyComponent =\n  | AnimatedNode\n  | $ReadOnlyArray<$ReadOnlyCompositeKeyComponent | null>\n  | $ReadOnly<{[string]: $ReadOnlyCompositeKeyComponent}>;\n\ntype AnimatedPropsMemoHook = (\n  () => AnimatedProps,\n  props: $ReadOnly<{[string]: mixed}>,\n) => AnimatedProps;\n\n/**\n * Creates a hook that returns an `AnimatedProps` object that is memoized based\n * on the subset of `props` that are instances of `AnimatedNode` or\n * `AnimatedEvent`.\n */\nexport function createAnimatedPropsMemoHook(\n  allowlist: ?AnimatedPropsAllowlist,\n): AnimatedPropsMemoHook {\n  return function useAnimatedPropsMemo(\n    create: () => AnimatedProps,\n    props: $ReadOnly<{[string]: mixed}>,\n  ): AnimatedProps {\n    // NOTE: This feature flag must be evaluated inside the hook because this\n    // module factory can be evaluated much sooner, before overrides are set.\n    const useAnimatedPropsImpl =\n      ReactNativeFeatureFlags.avoidStateUpdateInAnimatedPropsMemo()\n        ? useAnimatedPropsMemo_ref\n        : useAnimatedPropsMemo_state;\n    return useAnimatedPropsImpl(create, props);\n  };\n\n  function useAnimatedPropsMemo_ref(\n    create: () => AnimatedProps,\n    props: $ReadOnly<{[string]: mixed}>,\n  ): AnimatedProps {\n    const compositeKey = useMemo(\n      () => createCompositeKeyForProps(props, allowlist),\n      [props],\n    );\n\n    const prevRef = useRef<?$ReadOnly<{\n      compositeKey: typeof compositeKey,\n      node: AnimatedProps,\n    }>>();\n    const prev = prevRef.current;\n\n    const next =\n      prev != null &&\n      areCompositeKeysEqual(prev.compositeKey, compositeKey, allowlist)\n        ? prev\n        : {\n            compositeKey,\n            node: create(),\n          };\n\n    useInsertionEffect(() => {\n      prevRef.current = next;\n    }, [next]);\n\n    return next.node;\n  }\n\n  function useAnimatedPropsMemo_state(\n    create: () => AnimatedProps,\n    props: $ReadOnly<{[string]: mixed}>,\n  ): AnimatedProps {\n    const compositeKey = useMemo(\n      () => createCompositeKeyForProps(props, allowlist),\n      [props],\n    );\n\n    const [state, setState] = useState<{\n      allowlist: ?AnimatedPropsAllowlist,\n      compositeKey: $ReadOnlyCompositeKey | null,\n      value: AnimatedProps,\n    }>(() => ({\n      allowlist,\n      compositeKey,\n      value: create(),\n    }));\n\n    if (\n      state.allowlist !== allowlist ||\n      !areCompositeKeysEqual(state.compositeKey, compositeKey)\n    ) {\n      setState({\n        allowlist,\n        compositeKey,\n        value: create(),\n      });\n    }\n    return state.value;\n  }\n}\n\n/**\n * Creates a new composite key for a `props` object that can be used to detect\n * whether a new `AnimatedProps` instance must be created.\n *\n * - With an allowlist, those props are searched for `AnimatedNode` instances.\n * - Without an allowlist, `style` is searched for `AnimatedNode` instances,\n *   but all other objects and arrays are included (not searched). We do not\n *   search objects and arrays without an allowlist in case they are very large\n *   data structures. We safely traverse `style` becuase it is bounded.\n *\n * Any `AnimatedEvent` instances at the first depth are always included.\n *\n * If `props` contains no `AnimatedNode` or `AnimatedEvent` instances, this\n * returns null.\n */\nexport function createCompositeKeyForProps(\n  props: $ReadOnly<{[string]: mixed}>,\n  allowlist: ?AnimatedPropsAllowlist,\n): $ReadOnlyCompositeKey | null {\n  let compositeKey: CompositeKey | null = null;\n\n  const keys = Object.keys(props);\n  for (let ii = 0, length = keys.length; ii < length; ii++) {\n    const key = keys[ii];\n    const value = props[key];\n\n    if (allowlist == null || hasOwn(allowlist, key)) {\n      let compositeKeyComponent;\n      if (key === 'style') {\n        // $FlowFixMe[incompatible-call] - `style` is a valid argument.\n        // $FlowFixMe[incompatible-type] - `flattenStyle` returns an object.\n        const flatStyle: ?{[string]: mixed} = flattenStyle(value);\n        if (flatStyle != null) {\n          compositeKeyComponent = createCompositeKeyForObject(\n            flatStyle,\n            allowlist?.style,\n          );\n        }\n      } else if (\n        value instanceof AnimatedNode ||\n        value instanceof AnimatedEvent\n      ) {\n        compositeKeyComponent = value;\n      } else if (Array.isArray(value)) {\n        compositeKeyComponent =\n          allowlist == null ? value : createCompositeKeyForArray(value);\n      } else if (isPlainObject(value)) {\n        compositeKeyComponent =\n          allowlist == null ? value : createCompositeKeyForObject(value);\n      }\n      if (compositeKeyComponent != null) {\n        if (compositeKey == null) {\n          compositeKey = {} as CompositeKey;\n        }\n        compositeKey[key] = compositeKeyComponent;\n      }\n    }\n  }\n\n  return compositeKey;\n}\n\n/**\n * Creates a new composite key for an array that retains all values that are or\n * contain `AnimatedNode` instances, and `null` for the rest.\n *\n * If `array` contains no `AnimatedNode` instances, this returns null.\n */\nfunction createCompositeKeyForArray(\n  array: $ReadOnlyArray<mixed>,\n): $ReadOnlyArray<$ReadOnlyCompositeKeyComponent | null> | null {\n  let compositeKey: Array<$ReadOnlyCompositeKeyComponent | null> | null = null;\n\n  for (let ii = 0, length = array.length; ii < length; ii++) {\n    const value = array[ii];\n\n    let compositeKeyComponent;\n    if (value instanceof AnimatedNode) {\n      compositeKeyComponent = value;\n    } else if (Array.isArray(value)) {\n      compositeKeyComponent = createCompositeKeyForArray(value);\n    } else if (isPlainObject(value)) {\n      compositeKeyComponent = createCompositeKeyForObject(value);\n    }\n    if (compositeKeyComponent != null) {\n      if (compositeKey == null) {\n        compositeKey = new Array<$ReadOnlyCompositeKeyComponent | null>(\n          array.length,\n        ).fill(null);\n      }\n      compositeKey[ii] = compositeKeyComponent;\n    }\n  }\n\n  return compositeKey;\n}\n\n/**\n * Creates a new composite key for an object that retains only properties that\n * are or contain `AnimatedNode` instances.\n *\n * When used to create composite keys for `style` props:\n *\n * - With an allowlist, those properties are searched.\n * - Without an allowlist, every property is searched.\n *\n * If `object` contains no `AnimatedNode` instances, this returns null.\n */\nfunction createCompositeKeyForObject(\n  object: $ReadOnly<{[string]: mixed}>,\n  allowlist?: ?AnimatedStyleAllowlist,\n): $ReadOnly<{[string]: $ReadOnlyCompositeKeyComponent}> | null {\n  let compositeKey: {[string]: $ReadOnlyCompositeKeyComponent} | null = null;\n\n  const keys = Object.keys(object);\n  for (let ii = 0, length = keys.length; ii < length; ii++) {\n    const key = keys[ii];\n\n    if (allowlist == null || hasOwn(allowlist, key)) {\n      const value = object[key];\n\n      let compositeKeyComponent;\n      if (value instanceof AnimatedNode) {\n        compositeKeyComponent = value;\n      } else if (Array.isArray(value)) {\n        compositeKeyComponent = createCompositeKeyForArray(value);\n      } else if (isPlainObject(value)) {\n        compositeKeyComponent = createCompositeKeyForObject(value);\n      }\n      if (compositeKeyComponent != null) {\n        if (compositeKey == null) {\n          compositeKey = {} as {[string]: $ReadOnlyCompositeKeyComponent};\n        }\n        compositeKey[key] = compositeKeyComponent;\n      }\n    }\n  }\n\n  return compositeKey;\n}\n\nexport function areCompositeKeysEqual(\n  maybePrev: $ReadOnlyCompositeKey | null,\n  maybeNext: $ReadOnlyCompositeKey | null,\n  allowlist: ?AnimatedPropsAllowlist,\n): boolean {\n  if (maybePrev === maybeNext) {\n    return true;\n  }\n  if (maybePrev === null || maybeNext === null) {\n    return false;\n  }\n  // Help Flow retain the type refinements of these.\n  const prev = maybePrev;\n  const next = maybeNext;\n\n  const keys = Object.keys(prev);\n  const length = keys.length;\n  if (length !== Object.keys(next).length) {\n    return false;\n  }\n  for (let ii = 0; ii < length; ii++) {\n    const key = keys[ii];\n    if (!hasOwn(next, key)) {\n      return false;\n    }\n    const prevComponent = prev[key];\n    const nextComponent = next[key];\n\n    if (key === 'style') {\n      // We know style components are objects with non-mixed values.\n      if (\n        !areCompositeKeyComponentsEqual(\n          // $FlowIgnore[incompatible-cast]\n          prevComponent as $ReadOnlyCompositeKeyComponent,\n          // $FlowIgnore[incompatible-cast]\n          nextComponent as $ReadOnlyCompositeKeyComponent,\n        )\n      ) {\n        return false;\n      }\n    } else if (\n      prevComponent instanceof AnimatedNode ||\n      prevComponent instanceof AnimatedEvent\n    ) {\n      if (prevComponent !== nextComponent) {\n        return false;\n      }\n    } else {\n      // When `allowlist` is null, the components must be the same. Otherwise,\n      // we created the components using deep traversal, so deep compare them.\n      if (allowlist == null) {\n        if (prevComponent !== nextComponent) {\n          return false;\n        }\n      } else {\n        if (\n          !areCompositeKeyComponentsEqual(\n            // $FlowIgnore[incompatible-cast]\n            prevComponent as $ReadOnlyCompositeKeyComponent,\n            // $FlowIgnore[incompatible-cast]\n            nextComponent as $ReadOnlyCompositeKeyComponent,\n          )\n        ) {\n          return false;\n        }\n      }\n    }\n  }\n  return true;\n}\n\nfunction areCompositeKeyComponentsEqual(\n  prev: $ReadOnlyCompositeKeyComponent | null,\n  next: $ReadOnlyCompositeKeyComponent | null,\n): boolean {\n  if (prev === next) {\n    return true;\n  }\n  if (prev instanceof AnimatedNode) {\n    return prev === next;\n  }\n  if (Array.isArray(prev)) {\n    if (!Array.isArray(next)) {\n      return false;\n    }\n    const length = prev.length;\n    if (length !== next.length) {\n      return false;\n    }\n    for (let ii = 0; ii < length; ii++) {\n      if (!areCompositeKeyComponentsEqual(prev[ii], next[ii])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  if (isPlainObject(prev)) {\n    if (!isPlainObject(next)) {\n      return false;\n    }\n    const keys = Object.keys(prev);\n    const length = keys.length;\n    if (length !== Object.keys(next).length) {\n      return false;\n    }\n    for (let ii = 0; ii < length; ii++) {\n      const key = keys[ii];\n      if (\n        !hasOwn(nullthrows(next), key) ||\n        !areCompositeKeyComponentsEqual(prev[key], next[key])\n      ) {\n        return false;\n      }\n    }\n    return true;\n  }\n  return false;\n}\n\n// Supported versions of JSC do not implement the newer Object.hasOwn. Remove\n// this shim when they do.\n// $FlowIgnore[method-unbinding]\nconst _hasOwnProp = Object.prototype.hasOwnProperty;\nconst hasOwn: (obj: $ReadOnly<{...}>, prop: string) => boolean =\n  // $FlowIgnore[method-unbinding]\n  Object.hasOwn ?? ((obj, prop) => _hasOwnProp.call(obj, prop));\n"], "mappings": ";;;;;;;;AAeA,IAAAA,cAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,eAAA,GAAAH,OAAA;AACA,IAAAI,aAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,uBAAA,GAAAC,uBAAA,CAAAN,OAAA;AACA,IAAAO,WAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,MAAA,GAAAR,OAAA;AAAoE,IAAAS,cAAA;AAAA,SAAAH,wBAAAI,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAN,uBAAA,YAAAA,wBAAAI,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAwC7D,SAASmB,2BAA2BA,CACzCC,SAAkC,EACX;EACvB,OAAO,SAASC,oBAAoBA,CAClCC,MAA2B,EAC3BC,KAAmC,EACpB;IAGf,IAAMC,oBAAoB,GACxB9B,uBAAuB,CAAC+B,mCAAmC,CAAC,CAAC,GACzDC,wBAAwB,GACxBC,0BAA0B;IAChC,OAAOH,oBAAoB,CAACF,MAAM,EAAEC,KAAK,CAAC;EAC5C,CAAC;EAED,SAASG,wBAAwBA,CAC/BJ,MAA2B,EAC3BC,KAAmC,EACpB;IACf,IAAMK,YAAY,GAAG,IAAAC,cAAO,EAC1B;MAAA,OAAMC,0BAA0B,CAACP,KAAK,EAAEH,SAAS,CAAC;IAAA,GAClD,CAACG,KAAK,CACR,CAAC;IAED,IAAMQ,OAAO,GAAG,IAAAC,aAAM,EAGlB,CAAC;IACL,IAAMC,IAAI,GAAGF,OAAO,CAACG,OAAO;IAE5B,IAAMC,IAAI,GACRF,IAAI,IAAI,IAAI,IACZG,qBAAqB,CAACH,IAAI,CAACL,YAAY,EAAEA,YAAY,EAAER,SAAS,CAAC,GAC7Da,IAAI,GACJ;MACEL,YAAY,EAAZA,YAAY;MACZS,IAAI,EAAEf,MAAM,CAAC;IACf,CAAC;IAEP,IAAAgB,yBAAkB,EAAC,YAAM;MACvBP,OAAO,CAACG,OAAO,GAAGC,IAAI;IACxB,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;IAEV,OAAOA,IAAI,CAACE,IAAI;EAClB;EAEA,SAASV,0BAA0BA,CACjCL,MAA2B,EAC3BC,KAAmC,EACpB;IACf,IAAMK,YAAY,GAAG,IAAAC,cAAO,EAC1B;MAAA,OAAMC,0BAA0B,CAACP,KAAK,EAAEH,SAAS,CAAC;IAAA,GAClD,CAACG,KAAK,CACR,CAAC;IAED,IAAAgB,SAAA,GAA0B,IAAAC,eAAQ,EAI/B;QAAA,OAAO;UACRpB,SAAS,EAATA,SAAS;UACTQ,YAAY,EAAZA,YAAY;UACZa,KAAK,EAAEnB,MAAM,CAAC;QAChB,CAAC;MAAA,CAAC,CAAC;MAAAoB,UAAA,OAAAC,eAAA,CAAAlC,OAAA,EAAA8B,SAAA;MARIK,KAAK,GAAAF,UAAA;MAAEG,QAAQ,GAAAH,UAAA;IAUtB,IACEE,KAAK,CAACxB,SAAS,KAAKA,SAAS,IAC7B,CAACgB,qBAAqB,CAACQ,KAAK,CAAChB,YAAY,EAAEA,YAAY,CAAC,EACxD;MACAiB,QAAQ,CAAC;QACPzB,SAAS,EAATA,SAAS;QACTQ,YAAY,EAAZA,YAAY;QACZa,KAAK,EAAEnB,MAAM,CAAC;MAChB,CAAC,CAAC;IACJ;IACA,OAAOsB,KAAK,CAACH,KAAK;EACpB;AACF;AAiBO,SAASX,0BAA0BA,CACxCP,KAAmC,EACnCH,SAAkC,EACJ;EAC9B,IAAIQ,YAAiC,GAAG,IAAI;EAE5C,IAAMkB,IAAI,GAAG9B,MAAM,CAAC8B,IAAI,CAACvB,KAAK,CAAC;EAC/B,KAAK,IAAIwB,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGF,IAAI,CAACE,MAAM,EAAED,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;IACxD,IAAME,GAAG,GAAGH,IAAI,CAACC,EAAE,CAAC;IACpB,IAAMN,KAAK,GAAGlB,KAAK,CAAC0B,GAAG,CAAC;IAExB,IAAI7B,SAAS,IAAI,IAAI,IAAI8B,MAAM,CAAC9B,SAAS,EAAE6B,GAAG,CAAC,EAAE;MAC/C,IAAIE,qBAAqB;MACzB,IAAIF,GAAG,KAAK,OAAO,EAAE;QAGnB,IAAMG,SAA6B,GAAG,IAAAC,qBAAY,EAACZ,KAAK,CAAC;QACzD,IAAIW,SAAS,IAAI,IAAI,EAAE;UACrBD,qBAAqB,GAAGG,2BAA2B,CACjDF,SAAS,EACThC,SAAS,oBAATA,SAAS,CAAEmC,KACb,CAAC;QACH;MACF,CAAC,MAAM,IACLd,KAAK,YAAYe,qBAAY,IAC7Bf,KAAK,YAAYgB,4BAAa,EAC9B;QACAN,qBAAqB,GAAGV,KAAK;MAC/B,CAAC,MAAM,IAAIiB,KAAK,CAACC,OAAO,CAAClB,KAAK,CAAC,EAAE;QAC/BU,qBAAqB,GACnB/B,SAAS,IAAI,IAAI,GAAGqB,KAAK,GAAGmB,0BAA0B,CAACnB,KAAK,CAAC;MACjE,CAAC,MAAM,IAAI,IAAAoB,6BAAa,EAACpB,KAAK,CAAC,EAAE;QAC/BU,qBAAqB,GACnB/B,SAAS,IAAI,IAAI,GAAGqB,KAAK,GAAGa,2BAA2B,CAACb,KAAK,CAAC;MAClE;MACA,IAAIU,qBAAqB,IAAI,IAAI,EAAE;QACjC,IAAIvB,YAAY,IAAI,IAAI,EAAE;UACxBA,YAAY,GAAG,CAAC,CAAiB;QACnC;QACAA,YAAY,CAACqB,GAAG,CAAC,GAAGE,qBAAqB;MAC3C;IACF;EACF;EAEA,OAAOvB,YAAY;AACrB;AAQA,SAASgC,0BAA0BA,CACjCE,KAA4B,EACkC;EAC9D,IAAIlC,YAAiE,GAAG,IAAI;EAE5E,KAAK,IAAImB,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGc,KAAK,CAACd,MAAM,EAAED,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;IACzD,IAAMN,KAAK,GAAGqB,KAAK,CAACf,EAAE,CAAC;IAEvB,IAAII,qBAAqB;IACzB,IAAIV,KAAK,YAAYe,qBAAY,EAAE;MACjCL,qBAAqB,GAAGV,KAAK;IAC/B,CAAC,MAAM,IAAIiB,KAAK,CAACC,OAAO,CAAClB,KAAK,CAAC,EAAE;MAC/BU,qBAAqB,GAAGS,0BAA0B,CAACnB,KAAK,CAAC;IAC3D,CAAC,MAAM,IAAI,IAAAoB,6BAAa,EAACpB,KAAK,CAAC,EAAE;MAC/BU,qBAAqB,GAAGG,2BAA2B,CAACb,KAAK,CAAC;IAC5D;IACA,IAAIU,qBAAqB,IAAI,IAAI,EAAE;MACjC,IAAIvB,YAAY,IAAI,IAAI,EAAE;QACxBA,YAAY,GAAG,IAAI8B,KAAK,CACtBI,KAAK,CAACd,MACR,CAAC,CAACe,IAAI,CAAC,IAAI,CAAC;MACd;MACAnC,YAAY,CAACmB,EAAE,CAAC,GAAGI,qBAAqB;IAC1C;EACF;EAEA,OAAOvB,YAAY;AACrB;AAaA,SAAS0B,2BAA2BA,CAClCU,MAAoC,EACpC5C,SAAmC,EAC2B;EAC9D,IAAIQ,YAA+D,GAAG,IAAI;EAE1E,IAAMkB,IAAI,GAAG9B,MAAM,CAAC8B,IAAI,CAACkB,MAAM,CAAC;EAChC,KAAK,IAAIjB,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGF,IAAI,CAACE,MAAM,EAAED,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;IACxD,IAAME,GAAG,GAAGH,IAAI,CAACC,EAAE,CAAC;IAEpB,IAAI3B,SAAS,IAAI,IAAI,IAAI8B,MAAM,CAAC9B,SAAS,EAAE6B,GAAG,CAAC,EAAE;MAC/C,IAAMR,KAAK,GAAGuB,MAAM,CAACf,GAAG,CAAC;MAEzB,IAAIE,qBAAqB;MACzB,IAAIV,KAAK,YAAYe,qBAAY,EAAE;QACjCL,qBAAqB,GAAGV,KAAK;MAC/B,CAAC,MAAM,IAAIiB,KAAK,CAACC,OAAO,CAAClB,KAAK,CAAC,EAAE;QAC/BU,qBAAqB,GAAGS,0BAA0B,CAACnB,KAAK,CAAC;MAC3D,CAAC,MAAM,IAAI,IAAAoB,6BAAa,EAACpB,KAAK,CAAC,EAAE;QAC/BU,qBAAqB,GAAGG,2BAA2B,CAACb,KAAK,CAAC;MAC5D;MACA,IAAIU,qBAAqB,IAAI,IAAI,EAAE;QACjC,IAAIvB,YAAY,IAAI,IAAI,EAAE;UACxBA,YAAY,GAAG,CAAC,CAA+C;QACjE;QACAA,YAAY,CAACqB,GAAG,CAAC,GAAGE,qBAAqB;MAC3C;IACF;EACF;EAEA,OAAOvB,YAAY;AACrB;AAEO,SAASQ,qBAAqBA,CACnC6B,SAAuC,EACvCC,SAAuC,EACvC9C,SAAkC,EACzB;EACT,IAAI6C,SAAS,KAAKC,SAAS,EAAE;IAC3B,OAAO,IAAI;EACb;EACA,IAAID,SAAS,KAAK,IAAI,IAAIC,SAAS,KAAK,IAAI,EAAE;IAC5C,OAAO,KAAK;EACd;EAEA,IAAMjC,IAAI,GAAGgC,SAAS;EACtB,IAAM9B,IAAI,GAAG+B,SAAS;EAEtB,IAAMpB,IAAI,GAAG9B,MAAM,CAAC8B,IAAI,CAACb,IAAI,CAAC;EAC9B,IAAMe,MAAM,GAAGF,IAAI,CAACE,MAAM;EAC1B,IAAIA,MAAM,KAAKhC,MAAM,CAAC8B,IAAI,CAACX,IAAI,CAAC,CAACa,MAAM,EAAE;IACvC,OAAO,KAAK;EACd;EACA,KAAK,IAAID,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;IAClC,IAAME,GAAG,GAAGH,IAAI,CAACC,EAAE,CAAC;IACpB,IAAI,CAACG,MAAM,CAACf,IAAI,EAAEc,GAAG,CAAC,EAAE;MACtB,OAAO,KAAK;IACd;IACA,IAAMkB,aAAa,GAAGlC,IAAI,CAACgB,GAAG,CAAC;IAC/B,IAAMmB,aAAa,GAAGjC,IAAI,CAACc,GAAG,CAAC;IAE/B,IAAIA,GAAG,KAAK,OAAO,EAAE;MAEnB,IACE,CAACoB,8BAA8B,CAE7BF,aAAa,EAEbC,aACF,CAAC,EACD;QACA,OAAO,KAAK;MACd;IACF,CAAC,MAAM,IACLD,aAAa,YAAYX,qBAAY,IACrCW,aAAa,YAAYV,4BAAa,EACtC;MACA,IAAIU,aAAa,KAAKC,aAAa,EAAE;QACnC,OAAO,KAAK;MACd;IACF,CAAC,MAAM;MAGL,IAAIhD,SAAS,IAAI,IAAI,EAAE;QACrB,IAAI+C,aAAa,KAAKC,aAAa,EAAE;UACnC,OAAO,KAAK;QACd;MACF,CAAC,MAAM;QACL,IACE,CAACC,8BAA8B,CAE7BF,aAAa,EAEbC,aACF,CAAC,EACD;UACA,OAAO,KAAK;QACd;MACF;IACF;EACF;EACA,OAAO,IAAI;AACb;AAEA,SAASC,8BAA8BA,CACrCpC,IAA2C,EAC3CE,IAA2C,EAClC;EACT,IAAIF,IAAI,KAAKE,IAAI,EAAE;IACjB,OAAO,IAAI;EACb;EACA,IAAIF,IAAI,YAAYuB,qBAAY,EAAE;IAChC,OAAOvB,IAAI,KAAKE,IAAI;EACtB;EACA,IAAIuB,KAAK,CAACC,OAAO,CAAC1B,IAAI,CAAC,EAAE;IACvB,IAAI,CAACyB,KAAK,CAACC,OAAO,CAACxB,IAAI,CAAC,EAAE;MACxB,OAAO,KAAK;IACd;IACA,IAAMa,MAAM,GAAGf,IAAI,CAACe,MAAM;IAC1B,IAAIA,MAAM,KAAKb,IAAI,CAACa,MAAM,EAAE;MAC1B,OAAO,KAAK;IACd;IACA,KAAK,IAAID,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;MAClC,IAAI,CAACsB,8BAA8B,CAACpC,IAAI,CAACc,EAAE,CAAC,EAAEZ,IAAI,CAACY,EAAE,CAAC,CAAC,EAAE;QACvD,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb;EACA,IAAI,IAAAc,6BAAa,EAAC5B,IAAI,CAAC,EAAE;IACvB,IAAI,CAAC,IAAA4B,6BAAa,EAAC1B,IAAI,CAAC,EAAE;MACxB,OAAO,KAAK;IACd;IACA,IAAMW,IAAI,GAAG9B,MAAM,CAAC8B,IAAI,CAACb,IAAI,CAAC;IAC9B,IAAMe,OAAM,GAAGF,IAAI,CAACE,MAAM;IAC1B,IAAIA,OAAM,KAAKhC,MAAM,CAAC8B,IAAI,CAACX,IAAI,CAAC,CAACa,MAAM,EAAE;MACvC,OAAO,KAAK;IACd;IACA,KAAK,IAAID,GAAE,GAAG,CAAC,EAAEA,GAAE,GAAGC,OAAM,EAAED,GAAE,EAAE,EAAE;MAClC,IAAME,GAAG,GAAGH,IAAI,CAACC,GAAE,CAAC;MACpB,IACE,CAACG,MAAM,CAAC,IAAAoB,mBAAU,EAACnC,IAAI,CAAC,EAAEc,GAAG,CAAC,IAC9B,CAACoB,8BAA8B,CAACpC,IAAI,CAACgB,GAAG,CAAC,EAAEd,IAAI,CAACc,GAAG,CAAC,CAAC,EACrD;QACA,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;AAKA,IAAMsB,WAAW,GAAGvD,MAAM,CAACwD,SAAS,CAAC1D,cAAc;AACnD,IAAMoC,MAAwD,IAAApD,cAAA,GAE5DkB,MAAM,CAACkC,MAAM,YAAApD,cAAA,GAAK,UAAC2E,GAAG,EAAEC,IAAI;EAAA,OAAKH,WAAW,CAACxD,IAAI,CAAC0D,GAAG,EAAEC,IAAI,CAAC;AAAA,CAAC", "ignoreList": []}