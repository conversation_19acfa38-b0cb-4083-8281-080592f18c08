/**
 * useAuth Hook - Comprehensive Authentication Management
 *
 * Hook Contract:
 * - Provides complete authentication state and actions
 * - <PERSON><PERSON> login, logout, registration, and password management
 * - Integrates with token manager for secure storage
 * - Supports role switching and profile management
 * - Implements automatic token refresh and validation
 * - Provides loading states and error handling
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { useCallback, useEffect, useState } from 'react';
import { Alert } from 'react-native';
import { useAuthStore } from '../store/authSlice';
import { authService, type LoginRequest, type RegisterRequest, type AuthResponse } from '../services/authService';
import { tokenManager } from '../services/tokenManager';
import { navigationGuards } from '../services/navigationGuards';
import { authErrorHandler } from '../services/authErrorHandler';

export interface UseAuthReturn {
  // State
  isAuthenticated: boolean;
  user: any;
  userRole: 'customer' | 'provider' | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (credentials: LoginRequest) => Promise<boolean>;
  register: (userData: RegisterRequest) => Promise<boolean>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<boolean>;
  
  // Profile management
  updateProfile: (data: any) => Promise<boolean>;
  changePassword: (data: { old_password: string; new_password: string }) => Promise<boolean>;
  
  // Email verification
  verifyEmail: (token: string) => Promise<boolean>;
  resendVerification: () => Promise<boolean>;
  
  // Password reset
  requestPasswordReset: (email: string) => Promise<boolean>;
  confirmPasswordReset: (token: string, password: string) => Promise<boolean>;
  
  // Role management
  switchRole: (role: 'customer' | 'service_provider') => Promise<boolean>;
  getAvailableRoles: () => Promise<string[]>;
  
  // Utility
  checkAuthStatus: () => Promise<boolean>;
  clearError: () => void;
}

export const useAuth = (): UseAuthReturn => {
  const authStore = useAuthStore();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize authentication on mount
  useEffect(() => {
    initializeAuth();
  }, []);

  /**
   * Initialize authentication state
   */
  const initializeAuth = useCallback(async () => {
    try {
      await tokenManager.initialize();
      
      if (tokenManager.isAuthenticated()) {
        const validation = tokenManager.validateToken();
        
        if (validation.needsRefresh) {
          await refreshToken();
        } else if (validation.isValid) {
          // Load user data from storage
          const userData = await tokenManager.getUserData();
          if (userData) {
            authStore.loginSuccess(
              tokenManager.getAccessToken()!,
              tokenManager.getRefreshToken()!,
              userData
            );
          }
        }
      }
    } catch (error) {
      console.error('Auth initialization failed:', error);
    }
  }, []);

  /**
   * Login user
   */
  const login = useCallback(async (credentials: LoginRequest): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      authStore.loginStart();
      
      const response = await authService.login(credentials);
      
      // Store tokens securely
      const expiresAt = Date.now() + 30 * 60 * 1000; // 30 minutes
      await tokenManager.storeTokens({
        accessToken: response.access,
        refreshToken: response.refresh,
        expiresAt,
        user: response.user,
      });
      
      // Update auth store
      authStore.loginSuccess(response.access, response.refresh, response.user);
      
      console.log('✅ Login successful');
      return true;
    } catch (error: any) {
      const authError = await authErrorHandler.handleAuthError(error, {
        component: 'useAuth',
        action: 'login',
      });

      setError(authError.userMessage);
      authStore.loginFailure(authError.userMessage);
      console.error('❌ Login failed:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Register user
   */
  const register = useCallback(async (userData: RegisterRequest): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      authStore.registerStart();
      
      const response = await authService.register(userData);
      
      // Store tokens securely
      const expiresAt = Date.now() + 30 * 60 * 1000; // 30 minutes
      await tokenManager.storeTokens({
        accessToken: response.access,
        refreshToken: response.refresh,
        expiresAt,
        user: response.user,
      });
      
      // Update auth store
      authStore.registerSuccess(response.access, response.refresh, response.user);
      
      console.log('✅ Registration successful');
      return true;
    } catch (error: any) {
      const errorMessage = error.message || 'Registration failed';
      setError(errorMessage);
      authStore.registerFailure(errorMessage);
      console.error('❌ Registration failed:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Logout user
   */
  const logout = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    
    try {
      const refreshToken = tokenManager.getRefreshToken();
      
      // Call logout API if refresh token exists
      if (refreshToken) {
        try {
          await authService.logout(refreshToken);
        } catch (error) {
          // Logout API errors are not critical
          console.warn('Logout API call failed:', error);
        }
      }
      
      // Clear tokens and auth state
      await tokenManager.clearTokens();
      authStore.logout();
      
      console.log('✅ Logout successful');
    } catch (error) {
      console.error('❌ Logout failed:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Refresh authentication token
   */
  const refreshToken = useCallback(async (): Promise<boolean> => {
    try {
      const newAccessToken = await tokenManager.refreshAccessToken();
      
      // Update auth store with new token
      const userData = await tokenManager.getUserData();
      if (userData) {
        authStore.loginSuccess(
          newAccessToken,
          tokenManager.getRefreshToken()!,
          userData
        );
      }
      
      return true;
    } catch (error) {
      console.error('❌ Token refresh failed:', error);
      await logout();
      return false;
    }
  }, []);

  /**
   * Update user profile
   */
  const updateProfile = useCallback(async (data: any): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const updatedUser = await authService.updateUserProfile(data);
      
      // Update auth store with new user data
      authStore.updateUser(updatedUser);
      
      // Update stored user data
      await tokenManager.storeTokens({
        accessToken: tokenManager.getAccessToken()!,
        refreshToken: tokenManager.getRefreshToken()!,
        expiresAt: Date.now() + 30 * 60 * 1000,
        user: updatedUser,
      });
      
      return true;
    } catch (error: any) {
      const errorMessage = error.message || 'Profile update failed';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Change password
   */
  const changePassword = useCallback(async (data: { old_password: string; new_password: string }): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      await authService.changePassword(data);
      return true;
    } catch (error: any) {
      const errorMessage = error.message || 'Password change failed';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Verify email
   */
  const verifyEmail = useCallback(async (token: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      await authService.verifyEmail({ token });
      
      // Update user verification status
      if (authStore.user) {
        const updatedUser = { ...authStore.user, is_verified: true };
        authStore.updateUser(updatedUser);
      }
      
      return true;
    } catch (error: any) {
      const errorMessage = error.message || 'Email verification failed';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Resend email verification
   */
  const resendVerification = useCallback(async (): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      await authService.resendEmailVerification();
      return true;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to resend verification email';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Request password reset
   */
  const requestPasswordReset = useCallback(async (email: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      await authService.requestPasswordReset({ email });
      return true;
    } catch (error: any) {
      const errorMessage = error.message || 'Password reset request failed';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Confirm password reset
   */
  const confirmPasswordReset = useCallback(async (token: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      await authService.confirmPasswordReset({ token, password });
      return true;
    } catch (error: any) {
      const errorMessage = error.message || 'Password reset failed';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Switch user role
   */
  const switchRole = useCallback(async (role: 'customer' | 'service_provider'): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await authService.switchRole(role);
      
      // Update tokens and user data
      const expiresAt = Date.now() + 30 * 60 * 1000;
      await tokenManager.storeTokens({
        accessToken: response.access,
        refreshToken: response.refresh,
        expiresAt,
        user: response.user,
      });
      
      // Update auth store
      authStore.loginSuccess(response.access, response.refresh, response.user);
      
      return true;
    } catch (error: any) {
      const errorMessage = error.message || 'Role switch failed';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Get available roles
   */
  const getAvailableRoles = useCallback(async (): Promise<string[]> => {
    try {
      const response = await authService.getAvailableRoles();
      return response.roles;
    } catch (error) {
      console.error('Failed to get available roles:', error);
      return [];
    }
  }, []);

  /**
   * Check authentication status
   */
  const checkAuthStatus = useCallback(async (): Promise<boolean> => {
    try {
      const response = await authService.checkAuthStatus();
      return response.authenticated;
    } catch (error) {
      return false;
    }
  }, []);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // State
    isAuthenticated: authStore.isAuthenticated,
    user: authStore.user,
    userRole: authStore.userRole,
    isLoading: isLoading || authStore.status === 'loading',
    error: error || authStore.error,
    
    // Actions
    login,
    register,
    logout,
    refreshToken,
    
    // Profile management
    updateProfile,
    changePassword,
    
    // Email verification
    verifyEmail,
    resendVerification,
    
    // Password reset
    requestPasswordReset,
    confirmPasswordReset,
    
    // Role management
    switchRole,
    getAvailableRoles,
    
    // Utility
    checkAuthStatus,
    clearError,
  };
};
