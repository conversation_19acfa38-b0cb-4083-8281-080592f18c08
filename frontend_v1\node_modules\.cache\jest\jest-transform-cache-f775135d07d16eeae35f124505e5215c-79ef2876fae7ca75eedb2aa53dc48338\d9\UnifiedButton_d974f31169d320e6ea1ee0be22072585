8fb610df13f375072d7ea33702edac53
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.UnifiedButton = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _vectorIcons = require("@expo/vector-icons");
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _ThemeContext = require("../../contexts/ThemeContext");
var _hapticPatterns = require("../../utils/hapticPatterns");
var _responsiveUtils = require("../../utils/responsiveUtils");
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var UnifiedButton = exports.UnifiedButton = function UnifiedButton(_ref) {
  var title = _ref.title,
    children = _ref.children,
    onPress = _ref.onPress,
    _ref$variant = _ref.variant,
    variant = _ref$variant === void 0 ? 'primary' : _ref$variant,
    _ref$size = _ref.size,
    size = _ref$size === void 0 ? 'medium' : _ref$size,
    _ref$disabled = _ref.disabled,
    disabled = _ref$disabled === void 0 ? false : _ref$disabled,
    _ref$loading = _ref.loading,
    loading = _ref$loading === void 0 ? false : _ref$loading,
    icon = _ref.icon,
    _ref$iconPosition = _ref.iconPosition,
    iconPosition = _ref$iconPosition === void 0 ? 'left' : _ref$iconPosition,
    _ref$iconOnly = _ref.iconOnly,
    iconOnly = _ref$iconOnly === void 0 ? false : _ref$iconOnly,
    _ref$fullWidth = _ref.fullWidth,
    fullWidth = _ref$fullWidth === void 0 ? false : _ref$fullWidth,
    style = _ref.style,
    textStyle = _ref.textStyle,
    accessibilityLabel = _ref.accessibilityLabel,
    accessibilityHint = _ref.accessibilityHint,
    _ref$accessibilityRol = _ref.accessibilityRole,
    accessibilityRole = _ref$accessibilityRol === void 0 ? 'button' : _ref$accessibilityRol,
    testID = _ref.testID,
    _ref$enableHaptics = _ref.enableHaptics,
    enableHaptics = _ref$enableHaptics === void 0 ? true : _ref$enableHaptics,
    onLongPress = _ref.onLongPress;
  var _useTheme = (0, _ThemeContext.useTheme)(),
    isDark = _useTheme.isDark,
    colors = _useTheme.colors;
  var _useState = (0, _react.useState)(false),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    isPressed = _useState2[0],
    setIsPressed = _useState2[1];
  var scaleAnim = (0, _react.useRef)(new _reactNative.Animated.Value(1)).current;
  var buttonContent = children || title;
  var handlePress = function handlePress() {
    if (disabled || loading) return;
    if (enableHaptics) {
      switch (variant) {
        case 'destructive':
          _hapticPatterns.HapticPatterns.warningPress();
          break;
        case 'success':
          _hapticPatterns.HapticPatterns.successPress();
          break;
        default:
          _hapticPatterns.HapticPatterns.lightImpact();
      }
    }
    onPress();
  };
  var handlePressIn = function handlePressIn() {
    setIsPressed(true);
    _reactNative.Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true
    }).start();
  };
  var handlePressOut = function handlePressOut() {
    setIsPressed(false);
    _reactNative.Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true
    }).start();
  };
  var getSizeConfig = function getSizeConfig() {
    var configs = {
      small: {
        height: Math.max(36, 44),
        paddingHorizontal: (0, _responsiveUtils.getResponsiveSpacing)(12),
        fontSize: (0, _responsiveUtils.getResponsiveFontSize)(14),
        iconSize: 16,
        minTouchTarget: 44
      },
      medium: {
        height: 44,
        paddingHorizontal: (0, _responsiveUtils.getResponsiveSpacing)(16),
        fontSize: (0, _responsiveUtils.getResponsiveFontSize)(16),
        iconSize: 20,
        minTouchTarget: 44
      },
      large: {
        height: 52,
        paddingHorizontal: (0, _responsiveUtils.getResponsiveSpacing)(20),
        fontSize: (0, _responsiveUtils.getResponsiveFontSize)(18),
        iconSize: 24,
        minTouchTarget: 52
      }
    };
    return configs[size];
  };
  var getVariantColors = function getVariantColors() {
    var variants = {
      primary: {
        background: colors.primary[500],
        text: colors.white,
        border: colors.primary[500]
      },
      secondary: {
        background: colors.gray[100],
        text: colors.gray[900],
        border: colors.gray[300]
      },
      outline: {
        background: 'transparent',
        text: colors.primary[500],
        border: colors.primary[500]
      },
      ghost: {
        background: 'transparent',
        text: colors.primary[500],
        border: 'transparent'
      },
      destructive: {
        background: colors.error[500],
        text: colors.white,
        border: colors.error[500]
      },
      success: {
        background: colors.success[500],
        text: colors.white,
        border: colors.success[500]
      },
      minimal: {
        background: 'transparent',
        text: colors.gray[700],
        border: 'transparent'
      }
    };
    return variants[variant];
  };
  var sizeConfig = getSizeConfig();
  var variantColors = getVariantColors();
  var buttonStyles = [styles.base, {
    height: sizeConfig.height,
    paddingHorizontal: iconOnly ? sizeConfig.height / 2 : sizeConfig.paddingHorizontal,
    backgroundColor: disabled ? colors.gray[300] : variantColors.background,
    borderColor: disabled ? colors.gray[300] : variantColors.border,
    borderWidth: variant === 'outline' ? 1 : 0,
    width: fullWidth ? '100%' : iconOnly ? sizeConfig.height : 'auto',
    minWidth: iconOnly ? Math.max(sizeConfig.height, sizeConfig.minTouchTarget) : sizeConfig.minTouchTarget,
    minHeight: sizeConfig.minTouchTarget
  }, style];
  var textStyles = [styles.text, {
    fontSize: sizeConfig.fontSize,
    color: disabled ? colors.gray[500] : variantColors.text
  }, textStyle];
  var renderIcon = function renderIcon() {
    if (!icon) return null;
    return (0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
      name: icon,
      size: sizeConfig.iconSize,
      color: disabled ? colors.gray[500] : variantColors.text,
      style: [iconPosition === 'right' && !iconOnly && styles.iconRight, iconPosition === 'left' && !iconOnly && styles.iconLeft]
    });
  };
  var renderContent = function renderContent() {
    if (loading) {
      return (0, _jsxRuntime.jsx)(_reactNative.ActivityIndicator, {
        size: "small",
        color: disabled ? colors.gray[500] : variantColors.text
      });
    }
    if (iconOnly) {
      return renderIcon();
    }
    var iconElement = renderIcon();
    var textElement = buttonContent ? (0, _jsxRuntime.jsx)(_reactNative.Text, {
      style: textStyles,
      numberOfLines: 1,
      children: buttonContent
    }) : null;
    if (iconPosition === 'right') {
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [textElement, iconElement]
      });
    }
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [iconElement, textElement]
    });
  };
  return (0, _jsxRuntime.jsx)(_reactNative.Animated.View, {
    style: {
      transform: [{
        scale: scaleAnim
      }]
    },
    children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
      style: buttonStyles,
      onPress: handlePress,
      onPressIn: handlePressIn,
      onPressOut: handlePressOut,
      onLongPress: onLongPress,
      disabled: disabled || loading,
      activeOpacity: 0.8,
      accessibilityRole: accessibilityRole,
      accessibilityLabel: accessibilityLabel || title,
      accessibilityHint: accessibilityHint,
      accessibilityState: {
        disabled: disabled || loading,
        busy: loading
      },
      testID: testID,
      children: (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.content,
        children: renderContent()
      })
    })
  });
};
var styles = _reactNative.StyleSheet.create({
  base: {
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row'
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center'
  },
  text: {
    fontWeight: '600',
    textAlign: 'center'
  },
  iconLeft: {
    marginRight: 8
  },
  iconRight: {
    marginLeft: 8
  }
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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