9d36c70e8ccf79a1adf5dd778577472c
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.searchApiService = exports.default = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _apiClient = require("./apiClient");
var SearchApiService = function () {
  function SearchApiService() {
    (0, _classCallCheck2.default)(this, SearchApiService);
    this.baseUrl = '/api/catalog';
  }
  return (0, _createClass2.default)(SearchApiService, [{
    key: "search",
    value: (function () {
      var _search = (0, _asyncToGenerator2.default)(function* () {
        var filters = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
        try {
          var _response$data$servic, _response$data$provid, _response$data$servic2, _response$data$provid2, _response$data$servic3, _response$data$provid3;
          console.log('🔍 SearchApiService: Performing enhanced search with filters:', filters);
          var params = new URLSearchParams();
          if (filters.query) params.append('q', filters.query);
          if (filters.category) params.append('category', filters.category);
          if (filters.location) params.append('location', filters.location);
          if (filters.price_min !== undefined) params.append('price_min', filters.price_min.toString());
          if (filters.price_max !== undefined) params.append('price_max', filters.price_max.toString());
          if (filters.rating_min !== undefined) params.append('rating_min', filters.rating_min.toString());
          if (filters.availability) params.append('availability', 'true');
          if (filters.distance_max !== undefined) params.append('distance_max', filters.distance_max.toString());
          if (filters.is_popular) params.append('is_popular', 'true');
          if (filters.is_featured) params.append('is_featured', 'true');
          var page = filters.page || 1;
          var limit = Math.min(filters.limit || 20, 50);
          params.append('page', page.toString());
          params.append('limit', limit.toString());
          params.append('type', 'services');
          var url = `${this.baseUrl}/search/enhanced/?${params.toString()}`;
          console.log('🔍 SearchApiService: Making request to:', url);
          var response = yield _apiClient.apiClient.get(url, {}, false, {
            enabled: true,
            ttl: 2 * 60 * 1000
          });
          var transformedResults = [];
          if ((_response$data$servic = response.data.services) != null && _response$data$servic.results) {
            response.data.services.results.forEach(function (service) {
              transformedResults.push({
                id: service.id,
                type: 'service',
                title: service.name,
                subtitle: service.provider_name,
                description: service.description,
                image: service.image,
                rating: service.average_rating,
                price: service.base_price,
                distance: service.distance,
                category: service.category,
                provider_name: service.provider_name,
                location: service.location
              });
            });
          }
          if ((_response$data$provid = response.data.providers) != null && _response$data$provid.results) {
            response.data.providers.results.forEach(function (provider) {
              transformedResults.push({
                id: provider.id,
                type: 'provider',
                title: provider.business_name,
                subtitle: provider.location,
                description: provider.description,
                image: provider.profile_image,
                rating: provider.average_rating,
                distance: provider.distance,
                location: provider.location
              });
            });
          }
          var suggestions = [];
          if (response.data.suggestions) {
            response.data.suggestions.forEach(function (suggestion) {
              suggestions.push({
                id: suggestion.id || suggestion.text,
                type: suggestion.type || 'query',
                text: suggestion.text,
                count: suggestion.count
              });
            });
          }
          return {
            results: transformedResults,
            suggestions: suggestions,
            total_count: response.data.total_results || transformedResults.length,
            page: filters.page || 1,
            has_next: (_response$data$servic2 = response.data.services) != null && _response$data$servic2.next || (_response$data$provid2 = response.data.providers) != null && _response$data$provid2.next ? true : false,
            has_previous: (_response$data$servic3 = response.data.services) != null && _response$data$servic3.previous || (_response$data$provid3 = response.data.providers) != null && _response$data$provid3.previous ? true : false,
            filters_applied: filters
          };
        } catch (error) {
          console.error('❌ SearchApiService: Enhanced search failed:', error);
          return this.getFallbackSearchResults(filters);
        }
      });
      function search() {
        return _search.apply(this, arguments);
      }
      return search;
    }())
  }, {
    key: "getFallbackSearchResults",
    value: function getFallbackSearchResults(filters) {
      console.log('🔄 SearchApiService: Using fallback search results');
      var mockResults = [];
      if (filters.query) {
        var commonServices = [{
          name: 'Hair Cut',
          category: 'Hair Services',
          price: 50
        }, {
          name: 'Nail Art',
          category: 'Nail Services',
          price: 30
        }, {
          name: 'Massage Therapy',
          category: 'Massage',
          price: 80
        }, {
          name: 'Facial Treatment',
          category: 'Skincare',
          price: 60
        }];
        commonServices.filter(function (service) {
          return service.name.toLowerCase().includes(filters.query.toLowerCase()) || service.category.toLowerCase().includes(filters.query.toLowerCase());
        }).forEach(function (service, index) {
          mockResults.push({
            id: `fallback-${index}`,
            type: 'service',
            title: service.name,
            subtitle: 'Available Provider',
            description: `Professional ${service.name.toLowerCase()} service`,
            rating: 4.5,
            price: service.price,
            category: service.category,
            provider_name: 'Local Provider',
            location: 'Nearby'
          });
        });
      }
      return {
        results: mockResults,
        suggestions: [],
        total_count: mockResults.length,
        page: 1,
        has_next: false,
        has_previous: false,
        filters_applied: filters
      };
    }
  }, {
    key: "searchNearby",
    value: (function () {
      var _searchNearby = (0, _asyncToGenerator2.default)(function* (latitude, longitude) {
        var filters = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
        var radius = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 10;
        try {
          console.log('📍 SearchApiService: Performing location-based search');
          var locationFilters = Object.assign({}, filters, {
            latitude: latitude.toString(),
            longitude: longitude.toString(),
            radius: radius.toString()
          });
          return yield this.search(locationFilters);
        } catch (error) {
          console.error('❌ SearchApiService: Location-based search failed:', error);
          return this.getFallbackSearchResults(filters);
        }
      });
      function searchNearby(_x, _x2) {
        return _searchNearby.apply(this, arguments);
      }
      return searchNearby;
    }())
  }, {
    key: "getSuggestions",
    value: (function () {
      var _getSuggestions = (0, _asyncToGenerator2.default)(function* (query) {
        var limit = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 10;
        try {
          var _response$data$sugges;
          console.log('💡 SearchApiService: Getting suggestions for query:', query);
          if (!query.trim()) {
            return [];
          }
          var params = new URLSearchParams({
            q: query,
            type: 'suggestions',
            limit: Math.min(limit, 20).toString()
          });
          var url = `${this.baseUrl}/search/enhanced/?${params.toString()}`;
          var response = yield _apiClient.apiClient.get(url, {}, false, {
            enabled: true,
            ttl: 5 * 60 * 1000
          });
          var suggestions = ((_response$data$sugges = response.data.suggestions) == null ? void 0 : _response$data$sugges.map(function (suggestion) {
            return {
              id: suggestion.id || suggestion.text,
              type: suggestion.type || 'query',
              text: suggestion.text,
              count: suggestion.count
            };
          })) || [];
          console.log('✅ SearchApiService: Got suggestions:', suggestions.length);
          return suggestions;
        } catch (error) {
          console.error('❌ SearchApiService: Suggestions failed:', error);
          return this.getFallbackSuggestions(query);
        }
      });
      function getSuggestions(_x3) {
        return _getSuggestions.apply(this, arguments);
      }
      return getSuggestions;
    }())
  }, {
    key: "getFallbackSuggestions",
    value: function getFallbackSuggestions(query) {
      var commonSuggestions = ['Hair Cut', 'Hair Color', 'Hair Styling', 'Nail Art', 'Manicure', 'Pedicure', 'Massage', 'Deep Tissue Massage', 'Relaxation Massage', 'Facial', 'Skincare', 'Anti-aging Treatment', 'Makeup', 'Bridal Makeup', 'Event Makeup', 'Eyebrow Threading', 'Waxing', 'Laser Hair Removal'];
      return commonSuggestions.filter(function (suggestion) {
        return suggestion.toLowerCase().includes(query.toLowerCase());
      }).slice(0, 8).map(function (text, index) {
        return {
          id: `fallback-suggestion-${index}`,
          type: 'service',
          text: text,
          count: Math.floor(Math.random() * 50) + 10
        };
      });
    }
  }, {
    key: "getSearchHistory",
    value: (function () {
      var _getSearchHistory = (0, _asyncToGenerator2.default)(function* () {
        var limit = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 20;
        try {
          var _response$data$result;
          var params = new URLSearchParams({
            limit: limit.toString()
          });
          var url = `${this.baseUrl}/search-history/?${params.toString()}`;
          var response = yield _apiClient.apiClient.get(url);
          return ((_response$data$result = response.data.results) == null ? void 0 : _response$data$result.map(function (item) {
            return {
              id: item.id,
              query: item.query,
              filters: item.filters || {},
              timestamp: item.created_at,
              result_count: item.result_count || 0
            };
          })) || [];
        } catch (error) {
          console.error('Search history API error:', error);
          return [];
        }
      });
      function getSearchHistory() {
        return _getSearchHistory.apply(this, arguments);
      }
      return getSearchHistory;
    }())
  }, {
    key: "saveSearchToHistory",
    value: (function () {
      var _saveSearchToHistory = (0, _asyncToGenerator2.default)(function* (query, filters, resultCount) {
        try {
          yield _apiClient.apiClient.post(`${this.baseUrl}/search-history/`, {
            query: query,
            filters: filters,
            result_count: resultCount
          });
        } catch (error) {
          console.error('Save search history error:', error);
        }
      });
      function saveSearchToHistory(_x4, _x5, _x6) {
        return _saveSearchToHistory.apply(this, arguments);
      }
      return saveSearchToHistory;
    }())
  }, {
    key: "clearSearchHistory",
    value: (function () {
      var _clearSearchHistory = (0, _asyncToGenerator2.default)(function* () {
        try {
          yield _apiClient.apiClient.delete(`${this.baseUrl}/search-history/clear/`);
        } catch (error) {
          console.error('Clear search history error:', error);
          throw error;
        }
      });
      function clearSearchHistory() {
        return _clearSearchHistory.apply(this, arguments);
      }
      return clearSearchHistory;
    }())
  }, {
    key: "getPopularSearches",
    value: (function () {
      var _getPopularSearches = (0, _asyncToGenerator2.default)(function* () {
        var limit = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 10;
        try {
          var _response$data$result2;
          var params = new URLSearchParams({
            type: 'popular',
            limit: limit.toString()
          });
          var url = `${this.baseUrl}/popular-searches/?${params.toString()}`;
          var response = yield _apiClient.apiClient.get(url);
          return ((_response$data$result2 = response.data.results) == null ? void 0 : _response$data$result2.map(function (item) {
            return {
              id: item.query,
              type: 'query',
              text: item.query,
              count: item.search_count
            };
          })) || [];
        } catch (error) {
          console.error('Popular searches API error:', error);
          return [];
        }
      });
      function getPopularSearches() {
        return _getPopularSearches.apply(this, arguments);
      }
      return getPopularSearches;
    }())
  }, {
    key: "getTrendingSearches",
    value: (function () {
      var _getTrendingSearches = (0, _asyncToGenerator2.default)(function* () {
        var limit = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 10;
        try {
          var _response$data$result3;
          var params = new URLSearchParams({
            type: 'trending',
            limit: limit.toString()
          });
          var url = `${this.baseUrl}/trending-searches/?${params.toString()}`;
          var response = yield _apiClient.apiClient.get(url);
          return ((_response$data$result3 = response.data.results) == null ? void 0 : _response$data$result3.map(function (item) {
            return {
              id: item.query,
              type: 'query',
              text: item.query,
              count: item.trend_score
            };
          })) || [];
        } catch (error) {
          console.error('Trending searches API error:', error);
          return [];
        }
      });
      function getTrendingSearches() {
        return _getTrendingSearches.apply(this, arguments);
      }
      return getTrendingSearches;
    }())
  }]);
}();
var searchApiService = exports.searchApiService = new SearchApiService();
var _default = exports.default = searchApiService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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