9f09598d21dc1330ba575a4895812cbf
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _reactNative = require("@testing-library/react-native");
var _react = _interopRequireDefault(require("react"));
var _ThemeContext = require("../../../contexts/ThemeContext");
var _HyperMinimalistTheme = require("../../../design-system/HyperMinimalistTheme");
var _Text = require("../Text");
var _jsxRuntime = require("react/jsx-runtime");
var TestWrapper = function TestWrapper(_ref) {
  var children = _ref.children;
  return (0, _jsxRuntime.jsx)(_ThemeContext.ThemeProvider, {
    theme: _HyperMinimalistTheme.HyperMinimalistTheme,
    children: children
  });
};
describe('Text Component', function () {
  var defaultProps = {
    children: 'Test Text'
  };
  describe('Basic Rendering', function () {
    it('should render with default props', function () {
      var _render = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps))
        })),
        getByText = _render.getByText;
      expect(getByText('Test Text')).toBeTruthy();
    });
    it('should render with custom children', function () {
      var _render2 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps, {
            children: "Custom Text Content"
          }))
        })),
        getByText = _render2.getByText;
      expect(getByText('Custom Text Content')).toBeTruthy();
    });
    it('should render string content', function () {
      var _render3 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, {
            children: "Simple string"
          })
        })),
        getByText = _render3.getByText;
      expect(getByText('Simple string')).toBeTruthy();
    });
  });
  describe('Typography Variants', function () {
    it('should apply body variant by default', function () {
      var _render4 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps))
        })),
        getByText = _render4.getByText;
      var text = getByText('Test Text');
      expect(text).toBeTruthy();
    });
    it('should apply heading variant correctly', function () {
      var _render5 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps, {
            variant: "heading"
          }))
        })),
        getByText = _render5.getByText;
      var text = getByText('Test Text');
      expect(text).toBeTruthy();
    });
    it('should apply caption variant correctly', function () {
      var _render6 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps, {
            variant: "caption"
          }))
        })),
        getByText = _render6.getByText;
      var text = getByText('Test Text');
      expect(text).toBeTruthy();
    });
    it('should apply label variant correctly', function () {
      var _render7 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps, {
            variant: "label"
          }))
        })),
        getByText = _render7.getByText;
      var text = getByText('Test Text');
      expect(text).toBeTruthy();
    });
    it('should apply display variant correctly', function () {
      var _render8 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps, {
            variant: "display"
          }))
        })),
        getByText = _render8.getByText;
      var text = getByText('Test Text');
      expect(text).toBeTruthy();
    });
  });
  describe('Text Sizes', function () {
    it('should apply xs size correctly', function () {
      var _render9 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps, {
            size: "xs"
          }))
        })),
        getByText = _render9.getByText;
      var text = getByText('Test Text');
      expect(text).toBeTruthy();
    });
    it('should apply sm size correctly', function () {
      var _render0 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps, {
            size: "sm"
          }))
        })),
        getByText = _render0.getByText;
      var text = getByText('Test Text');
      expect(text).toBeTruthy();
    });
    it('should apply base size by default', function () {
      var _render1 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps))
        })),
        getByText = _render1.getByText;
      var text = getByText('Test Text');
      expect(text).toBeTruthy();
    });
    it('should apply lg size correctly', function () {
      var _render10 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps, {
            size: "lg"
          }))
        })),
        getByText = _render10.getByText;
      var text = getByText('Test Text');
      expect(text).toBeTruthy();
    });
    it('should apply xl size correctly', function () {
      var _render11 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps, {
            size: "xl"
          }))
        })),
        getByText = _render11.getByText;
      var text = getByText('Test Text');
      expect(text).toBeTruthy();
    });
  });
  describe('Text Colors', function () {
    it('should apply primary color by default', function () {
      var _render12 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps))
        })),
        getByText = _render12.getByText;
      var text = getByText('Test Text');
      expect(text).toBeTruthy();
    });
    it('should apply secondary color correctly', function () {
      var _render13 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps, {
            color: "secondary"
          }))
        })),
        getByText = _render13.getByText;
      var text = getByText('Test Text');
      expect(text).toBeTruthy();
    });
    it('should apply error color correctly', function () {
      var _render14 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps, {
            color: "error"
          }))
        })),
        getByText = _render14.getByText;
      var text = getByText('Test Text');
      expect(text).toBeTruthy();
    });
    it('should apply success color correctly', function () {
      var _render15 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps, {
            color: "success"
          }))
        })),
        getByText = _render15.getByText;
      var text = getByText('Test Text');
      expect(text).toBeTruthy();
    });
  });
  describe('Text Weights', function () {
    it('should apply normal weight by default', function () {
      var _render16 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps))
        })),
        getByText = _render16.getByText;
      var text = getByText('Test Text');
      expect(text).toBeTruthy();
    });
    it('should apply bold weight correctly', function () {
      var _render17 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps, {
            weight: "bold"
          }))
        })),
        getByText = _render17.getByText;
      var text = getByText('Test Text');
      expect(text).toBeTruthy();
    });
    it('should apply medium weight correctly', function () {
      var _render18 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps, {
            weight: "medium"
          }))
        })),
        getByText = _render18.getByText;
      var text = getByText('Test Text');
      expect(text).toBeTruthy();
    });
  });
  describe('Text Alignment', function () {
    it('should apply left alignment by default', function () {
      var _render19 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps))
        })),
        getByText = _render19.getByText;
      var text = getByText('Test Text');
      expect(text).toBeTruthy();
    });
    it('should apply center alignment correctly', function () {
      var _render20 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps, {
            align: "center"
          }))
        })),
        getByText = _render20.getByText;
      var text = getByText('Test Text');
      expect(text).toBeTruthy();
    });
    it('should apply right alignment correctly', function () {
      var _render21 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps, {
            align: "right"
          }))
        })),
        getByText = _render21.getByText;
      var text = getByText('Test Text');
      expect(text).toBeTruthy();
    });
  });
  describe('Accessibility', function () {
    it('should support custom accessibility label', function () {
      var _render22 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps, {
            accessibilityLabel: "Custom Text Label"
          }))
        })),
        getByLabelText = _render22.getByLabelText;
      expect(getByLabelText('Custom Text Label')).toBeTruthy();
    });
    it('should support accessibility hint', function () {
      var _render23 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps, {
            accessibilityHint: "This is descriptive text"
          }))
        })),
        getByText = _render23.getByText;
      var text = getByText('Test Text');
      expect(text.props.accessibilityHint).toBe('This is descriptive text');
    });
    it('should have proper accessibility role for text', function () {
      var _render24 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps))
        })),
        getByText = _render24.getByText;
      var text = getByText('Test Text');
      expect(text).toBeTruthy();
    });
  });
  describe('Custom Styling', function () {
    it('should apply custom styles', function () {
      var customStyle = {
        marginTop: 20
      };
      var _render25 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps, {
            style: customStyle
          }))
        })),
        getByText = _render25.getByText;
      var text = getByText('Test Text');
      expect(text).toHaveStyle(customStyle);
    });
    it('should merge custom styles with component styles', function () {
      var customStyle = {
        marginTop: 20
      };
      var _render26 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, Object.assign({}, defaultProps, {
            size: "lg",
            style: customStyle
          }))
        })),
        getByText = _render26.getByText;
      var text = getByText('Test Text');
      expect(text).toHaveStyle(customStyle);
    });
  });
  describe('Component Contract Compliance', function () {
    it('should handle text content properly', function () {
      var _render27 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, {
            children: "Text content"
          })
        })),
        getByText = _render27.getByText;
      var text = getByText('Text content');
      expect(text.type).toBe('Text');
    });
    it('should support complex prop combinations', function () {
      var _render28 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Text.Text, {
            variant: "heading",
            color: "error",
            size: "xl",
            weight: "bold",
            align: "center",
            children: "Complex Text"
          })
        })),
        getByText = _render28.getByText;
      var text = getByText('Complex Text');
      expect(text).toBeTruthy();
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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