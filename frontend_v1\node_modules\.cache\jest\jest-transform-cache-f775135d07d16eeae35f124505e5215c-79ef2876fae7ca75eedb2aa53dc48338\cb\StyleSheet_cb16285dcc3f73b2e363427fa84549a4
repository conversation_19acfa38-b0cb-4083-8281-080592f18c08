aa9f55c2e9295acba57533d874f6baf8
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _composeStyles = _interopRequireDefault(require("../../src/private/styles/composeStyles"));
var _flattenStyle = _interopRequireDefault(require("./flattenStyle"));
var ReactNativeStyleAttributes = require("../Components/View/ReactNativeStyleAttributes").default;
var PixelRatio = require("../Utilities/PixelRatio").default;
var hairlineWidth = PixelRatio.roundToNearestPixel(0.4);
if (hairlineWidth === 0) {
  hairlineWidth = 1 / PixelRatio.get();
}
var absoluteFill = {
  position: 'absolute',
  left: 0,
  right: 0,
  top: 0,
  bottom: 0
};
if (__DEV__) {
  Object.freeze(absoluteFill);
}
var _default = exports.default = {
  hairlineWidth: hairlineWidth,
  absoluteFill: absoluteFill,
  absoluteFillObject: absoluteFill,
  compose: _composeStyles.default,
  flatten: _flattenStyle.default,
  setStyleAttributePreprocessor: function setStyleAttributePreprocessor(property, process) {
    var _ReactNativeStyleAttr, _ReactNativeStyleAttr2;
    var value;
    if (ReactNativeStyleAttributes[property] === true) {
      value = {
        process: process
      };
    } else if (typeof ReactNativeStyleAttributes[property] === 'object') {
      value = Object.assign({}, ReactNativeStyleAttributes[property], {
        process: process
      });
    } else {
      console.error(`${property} is not a valid style attribute`);
      return;
    }
    if (__DEV__ && typeof value.process === 'function' && typeof ((_ReactNativeStyleAttr = ReactNativeStyleAttributes[property]) == null ? void 0 : _ReactNativeStyleAttr.process) === 'function' && value.process !== ((_ReactNativeStyleAttr2 = ReactNativeStyleAttributes[property]) == null ? void 0 : _ReactNativeStyleAttr2.process)) {
      console.warn(`Overwriting ${property} style attribute preprocessor`);
    }
    ReactNativeStyleAttributes[property] = value;
  },
  create: function create(obj) {
    if (__DEV__) {
      for (var _key in obj) {
        if (obj[_key]) {
          Object.freeze(obj[_key]);
        }
      }
    }
    return obj;
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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