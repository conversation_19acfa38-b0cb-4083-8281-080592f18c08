/**
 * Authentication Error Handler - Centralized Auth Error Management
 *
 * Service Contract:
 * - Handles authentication-related errors consistently
 * - Provides user-friendly error messages
 * - Manages error recovery strategies
 * - Integrates with logging and analytics
 * - Supports error categorization and routing
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Alert } from 'react-native';
import { navigationGuards } from './navigationGuards';

export interface AuthError {
  code: string;
  message: string;
  details?: any;
  recoverable: boolean;
  userMessage: string;
  action?: 'retry' | 'logout' | 'redirect' | 'none';
}

export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  additionalData?: any;
}

class AuthErrorHandler {
  private errorMap: Map<string, AuthError> = new Map();

  constructor() {
    this.initializeErrorMap();
  }

  /**
   * Initialize error mapping
   */
  private initializeErrorMap(): void {
    // Authentication errors
    this.errorMap.set('invalid_credentials', {
      code: 'invalid_credentials',
      message: 'Invalid email or password',
      recoverable: true,
      userMessage: 'Please check your email and password and try again.',
      action: 'retry',
    });

    this.errorMap.set('account_locked', {
      code: 'account_locked',
      message: 'Account is temporarily locked',
      recoverable: false,
      userMessage: 'Your account has been temporarily locked. Please contact support.',
      action: 'none',
    });

    this.errorMap.set('account_suspended', {
      code: 'account_suspended',
      message: 'Account is suspended',
      recoverable: false,
      userMessage: 'Your account has been suspended. Please contact support.',
      action: 'none',
    });

    this.errorMap.set('email_not_verified', {
      code: 'email_not_verified',
      message: 'Email address not verified',
      recoverable: true,
      userMessage: 'Please verify your email address before logging in.',
      action: 'redirect',
    });

    // Token errors
    this.errorMap.set('token_expired', {
      code: 'token_expired',
      message: 'Authentication token has expired',
      recoverable: true,
      userMessage: 'Your session has expired. Please log in again.',
      action: 'logout',
    });

    this.errorMap.set('token_invalid', {
      code: 'token_invalid',
      message: 'Authentication token is invalid',
      recoverable: true,
      userMessage: 'Your session is invalid. Please log in again.',
      action: 'logout',
    });

    this.errorMap.set('refresh_token_expired', {
      code: 'refresh_token_expired',
      message: 'Refresh token has expired',
      recoverable: true,
      userMessage: 'Your session has expired. Please log in again.',
      action: 'logout',
    });

    // Registration errors
    this.errorMap.set('email_already_exists', {
      code: 'email_already_exists',
      message: 'Email address is already registered',
      recoverable: true,
      userMessage: 'This email address is already registered. Please use a different email or try logging in.',
      action: 'retry',
    });

    this.errorMap.set('weak_password', {
      code: 'weak_password',
      message: 'Password does not meet security requirements',
      recoverable: true,
      userMessage: 'Please choose a stronger password with at least 8 characters, including uppercase, lowercase, and numbers.',
      action: 'retry',
    });

    // Network errors
    this.errorMap.set('network_error', {
      code: 'network_error',
      message: 'Network connection failed',
      recoverable: true,
      userMessage: 'Please check your internet connection and try again.',
      action: 'retry',
    });

    this.errorMap.set('server_error', {
      code: 'server_error',
      message: 'Server error occurred',
      recoverable: true,
      userMessage: 'A server error occurred. Please try again later.',
      action: 'retry',
    });

    // Permission errors
    this.errorMap.set('insufficient_permissions', {
      code: 'insufficient_permissions',
      message: 'Insufficient permissions for this action',
      recoverable: false,
      userMessage: 'You do not have permission to perform this action.',
      action: 'none',
    });

    this.errorMap.set('role_not_allowed', {
      code: 'role_not_allowed',
      message: 'User role is not allowed for this action',
      recoverable: false,
      userMessage: 'Your account type does not have access to this feature.',
      action: 'none',
    });
  }

  /**
   * Handle authentication error
   */
  async handleAuthError(
    error: any,
    context: ErrorContext = {},
  ): Promise<AuthError> {
    const authError = this.parseError(error);
    
    // Log error for debugging
    console.error('🔐 Auth Error:', {
      code: authError.code,
      message: authError.message,
      context,
      details: authError.details,
    });

    // Execute error action
    await this.executeErrorAction(authError, context);

    return authError;
  }

  /**
   * Parse error into standardized format
   */
  private parseError(error: any): AuthError {
    // Handle API response errors
    if (error.response?.data) {
      const responseData = error.response.data;
      
      // Check for specific error codes
      if (responseData.code && this.errorMap.has(responseData.code)) {
        const mappedError = this.errorMap.get(responseData.code)!;
        return {
          ...mappedError,
          details: responseData,
        };
      }

      // Handle Django REST framework errors
      if (responseData.detail) {
        return this.mapDjangoError(responseData.detail, responseData);
      }

      if (responseData.non_field_errors) {
        return this.mapDjangoError(responseData.non_field_errors[0], responseData);
      }

      // Handle field-specific errors
      if (responseData.errors) {
        const firstError = Object.values(responseData.errors)[0];
        if (Array.isArray(firstError) && firstError.length > 0) {
          return this.mapDjangoError(firstError[0], responseData);
        }
      }
    }

    // Handle network errors
    if (error.code === 'NETWORK_ERROR' || !error.response) {
      return this.errorMap.get('network_error')!;
    }

    // Handle HTTP status codes
    if (error.response?.status) {
      switch (error.response.status) {
        case 401:
          return this.errorMap.get('token_invalid')!;
        case 403:
          return this.errorMap.get('insufficient_permissions')!;
        case 500:
          return this.errorMap.get('server_error')!;
      }
    }

    // Default error
    return {
      code: 'unknown_error',
      message: error.message || 'An unknown error occurred',
      recoverable: true,
      userMessage: 'An unexpected error occurred. Please try again.',
      action: 'retry',
      details: error,
    };
  }

  /**
   * Map Django error messages to standardized errors
   */
  private mapDjangoError(message: string, details: any): AuthError {
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('invalid credentials') || lowerMessage.includes('unable to log in')) {
      return { ...this.errorMap.get('invalid_credentials')!, details };
    }

    if (lowerMessage.includes('email') && lowerMessage.includes('already')) {
      return { ...this.errorMap.get('email_already_exists')!, details };
    }

    if (lowerMessage.includes('password') && lowerMessage.includes('weak')) {
      return { ...this.errorMap.get('weak_password')!, details };
    }

    if (lowerMessage.includes('email') && lowerMessage.includes('verify')) {
      return { ...this.errorMap.get('email_not_verified')!, details };
    }

    if (lowerMessage.includes('token') && lowerMessage.includes('expired')) {
      return { ...this.errorMap.get('token_expired')!, details };
    }

    if (lowerMessage.includes('token') && lowerMessage.includes('invalid')) {
      return { ...this.errorMap.get('token_invalid')!, details };
    }

    // Default mapping
    return {
      code: 'api_error',
      message,
      recoverable: true,
      userMessage: message,
      action: 'retry',
      details,
    };
  }

  /**
   * Execute error action
   */
  private async executeErrorAction(
    authError: AuthError,
    context: ErrorContext,
  ): Promise<void> {
    switch (authError.action) {
      case 'logout':
        // Import auth store dynamically to avoid circular dependencies
        const { useAuthStore } = await import('../store/authSlice');
        const authStore = useAuthStore.getState();
        authStore.logout();
        break;

      case 'redirect':
        if (authError.code === 'email_not_verified') {
          // Navigate to email verification screen
          // This would be handled by the navigation system
          console.log('Redirecting to email verification');
        }
        break;

      case 'retry':
        // Show retry option in alert
        this.showRetryAlert(authError);
        break;

      case 'none':
      default:
        // Just show the error message
        this.showErrorAlert(authError);
        break;
    }
  }

  /**
   * Show error alert with retry option
   */
  private showRetryAlert(authError: AuthError): void {
    Alert.alert(
      'Authentication Error',
      authError.userMessage,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Retry', onPress: () => console.log('Retry requested') },
      ],
    );
  }

  /**
   * Show simple error alert
   */
  private showErrorAlert(authError: AuthError): void {
    Alert.alert(
      'Authentication Error',
      authError.userMessage,
      [{ text: 'OK' }],
    );
  }

  /**
   * Get user-friendly error message
   */
  getUserMessage(error: any): string {
    const authError = this.parseError(error);
    return authError.userMessage;
  }

  /**
   * Check if error is recoverable
   */
  isRecoverable(error: any): boolean {
    const authError = this.parseError(error);
    return authError.recoverable;
  }
}

// Export singleton instance
export const authErrorHandler = new AuthErrorHandler();
export default authErrorHandler;
