/**
 * Search Screen - Service Discovery for Customers
 *
 * Component Contract:
 * - Provides service search functionality for customers
 * - Integrates with backend API for real-time service data
 * - Displays search results with clean design matching frontend_v0
 * - Supports navigation to service details
 * - Implements filters, map view, and provider listings
 * - Uses unified error handling system
 *
 * @version 3.0.0 - Complete rebuild based on frontend_v0 with backend integration
 * <AUTHOR> Development Team
 */

import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import type { StackNavigationProp, RouteProp } from '@react-navigation/stack';
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  StatusBar,
  Platform,
  Dimensions,
  RefreshControl,
  Keyboard,
} from 'react-native';

import { Box } from '../components/atoms/Box';
import { Button } from '../components/atoms/Button';
import { Input } from '../components/atoms/Input';
import { SafeAreaScreen } from '../components/ui/SafeAreaWrapper';
import { useTheme } from '../contexts/ThemeContext';
import { MapViewComponent } from '../components/molecules/MapView';
import { StoreImage } from '../components/molecules/StoreImage';
import { useUnifiedErrorHandling } from '../services/unifiedErrorHandling';
import { locationService } from '../services/locationService';
import type { LocationData } from '../services/locationService';

// Import service discovery API and types
import { serviceDiscoveryApi } from '../features/service-discovery/services/serviceDiscoveryApi';
import type {
  Service,
  ServiceProvider,
  SearchFilters,
  SearchResponse
} from '../features/service-discovery/types';

// Navigation types
import type { CustomerStackParamList } from '../navigation/types';

interface SearchState {
  query: string;
  filters: SearchFilters;
  results: {
    services: Service[];
    providers: ServiceProvider[];
  };
  isLoading: boolean;
  isRefreshing: boolean;
  error: string | null;
  hasMore: boolean;
  page: number;
  totalResults: number;
}

// Screen dimensions for responsive design
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Responsive utilities
const getResponsiveFontSize = (size: number) => {
  const scale = Math.min(screenWidth / 375, 1.3);
  return Math.round(size * scale);
};

const getResponsiveSpacing = (spacing: number) => {
  const scale = Math.min(screenWidth / 375, 1.2);
  return Math.round(spacing * scale);
};

// Device detection
const isTablet = screenWidth >= 768;
const hasNotch = Platform.OS === 'ios' && screenHeight >= 812;

// Navigation types
type SearchScreenNavigationProp = StackNavigationProp<CustomerStackParamList, 'Search'>;
type SearchScreenRouteProp = RouteProp<CustomerStackParamList, 'Search'>;

export const SearchScreen: React.FC = () => {
  const navigation = useNavigation<SearchScreenNavigationProp>();
  const route = useRoute<SearchScreenRouteProp>();
  const { colors } = useTheme();

  // Unified error handling
  const { handleError, withErrorHandling, clearError } = useUnifiedErrorHandling({
    component: 'SearchScreen',
    screen: 'Search'
  });

  // Get initial category from navigation params
  const initialCategory = route.params?.categoryName;

  // Location state
  const [userLocation, setUserLocation] = useState<LocationData | null>(null);
  const [locationPermissionGranted, setLocationPermissionGranted] = useState(false);
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);

  // Enhanced state management
  const [searchState, setSearchState] = useState<SearchState>({
    query: '',
    filters: {
      category: initialCategory,
      price_min: 0,
      price_max: 1000,
      rating_min: 0,
      location: undefined,
      distance_max: 10, // Default 10km radius
      is_popular: undefined,
      availability: undefined,
    },
    results: {
      services: [],
      providers: [],
    },
    isLoading: false,
    isRefreshing: false,
    error: null,
    hasMore: false,
    page: 1,
    totalResults: 0,
  });

  // UI state
  const [showFilters, setShowFilters] = useState(false);
  const [showMap, setShowMap] = useState(false);
  const [activeTab, setActiveTab] = useState<'services' | 'providers'>('services');

  // Debounced search function with backend integration
  const debouncedSearch = useMemo(
    () => {
      let timeoutId: NodeJS.Timeout;
      return (query: string, filters: SearchFilters) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(async () => {
          await performSearch(query, filters);
        }, 300);
      };
    },
    []
  );

  // Request location permission and get user location
  const requestLocationPermission = useCallback(async () => {
    if (locationPermissionGranted) return userLocation;

    setIsLoadingLocation(true);
    try {
      const hasPermission = await locationService.requestLocationPermission();
      setLocationPermissionGranted(hasPermission);

      if (hasPermission) {
        const location = await locationService.getCurrentLocation();
        setUserLocation(location);
        console.log('📍 Got user location:', location);
        return location;
      }
    } catch (error) {
      console.error('📍 Failed to get location:', error);
    } finally {
      setIsLoadingLocation(false);
    }
    return null;
  }, [locationPermissionGranted, userLocation]);

  // Perform search with backend API (enhanced with location support)
  const performSearch = async (query: string, filters: SearchFilters, page: number = 1, useLocation: boolean = true) => {
    console.log('🔍 performSearch called with query:', query, 'filters:', filters, 'page:', page);

    // Allow empty query for initial load, but require query >= 2 chars for search
    const hasFilters = Object.values(filters).some(v => v !== undefined && v !== '');
    if (query.length > 0 && query.length < 2 && !hasFilters) {
      console.log('🔍 performSearch skipped - query too short and no filters');
      return;
    }

    // Add location data to filters if available and requested
    let enhancedFilters = { ...filters };
    if (useLocation && userLocation?.coordinates) {
      enhancedFilters.location = {
        latitude: userLocation.coordinates.latitude,
        longitude: userLocation.coordinates.longitude,
        radius: filters.distance_max || 10, // Default 10km radius
      };
      console.log('📍 Adding location to search:', enhancedFilters.location);
    }

    setSearchState(prev => ({ 
      ...prev, 
      isLoading: page === 1, 
      error: null 
    }));

    const { data, error } = await withErrorHandling(
      async () => {
        const response = await serviceDiscoveryApi.search(query, enhancedFilters);
        return response;
      },
      {
        action: 'search_services',
        additionalData: { query, filters: enhancedFilters, page }
      }
    );

    if (data) {
      setSearchState(prev => ({
        ...prev,
        results: {
          services: page === 1 ? data.services.results : [...prev.results.services, ...data.services.results],
          providers: page === 1 ? data.providers.results : [...prev.results.providers, ...data.providers.results],
        },
        hasMore: !!data.services.next || !!data.providers.next,
        totalResults: data.total_results || (data.services.count + data.providers.count),
        page: page,
        isLoading: false,
        isRefreshing: false,
      }));
    } else {
      setSearchState(prev => ({
        ...prev,
        error: error?.message || 'Search failed',
        isLoading: false,
        isRefreshing: false,
      }));
    }
  };

  // Handle search input
  const handleSearch = useCallback(() => {
    debouncedSearch(searchState.query, searchState.filters);
  }, [searchState.query, searchState.filters, debouncedSearch]);

  // Handle nearby search
  const handleSearchNearby = useCallback(async () => {
    console.log('📍 Starting nearby search');

    // Get location if not already available
    let location = userLocation;
    if (!location) {
      location = await requestLocationPermission();
      if (!location) {
        console.log('📍 Location not available for nearby search');
        return;
      }
    }

    // Perform search with location
    const query = searchState.query || 'services'; // Use current query or default
    console.log('📍 Performing nearby search with location:', location.coordinates);
    performSearch(query, searchState.filters, 1, true);
  }, [userLocation, requestLocationPermission, searchState.query, searchState.filters, performSearch]);

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    setSearchState(prev => ({ ...prev, isRefreshing: true }));
    await performSearch(searchState.query, searchState.filters, 1);
  }, [searchState.query, searchState.filters]);

  // Load more results
  const loadMore = useCallback(async () => {
    if (searchState.hasMore && !searchState.isLoading) {
      await performSearch(searchState.query, searchState.filters, searchState.page + 1);
    }
  }, [searchState.hasMore, searchState.isLoading, searchState.query, searchState.filters, searchState.page]);

  // Update search query
  const updateQuery = useCallback((query: string) => {
    setSearchState(prev => ({ ...prev, query }));
    if (query.length >= 2) {
      debouncedSearch(query, searchState.filters);
    }
  }, [searchState.filters, debouncedSearch]);

  // Update filters
  const updateFilters = useCallback((newFilters: Partial<SearchFilters>) => {
    const updatedFilters = { ...searchState.filters, ...newFilters };
    setSearchState(prev => ({ ...prev, filters: updatedFilters }));
    debouncedSearch(searchState.query, updatedFilters);
  }, [searchState.query, searchState.filters, debouncedSearch]);

  // Navigate to service details
  const navigateToService = useCallback((serviceId: string) => {
    navigation.navigate('ServiceDetails', { serviceId });
  }, [navigation]);

  // Navigate to provider details
  const navigateToProvider = useCallback((providerId: string) => {
    navigation.navigate('ProviderDetails', { providerId });
  }, [navigation]);

  // Location initialization effect
  useEffect(() => {
    console.log('📍 SearchScreen initializing location services');
    requestLocationPermission();
  }, [requestLocationPermission]);

  // Load initial data on mount
  useEffect(() => {
    console.log('🔍 SearchScreen mounted, initialCategory:', initialCategory);
    if (initialCategory) {
      console.log('🔍 SearchScreen updating filters with category:', initialCategory);
      updateFilters({ category: initialCategory });
    } else {
      // Load initial services and providers when screen mounts without category
      // Use a default search query to satisfy backend requirements
      console.log('🔍 SearchScreen performing initial search with default query');
      performSearch('beauty', {}, 1);
    }
  }, [initialCategory, updateFilters]);

  // Auto-search when query changes
  useEffect(() => {
    if (searchState.query.length >= 2) {
      handleSearch();
    }
  }, [searchState.query, handleSearch]);

  return (
    <SafeAreaScreen style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />

      {/* Header - Redesigned to match Messages screen */}
      <View style={styles.header}>
        <Text style={styles.title}>Search Services</Text>
        <TouchableOpacity
          style={styles.headerActionButton}
          testID="search-help-button"
          accessibilityLabel="Search help"
          accessibilityRole="button">
          <Ionicons name="help-circle-outline" size={24} color={colors.text.primary} />
        </TouchableOpacity>
      </View>

      {/* Search Section - Redesigned with frontend_v0 inspiration */}
      <Box style={styles.searchSection}>
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Ionicons
              name="search"
              size={18}
              color={colors.sage400}
              style={styles.searchIcon}
            />
            <Input
              testID="search-input"
              placeholder="Search services, providers, locations..."
              value={searchState.query}
              onChangeText={updateQuery}
              style={styles.searchInput}
            />
          </View>

          <Button
            testID="search-button"
            onPress={handleSearch}
            style={styles.searchButton}
            variant="primary">
            Search
          </Button>
        </View>

        <View style={styles.buttonRow}>
          <Button
            testID="filter-button"
            onPress={() => setShowFilters(!showFilters)}
            variant="secondary"
            style={styles.filterButton}>
            {showFilters ? 'Hide Filters' : 'Filters'}
          </Button>

          <Button
            testID="search-nearby-button"
            onPress={handleSearchNearby}
            variant="secondary"
            style={styles.nearbyButton}
            disabled={isLoadingLocation}>
            {isLoadingLocation ? 'Loading...' : '📍 Nearby'}
          </Button>

          <Button
            testID="map-button"
            onPress={() => setShowMap(!showMap)}
            variant="secondary"
            style={styles.mapButton}>
            {showMap ? 'Hide Map' : 'Show Map'}
          </Button>
        </View>
      </Box>

      {/* Loading indicator */}
      {searchState.isLoading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary?.default || '#4A6B52'} />
          <Text style={styles.loadingText}>Searching...</Text>
        </View>
      )}

      {/* Error message */}
      {searchState.error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{searchState.error}</Text>
          <Button onPress={() => setSearchState(prev => ({ ...prev, error: null }))}>
            Dismiss
          </Button>
        </View>
      )}

      {/* Filters Panel */}
      {showFilters && (
        <View style={styles.filtersPanel}>
          <Text style={styles.filterTitle}>Filters</Text>

          {/* Category Filter */}
          <View style={styles.filterSection}>
            <Text style={styles.filterLabel}>Category</Text>
            <Input
              placeholder="Select category..."
              value={searchState.filters.category || ''}
              onChangeText={(category) => updateFilters({ category })}
              style={styles.filterInput}
            />
          </View>

          {/* Price Range Filter */}
          <View style={styles.filterSection}>
            <Text style={styles.filterLabel}>Price Range</Text>
            <View style={styles.priceRangeContainer}>
              <Input
                placeholder="Min"
                value={searchState.filters.price_min?.toString() || ''}
                onChangeText={(text) => updateFilters({ price_min: parseInt(text) || 0 })}
                style={[styles.filterInput, styles.priceInput]}
                keyboardType="numeric"
              />
              <Text style={styles.priceRangeSeparator}>-</Text>
              <Input
                placeholder="Max"
                value={searchState.filters.price_max?.toString() || ''}
                onChangeText={(text) => updateFilters({ price_max: parseInt(text) || 1000 })}
                style={[styles.filterInput, styles.priceInput]}
                keyboardType="numeric"
              />
            </View>
          </View>

          {/* Rating Filter */}
          <View style={styles.filterSection}>
            <Text style={styles.filterLabel}>Minimum Rating</Text>
            <Input
              placeholder="0-5 stars"
              value={searchState.filters.rating_min?.toString() || ''}
              onChangeText={(text) => updateFilters({ rating_min: parseFloat(text) || 0 })}
              style={styles.filterInput}
              keyboardType="numeric"
            />
          </View>

          {/* Clear Filters Button */}
          <Button
            onPress={() => updateFilters({
              category: undefined,
              price_min: 0,
              price_max: 1000,
              rating_min: 0
            })}
            variant="secondary"
            style={styles.clearFiltersButton}>
            Clear Filters
          </Button>
        </View>
      )}

      {/* Map View */}
      {showMap && (
        <View style={styles.mapContainer}>
          <MapViewComponent
            providers={searchState.results.providers}
            onProviderPress={navigateToProvider}
            style={styles.mapView}
          />
        </View>
      )}

      {/* Results Tabs */}
      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'services' && styles.activeTab]}
          onPress={() => setActiveTab('services')}>
          <Text style={[styles.tabText, activeTab === 'services' && styles.activeTabText]}>
            Services ({searchState.results.services.length})
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'providers' && styles.activeTab]}
          onPress={() => setActiveTab('providers')}>
          <Text style={[styles.tabText, activeTab === 'providers' && styles.activeTabText]}>
            Providers ({searchState.results.providers.length})
          </Text>
        </TouchableOpacity>
      </View>

      {/* Results List */}
      <FlatList
        data={activeTab === 'services' ? searchState.results.services : searchState.results.providers}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) =>
          activeTab === 'services'
            ? <ServiceCard service={item as Service} onPress={() => navigateToService(item.id)} />
            : <ProviderCard provider={item as ServiceProvider} onPress={() => navigateToProvider(item.id)} />
        }
        style={styles.resultsList}
        contentContainerStyle={styles.resultsListContent}
        refreshControl={
          <RefreshControl
            refreshing={searchState.isRefreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary?.default || '#4A6B52']}
          />
        }
        onEndReached={loadMore}
        onEndReachedThreshold={0.1}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              {searchState.query.length < 2
                ? 'Enter a search term to find services'
                : 'No results found. Try adjusting your search or filters.'
              }
            </Text>
          </View>
        }
        ListFooterComponent={
          searchState.hasMore && searchState.results.services.length > 0 ? (
            <View style={styles.loadMoreContainer}>
              <ActivityIndicator size="small" color={colors.primary?.default || '#4A6B52'} />
              <Text style={styles.loadMoreText}>Loading more...</Text>
            </View>
          ) : null
        }
      />
    </SafeAreaScreen>
  );
};

// Service Card Component
const ServiceCard: React.FC<{ service: Service; onPress: () => void }> = ({ service, onPress }) => {
  const { colors } = useTheme();

  return (
    <TouchableOpacity style={styles.serviceCard} onPress={onPress}>
      <View style={styles.serviceCardContent}>
        <View style={styles.serviceInfo}>
          <Text style={styles.serviceName}>{service.name}</Text>
          <Text style={styles.serviceDescription} numberOfLines={2}>
            {service.description}
          </Text>
          <View style={styles.serviceDetails}>
            <Text style={styles.servicePrice}>${service.base_price}</Text>
            <Text style={styles.serviceDuration}>{service.duration} min</Text>
            {service.provider_details?.rating && (
              <View style={styles.ratingContainer}>
                <Ionicons name="star" size={16} color="#FFD700" />
                <Text style={styles.ratingText}>{service.provider_details.rating.toFixed(1)}</Text>
              </View>
            )}
            {service.distance && (
              <View style={styles.distanceContainer}>
                <Ionicons name="location" size={14} color="#6B7280" />
                <Text style={styles.distanceText}>{service.distance.toFixed(1)} km</Text>
              </View>
            )}
          </View>
          <Text style={styles.providerName}>{service.provider_details?.business_name || 'Provider'}</Text>
        </View>
        {service.images && service.images.length > 0 && (
          <StoreImage
            uri={service.images[0]}
            style={styles.serviceImage}
            fallbackIcon="image-outline"
          />
        )}
      </View>
    </TouchableOpacity>
  );
};

// Provider Card Component
const ProviderCard: React.FC<{ provider: ServiceProvider; onPress: () => void }> = ({ provider, onPress }) => {
  const { colors } = useTheme();

  return (
    <TouchableOpacity style={styles.providerCard} onPress={onPress}>
      <View style={styles.providerCardContent}>
        <View style={styles.providerInfo}>
          <Text style={styles.providerName}>{provider.business_name}</Text>
          {provider.description && (
            <Text style={styles.providerDescription} numberOfLines={2}>
              {provider.description}
            </Text>
          )}
          <Text style={styles.providerAddress}>
            {provider.address}, {provider.city}, {provider.state}
          </Text>
          <View style={styles.providerDetails}>
            <View style={styles.ratingContainer}>
              <Ionicons name="star" size={16} color="#FFD700" />
              <Text style={styles.ratingText}>{provider.rating.toFixed(1)}</Text>
              <Text style={styles.reviewCount}>({provider.review_count})</Text>
            </View>
            {provider.is_verified && (
              <View style={styles.verifiedBadge}>
                <Ionicons name="checkmark-circle" size={16} color="#10B981" />
                <Text style={styles.verifiedText}>Verified</Text>
              </View>
            )}
            {provider.distance && (
              <View style={styles.distanceContainer}>
                <Ionicons name="location" size={14} color="#6B7280" />
                <Text style={styles.distanceText}>{provider.distance.toFixed(1)} km</Text>
              </View>
            )}
          </View>
          <Text style={styles.servicesCount}>
            Services available
          </Text>
        </View>
        {provider.profile_image && (
          <StoreImage
            uri={provider.profile_image}
            style={styles.providerImage}
            fallbackIcon="person-outline"
          />
        )}
      </View>
    </TouchableOpacity>
  );
};

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(20),
    paddingVertical: getResponsiveSpacing(16),
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: getResponsiveFontSize(24),
    fontWeight: '700',
    color: '#333333',
  },
  headerActionButton: {
    padding: getResponsiveSpacing(8),
    borderRadius: getResponsiveSpacing(8),
  },
  searchSection: {
    padding: getResponsiveSpacing(16),
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  searchContainer: {
    gap: getResponsiveSpacing(12),
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: getResponsiveSpacing(12),
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(4),
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  searchIcon: {
    marginRight: getResponsiveSpacing(12),
  },
  searchInput: {
    flex: 1,
    fontSize: getResponsiveFontSize(16),
    color: '#333333',
    paddingVertical: getResponsiveSpacing(12),
    backgroundColor: 'transparent',
    borderWidth: 0, // Remove default border since container has border
  },
  searchButton: {
    paddingVertical: getResponsiveSpacing(14),
    paddingHorizontal: getResponsiveSpacing(24),
    borderRadius: getResponsiveSpacing(12),
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  filterButton: {
    flex: 1,
    marginRight: getResponsiveSpacing(4),
  },
  nearbyButton: {
    flex: 1,
    marginHorizontal: getResponsiveSpacing(4),
  },
  mapButton: {
    flex: 1,
    marginLeft: getResponsiveSpacing(4),
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: getResponsiveSpacing(20),
  },
  loadingText: {
    marginTop: getResponsiveSpacing(12),
    fontSize: getResponsiveFontSize(16),
    color: '#6B7280',
  },
  errorContainer: {
    padding: getResponsiveSpacing(16),
    backgroundColor: '#FEF2F2',
    borderLeftWidth: 4,
    borderLeftColor: '#EF4444',
    margin: getResponsiveSpacing(16),
  },
  errorText: {
    fontSize: getResponsiveFontSize(14),
    color: '#DC2626',
    marginBottom: getResponsiveSpacing(8),
  },
  // Filters Panel Styles
  filtersPanel: {
    backgroundColor: '#F9FAFB',
    padding: getResponsiveSpacing(16),
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  filterTitle: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '600',
    color: '#111827',
    marginBottom: getResponsiveSpacing(16),
  },
  filterSection: {
    marginBottom: getResponsiveSpacing(16),
  },
  filterLabel: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '500',
    color: '#374151',
    marginBottom: getResponsiveSpacing(8),
  },
  filterInput: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: getResponsiveSpacing(12),
    fontSize: getResponsiveFontSize(14),
  },
  priceRangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priceInput: {
    flex: 1,
  },
  priceRangeSeparator: {
    marginHorizontal: getResponsiveSpacing(12),
    fontSize: getResponsiveFontSize(16),
    color: '#6B7280',
  },
  clearFiltersButton: {
    marginTop: getResponsiveSpacing(8),
  },

  // Map View Styles
  mapContainer: {
    height: 300,
    backgroundColor: '#F3F4F6',
  },
  mapView: {
    flex: 1,
  },

  // Tabs Styles
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  tab: {
    flex: 1,
    paddingVertical: getResponsiveSpacing(16),
    paddingHorizontal: getResponsiveSpacing(12),
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: '#3B82F6',
  },
  tabText: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '500',
    color: '#6B7280',
  },
  activeTabText: {
    color: '#3B82F6',
    fontWeight: '600',
  },

  // Results List Styles
  resultsList: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  resultsListContent: {
    padding: getResponsiveSpacing(16),
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: getResponsiveSpacing(40),
  },
  emptyText: {
    fontSize: getResponsiveFontSize(16),
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  loadMoreContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: getResponsiveSpacing(20),
  },
  loadMoreText: {
    marginLeft: getResponsiveSpacing(8),
    fontSize: getResponsiveFontSize(14),
    color: '#6B7280',
  },

  // Service Card Styles
  serviceCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: getResponsiveSpacing(16),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  serviceCardContent: {
    flexDirection: 'row',
    padding: getResponsiveSpacing(16),
  },
  serviceInfo: {
    flex: 1,
    marginRight: getResponsiveSpacing(12),
  },
  serviceName: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    color: '#111827',
    marginBottom: getResponsiveSpacing(4),
  },
  serviceDescription: {
    fontSize: getResponsiveFontSize(14),
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: getResponsiveSpacing(8),
  },
  serviceDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(8),
  },
  servicePrice: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    color: '#059669',
    marginRight: getResponsiveSpacing(12),
  },
  serviceDuration: {
    fontSize: getResponsiveFontSize(14),
    color: '#6B7280',
    marginRight: getResponsiveSpacing(12),
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: getResponsiveFontSize(14),
    color: '#374151',
    marginLeft: getResponsiveSpacing(4),
  },
  reviewCount: {
    fontSize: getResponsiveFontSize(12),
    color: '#6B7280',
    marginLeft: getResponsiveSpacing(4),
  },
  distanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: getResponsiveSpacing(8),
  },
  distanceText: {
    fontSize: getResponsiveFontSize(12),
    color: '#6B7280',
    marginLeft: 2,
  },
  providerName: {
    fontSize: getResponsiveFontSize(12),
    color: '#6B7280',
    fontStyle: 'italic',
  },
  serviceImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },

  // Provider Card Styles
  providerCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: getResponsiveSpacing(16),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  providerCardContent: {
    flexDirection: 'row',
    padding: getResponsiveSpacing(16),
  },
  providerInfo: {
    flex: 1,
    marginRight: getResponsiveSpacing(12),
  },
  providerDescription: {
    fontSize: getResponsiveFontSize(14),
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: getResponsiveSpacing(8),
  },
  providerAddress: {
    fontSize: getResponsiveFontSize(12),
    color: '#6B7280',
    marginBottom: getResponsiveSpacing(8),
  },
  providerDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(8),
  },
  verifiedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: getResponsiveSpacing(12),
  },
  verifiedText: {
    fontSize: getResponsiveFontSize(12),
    color: '#10B981',
    marginLeft: getResponsiveSpacing(4),
    fontWeight: '500',
  },
  servicesCount: {
    fontSize: getResponsiveFontSize(12),
    color: '#6B7280',
  },
  providerImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
});

export default SearchScreen;
