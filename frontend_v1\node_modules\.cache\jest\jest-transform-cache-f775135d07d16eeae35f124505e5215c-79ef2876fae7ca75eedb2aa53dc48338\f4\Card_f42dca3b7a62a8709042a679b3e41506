e044e8fa145a39a222b310488bfd243f
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Card = void 0;
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _react = _interopRequireDefault(require("react"));
var _Box = require("./Box");
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["children"];
var Card = exports.Card = function Card(_ref) {
  var children = _ref.children,
    props = (0, _objectWithoutProperties2.default)(_ref, _excluded);
  return (0, _jsxRuntime.jsx)(_Box.Box, Object.assign({
    backgroundColor: "surface",
    padding: "medium",
    borderRadius: "medium"
  }, props, {
    children: children
  }));
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfcmVhY3QiLCJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0IiwicmVxdWlyZSIsIl9Cb3giLCJfanN4UnVudGltZSIsIl9leGNsdWRlZCIsIkNhcmQiLCJleHBvcnRzIiwiX3JlZiIsImNoaWxkcmVuIiwicHJvcHMiLCJfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMyIiwiZGVmYXVsdCIsImpzeCIsIkJveCIsIk9iamVjdCIsImFzc2lnbiIsImJhY2tncm91bmRDb2xvciIsInBhZGRpbmciLCJib3JkZXJSYWRpdXMiXSwic291cmNlcyI6WyJDYXJkLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENhcmQgQ29tcG9uZW50IC0gU3R5bGVkIENvbnRhaW5lciBBdG9tXG4gKlxuICogQSBzdHlsZWQgY29udGFpbmVyIGNvbXBvbmVudCB0aGF0IHByb3ZpZGVzIGNhcmQtbGlrZSBhcHBlYXJhbmNlXG4gKiB3aXRoIGNvbnNpc3RlbnQgc3R5bGluZyBhY3Jvc3MgdGhlIGFwcGxpY2F0aW9uLlxuICpcbiAqIEB2ZXJzaW9uIDEuMC4wXG4gKiBAYXV0aG9yIFZpZXJsYSBEZXZlbG9wbWVudCBUZWFtXG4gKi9cblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcblxuaW1wb3J0IHsgQm94LCBCb3hQcm9wcyB9IGZyb20gJy4vQm94JztcblxuZXhwb3J0IGludGVyZmFjZSBDYXJkUHJvcHMgZXh0ZW5kcyBCb3hQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59XG5cbmV4cG9ydCBjb25zdCBDYXJkOiBSZWFjdC5GQzxDYXJkUHJvcHM+ID0gKHsgY2hpbGRyZW4sIC4uLnByb3BzIH0pID0+IHtcbiAgcmV0dXJuIChcbiAgICA8Qm94XG4gICAgICBiYWNrZ3JvdW5kQ29sb3I9XCJzdXJmYWNlXCJcbiAgICAgIHBhZGRpbmc9XCJtZWRpdW1cIlxuICAgICAgYm9yZGVyUmFkaXVzPVwibWVkaXVtXCJcbiAgICAgIHsuLi5wcm9wc30+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9Cb3g+XG4gICk7XG59O1xuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7QUFVQSxJQUFBQSxNQUFBLEdBQUFDLHNCQUFBLENBQUFDLE9BQUE7QUFFQSxJQUFBQyxJQUFBLEdBQUFELE9BQUE7QUFBc0MsSUFBQUUsV0FBQSxHQUFBRixPQUFBO0FBQUEsSUFBQUcsU0FBQTtBQU0vQixJQUFNQyxJQUF5QixHQUFBQyxPQUFBLENBQUFELElBQUEsR0FBRyxTQUE1QkEsSUFBeUJBLENBQUFFLElBQUEsRUFBK0I7RUFBQSxJQUF6QkMsUUFBUSxHQUFBRCxJQUFBLENBQVJDLFFBQVE7SUFBS0MsS0FBSyxPQUFBQyx5QkFBQSxDQUFBQyxPQUFBLEVBQUFKLElBQUEsRUFBQUgsU0FBQTtFQUM1RCxPQUNFLElBQUFELFdBQUEsQ0FBQVMsR0FBQSxFQUFDVixJQUFBLENBQUFXLEdBQUcsRUFBQUMsTUFBQSxDQUFBQyxNQUFBO0lBQ0ZDLGVBQWUsRUFBQyxTQUFTO0lBQ3pCQyxPQUFPLEVBQUMsUUFBUTtJQUNoQkMsWUFBWSxFQUFDO0VBQVEsR0FDakJULEtBQUs7SUFBQUQsUUFBQSxFQUNSQTtFQUFRLEVBQ04sQ0FBQztBQUVWLENBQUMiLCJpZ25vcmVMaXN0IjpbXX0=