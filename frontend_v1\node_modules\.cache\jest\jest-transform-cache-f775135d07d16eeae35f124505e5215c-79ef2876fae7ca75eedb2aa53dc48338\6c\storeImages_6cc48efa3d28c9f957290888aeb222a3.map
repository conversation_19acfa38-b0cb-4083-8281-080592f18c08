{"version": 3, "names": ["STORE_IMAGES", "exports", "hair_salon_1", "hair_salon_2", "hair_salon_3", "hair_salon_4", "hair_salon_5", "nail_salon_1", "nail_salon_2", "nail_salon_3", "nail_salon_4", "nail_salon_5", "lash_salon_1", "lash_salon_2", "lash_salon_3", "lash_salon_4", "lash_salon_5", "braiding_salon_1", "braiding_salon_2", "braiding_salon_3", "braiding_salon_4", "braiding_salon_5", "massage_salon_1", "massage_salon_2", "massage_salon_3", "massage_salon_4", "massage_salon_5", "skincare_salon_1", "skincare_salon_2", "skincare_salon_3", "skincare_salon_4", "skincare_salon_5", "CATEGORY_IMAGES", "<PERSON>", "Salon", "Braiding", "Massage", "Skincare", "getStoreImage", "providerId", "category", "categoryImages", "safeProviderId", "hash", "split", "reduce", "acc", "char", "charCodeAt", "imageIndex", "length", "image<PERSON>ey", "getRandomStoreImage", "allImages", "Object", "values", "Math", "floor", "random", "randomIndex", "getAvailableCategories", "keys", "isValidImageUrl", "url", "URL", "_unused", "getFallbackImage", "providerName", "safeName", "firstLetter", "char<PERSON>t", "toUpperCase", "charCode"], "sources": ["storeImages.ts"], "sourcesContent": ["/**\n * Store Images Utility\n *\n * Provides store/provider images for the Vierla application.\n * Includes placeholder images and real store images for different service categories.\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\n// Store image URLs - Using placeholder images from Unsplash for different service categories\nexport const STORE_IMAGES = {\n  // Hair Services\n  hair_salon_1:\n    'https://images.unsplash.com/photo-**********-138dadb4c035?w=400&h=400&fit=crop&crop=center',\n  hair_salon_2:\n    'https://images.unsplash.com/photo-1521590832167-7bcbfaa6381f?w=400&h=400&fit=crop&crop=center',\n  hair_salon_3:\n    'https://images.unsplash.com/photo-**********-8baeececf3df?w=400&h=400&fit=crop&crop=center',\n  hair_salon_4:\n    'https://images.unsplash.com/photo-**********-f09722fb4948?w=400&h=400&fit=crop&crop=center',\n  hair_salon_5:\n    'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=400&fit=crop&crop=center',\n\n  // Nail Services\n  nail_salon_1:\n    'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400&h=400&fit=crop&crop=center',\n  nail_salon_2:\n    'https://images.unsplash.com/photo-1607779097040-26e80aa78e66?w=400&h=400&fit=crop&crop=center',\n  nail_salon_3:\n    'https://images.unsplash.com/photo-1610992015732-2449b76344bc?w=400&h=400&fit=crop&crop=center',\n  nail_salon_4:\n    'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=400&fit=crop&crop=center',\n  nail_salon_5:\n    'https://images.unsplash.com/photo-1607779097040-26e80aa78e66?w=400&h=400&fit=crop&crop=center',\n\n  // Lash Services\n  lash_salon_1:\n    'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=400&h=400&fit=crop&crop=center',\n  lash_salon_2:\n    'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop&crop=center',\n  lash_salon_3:\n    'https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?w=400&h=400&fit=crop&crop=center',\n  lash_salon_4:\n    'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=400&h=400&fit=crop&crop=center',\n  lash_salon_5:\n    'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop&crop=center',\n\n  // Braiding Services\n  braiding_salon_1:\n    'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=400&fit=crop&crop=center',\n  braiding_salon_2:\n    'https://images.unsplash.com/photo-**********-138dadb4c035?w=400&h=400&fit=crop&crop=center',\n  braiding_salon_3:\n    'https://images.unsplash.com/photo-1521590832167-7bcbfaa6381f?w=400&h=400&fit=crop&crop=center',\n  braiding_salon_4:\n    'https://images.unsplash.com/photo-**********-8baeececf3df?w=400&h=400&fit=crop&crop=center',\n  braiding_salon_5:\n    'https://images.unsplash.com/photo-**********-f09722fb4948?w=400&h=400&fit=crop&crop=center',\n\n  // Massage Services\n  massage_salon_1:\n    'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400&h=400&fit=crop&crop=center',\n  massage_salon_2:\n    'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop&crop=center',\n  massage_salon_3:\n    'https://images.unsplash.com/photo-1540555700478-4be289fbecef?w=400&h=400&fit=crop&crop=center',\n  massage_salon_4:\n    'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400&h=400&fit=crop&crop=center',\n  massage_salon_5:\n    'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop&crop=center',\n\n  // Skincare Services\n  skincare_salon_1:\n    'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400&h=400&fit=crop&crop=center',\n  skincare_salon_2:\n    'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=400&h=400&fit=crop&crop=center',\n  skincare_salon_3:\n    'https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?w=400&h=400&fit=crop&crop=center',\n  skincare_salon_4:\n    'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400&h=400&fit=crop&crop=center',\n  skincare_salon_5:\n    'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=400&h=400&fit=crop&crop=center',\n};\n\n// Category to image mapping\nconst CATEGORY_IMAGES = {\n  Barber: [\n    'barber_shop_1',\n    'barber_shop_2',\n    'barber_shop_3',\n    'barber_shop_4',\n    'barber_shop_5',\n  ],\n  Salon: [\n    'hair_salon_1',\n    'hair_salon_2',\n    'hair_salon_3',\n    'hair_salon_4',\n    'hair_salon_5',\n  ],\n  'Nail Services': [\n    'nail_salon_1',\n    'nail_salon_2',\n    'nail_salon_3',\n    'nail_salon_4',\n    'nail_salon_5',\n  ],\n  'Lash Services': [\n    'lash_salon_1',\n    'lash_salon_2',\n    'lash_salon_3',\n    'lash_salon_4',\n    'lash_salon_5',\n  ],\n  Braiding: [\n    'braiding_salon_1',\n    'braiding_salon_2',\n    'braiding_salon_3',\n    'braiding_salon_4',\n    'braiding_salon_5',\n  ],\n  Massage: [\n    'massage_salon_1',\n    'massage_salon_2',\n    'massage_salon_3',\n    'massage_salon_4',\n    'massage_salon_5',\n  ],\n  Skincare: [\n    'skincare_salon_1',\n    'skincare_salon_2',\n    'skincare_salon_3',\n    'skincare_salon_4',\n    'skincare_salon_5',\n  ],\n};\n\n/**\n * Get a store image URL based on provider ID and category\n */\nexport const getStoreImage = (\n  providerId: string,\n  category?: string,\n): string => {\n  // If no category provided, use a default image\n  if (!category) {\n    return STORE_IMAGES.hair_salon_1;\n  }\n\n  // Get images for the category\n  const categoryImages =\n    CATEGORY_IMAGES[category as keyof typeof CATEGORY_IMAGES];\n  if (!categoryImages) {\n    return STORE_IMAGES.hair_salon_1;\n  }\n\n  // Use provider ID to consistently select an image\n  const safeProviderId = providerId || 'default';\n  const hash = safeProviderId\n    .split('')\n    .reduce((acc, char) => acc + char.charCodeAt(0), 0);\n  const imageIndex = hash % categoryImages.length;\n  const imageKey = categoryImages[imageIndex];\n\n  return STORE_IMAGES[imageKey as keyof typeof STORE_IMAGES];\n};\n\n/**\n * Get a random store image for a specific category\n */\nexport const getRandomStoreImage = (category?: string): string => {\n  if (!category) {\n    const allImages = Object.values(STORE_IMAGES);\n    return allImages[Math.floor(Math.random() * allImages.length)];\n  }\n\n  const categoryImages =\n    CATEGORY_IMAGES[category as keyof typeof CATEGORY_IMAGES];\n  if (!categoryImages) {\n    return STORE_IMAGES.hair_salon_1;\n  }\n\n  const randomIndex = Math.floor(Math.random() * categoryImages.length);\n  const imageKey = categoryImages[randomIndex];\n  return STORE_IMAGES[imageKey as keyof typeof STORE_IMAGES];\n};\n\n/**\n * Get all available categories\n */\nexport const getAvailableCategories = (): string[] => {\n  return Object.keys(CATEGORY_IMAGES);\n};\n\n/**\n * Check if an image URL is valid\n */\nexport const isValidImageUrl = (url: string): boolean => {\n  try {\n    new URL(url);\n    return true;\n  } catch {\n    return false;\n  }\n};\n\n/**\n * Get fallback image for a provider\n */\nexport const getFallbackImage = (providerName?: string): string => {\n  // Use the first letter of the provider name to select a consistent image\n  const safeName = providerName || 'Provider';\n  const firstLetter = safeName.charAt(0).toUpperCase();\n  const charCode = firstLetter.charCodeAt(0);\n  const allImages = Object.values(STORE_IMAGES);\n  const imageIndex = charCode % allImages.length;\n\n  return allImages[imageIndex];\n};\n"], "mappings": ";;;;AAWO,IAAMA,YAAY,GAAAC,OAAA,CAAAD,YAAA,GAAG;EAE1BE,YAAY,EACV,4FAA4F;EAC9FC,YAAY,EACV,+FAA+F;EACjGC,YAAY,EACV,4FAA4F;EAC9FC,YAAY,EACV,4FAA4F;EAC9FC,YAAY,EACV,+FAA+F;EAGjGC,YAAY,EACV,+FAA+F;EACjGC,YAAY,EACV,+FAA+F;EACjGC,YAAY,EACV,+FAA+F;EACjGC,YAAY,EACV,+FAA+F;EACjGC,YAAY,EACV,+FAA+F;EAGjGC,YAAY,EACV,+FAA+F;EACjGC,YAAY,EACV,+FAA+F;EACjGC,YAAY,EACV,+FAA+F;EACjGC,YAAY,EACV,+FAA+F;EACjGC,YAAY,EACV,+FAA+F;EAGjGC,gBAAgB,EACd,+FAA+F;EACjGC,gBAAgB,EACd,4FAA4F;EAC9FC,gBAAgB,EACd,+FAA+F;EACjGC,gBAAgB,EACd,4FAA4F;EAC9FC,gBAAgB,EACd,4FAA4F;EAG9FC,eAAe,EACb,4FAA4F;EAC9FC,eAAe,EACb,+FAA+F;EACjGC,eAAe,EACb,+FAA+F;EACjGC,eAAe,EACb,4FAA4F;EAC9FC,eAAe,EACb,+FAA+F;EAGjGC,gBAAgB,EACd,+FAA+F;EACjGC,gBAAgB,EACd,+FAA+F;EACjGC,gBAAgB,EACd,+FAA+F;EACjGC,gBAAgB,EACd,+FAA+F;EACjGC,gBAAgB,EACd;AACJ,CAAC;AAGD,IAAMC,eAAe,GAAG;EACtBC,MAAM,EAAE,CACN,eAAe,EACf,eAAe,EACf,eAAe,EACf,eAAe,EACf,eAAe,CAChB;EACDC,KAAK,EAAE,CACL,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,CACf;EACD,eAAe,EAAE,CACf,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,CACf;EACD,eAAe,EAAE,CACf,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,CACf;EACDC,QAAQ,EAAE,CACR,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,CACnB;EACDC,OAAO,EAAE,CACP,iBAAiB,EACjB,iBAAiB,EACjB,iBAAiB,EACjB,iBAAiB,EACjB,iBAAiB,CAClB;EACDC,QAAQ,EAAE,CACR,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB;AAEtB,CAAC;AAKM,IAAMC,aAAa,GAAArC,OAAA,CAAAqC,aAAA,GAAG,SAAhBA,aAAaA,CACxBC,UAAkB,EAClBC,QAAiB,EACN;EAEX,IAAI,CAACA,QAAQ,EAAE;IACb,OAAOxC,YAAY,CAACE,YAAY;EAClC;EAGA,IAAMuC,cAAc,GAClBT,eAAe,CAACQ,QAAQ,CAAiC;EAC3D,IAAI,CAACC,cAAc,EAAE;IACnB,OAAOzC,YAAY,CAACE,YAAY;EAClC;EAGA,IAAMwC,cAAc,GAAGH,UAAU,IAAI,SAAS;EAC9C,IAAMI,IAAI,GAAGD,cAAc,CACxBE,KAAK,CAAC,EAAE,CAAC,CACTC,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI;IAAA,OAAKD,GAAG,GAAGC,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC;EAAA,GAAE,CAAC,CAAC;EACrD,IAAMC,UAAU,GAAGN,IAAI,GAAGF,cAAc,CAACS,MAAM;EAC/C,IAAMC,QAAQ,GAAGV,cAAc,CAACQ,UAAU,CAAC;EAE3C,OAAOjD,YAAY,CAACmD,QAAQ,CAA8B;AAC5D,CAAC;AAKM,IAAMC,mBAAmB,GAAAnD,OAAA,CAAAmD,mBAAA,GAAG,SAAtBA,mBAAmBA,CAAIZ,QAAiB,EAAa;EAChE,IAAI,CAACA,QAAQ,EAAE;IACb,IAAMa,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACvD,YAAY,CAAC;IAC7C,OAAOqD,SAAS,CAACG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGL,SAAS,CAACH,MAAM,CAAC,CAAC;EAChE;EAEA,IAAMT,cAAc,GAClBT,eAAe,CAACQ,QAAQ,CAAiC;EAC3D,IAAI,CAACC,cAAc,EAAE;IACnB,OAAOzC,YAAY,CAACE,YAAY;EAClC;EAEA,IAAMyD,WAAW,GAAGH,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGjB,cAAc,CAACS,MAAM,CAAC;EACrE,IAAMC,QAAQ,GAAGV,cAAc,CAACkB,WAAW,CAAC;EAC5C,OAAO3D,YAAY,CAACmD,QAAQ,CAA8B;AAC5D,CAAC;AAKM,IAAMS,sBAAsB,GAAA3D,OAAA,CAAA2D,sBAAA,GAAG,SAAzBA,sBAAsBA,CAAA,EAAmB;EACpD,OAAON,MAAM,CAACO,IAAI,CAAC7B,eAAe,CAAC;AACrC,CAAC;AAKM,IAAM8B,eAAe,GAAA7D,OAAA,CAAA6D,eAAA,GAAG,SAAlBA,eAAeA,CAAIC,GAAW,EAAc;EACvD,IAAI;IACF,IAAIC,GAAG,CAACD,GAAG,CAAC;IACZ,OAAO,IAAI;EACb,CAAC,CAAC,OAAAE,OAAA,EAAM;IACN,OAAO,KAAK;EACd;AACF,CAAC;AAKM,IAAMC,gBAAgB,GAAAjE,OAAA,CAAAiE,gBAAA,GAAG,SAAnBA,gBAAgBA,CAAIC,YAAqB,EAAa;EAEjE,IAAMC,QAAQ,GAAGD,YAAY,IAAI,UAAU;EAC3C,IAAME,WAAW,GAAGD,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EACpD,IAAMC,QAAQ,GAAGH,WAAW,CAACrB,UAAU,CAAC,CAAC,CAAC;EAC1C,IAAMK,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACvD,YAAY,CAAC;EAC7C,IAAMiD,UAAU,GAAGuB,QAAQ,GAAGnB,SAAS,CAACH,MAAM;EAE9C,OAAOG,SAAS,CAACJ,UAAU,CAAC;AAC9B,CAAC", "ignoreList": []}