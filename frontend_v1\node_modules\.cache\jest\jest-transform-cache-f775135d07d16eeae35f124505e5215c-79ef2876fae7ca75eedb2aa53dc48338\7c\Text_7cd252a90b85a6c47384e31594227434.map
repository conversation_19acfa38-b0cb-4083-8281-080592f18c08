{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_ThemeContext", "_jsxRuntime", "_excluded", "Text", "exports", "_ref", "children", "_ref$variant", "variant", "_ref$size", "size", "_ref$color", "color", "_ref$weight", "weight", "_ref$align", "align", "_ref$italic", "italic", "_ref$underline", "underline", "_ref$strikethrough", "strikethrough", "style", "props", "_objectWithoutProperties2", "default", "_useTheme", "useTheme", "colors", "styles", "createStyles", "textStyleArray", "base", "filter", "Boolean", "jsx", "Object", "assign", "_colors$text", "_colors$text2", "_colors$text3", "_colors$text4", "_colors$primary", "_colors$text5", "StyleSheet", "create", "fontFamily", "fontSize", "lineHeight", "text", "primary", "body", "fontWeight", "heading", "caption", "label", "display", "size_xs", "size_sm", "size_base", "size_lg", "size_xl", "size_2xl", "size_3xl", "size_4xl", "size_5xl", "color_primary", "color_secondary", "secondary", "color_tertiary", "tertiary", "color_inverse", "contrast", "color_disabled", "color_success", "color_warning", "color_error", "color_info", "weight_light", "weight_normal", "weight_medium", "weight_semibold", "weight_bold", "align_left", "textAlign", "align_center", "align_right", "align_justify", "fontStyle", "textDecorationLine"], "sources": ["Text.tsx"], "sourcesContent": ["/**\n * Text Component - Enhanced Typography Atom\n *\n * Component Contract:\n * - Sources all styles from unified design tokens\n * - Supports semantic typography variants (body, heading, caption, label)\n * - Provides comprehensive size, color, and weight customization\n * - Handles accessibility for text content with proper contrast ratios\n * - Supports theme switching (light/dark mode)\n * - Follows React Native best practices for performance\n * - Serves as base for all text rendering in the app\n *\n * @version 2.0.0\n * <AUTHOR> Development Team\n */\n\nimport React from 'react';\nimport {\n  Text as RNText,\n  TextProps as RNTextProps,\n  StyleSheet,\n} from 'react-native';\n\nimport { useTheme } from '../../contexts/ThemeContext';\n\n// Type definitions\nexport type TextVariant = 'body' | 'heading' | 'caption' | 'label' | 'display';\nexport type TextSize =\n  | 'xs'\n  | 'sm'\n  | 'base'\n  | 'lg'\n  | 'xl'\n  | '2xl'\n  | '3xl'\n  | '4xl'\n  | '5xl';\nexport type TextColor =\n  | 'primary'\n  | 'secondary'\n  | 'tertiary'\n  | 'inverse'\n  | 'disabled'\n  | 'success'\n  | 'warning'\n  | 'error'\n  | 'info';\nexport type TextWeight = 'light' | 'normal' | 'medium' | 'semibold' | 'bold';\nexport type TextAlign = 'left' | 'center' | 'right' | 'justify';\nexport interface TextProps extends Omit<RNTextProps, 'style'> {\n  /** Text content */\n  children: React.ReactNode;\n  /** Typography variant */\n  variant?: TextVariant;\n  /** Font size from design tokens */\n  size?: TextSize;\n  /** Text color from design tokens */\n  color?: TextColor;\n  /** Font weight from design tokens */\n  weight?: TextWeight;\n  /** Text alignment */\n  align?: TextAlign;\n  /** Whether text should be italic */\n  italic?: boolean;\n  /** Whether text should be underlined */\n  underline?: boolean;\n  /** Whether text should be struck through */\n  strikethrough?: boolean;\n  /** Custom style override */\n  style?: RNTextProps['style'];\n}\n\nexport const Text: React.FC<TextProps> = ({\n  children,\n  variant = 'body',\n  size = 'base',\n  color = 'primary',\n  weight = 'normal',\n  align = 'left',\n  italic = false,\n  underline = false,\n  strikethrough = false,\n  style,\n  ...props\n}) => {\n  const { colors } = useTheme();\n  const styles = createStyles(colors);\n\n  // Build style array\n  const textStyleArray = [\n    styles.base,\n    styles[variant],\n    styles[`size_${size}`],\n    styles[`color_${color}`],\n    styles[`weight_${weight}`],\n    styles[`align_${align}`],\n    italic && styles.italic,\n    underline && styles.underline,\n    strikethrough && styles.strikethrough,\n    style,\n  ].filter(Boolean);\n\n  return (\n    <RNText style={textStyleArray} {...props}>\n      {children}\n    </RNText>\n  );\n};\n\n// Theme-based styles factory\nconst createStyles = (colors: any) =>\n  StyleSheet.create({\n    base: {\n      fontFamily: 'System',\n      fontSize: 16,\n      lineHeight: 24,\n      color: colors.text?.primary || '#1A1A1A',\n    },\n\n    // Variants\n    body: {\n      fontSize: 16,\n      fontWeight: '400',\n      lineHeight: 24,\n    },\n    heading: {\n      fontSize: 24,\n      fontWeight: '600',\n      lineHeight: 32,\n    },\n    caption: {\n      fontSize: 14,\n      fontWeight: '400',\n      lineHeight: 20,\n    },\n    label: {\n      fontSize: 14,\n      fontWeight: '500',\n      lineHeight: 20,\n    },\n    display: {\n      fontSize: 36,\n      fontWeight: '700',\n      lineHeight: 44,\n    },\n\n    // Sizes\n    size_xs: { fontSize: 12 },\n    size_sm: { fontSize: 14 },\n    size_base: { fontSize: 16 },\n    size_lg: { fontSize: 18 },\n    size_xl: { fontSize: 20 },\n    size_2xl: { fontSize: 24 },\n    size_3xl: { fontSize: 30 },\n    size_4xl: { fontSize: 36 },\n    size_5xl: { fontSize: 48 },\n\n    // Colors\n    color_primary: { color: colors.text?.primary || '#1A1A1A' },\n    color_secondary: { color: colors.text?.secondary || '#6B7280' },\n    color_tertiary: { color: colors.text?.tertiary || '#9CA3AF' },\n    color_inverse: { color: colors.primary?.contrast || '#FFFFFF' },\n    color_disabled: { color: colors.text?.tertiary || '#9CA3AF' },\n    color_success: { color: '#10B981' },\n    color_warning: { color: '#F59E0B' },\n    color_error: { color: '#EF4444' },\n    color_info: { color: '#3B82F6' },\n\n    // Weights\n    weight_light: { fontWeight: '300' },\n    weight_normal: { fontWeight: '400' },\n    weight_medium: { fontWeight: '500' },\n    weight_semibold: { fontWeight: '600' },\n    weight_bold: { fontWeight: '700' },\n\n    // Alignment\n    align_left: { textAlign: 'left' },\n    align_center: { textAlign: 'center' },\n    align_right: { textAlign: 'right' },\n    align_justify: { textAlign: 'justify' },\n\n    // Text decorations\n    italic: { fontStyle: 'italic' },\n    underline: { textDecorationLine: 'underline' },\n    strikethrough: { textDecorationLine: 'line-through' },\n  });\n"], "mappings": ";;;;;;AAgBA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAMA,IAAAE,aAAA,GAAAF,OAAA;AAAuD,IAAAG,WAAA,GAAAH,OAAA;AAAA,IAAAI,SAAA;AAiDhD,IAAMC,IAAyB,GAAAC,OAAA,CAAAD,IAAA,GAAG,SAA5BA,IAAyBA,CAAAE,IAAA,EAYhC;EAAA,IAXJC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IAAAC,YAAA,GAAAF,IAAA,CACRG,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,MAAM,GAAAA,YAAA;IAAAE,SAAA,GAAAJ,IAAA,CAChBK,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG,MAAM,GAAAA,SAAA;IAAAE,UAAA,GAAAN,IAAA,CACbO,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAG,SAAS,GAAAA,UAAA;IAAAE,WAAA,GAAAR,IAAA,CACjBS,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,QAAQ,GAAAA,WAAA;IAAAE,UAAA,GAAAV,IAAA,CACjBW,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAG,MAAM,GAAAA,UAAA;IAAAE,WAAA,GAAAZ,IAAA,CACda,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,KAAK,GAAAA,WAAA;IAAAE,cAAA,GAAAd,IAAA,CACde,SAAS;IAATA,SAAS,GAAAD,cAAA,cAAG,KAAK,GAAAA,cAAA;IAAAE,kBAAA,GAAAhB,IAAA,CACjBiB,aAAa;IAAbA,aAAa,GAAAD,kBAAA,cAAG,KAAK,GAAAA,kBAAA;IACrBE,KAAK,GAAAlB,IAAA,CAALkB,KAAK;IACFC,KAAK,OAAAC,yBAAA,CAAAC,OAAA,EAAArB,IAAA,EAAAH,SAAA;EAER,IAAAyB,SAAA,GAAmB,IAAAC,sBAAQ,EAAC,CAAC;IAArBC,MAAM,GAAAF,SAAA,CAANE,MAAM;EACd,IAAMC,MAAM,GAAGC,YAAY,CAACF,MAAM,CAAC;EAGnC,IAAMG,cAAc,GAAG,CACrBF,MAAM,CAACG,IAAI,EACXH,MAAM,CAACtB,OAAO,CAAC,EACfsB,MAAM,CAAC,QAAQpB,IAAI,EAAE,CAAC,EACtBoB,MAAM,CAAC,SAASlB,KAAK,EAAE,CAAC,EACxBkB,MAAM,CAAC,UAAUhB,MAAM,EAAE,CAAC,EAC1BgB,MAAM,CAAC,SAASd,KAAK,EAAE,CAAC,EACxBE,MAAM,IAAIY,MAAM,CAACZ,MAAM,EACvBE,SAAS,IAAIU,MAAM,CAACV,SAAS,EAC7BE,aAAa,IAAIQ,MAAM,CAACR,aAAa,EACrCC,KAAK,CACN,CAACW,MAAM,CAACC,OAAO,CAAC;EAEjB,OACE,IAAAlC,WAAA,CAAAmC,GAAA,EAACrC,YAAA,CAAAI,IAAM,EAAAkC,MAAA,CAAAC,MAAA;IAACf,KAAK,EAAES;EAAe,GAAKR,KAAK;IAAAlB,QAAA,EACrCA;EAAQ,EACH,CAAC;AAEb,CAAC;AAGD,IAAMyB,YAAY,GAAG,SAAfA,YAAYA,CAAIF,MAAW;EAAA,IAAAU,YAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,eAAA,EAAAC,aAAA;EAAA,OAC/BC,uBAAU,CAACC,MAAM,CAAC;IAChBb,IAAI,EAAE;MACJc,UAAU,EAAE,QAAQ;MACpBC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,EAAE;MACdrC,KAAK,EAAE,EAAA2B,YAAA,GAAAV,MAAM,CAACqB,IAAI,qBAAXX,YAAA,CAAaY,OAAO,KAAI;IACjC,CAAC;IAGDC,IAAI,EAAE;MACJJ,QAAQ,EAAE,EAAE;MACZK,UAAU,EAAE,KAAK;MACjBJ,UAAU,EAAE;IACd,CAAC;IACDK,OAAO,EAAE;MACPN,QAAQ,EAAE,EAAE;MACZK,UAAU,EAAE,KAAK;MACjBJ,UAAU,EAAE;IACd,CAAC;IACDM,OAAO,EAAE;MACPP,QAAQ,EAAE,EAAE;MACZK,UAAU,EAAE,KAAK;MACjBJ,UAAU,EAAE;IACd,CAAC;IACDO,KAAK,EAAE;MACLR,QAAQ,EAAE,EAAE;MACZK,UAAU,EAAE,KAAK;MACjBJ,UAAU,EAAE;IACd,CAAC;IACDQ,OAAO,EAAE;MACPT,QAAQ,EAAE,EAAE;MACZK,UAAU,EAAE,KAAK;MACjBJ,UAAU,EAAE;IACd,CAAC;IAGDS,OAAO,EAAE;MAAEV,QAAQ,EAAE;IAAG,CAAC;IACzBW,OAAO,EAAE;MAAEX,QAAQ,EAAE;IAAG,CAAC;IACzBY,SAAS,EAAE;MAAEZ,QAAQ,EAAE;IAAG,CAAC;IAC3Ba,OAAO,EAAE;MAAEb,QAAQ,EAAE;IAAG,CAAC;IACzBc,OAAO,EAAE;MAAEd,QAAQ,EAAE;IAAG,CAAC;IACzBe,QAAQ,EAAE;MAAEf,QAAQ,EAAE;IAAG,CAAC;IAC1BgB,QAAQ,EAAE;MAAEhB,QAAQ,EAAE;IAAG,CAAC;IAC1BiB,QAAQ,EAAE;MAAEjB,QAAQ,EAAE;IAAG,CAAC;IAC1BkB,QAAQ,EAAE;MAAElB,QAAQ,EAAE;IAAG,CAAC;IAG1BmB,aAAa,EAAE;MAAEvD,KAAK,EAAE,EAAA4B,aAAA,GAAAX,MAAM,CAACqB,IAAI,qBAAXV,aAAA,CAAaW,OAAO,KAAI;IAAU,CAAC;IAC3DiB,eAAe,EAAE;MAAExD,KAAK,EAAE,EAAA6B,aAAA,GAAAZ,MAAM,CAACqB,IAAI,qBAAXT,aAAA,CAAa4B,SAAS,KAAI;IAAU,CAAC;IAC/DC,cAAc,EAAE;MAAE1D,KAAK,EAAE,EAAA8B,aAAA,GAAAb,MAAM,CAACqB,IAAI,qBAAXR,aAAA,CAAa6B,QAAQ,KAAI;IAAU,CAAC;IAC7DC,aAAa,EAAE;MAAE5D,KAAK,EAAE,EAAA+B,eAAA,GAAAd,MAAM,CAACsB,OAAO,qBAAdR,eAAA,CAAgB8B,QAAQ,KAAI;IAAU,CAAC;IAC/DC,cAAc,EAAE;MAAE9D,KAAK,EAAE,EAAAgC,aAAA,GAAAf,MAAM,CAACqB,IAAI,qBAAXN,aAAA,CAAa2B,QAAQ,KAAI;IAAU,CAAC;IAC7DI,aAAa,EAAE;MAAE/D,KAAK,EAAE;IAAU,CAAC;IACnCgE,aAAa,EAAE;MAAEhE,KAAK,EAAE;IAAU,CAAC;IACnCiE,WAAW,EAAE;MAAEjE,KAAK,EAAE;IAAU,CAAC;IACjCkE,UAAU,EAAE;MAAElE,KAAK,EAAE;IAAU,CAAC;IAGhCmE,YAAY,EAAE;MAAE1B,UAAU,EAAE;IAAM,CAAC;IACnC2B,aAAa,EAAE;MAAE3B,UAAU,EAAE;IAAM,CAAC;IACpC4B,aAAa,EAAE;MAAE5B,UAAU,EAAE;IAAM,CAAC;IACpC6B,eAAe,EAAE;MAAE7B,UAAU,EAAE;IAAM,CAAC;IACtC8B,WAAW,EAAE;MAAE9B,UAAU,EAAE;IAAM,CAAC;IAGlC+B,UAAU,EAAE;MAAEC,SAAS,EAAE;IAAO,CAAC;IACjCC,YAAY,EAAE;MAAED,SAAS,EAAE;IAAS,CAAC;IACrCE,WAAW,EAAE;MAAEF,SAAS,EAAE;IAAQ,CAAC;IACnCG,aAAa,EAAE;MAAEH,SAAS,EAAE;IAAU,CAAC;IAGvCnE,MAAM,EAAE;MAAEuE,SAAS,EAAE;IAAS,CAAC;IAC/BrE,SAAS,EAAE;MAAEsE,kBAAkB,EAAE;IAAY,CAAC;IAC9CpE,aAAa,EAAE;MAAEoE,kBAAkB,EAAE;IAAe;EACtD,CAAC,CAAC;AAAA", "ignoreList": []}