a8e788e36731ef82a08c1f962622f0cc
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.MinimalistTypography = exports.MinimalistSpacing = exports.MinimalistShadows = exports.MinimalistLayout = exports.MinimalistEasing = exports.MinimalistDarkColors = exports.MinimalistComponents = exports.MinimalistColors = exports.MinimalistBorderRadius = exports.HyperMinimalistTheme = void 0;
var _Colors = require("../constants/Colors");
var MinimalistSpacing = exports.MinimalistSpacing = {
  xs: 2,
  sm: 4,
  md: 8,
  medium: 8,
  lg: 16,
  xl: 24,
  '2xl': 40,
  '3xl': 64,
  '4xl': 96,
  '5xl': 128,
  '6xl': 160,
  component: {
    padding: 12,
    margin: 20,
    gap: 8,
    section: 40,
    container: 16,
    card: 16,
    button: 12,
    medium: 8
  }
};
var MinimalistTypography = exports.MinimalistTypography = {
  fontFamily: {
    primary: 'System',
    mono: 'SF Mono, Monaco, Consolas, monospace'
  },
  fontSize: {
    xs: 11,
    sm: 13,
    base: 15,
    medium: 16,
    lg: 18,
    xl: 22,
    '2xl': 26,
    '3xl': 32,
    '4xl': 40,
    '5xl': 48
  },
  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700'
  },
  lineHeight: {
    tight: 1.1,
    snug: 1.25,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8
  },
  letterSpacing: {
    tighter: -0.05,
    tight: -0.025,
    normal: 0,
    wide: 0.025,
    wider: 0.05,
    widest: 0.1
  }
};
var MinimalistColors = exports.MinimalistColors = {
  white: '#FFFFFF',
  black: '#000000',
  gray: {
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#E5E5E5',
    300: '#D4D4D4',
    400: '#A3A3A3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717'
  },
  primary: {
    sage: _Colors.Colors.sage400,
    default: _Colors.Colors.sage400,
    light: _Colors.Colors.sage300,
    dark: _Colors.Colors.sage600,
    contrast: '#FFFFFF'
  },
  primaryLight: _Colors.Colors.sage300,
  primaryDark: _Colors.Colors.sage600,
  primaryContrast: '#FFFFFF',
  accent: {
    50: _Colors.Colors.sage50,
    100: _Colors.Colors.sage100,
    200: _Colors.Colors.sage200,
    300: _Colors.Colors.sage300,
    400: _Colors.Colors.sage400,
    500: _Colors.Colors.sage500,
    600: _Colors.Colors.sage600,
    700: _Colors.Colors.sage700,
    800: _Colors.Colors.sage800,
    900: _Colors.Colors.sage900
  },
  text: {
    primary: '#171717',
    secondary: '#525252',
    medium: '#525252',
    tertiary: '#A3A3A3',
    inverse: '#FFFFFF',
    disabled: '#D4D4D4',
    onPrimary: '#FFFFFF',
    link: _Colors.Colors.sage400
  },
  background: {
    primary: '#FFFFFF',
    secondary: '#FAFAFA',
    tertiary: '#F5F5F5',
    elevated: '#FFFFFF',
    overlay: 'rgba(0, 0, 0, 0.5)',
    sage: _Colors.Colors.sage50,
    disabled: '#F5F5F5'
  },
  border: {
    light: '#E5E5E5',
    medium: '#D4D4D4',
    dark: '#A3A3A3',
    primary: _Colors.Colors.sage400,
    disabled: '#E5E5E5'
  },
  surface: {
    primary: '#FFFFFF',
    secondary: '#FAFAFA',
    tertiary: '#F5F5F5',
    inverse: '#171717',
    disabled: '#F5F5F5',
    sage: _Colors.Colors.sage100
  },
  semantic: {
    success: '#22C55E',
    warning: '#F59E0B',
    error: '#EF4444',
    info: _Colors.Colors.sage400
  },
  success: '#22C55E',
  warning: '#F59E0B',
  error: '#EF4444',
  info: _Colors.Colors.sage400
};
var MinimalistDarkColors = exports.MinimalistDarkColors = {
  white: '#000000',
  black: '#FFFFFF',
  gray: {
    50: '#171717',
    100: '#262626',
    200: '#404040',
    300: '#525252',
    400: '#737373',
    500: '#A3A3A3',
    600: '#D4D4D4',
    700: '#E5E5E5',
    800: '#F5F5F5',
    900: '#FAFAFA'
  },
  primary: {
    sage: _Colors.Colors.sage600,
    default: _Colors.Colors.sage600,
    light: _Colors.Colors.sage500,
    dark: _Colors.Colors.sage700,
    contrast: '#000000'
  },
  primaryLight: _Colors.Colors.sage500,
  primaryDark: _Colors.Colors.sage700,
  primaryContrast: '#000000',
  accent: MinimalistColors.accent,
  text: {
    primary: '#FAFAFA',
    secondary: '#D4D4D4',
    medium: '#D4D4D4',
    tertiary: '#A3A3A3',
    inverse: '#171717',
    disabled: '#737373',
    onPrimary: '#000000',
    link: _Colors.Colors.sage500
  },
  background: {
    primary: '#171717',
    secondary: '#262626',
    tertiary: '#404040',
    elevated: '#262626',
    overlay: 'rgba(0, 0, 0, 0.8)',
    sage: '#1A3A22',
    disabled: '#404040'
  },
  border: {
    light: '#404040',
    medium: '#525252',
    dark: '#737373',
    primary: _Colors.Colors.sage500,
    disabled: '#404040'
  },
  surface: {
    primary: '#262626',
    secondary: '#404040',
    tertiary: '#525252',
    inverse: '#FAFAFA',
    disabled: '#404040',
    sage: '#2A4B32'
  },
  semantic: {
    success: '#16A34A',
    warning: '#D97706',
    error: '#DC2626',
    info: _Colors.Colors.sage600
  },
  success: '#16A34A',
  warning: '#D97706',
  error: '#DC2626',
  info: _Colors.Colors.sage600
};
var MinimalistShadows = exports.MinimalistShadows = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: {
      width: 0,
      height: 0
    },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0
  },
  subtle: {
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1
  },
  soft: {
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2
  },
  medium: {
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 4
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4
  }
};
var MinimalistBorderRadius = exports.MinimalistBorderRadius = {
  none: 0,
  sm: 2,
  md: 4,
  medium: 4,
  lg: 8,
  xl: 12,
  full: 9999
};
var MinimalistEasing = exports.MinimalistEasing = {
  linear: 'linear',
  easeIn: 'ease-in',
  easeOut: 'ease-out',
  easeInOut: 'ease-in-out',
  natural: 'cubic-bezier(0.4, 0.0, 0.2, 1)',
  gentle: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
};
var MinimalistComponents = exports.MinimalistComponents = {
  button: {
    primary: Object.assign({
      backgroundColor: MinimalistColors.accent[400],
      color: MinimalistColors.white,
      borderRadius: MinimalistBorderRadius.md,
      paddingVertical: MinimalistSpacing.md,
      paddingHorizontal: MinimalistSpacing.lg,
      fontSize: MinimalistTypography.fontSize.base,
      fontWeight: MinimalistTypography.fontWeight.medium
    }, MinimalistShadows.subtle),
    secondary: {
      backgroundColor: 'transparent',
      color: MinimalistColors.accent[400],
      borderWidth: 1,
      borderColor: MinimalistColors.accent[400],
      borderRadius: MinimalistBorderRadius.md,
      paddingVertical: MinimalistSpacing.md,
      paddingHorizontal: MinimalistSpacing.lg,
      fontSize: MinimalistTypography.fontSize.base,
      fontWeight: MinimalistTypography.fontWeight.medium
    },
    ghost: {
      backgroundColor: 'transparent',
      color: MinimalistColors.gray[600],
      borderRadius: MinimalistBorderRadius.md,
      paddingVertical: MinimalistSpacing.md,
      paddingHorizontal: MinimalistSpacing.lg,
      fontSize: MinimalistTypography.fontSize.base,
      fontWeight: MinimalistTypography.fontWeight.normal
    }
  },
  input: {
    base: Object.assign({
      backgroundColor: MinimalistColors.white,
      borderWidth: 1,
      borderColor: MinimalistColors.gray[200],
      borderRadius: MinimalistBorderRadius.md,
      paddingVertical: MinimalistSpacing.md,
      paddingHorizontal: MinimalistSpacing.md,
      fontSize: MinimalistTypography.fontSize.base,
      color: MinimalistColors.gray[900]
    }, MinimalistShadows.none),
    focused: Object.assign({
      borderColor: MinimalistColors.accent[400]
    }, MinimalistShadows.subtle),
    error: {
      borderColor: MinimalistColors.semantic.error
    }
  },
  card: {
    base: Object.assign({
      backgroundColor: MinimalistColors.white,
      borderRadius: MinimalistBorderRadius.lg,
      padding: MinimalistSpacing.lg
    }, MinimalistShadows.soft),
    elevated: Object.assign({
      backgroundColor: MinimalistColors.white,
      borderRadius: MinimalistBorderRadius.lg,
      padding: MinimalistSpacing.xl
    }, MinimalistShadows.medium),
    flat: Object.assign({
      backgroundColor: MinimalistColors.gray[50],
      borderRadius: MinimalistBorderRadius.lg,
      padding: MinimalistSpacing.lg
    }, MinimalistShadows.none)
  }
};
var MinimalistLayout = exports.MinimalistLayout = {
  container: {
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280
  },
  grid: {
    columns: 12,
    gutter: MinimalistSpacing.md
  },
  breakpoints: {
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280
  }
};
var HyperMinimalistTheme = exports.HyperMinimalistTheme = {
  spacing: MinimalistSpacing,
  typography: MinimalistTypography,
  colors: MinimalistColors,
  darkColors: MinimalistDarkColors,
  shadows: MinimalistShadows,
  borderRadius: MinimalistBorderRadius,
  easing: MinimalistEasing,
  components: MinimalistComponents,
  layout: MinimalistLayout
};
var _default = exports.default = HyperMinimalistTheme;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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