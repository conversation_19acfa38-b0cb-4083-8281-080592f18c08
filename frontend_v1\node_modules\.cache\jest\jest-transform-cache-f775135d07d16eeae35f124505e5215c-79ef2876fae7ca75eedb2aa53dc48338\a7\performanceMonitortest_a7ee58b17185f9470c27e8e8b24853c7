98d0dc0b1c2a6a7206477a453a8f8616
var _performanceMonitor = require("../performanceMonitor");
describe('PerformanceMonitorService', function () {
  beforeEach(function () {
    _performanceMonitor.performanceMonitor.clearMetrics();
    _performanceMonitor.performanceMonitor.startMonitoring();
  });
  afterEach(function () {
    _performanceMonitor.performanceMonitor.stopMonitoring();
    jest.clearAllMocks();
  });
  describe('Initialization', function () {
    it('starts monitoring correctly', function () {
      expect(_performanceMonitor.performanceMonitor.isMonitoring).toBe(true);
    });
    it('stops monitoring correctly', function () {
      _performanceMonitor.performanceMonitor.stopMonitoring();
      expect(_performanceMonitor.performanceMonitor.isMonitoring).toBe(false);
    });
    it('prevents double initialization', function () {
      var consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      _performanceMonitor.performanceMonitor.startMonitoring();
      _performanceMonitor.performanceMonitor.startMonitoring();
      expect(consoleSpy).toHaveBeenCalledTimes(1);
      consoleSpy.mockRestore();
    });
  });
  describe('Render Performance Tracking', function () {
    it('tracks component render times', function () {
      var _metrics$0$metadata;
      var componentName = 'TestComponent';
      var renderTime = 50;
      _performanceMonitor.performanceMonitor.trackRender(componentName, renderTime);
      var metrics = _performanceMonitor.performanceMonitor.getMetricsByCategory('render');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].name).toBe('component_render');
      expect(metrics[0].value).toBe(renderTime);
      expect((_metrics$0$metadata = metrics[0].metadata) == null ? void 0 : _metrics$0$metadata.componentName).toBe(componentName);
    });
    it('calculates average render times for components', function () {
      var componentName = 'TestComponent';
      _performanceMonitor.performanceMonitor.trackRender(componentName, 40);
      _performanceMonitor.performanceMonitor.trackRender(componentName, 60);
      var renderMetrics = _performanceMonitor.performanceMonitor.renderMetrics;
      var componentMetrics = renderMetrics.get(componentName);
      expect(componentMetrics == null ? void 0 : componentMetrics.renderTime).toBe(50);
      expect(componentMetrics == null ? void 0 : componentMetrics.reRenders).toBe(2);
    });
    it('warns about slow renders', function () {
      var consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      _performanceMonitor.performanceMonitor.trackRender('SlowComponent', 100);
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Slow render detected: SlowComponent took 100ms'));
      consoleSpy.mockRestore();
    });
    it('tracks render metadata correctly', function () {
      var metadata = {
        propsCount: 5,
        stateUpdates: 2
      };
      _performanceMonitor.performanceMonitor.trackRender('TestComponent', 30, metadata);
      var renderMetrics = _performanceMonitor.performanceMonitor.renderMetrics;
      var componentMetrics = renderMetrics.get('TestComponent');
      expect(componentMetrics == null ? void 0 : componentMetrics.propsCount).toBe(5);
      expect(componentMetrics == null ? void 0 : componentMetrics.stateUpdates).toBe(2);
    });
  });
  describe('Network Performance Tracking', function () {
    it('tracks network request performance', function () {
      var _metrics$0$metadata2, _metrics$0$metadata3, _metrics$0$metadata4;
      var url = '/api/test';
      var method = 'GET';
      var responseTime = 200;
      var statusCode = 200;
      _performanceMonitor.performanceMonitor.trackNetworkRequest(url, method, responseTime, statusCode);
      var metrics = _performanceMonitor.performanceMonitor.getMetricsByCategory('network');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].value).toBe(responseTime);
      expect((_metrics$0$metadata2 = metrics[0].metadata) == null ? void 0 : _metrics$0$metadata2.url).toBe(url);
      expect((_metrics$0$metadata3 = metrics[0].metadata) == null ? void 0 : _metrics$0$metadata3.method).toBe(method);
      expect((_metrics$0$metadata4 = metrics[0].metadata) == null ? void 0 : _metrics$0$metadata4.statusCode).toBe(statusCode);
    });
    it('warns about slow network requests', function () {
      var consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      _performanceMonitor.performanceMonitor.trackNetworkRequest('/api/slow', 'GET', 3000, 200);
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Slow network request: GET /api/slow took 3000ms'));
      consoleSpy.mockRestore();
    });
    it('tracks cached vs non-cached requests', function () {
      _performanceMonitor.performanceMonitor.trackNetworkRequest('/api/cached', 'GET', 50, 200, 0, 0, true);
      _performanceMonitor.performanceMonitor.trackNetworkRequest('/api/fresh', 'GET', 200, 200, 0, 0, false);
      var networkMetrics = _performanceMonitor.performanceMonitor.networkMetrics;
      expect(networkMetrics[0].cached).toBe(true);
      expect(networkMetrics[1].cached).toBe(false);
    });
    it('limits stored network metrics', function () {
      var maxMetrics = _performanceMonitor.performanceMonitor.MAX_METRICS;
      for (var i = 0; i < maxMetrics + 100; i++) {
        _performanceMonitor.performanceMonitor.trackNetworkRequest(`/api/test${i}`, 'GET', 100, 200);
      }
      var networkMetrics = _performanceMonitor.performanceMonitor.networkMetrics;
      expect(networkMetrics.length).toBeLessThanOrEqual(maxMetrics);
    });
  });
  describe('User Interaction Tracking', function () {
    it('tracks user interaction performance', function () {
      var _metrics$0$metadata5, _metrics$0$metadata6;
      var interactionType = 'button_click';
      var responseTime = 50;
      var metadata = {
        buttonId: 'submit'
      };
      _performanceMonitor.performanceMonitor.trackUserInteraction(interactionType, responseTime, metadata);
      var metrics = _performanceMonitor.performanceMonitor.getMetricsByCategory('user_interaction');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].value).toBe(responseTime);
      expect((_metrics$0$metadata5 = metrics[0].metadata) == null ? void 0 : _metrics$0$metadata5.interactionType).toBe(interactionType);
      expect((_metrics$0$metadata6 = metrics[0].metadata) == null ? void 0 : _metrics$0$metadata6.buttonId).toBe('submit');
    });
    it('warns about slow interactions', function () {
      var consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      _performanceMonitor.performanceMonitor.trackUserInteraction('slow_interaction', 150);
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Slow interaction: slow_interaction took 150ms'));
      consoleSpy.mockRestore();
    });
  });
  describe('Navigation Performance Tracking', function () {
    it('tracks navigation performance', function () {
      var _metrics$0$metadata7, _metrics$0$metadata8;
      var fromScreen = 'Home';
      var toScreen = 'Profile';
      var navigationTime = 300;
      _performanceMonitor.performanceMonitor.trackNavigation(fromScreen, toScreen, navigationTime);
      var metrics = _performanceMonitor.performanceMonitor.getMetricsByCategory('navigation');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].value).toBe(navigationTime);
      expect((_metrics$0$metadata7 = metrics[0].metadata) == null ? void 0 : _metrics$0$metadata7.fromScreen).toBe(fromScreen);
      expect((_metrics$0$metadata8 = metrics[0].metadata) == null ? void 0 : _metrics$0$metadata8.toScreen).toBe(toScreen);
    });
  });
  describe('Performance Reports', function () {
    beforeEach(function () {
      _performanceMonitor.performanceMonitor.trackRender('FastComponent', 10);
      _performanceMonitor.performanceMonitor.trackRender('SlowComponent', 50);
      _performanceMonitor.performanceMonitor.trackNetworkRequest('/api/fast', 'GET', 100, 200, 0, 0, true);
      _performanceMonitor.performanceMonitor.trackNetworkRequest('/api/slow', 'GET', 3000, 200, 0, 0, false);
      _performanceMonitor.performanceMonitor.trackUserInteraction('click', 25);
    });
    it('generates comprehensive performance report', function () {
      var report = _performanceMonitor.performanceMonitor.getPerformanceReport();
      expect(report.summary).toBeDefined();
      expect(report.summary.averageRenderTime).toBeGreaterThan(0);
      expect(report.summary.averageNetworkTime).toBeGreaterThan(0);
      expect(report.summary.cacheHitRate).toBeDefined();
      expect(report.slowComponents).toBeDefined();
      expect(report.slowNetworkRequests).toBeDefined();
      expect(report.recommendations).toBeDefined();
    });
    it('calculates cache hit rate correctly', function () {
      var report = _performanceMonitor.performanceMonitor.getPerformanceReport();
      expect(report.summary.cacheHitRate).toBe(0.5);
    });
    it('identifies slow components', function () {
      var report = _performanceMonitor.performanceMonitor.getPerformanceReport();
      var slowComponent = report.slowComponents.find(function (c) {
        return c.componentName === 'SlowComponent';
      });
      expect(slowComponent).toBeDefined();
      expect(slowComponent == null ? void 0 : slowComponent.renderTime).toBe(50);
    });
    it('identifies slow network requests', function () {
      var report = _performanceMonitor.performanceMonitor.getPerformanceReport();
      var slowRequest = report.slowNetworkRequests.find(function (r) {
        return r.url === '/api/slow';
      });
      expect(slowRequest).toBeDefined();
      expect(slowRequest == null ? void 0 : slowRequest.responseTime).toBe(3000);
    });
    it('generates performance recommendations', function () {
      var report = _performanceMonitor.performanceMonitor.getPerformanceReport();
      expect(report.recommendations).toContain(expect.stringContaining('Optimize slow components'));
      expect(report.recommendations).toContain(expect.stringContaining('request caching'));
    });
  });
  describe('Memory Monitoring', function () {
    it('collects memory metrics when available', function () {
      var mockMemory = {
        usedJSHeapSize: 1000000,
        totalJSHeapSize: 2000000,
        jsHeapSizeLimit: 4000000
      };
      global.performance = {
        memory: mockMemory
      };
      _performanceMonitor.performanceMonitor.collectMemoryMetrics();
      var memoryMetrics = _performanceMonitor.performanceMonitor.memoryMetrics;
      expect(memoryMetrics).toHaveLength(1);
      expect(memoryMetrics[0].usedJSHeapSize).toBe(1000000);
    });
    it('detects potential memory leaks', function () {
      var memoryMetrics = _performanceMonitor.performanceMonitor.memoryMetrics;
      for (var i = 0; i < 10; i++) {
        memoryMetrics.push({
          usedJSHeapSize: 1000000 + i * 10000000,
          totalJSHeapSize: 2000000,
          jsHeapSizeLimit: 4000000,
          timestamp: Date.now() + i
        });
      }
      var leaks = _performanceMonitor.performanceMonitor.detectMemoryLeaks();
      expect(leaks.length).toBeGreaterThan(0);
      expect(leaks[0]).toContain('Memory usage increased');
    });
    it('detects excessive re-renders', function () {
      for (var i = 0; i < 150; i++) {
        _performanceMonitor.performanceMonitor.trackRender('ExcessiveComponent', 10);
      }
      var leaks = _performanceMonitor.performanceMonitor.detectMemoryLeaks();
      var reRenderLeak = leaks.find(function (leak) {
        return leak.includes('ExcessiveComponent');
      });
      expect(reRenderLeak).toBeDefined();
      expect(reRenderLeak).toContain('150 re-renders');
    });
  });
  describe('Cleanup and Maintenance', function () {
    it('clears all metrics', function () {
      _performanceMonitor.performanceMonitor.trackRender('TestComponent', 50);
      _performanceMonitor.performanceMonitor.trackNetworkRequest('/api/test', 'GET', 100, 200);
      _performanceMonitor.performanceMonitor.clearMetrics();
      expect(_performanceMonitor.performanceMonitor.getMetricsByCategory('render')).toHaveLength(0);
      expect(_performanceMonitor.performanceMonitor.getMetricsByCategory('network')).toHaveLength(0);
    });
    it('cleans up old metrics automatically', function () {
      var oldTimestamp = Date.now() - 700000;
      _performanceMonitor.performanceMonitor.metrics.push({
        name: 'old_metric',
        value: 100,
        timestamp: oldTimestamp,
        category: 'render'
      });
      _performanceMonitor.performanceMonitor.cleanupOldMetrics();
      var oldMetric = _performanceMonitor.performanceMonitor.metrics.find(function (m) {
        return m.timestamp === oldTimestamp;
      });
      expect(oldMetric).toBeUndefined();
    });
    it('destroys service correctly', function () {
      var clearIntervalSpy = jest.spyOn(global, 'clearInterval');
      _performanceMonitor.performanceMonitor.destroy();
      expect(clearIntervalSpy).toHaveBeenCalled();
      expect(_performanceMonitor.performanceMonitor.memoryCache.size).toBe(0);
      clearIntervalSpy.mockRestore();
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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