{"version": 3, "names": ["_unifiedErrorHandling", "require", "WebSocketService", "config", "_classCallCheck2", "default", "socket", "reconnectAttempts", "reconnectTimer", "isConnecting", "eventListeners", "Map", "connectionPromise", "Object", "assign", "reconnectInterval", "maxReconnectAttempts", "_createClass2", "key", "value", "_connect", "_asyncToGenerator2", "token", "_this$socket", "_this", "readyState", "WebSocket", "OPEN", "Promise", "resolve", "reject", "wsUrl", "url", "protocols", "onopen", "console", "log", "emit", "onmessage", "event", "data", "JSON", "parse", "handleMessage", "error", "unifiedErrorHandlingService", "handleWebSocketError", "action", "additionalData", "rawMessage", "onclose", "_ref", "code", "reason", "handleAuthenticationError", "Error", "<PERSON><PERSON><PERSON>", "shouldReconnect", "scheduleReconnect", "_x2", "apply", "arguments", "onerror", "_ref2", "_this$socket2", "_x3", "connect", "_x", "disconnect", "clearTimeout", "close", "_send", "message", "_this$socket3", "send", "stringify", "_this$socket4", "warn", "_x4", "_sendChatMessage", "content", "replyTo", "type", "reply_to", "sendChatMessage", "_x5", "_x6", "_sendTypingIndicator", "isTyping", "is_typing", "sendTypingIndicator", "_x7", "_markMessagesRead", "messageIds", "message_ids", "markMessagesRead", "_x8", "on", "callback", "has", "set", "Set", "get", "add", "off", "listeners", "delete", "isConnected", "_this$socket5", "getConnectionState", "CONNECTING", "CLOSING", "CLOSED", "for<PERSON>ach", "_this2", "setTimeout", "catch", "createWebSocketService", "exports", "createMessagingWebSocketService", "conversationId", "createNotificationsWebSocketService", "userId", "_default"], "sources": ["websocketService.ts"], "sourcesContent": ["/**\n * WebSocket Service - Real-time Communication\n *\n * Service Contract:\n * - Manages WebSocket connections for real-time messaging\n * - Handles connection lifecycle and reconnection\n * - Provides event-based messaging interface\n * - Supports typing indicators and message status updates\n * - Implements proper error handling and fallback mechanisms\n * - Integrates with unified error handling system\n *\n * @version 2.0.0\n * <AUTHOR> Development Team\n */\n\nimport { unifiedErrorHandlingService } from './unifiedErrorHandling';\n\nexport interface WebSocketMessage {\n  type:\n    | 'chat_message'\n    | 'typing_indicator'\n    | 'messages_read'\n    | 'notification'\n    | 'error';\n  message?: any;\n  user_id?: string;\n  user_name?: string;\n  is_typing?: boolean;\n  message_ids?: string[];\n  reader_id?: string;\n  notification?: any;\n  error?: string;\n}\n\nexport interface WebSocketConfig {\n  url: string;\n  protocols?: string[];\n  reconnectInterval?: number;\n  maxReconnectAttempts?: number;\n}\n\nclass WebSocketService {\n  private socket: WebSocket | null = null;\n  private config: WebSocketConfig;\n  private reconnectAttempts = 0;\n  private reconnectTimer: NodeJS.Timeout | null = null;\n  private isConnecting = false;\n  private eventListeners: Map<string, Set<(data: any) => void>> = new Map();\n  private connectionPromise: Promise<void> | null = null;\n\n  constructor(config: WebSocketConfig) {\n    this.config = {\n      reconnectInterval: 3000,\n      maxReconnectAttempts: 5,\n      ...config,\n    };\n  }\n\n  /**\n   * Connect to WebSocket server\n   */\n  async connect(token?: string): Promise<void> {\n    if (this.socket?.readyState === WebSocket.OPEN) {\n      return Promise.resolve();\n    }\n\n    if (this.connectionPromise) {\n      return this.connectionPromise;\n    }\n\n    this.connectionPromise = new Promise((resolve, reject) => {\n      try {\n        this.isConnecting = true;\n\n        // Use provided token for WebSocket authentication\n        const wsUrl = token\n          ? `${this.config.url}?token=${token}`\n          : this.config.url;\n\n        this.socket = new WebSocket(wsUrl, this.config.protocols);\n\n        this.socket.onopen = () => {\n          console.log('WebSocket connected');\n          this.isConnecting = false;\n          this.reconnectAttempts = 0;\n          this.connectionPromise = null;\n          this.emit('connected', {});\n          resolve();\n        };\n\n        this.socket.onmessage = event => {\n          try {\n            const data: WebSocketMessage = JSON.parse(event.data);\n            this.handleMessage(data);\n          } catch (error) {\n            console.error('Failed to parse WebSocket message:', error);\n            // Handle message parsing errors with unified error handling\n            unifiedErrorHandlingService.handleWebSocketError(error as Error, {\n              action: 'message_parse_error',\n              additionalData: { rawMessage: event.data, url: this.config.url }\n            });\n          }\n        };\n\n        this.socket.onclose = async event => {\n          console.log('WebSocket disconnected:', event.code, event.reason);\n          this.isConnecting = false;\n          this.connectionPromise = null;\n          this.emit('disconnected', { code: event.code, reason: event.reason });\n\n          // Handle different close codes\n          if (event.code === 4001) {\n            // Authentication failed\n            await unifiedErrorHandlingService.handleAuthenticationError(\n              new Error('WebSocket authentication failed'),\n              {\n                action: 'websocket_auth_failed',\n                additionalData: { code: event.code, reason: event.reason, url: this.config.url }\n              }\n            );\n          } else if (!event.wasClean && this.shouldReconnect()) {\n            this.scheduleReconnect();\n          }\n        };\n\n        this.socket.onerror = async error => {\n          console.error('WebSocket error:', error);\n          this.isConnecting = false;\n          this.connectionPromise = null;\n\n          // Use unified error handling\n          await unifiedErrorHandlingService.handleWebSocketError(error as Error, {\n            action: 'connection_error',\n            additionalData: {\n              url: this.config.url,\n              readyState: this.socket?.readyState\n            }\n          });\n\n          this.emit('error', { error });\n          reject(error);\n        };\n      } catch (error) {\n        this.isConnecting = false;\n        this.connectionPromise = null;\n        reject(error);\n      }\n    });\n\n    return this.connectionPromise;\n  }\n\n  /**\n   * Disconnect from WebSocket server\n   */\n  disconnect(): void {\n    if (this.reconnectTimer) {\n      clearTimeout(this.reconnectTimer);\n      this.reconnectTimer = null;\n    }\n\n    if (this.socket) {\n      this.socket.close(1000, 'Client disconnect');\n      this.socket = null;\n    }\n\n    this.connectionPromise = null;\n    this.reconnectAttempts = 0;\n  }\n\n  /**\n   * Send message through WebSocket\n   */\n  async send(message: any): Promise<boolean> {\n    if (this.socket?.readyState === WebSocket.OPEN) {\n      try {\n        this.socket.send(JSON.stringify(message));\n        return true;\n      } catch (error) {\n        console.error('Failed to send WebSocket message:', error);\n        await unifiedErrorHandlingService.handleWebSocketError(error as Error, {\n          action: 'send_message_error',\n          additionalData: { message, url: this.config.url }\n        });\n        return false;\n      }\n    } else {\n      console.warn('WebSocket not connected, message not sent:', message);\n      await unifiedErrorHandlingService.handleWebSocketError(\n        new Error('WebSocket not connected'),\n        {\n          action: 'send_message_not_connected',\n          additionalData: {\n            message,\n            url: this.config.url,\n            readyState: this.socket?.readyState\n          }\n        }\n      );\n      return false;\n    }\n  }\n\n  /**\n   * Send chat message\n   */\n  async sendChatMessage(content: string, replyTo?: string): Promise<boolean> {\n    return await this.send({\n      type: 'chat_message',\n      content,\n      reply_to: replyTo,\n    });\n  }\n\n  /**\n   * Send typing indicator\n   */\n  async sendTypingIndicator(isTyping: boolean): Promise<boolean> {\n    return await this.send({\n      type: 'typing_indicator',\n      is_typing: isTyping,\n    });\n  }\n\n  /**\n   * Mark messages as read\n   */\n  async markMessagesRead(messageIds: string[]): Promise<boolean> {\n    return await this.send({\n      type: 'mark_read',\n      message_ids: messageIds,\n    });\n  }\n\n  /**\n   * Add event listener\n   */\n  on(event: string, callback: (data: any) => void): void {\n    if (!this.eventListeners.has(event)) {\n      this.eventListeners.set(event, new Set());\n    }\n    this.eventListeners.get(event)!.add(callback);\n  }\n\n  /**\n   * Remove event listener\n   */\n  off(event: string, callback: (data: any) => void): void {\n    const listeners = this.eventListeners.get(event);\n    if (listeners) {\n      listeners.delete(callback);\n    }\n  }\n\n  /**\n   * Get connection status\n   */\n  isConnected(): boolean {\n    return this.socket?.readyState === WebSocket.OPEN;\n  }\n\n  /**\n   * Get connection state\n   */\n  getConnectionState(): string {\n    if (!this.socket) return 'disconnected';\n\n    switch (this.socket.readyState) {\n      case WebSocket.CONNECTING:\n        return 'connecting';\n      case WebSocket.OPEN:\n        return 'connected';\n      case WebSocket.CLOSING:\n        return 'closing';\n      case WebSocket.CLOSED:\n        return 'disconnected';\n      default:\n        return 'unknown';\n    }\n  }\n\n  // Private methods\n  private handleMessage(data: WebSocketMessage): void {\n    this.emit(data.type, data);\n  }\n\n  private emit(event: string, data: any): void {\n    const listeners = this.eventListeners.get(event);\n    if (listeners) {\n      listeners.forEach(callback => {\n        try {\n          callback(data);\n        } catch (error) {\n          console.error(\n            `Error in WebSocket event listener for ${event}:`,\n            error,\n          );\n        }\n      });\n    }\n  }\n\n  private shouldReconnect(): boolean {\n    return this.reconnectAttempts < this.config.maxReconnectAttempts!;\n  }\n\n  private scheduleReconnect(): void {\n    if (this.reconnectTimer) {\n      clearTimeout(this.reconnectTimer);\n    }\n\n    this.reconnectTimer = setTimeout(() => {\n      this.reconnectAttempts++;\n      console.log(\n        `Attempting to reconnect (${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`,\n      );\n      this.connect().catch(error => {\n        console.error('Reconnection failed:', error);\n      });\n    }, this.config.reconnectInterval);\n  }\n}\n\n// WebSocket service factory\nexport const createWebSocketService = (\n  config: WebSocketConfig,\n): WebSocketService => {\n  return new WebSocketService(config);\n};\n\n// Messaging WebSocket service factory (requires conversation ID)\nexport const createMessagingWebSocketService = (conversationId: string) =>\n  createWebSocketService({\n    url: `ws://192.168.2.65:8000/ws/messaging/${conversationId}/`,\n    reconnectInterval: 3000,\n    maxReconnectAttempts: 5,\n  });\n\n// Notifications WebSocket service factory (requires user ID)\nexport const createNotificationsWebSocketService = (userId: string) =>\n  createWebSocketService({\n    url: `ws://192.168.2.65:8000/ws/notifications/${userId}/`,\n    reconnectInterval: 5000,\n    maxReconnectAttempts: 3,\n  });\n\nexport default WebSocketService;\n"], "mappings": ";;;;;;;;AAeA,IAAAA,qBAAA,GAAAC,OAAA;AAAqE,IA0B/DC,gBAAgB;EASpB,SAAAA,iBAAYC,MAAuB,EAAE;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAH,gBAAA;IAAA,KAR7BI,MAAM,GAAqB,IAAI;IAAA,KAE/BC,iBAAiB,GAAG,CAAC;IAAA,KACrBC,cAAc,GAA0B,IAAI;IAAA,KAC5CC,YAAY,GAAG,KAAK;IAAA,KACpBC,cAAc,GAA0C,IAAIC,GAAG,CAAC,CAAC;IAAA,KACjEC,iBAAiB,GAAyB,IAAI;IAGpD,IAAI,CAACT,MAAM,GAAAU,MAAA,CAAAC,MAAA;MACTC,iBAAiB,EAAE,IAAI;MACvBC,oBAAoB,EAAE;IAAC,GACpBb,MAAM,CACV;EACH;EAAC,WAAAc,aAAA,CAAAZ,OAAA,EAAAH,gBAAA;IAAAgB,GAAA;IAAAC,KAAA;MAAA,IAAAC,QAAA,OAAAC,kBAAA,CAAAhB,OAAA,EAKD,WAAciB,KAAc,EAAiB;QAAA,IAAAC,YAAA;UAAAC,KAAA;QAC3C,IAAI,EAAAD,YAAA,OAAI,CAACjB,MAAM,qBAAXiB,YAAA,CAAaE,UAAU,MAAKC,SAAS,CAACC,IAAI,EAAE;UAC9C,OAAOC,OAAO,CAACC,OAAO,CAAC,CAAC;QAC1B;QAEA,IAAI,IAAI,CAACjB,iBAAiB,EAAE;UAC1B,OAAO,IAAI,CAACA,iBAAiB;QAC/B;QAEA,IAAI,CAACA,iBAAiB,GAAG,IAAIgB,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;UACxD,IAAI;YACFN,KAAI,CAACf,YAAY,GAAG,IAAI;YAGxB,IAAMsB,KAAK,GAAGT,KAAK,GACf,GAAGE,KAAI,CAACrB,MAAM,CAAC6B,GAAG,UAAUV,KAAK,EAAE,GACnCE,KAAI,CAACrB,MAAM,CAAC6B,GAAG;YAEnBR,KAAI,CAAClB,MAAM,GAAG,IAAIoB,SAAS,CAACK,KAAK,EAAEP,KAAI,CAACrB,MAAM,CAAC8B,SAAS,CAAC;YAEzDT,KAAI,CAAClB,MAAM,CAAC4B,MAAM,GAAG,YAAM;cACzBC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;cAClCZ,KAAI,CAACf,YAAY,GAAG,KAAK;cACzBe,KAAI,CAACjB,iBAAiB,GAAG,CAAC;cAC1BiB,KAAI,CAACZ,iBAAiB,GAAG,IAAI;cAC7BY,KAAI,CAACa,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;cAC1BR,OAAO,CAAC,CAAC;YACX,CAAC;YAEDL,KAAI,CAAClB,MAAM,CAACgC,SAAS,GAAG,UAAAC,KAAK,EAAI;cAC/B,IAAI;gBACF,IAAMC,IAAsB,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;gBACrDhB,KAAI,CAACmB,aAAa,CAACH,IAAI,CAAC;cAC1B,CAAC,CAAC,OAAOI,KAAK,EAAE;gBACdT,OAAO,CAACS,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;gBAE1DC,iDAA2B,CAACC,oBAAoB,CAACF,KAAK,EAAW;kBAC/DG,MAAM,EAAE,qBAAqB;kBAC7BC,cAAc,EAAE;oBAAEC,UAAU,EAAEV,KAAK,CAACC,IAAI;oBAAER,GAAG,EAAER,KAAI,CAACrB,MAAM,CAAC6B;kBAAI;gBACjE,CAAC,CAAC;cACJ;YACF,CAAC;YAEDR,KAAI,CAAClB,MAAM,CAAC4C,OAAO;cAAA,IAAAC,IAAA,OAAA9B,kBAAA,CAAAhB,OAAA,EAAG,WAAMkC,KAAK,EAAI;gBACnCJ,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEG,KAAK,CAACa,IAAI,EAAEb,KAAK,CAACc,MAAM,CAAC;gBAChE7B,KAAI,CAACf,YAAY,GAAG,KAAK;gBACzBe,KAAI,CAACZ,iBAAiB,GAAG,IAAI;gBAC7BY,KAAI,CAACa,IAAI,CAAC,cAAc,EAAE;kBAAEe,IAAI,EAAEb,KAAK,CAACa,IAAI;kBAAEC,MAAM,EAAEd,KAAK,CAACc;gBAAO,CAAC,CAAC;gBAGrE,IAAId,KAAK,CAACa,IAAI,KAAK,IAAI,EAAE;kBAEvB,MAAMP,iDAA2B,CAACS,yBAAyB,CACzD,IAAIC,KAAK,CAAC,iCAAiC,CAAC,EAC5C;oBACER,MAAM,EAAE,uBAAuB;oBAC/BC,cAAc,EAAE;sBAAEI,IAAI,EAAEb,KAAK,CAACa,IAAI;sBAAEC,MAAM,EAAEd,KAAK,CAACc,MAAM;sBAAErB,GAAG,EAAER,KAAI,CAACrB,MAAM,CAAC6B;oBAAI;kBACjF,CACF,CAAC;gBACH,CAAC,MAAM,IAAI,CAACO,KAAK,CAACiB,QAAQ,IAAIhC,KAAI,CAACiC,eAAe,CAAC,CAAC,EAAE;kBACpDjC,KAAI,CAACkC,iBAAiB,CAAC,CAAC;gBAC1B;cACF,CAAC;cAAA,iBAAAC,GAAA;gBAAA,OAAAR,IAAA,CAAAS,KAAA,OAAAC,SAAA;cAAA;YAAA;YAEDrC,KAAI,CAAClB,MAAM,CAACwD,OAAO;cAAA,IAAAC,KAAA,OAAA1C,kBAAA,CAAAhB,OAAA,EAAG,WAAMuC,KAAK,EAAI;gBAAA,IAAAoB,aAAA;gBACnC7B,OAAO,CAACS,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;gBACxCpB,KAAI,CAACf,YAAY,GAAG,KAAK;gBACzBe,KAAI,CAACZ,iBAAiB,GAAG,IAAI;gBAG7B,MAAMiC,iDAA2B,CAACC,oBAAoB,CAACF,KAAK,EAAW;kBACrEG,MAAM,EAAE,kBAAkB;kBAC1BC,cAAc,EAAE;oBACdhB,GAAG,EAAER,KAAI,CAACrB,MAAM,CAAC6B,GAAG;oBACpBP,UAAU,GAAAuC,aAAA,GAAExC,KAAI,CAAClB,MAAM,qBAAX0D,aAAA,CAAavC;kBAC3B;gBACF,CAAC,CAAC;gBAEFD,KAAI,CAACa,IAAI,CAAC,OAAO,EAAE;kBAAEO,KAAK,EAALA;gBAAM,CAAC,CAAC;gBAC7Bd,MAAM,CAACc,KAAK,CAAC;cACf,CAAC;cAAA,iBAAAqB,GAAA;gBAAA,OAAAF,KAAA,CAAAH,KAAA,OAAAC,SAAA;cAAA;YAAA;UACH,CAAC,CAAC,OAAOjB,KAAK,EAAE;YACdpB,KAAI,CAACf,YAAY,GAAG,KAAK;YACzBe,KAAI,CAACZ,iBAAiB,GAAG,IAAI;YAC7BkB,MAAM,CAACc,KAAK,CAAC;UACf;QACF,CAAC,CAAC;QAEF,OAAO,IAAI,CAAChC,iBAAiB;MAC/B,CAAC;MAAA,SAzFKsD,OAAOA,CAAAC,EAAA;QAAA,OAAA/C,QAAA,CAAAwC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAPK,OAAO;IAAA;EAAA;IAAAhD,GAAA;IAAAC,KAAA,EA8Fb,SAAAiD,UAAUA,CAAA,EAAS;MACjB,IAAI,IAAI,CAAC5D,cAAc,EAAE;QACvB6D,YAAY,CAAC,IAAI,CAAC7D,cAAc,CAAC;QACjC,IAAI,CAACA,cAAc,GAAG,IAAI;MAC5B;MAEA,IAAI,IAAI,CAACF,MAAM,EAAE;QACf,IAAI,CAACA,MAAM,CAACgE,KAAK,CAAC,IAAI,EAAE,mBAAmB,CAAC;QAC5C,IAAI,CAAChE,MAAM,GAAG,IAAI;MACpB;MAEA,IAAI,CAACM,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACL,iBAAiB,GAAG,CAAC;IAC5B;EAAC;IAAAW,GAAA;IAAAC,KAAA;MAAA,IAAAoD,KAAA,OAAAlD,kBAAA,CAAAhB,OAAA,EAKD,WAAWmE,OAAY,EAAoB;QAAA,IAAAC,aAAA;QACzC,IAAI,EAAAA,aAAA,OAAI,CAACnE,MAAM,qBAAXmE,aAAA,CAAahD,UAAU,MAAKC,SAAS,CAACC,IAAI,EAAE;UAC9C,IAAI;YACF,IAAI,CAACrB,MAAM,CAACoE,IAAI,CAACjC,IAAI,CAACkC,SAAS,CAACH,OAAO,CAAC,CAAC;YACzC,OAAO,IAAI;UACb,CAAC,CAAC,OAAO5B,KAAK,EAAE;YACdT,OAAO,CAACS,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;YACzD,MAAMC,iDAA2B,CAACC,oBAAoB,CAACF,KAAK,EAAW;cACrEG,MAAM,EAAE,oBAAoB;cAC5BC,cAAc,EAAE;gBAAEwB,OAAO,EAAPA,OAAO;gBAAExC,GAAG,EAAE,IAAI,CAAC7B,MAAM,CAAC6B;cAAI;YAClD,CAAC,CAAC;YACF,OAAO,KAAK;UACd;QACF,CAAC,MAAM;UAAA,IAAA4C,aAAA;UACLzC,OAAO,CAAC0C,IAAI,CAAC,4CAA4C,EAAEL,OAAO,CAAC;UACnE,MAAM3B,iDAA2B,CAACC,oBAAoB,CACpD,IAAIS,KAAK,CAAC,yBAAyB,CAAC,EACpC;YACER,MAAM,EAAE,4BAA4B;YACpCC,cAAc,EAAE;cACdwB,OAAO,EAAPA,OAAO;cACPxC,GAAG,EAAE,IAAI,CAAC7B,MAAM,CAAC6B,GAAG;cACpBP,UAAU,GAAAmD,aAAA,GAAE,IAAI,CAACtE,MAAM,qBAAXsE,aAAA,CAAanD;YAC3B;UACF,CACF,CAAC;UACD,OAAO,KAAK;QACd;MACF,CAAC;MAAA,SA5BKiD,IAAIA,CAAAI,GAAA;QAAA,OAAAP,KAAA,CAAAX,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAJa,IAAI;IAAA;EAAA;IAAAxD,GAAA;IAAAC,KAAA;MAAA,IAAA4D,gBAAA,OAAA1D,kBAAA,CAAAhB,OAAA,EAiCV,WAAsB2E,OAAe,EAAEC,OAAgB,EAAoB;QACzE,aAAa,IAAI,CAACP,IAAI,CAAC;UACrBQ,IAAI,EAAE,cAAc;UACpBF,OAAO,EAAPA,OAAO;UACPG,QAAQ,EAAEF;QACZ,CAAC,CAAC;MACJ,CAAC;MAAA,SANKG,eAAeA,CAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAP,gBAAA,CAAAnB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAfuB,eAAe;IAAA;EAAA;IAAAlE,GAAA;IAAAC,KAAA;MAAA,IAAAoE,oBAAA,OAAAlE,kBAAA,CAAAhB,OAAA,EAWrB,WAA0BmF,QAAiB,EAAoB;QAC7D,aAAa,IAAI,CAACd,IAAI,CAAC;UACrBQ,IAAI,EAAE,kBAAkB;UACxBO,SAAS,EAAED;QACb,CAAC,CAAC;MACJ,CAAC;MAAA,SALKE,mBAAmBA,CAAAC,GAAA;QAAA,OAAAJ,oBAAA,CAAA3B,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAnB6B,mBAAmB;IAAA;EAAA;IAAAxE,GAAA;IAAAC,KAAA;MAAA,IAAAyE,iBAAA,OAAAvE,kBAAA,CAAAhB,OAAA,EAUzB,WAAuBwF,UAAoB,EAAoB;QAC7D,aAAa,IAAI,CAACnB,IAAI,CAAC;UACrBQ,IAAI,EAAE,WAAW;UACjBY,WAAW,EAAED;QACf,CAAC,CAAC;MACJ,CAAC;MAAA,SALKE,gBAAgBA,CAAAC,GAAA;QAAA,OAAAJ,iBAAA,CAAAhC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAhBkC,gBAAgB;IAAA;EAAA;IAAA7E,GAAA;IAAAC,KAAA,EAUtB,SAAA8E,EAAEA,CAAC1D,KAAa,EAAE2D,QAA6B,EAAQ;MACrD,IAAI,CAAC,IAAI,CAACxF,cAAc,CAACyF,GAAG,CAAC5D,KAAK,CAAC,EAAE;QACnC,IAAI,CAAC7B,cAAc,CAAC0F,GAAG,CAAC7D,KAAK,EAAE,IAAI8D,GAAG,CAAC,CAAC,CAAC;MAC3C;MACA,IAAI,CAAC3F,cAAc,CAAC4F,GAAG,CAAC/D,KAAK,CAAC,CAAEgE,GAAG,CAACL,QAAQ,CAAC;IAC/C;EAAC;IAAAhF,GAAA;IAAAC,KAAA,EAKD,SAAAqF,GAAGA,CAACjE,KAAa,EAAE2D,QAA6B,EAAQ;MACtD,IAAMO,SAAS,GAAG,IAAI,CAAC/F,cAAc,CAAC4F,GAAG,CAAC/D,KAAK,CAAC;MAChD,IAAIkE,SAAS,EAAE;QACbA,SAAS,CAACC,MAAM,CAACR,QAAQ,CAAC;MAC5B;IACF;EAAC;IAAAhF,GAAA;IAAAC,KAAA,EAKD,SAAAwF,WAAWA,CAAA,EAAY;MAAA,IAAAC,aAAA;MACrB,OAAO,EAAAA,aAAA,OAAI,CAACtG,MAAM,qBAAXsG,aAAA,CAAanF,UAAU,MAAKC,SAAS,CAACC,IAAI;IACnD;EAAC;IAAAT,GAAA;IAAAC,KAAA,EAKD,SAAA0F,kBAAkBA,CAAA,EAAW;MAC3B,IAAI,CAAC,IAAI,CAACvG,MAAM,EAAE,OAAO,cAAc;MAEvC,QAAQ,IAAI,CAACA,MAAM,CAACmB,UAAU;QAC5B,KAAKC,SAAS,CAACoF,UAAU;UACvB,OAAO,YAAY;QACrB,KAAKpF,SAAS,CAACC,IAAI;UACjB,OAAO,WAAW;QACpB,KAAKD,SAAS,CAACqF,OAAO;UACpB,OAAO,SAAS;QAClB,KAAKrF,SAAS,CAACsF,MAAM;UACnB,OAAO,cAAc;QACvB;UACE,OAAO,SAAS;MACpB;IACF;EAAC;IAAA9F,GAAA;IAAAC,KAAA,EAGD,SAAQwB,aAAaA,CAACH,IAAsB,EAAQ;MAClD,IAAI,CAACH,IAAI,CAACG,IAAI,CAAC0C,IAAI,EAAE1C,IAAI,CAAC;IAC5B;EAAC;IAAAtB,GAAA;IAAAC,KAAA,EAED,SAAQkB,IAAIA,CAACE,KAAa,EAAEC,IAAS,EAAQ;MAC3C,IAAMiE,SAAS,GAAG,IAAI,CAAC/F,cAAc,CAAC4F,GAAG,CAAC/D,KAAK,CAAC;MAChD,IAAIkE,SAAS,EAAE;QACbA,SAAS,CAACQ,OAAO,CAAC,UAAAf,QAAQ,EAAI;UAC5B,IAAI;YACFA,QAAQ,CAAC1D,IAAI,CAAC;UAChB,CAAC,CAAC,OAAOI,KAAK,EAAE;YACdT,OAAO,CAACS,KAAK,CACX,yCAAyCL,KAAK,GAAG,EACjDK,KACF,CAAC;UACH;QACF,CAAC,CAAC;MACJ;IACF;EAAC;IAAA1B,GAAA;IAAAC,KAAA,EAED,SAAQsC,eAAeA,CAAA,EAAY;MACjC,OAAO,IAAI,CAAClD,iBAAiB,GAAG,IAAI,CAACJ,MAAM,CAACa,oBAAqB;IACnE;EAAC;IAAAE,GAAA;IAAAC,KAAA,EAED,SAAQuC,iBAAiBA,CAAA,EAAS;MAAA,IAAAwD,MAAA;MAChC,IAAI,IAAI,CAAC1G,cAAc,EAAE;QACvB6D,YAAY,CAAC,IAAI,CAAC7D,cAAc,CAAC;MACnC;MAEA,IAAI,CAACA,cAAc,GAAG2G,UAAU,CAAC,YAAM;QACrCD,MAAI,CAAC3G,iBAAiB,EAAE;QACxB4B,OAAO,CAACC,GAAG,CACT,4BAA4B8E,MAAI,CAAC3G,iBAAiB,IAAI2G,MAAI,CAAC/G,MAAM,CAACa,oBAAoB,GACxF,CAAC;QACDkG,MAAI,CAAChD,OAAO,CAAC,CAAC,CAACkD,KAAK,CAAC,UAAAxE,KAAK,EAAI;UAC5BT,OAAO,CAACS,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC9C,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAACzC,MAAM,CAACY,iBAAiB,CAAC;IACnC;EAAC;AAAA;AAII,IAAMsG,sBAAsB,GAAAC,OAAA,CAAAD,sBAAA,GAAG,SAAzBA,sBAAsBA,CACjClH,MAAuB,EACF;EACrB,OAAO,IAAID,gBAAgB,CAACC,MAAM,CAAC;AACrC,CAAC;AAGM,IAAMoH,+BAA+B,GAAAD,OAAA,CAAAC,+BAAA,GAAG,SAAlCA,+BAA+BA,CAAIC,cAAsB;EAAA,OACpEH,sBAAsB,CAAC;IACrBrF,GAAG,EAAE,uCAAuCwF,cAAc,GAAG;IAC7DzG,iBAAiB,EAAE,IAAI;IACvBC,oBAAoB,EAAE;EACxB,CAAC,CAAC;AAAA;AAGG,IAAMyG,mCAAmC,GAAAH,OAAA,CAAAG,mCAAA,GAAG,SAAtCA,mCAAmCA,CAAIC,MAAc;EAAA,OAChEL,sBAAsB,CAAC;IACrBrF,GAAG,EAAE,2CAA2C0F,MAAM,GAAG;IACzD3G,iBAAiB,EAAE,IAAI;IACvBC,oBAAoB,EAAE;EACxB,CAAC,CAAC;AAAA;AAAC,IAAA2G,QAAA,GAAAL,OAAA,CAAAjH,OAAA,GAEUH,gBAAgB", "ignoreList": []}