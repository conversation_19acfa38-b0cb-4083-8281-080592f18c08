/**
 * Service Discovery API Service
 *
 * Main API service for service discovery functionality
 * Handles HTTP requests, caching, and error handling
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { ALL_SERVICE_PROVIDERS } from '../../../config/testAccounts';
import {
  Service,
  ServiceProvider,
  ServiceCategory,
  ServiceDetails,
  SearchFilters,
  PaginatedResponse,
  SearchResponse,
  Review,
  ServiceDiscoveryError,
  ServiceImage,
} from '../types';

import { apiConfig, endpoints } from './config';
import {
  buildQueryParams,
  handleApiError,
  retryApiCall,
  createTimeoutPromise,
} from './utils';

// Import test accounts for mock data

/**
 * Base API client with common functionality
 */
class ServiceDiscoveryApiClient {
  private baseUrl: string;
  private timeout: number;
  private retryAttempts: number;
  private cache: Map<string, { data: any; timestamp: number }>;
  private useMockData: boolean;

  constructor() {
    this.baseUrl = apiConfig.baseUrl;
    this.timeout = apiConfig.timeout;
    this.retryAttempts = apiConfig.retryAttempts;
    this.cache = new Map();
    // Use real API now that backend is available
    this.useMockData = false;
  }

  /**
   * Get authentication token from storage
   */
  private async getAuthToken(): Promise<string | null> {
    try {
      const AsyncStorage = await import('@react-native-async-storage/async-storage');
      const token = await AsyncStorage.default.getItem('auth_token');
      return token;
    } catch (error) {
      console.warn('Failed to get auth token:', error);
      return null;
    }
  }

  /**
   * Make HTTP request with timeout and retry logic
   */
  private async makeRequest<T>(
    url: string,
    options: RequestInit = {},
    useCache: boolean = true,
  ): Promise<T> {
    const fullUrl = `${this.baseUrl}${url}`;
    const cacheKey = `${options.method || 'GET'}_${fullUrl}`;

    // Check cache first
    if (useCache && this.isValidCache(cacheKey)) {
      return this.cache.get(cacheKey)!.data;
    }

    // Get authentication token
    const token = await this.getAuthToken();

    const requestOptions: RequestInit = {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      },
    };

    try {
      const response = await retryApiCall(async () => {
        const fetchPromise = fetch(fullUrl, requestOptions);
        const timeoutPromise = createTimeoutPromise(this.timeout);

        return Promise.race([fetchPromise, timeoutPromise]);
      }, this.retryAttempts);

      if (!response.ok) {
        throw {
          response: {
            status: response.status,
            data: await response.json().catch(() => ({})),
          },
        };
      }

      const data = await response.json();

      // Cache successful responses
      if (useCache && (options.method === 'GET' || !options.method)) {
        this.cache.set(cacheKey, {
          data,
          timestamp: Date.now(),
        });
      }

      return data;
    } catch (error) {
      throw handleApiError(error);
    }
  }

  /**
   * Check if cached data is still valid
   */
  private isValidCache(key: string): boolean {
    const cached = this.cache.get(key);
    if (!cached) return false;

    const age = Date.now() - cached.timestamp;
    return age < apiConfig.cacheTimeout;
  }

  /**
   * Generate mock services from test accounts
   */
  private generateMockServices(): Service[] {
    const services: Service[] = [];

    ALL_SERVICE_PROVIDERS.forEach((provider, index) => {
      const servicesByCategory: { [key: string]: Service[] } = {
        Barber: [
          {
            id: `${index}_1`,
            name: 'Classic Haircut',
            description: 'Traditional barbershop cut',
            price: 35,
            duration: 45,
            category: 'Barber',
          },
          {
            id: `${index}_2`,
            name: 'Beard Trim',
            description: 'Professional beard trimming',
            price: 25,
            duration: 30,
            category: 'Barber',
          },
        ],
        Salon: [
          {
            id: `${index}_3`,
            name: 'Hair Color',
            description: 'Full color treatment',
            price: 120,
            duration: 120,
            category: 'Salon',
          },
          {
            id: `${index}_4`,
            name: 'Hair Styling',
            description: 'Professional styling and blowout',
            price: 65,
            duration: 60,
            category: 'Salon',
          },
        ],
        'Nail Services': [
          {
            id: `${index}_5`,
            name: 'Manicure',
            description: 'Classic manicure',
            price: 35,
            duration: 45,
            category: 'Nails',
          },
          {
            id: `${index}_6`,
            name: 'Pedicure',
            description: 'Relaxing pedicure',
            price: 45,
            duration: 60,
            category: 'Nails',
          },
        ],
        'Lash Services': [
          {
            id: `${index}_7`,
            name: 'Lash Extensions',
            description: 'Beautiful lash extensions',
            price: 85,
            duration: 90,
            category: 'Lashes',
          },
        ],
        Braiding: [
          {
            id: `${index}_8`,
            name: 'Box Braids',
            description: 'Protective styling',
            price: 150,
            duration: 180,
            category: 'Braiding',
          },
        ],
        Massage: [
          {
            id: `${index}_9`,
            name: 'Relaxation Massage',
            description: 'Full body massage',
            price: 90,
            duration: 60,
            category: 'Massage',
          },
        ],
        Skincare: [
          {
            id: `${index}_10`,
            name: 'Facial Treatment',
            description: 'Deep cleansing facial',
            price: 75,
            duration: 75,
            category: 'Skincare',
          },
        ],
      };

      const categoryServices = servicesByCategory[provider.category] || [];
      services.push(...categoryServices);
    });

    return services;
  }

  /**
   * Generate mock service providers from test accounts
   */
  private generateMockProviders(): ServiceProvider[] {
    return ALL_SERVICE_PROVIDERS.map((account, index) => ({
      id: `provider_${index + 1}`,
      business_name: `${account.firstName} ${account.lastName} ${account.category}`,
      description: account.description || 'Professional beauty services',
      address: account.address || '123 Main St, Ottawa, ON',
      phone: account.phone || '(*************',
      email: account.email,
      rating: 4.5 + Math.random() * 0.5, // Random rating between 4.5-5.0
      review_count: Math.floor(Math.random() * 100) + 20, // Random review count 20-120
      is_featured: Math.random() > 0.7, // 30% chance of being featured
      services: [], // Will be populated separately
      availability: {
        monday: { open: '09:00', close: '17:00', is_open: true },
        tuesday: { open: '09:00', close: '17:00', is_open: true },
        wednesday: { open: '09:00', close: '17:00', is_open: true },
        thursday: { open: '09:00', close: '17:00', is_open: true },
        friday: { open: '09:00', close: '17:00', is_open: true },
        saturday: { open: '10:00', close: '16:00', is_open: true },
        sunday: { open: '10:00', close: '16:00', is_open: false },
      },
      images: [],
      location: {
        latitude: 45.4215 + (Math.random() - 0.5) * 0.1,
        longitude: -75.6972 + (Math.random() - 0.5) * 0.1,
      },
    }));
  }

  /**
   * Generate mock categories
   */
  private generateMockCategories(): ServiceCategory[] {
    return [
      {
        id: '1',
        name: 'Barber',
        description: "Professional barbering and men's grooming",
        service_count: 8,
        icon: 'scissors',
      },
      {
        id: '2',
        name: 'Salon',
        description: 'Hair styling, coloring, and treatments',
        service_count: 12,
        icon: 'hair-dryer',
      },
      {
        id: '3',
        name: 'Nail Services',
        description: 'Manicures, pedicures, and nail art',
        service_count: 12,
        icon: 'hand',
      },
      {
        id: '4',
        name: 'Lash Services',
        description: 'Eyelash extensions and treatments',
        service_count: 8,
        icon: 'eye',
      },
      {
        id: '5',
        name: 'Braiding',
        description: 'Protective styling and braiding services',
        service_count: 10,
        icon: 'braid',
      },
      {
        id: '6',
        name: 'Massage',
        description: 'Relaxation and therapeutic massage',
        service_count: 6,
        icon: 'massage',
      },
      {
        id: '7',
        name: 'Skincare',
        description: 'Facial treatments and skincare',
        service_count: 9,
        icon: 'face',
      },
    ];
  }

  /**
   * Clear cache
   */
  public clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get services with optional filters
   */
  async getServices(
    filters: SearchFilters = {},
    page: number = 1,
  ): Promise<PaginatedResponse<Service>> {
    const params = buildQueryParams({ ...filters, page });
    const url = `${endpoints.services}?${params.toString()}`;

    return this.makeRequest<PaginatedResponse<Service>>(url);
  }

  /**
   * Get service details by ID
   */
  async getServiceDetails(serviceId: string): Promise<ServiceDetails> {
    const url = endpoints.serviceDetails(serviceId);
    return this.makeRequest<ServiceDetails>(url);
  }

  /**
   * Get service providers with optional filters
   */
  async getProviders(
    filters: SearchFilters = {},
    page: number = 1,
  ): Promise<PaginatedResponse<ServiceProvider>> {
    const params = buildQueryParams({ ...filters, page });
    const url = `${endpoints.providers}?${params.toString()}`;

    return this.makeRequest<PaginatedResponse<ServiceProvider>>(url);
  }

  /**
   * Get provider details by ID
   */
  async getProviderDetails(providerId: string): Promise<ServiceProvider> {
    const url = endpoints.providerDetails(providerId);
    return this.makeRequest<ServiceProvider>(url);
  }

  /**
   * Get services by provider
   */
  async getServicesByProvider(providerId: string): Promise<Service[]> {
    const url = endpoints.providerServices(providerId);
    return this.makeRequest<Service[]>(url);
  }

  /**
   * Get service categories
   */
  async getCategories(): Promise<ServiceCategory[]> {
    if (this.useMockData) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 200));
      return this.generateMockCategories();
    }

    const response = await this.makeRequest<
      { results: ServiceCategory[] } | ServiceCategory[]
    >(endpoints.categories);

    // Handle paginated response format
    if (
      response &&
      typeof response === 'object' &&
      'results' in response &&
      Array.isArray(response.results)
    ) {
      return response.results;
    } else if (Array.isArray(response)) {
      // Fallback for direct array response
      return response;
    } else {
      console.warn('Unexpected response format for categories:', response);
      return [];
    }
  }

  /**
   * Get popular categories
   */
  async getPopularCategories(): Promise<ServiceCategory[]> {
    if (this.useMockData) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 200));
      return this.generateMockCategories().slice(0, 4); // Return top 4 popular categories
    }

    const response = await this.makeRequest<
      { results: ServiceCategory[] } | ServiceCategory[]
    >(endpoints.popularCategories);

    // Handle paginated response format
    if (
      response &&
      typeof response === 'object' &&
      'results' in response &&
      Array.isArray(response.results)
    ) {
      return response.results;
    } else if (Array.isArray(response)) {
      // Fallback for direct array response
      return response;
    } else {
      console.warn(
        'Unexpected response format for popular categories:',
        response,
      );
      return [];
    }
  }

  /**
   * Search services and providers
   */
  async search(
    query: string,
    filters: SearchFilters = {},
  ): Promise<SearchResponse> {
    console.log('🔍 serviceDiscoveryApi.search called with query:', query);
    console.log('🔍 serviceDiscoveryApi.search filters:', filters);

    try {
      // Build query parameters for the new backend API
      const params = new URLSearchParams();

      if (query) {
        params.append('q', query);
      }

      if (filters.category) {
        params.append('category', filters.category);
      }

      if (filters.location?.latitude && filters.location?.longitude) {
        params.append('latitude', filters.location.latitude.toString());
        params.append('longitude', filters.location.longitude.toString());
        if (filters.location.radius) {
          params.append('radius', filters.location.radius.toString());
        }
      }

      if (filters.price_min !== undefined) {
        params.append('price_min', filters.price_min.toString());
      }

      if (filters.price_max !== undefined) {
        params.append('price_max', filters.price_max.toString());
      }

      if (filters.rating_min !== undefined) {
        params.append('rating_min', filters.rating_min.toString());
      }

      if (filters.sort_by) {
        params.append('sort_by', filters.sort_by);
      }

      if (filters.page) {
        params.append('page', filters.page.toString());
      }

      if (filters.page_size) {
        params.append('page_size', filters.page_size.toString());
      }

      const url = `${endpoints.search}?${params.toString()}`;
      console.log('🔍 Making API call to:', url);

      const response = await this.makeRequest<any>(url, {}, false); // Don't cache search results
      console.log('🔍 Search API response:', response);

      // Transform the response to match our expected format
      return {
        services: response.services || { results: [], count: 0, next: null, previous: null },
        providers: response.providers || { results: [], count: 0, next: null, previous: null },
        categories: [], // Categories are not returned in search results
        total_results: response.search_metadata?.pagination?.total_results || 0,
        search_metadata: response.search_metadata,
      };
    } catch (error) {
      console.error('❌ Search API failed:', error);

      // Return fallback empty results
      return {
        services: { results: [], count: 0, next: null, previous: null },
        providers: { results: [], count: 0, next: null, previous: null },
        categories: [],
        total_results: 0,
      };
    }
  }

  /**
   * Get search suggestions
   */
  async getSearchSuggestions(query: string): Promise<any[]> {
    if (!query || query.length < 2) {
      return [];
    }

    try {
      const params = new URLSearchParams();
      params.append('q', query);
      params.append('limit', '10');

      const url = `${endpoints.searchSuggestions}?${params.toString()}`;
      const response = await this.makeRequest<{ suggestions: any[] }>(url, {}, false);

      return response.suggestions || [];
    } catch (error) {
      console.error('❌ Search suggestions failed:', error);
      return [];
    }
  }

  /**
   * Get popular searches and categories
   */
  async getPopularSearches(): Promise<any> {
    try {
      const response = await this.makeRequest<any>(endpoints.searchPopular);
      return response;
    } catch (error) {
      console.error('❌ Popular searches failed:', error);
      return {
        popular_categories: [],
        popular_services: [],
        featured_providers: [],
      };
    }
  }

  /**
   * Get reviews for a service
   */
  async getServiceReviews(
    serviceId: string,
    page: number = 1,
  ): Promise<PaginatedResponse<Review>> {
    const params = buildQueryParams({ page });
    const url = `${endpoints.serviceReviews(serviceId)}?${params.toString()}`;

    return this.makeRequest<PaginatedResponse<Review>>(url);
  }

  /**
   * Get all reviews with filters
   */
  async getReviews(
    filters: { service?: string; provider?: string; rating_min?: number } = {},
    page: number = 1,
  ): Promise<PaginatedResponse<Review>> {
    const params = buildQueryParams({ ...filters, page });
    const url = `${endpoints.reviews}?${params.toString()}`;

    return this.makeRequest<PaginatedResponse<Review>>(url);
  }

  /**
   * Get service gallery images
   */
  async getServiceGallery(serviceId: string): Promise<ServiceImage[]> {
    const url = `/api/catalog/services/${serviceId}/gallery/`;
    return this.makeRequest<ServiceImage[]>(url);
  }
}

// Export singleton instance
export const serviceDiscoveryApi = new ServiceDiscoveryApiClient();
