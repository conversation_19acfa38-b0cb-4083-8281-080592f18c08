/**
 * Unified Error Handling System - Main Export
 * 
 * Central export point for the unified error handling system.
 * This replaces all existing error handling services and provides
 * a single, consistent API for error handling across the application.
 * 
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

// Core Service - Import and re-export to avoid circular dependencies
import { unifiedErrorHandlingService as _unifiedErrorHandlingService } from './UnifiedErrorHandlingService';

// Add runtime validation for Hermes compatibility
if (!_unifiedErrorHandlingService) {
  console.error('[UnifiedErrorHandling] Service instance is undefined - this indicates a module loading issue');
  throw new Error('UnifiedErrorHandlingService failed to initialize properly');
}

export const unifiedErrorHandlingService = _unifiedErrorHandlingService;
export default unifiedErrorHandlingService;

// Sub-services
export {
  userFeedbackService
} from './UserFeedbackService';

export {
  analyticsIntegrationService
} from './AnalyticsIntegrationService';

// Types
export type {
  ErrorType,
  ErrorSeverity,
  ErrorCategory,
  ErrorContext,
  ErrorBreadcrumb,
  RecoveryStrategy,
  UserFeedbackConfig,
  ErrorReport,
  UnifiedErrorHandlingConfig,
  ErrorListener,
  ErrorMetrics
} from './types';

export { 
  UnifiedError,
  ErrorType,
  ErrorSeverity,
  ErrorCategory
} from './types';

// Hooks
export {
  useUnifiedErrorHandling,
  useErrorMetrics,
  useGlobalErrorListener,
  useErrorBoundary,
  useApiErrorHandling,
  useFormErrorHandling,
  useWebSocketErrorHandling
} from './hooks';

export type {
  UseUnifiedErrorHandlingOptions,
  UseUnifiedErrorHandlingResult
} from './hooks';

// Components
export {
  UnifiedErrorBoundary,
  ScreenErrorBoundary,
  FeatureErrorBoundary,
  ComponentErrorBoundary
} from './UnifiedErrorBoundary';

// Convenience Functions
export const handleError = (
  error: Error | string,
  context?: Partial<ErrorContext>
) => _unifiedErrorHandlingService.handleError(error, context);

export const handleNetworkError = (
  error: Error,
  context?: Partial<ErrorContext>
) => _unifiedErrorHandlingService.handleNetworkError(error, context);

export const handleAuthError = (
  error: Error,
  context?: Partial<ErrorContext>
) => _unifiedErrorHandlingService.handleAuthError(error, context);

export const handleValidationError = (
  error: Error,
  context?: Partial<ErrorContext>
) => _unifiedErrorHandlingService.handleValidationError(error, context);

export const handleWebSocketError = (
  error: Error,
  context?: Partial<ErrorContext>
) => _unifiedErrorHandlingService.handleWebSocketError(error, context);

export const addErrorListener = (listener: ErrorListener) =>
  _unifiedErrorHandlingService.addErrorListener(listener);

export const addRecoveryStrategy = (strategy: RecoveryStrategy) =>
  _unifiedErrorHandlingService.addRecoveryStrategy(strategy);

export const getErrorMetrics = () =>
  _unifiedErrorHandlingService.getMetrics();

export const clearErrorQueue = () =>
  _unifiedErrorHandlingService.clearErrorQueue();

export const updateErrorConfig = (config: Partial<UnifiedErrorHandlingConfig>) =>
  _unifiedErrorHandlingService.updateConfig(config);

// Error Monitoring Functions
export const getMonitoringMetrics = () =>
  _unifiedErrorHandlingService.getMonitoringMetrics();

export const getSystemHealth = () =>
  _unifiedErrorHandlingService.getSystemHealth();

export const getErrorTrends = (days?: number) =>
  _unifiedErrorHandlingService.getErrorTrends(days);

export const exportErrorData = (format?: 'json' | 'csv') =>
  _unifiedErrorHandlingService.exportErrorData(format);

export const clearMonitoringHistory = () =>
  _unifiedErrorHandlingService.clearMonitoringHistory();

// Migration Helpers
export {
  migrationUtils,
  legacyErrorHandlingService,
  legacyEnhancedErrorHandlingService,
  legacyErrorMonitoringService,
  legacyErrorReportingService,
  legacyRuntimeErrorHandler
} from './migrationHelper';
/**
 * Migration helper to replace existing error handling services
 * This provides backward compatibility while transitioning to the unified system
 */
export const createMigrationWrapper = () => {
  return {
    // Legacy errorHandlingService compatibility
    handleError: (error: any, context: any = {}, userMessage?: string) =>
      _unifiedErrorHandlingService.handleError(error, context, userMessage),

    // Legacy enhancedErrorHandlingService compatibility
    handleErrorWithRecovery: (error: any, context: any = {}) =>
      _unifiedErrorHandlingService.handleError(error, context),

    // Legacy errorMonitoringService compatibility
    reportError: (error: any, context: any = {}, severity: any = 'medium') =>
      _unifiedErrorHandlingService.handleError(error, { ...context, severity }),

    // Legacy ErrorReportingService compatibility
    reportErrorWithBreadcrumbs: (error: any, context: any = {}) =>
      _unifiedErrorHandlingService.handleError(error, context),

    // Legacy runtimeErrorHandler compatibility
    handleRuntimeError: (error: any, context: any = {}) =>
      _unifiedErrorHandlingService.handleError(error, context)
  };
};

// Initialize service on import
unifiedErrorHandlingService.initialize().catch(error => {
  console.error('Failed to initialize unified error handling service:', error);
});

/**
 * Usage Examples:
 * 
 * // Basic error handling
 * import { handleError } from './services/unifiedErrorHandling';
 * 
 * try {
 *   await someOperation();
 * } catch (error) {
 *   await handleError(error, { component: 'MyComponent', action: 'someOperation' });
 * }
 * 
 * // Using hooks in components
 * import { useUnifiedErrorHandling } from './services/unifiedErrorHandling';
 * 
 * const MyComponent = () => {
 *   const { handleError, withErrorHandling } = useUnifiedErrorHandling({
 *     component: 'MyComponent'
 *   });
 * 
 *   const loadData = async () => {
 *     const { data, error } = await withErrorHandling(
 *       () => apiClient.getData(),
 *       { action: 'loadData' }
 *     );
 *     
 *     if (data) {
 *       setData(data);
 *     }
 *   };
 * 
 *   return <div>...</div>;
 * };
 * 
 * // Using error boundaries
 * import { ScreenErrorBoundary } from './services/unifiedErrorHandling';
 * 
 * const MyScreen = () => (
 *   <ScreenErrorBoundary screenName="MyScreen">
 *     <MyScreenContent />
 *   </ScreenErrorBoundary>
 * );
 * 
 * // API error handling
 * import { useApiErrorHandling } from './services/unifiedErrorHandling';
 * 
 * const apiService = {
 *   async getData() {
 *     const { handleApiError } = useApiErrorHandling('DataService');
 *     
 *     try {
 *       return await fetch('/api/data');
 *     } catch (error) {
 *       await handleApiError(error, '/api/data', 'GET');
 *       throw error;
 *     }
 *   }
 * };
 */
