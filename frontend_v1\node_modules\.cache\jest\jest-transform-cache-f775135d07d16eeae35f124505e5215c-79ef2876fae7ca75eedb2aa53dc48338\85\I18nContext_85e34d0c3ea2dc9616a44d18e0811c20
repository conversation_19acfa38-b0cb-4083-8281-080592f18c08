06da9e2ac787a1d25552e55874c19548
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.LocalizedText = exports.LocaleSwitch = exports.I18nProvider = void 0;
Object.defineProperty(exports, "getAvailableLocales", {
  enumerable: true,
  get: function get() {
    return _i18n.getAvailableLocales;
  }
});
Object.defineProperty(exports, "getCurrentLocale", {
  enumerable: true,
  get: function get() {
    return _i18n.getCurrentLocale;
  }
});
exports.useTranslation = exports.useLocale = exports.useI18n = exports.useFormatting = void 0;
exports.withI18n = withI18n;
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _i18n = require("../utils/i18n");
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["i18nKey", "params", "fallback"];
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var I18nContext = (0, _react.createContext)(undefined);
var I18nProvider = exports.I18nProvider = function I18nProvider(_ref) {
  var children = _ref.children,
    _ref$fallbackLocale = _ref.fallbackLocale,
    fallbackLocale = _ref$fallbackLocale === void 0 ? 'en-CA' : _ref$fallbackLocale;
  var _useState = (0, _react.useState)(fallbackLocale),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    locale = _useState2[0],
    setLocaleState = _useState2[1];
  var _useState3 = (0, _react.useState)(true),
    _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
    isLoading = _useState4[0],
    setIsLoading = _useState4[1];
  (0, _react.useEffect)(function () {
    var initialize = function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        try {
          var initialLocale = yield (0, _i18n.initializeI18n)();
          setLocaleState(initialLocale);
        } catch (error) {
          console.error('Failed to initialize i18n:', error);
          setLocaleState(fallbackLocale);
        } finally {
          setIsLoading(false);
        }
      });
      return function initialize() {
        return _ref2.apply(this, arguments);
      };
    }();
    initialize();
  }, [fallbackLocale]);
  (0, _react.useEffect)(function () {
    var unsubscribe = (0, _i18n.onLocaleChange)(function (newLocale) {
      setLocaleState(newLocale);
    });
    return unsubscribe;
  }, []);
  var handleSetLocale = function () {
    var _ref3 = (0, _asyncToGenerator2.default)(function* (newLocale) {
      try {
        yield (0, _i18n.setLocale)(newLocale);
      } catch (error) {
        console.error('Failed to set locale:', error);
      }
    });
    return function handleSetLocale(_x) {
      return _ref3.apply(this, arguments);
    };
  }();
  var contextValue = {
    locale: locale,
    isLoading: isLoading,
    t: _i18n.t,
    tp: _i18n.tp,
    formatCurrency: _i18n.formatCurrency,
    formatDate: _i18n.formatDate,
    formatTime: _i18n.formatTime,
    formatPhoneNumber: _i18n.formatPhoneNumber,
    formatPostalCode: _i18n.formatPostalCode,
    setLocale: handleSetLocale,
    getAvailableLocales: _i18n.getAvailableLocales,
    getProvinces: _i18n.getProvinces,
    isFrenchCanadian: _i18n.isFrenchCanadian,
    isRTL: false
  };
  return (0, _jsxRuntime.jsx)(I18nContext.Provider, {
    value: contextValue,
    children: children
  });
};
var useI18n = exports.useI18n = function useI18n() {
  var context = (0, _react.useContext)(I18nContext);
  if (context === undefined) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  return context;
};
var useTranslation = exports.useTranslation = function useTranslation() {
  var _useI18n = useI18n(),
    t = _useI18n.t,
    tp = _useI18n.tp,
    locale = _useI18n.locale;
  return {
    t: t,
    tp: tp,
    locale: locale,
    common: {
      loading: function loading() {
        return t('common.loading');
      },
      error: function error() {
        return t('common.error');
      },
      success: function success() {
        return t('common.success');
      },
      cancel: function cancel() {
        return t('common.cancel');
      },
      confirm: function confirm() {
        return t('common.confirm');
      },
      save: function save() {
        return t('common.save');
      },
      edit: function edit() {
        return t('common.edit');
      },
      delete: function _delete() {
        return t('common.delete');
      },
      back: function back() {
        return t('common.back');
      },
      next: function next() {
        return t('common.next');
      },
      close: function close() {
        return t('common.close');
      },
      ok: function ok() {
        return t('common.ok');
      },
      yes: function yes() {
        return t('common.yes');
      },
      no: function no() {
        return t('common.no');
      }
    },
    navigation: {
      home: function home() {
        return t('navigation.home');
      },
      services: function services() {
        return t('navigation.services');
      },
      bookings: function bookings() {
        return t('navigation.bookings');
      },
      messages: function messages() {
        return t('navigation.messages');
      },
      profile: function profile() {
        return t('navigation.profile');
      },
      settings: function settings() {
        return t('navigation.settings');
      },
      help: function help() {
        return t('navigation.help');
      },
      about: function about() {
        return t('navigation.about');
      }
    },
    auth: {
      signIn: function signIn() {
        return t('auth.signIn');
      },
      signUp: function signUp() {
        return t('auth.signUp');
      },
      signOut: function signOut() {
        return t('auth.signOut');
      },
      email: function email() {
        return t('auth.email');
      },
      password: function password() {
        return t('auth.password');
      },
      forgotPassword: function forgotPassword() {
        return t('auth.forgotPassword');
      }
    }
  };
};
var useFormatting = exports.useFormatting = function useFormatting() {
  var _useI18n2 = useI18n(),
    formatCurrency = _useI18n2.formatCurrency,
    formatDate = _useI18n2.formatDate,
    formatTime = _useI18n2.formatTime,
    formatPhoneNumber = _useI18n2.formatPhoneNumber,
    formatPostalCode = _useI18n2.formatPostalCode,
    locale = _useI18n2.locale;
  return {
    currency: formatCurrency,
    date: formatDate,
    time: formatTime,
    phone: formatPhoneNumber,
    postalCode: formatPostalCode,
    locale: locale
  };
};
var useLocale = exports.useLocale = function useLocale() {
  var _useI18n3 = useI18n(),
    locale = _useI18n3.locale,
    setLocale = _useI18n3.setLocale,
    getAvailableLocales = _useI18n3.getAvailableLocales,
    isFrenchCanadian = _useI18n3.isFrenchCanadian,
    isRTL = _useI18n3.isRTL;
  return {
    locale: locale,
    setLocale: setLocale,
    availableLocales: getAvailableLocales(),
    isFrench: isFrenchCanadian(),
    isEnglish: locale === 'en-CA',
    isRTL: isRTL
  };
};
function withI18n(Component) {
  return function I18nComponent(props) {
    var i18n = useI18n();
    return (0, _jsxRuntime.jsx)(Component, Object.assign({}, props, {
      i18n: i18n
    }));
  };
}
var LocaleSwitch = exports.LocaleSwitch = function LocaleSwitch(_ref4) {
  var en = _ref4.en,
    fr = _ref4.fr,
    children = _ref4.children;
  var _useI18n4 = useI18n(),
    isFrenchCanadian = _useI18n4.isFrenchCanadian;
  if (isFrenchCanadian()) {
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: fr || children
    });
  } else {
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: en || children
    });
  }
};
var LocalizedText = exports.LocalizedText = function LocalizedText(_ref5) {
  var i18nKey = _ref5.i18nKey,
    params = _ref5.params,
    fallback = _ref5.fallback,
    textProps = (0, _objectWithoutProperties2.default)(_ref5, _excluded);
  var _useI18n5 = useI18n(),
    t = _useI18n5.t;
  var text = t(i18nKey, params);
  var displayText = text.startsWith('[Missing:') || text.startsWith('[Invalid:') ? fallback || i18nKey : text;
  return (0, _jsxRuntime.jsx)(_reactNative.Text, Object.assign({}, textProps, {
    children: displayText
  }));
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfcmVhY3QiLCJfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZCIsInJlcXVpcmUiLCJfcmVhY3ROYXRpdmUiLCJfaTE4biIsIl9qc3hSdW50aW1lIiwiX2V4Y2x1ZGVkIiwiZSIsInQiLCJXZWFrTWFwIiwiciIsIm4iLCJfX2VzTW9kdWxlIiwibyIsImkiLCJmIiwiX19wcm90b19fIiwiZGVmYXVsdCIsImhhcyIsImdldCIsInNldCIsIl90IiwiaGFzT3duUHJvcGVydHkiLCJjYWxsIiwiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJnZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IiLCJJMThuQ29udGV4dCIsImNyZWF0ZUNvbnRleHQiLCJ1bmRlZmluZWQiLCJJMThuUHJvdmlkZXIiLCJleHBvcnRzIiwiX3JlZiIsImNoaWxkcmVuIiwiX3JlZiRmYWxsYmFja0xvY2FsZSIsImZhbGxiYWNrTG9jYWxlIiwiX3VzZVN0YXRlIiwidXNlU3RhdGUiLCJfdXNlU3RhdGUyIiwiX3NsaWNlZFRvQXJyYXkyIiwibG9jYWxlIiwic2V0TG9jYWxlU3RhdGUiLCJfdXNlU3RhdGUzIiwiX3VzZVN0YXRlNCIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsInVzZUVmZmVjdCIsImluaXRpYWxpemUiLCJfcmVmMiIsIl9hc3luY1RvR2VuZXJhdG9yMiIsImluaXRpYWxMb2NhbGUiLCJpbml0aWFsaXplSTE4biIsImVycm9yIiwiY29uc29sZSIsImFwcGx5IiwiYXJndW1lbnRzIiwidW5zdWJzY3JpYmUiLCJvbkxvY2FsZUNoYW5nZSIsIm5ld0xvY2FsZSIsImhhbmRsZVNldExvY2FsZSIsIl9yZWYzIiwic2V0STE4bkxvY2FsZSIsIl94IiwiY29udGV4dFZhbHVlIiwidHAiLCJmb3JtYXRDdXJyZW5jeSIsImZvcm1hdERhdGUiLCJmb3JtYXRUaW1lIiwiZm9ybWF0UGhvbmVOdW1iZXIiLCJmb3JtYXRQb3N0YWxDb2RlIiwic2V0TG9jYWxlIiwiZ2V0QXZhaWxhYmxlTG9jYWxlcyIsImdldFByb3ZpbmNlcyIsImlzRnJlbmNoQ2FuYWRpYW4iLCJpc1JUTCIsImpzeCIsIlByb3ZpZGVyIiwidmFsdWUiLCJ1c2VJMThuIiwiY29udGV4dCIsInVzZUNvbnRleHQiLCJFcnJvciIsInVzZVRyYW5zbGF0aW9uIiwiX3VzZUkxOG4iLCJjb21tb24iLCJsb2FkaW5nIiwic3VjY2VzcyIsImNhbmNlbCIsImNvbmZpcm0iLCJzYXZlIiwiZWRpdCIsImRlbGV0ZSIsImJhY2siLCJuZXh0IiwiY2xvc2UiLCJvayIsInllcyIsIm5vIiwibmF2aWdhdGlvbiIsImhvbWUiLCJzZXJ2aWNlcyIsImJvb2tpbmdzIiwibWVzc2FnZXMiLCJwcm9maWxlIiwic2V0dGluZ3MiLCJoZWxwIiwiYWJvdXQiLCJhdXRoIiwic2lnbkluIiwic2lnblVwIiwic2lnbk91dCIsImVtYWlsIiwicGFzc3dvcmQiLCJmb3Jnb3RQYXNzd29yZCIsInVzZUZvcm1hdHRpbmciLCJfdXNlSTE4bjIiLCJjdXJyZW5jeSIsImRhdGUiLCJ0aW1lIiwicGhvbmUiLCJwb3N0YWxDb2RlIiwidXNlTG9jYWxlIiwiX3VzZUkxOG4zIiwiYXZhaWxhYmxlTG9jYWxlcyIsImlzRnJlbmNoIiwiaXNFbmdsaXNoIiwid2l0aEkxOG4iLCJDb21wb25lbnQiLCJJMThuQ29tcG9uZW50IiwicHJvcHMiLCJpMThuIiwiYXNzaWduIiwiTG9jYWxlU3dpdGNoIiwiX3JlZjQiLCJlbiIsImZyIiwiX3VzZUkxOG40IiwiRnJhZ21lbnQiLCJMb2NhbGl6ZWRUZXh0IiwiX3JlZjUiLCJpMThuS2V5IiwicGFyYW1zIiwiZmFsbGJhY2siLCJ0ZXh0UHJvcHMiLCJfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMyIiwiX3VzZUkxOG41IiwidGV4dCIsImRpc3BsYXlUZXh0Iiwic3RhcnRzV2l0aCIsIlRleHQiXSwic291cmNlcyI6WyJJMThuQ29udGV4dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBJbnRlcm5hdGlvbmFsaXphdGlvbiBDb250ZXh0XG4gKlxuICogUmVhY3QgY29udGV4dCBmb3IgbWFuYWdpbmcgaW50ZXJuYXRpb25hbGl6YXRpb24gc3RhdGUgYW5kIHByb3ZpZGluZ1xuICogdHJhbnNsYXRpb24gZnVuY3Rpb25zIHRocm91Z2hvdXQgdGhlIGFwcGxpY2F0aW9uLlxuICpcbiAqIEB2ZXJzaW9uIDEuMC4wXG4gKiBAYXV0aG9yIFZpZXJsYSBEZXZlbG9wbWVudCBUZWFtXG4gKi9cblxuaW1wb3J0IFJlYWN0LCB7XG4gIGNyZWF0ZUNvbnRleHQsXG4gIHVzZUNvbnRleHQsXG4gIHVzZUVmZmVjdCxcbiAgdXNlU3RhdGUsXG4gIFJlYWN0Tm9kZSxcbn0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgVGV4dCB9IGZyb20gJ3JlYWN0LW5hdGl2ZSc7XG5cbmltcG9ydCB7XG4gIFN1cHBvcnRlZExvY2FsZSxcbiAgaW5pdGlhbGl6ZUkxOG4sXG4gIHNldExvY2FsZSBhcyBzZXRJMThuTG9jYWxlLFxuICBnZXRDdXJyZW50TG9jYWxlLFxuICBvbkxvY2FsZUNoYW5nZSxcbiAgdCxcbiAgdHAsXG4gIGZvcm1hdEN1cnJlbmN5LFxuICBmb3JtYXREYXRlLFxuICBmb3JtYXRUaW1lLFxuICBmb3JtYXRQaG9uZU51bWJlcixcbiAgZm9ybWF0UG9zdGFsQ29kZSxcbiAgZ2V0UHJvdmluY2VzLFxuICBnZXRBdmFpbGFibGVMb2NhbGVzLFxuICBpc0ZyZW5jaENhbmFkaWFuLFxufSBmcm9tICcuLi91dGlscy9pMThuJztcblxuLy8gSTE4biBjb250ZXh0IGludGVyZmFjZVxuaW50ZXJmYWNlIEkxOG5Db250ZXh0VHlwZSB7XG4gIC8vIEN1cnJlbnQgbG9jYWxlIHN0YXRlXG4gIGxvY2FsZTogU3VwcG9ydGVkTG9jYWxlO1xuICBpc0xvYWRpbmc6IGJvb2xlYW47XG5cbiAgLy8gVHJhbnNsYXRpb24gZnVuY3Rpb25zXG4gIHQ6IHR5cGVvZiB0O1xuICB0cDogdHlwZW9mIHRwO1xuXG4gIC8vIEZvcm1hdHRpbmcgZnVuY3Rpb25zXG4gIGZvcm1hdEN1cnJlbmN5OiB0eXBlb2YgZm9ybWF0Q3VycmVuY3k7XG4gIGZvcm1hdERhdGU6IHR5cGVvZiBmb3JtYXREYXRlO1xuICBmb3JtYXRUaW1lOiB0eXBlb2YgZm9ybWF0VGltZTtcbiAgZm9ybWF0UGhvbmVOdW1iZXI6IHR5cGVvZiBmb3JtYXRQaG9uZU51bWJlcjtcbiAgZm9ybWF0UG9zdGFsQ29kZTogdHlwZW9mIGZvcm1hdFBvc3RhbENvZGU7XG5cbiAgLy8gTG9jYWxlIG1hbmFnZW1lbnRcbiAgc2V0TG9jYWxlOiAobG9jYWxlOiBTdXBwb3J0ZWRMb2NhbGUpID0+IFByb21pc2U8dm9pZD47XG4gIGdldEF2YWlsYWJsZUxvY2FsZXM6IHR5cGVvZiBnZXRBdmFpbGFibGVMb2NhbGVzO1xuICBnZXRQcm92aW5jZXM6IHR5cGVvZiBnZXRQcm92aW5jZXM7XG5cbiAgLy8gVXRpbGl0eSBmdW5jdGlvbnNcbiAgaXNGcmVuY2hDYW5hZGlhbjogdHlwZW9mIGlzRnJlbmNoQ2FuYWRpYW47XG4gIGlzUlRMOiBib29sZWFuOyAvLyBGb3IgZnV0dXJlIFJUTCBzdXBwb3J0XG59XG5cbi8vIENyZWF0ZSBjb250ZXh0XG5jb25zdCBJMThuQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8STE4bkNvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xuXG4vLyBQcm92aWRlciBwcm9wc1xuaW50ZXJmYWNlIEkxOG5Qcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcbiAgZmFsbGJhY2tMb2NhbGU/OiBTdXBwb3J0ZWRMb2NhbGU7XG59XG5cbi8qKlxuICogSTE4biBQcm92aWRlciBDb21wb25lbnRcbiAqL1xuZXhwb3J0IGNvbnN0IEkxOG5Qcm92aWRlcjogUmVhY3QuRkM8STE4blByb3ZpZGVyUHJvcHM+ID0gKHtcbiAgY2hpbGRyZW4sXG4gIGZhbGxiYWNrTG9jYWxlID0gJ2VuLUNBJyxcbn0pID0+IHtcbiAgY29uc3QgW2xvY2FsZSwgc2V0TG9jYWxlU3RhdGVdID0gdXNlU3RhdGU8U3VwcG9ydGVkTG9jYWxlPihmYWxsYmFja0xvY2FsZSk7XG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcblxuICAvLyBJbml0aWFsaXplIGkxOG4gc3lzdGVtXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaW5pdGlhbGl6ZSA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IGluaXRpYWxMb2NhbGUgPSBhd2FpdCBpbml0aWFsaXplSTE4bigpO1xuICAgICAgICBzZXRMb2NhbGVTdGF0ZShpbml0aWFsTG9jYWxlKTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBpbml0aWFsaXplIGkxOG46JywgZXJyb3IpO1xuICAgICAgICBzZXRMb2NhbGVTdGF0ZShmYWxsYmFja0xvY2FsZSk7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICBpbml0aWFsaXplKCk7XG4gIH0sIFtmYWxsYmFja0xvY2FsZV0pO1xuXG4gIC8vIExpc3RlbiBmb3IgbG9jYWxlIGNoYW5nZXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCB1bnN1YnNjcmliZSA9IG9uTG9jYWxlQ2hhbmdlKG5ld0xvY2FsZSA9PiB7XG4gICAgICBzZXRMb2NhbGVTdGF0ZShuZXdMb2NhbGUpO1xuICAgIH0pO1xuXG4gICAgcmV0dXJuIHVuc3Vic2NyaWJlO1xuICB9LCBbXSk7XG5cbiAgLy8gSGFuZGxlIGxvY2FsZSBjaGFuZ2VcbiAgY29uc3QgaGFuZGxlU2V0TG9jYWxlID0gYXN5bmMgKG5ld0xvY2FsZTogU3VwcG9ydGVkTG9jYWxlKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IHNldEkxOG5Mb2NhbGUobmV3TG9jYWxlKTtcbiAgICAgIC8vIFN0YXRlIHdpbGwgYmUgdXBkYXRlZCB2aWEgdGhlIGxvY2FsZSBjaGFuZ2UgbGlzdGVuZXJcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHNldCBsb2NhbGU6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICAvLyBDb250ZXh0IHZhbHVlXG4gIGNvbnN0IGNvbnRleHRWYWx1ZTogSTE4bkNvbnRleHRUeXBlID0ge1xuICAgIGxvY2FsZSxcbiAgICBpc0xvYWRpbmcsXG4gICAgdCxcbiAgICB0cCxcbiAgICBmb3JtYXRDdXJyZW5jeSxcbiAgICBmb3JtYXREYXRlLFxuICAgIGZvcm1hdFRpbWUsXG4gICAgZm9ybWF0UGhvbmVOdW1iZXIsXG4gICAgZm9ybWF0UG9zdGFsQ29kZSxcbiAgICBzZXRMb2NhbGU6IGhhbmRsZVNldExvY2FsZSxcbiAgICBnZXRBdmFpbGFibGVMb2NhbGVzLFxuICAgIGdldFByb3ZpbmNlcyxcbiAgICBpc0ZyZW5jaENhbmFkaWFuLFxuICAgIGlzUlRMOiBmYWxzZSwgLy8gRnV0dXJlIFJUTCBzdXBwb3J0XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8STE4bkNvbnRleHQuUHJvdmlkZXIgdmFsdWU9e2NvbnRleHRWYWx1ZX0+e2NoaWxkcmVufTwvSTE4bkNvbnRleHQuUHJvdmlkZXI+XG4gICk7XG59O1xuXG4vKipcbiAqIEhvb2sgdG8gdXNlIGkxOG4gY29udGV4dFxuICovXG5leHBvcnQgY29uc3QgdXNlSTE4biA9ICgpOiBJMThuQ29udGV4dFR5cGUgPT4ge1xuICBjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChJMThuQ29udGV4dCk7XG5cbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlSTE4biBtdXN0IGJlIHVzZWQgd2l0aGluIGFuIEkxOG5Qcm92aWRlcicpO1xuICB9XG5cbiAgcmV0dXJuIGNvbnRleHQ7XG59O1xuXG4vKipcbiAqIEhvb2sgZm9yIHRyYW5zbGF0aW9uIHdpdGggYXV0b21hdGljIHJlLXJlbmRlcmluZyBvbiBsb2NhbGUgY2hhbmdlXG4gKi9cbmV4cG9ydCBjb25zdCB1c2VUcmFuc2xhdGlvbiA9ICgpID0+IHtcbiAgY29uc3QgeyB0LCB0cCwgbG9jYWxlIH0gPSB1c2VJMThuKCk7XG5cbiAgcmV0dXJuIHtcbiAgICB0LFxuICAgIHRwLFxuICAgIGxvY2FsZSxcbiAgICAvLyBDb252ZW5pZW5jZSBmdW5jdGlvbiBmb3IgY29tbW9uIHRyYW5zbGF0aW9uc1xuICAgIGNvbW1vbjoge1xuICAgICAgbG9hZGluZzogKCkgPT4gdCgnY29tbW9uLmxvYWRpbmcnKSxcbiAgICAgIGVycm9yOiAoKSA9PiB0KCdjb21tb24uZXJyb3InKSxcbiAgICAgIHN1Y2Nlc3M6ICgpID0+IHQoJ2NvbW1vbi5zdWNjZXNzJyksXG4gICAgICBjYW5jZWw6ICgpID0+IHQoJ2NvbW1vbi5jYW5jZWwnKSxcbiAgICAgIGNvbmZpcm06ICgpID0+IHQoJ2NvbW1vbi5jb25maXJtJyksXG4gICAgICBzYXZlOiAoKSA9PiB0KCdjb21tb24uc2F2ZScpLFxuICAgICAgZWRpdDogKCkgPT4gdCgnY29tbW9uLmVkaXQnKSxcbiAgICAgIGRlbGV0ZTogKCkgPT4gdCgnY29tbW9uLmRlbGV0ZScpLFxuICAgICAgYmFjazogKCkgPT4gdCgnY29tbW9uLmJhY2snKSxcbiAgICAgIG5leHQ6ICgpID0+IHQoJ2NvbW1vbi5uZXh0JyksXG4gICAgICBjbG9zZTogKCkgPT4gdCgnY29tbW9uLmNsb3NlJyksXG4gICAgICBvazogKCkgPT4gdCgnY29tbW9uLm9rJyksXG4gICAgICB5ZXM6ICgpID0+IHQoJ2NvbW1vbi55ZXMnKSxcbiAgICAgIG5vOiAoKSA9PiB0KCdjb21tb24ubm8nKSxcbiAgICB9LFxuICAgIG5hdmlnYXRpb246IHtcbiAgICAgIGhvbWU6ICgpID0+IHQoJ25hdmlnYXRpb24uaG9tZScpLFxuICAgICAgc2VydmljZXM6ICgpID0+IHQoJ25hdmlnYXRpb24uc2VydmljZXMnKSxcbiAgICAgIGJvb2tpbmdzOiAoKSA9PiB0KCduYXZpZ2F0aW9uLmJvb2tpbmdzJyksXG4gICAgICBtZXNzYWdlczogKCkgPT4gdCgnbmF2aWdhdGlvbi5tZXNzYWdlcycpLFxuICAgICAgcHJvZmlsZTogKCkgPT4gdCgnbmF2aWdhdGlvbi5wcm9maWxlJyksXG4gICAgICBzZXR0aW5nczogKCkgPT4gdCgnbmF2aWdhdGlvbi5zZXR0aW5ncycpLFxuICAgICAgaGVscDogKCkgPT4gdCgnbmF2aWdhdGlvbi5oZWxwJyksXG4gICAgICBhYm91dDogKCkgPT4gdCgnbmF2aWdhdGlvbi5hYm91dCcpLFxuICAgIH0sXG4gICAgYXV0aDoge1xuICAgICAgc2lnbkluOiAoKSA9PiB0KCdhdXRoLnNpZ25JbicpLFxuICAgICAgc2lnblVwOiAoKSA9PiB0KCdhdXRoLnNpZ25VcCcpLFxuICAgICAgc2lnbk91dDogKCkgPT4gdCgnYXV0aC5zaWduT3V0JyksXG4gICAgICBlbWFpbDogKCkgPT4gdCgnYXV0aC5lbWFpbCcpLFxuICAgICAgcGFzc3dvcmQ6ICgpID0+IHQoJ2F1dGgucGFzc3dvcmQnKSxcbiAgICAgIGZvcmdvdFBhc3N3b3JkOiAoKSA9PiB0KCdhdXRoLmZvcmdvdFBhc3N3b3JkJyksXG4gICAgfSxcbiAgfTtcbn07XG5cbi8qKlxuICogSG9vayBmb3IgZm9ybWF0dGluZyBmdW5jdGlvbnNcbiAqL1xuZXhwb3J0IGNvbnN0IHVzZUZvcm1hdHRpbmcgPSAoKSA9PiB7XG4gIGNvbnN0IHtcbiAgICBmb3JtYXRDdXJyZW5jeSxcbiAgICBmb3JtYXREYXRlLFxuICAgIGZvcm1hdFRpbWUsXG4gICAgZm9ybWF0UGhvbmVOdW1iZXIsXG4gICAgZm9ybWF0UG9zdGFsQ29kZSxcbiAgICBsb2NhbGUsXG4gIH0gPSB1c2VJMThuKCk7XG5cbiAgcmV0dXJuIHtcbiAgICBjdXJyZW5jeTogZm9ybWF0Q3VycmVuY3ksXG4gICAgZGF0ZTogZm9ybWF0RGF0ZSxcbiAgICB0aW1lOiBmb3JtYXRUaW1lLFxuICAgIHBob25lOiBmb3JtYXRQaG9uZU51bWJlcixcbiAgICBwb3N0YWxDb2RlOiBmb3JtYXRQb3N0YWxDb2RlLFxuICAgIGxvY2FsZSxcbiAgfTtcbn07XG5cbi8qKlxuICogSG9vayBmb3IgbG9jYWxlIG1hbmFnZW1lbnRcbiAqL1xuZXhwb3J0IGNvbnN0IHVzZUxvY2FsZSA9ICgpID0+IHtcbiAgY29uc3QgeyBsb2NhbGUsIHNldExvY2FsZSwgZ2V0QXZhaWxhYmxlTG9jYWxlcywgaXNGcmVuY2hDYW5hZGlhbiwgaXNSVEwgfSA9XG4gICAgdXNlSTE4bigpO1xuXG4gIHJldHVybiB7XG4gICAgbG9jYWxlLFxuICAgIHNldExvY2FsZSxcbiAgICBhdmFpbGFibGVMb2NhbGVzOiBnZXRBdmFpbGFibGVMb2NhbGVzKCksXG4gICAgaXNGcmVuY2g6IGlzRnJlbmNoQ2FuYWRpYW4oKSxcbiAgICBpc0VuZ2xpc2g6IGxvY2FsZSA9PT0gJ2VuLUNBJyxcbiAgICBpc1JUTCxcbiAgfTtcbn07XG5cbi8qKlxuICogSGlnaGVyLW9yZGVyIGNvbXBvbmVudCBmb3IgaTE4blxuICovXG5leHBvcnQgZnVuY3Rpb24gd2l0aEkxOG48UCBleHRlbmRzIG9iamVjdD4oXG4gIENvbXBvbmVudDogUmVhY3QuQ29tcG9uZW50VHlwZTxQPixcbik6IFJlYWN0LkNvbXBvbmVudFR5cGU8UD4ge1xuICByZXR1cm4gZnVuY3Rpb24gSTE4bkNvbXBvbmVudChwcm9wczogUCkge1xuICAgIGNvbnN0IGkxOG4gPSB1c2VJMThuKCk7XG5cbiAgICByZXR1cm4gPENvbXBvbmVudCB7Li4ucHJvcHN9IGkxOG49e2kxOG59IC8+O1xuICB9O1xufVxuXG4vKipcbiAqIENvbXBvbmVudCBmb3IgY29uZGl0aW9uYWwgcmVuZGVyaW5nIGJhc2VkIG9uIGxvY2FsZVxuICovXG5leHBvcnQgY29uc3QgTG9jYWxlU3dpdGNoOiBSZWFjdC5GQzx7XG4gIGVuPzogUmVhY3ROb2RlO1xuICBmcj86IFJlYWN0Tm9kZTtcbiAgY2hpbGRyZW4/OiBSZWFjdE5vZGU7XG59PiA9ICh7IGVuLCBmciwgY2hpbGRyZW4gfSkgPT4ge1xuICBjb25zdCB7IGlzRnJlbmNoQ2FuYWRpYW4gfSA9IHVzZUkxOG4oKTtcblxuICBpZiAoaXNGcmVuY2hDYW5hZGlhbigpKSB7XG4gICAgcmV0dXJuIDw+e2ZyIHx8IGNoaWxkcmVufTwvPjtcbiAgfSBlbHNlIHtcbiAgICByZXR1cm4gPD57ZW4gfHwgY2hpbGRyZW59PC8+O1xuICB9XG59O1xuXG4vKipcbiAqIENvbXBvbmVudCBmb3IgZGlzcGxheWluZyBsb2NhbGl6ZWQgdGV4dFxuICovXG5leHBvcnQgY29uc3QgTG9jYWxpemVkVGV4dDogUmVhY3QuRkM8e1xuICBpMThuS2V5OiBzdHJpbmc7XG4gIHBhcmFtcz86IFJlY29yZDxzdHJpbmcsIHN0cmluZyB8IG51bWJlcj47XG4gIGZhbGxiYWNrPzogc3RyaW5nO1xuICBzdHlsZT86IGFueTtcbiAgbnVtYmVyT2ZMaW5lcz86IG51bWJlcjtcbn0+ID0gKHsgaTE4bktleSwgcGFyYW1zLCBmYWxsYmFjaywgLi4udGV4dFByb3BzIH0pID0+IHtcbiAgY29uc3QgeyB0IH0gPSB1c2VJMThuKCk7XG5cbiAgY29uc3QgdGV4dCA9IHQoaTE4bktleSwgcGFyYW1zKTtcbiAgY29uc3QgZGlzcGxheVRleHQgPVxuICAgIHRleHQuc3RhcnRzV2l0aCgnW01pc3Npbmc6JykgfHwgdGV4dC5zdGFydHNXaXRoKCdbSW52YWxpZDonKVxuICAgICAgPyBmYWxsYmFjayB8fCBpMThuS2V5XG4gICAgICA6IHRleHQ7XG5cbiAgcmV0dXJuIDxUZXh0IHsuLi50ZXh0UHJvcHN9PntkaXNwbGF5VGV4dH08L1RleHQ+O1xufTtcblxuLy8gUmUtZXhwb3J0IHR5cGVzIGFuZCB1dGlsaXRpZXNcbmV4cG9ydCB0eXBlIHsgU3VwcG9ydGVkTG9jYWxlIH07XG5leHBvcnQgeyBnZXRDdXJyZW50TG9jYWxlLCBnZXRBdmFpbGFibGVMb2NhbGVzIH07XG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFVQSxJQUFBQSxNQUFBLEdBQUFDLHVCQUFBLENBQUFDLE9BQUE7QUFPQSxJQUFBQyxZQUFBLEdBQUFELE9BQUE7QUFFQSxJQUFBRSxLQUFBLEdBQUFGLE9BQUE7QUFnQnVCLElBQUFHLFdBQUEsR0FBQUgsT0FBQTtBQUFBLElBQUFJLFNBQUE7QUFBQSxTQUFBTCx3QkFBQU0sQ0FBQSxFQUFBQyxDQUFBLDZCQUFBQyxPQUFBLE1BQUFDLENBQUEsT0FBQUQsT0FBQSxJQUFBRSxDQUFBLE9BQUFGLE9BQUEsWUFBQVIsdUJBQUEsWUFBQUEsd0JBQUFNLENBQUEsRUFBQUMsQ0FBQSxTQUFBQSxDQUFBLElBQUFELENBQUEsSUFBQUEsQ0FBQSxDQUFBSyxVQUFBLFNBQUFMLENBQUEsTUFBQU0sQ0FBQSxFQUFBQyxDQUFBLEVBQUFDLENBQUEsS0FBQUMsU0FBQSxRQUFBQyxPQUFBLEVBQUFWLENBQUEsaUJBQUFBLENBQUEsdUJBQUFBLENBQUEseUJBQUFBLENBQUEsU0FBQVEsQ0FBQSxNQUFBRixDQUFBLEdBQUFMLENBQUEsR0FBQUcsQ0FBQSxHQUFBRCxDQUFBLFFBQUFHLENBQUEsQ0FBQUssR0FBQSxDQUFBWCxDQUFBLFVBQUFNLENBQUEsQ0FBQU0sR0FBQSxDQUFBWixDQUFBLEdBQUFNLENBQUEsQ0FBQU8sR0FBQSxDQUFBYixDQUFBLEVBQUFRLENBQUEsY0FBQU0sRUFBQSxJQUFBZCxDQUFBLGdCQUFBYyxFQUFBLE9BQUFDLGNBQUEsQ0FBQUMsSUFBQSxDQUFBaEIsQ0FBQSxFQUFBYyxFQUFBLE9BQUFQLENBQUEsSUFBQUQsQ0FBQSxHQUFBVyxNQUFBLENBQUFDLGNBQUEsS0FBQUQsTUFBQSxDQUFBRSx3QkFBQSxDQUFBbkIsQ0FBQSxFQUFBYyxFQUFBLE9BQUFQLENBQUEsQ0FBQUssR0FBQSxJQUFBTCxDQUFBLENBQUFNLEdBQUEsSUFBQVAsQ0FBQSxDQUFBRSxDQUFBLEVBQUFNLEVBQUEsRUFBQVAsQ0FBQSxJQUFBQyxDQUFBLENBQUFNLEVBQUEsSUFBQWQsQ0FBQSxDQUFBYyxFQUFBLFdBQUFOLENBQUEsS0FBQVIsQ0FBQSxFQUFBQyxDQUFBO0FBOEJ2QixJQUFNbUIsV0FBVyxHQUFHLElBQUFDLG9CQUFhLEVBQThCQyxTQUFTLENBQUM7QUFXbEUsSUFBTUMsWUFBeUMsR0FBQUMsT0FBQSxDQUFBRCxZQUFBLEdBQUcsU0FBNUNBLFlBQXlDQSxDQUFBRSxJQUFBLEVBR2hEO0VBQUEsSUFGSkMsUUFBUSxHQUFBRCxJQUFBLENBQVJDLFFBQVE7SUFBQUMsbUJBQUEsR0FBQUYsSUFBQSxDQUNSRyxjQUFjO0lBQWRBLGNBQWMsR0FBQUQsbUJBQUEsY0FBRyxPQUFPLEdBQUFBLG1CQUFBO0VBRXhCLElBQUFFLFNBQUEsR0FBaUMsSUFBQUMsZUFBUSxFQUFrQkYsY0FBYyxDQUFDO0lBQUFHLFVBQUEsT0FBQUMsZUFBQSxDQUFBdEIsT0FBQSxFQUFBbUIsU0FBQTtJQUFuRUksTUFBTSxHQUFBRixVQUFBO0lBQUVHLGNBQWMsR0FBQUgsVUFBQTtFQUM3QixJQUFBSSxVQUFBLEdBQWtDLElBQUFMLGVBQVEsRUFBQyxJQUFJLENBQUM7SUFBQU0sVUFBQSxPQUFBSixlQUFBLENBQUF0QixPQUFBLEVBQUF5QixVQUFBO0lBQXpDRSxTQUFTLEdBQUFELFVBQUE7SUFBRUUsWUFBWSxHQUFBRixVQUFBO0VBRzlCLElBQUFHLGdCQUFTLEVBQUMsWUFBTTtJQUNkLElBQU1DLFVBQVU7TUFBQSxJQUFBQyxLQUFBLE9BQUFDLGtCQUFBLENBQUFoQyxPQUFBLEVBQUcsYUFBWTtRQUM3QixJQUFJO1VBQ0YsSUFBTWlDLGFBQWEsU0FBUyxJQUFBQyxvQkFBYyxFQUFDLENBQUM7VUFDNUNWLGNBQWMsQ0FBQ1MsYUFBYSxDQUFDO1FBQy9CLENBQUMsQ0FBQyxPQUFPRSxLQUFLLEVBQUU7VUFDZEMsT0FBTyxDQUFDRCxLQUFLLENBQUMsNEJBQTRCLEVBQUVBLEtBQUssQ0FBQztVQUNsRFgsY0FBYyxDQUFDTixjQUFjLENBQUM7UUFDaEMsQ0FBQyxTQUFTO1VBQ1JVLFlBQVksQ0FBQyxLQUFLLENBQUM7UUFDckI7TUFDRixDQUFDO01BQUEsZ0JBVktFLFVBQVVBLENBQUE7UUFBQSxPQUFBQyxLQUFBLENBQUFNLEtBQUEsT0FBQUMsU0FBQTtNQUFBO0lBQUEsR0FVZjtJQUVEUixVQUFVLENBQUMsQ0FBQztFQUNkLENBQUMsRUFBRSxDQUFDWixjQUFjLENBQUMsQ0FBQztFQUdwQixJQUFBVyxnQkFBUyxFQUFDLFlBQU07SUFDZCxJQUFNVSxXQUFXLEdBQUcsSUFBQUMsb0JBQWMsRUFBQyxVQUFBQyxTQUFTLEVBQUk7TUFDOUNqQixjQUFjLENBQUNpQixTQUFTLENBQUM7SUFDM0IsQ0FBQyxDQUFDO0lBRUYsT0FBT0YsV0FBVztFQUNwQixDQUFDLEVBQUUsRUFBRSxDQUFDO0VBR04sSUFBTUcsZUFBZTtJQUFBLElBQUFDLEtBQUEsT0FBQVgsa0JBQUEsQ0FBQWhDLE9BQUEsRUFBRyxXQUFPeUMsU0FBMEIsRUFBSztNQUM1RCxJQUFJO1FBQ0YsTUFBTSxJQUFBRyxlQUFhLEVBQUNILFNBQVMsQ0FBQztNQUVoQyxDQUFDLENBQUMsT0FBT04sS0FBSyxFQUFFO1FBQ2RDLE9BQU8sQ0FBQ0QsS0FBSyxDQUFDLHVCQUF1QixFQUFFQSxLQUFLLENBQUM7TUFDL0M7SUFDRixDQUFDO0lBQUEsZ0JBUEtPLGVBQWVBLENBQUFHLEVBQUE7TUFBQSxPQUFBRixLQUFBLENBQUFOLEtBQUEsT0FBQUMsU0FBQTtJQUFBO0VBQUEsR0FPcEI7RUFHRCxJQUFNUSxZQUE2QixHQUFHO0lBQ3BDdkIsTUFBTSxFQUFOQSxNQUFNO0lBQ05JLFNBQVMsRUFBVEEsU0FBUztJQUNUcEMsQ0FBQyxFQUFEQSxPQUFDO0lBQ0R3RCxFQUFFLEVBQUZBLFFBQUU7SUFDRkMsY0FBYyxFQUFkQSxvQkFBYztJQUNkQyxVQUFVLEVBQVZBLGdCQUFVO0lBQ1ZDLFVBQVUsRUFBVkEsZ0JBQVU7SUFDVkMsaUJBQWlCLEVBQWpCQSx1QkFBaUI7SUFDakJDLGdCQUFnQixFQUFoQkEsc0JBQWdCO0lBQ2hCQyxTQUFTLEVBQUVYLGVBQWU7SUFDMUJZLG1CQUFtQixFQUFuQkEseUJBQW1CO0lBQ25CQyxZQUFZLEVBQVpBLGtCQUFZO0lBQ1pDLGdCQUFnQixFQUFoQkEsc0JBQWdCO0lBQ2hCQyxLQUFLLEVBQUU7RUFDVCxDQUFDO0VBRUQsT0FDRSxJQUFBckUsV0FBQSxDQUFBc0UsR0FBQSxFQUFDaEQsV0FBVyxDQUFDaUQsUUFBUTtJQUFDQyxLQUFLLEVBQUVkLFlBQWE7SUFBQTlCLFFBQUEsRUFBRUE7RUFBUSxDQUF1QixDQUFDO0FBRWhGLENBQUM7QUFLTSxJQUFNNkMsT0FBTyxHQUFBL0MsT0FBQSxDQUFBK0MsT0FBQSxHQUFHLFNBQVZBLE9BQU9BLENBQUEsRUFBMEI7RUFDNUMsSUFBTUMsT0FBTyxHQUFHLElBQUFDLGlCQUFVLEVBQUNyRCxXQUFXLENBQUM7RUFFdkMsSUFBSW9ELE9BQU8sS0FBS2xELFNBQVMsRUFBRTtJQUN6QixNQUFNLElBQUlvRCxLQUFLLENBQUMsNkNBQTZDLENBQUM7RUFDaEU7RUFFQSxPQUFPRixPQUFPO0FBQ2hCLENBQUM7QUFLTSxJQUFNRyxjQUFjLEdBQUFuRCxPQUFBLENBQUFtRCxjQUFBLEdBQUcsU0FBakJBLGNBQWNBLENBQUEsRUFBUztFQUNsQyxJQUFBQyxRQUFBLEdBQTBCTCxPQUFPLENBQUMsQ0FBQztJQUEzQnRFLENBQUMsR0FBQTJFLFFBQUEsQ0FBRDNFLENBQUM7SUFBRXdELEVBQUUsR0FBQW1CLFFBQUEsQ0FBRm5CLEVBQUU7SUFBRXhCLE1BQU0sR0FBQTJDLFFBQUEsQ0FBTjNDLE1BQU07RUFFckIsT0FBTztJQUNMaEMsQ0FBQyxFQUFEQSxDQUFDO0lBQ0R3RCxFQUFFLEVBQUZBLEVBQUU7SUFDRnhCLE1BQU0sRUFBTkEsTUFBTTtJQUVONEMsTUFBTSxFQUFFO01BQ05DLE9BQU8sRUFBRSxTQUFUQSxPQUFPQSxDQUFBO1FBQUEsT0FBUTdFLENBQUMsQ0FBQyxnQkFBZ0IsQ0FBQztNQUFBO01BQ2xDNEMsS0FBSyxFQUFFLFNBQVBBLEtBQUtBLENBQUE7UUFBQSxPQUFRNUMsQ0FBQyxDQUFDLGNBQWMsQ0FBQztNQUFBO01BQzlCOEUsT0FBTyxFQUFFLFNBQVRBLE9BQU9BLENBQUE7UUFBQSxPQUFROUUsQ0FBQyxDQUFDLGdCQUFnQixDQUFDO01BQUE7TUFDbEMrRSxNQUFNLEVBQUUsU0FBUkEsTUFBTUEsQ0FBQTtRQUFBLE9BQVEvRSxDQUFDLENBQUMsZUFBZSxDQUFDO01BQUE7TUFDaENnRixPQUFPLEVBQUUsU0FBVEEsT0FBT0EsQ0FBQTtRQUFBLE9BQVFoRixDQUFDLENBQUMsZ0JBQWdCLENBQUM7TUFBQTtNQUNsQ2lGLElBQUksRUFBRSxTQUFOQSxJQUFJQSxDQUFBO1FBQUEsT0FBUWpGLENBQUMsQ0FBQyxhQUFhLENBQUM7TUFBQTtNQUM1QmtGLElBQUksRUFBRSxTQUFOQSxJQUFJQSxDQUFBO1FBQUEsT0FBUWxGLENBQUMsQ0FBQyxhQUFhLENBQUM7TUFBQTtNQUM1Qm1GLE1BQU0sRUFBRSxTQUFSQSxPQUFNQSxDQUFBO1FBQUEsT0FBUW5GLENBQUMsQ0FBQyxlQUFlLENBQUM7TUFBQTtNQUNoQ29GLElBQUksRUFBRSxTQUFOQSxJQUFJQSxDQUFBO1FBQUEsT0FBUXBGLENBQUMsQ0FBQyxhQUFhLENBQUM7TUFBQTtNQUM1QnFGLElBQUksRUFBRSxTQUFOQSxJQUFJQSxDQUFBO1FBQUEsT0FBUXJGLENBQUMsQ0FBQyxhQUFhLENBQUM7TUFBQTtNQUM1QnNGLEtBQUssRUFBRSxTQUFQQSxLQUFLQSxDQUFBO1FBQUEsT0FBUXRGLENBQUMsQ0FBQyxjQUFjLENBQUM7TUFBQTtNQUM5QnVGLEVBQUUsRUFBRSxTQUFKQSxFQUFFQSxDQUFBO1FBQUEsT0FBUXZGLENBQUMsQ0FBQyxXQUFXLENBQUM7TUFBQTtNQUN4QndGLEdBQUcsRUFBRSxTQUFMQSxHQUFHQSxDQUFBO1FBQUEsT0FBUXhGLENBQUMsQ0FBQyxZQUFZLENBQUM7TUFBQTtNQUMxQnlGLEVBQUUsRUFBRSxTQUFKQSxFQUFFQSxDQUFBO1FBQUEsT0FBUXpGLENBQUMsQ0FBQyxXQUFXLENBQUM7TUFBQTtJQUMxQixDQUFDO0lBQ0QwRixVQUFVLEVBQUU7TUFDVkMsSUFBSSxFQUFFLFNBQU5BLElBQUlBLENBQUE7UUFBQSxPQUFRM0YsQ0FBQyxDQUFDLGlCQUFpQixDQUFDO01BQUE7TUFDaEM0RixRQUFRLEVBQUUsU0FBVkEsUUFBUUEsQ0FBQTtRQUFBLE9BQVE1RixDQUFDLENBQUMscUJBQXFCLENBQUM7TUFBQTtNQUN4QzZGLFFBQVEsRUFBRSxTQUFWQSxRQUFRQSxDQUFBO1FBQUEsT0FBUTdGLENBQUMsQ0FBQyxxQkFBcUIsQ0FBQztNQUFBO01BQ3hDOEYsUUFBUSxFQUFFLFNBQVZBLFFBQVFBLENBQUE7UUFBQSxPQUFROUYsQ0FBQyxDQUFDLHFCQUFxQixDQUFDO01BQUE7TUFDeEMrRixPQUFPLEVBQUUsU0FBVEEsT0FBT0EsQ0FBQTtRQUFBLE9BQVEvRixDQUFDLENBQUMsb0JBQW9CLENBQUM7TUFBQTtNQUN0Q2dHLFFBQVEsRUFBRSxTQUFWQSxRQUFRQSxDQUFBO1FBQUEsT0FBUWhHLENBQUMsQ0FBQyxxQkFBcUIsQ0FBQztNQUFBO01BQ3hDaUcsSUFBSSxFQUFFLFNBQU5BLElBQUlBLENBQUE7UUFBQSxPQUFRakcsQ0FBQyxDQUFDLGlCQUFpQixDQUFDO01BQUE7TUFDaENrRyxLQUFLLEVBQUUsU0FBUEEsS0FBS0EsQ0FBQTtRQUFBLE9BQVFsRyxDQUFDLENBQUMsa0JBQWtCLENBQUM7TUFBQTtJQUNwQyxDQUFDO0lBQ0RtRyxJQUFJLEVBQUU7TUFDSkMsTUFBTSxFQUFFLFNBQVJBLE1BQU1BLENBQUE7UUFBQSxPQUFRcEcsQ0FBQyxDQUFDLGFBQWEsQ0FBQztNQUFBO01BQzlCcUcsTUFBTSxFQUFFLFNBQVJBLE1BQU1BLENBQUE7UUFBQSxPQUFRckcsQ0FBQyxDQUFDLGFBQWEsQ0FBQztNQUFBO01BQzlCc0csT0FBTyxFQUFFLFNBQVRBLE9BQU9BLENBQUE7UUFBQSxPQUFRdEcsQ0FBQyxDQUFDLGNBQWMsQ0FBQztNQUFBO01BQ2hDdUcsS0FBSyxFQUFFLFNBQVBBLEtBQUtBLENBQUE7UUFBQSxPQUFRdkcsQ0FBQyxDQUFDLFlBQVksQ0FBQztNQUFBO01BQzVCd0csUUFBUSxFQUFFLFNBQVZBLFFBQVFBLENBQUE7UUFBQSxPQUFReEcsQ0FBQyxDQUFDLGVBQWUsQ0FBQztNQUFBO01BQ2xDeUcsY0FBYyxFQUFFLFNBQWhCQSxjQUFjQSxDQUFBO1FBQUEsT0FBUXpHLENBQUMsQ0FBQyxxQkFBcUIsQ0FBQztNQUFBO0lBQ2hEO0VBQ0YsQ0FBQztBQUNILENBQUM7QUFLTSxJQUFNMEcsYUFBYSxHQUFBbkYsT0FBQSxDQUFBbUYsYUFBQSxHQUFHLFNBQWhCQSxhQUFhQSxDQUFBLEVBQVM7RUFDakMsSUFBQUMsU0FBQSxHQU9JckMsT0FBTyxDQUFDLENBQUM7SUFOWGIsY0FBYyxHQUFBa0QsU0FBQSxDQUFkbEQsY0FBYztJQUNkQyxVQUFVLEdBQUFpRCxTQUFBLENBQVZqRCxVQUFVO0lBQ1ZDLFVBQVUsR0FBQWdELFNBQUEsQ0FBVmhELFVBQVU7SUFDVkMsaUJBQWlCLEdBQUErQyxTQUFBLENBQWpCL0MsaUJBQWlCO0lBQ2pCQyxnQkFBZ0IsR0FBQThDLFNBQUEsQ0FBaEI5QyxnQkFBZ0I7SUFDaEI3QixNQUFNLEdBQUEyRSxTQUFBLENBQU4zRSxNQUFNO0VBR1IsT0FBTztJQUNMNEUsUUFBUSxFQUFFbkQsY0FBYztJQUN4Qm9ELElBQUksRUFBRW5ELFVBQVU7SUFDaEJvRCxJQUFJLEVBQUVuRCxVQUFVO0lBQ2hCb0QsS0FBSyxFQUFFbkQsaUJBQWlCO0lBQ3hCb0QsVUFBVSxFQUFFbkQsZ0JBQWdCO0lBQzVCN0IsTUFBTSxFQUFOQTtFQUNGLENBQUM7QUFDSCxDQUFDO0FBS00sSUFBTWlGLFNBQVMsR0FBQTFGLE9BQUEsQ0FBQTBGLFNBQUEsR0FBRyxTQUFaQSxTQUFTQSxDQUFBLEVBQVM7RUFDN0IsSUFBQUMsU0FBQSxHQUNFNUMsT0FBTyxDQUFDLENBQUM7SUFESHRDLE1BQU0sR0FBQWtGLFNBQUEsQ0FBTmxGLE1BQU07SUFBRThCLFNBQVMsR0FBQW9ELFNBQUEsQ0FBVHBELFNBQVM7SUFBRUMsbUJBQW1CLEdBQUFtRCxTQUFBLENBQW5CbkQsbUJBQW1CO0lBQUVFLGdCQUFnQixHQUFBaUQsU0FBQSxDQUFoQmpELGdCQUFnQjtJQUFFQyxLQUFLLEdBQUFnRCxTQUFBLENBQUxoRCxLQUFLO0VBR3ZFLE9BQU87SUFDTGxDLE1BQU0sRUFBTkEsTUFBTTtJQUNOOEIsU0FBUyxFQUFUQSxTQUFTO0lBQ1RxRCxnQkFBZ0IsRUFBRXBELG1CQUFtQixDQUFDLENBQUM7SUFDdkNxRCxRQUFRLEVBQUVuRCxnQkFBZ0IsQ0FBQyxDQUFDO0lBQzVCb0QsU0FBUyxFQUFFckYsTUFBTSxLQUFLLE9BQU87SUFDN0JrQyxLQUFLLEVBQUxBO0VBQ0YsQ0FBQztBQUNILENBQUM7QUFLTSxTQUFTb0QsUUFBUUEsQ0FDdEJDLFNBQWlDLEVBQ1Q7RUFDeEIsT0FBTyxTQUFTQyxhQUFhQSxDQUFDQyxLQUFRLEVBQUU7SUFDdEMsSUFBTUMsSUFBSSxHQUFHcEQsT0FBTyxDQUFDLENBQUM7SUFFdEIsT0FBTyxJQUFBekUsV0FBQSxDQUFBc0UsR0FBQSxFQUFDb0QsU0FBUyxFQUFBdkcsTUFBQSxDQUFBMkcsTUFBQSxLQUFLRixLQUFLO01BQUVDLElBQUksRUFBRUE7SUFBSyxFQUFFLENBQUM7RUFDN0MsQ0FBQztBQUNIO0FBS08sSUFBTUUsWUFJWCxHQUFBckcsT0FBQSxDQUFBcUcsWUFBQSxHQUFHLFNBSlFBLFlBSVhBLENBQUFDLEtBQUEsRUFBNkI7RUFBQSxJQUF2QkMsRUFBRSxHQUFBRCxLQUFBLENBQUZDLEVBQUU7SUFBRUMsRUFBRSxHQUFBRixLQUFBLENBQUZFLEVBQUU7SUFBRXRHLFFBQVEsR0FBQW9HLEtBQUEsQ0FBUnBHLFFBQVE7RUFDdEIsSUFBQXVHLFNBQUEsR0FBNkIxRCxPQUFPLENBQUMsQ0FBQztJQUE5QkwsZ0JBQWdCLEdBQUErRCxTQUFBLENBQWhCL0QsZ0JBQWdCO0VBRXhCLElBQUlBLGdCQUFnQixDQUFDLENBQUMsRUFBRTtJQUN0QixPQUFPLElBQUFwRSxXQUFBLENBQUFzRSxHQUFBLEVBQUF0RSxXQUFBLENBQUFvSSxRQUFBO01BQUF4RyxRQUFBLEVBQUdzRyxFQUFFLElBQUl0RztJQUFRLENBQUcsQ0FBQztFQUM5QixDQUFDLE1BQU07SUFDTCxPQUFPLElBQUE1QixXQUFBLENBQUFzRSxHQUFBLEVBQUF0RSxXQUFBLENBQUFvSSxRQUFBO01BQUF4RyxRQUFBLEVBQUdxRyxFQUFFLElBQUlyRztJQUFRLENBQUcsQ0FBQztFQUM5QjtBQUNGLENBQUM7QUFLTSxJQUFNeUcsYUFNWCxHQUFBM0csT0FBQSxDQUFBMkcsYUFBQSxHQUFHLFNBTlFBLGFBTVhBLENBQUFDLEtBQUEsRUFBb0Q7RUFBQSxJQUE5Q0MsT0FBTyxHQUFBRCxLQUFBLENBQVBDLE9BQU87SUFBRUMsTUFBTSxHQUFBRixLQUFBLENBQU5FLE1BQU07SUFBRUMsUUFBUSxHQUFBSCxLQUFBLENBQVJHLFFBQVE7SUFBS0MsU0FBUyxPQUFBQyx5QkFBQSxDQUFBL0gsT0FBQSxFQUFBMEgsS0FBQSxFQUFBckksU0FBQTtFQUM3QyxJQUFBMkksU0FBQSxHQUFjbkUsT0FBTyxDQUFDLENBQUM7SUFBZnRFLENBQUMsR0FBQXlJLFNBQUEsQ0FBRHpJLENBQUM7RUFFVCxJQUFNMEksSUFBSSxHQUFHMUksQ0FBQyxDQUFDb0ksT0FBTyxFQUFFQyxNQUFNLENBQUM7RUFDL0IsSUFBTU0sV0FBVyxHQUNmRCxJQUFJLENBQUNFLFVBQVUsQ0FBQyxXQUFXLENBQUMsSUFBSUYsSUFBSSxDQUFDRSxVQUFVLENBQUMsV0FBVyxDQUFDLEdBQ3hETixRQUFRLElBQUlGLE9BQU8sR0FDbkJNLElBQUk7RUFFVixPQUFPLElBQUE3SSxXQUFBLENBQUFzRSxHQUFBLEVBQUN4RSxZQUFBLENBQUFrSixJQUFJLEVBQUE3SCxNQUFBLENBQUEyRyxNQUFBLEtBQUtZLFNBQVM7SUFBQTlHLFFBQUEsRUFBR2tIO0VBQVcsRUFBTyxDQUFDO0FBQ2xELENBQUMiLCJpZ25vcmVMaXN0IjpbXX0=