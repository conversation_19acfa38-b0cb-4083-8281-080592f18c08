/**
 * Messages Screen - Customer Communication
 *
 * Component Contract:
 * - Displays customer's message conversations with providers
 * - Shows conversation list with last message and unread counts
 * - Supports navigation to individual chat screens
 * - Integrates with backend messaging API
 * - Follows responsive design and accessibility guidelines
 *
 * @version 2.0.0 - Redesigned to match frontend_v0 patterns
 * <AUTHOR> Development Team
 */

import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';

import { SafeAreaScreen } from '../components/ui/SafeAreaWrapper';
import { messagingService } from '../services/messagingService';

import { useTheme } from '../contexts/ThemeContext';
import type { CustomerStackParamList } from '../navigation/types';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
  getMinimumTouchTarget,
} from '../utils/responsiveUtils';

// Mock conversation data for now
interface MockConversation {
  id: string;
  participantName: string;
  lastMessage: string;
  timestamp: string;
  unreadCount: number;
  avatar?: string;
}

// Helper function to generate avatar colors
const getAvatarColor = (name: string): string => {
  const avatarColors = [
    '#5A7A63', // Primary sage
    '#4A6B52', // Medium sage
    '#3A5B42', // Darker sage
    '#10B981', // Success green
    '#F59E0B', // Warning orange
    '#3B82F6', // Info blue
  ];
  const index = name.charCodeAt(0) % avatarColors.length;
  return avatarColors[index];
};

// Helper function to format last message time
const formatLastMessageTime = (timestamp: string): string => {
  const now = new Date();
  const messageTime = new Date(timestamp);
  const diffInMinutes = Math.floor(
    (now.getTime() - messageTime.getTime()) / (1000 * 60),
  );

  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m`;

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours}h`;

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) return `${diffInDays}d`;

  return messageTime.toLocaleDateString();
};

type MessagesScreenNavigationProp = StackNavigationProp<CustomerStackParamList>;

export const MessagesScreen: React.FC = () => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const navigation = useNavigation<MessagesScreenNavigationProp>();

  const [conversations, setConversations] = useState<MockConversation[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  useEffect(() => {
    loadConversations();
  }, []);

  const loadConversations = async () => {
    setLoading(true);
    setError(null);
    try {
      // Load conversations from backend API
      const response = await messagingService.getConversations();

      // Transform backend data to match frontend interface
      const transformedConversations: MockConversation[] = response.conversations.map((conv: any) => ({
        id: conv.id.toString(),
        participantName: conv.participants
          .filter((p: any) => p.id !== conv.current_user_id)
          .map((p: any) => `${p.first_name} ${p.last_name}`)
          .join(', ') || 'Unknown',
        lastMessage: conv.last_message?.content || 'No messages yet',
        timestamp: conv.last_message?.created_at || conv.updated_at,
        unreadCount: conv.unread_count || 0,
      }));

      setConversations(transformedConversations);
    } catch (err) {
      console.error('Error loading conversations:', err);
      setError('Failed to load conversations. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    setError(null);
    await loadConversations();
    setRefreshing(false);
  };

  const handleConversationPress = (conversation: MockConversation) => {
    // Mark as read locally
    setConversations(prev =>
      prev.map(conv =>
        conv.id === conversation.id ? { ...conv, unreadCount: 0 } : conv,
      ),
    );

    // Navigate to conversation screen
    navigation.navigate('Conversation', {
      conversationId: conversation.id,
      participantName: conversation.participantName,
    });
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="chatbubbles-outline" size={64} color={colors.sage200} />
      <Text style={styles.emptyStateText}>No conversations yet</Text>
      <Text style={styles.emptyStateSubtext}>
        Your messages with providers will appear here
      </Text>
    </View>
  );

  const renderConversation = ({ item }: { item: MockConversation }) => {
    const hasUnread = item.unreadCount > 0;
    const isOnline = Math.random() > 0.5; // Mock online status

    return (
      <TouchableOpacity
        onPress={() => handleConversationPress(item)}
        testID={`conversation-${item.id}`}
        accessibilityLabel={`Conversation with ${item.participantName}`}
        accessibilityHint="Tap to open chat"
        style={[styles.conversationItem, hasUnread && styles.unreadConversation]}
        activeOpacity={0.7}>
        <View style={styles.conversationContent}>
          {/* Avatar */}
          <View style={styles.avatarContainer}>
            <View style={[styles.avatar, { backgroundColor: getAvatarColor(item.participantName) }]}>
              <Text style={styles.avatarText}>
                {item.participantName.charAt(0).toUpperCase()}
              </Text>
            </View>
            {/* Online indicator */}
            {isOnline && <View style={styles.onlineIndicator} />}
          </View>

          {/* Conversation Info */}
          <View style={styles.conversationInfo}>
            <View style={styles.conversationHeader}>
              <Text
                style={[
                  styles.participantName,
                  hasUnread && styles.unreadText,
                ]}
                numberOfLines={1}>
                {item.participantName}
              </Text>
              <Text style={styles.timestamp}>
                {formatLastMessageTime(item.timestamp)}
              </Text>
            </View>

            <View style={styles.lastMessageContainer}>
              <Text
                style={[styles.lastMessage, hasUnread && styles.unreadText]}
                numberOfLines={2}>
                {item.lastMessage}
              </Text>
              {hasUnread && (
                <View style={styles.unreadBadge}>
                  <Text style={styles.unreadCount}>
                    {item.unreadCount > 99 ? '99+' : item.unreadCount}
                  </Text>
                </View>
              )}
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaScreen
      backgroundColor={colors.background.primary}
      statusBarStyle="dark-content"
      respectNotch={true}
      respectGestures={true}
      testID="messages-screen">
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Messages</Text>
        <TouchableOpacity
          style={styles.newMessageButton}
          testID="new-message-button"
          accessibilityLabel="Start new conversation"
          accessibilityRole="button">
          <Ionicons name="create-outline" size={24} color={colors.sage400} />
        </TouchableOpacity>
      </View>

      {/* Conversations List */}
      {loading && conversations.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.sage400} />
          <Text style={styles.loadingText}>Loading conversations...</Text>
        </View>
      ) : (
        <FlatList
          data={conversations}
          renderItem={renderConversation}
          keyExtractor={item => item.id}
          style={styles.conversationsList}
          contentContainerStyle={styles.conversationsContent}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[colors.sage400]}
              tintColor={colors.sage400}
            />
          }
          ListEmptyComponent={renderEmptyState}
        />
      )}

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            Error loading conversations. Please try again.
          </Text>
        </View>
      )}
    </SafeAreaScreen>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: getResponsiveSpacing(20),
      paddingVertical: getResponsiveSpacing(16),
      backgroundColor: colors.background.primary,
      borderBottomWidth: 1,
      borderBottomColor: colors.sage100,
    },
    title: {
      fontSize: getResponsiveFontSize(24),
      fontWeight: '700',
      color: colors.text.primary,
    },
    newMessageButton: {
      padding: getResponsiveSpacing(8),
      borderRadius: getResponsiveSpacing(8),
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: getResponsiveSpacing(40),
    },
    loadingText: {
      marginTop: getResponsiveSpacing(12),
      fontSize: getResponsiveFontSize(16),
      color: colors.text.secondary,
    },
    conversationsList: {
      flex: 1,
    },
    conversationsContent: {
      paddingVertical: getResponsiveSpacing(8),
    },
    conversationItem: {
      backgroundColor: colors.background.primary,
      paddingHorizontal: getResponsiveSpacing(20),
      paddingVertical: getResponsiveSpacing(16),
      borderBottomWidth: 1,
      borderBottomColor: colors.sage50,
    },
    unreadConversation: {
      backgroundColor: colors.sage25,
    },
    conversationContent: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    avatarContainer: {
      position: 'relative',
      marginRight: getResponsiveSpacing(12),
    },
    avatar: {
      width: getMinimumTouchTarget(48),
      height: getMinimumTouchTarget(48),
      borderRadius: getMinimumTouchTarget(24),
      justifyContent: 'center',
      alignItems: 'center',
    },
    avatarText: {
      fontSize: getResponsiveFontSize(18),
      fontWeight: '600',
      color: '#FFFFFF',
    },
    onlineIndicator: {
      position: 'absolute',
      bottom: 2,
      right: 2,
      width: 12,
      height: 12,
      borderRadius: 6,
      backgroundColor: '#10B981',
      borderWidth: 2,
      borderColor: colors.background.primary,
    },
    conversationInfo: {
      flex: 1,
    },
    conversationHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: getResponsiveSpacing(4),
    },
    participantName: {
      fontSize: getResponsiveFontSize(16),
      fontWeight: '600',
      color: colors.text.primary,
      flex: 1,
      marginRight: getResponsiveSpacing(8),
    },
    timestamp: {
      fontSize: getResponsiveFontSize(12),
      color: colors.text.tertiary,
    },
    lastMessageContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    lastMessage: {
      fontSize: getResponsiveFontSize(14),
      color: colors.text.secondary,
      flex: 1,
      marginRight: getResponsiveSpacing(8),
    },
    unreadText: {
      fontWeight: '600',
      color: colors.text.primary,
    },
    unreadBadge: {
      backgroundColor: colors.sage400,
      borderRadius: 10,
      minWidth: 20,
      height: 20,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 6,
    },
    unreadCount: {
      fontSize: getResponsiveFontSize(12),
      fontWeight: '600',
      color: '#FFFFFF',
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: getResponsiveSpacing(40),
      paddingVertical: getResponsiveSpacing(60),
    },
    emptyStateText: {
      fontSize: getResponsiveFontSize(18),
      fontWeight: '600',
      color: colors.text.primary,
      marginTop: getResponsiveSpacing(16),
      marginBottom: getResponsiveSpacing(8),
    },
    emptyStateSubtext: {
      fontSize: getResponsiveFontSize(14),
      color: colors.text.secondary,
      textAlign: 'center',
      lineHeight: 20,
    },
    errorContainer: {
      padding: getResponsiveSpacing(20),
      alignItems: 'center',
    },
    errorText: {
      fontSize: getResponsiveFontSize(16),
      color: colors.error,
      textAlign: 'center',
    },
  });
