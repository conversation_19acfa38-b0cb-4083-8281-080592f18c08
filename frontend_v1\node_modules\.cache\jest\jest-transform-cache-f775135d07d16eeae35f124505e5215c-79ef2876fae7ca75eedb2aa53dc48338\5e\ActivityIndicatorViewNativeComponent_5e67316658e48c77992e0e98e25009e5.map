{"version": 3, "names": ["_codegenNativeComponent", "_interopRequireDefault", "require", "NativeComponentRegistry", "nativeComponentName", "__INTERNAL_VIEW_CONFIG", "exports", "uiViewClassName", "validAttributes", "hidesWhenStopped", "animating", "color", "process", "default", "size", "_default", "get"], "sources": ["ActivityIndicatorViewNativeComponent.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\nimport type {ViewProps} from '../../../../Libraries/Components/View/ViewPropTypes';\nimport type {ColorValue} from '../../../../Libraries/StyleSheet/StyleSheet';\nimport type {WithDefault} from '../../../../Libraries/Types/CodegenTypes';\nimport type {HostComponent} from '../../types/HostComponent';\n\nimport codegenNativeComponent from '../../../../Libraries/Utilities/codegenNativeComponent';\n\ntype NativeProps = $ReadOnly<{\n  ...ViewProps,\n\n  /**\n   * Whether the indicator should hide when not animating (true by default).\n   *\n   * See https://reactnative.dev/docs/activityindicator#hideswhenstopped-ios\n   */\n  hidesWhenStopped?: WithDefault<boolean, true>,\n\n  /**\n   * Whether to show the indicator (true, the default) or hide it (false).\n   *\n   * See https://reactnative.dev/docs/activityindicator#animating\n   */\n  animating?: WithDefault<boolean, true>,\n\n  /**\n   * The foreground color of the spinner (default is gray).\n   *\n   * See https://reactnative.dev/docs/activityindicator#color\n   */\n  color?: ?ColorValue,\n\n  /**\n   * Size of the indicator (default is 'small').\n   * Passing a number to the size prop is only supported on Android.\n   *\n   * See https://reactnative.dev/docs/activityindicator#size\n   */\n  size?: WithDefault<'small' | 'large', 'small'>,\n}>;\n\nexport default (codegenNativeComponent<NativeProps>('ActivityIndicatorView', {\n  paperComponentName: 'RCTActivityIndicatorView',\n}): HostComponent<NativeProps>);\n"], "mappings": ";;;;;AAeA,IAAAA,uBAAA,GAAAC,sBAAA,CAAAC,OAAA;AAmCA,IAAAC,uBAEgC,GAFhCD,OAEgC,CAFhC,gEAE+B,CAAC;AAFhC,IAAAE,mBAEgC,GAFhC,0BAEgC;AAFhC,IAAAC,sBAEgC,GAAAC,OAAA,CAAAD,sBAAA,GAFhC;EAAAE,eAEgC,EAFhC,0BAEgC;EAFhCC,eAEgC,EAFhC;IAAAC,gBAEgC,EAFhC,IAEgC;IAFhCC,SAEgC,EAFhC,IAEgC;IAFhCC,KAEgC,EAFhC;MAAAC,OAEgC,EAFhCV,OAEgC,CAFhC,gDAE+B,CAAC,CAFhCW;IAE+B,CAAC;IAFhCC,IAEgC,EAFhC;EAE+B;AAAA,CAAC;AAAA,IAAAC,QAAA,GAAAT,OAAA,CAAAO,OAAA,GAFhCV,uBAEgC,CAFhCa,GAEgC,CAFhCZ,mBAEgC,EAFhC;EAAA,OAAAC,sBAEgC;AAAA,CAAD,CAAC", "ignoreList": []}