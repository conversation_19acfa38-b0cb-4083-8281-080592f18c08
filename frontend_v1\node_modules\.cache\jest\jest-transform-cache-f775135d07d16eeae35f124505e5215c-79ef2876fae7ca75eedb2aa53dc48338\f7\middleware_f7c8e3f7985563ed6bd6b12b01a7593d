7a1ab811803fdb311b04e71cf96e6ea8
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _excluded = ["enabled", "anonymousActionType", "store"],
  _excluded2 = ["connection"];
var reduxImpl = function reduxImpl(reducer, initial) {
  return function (set, _get, api) {
    api.dispatch = function (action) {
      set(function (state) {
        return reducer(state, action);
      }, false, action);
      return action;
    };
    api.dispatchFromDevtools = true;
    return Object.assign({
      dispatch: function dispatch() {
        return api.dispatch.apply(api, arguments);
      }
    }, initial);
  };
};
var redux = reduxImpl;
var trackedConnections = new Map();
var getTrackedConnectionState = function getTrackedConnectionState(name) {
  var api = trackedConnections.get(name);
  if (!api) return {};
  return Object.fromEntries(Object.entries(api.stores).map(function (_ref) {
    var _ref2 = (0, _slicedToArray2.default)(_ref, 2),
      key = _ref2[0],
      api2 = _ref2[1];
    return [key, api2.getState()];
  }));
};
var extractConnectionInformation = function extractConnectionInformation(store, extensionConnector, options) {
  if (store === void 0) {
    return {
      type: "untracked",
      connection: extensionConnector.connect(options)
    };
  }
  var existingConnection = trackedConnections.get(options.name);
  if (existingConnection) {
    return Object.assign({
      type: "tracked",
      store: store
    }, existingConnection);
  }
  var newConnection = {
    connection: extensionConnector.connect(options),
    stores: {}
  };
  trackedConnections.set(options.name, newConnection);
  return Object.assign({
    type: "tracked",
    store: store
  }, newConnection);
};
var removeStoreFromTrackedConnections = function removeStoreFromTrackedConnections(name, store) {
  if (store === void 0) return;
  var connectionInfo = trackedConnections.get(name);
  if (!connectionInfo) return;
  delete connectionInfo.stores[store];
  if (Object.keys(connectionInfo.stores).length === 0) {
    trackedConnections.delete(name);
  }
};
var findCallerName = function findCallerName(stack) {
  var _a, _b;
  if (!stack) return void 0;
  var traceLines = stack.split("\n");
  var apiSetStateLineIndex = traceLines.findIndex(function (traceLine) {
    return traceLine.includes("api.setState");
  });
  if (apiSetStateLineIndex < 0) return void 0;
  var callerLine = ((_a = traceLines[apiSetStateLineIndex + 1]) == null ? void 0 : _a.trim()) || "";
  return (_b = /.+ (.+) .+/.exec(callerLine)) == null ? void 0 : _b[1];
};
var devtoolsImpl = function devtoolsImpl(fn) {
  var devtoolsOptions = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return function (set, get, api) {
    var enabled = devtoolsOptions.enabled,
      anonymousActionType = devtoolsOptions.anonymousActionType,
      store = devtoolsOptions.store,
      options = (0, _objectWithoutProperties2.default)(devtoolsOptions, _excluded);
    var extensionConnector;
    try {
      extensionConnector = (enabled != null ? enabled : process.env.NODE_ENV !== "production") && window.__REDUX_DEVTOOLS_EXTENSION__;
    } catch (e) {}
    if (!extensionConnector) {
      return fn(set, get, api);
    }
    var _extractConnectionInf = extractConnectionInformation(store, extensionConnector, options),
      connection = _extractConnectionInf.connection,
      connectionInformation = (0, _objectWithoutProperties2.default)(_extractConnectionInf, _excluded2);
    var isRecording = true;
    api.setState = function (state, replace, nameOrAction) {
      var r = set(state, replace);
      if (!isRecording) return r;
      var action = nameOrAction === void 0 ? {
        type: anonymousActionType || findCallerName(new Error().stack) || "anonymous"
      } : typeof nameOrAction === "string" ? {
        type: nameOrAction
      } : nameOrAction;
      if (store === void 0) {
        connection == null ? void 0 : connection.send(action, get());
        return r;
      }
      connection == null ? void 0 : connection.send(Object.assign({}, action, {
        type: `${store}/${action.type}`
      }), Object.assign({}, getTrackedConnectionState(options.name), (0, _defineProperty2.default)({}, store, api.getState())));
      return r;
    };
    api.devtools = {
      cleanup: function cleanup() {
        if (connection && typeof connection.unsubscribe === "function") {
          connection.unsubscribe();
        }
        removeStoreFromTrackedConnections(options.name, store);
      }
    };
    var setStateFromDevtools = function setStateFromDevtools() {
      var originalIsRecording = isRecording;
      isRecording = false;
      set.apply(void 0, arguments);
      isRecording = originalIsRecording;
    };
    var initialState = fn(api.setState, get, api);
    if (connectionInformation.type === "untracked") {
      connection == null ? void 0 : connection.init(initialState);
    } else {
      connectionInformation.stores[connectionInformation.store] = api;
      connection == null ? void 0 : connection.init(Object.fromEntries(Object.entries(connectionInformation.stores).map(function (_ref3) {
        var _ref4 = (0, _slicedToArray2.default)(_ref3, 2),
          key = _ref4[0],
          store2 = _ref4[1];
        return [key, key === connectionInformation.store ? initialState : store2.getState()];
      })));
    }
    if (api.dispatchFromDevtools && typeof api.dispatch === "function") {
      var didWarnAboutReservedActionType = false;
      var originalDispatch = api.dispatch;
      api.dispatch = function () {
        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
          args[_key] = arguments[_key];
        }
        if (process.env.NODE_ENV !== "production" && args[0].type === "__setState" && !didWarnAboutReservedActionType) {
          console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.');
          didWarnAboutReservedActionType = true;
        }
        originalDispatch.apply(void 0, args);
      };
    }
    connection.subscribe(function (message) {
      var _a;
      switch (message.type) {
        case "ACTION":
          if (typeof message.payload !== "string") {
            console.error("[zustand devtools middleware] Unsupported action format");
            return;
          }
          return parseJsonThen(message.payload, function (action) {
            if (action.type === "__setState") {
              if (store === void 0) {
                setStateFromDevtools(action.state);
                return;
              }
              if (Object.keys(action.state).length !== 1) {
                console.error(`
                    [zustand devtools middleware] Unsupported __setState action format.
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);
              }
              var stateFromDevtools = action.state[store];
              if (stateFromDevtools === void 0 || stateFromDevtools === null) {
                return;
              }
              if (JSON.stringify(api.getState()) !== JSON.stringify(stateFromDevtools)) {
                setStateFromDevtools(stateFromDevtools);
              }
              return;
            }
            if (!api.dispatchFromDevtools) return;
            if (typeof api.dispatch !== "function") return;
            api.dispatch(action);
          });
        case "DISPATCH":
          switch (message.payload.type) {
            case "RESET":
              setStateFromDevtools(initialState);
              if (store === void 0) {
                return connection == null ? void 0 : connection.init(api.getState());
              }
              return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));
            case "COMMIT":
              if (store === void 0) {
                connection == null ? void 0 : connection.init(api.getState());
                return;
              }
              return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));
            case "ROLLBACK":
              return parseJsonThen(message.state, function (state) {
                if (store === void 0) {
                  setStateFromDevtools(state);
                  connection == null ? void 0 : connection.init(api.getState());
                  return;
                }
                setStateFromDevtools(state[store]);
                connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));
              });
            case "JUMP_TO_STATE":
            case "JUMP_TO_ACTION":
              return parseJsonThen(message.state, function (state) {
                if (store === void 0) {
                  setStateFromDevtools(state);
                  return;
                }
                if (JSON.stringify(api.getState()) !== JSON.stringify(state[store])) {
                  setStateFromDevtools(state[store]);
                }
              });
            case "IMPORT_STATE":
              {
                var nextLiftedState = message.payload.nextLiftedState;
                var lastComputedState = (_a = nextLiftedState.computedStates.slice(-1)[0]) == null ? void 0 : _a.state;
                if (!lastComputedState) return;
                if (store === void 0) {
                  setStateFromDevtools(lastComputedState);
                } else {
                  setStateFromDevtools(lastComputedState[store]);
                }
                connection == null ? void 0 : connection.send(null, nextLiftedState);
                return;
              }
            case "PAUSE_RECORDING":
              return isRecording = !isRecording;
          }
          return;
      }
    });
    return initialState;
  };
};
var devtools = devtoolsImpl;
var parseJsonThen = function parseJsonThen(stringified, fn) {
  var parsed;
  try {
    parsed = JSON.parse(stringified);
  } catch (e) {
    console.error("[zustand devtools middleware] Could not parse the received json", e);
  }
  if (parsed !== void 0) fn(parsed);
};
var subscribeWithSelectorImpl = function subscribeWithSelectorImpl(fn) {
  return function (set, get, api) {
    var origSubscribe = api.subscribe;
    api.subscribe = function (selector, optListener, options) {
      var listener = selector;
      if (optListener) {
        var equalityFn = (options == null ? void 0 : options.equalityFn) || Object.is;
        var currentSlice = selector(api.getState());
        listener = function listener(state) {
          var nextSlice = selector(state);
          if (!equalityFn(currentSlice, nextSlice)) {
            var previousSlice = currentSlice;
            optListener(currentSlice = nextSlice, previousSlice);
          }
        };
        if (options == null ? void 0 : options.fireImmediately) {
          optListener(currentSlice, currentSlice);
        }
      }
      return origSubscribe(listener);
    };
    var initialState = fn(set, get, api);
    return initialState;
  };
};
var subscribeWithSelector = subscribeWithSelectorImpl;
function combine(initialState, create) {
  return function () {
    return Object.assign({}, initialState, create.apply(void 0, arguments));
  };
}
function createJSONStorage(getStorage, options) {
  var storage;
  try {
    storage = getStorage();
  } catch (e) {
    return;
  }
  var persistStorage = {
    getItem: function getItem(name) {
      var _a;
      var parse = function parse(str2) {
        if (str2 === null) {
          return null;
        }
        return JSON.parse(str2, options == null ? void 0 : options.reviver);
      };
      var str = (_a = storage.getItem(name)) != null ? _a : null;
      if (str instanceof Promise) {
        return str.then(parse);
      }
      return parse(str);
    },
    setItem: function setItem(name, newValue) {
      return storage.setItem(name, JSON.stringify(newValue, options == null ? void 0 : options.replacer));
    },
    removeItem: function removeItem(name) {
      return storage.removeItem(name);
    }
  };
  return persistStorage;
}
var _toThenable = function toThenable(fn) {
  return function (input) {
    try {
      var result = fn(input);
      if (result instanceof Promise) {
        return result;
      }
      return {
        then: function then(onFulfilled) {
          return _toThenable(onFulfilled)(result);
        },
        catch: function _catch(_onRejected) {
          return this;
        }
      };
    } catch (e) {
      return {
        then: function then(_onFulfilled) {
          return this;
        },
        catch: function _catch(onRejected) {
          return _toThenable(onRejected)(e);
        }
      };
    }
  };
};
var persistImpl = function persistImpl(config, baseOptions) {
  return function (set, get, api) {
    var options = Object.assign({
      storage: createJSONStorage(function () {
        return localStorage;
      }),
      partialize: function partialize(state) {
        return state;
      },
      version: 0,
      merge: function merge(persistedState, currentState) {
        return Object.assign({}, currentState, persistedState);
      }
    }, baseOptions);
    var _hasHydrated = false;
    var hydrationListeners = new Set();
    var finishHydrationListeners = new Set();
    var storage = options.storage;
    if (!storage) {
      return config(function () {
        console.warn(`[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`);
        set.apply(void 0, arguments);
      }, get, api);
    }
    var setItem = function setItem() {
      var state = options.partialize(Object.assign({}, get()));
      return storage.setItem(options.name, {
        state: state,
        version: options.version
      });
    };
    var savedSetState = api.setState;
    api.setState = function (state, replace) {
      savedSetState(state, replace);
      void setItem();
    };
    var configResult = config(function () {
      set.apply(void 0, arguments);
      void setItem();
    }, get, api);
    api.getInitialState = function () {
      return configResult;
    };
    var stateFromStorage;
    var hydrate = function hydrate() {
      var _a, _b;
      if (!storage) return;
      _hasHydrated = false;
      hydrationListeners.forEach(function (cb) {
        var _a2;
        return cb((_a2 = get()) != null ? _a2 : configResult);
      });
      var postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? void 0 : _b.call(options, (_a = get()) != null ? _a : configResult)) || void 0;
      return _toThenable(storage.getItem.bind(storage))(options.name).then(function (deserializedStorageValue) {
        if (deserializedStorageValue) {
          if (typeof deserializedStorageValue.version === "number" && deserializedStorageValue.version !== options.version) {
            if (options.migrate) {
              var migration = options.migrate(deserializedStorageValue.state, deserializedStorageValue.version);
              if (migration instanceof Promise) {
                return migration.then(function (result) {
                  return [true, result];
                });
              }
              return [true, migration];
            }
            console.error(`State loaded from storage couldn't be migrated since no migrate function was provided`);
          } else {
            return [false, deserializedStorageValue.state];
          }
        }
        return [false, void 0];
      }).then(function (migrationResult) {
        var _a2;
        var _migrationResult = (0, _slicedToArray2.default)(migrationResult, 2),
          migrated = _migrationResult[0],
          migratedState = _migrationResult[1];
        stateFromStorage = options.merge(migratedState, (_a2 = get()) != null ? _a2 : configResult);
        set(stateFromStorage, true);
        if (migrated) {
          return setItem();
        }
      }).then(function () {
        postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);
        stateFromStorage = get();
        _hasHydrated = true;
        finishHydrationListeners.forEach(function (cb) {
          return cb(stateFromStorage);
        });
      }).catch(function (e) {
        postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);
      });
    };
    api.persist = {
      setOptions: function setOptions(newOptions) {
        options = Object.assign({}, options, newOptions);
        if (newOptions.storage) {
          storage = newOptions.storage;
        }
      },
      clearStorage: function clearStorage() {
        storage == null ? void 0 : storage.removeItem(options.name);
      },
      getOptions: function getOptions() {
        return options;
      },
      rehydrate: function rehydrate() {
        return hydrate();
      },
      hasHydrated: function hasHydrated() {
        return _hasHydrated;
      },
      onHydrate: function onHydrate(cb) {
        hydrationListeners.add(cb);
        return function () {
          hydrationListeners.delete(cb);
        };
      },
      onFinishHydration: function onFinishHydration(cb) {
        finishHydrationListeners.add(cb);
        return function () {
          finishHydrationListeners.delete(cb);
        };
      }
    };
    if (!options.skipHydration) {
      hydrate();
    }
    return stateFromStorage || configResult;
  };
};
var persist = persistImpl;
exports.combine = combine;
exports.createJSONStorage = createJSONStorage;
exports.devtools = devtools;
exports.persist = persist;
exports.redux = redux;
exports.subscribeWithSelector = subscribeWithSelector;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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