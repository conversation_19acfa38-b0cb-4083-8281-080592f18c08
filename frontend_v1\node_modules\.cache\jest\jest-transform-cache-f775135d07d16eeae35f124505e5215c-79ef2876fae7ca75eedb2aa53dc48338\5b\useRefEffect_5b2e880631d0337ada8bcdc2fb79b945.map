{"version": 3, "names": ["_react", "require", "useRefEffect", "effect", "cleanupRef", "useRef", "undefined", "useCallback", "instance", "current"], "sources": ["useRefEffect.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\nimport {useCallback, useRef} from 'react';\n\ntype CallbackRef<T> = T => mixed;\n\n/**\n * Constructs a callback ref that provides similar semantics as `useEffect`. The\n * supplied `effect` callback will be called with non-null component instances.\n * The `effect` callback can also optionally return a cleanup function.\n *\n * When a component is updated or unmounted, the cleanup function is called. The\n * `effect` callback will then be called again, if applicable.\n *\n * When a new `effect` callback is supplied, the previously returned cleanup\n * function will be called before the new `effect` callback is called with the\n * same instance.\n *\n * WARNING: The `effect` callback should be stable (e.g. using `useCallback`).\n */\nexport default function useRefEffect<TInstance>(\n  effect: TInstance => (() => void) | void,\n): CallbackRef<TInstance | null> {\n  const cleanupRef = useRef<(() => void) | void>(undefined);\n  return useCallback(\n    (instance: null | TInstance) => {\n      if (cleanupRef.current) {\n        cleanupRef.current();\n        cleanupRef.current = undefined;\n      }\n      if (instance != null) {\n        cleanupRef.current = effect(instance);\n      }\n    },\n    [effect],\n  );\n}\n"], "mappings": ";;;;AAUA,IAAAA,MAAA,GAAAC,OAAA;AAkBe,SAASC,YAAYA,CAClCC,MAAwC,EACT;EAC/B,IAAMC,UAAU,GAAG,IAAAC,aAAM,EAAsBC,SAAS,CAAC;EACzD,OAAO,IAAAC,kBAAW,EAChB,UAACC,QAA0B,EAAK;IAC9B,IAAIJ,UAAU,CAACK,OAAO,EAAE;MACtBL,UAAU,CAACK,OAAO,CAAC,CAAC;MACpBL,UAAU,CAACK,OAAO,GAAGH,SAAS;IAChC;IACA,IAAIE,QAAQ,IAAI,IAAI,EAAE;MACpBJ,UAAU,CAACK,OAAO,GAAGN,MAAM,CAACK,QAAQ,CAAC;IACvC;EACF,CAAC,EACD,CAACL,MAAM,CACT,CAAC;AACH", "ignoreList": []}