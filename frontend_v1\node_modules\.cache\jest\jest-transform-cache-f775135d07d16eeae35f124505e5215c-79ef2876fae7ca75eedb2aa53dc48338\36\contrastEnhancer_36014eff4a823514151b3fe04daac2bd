0df584494794d2048acdff951c0cde25
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.validateInteractiveColors = exports.testColorCombination = exports.getBestTextColor = exports.generateEnhancedCTAColors = exports.generateContrastReport = exports.ensureMinimumContrast = exports.enhancePrimaryCTAContrast = exports.default = exports.applyEnhancedColors = exports.EnhancedCTAColors = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _accessibilityUtils = require("./accessibilityUtils");
var enhancePrimaryCTAContrast = exports.enhancePrimaryCTAContrast = function enhancePrimaryCTAContrast(backgroundColor) {
  var textColor = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '#FFFFFF';
  var targetRatio = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 4.5;
  var currentRatio = _accessibilityUtils.ColorContrastUtils.getContrastRatio(textColor, backgroundColor);
  var result = {
    ratio: Math.round(currentRatio * 100) / 100,
    isCompliant: currentRatio >= targetRatio,
    isOptimal: currentRatio >= 7.0,
    recommendation: ''
  };
  if (currentRatio >= 7.0) {
    result.recommendation = `Excellent contrast (${result.ratio}:1) - Exceeds WCAG AAA standards`;
    return result;
  }
  if (currentRatio >= targetRatio) {
    result.recommendation = `Good contrast (${result.ratio}:1) - Meets WCAG AA standards`;
    return result;
  }
  var enhancedColor = _accessibilityUtils.ColorContrastUtils.enhanceColorContrast(backgroundColor, textColor, targetRatio);
  var enhancedRatio = _accessibilityUtils.ColorContrastUtils.getContrastRatio(textColor, enhancedColor);
  result.enhancedColor = enhancedColor;
  result.recommendation = `Enhanced from ${result.ratio}:1 to ${Math.round(enhancedRatio * 100) / 100}:1 for WCAG AA compliance`;
  return result;
};
var validateInteractiveColors = exports.validateInteractiveColors = function validateInteractiveColors() {
  var colors = {
    primary: '#5A7A63',
    primaryHover: '#4A6B52',
    primaryPressed: '#3A5B42',
    secondary: '#E1EDE4',
    destructive: '#DC2626'
  };
  var results = {};
  Object.entries(colors).forEach(function (_ref) {
    var _ref2 = (0, _slicedToArray2.default)(_ref, 2),
      key = _ref2[0],
      color = _ref2[1];
    var textColor = key === 'secondary' ? '#1F2937' : '#FFFFFF';
    results[key] = enhancePrimaryCTAContrast(color, textColor);
  });
  return results;
};
var generateEnhancedCTAColors = exports.generateEnhancedCTAColors = function generateEnhancedCTAColors() {
  var originalColors = {
    primary: '#5A7A63',
    primaryHover: '#4A6B52',
    primaryPressed: '#3A5B42'
  };
  var enhancedColors = {};
  Object.entries(originalColors).forEach(function (_ref3) {
    var _ref4 = (0, _slicedToArray2.default)(_ref3, 2),
      key = _ref4[0],
      color = _ref4[1];
    var validation = enhancePrimaryCTAContrast(color);
    enhancedColors[key] = validation.enhancedColor || color;
  });
  return enhancedColors;
};
var testColorCombination = exports.testColorCombination = function testColorCombination(foreground, background) {
  var context = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'General';
  var validation = enhancePrimaryCTAContrast(background, foreground);
  return {
    context: context,
    foreground: foreground,
    background: background,
    validation: validation
  };
};
var generateContrastReport = exports.generateContrastReport = function generateContrastReport() {
  var testCases = [{
    fg: '#FFFFFF',
    bg: '#5A7A63',
    context: 'Primary CTA Button'
  }, {
    fg: '#FFFFFF',
    bg: '#4A6B52',
    context: 'Primary CTA Hover'
  }, {
    fg: '#FFFFFF',
    bg: '#3A5B42',
    context: 'Primary CTA Pressed'
  }, {
    fg: '#1F2937',
    bg: '#E1EDE4',
    context: 'Secondary Button'
  }, {
    fg: '#FFFFFF',
    bg: '#DC2626',
    context: 'Destructive Button'
  }, {
    fg: '#1F2937',
    bg: '#FFFFFF',
    context: 'Primary Text'
  }, {
    fg: '#6B7280',
    bg: '#FFFFFF',
    context: 'Secondary Text'
  }, {
    fg: '#9CA3AF',
    bg: '#FFFFFF',
    context: 'Tertiary Text'
  }];
  var results = testCases.map(function (testCase) {
    return testColorCombination(testCase.fg, testCase.bg, testCase.context);
  });
  var compliantCount = results.filter(function (r) {
    return r.validation.isCompliant;
  }).length;
  var optimalCount = results.filter(function (r) {
    return r.validation.isOptimal;
  }).length;
  return {
    totalTests: results.length,
    compliantCount: compliantCount,
    optimalCount: optimalCount,
    complianceRate: Math.round(compliantCount / results.length * 100),
    results: results,
    summary: {
      wcagAA: `${compliantCount}/${results.length} combinations meet WCAG AA standards`,
      wcagAAA: `${optimalCount}/${results.length} combinations meet WCAG AAA standards`,
      overallStatus: compliantCount === results.length ? 'FULLY_COMPLIANT' : 'NEEDS_IMPROVEMENT'
    }
  };
};
var applyEnhancedColors = exports.applyEnhancedColors = function applyEnhancedColors() {
  var enhancedColors = generateEnhancedCTAColors();
  var report = generateContrastReport();
  console.log('🎨 Color Contrast Enhancement Report:');
  console.log(`📊 Compliance Rate: ${report.complianceRate}%`);
  console.log(`✅ WCAG AA: ${report.summary.wcagAA}`);
  console.log(`🌟 WCAG AAA: ${report.summary.wcagAAA}`);
  console.log(`🔍 Status: ${report.summary.overallStatus}`);
  report.results.forEach(function (result) {
    if (!result.validation.isCompliant) {
      console.log(`⚠️  ${result.context}: ${result.validation.recommendation}`);
    }
  });
  return {
    enhancedColors: enhancedColors,
    report: report
  };
};
var getBestTextColor = exports.getBestTextColor = function getBestTextColor(backgroundColor) {
  var whiteContrast = _accessibilityUtils.ColorContrastUtils.getContrastRatio('#FFFFFF', backgroundColor);
  var blackContrast = _accessibilityUtils.ColorContrastUtils.getContrastRatio('#000000', backgroundColor);
  return whiteContrast > blackContrast ? '#FFFFFF' : '#000000';
};
var ensureMinimumContrast = exports.ensureMinimumContrast = function ensureMinimumContrast(foregroundColor, backgroundColor) {
  var minimumRatio = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 4.5;
  var currentRatio = _accessibilityUtils.ColorContrastUtils.getContrastRatio(foregroundColor, backgroundColor);
  if (currentRatio >= minimumRatio) {
    return foregroundColor;
  }
  return getBestTextColor(backgroundColor);
};
var EnhancedCTAColors = exports.EnhancedCTAColors = {
  primary: '#5A7A63',
  primaryHover: '#4A6B52',
  primaryPressed: '#3A5B42',
  primaryText: '#FFFFFF',
  secondary: '#E1EDE4',
  secondaryText: '#1F2937',
  success: '#10B981',
  warning: '#F59E0B',
  error: '#DC2626',
  info: '#3B82F6'
};
var _default = exports.default = {
  enhancePrimaryCTAContrast: enhancePrimaryCTAContrast,
  validateInteractiveColors: validateInteractiveColors,
  generateEnhancedCTAColors: generateEnhancedCTAColors,
  testColorCombination: testColorCombination,
  generateContrastReport: generateContrastReport,
  applyEnhancedColors: applyEnhancedColors,
  getBestTextColor: getBestTextColor,
  ensureMinimumContrast: ensureMinimumContrast,
  EnhancedCTAColors: EnhancedCTAColors
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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