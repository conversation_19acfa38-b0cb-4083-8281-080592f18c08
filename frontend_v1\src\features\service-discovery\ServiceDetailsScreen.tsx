/**
 * Service Details Screen - Detailed Service Information Display
 *
 * Component Contract:
 * - Displays comprehensive service information following frontend_v0 design patterns
 * - Shows service details, provider information, and reviews
 * - Provides booking functionality with backend integration
 * - Follows atomic design system with Box, Button, and SafeAreaWrapper components
 * - Integrates with Django backend API for real-time service data
 * - Responsive design with proper accessibility support
 *
 * @version 2.0.0 - Redesigned to match frontend_v0 patterns
 * <AUTHOR> Development Team
 */

import {
  useNavigation,
  useRoute,
  useFocusEffect,
} from '@react-navigation/native';
import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Platform,
  Dimensions,
  StyleSheet,
  ActivityIndicator,
  Alert,
  StatusBar,
  Image,
} from 'react-native';

import { Box } from '../../components/atoms/Box';
import { Button } from '../../components/atoms/Button';
import { IconButton } from '../../components/atoms/IconButton';
import { SafeAreaWrapper } from '../../components/ui/SafeAreaWrapper';

// Get screen dimensions for responsive design
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Responsive design utilities
const getResponsiveFontSize = (size: number) => {
  const scale = screenWidth / 375; // Base width (iPhone X)
  return Math.round(size * scale);
};

const getResponsiveSpacing = (spacing: number) => {
  const scale = screenWidth / 375;
  return Math.round(spacing * scale);
};

const isTablet = screenWidth >= 768;
const isSmallScreen = screenWidth < 375;

// Service details interface matching frontend_v0
interface ServiceDetails {
  id: string;
  name: string;
  description: string;
  longDescription: string;
  category: string;
  price: number;
  duration: number;
  provider: {
    id: string;
    name: string;
    rating: number;
    reviewCount: number;
    location: string;
    address: string;
    phone: string;
    email: string;
  };
  images: string[];
  features: string[];
  reviews: Review[];
  availability: string[];
}

// Review interface
interface Review {
  id: string;
  customerName: string;
  rating: number;
  comment: string;
  date: string;
}

// Route parameters interface
interface RouteParams {
  serviceId: string;
  providerId?: string;
}

export const ServiceDetailsScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { serviceId, providerId } = route.params as RouteParams;

  // State management
  const [service, setService] = useState<ServiceDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isBooking, setIsBooking] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchServiceDetails = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // TODO: Replace with actual API call to backend
      // const response = await fetch(`http://************:8000/api/catalog/services/${serviceId}/`);
      // const serviceData = await response.json();

      // Mock service data matching frontend_v0 structure
      const mockService: ServiceDetails = {
        id: serviceId,
        name: 'Hair Cut & Style',
        description: 'Professional haircut and styling service',
        longDescription:
          'Experience a premium haircut and styling service with our expert stylists. We use high-quality products and the latest techniques to give you the perfect look that suits your face shape and lifestyle.',
        category: 'Hair',
        price: 45,
        duration: 60,
        provider: {
          id: providerId || '1',
          name: 'Bella Beauty Salon',
          rating: 4.8,
          reviewCount: 127,
          location: 'Downtown',
          address: '123 Main Street, Downtown',
          phone: '+****************',
          email: '<EMAIL>',
        },
        images: [
          'https://images.unsplash.com/photo-**********-138dadb4c035?w=400&h=400&fit=crop',
          'https://images.unsplash.com/photo-**********-8baeececf3df?w=400&h=400&fit=crop',
        ],
        features: [
          'Professional consultation',
          'Premium hair products',
          'Styling included',
          'Wash and blow dry',
        ],
        reviews: [
          {
            id: '1',
            customerName: 'Sarah Johnson',
            rating: 5,
            comment:
              'Amazing service! The stylist really understood what I wanted.',
            date: '2024-01-15',
          },
          {
            id: '2',
            customerName: 'Mike Chen',
            rating: 4,
            comment: 'Great haircut, very professional staff.',
            date: '2024-01-10',
          },
        ],
        availability: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
      };

      setService(mockService);
    } catch (err) {
      console.error('Failed to load service details:', err);
      setError('Failed to load service details');
    } finally {
      setIsLoading(false);
    }
  }, [serviceId, providerId]);

  // Load service details when screen is focused
  useFocusEffect(
    useCallback(() => {
      fetchServiceDetails();
    }, [fetchServiceDetails]),
  );

  const handleBookService = async () => {
    if (!service) return;

    setIsBooking(true);
    try {
      // Navigate to booking flow with service details
      navigation.navigate('BookingFlow' as never, {
        serviceId: service.id,
        providerId: service.provider.id,
        serviceName: service.name,
        servicePrice: service.price,
        serviceDuration: service.duration,
      });
    } catch (err) {
      console.error('Failed to navigate to booking:', err);
      Alert.alert('Error', 'Failed to start booking process');
    } finally {
      setIsBooking(false);
    }
  };

  // Render star rating
  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Text key={i} style={styles.star}>
          ⭐
        </Text>,
      );
    }

    if (hasHalfStar) {
      stars.push(
        <Text key="half" style={styles.star}>
          ⭐
        </Text>,
      );
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <Text key={`empty-${i}`} style={styles.emptyStar}>
          ☆
        </Text>,
      );
    }

    return stars;
  };

  // Render individual review
  const renderReview = (review: Review) => (
    <View key={review.id} style={styles.reviewItem}>
      <View style={styles.reviewHeader}>
        <Text style={styles.reviewerName}>{review.customerName}</Text>
        <View style={styles.reviewRating}>{renderStars(review.rating)}</View>
      </View>
      <Text style={styles.reviewComment}>{review.comment}</Text>
      <Text style={styles.reviewDate}>{review.date}</Text>
    </View>
  );

  // Loading state
  if (isLoading) {
    return (
      <SafeAreaWrapper>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6B7280" />
          <Text style={styles.loadingText}>Loading service details...</Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  // Error state
  if (error || !service) {
    return (
      <SafeAreaWrapper>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error || 'Service not found'}</Text>
          <Button
            title="Retry"
            onPress={fetchServiceDetails}
            variant="primary"
            style={styles.retryButton}
          />
        </View>
      </SafeAreaWrapper>
    );
  }

  // Main render following frontend_v0 layout
  return (
    <SafeAreaWrapper testID="service-details-screen">
      <StatusBar
        barStyle="dark-content"
        backgroundColor="#FFFFFF"
        translucent={false}
      />

      {/* Header with back button */}
      <View style={styles.header}>
        <IconButton
          name="arrow-back"
          onPress={() => navigation.goBack()}
          style={styles.backButton}
          testID="back-button"
          accessibilityLabel="Go back"
        />
      </View>

      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Service Header */}
        <Box style={styles.serviceHeader}>
          <Text style={styles.serviceName} testID="service-name">
            {service.name}
          </Text>
          <Text style={styles.serviceCategory} testID="service-category">
            {service.category}
          </Text>
          <Text style={styles.serviceDescription} testID="service-description">
            {service.description}
          </Text>

          <View style={styles.serviceMeta}>
            <Text style={styles.servicePrice} testID="service-price">
              ${service.price}
            </Text>
            <Text style={styles.serviceDuration} testID="service-duration">
              {service.duration} minutes
            </Text>
          </View>
        </Box>

        {/* Provider Information */}
        <Box style={styles.providerSection}>
          <Text style={styles.sectionTitle}>Provider</Text>
          <Text style={styles.providerName} testID="provider-name">
            {service.provider.name}
          </Text>
          <Text style={styles.providerLocation} testID="provider-location">
            {service.provider.address}
          </Text>

          <View style={styles.providerRating}>
            <View style={styles.ratingStars}>
              {renderStars(Math.round(service.provider.rating))}
            </View>
            <Text style={styles.ratingText} testID="provider-rating">
              {service.provider.rating} ({service.provider.reviewCount} reviews)
            </Text>
          </View>

          <Text style={styles.providerContact} testID="provider-phone">
            Phone: {service.provider.phone}
          </Text>
        </Box>

        {/* Service Details */}
        <Box style={styles.detailsSection}>
          <Text style={styles.sectionTitle}>About This Service</Text>
          <Text style={styles.longDescription} testID="long-description">
            {service.longDescription}
          </Text>

          <Text style={styles.featuresTitle}>What's Included:</Text>
          {service.features.map((feature, index) => (
            <Text
              key={index}
              style={styles.featureItem}
              testID={`feature-${index}`}>
              • {feature}
            </Text>
          ))}
        </Box>

        {/* Reviews */}
        <Box style={styles.reviewsSection}>
          <Text style={styles.sectionTitle}>Reviews</Text>
          {service.reviews.map(renderReview)}
        </Box>
      </ScrollView>

      {/* Book Service Button */}
      <View style={styles.bookingContainer}>
        <Button
          title={isBooking ? 'Booking...' : 'Book Service'}
          onPress={handleBookService}
          variant="primary"
          disabled={isBooking}
          loading={isBooking}
          fullWidth
          testID="book-service-button"
          accessibilityLabel={`Book ${service.name}`}
        />
      </View>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    position: 'absolute',
    top: 50,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(16),
    zIndex: 10,
  },
  backButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: getResponsiveSpacing(24),
  },
  loadingText: {
    marginTop: getResponsiveSpacing(16),
    fontSize: getResponsiveFontSize(16),
    color: '#6B7280',
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: getResponsiveSpacing(24),
  },
  errorText: {
    fontSize: getResponsiveFontSize(18),
    color: '#EF4444',
    marginBottom: getResponsiveSpacing(24),
    textAlign: 'center',
    lineHeight: getResponsiveFontSize(24),
  },
  retryButton: {
    marginTop: getResponsiveSpacing(16),
  },
  serviceHeader: {
    paddingHorizontal: getResponsiveSpacing(20),
    paddingTop: getResponsiveSpacing(80), // Account for header
    paddingBottom: getResponsiveSpacing(24),
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
  },
  serviceName: {
    fontSize: getResponsiveFontSize(24),
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: getResponsiveSpacing(8),
    lineHeight: getResponsiveFontSize(32),
  },
  serviceCategory: {
    fontSize: getResponsiveFontSize(14),
    color: '#6B7280',
    marginBottom: getResponsiveSpacing(12),
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  serviceDescription: {
    fontSize: getResponsiveFontSize(16),
    color: '#6B7280',
    marginBottom: getResponsiveSpacing(16),
    lineHeight: getResponsiveFontSize(22),
  },
  serviceMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: isSmallScreen ? 'wrap' : 'nowrap',
    gap: getResponsiveSpacing(12),
  },
  servicePrice: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: 'bold',
    color: '#1F2937',
  },
  serviceDuration: {
    fontSize: getResponsiveFontSize(16),
    color: '#6B7280',
  },
  providerSection: {
    paddingHorizontal: getResponsiveSpacing(20),
    paddingVertical: getResponsiveSpacing(16),
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
  },
  sectionTitle: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: getResponsiveSpacing(12),
  },
  providerName: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: getResponsiveSpacing(4),
  },
  providerLocation: {
    fontSize: getResponsiveFontSize(14),
    color: '#6B7280',
    marginBottom: getResponsiveSpacing(8),
  },
  providerRating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(8),
    gap: getResponsiveSpacing(8),
  },
  ratingStars: {
    flexDirection: 'row',
  },
  star: {
    fontSize: getResponsiveFontSize(14),
    color: '#F59E0B',
  },
  emptyStar: {
    fontSize: getResponsiveFontSize(14),
    color: '#D1D5DB',
  },
  ratingText: {
    fontSize: getResponsiveFontSize(14),
    color: '#6B7280',
  },
  providerContact: {
    fontSize: getResponsiveFontSize(14),
    color: '#6B7280',
  },
  detailsSection: {
    paddingHorizontal: getResponsiveSpacing(20),
    paddingVertical: getResponsiveSpacing(16),
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
  },
  longDescription: {
    fontSize: getResponsiveFontSize(16),
    color: '#6B7280',
    lineHeight: getResponsiveFontSize(24),
    marginBottom: getResponsiveSpacing(16),
  },
  featuresTitle: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: getResponsiveSpacing(8),
  },
  featureItem: {
    fontSize: getResponsiveFontSize(14),
    color: '#6B7280',
    marginBottom: getResponsiveSpacing(4),
    lineHeight: getResponsiveFontSize(20),
  },
  reviewsSection: {
    paddingHorizontal: getResponsiveSpacing(20),
    paddingVertical: getResponsiveSpacing(16),
    backgroundColor: '#FFFFFF',
  },
  reviewItem: {
    marginBottom: getResponsiveSpacing(16),
    paddingBottom: getResponsiveSpacing(16),
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#E5E7EB',
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(8),
  },
  reviewerName: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '500',
    color: '#1F2937',
  },
  reviewRating: {
    flexDirection: 'row',
  },
  reviewComment: {
    fontSize: getResponsiveFontSize(14),
    color: '#6B7280',
    lineHeight: getResponsiveFontSize(20),
    marginBottom: getResponsiveSpacing(4),
  },
  reviewDate: {
    fontSize: getResponsiveFontSize(12),
    color: '#9CA3AF',
  },
  bookingContainer: {
    paddingHorizontal: getResponsiveSpacing(20),
    paddingVertical: getResponsiveSpacing(16),
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
  },
});

// Default export for lazy loading
export default ServiceDetailsScreen;
