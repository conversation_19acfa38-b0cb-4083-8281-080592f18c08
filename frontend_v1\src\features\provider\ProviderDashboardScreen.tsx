/**
 * Provider Dashboard Screen - Main dashboard for service providers
 *
 * Screen Contract:
 * - Displays comprehensive business analytics and metrics
 * - Shows upcoming bookings and recent activity
 * - Provides quick access to key provider functions
 * - Real-time updates for bookings and revenue
 * - Performance insights and business recommendations
 * - Responsive design for all screen sizes
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  RefreshControl,
  Alert,
} from 'react-native';
import styled from 'styled-components/native';

import { ErrorMessage } from '../../components/common/ErrorMessage';
import { FullScreenLoading } from '../../components/ui/LoadingStates';
import { SafeAreaWrapper } from '../../components/ui/SafeAreaWrapper';
import { Colors } from '../../constants/Colors';
import { useAuthStore } from '../../store/authSlice';
import { useProviderStore } from '../../store/providerSlice';
import { useUnifiedErrorHandling } from '../../services/unifiedErrorHandling';

const { width } = Dimensions.get('window');

interface DashboardCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: string;
  color: string;
  onPress?: () => void;
}

const ProviderDashboardScreen: React.FC = () => {
  const navigation = useNavigation();
  const { user } = useAuthStore();
  const {
    profile,
    isLoading,
    error,
    analyticsLoading,
    loadProfile,
    refreshAnalytics,
    clearError,
  } = useProviderStore();

  // Unified error handling
  const { handleError, handleNetworkError, withErrorHandling } = useUnifiedErrorHandling({
    component: 'ProviderDashboardScreen',
    screen: 'ProviderDashboard'
  });

  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (user?.id) {
      loadProfile(user.id);
    }
  }, [user?.id, loadProfile]);

  const handleRefresh = async () => {
    setRefreshing(true);

    const { error } = await withErrorHandling(
      async () => {
        if (user?.id) {
          await Promise.all([loadProfile(user.id), refreshAnalytics()]);
        }
      },
      {
        action: 'refresh_dashboard',
        additionalData: { userId: user?.id }
      }
    );

    setRefreshing(false);
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
    }).format(amount);
  };

  const formatPercentage = (value: number): string => {
    return `${value.toFixed(1)}%`;
  };

  const DashboardCard: React.FC<DashboardCardProps> = ({
    title,
    value,
    subtitle,
    icon,
    color,
    onPress,
  }) => (
    <CardContainer onPress={onPress} disabled={!onPress}>
      <CardHeader>
        <CardIcon name={icon as any} size={24} color={color} />
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardValue>{value}</CardValue>
      {subtitle && <CardSubtitle>{subtitle}</CardSubtitle>}
    </CardContainer>
  );

  if (isLoading && !profile) {
    return (
      <Container>
        <FullScreenLoading size="large" />
      </Container>
    );
  }

  if (error && !profile) {
    return (
      <Container>
        <ErrorMessage
          message={error}
          onRetry={() => user?.id && loadProfile(user.id)}
          onDismiss={clearError}
        />
      </Container>
    );
  }

  if (!profile) {
    return (
      <Container>
        <EmptyStateContainer>
          <EmptyStateIcon
            name="business-outline"
            size={64}
            color={Colors.text.primary}
          />
          <EmptyStateTitle>Complete Your Profile</EmptyStateTitle>
          <EmptyStateText>
            Set up your business profile to start receiving bookings
          </EmptyStateText>
          <SetupButton
            onPress={() => navigation.navigate('ProviderSetup' as never)}>
            <SetupButtonText>Setup Profile</SetupButtonText>
          </SetupButton>
        </EmptyStateContainer>
      </Container>
    );
  }

  const analytics = profile.analytics;

  return (
    <Container>
      <SafeAreaWrapper style={{ flex: 1 }}>
        <Header>
          <HeaderContent>
            <WelcomeText>Welcome back,</WelcomeText>
            <BusinessName>{profile.businessName}</BusinessName>
          </HeaderContent>
          <HeaderActions>
            <HeaderButton
              onPress={() => navigation.navigate('ProviderSettings' as never)}>
              <Ionicons
                name="settings-outline"
                size={24}
                color={Colors.text.primary}
              />
            </HeaderButton>
            <HeaderButton
              onPress={() => navigation.navigate('ProviderProfile' as never)}>
              <Ionicons
                name="person-outline"
                size={24}
                color={Colors.text.primary}
              />
            </HeaderButton>
          </HeaderActions>
        </Header>

        <ScrollView
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[Colors.primary.default]}
            />
          }>
          {/* Quick Stats */}
          <Section>
            <SectionTitle>Today's Overview</SectionTitle>
            <StatsGrid>
              <DashboardCard
                title="Total Revenue"
                value={formatCurrency(analytics.totalRevenue)}
                subtitle="All time"
                icon="cash-outline"
                color={Colors.success}
                onPress={() => navigation.navigate('RevenueAnalytics' as never)}
              />
              <DashboardCard
                title="Bookings"
                value={analytics.totalBookings}
                subtitle={`${analytics.completedBookings} completed`}
                icon="calendar-outline"
                color={Colors.primary.default}
                onPress={() => {
                  // TODO: Navigate to BookingManagement when it's re-enabled
                  console.log(
                    'BookingManagement navigation temporarily disabled',
                  );
                }}
              />
              <DashboardCard
                title="Rating"
                value={analytics.averageRating ? analytics.averageRating.toFixed(1) : '0.0'}
                subtitle={`${analytics.totalReviews} reviews`}
                icon="star-outline"
                color={Colors.warning}
                onPress={() =>
                  navigation.navigate('ReviewsManagement' as never)
                }
              />
              <DashboardCard
                title="Completion Rate"
                value={formatPercentage(analytics.completionRate)}
                subtitle="This month"
                icon="checkmark-circle-outline"
                color={Colors.info}
              />
            </StatsGrid>
          </Section>

          {/* Quick Actions */}
          <Section>
            <SectionTitle>Quick Actions</SectionTitle>
            <ActionsGrid>
              <ActionButton
                onPress={() =>
                  navigation.navigate('ServiceManagement' as never)
                }>
                <ActionIcon
                  name="list-outline"
                  size={32}
                  color={Colors.primary.default}
                />
                <ActionText>Manage Services</ActionText>
              </ActionButton>
              <ActionButton
                onPress={() =>
                  navigation.navigate('AvailabilitySettings' as never)
                }>
                <ActionIcon
                  name="time-outline"
                  size={32}
                  color={Colors.primary.default}
                />
                <ActionText>Set Availability</ActionText>
              </ActionButton>
              <ActionButton
                onPress={() =>
                  navigation.navigate('PortfolioManagement' as never)
                }>
                <ActionIcon
                  name="images-outline"
                  size={32}
                  color={Colors.primary.default}
                />
                <ActionText>Portfolio</ActionText>
              </ActionButton>
              <ActionButton
                onPress={() =>
                  navigation.navigate('ProviderMessaging' as never)
                }>
                <ActionIcon
                  name="chatbubbles-outline"
                  size={32}
                  color={Colors.primary.default}
                />
                <ActionText>Messages</ActionText>
              </ActionButton>
            </ActionsGrid>
          </Section>

          {/* Recent Activity */}
          <Section>
            <SectionHeader>
              <SectionTitle>Recent Activity</SectionTitle>
              <ViewAllButton
                onPress={() => navigation.navigate('ActivityHistory' as never)}>
                <ViewAllText>View All</ViewAllText>
              </ViewAllButton>
            </SectionHeader>
            <ActivityContainer>
              <ActivityItem>
                <ActivityIcon
                  name="calendar"
                  size={20}
                  color={Colors.success}
                />
                <ActivityContent>
                  <ActivityTitle>New booking from Sarah Johnson</ActivityTitle>
                  <ActivityTime>2 hours ago</ActivityTime>
                </ActivityContent>
              </ActivityItem>
              <ActivityItem>
                <ActivityIcon name="star" size={20} color={Colors.warning} />
                <ActivityContent>
                  <ActivityTitle>New 5-star review received</ActivityTitle>
                  <ActivityTime>4 hours ago</ActivityTime>
                </ActivityContent>
              </ActivityItem>
              <ActivityItem>
                <ActivityIcon name="cash" size={20} color={Colors.success} />
                <ActivityContent>
                  <ActivityTitle>Payment received - $85.00</ActivityTitle>
                  <ActivityTime>6 hours ago</ActivityTime>
                </ActivityContent>
              </ActivityItem>
            </ActivityContainer>
          </Section>

          {/* Performance Insights */}
          <Section>
            <SectionTitle>Performance Insights</SectionTitle>
            <InsightCard>
              <InsightHeader>
                <InsightIcon
                  name="trending-up"
                  size={24}
                  color={Colors.success}
                />
                <InsightTitle>Business Growth</InsightTitle>
              </InsightHeader>
              <InsightText>
                Your bookings increased by 23% this month compared to last
                month. Keep up the great work!
              </InsightText>
            </InsightCard>
            <InsightCard>
              <InsightHeader>
                <InsightIcon name="time" size={24} color={Colors.info} />
                <InsightTitle>Peak Hours</InsightTitle>
              </InsightHeader>
              <InsightText>
                Most bookings occur between 2-4 PM. Consider adjusting your
                availability to maximize revenue.
              </InsightText>
            </InsightCard>
          </Section>

          {/* Verification Status */}
          {profile.verification.status !== 'verified' && (
            <Section>
              <VerificationCard>
                <VerificationHeader>
                  <VerificationIcon
                    name="shield-checkmark-outline"
                    size={24}
                    color={Colors.warning}
                  />
                  <VerificationTitle>Complete Verification</VerificationTitle>
                </VerificationHeader>
                <VerificationText>
                  Complete your business verification to build trust with
                  customers and unlock premium features.
                </VerificationText>
                <VerificationButton
                  onPress={() =>
                    navigation.navigate('ProviderVerification' as never)
                  }>
                  <VerificationButtonText>
                    Complete Verification
                  </VerificationButtonText>
                </VerificationButton>
              </VerificationCard>
            </Section>
          )}
        </ScrollView>
      </SafeAreaWrapper>
    </Container>
  );
};

const Container = styled.View`
  flex: 1;
  background-color: ${Colors.background.primary};
`;

const Header = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: ${Colors.surface.primary};
  border-bottom-width: 1px;
  border-bottom-color: ${Colors.border.primary};
`;

const HeaderContent = styled.View`
  flex: 1;
`;

const WelcomeText = styled.Text`
  font-size: 14px;
  color: ${Colors.text.secondary};
  margin-bottom: 4px;
`;

const BusinessName = styled.Text`
  font-size: 20px;
  font-weight: bold;
  color: ${Colors.text.primary};
`;

const HeaderActions = styled.View`
  flex-direction: row;
  gap: 12px;
`;

const HeaderButton = styled.TouchableOpacity`
  padding: 8px;
  border-radius: 8px;
  background-color: ${Colors.background.primary};
`;

const Section = styled.View`
  padding: 16px;
`;

const SectionHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const SectionTitle = styled.Text`
  font-size: 18px;
  font-weight: bold;
  color: ${Colors.text.primary};
  margin-bottom: 16px;
`;

const ViewAllButton = styled.TouchableOpacity``;

const ViewAllText = styled.Text`
  color: ${Colors.primary.default};
  font-weight: 600;
`;

const StatsGrid = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  gap: 12px;
`;

const CardContainer = styled.TouchableOpacity`
  background-color: ${Colors.surface.primary};
  border-radius: 12px;
  padding: 16px;
  width: ${(width - 48) / 2}px;
  border-width: 1px;
  border-color: ${Colors.border.primary};
`;

const CardHeader = styled.View`
  flex-direction: row;
  align-items: center;
  margin-bottom: 12px;
`;

const CardIcon = styled(Ionicons)`
  margin-right: 8px;
`;

const CardTitle = styled.Text`
  font-size: 14px;
  color: ${Colors.text.secondary};
  font-weight: 500;
`;

const CardValue = styled.Text`
  font-size: 24px;
  font-weight: bold;
  color: ${Colors.text.primary};
  margin-bottom: 4px;
`;

const CardSubtitle = styled.Text`
  font-size: 12px;
  color: ${Colors.text.secondary};
`;

const ActionsGrid = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  gap: 12px;
`;

const ActionButton = styled.TouchableOpacity`
  background-color: ${Colors.surface.primary};
  border-radius: 12px;
  padding: 20px;
  width: ${(width - 48) / 2}px;
  align-items: center;
  border-width: 1px;
  border-color: ${Colors.border.primary};
`;

const ActionIcon = styled(Ionicons)`
  margin-bottom: 8px;
`;

const ActionText = styled.Text`
  font-size: 14px;
  font-weight: 600;
  color: ${Colors.text.primary};
  text-align: center;
`;

const ActivityContainer = styled.View`
  background-color: ${Colors.surface.primary};
  border-radius: 12px;
  border-width: 1px;
  border-color: ${Colors.border.primary};
`;

const ActivityItem = styled.View`
  flex-direction: row;
  align-items: center;
  padding: 16px;
  border-bottom-width: 1px;
  border-bottom-color: ${Colors.border.primary};
`;

const ActivityIcon = styled(Ionicons)`
  margin-right: 12px;
`;

const ActivityContent = styled.View`
  flex: 1;
`;

const ActivityTitle = styled.Text`
  font-size: 14px;
  font-weight: 500;
  color: ${Colors.text.primary};
  margin-bottom: 4px;
`;

const ActivityTime = styled.Text`
  font-size: 12px;
  color: ${Colors.text.primarySecondary};
`;

const InsightCard = styled.View`
  background-color: ${Colors.surface.primary};
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  border-width: 1px;
  border-color: ${Colors.border.primary};
`;

const InsightHeader = styled.View`
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
`;

const InsightIcon = styled(Ionicons)`
  margin-right: 8px;
`;

const InsightTitle = styled.Text`
  font-size: 16px;
  font-weight: 600;
  color: ${Colors.text.primary};
`;

const InsightText = styled.Text`
  font-size: 14px;
  color: ${Colors.text.primarySecondary};
  line-height: 20px;
`;

const VerificationCard = styled.View`
  background-color: ${Colors.warning}20;
  border-radius: 12px;
  padding: 16px;
  border-width: 1px;
  border-color: ${Colors.warning};
`;

const VerificationHeader = styled.View`
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
`;

const VerificationIcon = styled(Ionicons)`
  margin-right: 8px;
`;

const VerificationTitle = styled.Text`
  font-size: 16px;
  font-weight: 600;
  color: ${Colors.text.primary};
`;

const VerificationText = styled.Text`
  font-size: 14px;
  color: ${Colors.text.primarySecondary};
  line-height: 20px;
  margin-bottom: 12px;
`;

const VerificationButton = styled.TouchableOpacity`
  background-color: ${Colors.warning};
  border-radius: 8px;
  padding: 12px;
  align-items: center;
`;

const VerificationButtonText = styled.Text`
  color: white;
  font-weight: 600;
  font-size: 14px;
`;

const EmptyStateContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 32px;
`;

const EmptyStateIcon = styled(Ionicons)`
  margin-bottom: 16px;
`;

const EmptyStateTitle = styled.Text`
  font-size: 20px;
  font-weight: bold;
  color: ${Colors.text.primary};
  margin-bottom: 8px;
  text-align: center;
`;

const EmptyStateText = styled.Text`
  font-size: 16px;
  color: ${Colors.text.primarySecondary};
  text-align: center;
  line-height: 24px;
  margin-bottom: 24px;
`;

const SetupButton = styled.TouchableOpacity`
  background-color: ${Colors.primary.default};
  border-radius: 8px;
  padding: 16px 32px;
`;

const SetupButtonText = styled.Text`
  color: white;
  font-weight: 600;
  font-size: 16px;
`;

export default ProviderDashboardScreen;
