2cffe9ff6f369100aadb7acb86e98138
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _StyleSheet = _interopRequireDefault(require("../../StyleSheet/StyleSheet"));
var _Platform = _interopRequireDefault(require("../../Utilities/Platform"));
var _View = _interopRequireDefault(require("../View/View"));
var React = _interopRequireWildcard(require("react"));
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["animating", "color", "hidesWhenStopped", "onLayout", "size", "style"];
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var PlatformActivityIndicator = _Platform.default.OS === 'android' ? require("../ProgressBarAndroid/ProgressBarAndroid").default : require("./ActivityIndicatorViewNativeComponent").default;
var GRAY = '#999999';
var ActivityIndicator = function ActivityIndicator(_ref, forwardedRef) {
  var _ref$animating = _ref.animating,
    animating = _ref$animating === void 0 ? true : _ref$animating,
    _ref$color = _ref.color,
    color = _ref$color === void 0 ? _Platform.default.OS === 'ios' ? GRAY : null : _ref$color,
    _ref$hidesWhenStopped = _ref.hidesWhenStopped,
    hidesWhenStopped = _ref$hidesWhenStopped === void 0 ? true : _ref$hidesWhenStopped,
    onLayout = _ref.onLayout,
    _ref$size = _ref.size,
    size = _ref$size === void 0 ? 'small' : _ref$size,
    style = _ref.style,
    restProps = (0, _objectWithoutProperties2.default)(_ref, _excluded);
  var sizeStyle;
  var sizeProp;
  switch (size) {
    case 'small':
      sizeStyle = styles.sizeSmall;
      sizeProp = 'small';
      break;
    case 'large':
      sizeStyle = styles.sizeLarge;
      sizeProp = 'large';
      break;
    default:
      sizeStyle = {
        height: size,
        width: size
      };
      break;
  }
  var nativeProps = Object.assign({
    animating: animating,
    color: color,
    hidesWhenStopped: hidesWhenStopped
  }, restProps, {
    ref: forwardedRef,
    style: sizeStyle,
    size: sizeProp
  });
  var androidProps = {
    styleAttr: 'Normal',
    indeterminate: true
  };
  return (0, _jsxRuntime.jsx)(_View.default, {
    onLayout: onLayout,
    style: _StyleSheet.default.compose(styles.container, style),
    children: _Platform.default.OS === 'android' ? (0, _jsxRuntime.jsx)(PlatformActivityIndicator, Object.assign({}, nativeProps, androidProps)) : (0, _jsxRuntime.jsx)(PlatformActivityIndicator, Object.assign({}, nativeProps))
  });
};
var ActivityIndicatorWithRef = React.forwardRef(ActivityIndicator);
ActivityIndicatorWithRef.displayName = 'ActivityIndicator';
var styles = _StyleSheet.default.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center'
  },
  sizeSmall: {
    width: 20,
    height: 20
  },
  sizeLarge: {
    width: 36,
    height: 36
  }
});
var _default = exports.default = ActivityIndicatorWithRef;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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