59b4a88fe58befafc9dce8d72b0d59f5
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.cachingService = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _asyncStorage = _interopRequireDefault(require("@react-native-async-storage/async-storage"));
var _performanceMonitor = require("./performanceMonitor");
var DEFAULT_CONFIG = {
  maxMemorySize: 50,
  maxStorageSize: 100,
  defaultTTL: 30 * 60 * 1000,
  cleanupInterval: 5 * 60 * 1000,
  enableCompression: true,
  enableEncryption: false
};
var CachingService = function () {
  function CachingService() {
    var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    (0, _classCallCheck2.default)(this, CachingService);
    this.memoryCache = new Map();
    this.cleanupTimer = null;
    this.config = Object.assign({}, DEFAULT_CONFIG, config);
    this.metrics = {
      memoryUsage: 0,
      storageUsage: 0,
      hitRate: 0,
      missRate: 0,
      totalRequests: 0,
      totalHits: 0,
      totalMisses: 0
    };
    this.startCleanupTimer();
  }
  return (0, _createClass2.default)(CachingService, [{
    key: "initialize",
    value: (function () {
      var _initialize = (0, _asyncToGenerator2.default)(function* () {
        try {
          yield this.loadStorageMetrics();
          yield this.cleanupExpiredEntries();
          console.log('[CachingService] Initialized successfully');
        } catch (error) {
          console.error('[CachingService] Initialization failed:', error);
        }
      });
      function initialize() {
        return _initialize.apply(this, arguments);
      }
      return initialize;
    }())
  }, {
    key: "setMemory",
    value: function setMemory(key, data, ttl) {
      var entry = {
        data: data,
        timestamp: Date.now(),
        ttl: ttl || this.config.defaultTTL,
        size: this.calculateSize(data),
        accessCount: 0,
        lastAccessed: Date.now()
      };
      if (this.getMemoryUsage() + entry.size > this.config.maxMemorySize * 1024 * 1024) {
        this.evictLeastRecentlyUsed();
      }
      this.memoryCache.set(key, entry);
      this.updateMemoryUsage();
      _performanceMonitor.performanceMonitor.trackCacheOperation('memory_set', key, entry.size);
    }
  }, {
    key: "getMemory",
    value: function getMemory(key) {
      this.metrics.totalRequests++;
      var entry = this.memoryCache.get(key);
      if (!entry) {
        this.metrics.totalMisses++;
        this.updateHitRate();
        return null;
      }
      if (Date.now() - entry.timestamp > entry.ttl) {
        this.memoryCache.delete(key);
        this.updateMemoryUsage();
        this.metrics.totalMisses++;
        this.updateHitRate();
        return null;
      }
      entry.accessCount++;
      entry.lastAccessed = Date.now();
      this.metrics.totalHits++;
      this.updateHitRate();
      _performanceMonitor.performanceMonitor.trackCacheOperation('memory_get', key, entry.size);
      return entry.data;
    }
  }, {
    key: "setStorage",
    value: (function () {
      var _setStorage = (0, _asyncToGenerator2.default)(function* (key, data, ttl) {
        try {
          var entry = {
            data: data,
            timestamp: Date.now(),
            ttl: ttl || this.config.defaultTTL,
            size: this.calculateSize(data),
            accessCount: 0,
            lastAccessed: Date.now()
          };
          var serialized = JSON.stringify(entry);
          if (this.config.enableCompression) {}
          yield _asyncStorage.default.setItem(`cache_${key}`, serialized);
          _performanceMonitor.performanceMonitor.trackCacheOperation('storage_set', key, entry.size);
        } catch (error) {
          console.error('[CachingService] Storage set failed:', error);
        }
      });
      function setStorage(_x, _x2, _x3) {
        return _setStorage.apply(this, arguments);
      }
      return setStorage;
    }())
  }, {
    key: "getStorage",
    value: (function () {
      var _getStorage = (0, _asyncToGenerator2.default)(function* (key) {
        try {
          this.metrics.totalRequests++;
          var serialized = yield _asyncStorage.default.getItem(`cache_${key}`);
          if (!serialized) {
            this.metrics.totalMisses++;
            this.updateHitRate();
            return null;
          }
          var entry = JSON.parse(serialized);
          if (Date.now() - entry.timestamp > entry.ttl) {
            yield _asyncStorage.default.removeItem(`cache_${key}`);
            this.metrics.totalMisses++;
            this.updateHitRate();
            return null;
          }
          entry.accessCount++;
          entry.lastAccessed = Date.now();
          yield _asyncStorage.default.setItem(`cache_${key}`, JSON.stringify(entry));
          this.metrics.totalHits++;
          this.updateHitRate();
          _performanceMonitor.performanceMonitor.trackCacheOperation('storage_get', key, entry.size);
          return entry.data;
        } catch (error) {
          console.error('[CachingService] Storage get failed:', error);
          this.metrics.totalMisses++;
          this.updateHitRate();
          return null;
        }
      });
      function getStorage(_x4) {
        return _getStorage.apply(this, arguments);
      }
      return getStorage;
    }())
  }, {
    key: "cacheApiResponse",
    value: (function () {
      var _cacheApiResponse = (0, _asyncToGenerator2.default)(function* (endpoint, params, data, ttl) {
        var key = this.generateApiCacheKey(endpoint, params);
        this.setMemory(key, data, ttl);
        yield this.setStorage(key, data, ttl);
      });
      function cacheApiResponse(_x5, _x6, _x7, _x8) {
        return _cacheApiResponse.apply(this, arguments);
      }
      return cacheApiResponse;
    }())
  }, {
    key: "getCachedApiResponse",
    value: (function () {
      var _getCachedApiResponse = (0, _asyncToGenerator2.default)(function* (endpoint, params) {
        var key = this.generateApiCacheKey(endpoint, params);
        var data = this.getMemory(key);
        if (data) {
          return data;
        }
        data = yield this.getStorage(key);
        if (data) {
          this.setMemory(key, data);
        }
        return data;
      });
      function getCachedApiResponse(_x9, _x0) {
        return _getCachedApiResponse.apply(this, arguments);
      }
      return getCachedApiResponse;
    }())
  }, {
    key: "clearAll",
    value: (function () {
      var _clearAll = (0, _asyncToGenerator2.default)(function* () {
        this.memoryCache.clear();
        this.updateMemoryUsage();
        try {
          var keys = yield _asyncStorage.default.getAllKeys();
          var cacheKeys = keys.filter(function (key) {
            return key.startsWith('cache_');
          });
          yield _asyncStorage.default.multiRemove(cacheKeys);
        } catch (error) {
          console.error('[CachingService] Clear all failed:', error);
        }
        this.resetMetrics();
      });
      function clearAll() {
        return _clearAll.apply(this, arguments);
      }
      return clearAll;
    }())
  }, {
    key: "getMetrics",
    value: function getMetrics() {
      return Object.assign({}, this.metrics);
    }
  }, {
    key: "generateApiCacheKey",
    value: function generateApiCacheKey(endpoint, params) {
      var sortedParams = Object.keys(params).sort().reduce(function (result, key) {
        result[key] = params[key];
        return result;
      }, {});
      return `api_${endpoint}_${JSON.stringify(sortedParams)}`;
    }
  }, {
    key: "calculateSize",
    value: function calculateSize(data) {
      return JSON.stringify(data).length * 2;
    }
  }, {
    key: "getMemoryUsage",
    value: function getMemoryUsage() {
      return Array.from(this.memoryCache.values()).reduce(function (total, entry) {
        return total + entry.size;
      }, 0);
    }
  }, {
    key: "updateMemoryUsage",
    value: function updateMemoryUsage() {
      this.metrics.memoryUsage = this.getMemoryUsage();
    }
  }, {
    key: "updateHitRate",
    value: function updateHitRate() {
      if (this.metrics.totalRequests > 0) {
        this.metrics.hitRate = this.metrics.totalHits / this.metrics.totalRequests;
        this.metrics.missRate = this.metrics.totalMisses / this.metrics.totalRequests;
      }
    }
  }, {
    key: "evictLeastRecentlyUsed",
    value: function evictLeastRecentlyUsed() {
      var oldestEntry = null;
      for (var _ref of this.memoryCache.entries()) {
        var _ref2 = (0, _slicedToArray2.default)(_ref, 2);
        var key = _ref2[0];
        var entry = _ref2[1];
        if (!oldestEntry || entry.lastAccessed < oldestEntry.lastAccessed) {
          oldestEntry = {
            key: key,
            lastAccessed: entry.lastAccessed
          };
        }
      }
      if (oldestEntry) {
        this.memoryCache.delete(oldestEntry.key);
        console.log(`[CachingService] Evicted LRU entry: ${oldestEntry.key}`);
      }
    }
  }, {
    key: "cleanupExpiredEntries",
    value: function () {
      var _cleanupExpiredEntries = (0, _asyncToGenerator2.default)(function* () {
        var now = Date.now();
        for (var _ref3 of this.memoryCache.entries()) {
          var _ref4 = (0, _slicedToArray2.default)(_ref3, 2);
          var key = _ref4[0];
          var entry = _ref4[1];
          if (now - entry.timestamp > entry.ttl) {
            this.memoryCache.delete(key);
          }
        }
        this.updateMemoryUsage();
        try {
          var keys = yield _asyncStorage.default.getAllKeys();
          var cacheKeys = keys.filter(function (key) {
            return key.startsWith('cache_');
          });
          for (var _key of cacheKeys) {
            var serialized = yield _asyncStorage.default.getItem(_key);
            if (serialized) {
              var _entry = JSON.parse(serialized);
              if (now - _entry.timestamp > _entry.ttl) {
                yield _asyncStorage.default.removeItem(_key);
              }
            }
          }
        } catch (error) {
          console.error('[CachingService] Cleanup failed:', error);
        }
      });
      function cleanupExpiredEntries() {
        return _cleanupExpiredEntries.apply(this, arguments);
      }
      return cleanupExpiredEntries;
    }()
  }, {
    key: "loadStorageMetrics",
    value: function () {
      var _loadStorageMetrics = (0, _asyncToGenerator2.default)(function* () {
        try {
          var keys = yield _asyncStorage.default.getAllKeys();
          var cacheKeys = keys.filter(function (key) {
            return key.startsWith('cache_');
          });
          var totalSize = 0;
          for (var key of cacheKeys) {
            var serialized = yield _asyncStorage.default.getItem(key);
            if (serialized) {
              totalSize += serialized.length * 2;
            }
          }
          this.metrics.storageUsage = totalSize;
        } catch (error) {
          console.error('[CachingService] Load storage metrics failed:', error);
        }
      });
      function loadStorageMetrics() {
        return _loadStorageMetrics.apply(this, arguments);
      }
      return loadStorageMetrics;
    }()
  }, {
    key: "startCleanupTimer",
    value: function startCleanupTimer() {
      var _this = this;
      this.cleanupTimer = setInterval(function () {
        _this.cleanupExpiredEntries();
      }, this.config.cleanupInterval);
    }
  }, {
    key: "resetMetrics",
    value: function resetMetrics() {
      this.metrics = {
        memoryUsage: 0,
        storageUsage: 0,
        hitRate: 0,
        missRate: 0,
        totalRequests: 0,
        totalHits: 0,
        totalMisses: 0
      };
    }
  }, {
    key: "destroy",
    value: function destroy() {
      if (this.cleanupTimer) {
        clearInterval(this.cleanupTimer);
        this.cleanupTimer = null;
      }
    }
  }]);
}();
var cachingService = exports.cachingService = new CachingService();
var _default = exports.default = cachingService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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