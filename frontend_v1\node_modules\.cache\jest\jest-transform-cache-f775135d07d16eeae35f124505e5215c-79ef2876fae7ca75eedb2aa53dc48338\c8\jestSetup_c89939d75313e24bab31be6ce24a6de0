2659813399e057365347ae94b9453a5b
var originalConsoleError = console.error;
var originalConsoleWarn = console.warn;
console.error = function () {
  var message = arguments.length <= 0 ? undefined : arguments[0];
  if (typeof message === 'string' && (message.includes('Warning: ReactDOM.render is no longer supported') || message.includes('Warning: componentWillMount has been renamed') || message.includes('Warning: componentWillReceiveProps has been renamed') || message.includes('VirtualizedLists should never be nested'))) {
    return;
  }
  originalConsoleError.apply(void 0, arguments);
};
console.warn = function () {
  var message = arguments.length <= 0 ? undefined : arguments[0];
  if (typeof message === 'string' && (message.includes('Animated: `useNativeDriver`') || message.includes('source.uri should not be an empty string'))) {
    return;
  }
  originalConsoleWarn.apply(void 0, arguments);
};
global.mockNavigate = jest.fn();
global.mockGoBack = jest.fn();
global.mockReset = jest.fn();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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