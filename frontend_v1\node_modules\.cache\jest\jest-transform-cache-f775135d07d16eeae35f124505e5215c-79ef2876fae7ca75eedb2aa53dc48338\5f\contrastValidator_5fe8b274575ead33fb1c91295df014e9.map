{"version": 3, "names": ["CONTRAST_STANDARDS", "exports", "AA_NORMAL", "AA_LARGE", "AAA_NORMAL", "AAA_LARGE", "NON_TEXT", "hexToRgb", "hex", "result", "exec", "r", "parseInt", "g", "b", "rgbToHex", "toString", "slice", "getRelativeLuminance", "rgb", "rsRGB", "gsRGB", "bsRGB", "rLinear", "Math", "pow", "gLinear", "bLinear", "getContrastRatio", "foreground", "background", "l1", "l2", "lighter", "max", "darker", "min", "validateC<PERSON><PERSON>t", "isLargeText", "arguments", "length", "undefined", "targetLevel", "ratio", "requiredRatio", "<PERSON><PERSON><PERSON><PERSON>", "level", "recommendation", "deficit", "toFixed", "darkenColor", "percentage", "factor", "round", "lightenColor", "fixContrast", "targetRatio", "adjustBackground", "currentForeground", "currentBackground", "currentRatio", "foregroundLuminance", "backgroundLuminance", "i", "testBackground", "testRatio", "testForeground", "getButtonColorSuggestions", "brandColor", "suggestions", "primary", "text", "secondary", "border", "disabled", "primaryValidation", "fixed", "secondaryValidation", "_default", "default"], "sources": ["contrastValidator.ts"], "sourcesContent": ["/**\n * WCAG 2.1 AA Contrast Validator and Fixer\n *\n * Implements REC-ACC-001: Fix button contrast ratios to meet WCAG 2.1 AA standards.\n * Provides utilities to validate and automatically fix color contrast issues.\n *\n * WCAG 2.1 AA Requirements:\n * - Normal text: 4.5:1 contrast ratio minimum\n * - Large text (18pt+ or 14pt+ bold): 3:1 contrast ratio minimum\n * - Non-text elements: 3:1 contrast ratio minimum\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\n// WCAG 2.1 AA contrast standards\nexport const CONTRAST_STANDARDS = {\n  AA_NORMAL: 4.5,\n  AA_LARGE: 3.0,\n  AAA_NORMAL: 7.0,\n  AAA_LARGE: 4.5,\n  NON_TEXT: 3.0,\n} as const;\n\n// Color contrast validation result\nexport interface ContrastValidationResult {\n  ratio: number;\n  isValid: boolean;\n  level: 'AA' | 'AAA' | 'FAIL';\n  recommendation?: string;\n  suggestedColors?: {\n    foreground?: string;\n    background?: string;\n  };\n}\n\n/**\n * Convert hex color to RGB values\n */\nexport const hexToRgb = (\n  hex: string,\n): { r: number; g: number; b: number } | null => {\n  const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n  return result\n    ? {\n        r: parseInt(result[1], 16),\n        g: parseInt(result[2], 16),\n        b: parseInt(result[3], 16),\n      }\n    : null;\n};\n\n/**\n * Convert RGB to hex color\n */\nexport const rgbToHex = (r: number, g: number, b: number): string => {\n  return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);\n};\n\n/**\n * Calculate relative luminance of a color\n */\nexport const getRelativeLuminance = (hex: string): number => {\n  const rgb = hexToRgb(hex);\n  if (!rgb) return 0;\n\n  const { r, g, b } = rgb;\n\n  // Convert to sRGB\n  const rsRGB = r / 255;\n  const gsRGB = g / 255;\n  const bsRGB = b / 255;\n\n  // Apply gamma correction\n  const rLinear =\n    rsRGB <= 0.03928 ? rsRGB / 12.92 : Math.pow((rsRGB + 0.055) / 1.055, 2.4);\n  const gLinear =\n    gsRGB <= 0.03928 ? gsRGB / 12.92 : Math.pow((gsRGB + 0.055) / 1.055, 2.4);\n  const bLinear =\n    bsRGB <= 0.03928 ? bsRGB / 12.92 : Math.pow((bsRGB + 0.055) / 1.055, 2.4);\n\n  // Calculate relative luminance\n  return 0.2126 * rLinear + 0.7152 * gLinear + 0.0722 * bLinear;\n};\n\n/**\n * Calculate contrast ratio between two colors\n */\nexport const getContrastRatio = (\n  foreground: string,\n  background: string,\n): number => {\n  const l1 = getRelativeLuminance(foreground);\n  const l2 = getRelativeLuminance(background);\n\n  const lighter = Math.max(l1, l2);\n  const darker = Math.min(l1, l2);\n\n  return (lighter + 0.05) / (darker + 0.05);\n};\n\n/**\n * Validate contrast ratio against WCAG standards\n */\nexport const validateContrast = (\n  foreground: string,\n  background: string,\n  isLargeText: boolean = false,\n  targetLevel: 'AA' | 'AAA' = 'AA',\n): ContrastValidationResult => {\n  const ratio = getContrastRatio(foreground, background);\n\n  const requiredRatio =\n    targetLevel === 'AAA'\n      ? isLargeText\n        ? CONTRAST_STANDARDS.AAA_LARGE\n        : CONTRAST_STANDARDS.AAA_NORMAL\n      : isLargeText\n        ? CONTRAST_STANDARDS.AA_LARGE\n        : CONTRAST_STANDARDS.AA_NORMAL;\n\n  const isValid = ratio >= requiredRatio;\n\n  let level: 'AA' | 'AAA' | 'FAIL' = 'FAIL';\n  if (\n    ratio >= CONTRAST_STANDARDS.AAA_NORMAL ||\n    (isLargeText && ratio >= CONTRAST_STANDARDS.AAA_LARGE)\n  ) {\n    level = 'AAA';\n  } else if (\n    ratio >= CONTRAST_STANDARDS.AA_NORMAL ||\n    (isLargeText && ratio >= CONTRAST_STANDARDS.AA_LARGE)\n  ) {\n    level = 'AA';\n  }\n\n  let recommendation: string | undefined;\n  if (!isValid) {\n    const deficit = requiredRatio - ratio;\n    recommendation =\n      `Contrast ratio ${ratio.toFixed(2)}:1 is below ${targetLevel} standard (${requiredRatio}:1). ` +\n      `Increase contrast by ${deficit.toFixed(2)} to meet accessibility requirements.`;\n  }\n\n  return {\n    ratio,\n    isValid,\n    level,\n    recommendation,\n  };\n};\n\n/**\n * Darken a color by a percentage\n */\nexport const darkenColor = (hex: string, percentage: number): string => {\n  const rgb = hexToRgb(hex);\n  if (!rgb) return hex;\n\n  const factor = 1 - percentage / 100;\n  return rgbToHex(\n    Math.round(rgb.r * factor),\n    Math.round(rgb.g * factor),\n    Math.round(rgb.b * factor),\n  );\n};\n\n/**\n * Lighten a color by a percentage\n */\nexport const lightenColor = (hex: string, percentage: number): string => {\n  const rgb = hexToRgb(hex);\n  if (!rgb) return hex;\n\n  const factor = percentage / 100;\n  return rgbToHex(\n    Math.round(rgb.r + (255 - rgb.r) * factor),\n    Math.round(rgb.g + (255 - rgb.g) * factor),\n    Math.round(rgb.b + (255 - rgb.b) * factor),\n  );\n};\n\n/**\n * Automatically fix contrast by adjusting colors\n */\nexport const fixContrast = (\n  foreground: string,\n  background: string,\n  targetRatio: number = CONTRAST_STANDARDS.AA_NORMAL,\n  adjustBackground: boolean = false,\n): { foreground: string; background: string; ratio: number } => {\n  const currentForeground = foreground;\n  const currentBackground = background;\n  const currentRatio = getContrastRatio(currentForeground, currentBackground);\n\n  // If already meets target, return as-is\n  if (currentRatio >= targetRatio) {\n    return {\n      foreground: currentForeground,\n      background: currentBackground,\n      ratio: currentRatio,\n    };\n  }\n\n  // Determine which color to adjust\n  const foregroundLuminance = getRelativeLuminance(foreground);\n  const backgroundLuminance = getRelativeLuminance(background);\n\n  if (adjustBackground) {\n    // Adjust background color\n    if (foregroundLuminance > backgroundLuminance) {\n      // Darken background\n      for (let i = 5; i <= 80; i += 5) {\n        const testBackground = darkenColor(background, i);\n        const testRatio = getContrastRatio(foreground, testBackground);\n        if (testRatio >= targetRatio) {\n          return { foreground, background: testBackground, ratio: testRatio };\n        }\n      }\n    } else {\n      // Lighten background\n      for (let i = 5; i <= 80; i += 5) {\n        const testBackground = lightenColor(background, i);\n        const testRatio = getContrastRatio(foreground, testBackground);\n        if (testRatio >= targetRatio) {\n          return { foreground, background: testBackground, ratio: testRatio };\n        }\n      }\n    }\n  } else {\n    // Adjust foreground color\n    if (foregroundLuminance > backgroundLuminance) {\n      // Lighten foreground\n      for (let i = 5; i <= 80; i += 5) {\n        const testForeground = lightenColor(foreground, i);\n        const testRatio = getContrastRatio(testForeground, background);\n        if (testRatio >= targetRatio) {\n          return { foreground: testForeground, background, ratio: testRatio };\n        }\n      }\n    } else {\n      // Darken foreground\n      for (let i = 5; i <= 80; i += 5) {\n        const testForeground = darkenColor(foreground, i);\n        const testRatio = getContrastRatio(testForeground, background);\n        if (testRatio >= targetRatio) {\n          return { foreground: testForeground, background, ratio: testRatio };\n        }\n      }\n    }\n  }\n\n  // If we can't fix it by adjusting one color, try extreme values\n  if (foregroundLuminance > backgroundLuminance) {\n    return {\n      foreground: '#FFFFFF',\n      background: darkenColor(background, 60),\n      ratio: getContrastRatio('#FFFFFF', darkenColor(background, 60)),\n    };\n  } else {\n    return {\n      foreground: darkenColor(foreground, 60),\n      background: '#FFFFFF',\n      ratio: getContrastRatio(darkenColor(foreground, 60), '#FFFFFF'),\n    };\n  }\n};\n\n/**\n * Get WCAG-compliant color suggestions for common button states\n */\nexport const getButtonColorSuggestions = (brandColor: string) => {\n  const suggestions = {\n    primary: {\n      background: brandColor,\n      text: '#FFFFFF',\n    },\n    secondary: {\n      background: 'transparent',\n      text: brandColor,\n      border: brandColor,\n    },\n    disabled: {\n      background: '#F3F4F6',\n      text: '#9CA3AF',\n    },\n  };\n\n  // Validate and fix primary button\n  const primaryValidation = validateContrast(\n    suggestions.primary.text,\n    suggestions.primary.background,\n  );\n  if (!primaryValidation.isValid) {\n    const fixed = fixContrast(\n      suggestions.primary.text,\n      suggestions.primary.background,\n    );\n    suggestions.primary.background = fixed.background;\n    suggestions.primary.text = fixed.foreground;\n  }\n\n  // Validate and fix secondary button\n  const secondaryValidation = validateContrast(\n    suggestions.secondary.text,\n    '#FFFFFF',\n  ); // Assuming white background\n  if (!secondaryValidation.isValid) {\n    const fixed = fixContrast(suggestions.secondary.text, '#FFFFFF');\n    suggestions.secondary.text = fixed.foreground;\n    suggestions.secondary.border = fixed.foreground;\n  }\n\n  return suggestions;\n};\n\nexport default {\n  CONTRAST_STANDARDS,\n  hexToRgb,\n  rgbToHex,\n  getRelativeLuminance,\n  getContrastRatio,\n  validateContrast,\n  darkenColor,\n  lightenColor,\n  fixContrast,\n  getButtonColorSuggestions,\n};\n"], "mappings": ";;;;AAgBO,IAAMA,kBAAkB,GAAAC,OAAA,CAAAD,kBAAA,GAAG;EAChCE,SAAS,EAAE,GAAG;EACdC,QAAQ,EAAE,GAAG;EACbC,UAAU,EAAE,GAAG;EACfC,SAAS,EAAE,GAAG;EACdC,QAAQ,EAAE;AACZ,CAAU;AAiBH,IAAMC,QAAQ,GAAAN,OAAA,CAAAM,QAAA,GAAG,SAAXA,QAAQA,CACnBC,GAAW,EACoC;EAC/C,IAAMC,MAAM,GAAG,2CAA2C,CAACC,IAAI,CAACF,GAAG,CAAC;EACpE,OAAOC,MAAM,GACT;IACEE,CAAC,EAAEC,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC1BI,CAAC,EAAED,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC1BK,CAAC,EAAEF,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE;EAC3B,CAAC,GACD,IAAI;AACV,CAAC;AAKM,IAAMM,QAAQ,GAAAd,OAAA,CAAAc,QAAA,GAAG,SAAXA,QAAQA,CAAIJ,CAAS,EAAEE,CAAS,EAAEC,CAAS,EAAa;EACnE,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAKH,CAAC,IAAI,EAAE,CAAC,IAAIE,CAAC,IAAI,CAAC,CAAC,GAAGC,CAAC,EAAEE,QAAQ,CAAC,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;AAC3E,CAAC;AAKM,IAAMC,oBAAoB,GAAAjB,OAAA,CAAAiB,oBAAA,GAAG,SAAvBA,oBAAoBA,CAAIV,GAAW,EAAa;EAC3D,IAAMW,GAAG,GAAGZ,QAAQ,CAACC,GAAG,CAAC;EACzB,IAAI,CAACW,GAAG,EAAE,OAAO,CAAC;EAElB,IAAQR,CAAC,GAAWQ,GAAG,CAAfR,CAAC;IAAEE,CAAC,GAAQM,GAAG,CAAZN,CAAC;IAAEC,CAAC,GAAKK,GAAG,CAATL,CAAC;EAGf,IAAMM,KAAK,GAAGT,CAAC,GAAG,GAAG;EACrB,IAAMU,KAAK,GAAGR,CAAC,GAAG,GAAG;EACrB,IAAMS,KAAK,GAAGR,CAAC,GAAG,GAAG;EAGrB,IAAMS,OAAO,GACXH,KAAK,IAAI,OAAO,GAAGA,KAAK,GAAG,KAAK,GAAGI,IAAI,CAACC,GAAG,CAAC,CAACL,KAAK,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;EAC3E,IAAMM,OAAO,GACXL,KAAK,IAAI,OAAO,GAAGA,KAAK,GAAG,KAAK,GAAGG,IAAI,CAACC,GAAG,CAAC,CAACJ,KAAK,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;EAC3E,IAAMM,OAAO,GACXL,KAAK,IAAI,OAAO,GAAGA,KAAK,GAAG,KAAK,GAAGE,IAAI,CAACC,GAAG,CAAC,CAACH,KAAK,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;EAG3E,OAAO,MAAM,GAAGC,OAAO,GAAG,MAAM,GAAGG,OAAO,GAAG,MAAM,GAAGC,OAAO;AAC/D,CAAC;AAKM,IAAMC,gBAAgB,GAAA3B,OAAA,CAAA2B,gBAAA,GAAG,SAAnBA,gBAAgBA,CAC3BC,UAAkB,EAClBC,UAAkB,EACP;EACX,IAAMC,EAAE,GAAGb,oBAAoB,CAACW,UAAU,CAAC;EAC3C,IAAMG,EAAE,GAAGd,oBAAoB,CAACY,UAAU,CAAC;EAE3C,IAAMG,OAAO,GAAGT,IAAI,CAACU,GAAG,CAACH,EAAE,EAAEC,EAAE,CAAC;EAChC,IAAMG,MAAM,GAAGX,IAAI,CAACY,GAAG,CAACL,EAAE,EAAEC,EAAE,CAAC;EAE/B,OAAO,CAACC,OAAO,GAAG,IAAI,KAAKE,MAAM,GAAG,IAAI,CAAC;AAC3C,CAAC;AAKM,IAAME,gBAAgB,GAAApC,OAAA,CAAAoC,gBAAA,GAAG,SAAnBA,gBAAgBA,CAC3BR,UAAkB,EAClBC,UAAkB,EAGW;EAAA,IAF7BQ,WAAoB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAAA,IAC5BG,WAAyB,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAEhC,IAAMI,KAAK,GAAGf,gBAAgB,CAACC,UAAU,EAAEC,UAAU,CAAC;EAEtD,IAAMc,aAAa,GACjBF,WAAW,KAAK,KAAK,GACjBJ,WAAW,GACTtC,kBAAkB,CAACK,SAAS,GAC5BL,kBAAkB,CAACI,UAAU,GAC/BkC,WAAW,GACTtC,kBAAkB,CAACG,QAAQ,GAC3BH,kBAAkB,CAACE,SAAS;EAEpC,IAAM2C,OAAO,GAAGF,KAAK,IAAIC,aAAa;EAEtC,IAAIE,KAA4B,GAAG,MAAM;EACzC,IACEH,KAAK,IAAI3C,kBAAkB,CAACI,UAAU,IACrCkC,WAAW,IAAIK,KAAK,IAAI3C,kBAAkB,CAACK,SAAU,EACtD;IACAyC,KAAK,GAAG,KAAK;EACf,CAAC,MAAM,IACLH,KAAK,IAAI3C,kBAAkB,CAACE,SAAS,IACpCoC,WAAW,IAAIK,KAAK,IAAI3C,kBAAkB,CAACG,QAAS,EACrD;IACA2C,KAAK,GAAG,IAAI;EACd;EAEA,IAAIC,cAAkC;EACtC,IAAI,CAACF,OAAO,EAAE;IACZ,IAAMG,OAAO,GAAGJ,aAAa,GAAGD,KAAK;IACrCI,cAAc,GACZ,kBAAkBJ,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,eAAeP,WAAW,cAAcE,aAAa,OAAO,GAC9F,wBAAwBI,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC,sCAAsC;EACpF;EAEA,OAAO;IACLN,KAAK,EAALA,KAAK;IACLE,OAAO,EAAPA,OAAO;IACPC,KAAK,EAALA,KAAK;IACLC,cAAc,EAAdA;EACF,CAAC;AACH,CAAC;AAKM,IAAMG,WAAW,GAAAjD,OAAA,CAAAiD,WAAA,GAAG,SAAdA,WAAWA,CAAI1C,GAAW,EAAE2C,UAAkB,EAAa;EACtE,IAAMhC,GAAG,GAAGZ,QAAQ,CAACC,GAAG,CAAC;EACzB,IAAI,CAACW,GAAG,EAAE,OAAOX,GAAG;EAEpB,IAAM4C,MAAM,GAAG,CAAC,GAAGD,UAAU,GAAG,GAAG;EACnC,OAAOpC,QAAQ,CACbS,IAAI,CAAC6B,KAAK,CAAClC,GAAG,CAACR,CAAC,GAAGyC,MAAM,CAAC,EAC1B5B,IAAI,CAAC6B,KAAK,CAAClC,GAAG,CAACN,CAAC,GAAGuC,MAAM,CAAC,EAC1B5B,IAAI,CAAC6B,KAAK,CAAClC,GAAG,CAACL,CAAC,GAAGsC,MAAM,CAC3B,CAAC;AACH,CAAC;AAKM,IAAME,YAAY,GAAArD,OAAA,CAAAqD,YAAA,GAAG,SAAfA,YAAYA,CAAI9C,GAAW,EAAE2C,UAAkB,EAAa;EACvE,IAAMhC,GAAG,GAAGZ,QAAQ,CAACC,GAAG,CAAC;EACzB,IAAI,CAACW,GAAG,EAAE,OAAOX,GAAG;EAEpB,IAAM4C,MAAM,GAAGD,UAAU,GAAG,GAAG;EAC/B,OAAOpC,QAAQ,CACbS,IAAI,CAAC6B,KAAK,CAAClC,GAAG,CAACR,CAAC,GAAG,CAAC,GAAG,GAAGQ,GAAG,CAACR,CAAC,IAAIyC,MAAM,CAAC,EAC1C5B,IAAI,CAAC6B,KAAK,CAAClC,GAAG,CAACN,CAAC,GAAG,CAAC,GAAG,GAAGM,GAAG,CAACN,CAAC,IAAIuC,MAAM,CAAC,EAC1C5B,IAAI,CAAC6B,KAAK,CAAClC,GAAG,CAACL,CAAC,GAAG,CAAC,GAAG,GAAGK,GAAG,CAACL,CAAC,IAAIsC,MAAM,CAC3C,CAAC;AACH,CAAC;AAKM,IAAMG,WAAW,GAAAtD,OAAA,CAAAsD,WAAA,GAAG,SAAdA,WAAWA,CACtB1B,UAAkB,EAClBC,UAAkB,EAG4C;EAAA,IAF9D0B,WAAmB,GAAAjB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGvC,kBAAkB,CAACE,SAAS;EAAA,IAClDuD,gBAAyB,GAAAlB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAEjC,IAAMmB,iBAAiB,GAAG7B,UAAU;EACpC,IAAM8B,iBAAiB,GAAG7B,UAAU;EACpC,IAAM8B,YAAY,GAAGhC,gBAAgB,CAAC8B,iBAAiB,EAAEC,iBAAiB,CAAC;EAG3E,IAAIC,YAAY,IAAIJ,WAAW,EAAE;IAC/B,OAAO;MACL3B,UAAU,EAAE6B,iBAAiB;MAC7B5B,UAAU,EAAE6B,iBAAiB;MAC7BhB,KAAK,EAAEiB;IACT,CAAC;EACH;EAGA,IAAMC,mBAAmB,GAAG3C,oBAAoB,CAACW,UAAU,CAAC;EAC5D,IAAMiC,mBAAmB,GAAG5C,oBAAoB,CAACY,UAAU,CAAC;EAE5D,IAAI2B,gBAAgB,EAAE;IAEpB,IAAII,mBAAmB,GAAGC,mBAAmB,EAAE;MAE7C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAE;QAC/B,IAAMC,cAAc,GAAGd,WAAW,CAACpB,UAAU,EAAEiC,CAAC,CAAC;QACjD,IAAME,SAAS,GAAGrC,gBAAgB,CAACC,UAAU,EAAEmC,cAAc,CAAC;QAC9D,IAAIC,SAAS,IAAIT,WAAW,EAAE;UAC5B,OAAO;YAAE3B,UAAU,EAAVA,UAAU;YAAEC,UAAU,EAAEkC,cAAc;YAAErB,KAAK,EAAEsB;UAAU,CAAC;QACrE;MACF;IACF,CAAC,MAAM;MAEL,KAAK,IAAIF,EAAC,GAAG,CAAC,EAAEA,EAAC,IAAI,EAAE,EAAEA,EAAC,IAAI,CAAC,EAAE;QAC/B,IAAMC,eAAc,GAAGV,YAAY,CAACxB,UAAU,EAAEiC,EAAC,CAAC;QAClD,IAAME,UAAS,GAAGrC,gBAAgB,CAACC,UAAU,EAAEmC,eAAc,CAAC;QAC9D,IAAIC,UAAS,IAAIT,WAAW,EAAE;UAC5B,OAAO;YAAE3B,UAAU,EAAVA,UAAU;YAAEC,UAAU,EAAEkC,eAAc;YAAErB,KAAK,EAAEsB;UAAU,CAAC;QACrE;MACF;IACF;EACF,CAAC,MAAM;IAEL,IAAIJ,mBAAmB,GAAGC,mBAAmB,EAAE;MAE7C,KAAK,IAAIC,GAAC,GAAG,CAAC,EAAEA,GAAC,IAAI,EAAE,EAAEA,GAAC,IAAI,CAAC,EAAE;QAC/B,IAAMG,cAAc,GAAGZ,YAAY,CAACzB,UAAU,EAAEkC,GAAC,CAAC;QAClD,IAAME,WAAS,GAAGrC,gBAAgB,CAACsC,cAAc,EAAEpC,UAAU,CAAC;QAC9D,IAAImC,WAAS,IAAIT,WAAW,EAAE;UAC5B,OAAO;YAAE3B,UAAU,EAAEqC,cAAc;YAAEpC,UAAU,EAAVA,UAAU;YAAEa,KAAK,EAAEsB;UAAU,CAAC;QACrE;MACF;IACF,CAAC,MAAM;MAEL,KAAK,IAAIF,GAAC,GAAG,CAAC,EAAEA,GAAC,IAAI,EAAE,EAAEA,GAAC,IAAI,CAAC,EAAE;QAC/B,IAAMG,eAAc,GAAGhB,WAAW,CAACrB,UAAU,EAAEkC,GAAC,CAAC;QACjD,IAAME,WAAS,GAAGrC,gBAAgB,CAACsC,eAAc,EAAEpC,UAAU,CAAC;QAC9D,IAAImC,WAAS,IAAIT,WAAW,EAAE;UAC5B,OAAO;YAAE3B,UAAU,EAAEqC,eAAc;YAAEpC,UAAU,EAAVA,UAAU;YAAEa,KAAK,EAAEsB;UAAU,CAAC;QACrE;MACF;IACF;EACF;EAGA,IAAIJ,mBAAmB,GAAGC,mBAAmB,EAAE;IAC7C,OAAO;MACLjC,UAAU,EAAE,SAAS;MACrBC,UAAU,EAAEoB,WAAW,CAACpB,UAAU,EAAE,EAAE,CAAC;MACvCa,KAAK,EAAEf,gBAAgB,CAAC,SAAS,EAAEsB,WAAW,CAACpB,UAAU,EAAE,EAAE,CAAC;IAChE,CAAC;EACH,CAAC,MAAM;IACL,OAAO;MACLD,UAAU,EAAEqB,WAAW,CAACrB,UAAU,EAAE,EAAE,CAAC;MACvCC,UAAU,EAAE,SAAS;MACrBa,KAAK,EAAEf,gBAAgB,CAACsB,WAAW,CAACrB,UAAU,EAAE,EAAE,CAAC,EAAE,SAAS;IAChE,CAAC;EACH;AACF,CAAC;AAKM,IAAMsC,yBAAyB,GAAAlE,OAAA,CAAAkE,yBAAA,GAAG,SAA5BA,yBAAyBA,CAAIC,UAAkB,EAAK;EAC/D,IAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE;MACPxC,UAAU,EAAEsC,UAAU;MACtBG,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACT1C,UAAU,EAAE,aAAa;MACzByC,IAAI,EAAEH,UAAU;MAChBK,MAAM,EAAEL;IACV,CAAC;IACDM,QAAQ,EAAE;MACR5C,UAAU,EAAE,SAAS;MACrByC,IAAI,EAAE;IACR;EACF,CAAC;EAGD,IAAMI,iBAAiB,GAAGtC,gBAAgB,CACxCgC,WAAW,CAACC,OAAO,CAACC,IAAI,EACxBF,WAAW,CAACC,OAAO,CAACxC,UACtB,CAAC;EACD,IAAI,CAAC6C,iBAAiB,CAAC9B,OAAO,EAAE;IAC9B,IAAM+B,KAAK,GAAGrB,WAAW,CACvBc,WAAW,CAACC,OAAO,CAACC,IAAI,EACxBF,WAAW,CAACC,OAAO,CAACxC,UACtB,CAAC;IACDuC,WAAW,CAACC,OAAO,CAACxC,UAAU,GAAG8C,KAAK,CAAC9C,UAAU;IACjDuC,WAAW,CAACC,OAAO,CAACC,IAAI,GAAGK,KAAK,CAAC/C,UAAU;EAC7C;EAGA,IAAMgD,mBAAmB,GAAGxC,gBAAgB,CAC1CgC,WAAW,CAACG,SAAS,CAACD,IAAI,EAC1B,SACF,CAAC;EACD,IAAI,CAACM,mBAAmB,CAAChC,OAAO,EAAE;IAChC,IAAM+B,MAAK,GAAGrB,WAAW,CAACc,WAAW,CAACG,SAAS,CAACD,IAAI,EAAE,SAAS,CAAC;IAChEF,WAAW,CAACG,SAAS,CAACD,IAAI,GAAGK,MAAK,CAAC/C,UAAU;IAC7CwC,WAAW,CAACG,SAAS,CAACC,MAAM,GAAGG,MAAK,CAAC/C,UAAU;EACjD;EAEA,OAAOwC,WAAW;AACpB,CAAC;AAAC,IAAAS,QAAA,GAAA7E,OAAA,CAAA8E,OAAA,GAEa;EACb/E,kBAAkB,EAAlBA,kBAAkB;EAClBO,QAAQ,EAARA,QAAQ;EACRQ,QAAQ,EAARA,QAAQ;EACRG,oBAAoB,EAApBA,oBAAoB;EACpBU,gBAAgB,EAAhBA,gBAAgB;EAChBS,gBAAgB,EAAhBA,gBAAgB;EAChBa,WAAW,EAAXA,WAAW;EACXI,YAAY,EAAZA,YAAY;EACZC,WAAW,EAAXA,WAAW;EACXY,yBAAyB,EAAzBA;AACF,CAAC", "ignoreList": []}