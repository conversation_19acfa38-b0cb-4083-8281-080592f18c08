/**
 * Error Prevention System
 *
 * Comprehensive error prevention and validation system.
 * Implements proactive error prevention, real-time validation, and user guidance.
 *
 * Features:
 * - Real-time form validation
 * - Input constraints and formatting
 * - Proactive error messages
 * - Smart defaults and suggestions
 * - Contextual help and guidance
 * - Progressive disclosure of complexity
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Ionicons } from '@expo/vector-icons';
import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useEffect,
} from 'react';
import { View, StyleSheet } from 'react-native';

import { useTheme } from '../../contexts/ThemeContext';
import { Text } from '../atoms/Text';

export interface ValidationRule {
  id: string;
  field: string;
  type: 'required' | 'email' | 'phone' | 'password' | 'custom';
  message: string;
  validator?: (value: any) => boolean;
  severity: 'error' | 'warning' | 'info';
  preventSubmit?: boolean;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
  suggestions: string[];
}

export interface ValidationError {
  field: string;
  message: string;
  severity: 'error' | 'warning' | 'info';
  code?: string;
}

export interface ErrorPreventionState {
  validationRules: ValidationRule[];
  fieldErrors: Map<string, ValidationError[]>;
  formValid: boolean;
  preventSubmit: boolean;
  suggestions: Map<string, string[]>;
}

interface ErrorPreventionContextType {
  state: ErrorPreventionState;
  addValidationRule: (rule: ValidationRule) => void;
  removeValidationRule: (ruleId: string) => void;
  validateField: (field: string, value: any) => ValidationResult;
  validateForm: (formData: Record<string, any>) => ValidationResult;
  clearFieldErrors: (field: string) => void;
  clearAllErrors: () => void;
  getFieldErrors: (field: string) => ValidationError[];
  getFieldSuggestions: (field: string) => string[];
  isFieldValid: (field: string) => boolean;
  canSubmitForm: () => boolean;
}

const ErrorPreventionContext = createContext<ErrorPreventionContextType | null>(
  null,
);

export const useErrorPrevention = () => {
  const context = useContext(ErrorPreventionContext);
  if (!context) {
    throw new Error(
      'useErrorPrevention must be used within ErrorPreventionProvider',
    );
  }
  return context;
};

interface ErrorPreventionProviderProps {
  children: React.ReactNode;
}

export const ErrorPreventionProvider: React.FC<
  ErrorPreventionProviderProps
> = ({ children }) => {
  const [state, setState] = useState<ErrorPreventionState>({
    validationRules: [],
    fieldErrors: new Map(),
    formValid: true,
    preventSubmit: false,
    suggestions: new Map(),
  });

  const addValidationRule = useCallback((rule: ValidationRule) => {
    setState(prev => ({
      ...prev,
      validationRules: [
        ...prev.validationRules.filter(r => r.id !== rule.id),
        rule,
      ],
    }));
  }, []);

  const removeValidationRule = useCallback((ruleId: string) => {
    setState(prev => ({
      ...prev,
      validationRules: prev.validationRules.filter(r => r.id !== ruleId),
    }));
  }, []);

  const validateField = useCallback(
    (field: string, value: any): ValidationResult => {
      const fieldRules = state.validationRules.filter(
        rule => rule.field === field,
      );
      const errors: ValidationError[] = [];
      const warnings: ValidationError[] = [];
      const suggestions: string[] = [];

      fieldRules.forEach(rule => {
        let isValid = true;

        switch (rule.type) {
          case 'required':
            isValid = value !== null && value !== undefined && value !== '';
            break;
          case 'email':
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            isValid = !value || emailRegex.test(value);
            if (value && !isValid) {
              suggestions.push(
                'Please enter a valid email address (e.g., <EMAIL>)',
              );
            }
            break;
          case 'phone':
            const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
            isValid = !value || phoneRegex.test(value);
            if (value && !isValid) {
              suggestions.push(
                'Please enter a valid phone number with area code',
              );
            }
            break;
          case 'password':
            if (value) {
              const hasMinLength = value.length >= 8;
              const hasUpperCase = /[A-Z]/.test(value);
              const hasLowerCase = /[a-z]/.test(value);
              const hasNumbers = /\d/.test(value);
              const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);

              isValid =
                hasMinLength && hasUpperCase && hasLowerCase && hasNumbers;

              if (!hasMinLength)
                suggestions.push('Password must be at least 8 characters long');
              if (!hasUpperCase)
                suggestions.push(
                  'Password must contain at least one uppercase letter',
                );
              if (!hasLowerCase)
                suggestions.push(
                  'Password must contain at least one lowercase letter',
                );
              if (!hasNumbers)
                suggestions.push('Password must contain at least one number');
              if (!hasSpecialChar)
                suggestions.push(
                  'Consider adding special characters for stronger security',
                );
            }
            break;
          case 'custom':
            isValid = rule.validator ? rule.validator(value) : true;
            break;
        }

        if (!isValid) {
          const error: ValidationError = {
            field,
            message: rule.message,
            severity: rule.severity,
            code: rule.id,
          };

          if (rule.severity === 'error') {
            errors.push(error);
          } else if (rule.severity === 'warning') {
            warnings.push(error);
          }
        }
      });

      // Update field errors
      setState(prev => {
        const newFieldErrors = new Map(prev.fieldErrors);
        const newSuggestions = new Map(prev.suggestions);

        if (errors.length > 0 || warnings.length > 0) {
          newFieldErrors.set(field, [...errors, ...warnings]);
        } else {
          newFieldErrors.delete(field);
        }

        if (suggestions.length > 0) {
          newSuggestions.set(field, suggestions);
        } else {
          newSuggestions.delete(field);
        }

        const hasErrors = Array.from(newFieldErrors.values()).some(
          fieldErrors => fieldErrors.some(error => error.severity === 'error'),
        );

        return {
          ...prev,
          fieldErrors: newFieldErrors,
          suggestions: newSuggestions,
          formValid: !hasErrors,
          preventSubmit: hasErrors,
        };
      });

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        suggestions,
      };
    },
    [state.validationRules],
  );

  const validateForm = useCallback(
    (formData: Record<string, any>): ValidationResult => {
      const allErrors: ValidationError[] = [];
      const allWarnings: ValidationError[] = [];
      const allSuggestions: string[] = [];

      Object.entries(formData).forEach(([field, value]) => {
        const result = validateField(field, value);
        allErrors.push(...result.errors);
        allWarnings.push(...result.warnings);
        allSuggestions.push(...result.suggestions);
      });

      return {
        isValid: allErrors.length === 0,
        errors: allErrors,
        warnings: allWarnings,
        suggestions: allSuggestions,
      };
    },
    [validateField],
  );

  const clearFieldErrors = useCallback((field: string) => {
    setState(prev => {
      const newFieldErrors = new Map(prev.fieldErrors);
      const newSuggestions = new Map(prev.suggestions);
      newFieldErrors.delete(field);
      newSuggestions.delete(field);

      const hasErrors = Array.from(newFieldErrors.values()).some(fieldErrors =>
        fieldErrors.some(error => error.severity === 'error'),
      );

      return {
        ...prev,
        fieldErrors: newFieldErrors,
        suggestions: newSuggestions,
        formValid: !hasErrors,
        preventSubmit: hasErrors,
      };
    });
  }, []);

  const clearAllErrors = useCallback(() => {
    setState(prev => ({
      ...prev,
      fieldErrors: new Map(),
      suggestions: new Map(),
      formValid: true,
      preventSubmit: false,
    }));
  }, []);

  const getFieldErrors = useCallback(
    (field: string): ValidationError[] => {
      return state.fieldErrors.get(field) || [];
    },
    [state.fieldErrors],
  );

  const getFieldSuggestions = useCallback(
    (field: string): string[] => {
      return state.suggestions.get(field) || [];
    },
    [state.suggestions],
  );

  const isFieldValid = useCallback(
    (field: string): boolean => {
      const errors = state.fieldErrors.get(field) || [];
      return !errors.some(error => error.severity === 'error');
    },
    [state.fieldErrors],
  );

  const canSubmitForm = useCallback((): boolean => {
    return state.formValid && !state.preventSubmit;
  }, [state.formValid, state.preventSubmit]);

  const contextValue: ErrorPreventionContextType = {
    state,
    addValidationRule,
    removeValidationRule,
    validateField,
    validateForm,
    clearFieldErrors,
    clearAllErrors,
    getFieldErrors,
    getFieldSuggestions,
    isFieldValid,
    canSubmitForm,
  };

  return (
    <ErrorPreventionContext.Provider value={contextValue}>
      {children}
    </ErrorPreventionContext.Provider>
  );
};

// Validation Error Display Component
interface ValidationErrorDisplayProps {
  field: string;
  showSuggestions?: boolean;
  style?: any;
}

export const ValidationErrorDisplay: React.FC<ValidationErrorDisplayProps> = ({
  field,
  showSuggestions = true,
  style,
}) => {
  const { colors } = useTheme();
  const { getFieldErrors, getFieldSuggestions } = useErrorPrevention();

  const errors = getFieldErrors(field);
  const suggestions = getFieldSuggestions(field);

  if (errors.length === 0 && suggestions.length === 0) {
    return null;
  }

  return (
    <View style={[styles.container, style]}>
      {/* Errors */}
      {errors.map((error, index) => (
        <View key={index} style={styles.errorRow}>
          <Ionicons
            name={error.severity === 'error' ? 'close-circle' : 'warning'}
            size={16}
            color={
              error.severity === 'error'
                ? colors.error || '#DC2626'
                : colors.warning || '#F59E0B'
            }
          />
          <Text
            variant="caption"
            color={error.severity === 'error' ? 'error' : 'warning'}
            style={styles.errorText}>
            {error.message}
          </Text>
        </View>
      ))}

      {/* Suggestions */}
      {showSuggestions &&
        suggestions.map((suggestion, index) => (
          <View key={`suggestion-${index}`} style={styles.suggestionRow}>
            <Ionicons
              name="bulb-outline"
              size={16}
              color={colors.accent[400]}
            />
            <Text
              variant="caption"
              color="secondary"
              style={styles.suggestionText}>
              {suggestion}
            </Text>
          </View>
        ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 4,
  },
  errorRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  suggestionRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  errorText: {
    marginLeft: 6,
    flex: 1,
  },
  suggestionText: {
    marginLeft: 6,
    flex: 1,
  },
});
