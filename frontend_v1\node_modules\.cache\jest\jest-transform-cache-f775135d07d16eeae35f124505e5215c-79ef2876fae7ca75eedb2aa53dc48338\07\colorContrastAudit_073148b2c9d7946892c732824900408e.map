{"version": 3, "names": ["WCAG_CONTRAST_RATIOS", "exports", "AA", "normalText", "largeText", "uiComponents", "AAA", "TEXT_SIZE_THRESHOLDS", "fontSize", "fontWeight", "largeBoldText", "hexToRgb", "hex", "result", "exec", "r", "parseInt", "g", "b", "getRelativeLuminance", "_map", "map", "c", "Math", "pow", "_map2", "_slicedToArray2", "default", "rs", "gs", "bs", "calculateContrastRatio", "color1", "color2", "rgb1", "rgb2", "Error", "lum1", "lum2", "brightest", "max", "darkest", "min", "auditColorPair", "pair", "ratio", "foreground", "background", "requiredRatio", "isUIComponent", "textSize", "passesAA", "passesAAA", "level", "recommendation", "generateRecommendation", "passes", "currentRatio", "improvement", "auditColorPalette", "colorPairs", "results", "Object", "assign", "audit", "summary", "total", "length", "passing", "filter", "failing", "aaCompliant", "aaaCompliant", "getWCAGCompliantColor", "arguments", "undefined", "fgRgb", "bgRgb", "bgLuminance", "targetLuminance", "factor", "adjustedR", "round", "adjustedG", "adjustedB", "toString", "padStart", "VIERLA_COLOR_PAIRS", "context", "runVierlaContrastAudit", "generateContrastReport", "auditResults", "report", "for<PERSON>ach", "toFixed", "status"], "sources": ["colorContrastAudit.ts"], "sourcesContent": ["/**\n * Color Contrast Audit System\n *\n * Comprehensive system for verifying WCAG 2.2 AA color contrast compliance\n * across all text and UI components in the application.\n *\n * Features:\n * - Automated contrast ratio calculation\n * - WCAG 2.2 AA/AAA compliance checking\n * - Real-time contrast validation\n * - Color palette optimization\n * - Accessibility reporting\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\n// WCAG contrast ratio requirements\nexport const WCAG_CONTRAST_RATIOS = {\n  AA: {\n    normalText: 4.5,\n    largeText: 3.0,\n    uiComponents: 3.0,\n  },\n  AAA: {\n    normalText: 7.0,\n    largeText: 4.5,\n    uiComponents: 4.5,\n  },\n} as const;\n\n// Text size thresholds for WCAG\nexport const TEXT_SIZE_THRESHOLDS = {\n  largeText: {\n    fontSize: 18, // 18px or larger\n    fontWeight: 'normal',\n  },\n  largeBoldText: {\n    fontSize: 14, // 14px or larger if bold\n    fontWeight: 'bold',\n  },\n} as const;\n\n// Color contrast audit result\nexport interface ContrastAuditResult {\n  ratio: number;\n  passes: {\n    AA: boolean;\n    AAA: boolean;\n  };\n  level: 'AA' | 'AAA' | 'FAIL';\n  recommendation?: string;\n}\n\n// Color pair for testing\nexport interface ColorPair {\n  foreground: string;\n  background: string;\n  context: string; // Description of where this pair is used\n  textSize?: 'normal' | 'large';\n  isUIComponent?: boolean;\n}\n\n/**\n * Convert hex color to RGB\n */\nfunction hexToRgb(hex: string): { r: number; g: number; b: number } | null {\n  const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n  return result\n    ? {\n        r: parseInt(result[1], 16),\n        g: parseInt(result[2], 16),\n        b: parseInt(result[3], 16),\n      }\n    : null;\n}\n\n/**\n * Calculate relative luminance of a color\n */\nfunction getRelativeLuminance(r: number, g: number, b: number): number {\n  const [rs, gs, bs] = [r, g, b].map(c => {\n    c = c / 255;\n    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);\n  });\n\n  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;\n}\n\n/**\n * Calculate contrast ratio between two colors\n */\nexport function calculateContrastRatio(color1: string, color2: string): number {\n  const rgb1 = hexToRgb(color1);\n  const rgb2 = hexToRgb(color2);\n\n  if (!rgb1 || !rgb2) {\n    throw new Error('Invalid color format. Please use hex colors.');\n  }\n\n  const lum1 = getRelativeLuminance(rgb1.r, rgb1.g, rgb1.b);\n  const lum2 = getRelativeLuminance(rgb2.r, rgb2.g, rgb2.b);\n\n  const brightest = Math.max(lum1, lum2);\n  const darkest = Math.min(lum1, lum2);\n\n  return (brightest + 0.05) / (darkest + 0.05);\n}\n\n/**\n * Check if color pair meets WCAG requirements\n */\nexport function auditColorPair(pair: ColorPair): ContrastAuditResult {\n  const ratio = calculateContrastRatio(pair.foreground, pair.background);\n\n  let requiredRatio: number;\n\n  if (pair.isUIComponent) {\n    requiredRatio = WCAG_CONTRAST_RATIOS.AA.uiComponents;\n  } else if (pair.textSize === 'large') {\n    requiredRatio = WCAG_CONTRAST_RATIOS.AA.largeText;\n  } else {\n    requiredRatio = WCAG_CONTRAST_RATIOS.AA.normalText;\n  }\n\n  const passesAA = ratio >= requiredRatio;\n  const passesAAA = pair.isUIComponent\n    ? ratio >= WCAG_CONTRAST_RATIOS.AAA.uiComponents\n    : pair.textSize === 'large'\n      ? ratio >= WCAG_CONTRAST_RATIOS.AAA.largeText\n      : ratio >= WCAG_CONTRAST_RATIOS.AAA.normalText;\n\n  let level: 'AA' | 'AAA' | 'FAIL';\n  let recommendation: string | undefined;\n\n  if (passesAAA) {\n    level = 'AAA';\n  } else if (passesAA) {\n    level = 'AA';\n  } else {\n    level = 'FAIL';\n    recommendation = generateRecommendation(pair, ratio, requiredRatio);\n  }\n\n  return {\n    ratio,\n    passes: {\n      AA: passesAA,\n      AAA: passesAAA,\n    },\n    level,\n    recommendation,\n  };\n}\n\n/**\n * Generate recommendation for failing color pairs\n */\nfunction generateRecommendation(\n  pair: ColorPair,\n  currentRatio: number,\n  requiredRatio: number,\n): string {\n  const improvement = requiredRatio / currentRatio;\n\n  if (improvement < 1.2) {\n    return `Slightly adjust the ${pair.foreground} foreground or ${pair.background} background color to improve contrast.`;\n  } else if (improvement < 2) {\n    return `Consider using a darker foreground or lighter background color for better contrast.`;\n  } else {\n    return `Significant color changes needed. Consider using high-contrast color combinations from the design system.`;\n  }\n}\n\n/**\n * Audit entire color palette\n */\nexport function auditColorPalette(colorPairs: ColorPair[]): {\n  results: Array<ColorPair & { audit: ContrastAuditResult }>;\n  summary: {\n    total: number;\n    passing: number;\n    failing: number;\n    aaCompliant: number;\n    aaaCompliant: number;\n  };\n} {\n  const results = colorPairs.map(pair => ({\n    ...pair,\n    audit: auditColorPair(pair),\n  }));\n\n  const summary = {\n    total: results.length,\n    passing: results.filter(r => r.audit.passes.AA).length,\n    failing: results.filter(r => !r.audit.passes.AA).length,\n    aaCompliant: results.filter(\n      r => r.audit.level === 'AA' || r.audit.level === 'AAA',\n    ).length,\n    aaaCompliant: results.filter(r => r.audit.level === 'AAA').length,\n  };\n\n  return { results, summary };\n}\n\n/**\n * Get WCAG compliant color\n */\nexport function getWCAGCompliantColor(\n  foreground: string,\n  background: string,\n  level: 'AA' | 'AAA' = 'AA',\n  textSize: 'normal' | 'large' = 'normal',\n  isUIComponent: boolean = false,\n): string {\n  const currentRatio = calculateContrastRatio(foreground, background);\n\n  let requiredRatio: number;\n  if (isUIComponent) {\n    requiredRatio = WCAG_CONTRAST_RATIOS[level].uiComponents;\n  } else if (textSize === 'large') {\n    requiredRatio = WCAG_CONTRAST_RATIOS[level].largeText;\n  } else {\n    requiredRatio = WCAG_CONTRAST_RATIOS[level].normalText;\n  }\n\n  if (currentRatio >= requiredRatio) {\n    return foreground; // Already compliant\n  }\n\n  // Adjust foreground color to meet requirements\n  const fgRgb = hexToRgb(foreground);\n  const bgRgb = hexToRgb(background);\n\n  if (!fgRgb || !bgRgb) {\n    throw new Error('Invalid color format');\n  }\n\n  const bgLuminance = getRelativeLuminance(bgRgb.r, bgRgb.g, bgRgb.b);\n\n  // Determine if we should make foreground darker or lighter\n  const targetLuminance =\n    bgLuminance > 0.5\n      ? (bgLuminance + 0.05) / requiredRatio - 0.05 // Make darker\n      : (bgLuminance + 0.05) * requiredRatio - 0.05; // Make lighter\n\n  // Adjust RGB values to achieve target luminance\n  const factor = targetLuminance > bgLuminance ? 1.2 : 0.8;\n\n  const adjustedR = Math.min(255, Math.max(0, Math.round(fgRgb.r * factor)));\n  const adjustedG = Math.min(255, Math.max(0, Math.round(fgRgb.g * factor)));\n  const adjustedB = Math.min(255, Math.max(0, Math.round(fgRgb.b * factor)));\n\n  return `#${adjustedR.toString(16).padStart(2, '0')}${adjustedG.toString(16).padStart(2, '0')}${adjustedB.toString(16).padStart(2, '0')}`;\n}\n\n/**\n * Vierla app color pairs for audit\n */\nexport const VIERLA_COLOR_PAIRS: ColorPair[] = [\n  // Primary text combinations\n  {\n    foreground: '#1F2937', // gray-800\n    background: '#FFFFFF', // white\n    context: 'Primary text on white background',\n    textSize: 'normal',\n  },\n  {\n    foreground: '#FFFFFF', // white\n    background: '#1F2937', // gray-800\n    context: 'White text on dark background',\n    textSize: 'normal',\n  },\n\n  // Primary button\n  {\n    foreground: '#FFFFFF', // white\n    background: '#2A4B32', // primary green\n    context: 'Primary button text',\n    textSize: 'normal',\n  },\n\n  // Secondary text\n  {\n    foreground: '#6B7280', // gray-500\n    background: '#FFFFFF', // white\n    context: 'Secondary text on white background',\n    textSize: 'normal',\n  },\n\n  // Error states\n  {\n    foreground: '#DC2626', // red-600\n    background: '#FFFFFF', // white\n    context: 'Error text',\n    textSize: 'normal',\n  },\n\n  // Success states\n  {\n    foreground: '#059669', // green-600\n    background: '#FFFFFF', // white\n    context: 'Success text',\n    textSize: 'normal',\n  },\n\n  // UI Components\n  {\n    foreground: '#2A4B32', // primary\n    background: '#F3F4F6', // gray-100\n    context: 'Primary button outline',\n    isUIComponent: true,\n  },\n\n  // Links\n  {\n    foreground: '#2563EB', // blue-600\n    background: '#FFFFFF', // white\n    context: 'Link text',\n    textSize: 'normal',\n  },\n\n  // Form inputs\n  {\n    foreground: '#1F2937', // gray-800\n    background: '#F9FAFB', // gray-50\n    context: 'Form input text',\n    textSize: 'normal',\n  },\n\n  // Navigation\n  {\n    foreground: '#374151', // gray-700\n    background: '#FFFFFF', // white\n    context: 'Navigation text',\n    textSize: 'normal',\n  },\n];\n\n/**\n * Run complete color contrast audit for Vierla app\n */\nexport function runVierlaContrastAudit() {\n  return auditColorPalette(VIERLA_COLOR_PAIRS);\n}\n\n/**\n * Generate contrast audit report\n */\nexport function generateContrastReport(\n  auditResults: ReturnType<typeof auditColorPalette>,\n): string {\n  const { results, summary } = auditResults;\n\n  let report = `# Color Contrast Audit Report\\n\\n`;\n  report += `## Summary\\n`;\n  report += `- Total color pairs tested: ${summary.total}\\n`;\n  report += `- Passing WCAG AA: ${summary.passing}/${summary.total} (${Math.round((summary.passing / summary.total) * 100)}%)\\n`;\n  report += `- Failing WCAG AA: ${summary.failing}/${summary.total} (${Math.round((summary.failing / summary.total) * 100)}%)\\n`;\n  report += `- WCAG AAA compliant: ${summary.aaaCompliant}/${summary.total} (${Math.round((summary.aaaCompliant / summary.total) * 100)}%)\\n\\n`;\n\n  if (summary.failing > 0) {\n    report += `## Failing Color Pairs\\n\\n`;\n    results\n      .filter(r => !r.audit.passes.AA)\n      .forEach(result => {\n        report += `### ${result.context}\\n`;\n        report += `- Foreground: ${result.foreground}\\n`;\n        report += `- Background: ${result.background}\\n`;\n        report += `- Contrast Ratio: ${result.audit.ratio.toFixed(2)}:1\\n`;\n        report += `- Status: ${result.audit.level}\\n`;\n        if (result.audit.recommendation) {\n          report += `- Recommendation: ${result.audit.recommendation}\\n`;\n        }\n        report += `\\n`;\n      });\n  }\n\n  report += `## All Results\\n\\n`;\n  results.forEach(result => {\n    const status = result.audit.passes.AA ? '✅' : '❌';\n    report += `${status} ${result.context}: ${result.audit.ratio.toFixed(2)}:1 (${result.audit.level})\\n`;\n  });\n\n  return report;\n}\n"], "mappings": ";;;;;;;;;;;;AAkBO,IAAMA,oBAAoB,GAAAC,OAAA,CAAAD,oBAAA,GAAG;EAClCE,EAAE,EAAE;IACFC,UAAU,EAAE,GAAG;IACfC,SAAS,EAAE,GAAG;IACdC,YAAY,EAAE;EAChB,CAAC;EACDC,GAAG,EAAE;IACHH,UAAU,EAAE,GAAG;IACfC,SAAS,EAAE,GAAG;IACdC,YAAY,EAAE;EAChB;AACF,CAAU;AAGH,IAAME,oBAAoB,GAAAN,OAAA,CAAAM,oBAAA,GAAG;EAClCH,SAAS,EAAE;IACTI,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd,CAAC;EACDC,aAAa,EAAE;IACbF,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd;AACF,CAAU;AAyBV,SAASE,QAAQA,CAACC,GAAW,EAA8C;EACzE,IAAMC,MAAM,GAAG,2CAA2C,CAACC,IAAI,CAACF,GAAG,CAAC;EACpE,OAAOC,MAAM,GACT;IACEE,CAAC,EAAEC,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC1BI,CAAC,EAAED,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC1BK,CAAC,EAAEF,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE;EAC3B,CAAC,GACD,IAAI;AACV;AAKA,SAASM,oBAAoBA,CAACJ,CAAS,EAAEE,CAAS,EAAEC,CAAS,EAAU;EACrE,IAAAE,IAAA,GAAqB,CAACL,CAAC,EAAEE,CAAC,EAAEC,CAAC,CAAC,CAACG,GAAG,CAAC,UAAAC,CAAC,EAAI;MACtCA,CAAC,GAAGA,CAAC,GAAG,GAAG;MACX,OAAOA,CAAC,IAAI,OAAO,GAAGA,CAAC,GAAG,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAACF,CAAC,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;IACtE,CAAC,CAAC;IAAAG,KAAA,OAAAC,eAAA,CAAAC,OAAA,EAAAP,IAAA;IAHKQ,EAAE,GAAAH,KAAA;IAAEI,EAAE,GAAAJ,KAAA;IAAEK,EAAE,GAAAL,KAAA;EAKjB,OAAO,MAAM,GAAGG,EAAE,GAAG,MAAM,GAAGC,EAAE,GAAG,MAAM,GAAGC,EAAE;AAChD;AAKO,SAASC,sBAAsBA,CAACC,MAAc,EAAEC,MAAc,EAAU;EAC7E,IAAMC,IAAI,GAAGvB,QAAQ,CAACqB,MAAM,CAAC;EAC7B,IAAMG,IAAI,GAAGxB,QAAQ,CAACsB,MAAM,CAAC;EAE7B,IAAI,CAACC,IAAI,IAAI,CAACC,IAAI,EAAE;IAClB,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;EACjE;EAEA,IAAMC,IAAI,GAAGlB,oBAAoB,CAACe,IAAI,CAACnB,CAAC,EAAEmB,IAAI,CAACjB,CAAC,EAAEiB,IAAI,CAAChB,CAAC,CAAC;EACzD,IAAMoB,IAAI,GAAGnB,oBAAoB,CAACgB,IAAI,CAACpB,CAAC,EAAEoB,IAAI,CAAClB,CAAC,EAAEkB,IAAI,CAACjB,CAAC,CAAC;EAEzD,IAAMqB,SAAS,GAAGhB,IAAI,CAACiB,GAAG,CAACH,IAAI,EAAEC,IAAI,CAAC;EACtC,IAAMG,OAAO,GAAGlB,IAAI,CAACmB,GAAG,CAACL,IAAI,EAAEC,IAAI,CAAC;EAEpC,OAAO,CAACC,SAAS,GAAG,IAAI,KAAKE,OAAO,GAAG,IAAI,CAAC;AAC9C;AAKO,SAASE,cAAcA,CAACC,IAAe,EAAuB;EACnE,IAAMC,KAAK,GAAGd,sBAAsB,CAACa,IAAI,CAACE,UAAU,EAAEF,IAAI,CAACG,UAAU,CAAC;EAEtE,IAAIC,aAAqB;EAEzB,IAAIJ,IAAI,CAACK,aAAa,EAAE;IACtBD,aAAa,GAAGhD,oBAAoB,CAACE,EAAE,CAACG,YAAY;EACtD,CAAC,MAAM,IAAIuC,IAAI,CAACM,QAAQ,KAAK,OAAO,EAAE;IACpCF,aAAa,GAAGhD,oBAAoB,CAACE,EAAE,CAACE,SAAS;EACnD,CAAC,MAAM;IACL4C,aAAa,GAAGhD,oBAAoB,CAACE,EAAE,CAACC,UAAU;EACpD;EAEA,IAAMgD,QAAQ,GAAGN,KAAK,IAAIG,aAAa;EACvC,IAAMI,SAAS,GAAGR,IAAI,CAACK,aAAa,GAChCJ,KAAK,IAAI7C,oBAAoB,CAACM,GAAG,CAACD,YAAY,GAC9CuC,IAAI,CAACM,QAAQ,KAAK,OAAO,GACvBL,KAAK,IAAI7C,oBAAoB,CAACM,GAAG,CAACF,SAAS,GAC3CyC,KAAK,IAAI7C,oBAAoB,CAACM,GAAG,CAACH,UAAU;EAElD,IAAIkD,KAA4B;EAChC,IAAIC,cAAkC;EAEtC,IAAIF,SAAS,EAAE;IACbC,KAAK,GAAG,KAAK;EACf,CAAC,MAAM,IAAIF,QAAQ,EAAE;IACnBE,KAAK,GAAG,IAAI;EACd,CAAC,MAAM;IACLA,KAAK,GAAG,MAAM;IACdC,cAAc,GAAGC,sBAAsB,CAACX,IAAI,EAAEC,KAAK,EAAEG,aAAa,CAAC;EACrE;EAEA,OAAO;IACLH,KAAK,EAALA,KAAK;IACLW,MAAM,EAAE;MACNtD,EAAE,EAAEiD,QAAQ;MACZ7C,GAAG,EAAE8C;IACP,CAAC;IACDC,KAAK,EAALA,KAAK;IACLC,cAAc,EAAdA;EACF,CAAC;AACH;AAKA,SAASC,sBAAsBA,CAC7BX,IAAe,EACfa,YAAoB,EACpBT,aAAqB,EACb;EACR,IAAMU,WAAW,GAAGV,aAAa,GAAGS,YAAY;EAEhD,IAAIC,WAAW,GAAG,GAAG,EAAE;IACrB,OAAO,uBAAuBd,IAAI,CAACE,UAAU,kBAAkBF,IAAI,CAACG,UAAU,wCAAwC;EACxH,CAAC,MAAM,IAAIW,WAAW,GAAG,CAAC,EAAE;IAC1B,OAAO,qFAAqF;EAC9F,CAAC,MAAM;IACL,OAAO,2GAA2G;EACpH;AACF;AAKO,SAASC,iBAAiBA,CAACC,UAAuB,EASvD;EACA,IAAMC,OAAO,GAAGD,UAAU,CAACvC,GAAG,CAAC,UAAAuB,IAAI;IAAA,OAAAkB,MAAA,CAAAC,MAAA,KAC9BnB,IAAI;MACPoB,KAAK,EAAErB,cAAc,CAACC,IAAI;IAAC;EAAA,CAC3B,CAAC;EAEH,IAAMqB,OAAO,GAAG;IACdC,KAAK,EAAEL,OAAO,CAACM,MAAM;IACrBC,OAAO,EAAEP,OAAO,CAACQ,MAAM,CAAC,UAAAtD,CAAC;MAAA,OAAIA,CAAC,CAACiD,KAAK,CAACR,MAAM,CAACtD,EAAE;IAAA,EAAC,CAACiE,MAAM;IACtDG,OAAO,EAAET,OAAO,CAACQ,MAAM,CAAC,UAAAtD,CAAC;MAAA,OAAI,CAACA,CAAC,CAACiD,KAAK,CAACR,MAAM,CAACtD,EAAE;IAAA,EAAC,CAACiE,MAAM;IACvDI,WAAW,EAAEV,OAAO,CAACQ,MAAM,CACzB,UAAAtD,CAAC;MAAA,OAAIA,CAAC,CAACiD,KAAK,CAACX,KAAK,KAAK,IAAI,IAAItC,CAAC,CAACiD,KAAK,CAACX,KAAK,KAAK,KAAK;IAAA,CACxD,CAAC,CAACc,MAAM;IACRK,YAAY,EAAEX,OAAO,CAACQ,MAAM,CAAC,UAAAtD,CAAC;MAAA,OAAIA,CAAC,CAACiD,KAAK,CAACX,KAAK,KAAK,KAAK;IAAA,EAAC,CAACc;EAC7D,CAAC;EAED,OAAO;IAAEN,OAAO,EAAPA,OAAO;IAAEI,OAAO,EAAPA;EAAQ,CAAC;AAC7B;AAKO,SAASQ,qBAAqBA,CACnC3B,UAAkB,EAClBC,UAAkB,EAIV;EAAA,IAHRM,KAAmB,GAAAqB,SAAA,CAAAP,MAAA,QAAAO,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI;EAAA,IAC1BxB,QAA4B,GAAAwB,SAAA,CAAAP,MAAA,QAAAO,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,QAAQ;EAAA,IACvCzB,aAAsB,GAAAyB,SAAA,CAAAP,MAAA,QAAAO,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;EAE9B,IAAMjB,YAAY,GAAG1B,sBAAsB,CAACe,UAAU,EAAEC,UAAU,CAAC;EAEnE,IAAIC,aAAqB;EACzB,IAAIC,aAAa,EAAE;IACjBD,aAAa,GAAGhD,oBAAoB,CAACqD,KAAK,CAAC,CAAChD,YAAY;EAC1D,CAAC,MAAM,IAAI6C,QAAQ,KAAK,OAAO,EAAE;IAC/BF,aAAa,GAAGhD,oBAAoB,CAACqD,KAAK,CAAC,CAACjD,SAAS;EACvD,CAAC,MAAM;IACL4C,aAAa,GAAGhD,oBAAoB,CAACqD,KAAK,CAAC,CAAClD,UAAU;EACxD;EAEA,IAAIsD,YAAY,IAAIT,aAAa,EAAE;IACjC,OAAOF,UAAU;EACnB;EAGA,IAAM8B,KAAK,GAAGjE,QAAQ,CAACmC,UAAU,CAAC;EAClC,IAAM+B,KAAK,GAAGlE,QAAQ,CAACoC,UAAU,CAAC;EAElC,IAAI,CAAC6B,KAAK,IAAI,CAACC,KAAK,EAAE;IACpB,MAAM,IAAIzC,KAAK,CAAC,sBAAsB,CAAC;EACzC;EAEA,IAAM0C,WAAW,GAAG3D,oBAAoB,CAAC0D,KAAK,CAAC9D,CAAC,EAAE8D,KAAK,CAAC5D,CAAC,EAAE4D,KAAK,CAAC3D,CAAC,CAAC;EAGnE,IAAM6D,eAAe,GACnBD,WAAW,GAAG,GAAG,GACb,CAACA,WAAW,GAAG,IAAI,IAAI9B,aAAa,GAAG,IAAI,GAC3C,CAAC8B,WAAW,GAAG,IAAI,IAAI9B,aAAa,GAAG,IAAI;EAGjD,IAAMgC,MAAM,GAAGD,eAAe,GAAGD,WAAW,GAAG,GAAG,GAAG,GAAG;EAExD,IAAMG,SAAS,GAAG1D,IAAI,CAACmB,GAAG,CAAC,GAAG,EAAEnB,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAEjB,IAAI,CAAC2D,KAAK,CAACN,KAAK,CAAC7D,CAAC,GAAGiE,MAAM,CAAC,CAAC,CAAC;EAC1E,IAAMG,SAAS,GAAG5D,IAAI,CAACmB,GAAG,CAAC,GAAG,EAAEnB,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAEjB,IAAI,CAAC2D,KAAK,CAACN,KAAK,CAAC3D,CAAC,GAAG+D,MAAM,CAAC,CAAC,CAAC;EAC1E,IAAMI,SAAS,GAAG7D,IAAI,CAACmB,GAAG,CAAC,GAAG,EAAEnB,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAEjB,IAAI,CAAC2D,KAAK,CAACN,KAAK,CAAC1D,CAAC,GAAG8D,MAAM,CAAC,CAAC,CAAC;EAE1E,OAAO,IAAIC,SAAS,CAACI,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAGH,SAAS,CAACE,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAGF,SAAS,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;AAC1I;AAKO,IAAMC,kBAA+B,GAAAtF,OAAA,CAAAsF,kBAAA,GAAG,CAE7C;EACEzC,UAAU,EAAE,SAAS;EACrBC,UAAU,EAAE,SAAS;EACrByC,OAAO,EAAE,kCAAkC;EAC3CtC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEJ,UAAU,EAAE,SAAS;EACrBC,UAAU,EAAE,SAAS;EACrByC,OAAO,EAAE,+BAA+B;EACxCtC,QAAQ,EAAE;AACZ,CAAC,EAGD;EACEJ,UAAU,EAAE,SAAS;EACrBC,UAAU,EAAE,SAAS;EACrByC,OAAO,EAAE,qBAAqB;EAC9BtC,QAAQ,EAAE;AACZ,CAAC,EAGD;EACEJ,UAAU,EAAE,SAAS;EACrBC,UAAU,EAAE,SAAS;EACrByC,OAAO,EAAE,oCAAoC;EAC7CtC,QAAQ,EAAE;AACZ,CAAC,EAGD;EACEJ,UAAU,EAAE,SAAS;EACrBC,UAAU,EAAE,SAAS;EACrByC,OAAO,EAAE,YAAY;EACrBtC,QAAQ,EAAE;AACZ,CAAC,EAGD;EACEJ,UAAU,EAAE,SAAS;EACrBC,UAAU,EAAE,SAAS;EACrByC,OAAO,EAAE,cAAc;EACvBtC,QAAQ,EAAE;AACZ,CAAC,EAGD;EACEJ,UAAU,EAAE,SAAS;EACrBC,UAAU,EAAE,SAAS;EACrByC,OAAO,EAAE,wBAAwB;EACjCvC,aAAa,EAAE;AACjB,CAAC,EAGD;EACEH,UAAU,EAAE,SAAS;EACrBC,UAAU,EAAE,SAAS;EACrByC,OAAO,EAAE,WAAW;EACpBtC,QAAQ,EAAE;AACZ,CAAC,EAGD;EACEJ,UAAU,EAAE,SAAS;EACrBC,UAAU,EAAE,SAAS;EACrByC,OAAO,EAAE,iBAAiB;EAC1BtC,QAAQ,EAAE;AACZ,CAAC,EAGD;EACEJ,UAAU,EAAE,SAAS;EACrBC,UAAU,EAAE,SAAS;EACrByC,OAAO,EAAE,iBAAiB;EAC1BtC,QAAQ,EAAE;AACZ,CAAC,CACF;AAKM,SAASuC,sBAAsBA,CAAA,EAAG;EACvC,OAAO9B,iBAAiB,CAAC4B,kBAAkB,CAAC;AAC9C;AAKO,SAASG,sBAAsBA,CACpCC,YAAkD,EAC1C;EACR,IAAQ9B,OAAO,GAAc8B,YAAY,CAAjC9B,OAAO;IAAEI,OAAO,GAAK0B,YAAY,CAAxB1B,OAAO;EAExB,IAAI2B,MAAM,GAAG,mCAAmC;EAChDA,MAAM,IAAI,cAAc;EACxBA,MAAM,IAAI,+BAA+B3B,OAAO,CAACC,KAAK,IAAI;EAC1D0B,MAAM,IAAI,sBAAsB3B,OAAO,CAACG,OAAO,IAAIH,OAAO,CAACC,KAAK,KAAK3C,IAAI,CAAC2D,KAAK,CAAEjB,OAAO,CAACG,OAAO,GAAGH,OAAO,CAACC,KAAK,GAAI,GAAG,CAAC,MAAM;EAC9H0B,MAAM,IAAI,sBAAsB3B,OAAO,CAACK,OAAO,IAAIL,OAAO,CAACC,KAAK,KAAK3C,IAAI,CAAC2D,KAAK,CAAEjB,OAAO,CAACK,OAAO,GAAGL,OAAO,CAACC,KAAK,GAAI,GAAG,CAAC,MAAM;EAC9H0B,MAAM,IAAI,yBAAyB3B,OAAO,CAACO,YAAY,IAAIP,OAAO,CAACC,KAAK,KAAK3C,IAAI,CAAC2D,KAAK,CAAEjB,OAAO,CAACO,YAAY,GAAGP,OAAO,CAACC,KAAK,GAAI,GAAG,CAAC,QAAQ;EAE7I,IAAID,OAAO,CAACK,OAAO,GAAG,CAAC,EAAE;IACvBsB,MAAM,IAAI,4BAA4B;IACtC/B,OAAO,CACJQ,MAAM,CAAC,UAAAtD,CAAC;MAAA,OAAI,CAACA,CAAC,CAACiD,KAAK,CAACR,MAAM,CAACtD,EAAE;IAAA,EAAC,CAC/B2F,OAAO,CAAC,UAAAhF,MAAM,EAAI;MACjB+E,MAAM,IAAI,OAAO/E,MAAM,CAAC2E,OAAO,IAAI;MACnCI,MAAM,IAAI,iBAAiB/E,MAAM,CAACiC,UAAU,IAAI;MAChD8C,MAAM,IAAI,iBAAiB/E,MAAM,CAACkC,UAAU,IAAI;MAChD6C,MAAM,IAAI,qBAAqB/E,MAAM,CAACmD,KAAK,CAACnB,KAAK,CAACiD,OAAO,CAAC,CAAC,CAAC,MAAM;MAClEF,MAAM,IAAI,aAAa/E,MAAM,CAACmD,KAAK,CAACX,KAAK,IAAI;MAC7C,IAAIxC,MAAM,CAACmD,KAAK,CAACV,cAAc,EAAE;QAC/BsC,MAAM,IAAI,qBAAqB/E,MAAM,CAACmD,KAAK,CAACV,cAAc,IAAI;MAChE;MACAsC,MAAM,IAAI,IAAI;IAChB,CAAC,CAAC;EACN;EAEAA,MAAM,IAAI,oBAAoB;EAC9B/B,OAAO,CAACgC,OAAO,CAAC,UAAAhF,MAAM,EAAI;IACxB,IAAMkF,MAAM,GAAGlF,MAAM,CAACmD,KAAK,CAACR,MAAM,CAACtD,EAAE,GAAG,GAAG,GAAG,GAAG;IACjD0F,MAAM,IAAI,GAAGG,MAAM,IAAIlF,MAAM,CAAC2E,OAAO,KAAK3E,MAAM,CAACmD,KAAK,CAACnB,KAAK,CAACiD,OAAO,CAAC,CAAC,CAAC,OAAOjF,MAAM,CAACmD,KAAK,CAACX,KAAK,KAAK;EACvG,CAAC,CAAC;EAEF,OAAOuC,MAAM;AACf", "ignoreList": []}