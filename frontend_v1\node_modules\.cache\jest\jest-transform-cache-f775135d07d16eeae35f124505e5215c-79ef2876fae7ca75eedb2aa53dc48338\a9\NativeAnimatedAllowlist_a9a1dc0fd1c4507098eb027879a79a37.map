{"version": 3, "names": ["ReactNativeFeatureFlags", "_interopRequireWildcard", "require", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "SUPPORTED_COLOR_STYLES", "backgroundColor", "borderBottomColor", "borderColor", "borderEndColor", "borderLeftColor", "borderRightColor", "borderStartColor", "borderTopColor", "color", "tintColor", "SUPPORTED_STYLES", "assign", "borderBottomEndRadius", "borderBottomLeftRadius", "borderBottomRightRadius", "borderBottomStartRadius", "borderEndEndRadius", "borderEndStartRadius", "borderRadius", "borderTopEndRadius", "borderTopLeftRadius", "borderTopRightRadius", "borderTopStartRadius", "borderStartEndRadius", "borderStartStartRadius", "elevation", "opacity", "transform", "zIndex", "shadowOpacity", "shadowRadius", "scaleX", "scaleY", "translateX", "translateY", "SUPPORTED_TRANSFORMS", "scale", "rotate", "rotateX", "rotateY", "rotateZ", "perspective", "skewX", "skewY", "shouldUseAnimatedObjectForTransform", "matrix", "SUPPORTED_INTERPOLATION_PARAMS", "inputRange", "outputRange", "extrapolate", "extrapolateRight", "extrapolateLeft", "_default", "exports", "style", "allowInterpolationParam", "param", "allowStyleProp", "prop", "allowTransformProp", "isSupportedColorStyleProp", "isSupportedInterpolationParam", "isSupportedStyleProp", "isSupportedTransformProp"], "sources": ["NativeAnimatedAllowlist.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {AnimatedPropsAllowlist} from './nodes/AnimatedProps';\n\nimport * as ReactNativeFeatureFlags from '../../src/private/featureflags/ReactNativeFeatureFlags';\n\n/**\n * Styles allowed by the native animated implementation.\n *\n * In general native animated implementation should support any numeric or color property that\n * doesn't need to be updated through the shadow view hierarchy (all non-layout properties).\n */\nconst SUPPORTED_COLOR_STYLES: {[string]: true} = {\n  backgroundColor: true,\n  borderBottomColor: true,\n  borderColor: true,\n  borderEndColor: true,\n  borderLeftColor: true,\n  borderRightColor: true,\n  borderStartColor: true,\n  borderTopColor: true,\n  color: true,\n  tintColor: true,\n};\n\nconst SUPPORTED_STYLES: {[string]: true} = {\n  ...SUPPORTED_COLOR_STYLES,\n  borderBottomEndRadius: true,\n  borderBottomLeftRadius: true,\n  borderBottomRightRadius: true,\n  borderBottomStartRadius: true,\n  borderEndEndRadius: true,\n  borderEndStartRadius: true,\n  borderRadius: true,\n  borderTopEndRadius: true,\n  borderTopLeftRadius: true,\n  borderTopRightRadius: true,\n  borderTopStartRadius: true,\n  borderStartEndRadius: true,\n  borderStartStartRadius: true,\n  elevation: true,\n  opacity: true,\n  transform: true,\n  zIndex: true,\n  /* ios styles */\n  shadowOpacity: true,\n  shadowRadius: true,\n  /* legacy android transform properties */\n  scaleX: true,\n  scaleY: true,\n  translateX: true,\n  translateY: true,\n};\n\nconst SUPPORTED_TRANSFORMS: {[string]: true} = {\n  translateX: true,\n  translateY: true,\n  scale: true,\n  scaleX: true,\n  scaleY: true,\n  rotate: true,\n  rotateX: true,\n  rotateY: true,\n  rotateZ: true,\n  perspective: true,\n  skewX: true,\n  skewY: true,\n  ...(ReactNativeFeatureFlags.shouldUseAnimatedObjectForTransform()\n    ? {matrix: true}\n    : {}),\n};\n\nconst SUPPORTED_INTERPOLATION_PARAMS: {[string]: true} = {\n  inputRange: true,\n  outputRange: true,\n  extrapolate: true,\n  extrapolateRight: true,\n  extrapolateLeft: true,\n};\n\n/**\n * Default allowlist for component props that support native animated values.\n */\nexport default {\n  style: SUPPORTED_STYLES,\n} as AnimatedPropsAllowlist;\n\nexport function allowInterpolationParam(param: string): void {\n  SUPPORTED_INTERPOLATION_PARAMS[param] = true;\n}\n\nexport function allowStyleProp(prop: string): void {\n  SUPPORTED_STYLES[prop] = true;\n}\n\nexport function allowTransformProp(prop: string): void {\n  SUPPORTED_TRANSFORMS[prop] = true;\n}\n\nexport function isSupportedColorStyleProp(prop: string): boolean {\n  return SUPPORTED_COLOR_STYLES.hasOwnProperty(prop);\n}\n\nexport function isSupportedInterpolationParam(param: string): boolean {\n  return SUPPORTED_INTERPOLATION_PARAMS.hasOwnProperty(param);\n}\n\nexport function isSupportedStyleProp(prop: string): boolean {\n  return SUPPORTED_STYLES.hasOwnProperty(prop);\n}\n\nexport function isSupportedTransformProp(prop: string): boolean {\n  return SUPPORTED_TRANSFORMS.hasOwnProperty(prop);\n}\n"], "mappings": ";;;;;;;;;;;AAYA,IAAAA,uBAAA,GAAAC,uBAAA,CAAAC,OAAA;AAAkG,SAAAD,wBAAAE,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAJ,uBAAA,YAAAA,wBAAAE,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAQlG,IAAMmB,sBAAwC,GAAG;EAC/CC,eAAe,EAAE,IAAI;EACrBC,iBAAiB,EAAE,IAAI;EACvBC,WAAW,EAAE,IAAI;EACjBC,cAAc,EAAE,IAAI;EACpBC,eAAe,EAAE,IAAI;EACrBC,gBAAgB,EAAE,IAAI;EACtBC,gBAAgB,EAAE,IAAI;EACtBC,cAAc,EAAE,IAAI;EACpBC,KAAK,EAAE,IAAI;EACXC,SAAS,EAAE;AACb,CAAC;AAED,IAAMC,gBAAkC,GAAAd,MAAA,CAAAe,MAAA,KACnCZ,sBAAsB;EACzBa,qBAAqB,EAAE,IAAI;EAC3BC,sBAAsB,EAAE,IAAI;EAC5BC,uBAAuB,EAAE,IAAI;EAC7BC,uBAAuB,EAAE,IAAI;EAC7BC,kBAAkB,EAAE,IAAI;EACxBC,oBAAoB,EAAE,IAAI;EAC1BC,YAAY,EAAE,IAAI;EAClBC,kBAAkB,EAAE,IAAI;EACxBC,mBAAmB,EAAE,IAAI;EACzBC,oBAAoB,EAAE,IAAI;EAC1BC,oBAAoB,EAAE,IAAI;EAC1BC,oBAAoB,EAAE,IAAI;EAC1BC,sBAAsB,EAAE,IAAI;EAC5BC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAE,IAAI;EACbC,SAAS,EAAE,IAAI;EACfC,MAAM,EAAE,IAAI;EAEZC,aAAa,EAAE,IAAI;EACnBC,YAAY,EAAE,IAAI;EAElBC,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE;AAAI,EACjB;AAED,IAAMC,oBAAsC,GAAAvC,MAAA,CAAAe,MAAA;EAC1CsB,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,IAAI;EAChBE,KAAK,EAAE,IAAI;EACXL,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,IAAI;EACZK,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE,IAAI;EACbC,WAAW,EAAE,IAAI;EACjBC,KAAK,EAAE,IAAI;EACXC,KAAK,EAAE;AAAI,GACPnE,uBAAuB,CAACoE,mCAAmC,CAAC,CAAC,GAC7D;EAACC,MAAM,EAAE;AAAI,CAAC,GACd,CAAC,CAAC,CACP;AAED,IAAMC,8BAAgD,GAAG;EACvDC,UAAU,EAAE,IAAI;EAChBC,WAAW,EAAE,IAAI;EACjBC,WAAW,EAAE,IAAI;EACjBC,gBAAgB,EAAE,IAAI;EACtBC,eAAe,EAAE;AACnB,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAhE,OAAA,GAKa;EACbiE,KAAK,EAAE5C;AACT,CAAC;AAEM,SAAS6C,uBAAuBA,CAACC,KAAa,EAAQ;EAC3DV,8BAA8B,CAACU,KAAK,CAAC,GAAG,IAAI;AAC9C;AAEO,SAASC,cAAcA,CAACC,IAAY,EAAQ;EACjDhD,gBAAgB,CAACgD,IAAI,CAAC,GAAG,IAAI;AAC/B;AAEO,SAASC,kBAAkBA,CAACD,IAAY,EAAQ;EACrDvB,oBAAoB,CAACuB,IAAI,CAAC,GAAG,IAAI;AACnC;AAEO,SAASE,yBAAyBA,CAACF,IAAY,EAAW;EAC/D,OAAO3D,sBAAsB,CAACL,cAAc,CAACgE,IAAI,CAAC;AACpD;AAEO,SAASG,6BAA6BA,CAACL,KAAa,EAAW;EACpE,OAAOV,8BAA8B,CAACpD,cAAc,CAAC8D,KAAK,CAAC;AAC7D;AAEO,SAASM,oBAAoBA,CAACJ,IAAY,EAAW;EAC1D,OAAOhD,gBAAgB,CAAChB,cAAc,CAACgE,IAAI,CAAC;AAC9C;AAEO,SAASK,wBAAwBA,CAACL,IAAY,EAAW;EAC9D,OAAOvB,oBAAoB,CAACzC,cAAc,CAACgE,IAAI,CAAC;AAClD", "ignoreList": []}