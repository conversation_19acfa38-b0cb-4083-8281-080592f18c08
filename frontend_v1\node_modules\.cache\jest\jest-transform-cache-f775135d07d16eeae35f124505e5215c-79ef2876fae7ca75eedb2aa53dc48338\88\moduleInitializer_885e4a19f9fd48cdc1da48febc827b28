a37da5e3806655d6ca4d6280d3463d11
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.safeModuleLoader = exports.initializeCriticalModules = exports.getModuleCacheStatus = exports.getModuleCache = exports.emergencyModuleReset = exports.clearModuleCache = exports.checkModuleHealth = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var moduleCache = new Map();
var safeModuleLoader = exports.safeModuleLoader = function safeModuleLoader(modulePath, moduleLoader, fallback) {
  var maxRetries = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 3;
  var cacheKey = `module_${modulePath}`;
  if (moduleCache.has(cacheKey)) {
    console.log(`[ModuleInitializer] Using cached module: ${modulePath}`);
    return moduleCache.get(cacheKey);
  }
  var attempts = 0;
  var lastError = null;
  while (attempts < maxRetries) {
    try {
      console.log(`[ModuleInitializer] Loading module: ${modulePath} (attempt ${attempts + 1})`);
      var module = moduleLoader();
      if (!module) {
        throw new Error(`Module ${modulePath} returned null or undefined`);
      }
      if (typeof module === 'object' && module !== null) {
        console.log(`[ModuleInitializer] ✅ Successfully loaded module: ${modulePath}`);
        moduleCache.set(cacheKey, module);
        return module;
      } else {
        throw new Error(`Module ${modulePath} is not a valid object`);
      }
    } catch (error) {
      lastError = error;
      attempts++;
      console.error(`[ModuleInitializer] ❌ Failed to load module ${modulePath} (attempt ${attempts}):`, error);
      if (attempts < maxRetries) {
        var delay = attempts * 100;
        console.log(`[ModuleInitializer] Retrying in ${delay}ms...`);
      }
    }
  }
  console.error(`[ModuleInitializer] ❌ Failed to load module ${modulePath} after ${maxRetries} attempts. Using fallback.`);
  console.error(`[ModuleInitializer] Last error:`, lastError);
  moduleCache.set(cacheKey, fallback);
  return fallback;
};
var initializeCriticalModules = exports.initializeCriticalModules = function initializeCriticalModules() {
  console.log('[ModuleInitializer] Initializing critical modules...');
  try {
    safeModuleLoader('Colors', function () {
      var colorsModule = require("../constants/Colors");
      if (!colorsModule.Colors) {
        throw new Error('Colors module does not export Colors object');
      }
      return colorsModule;
    }, {
      Colors: {
        primary: {
          default: '#4A6B52',
          light: '#6B8A74',
          dark: '#2A4B32',
          contrast: '#FFFFFF'
        },
        text: {
          primary: '#1A1A1A',
          secondary: '#6B7280',
          tertiary: '#9CA3AF'
        },
        background: {
          primary: '#FFFFFF',
          secondary: '#F9FAFB',
          tertiary: '#F3F4F6'
        },
        surface: {
          primary: '#FFFFFF',
          secondary: '#F9FAFB',
          tertiary: '#F3F4F6'
        }
      },
      DarkModeColors: {
        sage200: '#1F3A26',
        sage300: '#2A4B32',
        sage400: '#4A6B52',
        sage500: '#5A7A63',
        sage600: '#6B8A74'
      }
    });
    console.log('[ModuleInitializer] ✅ Critical modules initialized successfully');
  } catch (error) {
    console.error('[ModuleInitializer] ❌ Failed to initialize critical modules:', error);
  }
};
var checkModuleHealth = exports.checkModuleHealth = function checkModuleHealth(moduleName, moduleObject) {
  try {
    if (!moduleObject || typeof moduleObject !== 'object') {
      console.error(`[ModuleInitializer] Health check failed for ${moduleName}: not an object`);
      return false;
    }
    if (moduleName === 'Colors') {
      var requiredProperties = ['primary', 'text', 'background', 'surface'];
      for (var prop of requiredProperties) {
        if (!moduleObject[prop]) {
          console.error(`[ModuleInitializer] Health check failed for ${moduleName}: missing ${prop}`);
          return false;
        }
      }
      if (!moduleObject.primary.default) {
        console.error(`[ModuleInitializer] Health check failed for ${moduleName}: missing primary.default`);
        return false;
      }
    }
    console.log(`[ModuleInitializer] ✅ Health check passed for ${moduleName}`);
    return true;
  } catch (error) {
    console.error(`[ModuleInitializer] Health check error for ${moduleName}:`, error);
    return false;
  }
};
var clearModuleCache = exports.clearModuleCache = function clearModuleCache() {
  console.log('[ModuleInitializer] Clearing module cache');
  moduleCache.clear();
};
var getModuleCacheStatus = exports.getModuleCacheStatus = function getModuleCacheStatus() {
  var status = {};
  for (var _ref of moduleCache.entries()) {
    var _ref2 = (0, _slicedToArray2.default)(_ref, 2);
    var key = _ref2[0];
    var value = _ref2[1];
    status[key] = value !== null && value !== undefined;
  }
  return status;
};
var emergencyModuleReset = exports.emergencyModuleReset = function emergencyModuleReset() {
  console.warn('[ModuleInitializer] 🚨 Emergency module reset triggered');
  clearModuleCache();
  setTimeout(function () {
    initializeCriticalModules();
  }, 100);
};
var getModuleCache = exports.getModuleCache = function getModuleCache() {
  return moduleCache;
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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