62e567e84e6ca280baea400ccaca817c
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.bookingService = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _apiClient = require("./apiClient");
var _cachingService = require("./cachingService");
var MOCK_BOOKINGS = [{
  id: 'booking_1',
  customer_id: 'customer_1',
  provider_id: 'provider_1',
  provider_name: 'Bella Beauty Studio',
  service_id: 'service_1',
  service_name: 'Hair Cut & Style',
  service_category: 'Hair',
  scheduled_datetime: '2024-01-20T14:00:00Z',
  duration_minutes: 60,
  base_price: 45.0,
  total_amount: 45.0,
  status: 'confirmed',
  payment_status: 'paid',
  notes: 'Regular trim and style',
  created_at: '2024-01-15T10:00:00Z',
  updated_at: '2024-01-15T10:00:00Z'
}, {
  id: 'booking_2',
  customer_id: 'customer_1',
  provider_id: 'provider_2',
  provider_name: 'Glow Spa & Wellness',
  service_id: 'service_2',
  service_name: 'Facial Treatment',
  service_category: 'Skincare',
  scheduled_datetime: '2024-01-10T11:00:00Z',
  duration_minutes: 90,
  base_price: 75.0,
  total_amount: 75.0,
  status: 'completed',
  payment_status: 'paid',
  notes: 'Deep cleansing facial',
  created_at: '2024-01-08T14:30:00Z',
  updated_at: '2024-01-10T12:30:00Z'
}];
var MOCK_TIME_SLOTS = [{
  time: '09:00',
  available: true,
  provider_id: 'provider_1',
  date: '2024-01-20'
}, {
  time: '09:30',
  available: true,
  provider_id: 'provider_1',
  date: '2024-01-20'
}, {
  time: '10:00',
  available: false,
  provider_id: 'provider_1',
  date: '2024-01-20'
}, {
  time: '10:30',
  available: true,
  provider_id: 'provider_1',
  date: '2024-01-20'
}, {
  time: '11:00',
  available: true,
  provider_id: 'provider_1',
  date: '2024-01-20'
}, {
  time: '11:30',
  available: false,
  provider_id: 'provider_1',
  date: '2024-01-20'
}, {
  time: '14:00',
  available: true,
  provider_id: 'provider_1',
  date: '2024-01-20'
}, {
  time: '14:30',
  available: true,
  provider_id: 'provider_1',
  date: '2024-01-20'
}, {
  time: '15:00',
  available: true,
  provider_id: 'provider_1',
  date: '2024-01-20'
}, {
  time: '15:30',
  available: false,
  provider_id: 'provider_1',
  date: '2024-01-20'
}, {
  time: '16:00',
  available: true,
  provider_id: 'provider_1',
  date: '2024-01-20'
}, {
  time: '16:30',
  available: true,
  provider_id: 'provider_1',
  date: '2024-01-20'
}];
var BookingService = function () {
  function BookingService() {
    (0, _classCallCheck2.default)(this, BookingService);
    this.baseUrl = '/api/bookings/bookings';
  }
  return (0, _createClass2.default)(BookingService, [{
    key: "getBookings",
    value: (function () {
      var _getBookings = (0, _asyncToGenerator2.default)(function* (filters) {
        try {
          var params = new URLSearchParams();
          if (filters) {
            Object.entries(filters).forEach(function (_ref) {
              var _ref2 = (0, _slicedToArray2.default)(_ref, 2),
                key = _ref2[0],
                value = _ref2[1];
              if (value) {
                params.append(key, value);
              }
            });
          }
          var url = `${this.baseUrl}/${params.toString() ? `?${params.toString()}` : ''}`;
          var response = yield _apiClient.apiClient.get(url);
          return response.data;
        } catch (error) {
          var errorMessage = 'Error fetching bookings';
          if ((error == null ? void 0 : error.status) === 401) {
            errorMessage = 'Authentication error - token may be invalid or expired';
            console.warn('🔐 Bookings API: Authentication failed, using fallback data');
          } else if ((error == null ? void 0 : error.status) === 403) {
            errorMessage = 'Access denied - insufficient permissions';
            console.warn('🚫 Bookings API: Access denied, using fallback data');
          } else if ((error == null ? void 0 : error.status) === 404) {
            errorMessage = 'Bookings endpoint not found';
            console.warn('🔍 Bookings API: Endpoint not found, using fallback data');
          } else if ((error == null ? void 0 : error.status) === 500) {
            errorMessage = 'Server error occurred';
            console.warn('🔥 Bookings API: Server error, using fallback data');
          } else if ((error == null ? void 0 : error.status) === 0 || !(error != null && error.status)) {
            errorMessage = 'Network connection error';
            console.warn('🌐 Bookings API: Network error, using fallback data');
          } else {
            errorMessage = `API error (${(error == null ? void 0 : error.status) || 'unknown'})`;
            console.warn(`⚠️ Bookings API: ${errorMessage}, using fallback data`);
          }
          console.error('Bookings API Error Details:', {
            status: error == null ? void 0 : error.status,
            message: error == null ? void 0 : error.message,
            details: error == null ? void 0 : error.details,
            fallbackUsed: true
          });
          var filteredBookings = MOCK_BOOKINGS;
          if (filters != null && filters.status) {
            filteredBookings = filteredBookings.filter(function (booking) {
              return booking.status === filters.status;
            });
          }
          return {
            count: filteredBookings.length,
            next: null,
            previous: null,
            results: filteredBookings
          };
        }
      });
      function getBookings(_x) {
        return _getBookings.apply(this, arguments);
      }
      return getBookings;
    }())
  }, {
    key: "getUpcomingBookings",
    value: (function () {
      var _getUpcomingBookings = (0, _asyncToGenerator2.default)(function* () {
        try {
          var response = yield _apiClient.apiClient.get(`${this.baseUrl}/upcoming/`);
          return response.data;
        } catch (error) {
          if ((error == null ? void 0 : error.status) === 401) {
            console.warn('🔐 Upcoming Bookings API: Authentication failed, using fallback data');
          } else if ((error == null ? void 0 : error.status) === 403) {
            console.warn('🚫 Upcoming Bookings API: Access denied, using fallback data');
          } else if ((error == null ? void 0 : error.status) === 0 || !(error != null && error.status)) {
            console.warn('🌐 Upcoming Bookings API: Network error, using fallback data');
          } else {
            console.warn(`⚠️ Upcoming Bookings API: Error (${(error == null ? void 0 : error.status) || 'unknown'}), using fallback data`);
          }
          console.error('Upcoming Bookings API Error Details:', {
            status: error == null ? void 0 : error.status,
            message: error == null ? void 0 : error.message,
            details: error == null ? void 0 : error.details,
            fallbackUsed: true
          });
          var now = new Date();
          var upcomingBookings = MOCK_BOOKINGS.filter(function (booking) {
            var bookingDate = new Date(booking.scheduled_datetime);
            return bookingDate > now && (booking.status === 'confirmed' || booking.status === 'pending');
          });
          return {
            count: upcomingBookings.length,
            next: null,
            previous: null,
            results: upcomingBookings
          };
        }
      });
      function getUpcomingBookings() {
        return _getUpcomingBookings.apply(this, arguments);
      }
      return getUpcomingBookings;
    }())
  }, {
    key: "getBookingDetails",
    value: (function () {
      var _getBookingDetails = (0, _asyncToGenerator2.default)(function* (bookingId) {
        try {
          var response = yield _apiClient.apiClient.get(`${this.baseUrl}/${bookingId}/`);
          return response.data;
        } catch (error) {
          console.error('Error fetching booking details:', error);
          throw error;
        }
      });
      function getBookingDetails(_x2) {
        return _getBookingDetails.apply(this, arguments);
      }
      return getBookingDetails;
    }())
  }, {
    key: "createBooking",
    value: (function () {
      var _createBooking = (0, _asyncToGenerator2.default)(function* (bookingData) {
        try {
          var response = yield _apiClient.apiClient.post(`${this.baseUrl}/`, {
            service: bookingData.serviceId,
            provider: bookingData.providerId,
            scheduled_datetime: `${bookingData.date}T${bookingData.timeSlot}:00`,
            notes: bookingData.notes || '',
            special_requests: bookingData.specialRequests || '',
            payment_method: bookingData.paymentMethod || 'card'
          }, true);
          yield _cachingService.cachingService.invalidatePattern('bookings-');
          yield _cachingService.cachingService.invalidatePattern('time-slots-');
          console.log('✅ Booking: Successfully created via API');
          return response.data;
        } catch (error) {
          console.error('Error creating booking:', error);
          if ((error == null ? void 0 : error.status) === 401) {
            console.warn('🔐 Booking Creation: Authentication failed');
            throw new Error('Authentication required to create booking');
          } else if ((error == null ? void 0 : error.status) === 400) {
            console.warn('📝 Booking Creation: Invalid booking data');
            throw new Error('Invalid booking data provided');
          } else if ((error == null ? void 0 : error.status) === 409) {
            console.warn('⏰ Booking Creation: Time slot no longer available');
            throw new Error('Selected time slot is no longer available');
          }
          return this.createFallbackBooking(bookingData);
        }
      });
      function createBooking(_x3) {
        return _createBooking.apply(this, arguments);
      }
      return createBooking;
    }())
  }, {
    key: "createFallbackBooking",
    value: function createFallbackBooking(bookingData) {
      var mockBooking = {
        id: `booking_${Date.now()}`,
        booking_number: `BK${Date.now().toString().slice(-6)}`,
        customer_id: 'customer_1',
        provider_id: bookingData.providerId,
        provider_name: 'Offline Provider',
        service_id: bookingData.serviceId,
        service_name: 'Offline Service',
        service_category: 'Beauty',
        scheduled_datetime: `${bookingData.date}T${bookingData.timeSlot}:00Z`,
        duration_minutes: 60,
        base_price: 50.0,
        total_amount: 50.0,
        status: 'pending',
        payment_status: 'pending',
        notes: bookingData.notes || '',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      MOCK_BOOKINGS.push(mockBooking);
      console.log('📱 Booking: Created fallback booking for offline support');
      return mockBooking;
    }
  }, {
    key: "updateBookingStatus",
    value: (function () {
      var _updateBookingStatus = (0, _asyncToGenerator2.default)(function* (bookingId, status) {
        try {
          var response = yield _apiClient.apiClient.patch(`${this.baseUrl}/${bookingId}/`, {
            status: status
          });
          return response.data;
        } catch (error) {
          console.error('Error updating booking status:', error);
          throw error;
        }
      });
      function updateBookingStatus(_x4, _x5) {
        return _updateBookingStatus.apply(this, arguments);
      }
      return updateBookingStatus;
    }())
  }, {
    key: "cancelBooking",
    value: (function () {
      var _cancelBooking = (0, _asyncToGenerator2.default)(function* (bookingId, reason) {
        try {
          var response = yield _apiClient.apiClient.post(`${this.baseUrl}/${bookingId}/cancel/`, {
            cancellation_reason: reason || 'Customer cancellation'
          });
          return response.data;
        } catch (error) {
          console.error('Error cancelling booking:', error);
          var bookingIndex = MOCK_BOOKINGS.findIndex(function (b) {
            return b.id === bookingId;
          });
          if (bookingIndex !== -1) {
            MOCK_BOOKINGS[bookingIndex] = Object.assign({}, MOCK_BOOKINGS[bookingIndex], {
              status: 'cancelled',
              updated_at: new Date().toISOString()
            });
            return MOCK_BOOKINGS[bookingIndex];
          }
          throw error;
        }
      });
      function cancelBooking(_x6, _x7) {
        return _cancelBooking.apply(this, arguments);
      }
      return cancelBooking;
    }())
  }, {
    key: "confirmBooking",
    value: (function () {
      var _confirmBooking = (0, _asyncToGenerator2.default)(function* (bookingId) {
        try {
          var response = yield _apiClient.apiClient.post(`${this.baseUrl}/${bookingId}/confirm/`);
          return response.data;
        } catch (error) {
          console.error('Error confirming booking:', error);
          var bookingIndex = MOCK_BOOKINGS.findIndex(function (b) {
            return b.id === bookingId;
          });
          if (bookingIndex !== -1) {
            MOCK_BOOKINGS[bookingIndex] = Object.assign({}, MOCK_BOOKINGS[bookingIndex], {
              status: 'confirmed',
              updated_at: new Date().toISOString()
            });
            return MOCK_BOOKINGS[bookingIndex];
          }
          throw error;
        }
      });
      function confirmBooking(_x8) {
        return _confirmBooking.apply(this, arguments);
      }
      return confirmBooking;
    }())
  }, {
    key: "getAvailableTimeSlots",
    value: (function () {
      var _getAvailableTimeSlots = (0, _asyncToGenerator2.default)(function* (providerId, date) {
        try {
          var cachedData = yield _cachingService.cachingService.getCachedApiResponse('/api/bookings/time-slots/available/', {
            provider: providerId,
            date: date
          });
          if (cachedData) {
            console.log('📦 Time Slots: Using cached data');
            return cachedData;
          }
          var response = yield _apiClient.apiClient.get(`/api/bookings/time-slots/available/`, {
            params: {
              provider: providerId,
              date: date
            }
          }, false, {
            enabled: true,
            ttl: 2 * 60 * 1000
          });
          yield _cachingService.cachingService.cacheApiResponse('/api/bookings/time-slots/available/', {
            provider: providerId,
            date: date
          }, response.data, 2 * 60 * 1000);
          console.log('✅ Time Slots: Successfully fetched from API');
          return response.data;
        } catch (error) {
          var errorMessage = 'Error fetching time slots';
          if ((error == null ? void 0 : error.status) === 401) {
            errorMessage = 'Authentication error - token may be invalid or expired';
            console.warn('🔐 Time Slots API: Authentication failed, using fallback data');
          } else if ((error == null ? void 0 : error.status) === 403) {
            errorMessage = 'Access denied - insufficient permissions';
            console.warn('🚫 Time Slots API: Access denied, using fallback data');
          } else if ((error == null ? void 0 : error.status) === 404) {
            errorMessage = 'Time slots endpoint not found';
            console.warn('🔍 Time Slots API: Endpoint not found, using fallback data');
          } else if ((error == null ? void 0 : error.status) === 0 || !(error != null && error.status)) {
            errorMessage = 'Network connection error';
            console.warn('🌐 Time Slots API: Network error, using fallback data');
          } else {
            errorMessage = `API error (${(error == null ? void 0 : error.status) || 'unknown'})`;
            console.warn(`⚠️ Time Slots API: ${errorMessage}, using fallback data`);
          }
          console.error('Time Slots API Error Details:', {
            status: error == null ? void 0 : error.status,
            message: error == null ? void 0 : error.message,
            details: error == null ? void 0 : error.details,
            providerId: providerId,
            date: date,
            fallbackUsed: true
          });
          return this.getFallbackTimeSlots(providerId, date);
        }
      });
      function getAvailableTimeSlots(_x9, _x0) {
        return _getAvailableTimeSlots.apply(this, arguments);
      }
      return getAvailableTimeSlots;
    }())
  }, {
    key: "getFallbackTimeSlots",
    value: function getFallbackTimeSlots(providerId, date) {
      return MOCK_TIME_SLOTS.filter(function (slot) {
        return slot.provider_id === providerId && slot.date === date && slot.available;
      });
    }
  }, {
    key: "getBookingStats",
    value: (function () {
      var _getBookingStats = (0, _asyncToGenerator2.default)(function* () {
        try {
          var response = yield _apiClient.apiClient.get(`${this.baseUrl}/stats/`);
          return response.data;
        } catch (error) {
          console.error('Error fetching booking stats:', error);
          return {
            total_bookings: MOCK_BOOKINGS.length,
            upcoming_bookings: MOCK_BOOKINGS.filter(function (b) {
              return new Date(b.scheduled_datetime) > new Date() && (b.status === 'confirmed' || b.status === 'pending');
            }).length,
            completed_bookings: MOCK_BOOKINGS.filter(function (b) {
              return b.status === 'completed';
            }).length,
            cancelled_bookings: MOCK_BOOKINGS.filter(function (b) {
              return b.status === 'cancelled';
            }).length,
            total_revenue: MOCK_BOOKINGS.filter(function (b) {
              return b.status === 'completed';
            }).reduce(function (sum, b) {
              return sum + b.total_amount;
            }, 0)
          };
        }
      });
      function getBookingStats() {
        return _getBookingStats.apply(this, arguments);
      }
      return getBookingStats;
    }())
  }, {
    key: "updateBooking",
    value: (function () {
      var _updateBooking = (0, _asyncToGenerator2.default)(function* (bookingId, updates) {
        try {
          var response = yield _apiClient.apiClient.patch(`${this.baseUrl}/${bookingId}/`, updates);
          return response.data;
        } catch (error) {
          console.error('Failed to update booking:', error);
          var bookingIndex = MOCK_BOOKINGS.findIndex(function (b) {
            return b.id === bookingId;
          });
          if (bookingIndex !== -1) {
            MOCK_BOOKINGS[bookingIndex] = Object.assign({}, MOCK_BOOKINGS[bookingIndex], {
              updated_at: new Date().toISOString()
            }, updates.notes && {
              notes: updates.notes
            });
            return MOCK_BOOKINGS[bookingIndex];
          }
          throw new Error('Failed to update booking');
        }
      });
      function updateBooking(_x1, _x10) {
        return _updateBooking.apply(this, arguments);
      }
      return updateBooking;
    }())
  }, {
    key: "rescheduleBooking",
    value: (function () {
      var _rescheduleBooking = (0, _asyncToGenerator2.default)(function* (bookingId, newDate, newTimeSlot) {
        try {
          var response = yield _apiClient.apiClient.post(`${this.baseUrl}/${bookingId}/reschedule/`, {
            scheduled_datetime: `${newDate}T${newTimeSlot}:00`
          });
          return response.data;
        } catch (error) {
          console.error('Failed to reschedule booking:', error);
          var bookingIndex = MOCK_BOOKINGS.findIndex(function (b) {
            return b.id === bookingId;
          });
          if (bookingIndex !== -1) {
            MOCK_BOOKINGS[bookingIndex] = Object.assign({}, MOCK_BOOKINGS[bookingIndex], {
              scheduled_datetime: `${newDate}T${newTimeSlot}:00Z`,
              updated_at: new Date().toISOString()
            });
            return MOCK_BOOKINGS[bookingIndex];
          }
          throw new Error('Failed to reschedule booking');
        }
      });
      function rescheduleBooking(_x11, _x12, _x13) {
        return _rescheduleBooking.apply(this, arguments);
      }
      return rescheduleBooking;
    }())
  }, {
    key: "getRecentBookings",
    value: (function () {
      var _getRecentBookings = (0, _asyncToGenerator2.default)(function* () {
        var limit = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 5;
        try {
          var cachedData = yield _cachingService.cachingService.getCachedApiResponse(`${this.baseUrl}/history/`, {
            limit: limit
          });
          if (cachedData) {
            console.log('📦 Recent Bookings: Using cached data');
            return cachedData.results;
          }
          var response = yield _apiClient.apiClient.get(`${this.baseUrl}/history/`, {
            params: {
              limit: limit
            }
          }, true, {
            enabled: true,
            ttl: 5 * 60 * 1000
          });
          yield _cachingService.cachingService.cacheApiResponse(`${this.baseUrl}/history/`, {
            limit: limit
          }, response.data, 5 * 60 * 1000);
          console.log('✅ Recent Bookings: Successfully fetched from API');
          return response.data.results;
        } catch (error) {
          console.error('Failed to fetch recent bookings:', error);
          if ((error == null ? void 0 : error.status) === 401) {
            console.warn('🔐 Recent Bookings: Authentication failed, using fallback data');
          } else if ((error == null ? void 0 : error.status) === 404) {
            console.warn('🔍 Recent Bookings: Endpoint not found, using fallback data');
          } else {
            console.warn('⚠️ Recent Bookings: API error, using fallback data');
          }
          return this.getFallbackRecentBookings(limit);
        }
      });
      function getRecentBookings() {
        return _getRecentBookings.apply(this, arguments);
      }
      return getRecentBookings;
    }())
  }, {
    key: "getFallbackRecentBookings",
    value: function getFallbackRecentBookings(limit) {
      return MOCK_BOOKINGS.sort(function (a, b) {
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      }).slice(0, limit);
    }
  }, {
    key: "getUpcomingBookingsList",
    value: (function () {
      var _getUpcomingBookingsList = (0, _asyncToGenerator2.default)(function* () {
        var limit = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 10;
        try {
          var response = yield this.getUpcomingBookings();
          return response.results.slice(0, limit);
        } catch (error) {
          console.error('Failed to fetch upcoming bookings:', error);
          var now = new Date();
          return MOCK_BOOKINGS.filter(function (booking) {
            var bookingDate = new Date(booking.scheduled_datetime);
            return bookingDate > now && (booking.status === 'confirmed' || booking.status === 'pending');
          }).slice(0, limit);
        }
      });
      function getUpcomingBookingsList() {
        return _getUpcomingBookingsList.apply(this, arguments);
      }
      return getUpcomingBookingsList;
    }())
  }]);
}();
var bookingService = exports.bookingService = new BookingService();
var _default = exports.default = bookingService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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