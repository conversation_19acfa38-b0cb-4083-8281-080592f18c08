9bd93a1ba9c7ffbaae820666a926fc97
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.validateContrast = exports.rgbToHex = exports.lightenColor = exports.hexToRgb = exports.getRelativeLuminance = exports.getContrastRatio = exports.getButtonColorSuggestions = exports.fixContrast = exports.default = exports.darkenColor = exports.CONTRAST_STANDARDS = void 0;
var CONTRAST_STANDARDS = exports.CONTRAST_STANDARDS = {
  AA_NORMAL: 4.5,
  AA_LARGE: 3.0,
  AAA_NORMAL: 7.0,
  AAA_LARGE: 4.5,
  NON_TEXT: 3.0
};
var hexToRgb = exports.hexToRgb = function hexToRgb(hex) {
  var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
};
var rgbToHex = exports.rgbToHex = function rgbToHex(r, g, b) {
  return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
};
var getRelativeLuminance = exports.getRelativeLuminance = function getRelativeLuminance(hex) {
  var rgb = hexToRgb(hex);
  if (!rgb) return 0;
  var r = rgb.r,
    g = rgb.g,
    b = rgb.b;
  var rsRGB = r / 255;
  var gsRGB = g / 255;
  var bsRGB = b / 255;
  var rLinear = rsRGB <= 0.03928 ? rsRGB / 12.92 : Math.pow((rsRGB + 0.055) / 1.055, 2.4);
  var gLinear = gsRGB <= 0.03928 ? gsRGB / 12.92 : Math.pow((gsRGB + 0.055) / 1.055, 2.4);
  var bLinear = bsRGB <= 0.03928 ? bsRGB / 12.92 : Math.pow((bsRGB + 0.055) / 1.055, 2.4);
  return 0.2126 * rLinear + 0.7152 * gLinear + 0.0722 * bLinear;
};
var getContrastRatio = exports.getContrastRatio = function getContrastRatio(foreground, background) {
  var l1 = getRelativeLuminance(foreground);
  var l2 = getRelativeLuminance(background);
  var lighter = Math.max(l1, l2);
  var darker = Math.min(l1, l2);
  return (lighter + 0.05) / (darker + 0.05);
};
var validateContrast = exports.validateContrast = function validateContrast(foreground, background) {
  var isLargeText = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
  var targetLevel = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'AA';
  var ratio = getContrastRatio(foreground, background);
  var requiredRatio = targetLevel === 'AAA' ? isLargeText ? CONTRAST_STANDARDS.AAA_LARGE : CONTRAST_STANDARDS.AAA_NORMAL : isLargeText ? CONTRAST_STANDARDS.AA_LARGE : CONTRAST_STANDARDS.AA_NORMAL;
  var isValid = ratio >= requiredRatio;
  var level = 'FAIL';
  if (ratio >= CONTRAST_STANDARDS.AAA_NORMAL || isLargeText && ratio >= CONTRAST_STANDARDS.AAA_LARGE) {
    level = 'AAA';
  } else if (ratio >= CONTRAST_STANDARDS.AA_NORMAL || isLargeText && ratio >= CONTRAST_STANDARDS.AA_LARGE) {
    level = 'AA';
  }
  var recommendation;
  if (!isValid) {
    var deficit = requiredRatio - ratio;
    recommendation = `Contrast ratio ${ratio.toFixed(2)}:1 is below ${targetLevel} standard (${requiredRatio}:1). ` + `Increase contrast by ${deficit.toFixed(2)} to meet accessibility requirements.`;
  }
  return {
    ratio: ratio,
    isValid: isValid,
    level: level,
    recommendation: recommendation
  };
};
var darkenColor = exports.darkenColor = function darkenColor(hex, percentage) {
  var rgb = hexToRgb(hex);
  if (!rgb) return hex;
  var factor = 1 - percentage / 100;
  return rgbToHex(Math.round(rgb.r * factor), Math.round(rgb.g * factor), Math.round(rgb.b * factor));
};
var lightenColor = exports.lightenColor = function lightenColor(hex, percentage) {
  var rgb = hexToRgb(hex);
  if (!rgb) return hex;
  var factor = percentage / 100;
  return rgbToHex(Math.round(rgb.r + (255 - rgb.r) * factor), Math.round(rgb.g + (255 - rgb.g) * factor), Math.round(rgb.b + (255 - rgb.b) * factor));
};
var fixContrast = exports.fixContrast = function fixContrast(foreground, background) {
  var targetRatio = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : CONTRAST_STANDARDS.AA_NORMAL;
  var adjustBackground = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;
  var currentForeground = foreground;
  var currentBackground = background;
  var currentRatio = getContrastRatio(currentForeground, currentBackground);
  if (currentRatio >= targetRatio) {
    return {
      foreground: currentForeground,
      background: currentBackground,
      ratio: currentRatio
    };
  }
  var foregroundLuminance = getRelativeLuminance(foreground);
  var backgroundLuminance = getRelativeLuminance(background);
  if (adjustBackground) {
    if (foregroundLuminance > backgroundLuminance) {
      for (var i = 5; i <= 80; i += 5) {
        var testBackground = darkenColor(background, i);
        var testRatio = getContrastRatio(foreground, testBackground);
        if (testRatio >= targetRatio) {
          return {
            foreground: foreground,
            background: testBackground,
            ratio: testRatio
          };
        }
      }
    } else {
      for (var _i = 5; _i <= 80; _i += 5) {
        var _testBackground = lightenColor(background, _i);
        var _testRatio = getContrastRatio(foreground, _testBackground);
        if (_testRatio >= targetRatio) {
          return {
            foreground: foreground,
            background: _testBackground,
            ratio: _testRatio
          };
        }
      }
    }
  } else {
    if (foregroundLuminance > backgroundLuminance) {
      for (var _i2 = 5; _i2 <= 80; _i2 += 5) {
        var testForeground = lightenColor(foreground, _i2);
        var _testRatio2 = getContrastRatio(testForeground, background);
        if (_testRatio2 >= targetRatio) {
          return {
            foreground: testForeground,
            background: background,
            ratio: _testRatio2
          };
        }
      }
    } else {
      for (var _i3 = 5; _i3 <= 80; _i3 += 5) {
        var _testForeground = darkenColor(foreground, _i3);
        var _testRatio3 = getContrastRatio(_testForeground, background);
        if (_testRatio3 >= targetRatio) {
          return {
            foreground: _testForeground,
            background: background,
            ratio: _testRatio3
          };
        }
      }
    }
  }
  if (foregroundLuminance > backgroundLuminance) {
    return {
      foreground: '#FFFFFF',
      background: darkenColor(background, 60),
      ratio: getContrastRatio('#FFFFFF', darkenColor(background, 60))
    };
  } else {
    return {
      foreground: darkenColor(foreground, 60),
      background: '#FFFFFF',
      ratio: getContrastRatio(darkenColor(foreground, 60), '#FFFFFF')
    };
  }
};
var getButtonColorSuggestions = exports.getButtonColorSuggestions = function getButtonColorSuggestions(brandColor) {
  var suggestions = {
    primary: {
      background: brandColor,
      text: '#FFFFFF'
    },
    secondary: {
      background: 'transparent',
      text: brandColor,
      border: brandColor
    },
    disabled: {
      background: '#F3F4F6',
      text: '#9CA3AF'
    }
  };
  var primaryValidation = validateContrast(suggestions.primary.text, suggestions.primary.background);
  if (!primaryValidation.isValid) {
    var fixed = fixContrast(suggestions.primary.text, suggestions.primary.background);
    suggestions.primary.background = fixed.background;
    suggestions.primary.text = fixed.foreground;
  }
  var secondaryValidation = validateContrast(suggestions.secondary.text, '#FFFFFF');
  if (!secondaryValidation.isValid) {
    var _fixed = fixContrast(suggestions.secondary.text, '#FFFFFF');
    suggestions.secondary.text = _fixed.foreground;
    suggestions.secondary.border = _fixed.foreground;
  }
  return suggestions;
};
var _default = exports.default = {
  CONTRAST_STANDARDS: CONTRAST_STANDARDS,
  hexToRgb: hexToRgb,
  rgbToHex: rgbToHex,
  getRelativeLuminance: getRelativeLuminance,
  getContrastRatio: getContrastRatio,
  validateContrast: validateContrast,
  darkenColor: darkenColor,
  lightenColor: lightenColor,
  fixContrast: fixContrast,
  getButtonColorSuggestions: getButtonColorSuggestions
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJDT05UUkFTVF9TVEFOREFSRFMiLCJleHBvcnRzIiwiQUFfTk9STUFMIiwiQUFfTEFSR0UiLCJBQUFfTk9STUFMIiwiQUFBX0xBUkdFIiwiTk9OX1RFWFQiLCJoZXhUb1JnYiIsImhleCIsInJlc3VsdCIsImV4ZWMiLCJyIiwicGFyc2VJbnQiLCJnIiwiYiIsInJnYlRvSGV4IiwidG9TdHJpbmciLCJzbGljZSIsImdldFJlbGF0aXZlTHVtaW5hbmNlIiwicmdiIiwicnNSR0IiLCJnc1JHQiIsImJzUkdCIiwickxpbmVhciIsIk1hdGgiLCJwb3ciLCJnTGluZWFyIiwiYkxpbmVhciIsImdldENvbnRyYXN0UmF0aW8iLCJmb3JlZ3JvdW5kIiwiYmFja2dyb3VuZCIsImwxIiwibDIiLCJsaWdodGVyIiwibWF4IiwiZGFya2VyIiwibWluIiwidmFsaWRhdGVDb250cmFzdCIsImlzTGFyZ2VUZXh0IiwiYXJndW1lbnRzIiwibGVuZ3RoIiwidW5kZWZpbmVkIiwidGFyZ2V0TGV2ZWwiLCJyYXRpbyIsInJlcXVpcmVkUmF0aW8iLCJpc1ZhbGlkIiwibGV2ZWwiLCJyZWNvbW1lbmRhdGlvbiIsImRlZmljaXQiLCJ0b0ZpeGVkIiwiZGFya2VuQ29sb3IiLCJwZXJjZW50YWdlIiwiZmFjdG9yIiwicm91bmQiLCJsaWdodGVuQ29sb3IiLCJmaXhDb250cmFzdCIsInRhcmdldFJhdGlvIiwiYWRqdXN0QmFja2dyb3VuZCIsImN1cnJlbnRGb3JlZ3JvdW5kIiwiY3VycmVudEJhY2tncm91bmQiLCJjdXJyZW50UmF0aW8iLCJmb3JlZ3JvdW5kTHVtaW5hbmNlIiwiYmFja2dyb3VuZEx1bWluYW5jZSIsImkiLCJ0ZXN0QmFja2dyb3VuZCIsInRlc3RSYXRpbyIsInRlc3RGb3JlZ3JvdW5kIiwiZ2V0QnV0dG9uQ29sb3JTdWdnZXN0aW9ucyIsImJyYW5kQ29sb3IiLCJzdWdnZXN0aW9ucyIsInByaW1hcnkiLCJ0ZXh0Iiwic2Vjb25kYXJ5IiwiYm9yZGVyIiwiZGlzYWJsZWQiLCJwcmltYXJ5VmFsaWRhdGlvbiIsImZpeGVkIiwic2Vjb25kYXJ5VmFsaWRhdGlvbiIsIl9kZWZhdWx0IiwiZGVmYXVsdCJdLCJzb3VyY2VzIjpbImNvbnRyYXN0VmFsaWRhdG9yLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogV0NBRyAyLjEgQUEgQ29udHJhc3QgVmFsaWRhdG9yIGFuZCBGaXhlclxuICpcbiAqIEltcGxlbWVudHMgUkVDLUFDQy0wMDE6IEZpeCBidXR0b24gY29udHJhc3QgcmF0aW9zIHRvIG1lZXQgV0NBRyAyLjEgQUEgc3RhbmRhcmRzLlxuICogUHJvdmlkZXMgdXRpbGl0aWVzIHRvIHZhbGlkYXRlIGFuZCBhdXRvbWF0aWNhbGx5IGZpeCBjb2xvciBjb250cmFzdCBpc3N1ZXMuXG4gKlxuICogV0NBRyAyLjEgQUEgUmVxdWlyZW1lbnRzOlxuICogLSBOb3JtYWwgdGV4dDogNC41OjEgY29udHJhc3QgcmF0aW8gbWluaW11bVxuICogLSBMYXJnZSB0ZXh0ICgxOHB0KyBvciAxNHB0KyBib2xkKTogMzoxIGNvbnRyYXN0IHJhdGlvIG1pbmltdW1cbiAqIC0gTm9uLXRleHQgZWxlbWVudHM6IDM6MSBjb250cmFzdCByYXRpbyBtaW5pbXVtXG4gKlxuICogQHZlcnNpb24gMS4wLjBcbiAqIEBhdXRob3IgVmllcmxhIERldmVsb3BtZW50IFRlYW1cbiAqL1xuXG4vLyBXQ0FHIDIuMSBBQSBjb250cmFzdCBzdGFuZGFyZHNcbmV4cG9ydCBjb25zdCBDT05UUkFTVF9TVEFOREFSRFMgPSB7XG4gIEFBX05PUk1BTDogNC41LFxuICBBQV9MQVJHRTogMy4wLFxuICBBQUFfTk9STUFMOiA3LjAsXG4gIEFBQV9MQVJHRTogNC41LFxuICBOT05fVEVYVDogMy4wLFxufSBhcyBjb25zdDtcblxuLy8gQ29sb3IgY29udHJhc3QgdmFsaWRhdGlvbiByZXN1bHRcbmV4cG9ydCBpbnRlcmZhY2UgQ29udHJhc3RWYWxpZGF0aW9uUmVzdWx0IHtcbiAgcmF0aW86IG51bWJlcjtcbiAgaXNWYWxpZDogYm9vbGVhbjtcbiAgbGV2ZWw6ICdBQScgfCAnQUFBJyB8ICdGQUlMJztcbiAgcmVjb21tZW5kYXRpb24/OiBzdHJpbmc7XG4gIHN1Z2dlc3RlZENvbG9ycz86IHtcbiAgICBmb3JlZ3JvdW5kPzogc3RyaW5nO1xuICAgIGJhY2tncm91bmQ/OiBzdHJpbmc7XG4gIH07XG59XG5cbi8qKlxuICogQ29udmVydCBoZXggY29sb3IgdG8gUkdCIHZhbHVlc1xuICovXG5leHBvcnQgY29uc3QgaGV4VG9SZ2IgPSAoXG4gIGhleDogc3RyaW5nLFxuKTogeyByOiBudW1iZXI7IGc6IG51bWJlcjsgYjogbnVtYmVyIH0gfCBudWxsID0+IHtcbiAgY29uc3QgcmVzdWx0ID0gL14jPyhbYS1mXFxkXXsyfSkoW2EtZlxcZF17Mn0pKFthLWZcXGRdezJ9KSQvaS5leGVjKGhleCk7XG4gIHJldHVybiByZXN1bHRcbiAgICA/IHtcbiAgICAgICAgcjogcGFyc2VJbnQocmVzdWx0WzFdLCAxNiksXG4gICAgICAgIGc6IHBhcnNlSW50KHJlc3VsdFsyXSwgMTYpLFxuICAgICAgICBiOiBwYXJzZUludChyZXN1bHRbM10sIDE2KSxcbiAgICAgIH1cbiAgICA6IG51bGw7XG59O1xuXG4vKipcbiAqIENvbnZlcnQgUkdCIHRvIGhleCBjb2xvclxuICovXG5leHBvcnQgY29uc3QgcmdiVG9IZXggPSAocjogbnVtYmVyLCBnOiBudW1iZXIsIGI6IG51bWJlcik6IHN0cmluZyA9PiB7XG4gIHJldHVybiAnIycgKyAoKDEgPDwgMjQpICsgKHIgPDwgMTYpICsgKGcgPDwgOCkgKyBiKS50b1N0cmluZygxNikuc2xpY2UoMSk7XG59O1xuXG4vKipcbiAqIENhbGN1bGF0ZSByZWxhdGl2ZSBsdW1pbmFuY2Ugb2YgYSBjb2xvclxuICovXG5leHBvcnQgY29uc3QgZ2V0UmVsYXRpdmVMdW1pbmFuY2UgPSAoaGV4OiBzdHJpbmcpOiBudW1iZXIgPT4ge1xuICBjb25zdCByZ2IgPSBoZXhUb1JnYihoZXgpO1xuICBpZiAoIXJnYikgcmV0dXJuIDA7XG5cbiAgY29uc3QgeyByLCBnLCBiIH0gPSByZ2I7XG5cbiAgLy8gQ29udmVydCB0byBzUkdCXG4gIGNvbnN0IHJzUkdCID0gciAvIDI1NTtcbiAgY29uc3QgZ3NSR0IgPSBnIC8gMjU1O1xuICBjb25zdCBic1JHQiA9IGIgLyAyNTU7XG5cbiAgLy8gQXBwbHkgZ2FtbWEgY29ycmVjdGlvblxuICBjb25zdCByTGluZWFyID1cbiAgICByc1JHQiA8PSAwLjAzOTI4ID8gcnNSR0IgLyAxMi45MiA6IE1hdGgucG93KChyc1JHQiArIDAuMDU1KSAvIDEuMDU1LCAyLjQpO1xuICBjb25zdCBnTGluZWFyID1cbiAgICBnc1JHQiA8PSAwLjAzOTI4ID8gZ3NSR0IgLyAxMi45MiA6IE1hdGgucG93KChnc1JHQiArIDAuMDU1KSAvIDEuMDU1LCAyLjQpO1xuICBjb25zdCBiTGluZWFyID1cbiAgICBic1JHQiA8PSAwLjAzOTI4ID8gYnNSR0IgLyAxMi45MiA6IE1hdGgucG93KChic1JHQiArIDAuMDU1KSAvIDEuMDU1LCAyLjQpO1xuXG4gIC8vIENhbGN1bGF0ZSByZWxhdGl2ZSBsdW1pbmFuY2VcbiAgcmV0dXJuIDAuMjEyNiAqIHJMaW5lYXIgKyAwLjcxNTIgKiBnTGluZWFyICsgMC4wNzIyICogYkxpbmVhcjtcbn07XG5cbi8qKlxuICogQ2FsY3VsYXRlIGNvbnRyYXN0IHJhdGlvIGJldHdlZW4gdHdvIGNvbG9yc1xuICovXG5leHBvcnQgY29uc3QgZ2V0Q29udHJhc3RSYXRpbyA9IChcbiAgZm9yZWdyb3VuZDogc3RyaW5nLFxuICBiYWNrZ3JvdW5kOiBzdHJpbmcsXG4pOiBudW1iZXIgPT4ge1xuICBjb25zdCBsMSA9IGdldFJlbGF0aXZlTHVtaW5hbmNlKGZvcmVncm91bmQpO1xuICBjb25zdCBsMiA9IGdldFJlbGF0aXZlTHVtaW5hbmNlKGJhY2tncm91bmQpO1xuXG4gIGNvbnN0IGxpZ2h0ZXIgPSBNYXRoLm1heChsMSwgbDIpO1xuICBjb25zdCBkYXJrZXIgPSBNYXRoLm1pbihsMSwgbDIpO1xuXG4gIHJldHVybiAobGlnaHRlciArIDAuMDUpIC8gKGRhcmtlciArIDAuMDUpO1xufTtcblxuLyoqXG4gKiBWYWxpZGF0ZSBjb250cmFzdCByYXRpbyBhZ2FpbnN0IFdDQUcgc3RhbmRhcmRzXG4gKi9cbmV4cG9ydCBjb25zdCB2YWxpZGF0ZUNvbnRyYXN0ID0gKFxuICBmb3JlZ3JvdW5kOiBzdHJpbmcsXG4gIGJhY2tncm91bmQ6IHN0cmluZyxcbiAgaXNMYXJnZVRleHQ6IGJvb2xlYW4gPSBmYWxzZSxcbiAgdGFyZ2V0TGV2ZWw6ICdBQScgfCAnQUFBJyA9ICdBQScsXG4pOiBDb250cmFzdFZhbGlkYXRpb25SZXN1bHQgPT4ge1xuICBjb25zdCByYXRpbyA9IGdldENvbnRyYXN0UmF0aW8oZm9yZWdyb3VuZCwgYmFja2dyb3VuZCk7XG5cbiAgY29uc3QgcmVxdWlyZWRSYXRpbyA9XG4gICAgdGFyZ2V0TGV2ZWwgPT09ICdBQUEnXG4gICAgICA/IGlzTGFyZ2VUZXh0XG4gICAgICAgID8gQ09OVFJBU1RfU1RBTkRBUkRTLkFBQV9MQVJHRVxuICAgICAgICA6IENPTlRSQVNUX1NUQU5EQVJEUy5BQUFfTk9STUFMXG4gICAgICA6IGlzTGFyZ2VUZXh0XG4gICAgICAgID8gQ09OVFJBU1RfU1RBTkRBUkRTLkFBX0xBUkdFXG4gICAgICAgIDogQ09OVFJBU1RfU1RBTkRBUkRTLkFBX05PUk1BTDtcblxuICBjb25zdCBpc1ZhbGlkID0gcmF0aW8gPj0gcmVxdWlyZWRSYXRpbztcblxuICBsZXQgbGV2ZWw6ICdBQScgfCAnQUFBJyB8ICdGQUlMJyA9ICdGQUlMJztcbiAgaWYgKFxuICAgIHJhdGlvID49IENPTlRSQVNUX1NUQU5EQVJEUy5BQUFfTk9STUFMIHx8XG4gICAgKGlzTGFyZ2VUZXh0ICYmIHJhdGlvID49IENPTlRSQVNUX1NUQU5EQVJEUy5BQUFfTEFSR0UpXG4gICkge1xuICAgIGxldmVsID0gJ0FBQSc7XG4gIH0gZWxzZSBpZiAoXG4gICAgcmF0aW8gPj0gQ09OVFJBU1RfU1RBTkRBUkRTLkFBX05PUk1BTCB8fFxuICAgIChpc0xhcmdlVGV4dCAmJiByYXRpbyA+PSBDT05UUkFTVF9TVEFOREFSRFMuQUFfTEFSR0UpXG4gICkge1xuICAgIGxldmVsID0gJ0FBJztcbiAgfVxuXG4gIGxldCByZWNvbW1lbmRhdGlvbjogc3RyaW5nIHwgdW5kZWZpbmVkO1xuICBpZiAoIWlzVmFsaWQpIHtcbiAgICBjb25zdCBkZWZpY2l0ID0gcmVxdWlyZWRSYXRpbyAtIHJhdGlvO1xuICAgIHJlY29tbWVuZGF0aW9uID1cbiAgICAgIGBDb250cmFzdCByYXRpbyAke3JhdGlvLnRvRml4ZWQoMil9OjEgaXMgYmVsb3cgJHt0YXJnZXRMZXZlbH0gc3RhbmRhcmQgKCR7cmVxdWlyZWRSYXRpb306MSkuIGAgK1xuICAgICAgYEluY3JlYXNlIGNvbnRyYXN0IGJ5ICR7ZGVmaWNpdC50b0ZpeGVkKDIpfSB0byBtZWV0IGFjY2Vzc2liaWxpdHkgcmVxdWlyZW1lbnRzLmA7XG4gIH1cblxuICByZXR1cm4ge1xuICAgIHJhdGlvLFxuICAgIGlzVmFsaWQsXG4gICAgbGV2ZWwsXG4gICAgcmVjb21tZW5kYXRpb24sXG4gIH07XG59O1xuXG4vKipcbiAqIERhcmtlbiBhIGNvbG9yIGJ5IGEgcGVyY2VudGFnZVxuICovXG5leHBvcnQgY29uc3QgZGFya2VuQ29sb3IgPSAoaGV4OiBzdHJpbmcsIHBlcmNlbnRhZ2U6IG51bWJlcik6IHN0cmluZyA9PiB7XG4gIGNvbnN0IHJnYiA9IGhleFRvUmdiKGhleCk7XG4gIGlmICghcmdiKSByZXR1cm4gaGV4O1xuXG4gIGNvbnN0IGZhY3RvciA9IDEgLSBwZXJjZW50YWdlIC8gMTAwO1xuICByZXR1cm4gcmdiVG9IZXgoXG4gICAgTWF0aC5yb3VuZChyZ2IuciAqIGZhY3RvciksXG4gICAgTWF0aC5yb3VuZChyZ2IuZyAqIGZhY3RvciksXG4gICAgTWF0aC5yb3VuZChyZ2IuYiAqIGZhY3RvciksXG4gICk7XG59O1xuXG4vKipcbiAqIExpZ2h0ZW4gYSBjb2xvciBieSBhIHBlcmNlbnRhZ2VcbiAqL1xuZXhwb3J0IGNvbnN0IGxpZ2h0ZW5Db2xvciA9IChoZXg6IHN0cmluZywgcGVyY2VudGFnZTogbnVtYmVyKTogc3RyaW5nID0+IHtcbiAgY29uc3QgcmdiID0gaGV4VG9SZ2IoaGV4KTtcbiAgaWYgKCFyZ2IpIHJldHVybiBoZXg7XG5cbiAgY29uc3QgZmFjdG9yID0gcGVyY2VudGFnZSAvIDEwMDtcbiAgcmV0dXJuIHJnYlRvSGV4KFxuICAgIE1hdGgucm91bmQocmdiLnIgKyAoMjU1IC0gcmdiLnIpICogZmFjdG9yKSxcbiAgICBNYXRoLnJvdW5kKHJnYi5nICsgKDI1NSAtIHJnYi5nKSAqIGZhY3RvciksXG4gICAgTWF0aC5yb3VuZChyZ2IuYiArICgyNTUgLSByZ2IuYikgKiBmYWN0b3IpLFxuICApO1xufTtcblxuLyoqXG4gKiBBdXRvbWF0aWNhbGx5IGZpeCBjb250cmFzdCBieSBhZGp1c3RpbmcgY29sb3JzXG4gKi9cbmV4cG9ydCBjb25zdCBmaXhDb250cmFzdCA9IChcbiAgZm9yZWdyb3VuZDogc3RyaW5nLFxuICBiYWNrZ3JvdW5kOiBzdHJpbmcsXG4gIHRhcmdldFJhdGlvOiBudW1iZXIgPSBDT05UUkFTVF9TVEFOREFSRFMuQUFfTk9STUFMLFxuICBhZGp1c3RCYWNrZ3JvdW5kOiBib29sZWFuID0gZmFsc2UsXG4pOiB7IGZvcmVncm91bmQ6IHN0cmluZzsgYmFja2dyb3VuZDogc3RyaW5nOyByYXRpbzogbnVtYmVyIH0gPT4ge1xuICBjb25zdCBjdXJyZW50Rm9yZWdyb3VuZCA9IGZvcmVncm91bmQ7XG4gIGNvbnN0IGN1cnJlbnRCYWNrZ3JvdW5kID0gYmFja2dyb3VuZDtcbiAgY29uc3QgY3VycmVudFJhdGlvID0gZ2V0Q29udHJhc3RSYXRpbyhjdXJyZW50Rm9yZWdyb3VuZCwgY3VycmVudEJhY2tncm91bmQpO1xuXG4gIC8vIElmIGFscmVhZHkgbWVldHMgdGFyZ2V0LCByZXR1cm4gYXMtaXNcbiAgaWYgKGN1cnJlbnRSYXRpbyA+PSB0YXJnZXRSYXRpbykge1xuICAgIHJldHVybiB7XG4gICAgICBmb3JlZ3JvdW5kOiBjdXJyZW50Rm9yZWdyb3VuZCxcbiAgICAgIGJhY2tncm91bmQ6IGN1cnJlbnRCYWNrZ3JvdW5kLFxuICAgICAgcmF0aW86IGN1cnJlbnRSYXRpbyxcbiAgICB9O1xuICB9XG5cbiAgLy8gRGV0ZXJtaW5lIHdoaWNoIGNvbG9yIHRvIGFkanVzdFxuICBjb25zdCBmb3JlZ3JvdW5kTHVtaW5hbmNlID0gZ2V0UmVsYXRpdmVMdW1pbmFuY2UoZm9yZWdyb3VuZCk7XG4gIGNvbnN0IGJhY2tncm91bmRMdW1pbmFuY2UgPSBnZXRSZWxhdGl2ZUx1bWluYW5jZShiYWNrZ3JvdW5kKTtcblxuICBpZiAoYWRqdXN0QmFja2dyb3VuZCkge1xuICAgIC8vIEFkanVzdCBiYWNrZ3JvdW5kIGNvbG9yXG4gICAgaWYgKGZvcmVncm91bmRMdW1pbmFuY2UgPiBiYWNrZ3JvdW5kTHVtaW5hbmNlKSB7XG4gICAgICAvLyBEYXJrZW4gYmFja2dyb3VuZFxuICAgICAgZm9yIChsZXQgaSA9IDU7IGkgPD0gODA7IGkgKz0gNSkge1xuICAgICAgICBjb25zdCB0ZXN0QmFja2dyb3VuZCA9IGRhcmtlbkNvbG9yKGJhY2tncm91bmQsIGkpO1xuICAgICAgICBjb25zdCB0ZXN0UmF0aW8gPSBnZXRDb250cmFzdFJhdGlvKGZvcmVncm91bmQsIHRlc3RCYWNrZ3JvdW5kKTtcbiAgICAgICAgaWYgKHRlc3RSYXRpbyA+PSB0YXJnZXRSYXRpbykge1xuICAgICAgICAgIHJldHVybiB7IGZvcmVncm91bmQsIGJhY2tncm91bmQ6IHRlc3RCYWNrZ3JvdW5kLCByYXRpbzogdGVzdFJhdGlvIH07XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgLy8gTGlnaHRlbiBiYWNrZ3JvdW5kXG4gICAgICBmb3IgKGxldCBpID0gNTsgaSA8PSA4MDsgaSArPSA1KSB7XG4gICAgICAgIGNvbnN0IHRlc3RCYWNrZ3JvdW5kID0gbGlnaHRlbkNvbG9yKGJhY2tncm91bmQsIGkpO1xuICAgICAgICBjb25zdCB0ZXN0UmF0aW8gPSBnZXRDb250cmFzdFJhdGlvKGZvcmVncm91bmQsIHRlc3RCYWNrZ3JvdW5kKTtcbiAgICAgICAgaWYgKHRlc3RSYXRpbyA+PSB0YXJnZXRSYXRpbykge1xuICAgICAgICAgIHJldHVybiB7IGZvcmVncm91bmQsIGJhY2tncm91bmQ6IHRlc3RCYWNrZ3JvdW5kLCByYXRpbzogdGVzdFJhdGlvIH07XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgLy8gQWRqdXN0IGZvcmVncm91bmQgY29sb3JcbiAgICBpZiAoZm9yZWdyb3VuZEx1bWluYW5jZSA+IGJhY2tncm91bmRMdW1pbmFuY2UpIHtcbiAgICAgIC8vIExpZ2h0ZW4gZm9yZWdyb3VuZFxuICAgICAgZm9yIChsZXQgaSA9IDU7IGkgPD0gODA7IGkgKz0gNSkge1xuICAgICAgICBjb25zdCB0ZXN0Rm9yZWdyb3VuZCA9IGxpZ2h0ZW5Db2xvcihmb3JlZ3JvdW5kLCBpKTtcbiAgICAgICAgY29uc3QgdGVzdFJhdGlvID0gZ2V0Q29udHJhc3RSYXRpbyh0ZXN0Rm9yZWdyb3VuZCwgYmFja2dyb3VuZCk7XG4gICAgICAgIGlmICh0ZXN0UmF0aW8gPj0gdGFyZ2V0UmF0aW8pIHtcbiAgICAgICAgICByZXR1cm4geyBmb3JlZ3JvdW5kOiB0ZXN0Rm9yZWdyb3VuZCwgYmFja2dyb3VuZCwgcmF0aW86IHRlc3RSYXRpbyB9O1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIERhcmtlbiBmb3JlZ3JvdW5kXG4gICAgICBmb3IgKGxldCBpID0gNTsgaSA8PSA4MDsgaSArPSA1KSB7XG4gICAgICAgIGNvbnN0IHRlc3RGb3JlZ3JvdW5kID0gZGFya2VuQ29sb3IoZm9yZWdyb3VuZCwgaSk7XG4gICAgICAgIGNvbnN0IHRlc3RSYXRpbyA9IGdldENvbnRyYXN0UmF0aW8odGVzdEZvcmVncm91bmQsIGJhY2tncm91bmQpO1xuICAgICAgICBpZiAodGVzdFJhdGlvID49IHRhcmdldFJhdGlvKSB7XG4gICAgICAgICAgcmV0dXJuIHsgZm9yZWdyb3VuZDogdGVzdEZvcmVncm91bmQsIGJhY2tncm91bmQsIHJhdGlvOiB0ZXN0UmF0aW8gfTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8vIElmIHdlIGNhbid0IGZpeCBpdCBieSBhZGp1c3Rpbmcgb25lIGNvbG9yLCB0cnkgZXh0cmVtZSB2YWx1ZXNcbiAgaWYgKGZvcmVncm91bmRMdW1pbmFuY2UgPiBiYWNrZ3JvdW5kTHVtaW5hbmNlKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIGZvcmVncm91bmQ6ICcjRkZGRkZGJyxcbiAgICAgIGJhY2tncm91bmQ6IGRhcmtlbkNvbG9yKGJhY2tncm91bmQsIDYwKSxcbiAgICAgIHJhdGlvOiBnZXRDb250cmFzdFJhdGlvKCcjRkZGRkZGJywgZGFya2VuQ29sb3IoYmFja2dyb3VuZCwgNjApKSxcbiAgICB9O1xuICB9IGVsc2Uge1xuICAgIHJldHVybiB7XG4gICAgICBmb3JlZ3JvdW5kOiBkYXJrZW5Db2xvcihmb3JlZ3JvdW5kLCA2MCksXG4gICAgICBiYWNrZ3JvdW5kOiAnI0ZGRkZGRicsXG4gICAgICByYXRpbzogZ2V0Q29udHJhc3RSYXRpbyhkYXJrZW5Db2xvcihmb3JlZ3JvdW5kLCA2MCksICcjRkZGRkZGJyksXG4gICAgfTtcbiAgfVxufTtcblxuLyoqXG4gKiBHZXQgV0NBRy1jb21wbGlhbnQgY29sb3Igc3VnZ2VzdGlvbnMgZm9yIGNvbW1vbiBidXR0b24gc3RhdGVzXG4gKi9cbmV4cG9ydCBjb25zdCBnZXRCdXR0b25Db2xvclN1Z2dlc3Rpb25zID0gKGJyYW5kQ29sb3I6IHN0cmluZykgPT4ge1xuICBjb25zdCBzdWdnZXN0aW9ucyA9IHtcbiAgICBwcmltYXJ5OiB7XG4gICAgICBiYWNrZ3JvdW5kOiBicmFuZENvbG9yLFxuICAgICAgdGV4dDogJyNGRkZGRkYnLFxuICAgIH0sXG4gICAgc2Vjb25kYXJ5OiB7XG4gICAgICBiYWNrZ3JvdW5kOiAndHJhbnNwYXJlbnQnLFxuICAgICAgdGV4dDogYnJhbmRDb2xvcixcbiAgICAgIGJvcmRlcjogYnJhbmRDb2xvcixcbiAgICB9LFxuICAgIGRpc2FibGVkOiB7XG4gICAgICBiYWNrZ3JvdW5kOiAnI0YzRjRGNicsXG4gICAgICB0ZXh0OiAnIzlDQTNBRicsXG4gICAgfSxcbiAgfTtcblxuICAvLyBWYWxpZGF0ZSBhbmQgZml4IHByaW1hcnkgYnV0dG9uXG4gIGNvbnN0IHByaW1hcnlWYWxpZGF0aW9uID0gdmFsaWRhdGVDb250cmFzdChcbiAgICBzdWdnZXN0aW9ucy5wcmltYXJ5LnRleHQsXG4gICAgc3VnZ2VzdGlvbnMucHJpbWFyeS5iYWNrZ3JvdW5kLFxuICApO1xuICBpZiAoIXByaW1hcnlWYWxpZGF0aW9uLmlzVmFsaWQpIHtcbiAgICBjb25zdCBmaXhlZCA9IGZpeENvbnRyYXN0KFxuICAgICAgc3VnZ2VzdGlvbnMucHJpbWFyeS50ZXh0LFxuICAgICAgc3VnZ2VzdGlvbnMucHJpbWFyeS5iYWNrZ3JvdW5kLFxuICAgICk7XG4gICAgc3VnZ2VzdGlvbnMucHJpbWFyeS5iYWNrZ3JvdW5kID0gZml4ZWQuYmFja2dyb3VuZDtcbiAgICBzdWdnZXN0aW9ucy5wcmltYXJ5LnRleHQgPSBmaXhlZC5mb3JlZ3JvdW5kO1xuICB9XG5cbiAgLy8gVmFsaWRhdGUgYW5kIGZpeCBzZWNvbmRhcnkgYnV0dG9uXG4gIGNvbnN0IHNlY29uZGFyeVZhbGlkYXRpb24gPSB2YWxpZGF0ZUNvbnRyYXN0KFxuICAgIHN1Z2dlc3Rpb25zLnNlY29uZGFyeS50ZXh0LFxuICAgICcjRkZGRkZGJyxcbiAgKTsgLy8gQXNzdW1pbmcgd2hpdGUgYmFja2dyb3VuZFxuICBpZiAoIXNlY29uZGFyeVZhbGlkYXRpb24uaXNWYWxpZCkge1xuICAgIGNvbnN0IGZpeGVkID0gZml4Q29udHJhc3Qoc3VnZ2VzdGlvbnMuc2Vjb25kYXJ5LnRleHQsICcjRkZGRkZGJyk7XG4gICAgc3VnZ2VzdGlvbnMuc2Vjb25kYXJ5LnRleHQgPSBmaXhlZC5mb3JlZ3JvdW5kO1xuICAgIHN1Z2dlc3Rpb25zLnNlY29uZGFyeS5ib3JkZXIgPSBmaXhlZC5mb3JlZ3JvdW5kO1xuICB9XG5cbiAgcmV0dXJuIHN1Z2dlc3Rpb25zO1xufTtcblxuZXhwb3J0IGRlZmF1bHQge1xuICBDT05UUkFTVF9TVEFOREFSRFMsXG4gIGhleFRvUmdiLFxuICByZ2JUb0hleCxcbiAgZ2V0UmVsYXRpdmVMdW1pbmFuY2UsXG4gIGdldENvbnRyYXN0UmF0aW8sXG4gIHZhbGlkYXRlQ29udHJhc3QsXG4gIGRhcmtlbkNvbG9yLFxuICBsaWdodGVuQ29sb3IsXG4gIGZpeENvbnRyYXN0LFxuICBnZXRCdXR0b25Db2xvclN1Z2dlc3Rpb25zLFxufTtcbiJdLCJtYXBwaW5ncyI6Ijs7OztBQWdCTyxJQUFNQSxrQkFBa0IsR0FBQUMsT0FBQSxDQUFBRCxrQkFBQSxHQUFHO0VBQ2hDRSxTQUFTLEVBQUUsR0FBRztFQUNkQyxRQUFRLEVBQUUsR0FBRztFQUNiQyxVQUFVLEVBQUUsR0FBRztFQUNmQyxTQUFTLEVBQUUsR0FBRztFQUNkQyxRQUFRLEVBQUU7QUFDWixDQUFVO0FBaUJILElBQU1DLFFBQVEsR0FBQU4sT0FBQSxDQUFBTSxRQUFBLEdBQUcsU0FBWEEsUUFBUUEsQ0FDbkJDLEdBQVcsRUFDb0M7RUFDL0MsSUFBTUMsTUFBTSxHQUFHLDJDQUEyQyxDQUFDQyxJQUFJLENBQUNGLEdBQUcsQ0FBQztFQUNwRSxPQUFPQyxNQUFNLEdBQ1Q7SUFDRUUsQ0FBQyxFQUFFQyxRQUFRLENBQUNILE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRSxFQUFFLENBQUM7SUFDMUJJLENBQUMsRUFBRUQsUUFBUSxDQUFDSCxNQUFNLENBQUMsQ0FBQyxDQUFDLEVBQUUsRUFBRSxDQUFDO0lBQzFCSyxDQUFDLEVBQUVGLFFBQVEsQ0FBQ0gsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFLEVBQUU7RUFDM0IsQ0FBQyxHQUNELElBQUk7QUFDVixDQUFDO0FBS00sSUFBTU0sUUFBUSxHQUFBZCxPQUFBLENBQUFjLFFBQUEsR0FBRyxTQUFYQSxRQUFRQSxDQUFJSixDQUFTLEVBQUVFLENBQVMsRUFBRUMsQ0FBUyxFQUFhO0VBQ25FLE9BQU8sR0FBRyxHQUFHLENBQUMsQ0FBQyxDQUFDLElBQUksRUFBRSxLQUFLSCxDQUFDLElBQUksRUFBRSxDQUFDLElBQUlFLENBQUMsSUFBSSxDQUFDLENBQUMsR0FBR0MsQ0FBQyxFQUFFRSxRQUFRLENBQUMsRUFBRSxDQUFDLENBQUNDLEtBQUssQ0FBQyxDQUFDLENBQUM7QUFDM0UsQ0FBQztBQUtNLElBQU1DLG9CQUFvQixHQUFBakIsT0FBQSxDQUFBaUIsb0JBQUEsR0FBRyxTQUF2QkEsb0JBQW9CQSxDQUFJVixHQUFXLEVBQWE7RUFDM0QsSUFBTVcsR0FBRyxHQUFHWixRQUFRLENBQUNDLEdBQUcsQ0FBQztFQUN6QixJQUFJLENBQUNXLEdBQUcsRUFBRSxPQUFPLENBQUM7RUFFbEIsSUFBUVIsQ0FBQyxHQUFXUSxHQUFHLENBQWZSLENBQUM7SUFBRUUsQ0FBQyxHQUFRTSxHQUFHLENBQVpOLENBQUM7SUFBRUMsQ0FBQyxHQUFLSyxHQUFHLENBQVRMLENBQUM7RUFHZixJQUFNTSxLQUFLLEdBQUdULENBQUMsR0FBRyxHQUFHO0VBQ3JCLElBQU1VLEtBQUssR0FBR1IsQ0FBQyxHQUFHLEdBQUc7RUFDckIsSUFBTVMsS0FBSyxHQUFHUixDQUFDLEdBQUcsR0FBRztFQUdyQixJQUFNUyxPQUFPLEdBQ1hILEtBQUssSUFBSSxPQUFPLEdBQUdBLEtBQUssR0FBRyxLQUFLLEdBQUdJLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUNMLEtBQUssR0FBRyxLQUFLLElBQUksS0FBSyxFQUFFLEdBQUcsQ0FBQztFQUMzRSxJQUFNTSxPQUFPLEdBQ1hMLEtBQUssSUFBSSxPQUFPLEdBQUdBLEtBQUssR0FBRyxLQUFLLEdBQUdHLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUNKLEtBQUssR0FBRyxLQUFLLElBQUksS0FBSyxFQUFFLEdBQUcsQ0FBQztFQUMzRSxJQUFNTSxPQUFPLEdBQ1hMLEtBQUssSUFBSSxPQUFPLEdBQUdBLEtBQUssR0FBRyxLQUFLLEdBQUdFLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUNILEtBQUssR0FBRyxLQUFLLElBQUksS0FBSyxFQUFFLEdBQUcsQ0FBQztFQUczRSxPQUFPLE1BQU0sR0FBR0MsT0FBTyxHQUFHLE1BQU0sR0FBR0csT0FBTyxHQUFHLE1BQU0sR0FBR0MsT0FBTztBQUMvRCxDQUFDO0FBS00sSUFBTUMsZ0JBQWdCLEdBQUEzQixPQUFBLENBQUEyQixnQkFBQSxHQUFHLFNBQW5CQSxnQkFBZ0JBLENBQzNCQyxVQUFrQixFQUNsQkMsVUFBa0IsRUFDUDtFQUNYLElBQU1DLEVBQUUsR0FBR2Isb0JBQW9CLENBQUNXLFVBQVUsQ0FBQztFQUMzQyxJQUFNRyxFQUFFLEdBQUdkLG9CQUFvQixDQUFDWSxVQUFVLENBQUM7RUFFM0MsSUFBTUcsT0FBTyxHQUFHVCxJQUFJLENBQUNVLEdBQUcsQ0FBQ0gsRUFBRSxFQUFFQyxFQUFFLENBQUM7RUFDaEMsSUFBTUcsTUFBTSxHQUFHWCxJQUFJLENBQUNZLEdBQUcsQ0FBQ0wsRUFBRSxFQUFFQyxFQUFFLENBQUM7RUFFL0IsT0FBTyxDQUFDQyxPQUFPLEdBQUcsSUFBSSxLQUFLRSxNQUFNLEdBQUcsSUFBSSxDQUFDO0FBQzNDLENBQUM7QUFLTSxJQUFNRSxnQkFBZ0IsR0FBQXBDLE9BQUEsQ0FBQW9DLGdCQUFBLEdBQUcsU0FBbkJBLGdCQUFnQkEsQ0FDM0JSLFVBQWtCLEVBQ2xCQyxVQUFrQixFQUdXO0VBQUEsSUFGN0JRLFdBQW9CLEdBQUFDLFNBQUEsQ0FBQUMsTUFBQSxRQUFBRCxTQUFBLFFBQUFFLFNBQUEsR0FBQUYsU0FBQSxNQUFHLEtBQUs7RUFBQSxJQUM1QkcsV0FBeUIsR0FBQUgsU0FBQSxDQUFBQyxNQUFBLFFBQUFELFNBQUEsUUFBQUUsU0FBQSxHQUFBRixTQUFBLE1BQUcsSUFBSTtFQUVoQyxJQUFNSSxLQUFLLEdBQUdmLGdCQUFnQixDQUFDQyxVQUFVLEVBQUVDLFVBQVUsQ0FBQztFQUV0RCxJQUFNYyxhQUFhLEdBQ2pCRixXQUFXLEtBQUssS0FBSyxHQUNqQkosV0FBVyxHQUNUdEMsa0JBQWtCLENBQUNLLFNBQVMsR0FDNUJMLGtCQUFrQixDQUFDSSxVQUFVLEdBQy9Ca0MsV0FBVyxHQUNUdEMsa0JBQWtCLENBQUNHLFFBQVEsR0FDM0JILGtCQUFrQixDQUFDRSxTQUFTO0VBRXBDLElBQU0yQyxPQUFPLEdBQUdGLEtBQUssSUFBSUMsYUFBYTtFQUV0QyxJQUFJRSxLQUE0QixHQUFHLE1BQU07RUFDekMsSUFDRUgsS0FBSyxJQUFJM0Msa0JBQWtCLENBQUNJLFVBQVUsSUFDckNrQyxXQUFXLElBQUlLLEtBQUssSUFBSTNDLGtCQUFrQixDQUFDSyxTQUFVLEVBQ3REO0lBQ0F5QyxLQUFLLEdBQUcsS0FBSztFQUNmLENBQUMsTUFBTSxJQUNMSCxLQUFLLElBQUkzQyxrQkFBa0IsQ0FBQ0UsU0FBUyxJQUNwQ29DLFdBQVcsSUFBSUssS0FBSyxJQUFJM0Msa0JBQWtCLENBQUNHLFFBQVMsRUFDckQ7SUFDQTJDLEtBQUssR0FBRyxJQUFJO0VBQ2Q7RUFFQSxJQUFJQyxjQUFrQztFQUN0QyxJQUFJLENBQUNGLE9BQU8sRUFBRTtJQUNaLElBQU1HLE9BQU8sR0FBR0osYUFBYSxHQUFHRCxLQUFLO0lBQ3JDSSxjQUFjLEdBQ1osa0JBQWtCSixLQUFLLENBQUNNLE9BQU8sQ0FBQyxDQUFDLENBQUMsZUFBZVAsV0FBVyxjQUFjRSxhQUFhLE9BQU8sR0FDOUYsd0JBQXdCSSxPQUFPLENBQUNDLE9BQU8sQ0FBQyxDQUFDLENBQUMsc0NBQXNDO0VBQ3BGO0VBRUEsT0FBTztJQUNMTixLQUFLLEVBQUxBLEtBQUs7SUFDTEUsT0FBTyxFQUFQQSxPQUFPO0lBQ1BDLEtBQUssRUFBTEEsS0FBSztJQUNMQyxjQUFjLEVBQWRBO0VBQ0YsQ0FBQztBQUNILENBQUM7QUFLTSxJQUFNRyxXQUFXLEdBQUFqRCxPQUFBLENBQUFpRCxXQUFBLEdBQUcsU0FBZEEsV0FBV0EsQ0FBSTFDLEdBQVcsRUFBRTJDLFVBQWtCLEVBQWE7RUFDdEUsSUFBTWhDLEdBQUcsR0FBR1osUUFBUSxDQUFDQyxHQUFHLENBQUM7RUFDekIsSUFBSSxDQUFDVyxHQUFHLEVBQUUsT0FBT1gsR0FBRztFQUVwQixJQUFNNEMsTUFBTSxHQUFHLENBQUMsR0FBR0QsVUFBVSxHQUFHLEdBQUc7RUFDbkMsT0FBT3BDLFFBQVEsQ0FDYlMsSUFBSSxDQUFDNkIsS0FBSyxDQUFDbEMsR0FBRyxDQUFDUixDQUFDLEdBQUd5QyxNQUFNLENBQUMsRUFDMUI1QixJQUFJLENBQUM2QixLQUFLLENBQUNsQyxHQUFHLENBQUNOLENBQUMsR0FBR3VDLE1BQU0sQ0FBQyxFQUMxQjVCLElBQUksQ0FBQzZCLEtBQUssQ0FBQ2xDLEdBQUcsQ0FBQ0wsQ0FBQyxHQUFHc0MsTUFBTSxDQUMzQixDQUFDO0FBQ0gsQ0FBQztBQUtNLElBQU1FLFlBQVksR0FBQXJELE9BQUEsQ0FBQXFELFlBQUEsR0FBRyxTQUFmQSxZQUFZQSxDQUFJOUMsR0FBVyxFQUFFMkMsVUFBa0IsRUFBYTtFQUN2RSxJQUFNaEMsR0FBRyxHQUFHWixRQUFRLENBQUNDLEdBQUcsQ0FBQztFQUN6QixJQUFJLENBQUNXLEdBQUcsRUFBRSxPQUFPWCxHQUFHO0VBRXBCLElBQU00QyxNQUFNLEdBQUdELFVBQVUsR0FBRyxHQUFHO0VBQy9CLE9BQU9wQyxRQUFRLENBQ2JTLElBQUksQ0FBQzZCLEtBQUssQ0FBQ2xDLEdBQUcsQ0FBQ1IsQ0FBQyxHQUFHLENBQUMsR0FBRyxHQUFHUSxHQUFHLENBQUNSLENBQUMsSUFBSXlDLE1BQU0sQ0FBQyxFQUMxQzVCLElBQUksQ0FBQzZCLEtBQUssQ0FBQ2xDLEdBQUcsQ0FBQ04sQ0FBQyxHQUFHLENBQUMsR0FBRyxHQUFHTSxHQUFHLENBQUNOLENBQUMsSUFBSXVDLE1BQU0sQ0FBQyxFQUMxQzVCLElBQUksQ0FBQzZCLEtBQUssQ0FBQ2xDLEdBQUcsQ0FBQ0wsQ0FBQyxHQUFHLENBQUMsR0FBRyxHQUFHSyxHQUFHLENBQUNMLENBQUMsSUFBSXNDLE1BQU0sQ0FDM0MsQ0FBQztBQUNILENBQUM7QUFLTSxJQUFNRyxXQUFXLEdBQUF0RCxPQUFBLENBQUFzRCxXQUFBLEdBQUcsU0FBZEEsV0FBV0EsQ0FDdEIxQixVQUFrQixFQUNsQkMsVUFBa0IsRUFHNEM7RUFBQSxJQUY5RDBCLFdBQW1CLEdBQUFqQixTQUFBLENBQUFDLE1BQUEsUUFBQUQsU0FBQSxRQUFBRSxTQUFBLEdBQUFGLFNBQUEsTUFBR3ZDLGtCQUFrQixDQUFDRSxTQUFTO0VBQUEsSUFDbER1RCxnQkFBeUIsR0FBQWxCLFNBQUEsQ0FBQUMsTUFBQSxRQUFBRCxTQUFBLFFBQUFFLFNBQUEsR0FBQUYsU0FBQSxNQUFHLEtBQUs7RUFFakMsSUFBTW1CLGlCQUFpQixHQUFHN0IsVUFBVTtFQUNwQyxJQUFNOEIsaUJBQWlCLEdBQUc3QixVQUFVO0VBQ3BDLElBQU04QixZQUFZLEdBQUdoQyxnQkFBZ0IsQ0FBQzhCLGlCQUFpQixFQUFFQyxpQkFBaUIsQ0FBQztFQUczRSxJQUFJQyxZQUFZLElBQUlKLFdBQVcsRUFBRTtJQUMvQixPQUFPO01BQ0wzQixVQUFVLEVBQUU2QixpQkFBaUI7TUFDN0I1QixVQUFVLEVBQUU2QixpQkFBaUI7TUFDN0JoQixLQUFLLEVBQUVpQjtJQUNULENBQUM7RUFDSDtFQUdBLElBQU1DLG1CQUFtQixHQUFHM0Msb0JBQW9CLENBQUNXLFVBQVUsQ0FBQztFQUM1RCxJQUFNaUMsbUJBQW1CLEdBQUc1QyxvQkFBb0IsQ0FBQ1ksVUFBVSxDQUFDO0VBRTVELElBQUkyQixnQkFBZ0IsRUFBRTtJQUVwQixJQUFJSSxtQkFBbUIsR0FBR0MsbUJBQW1CLEVBQUU7TUFFN0MsS0FBSyxJQUFJQyxDQUFDLEdBQUcsQ0FBQyxFQUFFQSxDQUFDLElBQUksRUFBRSxFQUFFQSxDQUFDLElBQUksQ0FBQyxFQUFFO1FBQy9CLElBQU1DLGNBQWMsR0FBR2QsV0FBVyxDQUFDcEIsVUFBVSxFQUFFaUMsQ0FBQyxDQUFDO1FBQ2pELElBQU1FLFNBQVMsR0FBR3JDLGdCQUFnQixDQUFDQyxVQUFVLEVBQUVtQyxjQUFjLENBQUM7UUFDOUQsSUFBSUMsU0FBUyxJQUFJVCxXQUFXLEVBQUU7VUFDNUIsT0FBTztZQUFFM0IsVUFBVSxFQUFWQSxVQUFVO1lBQUVDLFVBQVUsRUFBRWtDLGNBQWM7WUFBRXJCLEtBQUssRUFBRXNCO1VBQVUsQ0FBQztRQUNyRTtNQUNGO0lBQ0YsQ0FBQyxNQUFNO01BRUwsS0FBSyxJQUFJRixFQUFDLEdBQUcsQ0FBQyxFQUFFQSxFQUFDLElBQUksRUFBRSxFQUFFQSxFQUFDLElBQUksQ0FBQyxFQUFFO1FBQy9CLElBQU1DLGVBQWMsR0FBR1YsWUFBWSxDQUFDeEIsVUFBVSxFQUFFaUMsRUFBQyxDQUFDO1FBQ2xELElBQU1FLFVBQVMsR0FBR3JDLGdCQUFnQixDQUFDQyxVQUFVLEVBQUVtQyxlQUFjLENBQUM7UUFDOUQsSUFBSUMsVUFBUyxJQUFJVCxXQUFXLEVBQUU7VUFDNUIsT0FBTztZQUFFM0IsVUFBVSxFQUFWQSxVQUFVO1lBQUVDLFVBQVUsRUFBRWtDLGVBQWM7WUFBRXJCLEtBQUssRUFBRXNCO1VBQVUsQ0FBQztRQUNyRTtNQUNGO0lBQ0Y7RUFDRixDQUFDLE1BQU07SUFFTCxJQUFJSixtQkFBbUIsR0FBR0MsbUJBQW1CLEVBQUU7TUFFN0MsS0FBSyxJQUFJQyxHQUFDLEdBQUcsQ0FBQyxFQUFFQSxHQUFDLElBQUksRUFBRSxFQUFFQSxHQUFDLElBQUksQ0FBQyxFQUFFO1FBQy9CLElBQU1HLGNBQWMsR0FBR1osWUFBWSxDQUFDekIsVUFBVSxFQUFFa0MsR0FBQyxDQUFDO1FBQ2xELElBQU1FLFdBQVMsR0FBR3JDLGdCQUFnQixDQUFDc0MsY0FBYyxFQUFFcEMsVUFBVSxDQUFDO1FBQzlELElBQUltQyxXQUFTLElBQUlULFdBQVcsRUFBRTtVQUM1QixPQUFPO1lBQUUzQixVQUFVLEVBQUVxQyxjQUFjO1lBQUVwQyxVQUFVLEVBQVZBLFVBQVU7WUFBRWEsS0FBSyxFQUFFc0I7VUFBVSxDQUFDO1FBQ3JFO01BQ0Y7SUFDRixDQUFDLE1BQU07TUFFTCxLQUFLLElBQUlGLEdBQUMsR0FBRyxDQUFDLEVBQUVBLEdBQUMsSUFBSSxFQUFFLEVBQUVBLEdBQUMsSUFBSSxDQUFDLEVBQUU7UUFDL0IsSUFBTUcsZUFBYyxHQUFHaEIsV0FBVyxDQUFDckIsVUFBVSxFQUFFa0MsR0FBQyxDQUFDO1FBQ2pELElBQU1FLFdBQVMsR0FBR3JDLGdCQUFnQixDQUFDc0MsZUFBYyxFQUFFcEMsVUFBVSxDQUFDO1FBQzlELElBQUltQyxXQUFTLElBQUlULFdBQVcsRUFBRTtVQUM1QixPQUFPO1lBQUUzQixVQUFVLEVBQUVxQyxlQUFjO1lBQUVwQyxVQUFVLEVBQVZBLFVBQVU7WUFBRWEsS0FBSyxFQUFFc0I7VUFBVSxDQUFDO1FBQ3JFO01BQ0Y7SUFDRjtFQUNGO0VBR0EsSUFBSUosbUJBQW1CLEdBQUdDLG1CQUFtQixFQUFFO0lBQzdDLE9BQU87TUFDTGpDLFVBQVUsRUFBRSxTQUFTO01BQ3JCQyxVQUFVLEVBQUVvQixXQUFXLENBQUNwQixVQUFVLEVBQUUsRUFBRSxDQUFDO01BQ3ZDYSxLQUFLLEVBQUVmLGdCQUFnQixDQUFDLFNBQVMsRUFBRXNCLFdBQVcsQ0FBQ3BCLFVBQVUsRUFBRSxFQUFFLENBQUM7SUFDaEUsQ0FBQztFQUNILENBQUMsTUFBTTtJQUNMLE9BQU87TUFDTEQsVUFBVSxFQUFFcUIsV0FBVyxDQUFDckIsVUFBVSxFQUFFLEVBQUUsQ0FBQztNQUN2Q0MsVUFBVSxFQUFFLFNBQVM7TUFDckJhLEtBQUssRUFBRWYsZ0JBQWdCLENBQUNzQixXQUFXLENBQUNyQixVQUFVLEVBQUUsRUFBRSxDQUFDLEVBQUUsU0FBUztJQUNoRSxDQUFDO0VBQ0g7QUFDRixDQUFDO0FBS00sSUFBTXNDLHlCQUF5QixHQUFBbEUsT0FBQSxDQUFBa0UseUJBQUEsR0FBRyxTQUE1QkEseUJBQXlCQSxDQUFJQyxVQUFrQixFQUFLO0VBQy9ELElBQU1DLFdBQVcsR0FBRztJQUNsQkMsT0FBTyxFQUFFO01BQ1B4QyxVQUFVLEVBQUVzQyxVQUFVO01BQ3RCRyxJQUFJLEVBQUU7SUFDUixDQUFDO0lBQ0RDLFNBQVMsRUFBRTtNQUNUMUMsVUFBVSxFQUFFLGFBQWE7TUFDekJ5QyxJQUFJLEVBQUVILFVBQVU7TUFDaEJLLE1BQU0sRUFBRUw7SUFDVixDQUFDO0lBQ0RNLFFBQVEsRUFBRTtNQUNSNUMsVUFBVSxFQUFFLFNBQVM7TUFDckJ5QyxJQUFJLEVBQUU7SUFDUjtFQUNGLENBQUM7RUFHRCxJQUFNSSxpQkFBaUIsR0FBR3RDLGdCQUFnQixDQUN4Q2dDLFdBQVcsQ0FBQ0MsT0FBTyxDQUFDQyxJQUFJLEVBQ3hCRixXQUFXLENBQUNDLE9BQU8sQ0FBQ3hDLFVBQ3RCLENBQUM7RUFDRCxJQUFJLENBQUM2QyxpQkFBaUIsQ0FBQzlCLE9BQU8sRUFBRTtJQUM5QixJQUFNK0IsS0FBSyxHQUFHckIsV0FBVyxDQUN2QmMsV0FBVyxDQUFDQyxPQUFPLENBQUNDLElBQUksRUFDeEJGLFdBQVcsQ0FBQ0MsT0FBTyxDQUFDeEMsVUFDdEIsQ0FBQztJQUNEdUMsV0FBVyxDQUFDQyxPQUFPLENBQUN4QyxVQUFVLEdBQUc4QyxLQUFLLENBQUM5QyxVQUFVO0lBQ2pEdUMsV0FBVyxDQUFDQyxPQUFPLENBQUNDLElBQUksR0FBR0ssS0FBSyxDQUFDL0MsVUFBVTtFQUM3QztFQUdBLElBQU1nRCxtQkFBbUIsR0FBR3hDLGdCQUFnQixDQUMxQ2dDLFdBQVcsQ0FBQ0csU0FBUyxDQUFDRCxJQUFJLEVBQzFCLFNBQ0YsQ0FBQztFQUNELElBQUksQ0FBQ00sbUJBQW1CLENBQUNoQyxPQUFPLEVBQUU7SUFDaEMsSUFBTStCLE1BQUssR0FBR3JCLFdBQVcsQ0FBQ2MsV0FBVyxDQUFDRyxTQUFTLENBQUNELElBQUksRUFBRSxTQUFTLENBQUM7SUFDaEVGLFdBQVcsQ0FBQ0csU0FBUyxDQUFDRCxJQUFJLEdBQUdLLE1BQUssQ0FBQy9DLFVBQVU7SUFDN0N3QyxXQUFXLENBQUNHLFNBQVMsQ0FBQ0MsTUFBTSxHQUFHRyxNQUFLLENBQUMvQyxVQUFVO0VBQ2pEO0VBRUEsT0FBT3dDLFdBQVc7QUFDcEIsQ0FBQztBQUFDLElBQUFTLFFBQUEsR0FBQTdFLE9BQUEsQ0FBQThFLE9BQUEsR0FFYTtFQUNiL0Usa0JBQWtCLEVBQWxCQSxrQkFBa0I7RUFDbEJPLFFBQVEsRUFBUkEsUUFBUTtFQUNSUSxRQUFRLEVBQVJBLFFBQVE7RUFDUkcsb0JBQW9CLEVBQXBCQSxvQkFBb0I7RUFDcEJVLGdCQUFnQixFQUFoQkEsZ0JBQWdCO0VBQ2hCUyxnQkFBZ0IsRUFBaEJBLGdCQUFnQjtFQUNoQmEsV0FBVyxFQUFYQSxXQUFXO0VBQ1hJLFlBQVksRUFBWkEsWUFBWTtFQUNaQyxXQUFXLEVBQVhBLFdBQVc7RUFDWFkseUJBQXlCLEVBQXpCQTtBQUNGLENBQUMiLCJpZ25vcmVMaXN0IjpbXX0=