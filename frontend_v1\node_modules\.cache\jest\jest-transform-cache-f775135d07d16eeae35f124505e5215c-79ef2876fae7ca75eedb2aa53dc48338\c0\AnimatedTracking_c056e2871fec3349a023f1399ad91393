0c3caec6236ced1a931fa87911c18c4c
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _NativeAnimatedHelper = _interopRequireDefault(require("../../../src/private/animated/NativeAnimatedHelper"));
var _AnimatedNode2 = _interopRequireDefault(require("./AnimatedNode"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var AnimatedTracking = exports.default = function (_AnimatedNode) {
  function AnimatedTracking(value, parent, animationClass, animationConfig, callback, config) {
    var _this;
    (0, _classCallCheck2.default)(this, AnimatedTracking);
    _this = _callSuper(this, AnimatedTracking, [config]);
    _this._value = value;
    _this._parent = parent;
    _this._animationClass = animationClass;
    _this._animationConfig = animationConfig;
    _this._useNativeDriver = _NativeAnimatedHelper.default.shouldUseNativeDriver(animationConfig);
    _this._callback = callback;
    _this.__attach();
    return _this;
  }
  (0, _inherits2.default)(AnimatedTracking, _AnimatedNode);
  return (0, _createClass2.default)(AnimatedTracking, [{
    key: "__makeNative",
    value: function __makeNative(platformConfig) {
      this.__isNative = true;
      this._parent.__makeNative(platformConfig);
      _superPropGet(AnimatedTracking, "__makeNative", this, 3)([platformConfig]);
      this._value.__makeNative(platformConfig);
    }
  }, {
    key: "__getValue",
    value: function __getValue() {
      return this._parent.__getValue();
    }
  }, {
    key: "__attach",
    value: function __attach() {
      this._parent.__addChild(this);
      if (this._useNativeDriver) {
        var platformConfig = this._animationConfig.platformConfig;
        this.__makeNative(platformConfig);
      }
      _superPropGet(AnimatedTracking, "__attach", this, 3)([]);
    }
  }, {
    key: "__detach",
    value: function __detach() {
      this._parent.__removeChild(this);
      _superPropGet(AnimatedTracking, "__detach", this, 3)([]);
    }
  }, {
    key: "update",
    value: function update() {
      this._value.animate(new this._animationClass(Object.assign({}, this._animationConfig, {
        toValue: this._animationConfig.toValue.__getValue()
      })), this._callback);
    }
  }, {
    key: "__getNativeConfig",
    value: function __getNativeConfig() {
      var animation = new this._animationClass(Object.assign({}, this._animationConfig, {
        toValue: undefined
      }));
      var animationConfig = animation.__getNativeAnimationConfig();
      return {
        type: 'tracking',
        animationId: _NativeAnimatedHelper.default.generateNewAnimationId(),
        animationConfig: animationConfig,
        toValue: this._parent.__getNativeTag(),
        value: this._value.__getNativeTag(),
        debugID: this.__getDebugID()
      };
    }
  }]);
}(_AnimatedNode2.default);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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