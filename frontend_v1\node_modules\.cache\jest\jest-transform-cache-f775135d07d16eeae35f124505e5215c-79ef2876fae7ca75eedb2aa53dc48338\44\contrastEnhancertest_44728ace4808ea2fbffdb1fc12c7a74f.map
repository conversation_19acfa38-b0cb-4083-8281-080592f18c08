{"version": 3, "names": ["_contrastEnhancer", "require", "describe", "it", "result", "enhancePrimaryCTAContrast", "expect", "isCompliant", "toBe", "ratio", "toBeGreaterThanOrEqual", "recommendation", "toContain", "enhancedColor", "toBeDefined", "isOptimal", "results", "validateInteractiveColors", "primary", "primaryHover", "primaryPressed", "secondary", "destructive", "report", "generateContrastReport", "totalTests", "toBeGreaterThan", "compliantCount", "complianceRate", "toHave<PERSON>ength", "summary", "overallStatus", "toMatch", "textColor", "getBestTextColor", "ensureMinimumContrast", "EnhancedCTAColors", "primaryText", "secondaryText", "primaryResult", "secondaryResult", "testCases", "name", "background", "text", "expectedMinRatio", "for<PERSON>ach", "_ref"], "sources": ["contrastEnhancer.test.ts"], "sourcesContent": ["/**\n * Contrast Enhancer Tests\n *\n * Tests for WCAG 2.2 AA color contrast compliance and enhancement utilities.\n */\n\nimport {\n  enhancePrimaryCTAContrast,\n  validateInteractiveColors,\n  generateContrastReport,\n  getBestTextColor,\n  ensureMinimumContrast,\n  EnhancedCTAColors,\n} from '../contrastEnhancer';\n\ndescribe('Contrast Enhancer', () => {\n  describe('enhancePrimaryCTAContrast', () => {\n    it('should validate compliant colors', () => {\n      const result = enhancePrimaryCTAContrast('#5A7A63', '#FFFFFF');\n\n      expect(result.isCompliant).toBe(true);\n      expect(result.ratio).toBeGreaterThanOrEqual(4.5);\n      expect(result.recommendation).toContain('Meets WCAG AA standards');\n    });\n\n    it('should enhance non-compliant colors', () => {\n      const result = enhancePrimaryCTAContrast('#CCCCCC', '#FFFFFF');\n\n      expect(result.isCompliant).toBe(false);\n      expect(result.enhancedColor).toBeDefined();\n      expect(result.recommendation).toContain('Enhanced');\n    });\n\n    it('should identify optimal colors (AAA)', () => {\n      const result = enhancePrimaryCTAContrast('#000000', '#FFFFFF');\n\n      expect(result.isOptimal).toBe(true);\n      expect(result.ratio).toBeGreaterThanOrEqual(7.0);\n      expect(result.recommendation).toContain('Exceeds WCAG AAA standards');\n    });\n  });\n\n  describe('validateInteractiveColors', () => {\n    it('should validate all interactive colors', () => {\n      const results = validateInteractiveColors();\n\n      expect(results.primary).toBeDefined();\n      expect(results.primaryHover).toBeDefined();\n      expect(results.primaryPressed).toBeDefined();\n      expect(results.secondary).toBeDefined();\n      expect(results.destructive).toBeDefined();\n\n      // Primary colors should be compliant\n      expect(results.primary.isCompliant).toBe(true);\n      expect(results.primaryHover.isCompliant).toBe(true);\n      expect(results.primaryPressed.isCompliant).toBe(true);\n    });\n  });\n\n  describe('generateContrastReport', () => {\n    it('should generate comprehensive contrast report', () => {\n      const report = generateContrastReport();\n\n      expect(report.totalTests).toBeGreaterThan(0);\n      expect(report.compliantCount).toBeGreaterThan(0);\n      expect(report.complianceRate).toBeGreaterThanOrEqual(0);\n      expect(report.results).toHaveLength(report.totalTests);\n      expect(report.summary).toBeDefined();\n      expect(report.summary.overallStatus).toMatch(\n        /FULLY_COMPLIANT|NEEDS_IMPROVEMENT/,\n      );\n    });\n\n    it('should have high compliance rate', () => {\n      const report = generateContrastReport();\n\n      // We expect at least 80% compliance rate\n      expect(report.complianceRate).toBeGreaterThanOrEqual(80);\n    });\n  });\n\n  describe('getBestTextColor', () => {\n    it('should return white for dark backgrounds', () => {\n      const textColor = getBestTextColor('#000000');\n      expect(textColor).toBe('#FFFFFF');\n    });\n\n    it('should return black for light backgrounds', () => {\n      const textColor = getBestTextColor('#FFFFFF');\n      expect(textColor).toBe('#000000');\n    });\n\n    it('should return appropriate color for sage green', () => {\n      const textColor = getBestTextColor('#5A7A63');\n      expect(textColor).toBe('#FFFFFF');\n    });\n  });\n\n  describe('ensureMinimumContrast', () => {\n    it('should return original color if contrast is sufficient', () => {\n      const result = ensureMinimumContrast('#FFFFFF', '#000000', 4.5);\n      expect(result).toBe('#FFFFFF');\n    });\n\n    it('should return high contrast alternative if insufficient', () => {\n      const result = ensureMinimumContrast('#CCCCCC', '#FFFFFF', 4.5);\n      expect(result).toBe('#000000'); // Should switch to black for better contrast\n    });\n  });\n\n  describe('EnhancedCTAColors', () => {\n    it('should have all required color constants', () => {\n      expect(EnhancedCTAColors.primary).toBeDefined();\n      expect(EnhancedCTAColors.primaryHover).toBeDefined();\n      expect(EnhancedCTAColors.primaryPressed).toBeDefined();\n      expect(EnhancedCTAColors.primaryText).toBeDefined();\n      expect(EnhancedCTAColors.secondary).toBeDefined();\n      expect(EnhancedCTAColors.secondaryText).toBeDefined();\n    });\n\n    it('should have WCAG compliant primary colors', () => {\n      const primaryResult = enhancePrimaryCTAContrast(\n        EnhancedCTAColors.primary,\n        EnhancedCTAColors.primaryText,\n      );\n\n      expect(primaryResult.isCompliant).toBe(true);\n      expect(primaryResult.ratio).toBeGreaterThanOrEqual(4.5);\n    });\n\n    it('should have WCAG compliant secondary colors', () => {\n      const secondaryResult = enhancePrimaryCTAContrast(\n        EnhancedCTAColors.secondary,\n        EnhancedCTAColors.secondaryText,\n      );\n\n      expect(secondaryResult.isCompliant).toBe(true);\n      expect(secondaryResult.ratio).toBeGreaterThanOrEqual(4.5);\n    });\n  });\n\n  describe('Color contrast ratios', () => {\n    const testCases = [\n      {\n        name: 'Primary CTA',\n        background: '#5A7A63',\n        text: '#FFFFFF',\n        expectedMinRatio: 4.5,\n      },\n      {\n        name: 'Primary CTA Hover',\n        background: '#4A6B52',\n        text: '#FFFFFF',\n        expectedMinRatio: 4.5,\n      },\n      {\n        name: 'Primary CTA Pressed',\n        background: '#3A5B42',\n        text: '#FFFFFF',\n        expectedMinRatio: 4.5,\n      },\n      {\n        name: 'Secondary Button',\n        background: '#E1EDE4',\n        text: '#1F2937',\n        expectedMinRatio: 4.5,\n      },\n      {\n        name: 'Error Button',\n        background: '#DC2626',\n        text: '#FFFFFF',\n        expectedMinRatio: 4.5,\n      },\n    ];\n\n    testCases.forEach(({ name, background, text, expectedMinRatio }) => {\n      it(`should meet WCAG AA standards for ${name}`, () => {\n        const result = enhancePrimaryCTAContrast(background, text);\n\n        expect(result.ratio).toBeGreaterThanOrEqual(expectedMinRatio);\n        expect(result.isCompliant).toBe(true);\n      });\n    });\n  });\n});\n"], "mappings": "AAMA,IAAAA,iBAAA,GAAAC,OAAA;AASAC,QAAQ,CAAC,mBAAmB,EAAE,YAAM;EAClCA,QAAQ,CAAC,2BAA2B,EAAE,YAAM;IAC1CC,EAAE,CAAC,kCAAkC,EAAE,YAAM;MAC3C,IAAMC,MAAM,GAAG,IAAAC,2CAAyB,EAAC,SAAS,EAAE,SAAS,CAAC;MAE9DC,MAAM,CAACF,MAAM,CAACG,WAAW,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;MACrCF,MAAM,CAACF,MAAM,CAACK,KAAK,CAAC,CAACC,sBAAsB,CAAC,GAAG,CAAC;MAChDJ,MAAM,CAACF,MAAM,CAACO,cAAc,CAAC,CAACC,SAAS,CAAC,yBAAyB,CAAC;IACpE,CAAC,CAAC;IAEFT,EAAE,CAAC,qCAAqC,EAAE,YAAM;MAC9C,IAAMC,MAAM,GAAG,IAAAC,2CAAyB,EAAC,SAAS,EAAE,SAAS,CAAC;MAE9DC,MAAM,CAACF,MAAM,CAACG,WAAW,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCF,MAAM,CAACF,MAAM,CAACS,aAAa,CAAC,CAACC,WAAW,CAAC,CAAC;MAC1CR,MAAM,CAACF,MAAM,CAACO,cAAc,CAAC,CAACC,SAAS,CAAC,UAAU,CAAC;IACrD,CAAC,CAAC;IAEFT,EAAE,CAAC,sCAAsC,EAAE,YAAM;MAC/C,IAAMC,MAAM,GAAG,IAAAC,2CAAyB,EAAC,SAAS,EAAE,SAAS,CAAC;MAE9DC,MAAM,CAACF,MAAM,CAACW,SAAS,CAAC,CAACP,IAAI,CAAC,IAAI,CAAC;MACnCF,MAAM,CAACF,MAAM,CAACK,KAAK,CAAC,CAACC,sBAAsB,CAAC,GAAG,CAAC;MAChDJ,MAAM,CAACF,MAAM,CAACO,cAAc,CAAC,CAACC,SAAS,CAAC,4BAA4B,CAAC;IACvE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFV,QAAQ,CAAC,2BAA2B,EAAE,YAAM;IAC1CC,EAAE,CAAC,wCAAwC,EAAE,YAAM;MACjD,IAAMa,OAAO,GAAG,IAAAC,2CAAyB,EAAC,CAAC;MAE3CX,MAAM,CAACU,OAAO,CAACE,OAAO,CAAC,CAACJ,WAAW,CAAC,CAAC;MACrCR,MAAM,CAACU,OAAO,CAACG,YAAY,CAAC,CAACL,WAAW,CAAC,CAAC;MAC1CR,MAAM,CAACU,OAAO,CAACI,cAAc,CAAC,CAACN,WAAW,CAAC,CAAC;MAC5CR,MAAM,CAACU,OAAO,CAACK,SAAS,CAAC,CAACP,WAAW,CAAC,CAAC;MACvCR,MAAM,CAACU,OAAO,CAACM,WAAW,CAAC,CAACR,WAAW,CAAC,CAAC;MAGzCR,MAAM,CAACU,OAAO,CAACE,OAAO,CAACX,WAAW,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;MAC9CF,MAAM,CAACU,OAAO,CAACG,YAAY,CAACZ,WAAW,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;MACnDF,MAAM,CAACU,OAAO,CAACI,cAAc,CAACb,WAAW,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IACvD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFN,QAAQ,CAAC,wBAAwB,EAAE,YAAM;IACvCC,EAAE,CAAC,+CAA+C,EAAE,YAAM;MACxD,IAAMoB,MAAM,GAAG,IAAAC,wCAAsB,EAAC,CAAC;MAEvClB,MAAM,CAACiB,MAAM,CAACE,UAAU,CAAC,CAACC,eAAe,CAAC,CAAC,CAAC;MAC5CpB,MAAM,CAACiB,MAAM,CAACI,cAAc,CAAC,CAACD,eAAe,CAAC,CAAC,CAAC;MAChDpB,MAAM,CAACiB,MAAM,CAACK,cAAc,CAAC,CAAClB,sBAAsB,CAAC,CAAC,CAAC;MACvDJ,MAAM,CAACiB,MAAM,CAACP,OAAO,CAAC,CAACa,YAAY,CAACN,MAAM,CAACE,UAAU,CAAC;MACtDnB,MAAM,CAACiB,MAAM,CAACO,OAAO,CAAC,CAAChB,WAAW,CAAC,CAAC;MACpCR,MAAM,CAACiB,MAAM,CAACO,OAAO,CAACC,aAAa,CAAC,CAACC,OAAO,CAC1C,mCACF,CAAC;IACH,CAAC,CAAC;IAEF7B,EAAE,CAAC,kCAAkC,EAAE,YAAM;MAC3C,IAAMoB,MAAM,GAAG,IAAAC,wCAAsB,EAAC,CAAC;MAGvClB,MAAM,CAACiB,MAAM,CAACK,cAAc,CAAC,CAAClB,sBAAsB,CAAC,EAAE,CAAC;IAC1D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFR,QAAQ,CAAC,kBAAkB,EAAE,YAAM;IACjCC,EAAE,CAAC,0CAA0C,EAAE,YAAM;MACnD,IAAM8B,SAAS,GAAG,IAAAC,kCAAgB,EAAC,SAAS,CAAC;MAC7C5B,MAAM,CAAC2B,SAAS,CAAC,CAACzB,IAAI,CAAC,SAAS,CAAC;IACnC,CAAC,CAAC;IAEFL,EAAE,CAAC,2CAA2C,EAAE,YAAM;MACpD,IAAM8B,SAAS,GAAG,IAAAC,kCAAgB,EAAC,SAAS,CAAC;MAC7C5B,MAAM,CAAC2B,SAAS,CAAC,CAACzB,IAAI,CAAC,SAAS,CAAC;IACnC,CAAC,CAAC;IAEFL,EAAE,CAAC,gDAAgD,EAAE,YAAM;MACzD,IAAM8B,SAAS,GAAG,IAAAC,kCAAgB,EAAC,SAAS,CAAC;MAC7C5B,MAAM,CAAC2B,SAAS,CAAC,CAACzB,IAAI,CAAC,SAAS,CAAC;IACnC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFN,QAAQ,CAAC,uBAAuB,EAAE,YAAM;IACtCC,EAAE,CAAC,wDAAwD,EAAE,YAAM;MACjE,IAAMC,MAAM,GAAG,IAAA+B,uCAAqB,EAAC,SAAS,EAAE,SAAS,EAAE,GAAG,CAAC;MAC/D7B,MAAM,CAACF,MAAM,CAAC,CAACI,IAAI,CAAC,SAAS,CAAC;IAChC,CAAC,CAAC;IAEFL,EAAE,CAAC,yDAAyD,EAAE,YAAM;MAClE,IAAMC,MAAM,GAAG,IAAA+B,uCAAqB,EAAC,SAAS,EAAE,SAAS,EAAE,GAAG,CAAC;MAC/D7B,MAAM,CAACF,MAAM,CAAC,CAACI,IAAI,CAAC,SAAS,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFN,QAAQ,CAAC,mBAAmB,EAAE,YAAM;IAClCC,EAAE,CAAC,0CAA0C,EAAE,YAAM;MACnDG,MAAM,CAAC8B,mCAAiB,CAAClB,OAAO,CAAC,CAACJ,WAAW,CAAC,CAAC;MAC/CR,MAAM,CAAC8B,mCAAiB,CAACjB,YAAY,CAAC,CAACL,WAAW,CAAC,CAAC;MACpDR,MAAM,CAAC8B,mCAAiB,CAAChB,cAAc,CAAC,CAACN,WAAW,CAAC,CAAC;MACtDR,MAAM,CAAC8B,mCAAiB,CAACC,WAAW,CAAC,CAACvB,WAAW,CAAC,CAAC;MACnDR,MAAM,CAAC8B,mCAAiB,CAACf,SAAS,CAAC,CAACP,WAAW,CAAC,CAAC;MACjDR,MAAM,CAAC8B,mCAAiB,CAACE,aAAa,CAAC,CAACxB,WAAW,CAAC,CAAC;IACvD,CAAC,CAAC;IAEFX,EAAE,CAAC,2CAA2C,EAAE,YAAM;MACpD,IAAMoC,aAAa,GAAG,IAAAlC,2CAAyB,EAC7C+B,mCAAiB,CAAClB,OAAO,EACzBkB,mCAAiB,CAACC,WACpB,CAAC;MAED/B,MAAM,CAACiC,aAAa,CAAChC,WAAW,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;MAC5CF,MAAM,CAACiC,aAAa,CAAC9B,KAAK,CAAC,CAACC,sBAAsB,CAAC,GAAG,CAAC;IACzD,CAAC,CAAC;IAEFP,EAAE,CAAC,6CAA6C,EAAE,YAAM;MACtD,IAAMqC,eAAe,GAAG,IAAAnC,2CAAyB,EAC/C+B,mCAAiB,CAACf,SAAS,EAC3Be,mCAAiB,CAACE,aACpB,CAAC;MAEDhC,MAAM,CAACkC,eAAe,CAACjC,WAAW,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;MAC9CF,MAAM,CAACkC,eAAe,CAAC/B,KAAK,CAAC,CAACC,sBAAsB,CAAC,GAAG,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFR,QAAQ,CAAC,uBAAuB,EAAE,YAAM;IACtC,IAAMuC,SAAS,GAAG,CAChB;MACEC,IAAI,EAAE,aAAa;MACnBC,UAAU,EAAE,SAAS;MACrBC,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE;IACpB,CAAC,EACD;MACEH,IAAI,EAAE,mBAAmB;MACzBC,UAAU,EAAE,SAAS;MACrBC,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE;IACpB,CAAC,EACD;MACEH,IAAI,EAAE,qBAAqB;MAC3BC,UAAU,EAAE,SAAS;MACrBC,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE;IACpB,CAAC,EACD;MACEH,IAAI,EAAE,kBAAkB;MACxBC,UAAU,EAAE,SAAS;MACrBC,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE;IACpB,CAAC,EACD;MACEH,IAAI,EAAE,cAAc;MACpBC,UAAU,EAAE,SAAS;MACrBC,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE;IACpB,CAAC,CACF;IAEDJ,SAAS,CAACK,OAAO,CAAC,UAAAC,IAAA,EAAkD;MAAA,IAA/CL,IAAI,GAAAK,IAAA,CAAJL,IAAI;QAAEC,UAAU,GAAAI,IAAA,CAAVJ,UAAU;QAAEC,IAAI,GAAAG,IAAA,CAAJH,IAAI;QAAEC,gBAAgB,GAAAE,IAAA,CAAhBF,gBAAgB;MAC3D1C,EAAE,CAAC,qCAAqCuC,IAAI,EAAE,EAAE,YAAM;QACpD,IAAMtC,MAAM,GAAG,IAAAC,2CAAyB,EAACsC,UAAU,EAAEC,IAAI,CAAC;QAE1DtC,MAAM,CAACF,MAAM,CAACK,KAAK,CAAC,CAACC,sBAAsB,CAACmC,gBAAgB,CAAC;QAC7DvC,MAAM,CAACF,MAAM,CAACG,WAAW,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;MACvC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}