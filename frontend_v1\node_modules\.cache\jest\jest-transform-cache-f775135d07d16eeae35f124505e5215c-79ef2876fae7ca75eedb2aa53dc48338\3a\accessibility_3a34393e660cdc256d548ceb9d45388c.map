{"version": 3, "names": ["_reactNative", "require", "WCAG_CONSTANTS", "exports", "CONTRAST_RATIOS", "NORMAL_TEXT", "LARGE_TEXT", "NON_TEXT", "TOUCH_TARGET", "MIN_SIZE", "RECOMMENDED_SIZE", "ANIMATION", "MAX_DURATION", "REDUCED_MOTION_DURATION", "TEXT", "LARGE_TEXT_THRESHOLD", "ScreenReaderUtils", "_classCallCheck2", "default", "_createClass2", "key", "value", "_isScreenReaderEnabled", "_asyncToGenerator2", "AccessibilityInfo", "isScreenReaderEnabled", "error", "console", "warn", "apply", "arguments", "announceForAccessibility", "message", "Platform", "OS", "setAccessibilityFocus", "reactTag", "generateFormFieldLabel", "label", "required", "length", "undefined", "accessibleLabel", "generateInteractionHint", "action", "additionalInfo", "hint", "ColorContrastUtils", "getRelativeLuminance", "color", "hex", "replace", "r", "parseInt", "substr", "g", "b", "sRGB", "map", "c", "Math", "pow", "getContrastRatio", "color1", "color2", "lum1", "lum2", "lighter", "max", "darker", "min", "meetsWCAGAA", "foreground", "background", "isLargeText", "ratio", "requiredRatio", "suggestAccessibleColor", "factors", "factor", "newR", "floor", "newG", "newB", "newColor", "toString", "padStart", "FocusManagementUtils", "pushFocus", "focusStack", "push", "popFocus", "pop", "previousFocus", "clearFocusStack", "createFocusTrap", "firstElement", "lastElement", "onKeyPress", "event", "shift<PERSON>ey", "target", "preventDefault", "SemanticMarkupUtils", "generateHeadingProps", "level", "text", "accessibilityRole", "accessibilityLevel", "accessibilityLabel", "generateListProps", "itemCount", "generateListItemProps", "index", "total", "content", "generateButtonProps", "state", "accessibilityHint", "accessibilityState", "generateInputProps", "accessibilityValue", "disabled", "VALID_ACCESSIBILITY_ROLES", "isValidAccessibilityRole", "role", "includes", "getSafeAccessibilityRole", "AccessibilityTestUtils", "validateAccessibilityProps", "props", "_props$style", "_props$style2", "issues", "onPress", "join", "children", "style", "width", "height", "generateAccessibilityReport", "componentTree", "_this", "passed", "failed", "for<PERSON>ach", "component", "componentIssues", "type", "AdvancedAccessibilityUtils", "announceLiveRegion", "priority", "setTimeout", "announceContentChange", "element", "changeType", "details", "announceFormValidation", "fieldName", "<PERSON><PERSON><PERSON><PERSON>", "errorMessage", "announceProgress", "current", "percentage", "round", "announceLoadingState", "isLoading", "context", "AccessibilityMonitoringUtils", "trackAccessibilityUsage", "_trackAccessibilityUsage", "stats", "screenReaderUsage", "keyboardNavigation", "voiceControlUsage", "reducedMotionPreference", "isReduceMotionEnabled", "validateCompliance", "_validateCompliance", "compliance", "screenReaderSupport", "colorContrast", "touchTargets", "textScaling", "reducedMotion"], "sources": ["accessibility.ts"], "sourcesContent": ["/**\n * Accessibility Utilities\n *\n * Comprehensive accessibility utilities for WCAG 2.2 AA compliance.\n * Implements REC-ACC-001, REC-ACC-002, and REC-ACC-003 requirements.\n *\n * Features:\n * - Screen reader support utilities\n * - Keyboard navigation helpers\n * - Color contrast validation\n * - Focus management\n * - Semantic markup helpers\n * - Accessibility testing utilities\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport { Platform, AccessibilityInfo } from 'react-native';\n\n// WCAG 2.2 AA compliance constants\nexport const WCAG_CONSTANTS = {\n  // Color contrast ratios\n  CONTRAST_RATIOS: {\n    NORMAL_TEXT: 4.5,\n    LARGE_TEXT: 3.0,\n    NON_TEXT: 3.0,\n  },\n\n  // Touch target sizes (44x44 minimum)\n  TOUCH_TARGET: {\n    MIN_SIZE: 44,\n    RECOMMENDED_SIZE: 48,\n  },\n\n  // Animation timing\n  ANIMATION: {\n    MAX_DURATION: 5000, // 5 seconds max for auto-playing content\n    REDUCED_MOTION_DURATION: 200, // Reduced motion preference\n  },\n\n  // Text sizing\n  TEXT: {\n    MIN_SIZE: 12,\n    LARGE_TEXT_THRESHOLD: 18,\n  },\n} as const;\n\n// Accessibility role types\nexport type AccessibilityRole =\n  | 'button'\n  | 'link'\n  | 'search'\n  | 'image'\n  | 'keyboardkey'\n  | 'text'\n  | 'adjustable'\n  | 'imagebutton'\n  | 'header'\n  | 'summary'\n  | 'alert'\n  | 'checkbox'\n  | 'combobox'\n  | 'menu'\n  | 'menubar'\n  | 'menuitem'\n  | 'progressbar'\n  | 'radio'\n  | 'radiogroup'\n  | 'scrollbar'\n  | 'spinbutton'\n  | 'switch'\n  | 'tab'\n  | 'tablist'\n  | 'timer'\n  | 'toolbar'\n  | 'grid'\n  | 'list'\n  | 'listitem';\n\n// Accessibility state interface\nexport interface AccessibilityState {\n  disabled?: boolean;\n  selected?: boolean;\n  checked?: boolean | 'mixed';\n  busy?: boolean;\n  expanded?: boolean;\n}\n\n// Screen reader utilities\nexport class ScreenReaderUtils {\n  /**\n   * Check if screen reader is enabled\n   */\n  static async isScreenReaderEnabled(): Promise<boolean> {\n    try {\n      return await AccessibilityInfo.isScreenReaderEnabled();\n    } catch (error) {\n      console.warn('Failed to check screen reader status:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Announce message to screen reader\n   */\n  static announceForAccessibility(message: string): void {\n    if (Platform.OS === 'ios' || Platform.OS === 'android') {\n      AccessibilityInfo.announceForAccessibility(message);\n    }\n  }\n\n  /**\n   * Set accessibility focus to element\n   */\n  static setAccessibilityFocus(reactTag: number): void {\n    if (Platform.OS === 'ios' || Platform.OS === 'android') {\n      AccessibilityInfo.setAccessibilityFocus(reactTag);\n    }\n  }\n\n  /**\n   * Generate accessible label for form fields\n   */\n  static generateFormFieldLabel(\n    label: string,\n    required: boolean = false,\n    error?: string,\n  ): string {\n    let accessibleLabel = label;\n\n    if (required) {\n      accessibleLabel += ', required';\n    }\n\n    if (error) {\n      accessibleLabel += `, error: ${error}`;\n    }\n\n    return accessibleLabel;\n  }\n\n  /**\n   * Generate accessible hint for interactive elements\n   */\n  static generateInteractionHint(\n    action: string,\n    additionalInfo?: string,\n  ): string {\n    let hint = `Double tap to ${action}`;\n\n    if (additionalInfo) {\n      hint += `. ${additionalInfo}`;\n    }\n\n    return hint;\n  }\n}\n\n// Color contrast utilities\nexport class ColorContrastUtils {\n  /**\n   * Calculate relative luminance of a color\n   */\n  static getRelativeLuminance(color: string): number {\n    // Convert hex to RGB\n    const hex = color.replace('#', '');\n    const r = parseInt(hex.substr(0, 2), 16) / 255;\n    const g = parseInt(hex.substr(2, 2), 16) / 255;\n    const b = parseInt(hex.substr(4, 2), 16) / 255;\n\n    // Apply gamma correction\n    const sRGB = [r, g, b].map(c => {\n      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);\n    });\n\n    // Calculate relative luminance\n    return 0.2126 * sRGB[0] + 0.7152 * sRGB[1] + 0.0722 * sRGB[2];\n  }\n\n  /**\n   * Calculate contrast ratio between two colors\n   */\n  static getContrastRatio(color1: string, color2: string): number {\n    const lum1 = this.getRelativeLuminance(color1);\n    const lum2 = this.getRelativeLuminance(color2);\n\n    const lighter = Math.max(lum1, lum2);\n    const darker = Math.min(lum1, lum2);\n\n    return (lighter + 0.05) / (darker + 0.05);\n  }\n\n  /**\n   * Check if color combination meets WCAG AA standards\n   */\n  static meetsWCAGAA(\n    foreground: string,\n    background: string,\n    isLargeText: boolean = false,\n  ): boolean {\n    const ratio = this.getContrastRatio(foreground, background);\n    const requiredRatio = isLargeText\n      ? WCAG_CONSTANTS.CONTRAST_RATIOS.LARGE_TEXT\n      : WCAG_CONSTANTS.CONTRAST_RATIOS.NORMAL_TEXT;\n\n    return ratio >= requiredRatio;\n  }\n\n  /**\n   * Suggest accessible color alternatives\n   */\n  static suggestAccessibleColor(\n    foreground: string,\n    background: string,\n    isLargeText: boolean = false,\n  ): string | null {\n    if (this.meetsWCAGAA(foreground, background, isLargeText)) {\n      return null; // Already accessible\n    }\n\n    // Simple algorithm to darken/lighten color\n    // In production, this would be more sophisticated\n    const hex = foreground.replace('#', '');\n    const r = parseInt(hex.substr(0, 2), 16);\n    const g = parseInt(hex.substr(2, 2), 16);\n    const b = parseInt(hex.substr(4, 2), 16);\n\n    // Try multiple darkening factors\n    const factors = [0.7, 0.5, 0.3, 0.1];\n\n    for (const factor of factors) {\n      const newR = Math.floor(r * factor);\n      const newG = Math.floor(g * factor);\n      const newB = Math.floor(b * factor);\n\n      const newColor = `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;\n\n      if (this.meetsWCAGAA(newColor, background, isLargeText)) {\n        return newColor;\n      }\n    }\n\n    // If darkening doesn't work, try pure black\n    return this.meetsWCAGAA('#000000', background, isLargeText)\n      ? '#000000'\n      : null;\n  }\n}\n\n// Focus management utilities\nexport class FocusManagementUtils {\n  private static focusStack: number[] = [];\n\n  /**\n   * Push focus to stack and set new focus\n   */\n  static pushFocus(reactTag: number): void {\n    // Store current focus (simplified - in production would get actual focused element)\n    this.focusStack.push(reactTag);\n    ScreenReaderUtils.setAccessibilityFocus(reactTag);\n  }\n\n  /**\n   * Pop focus from stack and restore previous focus\n   */\n  static popFocus(): void {\n    this.focusStack.pop(); // Remove current\n    const previousFocus = this.focusStack[this.focusStack.length - 1];\n\n    if (previousFocus) {\n      ScreenReaderUtils.setAccessibilityFocus(previousFocus);\n    }\n  }\n\n  /**\n   * Clear focus stack\n   */\n  static clearFocusStack(): void {\n    this.focusStack = [];\n  }\n\n  /**\n   * Generate focus trap for modals\n   */\n  static createFocusTrap(firstElement: number, lastElement: number) {\n    return {\n      onKeyPress: (event: any) => {\n        if (event.key === 'Tab') {\n          if (event.shiftKey) {\n            // Shift+Tab - move to previous element\n            if (event.target === firstElement) {\n              event.preventDefault();\n              ScreenReaderUtils.setAccessibilityFocus(lastElement);\n            }\n          } else {\n            // Tab - move to next element\n            if (event.target === lastElement) {\n              event.preventDefault();\n              ScreenReaderUtils.setAccessibilityFocus(firstElement);\n            }\n          }\n        }\n      },\n    };\n  }\n}\n\n// Semantic markup helpers\nexport class SemanticMarkupUtils {\n  /**\n   * Generate semantic props for headings\n   */\n  static generateHeadingProps(level: 1 | 2 | 3 | 4 | 5 | 6, text: string) {\n    return {\n      accessibilityRole: 'header' as AccessibilityRole,\n      accessibilityLevel: level,\n      accessibilityLabel: text,\n    };\n  }\n\n  /**\n   * Generate semantic props for lists\n   */\n  static generateListProps(itemCount: number) {\n    return {\n      accessibilityRole: 'list' as AccessibilityRole,\n      accessibilityLabel: `List with ${itemCount} items`,\n    };\n  }\n\n  /**\n   * Generate semantic props for list items\n   */\n  static generateListItemProps(index: number, total: number, content: string) {\n    return {\n      accessibilityRole: 'listitem' as AccessibilityRole,\n      accessibilityLabel: `${content}, ${index + 1} of ${total}`,\n    };\n  }\n\n  /**\n   * Generate semantic props for buttons\n   */\n  static generateButtonProps(\n    label: string,\n    action?: string,\n    state?: AccessibilityState,\n  ) {\n    return {\n      accessibilityRole: 'button' as AccessibilityRole,\n      accessibilityLabel: label,\n      accessibilityHint: action\n        ? ScreenReaderUtils.generateInteractionHint(action)\n        : undefined,\n      accessibilityState: state,\n    };\n  }\n\n  /**\n   * Generate semantic props for form inputs\n   */\n  static generateInputProps(\n    label: string,\n    value?: string,\n    required: boolean = false,\n    error?: string,\n  ) {\n    return {\n      accessibilityLabel: ScreenReaderUtils.generateFormFieldLabel(\n        label,\n        required,\n        error,\n      ),\n      accessibilityValue: value ? { text: value } : undefined,\n      accessibilityState: {\n        disabled: false,\n      } as AccessibilityState,\n    };\n  }\n}\n\n// Valid React Native accessibility roles\nexport const VALID_ACCESSIBILITY_ROLES: AccessibilityRole[] = [\n  'button',\n  'link',\n  'search',\n  'image',\n  'keyboardkey',\n  'text',\n  'adjustable',\n  'imagebutton',\n  'header',\n  'summary',\n  'alert',\n  'checkbox',\n  'combobox',\n  'menu',\n  'menubar',\n  'menuitem',\n  'progressbar',\n  'radio',\n  'radiogroup',\n  'scrollbar',\n  'spinbutton',\n  'switch',\n  'tab',\n  'tablist',\n  'timer',\n  'toolbar',\n  'grid',\n  'list',\n  'listitem',\n  'none',\n];\n\n/**\n * Validate if an accessibility role is supported in React Native\n */\nexport const isValidAccessibilityRole = (role: string): boolean => {\n  return VALID_ACCESSIBILITY_ROLES.includes(role as AccessibilityRole);\n};\n\n/**\n * Get a safe accessibility role, falling back to 'none' for invalid roles\n */\nexport const getSafeAccessibilityRole = (role: string): AccessibilityRole => {\n  return isValidAccessibilityRole(role) ? (role as AccessibilityRole) : 'none';\n};\n\n// Accessibility testing utilities\nexport class AccessibilityTestUtils {\n  /**\n   * Validate component accessibility props\n   */\n  static validateAccessibilityProps(props: any): string[] {\n    const issues: string[] = [];\n\n    // Check for missing accessibility role\n    if (!props.accessibilityRole && props.onPress) {\n      issues.push('Interactive element missing accessibilityRole');\n    }\n\n    // Check for invalid accessibility role\n    if (\n      props.accessibilityRole &&\n      !isValidAccessibilityRole(props.accessibilityRole)\n    ) {\n      issues.push(\n        `Invalid accessibility role: ${props.accessibilityRole}. Use one of: ${VALID_ACCESSIBILITY_ROLES.join(', ')}`,\n      );\n    }\n\n    // Check for missing accessibility label\n    if (!props.accessibilityLabel && !props.children) {\n      issues.push('Element missing accessibilityLabel or text content');\n    }\n\n    // Check touch target size\n    if (props.style?.width && props.style?.height) {\n      const width = props.style.width;\n      const height = props.style.height;\n\n      if (\n        width < WCAG_CONSTANTS.TOUCH_TARGET.MIN_SIZE ||\n        height < WCAG_CONSTANTS.TOUCH_TARGET.MIN_SIZE\n      ) {\n        issues.push(\n          `Touch target too small: ${width}x${height}. Minimum: ${WCAG_CONSTANTS.TOUCH_TARGET.MIN_SIZE}x${WCAG_CONSTANTS.TOUCH_TARGET.MIN_SIZE}`,\n        );\n      }\n    }\n\n    return issues;\n  }\n\n  /**\n   * Generate accessibility report for component tree\n   */\n  static generateAccessibilityReport(componentTree: any[]): {\n    passed: number;\n    failed: number;\n    issues: Array<{ component: string; issues: string[] }>;\n  } {\n    let passed = 0;\n    let failed = 0;\n    const issues: Array<{ component: string; issues: string[] }> = [];\n\n    componentTree.forEach((component, index) => {\n      const componentIssues = this.validateAccessibilityProps(component.props);\n\n      if (componentIssues.length === 0) {\n        passed++;\n      } else {\n        failed++;\n        issues.push({\n          component: component.type || `Component ${index}`,\n          issues: componentIssues,\n        });\n      }\n    });\n\n    return { passed, failed, issues };\n  }\n}\n\n// Advanced accessibility features\nexport const AdvancedAccessibilityUtils = {\n  // Live region announcements with priority\n  announceLiveRegion: (\n    message: string,\n    priority: 'polite' | 'assertive' = 'polite',\n  ) => {\n    if (Platform.OS === 'ios') {\n      AccessibilityInfo.announceForAccessibility(message);\n    } else {\n      // For Android, we can simulate live regions\n      setTimeout(\n        () => {\n          AccessibilityInfo.announceForAccessibility(message);\n        },\n        priority === 'assertive' ? 0 : 100,\n      );\n    }\n  },\n\n  // Dynamic content updates\n  announceContentChange: (\n    element: string,\n    changeType: 'added' | 'removed' | 'updated',\n    details?: string,\n  ) => {\n    const message = `${element} ${changeType}${details ? `: ${details}` : ''}`;\n    AdvancedAccessibilityUtils.announceLiveRegion(message, 'polite');\n  },\n\n  // Form validation announcements\n  announceFormValidation: (\n    fieldName: string,\n    isValid: boolean,\n    errorMessage?: string,\n  ) => {\n    if (isValid) {\n      AdvancedAccessibilityUtils.announceLiveRegion(\n        `${fieldName} is valid`,\n        'polite',\n      );\n    } else {\n      AdvancedAccessibilityUtils.announceLiveRegion(\n        `${fieldName} error: ${errorMessage || 'Invalid input'}`,\n        'assertive',\n      );\n    }\n  },\n\n  // Progress announcements\n  announceProgress: (\n    current: number,\n    total: number,\n    label: string = 'Progress',\n  ) => {\n    const percentage = Math.round((current / total) * 100);\n    AdvancedAccessibilityUtils.announceLiveRegion(\n      `${label}: ${percentage}% complete, ${current} of ${total}`,\n      'polite',\n    );\n  },\n\n  // Loading state announcements\n  announceLoadingState: (isLoading: boolean, context: string = 'Content') => {\n    if (isLoading) {\n      AdvancedAccessibilityUtils.announceLiveRegion(\n        `${context} loading`,\n        'polite',\n      );\n    } else {\n      AdvancedAccessibilityUtils.announceLiveRegion(\n        `${context} loaded`,\n        'polite',\n      );\n    }\n  },\n};\n\n// Accessibility performance monitoring\nexport const AccessibilityMonitoringUtils = {\n  // Track accessibility interactions\n  trackAccessibilityUsage: async () => {\n    const stats = {\n      screenReaderUsage: 0,\n      keyboardNavigation: 0,\n      voiceControlUsage: 0,\n      reducedMotionPreference: false,\n    };\n\n    try {\n      // Monitor screen reader usage\n      stats.screenReaderUsage =\n        (await AccessibilityInfo.isScreenReaderEnabled()) ? 1 : 0;\n\n      // Monitor reduced motion preference\n      if (Platform.OS === 'ios') {\n        stats.reducedMotionPreference =\n          await AccessibilityInfo.isReduceMotionEnabled();\n      }\n    } catch (error) {\n      console.warn('Accessibility usage tracking failed:', error);\n    }\n\n    return stats;\n  },\n\n  // Validate accessibility compliance\n  validateCompliance: async () => {\n    const compliance = {\n      screenReaderSupport: false,\n      keyboardNavigation: true, // Assume true for React Native\n      colorContrast: true, // Would need specific color checking\n      touchTargets: true, // Would need specific size checking\n      textScaling: true, // React Native handles this\n      reducedMotion: false,\n    };\n\n    try {\n      compliance.screenReaderSupport =\n        await AccessibilityInfo.isScreenReaderEnabled();\n\n      if (Platform.OS === 'ios') {\n        compliance.reducedMotion =\n          await AccessibilityInfo.isReduceMotionEnabled();\n      }\n    } catch (error) {\n      console.warn('Accessibility compliance check failed:', error);\n    }\n\n    return compliance;\n  },\n};\n\n// Export all utilities\nexport {\n  ScreenReaderUtils,\n  ColorContrastUtils,\n  FocusManagementUtils,\n  SemanticMarkupUtils,\n  AccessibilityTestUtils,\n  AdvancedAccessibilityUtils,\n  AccessibilityMonitoringUtils,\n};\n"], "mappings": ";;;;;;;;AAkBA,IAAAA,YAAA,GAAAC,OAAA;AAGO,IAAMC,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAG;EAE5BE,eAAe,EAAE;IACfC,WAAW,EAAE,GAAG;IAChBC,UAAU,EAAE,GAAG;IACfC,QAAQ,EAAE;EACZ,CAAC;EAGDC,YAAY,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,gBAAgB,EAAE;EACpB,CAAC;EAGDC,SAAS,EAAE;IACTC,YAAY,EAAE,IAAI;IAClBC,uBAAuB,EAAE;EAC3B,CAAC;EAGDC,IAAI,EAAE;IACJL,QAAQ,EAAE,EAAE;IACZM,oBAAoB,EAAE;EACxB;AACF,CAAU;AAAC,IA4CEC,iBAAiB,GAAAb,OAAA,CAAAa,iBAAA,GAAAb,OAAA,CAAAa,iBAAA;EAAA,SAAAA,kBAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAF,iBAAA;EAAA;EAAA,WAAAG,aAAA,CAAAD,OAAA,EAAAF,iBAAA;IAAAI,GAAA;IAAAC,KAAA;MAAA,IAAAC,sBAAA,OAAAC,kBAAA,CAAAL,OAAA,EAI5B,aAAuD;QACrD,IAAI;UACF,aAAaM,8BAAiB,CAACC,qBAAqB,CAAC,CAAC;QACxD,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACC,IAAI,CAAC,uCAAuC,EAAEF,KAAK,CAAC;UAC5D,OAAO,KAAK;QACd;MACF,CAAC;MAAA,SAPYD,qBAAqBA,CAAA;QAAA,OAAAH,sBAAA,CAAAO,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArBL,qBAAqB;IAAA;EAAA;IAAAL,GAAA;IAAAC,KAAA,EAYlC,SAAOU,wBAAwBA,CAACC,OAAe,EAAQ;MACrD,IAAIC,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAID,qBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;QACtDV,8BAAiB,CAACO,wBAAwB,CAACC,OAAO,CAAC;MACrD;IACF;EAAC;IAAAZ,GAAA;IAAAC,KAAA,EAKD,SAAOc,qBAAqBA,CAACC,QAAgB,EAAQ;MACnD,IAAIH,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAID,qBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;QACtDV,8BAAiB,CAACW,qBAAqB,CAACC,QAAQ,CAAC;MACnD;IACF;EAAC;IAAAhB,GAAA;IAAAC,KAAA,EAKD,SAAOgB,sBAAsBA,CAC3BC,KAAa,EAGL;MAAA,IAFRC,QAAiB,GAAAT,SAAA,CAAAU,MAAA,QAAAV,SAAA,QAAAW,SAAA,GAAAX,SAAA,MAAG,KAAK;MAAA,IACzBJ,KAAc,GAAAI,SAAA,CAAAU,MAAA,OAAAV,SAAA,MAAAW,SAAA;MAEd,IAAIC,eAAe,GAAGJ,KAAK;MAE3B,IAAIC,QAAQ,EAAE;QACZG,eAAe,IAAI,YAAY;MACjC;MAEA,IAAIhB,KAAK,EAAE;QACTgB,eAAe,IAAI,YAAYhB,KAAK,EAAE;MACxC;MAEA,OAAOgB,eAAe;IACxB;EAAC;IAAAtB,GAAA;IAAAC,KAAA,EAKD,SAAOsB,uBAAuBA,CAC5BC,MAAc,EACdC,cAAuB,EACf;MACR,IAAIC,IAAI,GAAG,iBAAiBF,MAAM,EAAE;MAEpC,IAAIC,cAAc,EAAE;QAClBC,IAAI,IAAI,KAAKD,cAAc,EAAE;MAC/B;MAEA,OAAOC,IAAI;IACb;EAAC;AAAA;AAAA,IAIUC,kBAAkB,GAAA5C,OAAA,CAAA4C,kBAAA,GAAA5C,OAAA,CAAA4C,kBAAA;EAAA,SAAAA,mBAAA;IAAA,IAAA9B,gBAAA,CAAAC,OAAA,QAAA6B,kBAAA;EAAA;EAAA,WAAA5B,aAAA,CAAAD,OAAA,EAAA6B,kBAAA;IAAA3B,GAAA;IAAAC,KAAA,EAI7B,SAAO2B,oBAAoBA,CAACC,KAAa,EAAU;MAEjD,IAAMC,GAAG,GAAGD,KAAK,CAACE,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;MAClC,IAAMC,CAAC,GAAGC,QAAQ,CAACH,GAAG,CAACI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG;MAC9C,IAAMC,CAAC,GAAGF,QAAQ,CAACH,GAAG,CAACI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG;MAC9C,IAAME,CAAC,GAAGH,QAAQ,CAACH,GAAG,CAACI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG;MAG9C,IAAMG,IAAI,GAAG,CAACL,CAAC,EAAEG,CAAC,EAAEC,CAAC,CAAC,CAACE,GAAG,CAAC,UAAAC,CAAC,EAAI;QAC9B,OAAOA,CAAC,IAAI,OAAO,GAAGA,CAAC,GAAG,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAACF,CAAC,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;MACtE,CAAC,CAAC;MAGF,OAAO,MAAM,GAAGF,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,GAAGA,IAAI,CAAC,CAAC,CAAC;IAC/D;EAAC;IAAArC,GAAA;IAAAC,KAAA,EAKD,SAAOyC,gBAAgBA,CAACC,MAAc,EAAEC,MAAc,EAAU;MAC9D,IAAMC,IAAI,GAAG,IAAI,CAACjB,oBAAoB,CAACe,MAAM,CAAC;MAC9C,IAAMG,IAAI,GAAG,IAAI,CAAClB,oBAAoB,CAACgB,MAAM,CAAC;MAE9C,IAAMG,OAAO,GAAGP,IAAI,CAACQ,GAAG,CAACH,IAAI,EAAEC,IAAI,CAAC;MACpC,IAAMG,MAAM,GAAGT,IAAI,CAACU,GAAG,CAACL,IAAI,EAAEC,IAAI,CAAC;MAEnC,OAAO,CAACC,OAAO,GAAG,IAAI,KAAKE,MAAM,GAAG,IAAI,CAAC;IAC3C;EAAC;IAAAjD,GAAA;IAAAC,KAAA,EAKD,SAAOkD,WAAWA,CAChBC,UAAkB,EAClBC,UAAkB,EAET;MAAA,IADTC,WAAoB,GAAA5C,SAAA,CAAAU,MAAA,QAAAV,SAAA,QAAAW,SAAA,GAAAX,SAAA,MAAG,KAAK;MAE5B,IAAM6C,KAAK,GAAG,IAAI,CAACb,gBAAgB,CAACU,UAAU,EAAEC,UAAU,CAAC;MAC3D,IAAMG,aAAa,GAAGF,WAAW,GAC7BxE,cAAc,CAACE,eAAe,CAACE,UAAU,GACzCJ,cAAc,CAACE,eAAe,CAACC,WAAW;MAE9C,OAAOsE,KAAK,IAAIC,aAAa;IAC/B;EAAC;IAAAxD,GAAA;IAAAC,KAAA,EAKD,SAAOwD,sBAAsBA,CAC3BL,UAAkB,EAClBC,UAAkB,EAEH;MAAA,IADfC,WAAoB,GAAA5C,SAAA,CAAAU,MAAA,QAAAV,SAAA,QAAAW,SAAA,GAAAX,SAAA,MAAG,KAAK;MAE5B,IAAI,IAAI,CAACyC,WAAW,CAACC,UAAU,EAAEC,UAAU,EAAEC,WAAW,CAAC,EAAE;QACzD,OAAO,IAAI;MACb;MAIA,IAAMxB,GAAG,GAAGsB,UAAU,CAACrB,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;MACvC,IAAMC,CAAC,GAAGC,QAAQ,CAACH,GAAG,CAACI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;MACxC,IAAMC,CAAC,GAAGF,QAAQ,CAACH,GAAG,CAACI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;MACxC,IAAME,CAAC,GAAGH,QAAQ,CAACH,GAAG,CAACI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;MAGxC,IAAMwB,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAEpC,KAAK,IAAMC,MAAM,IAAID,OAAO,EAAE;QAC5B,IAAME,IAAI,GAAGpB,IAAI,CAACqB,KAAK,CAAC7B,CAAC,GAAG2B,MAAM,CAAC;QACnC,IAAMG,IAAI,GAAGtB,IAAI,CAACqB,KAAK,CAAC1B,CAAC,GAAGwB,MAAM,CAAC;QACnC,IAAMI,IAAI,GAAGvB,IAAI,CAACqB,KAAK,CAACzB,CAAC,GAAGuB,MAAM,CAAC;QAEnC,IAAMK,QAAQ,GAAG,IAAIJ,IAAI,CAACK,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAGJ,IAAI,CAACG,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAGH,IAAI,CAACE,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QAEnI,IAAI,IAAI,CAACf,WAAW,CAACa,QAAQ,EAAEX,UAAU,EAAEC,WAAW,CAAC,EAAE;UACvD,OAAOU,QAAQ;QACjB;MACF;MAGA,OAAO,IAAI,CAACb,WAAW,CAAC,SAAS,EAAEE,UAAU,EAAEC,WAAW,CAAC,GACvD,SAAS,GACT,IAAI;IACV;EAAC;AAAA;AAAA,IAIUa,oBAAoB,GAAApF,OAAA,CAAAoF,oBAAA,GAAApF,OAAA,CAAAoF,oBAAA;EAAA,SAAAA,qBAAA;IAAA,IAAAtE,gBAAA,CAAAC,OAAA,QAAAqE,oBAAA;EAAA;EAAA,WAAApE,aAAA,CAAAD,OAAA,EAAAqE,oBAAA;IAAAnE,GAAA;IAAAC,KAAA,EAM/B,SAAOmE,SAASA,CAACpD,QAAgB,EAAQ;MAEvC,IAAI,CAACqD,UAAU,CAACC,IAAI,CAACtD,QAAQ,CAAC;MAC9BpB,iBAAiB,CAACmB,qBAAqB,CAACC,QAAQ,CAAC;IACnD;EAAC;IAAAhB,GAAA;IAAAC,KAAA,EAKD,SAAOsE,QAAQA,CAAA,EAAS;MACtB,IAAI,CAACF,UAAU,CAACG,GAAG,CAAC,CAAC;MACrB,IAAMC,aAAa,GAAG,IAAI,CAACJ,UAAU,CAAC,IAAI,CAACA,UAAU,CAACjD,MAAM,GAAG,CAAC,CAAC;MAEjE,IAAIqD,aAAa,EAAE;QACjB7E,iBAAiB,CAACmB,qBAAqB,CAAC0D,aAAa,CAAC;MACxD;IACF;EAAC;IAAAzE,GAAA;IAAAC,KAAA,EAKD,SAAOyE,eAAeA,CAAA,EAAS;MAC7B,IAAI,CAACL,UAAU,GAAG,EAAE;IACtB;EAAC;IAAArE,GAAA;IAAAC,KAAA,EAKD,SAAO0E,eAAeA,CAACC,YAAoB,EAAEC,WAAmB,EAAE;MAChE,OAAO;QACLC,UAAU,EAAE,SAAZA,UAAUA,CAAGC,KAAU,EAAK;UAC1B,IAAIA,KAAK,CAAC/E,GAAG,KAAK,KAAK,EAAE;YACvB,IAAI+E,KAAK,CAACC,QAAQ,EAAE;cAElB,IAAID,KAAK,CAACE,MAAM,KAAKL,YAAY,EAAE;gBACjCG,KAAK,CAACG,cAAc,CAAC,CAAC;gBACtBtF,iBAAiB,CAACmB,qBAAqB,CAAC8D,WAAW,CAAC;cACtD;YACF,CAAC,MAAM;cAEL,IAAIE,KAAK,CAACE,MAAM,KAAKJ,WAAW,EAAE;gBAChCE,KAAK,CAACG,cAAc,CAAC,CAAC;gBACtBtF,iBAAiB,CAACmB,qBAAqB,CAAC6D,YAAY,CAAC;cACvD;YACF;UACF;QACF;MACF,CAAC;IACH;EAAC;AAAA;AAtDUT,oBAAoB,CAChBE,UAAU,GAAa,EAAE;AAAA,IAyD7Bc,mBAAmB,GAAApG,OAAA,CAAAoG,mBAAA,GAAApG,OAAA,CAAAoG,mBAAA;EAAA,SAAAA,oBAAA;IAAA,IAAAtF,gBAAA,CAAAC,OAAA,QAAAqF,mBAAA;EAAA;EAAA,WAAApF,aAAA,CAAAD,OAAA,EAAAqF,mBAAA;IAAAnF,GAAA;IAAAC,KAAA,EAI9B,SAAOmF,oBAAoBA,CAACC,KAA4B,EAAEC,IAAY,EAAE;MACtE,OAAO;QACLC,iBAAiB,EAAE,QAA6B;QAChDC,kBAAkB,EAAEH,KAAK;QACzBI,kBAAkB,EAAEH;MACtB,CAAC;IACH;EAAC;IAAAtF,GAAA;IAAAC,KAAA,EAKD,SAAOyF,iBAAiBA,CAACC,SAAiB,EAAE;MAC1C,OAAO;QACLJ,iBAAiB,EAAE,MAA2B;QAC9CE,kBAAkB,EAAE,aAAaE,SAAS;MAC5C,CAAC;IACH;EAAC;IAAA3F,GAAA;IAAAC,KAAA,EAKD,SAAO2F,qBAAqBA,CAACC,KAAa,EAAEC,KAAa,EAAEC,OAAe,EAAE;MAC1E,OAAO;QACLR,iBAAiB,EAAE,UAA+B;QAClDE,kBAAkB,EAAE,GAAGM,OAAO,KAAKF,KAAK,GAAG,CAAC,OAAOC,KAAK;MAC1D,CAAC;IACH;EAAC;IAAA9F,GAAA;IAAAC,KAAA,EAKD,SAAO+F,mBAAmBA,CACxB9E,KAAa,EACbM,MAAe,EACfyE,KAA0B,EAC1B;MACA,OAAO;QACLV,iBAAiB,EAAE,QAA6B;QAChDE,kBAAkB,EAAEvE,KAAK;QACzBgF,iBAAiB,EAAE1E,MAAM,GACrB5B,iBAAiB,CAAC2B,uBAAuB,CAACC,MAAM,CAAC,GACjDH,SAAS;QACb8E,kBAAkB,EAAEF;MACtB,CAAC;IACH;EAAC;IAAAjG,GAAA;IAAAC,KAAA,EAKD,SAAOmG,kBAAkBA,CACvBlF,KAAa,EACbjB,KAAc,EAGd;MAAA,IAFAkB,QAAiB,GAAAT,SAAA,CAAAU,MAAA,QAAAV,SAAA,QAAAW,SAAA,GAAAX,SAAA,MAAG,KAAK;MAAA,IACzBJ,KAAc,GAAAI,SAAA,CAAAU,MAAA,OAAAV,SAAA,MAAAW,SAAA;MAEd,OAAO;QACLoE,kBAAkB,EAAE7F,iBAAiB,CAACqB,sBAAsB,CAC1DC,KAAK,EACLC,QAAQ,EACRb,KACF,CAAC;QACD+F,kBAAkB,EAAEpG,KAAK,GAAG;UAAEqF,IAAI,EAAErF;QAAM,CAAC,GAAGoB,SAAS;QACvD8E,kBAAkB,EAAE;UAClBG,QAAQ,EAAE;QACZ;MACF,CAAC;IACH;EAAC;AAAA;AAII,IAAMC,yBAA8C,GAAAxH,OAAA,CAAAwH,yBAAA,GAAG,CAC5D,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,OAAO,EACP,aAAa,EACb,MAAM,EACN,YAAY,EACZ,aAAa,EACb,QAAQ,EACR,SAAS,EACT,OAAO,EACP,UAAU,EACV,UAAU,EACV,MAAM,EACN,SAAS,EACT,UAAU,EACV,aAAa,EACb,OAAO,EACP,YAAY,EACZ,WAAW,EACX,YAAY,EACZ,QAAQ,EACR,KAAK,EACL,SAAS,EACT,OAAO,EACP,SAAS,EACT,MAAM,EACN,MAAM,EACN,UAAU,EACV,MAAM,CACP;AAKM,IAAMC,wBAAwB,GAAAzH,OAAA,CAAAyH,wBAAA,GAAG,SAA3BA,wBAAwBA,CAAIC,IAAY,EAAc;EACjE,OAAOF,yBAAyB,CAACG,QAAQ,CAACD,IAAyB,CAAC;AACtE,CAAC;AAKM,IAAME,wBAAwB,GAAA5H,OAAA,CAAA4H,wBAAA,GAAG,SAA3BA,wBAAwBA,CAAIF,IAAY,EAAwB;EAC3E,OAAOD,wBAAwB,CAACC,IAAI,CAAC,GAAIA,IAAI,GAAyB,MAAM;AAC9E,CAAC;AAAC,IAGWG,sBAAsB,GAAA7H,OAAA,CAAA6H,sBAAA,GAAA7H,OAAA,CAAA6H,sBAAA;EAAA,SAAAA,uBAAA;IAAA,IAAA/G,gBAAA,CAAAC,OAAA,QAAA8G,sBAAA;EAAA;EAAA,WAAA7G,aAAA,CAAAD,OAAA,EAAA8G,sBAAA;IAAA5G,GAAA;IAAAC,KAAA,EAIjC,SAAO4G,0BAA0BA,CAACC,KAAU,EAAY;MAAA,IAAAC,YAAA,EAAAC,aAAA;MACtD,IAAMC,MAAgB,GAAG,EAAE;MAG3B,IAAI,CAACH,KAAK,CAACvB,iBAAiB,IAAIuB,KAAK,CAACI,OAAO,EAAE;QAC7CD,MAAM,CAAC3C,IAAI,CAAC,+CAA+C,CAAC;MAC9D;MAGA,IACEwC,KAAK,CAACvB,iBAAiB,IACvB,CAACiB,wBAAwB,CAACM,KAAK,CAACvB,iBAAiB,CAAC,EAClD;QACA0B,MAAM,CAAC3C,IAAI,CACT,+BAA+BwC,KAAK,CAACvB,iBAAiB,iBAAiBgB,yBAAyB,CAACY,IAAI,CAAC,IAAI,CAAC,EAC7G,CAAC;MACH;MAGA,IAAI,CAACL,KAAK,CAACrB,kBAAkB,IAAI,CAACqB,KAAK,CAACM,QAAQ,EAAE;QAChDH,MAAM,CAAC3C,IAAI,CAAC,oDAAoD,CAAC;MACnE;MAGA,IAAI,CAAAyC,YAAA,GAAAD,KAAK,CAACO,KAAK,aAAXN,YAAA,CAAaO,KAAK,KAAAN,aAAA,GAAIF,KAAK,CAACO,KAAK,aAAXL,aAAA,CAAaO,MAAM,EAAE;QAC7C,IAAMD,KAAK,GAAGR,KAAK,CAACO,KAAK,CAACC,KAAK;QAC/B,IAAMC,MAAM,GAAGT,KAAK,CAACO,KAAK,CAACE,MAAM;QAEjC,IACED,KAAK,GAAGxI,cAAc,CAACM,YAAY,CAACC,QAAQ,IAC5CkI,MAAM,GAAGzI,cAAc,CAACM,YAAY,CAACC,QAAQ,EAC7C;UACA4H,MAAM,CAAC3C,IAAI,CACT,2BAA2BgD,KAAK,IAAIC,MAAM,cAAczI,cAAc,CAACM,YAAY,CAACC,QAAQ,IAAIP,cAAc,CAACM,YAAY,CAACC,QAAQ,EACtI,CAAC;QACH;MACF;MAEA,OAAO4H,MAAM;IACf;EAAC;IAAAjH,GAAA;IAAAC,KAAA,EAKD,SAAOuH,2BAA2BA,CAACC,aAAoB,EAIrD;MAAA,IAAAC,KAAA;MACA,IAAIC,MAAM,GAAG,CAAC;MACd,IAAIC,MAAM,GAAG,CAAC;MACd,IAAMX,MAAsD,GAAG,EAAE;MAEjEQ,aAAa,CAACI,OAAO,CAAC,UAACC,SAAS,EAAEjC,KAAK,EAAK;QAC1C,IAAMkC,eAAe,GAAGL,KAAI,CAACb,0BAA0B,CAACiB,SAAS,CAAChB,KAAK,CAAC;QAExE,IAAIiB,eAAe,CAAC3G,MAAM,KAAK,CAAC,EAAE;UAChCuG,MAAM,EAAE;QACV,CAAC,MAAM;UACLC,MAAM,EAAE;UACRX,MAAM,CAAC3C,IAAI,CAAC;YACVwD,SAAS,EAAEA,SAAS,CAACE,IAAI,IAAI,aAAanC,KAAK,EAAE;YACjDoB,MAAM,EAAEc;UACV,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,OAAO;QAAEJ,MAAM,EAANA,MAAM;QAAEC,MAAM,EAANA,MAAM;QAAEX,MAAM,EAANA;MAAO,CAAC;IACnC;EAAC;AAAA;AAII,IAAMgB,0BAA0B,GAAAlJ,OAAA,CAAAkJ,0BAAA,GAAAlJ,OAAA,CAAAkJ,0BAAA,GAAG;EAExCC,kBAAkB,EAAE,SAApBA,kBAAkBA,CAChBtH,OAAe,EAEZ;IAAA,IADHuH,QAAgC,GAAAzH,SAAA,CAAAU,MAAA,QAAAV,SAAA,QAAAW,SAAA,GAAAX,SAAA,MAAG,QAAQ;IAE3C,IAAIG,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;MACzBV,8BAAiB,CAACO,wBAAwB,CAACC,OAAO,CAAC;IACrD,CAAC,MAAM;MAELwH,UAAU,CACR,YAAM;QACJhI,8BAAiB,CAACO,wBAAwB,CAACC,OAAO,CAAC;MACrD,CAAC,EACDuH,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG,GACjC,CAAC;IACH;EACF,CAAC;EAGDE,qBAAqB,EAAE,SAAvBA,qBAAqBA,CACnBC,OAAe,EACfC,UAA2C,EAC3CC,OAAgB,EACb;IACH,IAAM5H,OAAO,GAAG,GAAG0H,OAAO,IAAIC,UAAU,GAAGC,OAAO,GAAG,KAAKA,OAAO,EAAE,GAAG,EAAE,EAAE;IAC1EP,0BAA0B,CAACC,kBAAkB,CAACtH,OAAO,EAAE,QAAQ,CAAC;EAClE,CAAC;EAGD6H,sBAAsB,EAAE,SAAxBA,sBAAsBA,CACpBC,SAAiB,EACjBC,OAAgB,EAChBC,YAAqB,EAClB;IACH,IAAID,OAAO,EAAE;MACXV,0BAA0B,CAACC,kBAAkB,CAC3C,GAAGQ,SAAS,WAAW,EACvB,QACF,CAAC;IACH,CAAC,MAAM;MACLT,0BAA0B,CAACC,kBAAkB,CAC3C,GAAGQ,SAAS,WAAWE,YAAY,IAAI,eAAe,EAAE,EACxD,WACF,CAAC;IACH;EACF,CAAC;EAGDC,gBAAgB,EAAE,SAAlBA,gBAAgBA,CACdC,OAAe,EACfhD,KAAa,EAEV;IAAA,IADH5E,KAAa,GAAAR,SAAA,CAAAU,MAAA,QAAAV,SAAA,QAAAW,SAAA,GAAAX,SAAA,MAAG,UAAU;IAE1B,IAAMqI,UAAU,GAAGvG,IAAI,CAACwG,KAAK,CAAEF,OAAO,GAAGhD,KAAK,GAAI,GAAG,CAAC;IACtDmC,0BAA0B,CAACC,kBAAkB,CAC3C,GAAGhH,KAAK,KAAK6H,UAAU,eAAeD,OAAO,OAAOhD,KAAK,EAAE,EAC3D,QACF,CAAC;EACH,CAAC;EAGDmD,oBAAoB,EAAE,SAAtBA,oBAAoBA,CAAGC,SAAkB,EAAkC;IAAA,IAAhCC,OAAe,GAAAzI,SAAA,CAAAU,MAAA,QAAAV,SAAA,QAAAW,SAAA,GAAAX,SAAA,MAAG,SAAS;IACpE,IAAIwI,SAAS,EAAE;MACbjB,0BAA0B,CAACC,kBAAkB,CAC3C,GAAGiB,OAAO,UAAU,EACpB,QACF,CAAC;IACH,CAAC,MAAM;MACLlB,0BAA0B,CAACC,kBAAkB,CAC3C,GAAGiB,OAAO,SAAS,EACnB,QACF,CAAC;IACH;EACF;AACF,CAAC;AAGM,IAAMC,4BAA4B,GAAArK,OAAA,CAAAqK,4BAAA,GAAArK,OAAA,CAAAqK,4BAAA,GAAG;EAE1CC,uBAAuB;IAAA,IAAAC,wBAAA,OAAAnJ,kBAAA,CAAAL,OAAA,EAAE,aAAY;MACnC,IAAMyJ,KAAK,GAAG;QACZC,iBAAiB,EAAE,CAAC;QACpBC,kBAAkB,EAAE,CAAC;QACrBC,iBAAiB,EAAE,CAAC;QACpBC,uBAAuB,EAAE;MAC3B,CAAC;MAED,IAAI;QAEFJ,KAAK,CAACC,iBAAiB,GACrB,OAAOpJ,8BAAiB,CAACC,qBAAqB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;QAG3D,IAAIQ,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;UACzByI,KAAK,CAACI,uBAAuB,SACrBvJ,8BAAiB,CAACwJ,qBAAqB,CAAC,CAAC;QACnD;MACF,CAAC,CAAC,OAAOtJ,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,sCAAsC,EAAEF,KAAK,CAAC;MAC7D;MAEA,OAAOiJ,KAAK;IACd,CAAC;IAAA,SAvBDF,uBAAuBA,CAAA;MAAA,OAAAC,wBAAA,CAAA7I,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAvB2I,uBAAuB;EAAA,GAuBtB;EAGDQ,kBAAkB;IAAA,IAAAC,mBAAA,OAAA3J,kBAAA,CAAAL,OAAA,EAAE,aAAY;MAC9B,IAAMiK,UAAU,GAAG;QACjBC,mBAAmB,EAAE,KAAK;QAC1BP,kBAAkB,EAAE,IAAI;QACxBQ,aAAa,EAAE,IAAI;QACnBC,YAAY,EAAE,IAAI;QAClBC,WAAW,EAAE,IAAI;QACjBC,aAAa,EAAE;MACjB,CAAC;MAED,IAAI;QACFL,UAAU,CAACC,mBAAmB,SACtB5J,8BAAiB,CAACC,qBAAqB,CAAC,CAAC;QAEjD,IAAIQ,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;UACzBiJ,UAAU,CAACK,aAAa,SAChBhK,8BAAiB,CAACwJ,qBAAqB,CAAC,CAAC;QACnD;MACF,CAAC,CAAC,OAAOtJ,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,wCAAwC,EAAEF,KAAK,CAAC;MAC/D;MAEA,OAAOyJ,UAAU;IACnB,CAAC;IAAA,SAvBDF,kBAAkBA,CAAA;MAAA,OAAAC,mBAAA,CAAArJ,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAlBmJ,kBAAkB;EAAA;AAwBpB,CAAC", "ignoreList": []}