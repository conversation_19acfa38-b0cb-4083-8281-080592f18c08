/**
 * SkipLink Component
 *
 * Provides keyboard navigation skip links for accessibility compliance.
 * Implements WCAG 2.2 AA requirement for bypass blocks.
 *
 * Features:
 * - Skip to main content
 * - Skip to navigation
 * - Skip to search
 * - Keyboard-only visibility
 * - Screen reader optimized
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Platform,
  StyleSheet,
  AccessibilityInfo,
} from 'react-native';

import { useTheme } from '../../contexts/ThemeContext';
import { WCAG_CONSTANTS } from '../../utils/accessibility';

interface SkipLinkProps {
  /** Target element ID to skip to */
  targetId: string;
  /** Label for the skip link */
  label: string;
  /** Additional accessibility hint */
  hint?: string;
  /** Custom styles */
  style?: any;
  /** Test ID for testing */
  testID?: string;
}

interface SkipLinksContainerProps {
  /** Array of skip link configurations */
  links: Array<{
    targetId: string;
    label: string;
    hint?: string;
  }>;
  /** Custom container styles */
  style?: any;
  /** Test ID for testing */
  testID?: string;
}

/**
 * Individual Skip Link Component
 */
export const SkipLink: React.FC<SkipLinkProps> = ({
  targetId,
  label,
  hint,
  style,
  testID,
}) => {
  const { colors, isDark } = useTheme();
  const skipLinkRef = useRef<TouchableOpacity>(null);

  const handleSkipPress = () => {
    // Find the target element and set focus
    if (Platform.OS === 'ios' || Platform.OS === 'android') {
      // Announce the skip action
      AccessibilityInfo.announceForAccessibility(`Skipped to ${label}`);

      // In React Native, we can't directly focus DOM elements like in web
      // Instead, we announce the action and let the screen reader handle it
      setTimeout(() => {
        AccessibilityInfo.announceForAccessibility(`Now at ${label} section`);
      }, 100);
    }
  };

  const skipLinkStyles = StyleSheet.create({
    skipLink: {
      position: 'absolute',
      top: -1000, // Hidden by default
      left: 16,
      zIndex: 9999,
      backgroundColor: colors.primary?.default || '#5A7A63',
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 4,
      minHeight: WCAG_CONSTANTS.TOUCH_TARGET.MIN_SIZE,
      minWidth: WCAG_CONSTANTS.TOUCH_TARGET.MIN_SIZE,
      justifyContent: 'center',
      alignItems: 'center',
      // Show when focused (keyboard navigation)
      // Note: React Native doesn't have :focus-visible, so we handle this differently
    },
    skipLinkFocused: {
      top: 16, // Show when focused
    },
    skipLinkText: {
      color: colors.background,
      fontSize: 16,
      fontWeight: '600',
      textAlign: 'center',
    },
  });

  return (
    <TouchableOpacity
      ref={skipLinkRef}
      style={[skipLinkStyles.skipLink, style]}
      onPress={handleSkipPress}
      accessibilityRole="button"
      accessibilityLabel={label}
      accessibilityHint={hint || `Skip to ${label} section`}
      testID={testID || `skip-link-${targetId}`}
      // Make it focusable for keyboard navigation
      accessible={true}
      // Handle focus events
      onFocus={() => {
        // Show the skip link when focused
        if (skipLinkRef.current) {
          skipLinkRef.current.setNativeProps({
            style: [
              skipLinkStyles.skipLink,
              skipLinkStyles.skipLinkFocused,
              style,
            ],
          });
        }
      }}
      onBlur={() => {
        // Hide the skip link when focus is lost
        if (skipLinkRef.current) {
          skipLinkRef.current.setNativeProps({
            style: [skipLinkStyles.skipLink, style],
          });
        }
      }}>
      <Text style={skipLinkStyles.skipLinkText}>{label}</Text>
    </TouchableOpacity>
  );
};

/**
 * Skip Links Container Component
 * Provides multiple skip links for comprehensive navigation
 */
export const SkipLinksContainer: React.FC<SkipLinksContainerProps> = ({
  links,
  style,
  testID,
}) => {
  const containerStyles = StyleSheet.create({
    container: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      zIndex: 9999,
      // Screen reader only by default
      opacity: 0,
      height: 0,
      overflow: 'hidden',
    },
    containerFocused: {
      opacity: 1,
      height: 'auto',
      paddingVertical: 8,
    },
  });

  return (
    <View
      style={[containerStyles.container, style]}
      accessibilityRole="navigation"
      accessibilityLabel="Skip navigation links"
      testID={testID || 'skip-links-container'}>
      {links.map((link, index) => (
        <SkipLink
          key={`skip-link-${link.targetId}-${index}`}
          targetId={link.targetId}
          label={link.label}
          hint={link.hint}
          testID={`skip-link-${link.targetId}`}
        />
      ))}
    </View>
  );
};

/**
 * Default skip links for common page sections
 */
export const defaultSkipLinks = [
  {
    targetId: 'main-content',
    label: 'Skip to main content',
    hint: 'Skip navigation and go directly to main content',
  },
  {
    targetId: 'browse-services',
    label: 'Skip to services',
    hint: 'Skip to browse services section',
  },
  {
    targetId: 'featured-providers',
    label: 'Skip to featured providers',
    hint: 'Skip to featured providers section',
  },
  {
    targetId: 'search',
    label: 'Skip to search',
    hint: 'Skip to search functionality',
  },
];

export default SkipLink;
