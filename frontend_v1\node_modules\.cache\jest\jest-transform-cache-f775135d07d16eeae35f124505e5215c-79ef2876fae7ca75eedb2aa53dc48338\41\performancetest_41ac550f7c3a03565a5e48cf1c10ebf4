f83fc76e214a3f7a202e93bdc588bef6
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _performance = require("../performance");
var mockPerformance = {
  now: jest.fn(function () {
    return Date.now();
  }),
  mark: jest.fn(),
  measure: jest.fn(),
  getEntriesByType: jest.fn(function () {
    return [];
  }),
  getEntriesByName: jest.fn(function () {
    return [];
  }),
  memory: {
    usedJSHeapSize: 1000000,
    totalJSHeapSize: 2000000
  }
};
var mockPerformanceObserver = jest.fn();
mockPerformanceObserver.prototype.observe = jest.fn();
mockPerformanceObserver.prototype.disconnect = jest.fn();
var mockWindow = {
  performance: mockPerformance,
  PerformanceObserver: mockPerformanceObserver,
  requestIdleCallback: jest.fn(function (callback) {
    return setTimeout(callback, 0);
  })
};
Object.defineProperty(global, 'window', {
  value: mockWindow,
  writable: true
});
Object.defineProperty(global, 'performance', {
  value: mockPerformance,
  writable: true
});
Object.defineProperty(global, 'requestIdleCallback', {
  value: mockWindow.requestIdleCallback,
  writable: true
});
var mockDocument = {
  createElement: jest.fn(function () {
    return {
      rel: '',
      href: '',
      as: '',
      type: '',
      media: '',
      onload: null
    };
  }),
  head: {
    appendChild: jest.fn()
  }
};
Object.defineProperty(global, 'window', {
  value: mockWindow,
  writable: true
});
Object.defineProperty(global, 'document', {
  value: mockDocument,
  writable: true
});
Object.defineProperty(global, 'performance', {
  value: mockPerformance,
  writable: true
});
Object.defineProperty(global, '__DEV__', {
  value: true,
  writable: true
});
describe('Performance Utilities', function () {
  beforeEach(function () {
    jest.clearAllMocks();
    mockPerformance.now.mockReturnValue(1000);
  });
  describe('PerformanceMonitor', function () {
    var monitor;
    beforeEach(function () {
      monitor = _performance.PerformanceMonitor.getInstance();
      monitor.clearMetrics();
    });
    it('creates singleton instance', function () {
      var instance1 = _performance.PerformanceMonitor.getInstance();
      var instance2 = _performance.PerformanceMonitor.getInstance();
      expect(instance1).toBe(instance2);
    });
    it('records metrics correctly', function () {
      var metric = {
        name: 'test-metric',
        value: 100,
        timestamp: Date.now(),
        type: 'timing',
        tags: {
          test: 'true'
        }
      };
      monitor.recordMetric(metric);
      var metrics = monitor.getMetrics('test-metric');
      expect(metrics).toHaveLength(1);
      expect(metrics[0]).toEqual(metric);
    });
    it('gets all metrics when no name specified', function () {
      var metric1 = {
        name: 'metric-1',
        value: 100,
        timestamp: Date.now(),
        type: 'timing'
      };
      var metric2 = {
        name: 'metric-2',
        value: 200,
        timestamp: Date.now(),
        type: 'counter'
      };
      monitor.recordMetric(metric1);
      monitor.recordMetric(metric2);
      var allMetrics = monitor.getMetrics();
      expect(allMetrics).toHaveLength(2);
    });
    it('generates performance summary', function () {
      monitor.recordMetric({
        name: 'test-timing',
        value: 100,
        timestamp: Date.now(),
        type: 'timing'
      });
      monitor.recordMetric({
        name: 'test-timing',
        value: 200,
        timestamp: Date.now(),
        type: 'timing'
      });
      var summary = monitor.getSummary();
      expect(summary['test-timing']).toEqual({
        avg: 150,
        min: 100,
        max: 200,
        count: 2
      });
    });
    it('limits metrics to prevent memory leaks', function () {
      var originalLog = console.log;
      console.log = jest.fn();
      try {
        for (var i = 0; i < 10; i++) {
          monitor.recordMetric({
            name: `metric-${i}`,
            value: i,
            timestamp: Date.now(),
            type: 'counter'
          });
        }
        var allMetrics = monitor.getMetrics();
        expect(allMetrics.length).toBe(10);
        expect(allMetrics[0].name).toBe('metric-0');
        expect(allMetrics[9].name).toBe('metric-9');
      } finally {
        console.log = originalLog;
      }
    });
    it('clears metrics', function () {
      monitor.recordMetric({
        name: 'test',
        value: 100,
        timestamp: Date.now(),
        type: 'timing'
      });
      expect(monitor.getMetrics()).toHaveLength(1);
      monitor.clearMetrics();
      expect(monitor.getMetrics()).toHaveLength(0);
    });
  });
  describe('PerformanceTimer', function () {
    it('measures timing correctly', function () {
      mockPerformance.now.mockReturnValueOnce(1000).mockReturnValueOnce(1500);
      var timer = new _performance.PerformanceTimer('test-timer');
      var duration = timer.end();
      expect(duration).toBe(500);
      var metrics = _performance.performanceMonitor.getMetrics('test-timer');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].value).toBe(500);
    });
    it('includes tags in metrics', function () {
      mockPerformance.now.mockReturnValueOnce(1000).mockReturnValueOnce(1200);
      var timer = new _performance.PerformanceTimer('tagged-timer');
      timer.end({
        component: 'test',
        action: 'render'
      });
      var metrics = _performance.performanceMonitor.getMetrics('tagged-timer');
      expect(metrics[0].tags).toEqual({
        component: 'test',
        action: 'render'
      });
    });
  });
  describe('CriticalCSSOptimizer', function () {
    beforeEach(function () {
      mockDocument.createElement.mockReturnValue({
        rel: '',
        href: '',
        as: '',
        type: '',
        media: '',
        onload: null
      });
    });
    it('sets and gets critical CSS', function () {
      var css = 'body { margin: 0; }';
      _performance.CriticalCSSOptimizer.setCriticalCSS(css);
      expect(_performance.CriticalCSSOptimizer.getCriticalCSS()).toBe(css);
    });
    it('loads non-critical CSS asynchronously', function () {
      var cssUrl = '/styles/non-critical.css';
      _performance.CriticalCSSOptimizer.addNonCriticalCSS(cssUrl);
      expect(mockDocument.createElement).toHaveBeenCalledWith('link');
      expect(mockDocument.head.appendChild).toHaveBeenCalled();
    });
    it('preloads critical resources', function () {
      var resources = [{
        href: '/font.woff2',
        as: 'font',
        type: 'font/woff2'
      }, {
        href: '/critical.css',
        as: 'style'
      }];
      _performance.CriticalCSSOptimizer.preloadCriticalResources(resources);
      expect(mockDocument.createElement).toHaveBeenCalledTimes(2);
      expect(mockDocument.head.appendChild).toHaveBeenCalledTimes(2);
    });
  });
  describe('CodeSplittingUtils', function () {
    it('imports module with performance tracking', (0, _asyncToGenerator2.default)(function* () {
      mockPerformance.now.mockReturnValueOnce(1000).mockReturnValueOnce(1500);
      var mockModule = {
        default: 'test-component'
      };
      var importFn = jest.fn().mockResolvedValue(mockModule);
      var result = yield _performance.CodeSplittingUtils.importModule(importFn, 'test-chunk');
      expect(result).toBe(mockModule);
      expect(importFn).toHaveBeenCalled();
      var metrics = _performance.performanceMonitor.getMetrics('chunk-load-test-chunk');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].tags).toEqual({
        chunk: 'test-chunk',
        status: 'success'
      });
    }));
    it('handles import errors', (0, _asyncToGenerator2.default)(function* () {
      var error = new Error('Import failed');
      var importFn = jest.fn().mockRejectedValue(error);
      yield expect(_performance.CodeSplittingUtils.importModule(importFn, 'error-chunk')).rejects.toThrow('Import failed');
      var metrics = _performance.performanceMonitor.getMetrics('chunk-load-error-chunk');
      expect(metrics[0].tags).toEqual({
        chunk: 'error-chunk',
        status: 'error'
      });
    }));
    it('preloads chunks', function () {
      var importFn = jest.fn().mockResolvedValue({
        default: 'preloaded'
      });
      _performance.CodeSplittingUtils.preloadChunk(importFn, 'preload-chunk');
      expect(mockWindow.requestIdleCallback).toHaveBeenCalled();
    });
    it('tracks loaded chunks', (0, _asyncToGenerator2.default)(function* () {
      var importFn = jest.fn().mockResolvedValue({
        default: 'test'
      });
      yield _performance.CodeSplittingUtils.importModule(importFn, 'tracked-chunk');
      var loadedChunks = _performance.CodeSplittingUtils.getLoadedChunks();
      expect(loadedChunks).toContain('tracked-chunk');
    }));
  });
  describe('MemoryMonitor', function () {
    it('gets memory usage information', function () {
      var usage = _performance.MemoryMonitor.getMemoryUsage();
      expect(usage).toEqual({
        used: 1000000,
        total: 2000000,
        percentage: 50
      });
    });
    it('returns null when memory API not available', function () {
      var originalMemory = mockPerformance.memory;
      delete mockPerformance.memory;
      var usage = _performance.MemoryMonitor.getMemoryUsage();
      expect(usage).toBeNull();
      mockPerformance.memory = originalMemory;
    });
    it('starts memory monitoring', function () {
      jest.useFakeTimers();
      _performance.performanceMonitor.clearMetrics();
      var originalLog = console.log;
      console.log = jest.fn();
      try {
        _performance.MemoryMonitor.startMemoryMonitoring(40);
        jest.advanceTimersByTime(30000);
        expect(jest.getTimerCount()).toBeGreaterThanOrEqual(0);
      } finally {
        console.log = originalLog;
        jest.useRealTimers();
      }
    });
  });
  describe('Utility Functions', function () {
    it('measurePerformance creates PerformanceTimer', function () {
      var timer = (0, _performance.measurePerformance)('test-measure');
      expect(timer).toBeInstanceOf(_performance.PerformanceTimer);
    });
    it('withPerformanceTracking wraps synchronous functions', function () {
      var originalFn = jest.fn(function (x) {
        return x * 2;
      });
      var wrappedFn = (0, _performance.withPerformanceTracking)(originalFn, 'sync-function');
      var result = wrappedFn(5);
      expect(result).toBe(10);
      expect(originalFn).toHaveBeenCalledWith(5);
      var metrics = _performance.performanceMonitor.getMetrics('sync-function');
      expect(metrics).toHaveLength(1);
      expect(typeof metrics[0].value).toBe('number');
    });
    it('withPerformanceTracking wraps asynchronous functions', (0, _asyncToGenerator2.default)(function* () {
      var originalFn = jest.fn().mockResolvedValue('async-result');
      var wrappedFn = (0, _performance.withPerformanceTracking)(originalFn, 'async-function');
      var result = yield wrappedFn();
      expect(result).toBe('async-result');
      var metrics = _performance.performanceMonitor.getMetrics('async-function');
      expect(metrics).toHaveLength(1);
      expect(typeof metrics[0].value).toBe('number');
    }));
    it('withPerformanceTracking handles errors', function () {
      var error = new Error('Test error');
      var originalFn = jest.fn().mockImplementation(function () {
        throw error;
      });
      var wrappedFn = (0, _performance.withPerformanceTracking)(originalFn, 'error-function');
      expect(function () {
        return wrappedFn();
      }).toThrow('Test error');
      var metrics = _performance.performanceMonitor.getMetrics('error-function');
      expect(metrics[0].tags).toEqual({
        status: 'error'
      });
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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