{"version": 3, "names": ["_ViewConfig", "require", "_UIManager", "_interopRequireDefault", "_createReactNativeComponentClass", "textViewConfig", "validAttributes", "isHighlighted", "isPressable", "numberOfLines", "ellipsizeMode", "allowFontScaling", "dynamicTypeRamp", "maxFontSizeMultiplier", "disabled", "selectable", "selectionColor", "adjustsFontSizeToFit", "minimumFontScale", "textBreakStrategy", "onTextLayout", "onInlineViewLayout", "dataDetectorType", "android_hyphenationFrequency", "lineBreakStrategyIOS", "directEventTypes", "topTextLayout", "registrationName", "topInlineViewLayout", "uiViewClassName", "virtualTextViewConfig", "NativeText", "exports", "createReactNativeComponentClass", "createViewConfig", "NativeVirtualText", "global", "RN$Bridgeless", "UIManager", "hasViewManagerConfig"], "sources": ["TextNativeComponent.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n */\n\nimport type {HostComponent} from '../../src/private/types/HostComponent';\nimport type {ProcessedColorValue} from '../StyleSheet/processColor';\nimport type {GestureResponderEvent} from '../Types/CoreEventTypes';\nimport type {TextProps} from './TextProps';\n\nimport {createViewConfig} from '../NativeComponent/ViewConfig';\nimport UIManager from '../ReactNative/UIManager';\nimport createReactNativeComponentClass from '../Renderer/shims/createReactNativeComponentClass';\n\nexport type NativeTextProps = $ReadOnly<{\n  ...TextProps,\n  isHighlighted?: ?boolean,\n  selectionColor?: ?ProcessedColorValue,\n  onClick?: ?(event: GestureResponderEvent) => mixed,\n  // This is only needed for platforms that optimize text hit testing, e.g.,\n  // react-native-windows. It can be used to only hit test virtual text spans\n  // that have pressable events attached to them.\n  isPressable?: ?boolean,\n}>;\n\nconst textViewConfig = {\n  validAttributes: {\n    isHighlighted: true,\n    isPressable: true,\n    numberOfLines: true,\n    ellipsizeMode: true,\n    allowFontScaling: true,\n    dynamicTypeRamp: true,\n    maxFontSizeMultiplier: true,\n    disabled: true,\n    selectable: true,\n    selectionColor: true,\n    adjustsFontSizeToFit: true,\n    minimumFontScale: true,\n    textBreakStrategy: true,\n    onTextLayout: true,\n    onInlineViewLayout: true,\n    dataDetectorType: true,\n    android_hyphenationFrequency: true,\n    lineBreakStrategyIOS: true,\n  },\n  directEventTypes: {\n    topTextLayout: {\n      registrationName: 'onTextLayout',\n    },\n    topInlineViewLayout: {\n      registrationName: 'onInlineViewLayout',\n    },\n  },\n  uiViewClassName: 'RCTText',\n};\n\nconst virtualTextViewConfig = {\n  validAttributes: {\n    isHighlighted: true,\n    isPressable: true,\n    maxFontSizeMultiplier: true,\n  },\n  uiViewClassName: 'RCTVirtualText',\n};\n\nexport const NativeText: HostComponent<NativeTextProps> =\n  (createReactNativeComponentClass('RCTText', () =>\n    createViewConfig(textViewConfig),\n  ): any);\n\nexport const NativeVirtualText: HostComponent<NativeTextProps> =\n  !global.RN$Bridgeless && !UIManager.hasViewManagerConfig('RCTVirtualText')\n    ? NativeText\n    : (createReactNativeComponentClass('RCTVirtualText', () =>\n        createViewConfig(virtualTextViewConfig),\n      ): any);\n"], "mappings": ";;;;;AAeA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,gCAAA,GAAAD,sBAAA,CAAAF,OAAA;AAaA,IAAMI,cAAc,GAAG;EACrBC,eAAe,EAAE;IACfC,aAAa,EAAE,IAAI;IACnBC,WAAW,EAAE,IAAI;IACjBC,aAAa,EAAE,IAAI;IACnBC,aAAa,EAAE,IAAI;IACnBC,gBAAgB,EAAE,IAAI;IACtBC,eAAe,EAAE,IAAI;IACrBC,qBAAqB,EAAE,IAAI;IAC3BC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,IAAI;IAChBC,cAAc,EAAE,IAAI;IACpBC,oBAAoB,EAAE,IAAI;IAC1BC,gBAAgB,EAAE,IAAI;IACtBC,iBAAiB,EAAE,IAAI;IACvBC,YAAY,EAAE,IAAI;IAClBC,kBAAkB,EAAE,IAAI;IACxBC,gBAAgB,EAAE,IAAI;IACtBC,4BAA4B,EAAE,IAAI;IAClCC,oBAAoB,EAAE;EACxB,CAAC;EACDC,gBAAgB,EAAE;IAChBC,aAAa,EAAE;MACbC,gBAAgB,EAAE;IACpB,CAAC;IACDC,mBAAmB,EAAE;MACnBD,gBAAgB,EAAE;IACpB;EACF,CAAC;EACDE,eAAe,EAAE;AACnB,CAAC;AAED,IAAMC,qBAAqB,GAAG;EAC5BxB,eAAe,EAAE;IACfC,aAAa,EAAE,IAAI;IACnBC,WAAW,EAAE,IAAI;IACjBK,qBAAqB,EAAE;EACzB,CAAC;EACDgB,eAAe,EAAE;AACnB,CAAC;AAEM,IAAME,UAA0C,GAAAC,OAAA,CAAAD,UAAA,GACpD,IAAAE,wCAA+B,EAAC,SAAS,EAAE;EAAA,OAC1C,IAAAC,4BAAgB,EAAC7B,cAAc,CAAC;AAAA,CAClC,CAAO;AAEF,IAAM8B,iBAAiD,GAAAH,OAAA,CAAAG,iBAAA,GAC5D,CAACC,MAAM,CAACC,aAAa,IAAI,CAACC,kBAAS,CAACC,oBAAoB,CAAC,gBAAgB,CAAC,GACtER,UAAU,GACT,IAAAE,wCAA+B,EAAC,gBAAgB,EAAE;EAAA,OACjD,IAAAC,4BAAgB,EAACJ,qBAAqB,CAAC;AAAA,CACzC,CAAO", "ignoreList": []}