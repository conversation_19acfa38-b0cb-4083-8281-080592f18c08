{"version": 3, "names": ["_asyncStorage", "_interopRequireDefault", "require", "_reactNative", "_enCA", "_frCA", "translations", "enCA", "frCA", "currentLocale", "currentTranslations", "LOCALE_STORAGE_KEY", "getDeviceLocale", "deviceLocale", "Platform", "OS", "_NativeModules$Settin", "_NativeModules$Settin2", "NativeModules", "SettingsManager", "settings", "AppleLocale", "AppleLanguages", "_NativeModules$I18nMa", "I18nManager", "localeIdentifier", "navigator", "language", "startsWith", "initializeI18n", "_initializeI18n", "apply", "arguments", "_asyncToGenerator2", "default", "savedLocale", "AsyncStorage", "getItem", "error", "console", "warn", "setLocale", "_x", "_setLocale", "locale", "setItem", "localeChangeListeners", "for<PERSON>ach", "listener", "getCurrentLocale", "isFrenchCanadian", "t", "key", "params", "keys", "split", "value", "k", "fallback<PERSON><PERSON><PERSON>", "fallback<PERSON><PERSON><PERSON>", "fk", "replace", "match", "<PERSON><PERSON><PERSON><PERSON>", "_params$paramKey", "toString", "tp", "count", "pluralKey", "fallback<PERSON><PERSON>", "translation", "allParams", "Object", "assign", "formatCurrency", "amount", "options", "showCents", "formatter", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "formatDate", "date", "formatOptions", "year", "month", "day", "weekday", "DateTimeFormat", "formatTime", "hour", "minute", "hour12", "formatPhoneNumber", "phoneNumber", "digits", "length", "slice", "formatPostalCode", "postalCode", "cleaned", "toUpperCase", "test", "getProvinces", "provinces", "code", "<PERSON><PERSON><PERSON>", "map", "province", "name", "onLocaleChange", "push", "index", "indexOf", "splice", "getAvailableLocales", "nativeName", "validateTranslations", "en<PERSON>eys", "getAllKeys", "fr<PERSON><PERSON><PERSON>", "missing", "filter", "includes", "extra", "complete", "obj", "prefix", "undefined", "<PERSON><PERSON><PERSON>", "concat", "_default", "exports"], "sources": ["i18n.ts"], "sourcesContent": ["/**\n * Internationalization (i18n) System\n *\n * Comprehensive internationalization system for Canadian market localization\n * with support for English Canadian (en-CA) and French Canadian (fr-CA).\n *\n * Features:\n * - Dynamic locale switching\n * - Nested translation keys\n * - Pluralization support\n * - Date/time formatting\n * - Currency formatting\n * - Number formatting\n * - RTL support preparation\n * - Fallback handling\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport AsyncStorage from '@react-native-async-storage/async-storage';\nimport { Platform, NativeModules } from 'react-native';\n\n// Import locale files\nimport enCA from '../locales/en-CA.json';\nimport frCA from '../locales/fr-CA.json';\n\n// Supported locales\nexport type SupportedLocale = 'en-CA' | 'fr-CA';\n\n// Translation resources\nconst translations = {\n  'en-CA': enCA,\n  'fr-CA': frCA,\n};\n\n// Current locale state\nlet currentLocale: SupportedLocale = 'en-CA';\nlet currentTranslations = translations[currentLocale];\n\n// Storage key for persisting locale preference\nconst LOCALE_STORAGE_KEY = '@vierla_locale';\n\n/**\n * Get device locale with Canadian fallback\n */\nfunction getDeviceLocale(): SupportedLocale {\n  let deviceLocale = 'en-CA';\n\n  if (Platform.OS === 'ios') {\n    deviceLocale =\n      NativeModules.SettingsManager?.settings?.AppleLocale ||\n      NativeModules.SettingsManager?.settings?.AppleLanguages?.[0] ||\n      'en-CA';\n  } else if (Platform.OS === 'android') {\n    deviceLocale = NativeModules.I18nManager?.localeIdentifier || 'en-CA';\n  } else if (Platform.OS === 'web') {\n    deviceLocale = navigator.language || 'en-CA';\n  }\n\n  // Map common locales to Canadian variants\n  if (deviceLocale.startsWith('fr')) {\n    return 'fr-CA';\n  } else {\n    return 'en-CA';\n  }\n}\n\n/**\n * Initialize i18n system\n */\nexport async function initializeI18n(): Promise<SupportedLocale> {\n  try {\n    // Try to load saved locale preference\n    const savedLocale = await AsyncStorage.getItem(LOCALE_STORAGE_KEY);\n\n    if (savedLocale && (savedLocale === 'en-CA' || savedLocale === 'fr-CA')) {\n      currentLocale = savedLocale as SupportedLocale;\n    } else {\n      // Use device locale as fallback\n      currentLocale = getDeviceLocale();\n    }\n\n    currentTranslations = translations[currentLocale];\n    return currentLocale;\n  } catch (error) {\n    console.warn('Failed to initialize i18n:', error);\n    currentLocale = 'en-CA';\n    currentTranslations = translations[currentLocale];\n    return currentLocale;\n  }\n}\n\n/**\n * Change current locale\n */\nexport async function setLocale(locale: SupportedLocale): Promise<void> {\n  try {\n    currentLocale = locale;\n    currentTranslations = translations[locale];\n\n    // Persist locale preference\n    await AsyncStorage.setItem(LOCALE_STORAGE_KEY, locale);\n\n    // Notify listeners about locale change\n    localeChangeListeners.forEach(listener => listener(locale));\n  } catch (error) {\n    console.error('Failed to set locale:', error);\n  }\n}\n\n/**\n * Get current locale\n */\nexport function getCurrentLocale(): SupportedLocale {\n  return currentLocale;\n}\n\n/**\n * Check if current locale is French Canadian\n */\nexport function isFrenchCanadian(): boolean {\n  return currentLocale === 'fr-CA';\n}\n\n/**\n * Get translation for a key with nested object support\n */\nexport function t(\n  key: string,\n  params?: Record<string, string | number>,\n): string {\n  const keys = key.split('.');\n  let value: any = currentTranslations;\n\n  // Navigate through nested object\n  for (const k of keys) {\n    if (value && typeof value === 'object' && k in value) {\n      value = value[k];\n    } else {\n      // Fallback to English if key not found in current locale\n      if (currentLocale !== 'en-CA') {\n        const fallbackKeys = key.split('.');\n        let fallbackValue: any = translations['en-CA'];\n\n        for (const fk of fallbackKeys) {\n          if (\n            fallbackValue &&\n            typeof fallbackValue === 'object' &&\n            fk in fallbackValue\n          ) {\n            fallbackValue = fallbackValue[fk];\n          } else {\n            return `[Missing: ${key}]`;\n          }\n        }\n\n        value = fallbackValue;\n      } else {\n        return `[Missing: ${key}]`;\n      }\n      break;\n    }\n  }\n\n  if (typeof value !== 'string') {\n    return `[Invalid: ${key}]`;\n  }\n\n  // Replace parameters\n  if (params) {\n    return value.replace(/\\{\\{(\\w+)\\}\\}/g, (match, paramKey) => {\n      return params[paramKey]?.toString() || match;\n    });\n  }\n\n  return value;\n}\n\n/**\n * Pluralization support\n */\nexport function tp(\n  key: string,\n  count: number,\n  params?: Record<string, string | number>,\n): string {\n  const pluralKey = count === 1 ? `${key}.singular` : `${key}.plural`;\n  const fallbackKey = key;\n\n  // Try plural key first, then fallback to base key\n  let translation = t(pluralKey);\n  if (\n    translation.startsWith('[Missing:') ||\n    translation.startsWith('[Invalid:')\n  ) {\n    translation = t(fallbackKey);\n  }\n\n  // Add count to parameters\n  const allParams = { ...params, count };\n\n  return t(translation, allParams);\n}\n\n/**\n * Format currency for Canadian market\n */\nexport function formatCurrency(\n  amount: number,\n  options?: {\n    showCents?: boolean;\n    locale?: SupportedLocale;\n  },\n): string {\n  const locale = options?.locale || currentLocale;\n  const showCents = options?.showCents !== false;\n\n  const formatter = new Intl.NumberFormat(locale, {\n    style: 'currency',\n    currency: 'CAD',\n    minimumFractionDigits: showCents ? 2 : 0,\n    maximumFractionDigits: showCents ? 2 : 0,\n  });\n\n  return formatter.format(amount);\n}\n\n/**\n * Format date for Canadian market\n */\nexport function formatDate(\n  date: Date,\n  options?: {\n    style?: 'short' | 'medium' | 'long' | 'full';\n    locale?: SupportedLocale;\n  },\n): string {\n  const locale = options?.locale || currentLocale;\n  const style = options?.style || 'medium';\n\n  const formatOptions: Intl.DateTimeFormatOptions = {\n    year: 'numeric',\n    month:\n      style === 'short' ? 'numeric' : style === 'medium' ? 'short' : 'long',\n    day: 'numeric',\n  };\n\n  if (style === 'full') {\n    formatOptions.weekday = 'long';\n  }\n\n  return new Intl.DateTimeFormat(locale, formatOptions).format(date);\n}\n\n/**\n * Format time for Canadian market\n */\nexport function formatTime(\n  date: Date,\n  options?: {\n    format?: '12h' | '24h';\n    locale?: SupportedLocale;\n  },\n): string {\n  const locale = options?.locale || currentLocale;\n  const format = options?.format || '12h';\n\n  const formatOptions: Intl.DateTimeFormatOptions = {\n    hour: 'numeric',\n    minute: '2-digit',\n    hour12: format === '12h',\n  };\n\n  return new Intl.DateTimeFormat(locale, formatOptions).format(date);\n}\n\n/**\n * Format phone number for Canadian format\n */\nexport function formatPhoneNumber(phoneNumber: string): string {\n  // Remove all non-digits\n  const digits = phoneNumber.replace(/\\D/g, '');\n\n  // Canadian phone number format: (XXX) XXX-XXXX\n  if (digits.length === 10) {\n    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;\n  } else if (digits.length === 11 && digits.startsWith('1')) {\n    return `+1 (${digits.slice(1, 4)}) ${digits.slice(4, 7)}-${digits.slice(7)}`;\n  }\n\n  return phoneNumber; // Return original if not a valid Canadian number\n}\n\n/**\n * Format postal code for Canadian format\n */\nexport function formatPostalCode(postalCode: string): string {\n  // Remove spaces and convert to uppercase\n  const cleaned = postalCode.replace(/\\s/g, '').toUpperCase();\n\n  // Canadian postal code format: A1A 1A1\n  if (cleaned.length === 6 && /^[A-Z]\\d[A-Z]\\d[A-Z]\\d$/.test(cleaned)) {\n    return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;\n  }\n\n  return postalCode; // Return original if not a valid Canadian postal code\n}\n\n/**\n * Get localized province names\n */\nexport function getProvinces(): Array<{ code: string; name: string }> {\n  const provinces = [\n    { code: 'AB', nameKey: 'provinces.alberta' },\n    { code: 'BC', nameKey: 'provinces.britishColumbia' },\n    { code: 'MB', nameKey: 'provinces.manitoba' },\n    { code: 'NB', nameKey: 'provinces.newBrunswick' },\n    { code: 'NL', nameKey: 'provinces.newfoundlandLabrador' },\n    { code: 'NS', nameKey: 'provinces.novaScotia' },\n    { code: 'ON', nameKey: 'provinces.ontario' },\n    { code: 'PE', nameKey: 'provinces.princeEdwardIsland' },\n    { code: 'QC', nameKey: 'provinces.quebec' },\n    { code: 'SK', nameKey: 'provinces.saskatchewan' },\n    { code: 'NT', nameKey: 'provinces.northwestTerritories' },\n    { code: 'NU', nameKey: 'provinces.nunavut' },\n    { code: 'YT', nameKey: 'provinces.yukon' },\n  ];\n\n  return provinces.map(province => ({\n    code: province.code,\n    name: t(province.nameKey) || province.code,\n  }));\n}\n\n/**\n * Locale change listeners\n */\ntype LocaleChangeListener = (locale: SupportedLocale) => void;\nconst localeChangeListeners: LocaleChangeListener[] = [];\n\n/**\n * Subscribe to locale changes\n */\nexport function onLocaleChange(listener: LocaleChangeListener): () => void {\n  localeChangeListeners.push(listener);\n\n  // Return unsubscribe function\n  return () => {\n    const index = localeChangeListeners.indexOf(listener);\n    if (index > -1) {\n      localeChangeListeners.splice(index, 1);\n    }\n  };\n}\n\n/**\n * Get available locales\n */\nexport function getAvailableLocales(): Array<{\n  code: SupportedLocale;\n  name: string;\n  nativeName: string;\n}> {\n  return [\n    {\n      code: 'en-CA',\n      name: 'English (Canada)',\n      nativeName: 'English (Canada)',\n    },\n    {\n      code: 'fr-CA',\n      name: 'French (Canada)',\n      nativeName: 'Français (Canada)',\n    },\n  ];\n}\n\n/**\n * Validate translation completeness\n */\nexport function validateTranslations(): {\n  missing: string[];\n  extra: string[];\n  complete: boolean;\n} {\n  const enKeys = getAllKeys(translations['en-CA']);\n  const frKeys = getAllKeys(translations['fr-CA']);\n\n  const missing = enKeys.filter(key => !frKeys.includes(key));\n  const extra = frKeys.filter(key => !enKeys.includes(key));\n\n  return {\n    missing,\n    extra,\n    complete: missing.length === 0 && extra.length === 0,\n  };\n}\n\n/**\n * Helper function to get all nested keys from an object\n */\nfunction getAllKeys(obj: any, prefix = ''): string[] {\n  let keys: string[] = [];\n\n  for (const key in obj) {\n    const fullKey = prefix ? `${prefix}.${key}` : key;\n\n    if (typeof obj[key] === 'object' && obj[key] !== null) {\n      keys = keys.concat(getAllKeys(obj[key], fullKey));\n    } else {\n      keys.push(fullKey);\n    }\n  }\n\n  return keys;\n}\n\n// Export commonly used functions with shorter names\nexport { t as translate };\nexport { tp as translatePlural };\nexport { formatCurrency as currency };\nexport { formatDate as date };\nexport { formatTime as time };\n\n// Default export for easy importing\nexport default {\n  t,\n  tp,\n  setLocale,\n  getCurrentLocale,\n  isFrenchCanadian,\n  formatCurrency,\n  formatDate,\n  formatTime,\n  formatPhoneNumber,\n  formatPostalCode,\n  getProvinces,\n  onLocaleChange,\n  getAvailableLocales,\n  initializeI18n,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAoBA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAGA,IAAAE,KAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,KAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAMA,IAAMI,YAAY,GAAG;EACnB,OAAO,EAAEC,aAAI;EACb,OAAO,EAAEC;AACX,CAAC;AAGD,IAAIC,aAA8B,GAAG,OAAO;AAC5C,IAAIC,mBAAmB,GAAGJ,YAAY,CAACG,aAAa,CAAC;AAGrD,IAAME,kBAAkB,GAAG,gBAAgB;AAK3C,SAASC,eAAeA,CAAA,EAAoB;EAC1C,IAAIC,YAAY,GAAG,OAAO;EAE1B,IAAIC,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IACzBJ,YAAY,GACV,EAAAG,qBAAA,GAAAE,0BAAa,CAACC,eAAe,cAAAH,qBAAA,GAA7BA,qBAAA,CAA+BI,QAAQ,qBAAvCJ,qBAAA,CAAyCK,WAAW,OAAAJ,sBAAA,GACpDC,0BAAa,CAACC,eAAe,cAAAF,sBAAA,GAA7BA,sBAAA,CAA+BG,QAAQ,cAAAH,sBAAA,GAAvCA,sBAAA,CAAyCK,cAAc,qBAAvDL,sBAAA,CAA0D,CAAC,CAAC,KAC5D,OAAO;EACX,CAAC,MAAM,IAAIH,qBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;IAAA,IAAAQ,qBAAA;IACpCV,YAAY,GAAG,EAAAU,qBAAA,GAAAL,0BAAa,CAACM,WAAW,qBAAzBD,qBAAA,CAA2BE,gBAAgB,KAAI,OAAO;EACvE,CAAC,MAAM,IAAIX,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IAChCF,YAAY,GAAGa,SAAS,CAACC,QAAQ,IAAI,OAAO;EAC9C;EAGA,IAAId,YAAY,CAACe,UAAU,CAAC,IAAI,CAAC,EAAE;IACjC,OAAO,OAAO;EAChB,CAAC,MAAM;IACL,OAAO,OAAO;EAChB;AACF;AAAC,SAKqBC,cAAcA,CAAA;EAAA,OAAAC,eAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,gBAAA;EAAAA,eAAA,OAAAG,kBAAA,CAAAC,OAAA,EAA7B,aAA0D;IAC/D,IAAI;MAEF,IAAMC,WAAW,SAASC,qBAAY,CAACC,OAAO,CAAC1B,kBAAkB,CAAC;MAElE,IAAIwB,WAAW,KAAKA,WAAW,KAAK,OAAO,IAAIA,WAAW,KAAK,OAAO,CAAC,EAAE;QACvE1B,aAAa,GAAG0B,WAA8B;MAChD,CAAC,MAAM;QAEL1B,aAAa,GAAGG,eAAe,CAAC,CAAC;MACnC;MAEAF,mBAAmB,GAAGJ,YAAY,CAACG,aAAa,CAAC;MACjD,OAAOA,aAAa;IACtB,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,4BAA4B,EAAEF,KAAK,CAAC;MACjD7B,aAAa,GAAG,OAAO;MACvBC,mBAAmB,GAAGJ,YAAY,CAACG,aAAa,CAAC;MACjD,OAAOA,aAAa;IACtB;EACF,CAAC;EAAA,OAAAqB,eAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAKqBS,SAASA,CAAAC,EAAA;EAAA,OAAAC,UAAA,CAAAZ,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAW,WAAA;EAAAA,UAAA,OAAAV,kBAAA,CAAAC,OAAA,EAAxB,WAAyBU,MAAuB,EAAiB;IACtE,IAAI;MACFnC,aAAa,GAAGmC,MAAM;MACtBlC,mBAAmB,GAAGJ,YAAY,CAACsC,MAAM,CAAC;MAG1C,MAAMR,qBAAY,CAACS,OAAO,CAAClC,kBAAkB,EAAEiC,MAAM,CAAC;MAGtDE,qBAAqB,CAACC,OAAO,CAAC,UAAAC,QAAQ;QAAA,OAAIA,QAAQ,CAACJ,MAAM,CAAC;MAAA,EAAC;IAC7D,CAAC,CAAC,OAAON,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAAA,OAAAK,UAAA,CAAAZ,KAAA,OAAAC,SAAA;AAAA;AAKM,SAASiB,gBAAgBA,CAAA,EAAoB;EAClD,OAAOxC,aAAa;AACtB;AAKO,SAASyC,gBAAgBA,CAAA,EAAY;EAC1C,OAAOzC,aAAa,KAAK,OAAO;AAClC;AAKO,SAAS0C,CAACA,CACfC,GAAW,EACXC,MAAwC,EAChC;EACR,IAAMC,IAAI,GAAGF,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC;EAC3B,IAAIC,KAAU,GAAG9C,mBAAmB;EAGpC,KAAK,IAAM+C,CAAC,IAAIH,IAAI,EAAE;IACpB,IAAIE,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIC,CAAC,IAAID,KAAK,EAAE;MACpDA,KAAK,GAAGA,KAAK,CAACC,CAAC,CAAC;IAClB,CAAC,MAAM;MAEL,IAAIhD,aAAa,KAAK,OAAO,EAAE;QAC7B,IAAMiD,YAAY,GAAGN,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC;QACnC,IAAII,aAAkB,GAAGrD,YAAY,CAAC,OAAO,CAAC;QAE9C,KAAK,IAAMsD,EAAE,IAAIF,YAAY,EAAE;UAC7B,IACEC,aAAa,IACb,OAAOA,aAAa,KAAK,QAAQ,IACjCC,EAAE,IAAID,aAAa,EACnB;YACAA,aAAa,GAAGA,aAAa,CAACC,EAAE,CAAC;UACnC,CAAC,MAAM;YACL,OAAO,aAAaR,GAAG,GAAG;UAC5B;QACF;QAEAI,KAAK,GAAGG,aAAa;MACvB,CAAC,MAAM;QACL,OAAO,aAAaP,GAAG,GAAG;MAC5B;MACA;IACF;EACF;EAEA,IAAI,OAAOI,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAO,aAAaJ,GAAG,GAAG;EAC5B;EAGA,IAAIC,MAAM,EAAE;IACV,OAAOG,KAAK,CAACK,OAAO,CAAC,gBAAgB,EAAE,UAACC,KAAK,EAAEC,QAAQ,EAAK;MAAA,IAAAC,gBAAA;MAC1D,OAAO,EAAAA,gBAAA,GAAAX,MAAM,CAACU,QAAQ,CAAC,qBAAhBC,gBAAA,CAAkBC,QAAQ,CAAC,CAAC,KAAIH,KAAK;IAC9C,CAAC,CAAC;EACJ;EAEA,OAAON,KAAK;AACd;AAKO,SAASU,EAAEA,CAChBd,GAAW,EACXe,KAAa,EACbd,MAAwC,EAChC;EACR,IAAMe,SAAS,GAAGD,KAAK,KAAK,CAAC,GAAG,GAAGf,GAAG,WAAW,GAAG,GAAGA,GAAG,SAAS;EACnE,IAAMiB,WAAW,GAAGjB,GAAG;EAGvB,IAAIkB,WAAW,GAAGnB,CAAC,CAACiB,SAAS,CAAC;EAC9B,IACEE,WAAW,CAAC1C,UAAU,CAAC,WAAW,CAAC,IACnC0C,WAAW,CAAC1C,UAAU,CAAC,WAAW,CAAC,EACnC;IACA0C,WAAW,GAAGnB,CAAC,CAACkB,WAAW,CAAC;EAC9B;EAGA,IAAME,SAAS,GAAAC,MAAA,CAAAC,MAAA,KAAQpB,MAAM;IAAEc,KAAK,EAALA;EAAK,EAAE;EAEtC,OAAOhB,CAAC,CAACmB,WAAW,EAAEC,SAAS,CAAC;AAClC;AAKO,SAASG,cAAcA,CAC5BC,MAAc,EACdC,OAGC,EACO;EACR,IAAMhC,MAAM,GAAG,CAAAgC,OAAO,oBAAPA,OAAO,CAAEhC,MAAM,KAAInC,aAAa;EAC/C,IAAMoE,SAAS,GAAG,CAAAD,OAAO,oBAAPA,OAAO,CAAEC,SAAS,MAAK,KAAK;EAE9C,IAAMC,SAAS,GAAG,IAAIC,IAAI,CAACC,YAAY,CAACpC,MAAM,EAAE;IAC9CqC,KAAK,EAAE,UAAU;IACjBC,QAAQ,EAAE,KAAK;IACfC,qBAAqB,EAAEN,SAAS,GAAG,CAAC,GAAG,CAAC;IACxCO,qBAAqB,EAAEP,SAAS,GAAG,CAAC,GAAG;EACzC,CAAC,CAAC;EAEF,OAAOC,SAAS,CAACO,MAAM,CAACV,MAAM,CAAC;AACjC;AAKO,SAASW,UAAUA,CACxBC,IAAU,EACVX,OAGC,EACO;EACR,IAAMhC,MAAM,GAAG,CAAAgC,OAAO,oBAAPA,OAAO,CAAEhC,MAAM,KAAInC,aAAa;EAC/C,IAAMwE,KAAK,GAAG,CAAAL,OAAO,oBAAPA,OAAO,CAAEK,KAAK,KAAI,QAAQ;EAExC,IAAMO,aAAyC,GAAG;IAChDC,IAAI,EAAE,SAAS;IACfC,KAAK,EACHT,KAAK,KAAK,OAAO,GAAG,SAAS,GAAGA,KAAK,KAAK,QAAQ,GAAG,OAAO,GAAG,MAAM;IACvEU,GAAG,EAAE;EACP,CAAC;EAED,IAAIV,KAAK,KAAK,MAAM,EAAE;IACpBO,aAAa,CAACI,OAAO,GAAG,MAAM;EAChC;EAEA,OAAO,IAAIb,IAAI,CAACc,cAAc,CAACjD,MAAM,EAAE4C,aAAa,CAAC,CAACH,MAAM,CAACE,IAAI,CAAC;AACpE;AAKO,SAASO,UAAUA,CACxBP,IAAU,EACVX,OAGC,EACO;EACR,IAAMhC,MAAM,GAAG,CAAAgC,OAAO,oBAAPA,OAAO,CAAEhC,MAAM,KAAInC,aAAa;EAC/C,IAAM4E,MAAM,GAAG,CAAAT,OAAO,oBAAPA,OAAO,CAAES,MAAM,KAAI,KAAK;EAEvC,IAAMG,aAAyC,GAAG;IAChDO,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAEZ,MAAM,KAAK;EACrB,CAAC;EAED,OAAO,IAAIN,IAAI,CAACc,cAAc,CAACjD,MAAM,EAAE4C,aAAa,CAAC,CAACH,MAAM,CAACE,IAAI,CAAC;AACpE;AAKO,SAASW,iBAAiBA,CAACC,WAAmB,EAAU;EAE7D,IAAMC,MAAM,GAAGD,WAAW,CAACtC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EAG7C,IAAIuC,MAAM,CAACC,MAAM,KAAK,EAAE,EAAE;IACxB,OAAO,IAAID,MAAM,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKF,MAAM,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAIF,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,EAAE;EAC3E,CAAC,MAAM,IAAIF,MAAM,CAACC,MAAM,KAAK,EAAE,IAAID,MAAM,CAACxE,UAAU,CAAC,GAAG,CAAC,EAAE;IACzD,OAAO,OAAOwE,MAAM,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKF,MAAM,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAIF,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,EAAE;EAC9E;EAEA,OAAOH,WAAW;AACpB;AAKO,SAASI,gBAAgBA,CAACC,UAAkB,EAAU;EAE3D,IAAMC,OAAO,GAAGD,UAAU,CAAC3C,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC6C,WAAW,CAAC,CAAC;EAG3D,IAAID,OAAO,CAACJ,MAAM,KAAK,CAAC,IAAI,yBAAyB,CAACM,IAAI,CAACF,OAAO,CAAC,EAAE;IACnE,OAAO,GAAGA,OAAO,CAACH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAIG,OAAO,CAACH,KAAK,CAAC,CAAC,CAAC,EAAE;EACrD;EAEA,OAAOE,UAAU;AACnB;AAKO,SAASI,YAAYA,CAAA,EAA0C;EACpE,IAAMC,SAAS,GAAG,CAChB;IAAEC,IAAI,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAoB,CAAC,EAC5C;IAAED,IAAI,EAAE,IAAI;IAAEC,OAAO,EAAE;EAA4B,CAAC,EACpD;IAAED,IAAI,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAqB,CAAC,EAC7C;IAAED,IAAI,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAyB,CAAC,EACjD;IAAED,IAAI,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAiC,CAAC,EACzD;IAAED,IAAI,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAuB,CAAC,EAC/C;IAAED,IAAI,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAoB,CAAC,EAC5C;IAAED,IAAI,EAAE,IAAI;IAAEC,OAAO,EAAE;EAA+B,CAAC,EACvD;IAAED,IAAI,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAmB,CAAC,EAC3C;IAAED,IAAI,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAyB,CAAC,EACjD;IAAED,IAAI,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAiC,CAAC,EACzD;IAAED,IAAI,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAoB,CAAC,EAC5C;IAAED,IAAI,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAkB,CAAC,CAC3C;EAED,OAAOF,SAAS,CAACG,GAAG,CAAC,UAAAC,QAAQ;IAAA,OAAK;MAChCH,IAAI,EAAEG,QAAQ,CAACH,IAAI;MACnBI,IAAI,EAAE/D,CAAC,CAAC8D,QAAQ,CAACF,OAAO,CAAC,IAAIE,QAAQ,CAACH;IACxC,CAAC;EAAA,CAAC,CAAC;AACL;AAMA,IAAMhE,qBAA6C,GAAG,EAAE;AAKjD,SAASqE,cAAcA,CAACnE,QAA8B,EAAc;EACzEF,qBAAqB,CAACsE,IAAI,CAACpE,QAAQ,CAAC;EAGpC,OAAO,YAAM;IACX,IAAMqE,KAAK,GAAGvE,qBAAqB,CAACwE,OAAO,CAACtE,QAAQ,CAAC;IACrD,IAAIqE,KAAK,GAAG,CAAC,CAAC,EAAE;MACdvE,qBAAqB,CAACyE,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IACxC;EACF,CAAC;AACH;AAKO,SAASG,mBAAmBA,CAAA,EAIhC;EACD,OAAO,CACL;IACEV,IAAI,EAAE,OAAO;IACbI,IAAI,EAAE,kBAAkB;IACxBO,UAAU,EAAE;EACd,CAAC,EACD;IACEX,IAAI,EAAE,OAAO;IACbI,IAAI,EAAE,iBAAiB;IACvBO,UAAU,EAAE;EACd,CAAC,CACF;AACH;AAKO,SAASC,oBAAoBA,CAAA,EAIlC;EACA,IAAMC,MAAM,GAAGC,UAAU,CAACtH,YAAY,CAAC,OAAO,CAAC,CAAC;EAChD,IAAMuH,MAAM,GAAGD,UAAU,CAACtH,YAAY,CAAC,OAAO,CAAC,CAAC;EAEhD,IAAMwH,OAAO,GAAGH,MAAM,CAACI,MAAM,CAAC,UAAA3E,GAAG;IAAA,OAAI,CAACyE,MAAM,CAACG,QAAQ,CAAC5E,GAAG,CAAC;EAAA,EAAC;EAC3D,IAAM6E,KAAK,GAAGJ,MAAM,CAACE,MAAM,CAAC,UAAA3E,GAAG;IAAA,OAAI,CAACuE,MAAM,CAACK,QAAQ,CAAC5E,GAAG,CAAC;EAAA,EAAC;EAEzD,OAAO;IACL0E,OAAO,EAAPA,OAAO;IACPG,KAAK,EAALA,KAAK;IACLC,QAAQ,EAAEJ,OAAO,CAACzB,MAAM,KAAK,CAAC,IAAI4B,KAAK,CAAC5B,MAAM,KAAK;EACrD,CAAC;AACH;AAKA,SAASuB,UAAUA,CAACO,GAAQ,EAAyB;EAAA,IAAvBC,MAAM,GAAApG,SAAA,CAAAqE,MAAA,QAAArE,SAAA,QAAAqG,SAAA,GAAArG,SAAA,MAAG,EAAE;EACvC,IAAIsB,IAAc,GAAG,EAAE;EAEvB,KAAK,IAAMF,GAAG,IAAI+E,GAAG,EAAE;IACrB,IAAMG,OAAO,GAAGF,MAAM,GAAG,GAAGA,MAAM,IAAIhF,GAAG,EAAE,GAAGA,GAAG;IAEjD,IAAI,OAAO+E,GAAG,CAAC/E,GAAG,CAAC,KAAK,QAAQ,IAAI+E,GAAG,CAAC/E,GAAG,CAAC,KAAK,IAAI,EAAE;MACrDE,IAAI,GAAGA,IAAI,CAACiF,MAAM,CAACX,UAAU,CAACO,GAAG,CAAC/E,GAAG,CAAC,EAAEkF,OAAO,CAAC,CAAC;IACnD,CAAC,MAAM;MACLhF,IAAI,CAAC8D,IAAI,CAACkB,OAAO,CAAC;IACpB;EACF;EAEA,OAAOhF,IAAI;AACb;AAAC,IAAAkF,QAAA,GAAAC,OAAA,CAAAvG,OAAA,GAUc;EACbiB,CAAC,EAADA,CAAC;EACDe,EAAE,EAAFA,EAAE;EACFzB,SAAS,EAATA,SAAS;EACTQ,gBAAgB,EAAhBA,gBAAgB;EAChBC,gBAAgB,EAAhBA,gBAAgB;EAChBwB,cAAc,EAAdA,cAAc;EACdY,UAAU,EAAVA,UAAU;EACVQ,UAAU,EAAVA,UAAU;EACVI,iBAAiB,EAAjBA,iBAAiB;EACjBK,gBAAgB,EAAhBA,gBAAgB;EAChBK,YAAY,EAAZA,YAAY;EACZO,cAAc,EAAdA,cAAc;EACdK,mBAAmB,EAAnBA,mBAAmB;EACnB3F,cAAc,EAAdA;AACF,CAAC", "ignoreList": []}