599e4228ab5be8fd2d9d1a3d0ac74362
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.createWebSocketService = exports.createNotificationsWebSocketService = exports.createMessagingWebSocketService = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _unifiedErrorHandling = require("./unifiedErrorHandling");
var WebSocketService = function () {
  function WebSocketService(config) {
    (0, _classCallCheck2.default)(this, WebSocketService);
    this.socket = null;
    this.reconnectAttempts = 0;
    this.reconnectTimer = null;
    this.isConnecting = false;
    this.eventListeners = new Map();
    this.connectionPromise = null;
    this.config = Object.assign({
      reconnectInterval: 3000,
      maxReconnectAttempts: 5
    }, config);
  }
  return (0, _createClass2.default)(WebSocketService, [{
    key: "connect",
    value: (function () {
      var _connect = (0, _asyncToGenerator2.default)(function* (token) {
        var _this$socket,
          _this = this;
        if (((_this$socket = this.socket) == null ? void 0 : _this$socket.readyState) === WebSocket.OPEN) {
          return Promise.resolve();
        }
        if (this.connectionPromise) {
          return this.connectionPromise;
        }
        this.connectionPromise = new Promise(function (resolve, reject) {
          try {
            _this.isConnecting = true;
            var wsUrl = token ? `${_this.config.url}?token=${token}` : _this.config.url;
            _this.socket = new WebSocket(wsUrl, _this.config.protocols);
            _this.socket.onopen = function () {
              console.log('WebSocket connected');
              _this.isConnecting = false;
              _this.reconnectAttempts = 0;
              _this.connectionPromise = null;
              _this.emit('connected', {});
              resolve();
            };
            _this.socket.onmessage = function (event) {
              try {
                var data = JSON.parse(event.data);
                _this.handleMessage(data);
              } catch (error) {
                console.error('Failed to parse WebSocket message:', error);
                _unifiedErrorHandling.unifiedErrorHandlingService.handleWebSocketError(error, {
                  action: 'message_parse_error',
                  additionalData: {
                    rawMessage: event.data,
                    url: _this.config.url
                  }
                });
              }
            };
            _this.socket.onclose = function () {
              var _ref = (0, _asyncToGenerator2.default)(function* (event) {
                console.log('WebSocket disconnected:', event.code, event.reason);
                _this.isConnecting = false;
                _this.connectionPromise = null;
                _this.emit('disconnected', {
                  code: event.code,
                  reason: event.reason
                });
                if (event.code === 4001) {
                  yield _unifiedErrorHandling.unifiedErrorHandlingService.handleAuthenticationError(new Error('WebSocket authentication failed'), {
                    action: 'websocket_auth_failed',
                    additionalData: {
                      code: event.code,
                      reason: event.reason,
                      url: _this.config.url
                    }
                  });
                } else if (!event.wasClean && _this.shouldReconnect()) {
                  _this.scheduleReconnect();
                }
              });
              return function (_x2) {
                return _ref.apply(this, arguments);
              };
            }();
            _this.socket.onerror = function () {
              var _ref2 = (0, _asyncToGenerator2.default)(function* (error) {
                var _this$socket2;
                console.error('WebSocket error:', error);
                _this.isConnecting = false;
                _this.connectionPromise = null;
                yield _unifiedErrorHandling.unifiedErrorHandlingService.handleWebSocketError(error, {
                  action: 'connection_error',
                  additionalData: {
                    url: _this.config.url,
                    readyState: (_this$socket2 = _this.socket) == null ? void 0 : _this$socket2.readyState
                  }
                });
                _this.emit('error', {
                  error: error
                });
                reject(error);
              });
              return function (_x3) {
                return _ref2.apply(this, arguments);
              };
            }();
          } catch (error) {
            _this.isConnecting = false;
            _this.connectionPromise = null;
            reject(error);
          }
        });
        return this.connectionPromise;
      });
      function connect(_x) {
        return _connect.apply(this, arguments);
      }
      return connect;
    }())
  }, {
    key: "disconnect",
    value: function disconnect() {
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }
      if (this.socket) {
        this.socket.close(1000, 'Client disconnect');
        this.socket = null;
      }
      this.connectionPromise = null;
      this.reconnectAttempts = 0;
    }
  }, {
    key: "send",
    value: (function () {
      var _send = (0, _asyncToGenerator2.default)(function* (message) {
        var _this$socket3;
        if (((_this$socket3 = this.socket) == null ? void 0 : _this$socket3.readyState) === WebSocket.OPEN) {
          try {
            this.socket.send(JSON.stringify(message));
            return true;
          } catch (error) {
            console.error('Failed to send WebSocket message:', error);
            yield _unifiedErrorHandling.unifiedErrorHandlingService.handleWebSocketError(error, {
              action: 'send_message_error',
              additionalData: {
                message: message,
                url: this.config.url
              }
            });
            return false;
          }
        } else {
          var _this$socket4;
          console.warn('WebSocket not connected, message not sent:', message);
          yield _unifiedErrorHandling.unifiedErrorHandlingService.handleWebSocketError(new Error('WebSocket not connected'), {
            action: 'send_message_not_connected',
            additionalData: {
              message: message,
              url: this.config.url,
              readyState: (_this$socket4 = this.socket) == null ? void 0 : _this$socket4.readyState
            }
          });
          return false;
        }
      });
      function send(_x4) {
        return _send.apply(this, arguments);
      }
      return send;
    }())
  }, {
    key: "sendChatMessage",
    value: (function () {
      var _sendChatMessage = (0, _asyncToGenerator2.default)(function* (content, replyTo) {
        return yield this.send({
          type: 'chat_message',
          content: content,
          reply_to: replyTo
        });
      });
      function sendChatMessage(_x5, _x6) {
        return _sendChatMessage.apply(this, arguments);
      }
      return sendChatMessage;
    }())
  }, {
    key: "sendTypingIndicator",
    value: (function () {
      var _sendTypingIndicator = (0, _asyncToGenerator2.default)(function* (isTyping) {
        return yield this.send({
          type: 'typing_indicator',
          is_typing: isTyping
        });
      });
      function sendTypingIndicator(_x7) {
        return _sendTypingIndicator.apply(this, arguments);
      }
      return sendTypingIndicator;
    }())
  }, {
    key: "markMessagesRead",
    value: (function () {
      var _markMessagesRead = (0, _asyncToGenerator2.default)(function* (messageIds) {
        return yield this.send({
          type: 'mark_read',
          message_ids: messageIds
        });
      });
      function markMessagesRead(_x8) {
        return _markMessagesRead.apply(this, arguments);
      }
      return markMessagesRead;
    }())
  }, {
    key: "on",
    value: function on(event, callback) {
      if (!this.eventListeners.has(event)) {
        this.eventListeners.set(event, new Set());
      }
      this.eventListeners.get(event).add(callback);
    }
  }, {
    key: "off",
    value: function off(event, callback) {
      var listeners = this.eventListeners.get(event);
      if (listeners) {
        listeners.delete(callback);
      }
    }
  }, {
    key: "isConnected",
    value: function isConnected() {
      var _this$socket5;
      return ((_this$socket5 = this.socket) == null ? void 0 : _this$socket5.readyState) === WebSocket.OPEN;
    }
  }, {
    key: "getConnectionState",
    value: function getConnectionState() {
      if (!this.socket) return 'disconnected';
      switch (this.socket.readyState) {
        case WebSocket.CONNECTING:
          return 'connecting';
        case WebSocket.OPEN:
          return 'connected';
        case WebSocket.CLOSING:
          return 'closing';
        case WebSocket.CLOSED:
          return 'disconnected';
        default:
          return 'unknown';
      }
    }
  }, {
    key: "handleMessage",
    value: function handleMessage(data) {
      this.emit(data.type, data);
    }
  }, {
    key: "emit",
    value: function emit(event, data) {
      var listeners = this.eventListeners.get(event);
      if (listeners) {
        listeners.forEach(function (callback) {
          try {
            callback(data);
          } catch (error) {
            console.error(`Error in WebSocket event listener for ${event}:`, error);
          }
        });
      }
    }
  }, {
    key: "shouldReconnect",
    value: function shouldReconnect() {
      return this.reconnectAttempts < this.config.maxReconnectAttempts;
    }
  }, {
    key: "scheduleReconnect",
    value: function scheduleReconnect() {
      var _this2 = this;
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
      }
      this.reconnectTimer = setTimeout(function () {
        _this2.reconnectAttempts++;
        console.log(`Attempting to reconnect (${_this2.reconnectAttempts}/${_this2.config.maxReconnectAttempts})`);
        _this2.connect().catch(function (error) {
          console.error('Reconnection failed:', error);
        });
      }, this.config.reconnectInterval);
    }
  }]);
}();
var createWebSocketService = exports.createWebSocketService = function createWebSocketService(config) {
  return new WebSocketService(config);
};
var createMessagingWebSocketService = exports.createMessagingWebSocketService = function createMessagingWebSocketService(conversationId) {
  return createWebSocketService({
    url: `ws://192.168.2.65:8000/ws/messaging/${conversationId}/`,
    reconnectInterval: 3000,
    maxReconnectAttempts: 5
  });
};
var createNotificationsWebSocketService = exports.createNotificationsWebSocketService = function createNotificationsWebSocketService(userId) {
  return createWebSocketService({
    url: `ws://192.168.2.65:8000/ws/notifications/${userId}/`,
    reconnectInterval: 5000,
    maxReconnectAttempts: 3
  });
};
var _default = exports.default = WebSocketService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfdW5pZmllZEVycm9ySGFuZGxpbmciLCJyZXF1aXJlIiwiV2ViU29ja2V0U2VydmljZSIsImNvbmZpZyIsIl9jbGFzc0NhbGxDaGVjazIiLCJkZWZhdWx0Iiwic29ja2V0IiwicmVjb25uZWN0QXR0ZW1wdHMiLCJyZWNvbm5lY3RUaW1lciIsImlzQ29ubmVjdGluZyIsImV2ZW50TGlzdGVuZXJzIiwiTWFwIiwiY29ubmVjdGlvblByb21pc2UiLCJPYmplY3QiLCJhc3NpZ24iLCJyZWNvbm5lY3RJbnRlcnZhbCIsIm1heFJlY29ubmVjdEF0dGVtcHRzIiwiX2NyZWF0ZUNsYXNzMiIsImtleSIsInZhbHVlIiwiX2Nvbm5lY3QiLCJfYXN5bmNUb0dlbmVyYXRvcjIiLCJ0b2tlbiIsIl90aGlzJHNvY2tldCIsIl90aGlzIiwicmVhZHlTdGF0ZSIsIldlYlNvY2tldCIsIk9QRU4iLCJQcm9taXNlIiwicmVzb2x2ZSIsInJlamVjdCIsIndzVXJsIiwidXJsIiwicHJvdG9jb2xzIiwib25vcGVuIiwiY29uc29sZSIsImxvZyIsImVtaXQiLCJvbm1lc3NhZ2UiLCJldmVudCIsImRhdGEiLCJKU09OIiwicGFyc2UiLCJoYW5kbGVNZXNzYWdlIiwiZXJyb3IiLCJ1bmlmaWVkRXJyb3JIYW5kbGluZ1NlcnZpY2UiLCJoYW5kbGVXZWJTb2NrZXRFcnJvciIsImFjdGlvbiIsImFkZGl0aW9uYWxEYXRhIiwicmF3TWVzc2FnZSIsIm9uY2xvc2UiLCJfcmVmIiwiY29kZSIsInJlYXNvbiIsImhhbmRsZUF1dGhlbnRpY2F0aW9uRXJyb3IiLCJFcnJvciIsIndhc0NsZWFuIiwic2hvdWxkUmVjb25uZWN0Iiwic2NoZWR1bGVSZWNvbm5lY3QiLCJfeDIiLCJhcHBseSIsImFyZ3VtZW50cyIsIm9uZXJyb3IiLCJfcmVmMiIsIl90aGlzJHNvY2tldDIiLCJfeDMiLCJjb25uZWN0IiwiX3giLCJkaXNjb25uZWN0IiwiY2xlYXJUaW1lb3V0IiwiY2xvc2UiLCJfc2VuZCIsIm1lc3NhZ2UiLCJfdGhpcyRzb2NrZXQzIiwic2VuZCIsInN0cmluZ2lmeSIsIl90aGlzJHNvY2tldDQiLCJ3YXJuIiwiX3g0IiwiX3NlbmRDaGF0TWVzc2FnZSIsImNvbnRlbnQiLCJyZXBseVRvIiwidHlwZSIsInJlcGx5X3RvIiwic2VuZENoYXRNZXNzYWdlIiwiX3g1IiwiX3g2IiwiX3NlbmRUeXBpbmdJbmRpY2F0b3IiLCJpc1R5cGluZyIsImlzX3R5cGluZyIsInNlbmRUeXBpbmdJbmRpY2F0b3IiLCJfeDciLCJfbWFya01lc3NhZ2VzUmVhZCIsIm1lc3NhZ2VJZHMiLCJtZXNzYWdlX2lkcyIsIm1hcmtNZXNzYWdlc1JlYWQiLCJfeDgiLCJvbiIsImNhbGxiYWNrIiwiaGFzIiwic2V0IiwiU2V0IiwiZ2V0IiwiYWRkIiwib2ZmIiwibGlzdGVuZXJzIiwiZGVsZXRlIiwiaXNDb25uZWN0ZWQiLCJfdGhpcyRzb2NrZXQ1IiwiZ2V0Q29ubmVjdGlvblN0YXRlIiwiQ09OTkVDVElORyIsIkNMT1NJTkciLCJDTE9TRUQiLCJmb3JFYWNoIiwiX3RoaXMyIiwic2V0VGltZW91dCIsImNhdGNoIiwiY3JlYXRlV2ViU29ja2V0U2VydmljZSIsImV4cG9ydHMiLCJjcmVhdGVNZXNzYWdpbmdXZWJTb2NrZXRTZXJ2aWNlIiwiY29udmVyc2F0aW9uSWQiLCJjcmVhdGVOb3RpZmljYXRpb25zV2ViU29ja2V0U2VydmljZSIsInVzZXJJZCIsIl9kZWZhdWx0Il0sInNvdXJjZXMiOlsid2Vic29ja2V0U2VydmljZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFdlYlNvY2tldCBTZXJ2aWNlIC0gUmVhbC10aW1lIENvbW11bmljYXRpb25cbiAqXG4gKiBTZXJ2aWNlIENvbnRyYWN0OlxuICogLSBNYW5hZ2VzIFdlYlNvY2tldCBjb25uZWN0aW9ucyBmb3IgcmVhbC10aW1lIG1lc3NhZ2luZ1xuICogLSBIYW5kbGVzIGNvbm5lY3Rpb24gbGlmZWN5Y2xlIGFuZCByZWNvbm5lY3Rpb25cbiAqIC0gUHJvdmlkZXMgZXZlbnQtYmFzZWQgbWVzc2FnaW5nIGludGVyZmFjZVxuICogLSBTdXBwb3J0cyB0eXBpbmcgaW5kaWNhdG9ycyBhbmQgbWVzc2FnZSBzdGF0dXMgdXBkYXRlc1xuICogLSBJbXBsZW1lbnRzIHByb3BlciBlcnJvciBoYW5kbGluZyBhbmQgZmFsbGJhY2sgbWVjaGFuaXNtc1xuICogLSBJbnRlZ3JhdGVzIHdpdGggdW5pZmllZCBlcnJvciBoYW5kbGluZyBzeXN0ZW1cbiAqXG4gKiBAdmVyc2lvbiAyLjAuMFxuICogQGF1dGhvciBWaWVybGEgRGV2ZWxvcG1lbnQgVGVhbVxuICovXG5cbmltcG9ydCB7IHVuaWZpZWRFcnJvckhhbmRsaW5nU2VydmljZSB9IGZyb20gJy4vdW5pZmllZEVycm9ySGFuZGxpbmcnO1xuXG5leHBvcnQgaW50ZXJmYWNlIFdlYlNvY2tldE1lc3NhZ2Uge1xuICB0eXBlOlxuICAgIHwgJ2NoYXRfbWVzc2FnZSdcbiAgICB8ICd0eXBpbmdfaW5kaWNhdG9yJ1xuICAgIHwgJ21lc3NhZ2VzX3JlYWQnXG4gICAgfCAnbm90aWZpY2F0aW9uJ1xuICAgIHwgJ2Vycm9yJztcbiAgbWVzc2FnZT86IGFueTtcbiAgdXNlcl9pZD86IHN0cmluZztcbiAgdXNlcl9uYW1lPzogc3RyaW5nO1xuICBpc190eXBpbmc/OiBib29sZWFuO1xuICBtZXNzYWdlX2lkcz86IHN0cmluZ1tdO1xuICByZWFkZXJfaWQ/OiBzdHJpbmc7XG4gIG5vdGlmaWNhdGlvbj86IGFueTtcbiAgZXJyb3I/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgV2ViU29ja2V0Q29uZmlnIHtcbiAgdXJsOiBzdHJpbmc7XG4gIHByb3RvY29scz86IHN0cmluZ1tdO1xuICByZWNvbm5lY3RJbnRlcnZhbD86IG51bWJlcjtcbiAgbWF4UmVjb25uZWN0QXR0ZW1wdHM/OiBudW1iZXI7XG59XG5cbmNsYXNzIFdlYlNvY2tldFNlcnZpY2Uge1xuICBwcml2YXRlIHNvY2tldDogV2ViU29ja2V0IHwgbnVsbCA9IG51bGw7XG4gIHByaXZhdGUgY29uZmlnOiBXZWJTb2NrZXRDb25maWc7XG4gIHByaXZhdGUgcmVjb25uZWN0QXR0ZW1wdHMgPSAwO1xuICBwcml2YXRlIHJlY29ubmVjdFRpbWVyOiBOb2RlSlMuVGltZW91dCB8IG51bGwgPSBudWxsO1xuICBwcml2YXRlIGlzQ29ubmVjdGluZyA9IGZhbHNlO1xuICBwcml2YXRlIGV2ZW50TGlzdGVuZXJzOiBNYXA8c3RyaW5nLCBTZXQ8KGRhdGE6IGFueSkgPT4gdm9pZD4+ID0gbmV3IE1hcCgpO1xuICBwcml2YXRlIGNvbm5lY3Rpb25Qcm9taXNlOiBQcm9taXNlPHZvaWQ+IHwgbnVsbCA9IG51bGw7XG5cbiAgY29uc3RydWN0b3IoY29uZmlnOiBXZWJTb2NrZXRDb25maWcpIHtcbiAgICB0aGlzLmNvbmZpZyA9IHtcbiAgICAgIHJlY29ubmVjdEludGVydmFsOiAzMDAwLFxuICAgICAgbWF4UmVjb25uZWN0QXR0ZW1wdHM6IDUsXG4gICAgICAuLi5jb25maWcsXG4gICAgfTtcbiAgfVxuXG4gIC8qKlxuICAgKiBDb25uZWN0IHRvIFdlYlNvY2tldCBzZXJ2ZXJcbiAgICovXG4gIGFzeW5jIGNvbm5lY3QodG9rZW4/OiBzdHJpbmcpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBpZiAodGhpcy5zb2NrZXQ/LnJlYWR5U3RhdGUgPT09IFdlYlNvY2tldC5PUEVOKSB7XG4gICAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKCk7XG4gICAgfVxuXG4gICAgaWYgKHRoaXMuY29ubmVjdGlvblByb21pc2UpIHtcbiAgICAgIHJldHVybiB0aGlzLmNvbm5lY3Rpb25Qcm9taXNlO1xuICAgIH1cblxuICAgIHRoaXMuY29ubmVjdGlvblByb21pc2UgPSBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICB0cnkge1xuICAgICAgICB0aGlzLmlzQ29ubmVjdGluZyA9IHRydWU7XG5cbiAgICAgICAgLy8gVXNlIHByb3ZpZGVkIHRva2VuIGZvciBXZWJTb2NrZXQgYXV0aGVudGljYXRpb25cbiAgICAgICAgY29uc3Qgd3NVcmwgPSB0b2tlblxuICAgICAgICAgID8gYCR7dGhpcy5jb25maWcudXJsfT90b2tlbj0ke3Rva2VufWBcbiAgICAgICAgICA6IHRoaXMuY29uZmlnLnVybDtcblxuICAgICAgICB0aGlzLnNvY2tldCA9IG5ldyBXZWJTb2NrZXQod3NVcmwsIHRoaXMuY29uZmlnLnByb3RvY29scyk7XG5cbiAgICAgICAgdGhpcy5zb2NrZXQub25vcGVuID0gKCkgPT4ge1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdXZWJTb2NrZXQgY29ubmVjdGVkJyk7XG4gICAgICAgICAgdGhpcy5pc0Nvbm5lY3RpbmcgPSBmYWxzZTtcbiAgICAgICAgICB0aGlzLnJlY29ubmVjdEF0dGVtcHRzID0gMDtcbiAgICAgICAgICB0aGlzLmNvbm5lY3Rpb25Qcm9taXNlID0gbnVsbDtcbiAgICAgICAgICB0aGlzLmVtaXQoJ2Nvbm5lY3RlZCcsIHt9KTtcbiAgICAgICAgICByZXNvbHZlKCk7XG4gICAgICAgIH07XG5cbiAgICAgICAgdGhpcy5zb2NrZXQub25tZXNzYWdlID0gZXZlbnQgPT4ge1xuICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zdCBkYXRhOiBXZWJTb2NrZXRNZXNzYWdlID0gSlNPTi5wYXJzZShldmVudC5kYXRhKTtcbiAgICAgICAgICAgIHRoaXMuaGFuZGxlTWVzc2FnZShkYXRhKTtcbiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHBhcnNlIFdlYlNvY2tldCBtZXNzYWdlOicsIGVycm9yKTtcbiAgICAgICAgICAgIC8vIEhhbmRsZSBtZXNzYWdlIHBhcnNpbmcgZXJyb3JzIHdpdGggdW5pZmllZCBlcnJvciBoYW5kbGluZ1xuICAgICAgICAgICAgdW5pZmllZEVycm9ySGFuZGxpbmdTZXJ2aWNlLmhhbmRsZVdlYlNvY2tldEVycm9yKGVycm9yIGFzIEVycm9yLCB7XG4gICAgICAgICAgICAgIGFjdGlvbjogJ21lc3NhZ2VfcGFyc2VfZXJyb3InLFxuICAgICAgICAgICAgICBhZGRpdGlvbmFsRGF0YTogeyByYXdNZXNzYWdlOiBldmVudC5kYXRhLCB1cmw6IHRoaXMuY29uZmlnLnVybCB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICB9XG4gICAgICAgIH07XG5cbiAgICAgICAgdGhpcy5zb2NrZXQub25jbG9zZSA9IGFzeW5jIGV2ZW50ID0+IHtcbiAgICAgICAgICBjb25zb2xlLmxvZygnV2ViU29ja2V0IGRpc2Nvbm5lY3RlZDonLCBldmVudC5jb2RlLCBldmVudC5yZWFzb24pO1xuICAgICAgICAgIHRoaXMuaXNDb25uZWN0aW5nID0gZmFsc2U7XG4gICAgICAgICAgdGhpcy5jb25uZWN0aW9uUHJvbWlzZSA9IG51bGw7XG4gICAgICAgICAgdGhpcy5lbWl0KCdkaXNjb25uZWN0ZWQnLCB7IGNvZGU6IGV2ZW50LmNvZGUsIHJlYXNvbjogZXZlbnQucmVhc29uIH0pO1xuXG4gICAgICAgICAgLy8gSGFuZGxlIGRpZmZlcmVudCBjbG9zZSBjb2Rlc1xuICAgICAgICAgIGlmIChldmVudC5jb2RlID09PSA0MDAxKSB7XG4gICAgICAgICAgICAvLyBBdXRoZW50aWNhdGlvbiBmYWlsZWRcbiAgICAgICAgICAgIGF3YWl0IHVuaWZpZWRFcnJvckhhbmRsaW5nU2VydmljZS5oYW5kbGVBdXRoZW50aWNhdGlvbkVycm9yKFxuICAgICAgICAgICAgICBuZXcgRXJyb3IoJ1dlYlNvY2tldCBhdXRoZW50aWNhdGlvbiBmYWlsZWQnKSxcbiAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgIGFjdGlvbjogJ3dlYnNvY2tldF9hdXRoX2ZhaWxlZCcsXG4gICAgICAgICAgICAgICAgYWRkaXRpb25hbERhdGE6IHsgY29kZTogZXZlbnQuY29kZSwgcmVhc29uOiBldmVudC5yZWFzb24sIHVybDogdGhpcy5jb25maWcudXJsIH1cbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgKTtcbiAgICAgICAgICB9IGVsc2UgaWYgKCFldmVudC53YXNDbGVhbiAmJiB0aGlzLnNob3VsZFJlY29ubmVjdCgpKSB7XG4gICAgICAgICAgICB0aGlzLnNjaGVkdWxlUmVjb25uZWN0KCk7XG4gICAgICAgICAgfVxuICAgICAgICB9O1xuXG4gICAgICAgIHRoaXMuc29ja2V0Lm9uZXJyb3IgPSBhc3luYyBlcnJvciA9PiB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcignV2ViU29ja2V0IGVycm9yOicsIGVycm9yKTtcbiAgICAgICAgICB0aGlzLmlzQ29ubmVjdGluZyA9IGZhbHNlO1xuICAgICAgICAgIHRoaXMuY29ubmVjdGlvblByb21pc2UgPSBudWxsO1xuXG4gICAgICAgICAgLy8gVXNlIHVuaWZpZWQgZXJyb3IgaGFuZGxpbmdcbiAgICAgICAgICBhd2FpdCB1bmlmaWVkRXJyb3JIYW5kbGluZ1NlcnZpY2UuaGFuZGxlV2ViU29ja2V0RXJyb3IoZXJyb3IgYXMgRXJyb3IsIHtcbiAgICAgICAgICAgIGFjdGlvbjogJ2Nvbm5lY3Rpb25fZXJyb3InLFxuICAgICAgICAgICAgYWRkaXRpb25hbERhdGE6IHtcbiAgICAgICAgICAgICAgdXJsOiB0aGlzLmNvbmZpZy51cmwsXG4gICAgICAgICAgICAgIHJlYWR5U3RhdGU6IHRoaXMuc29ja2V0Py5yZWFkeVN0YXRlXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSk7XG5cbiAgICAgICAgICB0aGlzLmVtaXQoJ2Vycm9yJywgeyBlcnJvciB9KTtcbiAgICAgICAgICByZWplY3QoZXJyb3IpO1xuICAgICAgICB9O1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgdGhpcy5pc0Nvbm5lY3RpbmcgPSBmYWxzZTtcbiAgICAgICAgdGhpcy5jb25uZWN0aW9uUHJvbWlzZSA9IG51bGw7XG4gICAgICAgIHJlamVjdChlcnJvcik7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICByZXR1cm4gdGhpcy5jb25uZWN0aW9uUHJvbWlzZTtcbiAgfVxuXG4gIC8qKlxuICAgKiBEaXNjb25uZWN0IGZyb20gV2ViU29ja2V0IHNlcnZlclxuICAgKi9cbiAgZGlzY29ubmVjdCgpOiB2b2lkIHtcbiAgICBpZiAodGhpcy5yZWNvbm5lY3RUaW1lcikge1xuICAgICAgY2xlYXJUaW1lb3V0KHRoaXMucmVjb25uZWN0VGltZXIpO1xuICAgICAgdGhpcy5yZWNvbm5lY3RUaW1lciA9IG51bGw7XG4gICAgfVxuXG4gICAgaWYgKHRoaXMuc29ja2V0KSB7XG4gICAgICB0aGlzLnNvY2tldC5jbG9zZSgxMDAwLCAnQ2xpZW50IGRpc2Nvbm5lY3QnKTtcbiAgICAgIHRoaXMuc29ja2V0ID0gbnVsbDtcbiAgICB9XG5cbiAgICB0aGlzLmNvbm5lY3Rpb25Qcm9taXNlID0gbnVsbDtcbiAgICB0aGlzLnJlY29ubmVjdEF0dGVtcHRzID0gMDtcbiAgfVxuXG4gIC8qKlxuICAgKiBTZW5kIG1lc3NhZ2UgdGhyb3VnaCBXZWJTb2NrZXRcbiAgICovXG4gIGFzeW5jIHNlbmQobWVzc2FnZTogYW55KTogUHJvbWlzZTxib29sZWFuPiB7XG4gICAgaWYgKHRoaXMuc29ja2V0Py5yZWFkeVN0YXRlID09PSBXZWJTb2NrZXQuT1BFTikge1xuICAgICAgdHJ5IHtcbiAgICAgICAgdGhpcy5zb2NrZXQuc2VuZChKU09OLnN0cmluZ2lmeShtZXNzYWdlKSk7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHNlbmQgV2ViU29ja2V0IG1lc3NhZ2U6JywgZXJyb3IpO1xuICAgICAgICBhd2FpdCB1bmlmaWVkRXJyb3JIYW5kbGluZ1NlcnZpY2UuaGFuZGxlV2ViU29ja2V0RXJyb3IoZXJyb3IgYXMgRXJyb3IsIHtcbiAgICAgICAgICBhY3Rpb246ICdzZW5kX21lc3NhZ2VfZXJyb3InLFxuICAgICAgICAgIGFkZGl0aW9uYWxEYXRhOiB7IG1lc3NhZ2UsIHVybDogdGhpcy5jb25maWcudXJsIH1cbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgY29uc29sZS53YXJuKCdXZWJTb2NrZXQgbm90IGNvbm5lY3RlZCwgbWVzc2FnZSBub3Qgc2VudDonLCBtZXNzYWdlKTtcbiAgICAgIGF3YWl0IHVuaWZpZWRFcnJvckhhbmRsaW5nU2VydmljZS5oYW5kbGVXZWJTb2NrZXRFcnJvcihcbiAgICAgICAgbmV3IEVycm9yKCdXZWJTb2NrZXQgbm90IGNvbm5lY3RlZCcpLFxuICAgICAgICB7XG4gICAgICAgICAgYWN0aW9uOiAnc2VuZF9tZXNzYWdlX25vdF9jb25uZWN0ZWQnLFxuICAgICAgICAgIGFkZGl0aW9uYWxEYXRhOiB7XG4gICAgICAgICAgICBtZXNzYWdlLFxuICAgICAgICAgICAgdXJsOiB0aGlzLmNvbmZpZy51cmwsXG4gICAgICAgICAgICByZWFkeVN0YXRlOiB0aGlzLnNvY2tldD8ucmVhZHlTdGF0ZVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogU2VuZCBjaGF0IG1lc3NhZ2VcbiAgICovXG4gIGFzeW5jIHNlbmRDaGF0TWVzc2FnZShjb250ZW50OiBzdHJpbmcsIHJlcGx5VG8/OiBzdHJpbmcpOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgICByZXR1cm4gYXdhaXQgdGhpcy5zZW5kKHtcbiAgICAgIHR5cGU6ICdjaGF0X21lc3NhZ2UnLFxuICAgICAgY29udGVudCxcbiAgICAgIHJlcGx5X3RvOiByZXBseVRvLFxuICAgIH0pO1xuICB9XG5cbiAgLyoqXG4gICAqIFNlbmQgdHlwaW5nIGluZGljYXRvclxuICAgKi9cbiAgYXN5bmMgc2VuZFR5cGluZ0luZGljYXRvcihpc1R5cGluZzogYm9vbGVhbik6IFByb21pc2U8Ym9vbGVhbj4ge1xuICAgIHJldHVybiBhd2FpdCB0aGlzLnNlbmQoe1xuICAgICAgdHlwZTogJ3R5cGluZ19pbmRpY2F0b3InLFxuICAgICAgaXNfdHlwaW5nOiBpc1R5cGluZyxcbiAgICB9KTtcbiAgfVxuXG4gIC8qKlxuICAgKiBNYXJrIG1lc3NhZ2VzIGFzIHJlYWRcbiAgICovXG4gIGFzeW5jIG1hcmtNZXNzYWdlc1JlYWQobWVzc2FnZUlkczogc3RyaW5nW10pOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgICByZXR1cm4gYXdhaXQgdGhpcy5zZW5kKHtcbiAgICAgIHR5cGU6ICdtYXJrX3JlYWQnLFxuICAgICAgbWVzc2FnZV9pZHM6IG1lc3NhZ2VJZHMsXG4gICAgfSk7XG4gIH1cblxuICAvKipcbiAgICogQWRkIGV2ZW50IGxpc3RlbmVyXG4gICAqL1xuICBvbihldmVudDogc3RyaW5nLCBjYWxsYmFjazogKGRhdGE6IGFueSkgPT4gdm9pZCk6IHZvaWQge1xuICAgIGlmICghdGhpcy5ldmVudExpc3RlbmVycy5oYXMoZXZlbnQpKSB7XG4gICAgICB0aGlzLmV2ZW50TGlzdGVuZXJzLnNldChldmVudCwgbmV3IFNldCgpKTtcbiAgICB9XG4gICAgdGhpcy5ldmVudExpc3RlbmVycy5nZXQoZXZlbnQpIS5hZGQoY2FsbGJhY2spO1xuICB9XG5cbiAgLyoqXG4gICAqIFJlbW92ZSBldmVudCBsaXN0ZW5lclxuICAgKi9cbiAgb2ZmKGV2ZW50OiBzdHJpbmcsIGNhbGxiYWNrOiAoZGF0YTogYW55KSA9PiB2b2lkKTogdm9pZCB7XG4gICAgY29uc3QgbGlzdGVuZXJzID0gdGhpcy5ldmVudExpc3RlbmVycy5nZXQoZXZlbnQpO1xuICAgIGlmIChsaXN0ZW5lcnMpIHtcbiAgICAgIGxpc3RlbmVycy5kZWxldGUoY2FsbGJhY2spO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgY29ubmVjdGlvbiBzdGF0dXNcbiAgICovXG4gIGlzQ29ubmVjdGVkKCk6IGJvb2xlYW4ge1xuICAgIHJldHVybiB0aGlzLnNvY2tldD8ucmVhZHlTdGF0ZSA9PT0gV2ViU29ja2V0Lk9QRU47XG4gIH1cblxuICAvKipcbiAgICogR2V0IGNvbm5lY3Rpb24gc3RhdGVcbiAgICovXG4gIGdldENvbm5lY3Rpb25TdGF0ZSgpOiBzdHJpbmcge1xuICAgIGlmICghdGhpcy5zb2NrZXQpIHJldHVybiAnZGlzY29ubmVjdGVkJztcblxuICAgIHN3aXRjaCAodGhpcy5zb2NrZXQucmVhZHlTdGF0ZSkge1xuICAgICAgY2FzZSBXZWJTb2NrZXQuQ09OTkVDVElORzpcbiAgICAgICAgcmV0dXJuICdjb25uZWN0aW5nJztcbiAgICAgIGNhc2UgV2ViU29ja2V0Lk9QRU46XG4gICAgICAgIHJldHVybiAnY29ubmVjdGVkJztcbiAgICAgIGNhc2UgV2ViU29ja2V0LkNMT1NJTkc6XG4gICAgICAgIHJldHVybiAnY2xvc2luZyc7XG4gICAgICBjYXNlIFdlYlNvY2tldC5DTE9TRUQ6XG4gICAgICAgIHJldHVybiAnZGlzY29ubmVjdGVkJztcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiAndW5rbm93bic7XG4gICAgfVxuICB9XG5cbiAgLy8gUHJpdmF0ZSBtZXRob2RzXG4gIHByaXZhdGUgaGFuZGxlTWVzc2FnZShkYXRhOiBXZWJTb2NrZXRNZXNzYWdlKTogdm9pZCB7XG4gICAgdGhpcy5lbWl0KGRhdGEudHlwZSwgZGF0YSk7XG4gIH1cblxuICBwcml2YXRlIGVtaXQoZXZlbnQ6IHN0cmluZywgZGF0YTogYW55KTogdm9pZCB7XG4gICAgY29uc3QgbGlzdGVuZXJzID0gdGhpcy5ldmVudExpc3RlbmVycy5nZXQoZXZlbnQpO1xuICAgIGlmIChsaXN0ZW5lcnMpIHtcbiAgICAgIGxpc3RlbmVycy5mb3JFYWNoKGNhbGxiYWNrID0+IHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBjYWxsYmFjayhkYXRhKTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgICAgYEVycm9yIGluIFdlYlNvY2tldCBldmVudCBsaXN0ZW5lciBmb3IgJHtldmVudH06YCxcbiAgICAgICAgICAgIGVycm9yLFxuICAgICAgICAgICk7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgc2hvdWxkUmVjb25uZWN0KCk6IGJvb2xlYW4ge1xuICAgIHJldHVybiB0aGlzLnJlY29ubmVjdEF0dGVtcHRzIDwgdGhpcy5jb25maWcubWF4UmVjb25uZWN0QXR0ZW1wdHMhO1xuICB9XG5cbiAgcHJpdmF0ZSBzY2hlZHVsZVJlY29ubmVjdCgpOiB2b2lkIHtcbiAgICBpZiAodGhpcy5yZWNvbm5lY3RUaW1lcikge1xuICAgICAgY2xlYXJUaW1lb3V0KHRoaXMucmVjb25uZWN0VGltZXIpO1xuICAgIH1cblxuICAgIHRoaXMucmVjb25uZWN0VGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIHRoaXMucmVjb25uZWN0QXR0ZW1wdHMrKztcbiAgICAgIGNvbnNvbGUubG9nKFxuICAgICAgICBgQXR0ZW1wdGluZyB0byByZWNvbm5lY3QgKCR7dGhpcy5yZWNvbm5lY3RBdHRlbXB0c30vJHt0aGlzLmNvbmZpZy5tYXhSZWNvbm5lY3RBdHRlbXB0c30pYCxcbiAgICAgICk7XG4gICAgICB0aGlzLmNvbm5lY3QoKS5jYXRjaChlcnJvciA9PiB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1JlY29ubmVjdGlvbiBmYWlsZWQ6JywgZXJyb3IpO1xuICAgICAgfSk7XG4gICAgfSwgdGhpcy5jb25maWcucmVjb25uZWN0SW50ZXJ2YWwpO1xuICB9XG59XG5cbi8vIFdlYlNvY2tldCBzZXJ2aWNlIGZhY3RvcnlcbmV4cG9ydCBjb25zdCBjcmVhdGVXZWJTb2NrZXRTZXJ2aWNlID0gKFxuICBjb25maWc6IFdlYlNvY2tldENvbmZpZyxcbik6IFdlYlNvY2tldFNlcnZpY2UgPT4ge1xuICByZXR1cm4gbmV3IFdlYlNvY2tldFNlcnZpY2UoY29uZmlnKTtcbn07XG5cbi8vIE1lc3NhZ2luZyBXZWJTb2NrZXQgc2VydmljZSBmYWN0b3J5IChyZXF1aXJlcyBjb252ZXJzYXRpb24gSUQpXG5leHBvcnQgY29uc3QgY3JlYXRlTWVzc2FnaW5nV2ViU29ja2V0U2VydmljZSA9IChjb252ZXJzYXRpb25JZDogc3RyaW5nKSA9PlxuICBjcmVhdGVXZWJTb2NrZXRTZXJ2aWNlKHtcbiAgICB1cmw6IGB3czovLzE5Mi4xNjguMi42NTo4MDAwL3dzL21lc3NhZ2luZy8ke2NvbnZlcnNhdGlvbklkfS9gLFxuICAgIHJlY29ubmVjdEludGVydmFsOiAzMDAwLFxuICAgIG1heFJlY29ubmVjdEF0dGVtcHRzOiA1LFxuICB9KTtcblxuLy8gTm90aWZpY2F0aW9ucyBXZWJTb2NrZXQgc2VydmljZSBmYWN0b3J5IChyZXF1aXJlcyB1c2VyIElEKVxuZXhwb3J0IGNvbnN0IGNyZWF0ZU5vdGlmaWNhdGlvbnNXZWJTb2NrZXRTZXJ2aWNlID0gKHVzZXJJZDogc3RyaW5nKSA9PlxuICBjcmVhdGVXZWJTb2NrZXRTZXJ2aWNlKHtcbiAgICB1cmw6IGB3czovLzE5Mi4xNjguMi42NTo4MDAwL3dzL25vdGlmaWNhdGlvbnMvJHt1c2VySWR9L2AsXG4gICAgcmVjb25uZWN0SW50ZXJ2YWw6IDUwMDAsXG4gICAgbWF4UmVjb25uZWN0QXR0ZW1wdHM6IDMsXG4gIH0pO1xuXG5leHBvcnQgZGVmYXVsdCBXZWJTb2NrZXRTZXJ2aWNlO1xuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7OztBQWVBLElBQUFBLHFCQUFBLEdBQUFDLE9BQUE7QUFBcUUsSUEwQi9EQyxnQkFBZ0I7RUFTcEIsU0FBQUEsaUJBQVlDLE1BQXVCLEVBQUU7SUFBQSxJQUFBQyxnQkFBQSxDQUFBQyxPQUFBLFFBQUFILGdCQUFBO0lBQUEsS0FSN0JJLE1BQU0sR0FBcUIsSUFBSTtJQUFBLEtBRS9CQyxpQkFBaUIsR0FBRyxDQUFDO0lBQUEsS0FDckJDLGNBQWMsR0FBMEIsSUFBSTtJQUFBLEtBQzVDQyxZQUFZLEdBQUcsS0FBSztJQUFBLEtBQ3BCQyxjQUFjLEdBQTBDLElBQUlDLEdBQUcsQ0FBQyxDQUFDO0lBQUEsS0FDakVDLGlCQUFpQixHQUF5QixJQUFJO0lBR3BELElBQUksQ0FBQ1QsTUFBTSxHQUFBVSxNQUFBLENBQUFDLE1BQUE7TUFDVEMsaUJBQWlCLEVBQUUsSUFBSTtNQUN2QkMsb0JBQW9CLEVBQUU7SUFBQyxHQUNwQmIsTUFBTSxDQUNWO0VBQ0g7RUFBQyxXQUFBYyxhQUFBLENBQUFaLE9BQUEsRUFBQUgsZ0JBQUE7SUFBQWdCLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUFDLFFBQUEsT0FBQUMsa0JBQUEsQ0FBQWhCLE9BQUEsRUFLRCxXQUFjaUIsS0FBYyxFQUFpQjtRQUFBLElBQUFDLFlBQUE7VUFBQUMsS0FBQTtRQUMzQyxJQUFJLEVBQUFELFlBQUEsT0FBSSxDQUFDakIsTUFBTSxxQkFBWGlCLFlBQUEsQ0FBYUUsVUFBVSxNQUFLQyxTQUFTLENBQUNDLElBQUksRUFBRTtVQUM5QyxPQUFPQyxPQUFPLENBQUNDLE9BQU8sQ0FBQyxDQUFDO1FBQzFCO1FBRUEsSUFBSSxJQUFJLENBQUNqQixpQkFBaUIsRUFBRTtVQUMxQixPQUFPLElBQUksQ0FBQ0EsaUJBQWlCO1FBQy9CO1FBRUEsSUFBSSxDQUFDQSxpQkFBaUIsR0FBRyxJQUFJZ0IsT0FBTyxDQUFDLFVBQUNDLE9BQU8sRUFBRUMsTUFBTSxFQUFLO1VBQ3hELElBQUk7WUFDRk4sS0FBSSxDQUFDZixZQUFZLEdBQUcsSUFBSTtZQUd4QixJQUFNc0IsS0FBSyxHQUFHVCxLQUFLLEdBQ2YsR0FBR0UsS0FBSSxDQUFDckIsTUFBTSxDQUFDNkIsR0FBRyxVQUFVVixLQUFLLEVBQUUsR0FDbkNFLEtBQUksQ0FBQ3JCLE1BQU0sQ0FBQzZCLEdBQUc7WUFFbkJSLEtBQUksQ0FBQ2xCLE1BQU0sR0FBRyxJQUFJb0IsU0FBUyxDQUFDSyxLQUFLLEVBQUVQLEtBQUksQ0FBQ3JCLE1BQU0sQ0FBQzhCLFNBQVMsQ0FBQztZQUV6RFQsS0FBSSxDQUFDbEIsTUFBTSxDQUFDNEIsTUFBTSxHQUFHLFlBQU07Y0FDekJDLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHFCQUFxQixDQUFDO2NBQ2xDWixLQUFJLENBQUNmLFlBQVksR0FBRyxLQUFLO2NBQ3pCZSxLQUFJLENBQUNqQixpQkFBaUIsR0FBRyxDQUFDO2NBQzFCaUIsS0FBSSxDQUFDWixpQkFBaUIsR0FBRyxJQUFJO2NBQzdCWSxLQUFJLENBQUNhLElBQUksQ0FBQyxXQUFXLEVBQUUsQ0FBQyxDQUFDLENBQUM7Y0FDMUJSLE9BQU8sQ0FBQyxDQUFDO1lBQ1gsQ0FBQztZQUVETCxLQUFJLENBQUNsQixNQUFNLENBQUNnQyxTQUFTLEdBQUcsVUFBQUMsS0FBSyxFQUFJO2NBQy9CLElBQUk7Z0JBQ0YsSUFBTUMsSUFBc0IsR0FBR0MsSUFBSSxDQUFDQyxLQUFLLENBQUNILEtBQUssQ0FBQ0MsSUFBSSxDQUFDO2dCQUNyRGhCLEtBQUksQ0FBQ21CLGFBQWEsQ0FBQ0gsSUFBSSxDQUFDO2NBQzFCLENBQUMsQ0FBQyxPQUFPSSxLQUFLLEVBQUU7Z0JBQ2RULE9BQU8sQ0FBQ1MsS0FBSyxDQUFDLG9DQUFvQyxFQUFFQSxLQUFLLENBQUM7Z0JBRTFEQyxpREFBMkIsQ0FBQ0Msb0JBQW9CLENBQUNGLEtBQUssRUFBVztrQkFDL0RHLE1BQU0sRUFBRSxxQkFBcUI7a0JBQzdCQyxjQUFjLEVBQUU7b0JBQUVDLFVBQVUsRUFBRVYsS0FBSyxDQUFDQyxJQUFJO29CQUFFUixHQUFHLEVBQUVSLEtBQUksQ0FBQ3JCLE1BQU0sQ0FBQzZCO2tCQUFJO2dCQUNqRSxDQUFDLENBQUM7Y0FDSjtZQUNGLENBQUM7WUFFRFIsS0FBSSxDQUFDbEIsTUFBTSxDQUFDNEMsT0FBTztjQUFBLElBQUFDLElBQUEsT0FBQTlCLGtCQUFBLENBQUFoQixPQUFBLEVBQUcsV0FBTWtDLEtBQUssRUFBSTtnQkFDbkNKLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHlCQUF5QixFQUFFRyxLQUFLLENBQUNhLElBQUksRUFBRWIsS0FBSyxDQUFDYyxNQUFNLENBQUM7Z0JBQ2hFN0IsS0FBSSxDQUFDZixZQUFZLEdBQUcsS0FBSztnQkFDekJlLEtBQUksQ0FBQ1osaUJBQWlCLEdBQUcsSUFBSTtnQkFDN0JZLEtBQUksQ0FBQ2EsSUFBSSxDQUFDLGNBQWMsRUFBRTtrQkFBRWUsSUFBSSxFQUFFYixLQUFLLENBQUNhLElBQUk7a0JBQUVDLE1BQU0sRUFBRWQsS0FBSyxDQUFDYztnQkFBTyxDQUFDLENBQUM7Z0JBR3JFLElBQUlkLEtBQUssQ0FBQ2EsSUFBSSxLQUFLLElBQUksRUFBRTtrQkFFdkIsTUFBTVAsaURBQTJCLENBQUNTLHlCQUF5QixDQUN6RCxJQUFJQyxLQUFLLENBQUMsaUNBQWlDLENBQUMsRUFDNUM7b0JBQ0VSLE1BQU0sRUFBRSx1QkFBdUI7b0JBQy9CQyxjQUFjLEVBQUU7c0JBQUVJLElBQUksRUFBRWIsS0FBSyxDQUFDYSxJQUFJO3NCQUFFQyxNQUFNLEVBQUVkLEtBQUssQ0FBQ2MsTUFBTTtzQkFBRXJCLEdBQUcsRUFBRVIsS0FBSSxDQUFDckIsTUFBTSxDQUFDNkI7b0JBQUk7a0JBQ2pGLENBQ0YsQ0FBQztnQkFDSCxDQUFDLE1BQU0sSUFBSSxDQUFDTyxLQUFLLENBQUNpQixRQUFRLElBQUloQyxLQUFJLENBQUNpQyxlQUFlLENBQUMsQ0FBQyxFQUFFO2tCQUNwRGpDLEtBQUksQ0FBQ2tDLGlCQUFpQixDQUFDLENBQUM7Z0JBQzFCO2NBQ0YsQ0FBQztjQUFBLGlCQUFBQyxHQUFBO2dCQUFBLE9BQUFSLElBQUEsQ0FBQVMsS0FBQSxPQUFBQyxTQUFBO2NBQUE7WUFBQTtZQUVEckMsS0FBSSxDQUFDbEIsTUFBTSxDQUFDd0QsT0FBTztjQUFBLElBQUFDLEtBQUEsT0FBQTFDLGtCQUFBLENBQUFoQixPQUFBLEVBQUcsV0FBTXVDLEtBQUssRUFBSTtnQkFBQSxJQUFBb0IsYUFBQTtnQkFDbkM3QixPQUFPLENBQUNTLEtBQUssQ0FBQyxrQkFBa0IsRUFBRUEsS0FBSyxDQUFDO2dCQUN4Q3BCLEtBQUksQ0FBQ2YsWUFBWSxHQUFHLEtBQUs7Z0JBQ3pCZSxLQUFJLENBQUNaLGlCQUFpQixHQUFHLElBQUk7Z0JBRzdCLE1BQU1pQyxpREFBMkIsQ0FBQ0Msb0JBQW9CLENBQUNGLEtBQUssRUFBVztrQkFDckVHLE1BQU0sRUFBRSxrQkFBa0I7a0JBQzFCQyxjQUFjLEVBQUU7b0JBQ2RoQixHQUFHLEVBQUVSLEtBQUksQ0FBQ3JCLE1BQU0sQ0FBQzZCLEdBQUc7b0JBQ3BCUCxVQUFVLEdBQUF1QyxhQUFBLEdBQUV4QyxLQUFJLENBQUNsQixNQUFNLHFCQUFYMEQsYUFBQSxDQUFhdkM7a0JBQzNCO2dCQUNGLENBQUMsQ0FBQztnQkFFRkQsS0FBSSxDQUFDYSxJQUFJLENBQUMsT0FBTyxFQUFFO2tCQUFFTyxLQUFLLEVBQUxBO2dCQUFNLENBQUMsQ0FBQztnQkFDN0JkLE1BQU0sQ0FBQ2MsS0FBSyxDQUFDO2NBQ2YsQ0FBQztjQUFBLGlCQUFBcUIsR0FBQTtnQkFBQSxPQUFBRixLQUFBLENBQUFILEtBQUEsT0FBQUMsU0FBQTtjQUFBO1lBQUE7VUFDSCxDQUFDLENBQUMsT0FBT2pCLEtBQUssRUFBRTtZQUNkcEIsS0FBSSxDQUFDZixZQUFZLEdBQUcsS0FBSztZQUN6QmUsS0FBSSxDQUFDWixpQkFBaUIsR0FBRyxJQUFJO1lBQzdCa0IsTUFBTSxDQUFDYyxLQUFLLENBQUM7VUFDZjtRQUNGLENBQUMsQ0FBQztRQUVGLE9BQU8sSUFBSSxDQUFDaEMsaUJBQWlCO01BQy9CLENBQUM7TUFBQSxTQXpGS3NELE9BQU9BLENBQUFDLEVBQUE7UUFBQSxPQUFBL0MsUUFBQSxDQUFBd0MsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFQSyxPQUFPO0lBQUE7RUFBQTtJQUFBaEQsR0FBQTtJQUFBQyxLQUFBLEVBOEZiLFNBQUFpRCxVQUFVQSxDQUFBLEVBQVM7TUFDakIsSUFBSSxJQUFJLENBQUM1RCxjQUFjLEVBQUU7UUFDdkI2RCxZQUFZLENBQUMsSUFBSSxDQUFDN0QsY0FBYyxDQUFDO1FBQ2pDLElBQUksQ0FBQ0EsY0FBYyxHQUFHLElBQUk7TUFDNUI7TUFFQSxJQUFJLElBQUksQ0FBQ0YsTUFBTSxFQUFFO1FBQ2YsSUFBSSxDQUFDQSxNQUFNLENBQUNnRSxLQUFLLENBQUMsSUFBSSxFQUFFLG1CQUFtQixDQUFDO1FBQzVDLElBQUksQ0FBQ2hFLE1BQU0sR0FBRyxJQUFJO01BQ3BCO01BRUEsSUFBSSxDQUFDTSxpQkFBaUIsR0FBRyxJQUFJO01BQzdCLElBQUksQ0FBQ0wsaUJBQWlCLEdBQUcsQ0FBQztJQUM1QjtFQUFDO0lBQUFXLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUFvRCxLQUFBLE9BQUFsRCxrQkFBQSxDQUFBaEIsT0FBQSxFQUtELFdBQVdtRSxPQUFZLEVBQW9CO1FBQUEsSUFBQUMsYUFBQTtRQUN6QyxJQUFJLEVBQUFBLGFBQUEsT0FBSSxDQUFDbkUsTUFBTSxxQkFBWG1FLGFBQUEsQ0FBYWhELFVBQVUsTUFBS0MsU0FBUyxDQUFDQyxJQUFJLEVBQUU7VUFDOUMsSUFBSTtZQUNGLElBQUksQ0FBQ3JCLE1BQU0sQ0FBQ29FLElBQUksQ0FBQ2pDLElBQUksQ0FBQ2tDLFNBQVMsQ0FBQ0gsT0FBTyxDQUFDLENBQUM7WUFDekMsT0FBTyxJQUFJO1VBQ2IsQ0FBQyxDQUFDLE9BQU81QixLQUFLLEVBQUU7WUFDZFQsT0FBTyxDQUFDUyxLQUFLLENBQUMsbUNBQW1DLEVBQUVBLEtBQUssQ0FBQztZQUN6RCxNQUFNQyxpREFBMkIsQ0FBQ0Msb0JBQW9CLENBQUNGLEtBQUssRUFBVztjQUNyRUcsTUFBTSxFQUFFLG9CQUFvQjtjQUM1QkMsY0FBYyxFQUFFO2dCQUFFd0IsT0FBTyxFQUFQQSxPQUFPO2dCQUFFeEMsR0FBRyxFQUFFLElBQUksQ0FBQzdCLE1BQU0sQ0FBQzZCO2NBQUk7WUFDbEQsQ0FBQyxDQUFDO1lBQ0YsT0FBTyxLQUFLO1VBQ2Q7UUFDRixDQUFDLE1BQU07VUFBQSxJQUFBNEMsYUFBQTtVQUNMekMsT0FBTyxDQUFDMEMsSUFBSSxDQUFDLDRDQUE0QyxFQUFFTCxPQUFPLENBQUM7VUFDbkUsTUFBTTNCLGlEQUEyQixDQUFDQyxvQkFBb0IsQ0FDcEQsSUFBSVMsS0FBSyxDQUFDLHlCQUF5QixDQUFDLEVBQ3BDO1lBQ0VSLE1BQU0sRUFBRSw0QkFBNEI7WUFDcENDLGNBQWMsRUFBRTtjQUNkd0IsT0FBTyxFQUFQQSxPQUFPO2NBQ1B4QyxHQUFHLEVBQUUsSUFBSSxDQUFDN0IsTUFBTSxDQUFDNkIsR0FBRztjQUNwQlAsVUFBVSxHQUFBbUQsYUFBQSxHQUFFLElBQUksQ0FBQ3RFLE1BQU0scUJBQVhzRSxhQUFBLENBQWFuRDtZQUMzQjtVQUNGLENBQ0YsQ0FBQztVQUNELE9BQU8sS0FBSztRQUNkO01BQ0YsQ0FBQztNQUFBLFNBNUJLaUQsSUFBSUEsQ0FBQUksR0FBQTtRQUFBLE9BQUFQLEtBQUEsQ0FBQVgsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFKYSxJQUFJO0lBQUE7RUFBQTtJQUFBeEQsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQTRELGdCQUFBLE9BQUExRCxrQkFBQSxDQUFBaEIsT0FBQSxFQWlDVixXQUFzQjJFLE9BQWUsRUFBRUMsT0FBZ0IsRUFBb0I7UUFDekUsYUFBYSxJQUFJLENBQUNQLElBQUksQ0FBQztVQUNyQlEsSUFBSSxFQUFFLGNBQWM7VUFDcEJGLE9BQU8sRUFBUEEsT0FBTztVQUNQRyxRQUFRLEVBQUVGO1FBQ1osQ0FBQyxDQUFDO01BQ0osQ0FBQztNQUFBLFNBTktHLGVBQWVBLENBQUFDLEdBQUEsRUFBQUMsR0FBQTtRQUFBLE9BQUFQLGdCQUFBLENBQUFuQixLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQWZ1QixlQUFlO0lBQUE7RUFBQTtJQUFBbEUsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQW9FLG9CQUFBLE9BQUFsRSxrQkFBQSxDQUFBaEIsT0FBQSxFQVdyQixXQUEwQm1GLFFBQWlCLEVBQW9CO1FBQzdELGFBQWEsSUFBSSxDQUFDZCxJQUFJLENBQUM7VUFDckJRLElBQUksRUFBRSxrQkFBa0I7VUFDeEJPLFNBQVMsRUFBRUQ7UUFDYixDQUFDLENBQUM7TUFDSixDQUFDO01BQUEsU0FMS0UsbUJBQW1CQSxDQUFBQyxHQUFBO1FBQUEsT0FBQUosb0JBQUEsQ0FBQTNCLEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBbkI2QixtQkFBbUI7SUFBQTtFQUFBO0lBQUF4RSxHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBeUUsaUJBQUEsT0FBQXZFLGtCQUFBLENBQUFoQixPQUFBLEVBVXpCLFdBQXVCd0YsVUFBb0IsRUFBb0I7UUFDN0QsYUFBYSxJQUFJLENBQUNuQixJQUFJLENBQUM7VUFDckJRLElBQUksRUFBRSxXQUFXO1VBQ2pCWSxXQUFXLEVBQUVEO1FBQ2YsQ0FBQyxDQUFDO01BQ0osQ0FBQztNQUFBLFNBTEtFLGdCQUFnQkEsQ0FBQUMsR0FBQTtRQUFBLE9BQUFKLGlCQUFBLENBQUFoQyxLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQWhCa0MsZ0JBQWdCO0lBQUE7RUFBQTtJQUFBN0UsR0FBQTtJQUFBQyxLQUFBLEVBVXRCLFNBQUE4RSxFQUFFQSxDQUFDMUQsS0FBYSxFQUFFMkQsUUFBNkIsRUFBUTtNQUNyRCxJQUFJLENBQUMsSUFBSSxDQUFDeEYsY0FBYyxDQUFDeUYsR0FBRyxDQUFDNUQsS0FBSyxDQUFDLEVBQUU7UUFDbkMsSUFBSSxDQUFDN0IsY0FBYyxDQUFDMEYsR0FBRyxDQUFDN0QsS0FBSyxFQUFFLElBQUk4RCxHQUFHLENBQUMsQ0FBQyxDQUFDO01BQzNDO01BQ0EsSUFBSSxDQUFDM0YsY0FBYyxDQUFDNEYsR0FBRyxDQUFDL0QsS0FBSyxDQUFDLENBQUVnRSxHQUFHLENBQUNMLFFBQVEsQ0FBQztJQUMvQztFQUFDO0lBQUFoRixHQUFBO0lBQUFDLEtBQUEsRUFLRCxTQUFBcUYsR0FBR0EsQ0FBQ2pFLEtBQWEsRUFBRTJELFFBQTZCLEVBQVE7TUFDdEQsSUFBTU8sU0FBUyxHQUFHLElBQUksQ0FBQy9GLGNBQWMsQ0FBQzRGLEdBQUcsQ0FBQy9ELEtBQUssQ0FBQztNQUNoRCxJQUFJa0UsU0FBUyxFQUFFO1FBQ2JBLFNBQVMsQ0FBQ0MsTUFBTSxDQUFDUixRQUFRLENBQUM7TUFDNUI7SUFDRjtFQUFDO0lBQUFoRixHQUFBO0lBQUFDLEtBQUEsRUFLRCxTQUFBd0YsV0FBV0EsQ0FBQSxFQUFZO01BQUEsSUFBQUMsYUFBQTtNQUNyQixPQUFPLEVBQUFBLGFBQUEsT0FBSSxDQUFDdEcsTUFBTSxxQkFBWHNHLGFBQUEsQ0FBYW5GLFVBQVUsTUFBS0MsU0FBUyxDQUFDQyxJQUFJO0lBQ25EO0VBQUM7SUFBQVQsR0FBQTtJQUFBQyxLQUFBLEVBS0QsU0FBQTBGLGtCQUFrQkEsQ0FBQSxFQUFXO01BQzNCLElBQUksQ0FBQyxJQUFJLENBQUN2RyxNQUFNLEVBQUUsT0FBTyxjQUFjO01BRXZDLFFBQVEsSUFBSSxDQUFDQSxNQUFNLENBQUNtQixVQUFVO1FBQzVCLEtBQUtDLFNBQVMsQ0FBQ29GLFVBQVU7VUFDdkIsT0FBTyxZQUFZO1FBQ3JCLEtBQUtwRixTQUFTLENBQUNDLElBQUk7VUFDakIsT0FBTyxXQUFXO1FBQ3BCLEtBQUtELFNBQVMsQ0FBQ3FGLE9BQU87VUFDcEIsT0FBTyxTQUFTO1FBQ2xCLEtBQUtyRixTQUFTLENBQUNzRixNQUFNO1VBQ25CLE9BQU8sY0FBYztRQUN2QjtVQUNFLE9BQU8sU0FBUztNQUNwQjtJQUNGO0VBQUM7SUFBQTlGLEdBQUE7SUFBQUMsS0FBQSxFQUdELFNBQVF3QixhQUFhQSxDQUFDSCxJQUFzQixFQUFRO01BQ2xELElBQUksQ0FBQ0gsSUFBSSxDQUFDRyxJQUFJLENBQUMwQyxJQUFJLEVBQUUxQyxJQUFJLENBQUM7SUFDNUI7RUFBQztJQUFBdEIsR0FBQTtJQUFBQyxLQUFBLEVBRUQsU0FBUWtCLElBQUlBLENBQUNFLEtBQWEsRUFBRUMsSUFBUyxFQUFRO01BQzNDLElBQU1pRSxTQUFTLEdBQUcsSUFBSSxDQUFDL0YsY0FBYyxDQUFDNEYsR0FBRyxDQUFDL0QsS0FBSyxDQUFDO01BQ2hELElBQUlrRSxTQUFTLEVBQUU7UUFDYkEsU0FBUyxDQUFDUSxPQUFPLENBQUMsVUFBQWYsUUFBUSxFQUFJO1VBQzVCLElBQUk7WUFDRkEsUUFBUSxDQUFDMUQsSUFBSSxDQUFDO1VBQ2hCLENBQUMsQ0FBQyxPQUFPSSxLQUFLLEVBQUU7WUFDZFQsT0FBTyxDQUFDUyxLQUFLLENBQ1gseUNBQXlDTCxLQUFLLEdBQUcsRUFDakRLLEtBQ0YsQ0FBQztVQUNIO1FBQ0YsQ0FBQyxDQUFDO01BQ0o7SUFDRjtFQUFDO0lBQUExQixHQUFBO0lBQUFDLEtBQUEsRUFFRCxTQUFRc0MsZUFBZUEsQ0FBQSxFQUFZO01BQ2pDLE9BQU8sSUFBSSxDQUFDbEQsaUJBQWlCLEdBQUcsSUFBSSxDQUFDSixNQUFNLENBQUNhLG9CQUFxQjtJQUNuRTtFQUFDO0lBQUFFLEdBQUE7SUFBQUMsS0FBQSxFQUVELFNBQVF1QyxpQkFBaUJBLENBQUEsRUFBUztNQUFBLElBQUF3RCxNQUFBO01BQ2hDLElBQUksSUFBSSxDQUFDMUcsY0FBYyxFQUFFO1FBQ3ZCNkQsWUFBWSxDQUFDLElBQUksQ0FBQzdELGNBQWMsQ0FBQztNQUNuQztNQUVBLElBQUksQ0FBQ0EsY0FBYyxHQUFHMkcsVUFBVSxDQUFDLFlBQU07UUFDckNELE1BQUksQ0FBQzNHLGlCQUFpQixFQUFFO1FBQ3hCNEIsT0FBTyxDQUFDQyxHQUFHLENBQ1QsNEJBQTRCOEUsTUFBSSxDQUFDM0csaUJBQWlCLElBQUkyRyxNQUFJLENBQUMvRyxNQUFNLENBQUNhLG9CQUFvQixHQUN4RixDQUFDO1FBQ0RrRyxNQUFJLENBQUNoRCxPQUFPLENBQUMsQ0FBQyxDQUFDa0QsS0FBSyxDQUFDLFVBQUF4RSxLQUFLLEVBQUk7VUFDNUJULE9BQU8sQ0FBQ1MsS0FBSyxDQUFDLHNCQUFzQixFQUFFQSxLQUFLLENBQUM7UUFDOUMsQ0FBQyxDQUFDO01BQ0osQ0FBQyxFQUFFLElBQUksQ0FBQ3pDLE1BQU0sQ0FBQ1ksaUJBQWlCLENBQUM7SUFDbkM7RUFBQztBQUFBO0FBSUksSUFBTXNHLHNCQUFzQixHQUFBQyxPQUFBLENBQUFELHNCQUFBLEdBQUcsU0FBekJBLHNCQUFzQkEsQ0FDakNsSCxNQUF1QixFQUNGO0VBQ3JCLE9BQU8sSUFBSUQsZ0JBQWdCLENBQUNDLE1BQU0sQ0FBQztBQUNyQyxDQUFDO0FBR00sSUFBTW9ILCtCQUErQixHQUFBRCxPQUFBLENBQUFDLCtCQUFBLEdBQUcsU0FBbENBLCtCQUErQkEsQ0FBSUMsY0FBc0I7RUFBQSxPQUNwRUgsc0JBQXNCLENBQUM7SUFDckJyRixHQUFHLEVBQUUsdUNBQXVDd0YsY0FBYyxHQUFHO0lBQzdEekcsaUJBQWlCLEVBQUUsSUFBSTtJQUN2QkMsb0JBQW9CLEVBQUU7RUFDeEIsQ0FBQyxDQUFDO0FBQUE7QUFHRyxJQUFNeUcsbUNBQW1DLEdBQUFILE9BQUEsQ0FBQUcsbUNBQUEsR0FBRyxTQUF0Q0EsbUNBQW1DQSxDQUFJQyxNQUFjO0VBQUEsT0FDaEVMLHNCQUFzQixDQUFDO0lBQ3JCckYsR0FBRyxFQUFFLDJDQUEyQzBGLE1BQU0sR0FBRztJQUN6RDNHLGlCQUFpQixFQUFFLElBQUk7SUFDdkJDLG9CQUFvQixFQUFFO0VBQ3hCLENBQUMsQ0FBQztBQUFBO0FBQUMsSUFBQTJHLFFBQUEsR0FBQUwsT0FBQSxDQUFBakgsT0FBQSxHQUVVSCxnQkFBZ0IiLCJpZ25vcmVMaXN0IjpbXX0=