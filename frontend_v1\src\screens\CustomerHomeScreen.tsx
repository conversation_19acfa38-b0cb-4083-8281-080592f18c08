import { Ionicons } from '@expo/vector-icons';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import {
  useNavigation,
  CompositeNavigationProp,
} from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';

// Enhanced components following Aura design system
import { Box } from '../components/atoms/Box';
import { IconButton } from '../components/atoms/IconButton';
import { StoreImage } from '../components/molecules/StoreImage';
import { SafeAreaScreen } from '../components/ui/SafeAreaWrapper';
import { useTheme } from '../contexts/ThemeContext';
import { useI18n } from '../contexts/I18nContext';
import type {
  CustomerTabParamList,
  CustomerStackParamList,
} from '../navigation/types';
// import { useAuthStore } from '../store/authSlice'; // Commented out as unused

type CustomerHomeScreenNavigationProp = CompositeNavigationProp<
  BottomTabNavigationProp<CustomerTabParamList, 'Home'>,
  StackNavigationProp<CustomerStackParamList>
>;

interface ServiceCategory {
  id: string;
  name: string;
  slug: string;
  color: string;
  service_count: number;
  icon: string;
  mobile_icon?: string;
  description?: string;
  is_popular: boolean;
}

interface FeaturedProvider {
  id: string;
  business_name: string;
  business_description: string;
  city: string;
  state: string;
  rating: number;
  review_count: number;
  is_verified: boolean;
  is_featured: boolean;
  profile_image_url?: string;
  category_names?: string[];
  service_count: number;
  distance?: number;
}

// Screen dimensions for responsive design (commented out as unused)
// const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Import responsive utilities for consistent spacing and sizing
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
  getMinimumTouchTarget,
} from '../utils/responsiveUtils';

const CustomerHomeScreen: React.FC = () => {
  const navigation = useNavigation<CustomerHomeScreenNavigationProp>();
  const { colors } = useTheme();
  const { t, isLoading: i18nLoading } = useI18n();
  // const { isAuthenticated } = useAuthStore(); // Commented out as unused
  const styles = createStyles(colors);

  // Safe translation function that provides fallbacks
  const safeT = (key: string, fallback?: string): string => {
    if (i18nLoading) {
      return fallback || key.split('.').pop() || key;
    }
    const translation = t(key);
    if (translation && !translation.startsWith('[Missing:') && !translation.startsWith('[Invalid:')) {
      return translation;
    }
    return fallback || key.split('.').pop() || key;
  };

  // Enhanced state management
  const [greeting, setGreeting] = useState('');
  const [categories, setCategories] = useState<ServiceCategory[]>([]);
  const [featuredProviders, setFeaturedProviders] = useState<
    FeaturedProvider[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [providersLoading, setProvidersLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [providersError, setProvidersError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  // const [searchQuery, setSearchQuery] = useState(''); // Commented out as unused

  // Optimized fetch categories with error handling
  const fetchCategories = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(
        'http://************:8000/api/catalog/categories/',
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      // console.log('📊 Categories API Response:', data);

      // Handle both paginated and direct array responses
      const categoriesData = Array.isArray(data) ? data : data.results || [];

      // Take first 8 categories for home screen
      const homeCategories = categoriesData.slice(0, 8);
      setCategories(homeCategories);

      console.log(
        `✅ Loaded ${homeCategories.length} categories for home screen`,
      );
    } catch (err) {
      console.error('❌ Error fetching categories:', err);
      setError(
        err instanceof Error ? err.message : 'Failed to load categories',
      );

      // Fallback to static categories if API fails
      setCategories([
        {
          id: '1',
          name: 'Hair Services',
          slug: 'hair-services',
          color: colors.sage400 || '#8FBC8F',
          service_count: 24,
          icon: '💇‍♀️',
          description: 'Cuts, styling, coloring, and treatments',
          is_popular: true,
        },
        {
          id: '2',
          name: 'Nail Services',
          slug: 'nail-services',
          color: '#9ACD32',
          service_count: 18,
          icon: '💅',
          description: 'Manicures, pedicures, nail art',
          is_popular: true,
        },
        {
          id: '3',
          name: 'Lash Services',
          slug: 'lash-services',
          color: '#6B8E23',
          service_count: 12,
          icon: '👁️',
          description: 'Extensions, lifts, tinting',
          is_popular: true,
        },
        {
          id: '4',
          name: 'Braiding',
          slug: 'braiding',
          color: colors.sage400 || '#8FBC8F',
          service_count: 15,
          icon: '🤎',
          description: 'Protective styles and braiding',
          is_popular: true,
        },
        {
          id: '5',
          name: 'Skincare',
          slug: 'skincare',
          color: '#9ACD32',
          service_count: 20,
          icon: '✨',
          description: 'Facials, treatments, and skincare',
          is_popular: true,
        },
        {
          id: '6',
          name: 'Massage',
          slug: 'massage',
          color: '#6B8E23',
          service_count: 8,
          icon: '💆‍♀️',
          description: 'Relaxation and therapeutic massage',
          is_popular: true,
        },
        {
          id: '7',
          name: 'Makeup',
          slug: 'makeup',
          color: colors.sage400 || '#8FBC8F',
          service_count: 16,
          icon: '💄',
          description: 'Special occasion and everyday makeup',
          is_popular: true,
        },
        {
          id: '8',
          name: 'Locs & Twists',
          slug: 'locs-twists',
          color: '#9ACD32',
          service_count: 11,
          icon: '🌀',
          description: 'Loc maintenance and twist styles',
          is_popular: true,
        },
      ]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Optimized fetch featured providers with error handling
  const fetchFeaturedProviders = useCallback(async () => {
    try {
      setProvidersLoading(true);
      setProvidersError(null);

      const response = await fetch(
        'http://************:8000/api/catalog/providers/featured/?limit=10',
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      // console.log('🏪 Featured Providers API Response:', data);

      // Handle both direct array and object with results
      const providersData = Array.isArray(data)
        ? data
        : data.results || data.data || [];

      // Transform provider data to ensure rating is a number
      const transformedProviders = providersData.map((provider: any) => ({
        ...provider,
        rating: typeof provider.rating === 'string'
          ? parseFloat(provider.rating) || 0
          : provider.rating || 0,
        review_count: provider.review_count || 0,
      }));

      setFeaturedProviders(transformedProviders.slice(0, 6)); // Show max 6 providers
      console.log(`✅ Loaded ${providersData.length} featured providers`);
    } catch (err) {
      console.error('❌ Error fetching featured providers:', err);
      setProvidersError(
        err instanceof Error
          ? err.message
          : 'Failed to load featured providers',
      );

      // Fallback to empty array if API fails
      setFeaturedProviders([]);
    } finally {
      setProvidersLoading(false);
    }
  }, []);

  // Refresh functionality
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await Promise.all([fetchCategories(), fetchFeaturedProviders()]);
    } catch (error) {
      console.error('❌ Refresh error:', error);
    } finally {
      setRefreshing(false);
    }
  }, [fetchCategories, fetchFeaturedProviders]);

  // Initialize data and greeting
  useEffect(() => {
    // Don't set greeting if i18n is still loading
    if (i18nLoading) {
      return;
    }

    const hour = new Date().getHours();
    let greetingKey = 'home.greeting_morning';
    if (hour >= 12 && hour < 18) {
      greetingKey = 'home.greeting_afternoon';
    } else if (hour >= 18) {
      greetingKey = 'home.greeting_evening';
    }

    const translatedGreeting = t(greetingKey);
    // Only set greeting if translation is valid
    if (translatedGreeting && !translatedGreeting.startsWith('[Missing:') && !translatedGreeting.startsWith('[Invalid:')) {
      setGreeting(translatedGreeting);
    } else {
      // Fallback to English greeting
      const fallbackGreetings: Record<string, string> = {
        'home.greeting_morning': 'Good morning',
        'home.greeting_afternoon': 'Good afternoon',
        'home.greeting_evening': 'Good evening',
      };
      setGreeting(fallbackGreetings[greetingKey] || 'Hello');
    }

    // Fetch data from backend
    fetchCategories();
    fetchFeaturedProviders();
  }, [fetchCategories, fetchFeaturedProviders, t, i18nLoading]);

  const handleCategoryPress = (categoryName: string) => {
    console.log(`🔍 Category pressed: ${categoryName}`);
    navigation.navigate('Search', { categoryName });
  };

  const handleFavoritesPress = () => {
    console.log('❤️ Favorites pressed');
    // TODO: Navigate to Favorites screen when implemented
    navigation.navigate('Profile');
  };



  const handleProviderPress = (providerId: string) => {
    console.log(`🏪 Provider pressed: ${providerId}`);
    navigation.navigate('ProviderDetails', { providerId });
  };

  const handleSeeAllCategories = () => {
    console.log('📋 See all categories pressed');
    navigation.navigate('Search');
  };

  const handleViewAllProviders = () => {
    console.log('🏪 View all providers pressed');
    navigation.navigate('Search');
  };

  const renderCategoryCard = ({ item }: { item: ServiceCategory }) => (
    <TouchableOpacity
      style={styles.categoryCard}
      onPress={() => handleCategoryPress(item.name)}
      testID={`category-${item.id}`}
      accessibilityLabel={`${item.name} category`}
      accessibilityHint={`Browse ${item.name.toLowerCase()} services`}
      activeOpacity={0.8}>

      {/* Icon Container with Enhanced Styling */}
      <View style={[styles.categoryIconContainer, { backgroundColor: item.color || colors.sage400 || '#8FBC8F' }]}>
        <Ionicons
          name={(item.mobile_icon || item.icon || 'ellipse-outline') as any}
          size={28}
          color={colors.text?.onPrimary || '#FFFFFF'}
          style={styles.categoryIcon}
        />
      </View>

      {/* Category Information */}
      <View style={styles.categoryInfo}>
        <Text style={styles.categoryName}>{item.name}</Text>
      </View>
    </TouchableOpacity>
  );

  const renderProviderCard = ({ item }: { item: FeaturedProvider }) => (
    <TouchableOpacity
      style={styles.providerCard}
      onPress={() => handleProviderPress(item.id)}
      testID={`provider-${item.id}`}
      accessibilityLabel={`${item.business_name} provider`}
      accessibilityHint={`View details for ${item.business_name}`}
      activeOpacity={0.8}>

      {/* Featured Badge */}
      <View style={styles.featuredBadge}>
        <Text style={styles.featuredBadgeText}>Featured</Text>
      </View>

      {/* Provider Image/Avatar */}
      <View style={styles.providerImageContainer}>
        <StoreImage
          providerId={item.id}
          providerName={item.business_name}
          category={item.category_names?.[0] || 'general'}
          size="large"
          testID={`featured-provider-image-${item.id}`}
        />
      </View>

      <View style={styles.providerInfo}>
        <Text style={styles.providerName} numberOfLines={2}>
          {item.business_name}
        </Text>
        <Text style={styles.providerCategory} numberOfLines={1}>
          {item.category_names?.[0] || 'Beauty Services'}
        </Text>

        {/* Rating and Distance */}
        <View style={styles.providerMeta}>
          <View style={styles.ratingContainer}>
            <Ionicons
              name="star"
              size={14}
              color={colors.warning || '#FFC107'}
            />
            <Text style={styles.providerRating}>
              {item.rating ? item.rating.toFixed(1) : '0.0'}
            </Text>
            <Text style={styles.reviewCount}>
              ({item.review_count || 0})
            </Text>
          </View>
        </View>

        {/* Distance and Book Button */}
        <View style={styles.providerActions}>
          <Text style={styles.providerDistance}>
            {item.distance || '0.5 mi'}
          </Text>
          <TouchableOpacity style={styles.quickBookButton}>
            <Text style={styles.quickBookText}>Book</Text>
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaScreen
      backgroundColor={colors.background?.primary || '#FFFFFF'}
      style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.sage400 || '#8FBC8F']}
            tintColor={colors.sage400 || '#8FBC8F'}
          />
        }>
        {/* Header Section */}
        <Box style={styles.header}>
          <Box style={styles.headerTop}>
            <Text style={styles.appName}>Vierla</Text>
            <IconButton
              name="star"
              size="medium"
              variant="ghost"
              onPress={handleFavoritesPress}
              testID="favorites-button"
              accessibilityLabel="Open favorites"
              style={styles.profileButton}
              color="#FFFFFF"
            />
          </Box>
          <Text style={styles.greeting}>{greeting}!</Text>


        </Box>

        {/* Browse Services Section - Redesigned for Compliance */}
        <Box style={styles.browseServicesSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{safeT('home.browse_services', 'Browse Services')}</Text>
            <TouchableOpacity
              onPress={handleSeeAllCategories}
              style={styles.seeAllButton}
              testID="see-all-categories"
              accessibilityLabel="View all service categories"
              accessibilityRole="button">
              <Text style={styles.seeAllText}>{safeT('home.view_all', 'View All')}</Text>
              <Ionicons
                name="chevron-forward"
                size={16}
                color={colors.sage400 || '#8FBC8F'}
              />
            </TouchableOpacity>
          </View>

          {loading ? (
            <Box style={styles.loadingContainer}>
              <ActivityIndicator
                size="large"
                color={colors.sage400 || '#8FBC8F'}
              />
              <Text style={styles.loadingText}>Loading categories...</Text>
            </Box>
          ) : error ? (
            <Box style={styles.errorContainer}>
              <Text style={styles.errorText}>⚠️ {error}</Text>
              <TouchableOpacity
                style={styles.retryButton}
                onPress={fetchCategories}>
                <Text style={styles.retryButtonText}>Retry</Text>
              </TouchableOpacity>
            </Box>
          ) : (
            <FlatList
              data={categories}
              horizontal
              showsHorizontalScrollIndicator={false}
              keyExtractor={item => item.id}
              contentContainerStyle={styles.categoriesContainer}
              renderItem={renderCategoryCard}
              ItemSeparatorComponent={() => (
                <View style={styles.categorySeparator} />
              )}
            />
          )}
        </Box>

        {/* Featured Providers Section */}
        <Box style={styles.featuredSection}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionTitleContainer}>
              <Text style={styles.sectionTitle}>{safeT('home.featured_providers', 'Featured Providers')}</Text>
              <Text style={styles.sectionSubtitle}>Top-rated professionals near you</Text>
            </View>
            <TouchableOpacity
              onPress={handleViewAllProviders}
              style={styles.viewAllButton}
              testID="view-all-stores"
              accessibilityLabel="View all service providers"
              accessibilityRole="button">
              <Text style={styles.viewAllText}>{safeT('home.view_all', 'View All')}</Text>
            </TouchableOpacity>
          </View>

          {providersLoading ? (
            <Box style={styles.loadingContainer}>
              <ActivityIndicator
                size="large"
                color={colors.sage400 || '#8FBC8F'}
              />
              <Text style={styles.loadingText}>Loading providers...</Text>
            </Box>
          ) : providersError ? (
            <Box style={styles.errorContainer}>
              <Text style={styles.errorText}>⚠️ {providersError}</Text>
              <TouchableOpacity
                style={styles.retryButton}
                onPress={fetchFeaturedProviders}>
                <Text style={styles.retryButtonText}>Retry</Text>
              </TouchableOpacity>
            </Box>
          ) : featuredProviders.length > 0 ? (
            <FlatList
              data={featuredProviders}
              horizontal
              showsHorizontalScrollIndicator={false}
              keyExtractor={item => item.id}
              contentContainerStyle={styles.providersContainer}
              renderItem={renderProviderCard}
              ItemSeparatorComponent={() => (
                <View style={styles.providerSeparator} />
              )}
            />
          ) : (
            <Box style={styles.placeholderContainer}>
              <Text style={styles.placeholderText}>
                No featured providers available
              </Text>
            </Box>
          )}
        </Box>


      </ScrollView>
    </SafeAreaScreen>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background?.primary || '#FFFFFF',
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingBottom: 20,
    },
    header: {
      paddingHorizontal: getResponsiveSpacing(20),
      paddingTop: getResponsiveSpacing(16),
      paddingBottom: getResponsiveSpacing(24),
      backgroundColor: colors.sage400 || '#8FBC8F',
      borderTopLeftRadius: getResponsiveSpacing(24),
      borderTopRightRadius: getResponsiveSpacing(24),
      borderBottomLeftRadius: getResponsiveSpacing(24),
      borderBottomRightRadius: getResponsiveSpacing(24),
    },
    headerTop: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: getResponsiveSpacing(12),
    },
    appName: {
      fontSize: getResponsiveFontSize(24),
      fontWeight: '700',
      color: colors.text?.onPrimary || '#FFFFFF',
      letterSpacing: 1,
    },
    profileButton: {
      width: getMinimumTouchTarget(),
      height: getMinimumTouchTarget(),
      borderRadius: getResponsiveSpacing(20),
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    profileIcon: {
      fontSize: getResponsiveFontSize(20),
      color: colors.text?.onPrimary || '#FFFFFF',
    },
    greeting: {
      fontSize: getResponsiveFontSize(28),
      fontWeight: '600',
      color: colors.text?.onPrimary || '#FFFFFF',
      marginBottom: getResponsiveSpacing(4),
    },
    subtitle: {
      fontSize: getResponsiveFontSize(16),
      color: colors.text?.onPrimary
        ? `${colors.text.onPrimary}E6`
        : 'rgba(255, 255, 255, 0.9)',
      fontWeight: '400',
      marginBottom: getResponsiveSpacing(20),
    },

    browseServicesSection: {
      paddingHorizontal: getResponsiveSpacing(20),
      paddingTop: getResponsiveSpacing(12), // Reduced from 20 for better responsiveness
      paddingBottom: getResponsiveSpacing(32), // Increased from 20 to prevent shadow clipping
    },
    featuredSection: {
      paddingHorizontal: getResponsiveSpacing(20),
      paddingVertical: getResponsiveSpacing(20),
    },
    sectionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: getResponsiveSpacing(16),
    },
    sectionTitleContainer: {
      flex: 1,
    },
    sectionTitle: {
      fontSize: getResponsiveFontSize(20),
      fontWeight: '700',
      color: '#333333',
    },
    sectionSubtitle: {
      fontSize: getResponsiveFontSize(12),
      color: '#666666',
      marginTop: getResponsiveSpacing(2),
      lineHeight: getResponsiveFontSize(16),
    },
    seeAllButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: getResponsiveSpacing(8),
      paddingVertical: getResponsiveSpacing(4),
      borderRadius: getResponsiveSpacing(8),
      backgroundColor: 'transparent',
    },
    seeAllText: {
      fontSize: getResponsiveFontSize(14),
      fontWeight: '500',
      color: colors.sage400 || '#8FBC8F',
      marginRight: getResponsiveSpacing(4),
    },
    viewAllButton: {
      paddingHorizontal: getResponsiveSpacing(12),
      paddingVertical: getResponsiveSpacing(6),
      borderRadius: getResponsiveSpacing(8),
      backgroundColor: colors.sage100 || 'rgba(143, 188, 143, 0.1)',
    },
    viewAllText: {
      fontSize: getResponsiveFontSize(14),
      fontWeight: '600',
      color: colors.sage400 || '#8FBC8F',
    },
    categoriesContainer: {
      paddingLeft: 0,
    },
    categoryCard: {
      width: getResponsiveSpacing(120),
      height: getResponsiveSpacing(140),
      backgroundColor: '#FFFFFF',
      borderRadius: getResponsiveSpacing(16),
      padding: getResponsiveSpacing(16),
      marginRight: getResponsiveSpacing(12),
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.08,
      shadowRadius: 4,
      elevation: 3,
      borderWidth: 1,
      borderColor: colors.sage100 || 'rgba(143, 188, 143, 0.1)',
    },
    categoryIconContainer: {
      width: getResponsiveSpacing(56),
      height: getResponsiveSpacing(56),
      borderRadius: getResponsiveSpacing(28),
      justifyContent: 'center',
      alignItems: 'center',
      alignSelf: 'center', // Center the icon container within the card
      marginBottom: getResponsiveSpacing(12),
    },
    categoryIcon: {
      // Icon styling handled by Ionicons component
    },
    categoryInfo: {
      alignItems: 'center',
    },
    categoryName: {
      fontSize: getResponsiveFontSize(14),
      fontWeight: '600',
      color: colors.text?.primary || '#333333',
      textAlign: 'center',
      marginBottom: getResponsiveSpacing(4),
    },

    placeholderContainer: {
      backgroundColor: '#F5F5F5',
      borderRadius: 12,
      padding: 20,
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: 100,
    },
    placeholderText: {
      fontSize: 14,
      color: '#666666',
      textAlign: 'center',
    },
    loadingContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 40,
    },
    loadingText: {
      fontSize: 14,
      color: '#666666',
      marginTop: 12,
    },
    errorContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 40,
      backgroundColor: '#FFF5F5',
      borderRadius: 12,
      marginHorizontal: 4,
    },
    errorText: {
      fontSize: 14,
      color: '#E53E3E',
      textAlign: 'center',
      marginBottom: 16,
    },
    retryButton: {
      backgroundColor: colors.sage400 || '#8FBC8F',
      paddingHorizontal: 20,
      paddingVertical: 10,
      borderRadius: 8,
    },
    retryButtonText: {
      color: '#FFFFFF',
      fontSize: 14,
      fontWeight: '600',
    },
    providersContainer: {
      paddingLeft: 0,
    },
    providerCard: {
      width: getResponsiveSpacing(200),
      backgroundColor: '#FFFFFF',
      borderRadius: getResponsiveSpacing(20),
      padding: getResponsiveSpacing(20),
      marginRight: getResponsiveSpacing(16),
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.08,
      shadowRadius: 4,
      elevation: 3,
      borderWidth: 1,
      borderColor: colors.sage100 || 'rgba(143, 188, 143, 0.1)',
      position: 'relative',
    },
    providerSeparator: {
      width: 12,
    },
    providerImageContainer: {
      position: 'relative',
      alignItems: 'center',
      marginBottom: 8,
    },
    providerImagePlaceholder: {
      fontSize: 32,
      textAlign: 'center',
    },
    featuredBadge: {
      position: 'absolute',
      top: getResponsiveSpacing(8),
      right: getResponsiveSpacing(8),
      backgroundColor: colors.warning || '#FFC107',
      borderRadius: getResponsiveSpacing(12),
      paddingHorizontal: getResponsiveSpacing(8),
      paddingVertical: getResponsiveSpacing(4),
      zIndex: 10,
    },
    featuredBadgeText: {
      fontSize: getResponsiveFontSize(10),
      fontWeight: '600',
      color: '#FFFFFF',
    },
    verifiedBadge: {
      position: 'absolute',
      top: getResponsiveSpacing(-5),
      right: getResponsiveSpacing(10),
      backgroundColor: '#4CAF50',
      borderRadius: getResponsiveSpacing(10),
      width: getResponsiveSpacing(20),
      height: getResponsiveSpacing(20),
      justifyContent: 'center',
      alignItems: 'center',
    },
    verifiedIcon: {
      color: '#FFFFFF',
      fontSize: 12,
      fontWeight: 'bold',
    },
    providerInfo: {
      flex: 1,
    },
    providerName: {
      fontSize: getResponsiveFontSize(16),
      fontWeight: '600',
      color: colors.text?.primary || '#333333',
      marginBottom: getResponsiveSpacing(4),
    },
    providerCategory: {
      fontSize: getResponsiveFontSize(14),
      color: colors.text?.secondary || '#666666',
      marginBottom: getResponsiveSpacing(8),
    },
    providerMeta: {
      marginBottom: getResponsiveSpacing(12),
    },
    ratingContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    providerRating: {
      fontSize: getResponsiveFontSize(14),
      fontWeight: '600',
      color: colors.text?.primary || '#333333',
      marginLeft: getResponsiveSpacing(4),
    },
    reviewCount: {
      fontSize: getResponsiveFontSize(14),
      color: colors.text?.secondary || '#666666',
      marginLeft: getResponsiveSpacing(2),
    },
    providerActions: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    providerDistance: {
      fontSize: getResponsiveFontSize(14),
      color: colors.text?.secondary || '#666666',
      fontWeight: '500',
    },
    quickBookButton: {
      backgroundColor: colors.sage400 || '#8FBC8F',
      borderRadius: getResponsiveSpacing(16),
      paddingHorizontal: getResponsiveSpacing(12),
      paddingVertical: getResponsiveSpacing(6),
      alignItems: 'center',
      justifyContent: 'center',
    },
    quickBookText: {
      fontSize: getResponsiveFontSize(12),
      fontWeight: '600',
      color: '#FFFFFF',
    },
    categorySeparator: {
      width: getResponsiveSpacing(8),
    },
  });

export default CustomerHomeScreen;
