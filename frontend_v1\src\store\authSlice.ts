/**
 * Auth Slice - Zustand Store for Authentication State
 *
 * Component Contract:
 * - Manages authentication state for dual-role system (customer/provider)
 * - Handles login, registration, and logout actions
 * - Provides computed properties for authentication status
 * - Maintains error state for UI feedback
 * - Follows immutable state update patterns
 * - Supports state persistence and hydration
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

export type UserRole = 'customer' | 'provider';

export type AuthStatus = 'idle' | 'loading' | 'success' | 'error';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  profileImage?: string;
  phoneNumber?: string;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AuthState {
  // State
  authToken: string | null;
  refreshToken: string | null;
  user: User | null;
  userRole: UserRole | null;
  status: AuthStatus;
  error: string | null;
  tokenExpiresAt: number | null;

  // Computed properties
  isAuthenticated: boolean;

  // Actions
  loginStart: () => void;
  loginSuccess: (token: string, refreshToken: string, user: User) => void;
  loginFailure: (error: string) => void;

  registerStart: () => void;
  registerSuccess: (token: string, refreshToken: string, user: User) => void;
  registerFailure: (error: string) => void;

  updateProfile: (user: Partial<User>) => void;
  updateUser: (user: User) => void;
  updateTokens: (token: string, refreshToken?: string) => void;

  logout: () => void;
  reset: () => void;
  checkAuthStatus: () => Promise<void>;
  validateToken: () => Promise<boolean>;
}

const initialState = {
  authToken: null,
  refreshToken: null,
  user: null,
  userRole: null,
  status: 'idle' as AuthStatus,
  error: null,
  tokenExpiresAt: null,
  isAuthenticated: false,
};

// Create a safe auth store with error handling
const createAuthStore = () => {
  try {
    return create<AuthState>()(
      devtools(
        persist(
          (set, get) => ({
            // Initial state
            ...initialState,

            // Computed properties
            isAuthenticated: false,

            // Login actions
            loginStart: () =>
              set(
                state => ({
                  ...state,
                  status: 'loading',
                  error: null,
                }),
                false,
                'auth/loginStart',
              ),

            loginSuccess: (token: string, refreshToken: string, user: User) =>
              set(
                state => ({
                  ...state,
                  authToken: token,
                  refreshToken,
                  user,
                  userRole: user.role,
                  status: 'success',
                  error: null,
                  isAuthenticated: true,
                  tokenExpiresAt: Date.now() + 30 * 60 * 1000, // 30 minutes from now
                }),
                false,
                'auth/loginSuccess',
              ),

            loginFailure: (error: string) =>
              set(
                state => ({
                  ...state,
                  authToken: null,
                  userRole: null,
                  status: 'error',
                  error,
                  isAuthenticated: false,
                }),
                false,
                'auth/loginFailure',
              ),

            // Registration actions
            registerStart: () =>
              set(
                state => ({
                  ...state,
                  status: 'loading',
                  error: null,
                }),
                false,
                'auth/registerStart',
              ),

            registerSuccess: (
              token: string,
              refreshToken: string,
              user: User,
            ) =>
              set(
                state => ({
                  ...state,
                  authToken: token,
                  refreshToken,
                  user,
                  userRole: user.role,
                  status: 'success',
                  error: null,
                  isAuthenticated: true,
                  tokenExpiresAt: Date.now() + 30 * 60 * 1000, // 30 minutes from now
                }),
                false,
                'auth/registerSuccess',
              ),

            registerFailure: (error: string) =>
              set(
                state => ({
                  ...state,
                  authToken: null,
                  refreshToken: null,
                  user: null,
                  userRole: null,
                  status: 'error',
                  error,
                  isAuthenticated: false,
                  tokenExpiresAt: null,
                }),
                false,
                'auth/registerFailure',
              ),

            // Profile management
            updateProfile: (userUpdates: Partial<User>) =>
              set(
                state => ({
                  ...state,
                  user: state.user ? { ...state.user, ...userUpdates } : null,
                }),
                false,
                'auth/updateProfile',
              ),

            // Token management
            updateTokens: (token: string, refreshToken?: string) =>
              set(
                state => ({
                  ...state,
                  authToken: token,
                  refreshToken: refreshToken || state.refreshToken,
                  tokenExpiresAt: Date.now() + 30 * 60 * 1000, // 30 minutes from now
                }),
                false,
                'auth/updateTokens',
              ),

            // Logout action
            logout: async () => {
              // Clear onboarding completion status to ensure fresh initialization experience
              try {
                await AsyncStorage.removeItem('@vierla_onboarding_completed');
                console.log('🔄 Onboarding status cleared on logout');
              } catch (error) {
                console.warn('Error clearing onboarding status:', error);
              }

              set(
                () => ({
                  ...initialState,
                }),
                false,
                'auth/logout',
              );
            },

            // Reset action (for testing)
            reset: () =>
              set(
                () => ({
                  ...initialState,
                }),
                false,
                'auth/reset',
              ),

            // Check authentication status (for initialization)
            checkAuthStatus: async () => {
              try {
                const currentState = get();

                // If already authenticated and token is not expired, no need to check again
                if (
                  currentState.isAuthenticated &&
                  currentState.authToken &&
                  currentState.tokenExpiresAt
                ) {
                  if (Date.now() < currentState.tokenExpiresAt) {
                    return;
                  }
                }

                // Try to load tokens from AsyncStorage
                const storedToken = await AsyncStorage.getItem('auth_token');
                const storedRefreshToken =
                  await AsyncStorage.getItem('refresh_token');
                const storedUser = await AsyncStorage.getItem('auth_user');

                if (storedToken && storedUser) {
                  try {
                    const user = JSON.parse(storedUser);
                    set(
                      state => ({
                        ...state,
                        authToken: storedToken,
                        refreshToken: storedRefreshToken,
                        user,
                        userRole: user.role,
                        isAuthenticated: true,
                        status: 'success',
                        error: null,
                      }),
                      false,
                      'auth/checkAuthStatus',
                    );
                  } catch (parseError) {
                    console.error(
                      'Failed to parse stored user data:',
                      parseError,
                    );
                    await AsyncStorage.multiRemove([
                      'auth_token',
                      'refresh_token',
                      'auth_user',
                    ]);
                    set(() => initialState, false, 'auth/checkAuthStatusError');
                  }
                } else {
                  set(() => initialState, false, 'auth/checkAuthStatus');
                }
              } catch (error) {
                console.error('Auth status check failed:', error);
                set(() => initialState, false, 'auth/checkAuthStatusError');
              }
            },

            // Update user data
            updateUser: (userData: User) =>
              set(
                state => ({
                  ...state,
                  user: userData,
                }),
                false,
                'auth/updateUser',
              ),

            // Validate current token
            validateToken: async (): Promise<boolean> => {
              try {
                const currentState = get();

                if (!currentState.authToken) {
                  return false;
                }

                // Check if token is expired
                if (
                  currentState.tokenExpiresAt &&
                  Date.now() >= currentState.tokenExpiresAt
                ) {
                  // Try to refresh token
                  if (currentState.refreshToken) {
                    try {
                      // Import authService dynamically to avoid circular dependencies
                      const { authService } = await import(
                        '../services/authService'
                      );
                      const response = await authService.refreshToken(
                        currentState.refreshToken,
                      );

                      // Update tokens
                      set(
                        state => ({
                          ...state,
                          authToken: response.access,
                          tokenExpiresAt: Date.now() + 30 * 60 * 1000,
                        }),
                        false,
                        'auth/tokenRefreshed',
                      );

                      // Update AsyncStorage
                      await AsyncStorage.setItem('auth_token', response.access);

                      return true;
                    } catch (refreshError) {
                      console.error('Token refresh failed:', refreshError);
                      set(() => initialState, false, 'auth/tokenExpired');
                      await AsyncStorage.multiRemove([
                        'auth_token',
                        'refresh_token',
                        'auth_user',
                      ]);
                      return false;
                    }
                  } else {
                    set(() => initialState, false, 'auth/tokenExpired');
                    await AsyncStorage.multiRemove([
                      'auth_token',
                      'refresh_token',
                      'auth_user',
                    ]);
                    return false;
                  }
                }

                return true;
              } catch (error) {
                console.error('Token validation failed:', error);
                return false;
              }
            },
          }),
          {
            name: 'auth-store',
            storage: {
              getItem: async (name: string) => {
                try {
                  const value = await AsyncStorage.getItem(name);
                  if (value) {
                    const parsed = JSON.parse(value);
                    // Check if the stored data has the old loginSuccess signature
                    // If so, clear it to prevent errors
                    if (
                      parsed &&
                      typeof parsed === 'object' &&
                      'loginSuccess' in parsed
                    ) {
                      console.log(
                        '🔄 Clearing old auth store data due to structure change',
                      );
                      await AsyncStorage.removeItem(name);
                      return null;
                    }
                    return parsed;
                  }
                  return null;
                } catch (error) {
                  console.error('Failed to load auth state:', error);
                  // Clear corrupted data
                  try {
                    await AsyncStorage.removeItem(name);
                  } catch {}
                  return null;
                }
              },
              setItem: async (name: string, value: any) => {
                try {
                  await AsyncStorage.setItem(name, JSON.stringify(value));
                } catch (error) {
                  console.error('Failed to save auth state:', error);
                }
              },
              removeItem: async (name: string) => {
                try {
                  await AsyncStorage.removeItem(name);
                } catch (error) {
                  console.error('Failed to remove auth state:', error);
                }
              },
            },
            partialize: state => ({
              authToken: state.authToken,
              refreshToken: state.refreshToken,
              user: state.user,
              userRole: state.userRole,
              isAuthenticated: state.isAuthenticated,
              tokenExpiresAt: state.tokenExpiresAt,
              // Add version to track store structure changes
              _version: '2.0.0',
            }),
            // Add migration logic for store structure changes
            migrate: (persistedState: any, version: number) => {
              // If no version or old version, reset the store
              if (
                !persistedState ||
                !persistedState._version ||
                persistedState._version !== '2.0.0'
              ) {
                console.log(
                  '🔄 Migrating auth store to new version, clearing old data',
                );
                return initialState;
              }
              return persistedState;
            },
            version: 1,
          },
          {
            name: 'auth-store',
          },
        ),
      ),
    );
  } catch (error) {
    console.error('Failed to create auth store:', error);
    // Return a minimal fallback store
    return create<AuthState>()(() => ({
      ...initialState,
      isAuthenticated: false,
      loginStart: () => {},
      loginSuccess: () => {},
      loginFailure: () => {},
      registerStart: () => {},
      registerSuccess: () => {},
      registerFailure: () => {},
      updateProfile: () => {},
      updateTokens: () => {},
      logout: () => {},
      reset: () => {},
      checkAuthStatus: async () => {},
      validateToken: async () => false,
    }));
  }
};

export const useAuthStore = createAuthStore();

// Safe hook for accessing auth store with error handling
export const useSafeAuthStore = () => {
  try {
    const store = useAuthStore();
    // Ensure all required properties exist
    if (!store || typeof store !== 'object') {
      console.warn('Auth store is not properly initialized');
      return {
        ...initialState,
        isAuthenticated: false,
        loginStart: () => {},
        loginSuccess: () => {},
        loginFailure: () => {},
        registerStart: () => {},
        registerSuccess: () => {},
        registerFailure: () => {},
        updateProfile: () => {},
        updateTokens: () => {},
        logout: () => {},
        reset: () => {},
        checkAuthStatus: async () => {},
        validateToken: async () => false,
      };
    }
    return store;
  } catch (error) {
    console.error('Error accessing auth store:', error);
    return {
      ...initialState,
      isAuthenticated: false,
      loginStart: () => {},
      loginSuccess: () => {},
      loginFailure: () => {},
      registerStart: () => {},
      registerSuccess: () => {},
      registerFailure: () => {},
      updateProfile: () => {},
      updateTokens: () => {},
      logout: () => {},
      reset: () => {},
      checkAuthStatus: async () => {},
      validateToken: async () => false,
    };
  }
};
