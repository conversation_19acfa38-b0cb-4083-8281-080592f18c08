473ee44116e9d7dcbcdf5fa5e7d4c9c8
_getJestObj().mock("../../../services/performanceMonitor");
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _reactNative = require("@testing-library/react-native");
var _react = _interopRequireDefault(require("react"));
var _reactNative2 = require("react-native");
var _performanceMonitor = require("../../../services/performanceMonitor");
var _ErrorBoundary = require("../ErrorBoundary");
var _jsxRuntime = require("react/jsx-runtime");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var mockPerformanceMonitor = _performanceMonitor.performanceMonitor;
var ErrorComponent = function ErrorComponent() {
  throw new Error('Test error');
  return null;
};
var ButtonThatThrows = function ButtonThatThrows(_ref) {
  var onError = _ref.onError;
  var _React$useState = _react.default.useState(false),
    _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),
    shouldThrow = _React$useState2[0],
    setShouldThrow = _React$useState2[1];
  if (shouldThrow) {
    try {
      throw new Error('Button error');
    } catch (error) {
      if (onError) onError();
      throw error;
    }
  }
  return (0, _jsxRuntime.jsx)(_reactNative2.Button, {
    title: "Throw Error",
    onPress: function onPress() {
      return setShouldThrow(true);
    },
    testID: "throw-button"
  });
};
var WorkingComponent = function WorkingComponent() {
  return (0, _jsxRuntime.jsx)(_reactNative2.View, {
    testID: "working-component",
    children: (0, _jsxRuntime.jsx)(_reactNative2.Text, {
      children: "Working Component"
    })
  });
};
describe('ErrorBoundary', function () {
  beforeEach(function () {
    jest.clearAllMocks();
    jest.spyOn(console, 'error').mockImplementation(function () {});
  });
  afterEach(function () {
    jest.restoreAllMocks();
  });
  describe('Error Handling', function () {
    it('catches errors in child components', function () {
      var _render = (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
          children: (0, _jsxRuntime.jsx)(ErrorComponent, {})
        })),
        getByText = _render.getByText;
      expect(getByText('Something went wrong')).toBeTruthy();
    });
    it('renders children normally when no errors occur', function () {
      var _render2 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
          children: (0, _jsxRuntime.jsx)(WorkingComponent, {})
        })),
        getByTestId = _render2.getByTestId,
        getByText = _render2.getByText;
      expect(getByTestId('working-component')).toBeTruthy();
      expect(getByText('Working Component')).toBeTruthy();
    });
    it('calls onError callback when an error occurs', function () {
      var onError = jest.fn();
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
        onError: onError,
        children: (0, _jsxRuntime.jsx)(ErrorComponent, {})
      }));
      expect(onError).toHaveBeenCalledWith(expect.objectContaining({
        message: 'Test error'
      }));
    });
    it('tracks errors with performance monitor', function () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
        children: (0, _jsxRuntime.jsx)(ErrorComponent, {})
      }));
      expect(mockPerformanceMonitor.trackUserInteraction).toHaveBeenCalledWith('error_boundary_catch', 0, expect.objectContaining({
        error: 'Test error',
        componentStack: expect.any(String)
      }));
    });
  });
  describe('Fallback UI', function () {
    it('renders default fallback UI', function () {
      var _render3 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
          children: (0, _jsxRuntime.jsx)(ErrorComponent, {})
        })),
        getByText = _render3.getByText;
      expect(getByText('Something went wrong')).toBeTruthy();
      expect(getByText(/We're sorry, but an error occurred/)).toBeTruthy();
      expect(getByText('Try Again')).toBeTruthy();
    });
    it('renders custom fallback UI when provided', function () {
      var CustomFallback = function CustomFallback(_ref2) {
        var error = _ref2.error,
          retry = _ref2.retry;
        return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          testID: "custom-fallback",
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.Text, {
            children: ["Custom Error: ", error.message]
          }), (0, _jsxRuntime.jsx)(_reactNative2.Button, {
            title: "Custom Retry",
            onPress: retry,
            testID: "custom-retry"
          })]
        });
      };
      var _render4 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
          fallback: function fallback(error, retry) {
            return (0, _jsxRuntime.jsx)(CustomFallback, {
              error: error,
              retry: retry
            });
          },
          children: (0, _jsxRuntime.jsx)(ErrorComponent, {})
        })),
        getByTestId = _render4.getByTestId,
        getByText = _render4.getByText;
      expect(getByTestId('custom-fallback')).toBeTruthy();
      expect(getByText('Custom Error: Test error')).toBeTruthy();
      expect(getByText('Custom Retry')).toBeTruthy();
    });
    it('shows technical details in development mode', function () {
      global.__DEV__ = true;
      var _render5 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
          children: (0, _jsxRuntime.jsx)(ErrorComponent, {})
        })),
        getByText = _render5.getByText;
      expect(getByText(/Technical Details/)).toBeTruthy();
      expect(getByText('Test error')).toBeTruthy();
      global.__DEV__ = false;
    });
  });
  describe('Reset Functionality', function () {
    it('resets error state when retry button is pressed', (0, _asyncToGenerator2.default)(function* () {
      var TestComponent = function TestComponent(_ref4) {
        var resetKey = _ref4.resetKey;
        if (resetKey === 1) {
          throw new Error('Initial error');
        }
        return (0, _jsxRuntime.jsx)(_reactNative2.Text, {
          children: "Recovered Component"
        });
      };
      var _render6 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
          resetKeys: [1],
          resetOnPropsChange: true,
          children: (0, _jsxRuntime.jsx)(TestComponent, {
            resetKey: 1
          })
        })),
        getByText = _render6.getByText,
        rerender = _render6.rerender;
      expect(getByText('Something went wrong')).toBeTruthy();
      _reactNative.fireEvent.press(getByText('Try Again'));
      rerender((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
        resetKeys: [2],
        resetOnPropsChange: true,
        children: (0, _jsxRuntime.jsx)(TestComponent, {
          resetKey: 2
        })
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(getByText('Recovered Component')).toBeTruthy();
      });
    }));
    it('respects maxRetries limit', (0, _asyncToGenerator2.default)(function* () {
      var maxRetries = 2;
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
        maxRetries: maxRetries,
        children: (0, _jsxRuntime.jsx)(ErrorComponent, {})
      }));
      for (var i = 0; i < maxRetries; i++) {
        _reactNative.fireEvent.press(screen.getByText('Try Again'));
      }
      var retryButton = screen.getByText('Try Again');
      expect(retryButton.props.disabled).toBe(true);
    }));
    it('resets on props change when resetOnPropsChange is true', (0, _asyncToGenerator2.default)(function* () {
      var TestWrapper = function TestWrapper(_ref7) {
        var children = _ref7.children;
        var _React$useState3 = _react.default.useState(1),
          _React$useState4 = (0, _slicedToArray2.default)(_React$useState3, 2),
          key = _React$useState4[0],
          setKey = _React$useState4[1];
        return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          children: [(0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
            resetOnPropsChange: true,
            children: children
          }), (0, _jsxRuntime.jsx)(_reactNative2.Button, {
            title: "Change Props",
            onPress: function onPress() {
              return setKey(function (k) {
                return k + 1;
              });
            },
            testID: "change-props"
          })]
        });
      };
      var _render7 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(ErrorComponent, {})
        })),
        rerender = _render7.rerender;
      expect(screen.getByText('Something went wrong')).toBeTruthy();
      _reactNative.fireEvent.press(screen.getByTestID('change-props'));
      expect(screen.getByText('Something went wrong')).toBeTruthy();
    }));
  });
  describe('Dynamic Error Handling', function () {
    it('catches runtime errors from user interactions', (0, _asyncToGenerator2.default)(function* () {
      var onError = jest.fn();
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
        onError: onError,
        children: (0, _jsxRuntime.jsx)(ButtonThatThrows, {
          onError: onError
        })
      }));
      expect(screen.getByTestID('throw-button')).toBeTruthy();
      _reactNative.fireEvent.press(screen.getByTestID('throw-button'));
      yield (0, _reactNative.waitFor)(function () {
        expect(screen.getByText('Something went wrong')).toBeTruthy();
      });
      expect(onError).toHaveBeenCalledWith(expect.objectContaining({
        message: 'Button error'
      }));
    }));
    it('handles nested error boundaries correctly', function () {
      var OuterFallback = function OuterFallback() {
        return (0, _jsxRuntime.jsx)(_reactNative2.Text, {
          children: "Outer Error"
        });
      };
      var InnerFallback = function InnerFallback() {
        return (0, _jsxRuntime.jsx)(_reactNative2.Text, {
          children: "Inner Error"
        });
      };
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
        fallback: function fallback() {
          return (0, _jsxRuntime.jsx)(OuterFallback, {});
        },
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          children: [(0, _jsxRuntime.jsx)(_reactNative2.Text, {
            children: "Outer Content"
          }), (0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
            fallback: function fallback() {
              return (0, _jsxRuntime.jsx)(InnerFallback, {});
            },
            children: (0, _jsxRuntime.jsx)(ErrorComponent, {})
          })]
        })
      }));
      expect(screen.getByText('Inner Error')).toBeTruthy();
      expect(screen.getByText('Outer Content')).toBeTruthy();
      expect(screen.queryByText('Outer Error')).toBeNull();
    });
  });
  describe('Accessibility', function () {
    it('announces errors to screen readers', function () {
      _getJestObj().mock('react-native', function () {
        var rn = jest.requireActual('react-native');
        return Object.assign({}, rn, {
          AccessibilityInfo: Object.assign({}, rn.AccessibilityInfo, {
            announceForAccessibility: mockAnnounce
          })
        });
      });
      var mockAnnounce = jest.fn();
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
        children: (0, _jsxRuntime.jsx)(ErrorComponent, {})
      }));
      expect(mockAnnounce).toHaveBeenCalledWith(expect.stringContaining('error occurred'));
    });
    it('provides proper accessibility props on fallback UI', function () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
        children: (0, _jsxRuntime.jsx)(ErrorComponent, {})
      }));
      var errorContainer = screen.getByTestID('error-boundary-container');
      expect(errorContainer.props.accessibilityRole).toBe('alert');
      var retryButton = screen.getByText('Try Again');
      expect(retryButton.props.accessibilityRole).toBe('button');
      expect(retryButton.props.accessibilityLabel).toBeTruthy();
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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