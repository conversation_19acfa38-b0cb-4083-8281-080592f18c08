1fe9944799d0939c6c9dac6c3ae28c59
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.usePaymentStore = void 0;
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _asyncStorage = _interopRequireDefault(require("@react-native-async-storage/async-storage"));
var _zustand = require("zustand");
var _middleware = require("zustand/middleware");
var _paymentService = require("../services/paymentService");
var initialState = {
  paymentMethods: [],
  defaultPaymentMethod: null,
  transactions: [],
  recentTransactions: [],
  currentPaymentIntent: null,
  isProcessingPayment: false,
  paymentError: null,
  paymentSummary: null,
  isLoading: false,
  isAddingPaymentMethod: false,
  error: null
};
var usePaymentStore = exports.usePaymentStore = (0, _zustand.create)()((0, _middleware.devtools)((0, _middleware.persist)(function (set, get) {
  return Object.assign({}, initialState, {
    loadPaymentMethods: function () {
      var _loadPaymentMethods = (0, _asyncToGenerator2.default)(function* (userId) {
        set(function (state) {
          return Object.assign({}, state, {
            isLoading: true,
            error: null
          });
        }, false, 'payment/loadPaymentMethods/start');
        try {
          console.log('💳 PaymentStore: Loading payment methods for user:', userId);
          var paymentMethods = yield _paymentService.paymentService.getPaymentMethods();
          var transformedMethods = paymentMethods.map(function (pm) {
            return {
              id: pm.id,
              type: pm.type,
              brand: pm.brand || 'unknown',
              last4: pm.last4 || '0000',
              expiryMonth: pm.exp_month || 12,
              expiryYear: pm.exp_year || 2025,
              holderName: pm.cardholder_name || 'Unknown',
              isDefault: pm.is_default || false,
              isVerified: pm.is_verified || false,
              billingAddress: pm.billing_address || {
                firstName: '',
                lastName: '',
                addressLine1: '',
                city: '',
                state: '',
                postalCode: '',
                country: 'CA'
              },
              createdAt: pm.created_at || new Date().toISOString(),
              updatedAt: pm.updated_at || new Date().toISOString()
            };
          });
          set(function (state) {
            return Object.assign({}, state, {
              paymentMethods: transformedMethods,
              defaultPaymentMethod: transformedMethods.find(function (pm) {
                return pm.isDefault;
              }) || null,
              isLoading: false,
              error: null
            });
          }, false, 'payment/loadPaymentMethods/success');
          console.log('✅ PaymentStore: Payment methods loaded:', transformedMethods.length);
        } catch (error) {
          set(function (state) {
            return Object.assign({}, state, {
              isLoading: false,
              error: error instanceof Error ? error.message : 'Failed to load payment methods'
            });
          }, false, 'payment/loadPaymentMethods/error');
        }
      });
      function loadPaymentMethods(_x) {
        return _loadPaymentMethods.apply(this, arguments);
      }
      return loadPaymentMethods;
    }(),
    addPaymentMethod: function () {
      var _addPaymentMethod = (0, _asyncToGenerator2.default)(function* (paymentMethodData) {
        set(function (state) {
          return Object.assign({}, state, {
            isAddingPaymentMethod: true,
            error: null
          });
        }, false, 'payment/addPaymentMethod/start');
        try {
          yield new Promise(function (resolve) {
            return setTimeout(resolve, 1500);
          });
          var newPaymentMethod = {
            id: `pm_${Date.now()}`,
            type: paymentMethodData.type || 'credit_card',
            brand: paymentMethodData.brand || 'visa',
            last4: paymentMethodData.last4 || '0000',
            expiryMonth: paymentMethodData.expiryMonth || 12,
            expiryYear: paymentMethodData.expiryYear || 2025,
            holderName: paymentMethodData.holderName || '',
            isDefault: paymentMethodData.isDefault || false,
            isVerified: true,
            billingAddress: paymentMethodData.billingAddress,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };
          set(function (state) {
            return Object.assign({}, state, {
              paymentMethods: [].concat((0, _toConsumableArray2.default)(state.paymentMethods), [newPaymentMethod]),
              defaultPaymentMethod: newPaymentMethod.isDefault ? newPaymentMethod : state.defaultPaymentMethod,
              isAddingPaymentMethod: false,
              error: null
            });
          }, false, 'payment/addPaymentMethod/success');
          return newPaymentMethod;
        } catch (error) {
          set(function (state) {
            return Object.assign({}, state, {
              isAddingPaymentMethod: false,
              error: error instanceof Error ? error.message : 'Failed to add payment method'
            });
          }, false, 'payment/addPaymentMethod/error');
          throw error;
        }
      });
      function addPaymentMethod(_x2) {
        return _addPaymentMethod.apply(this, arguments);
      }
      return addPaymentMethod;
    }(),
    updatePaymentMethod: function () {
      var _updatePaymentMethod = (0, _asyncToGenerator2.default)(function* (paymentMethodId, updates) {
        set(function (state) {
          return Object.assign({}, state, {
            isLoading: true,
            error: null
          });
        }, false, 'payment/updatePaymentMethod/start');
        try {
          yield new Promise(function (resolve) {
            return setTimeout(resolve, 500);
          });
          set(function (state) {
            return Object.assign({}, state, {
              paymentMethods: state.paymentMethods.map(function (pm) {
                return pm.id === paymentMethodId ? Object.assign({}, pm, updates, {
                  updatedAt: new Date().toISOString()
                }) : pm;
              }),
              isLoading: false,
              error: null
            });
          }, false, 'payment/updatePaymentMethod/success');
        } catch (error) {
          set(function (state) {
            return Object.assign({}, state, {
              isLoading: false,
              error: error instanceof Error ? error.message : 'Failed to update payment method'
            });
          }, false, 'payment/updatePaymentMethod/error');
        }
      });
      function updatePaymentMethod(_x3, _x4) {
        return _updatePaymentMethod.apply(this, arguments);
      }
      return updatePaymentMethod;
    }(),
    deletePaymentMethod: function () {
      var _deletePaymentMethod = (0, _asyncToGenerator2.default)(function* (paymentMethodId) {
        set(function (state) {
          return Object.assign({}, state, {
            isLoading: true,
            error: null
          });
        }, false, 'payment/deletePaymentMethod/start');
        try {
          yield new Promise(function (resolve) {
            return setTimeout(resolve, 500);
          });
          set(function (state) {
            var _state$defaultPayment;
            return Object.assign({}, state, {
              paymentMethods: state.paymentMethods.filter(function (pm) {
                return pm.id !== paymentMethodId;
              }),
              defaultPaymentMethod: ((_state$defaultPayment = state.defaultPaymentMethod) == null ? void 0 : _state$defaultPayment.id) === paymentMethodId ? null : state.defaultPaymentMethod,
              isLoading: false,
              error: null
            });
          }, false, 'payment/deletePaymentMethod/success');
        } catch (error) {
          set(function (state) {
            return Object.assign({}, state, {
              isLoading: false,
              error: error instanceof Error ? error.message : 'Failed to delete payment method'
            });
          }, false, 'payment/deletePaymentMethod/error');
        }
      });
      function deletePaymentMethod(_x5) {
        return _deletePaymentMethod.apply(this, arguments);
      }
      return deletePaymentMethod;
    }(),
    setDefaultPaymentMethod: function () {
      var _setDefaultPaymentMethod = (0, _asyncToGenerator2.default)(function* (paymentMethodId) {
        set(function (state) {
          return Object.assign({}, state, {
            isLoading: true,
            error: null
          });
        }, false, 'payment/setDefaultPaymentMethod/start');
        try {
          yield new Promise(function (resolve) {
            return setTimeout(resolve, 300);
          });
          set(function (state) {
            return Object.assign({}, state, {
              paymentMethods: state.paymentMethods.map(function (pm) {
                return Object.assign({}, pm, {
                  isDefault: pm.id === paymentMethodId,
                  updatedAt: new Date().toISOString()
                });
              }),
              defaultPaymentMethod: state.paymentMethods.find(function (pm) {
                return pm.id === paymentMethodId;
              }) || null,
              isLoading: false,
              error: null
            });
          }, false, 'payment/setDefaultPaymentMethod/success');
        } catch (error) {
          set(function (state) {
            return Object.assign({}, state, {
              isLoading: false,
              error: error instanceof Error ? error.message : 'Failed to set default payment method'
            });
          }, false, 'payment/setDefaultPaymentMethod/error');
        }
      });
      function setDefaultPaymentMethod(_x6) {
        return _setDefaultPaymentMethod.apply(this, arguments);
      }
      return setDefaultPaymentMethod;
    }(),
    createPaymentIntent: function () {
      var _createPaymentIntent = (0, _asyncToGenerator2.default)(function* (amount, currency, metadata) {
        set(function (state) {
          return Object.assign({}, state, {
            isProcessingPayment: true,
            paymentError: null
          });
        }, false, 'payment/createPaymentIntent/start');
        try {
          yield new Promise(function (resolve) {
            return setTimeout(resolve, 1000);
          });
          var paymentIntent = {
            id: `pi_${Date.now()}`,
            clientSecret: `pi_${Date.now()}_secret_${Math.random().toString(36).substr(2, 9)}`,
            amount: amount,
            currency: currency,
            status: 'requires_action',
            paymentMethodTypes: ['credit_card', 'debit_card'],
            metadata: metadata,
            createdAt: new Date().toISOString()
          };
          set(function (state) {
            return Object.assign({}, state, {
              currentPaymentIntent: paymentIntent,
              isProcessingPayment: false,
              paymentError: null
            });
          }, false, 'payment/createPaymentIntent/success');
          return paymentIntent;
        } catch (error) {
          set(function (state) {
            return Object.assign({}, state, {
              isProcessingPayment: false,
              paymentError: error instanceof Error ? error.message : 'Failed to create payment intent'
            });
          }, false, 'payment/createPaymentIntent/error');
          throw error;
        }
      });
      function createPaymentIntent(_x7, _x8, _x9) {
        return _createPaymentIntent.apply(this, arguments);
      }
      return createPaymentIntent;
    }(),
    confirmPayment: function () {
      var _confirmPayment = (0, _asyncToGenerator2.default)(function* (paymentIntentId, paymentMethodId) {
        set(function (state) {
          return Object.assign({}, state, {
            isProcessingPayment: true,
            paymentError: null
          });
        }, false, 'payment/confirmPayment/start');
        try {
          yield new Promise(function (resolve) {
            return setTimeout(resolve, 2000);
          });
          var paymentMethod = get().paymentMethods.find(function (pm) {
            return pm.id === paymentMethodId;
          });
          var paymentIntent = get().currentPaymentIntent;
          if (!paymentMethod || !paymentIntent) {
            throw new Error('Payment method or intent not found');
          }
          var transaction = {
            id: `txn_${Date.now()}`,
            paymentIntentId: paymentIntentId,
            amount: paymentIntent.amount,
            currency: paymentIntent.currency,
            status: 'succeeded',
            paymentMethod: paymentMethod,
            description: `Payment for ${paymentIntent.metadata.serviceName}`,
            metadata: paymentIntent.metadata,
            fees: {
              stripeFee: paymentIntent.amount * 0.029 + 0.3,
              applicationFee: paymentIntent.amount * 0.05,
              processingFee: 0,
              total: paymentIntent.amount * 0.079 + 0.3
            },
            refunds: [],
            disputes: [],
            receipt: {
              id: `receipt_${Date.now()}`,
              receiptNumber: `RCP-${Date.now()}`,
              receiptUrl: `https://example.com/receipts/receipt_${Date.now()}.pdf`,
              emailSent: true,
              downloadUrl: `https://example.com/receipts/receipt_${Date.now()}.pdf`
            },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            processedAt: new Date().toISOString()
          };
          set(function (state) {
            return Object.assign({}, state, {
              transactions: [transaction].concat((0, _toConsumableArray2.default)(state.transactions)),
              recentTransactions: [transaction].concat((0, _toConsumableArray2.default)(state.recentTransactions.slice(0, 9))),
              currentPaymentIntent: null,
              isProcessingPayment: false,
              paymentError: null
            });
          }, false, 'payment/confirmPayment/success');
          return transaction;
        } catch (error) {
          set(function (state) {
            return Object.assign({}, state, {
              isProcessingPayment: false,
              paymentError: error instanceof Error ? error.message : 'Payment failed'
            });
          }, false, 'payment/confirmPayment/error');
          throw error;
        }
      });
      function confirmPayment(_x0, _x1) {
        return _confirmPayment.apply(this, arguments);
      }
      return confirmPayment;
    }(),
    cancelPayment: function () {
      var _cancelPayment = (0, _asyncToGenerator2.default)(function* (paymentIntentId) {
        set(function (state) {
          return Object.assign({}, state, {
            isProcessingPayment: true,
            paymentError: null
          });
        }, false, 'payment/cancelPayment/start');
        try {
          yield new Promise(function (resolve) {
            return setTimeout(resolve, 500);
          });
          set(function (state) {
            return Object.assign({}, state, {
              currentPaymentIntent: null,
              isProcessingPayment: false,
              paymentError: null
            });
          }, false, 'payment/cancelPayment/success');
        } catch (error) {
          set(function (state) {
            return Object.assign({}, state, {
              isProcessingPayment: false,
              paymentError: error instanceof Error ? error.message : 'Failed to cancel payment'
            });
          }, false, 'payment/cancelPayment/error');
        }
      });
      function cancelPayment(_x10) {
        return _cancelPayment.apply(this, arguments);
      }
      return cancelPayment;
    }(),
    loadTransactions: function () {
      var _loadTransactions = (0, _asyncToGenerator2.default)(function* (userId) {
        set(function (state) {
          return Object.assign({}, state, {
            isLoading: true,
            error: null
          });
        }, false, 'payment/loadTransactions/start');
        try {
          console.log('💳 PaymentStore: Loading transactions for user:', userId);
          var transactionResponse = yield _paymentService.paymentService.getTransactionHistory(1, 50);
          var transformedTransactions = transactionResponse.results.map(function (tx) {
            return {
              id: tx.id,
              paymentIntentId: tx.stripe_charge_id || `pi_${tx.id}`,
              amount: tx.total_amount,
              currency: tx.currency,
              status: tx.status,
              paymentMethod: {
                id: `pm_${tx.id}`,
                type: 'credit_card',
                brand: 'visa',
                last4: '4242'
              },
              description: tx.booking_service_name || `Transaction ${tx.id}`,
              metadata: tx.metadata || {},
              fees: {
                stripeFee: tx.total_amount * 0.029 + 0.3,
                applicationFee: tx.service_fee || 0,
                processingFee: 0,
                total: (tx.service_fee || 0) + (tx.total_amount * 0.029 + 0.3)
              },
              refunds: [],
              disputes: [],
              receipt: {
                id: `receipt_${tx.id}`,
                receiptNumber: `RCP-${tx.id}`,
                receiptUrl: `#`,
                emailSent: true,
                downloadUrl: `#`
              },
              createdAt: tx.created_at,
              updatedAt: tx.updated_at
            };
          });
          set(function (state) {
            return Object.assign({}, state, {
              transactions: transformedTransactions,
              isLoading: false,
              error: null
            });
          }, false, 'payment/loadTransactions/success');
          console.log('✅ PaymentStore: Transactions loaded:', transformedTransactions.length);
        } catch (error) {
          set(function (state) {
            return Object.assign({}, state, {
              isLoading: false,
              error: error instanceof Error ? error.message : 'Failed to load transactions'
            });
          }, false, 'payment/loadTransactions/error');
        }
      });
      function loadTransactions(_x11) {
        return _loadTransactions.apply(this, arguments);
      }
      return loadTransactions;
    }(),
    loadRecentTransactions: function () {
      var _loadRecentTransactions = (0, _asyncToGenerator2.default)(function* (userId) {
        set(function (state) {
          return Object.assign({}, state, {
            isLoading: true,
            error: null
          });
        }, false, 'payment/loadRecentTransactions/start');
        try {
          yield new Promise(function (resolve) {
            return setTimeout(resolve, 500);
          });
          var mockRecentTransactions = [];
          set(function (state) {
            return Object.assign({}, state, {
              recentTransactions: mockRecentTransactions,
              isLoading: false,
              error: null
            });
          }, false, 'payment/loadRecentTransactions/success');
        } catch (error) {
          set(function (state) {
            return Object.assign({}, state, {
              isLoading: false,
              error: error instanceof Error ? error.message : 'Failed to load recent transactions'
            });
          }, false, 'payment/loadRecentTransactions/error');
        }
      });
      function loadRecentTransactions(_x12) {
        return _loadRecentTransactions.apply(this, arguments);
      }
      return loadRecentTransactions;
    }(),
    requestRefund: function () {
      var _requestRefund = (0, _asyncToGenerator2.default)(function* (transactionId, amount, reason) {
        set(function (state) {
          return Object.assign({}, state, {
            isLoading: true,
            error: null
          });
        }, false, 'payment/requestRefund/start');
        try {
          console.log('💳 PaymentStore: Requesting refund for transaction:', transactionId);
          var refundResponse = yield _paymentService.paymentService.requestRefund(transactionId, amount, reason);
          var refund = {
            id: refundResponse.id,
            amount: refundResponse.amount,
            reason: refundResponse.reason,
            status: refundResponse.status,
            requestedBy: 'customer',
            requestedAt: refundResponse.createdAt
          };
          set(function (state) {
            return Object.assign({}, state, {
              transactions: state.transactions.map(function (txn) {
                return txn.id === transactionId ? Object.assign({}, txn, {
                  refunds: [].concat((0, _toConsumableArray2.default)(txn.refunds), [refund]),
                  updatedAt: new Date().toISOString()
                }) : txn;
              }),
              isLoading: false,
              error: null
            });
          }, false, 'payment/requestRefund/success');
          console.log('✅ PaymentStore: Refund requested successfully:', refund.id);
        } catch (error) {
          set(function (state) {
            return Object.assign({}, state, {
              isLoading: false,
              error: error instanceof Error ? error.message : 'Failed to request refund'
            });
          }, false, 'payment/requestRefund/error');
        }
      });
      function requestRefund(_x13, _x14, _x15) {
        return _requestRefund.apply(this, arguments);
      }
      return requestRefund;
    }(),
    loadPaymentSummary: function () {
      var _loadPaymentSummary = (0, _asyncToGenerator2.default)(function* (userId) {
        set(function (state) {
          return Object.assign({}, state, {
            isLoading: true,
            error: null
          });
        }, false, 'payment/loadPaymentSummary/start');
        try {
          yield new Promise(function (resolve) {
            return setTimeout(resolve, 800);
          });
          var mockSummary = {
            totalTransactions: 25,
            totalAmount: 1250.0,
            successfulTransactions: 23,
            failedTransactions: 2,
            refundedAmount: 150.0,
            averageTransactionAmount: 50.0,
            monthlyBreakdown: [{
              month: '2024-01',
              totalAmount: 400.0,
              transactionCount: 8,
              refundAmount: 50.0,
              averageAmount: 50.0
            }, {
              month: '2024-02',
              totalAmount: 450.0,
              transactionCount: 9,
              refundAmount: 100.0,
              averageAmount: 50.0
            }, {
              month: '2024-03',
              totalAmount: 400.0,
              transactionCount: 8,
              refundAmount: 0.0,
              averageAmount: 50.0
            }]
          };
          set(function (state) {
            return Object.assign({}, state, {
              paymentSummary: mockSummary,
              isLoading: false,
              error: null
            });
          }, false, 'payment/loadPaymentSummary/success');
        } catch (error) {
          set(function (state) {
            return Object.assign({}, state, {
              isLoading: false,
              error: error instanceof Error ? error.message : 'Failed to load payment summary'
            });
          }, false, 'payment/loadPaymentSummary/error');
        }
      });
      function loadPaymentSummary(_x16) {
        return _loadPaymentSummary.apply(this, arguments);
      }
      return loadPaymentSummary;
    }(),
    clearPaymentError: function clearPaymentError() {
      set(function (state) {
        return Object.assign({}, state, {
          paymentError: null
        });
      }, false, 'payment/clearPaymentError');
    },
    clearError: function clearError() {
      set(function (state) {
        return Object.assign({}, state, {
          error: null,
          paymentError: null
        });
      }, false, 'payment/clearError');
    },
    reset: function reset() {
      set(function () {
        return Object.assign({}, initialState);
      }, false, 'payment/reset');
    }
  });
}, {
  name: 'payment-store',
  storage: {
    getItem: function () {
      var _getItem = (0, _asyncToGenerator2.default)(function* (name) {
        try {
          var value = yield _asyncStorage.default.getItem(name);
          return value ? JSON.parse(value) : null;
        } catch (error) {
          console.error('Failed to load payment state:', error);
          return null;
        }
      });
      function getItem(_x17) {
        return _getItem.apply(this, arguments);
      }
      return getItem;
    }(),
    setItem: function () {
      var _setItem = (0, _asyncToGenerator2.default)(function* (name, value) {
        try {
          yield _asyncStorage.default.setItem(name, JSON.stringify(value));
        } catch (error) {
          console.error('Failed to save payment state:', error);
        }
      });
      function setItem(_x18, _x19) {
        return _setItem.apply(this, arguments);
      }
      return setItem;
    }(),
    removeItem: function () {
      var _removeItem = (0, _asyncToGenerator2.default)(function* (name) {
        try {
          yield _asyncStorage.default.removeItem(name);
        } catch (error) {
          console.error('Failed to remove payment state:', error);
        }
      });
      function removeItem(_x20) {
        return _removeItem.apply(this, arguments);
      }
      return removeItem;
    }()
  },
  partialize: function partialize(state) {
    return {
      paymentMethods: state.paymentMethods,
      defaultPaymentMethod: state.defaultPaymentMethod
    };
  }
})));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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