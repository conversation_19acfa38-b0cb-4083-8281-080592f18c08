{"version": 3, "names": ["_NativeAnimatedModule", "_interopRequireDefault", "require", "_NativeAnimatedTurboModule", "_NativeEventEmitter", "_RCTDeviceEventEmitter", "_Platform", "ReactNativeFeatureFlags", "_interopRequireWildcard", "_invariant", "_nullthrows", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "NativeAnimatedModule", "NativeAnimatedNonTurboModule", "NativeAnimatedTurboModule", "__nativeAnimatedNodeTagCount", "__nativeAnimationIdCount", "nativeEventEmitter", "waitingForQueuedOperations", "Set", "queueOperations", "queue", "singleOpQueue", "isSingleOpBatching", "Platform", "OS", "queueAndExecuteBatchedOperations", "animatedShouldUseSingleOp", "flushQueueImmediate", "eventListenerGetValueCallbacks", "eventListenerAnimationFinishedCallbacks", "globalEventEmitterGetValueListener", "globalEventEmitterAnimationFinishedListener", "createNativeOperations", "methodNames", "nativeOperations", "_loop", "methodName", "ii", "operationID", "_len", "arguments", "length", "args", "Array", "_key", "push", "apply", "concat", "_loop2", "_len2", "_key2", "method", "nullthrows", "NativeOperations", "API", "getValue", "tag", "save<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setWaitingForIdentifier", "id", "add", "animatedShouldDebounceQueueFlush", "enableAnimatedClearImmediateFix", "clearImmediate", "clearTimeout", "unsetWaitingForIdentifier", "delete", "size", "disableQueue", "invariant", "prevImmediate", "setImmediate", "flushQueue", "ensureGlobalEventEmitterListeners", "startOperationBatch", "q", "l", "finishOperationBatch", "createAnimatedNode", "config", "updateAnimatedNodeConfig", "startListeningToAnimatedNodeValue", "stopListeningToAnimatedNodeValue", "connectAnimatedNodes", "parentTag", "childTag", "disconnectAnimatedNodes", "startAnimatingNode", "animationId", "nodeTag", "endCallback", "stopAnimation", "setAnimatedNodeValue", "value", "setAnimatedNodeOffset", "offset", "flattenAnimatedNodeOffset", "extractAnimatedNodeOffset", "connectAnimatedNodeToView", "viewTag", "disconnectAnimatedNodeFromView", "restoreDefaultValues", "dropAnimatedNode", "addAnimatedEventToView", "eventName", "eventMapping", "removeAnimatedEventFromView", "animatedNodeTag", "RCTDeviceEventEmitter", "addListener", "params", "callback", "animations", "isArray", "animation", "generateNewNodeTag", "generateNewAnimationId", "assertNativeAnimatedModule", "_warnedMissingNativeAnimated", "shouldUseNativeDriver", "useNativeDriver", "console", "warn", "process", "env", "NODE_ENV", "transformDataType", "endsWith", "degrees", "parseFloat", "Math", "PI", "_default", "exports", "NativeEventEmitter"], "sources": ["NativeAnimatedHelper.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {EventConfig} from '../../../Libraries/Animated/AnimatedEvent';\nimport type {\n  AnimationConfig,\n  EndCallback,\n} from '../../../Libraries/Animated/animations/Animation';\nimport type {\n  AnimatedNodeConfig,\n  EventMapping,\n} from '../../../Libraries/Animated/NativeAnimatedModule';\nimport type {EventSubscription} from '../../../Libraries/vendor/emitter/EventEmitter';\n\nimport NativeAnimatedNonTurboModule from '../../../Libraries/Animated/NativeAnimatedModule';\nimport NativeAnimatedTurboModule from '../../../Libraries/Animated/NativeAnimatedTurboModule';\nimport NativeEventEmitter from '../../../Libraries/EventEmitter/NativeEventEmitter';\nimport RCTDeviceEventEmitter from '../../../Libraries/EventEmitter/RCTDeviceEventEmitter';\nimport Platform from '../../../Libraries/Utilities/Platform';\nimport * as ReactNativeFeatureFlags from '../featureflags/ReactNativeFeatureFlags';\nimport invariant from 'invariant';\nimport nullthrows from 'nullthrows';\n\n// TODO T69437152 @petetheheat - Delete this fork when Fabric ships to 100%.\nconst NativeAnimatedModule: typeof NativeAnimatedTurboModule =\n  NativeAnimatedNonTurboModule ?? NativeAnimatedTurboModule;\n\nlet __nativeAnimatedNodeTagCount = 1; /* used for animated nodes */\nlet __nativeAnimationIdCount = 1; /* used for started animations */\n\nlet nativeEventEmitter;\n\nlet waitingForQueuedOperations = new Set<string>();\nlet queueOperations = false;\nlet queue: Array<() => void> = [];\nlet singleOpQueue: Array<mixed> = [];\n\nconst isSingleOpBatching =\n  Platform.OS === 'android' &&\n  NativeAnimatedModule?.queueAndExecuteBatchedOperations != null &&\n  ReactNativeFeatureFlags.animatedShouldUseSingleOp();\nlet flushQueueImmediate = null;\n\nconst eventListenerGetValueCallbacks: {\n  [number]: (value: number) => void,\n} = {};\nconst eventListenerAnimationFinishedCallbacks: {\n  [number]: EndCallback,\n} = {};\nlet globalEventEmitterGetValueListener: ?EventSubscription = null;\nlet globalEventEmitterAnimationFinishedListener: ?EventSubscription = null;\n\nfunction createNativeOperations(): $NonMaybeType<typeof NativeAnimatedModule> {\n  const methodNames = [\n    'createAnimatedNode', // 1\n    'updateAnimatedNodeConfig', // 2\n    'getValue', // 3\n    'startListeningToAnimatedNodeValue', // 4\n    'stopListeningToAnimatedNodeValue', // 5\n    'connectAnimatedNodes', // 6\n    'disconnectAnimatedNodes', // 7\n    'startAnimatingNode', // 8\n    'stopAnimation', // 9\n    'setAnimatedNodeValue', // 10\n    'setAnimatedNodeOffset', // 11\n    'flattenAnimatedNodeOffset', // 12\n    'extractAnimatedNodeOffset', // 13\n    'connectAnimatedNodeToView', // 14\n    'disconnectAnimatedNodeFromView', // 15\n    'restoreDefaultValues', // 16\n    'dropAnimatedNode', // 17\n    'addAnimatedEventToView', // 18\n    'removeAnimatedEventFromView', // 19\n    'addListener', // 20\n    'removeListener', // 21\n  ];\n  const nativeOperations: {\n    [$Values<typeof methodNames>]: (...$ReadOnlyArray<mixed>) => void,\n  } = {};\n  if (isSingleOpBatching) {\n    for (let ii = 0, length = methodNames.length; ii < length; ii++) {\n      const methodName = methodNames[ii];\n      const operationID = ii + 1;\n      nativeOperations[methodName] = (...args) => {\n        // `singleOpQueue` is a flat array of operation IDs and arguments, which\n        // is possible because # arguments is fixed for each operation. For more\n        // details, see `NativeAnimatedModule.queueAndExecuteBatchedOperations`.\n        singleOpQueue.push(operationID, ...args);\n      };\n    }\n  } else {\n    for (let ii = 0, length = methodNames.length; ii < length; ii++) {\n      const methodName = methodNames[ii];\n      nativeOperations[methodName] = (...args) => {\n        const method = nullthrows(NativeAnimatedModule)[methodName];\n        // If queueing is explicitly on, *or* the queue has not yet\n        // been flushed, use the queue. This is to prevent operations\n        // from being executed out of order.\n        if (queueOperations || queue.length !== 0) {\n          // $FlowExpectedError[incompatible-call] - Dynamism.\n          queue.push(() => method(...args));\n        } else {\n          // $FlowExpectedError[incompatible-call] - Dynamism.\n          method(...args);\n        }\n      };\n    }\n  }\n  // $FlowExpectedError[incompatible-return] - Dynamism.\n  return nativeOperations;\n}\n\nconst NativeOperations = createNativeOperations();\n\n/**\n * Wrappers around NativeAnimatedModule to provide flow and autocomplete support for\n * the native module methods, and automatic queue management on Android\n */\nconst API = {\n  getValue: (isSingleOpBatching\n    ? (tag, saveValueCallback) => {\n        if (saveValueCallback) {\n          eventListenerGetValueCallbacks[tag] = saveValueCallback;\n        }\n        /* $FlowExpectedError[incompatible-call] - `saveValueCallback` is handled\n            differently when `isSingleOpBatching` is enabled. */\n        NativeOperations.getValue(tag);\n      }\n    : (tag, saveValueCallback) => {\n        NativeOperations.getValue(tag, saveValueCallback);\n      }) as $NonMaybeType<typeof NativeAnimatedModule>['getValue'],\n\n  setWaitingForIdentifier(id: string): void {\n    waitingForQueuedOperations.add(id);\n    queueOperations = true;\n    if (\n      ReactNativeFeatureFlags.animatedShouldDebounceQueueFlush() &&\n      flushQueueImmediate\n    ) {\n      if (ReactNativeFeatureFlags.enableAnimatedClearImmediateFix()) {\n        clearImmediate(flushQueueImmediate);\n      } else {\n        clearTimeout(flushQueueImmediate);\n      }\n    }\n  },\n\n  unsetWaitingForIdentifier(id: string): void {\n    waitingForQueuedOperations.delete(id);\n\n    if (waitingForQueuedOperations.size === 0) {\n      queueOperations = false;\n      API.disableQueue();\n    }\n  },\n\n  disableQueue(): void {\n    invariant(NativeAnimatedModule, 'Native animated module is not available');\n\n    if (ReactNativeFeatureFlags.animatedShouldDebounceQueueFlush()) {\n      const prevImmediate = flushQueueImmediate;\n      clearImmediate(prevImmediate);\n      flushQueueImmediate = setImmediate(API.flushQueue);\n    } else {\n      API.flushQueue();\n    }\n  },\n\n  flushQueue: (isSingleOpBatching\n    ? (): void => {\n        invariant(\n          NativeAnimatedModule,\n          'Native animated module is not available',\n        );\n        flushQueueImmediate = null;\n\n        if (singleOpQueue.length === 0) {\n          return;\n        }\n\n        // Set up event listener for callbacks if it's not set up\n        ensureGlobalEventEmitterListeners();\n\n        // Single op batching doesn't use callback functions, instead we\n        // use RCTDeviceEventEmitter. This reduces overhead of sending lots of\n        // JSI functions across to native code; but also, TM infrastructure currently\n        // does not support packing a function into native arrays.\n        NativeAnimatedModule?.queueAndExecuteBatchedOperations?.(singleOpQueue);\n        singleOpQueue.length = 0;\n      }\n    : (): void => {\n        invariant(\n          NativeAnimatedModule,\n          'Native animated module is not available',\n        );\n        flushQueueImmediate = null;\n\n        if (queue.length === 0) {\n          return;\n        }\n\n        if (Platform.OS === 'android') {\n          NativeAnimatedModule?.startOperationBatch?.();\n        }\n\n        for (let q = 0, l = queue.length; q < l; q++) {\n          queue[q]();\n        }\n        queue.length = 0;\n\n        if (Platform.OS === 'android') {\n          NativeAnimatedModule?.finishOperationBatch?.();\n        }\n      }) as () => void,\n\n  createAnimatedNode(tag: number, config: AnimatedNodeConfig): void {\n    NativeOperations.createAnimatedNode(tag, config);\n  },\n\n  updateAnimatedNodeConfig(tag: number, config: AnimatedNodeConfig): void {\n    NativeOperations.updateAnimatedNodeConfig?.(tag, config);\n  },\n\n  startListeningToAnimatedNodeValue(tag: number): void {\n    NativeOperations.startListeningToAnimatedNodeValue(tag);\n  },\n\n  stopListeningToAnimatedNodeValue(tag: number): void {\n    NativeOperations.stopListeningToAnimatedNodeValue(tag);\n  },\n\n  connectAnimatedNodes(parentTag: number, childTag: number): void {\n    NativeOperations.connectAnimatedNodes(parentTag, childTag);\n  },\n\n  disconnectAnimatedNodes(parentTag: number, childTag: number): void {\n    NativeOperations.disconnectAnimatedNodes(parentTag, childTag);\n  },\n\n  startAnimatingNode: (isSingleOpBatching\n    ? (animationId, nodeTag, config, endCallback) => {\n        if (endCallback) {\n          eventListenerAnimationFinishedCallbacks[animationId] = endCallback;\n        }\n        /* $FlowExpectedError[incompatible-call] - `endCallback` is handled\n            differently when `isSingleOpBatching` is enabled. */\n        NativeOperations.startAnimatingNode(animationId, nodeTag, config);\n      }\n    : (animationId, nodeTag, config, endCallback) => {\n        NativeOperations.startAnimatingNode(\n          animationId,\n          nodeTag,\n          config,\n          endCallback,\n        );\n      }) as $NonMaybeType<typeof NativeAnimatedModule>['startAnimatingNode'],\n\n  stopAnimation(animationId: number) {\n    NativeOperations.stopAnimation(animationId);\n  },\n\n  setAnimatedNodeValue(nodeTag: number, value: number): void {\n    NativeOperations.setAnimatedNodeValue(nodeTag, value);\n  },\n\n  setAnimatedNodeOffset(nodeTag: number, offset: number): void {\n    NativeOperations.setAnimatedNodeOffset(nodeTag, offset);\n  },\n\n  flattenAnimatedNodeOffset(nodeTag: number): void {\n    NativeOperations.flattenAnimatedNodeOffset(nodeTag);\n  },\n\n  extractAnimatedNodeOffset(nodeTag: number): void {\n    NativeOperations.extractAnimatedNodeOffset(nodeTag);\n  },\n\n  connectAnimatedNodeToView(nodeTag: number, viewTag: number): void {\n    NativeOperations.connectAnimatedNodeToView(nodeTag, viewTag);\n  },\n\n  disconnectAnimatedNodeFromView(nodeTag: number, viewTag: number): void {\n    NativeOperations.disconnectAnimatedNodeFromView(nodeTag, viewTag);\n  },\n\n  restoreDefaultValues(nodeTag: number): void {\n    NativeOperations.restoreDefaultValues?.(nodeTag);\n  },\n\n  dropAnimatedNode(tag: number): void {\n    NativeOperations.dropAnimatedNode(tag);\n  },\n\n  addAnimatedEventToView(\n    viewTag: number,\n    eventName: string,\n    eventMapping: EventMapping,\n  ) {\n    NativeOperations.addAnimatedEventToView(viewTag, eventName, eventMapping);\n  },\n\n  removeAnimatedEventFromView(\n    viewTag: number,\n    eventName: string,\n    animatedNodeTag: number,\n  ) {\n    NativeOperations.removeAnimatedEventFromView(\n      viewTag,\n      eventName,\n      animatedNodeTag,\n    );\n  },\n};\n\nfunction ensureGlobalEventEmitterListeners() {\n  if (\n    globalEventEmitterGetValueListener &&\n    globalEventEmitterAnimationFinishedListener\n  ) {\n    return;\n  }\n  globalEventEmitterGetValueListener = RCTDeviceEventEmitter.addListener(\n    'onNativeAnimatedModuleGetValue',\n    params => {\n      const {tag} = params;\n      const callback = eventListenerGetValueCallbacks[tag];\n      if (!callback) {\n        return;\n      }\n      callback(params.value);\n      delete eventListenerGetValueCallbacks[tag];\n    },\n  );\n  globalEventEmitterAnimationFinishedListener =\n    RCTDeviceEventEmitter.addListener(\n      'onNativeAnimatedModuleAnimationFinished',\n      params => {\n        // TODO: remove Array.isArray once native changes have propagated\n        const animations = Array.isArray(params) ? params : [params];\n        for (const animation of animations) {\n          const {animationId} = animation;\n          const callback = eventListenerAnimationFinishedCallbacks[animationId];\n          if (callback) {\n            callback(animation);\n            delete eventListenerAnimationFinishedCallbacks[animationId];\n          }\n        }\n      },\n    );\n}\n\nfunction generateNewNodeTag(): number {\n  return __nativeAnimatedNodeTagCount++;\n}\n\nfunction generateNewAnimationId(): number {\n  return __nativeAnimationIdCount++;\n}\n\nfunction assertNativeAnimatedModule(): void {\n  invariant(NativeAnimatedModule, 'Native animated module is not available');\n}\n\nlet _warnedMissingNativeAnimated = false;\n\nfunction shouldUseNativeDriver(\n  config: $ReadOnly<{...AnimationConfig, ...}> | EventConfig,\n): boolean {\n  if (config.useNativeDriver == null) {\n    console.warn(\n      'Animated: `useNativeDriver` was not specified. This is a required ' +\n        'option and must be explicitly set to `true` or `false`',\n    );\n  }\n\n  if (config.useNativeDriver === true && !NativeAnimatedModule) {\n    if (process.env.NODE_ENV !== 'test') {\n      if (!_warnedMissingNativeAnimated) {\n        console.warn(\n          'Animated: `useNativeDriver` is not supported because the native ' +\n            'animated module is missing. Falling back to JS-based animation. To ' +\n            'resolve this, add `RCTAnimation` module to this app, or remove ' +\n            '`useNativeDriver`. ' +\n            'Make sure to run `bundle exec pod install` first. Read more about autolinking: https://github.com/react-native-community/cli/blob/master/docs/autolinking.md',\n        );\n        _warnedMissingNativeAnimated = true;\n      }\n    }\n    return false;\n  }\n\n  return config.useNativeDriver || false;\n}\n\nfunction transformDataType(value: number | string): number | string {\n  // Change the string type to number type so we can reuse the same logic in\n  // iOS and Android platform\n  if (typeof value !== 'string') {\n    return value;\n  }\n\n  // Normalize degrees and radians to a number expressed in radians\n  if (value.endsWith('deg')) {\n    const degrees = parseFloat(value) || 0;\n    return (degrees * Math.PI) / 180.0;\n  } else if (value.endsWith('rad')) {\n    return parseFloat(value) || 0;\n  } else {\n    return value;\n  }\n}\n\nexport default {\n  API,\n  generateNewNodeTag,\n  generateNewAnimationId,\n  assertNativeAnimatedModule,\n  shouldUseNativeDriver,\n  transformDataType,\n  // $FlowExpectedError[unsafe-getters-setters] - unsafe getter lint suppression\n  // $FlowExpectedError[missing-type-arg] - unsafe getter lint suppression\n  get nativeEventEmitter(): NativeEventEmitter {\n    if (!nativeEventEmitter) {\n      // $FlowFixMe[underconstrained-implicit-instantiation]\n      nativeEventEmitter = new NativeEventEmitter(\n        // *********: NativeEventEmitter only used this parameter on iOS. Now it uses it on all platforms, so this code was modified automatically to preserve its behavior\n        // If you want to use the native module on other platforms, please remove this condition and test its behavior\n        Platform.OS !== 'ios' ? null : NativeAnimatedModule,\n      );\n    }\n    return nativeEventEmitter;\n  },\n};\n"], "mappings": ";;;;;AAqBA,IAAAA,qBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,0BAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,mBAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,sBAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,SAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,uBAAA,GAAAC,uBAAA,CAAAN,OAAA;AACA,IAAAO,UAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,WAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAoC,SAAAM,wBAAAG,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAL,uBAAA,YAAAA,wBAAAG,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAGpC,IAAMmB,oBAAsD,GAC1DC,6BAA4B,WAA5BA,6BAA4B,GAAIC,kCAAyB;AAE3D,IAAIC,4BAA4B,GAAG,CAAC;AACpC,IAAIC,wBAAwB,GAAG,CAAC;AAEhC,IAAIC,kBAAkB;AAEtB,IAAIC,0BAA0B,GAAG,IAAIC,GAAG,CAAS,CAAC;AAClD,IAAIC,eAAe,GAAG,KAAK;AAC3B,IAAIC,KAAwB,GAAG,EAAE;AACjC,IAAIC,aAA2B,GAAG,EAAE;AAEpC,IAAMC,kBAAkB,GACtBC,iBAAQ,CAACC,EAAE,KAAK,SAAS,IACzB,CAAAb,oBAAoB,oBAApBA,oBAAoB,CAAEc,gCAAgC,KAAI,IAAI,IAC9DtC,uBAAuB,CAACuC,yBAAyB,CAAC,CAAC;AACrD,IAAIC,mBAAmB,GAAG,IAAI;AAE9B,IAAMC,8BAEL,GAAG,CAAC,CAAC;AACN,IAAMC,uCAEL,GAAG,CAAC,CAAC;AACN,IAAIC,kCAAsD,GAAG,IAAI;AACjE,IAAIC,2CAA+D,GAAG,IAAI;AAE1E,SAASC,sBAAsBA,CAAA,EAA+C;EAC5E,IAAMC,WAAW,GAAG,CAClB,oBAAoB,EACpB,0BAA0B,EAC1B,UAAU,EACV,mCAAmC,EACnC,kCAAkC,EAClC,sBAAsB,EACtB,yBAAyB,EACzB,oBAAoB,EACpB,eAAe,EACf,sBAAsB,EACtB,uBAAuB,EACvB,2BAA2B,EAC3B,2BAA2B,EAC3B,2BAA2B,EAC3B,gCAAgC,EAChC,sBAAsB,EACtB,kBAAkB,EAClB,wBAAwB,EACxB,6BAA6B,EAC7B,aAAa,EACb,gBAAgB,CACjB;EACD,IAAMC,gBAEL,GAAG,CAAC,CAAC;EACN,IAAIZ,kBAAkB,EAAE;IAAA,IAAAa,KAAA,YAAAA,MAAA,EAC2C;MAC/D,IAAMC,UAAU,GAAGH,WAAW,CAACI,EAAE,CAAC;MAClC,IAAMC,WAAW,GAAGD,EAAE,GAAG,CAAC;MAC1BH,gBAAgB,CAACE,UAAU,CAAC,GAAG,YAAa;QAAA,SAAAG,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAATC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;UAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;QAAA;QAIrCvB,aAAa,CAACwB,IAAI,CAAAC,KAAA,CAAlBzB,aAAa,GAAMiB,WAAW,EAAAS,MAAA,CAAKL,IAAI,EAAC;MAC1C,CAAC;IACH,CAAC;IATD,KAAK,IAAIL,EAAE,GAAG,CAAC,EAAEI,MAAM,GAAGR,WAAW,CAACQ,MAAM,EAAEJ,EAAE,GAAGI,MAAM,EAAEJ,EAAE,EAAE;MAAAF,KAAA;IAAA;EAUjE,CAAC,MAAM;IAAA,IAAAa,MAAA,YAAAA,OAAA,EAC4D;MAC/D,IAAMZ,UAAU,GAAGH,WAAW,CAACI,GAAE,CAAC;MAClCH,gBAAgB,CAACE,UAAU,CAAC,GAAG,YAAa;QAAA,SAAAa,KAAA,GAAAT,SAAA,CAAAC,MAAA,EAATC,IAAI,OAAAC,KAAA,CAAAM,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;UAAJR,IAAI,CAAAQ,KAAA,IAAAV,SAAA,CAAAU,KAAA;QAAA;QACrC,IAAMC,MAAM,GAAG,IAAAC,mBAAU,EAACzC,oBAAoB,CAAC,CAACyB,UAAU,CAAC;QAI3D,IAAIjB,eAAe,IAAIC,KAAK,CAACqB,MAAM,KAAK,CAAC,EAAE;UAEzCrB,KAAK,CAACyB,IAAI,CAAC;YAAA,OAAMM,MAAM,CAAAL,KAAA,SAAIJ,IAAI,CAAC;UAAA,EAAC;QACnC,CAAC,MAAM;UAELS,MAAM,CAAAL,KAAA,SAAIJ,IAAI,CAAC;QACjB;MACF,CAAC;IACH,CAAC;IAfD,KAAK,IAAIL,GAAE,GAAG,CAAC,EAAEI,OAAM,GAAGR,WAAW,CAACQ,MAAM,EAAEJ,GAAE,GAAGI,OAAM,EAAEJ,GAAE,EAAE;MAAAW,MAAA;IAAA;EAgBjE;EAEA,OAAOd,gBAAgB;AACzB;AAEA,IAAMmB,gBAAgB,GAAGrB,sBAAsB,CAAC,CAAC;AAMjD,IAAMsB,GAAG,GAAG;EACVC,QAAQ,EAAGjC,kBAAkB,GACzB,UAACkC,GAAG,EAAEC,iBAAiB,EAAK;IAC1B,IAAIA,iBAAiB,EAAE;MACrB7B,8BAA8B,CAAC4B,GAAG,CAAC,GAAGC,iBAAiB;IACzD;IAGAJ,gBAAgB,CAACE,QAAQ,CAACC,GAAG,CAAC;EAChC,CAAC,GACD,UAACA,GAAG,EAAEC,iBAAiB,EAAK;IAC1BJ,gBAAgB,CAACE,QAAQ,CAACC,GAAG,EAAEC,iBAAiB,CAAC;EACnD,CAA4D;EAEhEC,uBAAuB,WAAvBA,uBAAuBA,CAACC,EAAU,EAAQ;IACxC1C,0BAA0B,CAAC2C,GAAG,CAACD,EAAE,CAAC;IAClCxC,eAAe,GAAG,IAAI;IACtB,IACEhC,uBAAuB,CAAC0E,gCAAgC,CAAC,CAAC,IAC1DlC,mBAAmB,EACnB;MACA,IAAIxC,uBAAuB,CAAC2E,+BAA+B,CAAC,CAAC,EAAE;QAC7DC,cAAc,CAACpC,mBAAmB,CAAC;MACrC,CAAC,MAAM;QACLqC,YAAY,CAACrC,mBAAmB,CAAC;MACnC;IACF;EACF,CAAC;EAEDsC,yBAAyB,WAAzBA,yBAAyBA,CAACN,EAAU,EAAQ;IAC1C1C,0BAA0B,CAACiD,MAAM,CAACP,EAAE,CAAC;IAErC,IAAI1C,0BAA0B,CAACkD,IAAI,KAAK,CAAC,EAAE;MACzChD,eAAe,GAAG,KAAK;MACvBmC,GAAG,CAACc,YAAY,CAAC,CAAC;IACpB;EACF,CAAC;EAEDA,YAAY,WAAZA,YAAYA,CAAA,EAAS;IACnB,IAAAC,kBAAS,EAAC1D,oBAAoB,EAAE,yCAAyC,CAAC;IAE1E,IAAIxB,uBAAuB,CAAC0E,gCAAgC,CAAC,CAAC,EAAE;MAC9D,IAAMS,aAAa,GAAG3C,mBAAmB;MACzCoC,cAAc,CAACO,aAAa,CAAC;MAC7B3C,mBAAmB,GAAG4C,YAAY,CAACjB,GAAG,CAACkB,UAAU,CAAC;IACpD,CAAC,MAAM;MACLlB,GAAG,CAACkB,UAAU,CAAC,CAAC;IAClB;EACF,CAAC;EAEDA,UAAU,EAAGlD,kBAAkB,GAC3B,YAAY;IACV,IAAA+C,kBAAS,EACP1D,oBAAoB,EACpB,yCACF,CAAC;IACDgB,mBAAmB,GAAG,IAAI;IAE1B,IAAIN,aAAa,CAACoB,MAAM,KAAK,CAAC,EAAE;MAC9B;IACF;IAGAgC,iCAAiC,CAAC,CAAC;IAMnC9D,oBAAoB,YAApBA,oBAAoB,CAAEc,gCAAgC,YAAtDd,oBAAoB,CAAEc,gCAAgC,CAAGJ,aAAa,CAAC;IACvEA,aAAa,CAACoB,MAAM,GAAG,CAAC;EAC1B,CAAC,GACD,YAAY;IACV,IAAA4B,kBAAS,EACP1D,oBAAoB,EACpB,yCACF,CAAC;IACDgB,mBAAmB,GAAG,IAAI;IAE1B,IAAIP,KAAK,CAACqB,MAAM,KAAK,CAAC,EAAE;MACtB;IACF;IAEA,IAAIlB,iBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;MAC7Bb,oBAAoB,YAApBA,oBAAoB,CAAE+D,mBAAmB,YAAzC/D,oBAAoB,CAAE+D,mBAAmB,CAAG,CAAC;IAC/C;IAEA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGxD,KAAK,CAACqB,MAAM,EAAEkC,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAC5CvD,KAAK,CAACuD,CAAC,CAAC,CAAC,CAAC;IACZ;IACAvD,KAAK,CAACqB,MAAM,GAAG,CAAC;IAEhB,IAAIlB,iBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;MAC7Bb,oBAAoB,YAApBA,oBAAoB,CAAEkE,oBAAoB,YAA1ClE,oBAAoB,CAAEkE,oBAAoB,CAAG,CAAC;IAChD;EACF,CAAgB;EAEpBC,kBAAkB,WAAlBA,kBAAkBA,CAACtB,GAAW,EAAEuB,MAA0B,EAAQ;IAChE1B,gBAAgB,CAACyB,kBAAkB,CAACtB,GAAG,EAAEuB,MAAM,CAAC;EAClD,CAAC;EAEDC,wBAAwB,WAAxBA,wBAAwBA,CAACxB,GAAW,EAAEuB,MAA0B,EAAQ;IACtE1B,gBAAgB,CAAC2B,wBAAwB,YAAzC3B,gBAAgB,CAAC2B,wBAAwB,CAAGxB,GAAG,EAAEuB,MAAM,CAAC;EAC1D,CAAC;EAEDE,iCAAiC,WAAjCA,iCAAiCA,CAACzB,GAAW,EAAQ;IACnDH,gBAAgB,CAAC4B,iCAAiC,CAACzB,GAAG,CAAC;EACzD,CAAC;EAED0B,gCAAgC,WAAhCA,gCAAgCA,CAAC1B,GAAW,EAAQ;IAClDH,gBAAgB,CAAC6B,gCAAgC,CAAC1B,GAAG,CAAC;EACxD,CAAC;EAED2B,oBAAoB,WAApBA,oBAAoBA,CAACC,SAAiB,EAAEC,QAAgB,EAAQ;IAC9DhC,gBAAgB,CAAC8B,oBAAoB,CAACC,SAAS,EAAEC,QAAQ,CAAC;EAC5D,CAAC;EAEDC,uBAAuB,WAAvBA,uBAAuBA,CAACF,SAAiB,EAAEC,QAAgB,EAAQ;IACjEhC,gBAAgB,CAACiC,uBAAuB,CAACF,SAAS,EAAEC,QAAQ,CAAC;EAC/D,CAAC;EAEDE,kBAAkB,EAAGjE,kBAAkB,GACnC,UAACkE,WAAW,EAAEC,OAAO,EAAEV,MAAM,EAAEW,WAAW,EAAK;IAC7C,IAAIA,WAAW,EAAE;MACf7D,uCAAuC,CAAC2D,WAAW,CAAC,GAAGE,WAAW;IACpE;IAGArC,gBAAgB,CAACkC,kBAAkB,CAACC,WAAW,EAAEC,OAAO,EAAEV,MAAM,CAAC;EACnE,CAAC,GACD,UAACS,WAAW,EAAEC,OAAO,EAAEV,MAAM,EAAEW,WAAW,EAAK;IAC7CrC,gBAAgB,CAACkC,kBAAkB,CACjCC,WAAW,EACXC,OAAO,EACPV,MAAM,EACNW,WACF,CAAC;EACH,CAAsE;EAE1EC,aAAa,WAAbA,aAAaA,CAACH,WAAmB,EAAE;IACjCnC,gBAAgB,CAACsC,aAAa,CAACH,WAAW,CAAC;EAC7C,CAAC;EAEDI,oBAAoB,WAApBA,oBAAoBA,CAACH,OAAe,EAAEI,KAAa,EAAQ;IACzDxC,gBAAgB,CAACuC,oBAAoB,CAACH,OAAO,EAAEI,KAAK,CAAC;EACvD,CAAC;EAEDC,qBAAqB,WAArBA,qBAAqBA,CAACL,OAAe,EAAEM,MAAc,EAAQ;IAC3D1C,gBAAgB,CAACyC,qBAAqB,CAACL,OAAO,EAAEM,MAAM,CAAC;EACzD,CAAC;EAEDC,yBAAyB,WAAzBA,yBAAyBA,CAACP,OAAe,EAAQ;IAC/CpC,gBAAgB,CAAC2C,yBAAyB,CAACP,OAAO,CAAC;EACrD,CAAC;EAEDQ,yBAAyB,WAAzBA,yBAAyBA,CAACR,OAAe,EAAQ;IAC/CpC,gBAAgB,CAAC4C,yBAAyB,CAACR,OAAO,CAAC;EACrD,CAAC;EAEDS,yBAAyB,WAAzBA,yBAAyBA,CAACT,OAAe,EAAEU,OAAe,EAAQ;IAChE9C,gBAAgB,CAAC6C,yBAAyB,CAACT,OAAO,EAAEU,OAAO,CAAC;EAC9D,CAAC;EAEDC,8BAA8B,WAA9BA,8BAA8BA,CAACX,OAAe,EAAEU,OAAe,EAAQ;IACrE9C,gBAAgB,CAAC+C,8BAA8B,CAACX,OAAO,EAAEU,OAAO,CAAC;EACnE,CAAC;EAEDE,oBAAoB,WAApBA,oBAAoBA,CAACZ,OAAe,EAAQ;IAC1CpC,gBAAgB,CAACgD,oBAAoB,YAArChD,gBAAgB,CAACgD,oBAAoB,CAAGZ,OAAO,CAAC;EAClD,CAAC;EAEDa,gBAAgB,WAAhBA,gBAAgBA,CAAC9C,GAAW,EAAQ;IAClCH,gBAAgB,CAACiD,gBAAgB,CAAC9C,GAAG,CAAC;EACxC,CAAC;EAED+C,sBAAsB,WAAtBA,sBAAsBA,CACpBJ,OAAe,EACfK,SAAiB,EACjBC,YAA0B,EAC1B;IACApD,gBAAgB,CAACkD,sBAAsB,CAACJ,OAAO,EAAEK,SAAS,EAAEC,YAAY,CAAC;EAC3E,CAAC;EAEDC,2BAA2B,WAA3BA,2BAA2BA,CACzBP,OAAe,EACfK,SAAiB,EACjBG,eAAuB,EACvB;IACAtD,gBAAgB,CAACqD,2BAA2B,CAC1CP,OAAO,EACPK,SAAS,EACTG,eACF,CAAC;EACH;AACF,CAAC;AAED,SAASlC,iCAAiCA,CAAA,EAAG;EAC3C,IACE3C,kCAAkC,IAClCC,2CAA2C,EAC3C;IACA;EACF;EACAD,kCAAkC,GAAG8E,8BAAqB,CAACC,WAAW,CACpE,gCAAgC,EAChC,UAAAC,MAAM,EAAI;IACR,IAAOtD,GAAG,GAAIsD,MAAM,CAAbtD,GAAG;IACV,IAAMuD,QAAQ,GAAGnF,8BAA8B,CAAC4B,GAAG,CAAC;IACpD,IAAI,CAACuD,QAAQ,EAAE;MACb;IACF;IACAA,QAAQ,CAACD,MAAM,CAACjB,KAAK,CAAC;IACtB,OAAOjE,8BAA8B,CAAC4B,GAAG,CAAC;EAC5C,CACF,CAAC;EACDzB,2CAA2C,GACzC6E,8BAAqB,CAACC,WAAW,CAC/B,yCAAyC,EACzC,UAAAC,MAAM,EAAI;IAER,IAAME,UAAU,GAAGrE,KAAK,CAACsE,OAAO,CAACH,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,CAAC;IAC5D,KAAK,IAAMI,SAAS,IAAIF,UAAU,EAAE;MAClC,IAAOxB,WAAW,GAAI0B,SAAS,CAAxB1B,WAAW;MAClB,IAAMuB,QAAQ,GAAGlF,uCAAuC,CAAC2D,WAAW,CAAC;MACrE,IAAIuB,QAAQ,EAAE;QACZA,QAAQ,CAACG,SAAS,CAAC;QACnB,OAAOrF,uCAAuC,CAAC2D,WAAW,CAAC;MAC7D;IACF;EACF,CACF,CAAC;AACL;AAEA,SAAS2B,kBAAkBA,CAAA,EAAW;EACpC,OAAOrG,4BAA4B,EAAE;AACvC;AAEA,SAASsG,sBAAsBA,CAAA,EAAW;EACxC,OAAOrG,wBAAwB,EAAE;AACnC;AAEA,SAASsG,0BAA0BA,CAAA,EAAS;EAC1C,IAAAhD,kBAAS,EAAC1D,oBAAoB,EAAE,yCAAyC,CAAC;AAC5E;AAEA,IAAI2G,4BAA4B,GAAG,KAAK;AAExC,SAASC,qBAAqBA,CAC5BxC,MAA0D,EACjD;EACT,IAAIA,MAAM,CAACyC,eAAe,IAAI,IAAI,EAAE;IAClCC,OAAO,CAACC,IAAI,CACV,oEAAoE,GAClE,wDACJ,CAAC;EACH;EAEA,IAAI3C,MAAM,CAACyC,eAAe,KAAK,IAAI,IAAI,CAAC7G,oBAAoB,EAAE;IAC5D,IAAIgH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;MACnC,IAAI,CAACP,4BAA4B,EAAE;QACjCG,OAAO,CAACC,IAAI,CACV,kEAAkE,GAChE,qEAAqE,GACrE,iEAAiE,GACjE,qBAAqB,GACrB,8JACJ,CAAC;QACDJ,4BAA4B,GAAG,IAAI;MACrC;IACF;IACA,OAAO,KAAK;EACd;EAEA,OAAOvC,MAAM,CAACyC,eAAe,IAAI,KAAK;AACxC;AAEA,SAASM,iBAAiBA,CAACjC,KAAsB,EAAmB;EAGlE,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOA,KAAK;EACd;EAGA,IAAIA,KAAK,CAACkC,QAAQ,CAAC,KAAK,CAAC,EAAE;IACzB,IAAMC,OAAO,GAAGC,UAAU,CAACpC,KAAK,CAAC,IAAI,CAAC;IACtC,OAAQmC,OAAO,GAAGE,IAAI,CAACC,EAAE,GAAI,KAAK;EACpC,CAAC,MAAM,IAAItC,KAAK,CAACkC,QAAQ,CAAC,KAAK,CAAC,EAAE;IAChC,OAAOE,UAAU,CAACpC,KAAK,CAAC,IAAI,CAAC;EAC/B,CAAC,MAAM;IACL,OAAOA,KAAK;EACd;AACF;AAAC,IAAAuC,QAAA,GAAAC,OAAA,CAAApI,OAAA,GAEc;EACbqD,GAAG,EAAHA,GAAG;EACH6D,kBAAkB,EAAlBA,kBAAkB;EAClBC,sBAAsB,EAAtBA,sBAAsB;EACtBC,0BAA0B,EAA1BA,0BAA0B;EAC1BE,qBAAqB,EAArBA,qBAAqB;EACrBO,iBAAiB,EAAjBA,iBAAiB;EAGjB,IAAI9G,kBAAkBA,CAAA,EAAuB;IAC3C,IAAI,CAACA,kBAAkB,EAAE;MAEvBA,kBAAkB,GAAG,IAAIsH,2BAAkB,CAGzC/G,iBAAQ,CAACC,EAAE,KAAK,KAAK,GAAG,IAAI,GAAGb,oBACjC,CAAC;IACH;IACA,OAAOK,kBAAkB;EAC3B;AACF,CAAC", "ignoreList": []}