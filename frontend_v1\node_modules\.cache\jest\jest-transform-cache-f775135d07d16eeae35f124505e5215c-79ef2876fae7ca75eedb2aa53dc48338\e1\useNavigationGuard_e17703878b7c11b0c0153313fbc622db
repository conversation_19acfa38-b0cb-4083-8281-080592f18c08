73814cca611382523b6326f5eb5d3a0d
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useNavigationPerformance = exports.useNavigationGuard = exports.useNavigationAnalytics = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _native = require("@react-navigation/native");
var _react = require("react");
var _navigationAnalytics = _interopRequireDefault(require("../services/navigationAnalytics"));
var _navigationGuards = _interopRequireDefault(require("../services/navigationGuards"));
var _authSlice = require("../store/authSlice");
var useNavigationGuard = exports.useNavigationGuard = function useNavigationGuard() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var _options$trackAnalyti = options.trackAnalytics,
    trackAnalytics = _options$trackAnalyti === void 0 ? true : _options$trackAnalyti,
    _options$enforceGuard = options.enforceGuards,
    enforceGuards = _options$enforceGuard === void 0 ? true : _options$enforceGuard,
    _options$logNavigatio = options.logNavigation,
    logNavigation = _options$logNavigatio === void 0 ? __DEV__ : _options$logNavigatio;
  var navigation = (0, _native.useNavigation)();
  var route = (0, _native.useRoute)();
  var _useAuthStore = (0, _authSlice.useAuthStore)(),
    userRole = _useAuthStore.userRole;
  var isNavigatingRef = (0, _react.useRef)(false);
  var screenStartTimeRef = (0, _react.useRef)(0);
  var currentScreenRef = (0, _react.useRef)(route.name);
  (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
    var screenName = route.name;
    var startTime = Date.now();
    screenStartTimeRef.current = startTime;
    currentScreenRef.current = screenName;
    if (trackAnalytics) {
      _navigationAnalytics.default.trackScreenView(screenName, route.params, userRole);
      var loadTime = startTime - (screenStartTimeRef.current || startTime);
      if (loadTime > 0) {
        _navigationAnalytics.default.trackScreenLoadTime(screenName, loadTime);
      }
    }
    if (logNavigation) {
      console.log(`🧭 Navigation: Focused on ${screenName}`, route.params);
    }
    return function () {
      if (trackAnalytics && screenStartTimeRef.current > 0) {
        var timeSpent = Date.now() - screenStartTimeRef.current;
      }
    };
  }, [route.name, route.params, trackAnalytics, userRole, logNavigation]));
  var navigate = (0, _react.useCallback)(function () {
    var _ref = (0, _asyncToGenerator2.default)(function* (routeName, params) {
      if (isNavigatingRef.current) {
        if (logNavigation) {
          console.warn('🧭 Navigation: Already navigating, ignoring request');
        }
        return false;
      }
      isNavigatingRef.current = true;
      try {
        if (enforceGuards) {
          var guardResult = _navigationGuards.default.canNavigate(routeName, params);
          if (!guardResult.allowed) {
            if (logNavigation) {
              console.warn('🧭 Navigation: Blocked by guard', {
                route: routeName,
                reason: guardResult.reason,
                redirectTo: guardResult.redirectTo
              });
            }
            if (guardResult.redirectTo) {
              navigation.reset({
                index: 0,
                routes: [{
                  name: guardResult.redirectTo
                }]
              });
            }
            return false;
          }
        }
        var flowResult = _navigationGuards.default.validateNavigationFlow(currentScreenRef.current, routeName, params);
        if (!flowResult.allowed) {
          if (logNavigation) {
            console.warn('🧭 Navigation: Invalid flow', {
              from: currentScreenRef.current,
              to: routeName,
              reason: flowResult.reason
            });
          }
          return false;
        }
        if (trackAnalytics) {
          _navigationAnalytics.default.trackNavigationAction('button_press', currentScreenRef.current, routeName, params);
        }
        navigation.navigate(routeName, params);
        if (logNavigation) {
          console.log('🧭 Navigation: Success', {
            from: currentScreenRef.current,
            to: routeName,
            params: params
          });
        }
        return true;
      } catch (error) {
        console.error('🧭 Navigation: Error', error);
        if (trackAnalytics) {
          _navigationAnalytics.default.trackNavigationError(error instanceof Error ? error.message : 'Unknown navigation error', routeName, params);
        }
        return false;
      } finally {
        setTimeout(function () {
          isNavigatingRef.current = false;
        }, 100);
      }
    });
    return function (_x, _x2) {
      return _ref.apply(this, arguments);
    };
  }(), [navigation, enforceGuards, trackAnalytics, logNavigation]);
  var goBack = (0, _react.useCallback)(function () {
    if (trackAnalytics) {
      _navigationAnalytics.default.trackNavigationAction('back_button', currentScreenRef.current, 'previous_screen');
    }
    if (logNavigation) {
      console.log('🧭 Navigation: Going back from', currentScreenRef.current);
    }
    navigation.goBack();
  }, [navigation, trackAnalytics, logNavigation]);
  var reset = (0, _react.useCallback)(function (state) {
    if (trackAnalytics) {
      var _state$routes;
      _navigationAnalytics.default.trackNavigationAction('deep_link', currentScreenRef.current, ((_state$routes = state.routes) == null || (_state$routes = _state$routes[state.index]) == null ? void 0 : _state$routes.name) || 'unknown');
    }
    if (logNavigation) {
      console.log('🧭 Navigation: Reset to', state);
    }
    navigation.reset(state);
  }, [navigation, trackAnalytics, logNavigation]);
  var canNavigate = (0, _react.useCallback)(function (routeName, params) {
    return _navigationGuards.default.canNavigate(routeName, params);
  }, []);
  var trackScreenView = (0, _react.useCallback)(function (params) {
    if (trackAnalytics) {
      _navigationAnalytics.default.trackScreenView(route.name, params, userRole);
    }
  }, [route.name, trackAnalytics, userRole]);
  return {
    navigate: navigate,
    goBack: goBack,
    reset: reset,
    canNavigate: canNavigate,
    trackScreenView: trackScreenView,
    isNavigating: isNavigatingRef.current
  };
};
var useNavigationPerformance = exports.useNavigationPerformance = function useNavigationPerformance() {
  var route = (0, _native.useRoute)();
  var startTimeRef = (0, _react.useRef)(Date.now());
  (0, _react.useEffect)(function () {
    startTimeRef.current = Date.now();
  }, [route.name]);
  var trackLoadTime = (0, _react.useCallback)(function (customStartTime) {
    var loadTime = Date.now() - (customStartTime || startTimeRef.current);
    _navigationAnalytics.default.trackScreenLoadTime(route.name, loadTime);
    return loadTime;
  }, [route.name]);
  return {
    trackLoadTime: trackLoadTime,
    getLoadTime: function getLoadTime() {
      return Date.now() - startTimeRef.current;
    }
  };
};
var useNavigationAnalytics = exports.useNavigationAnalytics = function useNavigationAnalytics() {
  var route = (0, _native.useRoute)();
  var _useAuthStore2 = (0, _authSlice.useAuthStore)(),
    userRole = _useAuthStore2.userRole;
  var trackEvent = (0, _react.useCallback)(function (eventType, data) {
    switch (eventType) {
      case 'screen_view':
        _navigationAnalytics.default.trackScreenView(data.screenName, data.params, userRole);
        break;
      case 'navigation_action':
        _navigationAnalytics.default.trackNavigationAction(data.action, data.fromScreen, data.toScreen, data.params);
        break;
      case 'flow_completion':
        _navigationAnalytics.default.trackFlowCompletion(data.flowName, data.success, data.metadata);
        break;
      case 'error':
        _navigationAnalytics.default.trackNavigationError(data.error, data.screenName, data.params);
        break;
    }
  }, [userRole]);
  var getStats = (0, _react.useCallback)(function () {
    return _navigationAnalytics.default.getNavigationStats();
  }, []);
  return {
    trackEvent: trackEvent,
    getStats: getStats,
    currentScreen: route.name
  };
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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