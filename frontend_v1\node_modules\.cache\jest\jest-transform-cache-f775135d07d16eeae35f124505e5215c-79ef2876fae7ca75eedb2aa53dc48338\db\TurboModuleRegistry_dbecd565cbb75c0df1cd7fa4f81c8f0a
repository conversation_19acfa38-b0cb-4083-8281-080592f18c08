a25365398a91eb80c94eed6e69b8ab94
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.get = get;
exports.getEnforcing = getEnforcing;
var _invariant = _interopRequireDefault(require("invariant"));
var NativeModules = require("../BatchedBridge/NativeModules").default;
var turboModuleProxy = global.__turboModuleProxy;
function requireModule(name) {
  if (turboModuleProxy != null) {
    var module = turboModuleProxy(name);
    if (module != null) {
      return module;
    }
  }
  if (global.RN$Bridgeless !== true || global.RN$TurboInterop === true || global.RN$UnifiedNativeModuleProxy === true) {
    var legacyModule = NativeModules[name];
    if (legacyModule != null) {
      return legacyModule;
    }
  }
  return null;
}
function get(name) {
  return requireModule(name);
}
function getEnforcing(name) {
  var module = requireModule(name);
  (0, _invariant.default)(module != null, `TurboModuleRegistry.getEnforcing(...): '${name}' could not be found. ` + 'Verify that a module by this name is registered in the native binary.');
  return module;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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