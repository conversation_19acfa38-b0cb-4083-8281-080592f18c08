0826d947e29d5b125c1fcdc1a457668d
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.allowInterpolationParam = allowInterpolationParam;
exports.allowStyleProp = allowStyleProp;
exports.allowTransformProp = allowTransformProp;
exports.default = void 0;
exports.isSupportedColorStyleProp = isSupportedColorStyleProp;
exports.isSupportedInterpolationParam = isSupportedInterpolationParam;
exports.isSupportedStyleProp = isSupportedStyleProp;
exports.isSupportedTransformProp = isSupportedTransformProp;
var ReactNativeFeatureFlags = _interopRequireWildcard(require("../../src/private/featureflags/ReactNativeFeatureFlags"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var SUPPORTED_COLOR_STYLES = {
  backgroundColor: true,
  borderBottomColor: true,
  borderColor: true,
  borderEndColor: true,
  borderLeftColor: true,
  borderRightColor: true,
  borderStartColor: true,
  borderTopColor: true,
  color: true,
  tintColor: true
};
var SUPPORTED_STYLES = Object.assign({}, SUPPORTED_COLOR_STYLES, {
  borderBottomEndRadius: true,
  borderBottomLeftRadius: true,
  borderBottomRightRadius: true,
  borderBottomStartRadius: true,
  borderEndEndRadius: true,
  borderEndStartRadius: true,
  borderRadius: true,
  borderTopEndRadius: true,
  borderTopLeftRadius: true,
  borderTopRightRadius: true,
  borderTopStartRadius: true,
  borderStartEndRadius: true,
  borderStartStartRadius: true,
  elevation: true,
  opacity: true,
  transform: true,
  zIndex: true,
  shadowOpacity: true,
  shadowRadius: true,
  scaleX: true,
  scaleY: true,
  translateX: true,
  translateY: true
});
var SUPPORTED_TRANSFORMS = Object.assign({
  translateX: true,
  translateY: true,
  scale: true,
  scaleX: true,
  scaleY: true,
  rotate: true,
  rotateX: true,
  rotateY: true,
  rotateZ: true,
  perspective: true,
  skewX: true,
  skewY: true
}, ReactNativeFeatureFlags.shouldUseAnimatedObjectForTransform() ? {
  matrix: true
} : {});
var SUPPORTED_INTERPOLATION_PARAMS = {
  inputRange: true,
  outputRange: true,
  extrapolate: true,
  extrapolateRight: true,
  extrapolateLeft: true
};
var _default = exports.default = {
  style: SUPPORTED_STYLES
};
function allowInterpolationParam(param) {
  SUPPORTED_INTERPOLATION_PARAMS[param] = true;
}
function allowStyleProp(prop) {
  SUPPORTED_STYLES[prop] = true;
}
function allowTransformProp(prop) {
  SUPPORTED_TRANSFORMS[prop] = true;
}
function isSupportedColorStyleProp(prop) {
  return SUPPORTED_COLOR_STYLES.hasOwnProperty(prop);
}
function isSupportedInterpolationParam(param) {
  return SUPPORTED_INTERPOLATION_PARAMS.hasOwnProperty(param);
}
function isSupportedStyleProp(prop) {
  return SUPPORTED_STYLES.hasOwnProperty(prop);
}
function isSupportedTransformProp(prop) {
  return SUPPORTED_TRANSFORMS.hasOwnProperty(prop);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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