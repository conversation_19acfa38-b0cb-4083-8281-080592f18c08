df090af545fbe24896d840a82b4076fb
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useSafeAuthStore = exports.useAuthStore = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _asyncStorage = _interopRequireDefault(require("@react-native-async-storage/async-storage"));
var _zustand = require("zustand");
var _middleware = require("zustand/middleware");
var initialState = {
  authToken: null,
  refreshToken: null,
  user: null,
  userRole: null,
  status: 'idle',
  error: null,
  tokenExpiresAt: null,
  isAuthenticated: false
};
var createAuthStore = function createAuthStore() {
  try {
    return (0, _zustand.create)()((0, _middleware.devtools)((0, _middleware.persist)(function (set, get) {
      return Object.assign({}, initialState, {
        isAuthenticated: false,
        loginStart: function loginStart() {
          return set(function (state) {
            return Object.assign({}, state, {
              status: 'loading',
              error: null
            });
          }, false, 'auth/loginStart');
        },
        loginSuccess: function loginSuccess(token, refreshToken, user) {
          return set(function (state) {
            return Object.assign({}, state, {
              authToken: token,
              refreshToken: refreshToken,
              user: user,
              userRole: user.role,
              status: 'success',
              error: null,
              isAuthenticated: true,
              tokenExpiresAt: Date.now() + 30 * 60 * 1000
            });
          }, false, 'auth/loginSuccess');
        },
        loginFailure: function loginFailure(error) {
          return set(function (state) {
            return Object.assign({}, state, {
              authToken: null,
              userRole: null,
              status: 'error',
              error: error,
              isAuthenticated: false
            });
          }, false, 'auth/loginFailure');
        },
        registerStart: function registerStart() {
          return set(function (state) {
            return Object.assign({}, state, {
              status: 'loading',
              error: null
            });
          }, false, 'auth/registerStart');
        },
        registerSuccess: function registerSuccess(token, refreshToken, user) {
          return set(function (state) {
            return Object.assign({}, state, {
              authToken: token,
              refreshToken: refreshToken,
              user: user,
              userRole: user.role,
              status: 'success',
              error: null,
              isAuthenticated: true,
              tokenExpiresAt: Date.now() + 30 * 60 * 1000
            });
          }, false, 'auth/registerSuccess');
        },
        registerFailure: function registerFailure(error) {
          return set(function (state) {
            return Object.assign({}, state, {
              authToken: null,
              refreshToken: null,
              user: null,
              userRole: null,
              status: 'error',
              error: error,
              isAuthenticated: false,
              tokenExpiresAt: null
            });
          }, false, 'auth/registerFailure');
        },
        updateProfile: function updateProfile(userUpdates) {
          return set(function (state) {
            return Object.assign({}, state, {
              user: state.user ? Object.assign({}, state.user, userUpdates) : null
            });
          }, false, 'auth/updateProfile');
        },
        updateTokens: function updateTokens(token, refreshToken) {
          return set(function (state) {
            return Object.assign({}, state, {
              authToken: token,
              refreshToken: refreshToken || state.refreshToken,
              tokenExpiresAt: Date.now() + 30 * 60 * 1000
            });
          }, false, 'auth/updateTokens');
        },
        logout: function logout() {
          return set(function () {
            return Object.assign({}, initialState);
          }, false, 'auth/logout');
        },
        reset: function reset() {
          return set(function () {
            return Object.assign({}, initialState);
          }, false, 'auth/reset');
        },
        checkAuthStatus: function () {
          var _checkAuthStatus = (0, _asyncToGenerator2.default)(function* () {
            try {
              var currentState = get();
              if (currentState.isAuthenticated && currentState.authToken && currentState.tokenExpiresAt) {
                if (Date.now() < currentState.tokenExpiresAt) {
                  return;
                }
              }
              var storedToken = yield _asyncStorage.default.getItem('auth_token');
              var storedRefreshToken = yield _asyncStorage.default.getItem('refresh_token');
              var storedUser = yield _asyncStorage.default.getItem('auth_user');
              if (storedToken && storedUser) {
                try {
                  var user = JSON.parse(storedUser);
                  set(function (state) {
                    return Object.assign({}, state, {
                      authToken: storedToken,
                      refreshToken: storedRefreshToken,
                      user: user,
                      userRole: user.role,
                      isAuthenticated: true,
                      status: 'success',
                      error: null
                    });
                  }, false, 'auth/checkAuthStatus');
                } catch (parseError) {
                  console.error('Failed to parse stored user data:', parseError);
                  yield _asyncStorage.default.multiRemove(['auth_token', 'refresh_token', 'auth_user']);
                  set(function () {
                    return initialState;
                  }, false, 'auth/checkAuthStatusError');
                }
              } else {
                set(function () {
                  return initialState;
                }, false, 'auth/checkAuthStatus');
              }
            } catch (error) {
              console.error('Auth status check failed:', error);
              set(function () {
                return initialState;
              }, false, 'auth/checkAuthStatusError');
            }
          });
          function checkAuthStatus() {
            return _checkAuthStatus.apply(this, arguments);
          }
          return checkAuthStatus;
        }(),
        validateToken: function () {
          var _validateToken = (0, _asyncToGenerator2.default)(function* () {
            try {
              var currentState = get();
              if (!currentState.authToken) {
                return false;
              }
              if (currentState.tokenExpiresAt && Date.now() >= currentState.tokenExpiresAt) {
                if (currentState.refreshToken) {
                  try {
                    var _yield$import = yield import("../services/authService"),
                      authService = _yield$import.authService;
                    var response = yield authService.refreshToken(currentState.refreshToken);
                    set(function (state) {
                      return Object.assign({}, state, {
                        authToken: response.access,
                        tokenExpiresAt: Date.now() + 30 * 60 * 1000
                      });
                    }, false, 'auth/tokenRefreshed');
                    yield _asyncStorage.default.setItem('auth_token', response.access);
                    return true;
                  } catch (refreshError) {
                    console.error('Token refresh failed:', refreshError);
                    set(function () {
                      return initialState;
                    }, false, 'auth/tokenExpired');
                    yield _asyncStorage.default.multiRemove(['auth_token', 'refresh_token', 'auth_user']);
                    return false;
                  }
                } else {
                  set(function () {
                    return initialState;
                  }, false, 'auth/tokenExpired');
                  yield _asyncStorage.default.multiRemove(['auth_token', 'refresh_token', 'auth_user']);
                  return false;
                }
              }
              return true;
            } catch (error) {
              console.error('Token validation failed:', error);
              return false;
            }
          });
          function validateToken() {
            return _validateToken.apply(this, arguments);
          }
          return validateToken;
        }()
      });
    }, {
      name: 'auth-store',
      storage: {
        getItem: function () {
          var _getItem = (0, _asyncToGenerator2.default)(function* (name) {
            try {
              var value = yield _asyncStorage.default.getItem(name);
              if (value) {
                var parsed = JSON.parse(value);
                if (parsed && typeof parsed === 'object' && 'loginSuccess' in parsed) {
                  console.log('🔄 Clearing old auth store data due to structure change');
                  yield _asyncStorage.default.removeItem(name);
                  return null;
                }
                return parsed;
              }
              return null;
            } catch (error) {
              console.error('Failed to load auth state:', error);
              try {
                yield _asyncStorage.default.removeItem(name);
              } catch (_unused) {}
              return null;
            }
          });
          function getItem(_x) {
            return _getItem.apply(this, arguments);
          }
          return getItem;
        }(),
        setItem: function () {
          var _setItem = (0, _asyncToGenerator2.default)(function* (name, value) {
            try {
              yield _asyncStorage.default.setItem(name, JSON.stringify(value));
            } catch (error) {
              console.error('Failed to save auth state:', error);
            }
          });
          function setItem(_x2, _x3) {
            return _setItem.apply(this, arguments);
          }
          return setItem;
        }(),
        removeItem: function () {
          var _removeItem = (0, _asyncToGenerator2.default)(function* (name) {
            try {
              yield _asyncStorage.default.removeItem(name);
            } catch (error) {
              console.error('Failed to remove auth state:', error);
            }
          });
          function removeItem(_x4) {
            return _removeItem.apply(this, arguments);
          }
          return removeItem;
        }()
      },
      partialize: function partialize(state) {
        return {
          authToken: state.authToken,
          refreshToken: state.refreshToken,
          user: state.user,
          userRole: state.userRole,
          isAuthenticated: state.isAuthenticated,
          tokenExpiresAt: state.tokenExpiresAt,
          _version: '2.0.0'
        };
      },
      migrate: function migrate(persistedState, version) {
        if (!persistedState || !persistedState._version || persistedState._version !== '2.0.0') {
          console.log('🔄 Migrating auth store to new version, clearing old data');
          return initialState;
        }
        return persistedState;
      },
      version: 1
    }, {
      name: 'auth-store'
    })));
  } catch (error) {
    console.error('Failed to create auth store:', error);
    return (0, _zustand.create)()(function () {
      return Object.assign({}, initialState, {
        isAuthenticated: false,
        loginStart: function loginStart() {},
        loginSuccess: function loginSuccess() {},
        loginFailure: function loginFailure() {},
        registerStart: function registerStart() {},
        registerSuccess: function registerSuccess() {},
        registerFailure: function registerFailure() {},
        updateProfile: function updateProfile() {},
        updateTokens: function updateTokens() {},
        logout: function logout() {},
        reset: function reset() {},
        checkAuthStatus: function () {
          var _checkAuthStatus2 = (0, _asyncToGenerator2.default)(function* () {});
          function checkAuthStatus() {
            return _checkAuthStatus2.apply(this, arguments);
          }
          return checkAuthStatus;
        }(),
        validateToken: function () {
          var _validateToken2 = (0, _asyncToGenerator2.default)(function* () {
            return false;
          });
          function validateToken() {
            return _validateToken2.apply(this, arguments);
          }
          return validateToken;
        }()
      });
    });
  }
};
var useAuthStore = exports.useAuthStore = createAuthStore();
var useSafeAuthStore = exports.useSafeAuthStore = function useSafeAuthStore() {
  try {
    var store = useAuthStore();
    if (!store || typeof store !== 'object') {
      console.warn('Auth store is not properly initialized');
      return Object.assign({}, initialState, {
        isAuthenticated: false,
        loginStart: function loginStart() {},
        loginSuccess: function loginSuccess() {},
        loginFailure: function loginFailure() {},
        registerStart: function registerStart() {},
        registerSuccess: function registerSuccess() {},
        registerFailure: function registerFailure() {},
        updateProfile: function updateProfile() {},
        updateTokens: function updateTokens() {},
        logout: function logout() {},
        reset: function reset() {},
        checkAuthStatus: function () {
          var _checkAuthStatus3 = (0, _asyncToGenerator2.default)(function* () {});
          function checkAuthStatus() {
            return _checkAuthStatus3.apply(this, arguments);
          }
          return checkAuthStatus;
        }(),
        validateToken: function () {
          var _validateToken3 = (0, _asyncToGenerator2.default)(function* () {
            return false;
          });
          function validateToken() {
            return _validateToken3.apply(this, arguments);
          }
          return validateToken;
        }()
      });
    }
    return store;
  } catch (error) {
    console.error('Error accessing auth store:', error);
    return Object.assign({}, initialState, {
      isAuthenticated: false,
      loginStart: function loginStart() {},
      loginSuccess: function loginSuccess() {},
      loginFailure: function loginFailure() {},
      registerStart: function registerStart() {},
      registerSuccess: function registerSuccess() {},
      registerFailure: function registerFailure() {},
      updateProfile: function updateProfile() {},
      updateTokens: function updateTokens() {},
      logout: function logout() {},
      reset: function reset() {},
      checkAuthStatus: function () {
        var _checkAuthStatus4 = (0, _asyncToGenerator2.default)(function* () {});
        function checkAuthStatus() {
          return _checkAuthStatus4.apply(this, arguments);
        }
        return checkAuthStatus;
      }(),
      validateToken: function () {
        var _validateToken4 = (0, _asyncToGenerator2.default)(function* () {
          return false;
        });
        function validateToken() {
          return _validateToken4.apply(this, arguments);
        }
        return validateToken;
      }()
    });
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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