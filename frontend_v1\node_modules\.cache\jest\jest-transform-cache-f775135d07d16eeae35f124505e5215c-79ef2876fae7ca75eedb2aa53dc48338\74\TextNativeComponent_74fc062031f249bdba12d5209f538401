6292f38665b014ac1be20bfe4b89b4c7
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.NativeVirtualText = exports.NativeText = void 0;
var _ViewConfig = require("../NativeComponent/ViewConfig");
var _UIManager = _interopRequireDefault(require("../ReactNative/UIManager"));
var _createReactNativeComponentClass = _interopRequireDefault(require("../Renderer/shims/createReactNativeComponentClass"));
var textViewConfig = {
  validAttributes: {
    isHighlighted: true,
    isPressable: true,
    numberOfLines: true,
    ellipsizeMode: true,
    allowFontScaling: true,
    dynamicTypeRamp: true,
    maxFontSizeMultiplier: true,
    disabled: true,
    selectable: true,
    selectionColor: true,
    adjustsFontSizeToFit: true,
    minimumFontScale: true,
    textBreakStrategy: true,
    onTextLayout: true,
    onInlineViewLayout: true,
    dataDetectorType: true,
    android_hyphenationFrequency: true,
    lineBreakStrategyIOS: true
  },
  directEventTypes: {
    topTextLayout: {
      registrationName: 'onTextLayout'
    },
    topInlineViewLayout: {
      registrationName: 'onInlineViewLayout'
    }
  },
  uiViewClassName: 'RCTText'
};
var virtualTextViewConfig = {
  validAttributes: {
    isHighlighted: true,
    isPressable: true,
    maxFontSizeMultiplier: true
  },
  uiViewClassName: 'RCTVirtualText'
};
var NativeText = exports.NativeText = (0, _createReactNativeComponentClass.default)('RCTText', function () {
  return (0, _ViewConfig.createViewConfig)(textViewConfig);
});
var NativeVirtualText = exports.NativeVirtualText = !global.RN$Bridgeless && !_UIManager.default.hasViewManagerConfig('RCTVirtualText') ? NativeText : (0, _createReactNativeComponentClass.default)('RCTVirtualText', function () {
  return (0, _ViewConfig.createViewConfig)(virtualTextViewConfig);
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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