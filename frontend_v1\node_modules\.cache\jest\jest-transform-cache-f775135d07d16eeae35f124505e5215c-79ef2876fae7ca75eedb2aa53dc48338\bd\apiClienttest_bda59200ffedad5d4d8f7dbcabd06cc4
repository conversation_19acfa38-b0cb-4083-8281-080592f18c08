8bc5e1f5df68b10a9310452dd928a28b
_getJestObj().mock('@react-native-async-storage/async-storage', function () {
  return {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn()
  };
});
_getJestObj().mock("../../store/authSlice", function () {
  return {
    useAuthStore: {
      getState: jest.fn(function () {
        return {
          loginSuccess: jest.fn(),
          logout: jest.fn()
        };
      })
    }
  };
});
_getJestObj().mock("../performanceCacheService", function () {
  return {
    performanceCacheService: {
      get: jest.fn(),
      set: jest.fn(),
      clear: jest.fn()
    }
  };
});
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _asyncStorage = _interopRequireDefault(require("@react-native-async-storage/async-storage"));
var _apiClient = require("../apiClient");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
global.fetch = jest.fn();
describe('ApiClient', function () {
  beforeEach(function () {
    jest.clearAllMocks();
    fetch.mockClear();
    _asyncStorage.default.getItem.mockClear();
    _asyncStorage.default.setItem.mockClear();
    _asyncStorage.default.removeItem.mockClear();
  });
  describe('Authentication Token Management', function () {
    it('should load auth token from AsyncStorage on initialization', (0, _asyncToGenerator2.default)(function* () {
      var mockToken = 'test-token-123';
      _asyncStorage.default.getItem.mockResolvedValue(mockToken);
      var client = new (require("../apiClient").ApiClient)();
      yield new Promise(function (resolve) {
        return setTimeout(resolve, 0);
      });
      expect(_asyncStorage.default.getItem).toHaveBeenCalledWith('auth_token');
    }));
    it('should set auth token and save to AsyncStorage', function () {
      var testToken = 'new-test-token';
      _apiClient.apiClient.setAuthToken(testToken);
      expect(_asyncStorage.default.setItem).toHaveBeenCalledWith('auth_token', testToken);
    });
    it('should remove auth token from AsyncStorage when set to null', function () {
      _apiClient.apiClient.setAuthToken(null);
      expect(_asyncStorage.default.removeItem).toHaveBeenCalledWith('auth_token');
    });
  });
  describe('HTTP Request Methods', function () {
    beforeEach(function () {
      fetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([['content-type', 'application/json']]),
        json: jest.fn().mockResolvedValue({
          data: 'test'
        })
      });
    });
    it('should make GET request with correct parameters', (0, _asyncToGenerator2.default)(function* () {
      var testUrl = '/api/test';
      var testParams = {
        page: 1,
        limit: 10
      };
      yield _apiClient.apiClient.get(testUrl, {
        params: testParams
      });
      expect(fetch).toHaveBeenCalledWith(expect.stringContaining('page=1&limit=10'), expect.objectContaining({
        method: 'GET',
        headers: expect.objectContaining({
          'Content-Type': 'application/json',
          Accept: 'application/json'
        })
      }));
    }));
    it('should make POST request with data', (0, _asyncToGenerator2.default)(function* () {
      var testUrl = '/api/test';
      var testData = {
        name: 'test',
        value: 123
      };
      yield _apiClient.apiClient.post(testUrl, testData);
      expect(fetch).toHaveBeenCalledWith(expect.stringContaining(testUrl), expect.objectContaining({
        method: 'POST',
        headers: expect.objectContaining({
          'Content-Type': 'application/json',
          Accept: 'application/json'
        }),
        body: JSON.stringify(testData)
      }));
    }));
    it('should include auth token in headers when available', (0, _asyncToGenerator2.default)(function* () {
      var testToken = 'bearer-token-123';
      _apiClient.apiClient.setAuthToken(testToken);
      yield _apiClient.apiClient.get('/api/protected');
      expect(fetch).toHaveBeenCalledWith(expect.any(String), expect.objectContaining({
        headers: expect.objectContaining({
          Authorization: `Bearer ${testToken}`
        })
      }));
    }));
  });
  describe('Error Handling', function () {
    it('should handle network errors', (0, _asyncToGenerator2.default)(function* () {
      fetch.mockRejectedValue(new Error('Network error'));
      yield expect(_apiClient.apiClient.get('/api/test')).rejects.toThrow('Network error');
    }));
    it('should handle HTTP error responses', (0, _asyncToGenerator2.default)(function* () {
      fetch.mockResolvedValue({
        ok: false,
        status: 404,
        statusText: 'Not Found',
        json: jest.fn().mockResolvedValue({
          detail: 'Resource not found'
        })
      });
      yield expect(_apiClient.apiClient.get('/api/nonexistent')).rejects.toThrow();
    }));
    it('should handle 401 unauthorized and attempt token refresh', (0, _asyncToGenerator2.default)(function* () {
      _asyncStorage.default.getItem.mockResolvedValueOnce('refresh-token-123').mockResolvedValueOnce(null);
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        json: jest.fn().mockResolvedValue({
          detail: 'Token expired'
        })
      }).mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValue({
          access: 'new-token-123'
        })
      }).mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValue({
          data: 'success'
        })
      });
      var result = yield _apiClient.apiClient.get('/api/protected');
      expect(fetch).toHaveBeenCalledTimes(3);
      expect(result.data).toEqual({
        data: 'success'
      });
    }));
  });
  describe('Request Configuration', function () {
    it('should respect custom timeout', (0, _asyncToGenerator2.default)(function* () {
      var customTimeout = 5000;
      yield _apiClient.apiClient.get('/api/test', {
        timeout: customTimeout
      });
      expect(fetch).toHaveBeenCalled();
    }));
    it('should handle custom headers', (0, _asyncToGenerator2.default)(function* () {
      var customHeaders = {
        'X-Custom-Header': 'test-value'
      };
      yield _apiClient.apiClient.get('/api/test', {
        headers: customHeaders
      });
      expect(fetch).toHaveBeenCalledWith(expect.any(String), expect.objectContaining({
        headers: expect.objectContaining(customHeaders)
      }));
    }));
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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