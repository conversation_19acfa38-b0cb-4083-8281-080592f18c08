{"version": 3, "names": ["ValidationRules", "exports", "required", "value", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "String", "trim", "length", "error", "email", "emailRegex", "test", "phone", "cleanPhone", "replace", "phoneRegex", "password", "<PERSON><PERSON><PERSON><PERSON>", "hasUpperCase", "hasLowerCase", "hasNumbers", "hasSpecialChar", "name", "nameRegex", "url", "URL", "_unused", "number", "isNaN", "Number", "isFinite", "date", "Date", "getTime", "max<PERSON><PERSON><PERSON>", "pattern", "errorMessage", "confirmPassword", "originalPassword", "validateField", "validation", "formData", "requiredResult", "emailResult", "phoneResult", "passwordResult", "nameResult", "urlResult", "numberResult", "dateResult", "minLengthResult", "max<PERSON>engthResult", "patternResult", "confirmResult", "custom", "customResult", "validateForm", "validationConfig", "errors", "Object", "keys", "for<PERSON>ach", "fieldName", "fieldValue", "fieldValidation", "result", "createDebouncedValidator", "validationFn", "delay", "arguments", "timeoutId", "clearTimeout", "setTimeout", "CommonValidations", "passwordFieldName", "optional", "FormValidator", "_classCallCheck2", "default", "touched", "_createClass2", "key", "setFieldTouched", "getFieldError", "getErrors", "isFieldTouched", "reset"], "sources": ["formValidation.ts"], "sourcesContent": ["/**\n * Form Validation Utilities\n *\n * Utility Functions:\n * - Comprehensive validation rules for common form fields\n * - Custom validation function support\n * - Form-level validation with error aggregation\n * - Real-time validation with debouncing\n * - Accessibility-friendly error messages\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\n// Validation result interface\nexport interface ValidationResult {\n  isValid: boolean;\n  error?: string;\n}\n\n// Form validation configuration\nexport interface FieldValidation {\n  required?: boolean;\n  minLength?: number;\n  maxLength?: number;\n  pattern?: RegExp;\n  custom?: (value: any) => ValidationResult;\n  email?: boolean;\n  phone?: boolean;\n  password?: boolean;\n  confirmPassword?: string; // Field name to match against\n  name?: boolean;\n  url?: boolean;\n  number?: boolean;\n  date?: boolean;\n}\n\nexport interface FormValidationConfig {\n  [fieldName: string]: FieldValidation;\n}\n\nexport interface FormValidationErrors {\n  [fieldName: string]: string;\n}\n\n// Built-in validation functions\nexport const ValidationRules = {\n  /**\n   * Required field validation\n   */\n  required: (value: any): ValidationResult => {\n    const isValid =\n      value !== null && value !== undefined && String(value).trim().length > 0;\n    return {\n      isValid,\n      error: isValid ? undefined : 'Please fill in this field to continue',\n    };\n  },\n\n  /**\n   * Email validation\n   */\n  email: (value: string): ValidationResult => {\n    if (!value) return { isValid: true }; // Allow empty if not required\n\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    const isValid = emailRegex.test(value);\n\n    return {\n      isValid,\n      error: isValid\n        ? undefined\n        : 'Please enter your email in <NAME_EMAIL>',\n    };\n  },\n\n  /**\n   * Phone number validation\n   */\n  phone: (value: string): ValidationResult => {\n    if (!value) return { isValid: true }; // Allow empty if not required\n\n    // Remove formatting characters\n    const cleanPhone = value.replace(/[\\s\\-\\(\\)]/g, '');\n    const phoneRegex = /^[\\+]?[1-9][\\d]{9,14}$/;\n    const isValid = phoneRegex.test(cleanPhone);\n\n    return {\n      isValid,\n      error: isValid\n        ? undefined\n        : 'Please enter your phone number with area code (e.g., ************)',\n    };\n  },\n\n  /**\n   * Password validation\n   */\n  password: (value: string): ValidationResult => {\n    if (!value) return { isValid: true }; // Allow empty if not required\n\n    const minLength = 8;\n    const hasUpperCase = /[A-Z]/.test(value);\n    const hasLowerCase = /[a-z]/.test(value);\n    const hasNumbers = /\\d/.test(value);\n    const hasSpecialChar = /[!@#$%^&*(),.?\":{}|<>]/.test(value);\n\n    if (value.length < minLength) {\n      return {\n        isValid: false,\n        error: `Your password needs at least ${minLength} characters. Please add ${minLength - value.length} more character${minLength - value.length > 1 ? 's' : ''}.`,\n      };\n    }\n\n    if (!hasUpperCase) {\n      return {\n        isValid: false,\n        error:\n          'Your password needs at least one uppercase letter (A-Z). Please add one to continue.',\n      };\n    }\n\n    if (!hasLowerCase) {\n      return {\n        isValid: false,\n        error:\n          'Your password needs at least one lowercase letter (a-z). Please add one to continue.',\n      };\n    }\n\n    if (!hasNumbers) {\n      return {\n        isValid: false,\n        error:\n          'Your password needs at least one number (0-9). Please add one to continue.',\n      };\n    }\n\n    return { isValid: true };\n  },\n\n  /**\n   * Name validation\n   */\n  name: (value: string): ValidationResult => {\n    if (!value) return { isValid: true }; // Allow empty if not required\n\n    const nameRegex = /^[a-zA-Z\\s\\-']{2,50}$/;\n    const isValid = nameRegex.test(value.trim());\n\n    return {\n      isValid,\n      error: isValid\n        ? undefined\n        : 'Please enter your name using only letters, spaces, hyphens, and apostrophes (2-50 characters)',\n    };\n  },\n\n  /**\n   * URL validation\n   */\n  url: (value: string): ValidationResult => {\n    if (!value) return { isValid: true }; // Allow empty if not required\n\n    try {\n      new URL(value);\n      return { isValid: true };\n    } catch {\n      return {\n        isValid: false,\n        error: 'Please enter a valid URL',\n      };\n    }\n  },\n\n  /**\n   * Number validation\n   */\n  number: (value: string): ValidationResult => {\n    if (!value) return { isValid: true }; // Allow empty if not required\n\n    const isValid = !isNaN(Number(value)) && isFinite(Number(value));\n\n    return {\n      isValid,\n      error: isValid ? undefined : 'Please enter a valid number',\n    };\n  },\n\n  /**\n   * Date validation\n   */\n  date: (value: string): ValidationResult => {\n    if (!value) return { isValid: true }; // Allow empty if not required\n\n    const date = new Date(value);\n    const isValid = date instanceof Date && !isNaN(date.getTime());\n\n    return {\n      isValid,\n      error: isValid ? undefined : 'Please enter a valid date',\n    };\n  },\n\n  /**\n   * Minimum length validation\n   */\n  minLength: (value: string, minLength: number): ValidationResult => {\n    if (!value) return { isValid: true }; // Allow empty if not required\n\n    const isValid = value.length >= minLength;\n\n    return {\n      isValid,\n      error: isValid\n        ? undefined\n        : `Must be at least ${minLength} characters long`,\n    };\n  },\n\n  /**\n   * Maximum length validation\n   */\n  maxLength: (value: string, maxLength: number): ValidationResult => {\n    if (!value) return { isValid: true }; // Allow empty if not required\n\n    const isValid = value.length <= maxLength;\n\n    return {\n      isValid,\n      error: isValid\n        ? undefined\n        : `Must be no more than ${maxLength} characters long`,\n    };\n  },\n\n  /**\n   * Pattern validation\n   */\n  pattern: (\n    value: string,\n    pattern: RegExp,\n    errorMessage?: string,\n  ): ValidationResult => {\n    if (!value) return { isValid: true }; // Allow empty if not required\n\n    const isValid = pattern.test(value);\n\n    return {\n      isValid,\n      error: isValid ? undefined : errorMessage || 'Invalid format',\n    };\n  },\n\n  /**\n   * Confirm password validation\n   */\n  confirmPassword: (\n    value: string,\n    originalPassword: string,\n  ): ValidationResult => {\n    const isValid = value === originalPassword;\n\n    return {\n      isValid,\n      error: isValid ? undefined : 'Passwords do not match',\n    };\n  },\n};\n\n/**\n * Validate a single field\n */\nexport const validateField = (\n  value: any,\n  validation: FieldValidation,\n  formData?: Record<string, any>,\n): ValidationResult => {\n  // Required validation\n  if (validation.required) {\n    const requiredResult = ValidationRules.required(value);\n    if (!requiredResult.isValid) {\n      return requiredResult;\n    }\n  }\n\n  // Skip other validations if value is empty and not required\n  if (!value && !validation.required) {\n    return { isValid: true };\n  }\n\n  // Email validation\n  if (validation.email) {\n    const emailResult = ValidationRules.email(value);\n    if (!emailResult.isValid) {\n      return emailResult;\n    }\n  }\n\n  // Phone validation\n  if (validation.phone) {\n    const phoneResult = ValidationRules.phone(value);\n    if (!phoneResult.isValid) {\n      return phoneResult;\n    }\n  }\n\n  // Password validation\n  if (validation.password) {\n    const passwordResult = ValidationRules.password(value);\n    if (!passwordResult.isValid) {\n      return passwordResult;\n    }\n  }\n\n  // Name validation\n  if (validation.name) {\n    const nameResult = ValidationRules.name(value);\n    if (!nameResult.isValid) {\n      return nameResult;\n    }\n  }\n\n  // URL validation\n  if (validation.url) {\n    const urlResult = ValidationRules.url(value);\n    if (!urlResult.isValid) {\n      return urlResult;\n    }\n  }\n\n  // Number validation\n  if (validation.number) {\n    const numberResult = ValidationRules.number(value);\n    if (!numberResult.isValid) {\n      return numberResult;\n    }\n  }\n\n  // Date validation\n  if (validation.date) {\n    const dateResult = ValidationRules.date(value);\n    if (!dateResult.isValid) {\n      return dateResult;\n    }\n  }\n\n  // Min length validation\n  if (validation.minLength !== undefined) {\n    const minLengthResult = ValidationRules.minLength(\n      value,\n      validation.minLength,\n    );\n    if (!minLengthResult.isValid) {\n      return minLengthResult;\n    }\n  }\n\n  // Max length validation\n  if (validation.maxLength !== undefined) {\n    const maxLengthResult = ValidationRules.maxLength(\n      value,\n      validation.maxLength,\n    );\n    if (!maxLengthResult.isValid) {\n      return maxLengthResult;\n    }\n  }\n\n  // Pattern validation\n  if (validation.pattern) {\n    const patternResult = ValidationRules.pattern(value, validation.pattern);\n    if (!patternResult.isValid) {\n      return patternResult;\n    }\n  }\n\n  // Confirm password validation\n  if (validation.confirmPassword && formData) {\n    const originalPassword = formData[validation.confirmPassword];\n    const confirmResult = ValidationRules.confirmPassword(\n      value,\n      originalPassword,\n    );\n    if (!confirmResult.isValid) {\n      return confirmResult;\n    }\n  }\n\n  // Custom validation\n  if (validation.custom) {\n    const customResult = validation.custom(value);\n    if (!customResult.isValid) {\n      return customResult;\n    }\n  }\n\n  return { isValid: true };\n};\n\n/**\n * Validate entire form\n */\nexport const validateForm = (\n  formData: Record<string, any>,\n  validationConfig: FormValidationConfig,\n): { isValid: boolean; errors: FormValidationErrors } => {\n  const errors: FormValidationErrors = {};\n  let isValid = true;\n\n  Object.keys(validationConfig).forEach(fieldName => {\n    const fieldValue = formData[fieldName];\n    const fieldValidation = validationConfig[fieldName];\n\n    const result = validateField(fieldValue, fieldValidation, formData);\n\n    if (!result.isValid) {\n      errors[fieldName] = result.error!;\n      isValid = false;\n    }\n  });\n\n  return { isValid, errors };\n};\n\n/**\n * Create debounced validation function\n */\nexport const createDebouncedValidator = (\n  validationFn: () => void,\n  delay: number = 300,\n) => {\n  let timeoutId: NodeJS.Timeout;\n\n  return () => {\n    clearTimeout(timeoutId);\n    timeoutId = setTimeout(validationFn, delay);\n  };\n};\n\n/**\n * Common validation configurations\n */\nexport const CommonValidations = {\n  email: {\n    required: true,\n    email: true,\n  } as FieldValidation,\n\n  password: {\n    required: true,\n    password: true,\n    minLength: 8,\n  } as FieldValidation,\n\n  confirmPassword: (passwordFieldName: string) =>\n    ({\n      required: true,\n      confirmPassword: passwordFieldName,\n    }) as FieldValidation,\n\n  name: {\n    required: true,\n    name: true,\n    minLength: 2,\n    maxLength: 50,\n  } as FieldValidation,\n\n  phone: {\n    required: true,\n    phone: true,\n  } as FieldValidation,\n\n  required: {\n    required: true,\n  } as FieldValidation,\n\n  optional: {} as FieldValidation,\n};\n\n/**\n * Form validation hook-like utility\n */\nexport class FormValidator {\n  private validationConfig: FormValidationConfig;\n  private errors: FormValidationErrors = {};\n  private touched: Record<string, boolean> = {};\n\n  constructor(validationConfig: FormValidationConfig) {\n    this.validationConfig = validationConfig;\n  }\n\n  validateField(fieldName: string, value: any, formData: Record<string, any>) {\n    const result = validateField(\n      value,\n      this.validationConfig[fieldName],\n      formData,\n    );\n\n    if (result.isValid) {\n      delete this.errors[fieldName];\n    } else {\n      this.errors[fieldName] = result.error!;\n    }\n\n    return result;\n  }\n\n  validateForm(formData: Record<string, any>) {\n    const result = validateForm(formData, this.validationConfig);\n    this.errors = result.errors;\n    return result;\n  }\n\n  setFieldTouched(fieldName: string, touched: boolean = true) {\n    this.touched[fieldName] = touched;\n  }\n\n  getFieldError(fieldName: string): string | undefined {\n    return this.touched[fieldName] ? this.errors[fieldName] : undefined;\n  }\n\n  getErrors(): FormValidationErrors {\n    return this.errors;\n  }\n\n  isFieldTouched(fieldName: string): boolean {\n    return this.touched[fieldName] || false;\n  }\n\n  reset() {\n    this.errors = {};\n    this.touched = {};\n  }\n}\n"], "mappings": ";;;;;;;AA8CO,IAAMA,eAAe,GAAAC,OAAA,CAAAD,eAAA,GAAG;EAI7BE,QAAQ,EAAE,SAAVA,QAAQA,CAAGC,KAAU,EAAuB;IAC1C,IAAMC,OAAO,GACXD,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKE,SAAS,IAAIC,MAAM,CAACH,KAAK,CAAC,CAACI,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC;IAC1E,OAAO;MACLJ,OAAO,EAAPA,OAAO;MACPK,KAAK,EAAEL,OAAO,GAAGC,SAAS,GAAG;IAC/B,CAAC;EACH,CAAC;EAKDK,KAAK,EAAE,SAAPA,KAAKA,CAAGP,KAAa,EAAuB;IAC1C,IAAI,CAACA,KAAK,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAK,CAAC;IAEpC,IAAMO,UAAU,GAAG,4BAA4B;IAC/C,IAAMP,OAAO,GAAGO,UAAU,CAACC,IAAI,CAACT,KAAK,CAAC;IAEtC,OAAO;MACLC,OAAO,EAAPA,OAAO;MACPK,KAAK,EAAEL,OAAO,GACVC,SAAS,GACT;IACN,CAAC;EACH,CAAC;EAKDQ,KAAK,EAAE,SAAPA,KAAKA,CAAGV,KAAa,EAAuB;IAC1C,IAAI,CAACA,KAAK,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAK,CAAC;IAGpC,IAAMU,UAAU,GAAGX,KAAK,CAACY,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;IACnD,IAAMC,UAAU,GAAG,wBAAwB;IAC3C,IAAMZ,OAAO,GAAGY,UAAU,CAACJ,IAAI,CAACE,UAAU,CAAC;IAE3C,OAAO;MACLV,OAAO,EAAPA,OAAO;MACPK,KAAK,EAAEL,OAAO,GACVC,SAAS,GACT;IACN,CAAC;EACH,CAAC;EAKDY,QAAQ,EAAE,SAAVA,QAAQA,CAAGd,KAAa,EAAuB;IAC7C,IAAI,CAACA,KAAK,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAK,CAAC;IAEpC,IAAMc,SAAS,GAAG,CAAC;IACnB,IAAMC,YAAY,GAAG,OAAO,CAACP,IAAI,CAACT,KAAK,CAAC;IACxC,IAAMiB,YAAY,GAAG,OAAO,CAACR,IAAI,CAACT,KAAK,CAAC;IACxC,IAAMkB,UAAU,GAAG,IAAI,CAACT,IAAI,CAACT,KAAK,CAAC;IACnC,IAAMmB,cAAc,GAAG,wBAAwB,CAACV,IAAI,CAACT,KAAK,CAAC;IAE3D,IAAIA,KAAK,CAACK,MAAM,GAAGU,SAAS,EAAE;MAC5B,OAAO;QACLd,OAAO,EAAE,KAAK;QACdK,KAAK,EAAE,gCAAgCS,SAAS,2BAA2BA,SAAS,GAAGf,KAAK,CAACK,MAAM,kBAAkBU,SAAS,GAAGf,KAAK,CAACK,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;MAC9J,CAAC;IACH;IAEA,IAAI,CAACW,YAAY,EAAE;MACjB,OAAO;QACLf,OAAO,EAAE,KAAK;QACdK,KAAK,EACH;MACJ,CAAC;IACH;IAEA,IAAI,CAACW,YAAY,EAAE;MACjB,OAAO;QACLhB,OAAO,EAAE,KAAK;QACdK,KAAK,EACH;MACJ,CAAC;IACH;IAEA,IAAI,CAACY,UAAU,EAAE;MACf,OAAO;QACLjB,OAAO,EAAE,KAAK;QACdK,KAAK,EACH;MACJ,CAAC;IACH;IAEA,OAAO;MAAEL,OAAO,EAAE;IAAK,CAAC;EAC1B,CAAC;EAKDmB,IAAI,EAAE,SAANA,IAAIA,CAAGpB,KAAa,EAAuB;IACzC,IAAI,CAACA,KAAK,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAK,CAAC;IAEpC,IAAMoB,SAAS,GAAG,uBAAuB;IACzC,IAAMpB,OAAO,GAAGoB,SAAS,CAACZ,IAAI,CAACT,KAAK,CAACI,IAAI,CAAC,CAAC,CAAC;IAE5C,OAAO;MACLH,OAAO,EAAPA,OAAO;MACPK,KAAK,EAAEL,OAAO,GACVC,SAAS,GACT;IACN,CAAC;EACH,CAAC;EAKDoB,GAAG,EAAE,SAALA,GAAGA,CAAGtB,KAAa,EAAuB;IACxC,IAAI,CAACA,KAAK,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAK,CAAC;IAEpC,IAAI;MACF,IAAIsB,GAAG,CAACvB,KAAK,CAAC;MACd,OAAO;QAAEC,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAAuB,OAAA,EAAM;MACN,OAAO;QACLvB,OAAO,EAAE,KAAK;QACdK,KAAK,EAAE;MACT,CAAC;IACH;EACF,CAAC;EAKDmB,MAAM,EAAE,SAARA,MAAMA,CAAGzB,KAAa,EAAuB;IAC3C,IAAI,CAACA,KAAK,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAK,CAAC;IAEpC,IAAMA,OAAO,GAAG,CAACyB,KAAK,CAACC,MAAM,CAAC3B,KAAK,CAAC,CAAC,IAAI4B,QAAQ,CAACD,MAAM,CAAC3B,KAAK,CAAC,CAAC;IAEhE,OAAO;MACLC,OAAO,EAAPA,OAAO;MACPK,KAAK,EAAEL,OAAO,GAAGC,SAAS,GAAG;IAC/B,CAAC;EACH,CAAC;EAKD2B,IAAI,EAAE,SAANA,IAAIA,CAAG7B,KAAa,EAAuB;IACzC,IAAI,CAACA,KAAK,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAK,CAAC;IAEpC,IAAM4B,IAAI,GAAG,IAAIC,IAAI,CAAC9B,KAAK,CAAC;IAC5B,IAAMC,OAAO,GAAG4B,IAAI,YAAYC,IAAI,IAAI,CAACJ,KAAK,CAACG,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC;IAE9D,OAAO;MACL9B,OAAO,EAAPA,OAAO;MACPK,KAAK,EAAEL,OAAO,GAAGC,SAAS,GAAG;IAC/B,CAAC;EACH,CAAC;EAKDa,SAAS,EAAE,SAAXA,SAASA,CAAGf,KAAa,EAAEe,UAAiB,EAAuB;IACjE,IAAI,CAACf,KAAK,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAK,CAAC;IAEpC,IAAMA,OAAO,GAAGD,KAAK,CAACK,MAAM,IAAIU,UAAS;IAEzC,OAAO;MACLd,OAAO,EAAPA,OAAO;MACPK,KAAK,EAAEL,OAAO,GACVC,SAAS,GACT,oBAAoBa,UAAS;IACnC,CAAC;EACH,CAAC;EAKDiB,SAAS,EAAE,SAAXA,SAASA,CAAGhC,KAAa,EAAEgC,UAAiB,EAAuB;IACjE,IAAI,CAAChC,KAAK,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAK,CAAC;IAEpC,IAAMA,OAAO,GAAGD,KAAK,CAACK,MAAM,IAAI2B,UAAS;IAEzC,OAAO;MACL/B,OAAO,EAAPA,OAAO;MACPK,KAAK,EAAEL,OAAO,GACVC,SAAS,GACT,wBAAwB8B,UAAS;IACvC,CAAC;EACH,CAAC;EAKDC,OAAO,EAAE,SAATA,OAAOA,CACLjC,KAAa,EACbiC,QAAe,EACfC,YAAqB,EACA;IACrB,IAAI,CAAClC,KAAK,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAK,CAAC;IAEpC,IAAMA,OAAO,GAAGgC,QAAO,CAACxB,IAAI,CAACT,KAAK,CAAC;IAEnC,OAAO;MACLC,OAAO,EAAPA,OAAO;MACPK,KAAK,EAAEL,OAAO,GAAGC,SAAS,GAAGgC,YAAY,IAAI;IAC/C,CAAC;EACH,CAAC;EAKDC,eAAe,EAAE,SAAjBA,eAAeA,CACbnC,KAAa,EACboC,gBAAwB,EACH;IACrB,IAAMnC,OAAO,GAAGD,KAAK,KAAKoC,gBAAgB;IAE1C,OAAO;MACLnC,OAAO,EAAPA,OAAO;MACPK,KAAK,EAAEL,OAAO,GAAGC,SAAS,GAAG;IAC/B,CAAC;EACH;AACF,CAAC;AAKM,IAAMmC,cAAa,GAAAvC,OAAA,CAAAuC,aAAA,GAAG,SAAhBA,aAAaA,CACxBrC,KAAU,EACVsC,UAA2B,EAC3BC,QAA8B,EACT;EAErB,IAAID,UAAU,CAACvC,QAAQ,EAAE;IACvB,IAAMyC,cAAc,GAAG3C,eAAe,CAACE,QAAQ,CAACC,KAAK,CAAC;IACtD,IAAI,CAACwC,cAAc,CAACvC,OAAO,EAAE;MAC3B,OAAOuC,cAAc;IACvB;EACF;EAGA,IAAI,CAACxC,KAAK,IAAI,CAACsC,UAAU,CAACvC,QAAQ,EAAE;IAClC,OAAO;MAAEE,OAAO,EAAE;IAAK,CAAC;EAC1B;EAGA,IAAIqC,UAAU,CAAC/B,KAAK,EAAE;IACpB,IAAMkC,WAAW,GAAG5C,eAAe,CAACU,KAAK,CAACP,KAAK,CAAC;IAChD,IAAI,CAACyC,WAAW,CAACxC,OAAO,EAAE;MACxB,OAAOwC,WAAW;IACpB;EACF;EAGA,IAAIH,UAAU,CAAC5B,KAAK,EAAE;IACpB,IAAMgC,WAAW,GAAG7C,eAAe,CAACa,KAAK,CAACV,KAAK,CAAC;IAChD,IAAI,CAAC0C,WAAW,CAACzC,OAAO,EAAE;MACxB,OAAOyC,WAAW;IACpB;EACF;EAGA,IAAIJ,UAAU,CAACxB,QAAQ,EAAE;IACvB,IAAM6B,cAAc,GAAG9C,eAAe,CAACiB,QAAQ,CAACd,KAAK,CAAC;IACtD,IAAI,CAAC2C,cAAc,CAAC1C,OAAO,EAAE;MAC3B,OAAO0C,cAAc;IACvB;EACF;EAGA,IAAIL,UAAU,CAAClB,IAAI,EAAE;IACnB,IAAMwB,UAAU,GAAG/C,eAAe,CAACuB,IAAI,CAACpB,KAAK,CAAC;IAC9C,IAAI,CAAC4C,UAAU,CAAC3C,OAAO,EAAE;MACvB,OAAO2C,UAAU;IACnB;EACF;EAGA,IAAIN,UAAU,CAAChB,GAAG,EAAE;IAClB,IAAMuB,SAAS,GAAGhD,eAAe,CAACyB,GAAG,CAACtB,KAAK,CAAC;IAC5C,IAAI,CAAC6C,SAAS,CAAC5C,OAAO,EAAE;MACtB,OAAO4C,SAAS;IAClB;EACF;EAGA,IAAIP,UAAU,CAACb,MAAM,EAAE;IACrB,IAAMqB,YAAY,GAAGjD,eAAe,CAAC4B,MAAM,CAACzB,KAAK,CAAC;IAClD,IAAI,CAAC8C,YAAY,CAAC7C,OAAO,EAAE;MACzB,OAAO6C,YAAY;IACrB;EACF;EAGA,IAAIR,UAAU,CAACT,IAAI,EAAE;IACnB,IAAMkB,UAAU,GAAGlD,eAAe,CAACgC,IAAI,CAAC7B,KAAK,CAAC;IAC9C,IAAI,CAAC+C,UAAU,CAAC9C,OAAO,EAAE;MACvB,OAAO8C,UAAU;IACnB;EACF;EAGA,IAAIT,UAAU,CAACvB,SAAS,KAAKb,SAAS,EAAE;IACtC,IAAM8C,eAAe,GAAGnD,eAAe,CAACkB,SAAS,CAC/Cf,KAAK,EACLsC,UAAU,CAACvB,SACb,CAAC;IACD,IAAI,CAACiC,eAAe,CAAC/C,OAAO,EAAE;MAC5B,OAAO+C,eAAe;IACxB;EACF;EAGA,IAAIV,UAAU,CAACN,SAAS,KAAK9B,SAAS,EAAE;IACtC,IAAM+C,eAAe,GAAGpD,eAAe,CAACmC,SAAS,CAC/ChC,KAAK,EACLsC,UAAU,CAACN,SACb,CAAC;IACD,IAAI,CAACiB,eAAe,CAAChD,OAAO,EAAE;MAC5B,OAAOgD,eAAe;IACxB;EACF;EAGA,IAAIX,UAAU,CAACL,OAAO,EAAE;IACtB,IAAMiB,aAAa,GAAGrD,eAAe,CAACoC,OAAO,CAACjC,KAAK,EAAEsC,UAAU,CAACL,OAAO,CAAC;IACxE,IAAI,CAACiB,aAAa,CAACjD,OAAO,EAAE;MAC1B,OAAOiD,aAAa;IACtB;EACF;EAGA,IAAIZ,UAAU,CAACH,eAAe,IAAII,QAAQ,EAAE;IAC1C,IAAMH,gBAAgB,GAAGG,QAAQ,CAACD,UAAU,CAACH,eAAe,CAAC;IAC7D,IAAMgB,aAAa,GAAGtD,eAAe,CAACsC,eAAe,CACnDnC,KAAK,EACLoC,gBACF,CAAC;IACD,IAAI,CAACe,aAAa,CAAClD,OAAO,EAAE;MAC1B,OAAOkD,aAAa;IACtB;EACF;EAGA,IAAIb,UAAU,CAACc,MAAM,EAAE;IACrB,IAAMC,YAAY,GAAGf,UAAU,CAACc,MAAM,CAACpD,KAAK,CAAC;IAC7C,IAAI,CAACqD,YAAY,CAACpD,OAAO,EAAE;MACzB,OAAOoD,YAAY;IACrB;EACF;EAEA,OAAO;IAAEpD,OAAO,EAAE;EAAK,CAAC;AAC1B,CAAC;AAKM,IAAMqD,aAAY,GAAAxD,OAAA,CAAAwD,YAAA,GAAG,SAAfA,YAAYA,CACvBf,QAA6B,EAC7BgB,gBAAsC,EACiB;EACvD,IAAMC,MAA4B,GAAG,CAAC,CAAC;EACvC,IAAIvD,OAAO,GAAG,IAAI;EAElBwD,MAAM,CAACC,IAAI,CAACH,gBAAgB,CAAC,CAACI,OAAO,CAAC,UAAAC,SAAS,EAAI;IACjD,IAAMC,UAAU,GAAGtB,QAAQ,CAACqB,SAAS,CAAC;IACtC,IAAME,eAAe,GAAGP,gBAAgB,CAACK,SAAS,CAAC;IAEnD,IAAMG,MAAM,GAAG1B,cAAa,CAACwB,UAAU,EAAEC,eAAe,EAAEvB,QAAQ,CAAC;IAEnE,IAAI,CAACwB,MAAM,CAAC9D,OAAO,EAAE;MACnBuD,MAAM,CAACI,SAAS,CAAC,GAAGG,MAAM,CAACzD,KAAM;MACjCL,OAAO,GAAG,KAAK;IACjB;EACF,CAAC,CAAC;EAEF,OAAO;IAAEA,OAAO,EAAPA,OAAO;IAAEuD,MAAM,EAANA;EAAO,CAAC;AAC5B,CAAC;AAKM,IAAMQ,wBAAwB,GAAAlE,OAAA,CAAAkE,wBAAA,GAAG,SAA3BA,wBAAwBA,CACnCC,YAAwB,EAErB;EAAA,IADHC,KAAa,GAAAC,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAjE,SAAA,GAAAiE,SAAA,MAAG,GAAG;EAEnB,IAAIC,SAAyB;EAE7B,OAAO,YAAM;IACXC,YAAY,CAACD,SAAS,CAAC;IACvBA,SAAS,GAAGE,UAAU,CAACL,YAAY,EAAEC,KAAK,CAAC;EAC7C,CAAC;AACH,CAAC;AAKM,IAAMK,iBAAiB,GAAAzE,OAAA,CAAAyE,iBAAA,GAAG;EAC/BhE,KAAK,EAAE;IACLR,QAAQ,EAAE,IAAI;IACdQ,KAAK,EAAE;EACT,CAAoB;EAEpBO,QAAQ,EAAE;IACRf,QAAQ,EAAE,IAAI;IACde,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE;EACb,CAAoB;EAEpBoB,eAAe,EAAE,SAAjBA,eAAeA,CAAGqC,iBAAyB;IAAA,OACxC;MACCzE,QAAQ,EAAE,IAAI;MACdoC,eAAe,EAAEqC;IACnB,CAAC;EAAA,CAAoB;EAEvBpD,IAAI,EAAE;IACJrB,QAAQ,EAAE,IAAI;IACdqB,IAAI,EAAE,IAAI;IACVL,SAAS,EAAE,CAAC;IACZiB,SAAS,EAAE;EACb,CAAoB;EAEpBtB,KAAK,EAAE;IACLX,QAAQ,EAAE,IAAI;IACdW,KAAK,EAAE;EACT,CAAoB;EAEpBX,QAAQ,EAAE;IACRA,QAAQ,EAAE;EACZ,CAAoB;EAEpB0E,QAAQ,EAAE,CAAC;AACb,CAAC;AAAC,IAKWC,aAAa,GAAA5E,OAAA,CAAA4E,aAAA;EAKxB,SAAAA,cAAYnB,gBAAsC,EAAE;IAAA,IAAAoB,gBAAA,CAAAC,OAAA,QAAAF,aAAA;IAAA,KAH5ClB,MAAM,GAAyB,CAAC,CAAC;IAAA,KACjCqB,OAAO,GAA4B,CAAC,CAAC;IAG3C,IAAI,CAACtB,gBAAgB,GAAGA,gBAAgB;EAC1C;EAAC,WAAAuB,aAAA,CAAAF,OAAA,EAAAF,aAAA;IAAAK,GAAA;IAAA/E,KAAA,EAED,SAAAqC,aAAaA,CAACuB,SAAiB,EAAE5D,KAAU,EAAEuC,QAA6B,EAAE;MAC1E,IAAMwB,MAAM,GAAG1B,cAAa,CAC1BrC,KAAK,EACL,IAAI,CAACuD,gBAAgB,CAACK,SAAS,CAAC,EAChCrB,QACF,CAAC;MAED,IAAIwB,MAAM,CAAC9D,OAAO,EAAE;QAClB,OAAO,IAAI,CAACuD,MAAM,CAACI,SAAS,CAAC;MAC/B,CAAC,MAAM;QACL,IAAI,CAACJ,MAAM,CAACI,SAAS,CAAC,GAAGG,MAAM,CAACzD,KAAM;MACxC;MAEA,OAAOyD,MAAM;IACf;EAAC;IAAAgB,GAAA;IAAA/E,KAAA,EAED,SAAAsD,YAAYA,CAACf,QAA6B,EAAE;MAC1C,IAAMwB,MAAM,GAAGT,aAAY,CAACf,QAAQ,EAAE,IAAI,CAACgB,gBAAgB,CAAC;MAC5D,IAAI,CAACC,MAAM,GAAGO,MAAM,CAACP,MAAM;MAC3B,OAAOO,MAAM;IACf;EAAC;IAAAgB,GAAA;IAAA/E,KAAA,EAED,SAAAgF,eAAeA,CAACpB,SAAiB,EAA2B;MAAA,IAAzBiB,OAAgB,GAAAV,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAjE,SAAA,GAAAiE,SAAA,MAAG,IAAI;MACxD,IAAI,CAACU,OAAO,CAACjB,SAAS,CAAC,GAAGiB,OAAO;IACnC;EAAC;IAAAE,GAAA;IAAA/E,KAAA,EAED,SAAAiF,aAAaA,CAACrB,SAAiB,EAAsB;MACnD,OAAO,IAAI,CAACiB,OAAO,CAACjB,SAAS,CAAC,GAAG,IAAI,CAACJ,MAAM,CAACI,SAAS,CAAC,GAAG1D,SAAS;IACrE;EAAC;IAAA6E,GAAA;IAAA/E,KAAA,EAED,SAAAkF,SAASA,CAAA,EAAyB;MAChC,OAAO,IAAI,CAAC1B,MAAM;IACpB;EAAC;IAAAuB,GAAA;IAAA/E,KAAA,EAED,SAAAmF,cAAcA,CAACvB,SAAiB,EAAW;MACzC,OAAO,IAAI,CAACiB,OAAO,CAACjB,SAAS,CAAC,IAAI,KAAK;IACzC;EAAC;IAAAmB,GAAA;IAAA/E,KAAA,EAED,SAAAoF,KAAKA,CAAA,EAAG;MACN,IAAI,CAAC5B,MAAM,GAAG,CAAC,CAAC;MAChB,IAAI,CAACqB,OAAO,GAAG,CAAC,CAAC;IACnB;EAAC;AAAA", "ignoreList": []}