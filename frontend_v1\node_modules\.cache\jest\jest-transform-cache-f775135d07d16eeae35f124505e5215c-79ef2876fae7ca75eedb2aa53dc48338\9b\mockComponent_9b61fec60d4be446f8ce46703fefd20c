9711df84c5c58ecc404c1602dd0eb83b
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
module.exports = function (moduleName, instanceMethods, isESModule) {
  var _Class;
  var RealComponent = isESModule ? jest.requireActual(moduleName).default : jest.requireActual(moduleName);
  var React = require('react');
  var SuperClass = typeof RealComponent === 'function' && RealComponent.prototype.constructor instanceof React.Component ? RealComponent : React.Component;
  var name = RealComponent.displayName || RealComponent.name || (RealComponent.render ? RealComponent.render.displayName || RealComponent.render.name : 'Unknown');
  var nameWithoutPrefix = name.replace(/^(RCT|RK)/, '');
  var Component = (_Class = function (_SuperClass) {
    function Component() {
      (0, _classCallCheck2.default)(this, Component);
      return _callSuper(this, Component, arguments);
    }
    (0, _inherits2.default)(Component, _SuperClass);
    return (0, _createClass2.default)(Component, [{
      key: "render",
      value: function render() {
        var _this = this;
        var props = Object.assign({}, RealComponent.defaultProps);
        if (this.props) {
          Object.keys(this.props).forEach(function (prop) {
            if (_this.props[prop] !== undefined) {
              props[prop] = _this.props[prop];
            }
          });
        }
        return React.createElement(nameWithoutPrefix, props, this.props.children);
      }
    }]);
  }(SuperClass), _Class.displayName = 'Component', _Class);
  Object.defineProperty(Component, 'name', {
    value: name,
    writable: false,
    enumerable: false,
    configurable: true
  });
  Component.displayName = nameWithoutPrefix;
  Object.keys(RealComponent).forEach(function (classStatic) {
    Component[classStatic] = RealComponent[classStatic];
  });
  if (instanceMethods != null) {
    Object.assign(Component.prototype, instanceMethods);
  }
  return Component;
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0IiwicmVxdWlyZSIsIl9jbGFzc0NhbGxDaGVjazIiLCJfY3JlYXRlQ2xhc3MyIiwiX3Bvc3NpYmxlQ29uc3RydWN0b3JSZXR1cm4yIiwiX2dldFByb3RvdHlwZU9mMiIsIl9pbmhlcml0czIiLCJfY2FsbFN1cGVyIiwidCIsIm8iLCJlIiwiZGVmYXVsdCIsIl9pc05hdGl2ZVJlZmxlY3RDb25zdHJ1Y3QiLCJSZWZsZWN0IiwiY29uc3RydWN0IiwiY29uc3RydWN0b3IiLCJhcHBseSIsIkJvb2xlYW4iLCJwcm90b3R5cGUiLCJ2YWx1ZU9mIiwiY2FsbCIsIm1vZHVsZSIsImV4cG9ydHMiLCJtb2R1bGVOYW1lIiwiaW5zdGFuY2VNZXRob2RzIiwiaXNFU01vZHVsZSIsIl9DbGFzcyIsIlJlYWxDb21wb25lbnQiLCJqZXN0IiwicmVxdWlyZUFjdHVhbCIsIlJlYWN0IiwiU3VwZXJDbGFzcyIsIkNvbXBvbmVudCIsIm5hbWUiLCJkaXNwbGF5TmFtZSIsInJlbmRlciIsIm5hbWVXaXRob3V0UHJlZml4IiwicmVwbGFjZSIsIl9TdXBlckNsYXNzIiwiYXJndW1lbnRzIiwia2V5IiwidmFsdWUiLCJfdGhpcyIsInByb3BzIiwiT2JqZWN0IiwiYXNzaWduIiwiZGVmYXVsdFByb3BzIiwia2V5cyIsImZvckVhY2giLCJwcm9wIiwidW5kZWZpbmVkIiwiY3JlYXRlRWxlbWVudCIsImNoaWxkcmVuIiwiZGVmaW5lUHJvcGVydHkiLCJ3cml0YWJsZSIsImVudW1lcmFibGUiLCJjb25maWd1cmFibGUiLCJjbGFzc1N0YXRpYyJdLCJzb3VyY2VzIjpbIm1vY2tDb21wb25lbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBDb3B5cmlnaHQgKGMpIE1ldGEgUGxhdGZvcm1zLCBJbmMuIGFuZCBhZmZpbGlhdGVzLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICpcbiAqIEBmb3JtYXRcbiAqL1xuXG4ndXNlIHN0cmljdCc7XG5cbm1vZHVsZS5leHBvcnRzID0gKG1vZHVsZU5hbWUsIGluc3RhbmNlTWV0aG9kcywgaXNFU01vZHVsZSkgPT4ge1xuICBjb25zdCBSZWFsQ29tcG9uZW50ID0gaXNFU01vZHVsZVxuICAgID8gamVzdC5yZXF1aXJlQWN0dWFsKG1vZHVsZU5hbWUpLmRlZmF1bHRcbiAgICA6IGplc3QucmVxdWlyZUFjdHVhbChtb2R1bGVOYW1lKTtcbiAgY29uc3QgUmVhY3QgPSByZXF1aXJlKCdyZWFjdCcpO1xuXG4gIGNvbnN0IFN1cGVyQ2xhc3MgPVxuICAgIHR5cGVvZiBSZWFsQ29tcG9uZW50ID09PSAnZnVuY3Rpb24nICYmXG4gICAgUmVhbENvbXBvbmVudC5wcm90b3R5cGUuY29uc3RydWN0b3IgaW5zdGFuY2VvZiBSZWFjdC5Db21wb25lbnRcbiAgICAgID8gUmVhbENvbXBvbmVudFxuICAgICAgOiBSZWFjdC5Db21wb25lbnQ7XG5cbiAgY29uc3QgbmFtZSA9XG4gICAgUmVhbENvbXBvbmVudC5kaXNwbGF5TmFtZSB8fFxuICAgIFJlYWxDb21wb25lbnQubmFtZSB8fFxuICAgIChSZWFsQ29tcG9uZW50LnJlbmRlciAvLyBoYW5kbGUgUmVhY3QuZm9yd2FyZFJlZlxuICAgICAgPyBSZWFsQ29tcG9uZW50LnJlbmRlci5kaXNwbGF5TmFtZSB8fCBSZWFsQ29tcG9uZW50LnJlbmRlci5uYW1lXG4gICAgICA6ICdVbmtub3duJyk7XG5cbiAgY29uc3QgbmFtZVdpdGhvdXRQcmVmaXggPSBuYW1lLnJlcGxhY2UoL14oUkNUfFJLKS8sICcnKTtcblxuICBjb25zdCBDb21wb25lbnQgPSBjbGFzcyBleHRlbmRzIFN1cGVyQ2xhc3Mge1xuICAgIHN0YXRpYyBkaXNwbGF5TmFtZSA9ICdDb21wb25lbnQnO1xuXG4gICAgcmVuZGVyKCkge1xuICAgICAgY29uc3QgcHJvcHMgPSBPYmplY3QuYXNzaWduKHt9LCBSZWFsQ29tcG9uZW50LmRlZmF1bHRQcm9wcyk7XG5cbiAgICAgIGlmICh0aGlzLnByb3BzKSB7XG4gICAgICAgIE9iamVjdC5rZXlzKHRoaXMucHJvcHMpLmZvckVhY2gocHJvcCA9PiB7XG4gICAgICAgICAgLy8gV2UgY2FuJ3QganVzdCBhc3NpZ24gcHJvcHMgb24gdG9wIG9mIGRlZmF1bHRQcm9wc1xuICAgICAgICAgIC8vIGJlY2F1c2UgUmVhY3QgdHJlYXRzIHVuZGVmaW5lZCBhcyBzcGVjaWFsIGFuZCBkaWZmZXJlbnQgZnJvbSBudWxsLlxuICAgICAgICAgIC8vIElmIGEgcHJvcCBpcyBzcGVjaWZpZWQgYnV0IHNldCB0byB1bmRlZmluZWQgaXQgaXMgaWdub3JlZCBhbmQgdGhlXG4gICAgICAgICAgLy8gZGVmYXVsdCBwcm9wIGlzIHVzZWQgaW5zdGVhZC4gSWYgaXQgaXMgc2V0IHRvIG51bGwsIHRoZW4gdGhlXG4gICAgICAgICAgLy8gbnVsbCB2YWx1ZSBvdmVyd3JpdGVzIHRoZSBkZWZhdWx0IHZhbHVlLlxuICAgICAgICAgIGlmICh0aGlzLnByb3BzW3Byb3BdICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHByb3BzW3Byb3BdID0gdGhpcy5wcm9wc1twcm9wXTtcbiAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gUmVhY3QuY3JlYXRlRWxlbWVudChuYW1lV2l0aG91dFByZWZpeCwgcHJvcHMsIHRoaXMucHJvcHMuY2hpbGRyZW4pO1xuICAgIH1cbiAgfTtcblxuICBPYmplY3QuZGVmaW5lUHJvcGVydHkoQ29tcG9uZW50LCAnbmFtZScsIHtcbiAgICB2YWx1ZTogbmFtZSxcbiAgICB3cml0YWJsZTogZmFsc2UsXG4gICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICB9KTtcblxuICBDb21wb25lbnQuZGlzcGxheU5hbWUgPSBuYW1lV2l0aG91dFByZWZpeDtcblxuICBPYmplY3Qua2V5cyhSZWFsQ29tcG9uZW50KS5mb3JFYWNoKGNsYXNzU3RhdGljID0+IHtcbiAgICBDb21wb25lbnRbY2xhc3NTdGF0aWNdID0gUmVhbENvbXBvbmVudFtjbGFzc1N0YXRpY107XG4gIH0pO1xuXG4gIGlmIChpbnN0YW5jZU1ldGhvZHMgIT0gbnVsbCkge1xuICAgIE9iamVjdC5hc3NpZ24oQ29tcG9uZW50LnByb3RvdHlwZSwgaW5zdGFuY2VNZXRob2RzKTtcbiAgfVxuXG4gIHJldHVybiBDb21wb25lbnQ7XG59O1xuIl0sIm1hcHBpbmdzIjoiQUFTQSxZQUFZOztBQUFDLElBQUFBLHNCQUFBLEdBQUFDLE9BQUE7QUFBQSxJQUFBQyxnQkFBQSxHQUFBRixzQkFBQSxDQUFBQyxPQUFBO0FBQUEsSUFBQUUsYUFBQSxHQUFBSCxzQkFBQSxDQUFBQyxPQUFBO0FBQUEsSUFBQUcsMkJBQUEsR0FBQUosc0JBQUEsQ0FBQUMsT0FBQTtBQUFBLElBQUFJLGdCQUFBLEdBQUFMLHNCQUFBLENBQUFDLE9BQUE7QUFBQSxJQUFBSyxVQUFBLEdBQUFOLHNCQUFBLENBQUFDLE9BQUE7QUFBQSxTQUFBTSxXQUFBQyxDQUFBLEVBQUFDLENBQUEsRUFBQUMsQ0FBQSxXQUFBRCxDQUFBLE9BQUFKLGdCQUFBLENBQUFNLE9BQUEsRUFBQUYsQ0FBQSxPQUFBTCwyQkFBQSxDQUFBTyxPQUFBLEVBQUFILENBQUEsRUFBQUkseUJBQUEsS0FBQUMsT0FBQSxDQUFBQyxTQUFBLENBQUFMLENBQUEsRUFBQUMsQ0FBQSxZQUFBTCxnQkFBQSxDQUFBTSxPQUFBLEVBQUFILENBQUEsRUFBQU8sV0FBQSxJQUFBTixDQUFBLENBQUFPLEtBQUEsQ0FBQVIsQ0FBQSxFQUFBRSxDQUFBO0FBQUEsU0FBQUUsMEJBQUEsY0FBQUosQ0FBQSxJQUFBUyxPQUFBLENBQUFDLFNBQUEsQ0FBQUMsT0FBQSxDQUFBQyxJQUFBLENBQUFQLE9BQUEsQ0FBQUMsU0FBQSxDQUFBRyxPQUFBLGlDQUFBVCxDQUFBLGFBQUFJLHlCQUFBLFlBQUFBLDBCQUFBLGFBQUFKLENBQUE7QUFFYmEsTUFBTSxDQUFDQyxPQUFPLEdBQUcsVUFBQ0MsVUFBVSxFQUFFQyxlQUFlLEVBQUVDLFVBQVUsRUFBSztFQUFBLElBQUFDLE1BQUE7RUFDNUQsSUFBTUMsYUFBYSxHQUFHRixVQUFVLEdBQzVCRyxJQUFJLENBQUNDLGFBQWEsQ0FBQ04sVUFBVSxDQUFDLENBQUNaLE9BQU8sR0FDdENpQixJQUFJLENBQUNDLGFBQWEsQ0FBQ04sVUFBVSxDQUFDO0VBQ2xDLElBQU1PLEtBQUssR0FBRzdCLE9BQU8sQ0FBQyxPQUFPLENBQUM7RUFFOUIsSUFBTThCLFVBQVUsR0FDZCxPQUFPSixhQUFhLEtBQUssVUFBVSxJQUNuQ0EsYUFBYSxDQUFDVCxTQUFTLENBQUNILFdBQVcsWUFBWWUsS0FBSyxDQUFDRSxTQUFTLEdBQzFETCxhQUFhLEdBQ2JHLEtBQUssQ0FBQ0UsU0FBUztFQUVyQixJQUFNQyxJQUFJLEdBQ1JOLGFBQWEsQ0FBQ08sV0FBVyxJQUN6QlAsYUFBYSxDQUFDTSxJQUFJLEtBQ2pCTixhQUFhLENBQUNRLE1BQU0sR0FDakJSLGFBQWEsQ0FBQ1EsTUFBTSxDQUFDRCxXQUFXLElBQUlQLGFBQWEsQ0FBQ1EsTUFBTSxDQUFDRixJQUFJLEdBQzdELFNBQVMsQ0FBQztFQUVoQixJQUFNRyxpQkFBaUIsR0FBR0gsSUFBSSxDQUFDSSxPQUFPLENBQUMsV0FBVyxFQUFFLEVBQUUsQ0FBQztFQUV2RCxJQUFNTCxTQUFTLElBQUFOLE1BQUEsYUFBQVksV0FBQTtJQUFBLFNBQUFOLFVBQUE7TUFBQSxJQUFBOUIsZ0JBQUEsQ0FBQVMsT0FBQSxRQUFBcUIsU0FBQTtNQUFBLE9BQUF6QixVQUFBLE9BQUF5QixTQUFBLEVBQUFPLFNBQUE7SUFBQTtJQUFBLElBQUFqQyxVQUFBLENBQUFLLE9BQUEsRUFBQXFCLFNBQUEsRUFBQU0sV0FBQTtJQUFBLFdBQUFuQyxhQUFBLENBQUFRLE9BQUEsRUFBQXFCLFNBQUE7TUFBQVEsR0FBQTtNQUFBQyxLQUFBLEVBR2IsU0FBQU4sTUFBTUEsQ0FBQSxFQUFHO1FBQUEsSUFBQU8sS0FBQTtRQUNQLElBQU1DLEtBQUssR0FBR0MsTUFBTSxDQUFDQyxNQUFNLENBQUMsQ0FBQyxDQUFDLEVBQUVsQixhQUFhLENBQUNtQixZQUFZLENBQUM7UUFFM0QsSUFBSSxJQUFJLENBQUNILEtBQUssRUFBRTtVQUNkQyxNQUFNLENBQUNHLElBQUksQ0FBQyxJQUFJLENBQUNKLEtBQUssQ0FBQyxDQUFDSyxPQUFPLENBQUMsVUFBQUMsSUFBSSxFQUFJO1lBTXRDLElBQUlQLEtBQUksQ0FBQ0MsS0FBSyxDQUFDTSxJQUFJLENBQUMsS0FBS0MsU0FBUyxFQUFFO2NBQ2xDUCxLQUFLLENBQUNNLElBQUksQ0FBQyxHQUFHUCxLQUFJLENBQUNDLEtBQUssQ0FBQ00sSUFBSSxDQUFDO1lBQ2hDO1VBQ0YsQ0FBQyxDQUFDO1FBQ0o7UUFFQSxPQUFPbkIsS0FBSyxDQUFDcUIsYUFBYSxDQUFDZixpQkFBaUIsRUFBRU8sS0FBSyxFQUFFLElBQUksQ0FBQ0EsS0FBSyxDQUFDUyxRQUFRLENBQUM7TUFDM0U7SUFBQztFQUFBLEVBcEI2QnJCLFVBQVUsR0FBQUwsTUFBQSxDQUNqQ1EsV0FBVyxHQUFHLFdBQVcsRUFBQVIsTUFBQSxDQW9CakM7RUFFRGtCLE1BQU0sQ0FBQ1MsY0FBYyxDQUFDckIsU0FBUyxFQUFFLE1BQU0sRUFBRTtJQUN2Q1MsS0FBSyxFQUFFUixJQUFJO0lBQ1hxQixRQUFRLEVBQUUsS0FBSztJQUNmQyxVQUFVLEVBQUUsS0FBSztJQUNqQkMsWUFBWSxFQUFFO0VBQ2hCLENBQUMsQ0FBQztFQUVGeEIsU0FBUyxDQUFDRSxXQUFXLEdBQUdFLGlCQUFpQjtFQUV6Q1EsTUFBTSxDQUFDRyxJQUFJLENBQUNwQixhQUFhLENBQUMsQ0FBQ3FCLE9BQU8sQ0FBQyxVQUFBUyxXQUFXLEVBQUk7SUFDaER6QixTQUFTLENBQUN5QixXQUFXLENBQUMsR0FBRzlCLGFBQWEsQ0FBQzhCLFdBQVcsQ0FBQztFQUNyRCxDQUFDLENBQUM7RUFFRixJQUFJakMsZUFBZSxJQUFJLElBQUksRUFBRTtJQUMzQm9CLE1BQU0sQ0FBQ0MsTUFBTSxDQUFDYixTQUFTLENBQUNkLFNBQVMsRUFBRU0sZUFBZSxDQUFDO0VBQ3JEO0VBRUEsT0FBT1EsU0FBUztBQUNsQixDQUFDIiwiaWdub3JlTGlzdCI6W119