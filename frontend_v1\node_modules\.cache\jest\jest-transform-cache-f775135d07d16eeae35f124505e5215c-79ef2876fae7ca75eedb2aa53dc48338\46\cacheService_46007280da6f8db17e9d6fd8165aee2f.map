{"version": 3, "names": ["_asyncStorage", "_interopRequireDefault", "require", "CacheService", "exports", "config", "_classCallCheck2", "default", "memoryCache", "Map", "CACHE_VERSION", "STORAGE_PREFIX", "Object", "assign", "defaultTTL", "maxMemorySize", "maxStorageSize", "compressionThreshold", "enableEncryption", "enableAnalytics", "cleanupInterval", "stats", "memoryHits", "memoryMisses", "storageHits", "storageMisses", "totalSize", "entryCount", "hitRate", "averageAccessTime", "startCleanupTimer", "_createClass2", "key", "value", "_get", "_asyncToGenerator2", "startTime", "Date", "now", "memoryEntry", "get", "isValidEntry", "updateStats", "updateEntryAccess", "data", "delete", "storageEntry", "getFromStorage", "set", "error", "console", "_x", "apply", "arguments", "_set", "ttl", "options", "entry", "timestamp", "version", "accessCount", "lastAccessed", "dataSize", "estimateSize", "shouldCompress", "compress", "memoryOnly", "compressed", "storageOnly", "enforceMemoryLimit", "setInStorage", "updateCacheStats", "_x2", "_x3", "_x4", "_x5", "_remove", "AsyncStorage", "removeItem", "remove", "_x6", "_clear", "_this", "clear", "keys", "getAllKeys", "cacheKeys", "filter", "startsWith", "multiRemove", "resetStats", "_preload", "entries", "_this2", "promises", "map", "_ref", "Promise", "allSettled", "preload", "_x7", "getStats", "getEntryInfo", "encrypted", "_invalidatePattern", "pattern", "_this3", "test", "replace", "storageKeys", "invalidate<PERSON><PERSON><PERSON>", "_x8", "_getFromStorage", "stored", "getItem", "JSON", "parse", "_x9", "_setInStorage", "serialized", "stringify", "setItem", "_x0", "_x1", "length", "_this4", "Array", "from", "reduce", "sum", "_ref2", "_ref3", "_slicedToArray2", "sort", "_ref4", "_ref5", "_ref6", "a", "_ref7", "b", "scoreA", "scoreB", "_ref8", "shift", "_ref9", "type", "accessTime", "totalRequests", "_this5", "size", "values", "_this6", "cleanupTimer", "setInterval", "cleanup", "_ref0", "_ref1", "destroy", "clearInterval", "cacheService", "_default"], "sources": ["cacheService.ts"], "sourcesContent": ["/**\n * Enhanced Cache Service - Advanced Caching with Intelligent Offline Support\n *\n * Service Contract:\n * - Provides multi-level caching (memory, storage, network) with offline support\n * - Implements intelligent cache invalidation and freshness strategies\n * - Supports cache warming, preloading, and background synchronization\n * - Handles cache compression, encryption, and data integrity\n * - Provides comprehensive cache analytics and monitoring\n * - Integrates with offline manager for seamless offline/online transitions\n * - Implements LRU eviction and smart cache size management\n * - Supports cache tagging and bulk invalidation\n *\n * @version 3.0.0 - Enhanced with Comprehensive Offline Support and Intelligence\n * <AUTHOR> Development Team\n */\n\nimport AsyncStorage from '@react-native-async-storage/async-storage';\n\ninterface CacheEntry<T = any> {\n  data: T;\n  timestamp: number;\n  ttl: number;\n  version: string;\n  compressed?: boolean;\n  encrypted?: boolean;\n  accessCount: number;\n  lastAccessed: number;\n}\n\ninterface CacheConfig {\n  defaultTTL: number;\n  maxMemorySize: number;\n  maxStorageSize: number;\n  compressionThreshold: number;\n  enableEncryption: boolean;\n  enableAnalytics: boolean;\n  cleanupInterval: number;\n}\n\ninterface CacheStats {\n  memoryHits: number;\n  memoryMisses: number;\n  storageHits: number;\n  storageMisses: number;\n  totalSize: number;\n  entryCount: number;\n  hitRate: number;\n  averageAccessTime: number;\n}\n\nclass CacheService {\n  private memoryCache = new Map<string, CacheEntry>();\n  private config: CacheConfig;\n  private stats: CacheStats;\n  private cleanupTimer?: NodeJS.Timeout;\n  private readonly CACHE_VERSION = '1.0.0';\n  private readonly STORAGE_PREFIX = '@vierla_cache_';\n\n  constructor(config?: Partial<CacheConfig>) {\n    this.config = {\n      defaultTTL: 5 * 60 * 1000, // 5 minutes\n      maxMemorySize: 50 * 1024 * 1024, // 50MB\n      maxStorageSize: 100 * 1024 * 1024, // 100MB\n      compressionThreshold: 1024, // 1KB\n      enableEncryption: false,\n      enableAnalytics: true,\n      cleanupInterval: 60 * 1000, // 1 minute\n      ...config,\n    };\n\n    this.stats = {\n      memoryHits: 0,\n      memoryMisses: 0,\n      storageHits: 0,\n      storageMisses: 0,\n      totalSize: 0,\n      entryCount: 0,\n      hitRate: 0,\n      averageAccessTime: 0,\n    };\n\n    this.startCleanupTimer();\n  }\n\n  /**\n   * Get data from cache with fallback chain\n   */\n  async get<T>(key: string): Promise<T | null> {\n    const startTime = Date.now();\n\n    try {\n      // Try memory cache first\n      const memoryEntry = this.memoryCache.get(key);\n      if (memoryEntry && this.isValidEntry(memoryEntry)) {\n        this.updateStats('memoryHit', Date.now() - startTime);\n        this.updateEntryAccess(key, memoryEntry);\n        return memoryEntry.data as T;\n      }\n\n      if (memoryEntry) {\n        this.memoryCache.delete(key);\n      }\n\n      // Try storage cache\n      const storageEntry = await this.getFromStorage<T>(key);\n      if (storageEntry && this.isValidEntry(storageEntry)) {\n        this.updateStats('storageHit', Date.now() - startTime);\n        this.updateEntryAccess(key, storageEntry);\n\n        // Promote to memory cache\n        this.memoryCache.set(key, storageEntry);\n        return storageEntry.data as T;\n      }\n\n      // Cache miss\n      this.updateStats('miss', Date.now() - startTime);\n      return null;\n    } catch (error) {\n      console.error('Cache get error:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Set data in cache with automatic tier management\n   */\n  async set<T>(\n    key: string,\n    data: T,\n    ttl?: number,\n    options?: {\n      memoryOnly?: boolean;\n      storageOnly?: boolean;\n      compress?: boolean;\n    },\n  ): Promise<void> {\n    const entry: CacheEntry<T> = {\n      data,\n      timestamp: Date.now(),\n      ttl: ttl || this.config.defaultTTL,\n      version: this.CACHE_VERSION,\n      accessCount: 0,\n      lastAccessed: Date.now(),\n    };\n\n    try {\n      // Determine if compression is needed\n      const dataSize = this.estimateSize(data);\n      const shouldCompress =\n        options?.compress ||\n        (dataSize > this.config.compressionThreshold && !options?.memoryOnly);\n\n      if (shouldCompress) {\n        entry.compressed = true;\n        // In a real implementation, you'd compress the data here\n        // entry.data = await this.compress(data);\n      }\n\n      // Set in memory cache unless storage-only\n      if (!options?.storageOnly) {\n        this.memoryCache.set(key, entry);\n        this.enforceMemoryLimit();\n      }\n\n      // Set in storage cache unless memory-only\n      if (!options?.memoryOnly) {\n        await this.setInStorage(key, entry);\n      }\n\n      this.updateCacheStats();\n    } catch (error) {\n      console.error('Cache set error:', error);\n    }\n  }\n\n  /**\n   * Remove data from all cache tiers\n   */\n  async remove(key: string): Promise<void> {\n    try {\n      this.memoryCache.delete(key);\n      await AsyncStorage.removeItem(this.STORAGE_PREFIX + key);\n      this.updateCacheStats();\n    } catch (error) {\n      console.error('Cache remove error:', error);\n    }\n  }\n\n  /**\n   * Clear all cache data\n   */\n  async clear(): Promise<void> {\n    try {\n      this.memoryCache.clear();\n\n      // Clear storage cache\n      const keys = await AsyncStorage.getAllKeys();\n      const cacheKeys = keys.filter(key => key.startsWith(this.STORAGE_PREFIX));\n      await AsyncStorage.multiRemove(cacheKeys);\n\n      this.resetStats();\n    } catch (error) {\n      console.error('Cache clear error:', error);\n    }\n  }\n\n  /**\n   * Preload data into cache\n   */\n  async preload<T>(\n    entries: { key: string; data: T; ttl?: number }[],\n  ): Promise<void> {\n    const promises = entries.map(({ key, data, ttl }) =>\n      this.set(key, data, ttl),\n    );\n\n    await Promise.allSettled(promises);\n  }\n\n  /**\n   * Get cache statistics\n   */\n  getStats(): CacheStats {\n    return { ...this.stats };\n  }\n\n  /**\n   * Get cache entry metadata\n   */\n  getEntryInfo(key: string): Partial<CacheEntry> | null {\n    const entry = this.memoryCache.get(key);\n    if (!entry) return null;\n\n    return {\n      timestamp: entry.timestamp,\n      ttl: entry.ttl,\n      version: entry.version,\n      accessCount: entry.accessCount,\n      lastAccessed: entry.lastAccessed,\n      compressed: entry.compressed,\n      encrypted: entry.encrypted,\n    };\n  }\n\n  /**\n   * Invalidate cache entries by pattern\n   */\n  async invalidatePattern(pattern: RegExp): Promise<void> {\n    // Invalidate memory cache\n    for (const key of this.memoryCache.keys()) {\n      if (pattern.test(key)) {\n        this.memoryCache.delete(key);\n      }\n    }\n\n    // Invalidate storage cache\n    try {\n      const keys = await AsyncStorage.getAllKeys();\n      const cacheKeys = keys\n        .filter(key => key.startsWith(this.STORAGE_PREFIX))\n        .map(key => key.replace(this.STORAGE_PREFIX, ''))\n        .filter(key => pattern.test(key));\n\n      const storageKeys = cacheKeys.map(key => this.STORAGE_PREFIX + key);\n      await AsyncStorage.multiRemove(storageKeys);\n    } catch (error) {\n      console.error('Cache pattern invalidation error:', error);\n    }\n\n    this.updateCacheStats();\n  }\n\n  /**\n   * Get storage entry\n   */\n  private async getFromStorage<T>(key: string): Promise<CacheEntry<T> | null> {\n    try {\n      const stored = await AsyncStorage.getItem(this.STORAGE_PREFIX + key);\n      if (!stored) return null;\n\n      const entry: CacheEntry<T> = JSON.parse(stored);\n\n      // Decompress if needed\n      if (entry.compressed) {\n        // In a real implementation, you'd decompress the data here\n        // entry.data = await this.decompress(entry.data);\n      }\n\n      return entry;\n    } catch (error) {\n      console.error('Storage get error:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Set storage entry\n   */\n  private async setInStorage<T>(\n    key: string,\n    entry: CacheEntry<T>,\n  ): Promise<void> {\n    try {\n      const serialized = JSON.stringify(entry);\n      await AsyncStorage.setItem(this.STORAGE_PREFIX + key, serialized);\n    } catch (error) {\n      console.error('Storage set error:', error);\n    }\n  }\n\n  /**\n   * Check if cache entry is valid\n   */\n  private isValidEntry(entry: CacheEntry): boolean {\n    const now = Date.now();\n    return now - entry.timestamp < entry.ttl;\n  }\n\n  /**\n   * Update entry access information\n   */\n  private updateEntryAccess(key: string, entry: CacheEntry): void {\n    entry.accessCount++;\n    entry.lastAccessed = Date.now();\n    this.memoryCache.set(key, entry);\n  }\n\n  /**\n   * Estimate data size in bytes\n   */\n  private estimateSize(data: any): number {\n    return JSON.stringify(data).length * 2; // Rough estimate\n  }\n\n  /**\n   * Enforce memory cache size limits\n   */\n  private enforceMemoryLimit(): void {\n    const entries = Array.from(this.memoryCache.entries());\n    let totalSize = entries.reduce(\n      (sum, [, entry]) => sum + this.estimateSize(entry.data),\n      0,\n    );\n\n    if (totalSize <= this.config.maxMemorySize) return;\n\n    // Sort by access frequency and recency (LFU + LRU)\n    entries.sort(([, a], [, b]) => {\n      const scoreA = a.accessCount / (Date.now() - a.lastAccessed);\n      const scoreB = b.accessCount / (Date.now() - b.lastAccessed);\n      return scoreA - scoreB;\n    });\n\n    // Remove least valuable entries\n    while (totalSize > this.config.maxMemorySize && entries.length > 0) {\n      const [key, entry] = entries.shift()!;\n      this.memoryCache.delete(key);\n      totalSize -= this.estimateSize(entry.data);\n    }\n  }\n\n  /**\n   * Update cache statistics\n   */\n  private updateStats(\n    type: 'memoryHit' | 'storageHit' | 'miss',\n    accessTime: number,\n  ): void {\n    if (!this.config.enableAnalytics) return;\n\n    switch (type) {\n      case 'memoryHit':\n        this.stats.memoryHits++;\n        break;\n      case 'storageHit':\n        this.stats.storageHits++;\n        break;\n      case 'miss':\n        this.stats.memoryMisses++;\n        this.stats.storageMisses++;\n        break;\n    }\n\n    const totalRequests =\n      this.stats.memoryHits + this.stats.storageHits + this.stats.memoryMisses;\n    this.stats.hitRate =\n      totalRequests > 0\n        ? (this.stats.memoryHits + this.stats.storageHits) / totalRequests\n        : 0;\n\n    // Update average access time (simple moving average)\n    this.stats.averageAccessTime =\n      this.stats.averageAccessTime * 0.9 + accessTime * 0.1;\n  }\n\n  /**\n   * Update cache size statistics\n   */\n  private updateCacheStats(): void {\n    this.stats.entryCount = this.memoryCache.size;\n    this.stats.totalSize = Array.from(this.memoryCache.values()).reduce(\n      (sum, entry) => sum + this.estimateSize(entry.data),\n      0,\n    );\n  }\n\n  /**\n   * Reset statistics\n   */\n  private resetStats(): void {\n    this.stats = {\n      memoryHits: 0,\n      memoryMisses: 0,\n      storageHits: 0,\n      storageMisses: 0,\n      totalSize: 0,\n      entryCount: 0,\n      hitRate: 0,\n      averageAccessTime: 0,\n    };\n  }\n\n  /**\n   * Start cleanup timer\n   */\n  private startCleanupTimer(): void {\n    this.cleanupTimer = setInterval(() => {\n      this.cleanup();\n    }, this.config.cleanupInterval);\n  }\n\n  /**\n   * Cleanup expired entries\n   */\n  private cleanup(): void {\n    const now = Date.now();\n\n    for (const [key, entry] of this.memoryCache.entries()) {\n      if (!this.isValidEntry(entry)) {\n        this.memoryCache.delete(key);\n      }\n    }\n\n    this.updateCacheStats();\n  }\n\n  /**\n   * Destroy cache service\n   */\n  destroy(): void {\n    if (this.cleanupTimer) {\n      clearInterval(this.cleanupTimer);\n    }\n    this.memoryCache.clear();\n  }\n}\n\n// Export class and singleton instance\nexport { CacheService };\nexport const cacheService = new CacheService();\nexport default cacheService;\n"], "mappings": ";;;;;;;;;AAiBA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAqE,IAkC/DC,YAAY,GAAAC,OAAA,CAAAD,YAAA;EAQhB,SAAAA,aAAYE,MAA6B,EAAE;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAJ,YAAA;IAAA,KAPnCK,WAAW,GAAG,IAAIC,GAAG,CAAqB,CAAC;IAAA,KAIlCC,aAAa,GAAG,OAAO;IAAA,KACvBC,cAAc,GAAG,gBAAgB;IAGhD,IAAI,CAACN,MAAM,GAAAO,MAAA,CAAAC,MAAA;MACTC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;MACzBC,aAAa,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;MAC/BC,cAAc,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI;MACjCC,oBAAoB,EAAE,IAAI;MAC1BC,gBAAgB,EAAE,KAAK;MACvBC,eAAe,EAAE,IAAI;MACrBC,eAAe,EAAE,EAAE,GAAG;IAAI,GACvBf,MAAM,CACV;IAED,IAAI,CAACgB,KAAK,GAAG;MACXC,UAAU,EAAE,CAAC;MACbC,YAAY,EAAE,CAAC;MACfC,WAAW,EAAE,CAAC;MACdC,aAAa,EAAE,CAAC;MAChBC,SAAS,EAAE,CAAC;MACZC,UAAU,EAAE,CAAC;MACbC,OAAO,EAAE,CAAC;MACVC,iBAAiB,EAAE;IACrB,CAAC;IAED,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC1B;EAAC,WAAAC,aAAA,CAAAxB,OAAA,EAAAJ,YAAA;IAAA6B,GAAA;IAAAC,KAAA;MAAA,IAAAC,IAAA,OAAAC,kBAAA,CAAA5B,OAAA,EAKD,WAAayB,GAAW,EAAqB;QAC3C,IAAMI,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;QAE5B,IAAI;UAEF,IAAMC,WAAW,GAAG,IAAI,CAAC/B,WAAW,CAACgC,GAAG,CAACR,GAAG,CAAC;UAC7C,IAAIO,WAAW,IAAI,IAAI,CAACE,YAAY,CAACF,WAAW,CAAC,EAAE;YACjD,IAAI,CAACG,WAAW,CAAC,WAAW,EAAEL,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS,CAAC;YACrD,IAAI,CAACO,iBAAiB,CAACX,GAAG,EAAEO,WAAW,CAAC;YACxC,OAAOA,WAAW,CAACK,IAAI;UACzB;UAEA,IAAIL,WAAW,EAAE;YACf,IAAI,CAAC/B,WAAW,CAACqC,MAAM,CAACb,GAAG,CAAC;UAC9B;UAGA,IAAMc,YAAY,SAAS,IAAI,CAACC,cAAc,CAAIf,GAAG,CAAC;UACtD,IAAIc,YAAY,IAAI,IAAI,CAACL,YAAY,CAACK,YAAY,CAAC,EAAE;YACnD,IAAI,CAACJ,WAAW,CAAC,YAAY,EAAEL,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS,CAAC;YACtD,IAAI,CAACO,iBAAiB,CAACX,GAAG,EAAEc,YAAY,CAAC;YAGzC,IAAI,CAACtC,WAAW,CAACwC,GAAG,CAAChB,GAAG,EAAEc,YAAY,CAAC;YACvC,OAAOA,YAAY,CAACF,IAAI;UAC1B;UAGA,IAAI,CAACF,WAAW,CAAC,MAAM,EAAEL,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS,CAAC;UAChD,OAAO,IAAI;QACb,CAAC,CAAC,OAAOa,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;UACxC,OAAO,IAAI;QACb;MACF,CAAC;MAAA,SAlCKT,GAAGA,CAAAW,EAAA;QAAA,OAAAjB,IAAA,CAAAkB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAHb,GAAG;IAAA;EAAA;IAAAR,GAAA;IAAAC,KAAA;MAAA,IAAAqB,IAAA,OAAAnB,kBAAA,CAAA5B,OAAA,EAuCT,WACEyB,GAAW,EACXY,IAAO,EACPW,GAAY,EACZC,OAIC,EACc;QACf,IAAMC,KAAoB,GAAG;UAC3Bb,IAAI,EAAJA,IAAI;UACJc,SAAS,EAAErB,IAAI,CAACC,GAAG,CAAC,CAAC;UACrBiB,GAAG,EAAEA,GAAG,IAAI,IAAI,CAAClD,MAAM,CAACS,UAAU;UAClC6C,OAAO,EAAE,IAAI,CAACjD,aAAa;UAC3BkD,WAAW,EAAE,CAAC;UACdC,YAAY,EAAExB,IAAI,CAACC,GAAG,CAAC;QACzB,CAAC;QAED,IAAI;UAEF,IAAMwB,QAAQ,GAAG,IAAI,CAACC,YAAY,CAACnB,IAAI,CAAC;UACxC,IAAMoB,cAAc,GAClB,CAAAR,OAAO,oBAAPA,OAAO,CAAES,QAAQ,KAChBH,QAAQ,GAAG,IAAI,CAACzD,MAAM,CAACY,oBAAoB,IAAI,EAACuC,OAAO,YAAPA,OAAO,CAAEU,UAAU,CAAC;UAEvE,IAAIF,cAAc,EAAE;YAClBP,KAAK,CAACU,UAAU,GAAG,IAAI;UAGzB;UAGA,IAAI,EAACX,OAAO,YAAPA,OAAO,CAAEY,WAAW,GAAE;YACzB,IAAI,CAAC5D,WAAW,CAACwC,GAAG,CAAChB,GAAG,EAAEyB,KAAK,CAAC;YAChC,IAAI,CAACY,kBAAkB,CAAC,CAAC;UAC3B;UAGA,IAAI,EAACb,OAAO,YAAPA,OAAO,CAAEU,UAAU,GAAE;YACxB,MAAM,IAAI,CAACI,YAAY,CAACtC,GAAG,EAAEyB,KAAK,CAAC;UACrC;UAEA,IAAI,CAACc,gBAAgB,CAAC,CAAC;QACzB,CAAC,CAAC,OAAOtB,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;QAC1C;MACF,CAAC;MAAA,SA/CKD,GAAGA,CAAAwB,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAArB,IAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAHL,GAAG;IAAA;EAAA;IAAAhB,GAAA;IAAAC,KAAA;MAAA,IAAA2C,OAAA,OAAAzC,kBAAA,CAAA5B,OAAA,EAoDT,WAAayB,GAAW,EAAiB;QACvC,IAAI;UACF,IAAI,CAACxB,WAAW,CAACqC,MAAM,CAACb,GAAG,CAAC;UAC5B,MAAM6C,qBAAY,CAACC,UAAU,CAAC,IAAI,CAACnE,cAAc,GAAGqB,GAAG,CAAC;UACxD,IAAI,CAACuC,gBAAgB,CAAC,CAAC;QACzB,CAAC,CAAC,OAAOtB,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC7C;MACF,CAAC;MAAA,SARK8B,MAAMA,CAAAC,GAAA;QAAA,OAAAJ,OAAA,CAAAxB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAN0B,MAAM;IAAA;EAAA;IAAA/C,GAAA;IAAAC,KAAA;MAAA,IAAAgD,MAAA,OAAA9C,kBAAA,CAAA5B,OAAA,EAaZ,aAA6B;QAAA,IAAA2E,KAAA;QAC3B,IAAI;UACF,IAAI,CAAC1E,WAAW,CAAC2E,KAAK,CAAC,CAAC;UAGxB,IAAMC,IAAI,SAASP,qBAAY,CAACQ,UAAU,CAAC,CAAC;UAC5C,IAAMC,SAAS,GAAGF,IAAI,CAACG,MAAM,CAAC,UAAAvD,GAAG;YAAA,OAAIA,GAAG,CAACwD,UAAU,CAACN,KAAI,CAACvE,cAAc,CAAC;UAAA,EAAC;UACzE,MAAMkE,qBAAY,CAACY,WAAW,CAACH,SAAS,CAAC;UAEzC,IAAI,CAACI,UAAU,CAAC,CAAC;QACnB,CAAC,CAAC,OAAOzC,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC5C;MACF,CAAC;MAAA,SAbKkC,KAAKA,CAAA;QAAA,OAAAF,MAAA,CAAA7B,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAL8B,KAAK;IAAA;EAAA;IAAAnD,GAAA;IAAAC,KAAA;MAAA,IAAA0D,QAAA,OAAAxD,kBAAA,CAAA5B,OAAA,EAkBX,WACEqF,OAAiD,EAClC;QAAA,IAAAC,MAAA;QACf,IAAMC,QAAQ,GAAGF,OAAO,CAACG,GAAG,CAAC,UAAAC,IAAA;UAAA,IAAGhE,GAAG,GAAAgE,IAAA,CAAHhE,GAAG;YAAEY,IAAI,GAAAoD,IAAA,CAAJpD,IAAI;YAAEW,GAAG,GAAAyC,IAAA,CAAHzC,GAAG;UAAA,OAC5CsC,MAAI,CAAC7C,GAAG,CAAChB,GAAG,EAAEY,IAAI,EAAEW,GAAG,CAAC;QAAA,CAC1B,CAAC;QAED,MAAM0C,OAAO,CAACC,UAAU,CAACJ,QAAQ,CAAC;MACpC,CAAC;MAAA,SARKK,OAAOA,CAAAC,GAAA;QAAA,OAAAT,QAAA,CAAAvC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAP8C,OAAO;IAAA;EAAA;IAAAnE,GAAA;IAAAC,KAAA,EAab,SAAAoE,QAAQA,CAAA,EAAe;MACrB,OAAAzF,MAAA,CAAAC,MAAA,KAAY,IAAI,CAACQ,KAAK;IACxB;EAAC;IAAAW,GAAA;IAAAC,KAAA,EAKD,SAAAqE,YAAYA,CAACtE,GAAW,EAA8B;MACpD,IAAMyB,KAAK,GAAG,IAAI,CAACjD,WAAW,CAACgC,GAAG,CAACR,GAAG,CAAC;MACvC,IAAI,CAACyB,KAAK,EAAE,OAAO,IAAI;MAEvB,OAAO;QACLC,SAAS,EAAED,KAAK,CAACC,SAAS;QAC1BH,GAAG,EAAEE,KAAK,CAACF,GAAG;QACdI,OAAO,EAAEF,KAAK,CAACE,OAAO;QACtBC,WAAW,EAAEH,KAAK,CAACG,WAAW;QAC9BC,YAAY,EAAEJ,KAAK,CAACI,YAAY;QAChCM,UAAU,EAAEV,KAAK,CAACU,UAAU;QAC5BoC,SAAS,EAAE9C,KAAK,CAAC8C;MACnB,CAAC;IACH;EAAC;IAAAvE,GAAA;IAAAC,KAAA;MAAA,IAAAuE,kBAAA,OAAArE,kBAAA,CAAA5B,OAAA,EAKD,WAAwBkG,OAAe,EAAiB;QAAA,IAAAC,MAAA;QAEtD,KAAK,IAAM1E,GAAG,IAAI,IAAI,CAACxB,WAAW,CAAC4E,IAAI,CAAC,CAAC,EAAE;UACzC,IAAIqB,OAAO,CAACE,IAAI,CAAC3E,GAAG,CAAC,EAAE;YACrB,IAAI,CAACxB,WAAW,CAACqC,MAAM,CAACb,GAAG,CAAC;UAC9B;QACF;QAGA,IAAI;UACF,IAAMoD,IAAI,SAASP,qBAAY,CAACQ,UAAU,CAAC,CAAC;UAC5C,IAAMC,SAAS,GAAGF,IAAI,CACnBG,MAAM,CAAC,UAAAvD,GAAG;YAAA,OAAIA,GAAG,CAACwD,UAAU,CAACkB,MAAI,CAAC/F,cAAc,CAAC;UAAA,EAAC,CAClDoF,GAAG,CAAC,UAAA/D,GAAG;YAAA,OAAIA,GAAG,CAAC4E,OAAO,CAACF,MAAI,CAAC/F,cAAc,EAAE,EAAE,CAAC;UAAA,EAAC,CAChD4E,MAAM,CAAC,UAAAvD,GAAG;YAAA,OAAIyE,OAAO,CAACE,IAAI,CAAC3E,GAAG,CAAC;UAAA,EAAC;UAEnC,IAAM6E,WAAW,GAAGvB,SAAS,CAACS,GAAG,CAAC,UAAA/D,GAAG;YAAA,OAAI0E,MAAI,CAAC/F,cAAc,GAAGqB,GAAG;UAAA,EAAC;UACnE,MAAM6C,qBAAY,CAACY,WAAW,CAACoB,WAAW,CAAC;QAC7C,CAAC,CAAC,OAAO5D,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QAC3D;QAEA,IAAI,CAACsB,gBAAgB,CAAC,CAAC;MACzB,CAAC;MAAA,SAvBKuC,iBAAiBA,CAAAC,GAAA;QAAA,OAAAP,kBAAA,CAAApD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjByD,iBAAiB;IAAA;EAAA;IAAA9E,GAAA;IAAAC,KAAA;MAAA,IAAA+E,eAAA,OAAA7E,kBAAA,CAAA5B,OAAA,EA4BvB,WAAgCyB,GAAW,EAAiC;QAC1E,IAAI;UACF,IAAMiF,MAAM,SAASpC,qBAAY,CAACqC,OAAO,CAAC,IAAI,CAACvG,cAAc,GAAGqB,GAAG,CAAC;UACpE,IAAI,CAACiF,MAAM,EAAE,OAAO,IAAI;UAExB,IAAMxD,KAAoB,GAAG0D,IAAI,CAACC,KAAK,CAACH,MAAM,CAAC;UAG/C,IAAIxD,KAAK,CAACU,UAAU,EAAE,CAGtB;UAEA,OAAOV,KAAK;QACd,CAAC,CAAC,OAAOR,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;UAC1C,OAAO,IAAI;QACb;MACF,CAAC;MAAA,SAlBaF,cAAcA,CAAAsE,GAAA;QAAA,OAAAL,eAAA,CAAA5D,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAdN,cAAc;IAAA;EAAA;IAAAf,GAAA;IAAAC,KAAA;MAAA,IAAAqF,aAAA,OAAAnF,kBAAA,CAAA5B,OAAA,EAuB5B,WACEyB,GAAW,EACXyB,KAAoB,EACL;QACf,IAAI;UACF,IAAM8D,UAAU,GAAGJ,IAAI,CAACK,SAAS,CAAC/D,KAAK,CAAC;UACxC,MAAMoB,qBAAY,CAAC4C,OAAO,CAAC,IAAI,CAAC9G,cAAc,GAAGqB,GAAG,EAAEuF,UAAU,CAAC;QACnE,CAAC,CAAC,OAAOtE,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC5C;MACF,CAAC;MAAA,SAVaqB,YAAYA,CAAAoD,GAAA,EAAAC,GAAA;QAAA,OAAAL,aAAA,CAAAlE,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAZiB,YAAY;IAAA;EAAA;IAAAtC,GAAA;IAAAC,KAAA,EAe1B,SAAQQ,YAAYA,CAACgB,KAAiB,EAAW;MAC/C,IAAMnB,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;MACtB,OAAOA,GAAG,GAAGmB,KAAK,CAACC,SAAS,GAAGD,KAAK,CAACF,GAAG;IAC1C;EAAC;IAAAvB,GAAA;IAAAC,KAAA,EAKD,SAAQU,iBAAiBA,CAACX,GAAW,EAAEyB,KAAiB,EAAQ;MAC9DA,KAAK,CAACG,WAAW,EAAE;MACnBH,KAAK,CAACI,YAAY,GAAGxB,IAAI,CAACC,GAAG,CAAC,CAAC;MAC/B,IAAI,CAAC9B,WAAW,CAACwC,GAAG,CAAChB,GAAG,EAAEyB,KAAK,CAAC;IAClC;EAAC;IAAAzB,GAAA;IAAAC,KAAA,EAKD,SAAQ8B,YAAYA,CAACnB,IAAS,EAAU;MACtC,OAAOuE,IAAI,CAACK,SAAS,CAAC5E,IAAI,CAAC,CAACgF,MAAM,GAAG,CAAC;IACxC;EAAC;IAAA5F,GAAA;IAAAC,KAAA,EAKD,SAAQoC,kBAAkBA,CAAA,EAAS;MAAA,IAAAwD,MAAA;MACjC,IAAMjC,OAAO,GAAGkC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACvH,WAAW,CAACoF,OAAO,CAAC,CAAC,CAAC;MACtD,IAAIlE,SAAS,GAAGkE,OAAO,CAACoC,MAAM,CAC5B,UAACC,GAAG,EAAAC,KAAA;QAAA,IAAAC,KAAA,OAAAC,eAAA,CAAA7H,OAAA,EAAA2H,KAAA;UAAKzE,KAAK,GAAA0E,KAAA;QAAA,OAAMF,GAAG,GAAGJ,MAAI,CAAC9D,YAAY,CAACN,KAAK,CAACb,IAAI,CAAC;MAAA,GACvD,CACF,CAAC;MAED,IAAIlB,SAAS,IAAI,IAAI,CAACrB,MAAM,CAACU,aAAa,EAAE;MAG5C6E,OAAO,CAACyC,IAAI,CAAC,UAAAC,KAAA,EAAAC,KAAA,EAAkB;QAAA,IAAAC,KAAA,OAAAJ,eAAA,CAAA7H,OAAA,EAAA+H,KAAA;UAAdG,CAAC,GAAAD,KAAA;QAAA,IAAAE,KAAA,OAAAN,eAAA,CAAA7H,OAAA,EAAAgI,KAAA;UAAMI,CAAC,GAAAD,KAAA;QACvB,IAAME,MAAM,GAAGH,CAAC,CAAC7E,WAAW,IAAIvB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGmG,CAAC,CAAC5E,YAAY,CAAC;QAC5D,IAAMgF,MAAM,GAAGF,CAAC,CAAC/E,WAAW,IAAIvB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGqG,CAAC,CAAC9E,YAAY,CAAC;QAC5D,OAAO+E,MAAM,GAAGC,MAAM;MACxB,CAAC,CAAC;MAGF,OAAOnH,SAAS,GAAG,IAAI,CAACrB,MAAM,CAACU,aAAa,IAAI6E,OAAO,CAACgC,MAAM,GAAG,CAAC,EAAE;QAClE,IAAAkB,KAAA,GAAqBlD,OAAO,CAACmD,KAAK,CAAC,CAAC;UAAAC,KAAA,OAAAZ,eAAA,CAAA7H,OAAA,EAAAuI,KAAA;UAA7B9G,GAAG,GAAAgH,KAAA;UAAEvF,KAAK,GAAAuF,KAAA;QACjB,IAAI,CAACxI,WAAW,CAACqC,MAAM,CAACb,GAAG,CAAC;QAC5BN,SAAS,IAAI,IAAI,CAACqC,YAAY,CAACN,KAAK,CAACb,IAAI,CAAC;MAC5C;IACF;EAAC;IAAAZ,GAAA;IAAAC,KAAA,EAKD,SAAQS,WAAWA,CACjBuG,IAAyC,EACzCC,UAAkB,EACZ;MACN,IAAI,CAAC,IAAI,CAAC7I,MAAM,CAACc,eAAe,EAAE;MAElC,QAAQ8H,IAAI;QACV,KAAK,WAAW;UACd,IAAI,CAAC5H,KAAK,CAACC,UAAU,EAAE;UACvB;QACF,KAAK,YAAY;UACf,IAAI,CAACD,KAAK,CAACG,WAAW,EAAE;UACxB;QACF,KAAK,MAAM;UACT,IAAI,CAACH,KAAK,CAACE,YAAY,EAAE;UACzB,IAAI,CAACF,KAAK,CAACI,aAAa,EAAE;UAC1B;MACJ;MAEA,IAAM0H,aAAa,GACjB,IAAI,CAAC9H,KAAK,CAACC,UAAU,GAAG,IAAI,CAACD,KAAK,CAACG,WAAW,GAAG,IAAI,CAACH,KAAK,CAACE,YAAY;MAC1E,IAAI,CAACF,KAAK,CAACO,OAAO,GAChBuH,aAAa,GAAG,CAAC,GACb,CAAC,IAAI,CAAC9H,KAAK,CAACC,UAAU,GAAG,IAAI,CAACD,KAAK,CAACG,WAAW,IAAI2H,aAAa,GAChE,CAAC;MAGP,IAAI,CAAC9H,KAAK,CAACQ,iBAAiB,GAC1B,IAAI,CAACR,KAAK,CAACQ,iBAAiB,GAAG,GAAG,GAAGqH,UAAU,GAAG,GAAG;IACzD;EAAC;IAAAlH,GAAA;IAAAC,KAAA,EAKD,SAAQsC,gBAAgBA,CAAA,EAAS;MAAA,IAAA6E,MAAA;MAC/B,IAAI,CAAC/H,KAAK,CAACM,UAAU,GAAG,IAAI,CAACnB,WAAW,CAAC6I,IAAI;MAC7C,IAAI,CAAChI,KAAK,CAACK,SAAS,GAAGoG,KAAK,CAACC,IAAI,CAAC,IAAI,CAACvH,WAAW,CAAC8I,MAAM,CAAC,CAAC,CAAC,CAACtB,MAAM,CACjE,UAACC,GAAG,EAAExE,KAAK;QAAA,OAAKwE,GAAG,GAAGmB,MAAI,CAACrF,YAAY,CAACN,KAAK,CAACb,IAAI,CAAC;MAAA,GACnD,CACF,CAAC;IACH;EAAC;IAAAZ,GAAA;IAAAC,KAAA,EAKD,SAAQyD,UAAUA,CAAA,EAAS;MACzB,IAAI,CAACrE,KAAK,GAAG;QACXC,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE,CAAC;QACfC,WAAW,EAAE,CAAC;QACdC,aAAa,EAAE,CAAC;QAChBC,SAAS,EAAE,CAAC;QACZC,UAAU,EAAE,CAAC;QACbC,OAAO,EAAE,CAAC;QACVC,iBAAiB,EAAE;MACrB,CAAC;IACH;EAAC;IAAAG,GAAA;IAAAC,KAAA,EAKD,SAAQH,iBAAiBA,CAAA,EAAS;MAAA,IAAAyH,MAAA;MAChC,IAAI,CAACC,YAAY,GAAGC,WAAW,CAAC,YAAM;QACpCF,MAAI,CAACG,OAAO,CAAC,CAAC;MAChB,CAAC,EAAE,IAAI,CAACrJ,MAAM,CAACe,eAAe,CAAC;IACjC;EAAC;IAAAY,GAAA;IAAAC,KAAA,EAKD,SAAQyH,OAAOA,CAAA,EAAS;MACtB,IAAMpH,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;MAEtB,SAAAqH,KAAA,IAA2B,IAAI,CAACnJ,WAAW,CAACoF,OAAO,CAAC,CAAC,EAAE;QAAA,IAAAgE,KAAA,OAAAxB,eAAA,CAAA7H,OAAA,EAAAoJ,KAAA;QAAA,IAA3C3H,GAAG,GAAA4H,KAAA;QAAA,IAAEnG,KAAK,GAAAmG,KAAA;QACpB,IAAI,CAAC,IAAI,CAACnH,YAAY,CAACgB,KAAK,CAAC,EAAE;UAC7B,IAAI,CAACjD,WAAW,CAACqC,MAAM,CAACb,GAAG,CAAC;QAC9B;MACF;MAEA,IAAI,CAACuC,gBAAgB,CAAC,CAAC;IACzB;EAAC;IAAAvC,GAAA;IAAAC,KAAA,EAKD,SAAA4H,OAAOA,CAAA,EAAS;MACd,IAAI,IAAI,CAACL,YAAY,EAAE;QACrBM,aAAa,CAAC,IAAI,CAACN,YAAY,CAAC;MAClC;MACA,IAAI,CAAChJ,WAAW,CAAC2E,KAAK,CAAC,CAAC;IAC1B;EAAC;AAAA;AAKI,IAAM4E,YAAY,GAAA3J,OAAA,CAAA2J,YAAA,GAAG,IAAI5J,YAAY,CAAC,CAAC;AAAC,IAAA6J,QAAA,GAAA5J,OAAA,CAAAG,OAAA,GAChCwJ,YAAY", "ignoreList": []}