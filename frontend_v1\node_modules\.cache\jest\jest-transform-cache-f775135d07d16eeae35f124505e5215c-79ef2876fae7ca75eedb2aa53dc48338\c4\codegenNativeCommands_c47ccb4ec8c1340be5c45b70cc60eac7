e5a7f6e8d960370bc71df81469d13c05
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _require = require("../ReactNative/RendererProxy"),
  dispatchCommand = _require.dispatchCommand;
function codegenNativeCommands(options) {
  var commandObj = {};
  options.supportedCommands.forEach(function (command) {
    commandObj[command] = function (ref) {
      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
        args[_key - 1] = arguments[_key];
      }
      dispatchCommand(ref, command, args);
    };
  });
  return commandObj;
}
var _default = exports.default = codegenNativeCommands;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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