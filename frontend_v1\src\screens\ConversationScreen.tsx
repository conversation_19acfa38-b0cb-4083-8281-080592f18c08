/**
 * Conversation Screen - Individual Chat Interface
 *
 * Component Contract:
 * - Displays individual conversation with a service provider
 * - Supports sending and receiving messages
 * - Implements proper date formatting
 * - Follows accessibility guidelines and testing standards
 * - Integrates with navigation and state management
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Platform,
  StyleSheet,
  FlatList,
  TextInput,
  KeyboardAvoidingView,
  Alert,
} from 'react-native';

import { SafeAreaWrapper } from '../components/ui/SafeAreaWrapper';
import { useTheme } from '../contexts/ThemeContext';
import type { CustomerStackParamList } from '../navigation/types';
import { messagingService } from '../services/messagingService';
import { createWebSocketService } from '../services/websocketService';
import { useAuthStore } from '../store/authSlice';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../utils/responsiveUtils';

// Message interface
interface Message {
  id: string;
  content: string;
  senderId: string;
  senderName: string;
  timestamp: string;
  isFromCurrentUser: boolean;
}

type ConversationScreenRouteProp = RouteProp<
  CustomerStackParamList,
  'Conversation'
>;
type ConversationScreenNavigationProp = StackNavigationProp<
  CustomerStackParamList,
  'Conversation'
>;

export const ConversationScreen: React.FC = () => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const navigation = useNavigation<ConversationScreenNavigationProp>();
  const route = useRoute<ConversationScreenRouteProp>();
  const { conversationId, participantName } = route.params;
  const { user: currentUser } = useAuthStore();

  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);
  const flatListRef = useRef<FlatList>(null);
  const wsService = useRef<any>(null);
  const typingTimer = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    loadMessages();
    setupWebSocket();

    return () => {
      if (wsService.current) {
        wsService.current.disconnect();
      }
      if (typingTimer.current) {
        clearTimeout(typingTimer.current);
      }
    };
  }, [conversationId]);

  const loadMessages = async () => {
    setLoading(true);
    try {
      // Load messages from backend API
      const response = await messagingService.getMessages(
        conversationId,
        1,
        50,
      );

      // Transform backend messages to match our interface
      const transformedMessages: Message[] = response.messages.map(msg => ({
        id: msg.id.toString(),
        content: msg.content,
        senderId: msg.sender.id.toString(),
        senderName: msg.sender.full_name,
        timestamp: msg.created_at,
        isFromCurrentUser: currentUser
          ? msg.sender.id === currentUser.id
          : false,
      }));

      setMessages(transformedMessages);

      // Mark messages as read
      const unreadMessageIds = response.messages
        .filter(msg => !msg.is_read && msg.sender.id !== currentUser?.id)
        .map(msg => msg.id.toString());

      if (unreadMessageIds.length > 0 && wsService.current) {
        wsService.current.markMessagesRead(unreadMessageIds);
      }
    } catch (error) {
      console.error('Failed to load messages:', error);
      Alert.alert('Error', 'Failed to load messages');
    } finally {
      setLoading(false);
    }
  };

  const setupWebSocket = async () => {
    try {
      wsService.current = createWebSocketService({
        url: `ws://192.168.2.65:8000/ws/messaging/${conversationId}/`,
      });

      // Set up event listeners
      wsService.current.on('chat_message', (data: any) => {
        const newMessage: Message = {
          id: data.message.id.toString(),
          content: data.message.content,
          senderId: data.message.sender.id.toString(),
          senderName: data.message.sender.full_name,
          timestamp: data.message.created_at,
          isFromCurrentUser: currentUser
            ? data.message.sender.id === currentUser.id
            : false,
        };

        setMessages(prev => [...prev, newMessage]);

        // Scroll to bottom
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }, 100);
      });

      wsService.current.on('typing_indicator', (data: any) => {
        if (data.is_typing) {
          setTypingUsers(prev => [
            ...prev.filter(id => id !== data.user_id),
            data.user_id,
          ]);
        } else {
          setTypingUsers(prev => prev.filter(id => id !== data.user_id));
        }
      });

      wsService.current.on('messages_read', (data: any) => {
        // Update message read status if needed
        console.log('Messages read:', data.message_ids);
      });

      // Connect to WebSocket with auth token
      const { authToken } = useAuthStore.getState();
      await wsService.current.connect(authToken);
    } catch (error) {
      console.error('Failed to setup WebSocket:', error);
    }
  };

  const formatMessageTime = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      const now = new Date();
      const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

      if (diffInHours < 1) {
        const diffInMinutes = Math.floor(diffInHours * 60);
        return diffInMinutes < 1 ? 'Just now' : `${diffInMinutes}m ago`;
      } else if (diffInHours < 24) {
        return `${Math.floor(diffInHours)}h ago`;
      } else if (diffInHours < 48) {
        return 'Yesterday';
      } else {
        return date.toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
        });
      }
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Unknown time';
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || sending) return;

    const messageContent = newMessage.trim();
    setNewMessage('');
    setSending(true);

    try {
      // Send via WebSocket for real-time delivery
      if (wsService.current && wsService.current.isConnected()) {
        wsService.current.sendChatMessage(messageContent);
      } else {
        // Fallback to HTTP API if WebSocket is not connected
        await messagingService.sendMessage(conversationId, messageContent);
        // Reload messages to get the new message
        await loadMessages();
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      Alert.alert('Error', 'Failed to send message. Please try again.');
      // Restore the message content if sending failed
      setNewMessage(messageContent);
    } finally {
      setSending(false);
    }
  };

  const handleTyping = (text: string) => {
    setNewMessage(text);

    // Send typing indicator
    if (wsService.current && wsService.current.isConnected()) {
      wsService.current.sendTypingIndicator(true);

      // Clear previous timer
      if (typingTimer.current) {
        clearTimeout(typingTimer.current);
      }

      // Set timer to stop typing indicator
      typingTimer.current = setTimeout(() => {
        wsService.current?.sendTypingIndicator(false);
      }, 1000);
    }
  };

  const renderMessage = ({ item }: { item: Message }) => (
    <View
      style={[
        styles.messageContainer,
        item.isFromCurrentUser ? styles.sentMessage : styles.receivedMessage,
      ]}>
      <View
        style={[
          styles.messageBubble,
          item.isFromCurrentUser ? styles.sentBubble : styles.receivedBubble,
        ]}>
        <Text
          style={[
            styles.messageText,
            item.isFromCurrentUser ? styles.sentText : styles.receivedText,
          ]}>
          {item.content}
        </Text>
      </View>
      <Text style={styles.messageTime}>
        {formatMessageTime(item.timestamp)}
      </Text>
    </View>
  );

  return (
    <SafeAreaWrapper backgroundColor={colors.background.primary}>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.backButton}
            testID="back-button">
            <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <View style={styles.headerInfo}>
            <Text style={styles.headerTitle}>{participantName}</Text>
            <Text style={styles.headerSubtitle}>Online</Text>
          </View>
          <View style={styles.headerActions}>
            <TouchableOpacity style={styles.callButton}>
              <Ionicons name="call" size={24} color={colors.sage400} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Messages List */}
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={item => item.id}
          style={styles.messagesList}
          contentContainerStyle={styles.messagesContainer}
          showsVerticalScrollIndicator={false}
          testID="messages-list"
        />

        {/* Typing Indicator */}
        {typingUsers.length > 0 && (
          <View style={styles.typingContainer}>
            <Text style={styles.typingText}>
              {typingUsers.length === 1
                ? `Someone is typing...`
                : `${typingUsers.length} people are typing...`}
            </Text>
          </View>
        )}

        {/* Message Input */}
        <View style={styles.inputContainer}>
          <TextInput
            style={styles.textInput}
            value={newMessage}
            onChangeText={handleTyping}
            placeholder="Type a message..."
            placeholderTextColor={colors.text.tertiary}
            multiline
            maxLength={500}
            testID="message-input"
          />
          <TouchableOpacity
            onPress={sendMessage}
            style={[
              styles.sendButton,
              { opacity: newMessage.trim() && !sending ? 1 : 0.5 },
            ]}
            disabled={!newMessage.trim() || sending}
            testID="send-button">
            <Ionicons
              name={sending ? 'hourglass' : 'send'}
              size={20}
              color="#FFFFFF"
            />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaWrapper>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: getResponsiveSpacing(16),
      paddingVertical: getResponsiveSpacing(12),
      borderBottomWidth: 1,
      borderBottomColor: colors.border.light,
      backgroundColor: colors.surface.primary,
    },
    backButton: {
      padding: getResponsiveSpacing(8),
      marginRight: getResponsiveSpacing(8),
    },
    headerInfo: {
      flex: 1,
    },
    headerActions: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: getResponsiveSpacing(8),
    },
    headerTitle: {
      fontSize: getResponsiveFontSize(18),
      fontWeight: '600',
      color: colors.text.primary,
    },
    headerSubtitle: {
      fontSize: getResponsiveFontSize(14),
      color: colors.sage400,
      marginTop: getResponsiveSpacing(2),
    },
    callButton: {
      padding: getResponsiveSpacing(8),
    },
    messagesList: {
      flex: 1,
      backgroundColor: colors.background.secondary,
    },
    messagesContainer: {
      paddingVertical: getResponsiveSpacing(16),
    },
    typingContainer: {
      paddingHorizontal: getResponsiveSpacing(16),
      paddingVertical: getResponsiveSpacing(8),
      backgroundColor: colors.background.secondary,
    },
    typingText: {
      fontSize: getResponsiveFontSize(12),
      color: colors.text.secondary,
      fontStyle: 'italic',
    },
    messageContainer: {
      marginVertical: getResponsiveSpacing(4),
      paddingHorizontal: getResponsiveSpacing(16),
    },
    sentMessage: {
      alignItems: 'flex-end',
    },
    receivedMessage: {
      alignItems: 'flex-start',
    },
    messageBubble: {
      maxWidth: '80%',
      paddingHorizontal: getResponsiveSpacing(16),
      paddingVertical: getResponsiveSpacing(12),
      borderRadius: getResponsiveSpacing(20),
    },
    sentBubble: {
      backgroundColor: colors.sage400,
    },
    receivedBubble: {
      backgroundColor: colors.surface.primary,
      borderWidth: 1,
      borderColor: colors.border.light,
    },
    messageText: {
      fontSize: getResponsiveFontSize(16),
      lineHeight: getResponsiveFontSize(22),
    },
    sentText: {
      color: '#FFFFFF',
    },
    receivedText: {
      color: colors.text.primary,
    },
    messageTime: {
      fontSize: getResponsiveFontSize(12),
      color: colors.text.tertiary,
      marginTop: getResponsiveSpacing(4),
    },
    inputContainer: {
      flexDirection: 'row',
      alignItems: 'flex-end',
      paddingHorizontal: getResponsiveSpacing(16),
      paddingVertical: getResponsiveSpacing(12),
      backgroundColor: colors.surface.primary,
      borderTopWidth: 1,
      borderTopColor: colors.border.light,
    },
    textInput: {
      flex: 1,
      borderWidth: 1,
      borderColor: colors.border.light,
      borderRadius: getResponsiveSpacing(20),
      paddingHorizontal: getResponsiveSpacing(16),
      paddingVertical: getResponsiveSpacing(12),
      fontSize: getResponsiveFontSize(16),
      color: colors.text.primary,
      backgroundColor: colors.background.primary,
      maxHeight: getResponsiveSpacing(100),
      marginRight: getResponsiveSpacing(12),
    },
    sendButton: {
      backgroundColor: colors.sage400,
      borderRadius: getResponsiveSpacing(20),
      padding: getResponsiveSpacing(12),
      justifyContent: 'center',
      alignItems: 'center',
    },
  });

export default ConversationScreen;
