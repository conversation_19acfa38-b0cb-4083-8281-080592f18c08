576fcc51c210006700aff59d1cc6f218
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _reactNative = require("@testing-library/react-native");
var _react = _interopRequireDefault(require("react"));
var _reactNative2 = require("react-native");
var _ThemeContext = require("../../../contexts/ThemeContext");
var _HyperMinimalistTheme = require("../../../design-system/HyperMinimalistTheme");
var _Card = require("../Card");
var _jsxRuntime = require("react/jsx-runtime");
var TestWrapper = function TestWrapper(_ref) {
  var children = _ref.children;
  return (0, _jsxRuntime.jsx)(_ThemeContext.ThemeProvider, {
    theme: _HyperMinimalistTheme.HyperMinimalistTheme,
    children: children
  });
};
describe('Card Component', function () {
  describe('Basic Rendering', function () {
    it('should render with children', function () {
      var _render = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Card.Card, {
            children: (0, _jsxRuntime.jsx)(_reactNative2.Text, {
              children: "Card Content"
            })
          })
        })),
        getByText = _render.getByText;
      expect(getByText('Card Content')).toBeTruthy();
    });
    it('should render with text children', function () {
      var _render2 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Card.Card, {
            children: (0, _jsxRuntime.jsx)(_reactNative2.Text, {
              children: "Simple text content"
            })
          })
        })),
        getByText = _render2.getByText;
      expect(getByText('Simple text content')).toBeTruthy();
    });
    it('should render with complex children', function () {
      var _render3 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsxs)(_Card.Card, {
            children: [(0, _jsxRuntime.jsx)(_reactNative2.Text, {
              children: "Title"
            }), (0, _jsxRuntime.jsx)(_reactNative2.Text, {
              children: "Description"
            })]
          })
        })),
        getByText = _render3.getByText;
      expect(getByText('Title')).toBeTruthy();
      expect(getByText('Description')).toBeTruthy();
    });
  });
  describe('Styling and Props', function () {
    it('should apply default card styling', function () {
      var _render4 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Card.Card, {
            testID: "test-card",
            children: (0, _jsxRuntime.jsx)(_reactNative2.Text, {
              children: "Content"
            })
          })
        })),
        getByTestId = _render4.getByTestId;
      var card = getByTestId('test-card');
      expect(card).toBeTruthy();
    });
    it('should accept custom styles through Box props', function () {
      var _render5 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Card.Card, {
            testID: "test-card",
            marginTop: "large",
            children: (0, _jsxRuntime.jsx)(_reactNative2.Text, {
              children: "Content"
            })
          })
        })),
        getByTestId = _render5.getByTestId;
      var card = getByTestId('test-card');
      expect(card).toBeTruthy();
    });
    it('should override default props when provided', function () {
      var _render6 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Card.Card, {
            testID: "test-card",
            backgroundColor: "primary",
            padding: "large",
            children: (0, _jsxRuntime.jsx)(_reactNative2.Text, {
              children: "Content"
            })
          })
        })),
        getByTestId = _render6.getByTestId;
      var card = getByTestId('test-card');
      expect(card).toBeTruthy();
    });
    it('should support custom border radius', function () {
      var _render7 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Card.Card, {
            testID: "test-card",
            borderRadius: "large",
            children: (0, _jsxRuntime.jsx)(_reactNative2.Text, {
              children: "Content"
            })
          })
        })),
        getByTestId = _render7.getByTestId;
      var card = getByTestId('test-card');
      expect(card).toBeTruthy();
    });
  });
  describe('Accessibility', function () {
    it('should support accessibility props', function () {
      var _render8 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Card.Card, {
            accessibilityLabel: "Information Card",
            children: (0, _jsxRuntime.jsx)(_reactNative2.Text, {
              children: "Card Content"
            })
          })
        })),
        getByLabelText = _render8.getByLabelText;
      expect(getByLabelText('Information Card')).toBeTruthy();
    });
    it('should support accessibility hint', function () {
      var _render9 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Card.Card, {
            testID: "test-card",
            accessibilityHint: "This card contains important information",
            children: (0, _jsxRuntime.jsx)(_reactNative2.Text, {
              children: "Content"
            })
          })
        })),
        getByTestId = _render9.getByTestId;
      var card = getByTestId('test-card');
      expect(card.props.accessibilityHint).toBe('This card contains important information');
    });
    it('should support accessibility role', function () {
      var _render0 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Card.Card, {
            testID: "test-card",
            accessibilityRole: "button",
            children: (0, _jsxRuntime.jsx)(_reactNative2.Text, {
              children: "Clickable Card"
            })
          })
        })),
        getByTestId = _render0.getByTestId;
      var card = getByTestId('test-card');
      expect(card.props.accessibilityRole).toBe('button');
    });
  });
  describe('Box Integration', function () {
    it('should inherit all Box component capabilities', function () {
      var _render1 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsxs)(_Card.Card, {
            testID: "test-card",
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            children: [(0, _jsxRuntime.jsx)(_reactNative2.Text, {
              children: "Left"
            }), (0, _jsxRuntime.jsx)(_reactNative2.Text, {
              children: "Right"
            })]
          })
        })),
        getByTestId = _render1.getByTestId;
      var card = getByTestId('test-card');
      expect(card).toBeTruthy();
    });
    it('should support Box spacing props', function () {
      var _render10 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Card.Card, {
            testID: "test-card",
            margin: "large",
            paddingHorizontal: "xlarge",
            paddingVertical: "small",
            children: (0, _jsxRuntime.jsx)(_reactNative2.Text, {
              children: "Spaced Content"
            })
          })
        })),
        getByTestId = _render10.getByTestId;
      var card = getByTestId('test-card');
      expect(card).toBeTruthy();
    });
    it('should support Box layout props', function () {
      var _render11 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Card.Card, {
            testID: "test-card",
            width: "100%",
            height: 200,
            flex: 1,
            children: (0, _jsxRuntime.jsx)(_reactNative2.Text, {
              children: "Layout Content"
            })
          })
        })),
        getByTestId = _render11.getByTestId;
      var card = getByTestId('test-card');
      expect(card).toBeTruthy();
    });
  });
  describe('Component Contract Compliance', function () {
    it('should render as a styled container', function () {
      var _render12 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Card.Card, {
            testID: "test-card",
            children: (0, _jsxRuntime.jsx)(_reactNative2.Text, {
              children: "Container Content"
            })
          })
        })),
        getByTestId = _render12.getByTestId;
      var card = getByTestId('test-card');
      expect(card).toBeTruthy();
    });
    it('should provide consistent card appearance', function () {
      var _render13 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_Card.Card, {
            testID: "test-card",
            children: (0, _jsxRuntime.jsx)(_reactNative2.Text, {
              children: "Consistent Styling"
            })
          })
        })),
        getByTestId = _render13.getByTestId;
      var card = getByTestId('test-card');
      expect(card).toBeTruthy();
    });
    it('should handle complex content structures', function () {
      var _render14 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsxs)(_Card.Card, {
            testID: "test-card",
            children: [(0, _jsxRuntime.jsx)(_reactNative2.Text, {
              children: "Header"
            }), (0, _jsxRuntime.jsx)(_reactNative2.Text, {
              children: "Body content with multiple lines"
            }), (0, _jsxRuntime.jsx)(_reactNative2.Text, {
              children: "Footer"
            })]
          })
        })),
        getByText = _render14.getByText,
        getByTestId = _render14.getByTestId;
      expect(getByTestId('test-card')).toBeTruthy();
      expect(getByText('Header')).toBeTruthy();
      expect(getByText('Body content with multiple lines')).toBeTruthy();
      expect(getByText('Footer')).toBeTruthy();
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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