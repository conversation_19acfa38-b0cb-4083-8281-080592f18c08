{"version": 3, "names": ["_apiClient", "require", "_cachingService", "MOCK_BOOKINGS", "id", "customer_id", "provider_id", "provider_name", "service_id", "service_name", "service_category", "scheduled_datetime", "duration_minutes", "base_price", "total_amount", "status", "payment_status", "notes", "created_at", "updated_at", "MOCK_TIME_SLOTS", "time", "available", "date", "BookingService", "_classCallCheck2", "default", "baseUrl", "_createClass2", "key", "value", "_getBookings", "_asyncToGenerator2", "filters", "params", "URLSearchParams", "Object", "entries", "for<PERSON>ach", "_ref", "_ref2", "_slicedToArray2", "append", "url", "toString", "response", "apiClient", "get", "data", "error", "errorMessage", "console", "warn", "message", "details", "fallbackUsed", "filteredBookings", "filter", "booking", "count", "length", "next", "previous", "results", "getBookings", "_x", "apply", "arguments", "_getUpcomingBookings", "now", "Date", "upcomingBookings", "bookingDate", "getUpcomingBookings", "_getBookingDetails", "bookingId", "getBookingDetails", "_x2", "_createBooking", "bookingData", "post", "service", "serviceId", "provider", "providerId", "timeSlot", "special_requests", "specialRequests", "payment_method", "paymentMethod", "cachingService", "invalidate<PERSON><PERSON><PERSON>", "log", "Error", "createFallbackBooking", "createBooking", "_x3", "mockBooking", "booking_number", "slice", "toISOString", "push", "_updateBookingStatus", "patch", "updateBookingStatus", "_x4", "_x5", "_cancelBooking", "reason", "cancellation_reason", "bookingIndex", "findIndex", "b", "assign", "cancelBooking", "_x6", "_x7", "_confirmBooking", "confirmBooking", "_x8", "_getAvailableTimeSlots", "cachedData", "getCachedApiResponse", "enabled", "ttl", "cacheApiResponse", "getFallbackTimeSlots", "getAvailableTimeSlots", "_x9", "_x0", "slot", "_getBookingStats", "total_bookings", "upcoming_bookings", "completed_bookings", "cancelled_bookings", "total_revenue", "reduce", "sum", "getBookingStats", "_updateBooking", "updates", "updateBooking", "_x1", "_x10", "_rescheduleBooking", "newDate", "newTimeSlot", "rescheduleBooking", "_x11", "_x12", "_x13", "_getRecentBookings", "limit", "undefined", "getFallbackRecentBookings", "getRecentBookings", "sort", "a", "getTime", "_getUpcomingBookingsList", "getUpcomingBookingsList", "bookingService", "exports", "_default"], "sources": ["bookingService.ts"], "sourcesContent": ["/**\n * Booking Service - API Integration for Booking Management\n *\n * Component Contract:\n * - Handles all booking-related API calls\n * - Provides booking creation, retrieval, and management\n * - Integrates with backend booking endpoints\n * - Supports time slot availability checking\n * - Handles booking status updates and cancellations\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport { apiClient } from './apiClient';\nimport { cachingService } from './cachingService';\n\n// Types\nexport type BookingStatus =\n  | 'pending'\n  | 'confirmed'\n  | 'in_progress'\n  | 'completed'\n  | 'cancelled'\n  | 'no_show';\n\nexport type PaymentStatus =\n  | 'pending'\n  | 'processing'\n  | 'paid'\n  | 'failed'\n  | 'refunded'\n  | 'partially_refunded';\n\nexport interface Booking {\n  id: string;\n  bookingNumber: string;\n  service: {\n    id: string;\n    name: string;\n    price: number;\n    duration: number;\n    category?: string;\n  };\n  provider: {\n    id: string;\n    name: string;\n    image?: string;\n    address?: string;\n    phone?: string;\n    email?: string;\n  };\n  customer: {\n    id: string;\n    firstName: string;\n    lastName: string;\n    email: string;\n    phone?: string;\n  };\n  date: string;\n  time: string;\n  endTime?: string;\n  status: BookingStatus;\n  payment: {\n    status: PaymentStatus;\n    method?: string;\n    amount: number;\n    transactionId?: string;\n    receiptUrl?: string;\n  };\n  notes?: string;\n  specialRequests?: string;\n  cancellationReason?: string;\n  cancellationPolicy?: string;\n  refundAmount?: number;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface TimeSlot {\n  time: string;\n  available: boolean;\n  provider_id: string;\n  date: string;\n  price?: number;\n  duration?: number;\n}\n\nexport interface BookingFilters {\n  status?: BookingStatus;\n  dateFrom?: string;\n  dateTo?: string;\n  providerId?: string;\n  serviceCategory?: string;\n  paymentStatus?: PaymentStatus;\n}\nexport interface BookingData {\n  serviceId: string;\n  providerId: string;\n  date: string;\n  timeSlot: string;\n  notes?: string;\n  customerInfo?: {\n    firstName: string;\n    lastName: string;\n    email: string;\n    phone: string;\n  };\n  paymentMethod?: string;\n  specialRequests?: string;\n}\n\n// Mock data for fallback\nconst MOCK_BOOKINGS: Booking[] = [\n  {\n    id: 'booking_1',\n    customer_id: 'customer_1',\n    provider_id: 'provider_1',\n    provider_name: 'Bella Beauty Studio',\n    service_id: 'service_1',\n    service_name: 'Hair Cut & Style',\n    service_category: 'Hair',\n    scheduled_datetime: '2024-01-20T14:00:00Z',\n    duration_minutes: 60,\n    base_price: 45.0,\n    total_amount: 45.0,\n    status: 'confirmed',\n    payment_status: 'paid',\n    notes: 'Regular trim and style',\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-15T10:00:00Z',\n  },\n  {\n    id: 'booking_2',\n    customer_id: 'customer_1',\n    provider_id: 'provider_2',\n    provider_name: 'Glow Spa & Wellness',\n    service_id: 'service_2',\n    service_name: 'Facial Treatment',\n    service_category: 'Skincare',\n    scheduled_datetime: '2024-01-10T11:00:00Z',\n    duration_minutes: 90,\n    base_price: 75.0,\n    total_amount: 75.0,\n    status: 'completed',\n    payment_status: 'paid',\n    notes: 'Deep cleansing facial',\n    created_at: '2024-01-08T14:30:00Z',\n    updated_at: '2024-01-10T12:30:00Z',\n  },\n];\n\nconst MOCK_TIME_SLOTS: TimeSlot[] = [\n  {\n    time: '09:00',\n    available: true,\n    provider_id: 'provider_1',\n    date: '2024-01-20',\n  },\n  {\n    time: '09:30',\n    available: true,\n    provider_id: 'provider_1',\n    date: '2024-01-20',\n  },\n  {\n    time: '10:00',\n    available: false,\n    provider_id: 'provider_1',\n    date: '2024-01-20',\n  },\n  {\n    time: '10:30',\n    available: true,\n    provider_id: 'provider_1',\n    date: '2024-01-20',\n  },\n  {\n    time: '11:00',\n    available: true,\n    provider_id: 'provider_1',\n    date: '2024-01-20',\n  },\n  {\n    time: '11:30',\n    available: false,\n    provider_id: 'provider_1',\n    date: '2024-01-20',\n  },\n  {\n    time: '14:00',\n    available: true,\n    provider_id: 'provider_1',\n    date: '2024-01-20',\n  },\n  {\n    time: '14:30',\n    available: true,\n    provider_id: 'provider_1',\n    date: '2024-01-20',\n  },\n  {\n    time: '15:00',\n    available: true,\n    provider_id: 'provider_1',\n    date: '2024-01-20',\n  },\n  {\n    time: '15:30',\n    available: false,\n    provider_id: 'provider_1',\n    date: '2024-01-20',\n  },\n  {\n    time: '16:00',\n    available: true,\n    provider_id: 'provider_1',\n    date: '2024-01-20',\n  },\n  {\n    time: '16:30',\n    available: true,\n    provider_id: 'provider_1',\n    date: '2024-01-20',\n  },\n];\n\nexport interface Booking {\n  id: string;\n  booking_number: string;\n  customer_id: string;\n  provider_id: string;\n  provider_name: string;\n  service_id: string;\n  service_name: string;\n  service_category: string;\n  scheduled_datetime: string;\n  duration_minutes: number;\n  status:\n    | 'pending'\n    | 'confirmed'\n    | 'in_progress'\n    | 'completed'\n    | 'cancelled'\n    | 'no_show';\n  payment_status: 'pending' | 'paid' | 'partially_paid' | 'refunded' | 'failed';\n  total_amount: number;\n  base_price: number;\n  notes?: string;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface TimeSlot {\n  id: string;\n  time: string;\n  available: boolean;\n  provider_id: string;\n  date: string;\n}\n\nexport interface BookingResponse {\n  results: Booking[];\n  count: number;\n  next?: string;\n  previous?: string;\n}\n\nexport interface BookingFilters {\n  status?: string;\n  date_from?: string;\n  date_to?: string;\n  provider?: string;\n  service?: string;\n}\n\nclass BookingService {\n  private readonly baseUrl = '/api/bookings/bookings';\n\n  /**\n   * Get bookings with optional filters\n   */\n  async getBookings(filters?: BookingFilters): Promise<BookingResponse> {\n    try {\n      const params = new URLSearchParams();\n      if (filters) {\n        Object.entries(filters).forEach(([key, value]) => {\n          if (value) {\n            params.append(key, value);\n          }\n        });\n      }\n\n      const url = `${this.baseUrl}/${params.toString() ? `?${params.toString()}` : ''}`;\n      const response = await apiClient.get<BookingResponse>(url);\n      return response.data;\n    } catch (error: any) {\n      // Enhanced error handling for different types of errors\n      let errorMessage = 'Error fetching bookings';\n\n      if (error?.status === 401) {\n        errorMessage = 'Authentication error - token may be invalid or expired';\n        console.warn(\n          '🔐 Bookings API: Authentication failed, using fallback data',\n        );\n      } else if (error?.status === 403) {\n        errorMessage = 'Access denied - insufficient permissions';\n        console.warn('🚫 Bookings API: Access denied, using fallback data');\n      } else if (error?.status === 404) {\n        errorMessage = 'Bookings endpoint not found';\n        console.warn(\n          '🔍 Bookings API: Endpoint not found, using fallback data',\n        );\n      } else if (error?.status === 500) {\n        errorMessage = 'Server error occurred';\n        console.warn('🔥 Bookings API: Server error, using fallback data');\n      } else if (error?.status === 0 || !error?.status) {\n        errorMessage = 'Network connection error';\n        console.warn('🌐 Bookings API: Network error, using fallback data');\n      } else {\n        errorMessage = `API error (${error?.status || 'unknown'})`;\n        console.warn(`⚠️ Bookings API: ${errorMessage}, using fallback data`);\n      }\n\n      console.error('Bookings API Error Details:', {\n        status: error?.status,\n        message: error?.message,\n        details: error?.details,\n        fallbackUsed: true,\n      });\n\n      // Return mock data as fallback\n      let filteredBookings = MOCK_BOOKINGS;\n\n      if (filters?.status) {\n        filteredBookings = filteredBookings.filter(\n          booking => booking.status === filters.status,\n        );\n      }\n\n      return {\n        count: filteredBookings.length,\n        next: null,\n        previous: null,\n        results: filteredBookings,\n      };\n    }\n  }\n\n  /**\n   * Get upcoming bookings\n   */\n  async getUpcomingBookings(): Promise<BookingResponse> {\n    try {\n      const response = await apiClient.get<BookingResponse>(\n        `${this.baseUrl}/upcoming/`,\n      );\n      return response.data;\n    } catch (error: any) {\n      // Enhanced error handling for upcoming bookings\n      if (error?.status === 401) {\n        console.warn(\n          '🔐 Upcoming Bookings API: Authentication failed, using fallback data',\n        );\n      } else if (error?.status === 403) {\n        console.warn(\n          '🚫 Upcoming Bookings API: Access denied, using fallback data',\n        );\n      } else if (error?.status === 0 || !error?.status) {\n        console.warn(\n          '🌐 Upcoming Bookings API: Network error, using fallback data',\n        );\n      } else {\n        console.warn(\n          `⚠️ Upcoming Bookings API: Error (${error?.status || 'unknown'}), using fallback data`,\n        );\n      }\n\n      console.error('Upcoming Bookings API Error Details:', {\n        status: error?.status,\n        message: error?.message,\n        details: error?.details,\n        fallbackUsed: true,\n      });\n\n      // Return filtered mock data for upcoming bookings\n      const now = new Date();\n      const upcomingBookings = MOCK_BOOKINGS.filter(booking => {\n        const bookingDate = new Date(booking.scheduled_datetime);\n        return (\n          bookingDate > now &&\n          (booking.status === 'confirmed' || booking.status === 'pending')\n        );\n      });\n\n      return {\n        count: upcomingBookings.length,\n        next: null,\n        previous: null,\n        results: upcomingBookings,\n      };\n    }\n  }\n\n  /**\n   * Get booking details by ID\n   */\n  async getBookingDetails(bookingId: string): Promise<Booking> {\n    try {\n      const response = await apiClient.get<Booking>(\n        `${this.baseUrl}/${bookingId}/`,\n      );\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching booking details:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Create a new booking\n   */\n  async createBooking(bookingData: BookingData): Promise<Booking> {\n    try {\n      const response = await apiClient.post<Booking>(\n        `${this.baseUrl}/`,\n        {\n          service: bookingData.serviceId,\n          provider: bookingData.providerId,\n          scheduled_datetime: `${bookingData.date}T${bookingData.timeSlot}:00`,\n          notes: bookingData.notes || '',\n          special_requests: bookingData.specialRequests || '',\n          payment_method: bookingData.paymentMethod || 'card',\n        },\n        true, // Requires authentication\n      );\n\n      // Invalidate related caches after successful booking creation\n      await cachingService.invalidatePattern('bookings-');\n      await cachingService.invalidatePattern('time-slots-');\n\n      console.log('✅ Booking: Successfully created via API');\n      return response.data;\n    } catch (error: any) {\n      console.error('Error creating booking:', error);\n\n      // Enhanced error handling\n      if (error?.status === 401) {\n        console.warn('🔐 Booking Creation: Authentication failed');\n        throw new Error('Authentication required to create booking');\n      } else if (error?.status === 400) {\n        console.warn('📝 Booking Creation: Invalid booking data');\n        throw new Error('Invalid booking data provided');\n      } else if (error?.status === 409) {\n        console.warn('⏰ Booking Creation: Time slot no longer available');\n        throw new Error('Selected time slot is no longer available');\n      }\n\n      // Create fallback booking for offline support\n      return this.createFallbackBooking(bookingData);\n    }\n  }\n\n  /**\n   * Create fallback booking for offline support\n   */\n  private createFallbackBooking(bookingData: BookingData): Booking {\n    const mockBooking: Booking = {\n      id: `booking_${Date.now()}`,\n      booking_number: `BK${Date.now().toString().slice(-6)}`,\n      customer_id: 'customer_1',\n      provider_id: bookingData.providerId,\n      provider_name: 'Offline Provider',\n      service_id: bookingData.serviceId,\n      service_name: 'Offline Service',\n      service_category: 'Beauty',\n      scheduled_datetime: `${bookingData.date}T${bookingData.timeSlot}:00Z`,\n      duration_minutes: 60,\n      base_price: 50.0,\n      total_amount: 50.0,\n      status: 'pending',\n      payment_status: 'pending',\n      notes: bookingData.notes || '',\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString(),\n    };\n\n    // Add to mock data for future retrieval\n    MOCK_BOOKINGS.push(mockBooking);\n    console.log('📱 Booking: Created fallback booking for offline support');\n\n    return mockBooking;\n  }\n\n  /**\n   * Update booking status\n   */\n  async updateBookingStatus(\n    bookingId: string,\n    status: string,\n  ): Promise<Booking> {\n    try {\n      const response = await apiClient.patch<Booking>(\n        `${this.baseUrl}/${bookingId}/`,\n        {\n          status,\n        },\n      );\n      return response.data;\n    } catch (error) {\n      console.error('Error updating booking status:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Cancel a booking\n   */\n  async cancelBooking(bookingId: string, reason?: string): Promise<Booking> {\n    try {\n      const response = await apiClient.post<Booking>(\n        `${this.baseUrl}/${bookingId}/cancel/`,\n        {\n          cancellation_reason: reason || 'Customer cancellation',\n        },\n      );\n      return response.data;\n    } catch (error) {\n      console.error('Error cancelling booking:', error);\n\n      // Update mock data as fallback\n      const bookingIndex = MOCK_BOOKINGS.findIndex(b => b.id === bookingId);\n      if (bookingIndex !== -1) {\n        MOCK_BOOKINGS[bookingIndex] = {\n          ...MOCK_BOOKINGS[bookingIndex],\n          status: 'cancelled',\n          updated_at: new Date().toISOString(),\n        };\n        return MOCK_BOOKINGS[bookingIndex];\n      }\n\n      throw error;\n    }\n  }\n\n  /**\n   * Confirm a booking\n   */\n  async confirmBooking(bookingId: string): Promise<Booking> {\n    try {\n      const response = await apiClient.post<Booking>(\n        `${this.baseUrl}/${bookingId}/confirm/`,\n      );\n      return response.data;\n    } catch (error) {\n      console.error('Error confirming booking:', error);\n\n      // Update mock data as fallback\n      const bookingIndex = MOCK_BOOKINGS.findIndex(b => b.id === bookingId);\n      if (bookingIndex !== -1) {\n        MOCK_BOOKINGS[bookingIndex] = {\n          ...MOCK_BOOKINGS[bookingIndex],\n          status: 'confirmed',\n          updated_at: new Date().toISOString(),\n        };\n        return MOCK_BOOKINGS[bookingIndex];\n      }\n\n      throw error;\n    }\n  }\n\n  /**\n   * Get available time slots for a provider and date\n   */\n  async getAvailableTimeSlots(\n    providerId: string,\n    date: string,\n  ): Promise<TimeSlot[]> {\n    try {\n      // Check cache first for better performance\n      const cachedData = await cachingService.getCachedApiResponse<TimeSlot[]>(\n        '/api/bookings/time-slots/available/',\n        { provider: providerId, date },\n      );\n\n      if (cachedData) {\n        console.log('📦 Time Slots: Using cached data');\n        return cachedData;\n      }\n\n      const response = await apiClient.get<TimeSlot[]>(\n        `/api/bookings/time-slots/available/`,\n        {\n          params: {\n            provider: providerId,\n            date: date,\n          },\n        },\n        false, // Public endpoint\n        { enabled: true, ttl: 2 * 60 * 1000 }, // Cache for 2 minutes\n      );\n\n      // Cache the successful response\n      await cachingService.cacheApiResponse(\n        '/api/bookings/time-slots/available/',\n        { provider: providerId, date },\n        response.data,\n        2 * 60 * 1000, // 2 minutes TTL\n      );\n\n      console.log('✅ Time Slots: Successfully fetched from API');\n      return response.data;\n    } catch (error: any) {\n      // Enhanced error handling for time slots\n      let errorMessage = 'Error fetching time slots';\n\n      if (error?.status === 401) {\n        errorMessage = 'Authentication error - token may be invalid or expired';\n        console.warn(\n          '🔐 Time Slots API: Authentication failed, using fallback data',\n        );\n      } else if (error?.status === 403) {\n        errorMessage = 'Access denied - insufficient permissions';\n        console.warn('🚫 Time Slots API: Access denied, using fallback data');\n      } else if (error?.status === 404) {\n        errorMessage = 'Time slots endpoint not found';\n        console.warn(\n          '🔍 Time Slots API: Endpoint not found, using fallback data',\n        );\n      } else if (error?.status === 0 || !error?.status) {\n        errorMessage = 'Network connection error';\n        console.warn('🌐 Time Slots API: Network error, using fallback data');\n      } else {\n        errorMessage = `API error (${error?.status || 'unknown'})`;\n        console.warn(`⚠️ Time Slots API: ${errorMessage}, using fallback data`);\n      }\n\n      console.error('Time Slots API Error Details:', {\n        status: error?.status,\n        message: error?.message,\n        details: error?.details,\n        providerId,\n        date,\n        fallbackUsed: true,\n      });\n\n      // Return fallback time slots\n      return this.getFallbackTimeSlots(providerId, date);\n    }\n  }\n\n  /**\n   * Get fallback time slots for offline support\n   */\n  private getFallbackTimeSlots(providerId: string, date: string): TimeSlot[] {\n    return MOCK_TIME_SLOTS.filter(\n      slot =>\n        slot.provider_id === providerId && slot.date === date && slot.available,\n    );\n  }\n\n  /**\n   * Get booking statistics\n   */\n  async getBookingStats(): Promise<any> {\n    try {\n      const response = await apiClient.get(`${this.baseUrl}/stats/`);\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching booking stats:', error);\n\n      // Return mock stats as fallback\n      return {\n        total_bookings: MOCK_BOOKINGS.length,\n        upcoming_bookings: MOCK_BOOKINGS.filter(\n          b =>\n            new Date(b.scheduled_datetime) > new Date() &&\n            (b.status === 'confirmed' || b.status === 'pending'),\n        ).length,\n        completed_bookings: MOCK_BOOKINGS.filter(b => b.status === 'completed')\n          .length,\n        cancelled_bookings: MOCK_BOOKINGS.filter(b => b.status === 'cancelled')\n          .length,\n        total_revenue: MOCK_BOOKINGS.filter(\n          b => b.status === 'completed',\n        ).reduce((sum, b) => sum + b.total_amount, 0),\n      };\n    }\n  }\n\n  /**\n   * Update booking\n   */\n  async updateBooking(\n    bookingId: string,\n    updates: Partial<BookingData>,\n  ): Promise<Booking> {\n    try {\n      const response = await apiClient.patch<Booking>(\n        `${this.baseUrl}/${bookingId}/`,\n        updates,\n      );\n      return response.data;\n    } catch (error) {\n      console.error('Failed to update booking:', error);\n\n      // Update mock data as fallback\n      const bookingIndex = MOCK_BOOKINGS.findIndex(b => b.id === bookingId);\n      if (bookingIndex !== -1) {\n        MOCK_BOOKINGS[bookingIndex] = {\n          ...MOCK_BOOKINGS[bookingIndex],\n          updated_at: new Date().toISOString(),\n          // Apply updates that make sense for the mock structure\n          ...(updates.notes && { notes: updates.notes }),\n        };\n        return MOCK_BOOKINGS[bookingIndex];\n      }\n\n      throw new Error('Failed to update booking');\n    }\n  }\n\n  /**\n   * Reschedule booking\n   */\n  async rescheduleBooking(\n    bookingId: string,\n    newDate: string,\n    newTimeSlot: string,\n  ): Promise<Booking> {\n    try {\n      const response = await apiClient.post<Booking>(\n        `${this.baseUrl}/${bookingId}/reschedule/`,\n        {\n          scheduled_datetime: `${newDate}T${newTimeSlot}:00`,\n        },\n      );\n      return response.data;\n    } catch (error) {\n      console.error('Failed to reschedule booking:', error);\n\n      // Update mock data as fallback\n      const bookingIndex = MOCK_BOOKINGS.findIndex(b => b.id === bookingId);\n      if (bookingIndex !== -1) {\n        MOCK_BOOKINGS[bookingIndex] = {\n          ...MOCK_BOOKINGS[bookingIndex],\n          scheduled_datetime: `${newDate}T${newTimeSlot}:00Z`,\n          updated_at: new Date().toISOString(),\n        };\n        return MOCK_BOOKINGS[bookingIndex];\n      }\n\n      throw new Error('Failed to reschedule booking');\n    }\n  }\n\n  /**\n   * Get recent bookings for home screen\n   */\n  async getRecentBookings(limit: number = 5): Promise<Booking[]> {\n    try {\n      // Check cache first for better performance\n      const cachedData = await cachingService.getCachedApiResponse<{\n        results: Booking[];\n      }>(`${this.baseUrl}/history/`, { limit });\n\n      if (cachedData) {\n        console.log('📦 Recent Bookings: Using cached data');\n        return cachedData.results;\n      }\n\n      const response = await apiClient.get<{ results: Booking[] }>(\n        `${this.baseUrl}/history/`,\n        { params: { limit } },\n        true, // Requires authentication\n        { enabled: true, ttl: 5 * 60 * 1000 }, // Cache for 5 minutes\n      );\n\n      // Cache the successful response\n      await cachingService.cacheApiResponse(\n        `${this.baseUrl}/history/`,\n        { limit },\n        response.data,\n        5 * 60 * 1000, // 5 minutes TTL\n      );\n\n      console.log('✅ Recent Bookings: Successfully fetched from API');\n      return response.data.results;\n    } catch (error: any) {\n      console.error('Failed to fetch recent bookings:', error);\n\n      // Enhanced error handling\n      if (error?.status === 401) {\n        console.warn(\n          '🔐 Recent Bookings: Authentication failed, using fallback data',\n        );\n      } else if (error?.status === 404) {\n        console.warn(\n          '🔍 Recent Bookings: Endpoint not found, using fallback data',\n        );\n      } else {\n        console.warn('⚠️ Recent Bookings: API error, using fallback data');\n      }\n\n      // Return fallback recent bookings\n      return this.getFallbackRecentBookings(limit);\n    }\n  }\n\n  /**\n   * Get fallback recent bookings for offline support\n   */\n  private getFallbackRecentBookings(limit: number): Booking[] {\n    return MOCK_BOOKINGS.sort(\n      (a, b) =>\n        new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),\n    ).slice(0, limit);\n  }\n\n  /**\n   * Get upcoming bookings (simplified version for other components)\n   */\n  async getUpcomingBookingsList(limit: number = 10): Promise<Booking[]> {\n    try {\n      const response = await this.getUpcomingBookings();\n      return response.results.slice(0, limit);\n    } catch (error) {\n      console.error('Failed to fetch upcoming bookings:', error);\n\n      // Return upcoming mock bookings as fallback\n      const now = new Date();\n      return MOCK_BOOKINGS.filter(booking => {\n        const bookingDate = new Date(booking.scheduled_datetime);\n        return (\n          bookingDate > now &&\n          (booking.status === 'confirmed' || booking.status === 'pending')\n        );\n      }).slice(0, limit);\n    }\n  }\n}\n\nexport const bookingService = new BookingService();\nexport default bookingService;\n"], "mappings": ";;;;;;;;;AAcA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,eAAA,GAAAD,OAAA;AAkGA,IAAME,aAAwB,GAAG,CAC/B;EACEC,EAAE,EAAE,WAAW;EACfC,WAAW,EAAE,YAAY;EACzBC,WAAW,EAAE,YAAY;EACzBC,aAAa,EAAE,qBAAqB;EACpCC,UAAU,EAAE,WAAW;EACvBC,YAAY,EAAE,kBAAkB;EAChCC,gBAAgB,EAAE,MAAM;EACxBC,kBAAkB,EAAE,sBAAsB;EAC1CC,gBAAgB,EAAE,EAAE;EACpBC,UAAU,EAAE,IAAI;EAChBC,YAAY,EAAE,IAAI;EAClBC,MAAM,EAAE,WAAW;EACnBC,cAAc,EAAE,MAAM;EACtBC,KAAK,EAAE,wBAAwB;EAC/BC,UAAU,EAAE,sBAAsB;EAClCC,UAAU,EAAE;AACd,CAAC,EACD;EACEf,EAAE,EAAE,WAAW;EACfC,WAAW,EAAE,YAAY;EACzBC,WAAW,EAAE,YAAY;EACzBC,aAAa,EAAE,qBAAqB;EACpCC,UAAU,EAAE,WAAW;EACvBC,YAAY,EAAE,kBAAkB;EAChCC,gBAAgB,EAAE,UAAU;EAC5BC,kBAAkB,EAAE,sBAAsB;EAC1CC,gBAAgB,EAAE,EAAE;EACpBC,UAAU,EAAE,IAAI;EAChBC,YAAY,EAAE,IAAI;EAClBC,MAAM,EAAE,WAAW;EACnBC,cAAc,EAAE,MAAM;EACtBC,KAAK,EAAE,uBAAuB;EAC9BC,UAAU,EAAE,sBAAsB;EAClCC,UAAU,EAAE;AACd,CAAC,CACF;AAED,IAAMC,eAA2B,GAAG,CAClC;EACEC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAE,IAAI;EACfhB,WAAW,EAAE,YAAY;EACzBiB,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,OAAO;EACbC,SAAS,EAAE,IAAI;EACfhB,WAAW,EAAE,YAAY;EACzBiB,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,OAAO;EACbC,SAAS,EAAE,KAAK;EAChBhB,WAAW,EAAE,YAAY;EACzBiB,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,OAAO;EACbC,SAAS,EAAE,IAAI;EACfhB,WAAW,EAAE,YAAY;EACzBiB,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,OAAO;EACbC,SAAS,EAAE,IAAI;EACfhB,WAAW,EAAE,YAAY;EACzBiB,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,OAAO;EACbC,SAAS,EAAE,KAAK;EAChBhB,WAAW,EAAE,YAAY;EACzBiB,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,OAAO;EACbC,SAAS,EAAE,IAAI;EACfhB,WAAW,EAAE,YAAY;EACzBiB,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,OAAO;EACbC,SAAS,EAAE,IAAI;EACfhB,WAAW,EAAE,YAAY;EACzBiB,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,OAAO;EACbC,SAAS,EAAE,IAAI;EACfhB,WAAW,EAAE,YAAY;EACzBiB,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,OAAO;EACbC,SAAS,EAAE,KAAK;EAChBhB,WAAW,EAAE,YAAY;EACzBiB,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,OAAO;EACbC,SAAS,EAAE,IAAI;EACfhB,WAAW,EAAE,YAAY;EACzBiB,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,OAAO;EACbC,SAAS,EAAE,IAAI;EACfhB,WAAW,EAAE,YAAY;EACzBiB,IAAI,EAAE;AACR,CAAC,CACF;AAAC,IAmDIC,cAAc;EAAA,SAAAA,eAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAF,cAAA;IAAA,KACDG,OAAO,GAAG,wBAAwB;EAAA;EAAA,WAAAC,aAAA,CAAAF,OAAA,EAAAF,cAAA;IAAAK,GAAA;IAAAC,KAAA;MAAA,IAAAC,YAAA,OAAAC,kBAAA,CAAAN,OAAA,EAKnD,WAAkBO,OAAwB,EAA4B;QACpE,IAAI;UACF,IAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;UACpC,IAAIF,OAAO,EAAE;YACXG,MAAM,CAACC,OAAO,CAACJ,OAAO,CAAC,CAACK,OAAO,CAAC,UAAAC,IAAA,EAAkB;cAAA,IAAAC,KAAA,OAAAC,eAAA,CAAAf,OAAA,EAAAa,IAAA;gBAAhBV,GAAG,GAAAW,KAAA;gBAAEV,KAAK,GAAAU,KAAA;cAC1C,IAAIV,KAAK,EAAE;gBACTI,MAAM,CAACQ,MAAM,CAACb,GAAG,EAAEC,KAAK,CAAC;cAC3B;YACF,CAAC,CAAC;UACJ;UAEA,IAAMa,GAAG,GAAG,GAAG,IAAI,CAAChB,OAAO,IAAIO,MAAM,CAACU,QAAQ,CAAC,CAAC,GAAG,IAAIV,MAAM,CAACU,QAAQ,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE;UACjF,IAAMC,QAAQ,SAASC,oBAAS,CAACC,GAAG,CAAkBJ,GAAG,CAAC;UAC1D,OAAOE,QAAQ,CAACG,IAAI;QACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;UAEnB,IAAIC,YAAY,GAAG,yBAAyB;UAE5C,IAAI,CAAAD,KAAK,oBAALA,KAAK,CAAElC,MAAM,MAAK,GAAG,EAAE;YACzBmC,YAAY,GAAG,wDAAwD;YACvEC,OAAO,CAACC,IAAI,CACV,6DACF,CAAC;UACH,CAAC,MAAM,IAAI,CAAAH,KAAK,oBAALA,KAAK,CAAElC,MAAM,MAAK,GAAG,EAAE;YAChCmC,YAAY,GAAG,0CAA0C;YACzDC,OAAO,CAACC,IAAI,CAAC,qDAAqD,CAAC;UACrE,CAAC,MAAM,IAAI,CAAAH,KAAK,oBAALA,KAAK,CAAElC,MAAM,MAAK,GAAG,EAAE;YAChCmC,YAAY,GAAG,6BAA6B;YAC5CC,OAAO,CAACC,IAAI,CACV,0DACF,CAAC;UACH,CAAC,MAAM,IAAI,CAAAH,KAAK,oBAALA,KAAK,CAAElC,MAAM,MAAK,GAAG,EAAE;YAChCmC,YAAY,GAAG,uBAAuB;YACtCC,OAAO,CAACC,IAAI,CAAC,oDAAoD,CAAC;UACpE,CAAC,MAAM,IAAI,CAAAH,KAAK,oBAALA,KAAK,CAAElC,MAAM,MAAK,CAAC,IAAI,EAACkC,KAAK,YAALA,KAAK,CAAElC,MAAM,GAAE;YAChDmC,YAAY,GAAG,0BAA0B;YACzCC,OAAO,CAACC,IAAI,CAAC,qDAAqD,CAAC;UACrE,CAAC,MAAM;YACLF,YAAY,GAAG,cAAc,CAAAD,KAAK,oBAALA,KAAK,CAAElC,MAAM,KAAI,SAAS,GAAG;YAC1DoC,OAAO,CAACC,IAAI,CAAC,oBAAoBF,YAAY,uBAAuB,CAAC;UACvE;UAEAC,OAAO,CAACF,KAAK,CAAC,6BAA6B,EAAE;YAC3ClC,MAAM,EAAEkC,KAAK,oBAALA,KAAK,CAAElC,MAAM;YACrBsC,OAAO,EAAEJ,KAAK,oBAALA,KAAK,CAAEI,OAAO;YACvBC,OAAO,EAAEL,KAAK,oBAALA,KAAK,CAAEK,OAAO;YACvBC,YAAY,EAAE;UAChB,CAAC,CAAC;UAGF,IAAIC,gBAAgB,GAAGrD,aAAa;UAEpC,IAAI8B,OAAO,YAAPA,OAAO,CAAElB,MAAM,EAAE;YACnByC,gBAAgB,GAAGA,gBAAgB,CAACC,MAAM,CACxC,UAAAC,OAAO;cAAA,OAAIA,OAAO,CAAC3C,MAAM,KAAKkB,OAAO,CAAClB,MAAM;YAAA,CAC9C,CAAC;UACH;UAEA,OAAO;YACL4C,KAAK,EAAEH,gBAAgB,CAACI,MAAM;YAC9BC,IAAI,EAAE,IAAI;YACVC,QAAQ,EAAE,IAAI;YACdC,OAAO,EAAEP;UACX,CAAC;QACH;MACF,CAAC;MAAA,SAjEKQ,WAAWA,CAAAC,EAAA;QAAA,OAAAlC,YAAA,CAAAmC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAXH,WAAW;IAAA;EAAA;IAAAnC,GAAA;IAAAC,KAAA;MAAA,IAAAsC,oBAAA,OAAApC,kBAAA,CAAAN,OAAA,EAsEjB,aAAsD;QACpD,IAAI;UACF,IAAMmB,QAAQ,SAASC,oBAAS,CAACC,GAAG,CAClC,GAAG,IAAI,CAACpB,OAAO,YACjB,CAAC;UACD,OAAOkB,QAAQ,CAACG,IAAI;QACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;UAEnB,IAAI,CAAAA,KAAK,oBAALA,KAAK,CAAElC,MAAM,MAAK,GAAG,EAAE;YACzBoC,OAAO,CAACC,IAAI,CACV,sEACF,CAAC;UACH,CAAC,MAAM,IAAI,CAAAH,KAAK,oBAALA,KAAK,CAAElC,MAAM,MAAK,GAAG,EAAE;YAChCoC,OAAO,CAACC,IAAI,CACV,8DACF,CAAC;UACH,CAAC,MAAM,IAAI,CAAAH,KAAK,oBAALA,KAAK,CAAElC,MAAM,MAAK,CAAC,IAAI,EAACkC,KAAK,YAALA,KAAK,CAAElC,MAAM,GAAE;YAChDoC,OAAO,CAACC,IAAI,CACV,8DACF,CAAC;UACH,CAAC,MAAM;YACLD,OAAO,CAACC,IAAI,CACV,oCAAoC,CAAAH,KAAK,oBAALA,KAAK,CAAElC,MAAM,KAAI,SAAS,wBAChE,CAAC;UACH;UAEAoC,OAAO,CAACF,KAAK,CAAC,sCAAsC,EAAE;YACpDlC,MAAM,EAAEkC,KAAK,oBAALA,KAAK,CAAElC,MAAM;YACrBsC,OAAO,EAAEJ,KAAK,oBAALA,KAAK,CAAEI,OAAO;YACvBC,OAAO,EAAEL,KAAK,oBAALA,KAAK,CAAEK,OAAO;YACvBC,YAAY,EAAE;UAChB,CAAC,CAAC;UAGF,IAAMc,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;UACtB,IAAMC,gBAAgB,GAAGpE,aAAa,CAACsD,MAAM,CAAC,UAAAC,OAAO,EAAI;YACvD,IAAMc,WAAW,GAAG,IAAIF,IAAI,CAACZ,OAAO,CAAC/C,kBAAkB,CAAC;YACxD,OACE6D,WAAW,GAAGH,GAAG,KAChBX,OAAO,CAAC3C,MAAM,KAAK,WAAW,IAAI2C,OAAO,CAAC3C,MAAM,KAAK,SAAS,CAAC;UAEpE,CAAC,CAAC;UAEF,OAAO;YACL4C,KAAK,EAAEY,gBAAgB,CAACX,MAAM;YAC9BC,IAAI,EAAE,IAAI;YACVC,QAAQ,EAAE,IAAI;YACdC,OAAO,EAAEQ;UACX,CAAC;QACH;MACF,CAAC;MAAA,SAlDKE,mBAAmBA,CAAA;QAAA,OAAAL,oBAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAnBM,mBAAmB;IAAA;EAAA;IAAA5C,GAAA;IAAAC,KAAA;MAAA,IAAA4C,kBAAA,OAAA1C,kBAAA,CAAAN,OAAA,EAuDzB,WAAwBiD,SAAiB,EAAoB;QAC3D,IAAI;UACF,IAAM9B,QAAQ,SAASC,oBAAS,CAACC,GAAG,CAClC,GAAG,IAAI,CAACpB,OAAO,IAAIgD,SAAS,GAC9B,CAAC;UACD,OAAO9B,QAAQ,CAACG,IAAI;QACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdE,OAAO,CAACF,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACvD,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SAVK2B,iBAAiBA,CAAAC,GAAA;QAAA,OAAAH,kBAAA,CAAAR,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjBS,iBAAiB;IAAA;EAAA;IAAA/C,GAAA;IAAAC,KAAA;MAAA,IAAAgD,cAAA,OAAA9C,kBAAA,CAAAN,OAAA,EAevB,WAAoBqD,WAAwB,EAAoB;QAC9D,IAAI;UACF,IAAMlC,QAAQ,SAASC,oBAAS,CAACkC,IAAI,CACnC,GAAG,IAAI,CAACrD,OAAO,GAAG,EAClB;YACEsD,OAAO,EAAEF,WAAW,CAACG,SAAS;YAC9BC,QAAQ,EAAEJ,WAAW,CAACK,UAAU;YAChCzE,kBAAkB,EAAE,GAAGoE,WAAW,CAACxD,IAAI,IAAIwD,WAAW,CAACM,QAAQ,KAAK;YACpEpE,KAAK,EAAE8D,WAAW,CAAC9D,KAAK,IAAI,EAAE;YAC9BqE,gBAAgB,EAAEP,WAAW,CAACQ,eAAe,IAAI,EAAE;YACnDC,cAAc,EAAET,WAAW,CAACU,aAAa,IAAI;UAC/C,CAAC,EACD,IACF,CAAC;UAGD,MAAMC,8BAAc,CAACC,iBAAiB,CAAC,WAAW,CAAC;UACnD,MAAMD,8BAAc,CAACC,iBAAiB,CAAC,aAAa,CAAC;UAErDxC,OAAO,CAACyC,GAAG,CAAC,yCAAyC,CAAC;UACtD,OAAO/C,QAAQ,CAACG,IAAI;QACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;UACnBE,OAAO,CAACF,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAG/C,IAAI,CAAAA,KAAK,oBAALA,KAAK,CAAElC,MAAM,MAAK,GAAG,EAAE;YACzBoC,OAAO,CAACC,IAAI,CAAC,4CAA4C,CAAC;YAC1D,MAAM,IAAIyC,KAAK,CAAC,2CAA2C,CAAC;UAC9D,CAAC,MAAM,IAAI,CAAA5C,KAAK,oBAALA,KAAK,CAAElC,MAAM,MAAK,GAAG,EAAE;YAChCoC,OAAO,CAACC,IAAI,CAAC,2CAA2C,CAAC;YACzD,MAAM,IAAIyC,KAAK,CAAC,+BAA+B,CAAC;UAClD,CAAC,MAAM,IAAI,CAAA5C,KAAK,oBAALA,KAAK,CAAElC,MAAM,MAAK,GAAG,EAAE;YAChCoC,OAAO,CAACC,IAAI,CAAC,mDAAmD,CAAC;YACjE,MAAM,IAAIyC,KAAK,CAAC,2CAA2C,CAAC;UAC9D;UAGA,OAAO,IAAI,CAACC,qBAAqB,CAACf,WAAW,CAAC;QAChD;MACF,CAAC;MAAA,SAvCKgB,aAAaA,CAAAC,GAAA;QAAA,OAAAlB,cAAA,CAAAZ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAb4B,aAAa;IAAA;EAAA;IAAAlE,GAAA;IAAAC,KAAA,EA4CnB,SAAQgE,qBAAqBA,CAACf,WAAwB,EAAW;MAC/D,IAAMkB,WAAoB,GAAG;QAC3B7F,EAAE,EAAE,WAAWkE,IAAI,CAACD,GAAG,CAAC,CAAC,EAAE;QAC3B6B,cAAc,EAAE,KAAK5B,IAAI,CAACD,GAAG,CAAC,CAAC,CAACzB,QAAQ,CAAC,CAAC,CAACuD,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;QACtD9F,WAAW,EAAE,YAAY;QACzBC,WAAW,EAAEyE,WAAW,CAACK,UAAU;QACnC7E,aAAa,EAAE,kBAAkB;QACjCC,UAAU,EAAEuE,WAAW,CAACG,SAAS;QACjCzE,YAAY,EAAE,iBAAiB;QAC/BC,gBAAgB,EAAE,QAAQ;QAC1BC,kBAAkB,EAAE,GAAGoE,WAAW,CAACxD,IAAI,IAAIwD,WAAW,CAACM,QAAQ,MAAM;QACrEzE,gBAAgB,EAAE,EAAE;QACpBC,UAAU,EAAE,IAAI;QAChBC,YAAY,EAAE,IAAI;QAClBC,MAAM,EAAE,SAAS;QACjBC,cAAc,EAAE,SAAS;QACzBC,KAAK,EAAE8D,WAAW,CAAC9D,KAAK,IAAI,EAAE;QAC9BC,UAAU,EAAE,IAAIoD,IAAI,CAAC,CAAC,CAAC8B,WAAW,CAAC,CAAC;QACpCjF,UAAU,EAAE,IAAImD,IAAI,CAAC,CAAC,CAAC8B,WAAW,CAAC;MACrC,CAAC;MAGDjG,aAAa,CAACkG,IAAI,CAACJ,WAAW,CAAC;MAC/B9C,OAAO,CAACyC,GAAG,CAAC,0DAA0D,CAAC;MAEvE,OAAOK,WAAW;IACpB;EAAC;IAAApE,GAAA;IAAAC,KAAA;MAAA,IAAAwE,oBAAA,OAAAtE,kBAAA,CAAAN,OAAA,EAKD,WACEiD,SAAiB,EACjB5D,MAAc,EACI;QAClB,IAAI;UACF,IAAM8B,QAAQ,SAASC,oBAAS,CAACyD,KAAK,CACpC,GAAG,IAAI,CAAC5E,OAAO,IAAIgD,SAAS,GAAG,EAC/B;YACE5D,MAAM,EAANA;UACF,CACF,CAAC;UACD,OAAO8B,QAAQ,CAACG,IAAI;QACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdE,OAAO,CAACF,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UACtD,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SAhBKuD,mBAAmBA,CAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAJ,oBAAA,CAAApC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAnBqC,mBAAmB;IAAA;EAAA;IAAA3E,GAAA;IAAAC,KAAA;MAAA,IAAA6E,cAAA,OAAA3E,kBAAA,CAAAN,OAAA,EAqBzB,WAAoBiD,SAAiB,EAAEiC,MAAe,EAAoB;QACxE,IAAI;UACF,IAAM/D,QAAQ,SAASC,oBAAS,CAACkC,IAAI,CACnC,GAAG,IAAI,CAACrD,OAAO,IAAIgD,SAAS,UAAU,EACtC;YACEkC,mBAAmB,EAAED,MAAM,IAAI;UACjC,CACF,CAAC;UACD,OAAO/D,QAAQ,CAACG,IAAI;QACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdE,OAAO,CAACF,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UAGjD,IAAM6D,YAAY,GAAG3G,aAAa,CAAC4G,SAAS,CAAC,UAAAC,CAAC;YAAA,OAAIA,CAAC,CAAC5G,EAAE,KAAKuE,SAAS;UAAA,EAAC;UACrE,IAAImC,YAAY,KAAK,CAAC,CAAC,EAAE;YACvB3G,aAAa,CAAC2G,YAAY,CAAC,GAAA1E,MAAA,CAAA6E,MAAA,KACtB9G,aAAa,CAAC2G,YAAY,CAAC;cAC9B/F,MAAM,EAAE,WAAW;cACnBI,UAAU,EAAE,IAAImD,IAAI,CAAC,CAAC,CAAC8B,WAAW,CAAC;YAAC,EACrC;YACD,OAAOjG,aAAa,CAAC2G,YAAY,CAAC;UACpC;UAEA,MAAM7D,KAAK;QACb;MACF,CAAC;MAAA,SAzBKiE,aAAaA,CAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAT,cAAA,CAAAzC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAb+C,aAAa;IAAA;EAAA;IAAArF,GAAA;IAAAC,KAAA;MAAA,IAAAuF,eAAA,OAAArF,kBAAA,CAAAN,OAAA,EA8BnB,WAAqBiD,SAAiB,EAAoB;QACxD,IAAI;UACF,IAAM9B,QAAQ,SAASC,oBAAS,CAACkC,IAAI,CACnC,GAAG,IAAI,CAACrD,OAAO,IAAIgD,SAAS,WAC9B,CAAC;UACD,OAAO9B,QAAQ,CAACG,IAAI;QACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdE,OAAO,CAACF,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UAGjD,IAAM6D,YAAY,GAAG3G,aAAa,CAAC4G,SAAS,CAAC,UAAAC,CAAC;YAAA,OAAIA,CAAC,CAAC5G,EAAE,KAAKuE,SAAS;UAAA,EAAC;UACrE,IAAImC,YAAY,KAAK,CAAC,CAAC,EAAE;YACvB3G,aAAa,CAAC2G,YAAY,CAAC,GAAA1E,MAAA,CAAA6E,MAAA,KACtB9G,aAAa,CAAC2G,YAAY,CAAC;cAC9B/F,MAAM,EAAE,WAAW;cACnBI,UAAU,EAAE,IAAImD,IAAI,CAAC,CAAC,CAAC8B,WAAW,CAAC;YAAC,EACrC;YACD,OAAOjG,aAAa,CAAC2G,YAAY,CAAC;UACpC;UAEA,MAAM7D,KAAK;QACb;MACF,CAAC;MAAA,SAtBKqE,cAAcA,CAAAC,GAAA;QAAA,OAAAF,eAAA,CAAAnD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAdmD,cAAc;IAAA;EAAA;IAAAzF,GAAA;IAAAC,KAAA;MAAA,IAAA0F,sBAAA,OAAAxF,kBAAA,CAAAN,OAAA,EA2BpB,WACE0D,UAAkB,EAClB7D,IAAY,EACS;QACrB,IAAI;UAEF,IAAMkG,UAAU,SAAS/B,8BAAc,CAACgC,oBAAoB,CAC1D,qCAAqC,EACrC;YAAEvC,QAAQ,EAAEC,UAAU;YAAE7D,IAAI,EAAJA;UAAK,CAC/B,CAAC;UAED,IAAIkG,UAAU,EAAE;YACdtE,OAAO,CAACyC,GAAG,CAAC,kCAAkC,CAAC;YAC/C,OAAO6B,UAAU;UACnB;UAEA,IAAM5E,QAAQ,SAASC,oBAAS,CAACC,GAAG,CAClC,qCAAqC,EACrC;YACEb,MAAM,EAAE;cACNiD,QAAQ,EAAEC,UAAU;cACpB7D,IAAI,EAAEA;YACR;UACF,CAAC,EACD,KAAK,EACL;YAAEoG,OAAO,EAAE,IAAI;YAAEC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG;UAAK,CACtC,CAAC;UAGD,MAAMlC,8BAAc,CAACmC,gBAAgB,CACnC,qCAAqC,EACrC;YAAE1C,QAAQ,EAAEC,UAAU;YAAE7D,IAAI,EAAJA;UAAK,CAAC,EAC9BsB,QAAQ,CAACG,IAAI,EACb,CAAC,GAAG,EAAE,GAAG,IACX,CAAC;UAEDG,OAAO,CAACyC,GAAG,CAAC,6CAA6C,CAAC;UAC1D,OAAO/C,QAAQ,CAACG,IAAI;QACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;UAEnB,IAAIC,YAAY,GAAG,2BAA2B;UAE9C,IAAI,CAAAD,KAAK,oBAALA,KAAK,CAAElC,MAAM,MAAK,GAAG,EAAE;YACzBmC,YAAY,GAAG,wDAAwD;YACvEC,OAAO,CAACC,IAAI,CACV,+DACF,CAAC;UACH,CAAC,MAAM,IAAI,CAAAH,KAAK,oBAALA,KAAK,CAAElC,MAAM,MAAK,GAAG,EAAE;YAChCmC,YAAY,GAAG,0CAA0C;YACzDC,OAAO,CAACC,IAAI,CAAC,uDAAuD,CAAC;UACvE,CAAC,MAAM,IAAI,CAAAH,KAAK,oBAALA,KAAK,CAAElC,MAAM,MAAK,GAAG,EAAE;YAChCmC,YAAY,GAAG,+BAA+B;YAC9CC,OAAO,CAACC,IAAI,CACV,4DACF,CAAC;UACH,CAAC,MAAM,IAAI,CAAAH,KAAK,oBAALA,KAAK,CAAElC,MAAM,MAAK,CAAC,IAAI,EAACkC,KAAK,YAALA,KAAK,CAAElC,MAAM,GAAE;YAChDmC,YAAY,GAAG,0BAA0B;YACzCC,OAAO,CAACC,IAAI,CAAC,uDAAuD,CAAC;UACvE,CAAC,MAAM;YACLF,YAAY,GAAG,cAAc,CAAAD,KAAK,oBAALA,KAAK,CAAElC,MAAM,KAAI,SAAS,GAAG;YAC1DoC,OAAO,CAACC,IAAI,CAAC,sBAAsBF,YAAY,uBAAuB,CAAC;UACzE;UAEAC,OAAO,CAACF,KAAK,CAAC,+BAA+B,EAAE;YAC7ClC,MAAM,EAAEkC,KAAK,oBAALA,KAAK,CAAElC,MAAM;YACrBsC,OAAO,EAAEJ,KAAK,oBAALA,KAAK,CAAEI,OAAO;YACvBC,OAAO,EAAEL,KAAK,oBAALA,KAAK,CAAEK,OAAO;YACvB8B,UAAU,EAAVA,UAAU;YACV7D,IAAI,EAAJA,IAAI;YACJgC,YAAY,EAAE;UAChB,CAAC,CAAC;UAGF,OAAO,IAAI,CAACuE,oBAAoB,CAAC1C,UAAU,EAAE7D,IAAI,CAAC;QACpD;MACF,CAAC;MAAA,SA3EKwG,qBAAqBA,CAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAT,sBAAA,CAAAtD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArB4D,qBAAqB;IAAA;EAAA;IAAAlG,GAAA;IAAAC,KAAA,EAgF3B,SAAQgG,oBAAoBA,CAAC1C,UAAkB,EAAE7D,IAAY,EAAc;MACzE,OAAOH,eAAe,CAACqC,MAAM,CAC3B,UAAAyE,IAAI;QAAA,OACFA,IAAI,CAAC5H,WAAW,KAAK8E,UAAU,IAAI8C,IAAI,CAAC3G,IAAI,KAAKA,IAAI,IAAI2G,IAAI,CAAC5G,SAAS;MAAA,CAC3E,CAAC;IACH;EAAC;IAAAO,GAAA;IAAAC,KAAA;MAAA,IAAAqG,gBAAA,OAAAnG,kBAAA,CAAAN,OAAA,EAKD,aAAsC;QACpC,IAAI;UACF,IAAMmB,QAAQ,SAASC,oBAAS,CAACC,GAAG,CAAC,GAAG,IAAI,CAACpB,OAAO,SAAS,CAAC;UAC9D,OAAOkB,QAAQ,CAACG,IAAI;QACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdE,OAAO,CAACF,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UAGrD,OAAO;YACLmF,cAAc,EAAEjI,aAAa,CAACyD,MAAM;YACpCyE,iBAAiB,EAAElI,aAAa,CAACsD,MAAM,CACrC,UAAAuD,CAAC;cAAA,OACC,IAAI1C,IAAI,CAAC0C,CAAC,CAACrG,kBAAkB,CAAC,GAAG,IAAI2D,IAAI,CAAC,CAAC,KAC1C0C,CAAC,CAACjG,MAAM,KAAK,WAAW,IAAIiG,CAAC,CAACjG,MAAM,KAAK,SAAS,CAAC;YAAA,CACxD,CAAC,CAAC6C,MAAM;YACR0E,kBAAkB,EAAEnI,aAAa,CAACsD,MAAM,CAAC,UAAAuD,CAAC;cAAA,OAAIA,CAAC,CAACjG,MAAM,KAAK,WAAW;YAAA,EAAC,CACpE6C,MAAM;YACT2E,kBAAkB,EAAEpI,aAAa,CAACsD,MAAM,CAAC,UAAAuD,CAAC;cAAA,OAAIA,CAAC,CAACjG,MAAM,KAAK,WAAW;YAAA,EAAC,CACpE6C,MAAM;YACT4E,aAAa,EAAErI,aAAa,CAACsD,MAAM,CACjC,UAAAuD,CAAC;cAAA,OAAIA,CAAC,CAACjG,MAAM,KAAK,WAAW;YAAA,CAC/B,CAAC,CAAC0H,MAAM,CAAC,UAACC,GAAG,EAAE1B,CAAC;cAAA,OAAK0B,GAAG,GAAG1B,CAAC,CAAClG,YAAY;YAAA,GAAE,CAAC;UAC9C,CAAC;QACH;MACF,CAAC;MAAA,SAxBK6H,eAAeA,CAAA;QAAA,OAAAR,gBAAA,CAAAjE,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAfwE,eAAe;IAAA;EAAA;IAAA9G,GAAA;IAAAC,KAAA;MAAA,IAAA8G,cAAA,OAAA5G,kBAAA,CAAAN,OAAA,EA6BrB,WACEiD,SAAiB,EACjBkE,OAA6B,EACX;QAClB,IAAI;UACF,IAAMhG,QAAQ,SAASC,oBAAS,CAACyD,KAAK,CACpC,GAAG,IAAI,CAAC5E,OAAO,IAAIgD,SAAS,GAAG,EAC/BkE,OACF,CAAC;UACD,OAAOhG,QAAQ,CAACG,IAAI;QACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdE,OAAO,CAACF,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UAGjD,IAAM6D,YAAY,GAAG3G,aAAa,CAAC4G,SAAS,CAAC,UAAAC,CAAC;YAAA,OAAIA,CAAC,CAAC5G,EAAE,KAAKuE,SAAS;UAAA,EAAC;UACrE,IAAImC,YAAY,KAAK,CAAC,CAAC,EAAE;YACvB3G,aAAa,CAAC2G,YAAY,CAAC,GAAA1E,MAAA,CAAA6E,MAAA,KACtB9G,aAAa,CAAC2G,YAAY,CAAC;cAC9B3F,UAAU,EAAE,IAAImD,IAAI,CAAC,CAAC,CAAC8B,WAAW,CAAC;YAAC,GAEhCyC,OAAO,CAAC5H,KAAK,IAAI;cAAEA,KAAK,EAAE4H,OAAO,CAAC5H;YAAM,CAAC,CAC9C;YACD,OAAOd,aAAa,CAAC2G,YAAY,CAAC;UACpC;UAEA,MAAM,IAAIjB,KAAK,CAAC,0BAA0B,CAAC;QAC7C;MACF,CAAC;MAAA,SA3BKiD,aAAaA,CAAAC,GAAA,EAAAC,IAAA;QAAA,OAAAJ,cAAA,CAAA1E,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAb2E,aAAa;IAAA;EAAA;IAAAjH,GAAA;IAAAC,KAAA;MAAA,IAAAmH,kBAAA,OAAAjH,kBAAA,CAAAN,OAAA,EAgCnB,WACEiD,SAAiB,EACjBuE,OAAe,EACfC,WAAmB,EACD;QAClB,IAAI;UACF,IAAMtG,QAAQ,SAASC,oBAAS,CAACkC,IAAI,CACnC,GAAG,IAAI,CAACrD,OAAO,IAAIgD,SAAS,cAAc,EAC1C;YACEhE,kBAAkB,EAAE,GAAGuI,OAAO,IAAIC,WAAW;UAC/C,CACF,CAAC;UACD,OAAOtG,QAAQ,CAACG,IAAI;QACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdE,OAAO,CAACF,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UAGrD,IAAM6D,YAAY,GAAG3G,aAAa,CAAC4G,SAAS,CAAC,UAAAC,CAAC;YAAA,OAAIA,CAAC,CAAC5G,EAAE,KAAKuE,SAAS;UAAA,EAAC;UACrE,IAAImC,YAAY,KAAK,CAAC,CAAC,EAAE;YACvB3G,aAAa,CAAC2G,YAAY,CAAC,GAAA1E,MAAA,CAAA6E,MAAA,KACtB9G,aAAa,CAAC2G,YAAY,CAAC;cAC9BnG,kBAAkB,EAAE,GAAGuI,OAAO,IAAIC,WAAW,MAAM;cACnDhI,UAAU,EAAE,IAAImD,IAAI,CAAC,CAAC,CAAC8B,WAAW,CAAC;YAAC,EACrC;YACD,OAAOjG,aAAa,CAAC2G,YAAY,CAAC;UACpC;UAEA,MAAM,IAAIjB,KAAK,CAAC,8BAA8B,CAAC;QACjD;MACF,CAAC;MAAA,SA7BKuD,iBAAiBA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;QAAA,OAAAN,kBAAA,CAAA/E,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjBiF,iBAAiB;IAAA;EAAA;IAAAvH,GAAA;IAAAC,KAAA;MAAA,IAAA0H,kBAAA,OAAAxH,kBAAA,CAAAN,OAAA,EAkCvB,aAA+D;QAAA,IAAvC+H,KAAa,GAAAtF,SAAA,CAAAP,MAAA,QAAAO,SAAA,QAAAuF,SAAA,GAAAvF,SAAA,MAAG,CAAC;QACvC,IAAI;UAEF,IAAMsD,UAAU,SAAS/B,8BAAc,CAACgC,oBAAoB,CAEzD,GAAG,IAAI,CAAC/F,OAAO,WAAW,EAAE;YAAE8H,KAAK,EAALA;UAAM,CAAC,CAAC;UAEzC,IAAIhC,UAAU,EAAE;YACdtE,OAAO,CAACyC,GAAG,CAAC,uCAAuC,CAAC;YACpD,OAAO6B,UAAU,CAAC1D,OAAO;UAC3B;UAEA,IAAMlB,QAAQ,SAASC,oBAAS,CAACC,GAAG,CAClC,GAAG,IAAI,CAACpB,OAAO,WAAW,EAC1B;YAAEO,MAAM,EAAE;cAAEuH,KAAK,EAALA;YAAM;UAAE,CAAC,EACrB,IAAI,EACJ;YAAE9B,OAAO,EAAE,IAAI;YAAEC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG;UAAK,CACtC,CAAC;UAGD,MAAMlC,8BAAc,CAACmC,gBAAgB,CACnC,GAAG,IAAI,CAAClG,OAAO,WAAW,EAC1B;YAAE8H,KAAK,EAALA;UAAM,CAAC,EACT5G,QAAQ,CAACG,IAAI,EACb,CAAC,GAAG,EAAE,GAAG,IACX,CAAC;UAEDG,OAAO,CAACyC,GAAG,CAAC,kDAAkD,CAAC;UAC/D,OAAO/C,QAAQ,CAACG,IAAI,CAACe,OAAO;QAC9B,CAAC,CAAC,OAAOd,KAAU,EAAE;UACnBE,OAAO,CAACF,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;UAGxD,IAAI,CAAAA,KAAK,oBAALA,KAAK,CAAElC,MAAM,MAAK,GAAG,EAAE;YACzBoC,OAAO,CAACC,IAAI,CACV,gEACF,CAAC;UACH,CAAC,MAAM,IAAI,CAAAH,KAAK,oBAALA,KAAK,CAAElC,MAAM,MAAK,GAAG,EAAE;YAChCoC,OAAO,CAACC,IAAI,CACV,6DACF,CAAC;UACH,CAAC,MAAM;YACLD,OAAO,CAACC,IAAI,CAAC,oDAAoD,CAAC;UACpE;UAGA,OAAO,IAAI,CAACuG,yBAAyB,CAACF,KAAK,CAAC;QAC9C;MACF,CAAC;MAAA,SAhDKG,iBAAiBA,CAAA;QAAA,OAAAJ,kBAAA,CAAAtF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjByF,iBAAiB;IAAA;EAAA;IAAA/H,GAAA;IAAAC,KAAA,EAqDvB,SAAQ6H,yBAAyBA,CAACF,KAAa,EAAa;MAC1D,OAAOtJ,aAAa,CAAC0J,IAAI,CACvB,UAACC,CAAC,EAAE9C,CAAC;QAAA,OACH,IAAI1C,IAAI,CAAC0C,CAAC,CAAC9F,UAAU,CAAC,CAAC6I,OAAO,CAAC,CAAC,GAAG,IAAIzF,IAAI,CAACwF,CAAC,CAAC5I,UAAU,CAAC,CAAC6I,OAAO,CAAC,CAAC;MAAA,CACvE,CAAC,CAAC5D,KAAK,CAAC,CAAC,EAAEsD,KAAK,CAAC;IACnB;EAAC;IAAA5H,GAAA;IAAAC,KAAA;MAAA,IAAAkI,wBAAA,OAAAhI,kBAAA,CAAAN,OAAA,EAKD,aAAsE;QAAA,IAAxC+H,KAAa,GAAAtF,SAAA,CAAAP,MAAA,QAAAO,SAAA,QAAAuF,SAAA,GAAAvF,SAAA,MAAG,EAAE;QAC9C,IAAI;UACF,IAAMtB,QAAQ,SAAS,IAAI,CAAC4B,mBAAmB,CAAC,CAAC;UACjD,OAAO5B,QAAQ,CAACkB,OAAO,CAACoC,KAAK,CAAC,CAAC,EAAEsD,KAAK,CAAC;QACzC,CAAC,CAAC,OAAOxG,KAAK,EAAE;UACdE,OAAO,CAACF,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;UAG1D,IAAMoB,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;UACtB,OAAOnE,aAAa,CAACsD,MAAM,CAAC,UAAAC,OAAO,EAAI;YACrC,IAAMc,WAAW,GAAG,IAAIF,IAAI,CAACZ,OAAO,CAAC/C,kBAAkB,CAAC;YACxD,OACE6D,WAAW,GAAGH,GAAG,KAChBX,OAAO,CAAC3C,MAAM,KAAK,WAAW,IAAI2C,OAAO,CAAC3C,MAAM,KAAK,SAAS,CAAC;UAEpE,CAAC,CAAC,CAACoF,KAAK,CAAC,CAAC,EAAEsD,KAAK,CAAC;QACpB;MACF,CAAC;MAAA,SAjBKQ,uBAAuBA,CAAA;QAAA,OAAAD,wBAAA,CAAA9F,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAvB8F,uBAAuB;IAAA;EAAA;AAAA;AAoBxB,IAAMC,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAG,IAAI1I,cAAc,CAAC,CAAC;AAAC,IAAA4I,QAAA,GAAAD,OAAA,CAAAzI,OAAA,GACpCwI,cAAc", "ignoreList": []}