{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "flattenStyle", "style", "undefined", "Array", "isArray", "result", "i", "styleLength", "length", "computedStyle", "key", "_default"], "sources": ["flattenStyle.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\n'use strict';\n\nimport type {DangerouslyImpreciseStyleProp} from './StyleSheet';\nimport type {____FlattenStyleProp_Internal} from './StyleSheetTypes';\n\nfunction flattenStyle<TStyleProp: DangerouslyImpreciseStyleProp>(\n  style: ?TStyleProp,\n  // $FlowFixMe[underconstrained-implicit-instantiation]\n): ?____FlattenStyleProp_Internal<TStyleProp> {\n  if (style === null || typeof style !== 'object') {\n    return undefined;\n  }\n\n  if (!Array.isArray(style)) {\n    // $FlowFixMe[incompatible-return]\n    return style;\n  }\n\n  const result: {[string]: $FlowFixMe} = {};\n  for (let i = 0, styleLength = style.length; i < styleLength; ++i) {\n    // $FlowFixMe[underconstrained-implicit-instantiation]\n    const computedStyle = flattenStyle(style[i]);\n    if (computedStyle) {\n      // $FlowFixMe[invalid-in-rhs]\n      for (const key in computedStyle) {\n        // $FlowFixMe[incompatible-use]\n        // $FlowFixMe[invalid-computed-prop]\n        result[key] = computedStyle[key];\n      }\n    }\n  }\n  // $FlowFixMe[incompatible-return]\n  return result;\n}\n\nexport default flattenStyle;\n"], "mappings": "AAUA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAKb,SAASC,YAAYA,CACnBC,KAAkB,EAE0B;EAC5C,IAAIA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC/C,OAAOC,SAAS;EAClB;EAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;IAEzB,OAAOA,KAAK;EACd;EAEA,IAAMI,MAA8B,GAAG,CAAC,CAAC;EACzC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,WAAW,GAAGN,KAAK,CAACO,MAAM,EAAEF,CAAC,GAAGC,WAAW,EAAE,EAAED,CAAC,EAAE;IAEhE,IAAMG,aAAa,GAAGT,YAAY,CAACC,KAAK,CAACK,CAAC,CAAC,CAAC;IAC5C,IAAIG,aAAa,EAAE;MAEjB,KAAK,IAAMC,GAAG,IAAID,aAAa,EAAE;QAG/BJ,MAAM,CAACK,GAAG,CAAC,GAAGD,aAAa,CAACC,GAAG,CAAC;MAClC;IACF;EACF;EAEA,OAAOL,MAAM;AACf;AAAC,IAAAM,QAAA,GAAAd,OAAA,CAAAE,OAAA,GAEcC,YAAY", "ignoreList": []}