096bc95c228dd4e6736bc668202c75a2
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.WCAG_CONTRAST_RATIOS = exports.VIERLA_COLOR_PAIRS = exports.TEXT_SIZE_THRESHOLDS = void 0;
exports.auditColorPair = auditColorPair;
exports.auditColorPalette = auditColorPalette;
exports.calculateContrastRatio = calculateContrastRatio;
exports.generateContrastReport = generateContrastReport;
exports.getWCAGCompliantColor = getWCAGCompliantColor;
exports.runVierlaContrastAudit = runVierlaContrastAudit;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var WCAG_CONTRAST_RATIOS = exports.WCAG_CONTRAST_RATIOS = {
  AA: {
    normalText: 4.5,
    largeText: 3.0,
    uiComponents: 3.0
  },
  AAA: {
    normalText: 7.0,
    largeText: 4.5,
    uiComponents: 4.5
  }
};
var TEXT_SIZE_THRESHOLDS = exports.TEXT_SIZE_THRESHOLDS = {
  largeText: {
    fontSize: 18,
    fontWeight: 'normal'
  },
  largeBoldText: {
    fontSize: 14,
    fontWeight: 'bold'
  }
};
function hexToRgb(hex) {
  var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}
function getRelativeLuminance(r, g, b) {
  var _map = [r, g, b].map(function (c) {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    }),
    _map2 = (0, _slicedToArray2.default)(_map, 3),
    rs = _map2[0],
    gs = _map2[1],
    bs = _map2[2];
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
}
function calculateContrastRatio(color1, color2) {
  var rgb1 = hexToRgb(color1);
  var rgb2 = hexToRgb(color2);
  if (!rgb1 || !rgb2) {
    throw new Error('Invalid color format. Please use hex colors.');
  }
  var lum1 = getRelativeLuminance(rgb1.r, rgb1.g, rgb1.b);
  var lum2 = getRelativeLuminance(rgb2.r, rgb2.g, rgb2.b);
  var brightest = Math.max(lum1, lum2);
  var darkest = Math.min(lum1, lum2);
  return (brightest + 0.05) / (darkest + 0.05);
}
function auditColorPair(pair) {
  var ratio = calculateContrastRatio(pair.foreground, pair.background);
  var requiredRatio;
  if (pair.isUIComponent) {
    requiredRatio = WCAG_CONTRAST_RATIOS.AA.uiComponents;
  } else if (pair.textSize === 'large') {
    requiredRatio = WCAG_CONTRAST_RATIOS.AA.largeText;
  } else {
    requiredRatio = WCAG_CONTRAST_RATIOS.AA.normalText;
  }
  var passesAA = ratio >= requiredRatio;
  var passesAAA = pair.isUIComponent ? ratio >= WCAG_CONTRAST_RATIOS.AAA.uiComponents : pair.textSize === 'large' ? ratio >= WCAG_CONTRAST_RATIOS.AAA.largeText : ratio >= WCAG_CONTRAST_RATIOS.AAA.normalText;
  var level;
  var recommendation;
  if (passesAAA) {
    level = 'AAA';
  } else if (passesAA) {
    level = 'AA';
  } else {
    level = 'FAIL';
    recommendation = generateRecommendation(pair, ratio, requiredRatio);
  }
  return {
    ratio: ratio,
    passes: {
      AA: passesAA,
      AAA: passesAAA
    },
    level: level,
    recommendation: recommendation
  };
}
function generateRecommendation(pair, currentRatio, requiredRatio) {
  var improvement = requiredRatio / currentRatio;
  if (improvement < 1.2) {
    return `Slightly adjust the ${pair.foreground} foreground or ${pair.background} background color to improve contrast.`;
  } else if (improvement < 2) {
    return `Consider using a darker foreground or lighter background color for better contrast.`;
  } else {
    return `Significant color changes needed. Consider using high-contrast color combinations from the design system.`;
  }
}
function auditColorPalette(colorPairs) {
  var results = colorPairs.map(function (pair) {
    return Object.assign({}, pair, {
      audit: auditColorPair(pair)
    });
  });
  var summary = {
    total: results.length,
    passing: results.filter(function (r) {
      return r.audit.passes.AA;
    }).length,
    failing: results.filter(function (r) {
      return !r.audit.passes.AA;
    }).length,
    aaCompliant: results.filter(function (r) {
      return r.audit.level === 'AA' || r.audit.level === 'AAA';
    }).length,
    aaaCompliant: results.filter(function (r) {
      return r.audit.level === 'AAA';
    }).length
  };
  return {
    results: results,
    summary: summary
  };
}
function getWCAGCompliantColor(foreground, background) {
  var level = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'AA';
  var textSize = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'normal';
  var isUIComponent = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;
  var currentRatio = calculateContrastRatio(foreground, background);
  var requiredRatio;
  if (isUIComponent) {
    requiredRatio = WCAG_CONTRAST_RATIOS[level].uiComponents;
  } else if (textSize === 'large') {
    requiredRatio = WCAG_CONTRAST_RATIOS[level].largeText;
  } else {
    requiredRatio = WCAG_CONTRAST_RATIOS[level].normalText;
  }
  if (currentRatio >= requiredRatio) {
    return foreground;
  }
  var fgRgb = hexToRgb(foreground);
  var bgRgb = hexToRgb(background);
  if (!fgRgb || !bgRgb) {
    throw new Error('Invalid color format');
  }
  var bgLuminance = getRelativeLuminance(bgRgb.r, bgRgb.g, bgRgb.b);
  var targetLuminance = bgLuminance > 0.5 ? (bgLuminance + 0.05) / requiredRatio - 0.05 : (bgLuminance + 0.05) * requiredRatio - 0.05;
  var factor = targetLuminance > bgLuminance ? 1.2 : 0.8;
  var adjustedR = Math.min(255, Math.max(0, Math.round(fgRgb.r * factor)));
  var adjustedG = Math.min(255, Math.max(0, Math.round(fgRgb.g * factor)));
  var adjustedB = Math.min(255, Math.max(0, Math.round(fgRgb.b * factor)));
  return `#${adjustedR.toString(16).padStart(2, '0')}${adjustedG.toString(16).padStart(2, '0')}${adjustedB.toString(16).padStart(2, '0')}`;
}
var VIERLA_COLOR_PAIRS = exports.VIERLA_COLOR_PAIRS = [{
  foreground: '#1F2937',
  background: '#FFFFFF',
  context: 'Primary text on white background',
  textSize: 'normal'
}, {
  foreground: '#FFFFFF',
  background: '#1F2937',
  context: 'White text on dark background',
  textSize: 'normal'
}, {
  foreground: '#FFFFFF',
  background: '#2A4B32',
  context: 'Primary button text',
  textSize: 'normal'
}, {
  foreground: '#6B7280',
  background: '#FFFFFF',
  context: 'Secondary text on white background',
  textSize: 'normal'
}, {
  foreground: '#DC2626',
  background: '#FFFFFF',
  context: 'Error text',
  textSize: 'normal'
}, {
  foreground: '#059669',
  background: '#FFFFFF',
  context: 'Success text',
  textSize: 'normal'
}, {
  foreground: '#2A4B32',
  background: '#F3F4F6',
  context: 'Primary button outline',
  isUIComponent: true
}, {
  foreground: '#2563EB',
  background: '#FFFFFF',
  context: 'Link text',
  textSize: 'normal'
}, {
  foreground: '#1F2937',
  background: '#F9FAFB',
  context: 'Form input text',
  textSize: 'normal'
}, {
  foreground: '#374151',
  background: '#FFFFFF',
  context: 'Navigation text',
  textSize: 'normal'
}];
function runVierlaContrastAudit() {
  return auditColorPalette(VIERLA_COLOR_PAIRS);
}
function generateContrastReport(auditResults) {
  var results = auditResults.results,
    summary = auditResults.summary;
  var report = `# Color Contrast Audit Report\n\n`;
  report += `## Summary\n`;
  report += `- Total color pairs tested: ${summary.total}\n`;
  report += `- Passing WCAG AA: ${summary.passing}/${summary.total} (${Math.round(summary.passing / summary.total * 100)}%)\n`;
  report += `- Failing WCAG AA: ${summary.failing}/${summary.total} (${Math.round(summary.failing / summary.total * 100)}%)\n`;
  report += `- WCAG AAA compliant: ${summary.aaaCompliant}/${summary.total} (${Math.round(summary.aaaCompliant / summary.total * 100)}%)\n\n`;
  if (summary.failing > 0) {
    report += `## Failing Color Pairs\n\n`;
    results.filter(function (r) {
      return !r.audit.passes.AA;
    }).forEach(function (result) {
      report += `### ${result.context}\n`;
      report += `- Foreground: ${result.foreground}\n`;
      report += `- Background: ${result.background}\n`;
      report += `- Contrast Ratio: ${result.audit.ratio.toFixed(2)}:1\n`;
      report += `- Status: ${result.audit.level}\n`;
      if (result.audit.recommendation) {
        report += `- Recommendation: ${result.audit.recommendation}\n`;
      }
      report += `\n`;
    });
  }
  report += `## All Results\n\n`;
  results.forEach(function (result) {
    var status = result.audit.passes.AA ? '✅' : '❌';
    report += `${status} ${result.context}: ${result.audit.ratio.toFixed(2)}:1 (${result.audit.level})\n`;
  });
  return report;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJXQ0FHX0NPTlRSQVNUX1JBVElPUyIsImV4cG9ydHMiLCJBQSIsIm5vcm1hbFRleHQiLCJsYXJnZVRleHQiLCJ1aUNvbXBvbmVudHMiLCJBQUEiLCJURVhUX1NJWkVfVEhSRVNIT0xEUyIsImZvbnRTaXplIiwiZm9udFdlaWdodCIsImxhcmdlQm9sZFRleHQiLCJoZXhUb1JnYiIsImhleCIsInJlc3VsdCIsImV4ZWMiLCJyIiwicGFyc2VJbnQiLCJnIiwiYiIsImdldFJlbGF0aXZlTHVtaW5hbmNlIiwiX21hcCIsIm1hcCIsImMiLCJNYXRoIiwicG93IiwiX21hcDIiLCJfc2xpY2VkVG9BcnJheTIiLCJkZWZhdWx0IiwicnMiLCJncyIsImJzIiwiY2FsY3VsYXRlQ29udHJhc3RSYXRpbyIsImNvbG9yMSIsImNvbG9yMiIsInJnYjEiLCJyZ2IyIiwiRXJyb3IiLCJsdW0xIiwibHVtMiIsImJyaWdodGVzdCIsIm1heCIsImRhcmtlc3QiLCJtaW4iLCJhdWRpdENvbG9yUGFpciIsInBhaXIiLCJyYXRpbyIsImZvcmVncm91bmQiLCJiYWNrZ3JvdW5kIiwicmVxdWlyZWRSYXRpbyIsImlzVUlDb21wb25lbnQiLCJ0ZXh0U2l6ZSIsInBhc3Nlc0FBIiwicGFzc2VzQUFBIiwibGV2ZWwiLCJyZWNvbW1lbmRhdGlvbiIsImdlbmVyYXRlUmVjb21tZW5kYXRpb24iLCJwYXNzZXMiLCJjdXJyZW50UmF0aW8iLCJpbXByb3ZlbWVudCIsImF1ZGl0Q29sb3JQYWxldHRlIiwiY29sb3JQYWlycyIsInJlc3VsdHMiLCJPYmplY3QiLCJhc3NpZ24iLCJhdWRpdCIsInN1bW1hcnkiLCJ0b3RhbCIsImxlbmd0aCIsInBhc3NpbmciLCJmaWx0ZXIiLCJmYWlsaW5nIiwiYWFDb21wbGlhbnQiLCJhYWFDb21wbGlhbnQiLCJnZXRXQ0FHQ29tcGxpYW50Q29sb3IiLCJhcmd1bWVudHMiLCJ1bmRlZmluZWQiLCJmZ1JnYiIsImJnUmdiIiwiYmdMdW1pbmFuY2UiLCJ0YXJnZXRMdW1pbmFuY2UiLCJmYWN0b3IiLCJhZGp1c3RlZFIiLCJyb3VuZCIsImFkanVzdGVkRyIsImFkanVzdGVkQiIsInRvU3RyaW5nIiwicGFkU3RhcnQiLCJWSUVSTEFfQ09MT1JfUEFJUlMiLCJjb250ZXh0IiwicnVuVmllcmxhQ29udHJhc3RBdWRpdCIsImdlbmVyYXRlQ29udHJhc3RSZXBvcnQiLCJhdWRpdFJlc3VsdHMiLCJyZXBvcnQiLCJmb3JFYWNoIiwidG9GaXhlZCIsInN0YXR1cyJdLCJzb3VyY2VzIjpbImNvbG9yQ29udHJhc3RBdWRpdC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENvbG9yIENvbnRyYXN0IEF1ZGl0IFN5c3RlbVxuICpcbiAqIENvbXByZWhlbnNpdmUgc3lzdGVtIGZvciB2ZXJpZnlpbmcgV0NBRyAyLjIgQUEgY29sb3IgY29udHJhc3QgY29tcGxpYW5jZVxuICogYWNyb3NzIGFsbCB0ZXh0IGFuZCBVSSBjb21wb25lbnRzIGluIHRoZSBhcHBsaWNhdGlvbi5cbiAqXG4gKiBGZWF0dXJlczpcbiAqIC0gQXV0b21hdGVkIGNvbnRyYXN0IHJhdGlvIGNhbGN1bGF0aW9uXG4gKiAtIFdDQUcgMi4yIEFBL0FBQSBjb21wbGlhbmNlIGNoZWNraW5nXG4gKiAtIFJlYWwtdGltZSBjb250cmFzdCB2YWxpZGF0aW9uXG4gKiAtIENvbG9yIHBhbGV0dGUgb3B0aW1pemF0aW9uXG4gKiAtIEFjY2Vzc2liaWxpdHkgcmVwb3J0aW5nXG4gKlxuICogQHZlcnNpb24gMS4wLjBcbiAqIEBhdXRob3IgVmllcmxhIERldmVsb3BtZW50IFRlYW1cbiAqL1xuXG4vLyBXQ0FHIGNvbnRyYXN0IHJhdGlvIHJlcXVpcmVtZW50c1xuZXhwb3J0IGNvbnN0IFdDQUdfQ09OVFJBU1RfUkFUSU9TID0ge1xuICBBQToge1xuICAgIG5vcm1hbFRleHQ6IDQuNSxcbiAgICBsYXJnZVRleHQ6IDMuMCxcbiAgICB1aUNvbXBvbmVudHM6IDMuMCxcbiAgfSxcbiAgQUFBOiB7XG4gICAgbm9ybWFsVGV4dDogNy4wLFxuICAgIGxhcmdlVGV4dDogNC41LFxuICAgIHVpQ29tcG9uZW50czogNC41LFxuICB9LFxufSBhcyBjb25zdDtcblxuLy8gVGV4dCBzaXplIHRocmVzaG9sZHMgZm9yIFdDQUdcbmV4cG9ydCBjb25zdCBURVhUX1NJWkVfVEhSRVNIT0xEUyA9IHtcbiAgbGFyZ2VUZXh0OiB7XG4gICAgZm9udFNpemU6IDE4LCAvLyAxOHB4IG9yIGxhcmdlclxuICAgIGZvbnRXZWlnaHQ6ICdub3JtYWwnLFxuICB9LFxuICBsYXJnZUJvbGRUZXh0OiB7XG4gICAgZm9udFNpemU6IDE0LCAvLyAxNHB4IG9yIGxhcmdlciBpZiBib2xkXG4gICAgZm9udFdlaWdodDogJ2JvbGQnLFxuICB9LFxufSBhcyBjb25zdDtcblxuLy8gQ29sb3IgY29udHJhc3QgYXVkaXQgcmVzdWx0XG5leHBvcnQgaW50ZXJmYWNlIENvbnRyYXN0QXVkaXRSZXN1bHQge1xuICByYXRpbzogbnVtYmVyO1xuICBwYXNzZXM6IHtcbiAgICBBQTogYm9vbGVhbjtcbiAgICBBQUE6IGJvb2xlYW47XG4gIH07XG4gIGxldmVsOiAnQUEnIHwgJ0FBQScgfCAnRkFJTCc7XG4gIHJlY29tbWVuZGF0aW9uPzogc3RyaW5nO1xufVxuXG4vLyBDb2xvciBwYWlyIGZvciB0ZXN0aW5nXG5leHBvcnQgaW50ZXJmYWNlIENvbG9yUGFpciB7XG4gIGZvcmVncm91bmQ6IHN0cmluZztcbiAgYmFja2dyb3VuZDogc3RyaW5nO1xuICBjb250ZXh0OiBzdHJpbmc7IC8vIERlc2NyaXB0aW9uIG9mIHdoZXJlIHRoaXMgcGFpciBpcyB1c2VkXG4gIHRleHRTaXplPzogJ25vcm1hbCcgfCAnbGFyZ2UnO1xuICBpc1VJQ29tcG9uZW50PzogYm9vbGVhbjtcbn1cblxuLyoqXG4gKiBDb252ZXJ0IGhleCBjb2xvciB0byBSR0JcbiAqL1xuZnVuY3Rpb24gaGV4VG9SZ2IoaGV4OiBzdHJpbmcpOiB7IHI6IG51bWJlcjsgZzogbnVtYmVyOyBiOiBudW1iZXIgfSB8IG51bGwge1xuICBjb25zdCByZXN1bHQgPSAvXiM/KFthLWZcXGRdezJ9KShbYS1mXFxkXXsyfSkoW2EtZlxcZF17Mn0pJC9pLmV4ZWMoaGV4KTtcbiAgcmV0dXJuIHJlc3VsdFxuICAgID8ge1xuICAgICAgICByOiBwYXJzZUludChyZXN1bHRbMV0sIDE2KSxcbiAgICAgICAgZzogcGFyc2VJbnQocmVzdWx0WzJdLCAxNiksXG4gICAgICAgIGI6IHBhcnNlSW50KHJlc3VsdFszXSwgMTYpLFxuICAgICAgfVxuICAgIDogbnVsbDtcbn1cblxuLyoqXG4gKiBDYWxjdWxhdGUgcmVsYXRpdmUgbHVtaW5hbmNlIG9mIGEgY29sb3JcbiAqL1xuZnVuY3Rpb24gZ2V0UmVsYXRpdmVMdW1pbmFuY2UocjogbnVtYmVyLCBnOiBudW1iZXIsIGI6IG51bWJlcik6IG51bWJlciB7XG4gIGNvbnN0IFtycywgZ3MsIGJzXSA9IFtyLCBnLCBiXS5tYXAoYyA9PiB7XG4gICAgYyA9IGMgLyAyNTU7XG4gICAgcmV0dXJuIGMgPD0gMC4wMzkyOCA/IGMgLyAxMi45MiA6IE1hdGgucG93KChjICsgMC4wNTUpIC8gMS4wNTUsIDIuNCk7XG4gIH0pO1xuXG4gIHJldHVybiAwLjIxMjYgKiBycyArIDAuNzE1MiAqIGdzICsgMC4wNzIyICogYnM7XG59XG5cbi8qKlxuICogQ2FsY3VsYXRlIGNvbnRyYXN0IHJhdGlvIGJldHdlZW4gdHdvIGNvbG9yc1xuICovXG5leHBvcnQgZnVuY3Rpb24gY2FsY3VsYXRlQ29udHJhc3RSYXRpbyhjb2xvcjE6IHN0cmluZywgY29sb3IyOiBzdHJpbmcpOiBudW1iZXIge1xuICBjb25zdCByZ2IxID0gaGV4VG9SZ2IoY29sb3IxKTtcbiAgY29uc3QgcmdiMiA9IGhleFRvUmdiKGNvbG9yMik7XG5cbiAgaWYgKCFyZ2IxIHx8ICFyZ2IyKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdJbnZhbGlkIGNvbG9yIGZvcm1hdC4gUGxlYXNlIHVzZSBoZXggY29sb3JzLicpO1xuICB9XG5cbiAgY29uc3QgbHVtMSA9IGdldFJlbGF0aXZlTHVtaW5hbmNlKHJnYjEuciwgcmdiMS5nLCByZ2IxLmIpO1xuICBjb25zdCBsdW0yID0gZ2V0UmVsYXRpdmVMdW1pbmFuY2UocmdiMi5yLCByZ2IyLmcsIHJnYjIuYik7XG5cbiAgY29uc3QgYnJpZ2h0ZXN0ID0gTWF0aC5tYXgobHVtMSwgbHVtMik7XG4gIGNvbnN0IGRhcmtlc3QgPSBNYXRoLm1pbihsdW0xLCBsdW0yKTtcblxuICByZXR1cm4gKGJyaWdodGVzdCArIDAuMDUpIC8gKGRhcmtlc3QgKyAwLjA1KTtcbn1cblxuLyoqXG4gKiBDaGVjayBpZiBjb2xvciBwYWlyIG1lZXRzIFdDQUcgcmVxdWlyZW1lbnRzXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBhdWRpdENvbG9yUGFpcihwYWlyOiBDb2xvclBhaXIpOiBDb250cmFzdEF1ZGl0UmVzdWx0IHtcbiAgY29uc3QgcmF0aW8gPSBjYWxjdWxhdGVDb250cmFzdFJhdGlvKHBhaXIuZm9yZWdyb3VuZCwgcGFpci5iYWNrZ3JvdW5kKTtcblxuICBsZXQgcmVxdWlyZWRSYXRpbzogbnVtYmVyO1xuXG4gIGlmIChwYWlyLmlzVUlDb21wb25lbnQpIHtcbiAgICByZXF1aXJlZFJhdGlvID0gV0NBR19DT05UUkFTVF9SQVRJT1MuQUEudWlDb21wb25lbnRzO1xuICB9IGVsc2UgaWYgKHBhaXIudGV4dFNpemUgPT09ICdsYXJnZScpIHtcbiAgICByZXF1aXJlZFJhdGlvID0gV0NBR19DT05UUkFTVF9SQVRJT1MuQUEubGFyZ2VUZXh0O1xuICB9IGVsc2Uge1xuICAgIHJlcXVpcmVkUmF0aW8gPSBXQ0FHX0NPTlRSQVNUX1JBVElPUy5BQS5ub3JtYWxUZXh0O1xuICB9XG5cbiAgY29uc3QgcGFzc2VzQUEgPSByYXRpbyA+PSByZXF1aXJlZFJhdGlvO1xuICBjb25zdCBwYXNzZXNBQUEgPSBwYWlyLmlzVUlDb21wb25lbnRcbiAgICA/IHJhdGlvID49IFdDQUdfQ09OVFJBU1RfUkFUSU9TLkFBQS51aUNvbXBvbmVudHNcbiAgICA6IHBhaXIudGV4dFNpemUgPT09ICdsYXJnZSdcbiAgICAgID8gcmF0aW8gPj0gV0NBR19DT05UUkFTVF9SQVRJT1MuQUFBLmxhcmdlVGV4dFxuICAgICAgOiByYXRpbyA+PSBXQ0FHX0NPTlRSQVNUX1JBVElPUy5BQUEubm9ybWFsVGV4dDtcblxuICBsZXQgbGV2ZWw6ICdBQScgfCAnQUFBJyB8ICdGQUlMJztcbiAgbGV0IHJlY29tbWVuZGF0aW9uOiBzdHJpbmcgfCB1bmRlZmluZWQ7XG5cbiAgaWYgKHBhc3Nlc0FBQSkge1xuICAgIGxldmVsID0gJ0FBQSc7XG4gIH0gZWxzZSBpZiAocGFzc2VzQUEpIHtcbiAgICBsZXZlbCA9ICdBQSc7XG4gIH0gZWxzZSB7XG4gICAgbGV2ZWwgPSAnRkFJTCc7XG4gICAgcmVjb21tZW5kYXRpb24gPSBnZW5lcmF0ZVJlY29tbWVuZGF0aW9uKHBhaXIsIHJhdGlvLCByZXF1aXJlZFJhdGlvKTtcbiAgfVxuXG4gIHJldHVybiB7XG4gICAgcmF0aW8sXG4gICAgcGFzc2VzOiB7XG4gICAgICBBQTogcGFzc2VzQUEsXG4gICAgICBBQUE6IHBhc3Nlc0FBQSxcbiAgICB9LFxuICAgIGxldmVsLFxuICAgIHJlY29tbWVuZGF0aW9uLFxuICB9O1xufVxuXG4vKipcbiAqIEdlbmVyYXRlIHJlY29tbWVuZGF0aW9uIGZvciBmYWlsaW5nIGNvbG9yIHBhaXJzXG4gKi9cbmZ1bmN0aW9uIGdlbmVyYXRlUmVjb21tZW5kYXRpb24oXG4gIHBhaXI6IENvbG9yUGFpcixcbiAgY3VycmVudFJhdGlvOiBudW1iZXIsXG4gIHJlcXVpcmVkUmF0aW86IG51bWJlcixcbik6IHN0cmluZyB7XG4gIGNvbnN0IGltcHJvdmVtZW50ID0gcmVxdWlyZWRSYXRpbyAvIGN1cnJlbnRSYXRpbztcblxuICBpZiAoaW1wcm92ZW1lbnQgPCAxLjIpIHtcbiAgICByZXR1cm4gYFNsaWdodGx5IGFkanVzdCB0aGUgJHtwYWlyLmZvcmVncm91bmR9IGZvcmVncm91bmQgb3IgJHtwYWlyLmJhY2tncm91bmR9IGJhY2tncm91bmQgY29sb3IgdG8gaW1wcm92ZSBjb250cmFzdC5gO1xuICB9IGVsc2UgaWYgKGltcHJvdmVtZW50IDwgMikge1xuICAgIHJldHVybiBgQ29uc2lkZXIgdXNpbmcgYSBkYXJrZXIgZm9yZWdyb3VuZCBvciBsaWdodGVyIGJhY2tncm91bmQgY29sb3IgZm9yIGJldHRlciBjb250cmFzdC5gO1xuICB9IGVsc2Uge1xuICAgIHJldHVybiBgU2lnbmlmaWNhbnQgY29sb3IgY2hhbmdlcyBuZWVkZWQuIENvbnNpZGVyIHVzaW5nIGhpZ2gtY29udHJhc3QgY29sb3IgY29tYmluYXRpb25zIGZyb20gdGhlIGRlc2lnbiBzeXN0ZW0uYDtcbiAgfVxufVxuXG4vKipcbiAqIEF1ZGl0IGVudGlyZSBjb2xvciBwYWxldHRlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBhdWRpdENvbG9yUGFsZXR0ZShjb2xvclBhaXJzOiBDb2xvclBhaXJbXSk6IHtcbiAgcmVzdWx0czogQXJyYXk8Q29sb3JQYWlyICYgeyBhdWRpdDogQ29udHJhc3RBdWRpdFJlc3VsdCB9PjtcbiAgc3VtbWFyeToge1xuICAgIHRvdGFsOiBudW1iZXI7XG4gICAgcGFzc2luZzogbnVtYmVyO1xuICAgIGZhaWxpbmc6IG51bWJlcjtcbiAgICBhYUNvbXBsaWFudDogbnVtYmVyO1xuICAgIGFhYUNvbXBsaWFudDogbnVtYmVyO1xuICB9O1xufSB7XG4gIGNvbnN0IHJlc3VsdHMgPSBjb2xvclBhaXJzLm1hcChwYWlyID0+ICh7XG4gICAgLi4ucGFpcixcbiAgICBhdWRpdDogYXVkaXRDb2xvclBhaXIocGFpciksXG4gIH0pKTtcblxuICBjb25zdCBzdW1tYXJ5ID0ge1xuICAgIHRvdGFsOiByZXN1bHRzLmxlbmd0aCxcbiAgICBwYXNzaW5nOiByZXN1bHRzLmZpbHRlcihyID0+IHIuYXVkaXQucGFzc2VzLkFBKS5sZW5ndGgsXG4gICAgZmFpbGluZzogcmVzdWx0cy5maWx0ZXIociA9PiAhci5hdWRpdC5wYXNzZXMuQUEpLmxlbmd0aCxcbiAgICBhYUNvbXBsaWFudDogcmVzdWx0cy5maWx0ZXIoXG4gICAgICByID0+IHIuYXVkaXQubGV2ZWwgPT09ICdBQScgfHwgci5hdWRpdC5sZXZlbCA9PT0gJ0FBQScsXG4gICAgKS5sZW5ndGgsXG4gICAgYWFhQ29tcGxpYW50OiByZXN1bHRzLmZpbHRlcihyID0+IHIuYXVkaXQubGV2ZWwgPT09ICdBQUEnKS5sZW5ndGgsXG4gIH07XG5cbiAgcmV0dXJuIHsgcmVzdWx0cywgc3VtbWFyeSB9O1xufVxuXG4vKipcbiAqIEdldCBXQ0FHIGNvbXBsaWFudCBjb2xvclxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0V0NBR0NvbXBsaWFudENvbG9yKFxuICBmb3JlZ3JvdW5kOiBzdHJpbmcsXG4gIGJhY2tncm91bmQ6IHN0cmluZyxcbiAgbGV2ZWw6ICdBQScgfCAnQUFBJyA9ICdBQScsXG4gIHRleHRTaXplOiAnbm9ybWFsJyB8ICdsYXJnZScgPSAnbm9ybWFsJyxcbiAgaXNVSUNvbXBvbmVudDogYm9vbGVhbiA9IGZhbHNlLFxuKTogc3RyaW5nIHtcbiAgY29uc3QgY3VycmVudFJhdGlvID0gY2FsY3VsYXRlQ29udHJhc3RSYXRpbyhmb3JlZ3JvdW5kLCBiYWNrZ3JvdW5kKTtcblxuICBsZXQgcmVxdWlyZWRSYXRpbzogbnVtYmVyO1xuICBpZiAoaXNVSUNvbXBvbmVudCkge1xuICAgIHJlcXVpcmVkUmF0aW8gPSBXQ0FHX0NPTlRSQVNUX1JBVElPU1tsZXZlbF0udWlDb21wb25lbnRzO1xuICB9IGVsc2UgaWYgKHRleHRTaXplID09PSAnbGFyZ2UnKSB7XG4gICAgcmVxdWlyZWRSYXRpbyA9IFdDQUdfQ09OVFJBU1RfUkFUSU9TW2xldmVsXS5sYXJnZVRleHQ7XG4gIH0gZWxzZSB7XG4gICAgcmVxdWlyZWRSYXRpbyA9IFdDQUdfQ09OVFJBU1RfUkFUSU9TW2xldmVsXS5ub3JtYWxUZXh0O1xuICB9XG5cbiAgaWYgKGN1cnJlbnRSYXRpbyA+PSByZXF1aXJlZFJhdGlvKSB7XG4gICAgcmV0dXJuIGZvcmVncm91bmQ7IC8vIEFscmVhZHkgY29tcGxpYW50XG4gIH1cblxuICAvLyBBZGp1c3QgZm9yZWdyb3VuZCBjb2xvciB0byBtZWV0IHJlcXVpcmVtZW50c1xuICBjb25zdCBmZ1JnYiA9IGhleFRvUmdiKGZvcmVncm91bmQpO1xuICBjb25zdCBiZ1JnYiA9IGhleFRvUmdiKGJhY2tncm91bmQpO1xuXG4gIGlmICghZmdSZ2IgfHwgIWJnUmdiKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdJbnZhbGlkIGNvbG9yIGZvcm1hdCcpO1xuICB9XG5cbiAgY29uc3QgYmdMdW1pbmFuY2UgPSBnZXRSZWxhdGl2ZUx1bWluYW5jZShiZ1JnYi5yLCBiZ1JnYi5nLCBiZ1JnYi5iKTtcblxuICAvLyBEZXRlcm1pbmUgaWYgd2Ugc2hvdWxkIG1ha2UgZm9yZWdyb3VuZCBkYXJrZXIgb3IgbGlnaHRlclxuICBjb25zdCB0YXJnZXRMdW1pbmFuY2UgPVxuICAgIGJnTHVtaW5hbmNlID4gMC41XG4gICAgICA/IChiZ0x1bWluYW5jZSArIDAuMDUpIC8gcmVxdWlyZWRSYXRpbyAtIDAuMDUgLy8gTWFrZSBkYXJrZXJcbiAgICAgIDogKGJnTHVtaW5hbmNlICsgMC4wNSkgKiByZXF1aXJlZFJhdGlvIC0gMC4wNTsgLy8gTWFrZSBsaWdodGVyXG5cbiAgLy8gQWRqdXN0IFJHQiB2YWx1ZXMgdG8gYWNoaWV2ZSB0YXJnZXQgbHVtaW5hbmNlXG4gIGNvbnN0IGZhY3RvciA9IHRhcmdldEx1bWluYW5jZSA+IGJnTHVtaW5hbmNlID8gMS4yIDogMC44O1xuXG4gIGNvbnN0IGFkanVzdGVkUiA9IE1hdGgubWluKDI1NSwgTWF0aC5tYXgoMCwgTWF0aC5yb3VuZChmZ1JnYi5yICogZmFjdG9yKSkpO1xuICBjb25zdCBhZGp1c3RlZEcgPSBNYXRoLm1pbigyNTUsIE1hdGgubWF4KDAsIE1hdGgucm91bmQoZmdSZ2IuZyAqIGZhY3RvcikpKTtcbiAgY29uc3QgYWRqdXN0ZWRCID0gTWF0aC5taW4oMjU1LCBNYXRoLm1heCgwLCBNYXRoLnJvdW5kKGZnUmdiLmIgKiBmYWN0b3IpKSk7XG5cbiAgcmV0dXJuIGAjJHthZGp1c3RlZFIudG9TdHJpbmcoMTYpLnBhZFN0YXJ0KDIsICcwJyl9JHthZGp1c3RlZEcudG9TdHJpbmcoMTYpLnBhZFN0YXJ0KDIsICcwJyl9JHthZGp1c3RlZEIudG9TdHJpbmcoMTYpLnBhZFN0YXJ0KDIsICcwJyl9YDtcbn1cblxuLyoqXG4gKiBWaWVybGEgYXBwIGNvbG9yIHBhaXJzIGZvciBhdWRpdFxuICovXG5leHBvcnQgY29uc3QgVklFUkxBX0NPTE9SX1BBSVJTOiBDb2xvclBhaXJbXSA9IFtcbiAgLy8gUHJpbWFyeSB0ZXh0IGNvbWJpbmF0aW9uc1xuICB7XG4gICAgZm9yZWdyb3VuZDogJyMxRjI5MzcnLCAvLyBncmF5LTgwMFxuICAgIGJhY2tncm91bmQ6ICcjRkZGRkZGJywgLy8gd2hpdGVcbiAgICBjb250ZXh0OiAnUHJpbWFyeSB0ZXh0IG9uIHdoaXRlIGJhY2tncm91bmQnLFxuICAgIHRleHRTaXplOiAnbm9ybWFsJyxcbiAgfSxcbiAge1xuICAgIGZvcmVncm91bmQ6ICcjRkZGRkZGJywgLy8gd2hpdGVcbiAgICBiYWNrZ3JvdW5kOiAnIzFGMjkzNycsIC8vIGdyYXktODAwXG4gICAgY29udGV4dDogJ1doaXRlIHRleHQgb24gZGFyayBiYWNrZ3JvdW5kJyxcbiAgICB0ZXh0U2l6ZTogJ25vcm1hbCcsXG4gIH0sXG5cbiAgLy8gUHJpbWFyeSBidXR0b25cbiAge1xuICAgIGZvcmVncm91bmQ6ICcjRkZGRkZGJywgLy8gd2hpdGVcbiAgICBiYWNrZ3JvdW5kOiAnIzJBNEIzMicsIC8vIHByaW1hcnkgZ3JlZW5cbiAgICBjb250ZXh0OiAnUHJpbWFyeSBidXR0b24gdGV4dCcsXG4gICAgdGV4dFNpemU6ICdub3JtYWwnLFxuICB9LFxuXG4gIC8vIFNlY29uZGFyeSB0ZXh0XG4gIHtcbiAgICBmb3JlZ3JvdW5kOiAnIzZCNzI4MCcsIC8vIGdyYXktNTAwXG4gICAgYmFja2dyb3VuZDogJyNGRkZGRkYnLCAvLyB3aGl0ZVxuICAgIGNvbnRleHQ6ICdTZWNvbmRhcnkgdGV4dCBvbiB3aGl0ZSBiYWNrZ3JvdW5kJyxcbiAgICB0ZXh0U2l6ZTogJ25vcm1hbCcsXG4gIH0sXG5cbiAgLy8gRXJyb3Igc3RhdGVzXG4gIHtcbiAgICBmb3JlZ3JvdW5kOiAnI0RDMjYyNicsIC8vIHJlZC02MDBcbiAgICBiYWNrZ3JvdW5kOiAnI0ZGRkZGRicsIC8vIHdoaXRlXG4gICAgY29udGV4dDogJ0Vycm9yIHRleHQnLFxuICAgIHRleHRTaXplOiAnbm9ybWFsJyxcbiAgfSxcblxuICAvLyBTdWNjZXNzIHN0YXRlc1xuICB7XG4gICAgZm9yZWdyb3VuZDogJyMwNTk2NjknLCAvLyBncmVlbi02MDBcbiAgICBiYWNrZ3JvdW5kOiAnI0ZGRkZGRicsIC8vIHdoaXRlXG4gICAgY29udGV4dDogJ1N1Y2Nlc3MgdGV4dCcsXG4gICAgdGV4dFNpemU6ICdub3JtYWwnLFxuICB9LFxuXG4gIC8vIFVJIENvbXBvbmVudHNcbiAge1xuICAgIGZvcmVncm91bmQ6ICcjMkE0QjMyJywgLy8gcHJpbWFyeVxuICAgIGJhY2tncm91bmQ6ICcjRjNGNEY2JywgLy8gZ3JheS0xMDBcbiAgICBjb250ZXh0OiAnUHJpbWFyeSBidXR0b24gb3V0bGluZScsXG4gICAgaXNVSUNvbXBvbmVudDogdHJ1ZSxcbiAgfSxcblxuICAvLyBMaW5rc1xuICB7XG4gICAgZm9yZWdyb3VuZDogJyMyNTYzRUInLCAvLyBibHVlLTYwMFxuICAgIGJhY2tncm91bmQ6ICcjRkZGRkZGJywgLy8gd2hpdGVcbiAgICBjb250ZXh0OiAnTGluayB0ZXh0JyxcbiAgICB0ZXh0U2l6ZTogJ25vcm1hbCcsXG4gIH0sXG5cbiAgLy8gRm9ybSBpbnB1dHNcbiAge1xuICAgIGZvcmVncm91bmQ6ICcjMUYyOTM3JywgLy8gZ3JheS04MDBcbiAgICBiYWNrZ3JvdW5kOiAnI0Y5RkFGQicsIC8vIGdyYXktNTBcbiAgICBjb250ZXh0OiAnRm9ybSBpbnB1dCB0ZXh0JyxcbiAgICB0ZXh0U2l6ZTogJ25vcm1hbCcsXG4gIH0sXG5cbiAgLy8gTmF2aWdhdGlvblxuICB7XG4gICAgZm9yZWdyb3VuZDogJyMzNzQxNTEnLCAvLyBncmF5LTcwMFxuICAgIGJhY2tncm91bmQ6ICcjRkZGRkZGJywgLy8gd2hpdGVcbiAgICBjb250ZXh0OiAnTmF2aWdhdGlvbiB0ZXh0JyxcbiAgICB0ZXh0U2l6ZTogJ25vcm1hbCcsXG4gIH0sXG5dO1xuXG4vKipcbiAqIFJ1biBjb21wbGV0ZSBjb2xvciBjb250cmFzdCBhdWRpdCBmb3IgVmllcmxhIGFwcFxuICovXG5leHBvcnQgZnVuY3Rpb24gcnVuVmllcmxhQ29udHJhc3RBdWRpdCgpIHtcbiAgcmV0dXJuIGF1ZGl0Q29sb3JQYWxldHRlKFZJRVJMQV9DT0xPUl9QQUlSUyk7XG59XG5cbi8qKlxuICogR2VuZXJhdGUgY29udHJhc3QgYXVkaXQgcmVwb3J0XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZUNvbnRyYXN0UmVwb3J0KFxuICBhdWRpdFJlc3VsdHM6IFJldHVyblR5cGU8dHlwZW9mIGF1ZGl0Q29sb3JQYWxldHRlPixcbik6IHN0cmluZyB7XG4gIGNvbnN0IHsgcmVzdWx0cywgc3VtbWFyeSB9ID0gYXVkaXRSZXN1bHRzO1xuXG4gIGxldCByZXBvcnQgPSBgIyBDb2xvciBDb250cmFzdCBBdWRpdCBSZXBvcnRcXG5cXG5gO1xuICByZXBvcnQgKz0gYCMjIFN1bW1hcnlcXG5gO1xuICByZXBvcnQgKz0gYC0gVG90YWwgY29sb3IgcGFpcnMgdGVzdGVkOiAke3N1bW1hcnkudG90YWx9XFxuYDtcbiAgcmVwb3J0ICs9IGAtIFBhc3NpbmcgV0NBRyBBQTogJHtzdW1tYXJ5LnBhc3Npbmd9LyR7c3VtbWFyeS50b3RhbH0gKCR7TWF0aC5yb3VuZCgoc3VtbWFyeS5wYXNzaW5nIC8gc3VtbWFyeS50b3RhbCkgKiAxMDApfSUpXFxuYDtcbiAgcmVwb3J0ICs9IGAtIEZhaWxpbmcgV0NBRyBBQTogJHtzdW1tYXJ5LmZhaWxpbmd9LyR7c3VtbWFyeS50b3RhbH0gKCR7TWF0aC5yb3VuZCgoc3VtbWFyeS5mYWlsaW5nIC8gc3VtbWFyeS50b3RhbCkgKiAxMDApfSUpXFxuYDtcbiAgcmVwb3J0ICs9IGAtIFdDQUcgQUFBIGNvbXBsaWFudDogJHtzdW1tYXJ5LmFhYUNvbXBsaWFudH0vJHtzdW1tYXJ5LnRvdGFsfSAoJHtNYXRoLnJvdW5kKChzdW1tYXJ5LmFhYUNvbXBsaWFudCAvIHN1bW1hcnkudG90YWwpICogMTAwKX0lKVxcblxcbmA7XG5cbiAgaWYgKHN1bW1hcnkuZmFpbGluZyA+IDApIHtcbiAgICByZXBvcnQgKz0gYCMjIEZhaWxpbmcgQ29sb3IgUGFpcnNcXG5cXG5gO1xuICAgIHJlc3VsdHNcbiAgICAgIC5maWx0ZXIociA9PiAhci5hdWRpdC5wYXNzZXMuQUEpXG4gICAgICAuZm9yRWFjaChyZXN1bHQgPT4ge1xuICAgICAgICByZXBvcnQgKz0gYCMjIyAke3Jlc3VsdC5jb250ZXh0fVxcbmA7XG4gICAgICAgIHJlcG9ydCArPSBgLSBGb3JlZ3JvdW5kOiAke3Jlc3VsdC5mb3JlZ3JvdW5kfVxcbmA7XG4gICAgICAgIHJlcG9ydCArPSBgLSBCYWNrZ3JvdW5kOiAke3Jlc3VsdC5iYWNrZ3JvdW5kfVxcbmA7XG4gICAgICAgIHJlcG9ydCArPSBgLSBDb250cmFzdCBSYXRpbzogJHtyZXN1bHQuYXVkaXQucmF0aW8udG9GaXhlZCgyKX06MVxcbmA7XG4gICAgICAgIHJlcG9ydCArPSBgLSBTdGF0dXM6ICR7cmVzdWx0LmF1ZGl0LmxldmVsfVxcbmA7XG4gICAgICAgIGlmIChyZXN1bHQuYXVkaXQucmVjb21tZW5kYXRpb24pIHtcbiAgICAgICAgICByZXBvcnQgKz0gYC0gUmVjb21tZW5kYXRpb246ICR7cmVzdWx0LmF1ZGl0LnJlY29tbWVuZGF0aW9ufVxcbmA7XG4gICAgICAgIH1cbiAgICAgICAgcmVwb3J0ICs9IGBcXG5gO1xuICAgICAgfSk7XG4gIH1cblxuICByZXBvcnQgKz0gYCMjIEFsbCBSZXN1bHRzXFxuXFxuYDtcbiAgcmVzdWx0cy5mb3JFYWNoKHJlc3VsdCA9PiB7XG4gICAgY29uc3Qgc3RhdHVzID0gcmVzdWx0LmF1ZGl0LnBhc3Nlcy5BQSA/ICfinIUnIDogJ+KdjCc7XG4gICAgcmVwb3J0ICs9IGAke3N0YXR1c30gJHtyZXN1bHQuY29udGV4dH06ICR7cmVzdWx0LmF1ZGl0LnJhdGlvLnRvRml4ZWQoMil9OjEgKCR7cmVzdWx0LmF1ZGl0LmxldmVsfSlcXG5gO1xuICB9KTtcblxuICByZXR1cm4gcmVwb3J0O1xufVxuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFrQk8sSUFBTUEsb0JBQW9CLEdBQUFDLE9BQUEsQ0FBQUQsb0JBQUEsR0FBRztFQUNsQ0UsRUFBRSxFQUFFO0lBQ0ZDLFVBQVUsRUFBRSxHQUFHO0lBQ2ZDLFNBQVMsRUFBRSxHQUFHO0lBQ2RDLFlBQVksRUFBRTtFQUNoQixDQUFDO0VBQ0RDLEdBQUcsRUFBRTtJQUNISCxVQUFVLEVBQUUsR0FBRztJQUNmQyxTQUFTLEVBQUUsR0FBRztJQUNkQyxZQUFZLEVBQUU7RUFDaEI7QUFDRixDQUFVO0FBR0gsSUFBTUUsb0JBQW9CLEdBQUFOLE9BQUEsQ0FBQU0sb0JBQUEsR0FBRztFQUNsQ0gsU0FBUyxFQUFFO0lBQ1RJLFFBQVEsRUFBRSxFQUFFO0lBQ1pDLFVBQVUsRUFBRTtFQUNkLENBQUM7RUFDREMsYUFBYSxFQUFFO0lBQ2JGLFFBQVEsRUFBRSxFQUFFO0lBQ1pDLFVBQVUsRUFBRTtFQUNkO0FBQ0YsQ0FBVTtBQXlCVixTQUFTRSxRQUFRQSxDQUFDQyxHQUFXLEVBQThDO0VBQ3pFLElBQU1DLE1BQU0sR0FBRywyQ0FBMkMsQ0FBQ0MsSUFBSSxDQUFDRixHQUFHLENBQUM7RUFDcEUsT0FBT0MsTUFBTSxHQUNUO0lBQ0VFLENBQUMsRUFBRUMsUUFBUSxDQUFDSCxNQUFNLENBQUMsQ0FBQyxDQUFDLEVBQUUsRUFBRSxDQUFDO0lBQzFCSSxDQUFDLEVBQUVELFFBQVEsQ0FBQ0gsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFLEVBQUUsQ0FBQztJQUMxQkssQ0FBQyxFQUFFRixRQUFRLENBQUNILE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRSxFQUFFO0VBQzNCLENBQUMsR0FDRCxJQUFJO0FBQ1Y7QUFLQSxTQUFTTSxvQkFBb0JBLENBQUNKLENBQVMsRUFBRUUsQ0FBUyxFQUFFQyxDQUFTLEVBQVU7RUFDckUsSUFBQUUsSUFBQSxHQUFxQixDQUFDTCxDQUFDLEVBQUVFLENBQUMsRUFBRUMsQ0FBQyxDQUFDLENBQUNHLEdBQUcsQ0FBQyxVQUFBQyxDQUFDLEVBQUk7TUFDdENBLENBQUMsR0FBR0EsQ0FBQyxHQUFHLEdBQUc7TUFDWCxPQUFPQSxDQUFDLElBQUksT0FBTyxHQUFHQSxDQUFDLEdBQUcsS0FBSyxHQUFHQyxJQUFJLENBQUNDLEdBQUcsQ0FBQyxDQUFDRixDQUFDLEdBQUcsS0FBSyxJQUFJLEtBQUssRUFBRSxHQUFHLENBQUM7SUFDdEUsQ0FBQyxDQUFDO0lBQUFHLEtBQUEsT0FBQUMsZUFBQSxDQUFBQyxPQUFBLEVBQUFQLElBQUE7SUFIS1EsRUFBRSxHQUFBSCxLQUFBO0lBQUVJLEVBQUUsR0FBQUosS0FBQTtJQUFFSyxFQUFFLEdBQUFMLEtBQUE7RUFLakIsT0FBTyxNQUFNLEdBQUdHLEVBQUUsR0FBRyxNQUFNLEdBQUdDLEVBQUUsR0FBRyxNQUFNLEdBQUdDLEVBQUU7QUFDaEQ7QUFLTyxTQUFTQyxzQkFBc0JBLENBQUNDLE1BQWMsRUFBRUMsTUFBYyxFQUFVO0VBQzdFLElBQU1DLElBQUksR0FBR3ZCLFFBQVEsQ0FBQ3FCLE1BQU0sQ0FBQztFQUM3QixJQUFNRyxJQUFJLEdBQUd4QixRQUFRLENBQUNzQixNQUFNLENBQUM7RUFFN0IsSUFBSSxDQUFDQyxJQUFJLElBQUksQ0FBQ0MsSUFBSSxFQUFFO0lBQ2xCLE1BQU0sSUFBSUMsS0FBSyxDQUFDLDhDQUE4QyxDQUFDO0VBQ2pFO0VBRUEsSUFBTUMsSUFBSSxHQUFHbEIsb0JBQW9CLENBQUNlLElBQUksQ0FBQ25CLENBQUMsRUFBRW1CLElBQUksQ0FBQ2pCLENBQUMsRUFBRWlCLElBQUksQ0FBQ2hCLENBQUMsQ0FBQztFQUN6RCxJQUFNb0IsSUFBSSxHQUFHbkIsb0JBQW9CLENBQUNnQixJQUFJLENBQUNwQixDQUFDLEVBQUVvQixJQUFJLENBQUNsQixDQUFDLEVBQUVrQixJQUFJLENBQUNqQixDQUFDLENBQUM7RUFFekQsSUFBTXFCLFNBQVMsR0FBR2hCLElBQUksQ0FBQ2lCLEdBQUcsQ0FBQ0gsSUFBSSxFQUFFQyxJQUFJLENBQUM7RUFDdEMsSUFBTUcsT0FBTyxHQUFHbEIsSUFBSSxDQUFDbUIsR0FBRyxDQUFDTCxJQUFJLEVBQUVDLElBQUksQ0FBQztFQUVwQyxPQUFPLENBQUNDLFNBQVMsR0FBRyxJQUFJLEtBQUtFLE9BQU8sR0FBRyxJQUFJLENBQUM7QUFDOUM7QUFLTyxTQUFTRSxjQUFjQSxDQUFDQyxJQUFlLEVBQXVCO0VBQ25FLElBQU1DLEtBQUssR0FBR2Qsc0JBQXNCLENBQUNhLElBQUksQ0FBQ0UsVUFBVSxFQUFFRixJQUFJLENBQUNHLFVBQVUsQ0FBQztFQUV0RSxJQUFJQyxhQUFxQjtFQUV6QixJQUFJSixJQUFJLENBQUNLLGFBQWEsRUFBRTtJQUN0QkQsYUFBYSxHQUFHaEQsb0JBQW9CLENBQUNFLEVBQUUsQ0FBQ0csWUFBWTtFQUN0RCxDQUFDLE1BQU0sSUFBSXVDLElBQUksQ0FBQ00sUUFBUSxLQUFLLE9BQU8sRUFBRTtJQUNwQ0YsYUFBYSxHQUFHaEQsb0JBQW9CLENBQUNFLEVBQUUsQ0FBQ0UsU0FBUztFQUNuRCxDQUFDLE1BQU07SUFDTDRDLGFBQWEsR0FBR2hELG9CQUFvQixDQUFDRSxFQUFFLENBQUNDLFVBQVU7RUFDcEQ7RUFFQSxJQUFNZ0QsUUFBUSxHQUFHTixLQUFLLElBQUlHLGFBQWE7RUFDdkMsSUFBTUksU0FBUyxHQUFHUixJQUFJLENBQUNLLGFBQWEsR0FDaENKLEtBQUssSUFBSTdDLG9CQUFvQixDQUFDTSxHQUFHLENBQUNELFlBQVksR0FDOUN1QyxJQUFJLENBQUNNLFFBQVEsS0FBSyxPQUFPLEdBQ3ZCTCxLQUFLLElBQUk3QyxvQkFBb0IsQ0FBQ00sR0FBRyxDQUFDRixTQUFTLEdBQzNDeUMsS0FBSyxJQUFJN0Msb0JBQW9CLENBQUNNLEdBQUcsQ0FBQ0gsVUFBVTtFQUVsRCxJQUFJa0QsS0FBNEI7RUFDaEMsSUFBSUMsY0FBa0M7RUFFdEMsSUFBSUYsU0FBUyxFQUFFO0lBQ2JDLEtBQUssR0FBRyxLQUFLO0VBQ2YsQ0FBQyxNQUFNLElBQUlGLFFBQVEsRUFBRTtJQUNuQkUsS0FBSyxHQUFHLElBQUk7RUFDZCxDQUFDLE1BQU07SUFDTEEsS0FBSyxHQUFHLE1BQU07SUFDZEMsY0FBYyxHQUFHQyxzQkFBc0IsQ0FBQ1gsSUFBSSxFQUFFQyxLQUFLLEVBQUVHLGFBQWEsQ0FBQztFQUNyRTtFQUVBLE9BQU87SUFDTEgsS0FBSyxFQUFMQSxLQUFLO0lBQ0xXLE1BQU0sRUFBRTtNQUNOdEQsRUFBRSxFQUFFaUQsUUFBUTtNQUNaN0MsR0FBRyxFQUFFOEM7SUFDUCxDQUFDO0lBQ0RDLEtBQUssRUFBTEEsS0FBSztJQUNMQyxjQUFjLEVBQWRBO0VBQ0YsQ0FBQztBQUNIO0FBS0EsU0FBU0Msc0JBQXNCQSxDQUM3QlgsSUFBZSxFQUNmYSxZQUFvQixFQUNwQlQsYUFBcUIsRUFDYjtFQUNSLElBQU1VLFdBQVcsR0FBR1YsYUFBYSxHQUFHUyxZQUFZO0VBRWhELElBQUlDLFdBQVcsR0FBRyxHQUFHLEVBQUU7SUFDckIsT0FBTyx1QkFBdUJkLElBQUksQ0FBQ0UsVUFBVSxrQkFBa0JGLElBQUksQ0FBQ0csVUFBVSx3Q0FBd0M7RUFDeEgsQ0FBQyxNQUFNLElBQUlXLFdBQVcsR0FBRyxDQUFDLEVBQUU7SUFDMUIsT0FBTyxxRkFBcUY7RUFDOUYsQ0FBQyxNQUFNO0lBQ0wsT0FBTywyR0FBMkc7RUFDcEg7QUFDRjtBQUtPLFNBQVNDLGlCQUFpQkEsQ0FBQ0MsVUFBdUIsRUFTdkQ7RUFDQSxJQUFNQyxPQUFPLEdBQUdELFVBQVUsQ0FBQ3ZDLEdBQUcsQ0FBQyxVQUFBdUIsSUFBSTtJQUFBLE9BQUFrQixNQUFBLENBQUFDLE1BQUEsS0FDOUJuQixJQUFJO01BQ1BvQixLQUFLLEVBQUVyQixjQUFjLENBQUNDLElBQUk7SUFBQztFQUFBLENBQzNCLENBQUM7RUFFSCxJQUFNcUIsT0FBTyxHQUFHO0lBQ2RDLEtBQUssRUFBRUwsT0FBTyxDQUFDTSxNQUFNO0lBQ3JCQyxPQUFPLEVBQUVQLE9BQU8sQ0FBQ1EsTUFBTSxDQUFDLFVBQUF0RCxDQUFDO01BQUEsT0FBSUEsQ0FBQyxDQUFDaUQsS0FBSyxDQUFDUixNQUFNLENBQUN0RCxFQUFFO0lBQUEsRUFBQyxDQUFDaUUsTUFBTTtJQUN0REcsT0FBTyxFQUFFVCxPQUFPLENBQUNRLE1BQU0sQ0FBQyxVQUFBdEQsQ0FBQztNQUFBLE9BQUksQ0FBQ0EsQ0FBQyxDQUFDaUQsS0FBSyxDQUFDUixNQUFNLENBQUN0RCxFQUFFO0lBQUEsRUFBQyxDQUFDaUUsTUFBTTtJQUN2REksV0FBVyxFQUFFVixPQUFPLENBQUNRLE1BQU0sQ0FDekIsVUFBQXRELENBQUM7TUFBQSxPQUFJQSxDQUFDLENBQUNpRCxLQUFLLENBQUNYLEtBQUssS0FBSyxJQUFJLElBQUl0QyxDQUFDLENBQUNpRCxLQUFLLENBQUNYLEtBQUssS0FBSyxLQUFLO0lBQUEsQ0FDeEQsQ0FBQyxDQUFDYyxNQUFNO0lBQ1JLLFlBQVksRUFBRVgsT0FBTyxDQUFDUSxNQUFNLENBQUMsVUFBQXRELENBQUM7TUFBQSxPQUFJQSxDQUFDLENBQUNpRCxLQUFLLENBQUNYLEtBQUssS0FBSyxLQUFLO0lBQUEsRUFBQyxDQUFDYztFQUM3RCxDQUFDO0VBRUQsT0FBTztJQUFFTixPQUFPLEVBQVBBLE9BQU87SUFBRUksT0FBTyxFQUFQQTtFQUFRLENBQUM7QUFDN0I7QUFLTyxTQUFTUSxxQkFBcUJBLENBQ25DM0IsVUFBa0IsRUFDbEJDLFVBQWtCLEVBSVY7RUFBQSxJQUhSTSxLQUFtQixHQUFBcUIsU0FBQSxDQUFBUCxNQUFBLFFBQUFPLFNBQUEsUUFBQUMsU0FBQSxHQUFBRCxTQUFBLE1BQUcsSUFBSTtFQUFBLElBQzFCeEIsUUFBNEIsR0FBQXdCLFNBQUEsQ0FBQVAsTUFBQSxRQUFBTyxTQUFBLFFBQUFDLFNBQUEsR0FBQUQsU0FBQSxNQUFHLFFBQVE7RUFBQSxJQUN2Q3pCLGFBQXNCLEdBQUF5QixTQUFBLENBQUFQLE1BQUEsUUFBQU8sU0FBQSxRQUFBQyxTQUFBLEdBQUFELFNBQUEsTUFBRyxLQUFLO0VBRTlCLElBQU1qQixZQUFZLEdBQUcxQixzQkFBc0IsQ0FBQ2UsVUFBVSxFQUFFQyxVQUFVLENBQUM7RUFFbkUsSUFBSUMsYUFBcUI7RUFDekIsSUFBSUMsYUFBYSxFQUFFO0lBQ2pCRCxhQUFhLEdBQUdoRCxvQkFBb0IsQ0FBQ3FELEtBQUssQ0FBQyxDQUFDaEQsWUFBWTtFQUMxRCxDQUFDLE1BQU0sSUFBSTZDLFFBQVEsS0FBSyxPQUFPLEVBQUU7SUFDL0JGLGFBQWEsR0FBR2hELG9CQUFvQixDQUFDcUQsS0FBSyxDQUFDLENBQUNqRCxTQUFTO0VBQ3ZELENBQUMsTUFBTTtJQUNMNEMsYUFBYSxHQUFHaEQsb0JBQW9CLENBQUNxRCxLQUFLLENBQUMsQ0FBQ2xELFVBQVU7RUFDeEQ7RUFFQSxJQUFJc0QsWUFBWSxJQUFJVCxhQUFhLEVBQUU7SUFDakMsT0FBT0YsVUFBVTtFQUNuQjtFQUdBLElBQU04QixLQUFLLEdBQUdqRSxRQUFRLENBQUNtQyxVQUFVLENBQUM7RUFDbEMsSUFBTStCLEtBQUssR0FBR2xFLFFBQVEsQ0FBQ29DLFVBQVUsQ0FBQztFQUVsQyxJQUFJLENBQUM2QixLQUFLLElBQUksQ0FBQ0MsS0FBSyxFQUFFO0lBQ3BCLE1BQU0sSUFBSXpDLEtBQUssQ0FBQyxzQkFBc0IsQ0FBQztFQUN6QztFQUVBLElBQU0wQyxXQUFXLEdBQUczRCxvQkFBb0IsQ0FBQzBELEtBQUssQ0FBQzlELENBQUMsRUFBRThELEtBQUssQ0FBQzVELENBQUMsRUFBRTRELEtBQUssQ0FBQzNELENBQUMsQ0FBQztFQUduRSxJQUFNNkQsZUFBZSxHQUNuQkQsV0FBVyxHQUFHLEdBQUcsR0FDYixDQUFDQSxXQUFXLEdBQUcsSUFBSSxJQUFJOUIsYUFBYSxHQUFHLElBQUksR0FDM0MsQ0FBQzhCLFdBQVcsR0FBRyxJQUFJLElBQUk5QixhQUFhLEdBQUcsSUFBSTtFQUdqRCxJQUFNZ0MsTUFBTSxHQUFHRCxlQUFlLEdBQUdELFdBQVcsR0FBRyxHQUFHLEdBQUcsR0FBRztFQUV4RCxJQUFNRyxTQUFTLEdBQUcxRCxJQUFJLENBQUNtQixHQUFHLENBQUMsR0FBRyxFQUFFbkIsSUFBSSxDQUFDaUIsR0FBRyxDQUFDLENBQUMsRUFBRWpCLElBQUksQ0FBQzJELEtBQUssQ0FBQ04sS0FBSyxDQUFDN0QsQ0FBQyxHQUFHaUUsTUFBTSxDQUFDLENBQUMsQ0FBQztFQUMxRSxJQUFNRyxTQUFTLEdBQUc1RCxJQUFJLENBQUNtQixHQUFHLENBQUMsR0FBRyxFQUFFbkIsSUFBSSxDQUFDaUIsR0FBRyxDQUFDLENBQUMsRUFBRWpCLElBQUksQ0FBQzJELEtBQUssQ0FBQ04sS0FBSyxDQUFDM0QsQ0FBQyxHQUFHK0QsTUFBTSxDQUFDLENBQUMsQ0FBQztFQUMxRSxJQUFNSSxTQUFTLEdBQUc3RCxJQUFJLENBQUNtQixHQUFHLENBQUMsR0FBRyxFQUFFbkIsSUFBSSxDQUFDaUIsR0FBRyxDQUFDLENBQUMsRUFBRWpCLElBQUksQ0FBQzJELEtBQUssQ0FBQ04sS0FBSyxDQUFDMUQsQ0FBQyxHQUFHOEQsTUFBTSxDQUFDLENBQUMsQ0FBQztFQUUxRSxPQUFPLElBQUlDLFNBQVMsQ0FBQ0ksUUFBUSxDQUFDLEVBQUUsQ0FBQyxDQUFDQyxRQUFRLENBQUMsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxHQUFHSCxTQUFTLENBQUNFLFFBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQ0MsUUFBUSxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsR0FBR0YsU0FBUyxDQUFDQyxRQUFRLENBQUMsRUFBRSxDQUFDLENBQUNDLFFBQVEsQ0FBQyxDQUFDLEVBQUUsR0FBRyxDQUFDLEVBQUU7QUFDMUk7QUFLTyxJQUFNQyxrQkFBK0IsR0FBQXRGLE9BQUEsQ0FBQXNGLGtCQUFBLEdBQUcsQ0FFN0M7RUFDRXpDLFVBQVUsRUFBRSxTQUFTO0VBQ3JCQyxVQUFVLEVBQUUsU0FBUztFQUNyQnlDLE9BQU8sRUFBRSxrQ0FBa0M7RUFDM0N0QyxRQUFRLEVBQUU7QUFDWixDQUFDLEVBQ0Q7RUFDRUosVUFBVSxFQUFFLFNBQVM7RUFDckJDLFVBQVUsRUFBRSxTQUFTO0VBQ3JCeUMsT0FBTyxFQUFFLCtCQUErQjtFQUN4Q3RDLFFBQVEsRUFBRTtBQUNaLENBQUMsRUFHRDtFQUNFSixVQUFVLEVBQUUsU0FBUztFQUNyQkMsVUFBVSxFQUFFLFNBQVM7RUFDckJ5QyxPQUFPLEVBQUUscUJBQXFCO0VBQzlCdEMsUUFBUSxFQUFFO0FBQ1osQ0FBQyxFQUdEO0VBQ0VKLFVBQVUsRUFBRSxTQUFTO0VBQ3JCQyxVQUFVLEVBQUUsU0FBUztFQUNyQnlDLE9BQU8sRUFBRSxvQ0FBb0M7RUFDN0N0QyxRQUFRLEVBQUU7QUFDWixDQUFDLEVBR0Q7RUFDRUosVUFBVSxFQUFFLFNBQVM7RUFDckJDLFVBQVUsRUFBRSxTQUFTO0VBQ3JCeUMsT0FBTyxFQUFFLFlBQVk7RUFDckJ0QyxRQUFRLEVBQUU7QUFDWixDQUFDLEVBR0Q7RUFDRUosVUFBVSxFQUFFLFNBQVM7RUFDckJDLFVBQVUsRUFBRSxTQUFTO0VBQ3JCeUMsT0FBTyxFQUFFLGNBQWM7RUFDdkJ0QyxRQUFRLEVBQUU7QUFDWixDQUFDLEVBR0Q7RUFDRUosVUFBVSxFQUFFLFNBQVM7RUFDckJDLFVBQVUsRUFBRSxTQUFTO0VBQ3JCeUMsT0FBTyxFQUFFLHdCQUF3QjtFQUNqQ3ZDLGFBQWEsRUFBRTtBQUNqQixDQUFDLEVBR0Q7RUFDRUgsVUFBVSxFQUFFLFNBQVM7RUFDckJDLFVBQVUsRUFBRSxTQUFTO0VBQ3JCeUMsT0FBTyxFQUFFLFdBQVc7RUFDcEJ0QyxRQUFRLEVBQUU7QUFDWixDQUFDLEVBR0Q7RUFDRUosVUFBVSxFQUFFLFNBQVM7RUFDckJDLFVBQVUsRUFBRSxTQUFTO0VBQ3JCeUMsT0FBTyxFQUFFLGlCQUFpQjtFQUMxQnRDLFFBQVEsRUFBRTtBQUNaLENBQUMsRUFHRDtFQUNFSixVQUFVLEVBQUUsU0FBUztFQUNyQkMsVUFBVSxFQUFFLFNBQVM7RUFDckJ5QyxPQUFPLEVBQUUsaUJBQWlCO0VBQzFCdEMsUUFBUSxFQUFFO0FBQ1osQ0FBQyxDQUNGO0FBS00sU0FBU3VDLHNCQUFzQkEsQ0FBQSxFQUFHO0VBQ3ZDLE9BQU85QixpQkFBaUIsQ0FBQzRCLGtCQUFrQixDQUFDO0FBQzlDO0FBS08sU0FBU0csc0JBQXNCQSxDQUNwQ0MsWUFBa0QsRUFDMUM7RUFDUixJQUFROUIsT0FBTyxHQUFjOEIsWUFBWSxDQUFqQzlCLE9BQU87SUFBRUksT0FBTyxHQUFLMEIsWUFBWSxDQUF4QjFCLE9BQU87RUFFeEIsSUFBSTJCLE1BQU0sR0FBRyxtQ0FBbUM7RUFDaERBLE1BQU0sSUFBSSxjQUFjO0VBQ3hCQSxNQUFNLElBQUksK0JBQStCM0IsT0FBTyxDQUFDQyxLQUFLLElBQUk7RUFDMUQwQixNQUFNLElBQUksc0JBQXNCM0IsT0FBTyxDQUFDRyxPQUFPLElBQUlILE9BQU8sQ0FBQ0MsS0FBSyxLQUFLM0MsSUFBSSxDQUFDMkQsS0FBSyxDQUFFakIsT0FBTyxDQUFDRyxPQUFPLEdBQUdILE9BQU8sQ0FBQ0MsS0FBSyxHQUFJLEdBQUcsQ0FBQyxNQUFNO0VBQzlIMEIsTUFBTSxJQUFJLHNCQUFzQjNCLE9BQU8sQ0FBQ0ssT0FBTyxJQUFJTCxPQUFPLENBQUNDLEtBQUssS0FBSzNDLElBQUksQ0FBQzJELEtBQUssQ0FBRWpCLE9BQU8sQ0FBQ0ssT0FBTyxHQUFHTCxPQUFPLENBQUNDLEtBQUssR0FBSSxHQUFHLENBQUMsTUFBTTtFQUM5SDBCLE1BQU0sSUFBSSx5QkFBeUIzQixPQUFPLENBQUNPLFlBQVksSUFBSVAsT0FBTyxDQUFDQyxLQUFLLEtBQUszQyxJQUFJLENBQUMyRCxLQUFLLENBQUVqQixPQUFPLENBQUNPLFlBQVksR0FBR1AsT0FBTyxDQUFDQyxLQUFLLEdBQUksR0FBRyxDQUFDLFFBQVE7RUFFN0ksSUFBSUQsT0FBTyxDQUFDSyxPQUFPLEdBQUcsQ0FBQyxFQUFFO0lBQ3ZCc0IsTUFBTSxJQUFJLDRCQUE0QjtJQUN0Qy9CLE9BQU8sQ0FDSlEsTUFBTSxDQUFDLFVBQUF0RCxDQUFDO01BQUEsT0FBSSxDQUFDQSxDQUFDLENBQUNpRCxLQUFLLENBQUNSLE1BQU0sQ0FBQ3RELEVBQUU7SUFBQSxFQUFDLENBQy9CMkYsT0FBTyxDQUFDLFVBQUFoRixNQUFNLEVBQUk7TUFDakIrRSxNQUFNLElBQUksT0FBTy9FLE1BQU0sQ0FBQzJFLE9BQU8sSUFBSTtNQUNuQ0ksTUFBTSxJQUFJLGlCQUFpQi9FLE1BQU0sQ0FBQ2lDLFVBQVUsSUFBSTtNQUNoRDhDLE1BQU0sSUFBSSxpQkFBaUIvRSxNQUFNLENBQUNrQyxVQUFVLElBQUk7TUFDaEQ2QyxNQUFNLElBQUkscUJBQXFCL0UsTUFBTSxDQUFDbUQsS0FBSyxDQUFDbkIsS0FBSyxDQUFDaUQsT0FBTyxDQUFDLENBQUMsQ0FBQyxNQUFNO01BQ2xFRixNQUFNLElBQUksYUFBYS9FLE1BQU0sQ0FBQ21ELEtBQUssQ0FBQ1gsS0FBSyxJQUFJO01BQzdDLElBQUl4QyxNQUFNLENBQUNtRCxLQUFLLENBQUNWLGNBQWMsRUFBRTtRQUMvQnNDLE1BQU0sSUFBSSxxQkFBcUIvRSxNQUFNLENBQUNtRCxLQUFLLENBQUNWLGNBQWMsSUFBSTtNQUNoRTtNQUNBc0MsTUFBTSxJQUFJLElBQUk7SUFDaEIsQ0FBQyxDQUFDO0VBQ047RUFFQUEsTUFBTSxJQUFJLG9CQUFvQjtFQUM5Qi9CLE9BQU8sQ0FBQ2dDLE9BQU8sQ0FBQyxVQUFBaEYsTUFBTSxFQUFJO0lBQ3hCLElBQU1rRixNQUFNLEdBQUdsRixNQUFNLENBQUNtRCxLQUFLLENBQUNSLE1BQU0sQ0FBQ3RELEVBQUUsR0FBRyxHQUFHLEdBQUcsR0FBRztJQUNqRDBGLE1BQU0sSUFBSSxHQUFHRyxNQUFNLElBQUlsRixNQUFNLENBQUMyRSxPQUFPLEtBQUszRSxNQUFNLENBQUNtRCxLQUFLLENBQUNuQixLQUFLLENBQUNpRCxPQUFPLENBQUMsQ0FBQyxDQUFDLE9BQU9qRixNQUFNLENBQUNtRCxLQUFLLENBQUNYLEtBQUssS0FBSztFQUN2RyxDQUFDLENBQUM7RUFFRixPQUFPdUMsTUFBTTtBQUNmIiwiaWdub3JlTGlzdCI6W119