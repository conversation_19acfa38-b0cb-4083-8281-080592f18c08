9049f5a8970f719ba85df0eb13bd5ff8
_getJestObj().mock("../../services/apiClient", function () {
  return {
    apiClient: mockApiClient
  };
});
_getJestObj().mock("../../services/cachingService", function () {
  return {
    cachingService: {
      get: jest.fn(),
      set: jest.fn(),
      delete: jest.fn(),
      clear: jest.fn(),
      has: jest.fn()
    }
  };
});
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
function _getJestObj() {
  var _require14 = require("@jest/globals"),
    jest = _require14.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var mockApiClient = {
  get: jest.fn(),
  post: jest.fn(),
  patch: jest.fn(),
  delete: jest.fn()
};
describe('Core Integration Tests', function () {
  beforeEach(function () {
    jest.clearAllMocks();
  });
  describe('API Client Integration', function () {
    it('should handle successful API requests', (0, _asyncToGenerator2.default)(function* () {
      var _require = require("../../services/apiClient"),
        apiClient = _require.apiClient;
      var mockResponse = {
        data: {
          id: 'test123',
          name: 'Test Service',
          status: 'active'
        }
      };
      apiClient.get.mockResolvedValue(mockResponse);
      var response = yield apiClient.get('/api/test', {}, true);
      expect(response.data.id).toBe('test123');
      expect(response.data.name).toBe('Test Service');
      expect(apiClient.get).toHaveBeenCalledWith('/api/test', {}, true);
    }));
    it('should handle API request failures', (0, _asyncToGenerator2.default)(function* () {
      var _require2 = require("../../services/apiClient"),
        apiClient = _require2.apiClient;
      var mockError = new Error('Network error');
      apiClient.get.mockRejectedValue(mockError);
      yield expect(apiClient.get('/api/test', {}, true)).rejects.toThrow('Network error');
    }));
    it('should handle different HTTP methods', (0, _asyncToGenerator2.default)(function* () {
      var _require3 = require("../../services/apiClient"),
        apiClient = _require3.apiClient;
      var mockData = {
        name: 'New Service'
      };
      var mockResponse = {
        data: Object.assign({
          id: 'new123'
        }, mockData)
      };
      apiClient.post.mockResolvedValue(mockResponse);
      apiClient.patch.mockResolvedValue(mockResponse);
      apiClient.delete.mockResolvedValue({
        data: {
          success: true
        }
      });
      var postResponse = yield apiClient.post('/api/services', mockData, true);
      expect(postResponse.data.id).toBe('new123');
      expect(apiClient.post).toHaveBeenCalledWith('/api/services', mockData, true);
      var patchResponse = yield apiClient.patch('/api/services/new123', mockData, true);
      expect(patchResponse.data.id).toBe('new123');
      expect(apiClient.patch).toHaveBeenCalledWith('/api/services/new123', mockData, true);
      var deleteResponse = yield apiClient.delete('/api/services/new123', true);
      expect(deleteResponse.data.success).toBe(true);
      expect(apiClient.delete).toHaveBeenCalledWith('/api/services/new123', true);
    }));
  });
  describe('Booking Flow Integration', function () {
    it('should complete booking creation flow', (0, _asyncToGenerator2.default)(function* () {
      var _require4 = require("../../services/apiClient"),
        apiClient = _require4.apiClient;
      var mockSearchResults = {
        results: [{
          id: 'service123',
          name: 'Premium Haircut',
          price: 50.0,
          provider: {
            id: 'provider123',
            name: 'Elite Salon'
          }
        }],
        total_count: 1
      };
      var mockBooking = {
        id: 'booking123',
        serviceId: 'service123',
        providerId: 'provider123',
        customerId: 'user123',
        status: 'pending',
        totalAmount: 50.0
      };
      apiClient.get.mockResolvedValueOnce({
        data: mockSearchResults
      });
      apiClient.post.mockResolvedValueOnce({
        data: mockBooking
      });
      var searchResponse = yield apiClient.get('/api/catalog/search/', {
        query: 'haircut',
        category: 'beauty'
      }, false);
      expect(searchResponse.data.results).toHaveLength(1);
      expect(searchResponse.data.results[0].name).toBe('Premium Haircut');
      var bookingData = {
        serviceId: 'service123',
        providerId: 'provider123',
        scheduledDate: '2024-08-02',
        scheduledTime: '10:00'
      };
      var bookingResponse = yield apiClient.post('/api/bookings/', bookingData, true);
      expect(bookingResponse.data.id).toBe('booking123');
      expect(bookingResponse.data.status).toBe('pending');
      expect(bookingResponse.data.totalAmount).toBe(50.0);
      expect(apiClient.get).toHaveBeenCalledWith('/api/catalog/search/', {
        query: 'haircut',
        category: 'beauty'
      }, false);
      expect(apiClient.post).toHaveBeenCalledWith('/api/bookings/', bookingData, true);
    }));
    it('should handle booking status updates', (0, _asyncToGenerator2.default)(function* () {
      var _require5 = require("../../services/apiClient"),
        apiClient = _require5.apiClient;
      var mockUpdatedBooking = {
        id: 'booking123',
        status: 'confirmed',
        paymentStatus: 'paid'
      };
      apiClient.patch.mockResolvedValue({
        data: mockUpdatedBooking
      });
      var response = yield apiClient.patch('/api/bookings/booking123/', {
        status: 'confirmed'
      }, true);
      expect(response.data.status).toBe('confirmed');
      expect(response.data.paymentStatus).toBe('paid');
    }));
  });
  describe('Payment Integration', function () {
    it('should handle payment intent creation', (0, _asyncToGenerator2.default)(function* () {
      var _require6 = require("../../services/apiClient"),
        apiClient = _require6.apiClient;
      var mockPaymentIntent = {
        id: 'pi_123',
        client_secret: 'pi_123_secret',
        amount: 50.0,
        currency: 'CAD',
        status: 'requires_payment_method'
      };
      apiClient.post.mockResolvedValue({
        data: mockPaymentIntent
      });
      var response = yield apiClient.post('/api/payments/intents/', {
        booking_id: 'booking123',
        amount: 50.0,
        currency: 'CAD',
        payment_method_id: 'pm_1'
      }, true);
      expect(response.data.id).toBe('pi_123');
      expect(response.data.amount).toBe(50.0);
      expect(response.data.status).toBe('requires_payment_method');
    }));
    it('should handle payment confirmation', (0, _asyncToGenerator2.default)(function* () {
      var _require7 = require("../../services/apiClient"),
        apiClient = _require7.apiClient;
      var mockPaymentConfirmation = {
        id: 'pi_123',
        status: 'succeeded',
        amount: 50.0
      };
      apiClient.post.mockResolvedValue({
        data: mockPaymentConfirmation
      });
      var response = yield apiClient.post('/api/payments/confirm/', {
        payment_intent_id: 'pi_123',
        payment_method_id: 'pm_1'
      }, true);
      expect(response.data.status).toBe('succeeded');
      expect(response.data.amount).toBe(50.0);
    }));
    it('should handle payment method management', (0, _asyncToGenerator2.default)(function* () {
      var _require8 = require("../../services/apiClient"),
        apiClient = _require8.apiClient;
      var mockPaymentMethods = [{
        id: 'pm_1',
        type: 'credit_card',
        brand: 'visa',
        last4: '4242',
        is_default: true
      }];
      apiClient.get.mockResolvedValue({
        data: mockPaymentMethods
      });
      var response = yield apiClient.get('/api/payments/methods/', {}, true);
      expect(response.data).toHaveLength(1);
      expect(response.data[0].brand).toBe('visa');
      expect(response.data[0].is_default).toBe(true);
    }));
  });
  describe('Provider Service Management', function () {
    it('should handle service creation', (0, _asyncToGenerator2.default)(function* () {
      var _require9 = require("../../services/apiClient"),
        apiClient = _require9.apiClient;
      var mockService = {
        id: 'service456',
        name: 'Hair Styling',
        description: 'Professional hair styling',
        price: 75.0,
        duration: 90,
        category: 'beauty',
        isActive: true
      };
      apiClient.post.mockResolvedValue({
        data: mockService
      });
      var serviceData = {
        name: 'Hair Styling',
        description: 'Professional hair styling',
        price: 75.0,
        duration: 90,
        category: 'beauty'
      };
      var response = yield apiClient.post('/api/services/', serviceData, true);
      expect(response.data.id).toBe('service456');
      expect(response.data.name).toBe('Hair Styling');
      expect(response.data.price).toBe(75.0);
    }));
    it('should handle provider booking management', (0, _asyncToGenerator2.default)(function* () {
      var _require0 = require("../../services/apiClient"),
        apiClient = _require0.apiClient;
      var mockBookings = [{
        id: 'booking456',
        serviceId: 'service123',
        customerId: 'customer456',
        customerName: 'Alice Johnson',
        status: 'pending',
        scheduledDate: '2024-08-03',
        scheduledTime: '14:00'
      }];
      apiClient.get.mockResolvedValue({
        data: mockBookings
      });
      var response = yield apiClient.get('/api/bookings/provider/provider123/', {}, true);
      expect(response.data).toHaveLength(1);
      expect(response.data[0].customerName).toBe('Alice Johnson');
      expect(response.data[0].status).toBe('pending');
    }));
  });
  describe('Error Handling and Recovery', function () {
    it('should handle network errors gracefully', (0, _asyncToGenerator2.default)(function* () {
      var _require1 = require("../../services/apiClient"),
        apiClient = _require1.apiClient;
      apiClient.get.mockRejectedValue(new Error('Network request failed'));
      yield expect(apiClient.get('/api/services/', {}, true)).rejects.toThrow('Network request failed');
    }));
    it('should handle authentication errors', (0, _asyncToGenerator2.default)(function* () {
      var _require10 = require("../../services/apiClient"),
        apiClient = _require10.apiClient;
      var authError = new Error('Unauthorized');
      authError.status = 401;
      apiClient.get.mockRejectedValue(authError);
      yield expect(apiClient.get('/api/protected/', {}, true)).rejects.toThrow('Unauthorized');
    }));
    it('should handle validation errors', (0, _asyncToGenerator2.default)(function* () {
      var _require11 = require("../../services/apiClient"),
        apiClient = _require11.apiClient;
      var validationError = new Error('Invalid data');
      validationError.status = 400;
      apiClient.post.mockRejectedValue(validationError);
      yield expect(apiClient.post('/api/services/', {
        invalid: 'data'
      }, true)).rejects.toThrow('Invalid data');
    }));
    it('should handle server errors', (0, _asyncToGenerator2.default)(function* () {
      var _require12 = require("../../services/apiClient"),
        apiClient = _require12.apiClient;
      var serverError = new Error('Internal server error');
      serverError.status = 500;
      apiClient.get.mockRejectedValue(serverError);
      yield expect(apiClient.get('/api/services/', {}, true)).rejects.toThrow('Internal server error');
    }));
  });
  describe('Data Flow Integration', function () {
    it('should handle complete customer journey data flow', (0, _asyncToGenerator2.default)(function* () {
      var _require13 = require("../../services/apiClient"),
        apiClient = _require13.apiClient;
      var mockSearchResults = {
        data: {
          results: [{
            id: 'service123',
            name: 'Test Service'
          }]
        }
      };
      var mockBooking = {
        data: {
          id: 'booking123',
          status: 'pending'
        }
      };
      var mockPaymentIntent = {
        data: {
          id: 'pi_123',
          status: 'requires_payment_method'
        }
      };
      var mockPaymentConfirmation = {
        data: {
          status: 'succeeded'
        }
      };
      var mockBookingUpdate = {
        data: {
          id: 'booking123',
          status: 'confirmed'
        }
      };
      apiClient.get.mockResolvedValueOnce(mockSearchResults);
      apiClient.post.mockResolvedValueOnce(mockBooking).mockResolvedValueOnce(mockPaymentIntent).mockResolvedValueOnce(mockPaymentConfirmation);
      apiClient.patch.mockResolvedValueOnce(mockBookingUpdate);
      var searchResponse = yield apiClient.get('/api/catalog/search/', {
        query: 'test'
      }, false);
      var bookingResponse = yield apiClient.post('/api/bookings/', {
        serviceId: 'service123'
      }, true);
      var paymentIntentResponse = yield apiClient.post('/api/payments/intents/', {
        booking_id: 'booking123'
      }, true);
      var paymentConfirmationResponse = yield apiClient.post('/api/payments/confirm/', {
        payment_intent_id: 'pi_123'
      }, true);
      var bookingUpdateResponse = yield apiClient.patch('/api/bookings/booking123/', {
        status: 'confirmed'
      }, true);
      expect(searchResponse.data.results[0].id).toBe('service123');
      expect(bookingResponse.data.id).toBe('booking123');
      expect(paymentIntentResponse.data.id).toBe('pi_123');
      expect(paymentConfirmationResponse.data.status).toBe('succeeded');
      expect(bookingUpdateResponse.data.status).toBe('confirmed');
      expect(apiClient.get).toHaveBeenCalledTimes(1);
      expect(apiClient.post).toHaveBeenCalledTimes(3);
      expect(apiClient.patch).toHaveBeenCalledTimes(1);
    }));
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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