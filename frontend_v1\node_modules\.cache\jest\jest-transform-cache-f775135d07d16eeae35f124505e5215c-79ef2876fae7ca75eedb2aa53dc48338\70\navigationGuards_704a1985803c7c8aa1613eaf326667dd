57a3118bb6f57c2c2345138c638ebe25
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.navigationGuards = exports.default = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _authSlice = require("../store/authSlice");
var _navigationAnalytics = _interopRequireDefault(require("./navigationAnalytics"));
var NavigationGuardsService = function () {
  function NavigationGuardsService() {
    (0, _classCallCheck2.default)(this, NavigationGuardsService);
    this.routeConfigs = new Map();
    this.initializeRouteConfigs();
  }
  return (0, _createClass2.default)(NavigationGuardsService, [{
    key: "initializeRouteConfigs",
    value: function initializeRouteConfigs() {
      this.addRouteConfig({
        name: 'Welcome',
        requiresAuth: false
      });
      this.addRouteConfig({
        name: 'Login',
        requiresAuth: false
      });
      this.addRouteConfig({
        name: 'Register',
        requiresAuth: false
      });
      this.addRouteConfig({
        name: 'ForgotPassword',
        requiresAuth: false
      });
      this.addRouteConfig({
        name: 'CustomerTabs',
        requiresAuth: true,
        allowedRoles: ['customer']
      });
      this.addRouteConfig({
        name: 'Home',
        requiresAuth: true,
        allowedRoles: ['customer']
      });
      this.addRouteConfig({
        name: 'Search',
        requiresAuth: true,
        allowedRoles: ['customer']
      });
      this.addRouteConfig({
        name: 'Bookings',
        requiresAuth: true,
        allowedRoles: ['customer']
      });
      this.addRouteConfig({
        name: 'Messages',
        requiresAuth: true,
        allowedRoles: ['customer']
      });
      this.addRouteConfig({
        name: 'Profile',
        requiresAuth: true,
        allowedRoles: ['customer']
      });
      this.addRouteConfig({
        name: 'ProviderDetails',
        requiresAuth: true,
        allowedRoles: ['customer']
      });
      this.addRouteConfig({
        name: 'ServiceDetails',
        requiresAuth: true,
        allowedRoles: ['customer']
      });
      this.addRouteConfig({
        name: 'BookingScreen',
        requiresAuth: true,
        allowedRoles: ['customer']
      });
      this.addRouteConfig({
        name: 'Checkout',
        requiresAuth: true,
        allowedRoles: ['customer'],
        requiresVerification: true
      });
      this.addRouteConfig({
        name: 'Payment',
        requiresAuth: true,
        allowedRoles: ['customer'],
        requiresVerification: true
      });
      this.addRouteConfig({
        name: 'ProviderTabs',
        requiresAuth: true,
        allowedRoles: ['provider']
      });
      this.addRouteConfig({
        name: 'ProviderDashboard',
        requiresAuth: true,
        allowedRoles: ['provider']
      });
      this.addRouteConfig({
        name: 'ProviderBookings',
        requiresAuth: true,
        allowedRoles: ['provider']
      });
      this.addRouteConfig({
        name: 'ProviderServices',
        requiresAuth: true,
        allowedRoles: ['provider']
      });
      this.addRouteConfig({
        name: 'ProviderProfile',
        requiresAuth: true,
        allowedRoles: ['provider']
      });
      this.addRouteConfig({
        name: 'Conversation',
        requiresAuth: true,
        allowedRoles: ['customer', 'provider']
      });
      this.addRouteConfig({
        name: 'Notifications',
        requiresAuth: true,
        allowedRoles: ['customer', 'provider']
      });
      this.addRouteConfig({
        name: 'AccountSettings',
        requiresAuth: true,
        allowedRoles: ['customer', 'provider']
      });
      this.addRouteConfig({
        name: 'EditProfile',
        requiresAuth: true,
        allowedRoles: ['customer', 'provider']
      });
    }
  }, {
    key: "addRouteConfig",
    value: function addRouteConfig(config) {
      this.routeConfigs.set(config.name, config);
    }
  }, {
    key: "canNavigate",
    value: function canNavigate(routeName, params) {
      var config = this.routeConfigs.get(routeName);
      if (!config) {
        console.warn(`Navigation guard: Route '${routeName}' not configured`);
        return {
          allowed: true
        };
      }
      var authStore = _authSlice.useAuthStore.getState();
      var isAuthenticated = authStore.isAuthenticated,
        userRole = authStore.userRole,
        user = authStore.user;
      if (config.requiresAuth && !isAuthenticated) {
        _navigationAnalytics.default.trackNavigationError('Authentication required', routeName, {
          requiresAuth: true
        });
        return {
          allowed: false,
          redirectTo: 'Login',
          reason: 'Authentication required',
          requiresAuth: true
        };
      }
      if (config.allowedRoles && config.allowedRoles.length > 0) {
        if (!userRole || !config.allowedRoles.includes(userRole)) {
          _navigationAnalytics.default.trackNavigationError('Insufficient role permissions', routeName, {
            userRole: userRole,
            allowedRoles: config.allowedRoles
          });
          return {
            allowed: false,
            redirectTo: this.getDefaultRouteForRole(userRole),
            reason: 'Insufficient role permissions',
            requiresRole: config.allowedRoles[0]
          };
        }
      }
      if (config.requiresOnboarding && user && !user.hasCompletedOnboarding) {
        return {
          allowed: false,
          redirectTo: userRole === 'customer' ? 'CustomerOnboarding' : 'ProviderOnboarding',
          reason: 'Onboarding required'
        };
      }
      if (config.requiresVerification && user && !user.isVerified) {
        return {
          allowed: false,
          redirectTo: 'VerificationRequired',
          reason: 'Account verification required'
        };
      }
      if (config.customValidator) {
        var customResult = config.customValidator({
          user: user,
          userRole: userRole,
          isAuthenticated: isAuthenticated
        });
        if (!customResult.allowed) {
          _navigationAnalytics.default.trackNavigationError(customResult.reason || 'Custom validation failed', routeName, {
            customValidator: true
          });
          return customResult;
        }
      }
      return {
        allowed: true
      };
    }
  }, {
    key: "getDefaultRouteForRole",
    value: function getDefaultRouteForRole(userRole) {
      switch (userRole) {
        case 'customer':
          return 'CustomerTabs';
        case 'provider':
          return 'ProviderTabs';
        default:
          return 'Login';
      }
    }
  }, {
    key: "validateNavigationFlow",
    value: function validateNavigationFlow(currentRoute, targetRoute, flowContext) {
      var validFlows = {
        ProviderDetails: ['ServiceDetails', 'BookingScreen'],
        ServiceDetails: ['BookingScreen', 'ProviderDetails'],
        BookingScreen: ['Checkout', 'ServiceDetails'],
        Checkout: ['Payment', 'BookingScreen'],
        Payment: ['BookingConfirmation', 'Checkout'],
        Profile: ['EditProfile', 'AccountSettings'],
        EditProfile: ['Profile'],
        AccountSettings: ['Profile'],
        Messages: ['Conversation'],
        Conversation: ['Messages']
      };
      var allowedTargets = validFlows[currentRoute];
      if (allowedTargets && !allowedTargets.includes(targetRoute)) {
        var backNavigation = this.isValidBackNavigation(currentRoute, targetRoute);
        if (!backNavigation) {
          _navigationAnalytics.default.trackNavigationError('Invalid navigation flow', targetRoute, {
            currentRoute: currentRoute,
            validTargets: allowedTargets
          });
          return {
            allowed: false,
            reason: 'Invalid navigation flow'
          };
        }
      }
      return {
        allowed: true
      };
    }
  }, {
    key: "isValidBackNavigation",
    value: function isValidBackNavigation(currentRoute, targetRoute) {
      var backNavigationMap = {
        ServiceDetails: ['ProviderDetails', 'Search', 'Home'],
        BookingScreen: ['ServiceDetails', 'ProviderDetails'],
        Checkout: ['BookingScreen'],
        Payment: ['Checkout'],
        BookingConfirmation: ['Home', 'Bookings'],
        EditProfile: ['Profile'],
        AccountSettings: ['Profile'],
        Conversation: ['Messages'],
        ProviderDetails: ['Search', 'Home']
      };
      var validBackTargets = backNavigationMap[currentRoute];
      return validBackTargets ? validBackTargets.includes(targetRoute) : true;
    }
  }, {
    key: "handleNavigationGuardFailure",
    value: function handleNavigationGuardFailure(result, originalRoute, navigation) {
      if (result.redirectTo) {
        navigation.reset({
          index: 0,
          routes: [{
            name: result.redirectTo
          }]
        });
      } else {
        console.error('Navigation blocked:', result.reason);
      }
      _navigationAnalytics.default.trackNavigationError(result.reason || 'Navigation guard failure', originalRoute, {
        redirectTo: result.redirectTo,
        requiresAuth: result.requiresAuth,
        requiresRole: result.requiresRole
      });
    }
  }, {
    key: "getRouteConfig",
    value: function getRouteConfig(routeName) {
      return this.routeConfigs.get(routeName);
    }
  }, {
    key: "requiresAuth",
    value: function requiresAuth(routeName) {
      var config = this.routeConfigs.get(routeName);
      return (config == null ? void 0 : config.requiresAuth) || false;
    }
  }, {
    key: "isAllowedForRole",
    value: function isAllowedForRole(routeName, userRole) {
      var config = this.routeConfigs.get(routeName);
      if (!config || !config.allowedRoles) return true;
      return config.allowedRoles.includes(userRole);
    }
  }]);
}();
var navigationGuards = exports.navigationGuards = new NavigationGuardsService();
var _default = exports.default = navigationGuards;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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