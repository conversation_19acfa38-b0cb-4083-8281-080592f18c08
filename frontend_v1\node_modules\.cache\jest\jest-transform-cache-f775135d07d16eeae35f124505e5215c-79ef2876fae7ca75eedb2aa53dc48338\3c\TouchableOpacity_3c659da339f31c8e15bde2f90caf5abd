997dbdfe4682ba113040aee2ae68955a
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _Animated = _interopRequireDefault(require("../../Animated/Animated"));
var _Easing = _interopRequireDefault(require("../../Animated/Easing"));
var _Pressability = _interopRequireDefault(require("../../Pressability/Pressability"));
var _PressabilityDebug = require("../../Pressability/PressabilityDebug");
var _flattenStyle4 = _interopRequireDefault(require("../../StyleSheet/flattenStyle"));
var _Platform = _interopRequireDefault(require("../../Utilities/Platform"));
var React = _interopRequireWildcard(require("react"));
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["onBlur", "onFocus"];
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var TouchableOpacity = function (_React$Component) {
  function TouchableOpacity() {
    var _this;
    (0, _classCallCheck2.default)(this, TouchableOpacity);
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _callSuper(this, TouchableOpacity, [].concat(args));
    _this.state = {
      anim: new _Animated.default.Value(_this._getChildStyleOpacityWithDefault()),
      pressability: new _Pressability.default(_this._createPressabilityConfig())
    };
    return _this;
  }
  (0, _inherits2.default)(TouchableOpacity, _React$Component);
  return (0, _createClass2.default)(TouchableOpacity, [{
    key: "_createPressabilityConfig",
    value: function _createPressabilityConfig() {
      var _ref,
        _this$props$disabled,
        _this$props$accessibi,
        _this2 = this;
      return {
        cancelable: !this.props.rejectResponderTermination,
        disabled: (_ref = (_this$props$disabled = this.props.disabled) != null ? _this$props$disabled : this.props['aria-disabled']) != null ? _ref : (_this$props$accessibi = this.props.accessibilityState) == null ? void 0 : _this$props$accessibi.disabled,
        hitSlop: this.props.hitSlop,
        delayLongPress: this.props.delayLongPress,
        delayPressIn: this.props.delayPressIn,
        delayPressOut: this.props.delayPressOut,
        minPressDuration: 0,
        pressRectOffset: this.props.pressRetentionOffset,
        onBlur: function onBlur(event) {
          if (_Platform.default.isTV) {
            _this2._opacityInactive(250);
          }
          if (_this2.props.onBlur != null) {
            _this2.props.onBlur(event);
          }
        },
        onFocus: function onFocus(event) {
          if (_Platform.default.isTV) {
            _this2._opacityActive(150);
          }
          if (_this2.props.onFocus != null) {
            _this2.props.onFocus(event);
          }
        },
        onLongPress: this.props.onLongPress,
        onPress: this.props.onPress,
        onPressIn: function onPressIn(event) {
          _this2._opacityActive(event.dispatchConfig.registrationName === 'onResponderGrant' ? 0 : 150);
          if (_this2.props.onPressIn != null) {
            _this2.props.onPressIn(event);
          }
        },
        onPressOut: function onPressOut(event) {
          _this2._opacityInactive(250);
          if (_this2.props.onPressOut != null) {
            _this2.props.onPressOut(event);
          }
        }
      };
    }
  }, {
    key: "_setOpacityTo",
    value: function _setOpacityTo(toValue, duration) {
      _Animated.default.timing(this.state.anim, {
        toValue: toValue,
        duration: duration,
        easing: _Easing.default.inOut(_Easing.default.quad),
        useNativeDriver: true
      }).start();
    }
  }, {
    key: "_opacityActive",
    value: function _opacityActive(duration) {
      var _this$props$activeOpa;
      this._setOpacityTo((_this$props$activeOpa = this.props.activeOpacity) != null ? _this$props$activeOpa : 0.2, duration);
    }
  }, {
    key: "_opacityInactive",
    value: function _opacityInactive(duration) {
      this._setOpacityTo(this._getChildStyleOpacityWithDefault(), duration);
    }
  }, {
    key: "_getChildStyleOpacityWithDefault",
    value: function _getChildStyleOpacityWithDefault() {
      var _flattenStyle;
      var opacity = (_flattenStyle = (0, _flattenStyle4.default)(this.props.style)) == null ? void 0 : _flattenStyle.opacity;
      return typeof opacity === 'number' ? opacity : 1;
    }
  }, {
    key: "render",
    value: function render() {
      var _this$props$ariaBusy, _this$props$accessibi2, _this$props$ariaChec, _this$props$accessibi3, _this$props$ariaDisa, _this$props$accessibi4, _this$props$ariaExpa, _this$props$accessibi5, _this$props$ariaSele, _this$props$accessibi6, _this$props$ariaValu, _this$props$accessibi7, _this$props$ariaValu2, _this$props$accessibi8, _this$props$ariaValu3, _this$props$accessibi9, _this$props$ariaValu4, _this$props$accessibi0, _this$props$ariaLive, _this$props$ariaLabe, _this$props$ariaModa, _this$props$ariaHidd, _this$props$id;
      var _this$state$pressabil = this.state.pressability.getEventHandlers(),
        onBlur = _this$state$pressabil.onBlur,
        onFocus = _this$state$pressabil.onFocus,
        eventHandlersWithoutBlurAndFocus = (0, _objectWithoutProperties2.default)(_this$state$pressabil, _excluded);
      var _accessibilityState = {
        busy: (_this$props$ariaBusy = this.props['aria-busy']) != null ? _this$props$ariaBusy : (_this$props$accessibi2 = this.props.accessibilityState) == null ? void 0 : _this$props$accessibi2.busy,
        checked: (_this$props$ariaChec = this.props['aria-checked']) != null ? _this$props$ariaChec : (_this$props$accessibi3 = this.props.accessibilityState) == null ? void 0 : _this$props$accessibi3.checked,
        disabled: (_this$props$ariaDisa = this.props['aria-disabled']) != null ? _this$props$ariaDisa : (_this$props$accessibi4 = this.props.accessibilityState) == null ? void 0 : _this$props$accessibi4.disabled,
        expanded: (_this$props$ariaExpa = this.props['aria-expanded']) != null ? _this$props$ariaExpa : (_this$props$accessibi5 = this.props.accessibilityState) == null ? void 0 : _this$props$accessibi5.expanded,
        selected: (_this$props$ariaSele = this.props['aria-selected']) != null ? _this$props$ariaSele : (_this$props$accessibi6 = this.props.accessibilityState) == null ? void 0 : _this$props$accessibi6.selected
      };
      _accessibilityState = this.props.disabled != null ? Object.assign({}, _accessibilityState, {
        disabled: this.props.disabled
      }) : _accessibilityState;
      var accessibilityValue = {
        max: (_this$props$ariaValu = this.props['aria-valuemax']) != null ? _this$props$ariaValu : (_this$props$accessibi7 = this.props.accessibilityValue) == null ? void 0 : _this$props$accessibi7.max,
        min: (_this$props$ariaValu2 = this.props['aria-valuemin']) != null ? _this$props$ariaValu2 : (_this$props$accessibi8 = this.props.accessibilityValue) == null ? void 0 : _this$props$accessibi8.min,
        now: (_this$props$ariaValu3 = this.props['aria-valuenow']) != null ? _this$props$ariaValu3 : (_this$props$accessibi9 = this.props.accessibilityValue) == null ? void 0 : _this$props$accessibi9.now,
        text: (_this$props$ariaValu4 = this.props['aria-valuetext']) != null ? _this$props$ariaValu4 : (_this$props$accessibi0 = this.props.accessibilityValue) == null ? void 0 : _this$props$accessibi0.text
      };
      var accessibilityLiveRegion = this.props['aria-live'] === 'off' ? 'none' : (_this$props$ariaLive = this.props['aria-live']) != null ? _this$props$ariaLive : this.props.accessibilityLiveRegion;
      var accessibilityLabel = (_this$props$ariaLabe = this.props['aria-label']) != null ? _this$props$ariaLabe : this.props.accessibilityLabel;
      return (0, _jsxRuntime.jsxs)(_Animated.default.View, Object.assign({
        accessible: this.props.accessible !== false,
        accessibilityLabel: accessibilityLabel,
        accessibilityHint: this.props.accessibilityHint,
        accessibilityLanguage: this.props.accessibilityLanguage,
        accessibilityRole: this.props.accessibilityRole,
        accessibilityState: _accessibilityState,
        accessibilityActions: this.props.accessibilityActions,
        onAccessibilityAction: this.props.onAccessibilityAction,
        accessibilityValue: accessibilityValue,
        importantForAccessibility: this.props['aria-hidden'] === true ? 'no-hide-descendants' : this.props.importantForAccessibility,
        accessibilityViewIsModal: (_this$props$ariaModa = this.props['aria-modal']) != null ? _this$props$ariaModa : this.props.accessibilityViewIsModal,
        accessibilityLiveRegion: accessibilityLiveRegion,
        accessibilityElementsHidden: (_this$props$ariaHidd = this.props['aria-hidden']) != null ? _this$props$ariaHidd : this.props.accessibilityElementsHidden,
        style: [this.props.style, {
          opacity: this.state.anim
        }],
        nativeID: (_this$props$id = this.props.id) != null ? _this$props$id : this.props.nativeID,
        testID: this.props.testID,
        onLayout: this.props.onLayout,
        nextFocusDown: this.props.nextFocusDown,
        nextFocusForward: this.props.nextFocusForward,
        nextFocusLeft: this.props.nextFocusLeft,
        nextFocusRight: this.props.nextFocusRight,
        nextFocusUp: this.props.nextFocusUp,
        hasTVPreferredFocus: this.props.hasTVPreferredFocus,
        hitSlop: this.props.hitSlop,
        focusable: this.props.focusable !== false && this.props.onPress !== undefined && !this.props.disabled,
        ref: this.props.hostRef
      }, eventHandlersWithoutBlurAndFocus, {
        children: [this.props.children, __DEV__ ? (0, _jsxRuntime.jsx)(_PressabilityDebug.PressabilityDebugView, {
          color: "cyan",
          hitSlop: this.props.hitSlop
        }) : null]
      }));
    }
  }, {
    key: "componentDidUpdate",
    value: function componentDidUpdate(prevProps, prevState) {
      var _flattenStyle2, _flattenStyle3;
      this.state.pressability.configure(this._createPressabilityConfig());
      if (this.props.disabled !== prevProps.disabled || ((_flattenStyle2 = (0, _flattenStyle4.default)(prevProps.style)) == null ? void 0 : _flattenStyle2.opacity) !== ((_flattenStyle3 = (0, _flattenStyle4.default)(this.props.style)) == null ? void 0 : _flattenStyle3.opacity)) {
        this._opacityInactive(250);
      }
    }
  }, {
    key: "componentDidMount",
    value: function componentDidMount() {
      this.state.pressability.configure(this._createPressabilityConfig());
    }
  }, {
    key: "componentWillUnmount",
    value: function componentWillUnmount() {
      this.state.pressability.reset();
      this.state.anim.resetAnimation();
    }
  }]);
}(React.Component);
var Touchable = React.forwardRef(function (props, ref) {
  return (0, _jsxRuntime.jsx)(TouchableOpacity, Object.assign({}, props, {
    hostRef: ref
  }));
});
Touchable.displayName = 'TouchableOpacity';
var _default = exports.default = Touchable;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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