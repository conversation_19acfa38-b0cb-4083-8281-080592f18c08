95aa8c77a54932a414f40ffdd25954c3
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _TextAncestor = _interopRequireDefault(require("../../Text/TextAncestor"));
var _ViewNativeComponent = _interopRequireDefault(require("./ViewNativeComponent"));
var React = _interopRequireWildcard(require("react"));
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["accessibilityElementsHidden", "accessibilityLabel", "accessibilityLabelledBy", "accessibilityLiveRegion", "accessibilityState", "accessibilityValue", "aria-busy", "aria-checked", "aria-disabled", "aria-expanded", "aria-hidden", "aria-label", "aria-labelledby", "aria-live", "aria-selected", "aria-valuemax", "aria-valuemin", "aria-valuenow", "aria-valuetext", "focusable", "id", "importantForAccessibility", "nativeID", "tabIndex"];
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var View = React.forwardRef(function (_ref, forwardedRef) {
  var _ariaLabelledBy$split;
  var accessibilityElementsHidden = _ref.accessibilityElementsHidden,
    accessibilityLabel = _ref.accessibilityLabel,
    accessibilityLabelledBy = _ref.accessibilityLabelledBy,
    accessibilityLiveRegion = _ref.accessibilityLiveRegion,
    accessibilityState = _ref.accessibilityState,
    accessibilityValue = _ref.accessibilityValue,
    ariaBusy = _ref['aria-busy'],
    ariaChecked = _ref['aria-checked'],
    ariaDisabled = _ref['aria-disabled'],
    ariaExpanded = _ref['aria-expanded'],
    ariaHidden = _ref['aria-hidden'],
    ariaLabel = _ref['aria-label'],
    ariaLabelledBy = _ref['aria-labelledby'],
    ariaLive = _ref['aria-live'],
    ariaSelected = _ref['aria-selected'],
    ariaValueMax = _ref['aria-valuemax'],
    ariaValueMin = _ref['aria-valuemin'],
    ariaValueNow = _ref['aria-valuenow'],
    ariaValueText = _ref['aria-valuetext'],
    focusable = _ref.focusable,
    id = _ref.id,
    importantForAccessibility = _ref.importantForAccessibility,
    nativeID = _ref.nativeID,
    tabIndex = _ref.tabIndex,
    otherProps = (0, _objectWithoutProperties2.default)(_ref, _excluded);
  var hasTextAncestor = React.useContext(_TextAncestor.default);
  var _accessibilityLabelledBy = (_ariaLabelledBy$split = ariaLabelledBy == null ? void 0 : ariaLabelledBy.split(/\s*,\s*/g)) != null ? _ariaLabelledBy$split : accessibilityLabelledBy;
  var _accessibilityState;
  if (accessibilityState != null || ariaBusy != null || ariaChecked != null || ariaDisabled != null || ariaExpanded != null || ariaSelected != null) {
    _accessibilityState = {
      busy: ariaBusy != null ? ariaBusy : accessibilityState == null ? void 0 : accessibilityState.busy,
      checked: ariaChecked != null ? ariaChecked : accessibilityState == null ? void 0 : accessibilityState.checked,
      disabled: ariaDisabled != null ? ariaDisabled : accessibilityState == null ? void 0 : accessibilityState.disabled,
      expanded: ariaExpanded != null ? ariaExpanded : accessibilityState == null ? void 0 : accessibilityState.expanded,
      selected: ariaSelected != null ? ariaSelected : accessibilityState == null ? void 0 : accessibilityState.selected
    };
  }
  var _accessibilityValue;
  if (accessibilityValue != null || ariaValueMax != null || ariaValueMin != null || ariaValueNow != null || ariaValueText != null) {
    _accessibilityValue = {
      max: ariaValueMax != null ? ariaValueMax : accessibilityValue == null ? void 0 : accessibilityValue.max,
      min: ariaValueMin != null ? ariaValueMin : accessibilityValue == null ? void 0 : accessibilityValue.min,
      now: ariaValueNow != null ? ariaValueNow : accessibilityValue == null ? void 0 : accessibilityValue.now,
      text: ariaValueText != null ? ariaValueText : accessibilityValue == null ? void 0 : accessibilityValue.text
    };
  }
  var actualView = (0, _jsxRuntime.jsx)(_ViewNativeComponent.default, Object.assign({}, otherProps, {
    accessibilityLiveRegion: ariaLive === 'off' ? 'none' : ariaLive != null ? ariaLive : accessibilityLiveRegion,
    accessibilityLabel: ariaLabel != null ? ariaLabel : accessibilityLabel,
    focusable: tabIndex !== undefined ? !tabIndex : focusable,
    accessibilityState: _accessibilityState,
    accessibilityElementsHidden: ariaHidden != null ? ariaHidden : accessibilityElementsHidden,
    accessibilityLabelledBy: _accessibilityLabelledBy,
    accessibilityValue: _accessibilityValue,
    importantForAccessibility: ariaHidden === true ? 'no-hide-descendants' : importantForAccessibility,
    nativeID: id != null ? id : nativeID,
    ref: forwardedRef
  }));
  if (hasTextAncestor) {
    return (0, _jsxRuntime.jsx)(_TextAncestor.default.Provider, {
      value: false,
      children: actualView
    });
  }
  return actualView;
});
View.displayName = 'View';
var _default = exports.default = View;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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