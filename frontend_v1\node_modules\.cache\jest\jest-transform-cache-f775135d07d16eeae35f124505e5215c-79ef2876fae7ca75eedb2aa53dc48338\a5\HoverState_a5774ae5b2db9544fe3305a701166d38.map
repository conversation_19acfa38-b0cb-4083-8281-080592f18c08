{"version": 3, "names": ["_Platform", "_interopRequireDefault", "require", "isEnabled", "Platform", "OS", "canUseDOM", "Boolean", "window", "document", "createElement", "HOVER_THRESHOLD_MS", "lastTouchTimestamp", "enableHover", "Date", "now", "disableHover", "addEventListener", "isHoverEnabled"], "sources": ["HoverState.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\nimport Platform from '../Utilities/Platform';\n\nlet isEnabled = false;\n\n/* $FlowFixMe[incompatible-type] Error found due to incomplete typing of\n * Platform.flow.js */\nif (Platform.OS === 'web') {\n  const canUseDOM = Boolean(\n    typeof window !== 'undefined' &&\n      window.document &&\n      // $FlowFixMe[method-unbinding]\n      window.document.createElement,\n  );\n\n  if (canUseDOM) {\n    /**\n     * Web browsers emulate mouse events (and hover states) after touch events.\n     * This code infers when the currently-in-use modality supports hover\n     * (including for multi-modality devices) and considers \"hover\" to be enabled\n     * if a mouse movement occurs more than 1 second after the last touch event.\n     * This threshold is long enough to account for longer delays between the\n     * browser firing touch and mouse events on low-powered devices.\n     */\n    const HOVER_THRESHOLD_MS = 1000;\n    let lastTouchTimestamp = 0;\n\n    const enableHover = () => {\n      if (isEnabled || Date.now() - lastTouchTimestamp < HOVER_THRESHOLD_MS) {\n        return;\n      }\n      isEnabled = true;\n    };\n\n    const disableHover = () => {\n      lastTouchTimestamp = Date.now();\n      if (isEnabled) {\n        isEnabled = false;\n      }\n    };\n\n    document.addEventListener('touchstart', disableHover, true);\n    document.addEventListener('touchmove', disableHover, true);\n    document.addEventListener('mousemove', enableHover, true);\n  }\n}\n\nexport function isHoverEnabled(): boolean {\n  return isEnabled;\n}\n"], "mappings": ";;;;;AAUA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAIC,SAAS,GAAG,KAAK;AAIrB,IAAIC,iBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;EACzB,IAAMC,SAAS,GAAGC,OAAO,CACvB,OAAOC,MAAM,KAAK,WAAW,IAC3BA,MAAM,CAACC,QAAQ,IAEfD,MAAM,CAACC,QAAQ,CAACC,aACpB,CAAC;EAED,IAAIJ,SAAS,EAAE;IASb,IAAMK,kBAAkB,GAAG,IAAI;IAC/B,IAAIC,kBAAkB,GAAG,CAAC;IAE1B,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB,IAAIV,SAAS,IAAIW,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGH,kBAAkB,GAAGD,kBAAkB,EAAE;QACrE;MACF;MACAR,SAAS,GAAG,IAAI;IAClB,CAAC;IAED,IAAMa,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzBJ,kBAAkB,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC;MAC/B,IAAIZ,SAAS,EAAE;QACbA,SAAS,GAAG,KAAK;MACnB;IACF,CAAC;IAEDM,QAAQ,CAACQ,gBAAgB,CAAC,YAAY,EAAED,YAAY,EAAE,IAAI,CAAC;IAC3DP,QAAQ,CAACQ,gBAAgB,CAAC,WAAW,EAAED,YAAY,EAAE,IAAI,CAAC;IAC1DP,QAAQ,CAACQ,gBAAgB,CAAC,WAAW,EAAEJ,WAAW,EAAE,IAAI,CAAC;EAC3D;AACF;AAEO,SAASK,cAAcA,CAAA,EAAY;EACxC,OAAOf,SAAS;AAClB", "ignoreList": []}