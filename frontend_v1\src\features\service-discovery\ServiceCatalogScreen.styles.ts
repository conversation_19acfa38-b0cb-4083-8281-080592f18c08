/**
 * Service Catalog Screen Styles - Responsive Design System
 *
 * Style Contract:
 * - Implements responsive design principles
 * - Follows Vierla design system
 * - Supports theme-based styling
 * - Ensures accessibility compliance
 * - Optimized for both iOS and Android
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { StyleSheet } from 'react-native';

import { Colors } from '../../constants/Colors';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../../utils/responsiveUtils';

export const createStyles = (colors: typeof Colors.light) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background?.primary || '#FFFFFF',
    },

    scrollView: {
      flex: 1,
    },

    header: {
      paddingHorizontal: getResponsiveSpacing(20),
      paddingVertical: getResponsiveSpacing(24),
      backgroundColor: colors.surface,
    },

    title: {
      fontSize: getResponsiveFontSize(28),
      fontWeight: '700',
      color: colors.text,
      marginBottom: getResponsiveSpacing(4),
    },

    subtitle: {
      fontSize: getResponsiveFontSize(16),
      color: colors.textSecondary,
      lineHeight: getResponsiveFontSize(22),
    },

    searchContainer: {
      flexDirection: 'row',
      paddingHorizontal: getResponsiveSpacing(20),
      paddingVertical: getResponsiveSpacing(16),
      gap: getResponsiveSpacing(12),
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },

    searchInput: {
      flex: 1,
    },

    searchButton: {
      paddingHorizontal: getResponsiveSpacing(20),
    },

    quickFilters: {
      paddingVertical: getResponsiveSpacing(16),
      paddingLeft: getResponsiveSpacing(20),
      backgroundColor: colors.surface,
    },

    filterChip: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: getResponsiveSpacing(16),
      paddingVertical: getResponsiveSpacing(8),
      backgroundColor: colors.surfaceVariant,
      borderRadius: getResponsiveSpacing(20),
      marginRight: getResponsiveSpacing(12),
      gap: getResponsiveSpacing(6),
    },

    filterChipText: {
      fontSize: getResponsiveFontSize(14),
      fontWeight: '500',
      color: colors.primary,
    },

    section: {
      paddingHorizontal: getResponsiveSpacing(20),
      paddingVertical: getResponsiveSpacing(24),
    },

    sectionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: getResponsiveSpacing(16),
    },

    sectionTitle: {
      fontSize: getResponsiveFontSize(22),
      fontWeight: '600',
      color: colors.text,
    },

    seeAllText: {
      fontSize: getResponsiveFontSize(16),
      fontWeight: '500',
      color: colors.primary,
    },

    categoryRow: {
      justifyContent: 'space-between',
      marginBottom: getResponsiveSpacing(16),
    },

    categoryCard: {
      flex: 1,
      backgroundColor: colors.surface,
      borderRadius: getResponsiveSpacing(12),
      padding: getResponsiveSpacing(16),
      marginHorizontal: getResponsiveSpacing(4),
      borderWidth: 1,
      borderColor: colors.border,
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },

    categoryIconContainer: {
      width: getResponsiveSpacing(48),
      height: getResponsiveSpacing(48),
      borderRadius: getResponsiveSpacing(24),
      backgroundColor: colors.primaryContainer,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: getResponsiveSpacing(12),
    },

    categoryInfo: {
      flex: 1,
    },

    categoryName: {
      fontSize: getResponsiveFontSize(16),
      fontWeight: '600',
      color: colors.text,
      marginBottom: getResponsiveSpacing(4),
    },

    categoryDescription: {
      fontSize: getResponsiveFontSize(14),
      color: colors.textSecondary,
      lineHeight: getResponsiveFontSize(18),
      marginBottom: getResponsiveSpacing(8),
    },

    categoryStats: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: getResponsiveSpacing(12),
    },

    categoryCount: {
      fontSize: getResponsiveFontSize(12),
      color: colors.textSecondary,
      fontWeight: '500',
    },

    categoryPrice: {
      fontSize: getResponsiveFontSize(12),
      color: colors.primary,
      fontWeight: '600',
    },

    popularServices: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: getResponsiveSpacing(4),
    },

    serviceTag: {
      backgroundColor: colors.primaryContainer,
      paddingHorizontal: getResponsiveSpacing(8),
      paddingVertical: getResponsiveSpacing(2),
      borderRadius: getResponsiveSpacing(10),
    },

    serviceTagText: {
      fontSize: getResponsiveFontSize(10),
      color: colors.primary,
      fontWeight: '500',
    },

    providersContainer: {
      paddingRight: getResponsiveSpacing(20),
    },

    providerCard: {
      width: getResponsiveSpacing(280),
      backgroundColor: colors.surface,
      borderRadius: getResponsiveSpacing(12),
      padding: getResponsiveSpacing(16),
      marginRight: getResponsiveSpacing(16),
      borderWidth: 1,
      borderColor: colors.border,
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },

    providerImageContainer: {
      position: 'relative',
      marginBottom: getResponsiveSpacing(12),
    },

    providerImagePlaceholder: {
      width: '100%',
      height: getResponsiveSpacing(120),
      backgroundColor: colors.surfaceVariant,
      borderRadius: getResponsiveSpacing(8),
      justifyContent: 'center',
      alignItems: 'center',
    },

    verifiedBadge: {
      position: 'absolute',
      top: getResponsiveSpacing(8),
      right: getResponsiveSpacing(8),
      backgroundColor: colors.surface,
      borderRadius: getResponsiveSpacing(12),
      padding: getResponsiveSpacing(4),
    },

    providerInfo: {
      flex: 1,
    },

    providerName: {
      fontSize: getResponsiveFontSize(18),
      fontWeight: '600',
      color: colors.text,
      marginBottom: getResponsiveSpacing(4),
    },

    providerCategory: {
      fontSize: getResponsiveFontSize(14),
      color: colors.textSecondary,
      marginBottom: getResponsiveSpacing(8),
    },

    providerStats: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: getResponsiveSpacing(8),
    },

    ratingContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: getResponsiveSpacing(4),
    },

    rating: {
      fontSize: getResponsiveFontSize(14),
      fontWeight: '600',
      color: colors.text,
    },

    reviewCount: {
      fontSize: getResponsiveFontSize(14),
      color: colors.textSecondary,
    },

    distance: {
      fontSize: getResponsiveFontSize(14),
      color: colors.textSecondary,
    },

    availabilityContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: getResponsiveSpacing(8),
      gap: getResponsiveSpacing(6),
    },

    statusIndicator: {
      width: getResponsiveSpacing(8),
      height: getResponsiveSpacing(8),
      borderRadius: getResponsiveSpacing(4),
    },

    availability: {
      fontSize: getResponsiveFontSize(14),
      color: colors.textSecondary,
    },

    offerContainer: {
      backgroundColor: colors.successContainer,
      paddingHorizontal: getResponsiveSpacing(8),
      paddingVertical: getResponsiveSpacing(4),
      borderRadius: getResponsiveSpacing(6),
      alignSelf: 'flex-start',
    },

    offerText: {
      fontSize: getResponsiveFontSize(12),
      color: colors.success,
      fontWeight: '500',
    },
  });
