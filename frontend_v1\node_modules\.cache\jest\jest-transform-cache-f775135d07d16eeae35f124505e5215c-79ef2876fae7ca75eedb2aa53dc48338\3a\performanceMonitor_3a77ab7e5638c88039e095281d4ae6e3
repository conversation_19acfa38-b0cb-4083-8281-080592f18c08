29b6fc64ccff9ddb138a6d840134c212
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.performanceMonitor = exports.default = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var PerformanceMonitorService = function () {
  function PerformanceMonitorService() {
    (0, _classCallCheck2.default)(this, PerformanceMonitorService);
    this.metrics = [];
    this.renderMetrics = new Map();
    this.networkMetrics = [];
    this.memoryMetrics = [];
    this.frameDropCount = 0;
    this.isMonitoring = false;
    this.slowRenderCallbacks = [];
    this.slowNetworkCallbacks = [];
    this.MAX_METRICS = 1000;
    this.SLOW_RENDER_THRESHOLD = 16;
    this.SLOW_NETWORK_THRESHOLD = 2000;
    this.MEMORY_LEAK_THRESHOLD = 50 * 1024 * 1024;
  }
  return (0, _createClass2.default)(PerformanceMonitorService, [{
    key: "startMonitoring",
    value: function startMonitoring() {
      var _this = this;
      if (this.isMonitoring) return;
      this.isMonitoring = true;
      console.log('📊 Performance Monitor: Started');
      this.monitoringInterval = setInterval(function () {
        _this.collectMemoryMetrics();
        _this.detectMemoryLeaks();
        _this.cleanupOldMetrics();
      }, 5000);
      this.startFrameMonitoring();
    }
  }, {
    key: "stopMonitoring",
    value: function stopMonitoring() {
      if (!this.isMonitoring) return;
      this.isMonitoring = false;
      console.log('📊 Performance Monitor: Stopped');
      if (this.monitoringInterval) {
        clearInterval(this.monitoringInterval);
      }
    }
  }, {
    key: "trackRender",
    value: function trackRender(componentName, renderTime, metadata) {
      var existing = this.renderMetrics.get(componentName);
      if (existing) {
        existing.renderTime = (existing.renderTime + renderTime) / 2;
        existing.reRenders++;
        existing.lastRenderTime = Date.now();
        if (metadata != null && metadata.propsCount) existing.propsCount = metadata.propsCount;
        if (metadata != null && metadata.stateUpdates) existing.stateUpdates += metadata.stateUpdates;
      } else {
        this.renderMetrics.set(componentName, {
          componentName: componentName,
          renderTime: renderTime,
          propsCount: (metadata == null ? void 0 : metadata.propsCount) || 0,
          stateUpdates: (metadata == null ? void 0 : metadata.stateUpdates) || 0,
          reRenders: 1,
          lastRenderTime: Date.now()
        });
      }
      this.addMetric({
        name: 'component_render',
        value: renderTime,
        timestamp: Date.now(),
        category: 'render',
        metadata: Object.assign({
          componentName: componentName
        }, metadata)
      });
      if (renderTime > this.SLOW_RENDER_THRESHOLD) {
        console.warn(`🐌 Slow render detected: ${componentName} took ${renderTime}ms`);
        this.slowRenderCallbacks.forEach(function (callback) {
          try {
            callback(renderTime, componentName);
          } catch (error) {
            console.error('Error in slow render callback:', error);
          }
        });
      }
    }
  }, {
    key: "trackNetworkRequest",
    value: function trackNetworkRequest(url, method, responseTime, statusCode) {
      var requestSize = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 0;
      var responseSize = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 0;
      var cached = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : false;
      var metric = {
        url: url,
        method: method,
        responseTime: responseTime,
        statusCode: statusCode,
        requestSize: requestSize,
        responseSize: responseSize,
        cached: cached,
        timestamp: Date.now()
      };
      this.networkMetrics.push(metric);
      this.addMetric({
        name: 'network_request',
        value: responseTime,
        timestamp: Date.now(),
        category: 'network',
        metadata: {
          url: url,
          method: method,
          statusCode: statusCode,
          cached: cached
        }
      });
      if (responseTime > this.SLOW_NETWORK_THRESHOLD) {
        console.warn(`🐌 Slow network request: ${method} ${url} took ${responseTime}ms`);
        this.slowNetworkCallbacks.forEach(function (callback) {
          try {
            callback(responseTime, url);
          } catch (error) {
            console.error('Error in slow network callback:', error);
          }
        });
      }
      if (this.networkMetrics.length > this.MAX_METRICS) {
        this.networkMetrics = this.networkMetrics.slice(-this.MAX_METRICS / 2);
      }
    }
  }, {
    key: "trackUserInteraction",
    value: function trackUserInteraction(interactionType, responseTime, metadata) {
      this.addMetric({
        name: 'user_interaction',
        value: responseTime,
        timestamp: Date.now(),
        category: 'user_interaction',
        metadata: Object.assign({
          interactionType: interactionType
        }, metadata)
      });
      if (responseTime > 100) {
        console.warn(`🐌 Slow interaction: ${interactionType} took ${responseTime}ms`);
      }
    }
  }, {
    key: "trackNavigation",
    value: function trackNavigation(fromScreen, toScreen, navigationTime, metadata) {
      this.addMetric({
        name: 'navigation',
        value: navigationTime,
        timestamp: Date.now(),
        category: 'navigation',
        metadata: Object.assign({
          fromScreen: fromScreen,
          toScreen: toScreen
        }, metadata)
      });
    }
  }, {
    key: "trackError",
    value: function trackError(error, metadata) {
      this.addMetric({
        name: 'error',
        value: 1,
        timestamp: Date.now(),
        category: 'error',
        metadata: Object.assign({
          errorMessage: error.message,
          errorName: error.name,
          errorStack: error.stack
        }, metadata)
      });
      console.warn('[PerformanceMonitor] Error breadcrumb:', {
        message: `Performance Monitor: Error tracked - ${error.message}`,
        category: 'performance',
        level: 'error',
        data: Object.assign({
          errorName: error.name
        }, metadata)
      });
      console.error('[PerformanceMonitor] Error captured:', {
        error: error.message,
        stack: error.stack,
        tags: {
          source: 'performance_monitor',
          errorType: error.name
        },
        extra: {
          performanceMetadata: metadata,
          errorStack: error.stack,
          timestamp: Date.now()
        },
        level: 'error'
      });
      console.warn('📊 Performance Monitor: Error tracked', {
        error: error.message,
        metadata: metadata
      });
    }
  }, {
    key: "getPerformanceReport",
    value: function getPerformanceReport() {
      var _this2 = this;
      var now = Date.now();
      var recentMetrics = this.metrics.filter(function (m) {
        return now - m.timestamp < 300000;
      });
      var renderMetrics = recentMetrics.filter(function (m) {
        return m.category === 'render';
      });
      var networkMetrics = recentMetrics.filter(function (m) {
        return m.category === 'network';
      });
      var averageRenderTime = renderMetrics.length > 0 ? renderMetrics.reduce(function (sum, m) {
        return sum + m.value;
      }, 0) / renderMetrics.length : 0;
      var averageNetworkTime = networkMetrics.length > 0 ? networkMetrics.reduce(function (sum, m) {
        return sum + m.value;
      }, 0) / networkMetrics.length : 0;
      var slowComponents = Array.from(this.renderMetrics.values()).filter(function (c) {
        return c.renderTime > _this2.SLOW_RENDER_THRESHOLD;
      }).sort(function (a, b) {
        return b.renderTime - a.renderTime;
      }).slice(0, 10);
      var slowNetworkRequests = this.networkMetrics.filter(function (n) {
        return n.responseTime > _this2.SLOW_NETWORK_THRESHOLD;
      }).sort(function (a, b) {
        return b.responseTime - a.responseTime;
      }).slice(0, 10);
      var cachedRequests = this.networkMetrics.filter(function (n) {
        return n.cached;
      }).length;
      var totalRequests = this.networkMetrics.length;
      var cacheHitRate = totalRequests > 0 ? cachedRequests / totalRequests : 0;
      var latestMemory = this.memoryMetrics[this.memoryMetrics.length - 1];
      var memoryUsage = latestMemory ? latestMemory.usedJSHeapSize : 0;
      var recommendations = this.generateRecommendations({
        averageRenderTime: averageRenderTime,
        averageNetworkTime: averageNetworkTime,
        slowComponents: slowComponents,
        slowNetworkRequests: slowNetworkRequests,
        cacheHitRate: cacheHitRate,
        memoryUsage: memoryUsage
      });
      return {
        summary: {
          averageRenderTime: averageRenderTime,
          averageNetworkTime: averageNetworkTime,
          memoryUsage: memoryUsage,
          frameDrops: this.frameDropCount,
          cacheHitRate: cacheHitRate
        },
        slowComponents: slowComponents,
        slowNetworkRequests: slowNetworkRequests,
        memoryLeaks: this.detectMemoryLeaks(),
        recommendations: recommendations
      };
    }
  }, {
    key: "getMetricsByCategory",
    value: function getMetricsByCategory(category) {
      return this.metrics.filter(function (m) {
        return m.category === category;
      });
    }
  }, {
    key: "clearMetrics",
    value: function clearMetrics() {
      this.metrics = [];
      this.renderMetrics.clear();
      this.networkMetrics = [];
      this.memoryMetrics = [];
      this.frameDropCount = 0;
    }
  }, {
    key: "onSlowRender",
    value: function onSlowRender(callback) {
      this.slowRenderCallbacks.push(callback);
    }
  }, {
    key: "onSlowNetwork",
    value: function onSlowNetwork(callback) {
      this.slowNetworkCallbacks.push(callback);
    }
  }, {
    key: "offSlowRender",
    value: function offSlowRender(callback) {
      var index = this.slowRenderCallbacks.indexOf(callback);
      if (index > -1) {
        this.slowRenderCallbacks.splice(index, 1);
      }
    }
  }, {
    key: "offSlowNetwork",
    value: function offSlowNetwork(callback) {
      var index = this.slowNetworkCallbacks.indexOf(callback);
      if (index > -1) {
        this.slowNetworkCallbacks.splice(index, 1);
      }
    }
  }, {
    key: "addMetric",
    value: function addMetric(metric) {
      this.metrics.push(metric);
      if (this.metrics.length > this.MAX_METRICS) {
        this.metrics = this.metrics.slice(-this.MAX_METRICS / 2);
      }
    }
  }, {
    key: "collectMemoryMetrics",
    value: function collectMemoryMetrics() {
      if (typeof performance !== 'undefined' && performance.memory) {
        var memory = performance.memory;
        this.memoryMetrics.push({
          usedJSHeapSize: memory.usedJSHeapSize,
          totalJSHeapSize: memory.totalJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit,
          timestamp: Date.now()
        });
        if (this.memoryMetrics.length > 100) {
          this.memoryMetrics = this.memoryMetrics.slice(-50);
        }
      }
    }
  }, {
    key: "detectMemoryLeaks",
    value: function detectMemoryLeaks() {
      var leaks = [];
      if (this.memoryMetrics.length < 10) return leaks;
      var recent = this.memoryMetrics.slice(-10);
      var growth = recent[recent.length - 1].usedJSHeapSize - recent[0].usedJSHeapSize;
      if (growth > this.MEMORY_LEAK_THRESHOLD) {
        leaks.push(`Memory usage increased by ${Math.round(growth / 1024 / 1024)}MB in recent measurements`);
      }
      for (var _ref of this.renderMetrics.entries()) {
        var _ref2 = (0, _slicedToArray2.default)(_ref, 2);
        var name = _ref2[0];
        var metrics = _ref2[1];
        if (metrics.reRenders > 100) {
          leaks.push(`Component ${name} has ${metrics.reRenders} re-renders`);
        }
      }
      return leaks;
    }
  }, {
    key: "startFrameMonitoring",
    value: function startFrameMonitoring() {
      if (__DEV__) {
        console.log('📊 Frame monitoring started (placeholder)');
      }
    }
  }, {
    key: "cleanupOldMetrics",
    value: function cleanupOldMetrics() {
      var cutoff = Date.now() - 600000;
      this.metrics = this.metrics.filter(function (m) {
        return m.timestamp > cutoff;
      });
    }
  }, {
    key: "generateRecommendations",
    value: function generateRecommendations(data) {
      var recommendations = [];
      if (data.averageRenderTime > this.SLOW_RENDER_THRESHOLD) {
        recommendations.push('Consider optimizing component renders with React.memo or useMemo');
      }
      if (data.slowComponents.length > 0) {
        recommendations.push(`Optimize slow components: ${data.slowComponents.slice(0, 3).map(function (c) {
          return c.componentName;
        }).join(', ')}`);
      }
      if (data.averageNetworkTime > 1000) {
        recommendations.push('Consider implementing request caching or optimizing API endpoints');
      }
      if (data.cacheHitRate < 0.5) {
        recommendations.push('Improve cache hit rate by implementing better caching strategies');
      }
      if (data.memoryUsage > 100 * 1024 * 1024) {
        recommendations.push('High memory usage detected - check for memory leaks');
      }
      if (data.slowNetworkRequests.length > 5) {
        recommendations.push('Multiple slow network requests detected - consider request batching');
      }
      return recommendations;
    }
  }]);
}();
var performanceMonitor = exports.performanceMonitor = new PerformanceMonitorService();
var _default = exports.default = performanceMonitor;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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