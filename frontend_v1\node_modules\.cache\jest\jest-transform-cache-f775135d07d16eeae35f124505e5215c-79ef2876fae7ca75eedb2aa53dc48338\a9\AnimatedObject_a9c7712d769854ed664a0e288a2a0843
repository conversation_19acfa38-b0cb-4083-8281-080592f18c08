496f4c483648916836e1ac12ea4ad25a
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
exports.isPlainObject = isPlainObject;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _classPrivateFieldLooseBase2 = _interopRequireDefault(require("@babel/runtime/helpers/classPrivateFieldLooseBase"));
var _classPrivateFieldLooseKey2 = _interopRequireDefault(require("@babel/runtime/helpers/classPrivateFieldLooseKey"));
var _AnimatedNode = _interopRequireDefault(require("./AnimatedNode"));
var _AnimatedWithChildren2 = _interopRequireDefault(require("./AnimatedWithChildren"));
var React = _interopRequireWildcard(require("react"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var MAX_DEPTH = 5;
function isPlainObject(value) {
  return value !== null && typeof value === 'object' && Object.getPrototypeOf(value).isPrototypeOf(Object) && !React.isValidElement(value);
}
function flatAnimatedNodes(value) {
  var nodes = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];
  var depth = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;
  if (depth >= MAX_DEPTH) {
    return nodes;
  }
  if (value instanceof _AnimatedNode.default) {
    nodes.push(value);
  } else if (Array.isArray(value)) {
    for (var ii = 0, length = value.length; ii < length; ii++) {
      var element = value[ii];
      flatAnimatedNodes(element, nodes, depth + 1);
    }
  } else if (isPlainObject(value)) {
    var keys = Object.keys(value);
    for (var _ii = 0, _length = keys.length; _ii < _length; _ii++) {
      var key = keys[_ii];
      flatAnimatedNodes(value[key], nodes, depth + 1);
    }
  }
  return nodes;
}
function mapAnimatedNodes(value, fn) {
  var depth = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;
  if (depth >= MAX_DEPTH) {
    return value;
  }
  if (value instanceof _AnimatedNode.default) {
    return fn(value);
  } else if (Array.isArray(value)) {
    return value.map(function (element) {
      return mapAnimatedNodes(element, fn, depth + 1);
    });
  } else if (isPlainObject(value)) {
    var result = {};
    var keys = Object.keys(value);
    for (var ii = 0, length = keys.length; ii < length; ii++) {
      var key = keys[ii];
      result[key] = mapAnimatedNodes(value[key], fn, depth + 1);
    }
    return result;
  } else {
    return value;
  }
}
var _nodes = (0, _classPrivateFieldLooseKey2.default)("nodes");
var AnimatedObject = exports.default = function (_AnimatedWithChildren) {
  function AnimatedObject(nodes, value, config) {
    var _this;
    (0, _classCallCheck2.default)(this, AnimatedObject);
    _this = _callSuper(this, AnimatedObject, [config]);
    Object.defineProperty(_this, _nodes, {
      writable: true,
      value: void 0
    });
    (0, _classPrivateFieldLooseBase2.default)(_this, _nodes)[_nodes] = nodes;
    _this._value = value;
    return _this;
  }
  (0, _inherits2.default)(AnimatedObject, _AnimatedWithChildren);
  return (0, _createClass2.default)(AnimatedObject, [{
    key: "__getValue",
    value: function __getValue() {
      return mapAnimatedNodes(this._value, function (node) {
        return node.__getValue();
      });
    }
  }, {
    key: "__getValueWithStaticObject",
    value: function __getValueWithStaticObject(staticObject) {
      var nodes = (0, _classPrivateFieldLooseBase2.default)(this, _nodes)[_nodes];
      var index = 0;
      return mapAnimatedNodes(staticObject, function () {
        return nodes[index++].__getValue();
      });
    }
  }, {
    key: "__getAnimatedValue",
    value: function __getAnimatedValue() {
      return mapAnimatedNodes(this._value, function (node) {
        return node.__getAnimatedValue();
      });
    }
  }, {
    key: "__attach",
    value: function __attach() {
      var nodes = (0, _classPrivateFieldLooseBase2.default)(this, _nodes)[_nodes];
      for (var ii = 0, length = nodes.length; ii < length; ii++) {
        var node = nodes[ii];
        node.__addChild(this);
      }
      _superPropGet(AnimatedObject, "__attach", this, 3)([]);
    }
  }, {
    key: "__detach",
    value: function __detach() {
      var nodes = (0, _classPrivateFieldLooseBase2.default)(this, _nodes)[_nodes];
      for (var ii = 0, length = nodes.length; ii < length; ii++) {
        var node = nodes[ii];
        node.__removeChild(this);
      }
      _superPropGet(AnimatedObject, "__detach", this, 3)([]);
    }
  }, {
    key: "__makeNative",
    value: function __makeNative(platformConfig) {
      var nodes = (0, _classPrivateFieldLooseBase2.default)(this, _nodes)[_nodes];
      for (var ii = 0, length = nodes.length; ii < length; ii++) {
        var node = nodes[ii];
        node.__makeNative(platformConfig);
      }
      _superPropGet(AnimatedObject, "__makeNative", this, 3)([platformConfig]);
    }
  }, {
    key: "__getNativeConfig",
    value: function __getNativeConfig() {
      return {
        type: 'object',
        value: mapAnimatedNodes(this._value, function (node) {
          return {
            nodeTag: node.__getNativeTag()
          };
        }),
        debugID: this.__getDebugID()
      };
    }
  }], [{
    key: "from",
    value: function from(value) {
      var nodes = flatAnimatedNodes(value);
      if (nodes.length === 0) {
        return null;
      }
      return new AnimatedObject(nodes, value);
    }
  }]);
}(_AnimatedWithChildren2.default);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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