{"version": 3, "names": ["_authService", "require", "BACKEND_AVAILABLE", "process", "env", "TEST_BACKEND", "describe", "beforeAll", "it", "skip", "_asyncToGenerator2", "default", "result", "authService", "login", "email", "password", "expect", "toBeDefined", "access", "user", "toBe", "role", "error", "console", "rejects", "toThrow", "register", "first_name", "last_name"], "sources": ["authService.integration.test.ts"], "sourcesContent": ["/**\n * Authentication Service Integration Tests\n * Tests actual API integration with backend\n */\n\nimport { authService } from '../authService';\n\n// Only run integration tests if backend is available\nconst BACKEND_AVAILABLE = process.env.TEST_BACKEND === 'true';\n\ndescribe('AuthService Integration Tests', () => {\n  // Skip integration tests if backend is not available\n  beforeAll(() => {\n    if (!BACKEND_AVAILABLE) {\n      // Skipping integration tests - set TEST_BACKEND=true to run\n    }\n  });\n\n  describe('login integration', () => {\n    it.skip('should successfully login with test credentials', async () => {\n      if (!BACKEND_AVAILABLE) return;\n\n      try {\n        const result = await authService.login({\n          email: '<EMAIL>',\n          password: 'Testpass123!',\n        });\n\n        expect(result).toBeDefined();\n        expect(result.access).toBeDefined();\n        expect(result.user).toBeDefined();\n        expect(result.user.email).toBe('<EMAIL>');\n        expect(result.user.role).toBe('customer');\n      } catch (error) {\n        console.error('Integration test failed:', error);\n        throw error;\n      }\n    });\n\n    it.skip('should fail with invalid credentials', async () => {\n      if (!BACKEND_AVAILABLE) return;\n\n      await expect(\n        authService.login({\n          email: '<EMAIL>',\n          password: 'wrongpassword',\n        }),\n      ).rejects.toThrow();\n    });\n  });\n\n  describe('register integration', () => {\n    it.skip('should handle registration attempt', async () => {\n      if (!BACKEND_AVAILABLE) return;\n\n      // This test might fail if user already exists, which is expected\n      try {\n        const result = await authService.register({\n          first_name: 'Test',\n          last_name: 'User',\n          email: '<EMAIL>',\n          password: 'Testpass123!',\n          role: 'customer',\n        });\n\n        expect(result).toBeDefined();\n        expect(result.user).toBeDefined();\n      } catch (error) {\n        // Expected if user already exists\n        // Registration test result available in error variable\n      }\n    });\n  });\n});\n"], "mappings": ";;AAKA,IAAAA,YAAA,GAAAC,OAAA;AAGA,IAAMC,iBAAiB,GAAGC,OAAO,CAACC,GAAG,CAACC,YAAY,KAAK,MAAM;AAE7DC,QAAQ,CAAC,+BAA+B,EAAE,YAAM;EAE9CC,SAAS,CAAC,YAAM;IACd,IAAI,CAACL,iBAAiB,EAAE,CAExB;EACF,CAAC,CAAC;EAEFI,QAAQ,CAAC,mBAAmB,EAAE,YAAM;IAClCE,EAAE,CAACC,IAAI,CAAC,iDAAiD,MAAAC,kBAAA,CAAAC,OAAA,EAAE,aAAY;MACrE,IAAI,CAACT,iBAAiB,EAAE;MAExB,IAAI;QACF,IAAMU,MAAM,SAASC,wBAAW,CAACC,KAAK,CAAC;UACrCC,KAAK,EAAE,mBAAmB;UAC1BC,QAAQ,EAAE;QACZ,CAAC,CAAC;QAEFC,MAAM,CAACL,MAAM,CAAC,CAACM,WAAW,CAAC,CAAC;QAC5BD,MAAM,CAACL,MAAM,CAACO,MAAM,CAAC,CAACD,WAAW,CAAC,CAAC;QACnCD,MAAM,CAACL,MAAM,CAACQ,IAAI,CAAC,CAACF,WAAW,CAAC,CAAC;QACjCD,MAAM,CAACL,MAAM,CAACQ,IAAI,CAACL,KAAK,CAAC,CAACM,IAAI,CAAC,mBAAmB,CAAC;QACnDJ,MAAM,CAACL,MAAM,CAACQ,IAAI,CAACE,IAAI,CAAC,CAACD,IAAI,CAAC,UAAU,CAAC;MAC3C,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,MAAMA,KAAK;MACb;IACF,CAAC,EAAC;IAEFf,EAAE,CAACC,IAAI,CAAC,sCAAsC,MAAAC,kBAAA,CAAAC,OAAA,EAAE,aAAY;MAC1D,IAAI,CAACT,iBAAiB,EAAE;MAExB,MAAMe,MAAM,CACVJ,wBAAW,CAACC,KAAK,CAAC;QAChBC,KAAK,EAAE,mBAAmB;QAC1BC,QAAQ,EAAE;MACZ,CAAC,CACH,CAAC,CAACS,OAAO,CAACC,OAAO,CAAC,CAAC;IACrB,CAAC,EAAC;EACJ,CAAC,CAAC;EAEFpB,QAAQ,CAAC,sBAAsB,EAAE,YAAM;IACrCE,EAAE,CAACC,IAAI,CAAC,oCAAoC,MAAAC,kBAAA,CAAAC,OAAA,EAAE,aAAY;MACxD,IAAI,CAACT,iBAAiB,EAAE;MAGxB,IAAI;QACF,IAAMU,MAAM,SAASC,wBAAW,CAACc,QAAQ,CAAC;UACxCC,UAAU,EAAE,MAAM;UAClBC,SAAS,EAAE,MAAM;UACjBd,KAAK,EAAE,sBAAsB;UAC7BC,QAAQ,EAAE,cAAc;UACxBM,IAAI,EAAE;QACR,CAAC,CAAC;QAEFL,MAAM,CAACL,MAAM,CAAC,CAACM,WAAW,CAAC,CAAC;QAC5BD,MAAM,CAACL,MAAM,CAACQ,IAAI,CAAC,CAACF,WAAW,CAAC,CAAC;MACnC,CAAC,CAAC,OAAOK,KAAK,EAAE,CAGhB;IACF,CAAC,EAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}