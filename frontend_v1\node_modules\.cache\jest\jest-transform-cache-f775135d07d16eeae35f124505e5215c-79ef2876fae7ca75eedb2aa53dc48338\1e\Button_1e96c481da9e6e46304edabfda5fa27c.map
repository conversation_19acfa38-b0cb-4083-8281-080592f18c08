{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_reactNative", "_ThemeContext", "_jsxRuntime", "_excluded", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "<PERSON><PERSON>", "exports", "_ref", "_colors$primary", "_colors$primary2", "children", "title", "_ref$variant", "variant", "_ref$size", "size", "style", "onPress", "_ref$disabled", "disabled", "_ref$loading", "loading", "_ref$fullWidth", "fullWidth", "leftIcon", "rightIcon", "accessibilityLabel", "accessibilityHint", "props", "_objectWithoutProperties2", "_useTheme", "useTheme", "colors", "_useState", "useState", "_useState2", "_slicedToArray2", "isFocused", "setIsFocused", "styles", "createStyles", "buttonContent", "buttonStyleArray", "base", "focused", "filter", "Boolean", "textStyleArray", "text", "disabledText", "isInteractionDisabled", "jsxs", "TouchableOpacity", "assign", "undefined", "onFocus", "onBlur", "accessibilityRole", "accessibilityState", "busy", "jsx", "ActivityIndicator", "color", "primary", "contrast", "main", "loadingIndicator", "Fragment", "Text", "numberOfLines", "_colors$primary3", "_colors$surface", "_colors$primary4", "_colors$primary5", "_colors$primary6", "_colors$primary7", "_colors$text", "_colors$primary8", "_colors$primary9", "_colors$text2", "StyleSheet", "create", "flexDirection", "alignItems", "justifyContent", "borderRadius", "minHeight", "min<PERSON><PERSON><PERSON>", "gap", "backgroundColor", "paddingHorizontal", "paddingVertical", "secondary", "surface", "borderWidth", "borderColor", "outline", "minimal", "sm", "height", "md", "lg", "opacity", "light", "width", "fontFamily", "fontWeight", "textAlign", "fontSize", "lineHeight", "primaryText", "secondaryText", "outlineText", "minimalText", "tertiary", "smText", "mdText", "lgText", "marginRight"], "sources": ["Button.tsx"], "sourcesContent": ["/**\n * Button Component - Enhanced Foundational UI Atom\n *\n * Component Contract:\n * - Sources all styles from unified design tokens\n * - Supports multiple variants (primary, secondary, outline, minimal)\n * - <PERSON><PERSON> disabled and loading states with proper accessibility\n * - Meets minimum touch target requirements (44x44)\n * - Provides comprehensive accessibility support\n * - Follows React Native best practices for performance\n * - Supports theme switching (light/dark mode)\n *\n * @version 2.0.0\n * <AUTHOR> Development Team\n */\n\nimport React, { useState } from 'react';\nimport {\n  TouchableOpacity,\n  Text,\n  ActivityIndicator,\n  StyleSheet,\n  ViewStyle,\n  TextStyle,\n  TouchableOpacityProps,\n} from 'react-native';\n\nimport { useTheme } from '../../contexts/ThemeContext';\n\nexport interface ButtonProps extends Omit<TouchableOpacityProps, 'style'> {\n  /** Button content */\n  children?: React.ReactNode;\n  /** Button title (alternative to children) */\n  title?: string;\n  /** Button variant */\n  variant?: 'primary' | 'secondary' | 'outline' | 'minimal';\n  /** Button size */\n  size?: 'sm' | 'md' | 'lg';\n  /** Custom style override */\n  style?: ViewStyle;\n  /** Press handler */\n  onPress: () => void;\n  /** Disabled state */\n  disabled?: boolean;\n  /** Loading state */\n  loading?: boolean;\n  /** Full width button */\n  fullWidth?: boolean;\n  /** Icon to display before text */\n  leftIcon?: React.ReactNode;\n  /** Icon to display after text */\n  rightIcon?: React.ReactNode;\n}\n\nexport const Button: React.FC<ButtonProps> = ({\n  children,\n  title,\n  variant = 'primary',\n  size = 'md',\n  style,\n  onPress,\n  disabled = false,\n  loading = false,\n  fullWidth = false,\n  leftIcon,\n  rightIcon,\n  accessibilityLabel,\n  accessibilityHint,\n  ...props\n}) => {\n  const { colors } = useTheme();\n  const [isFocused, setIsFocused] = useState(false);\n\n  const styles = createStyles(colors);\n\n  // Determine button content - children takes priority over title\n  const buttonContent = children || title;\n\n  // Create style arrays based on props\n  const buttonStyleArray = [\n    styles.base,\n    styles[variant],\n    styles[size],\n    fullWidth && styles.fullWidth,\n    disabled && styles.disabled,\n    loading && styles.loading,\n    isFocused && styles.focused,\n    style,\n  ].filter(Boolean);\n\n  const textStyleArray = [\n    styles.text,\n    styles[`${variant}Text`],\n    styles[`${size}Text`],\n    disabled && styles.disabledText,\n  ].filter(Boolean);\n\n  const isInteractionDisabled = disabled || loading;\n\n  return (\n    <TouchableOpacity\n      style={buttonStyleArray}\n      onPress={isInteractionDisabled ? undefined : onPress}\n      disabled={isInteractionDisabled}\n      onFocus={() => setIsFocused(true)}\n      onBlur={() => setIsFocused(false)}\n      accessibilityRole=\"button\"\n      accessibilityState={{\n        disabled: isInteractionDisabled,\n        busy: loading,\n      }}\n      accessibilityLabel={\n        accessibilityLabel ||\n        (typeof buttonContent === 'string' ? buttonContent : 'Button')\n      }\n      accessibilityHint={accessibilityHint}\n      {...props}>\n      {loading && (\n        <ActivityIndicator\n          size=\"small\"\n          color={\n            variant === 'primary'\n              ? colors.primary?.contrast || '#FFFFFF'\n              : colors.primary?.main || '#4A6B52'\n          }\n          style={styles.loadingIndicator}\n        />\n      )}\n\n      {!loading && leftIcon && <>{leftIcon}</>}\n\n      {buttonContent && (\n        <Text style={textStyleArray} numberOfLines={1}>\n          {buttonContent}\n        </Text>\n      )}\n\n      {!loading && rightIcon && <>{rightIcon}</>}\n    </TouchableOpacity>\n  );\n};\n\n// Theme-based styles factory\nconst createStyles = (colors: any) =>\n  StyleSheet.create({\n    base: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      justifyContent: 'center',\n      borderRadius: 8,\n      minHeight: 44,\n      minWidth: 44,\n      gap: 8,\n    },\n\n    // Variants\n    primary: {\n      backgroundColor: colors.primary?.default || '#4A6B52',\n      paddingHorizontal: 16,\n      paddingVertical: 12,\n    },\n    secondary: {\n      backgroundColor: colors.surface?.secondary || '#F9FAFB',\n      borderWidth: 1,\n      borderColor: colors.primary?.default || '#4A6B52',\n      paddingHorizontal: 16,\n      paddingVertical: 12,\n    },\n    outline: {\n      backgroundColor: 'transparent',\n      borderWidth: 1,\n      borderColor: colors.primary?.default || '#4A6B52',\n      paddingHorizontal: 16,\n      paddingVertical: 12,\n    },\n    minimal: {\n      backgroundColor: 'transparent',\n      paddingHorizontal: 8,\n      paddingVertical: 8,\n    },\n\n    // Sizes\n    sm: {\n      height: 36,\n      paddingHorizontal: 12,\n      paddingVertical: 8,\n    },\n    md: {\n      height: 44,\n      paddingHorizontal: 16,\n      paddingVertical: 12,\n    },\n    lg: {\n      height: 52,\n      paddingHorizontal: 20,\n      paddingVertical: 16,\n    },\n\n    // States\n    disabled: {\n      opacity: 0.5,\n    },\n    loading: {\n      opacity: 0.8,\n    },\n    focused: {\n      borderWidth: 2,\n      borderColor: colors.primary?.light || '#6B8A74',\n    },\n    fullWidth: {\n      width: '100%',\n    },\n\n    // Text styles\n    text: {\n      fontFamily: 'System',\n      fontWeight: '500',\n      textAlign: 'center',\n      fontSize: 16,\n      lineHeight: 24,\n    },\n    primaryText: {\n      color: colors.primary?.contrast || '#FFFFFF',\n    },\n    secondaryText: {\n      color: colors.text?.primary || '#1A1A1A',\n    },\n    outlineText: {\n      color: colors.primary?.default || '#4A6B52',\n    },\n    minimalText: {\n      color: colors.primary?.default || '#4A6B52',\n    },\n    disabledText: {\n      color: colors.text?.tertiary || '#9CA3AF',\n    },\n\n    // Size-specific text\n    smText: {\n      fontSize: 14,\n    },\n    mdText: {\n      fontSize: 16,\n    },\n    lgText: {\n      fontSize: 18,\n    },\n\n    // Loading indicator\n    loadingIndicator: {\n      marginRight: 8,\n    },\n  });\n"], "mappings": ";;;;;;;AAgBA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAUA,IAAAE,aAAA,GAAAF,OAAA;AAAuD,IAAAG,WAAA,GAAAH,OAAA;AAAA,IAAAI,SAAA;AAAA,SAAAL,wBAAAM,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAR,uBAAA,YAAAA,wBAAAM,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AA2BhD,IAAMmB,MAA6B,GAAAC,OAAA,CAAAD,MAAA,GAAG,SAAhCA,MAA6BA,CAAAE,IAAA,EAepC;EAAA,IAAAC,eAAA,EAAAC,gBAAA;EAAA,IAdJC,QAAQ,GAAAH,IAAA,CAARG,QAAQ;IACRC,KAAK,GAAAJ,IAAA,CAALI,KAAK;IAAAC,YAAA,GAAAL,IAAA,CACLM,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,SAAS,GAAAA,YAAA;IAAAE,SAAA,GAAAP,IAAA,CACnBQ,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG,IAAI,GAAAA,SAAA;IACXE,KAAK,GAAAT,IAAA,CAALS,KAAK;IACLC,OAAO,GAAAV,IAAA,CAAPU,OAAO;IAAAC,aAAA,GAAAX,IAAA,CACPY,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,KAAK,GAAAA,aAAA;IAAAE,YAAA,GAAAb,IAAA,CAChBc,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,KAAK,GAAAA,YAAA;IAAAE,cAAA,GAAAf,IAAA,CACfgB,SAAS;IAATA,SAAS,GAAAD,cAAA,cAAG,KAAK,GAAAA,cAAA;IACjBE,QAAQ,GAAAjB,IAAA,CAARiB,QAAQ;IACRC,SAAS,GAAAlB,IAAA,CAATkB,SAAS;IACTC,kBAAkB,GAAAnB,IAAA,CAAlBmB,kBAAkB;IAClBC,iBAAiB,GAAApB,IAAA,CAAjBoB,iBAAiB;IACdC,KAAK,OAAAC,yBAAA,CAAAlC,OAAA,EAAAY,IAAA,EAAAvB,SAAA;EAER,IAAA8C,SAAA,GAAmB,IAAAC,sBAAQ,EAAC,CAAC;IAArBC,MAAM,GAAAF,SAAA,CAANE,MAAM;EACd,IAAAC,SAAA,GAAkC,IAAAC,eAAQ,EAAC,KAAK,CAAC;IAAAC,UAAA,OAAAC,eAAA,CAAAzC,OAAA,EAAAsC,SAAA;IAA1CI,SAAS,GAAAF,UAAA;IAAEG,YAAY,GAAAH,UAAA;EAE9B,IAAMI,MAAM,GAAGC,YAAY,CAACR,MAAM,CAAC;EAGnC,IAAMS,aAAa,GAAG/B,QAAQ,IAAIC,KAAK;EAGvC,IAAM+B,gBAAgB,GAAG,CACvBH,MAAM,CAACI,IAAI,EACXJ,MAAM,CAAC1B,OAAO,CAAC,EACf0B,MAAM,CAACxB,IAAI,CAAC,EACZQ,SAAS,IAAIgB,MAAM,CAAChB,SAAS,EAC7BJ,QAAQ,IAAIoB,MAAM,CAACpB,QAAQ,EAC3BE,OAAO,IAAIkB,MAAM,CAAClB,OAAO,EACzBgB,SAAS,IAAIE,MAAM,CAACK,OAAO,EAC3B5B,KAAK,CACN,CAAC6B,MAAM,CAACC,OAAO,CAAC;EAEjB,IAAMC,cAAc,GAAG,CACrBR,MAAM,CAACS,IAAI,EACXT,MAAM,CAAC,GAAG1B,OAAO,MAAM,CAAC,EACxB0B,MAAM,CAAC,GAAGxB,IAAI,MAAM,CAAC,EACrBI,QAAQ,IAAIoB,MAAM,CAACU,YAAY,CAChC,CAACJ,MAAM,CAACC,OAAO,CAAC;EAEjB,IAAMI,qBAAqB,GAAG/B,QAAQ,IAAIE,OAAO;EAEjD,OACE,IAAAtC,WAAA,CAAAoE,IAAA,EAACtE,YAAA,CAAAuE,gBAAgB,EAAAlD,MAAA,CAAAmD,MAAA;IACfrC,KAAK,EAAE0B,gBAAiB;IACxBzB,OAAO,EAAEiC,qBAAqB,GAAGI,SAAS,GAAGrC,OAAQ;IACrDE,QAAQ,EAAE+B,qBAAsB;IAChCK,OAAO,EAAE,SAATA,OAAOA,CAAA;MAAA,OAAQjB,YAAY,CAAC,IAAI,CAAC;IAAA,CAAC;IAClCkB,MAAM,EAAE,SAARA,MAAMA,CAAA;MAAA,OAAQlB,YAAY,CAAC,KAAK,CAAC;IAAA,CAAC;IAClCmB,iBAAiB,EAAC,QAAQ;IAC1BC,kBAAkB,EAAE;MAClBvC,QAAQ,EAAE+B,qBAAqB;MAC/BS,IAAI,EAAEtC;IACR,CAAE;IACFK,kBAAkB,EAChBA,kBAAkB,KACjB,OAAOe,aAAa,KAAK,QAAQ,GAAGA,aAAa,GAAG,QAAQ,CAC9D;IACDd,iBAAiB,EAAEA;EAAkB,GACjCC,KAAK;IAAAlB,QAAA,GACRW,OAAO,IACN,IAAAtC,WAAA,CAAA6E,GAAA,EAAC/E,YAAA,CAAAgF,iBAAiB;MAChB9C,IAAI,EAAC,OAAO;MACZ+C,KAAK,EACHjD,OAAO,KAAK,SAAS,GACjB,EAAAL,eAAA,GAAAwB,MAAM,CAAC+B,OAAO,qBAAdvD,eAAA,CAAgBwD,QAAQ,KAAI,SAAS,GACrC,EAAAvD,gBAAA,GAAAuB,MAAM,CAAC+B,OAAO,qBAAdtD,gBAAA,CAAgBwD,IAAI,KAAI,SAC7B;MACDjD,KAAK,EAAEuB,MAAM,CAAC2B;IAAiB,CAChC,CACF,EAEA,CAAC7C,OAAO,IAAIG,QAAQ,IAAI,IAAAzC,WAAA,CAAA6E,GAAA,EAAA7E,WAAA,CAAAoF,QAAA;MAAAzD,QAAA,EAAGc;IAAQ,CAAG,CAAC,EAEvCiB,aAAa,IACZ,IAAA1D,WAAA,CAAA6E,GAAA,EAAC/E,YAAA,CAAAuF,IAAI;MAACpD,KAAK,EAAE+B,cAAe;MAACsB,aAAa,EAAE,CAAE;MAAA3D,QAAA,EAC3C+B;IAAa,CACV,CACP,EAEA,CAACpB,OAAO,IAAII,SAAS,IAAI,IAAA1C,WAAA,CAAA6E,GAAA,EAAA7E,WAAA,CAAAoF,QAAA;MAAAzD,QAAA,EAAGe;IAAS,CAAG,CAAC;EAAA,EAC1B,CAAC;AAEvB,CAAC;AAGD,IAAMe,YAAY,GAAG,SAAfA,YAAYA,CAAIR,MAAW;EAAA,IAAAsC,gBAAA,EAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,YAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,aAAA;EAAA,OAC/BC,uBAAU,CAACC,MAAM,CAAC;IAChBtC,IAAI,EAAE;MACJuC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,YAAY,EAAE,CAAC;MACfC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,GAAG,EAAE;IACP,CAAC;IAGDzB,OAAO,EAAE;MACP0B,eAAe,EAAE,EAAAnB,gBAAA,GAAAtC,MAAM,CAAC+B,OAAO,qBAAdO,gBAAA,CAAgB3E,OAAO,KAAI,SAAS;MACrD+F,iBAAiB,EAAE,EAAE;MACrBC,eAAe,EAAE;IACnB,CAAC;IACDC,SAAS,EAAE;MACTH,eAAe,EAAE,EAAAlB,eAAA,GAAAvC,MAAM,CAAC6D,OAAO,qBAAdtB,eAAA,CAAgBqB,SAAS,KAAI,SAAS;MACvDE,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,EAAAvB,gBAAA,GAAAxC,MAAM,CAAC+B,OAAO,qBAAdS,gBAAA,CAAgB7E,OAAO,KAAI,SAAS;MACjD+F,iBAAiB,EAAE,EAAE;MACrBC,eAAe,EAAE;IACnB,CAAC;IACDK,OAAO,EAAE;MACPP,eAAe,EAAE,aAAa;MAC9BK,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,EAAAtB,gBAAA,GAAAzC,MAAM,CAAC+B,OAAO,qBAAdU,gBAAA,CAAgB9E,OAAO,KAAI,SAAS;MACjD+F,iBAAiB,EAAE,EAAE;MACrBC,eAAe,EAAE;IACnB,CAAC;IACDM,OAAO,EAAE;MACPR,eAAe,EAAE,aAAa;MAC9BC,iBAAiB,EAAE,CAAC;MACpBC,eAAe,EAAE;IACnB,CAAC;IAGDO,EAAE,EAAE;MACFC,MAAM,EAAE,EAAE;MACVT,iBAAiB,EAAE,EAAE;MACrBC,eAAe,EAAE;IACnB,CAAC;IACDS,EAAE,EAAE;MACFD,MAAM,EAAE,EAAE;MACVT,iBAAiB,EAAE,EAAE;MACrBC,eAAe,EAAE;IACnB,CAAC;IACDU,EAAE,EAAE;MACFF,MAAM,EAAE,EAAE;MACVT,iBAAiB,EAAE,EAAE;MACrBC,eAAe,EAAE;IACnB,CAAC;IAGDxE,QAAQ,EAAE;MACRmF,OAAO,EAAE;IACX,CAAC;IACDjF,OAAO,EAAE;MACPiF,OAAO,EAAE;IACX,CAAC;IACD1D,OAAO,EAAE;MACPkD,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,EAAArB,gBAAA,GAAA1C,MAAM,CAAC+B,OAAO,qBAAdW,gBAAA,CAAgB6B,KAAK,KAAI;IACxC,CAAC;IACDhF,SAAS,EAAE;MACTiF,KAAK,EAAE;IACT,CAAC;IAGDxD,IAAI,EAAE;MACJyD,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE,QAAQ;MACnBC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE;IACd,CAAC;IACDC,WAAW,EAAE;MACXhD,KAAK,EAAE,EAAAa,gBAAA,GAAA3C,MAAM,CAAC+B,OAAO,qBAAdY,gBAAA,CAAgBX,QAAQ,KAAI;IACrC,CAAC;IACD+C,aAAa,EAAE;MACbjD,KAAK,EAAE,EAAAc,YAAA,GAAA5C,MAAM,CAACgB,IAAI,qBAAX4B,YAAA,CAAab,OAAO,KAAI;IACjC,CAAC;IACDiD,WAAW,EAAE;MACXlD,KAAK,EAAE,EAAAe,gBAAA,GAAA7C,MAAM,CAAC+B,OAAO,qBAAdc,gBAAA,CAAgBlF,OAAO,KAAI;IACpC,CAAC;IACDsH,WAAW,EAAE;MACXnD,KAAK,EAAE,EAAAgB,gBAAA,GAAA9C,MAAM,CAAC+B,OAAO,qBAAde,gBAAA,CAAgBnF,OAAO,KAAI;IACpC,CAAC;IACDsD,YAAY,EAAE;MACZa,KAAK,EAAE,EAAAiB,aAAA,GAAA/C,MAAM,CAACgB,IAAI,qBAAX+B,aAAA,CAAamC,QAAQ,KAAI;IAClC,CAAC;IAGDC,MAAM,EAAE;MACNP,QAAQ,EAAE;IACZ,CAAC;IACDQ,MAAM,EAAE;MACNR,QAAQ,EAAE;IACZ,CAAC;IACDS,MAAM,EAAE;MACNT,QAAQ,EAAE;IACZ,CAAC;IAGD1C,gBAAgB,EAAE;MAChBoD,WAAW,EAAE;IACf;EACF,CAAC,CAAC;AAAA", "ignoreList": []}