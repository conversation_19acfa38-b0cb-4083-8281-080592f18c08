{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_classCallCheck2", "_createClass2", "_Platform", "_RCTDeviceEventEmitter", "_invariant", "NativeEventEmitter", "nativeModule", "Platform", "OS", "invariant", "hasAddListener", "addListener", "hasRemoveListeners", "removeListeners", "_nativeModule", "console", "warn", "key", "eventType", "listener", "context", "_this$_nativeModule", "_this", "subscription", "RCTDeviceEventEmitter", "remove", "_this$_nativeModule2", "emit", "_len", "arguments", "length", "args", "Array", "_key", "apply", "concat", "removeAllListeners", "_this$_nativeModule3", "listenerCount"], "sources": ["NativeEventEmitter.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\n'use strict';\n\nimport type {\n  EventSubscription,\n  IEventEmitter,\n} from '../vendor/emitter/EventEmitter';\n\nimport Platform from '../Utilities/Platform';\nimport RCTDeviceEventEmitter from './RCTDeviceEventEmitter';\nimport invariant from 'invariant';\n\ninterface NativeModule {\n  addListener(eventType: string): void;\n  removeListeners(count: number): void;\n}\n\nexport type {EventSubscription};\n\n// $FlowFixMe[unclear-type] unclear type of events\ntype UnsafeObject = Object;\n\n/**\n * `NativeEventEmitter` is intended for use by Native Modules to emit events to\n * JavaScript listeners. If a `NativeModule` is supplied to the constructor, it\n * will be notified (via `addListener` and `removeListeners`) when the listener\n * count changes to manage \"native memory\".\n *\n * Currently, all native events are fired via a global `RCTDeviceEventEmitter`.\n * This means event names must be globally unique, and it means that call sites\n * can theoretically listen to `RCTDeviceEventEmitter` (although discouraged).\n */\nexport default class NativeEventEmitter<\n  TEventToArgsMap: $ReadOnly<\n    Record<string, $ReadOnlyArray<UnsafeObject>>,\n  > = $ReadOnly<Record<string, $ReadOnlyArray<UnsafeObject>>>,\n> implements IEventEmitter<TEventToArgsMap>\n{\n  _nativeModule: ?NativeModule;\n\n  constructor(nativeModule: ?NativeModule) {\n    if (Platform.OS === 'ios') {\n      invariant(\n        nativeModule != null,\n        '`new NativeEventEmitter()` requires a non-null argument.',\n      );\n    }\n\n    const hasAddListener =\n      // $FlowFixMe[method-unbinding] added when improving typing for this parameters\n      !!nativeModule && typeof nativeModule.addListener === 'function';\n    const hasRemoveListeners =\n      // $FlowFixMe[method-unbinding] added when improving typing for this parameters\n      !!nativeModule && typeof nativeModule.removeListeners === 'function';\n\n    if (nativeModule && hasAddListener && hasRemoveListeners) {\n      this._nativeModule = nativeModule;\n    } else if (nativeModule != null) {\n      if (!hasAddListener) {\n        console.warn(\n          '`new NativeEventEmitter()` was called with a non-null argument without the required `addListener` method.',\n        );\n      }\n      if (!hasRemoveListeners) {\n        console.warn(\n          '`new NativeEventEmitter()` was called with a non-null argument without the required `removeListeners` method.',\n        );\n      }\n    }\n  }\n\n  addListener<TEvent: $Keys<TEventToArgsMap>>(\n    eventType: TEvent,\n    listener: (...args: $ElementType<TEventToArgsMap, TEvent>) => mixed,\n    context?: mixed,\n  ): EventSubscription {\n    this._nativeModule?.addListener(eventType);\n    let subscription: ?EventSubscription = RCTDeviceEventEmitter.addListener(\n      eventType,\n      listener,\n      context,\n    );\n\n    return {\n      remove: () => {\n        if (subscription != null) {\n          this._nativeModule?.removeListeners(1);\n          // $FlowFixMe[incompatible-use]\n          subscription.remove();\n          subscription = null;\n        }\n      },\n    };\n  }\n\n  emit<TEvent: $Keys<TEventToArgsMap>>(\n    eventType: TEvent,\n    ...args: $ElementType<TEventToArgsMap, TEvent>\n  ): void {\n    // Generally, `RCTDeviceEventEmitter` is directly invoked. But this is\n    // included for completeness.\n    RCTDeviceEventEmitter.emit(eventType, ...args);\n  }\n\n  removeAllListeners<TEvent: $Keys<TEventToArgsMap>>(\n    eventType?: ?TEvent,\n  ): void {\n    invariant(\n      eventType != null,\n      '`NativeEventEmitter.removeAllListener()` requires a non-null argument.',\n    );\n    this._nativeModule?.removeListeners(this.listenerCount(eventType));\n    RCTDeviceEventEmitter.removeAllListeners(eventType);\n  }\n\n  listenerCount<TEvent: $Keys<TEventToArgsMap>>(eventType: TEvent): number {\n    return RCTDeviceEventEmitter.listenerCount(eventType);\n  }\n}\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AAOb,IAAAQ,SAAA,GAAAT,sBAAA,CAAAC,OAAA;AACA,IAAAS,sBAAA,GAAAV,sBAAA,CAAAC,OAAA;AACA,IAAAU,UAAA,GAAAX,sBAAA,CAAAC,OAAA;AAAkC,IAsBbW,kBAAkB,GAAAR,OAAA,CAAAE,OAAA;EAQrC,SAAAM,mBAAYC,YAA2B,EAAE;IAAA,IAAAN,gBAAA,CAAAD,OAAA,QAAAM,kBAAA;IACvC,IAAIE,iBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;MACzB,IAAAC,kBAAS,EACPH,YAAY,IAAI,IAAI,EACpB,0DACF,CAAC;IACH;IAEA,IAAMI,cAAc,GAElB,CAAC,CAACJ,YAAY,IAAI,OAAOA,YAAY,CAACK,WAAW,KAAK,UAAU;IAClE,IAAMC,kBAAkB,GAEtB,CAAC,CAACN,YAAY,IAAI,OAAOA,YAAY,CAACO,eAAe,KAAK,UAAU;IAEtE,IAAIP,YAAY,IAAII,cAAc,IAAIE,kBAAkB,EAAE;MACxD,IAAI,CAACE,aAAa,GAAGR,YAAY;IACnC,CAAC,MAAM,IAAIA,YAAY,IAAI,IAAI,EAAE;MAC/B,IAAI,CAACI,cAAc,EAAE;QACnBK,OAAO,CAACC,IAAI,CACV,2GACF,CAAC;MACH;MACA,IAAI,CAACJ,kBAAkB,EAAE;QACvBG,OAAO,CAACC,IAAI,CACV,+GACF,CAAC;MACH;IACF;EACF;EAAC,WAAAf,aAAA,CAAAF,OAAA,EAAAM,kBAAA;IAAAY,GAAA;IAAAnB,KAAA,EAED,SAAAa,WAAWA,CACTO,SAAiB,EACjBC,QAAmE,EACnEC,OAAe,EACI;MAAA,IAAAC,mBAAA;QAAAC,KAAA;MACnB,CAAAD,mBAAA,OAAI,CAACP,aAAa,aAAlBO,mBAAA,CAAoBV,WAAW,CAACO,SAAS,CAAC;MAC1C,IAAIK,YAAgC,GAAGC,8BAAqB,CAACb,WAAW,CACtEO,SAAS,EACTC,QAAQ,EACRC,OACF,CAAC;MAED,OAAO;QACLK,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ;UACZ,IAAIF,YAAY,IAAI,IAAI,EAAE;YAAA,IAAAG,oBAAA;YACxB,CAAAA,oBAAA,GAAAJ,KAAI,CAACR,aAAa,aAAlBY,oBAAA,CAAoBb,eAAe,CAAC,CAAC,CAAC;YAEtCU,YAAY,CAACE,MAAM,CAAC,CAAC;YACrBF,YAAY,GAAG,IAAI;UACrB;QACF;MACF,CAAC;IACH;EAAC;IAAAN,GAAA;IAAAnB,KAAA,EAED,SAAA6B,IAAIA,CACFT,SAAiB,EAEX;MAAA,SAAAU,IAAA,GAAAC,SAAA,CAAAC,MAAA,EADHC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;QAAJF,IAAI,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;MAAA;MAIPT,8BAAqB,CAACG,IAAI,CAAAO,KAAA,CAA1BV,8BAAqB,GAAMN,SAAS,EAAAiB,MAAA,CAAKJ,IAAI,EAAC;IAChD;EAAC;IAAAd,GAAA;IAAAnB,KAAA,EAED,SAAAsC,kBAAkBA,CAChBlB,SAAmB,EACb;MAAA,IAAAmB,oBAAA;MACN,IAAA5B,kBAAS,EACPS,SAAS,IAAI,IAAI,EACjB,wEACF,CAAC;MACD,CAAAmB,oBAAA,OAAI,CAACvB,aAAa,aAAlBuB,oBAAA,CAAoBxB,eAAe,CAAC,IAAI,CAACyB,aAAa,CAACpB,SAAS,CAAC,CAAC;MAClEM,8BAAqB,CAACY,kBAAkB,CAAClB,SAAS,CAAC;IACrD;EAAC;IAAAD,GAAA;IAAAnB,KAAA,EAED,SAAAwC,aAAaA,CAAiCpB,SAAiB,EAAU;MACvE,OAAOM,8BAAqB,CAACc,aAAa,CAACpB,SAAS,CAAC;IACvD;EAAC;AAAA", "ignoreList": []}