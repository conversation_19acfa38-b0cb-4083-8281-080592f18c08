977eb8f0230ad02b9a89551756080c03
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.passwordlessAuthService = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _asyncStorage = _interopRequireDefault(require("@react-native-async-storage/async-storage"));
var Crypto = _interopRequireWildcard(require("expo-crypto"));
var LocalAuthentication = _interopRequireWildcard(require("expo-local-authentication"));
var _authService = require("./authService");
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var PasswordlessAuthService = function () {
  function PasswordlessAuthService() {
    (0, _classCallCheck2.default)(this, PasswordlessAuthService);
    this.MAGIC_LINK_EXPIRY = 15 * 60 * 1000;
    this.OTP_EXPIRY = 5 * 60 * 1000;
    this.MAX_OTP_ATTEMPTS = 3;
    this.RATE_LIMIT_WINDOW = 60 * 1000;
    this.MAX_REQUESTS_PER_WINDOW = 5;
    this.requestCounts = new Map();
  }
  return (0, _createClass2.default)(PasswordlessAuthService, [{
    key: "checkRateLimit",
    value: function checkRateLimit(identifier) {
      var now = Date.now();
      var record = this.requestCounts.get(identifier);
      if (!record || now > record.resetTime) {
        this.requestCounts.set(identifier, {
          count: 1,
          resetTime: now + this.RATE_LIMIT_WINDOW
        });
        return false;
      }
      if (record.count >= this.MAX_REQUESTS_PER_WINDOW) {
        return true;
      }
      record.count++;
      return false;
    }
  }, {
    key: "generateSecureToken",
    value: (function () {
      var _generateSecureToken = (0, _asyncToGenerator2.default)(function* () {
        var randomBytes = yield Crypto.getRandomBytesAsync(32);
        return Array.from(randomBytes, function (byte) {
          return byte.toString(16).padStart(2, '0');
        }).join('');
      });
      function generateSecureToken() {
        return _generateSecureToken.apply(this, arguments);
      }
      return generateSecureToken;
    }())
  }, {
    key: "generateOTPCode",
    value: function generateOTPCode() {
      return Math.floor(100000 + Math.random() * 900000).toString();
    }
  }, {
    key: "sendMagicLink",
    value: (function () {
      var _sendMagicLink = (0, _asyncToGenerator2.default)(function* (email) {
        try {
          if (this.checkRateLimit(email)) {
            return {
              success: false,
              error: 'Too many requests. Please wait before trying again.'
            };
          }
          var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(email)) {
            return {
              success: false,
              error: 'Invalid email address format.'
            };
          }
          var token = yield this.generateSecureToken();
          var expiresAt = Date.now() + this.MAGIC_LINK_EXPIRY;
          var magicLinkData = {
            email: email,
            token: token,
            expiresAt: expiresAt,
            used: false
          };
          yield _asyncStorage.default.setItem(`magic_link_${token}`, JSON.stringify(magicLinkData));
          console.log(`🔗 Magic Link for ${email}: vierla://auth/magic?token=${token}`);
          return {
            success: true,
            requiresVerification: true,
            verificationId: token
          };
        } catch (error) {
          console.error('Error sending magic link:', error);
          return {
            success: false,
            error: 'Failed to send magic link. Please try again.'
          };
        }
      });
      function sendMagicLink(_x) {
        return _sendMagicLink.apply(this, arguments);
      }
      return sendMagicLink;
    }())
  }, {
    key: "verifyMagicLink",
    value: (function () {
      var _verifyMagicLink = (0, _asyncToGenerator2.default)(function* (token) {
        try {
          var storedData = yield _asyncStorage.default.getItem(`magic_link_${token}`);
          if (!storedData) {
            return {
              success: false,
              error: 'Invalid or expired magic link.'
            };
          }
          var magicLinkData = JSON.parse(storedData);
          if (Date.now() > magicLinkData.expiresAt) {
            yield _asyncStorage.default.removeItem(`magic_link_${token}`);
            return {
              success: false,
              error: 'Magic link has expired. Please request a new one.'
            };
          }
          if (magicLinkData.used) {
            return {
              success: false,
              error: 'Magic link has already been used.'
            };
          }
          magicLinkData.used = true;
          yield _asyncStorage.default.setItem(`magic_link_${token}`, JSON.stringify(magicLinkData));
          var authResult = yield _authService.authService.authenticateWithEmail(magicLinkData.email);
          yield _asyncStorage.default.removeItem(`magic_link_${token}`);
          return authResult;
        } catch (error) {
          console.error('Error verifying magic link:', error);
          return {
            success: false,
            error: 'Failed to verify magic link. Please try again.'
          };
        }
      });
      function verifyMagicLink(_x2) {
        return _verifyMagicLink.apply(this, arguments);
      }
      return verifyMagicLink;
    }())
  }, {
    key: "sendOTP",
    value: (function () {
      var _sendOTP = (0, _asyncToGenerator2.default)(function* (phone) {
        try {
          if (this.checkRateLimit(phone)) {
            return {
              success: false,
              error: 'Too many requests. Please wait before trying again.'
            };
          }
          var phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
          if (!phoneRegex.test(phone)) {
            return {
              success: false,
              error: 'Invalid phone number format.'
            };
          }
          var code = this.generateOTPCode();
          var expiresAt = Date.now() + this.OTP_EXPIRY;
          var otpData = {
            phone: phone,
            code: code,
            expiresAt: expiresAt,
            attempts: 0,
            maxAttempts: this.MAX_OTP_ATTEMPTS
          };
          yield _asyncStorage.default.setItem(`otp_${phone}`, JSON.stringify(otpData));
          console.log(`📱 OTP for ${phone}: ${code}`);
          return {
            success: true,
            requiresVerification: true,
            verificationId: phone
          };
        } catch (error) {
          console.error('Error sending OTP:', error);
          return {
            success: false,
            error: 'Failed to send OTP. Please try again.'
          };
        }
      });
      function sendOTP(_x3) {
        return _sendOTP.apply(this, arguments);
      }
      return sendOTP;
    }())
  }, {
    key: "verifyOTP",
    value: (function () {
      var _verifyOTP = (0, _asyncToGenerator2.default)(function* (phone, code) {
        try {
          var storedData = yield _asyncStorage.default.getItem(`otp_${phone}`);
          if (!storedData) {
            return {
              success: false,
              error: 'No OTP found for this phone number.'
            };
          }
          var otpData = JSON.parse(storedData);
          if (Date.now() > otpData.expiresAt) {
            yield _asyncStorage.default.removeItem(`otp_${phone}`);
            return {
              success: false,
              error: 'OTP has expired. Please request a new one.'
            };
          }
          if (otpData.attempts >= otpData.maxAttempts) {
            yield _asyncStorage.default.removeItem(`otp_${phone}`);
            return {
              success: false,
              error: 'Too many failed attempts. Please request a new OTP.'
            };
          }
          if (otpData.code !== code) {
            otpData.attempts++;
            yield _asyncStorage.default.setItem(`otp_${phone}`, JSON.stringify(otpData));
            var remainingAttempts = otpData.maxAttempts - otpData.attempts;
            return {
              success: false,
              error: `Invalid OTP. ${remainingAttempts} attempts remaining.`
            };
          }
          var authResult = yield _authService.authService.authenticateWithPhone(phone);
          yield _asyncStorage.default.removeItem(`otp_${phone}`);
          return authResult;
        } catch (error) {
          console.error('Error verifying OTP:', error);
          return {
            success: false,
            error: 'Failed to verify OTP. Please try again.'
          };
        }
      });
      function verifyOTP(_x4, _x5) {
        return _verifyOTP.apply(this, arguments);
      }
      return verifyOTP;
    }())
  }, {
    key: "authenticateWithBiometrics",
    value: (function () {
      var _authenticateWithBiometrics = (0, _asyncToGenerator2.default)(function* () {
        var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
        try {
          var hasHardware = yield LocalAuthentication.hasHardwareAsync();
          if (!hasHardware) {
            return {
              success: false,
              error: 'Biometric authentication is not available on this device.'
            };
          }
          var isEnrolled = yield LocalAuthentication.isEnrolledAsync();
          if (!isEnrolled) {
            return {
              success: false,
              error: 'No biometric credentials are enrolled on this device.'
            };
          }
          var result = yield LocalAuthentication.authenticateAsync({
            promptMessage: options.promptMessage || 'Authenticate to sign in',
            fallbackLabel: options.fallbackLabel || 'Use passcode',
            disableDeviceFallback: options.disableDeviceFallback || false
          });
          if (!result.success) {
            return {
              success: false,
              error: result.error || 'Biometric authentication failed.'
            };
          }
          var storedUser = yield _asyncStorage.default.getItem('biometric_user');
          if (!storedUser) {
            return {
              success: false,
              error: 'No biometric user data found. Please set up biometric authentication.'
            };
          }
          var userData = JSON.parse(storedUser);
          var authResult = yield _authService.authService.authenticateWithBiometric(userData);
          return authResult;
        } catch (error) {
          console.error('Error with biometric authentication:', error);
          return {
            success: false,
            error: 'Biometric authentication failed. Please try again.'
          };
        }
      });
      function authenticateWithBiometrics() {
        return _authenticateWithBiometrics.apply(this, arguments);
      }
      return authenticateWithBiometrics;
    }())
  }, {
    key: "setupBiometricAuth",
    value: (function () {
      var _setupBiometricAuth = (0, _asyncToGenerator2.default)(function* (user) {
        try {
          yield _asyncStorage.default.setItem('biometric_user', JSON.stringify(user));
          return true;
        } catch (error) {
          console.error('Error setting up biometric auth:', error);
          return false;
        }
      });
      function setupBiometricAuth(_x6) {
        return _setupBiometricAuth.apply(this, arguments);
      }
      return setupBiometricAuth;
    }())
  }, {
    key: "isBiometricSetup",
    value: (function () {
      var _isBiometricSetup = (0, _asyncToGenerator2.default)(function* () {
        try {
          var storedUser = yield _asyncStorage.default.getItem('biometric_user');
          return !!storedUser;
        } catch (error) {
          return false;
        }
      });
      function isBiometricSetup() {
        return _isBiometricSetup.apply(this, arguments);
      }
      return isBiometricSetup;
    }())
  }, {
    key: "clearAuthData",
    value: (function () {
      var _clearAuthData = (0, _asyncToGenerator2.default)(function* () {
        try {
          var keys = yield _asyncStorage.default.getAllKeys();
          var authKeys = keys.filter(function (key) {
            return key.startsWith('magic_link_') || key.startsWith('otp_') || key === 'biometric_user';
          });
          if (authKeys.length > 0) {
            yield _asyncStorage.default.multiRemove(authKeys);
          }
        } catch (error) {
          console.error('Error clearing auth data:', error);
        }
      });
      function clearAuthData() {
        return _clearAuthData.apply(this, arguments);
      }
      return clearAuthData;
    }())
  }]);
}();
var passwordlessAuthService = exports.passwordlessAuthService = new PasswordlessAuthService();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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