b87e5faedba2d8c8172ee562895f3b14
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = useRefEffect;
var _react = require("react");
function useRefEffect(effect) {
  var cleanupRef = (0, _react.useRef)(undefined);
  return (0, _react.useCallback)(function (instance) {
    if (cleanupRef.current) {
      cleanupRef.current();
      cleanupRef.current = undefined;
    }
    if (instance != null) {
      cleanupRef.current = effect(instance);
    }
  }, [effect]);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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