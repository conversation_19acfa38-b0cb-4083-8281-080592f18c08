a279f501e331f160b8a8f4b2ad0d7cf8
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Box = void 0;
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _react = _interopRequireDefault(require("react"));
var _reactNative = require("react-native");
var _theme = require("../../core/theme/theme");
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["children", "padding", "margin", "backgroundColor", "borderRadius", "style", "accessibilityLabel", "accessibilityHint"];
var spacingMap = {
  xs: parseInt(_theme.SpacingSemanticXs, 10),
  sm: parseInt(_theme.SpacingSemanticSm, 10),
  md: parseInt(_theme.SpacingSemanticMd, 10),
  lg: parseInt(_theme.SpacingSemanticLg, 10)
};
var backgroundColorMap = {
  primary: _theme.ColorBackgroundPrimary,
  secondary: _theme.ColorBackgroundSecondary,
  tertiary: _theme.ColorBackgroundTertiary
};
var borderRadiusMap = {
  none: parseInt(_theme.BorderRadiusNone, 10),
  sm: parseInt(_theme.BorderRadiusSm, 10),
  md: parseInt(_theme.BorderRadiusMd, 10),
  lg: parseInt(_theme.BorderRadiusLg, 10)
};
var Box = exports.Box = function Box(_ref) {
  var children = _ref.children,
    padding = _ref.padding,
    margin = _ref.margin,
    backgroundColor = _ref.backgroundColor,
    borderRadius = _ref.borderRadius,
    style = _ref.style,
    accessibilityLabel = _ref.accessibilityLabel,
    accessibilityHint = _ref.accessibilityHint,
    props = (0, _objectWithoutProperties2.default)(_ref, _excluded);
  var boxStyle = Object.assign({}, padding && {
    padding: spacingMap[padding]
  }, margin && {
    margin: spacingMap[margin]
  }, backgroundColor && {
    backgroundColor: backgroundColorMap[backgroundColor]
  }, borderRadius && {
    borderRadius: borderRadiusMap[borderRadius]
  }, style);
  return (0, _jsxRuntime.jsx)(_reactNative.View, Object.assign({
    style: boxStyle,
    accessibilityRole: "none",
    accessibilityLabel: accessibilityLabel,
    accessibilityHint: accessibilityHint
  }, props, {
    children: children
  }));
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfcmVhY3QiLCJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0IiwicmVxdWlyZSIsIl9yZWFjdE5hdGl2ZSIsIl90aGVtZSIsIl9qc3hSdW50aW1lIiwiX2V4Y2x1ZGVkIiwic3BhY2luZ01hcCIsInhzIiwicGFyc2VJbnQiLCJTcGFjaW5nU2VtYW50aWNYcyIsInNtIiwiU3BhY2luZ1NlbWFudGljU20iLCJtZCIsIlNwYWNpbmdTZW1hbnRpY01kIiwibGciLCJTcGFjaW5nU2VtYW50aWNMZyIsImJhY2tncm91bmRDb2xvck1hcCIsInByaW1hcnkiLCJDb2xvckJhY2tncm91bmRQcmltYXJ5Iiwic2Vjb25kYXJ5IiwiQ29sb3JCYWNrZ3JvdW5kU2Vjb25kYXJ5IiwidGVydGlhcnkiLCJDb2xvckJhY2tncm91bmRUZXJ0aWFyeSIsImJvcmRlclJhZGl1c01hcCIsIm5vbmUiLCJCb3JkZXJSYWRpdXNOb25lIiwiQm9yZGVyUmFkaXVzU20iLCJCb3JkZXJSYWRpdXNNZCIsIkJvcmRlclJhZGl1c0xnIiwiQm94IiwiZXhwb3J0cyIsIl9yZWYiLCJjaGlsZHJlbiIsInBhZGRpbmciLCJtYXJnaW4iLCJiYWNrZ3JvdW5kQ29sb3IiLCJib3JkZXJSYWRpdXMiLCJzdHlsZSIsImFjY2Vzc2liaWxpdHlMYWJlbCIsImFjY2Vzc2liaWxpdHlIaW50IiwicHJvcHMiLCJfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMyIiwiZGVmYXVsdCIsImJveFN0eWxlIiwiT2JqZWN0IiwiYXNzaWduIiwianN4IiwiVmlldyIsImFjY2Vzc2liaWxpdHlSb2xlIl0sInNvdXJjZXMiOlsiQm94LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEJveCBDb21wb25lbnQgLSBGb3VuZGF0aW9uYWwgTGF5b3V0IEF0b21cbiAqXG4gKiBDb21wb25lbnQgQ29udHJhY3Q6XG4gKiAtIFNvdXJjZXMgYWxsIHN0eWxlcyBmcm9tIGRlc2lnbiB0b2tlbnMgZXhjbHVzaXZlbHlcbiAqIC0gUHJvdmlkZXMgZmxleGlibGUgbGF5b3V0IGNvbnRhaW5lciBmdW5jdGlvbmFsaXR5XG4gKiAtIFN1cHBvcnRzIHNlbWFudGljIHNwYWNpbmcsIGNvbG9ycywgYW5kIGJvcmRlciByYWRpdXNcbiAqIC0gSGFuZGxlcyBhY2Nlc3NpYmlsaXR5IGZvciBjb250YWluZXIgZWxlbWVudHNcbiAqIC0gRm9sbG93cyBSZWFjdCBOYXRpdmUgYmVzdCBwcmFjdGljZXMgZm9yIHBlcmZvcm1hbmNlXG4gKiAtIFNlcnZlcyBhcyBiYXNlIGZvciBvdGhlciBsYXlvdXQgY29tcG9uZW50c1xuICpcbiAqIEB2ZXJzaW9uIDEuMC4wXG4gKiBAYXV0aG9yIFZpZXJsYSBEZXZlbG9wbWVudCBUZWFtXG4gKi9cblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IFZpZXcsIFZpZXdQcm9wcywgVmlld1N0eWxlIH0gZnJvbSAncmVhY3QtbmF0aXZlJztcblxuaW1wb3J0IHtcbiAgQ29sb3JCYWNrZ3JvdW5kUHJpbWFyeSxcbiAgQ29sb3JCYWNrZ3JvdW5kU2Vjb25kYXJ5LFxuICBDb2xvckJhY2tncm91bmRUZXJ0aWFyeSxcbiAgU3BhY2luZ1NlbWFudGljWHMsXG4gIFNwYWNpbmdTZW1hbnRpY1NtLFxuICBTcGFjaW5nU2VtYW50aWNNZCxcbiAgU3BhY2luZ1NlbWFudGljTGcsXG4gIEJvcmRlclJhZGl1c05vbmUsXG4gIEJvcmRlclJhZGl1c1NtLFxuICBCb3JkZXJSYWRpdXNNZCxcbiAgQm9yZGVyUmFkaXVzTGcsXG59IGZyb20gJy4uLy4uL2NvcmUvdGhlbWUvdGhlbWUnO1xuXG50eXBlIFNwYWNpbmdTaXplID0gJ3hzJyB8ICdzbScgfCAnbWQnIHwgJ2xnJztcbnR5cGUgQmFja2dyb3VuZENvbG9yID0gJ3ByaW1hcnknIHwgJ3NlY29uZGFyeScgfCAndGVydGlhcnknO1xudHlwZSBCb3JkZXJSYWRpdXNTaXplID0gJ25vbmUnIHwgJ3NtJyB8ICdtZCcgfCAnbGcnO1xuXG5leHBvcnQgaW50ZXJmYWNlIEJveFByb3BzIGV4dGVuZHMgT21pdDxWaWV3UHJvcHMsICdzdHlsZSc+IHtcbiAgLyoqIEJveCBjb250ZW50ICovXG4gIGNoaWxkcmVuPzogUmVhY3QuUmVhY3ROb2RlO1xuICAvKiogUGFkZGluZyBzaXplIGZyb20gZGVzaWduIHRva2VucyAqL1xuICBwYWRkaW5nPzogU3BhY2luZ1NpemU7XG4gIC8qKiBNYXJnaW4gc2l6ZSBmcm9tIGRlc2lnbiB0b2tlbnMgKi9cbiAgbWFyZ2luPzogU3BhY2luZ1NpemU7XG4gIC8qKiBCYWNrZ3JvdW5kIGNvbG9yIGZyb20gZGVzaWduIHRva2VucyAqL1xuICBiYWNrZ3JvdW5kQ29sb3I/OiBCYWNrZ3JvdW5kQ29sb3I7XG4gIC8qKiBCb3JkZXIgcmFkaXVzIGZyb20gZGVzaWduIHRva2VucyAqL1xuICBib3JkZXJSYWRpdXM/OiBCb3JkZXJSYWRpdXNTaXplO1xuICAvKiogQ3VzdG9tIHN0eWxlIG92ZXJyaWRlICovXG4gIHN0eWxlPzogVmlld1N0eWxlO1xufVxuXG5jb25zdCBzcGFjaW5nTWFwID0ge1xuICB4czogcGFyc2VJbnQoU3BhY2luZ1NlbWFudGljWHMsIDEwKSxcbiAgc206IHBhcnNlSW50KFNwYWNpbmdTZW1hbnRpY1NtLCAxMCksXG4gIG1kOiBwYXJzZUludChTcGFjaW5nU2VtYW50aWNNZCwgMTApLFxuICBsZzogcGFyc2VJbnQoU3BhY2luZ1NlbWFudGljTGcsIDEwKSxcbn07XG5cbmNvbnN0IGJhY2tncm91bmRDb2xvck1hcCA9IHtcbiAgcHJpbWFyeTogQ29sb3JCYWNrZ3JvdW5kUHJpbWFyeSxcbiAgc2Vjb25kYXJ5OiBDb2xvckJhY2tncm91bmRTZWNvbmRhcnksXG4gIHRlcnRpYXJ5OiBDb2xvckJhY2tncm91bmRUZXJ0aWFyeSxcbn07XG5cbmNvbnN0IGJvcmRlclJhZGl1c01hcCA9IHtcbiAgbm9uZTogcGFyc2VJbnQoQm9yZGVyUmFkaXVzTm9uZSwgMTApLFxuICBzbTogcGFyc2VJbnQoQm9yZGVyUmFkaXVzU20sIDEwKSxcbiAgbWQ6IHBhcnNlSW50KEJvcmRlclJhZGl1c01kLCAxMCksXG4gIGxnOiBwYXJzZUludChCb3JkZXJSYWRpdXNMZywgMTApLFxufTtcblxuZXhwb3J0IGNvbnN0IEJveDogUmVhY3QuRkM8Qm94UHJvcHM+ID0gKHtcbiAgY2hpbGRyZW4sXG4gIHBhZGRpbmcsXG4gIG1hcmdpbixcbiAgYmFja2dyb3VuZENvbG9yLFxuICBib3JkZXJSYWRpdXMsXG4gIHN0eWxlLFxuICBhY2Nlc3NpYmlsaXR5TGFiZWwsXG4gIGFjY2Vzc2liaWxpdHlIaW50LFxuICAuLi5wcm9wc1xufSkgPT4ge1xuICBjb25zdCBib3hTdHlsZTogVmlld1N0eWxlID0ge1xuICAgIC4uLihwYWRkaW5nICYmIHsgcGFkZGluZzogc3BhY2luZ01hcFtwYWRkaW5nXSB9KSxcbiAgICAuLi4obWFyZ2luICYmIHsgbWFyZ2luOiBzcGFjaW5nTWFwW21hcmdpbl0gfSksXG4gICAgLi4uKGJhY2tncm91bmRDb2xvciAmJiB7XG4gICAgICBiYWNrZ3JvdW5kQ29sb3I6IGJhY2tncm91bmRDb2xvck1hcFtiYWNrZ3JvdW5kQ29sb3JdLFxuICAgIH0pLFxuICAgIC4uLihib3JkZXJSYWRpdXMgJiYgeyBib3JkZXJSYWRpdXM6IGJvcmRlclJhZGl1c01hcFtib3JkZXJSYWRpdXNdIH0pLFxuICAgIC4uLnN0eWxlLFxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPFZpZXdcbiAgICAgIHN0eWxlPXtib3hTdHlsZX1cbiAgICAgIGFjY2Vzc2liaWxpdHlSb2xlPVwibm9uZVwiXG4gICAgICBhY2Nlc3NpYmlsaXR5TGFiZWw9e2FjY2Vzc2liaWxpdHlMYWJlbH1cbiAgICAgIGFjY2Vzc2liaWxpdHlIaW50PXthY2Nlc3NpYmlsaXR5SGludH1cbiAgICAgIHsuLi5wcm9wc30+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9WaWV3PlxuICApO1xufTtcbiJdLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBZUEsSUFBQUEsTUFBQSxHQUFBQyxzQkFBQSxDQUFBQyxPQUFBO0FBQ0EsSUFBQUMsWUFBQSxHQUFBRCxPQUFBO0FBRUEsSUFBQUUsTUFBQSxHQUFBRixPQUFBO0FBWWdDLElBQUFHLFdBQUEsR0FBQUgsT0FBQTtBQUFBLElBQUFJLFNBQUE7QUFxQmhDLElBQU1DLFVBQVUsR0FBRztFQUNqQkMsRUFBRSxFQUFFQyxRQUFRLENBQUNDLHdCQUFpQixFQUFFLEVBQUUsQ0FBQztFQUNuQ0MsRUFBRSxFQUFFRixRQUFRLENBQUNHLHdCQUFpQixFQUFFLEVBQUUsQ0FBQztFQUNuQ0MsRUFBRSxFQUFFSixRQUFRLENBQUNLLHdCQUFpQixFQUFFLEVBQUUsQ0FBQztFQUNuQ0MsRUFBRSxFQUFFTixRQUFRLENBQUNPLHdCQUFpQixFQUFFLEVBQUU7QUFDcEMsQ0FBQztBQUVELElBQU1DLGtCQUFrQixHQUFHO0VBQ3pCQyxPQUFPLEVBQUVDLDZCQUFzQjtFQUMvQkMsU0FBUyxFQUFFQywrQkFBd0I7RUFDbkNDLFFBQVEsRUFBRUM7QUFDWixDQUFDO0FBRUQsSUFBTUMsZUFBZSxHQUFHO0VBQ3RCQyxJQUFJLEVBQUVoQixRQUFRLENBQUNpQix1QkFBZ0IsRUFBRSxFQUFFLENBQUM7RUFDcENmLEVBQUUsRUFBRUYsUUFBUSxDQUFDa0IscUJBQWMsRUFBRSxFQUFFLENBQUM7RUFDaENkLEVBQUUsRUFBRUosUUFBUSxDQUFDbUIscUJBQWMsRUFBRSxFQUFFLENBQUM7RUFDaENiLEVBQUUsRUFBRU4sUUFBUSxDQUFDb0IscUJBQWMsRUFBRSxFQUFFO0FBQ2pDLENBQUM7QUFFTSxJQUFNQyxHQUF1QixHQUFBQyxPQUFBLENBQUFELEdBQUEsR0FBRyxTQUExQkEsR0FBdUJBLENBQUFFLElBQUEsRUFVOUI7RUFBQSxJQVRKQyxRQUFRLEdBQUFELElBQUEsQ0FBUkMsUUFBUTtJQUNSQyxPQUFPLEdBQUFGLElBQUEsQ0FBUEUsT0FBTztJQUNQQyxNQUFNLEdBQUFILElBQUEsQ0FBTkcsTUFBTTtJQUNOQyxlQUFlLEdBQUFKLElBQUEsQ0FBZkksZUFBZTtJQUNmQyxZQUFZLEdBQUFMLElBQUEsQ0FBWkssWUFBWTtJQUNaQyxLQUFLLEdBQUFOLElBQUEsQ0FBTE0sS0FBSztJQUNMQyxrQkFBa0IsR0FBQVAsSUFBQSxDQUFsQk8sa0JBQWtCO0lBQ2xCQyxpQkFBaUIsR0FBQVIsSUFBQSxDQUFqQlEsaUJBQWlCO0lBQ2RDLEtBQUssT0FBQUMseUJBQUEsQ0FBQUMsT0FBQSxFQUFBWCxJQUFBLEVBQUExQixTQUFBO0VBRVIsSUFBTXNDLFFBQW1CLEdBQUFDLE1BQUEsQ0FBQUMsTUFBQSxLQUNuQlosT0FBTyxJQUFJO0lBQUVBLE9BQU8sRUFBRTNCLFVBQVUsQ0FBQzJCLE9BQU87RUFBRSxDQUFDLEVBQzNDQyxNQUFNLElBQUk7SUFBRUEsTUFBTSxFQUFFNUIsVUFBVSxDQUFDNEIsTUFBTTtFQUFFLENBQUMsRUFDeENDLGVBQWUsSUFBSTtJQUNyQkEsZUFBZSxFQUFFbkIsa0JBQWtCLENBQUNtQixlQUFlO0VBQ3JELENBQUMsRUFDR0MsWUFBWSxJQUFJO0lBQUVBLFlBQVksRUFBRWIsZUFBZSxDQUFDYSxZQUFZO0VBQUUsQ0FBQyxFQUNoRUMsS0FBSyxDQUNUO0VBRUQsT0FDRSxJQUFBakMsV0FBQSxDQUFBMEMsR0FBQSxFQUFDNUMsWUFBQSxDQUFBNkMsSUFBSSxFQUFBSCxNQUFBLENBQUFDLE1BQUE7SUFDSFIsS0FBSyxFQUFFTSxRQUFTO0lBQ2hCSyxpQkFBaUIsRUFBQyxNQUFNO0lBQ3hCVixrQkFBa0IsRUFBRUEsa0JBQW1CO0lBQ3ZDQyxpQkFBaUIsRUFBRUE7RUFBa0IsR0FDakNDLEtBQUs7SUFBQVIsUUFBQSxFQUNSQTtFQUFRLEVBQ0wsQ0FBQztBQUVYLENBQUMiLCJpZ25vcmVMaXN0IjpbXX0=