83ab59051bbd5f3492b69b4e2ca56149
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _requireNativeComponent = _interopRequireDefault(require("../../Libraries/ReactNative/requireNativeComponent"));
var _UIManager = _interopRequireDefault(require("../ReactNative/UIManager"));
function codegenNativeComponent(componentName, options) {
  if (global.RN$Bridgeless === true && __DEV__) {
    console.warn(`Codegen didn't run for ${componentName}. This will be an error in the future. Make sure you are using @react-native/babel-preset when building your JavaScript code.`);
  }
  var componentNameInUse = options && options.paperComponentName != null ? options.paperComponentName : componentName;
  if (options != null && options.paperComponentNameDeprecated != null) {
    if (_UIManager.default.hasViewManagerConfig(componentName)) {
      componentNameInUse = componentName;
    } else if (options.paperComponentNameDeprecated != null && _UIManager.default.hasViewManagerConfig(options.paperComponentNameDeprecated)) {
      componentNameInUse = options.paperComponentNameDeprecated;
    } else {
      var _options$paperCompone;
      throw new Error(`Failed to find native component for either ${componentName} or ${(_options$paperCompone = options.paperComponentNameDeprecated) != null ? _options$paperCompone : '(unknown)'}`);
    }
  }
  return (0, _requireNativeComponent.default)(componentNameInUse);
}
var _default = exports.default = codegenNativeComponent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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