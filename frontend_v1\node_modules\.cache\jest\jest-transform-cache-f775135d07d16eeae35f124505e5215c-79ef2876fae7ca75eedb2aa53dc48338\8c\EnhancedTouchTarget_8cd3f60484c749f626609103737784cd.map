{"version": 3, "names": ["_vectorIcons", "require", "_react", "_interopRequireWildcard", "_reactNative", "_ThemeContext", "_accessibilityUtils", "_responsiveUtils", "_jsxRuntime", "_excluded", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_Dimensions$get", "Dimensions", "screenWidth", "width", "screenHeight", "height", "EnhancedTouchTarget", "exports", "_ref", "children", "onPress", "onLongPress", "onPressIn", "onPressOut", "_ref$disabled", "disabled", "style", "contentStyle", "accessibilityLabel", "accessibilityHint", "_ref$accessibilityRol", "accessibilityRole", "testID", "_ref$minimumSize", "minimumSize", "AccessibilityUtils", "WCAG_STANDARDS", "TOUCH_TARGETS", "MINIMUM_SIZE", "_ref$enforceMinimumSi", "enforceMinimumSize", "_ref$showTouchFeedbac", "showTouchFeedback", "touchFeedbackColor", "_ref$touchFeedbackOpa", "touchFeedbackOpacity", "_ref$enableHapticFeed", "enableHapticFeedback", "_ref$enableSoundFeedb", "enableSoundFeedback", "_ref$enableVisualFeed", "enableVisualFeedback", "_ref$feedbackDuration", "feedbackDuration", "_ref$enableSwipeGestu", "enableSwipeGestures", "onSwipeLeft", "onSwipeRight", "onSwipeUp", "onSwipeDown", "_ref$swipeThreshold", "swipe<PERSON><PERSON><PERSON><PERSON>", "_ref$autoFocus", "autoFocus", "_ref$focusable", "focusable", "tabIndex", "_useTheme", "useTheme", "colors", "_useState", "useState", "_useState2", "_slicedToArray2", "isPressed", "setIsPressed", "_useState3", "_useState4", "isFocused", "setIsFocused", "_useState5", "_useState6", "dimensions", "setDimensions", "touchRef", "useRef", "feedbackAnimation", "Animated", "Value", "current", "scaleAnimation", "getEffectiveTouchTargetStyle", "baseStyle", "min<PERSON><PERSON><PERSON>", "Math", "max", "minHeight", "horizontalPadding", "paddingHorizontal", "verticalPadding", "paddingVertical", "getFocusStyle", "borderWidth", "borderColor", "primary", "borderRadius", "shadowColor", "shadowOffset", "shadowOpacity", "shadowRadius", "elevation", "handlePressIn", "event", "parallel", "timing", "toValue", "duration", "useNativeDriver", "start", "Platform", "OS", "handlePressOut", "handlePress", "panResponder", "PanResponder", "create", "onStartShouldSetPanResponder", "onMoveShouldSetPanResponder", "onPanResponderMove", "onPanResponderRelease", "evt", "gestureState", "dx", "dy", "absDx", "abs", "absDy", "useEffect", "setTimeout", "_touchRef$current", "focus", "handleLayout", "getAccessibilityProps", "props", "accessible", "accessibilityState", "selected", "undefined", "renderTouchFeedback", "feedbackColor", "jsx", "View", "StyleSheet", "absoluteFillObject", "backgroundColor", "opacity", "interpolate", "inputRange", "outputRange", "pointerEvents", "renderSizeIndicator", "__DEV__", "meetsMinimumSize", "styles", "sizeIndicator", "jsxs", "Text", "sizeIndicatorText", "round", "combinedStyle", "touchableProps", "assign", "ref", "onFocus", "onBlur", "panHandlers", "TouchableOpacity", "transform", "scale", "onLayout", "AccessibleIconButton", "_ref2", "iconName", "_ref2$iconSize", "iconSize", "iconColor", "label", "_ref2$showLabel", "showLabel", "_ref2$labelPosition", "labelPosition", "touchTargetProps", "_objectWithoutProperties2", "_useTheme2", "finalIconColor", "text", "renderIcon", "Ionicons", "name", "size", "color", "renderLabel", "iconButtonLabel", "secondary", "marginTop", "marginBottom", "marginLeft", "marginRight", "getContentStyle", "isHorizontal", "flexDirection", "alignItems", "justifyContent", "position", "top", "right", "zIndex", "fontSize", "fontWeight", "getResponsiveFontSize", "textAlign"], "sources": ["EnhancedTouchTarget.tsx"], "sourcesContent": ["/**\n * Enhanced Touch Target Component\n * Ensures all interactive elements meet WCAG 2.1 AA touch target requirements (44x44px minimum)\n */\n\nimport { Ionicons } from '@expo/vector-icons';\nimport React, { useState, useRef, useEffect } from 'react';\nimport {\n  TouchableOpacity,\n  View,\n  Text,\n  Dimensions,\n  Platform,\n  StyleSheet,\n  ViewStyle,\n  TextStyle,\n  GestureResponderEvent,\n  PanResponder,\n  Animated,\n} from 'react-native';\n\nimport { useTheme } from '../../contexts/ThemeContext';\nimport { AccessibilityUtils } from '../../utils/accessibilityUtils';\nimport {\n  getResponsiveSpacing,\n  getResponsiveFontSize,\n} from '../../utils/responsiveUtils';\n\nconst { width: screenWidth, height: screenHeight } = Dimensions.get('window');\n\nexport interface EnhancedTouchTargetProps {\n  children: React.ReactNode;\n  onPress?: (event: GestureResponderEvent) => void;\n  onLongPress?: (event: GestureResponderEvent) => void;\n  onPressIn?: (event: GestureResponderEvent) => void;\n  onPressOut?: (event: GestureResponderEvent) => void;\n  disabled?: boolean;\n  style?: ViewStyle;\n  contentStyle?: ViewStyle;\n  accessibilityLabel?: string;\n  accessibilityHint?: string;\n  accessibilityRole?: string;\n  testID?: string;\n\n  // WCAG 2.1 AA specific props\n  minimumSize?: number; // Default 44px\n  enforceMinimumSize?: boolean;\n  showTouchFeedback?: boolean;\n  touchFeedbackColor?: string;\n  touchFeedbackOpacity?: number;\n\n  // Enhanced accessibility props\n  enableHapticFeedback?: boolean;\n  enableSoundFeedback?: boolean;\n  enableVisualFeedback?: boolean;\n  feedbackDuration?: number;\n\n  // Gesture support\n  enableSwipeGestures?: boolean;\n  onSwipeLeft?: () => void;\n  onSwipeRight?: () => void;\n  onSwipeUp?: () => void;\n  onSwipeDown?: () => void;\n  swipeThreshold?: number;\n\n  // Focus management\n  autoFocus?: boolean;\n  focusable?: boolean;\n  tabIndex?: number;\n}\n\nexport const EnhancedTouchTarget: React.FC<EnhancedTouchTargetProps> = ({\n  children,\n  onPress,\n  onLongPress,\n  onPressIn,\n  onPressOut,\n  disabled = false,\n  style,\n  contentStyle,\n  accessibilityLabel,\n  accessibilityHint,\n  accessibilityRole = 'button',\n  testID,\n\n  // WCAG props\n  minimumSize = AccessibilityUtils.WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE,\n  enforceMinimumSize = true,\n  showTouchFeedback = true,\n  touchFeedbackColor,\n  touchFeedbackOpacity = 0.1,\n\n  // Enhanced accessibility\n  enableHapticFeedback = true,\n  enableSoundFeedback = false,\n  enableVisualFeedback = true,\n  feedbackDuration = 150,\n\n  // Gesture support\n  enableSwipeGestures = false,\n  onSwipeLeft,\n  onSwipeRight,\n  onSwipeUp,\n  onSwipeDown,\n  swipeThreshold = 50,\n\n  // Focus management\n  autoFocus = false,\n  focusable = true,\n  tabIndex,\n}) => {\n  const { colors } = useTheme();\n  const [isPressed, setIsPressed] = useState(false);\n  const [isFocused, setIsFocused] = useState(false);\n  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });\n\n  const touchRef = useRef<TouchableOpacity>(null);\n  const feedbackAnimation = useRef(new Animated.Value(0)).current;\n  const scaleAnimation = useRef(new Animated.Value(1)).current;\n\n  // Calculate effective touch target size\n  const getEffectiveTouchTargetStyle = (): ViewStyle => {\n    const baseStyle: ViewStyle = {};\n\n    if (enforceMinimumSize) {\n      const minWidth = Math.max(dimensions.width, minimumSize);\n      const minHeight = Math.max(dimensions.height, minimumSize);\n\n      baseStyle.minWidth = minWidth;\n      baseStyle.minHeight = minHeight;\n\n      // Add padding if content is smaller than minimum size\n      if (dimensions.width < minimumSize) {\n        const horizontalPadding = (minimumSize - dimensions.width) / 2;\n        baseStyle.paddingHorizontal = horizontalPadding;\n      }\n\n      if (dimensions.height < minimumSize) {\n        const verticalPadding = (minimumSize - dimensions.height) / 2;\n        baseStyle.paddingVertical = verticalPadding;\n      }\n    }\n\n    return baseStyle;\n  };\n\n  // Get focus styles\n  const getFocusStyle = (): ViewStyle => {\n    if (!isFocused) return {};\n\n    return {\n      borderWidth: 2,\n      borderColor: colors.primary.default,\n      borderRadius: 4,\n      shadowColor: colors.primary.default,\n      shadowOffset: { width: 0, height: 0 },\n      shadowOpacity: 0.3,\n      shadowRadius: 4,\n      elevation: 4,\n    };\n  };\n\n  // Handle press feedback\n  const handlePressIn = (event: GestureResponderEvent) => {\n    setIsPressed(true);\n\n    if (enableVisualFeedback) {\n      Animated.parallel([\n        Animated.timing(feedbackAnimation, {\n          toValue: 1,\n          duration: feedbackDuration,\n          useNativeDriver: false,\n        }),\n        Animated.timing(scaleAnimation, {\n          toValue: 0.95,\n          duration: feedbackDuration,\n          useNativeDriver: true,\n        }),\n      ]).start();\n    }\n\n    if (enableHapticFeedback && Platform.OS === 'ios') {\n      // Add haptic feedback for iOS\n      // HapticFeedback.impact(HapticFeedback.ImpactFeedbackStyle.Light);\n    }\n\n    onPressIn?.(event);\n  };\n\n  const handlePressOut = (event: GestureResponderEvent) => {\n    setIsPressed(false);\n\n    if (enableVisualFeedback) {\n      Animated.parallel([\n        Animated.timing(feedbackAnimation, {\n          toValue: 0,\n          duration: feedbackDuration,\n          useNativeDriver: false,\n        }),\n        Animated.timing(scaleAnimation, {\n          toValue: 1,\n          duration: feedbackDuration,\n          useNativeDriver: true,\n        }),\n      ]).start();\n    }\n\n    onPressOut?.(event);\n  };\n\n  const handlePress = (event: GestureResponderEvent) => {\n    if (enableHapticFeedback && Platform.OS === 'ios') {\n      // Add haptic feedback for iOS\n      // HapticFeedback.impact(HapticFeedback.ImpactFeedbackStyle.Medium);\n    }\n\n    onPress?.(event);\n  };\n\n  // Swipe gesture handler\n  const panResponder = PanResponder.create({\n    onStartShouldSetPanResponder: () => enableSwipeGestures,\n    onMoveShouldSetPanResponder: () => enableSwipeGestures,\n    onPanResponderMove: () => {},\n    onPanResponderRelease: (evt, gestureState) => {\n      if (!enableSwipeGestures) return;\n\n      const { dx, dy } = gestureState;\n      const absDx = Math.abs(dx);\n      const absDy = Math.abs(dy);\n\n      if (absDx > swipeThreshold || absDy > swipeThreshold) {\n        if (absDx > absDy) {\n          // Horizontal swipe\n          if (dx > 0) {\n            onSwipeRight?.();\n          } else {\n            onSwipeLeft?.();\n          }\n        } else {\n          // Vertical swipe\n          if (dy > 0) {\n            onSwipeDown?.();\n          } else {\n            onSwipeUp?.();\n          }\n        }\n      }\n    },\n  });\n\n  // Auto-focus effect\n  useEffect(() => {\n    if (autoFocus && touchRef.current) {\n      setTimeout(() => {\n        touchRef.current?.focus?.();\n        setIsFocused(true);\n      }, 100);\n    }\n  }, [autoFocus]);\n\n  // Temporarily disable layout handling to prevent infinite loops\n  // TODO: Re-enable once infinite loop is fixed\n  const handleLayout = (event: any) => {\n    // Disabled to prevent infinite loops\n    // const { width, height } = event.nativeEvent.layout;\n    // setDimensions({ width, height });\n  };\n\n  // Get accessibility props\n  const getAccessibilityProps = () => {\n    const props: any = {\n      accessible: true,\n      accessibilityRole,\n      accessibilityLabel,\n      accessibilityHint,\n      accessibilityState: {\n        disabled,\n        selected: isPressed,\n      },\n    };\n\n    if (Platform.OS === 'web' && tabIndex !== undefined) {\n      props.tabIndex = disabled ? -1 : tabIndex;\n    }\n\n    return props;\n  };\n\n  // Render touch feedback overlay\n  const renderTouchFeedback = () => {\n    if (!showTouchFeedback || !enableVisualFeedback) return null;\n\n    const feedbackColor = touchFeedbackColor || colors.primary.default;\n\n    return (\n      <Animated.View\n        style={[\n          StyleSheet.absoluteFillObject,\n          {\n            backgroundColor: feedbackColor,\n            opacity: feedbackAnimation.interpolate({\n              inputRange: [0, 1],\n              outputRange: [0, touchFeedbackOpacity],\n            }),\n            borderRadius: 4,\n          },\n        ]}\n        pointerEvents=\"none\"\n      />\n    );\n  };\n\n  // Render size indicator for development\n  const renderSizeIndicator = () => {\n    if (!__DEV__ || !enforceMinimumSize) return null;\n\n    const meetsMinimumSize =\n      dimensions.width >= minimumSize && dimensions.height >= minimumSize;\n\n    return (\n      <View\n        style={[\n          styles.sizeIndicator,\n          {\n            backgroundColor: meetsMinimumSize ? '#4CAF50' : '#FF6B6B',\n          },\n        ]}>\n        <Text style={styles.sizeIndicatorText}>\n          {Math.round(dimensions.width)}x{Math.round(dimensions.height)}\n        </Text>\n      </View>\n    );\n  };\n\n  const combinedStyle = [\n    getEffectiveTouchTargetStyle(),\n    getFocusStyle(),\n    style,\n    {\n      opacity: disabled ? 0.6 : 1,\n    },\n  ];\n\n  const touchableProps = {\n    ref: touchRef,\n    onPress: handlePress,\n    onLongPress,\n    onPressIn: handlePressIn,\n    onPressOut: handlePressOut,\n    disabled,\n    style: combinedStyle,\n    testID,\n    onFocus: () => setIsFocused(true),\n    onBlur: () => setIsFocused(false),\n    ...getAccessibilityProps(),\n    ...(enableSwipeGestures ? panResponder.panHandlers : {}),\n  };\n\n  return (\n    <TouchableOpacity {...touchableProps}>\n      <Animated.View\n        style={[\n          contentStyle,\n          {\n            transform: [{ scale: scaleAnimation }],\n          },\n        ]}\n        onLayout={handleLayout}>\n        {children}\n        {renderTouchFeedback()}\n        {renderSizeIndicator()}\n      </Animated.View>\n    </TouchableOpacity>\n  );\n};\n\n// Convenience component for icon buttons\nexport interface AccessibleIconButtonProps\n  extends Omit<EnhancedTouchTargetProps, 'children'> {\n  iconName: string;\n  iconSize?: number;\n  iconColor?: string;\n  label: string;\n  showLabel?: boolean;\n  labelPosition?: 'bottom' | 'right' | 'left' | 'top';\n}\n\nexport const AccessibleIconButton: React.FC<AccessibleIconButtonProps> = ({\n  iconName,\n  iconSize = 24,\n  iconColor,\n  label,\n  showLabel = false,\n  labelPosition = 'bottom',\n  ...touchTargetProps\n}) => {\n  const { colors } = useTheme();\n  const finalIconColor = iconColor || colors.text.primary;\n\n  const renderIcon = () => (\n    <Ionicons name={iconName as any} size={iconSize} color={finalIconColor} />\n  );\n\n  const renderLabel = () => {\n    if (!showLabel) return null;\n\n    return (\n      <Text\n        style={[\n          styles.iconButtonLabel,\n          {\n            color: colors.text.secondary,\n            marginTop: labelPosition === 'bottom' ? 4 : 0,\n            marginBottom: labelPosition === 'top' ? 4 : 0,\n            marginLeft: labelPosition === 'right' ? 4 : 0,\n            marginRight: labelPosition === 'left' ? 4 : 0,\n          },\n        ]}>\n        {label}\n      </Text>\n    );\n  };\n\n  const getContentStyle = (): ViewStyle => {\n    const isHorizontal = labelPosition === 'left' || labelPosition === 'right';\n\n    return {\n      flexDirection: isHorizontal ? 'row' : 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n    };\n  };\n\n  return (\n    <EnhancedTouchTarget\n      {...touchTargetProps}\n      accessibilityLabel={touchTargetProps.accessibilityLabel || label}\n      contentStyle={getContentStyle()}>\n      {labelPosition === 'top' && renderLabel()}\n      {labelPosition === 'left' && renderLabel()}\n      {renderIcon()}\n      {labelPosition === 'right' && renderLabel()}\n      {labelPosition === 'bottom' && renderLabel()}\n    </EnhancedTouchTarget>\n  );\n};\n\nconst styles = StyleSheet.create({\n  sizeIndicator: {\n    position: 'absolute',\n    top: -15,\n    right: -5,\n    paddingHorizontal: 4,\n    paddingVertical: 2,\n    borderRadius: 4,\n    zIndex: 1000,\n  },\n  sizeIndicatorText: {\n    color: '#FFFFFF',\n    fontSize: 8,\n    fontWeight: 'bold',\n  },\n  iconButtonLabel: {\n    fontSize: getResponsiveFontSize(12),\n    textAlign: 'center',\n  },\n});\n"], "mappings": ";;;;;;;AAKA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,YAAA,GAAAH,OAAA;AAcA,IAAAI,aAAA,GAAAJ,OAAA;AACA,IAAAK,mBAAA,GAAAL,OAAA;AACA,IAAAM,gBAAA,GAAAN,OAAA;AAGqC,IAAAO,WAAA,GAAAP,OAAA;AAAA,IAAAQ,SAAA;AAAA,SAAAN,wBAAAO,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAT,uBAAA,YAAAA,wBAAAO,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAErC,IAAAmB,eAAA,GAAqDC,uBAAU,CAACT,GAAG,CAAC,QAAQ,CAAC;EAA9DU,WAAW,GAAAF,eAAA,CAAlBG,KAAK;EAAuBC,YAAY,GAAAJ,eAAA,CAApBK,MAAM;AA2C3B,IAAMC,mBAAuD,GAAAC,OAAA,CAAAD,mBAAA,GAAG,SAA1DA,mBAAuDA,CAAAE,IAAA,EAuC9D;EAAA,IAtCJC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IACRC,OAAO,GAAAF,IAAA,CAAPE,OAAO;IACPC,WAAW,GAAAH,IAAA,CAAXG,WAAW;IACXC,SAAS,GAAAJ,IAAA,CAATI,SAAS;IACTC,UAAU,GAAAL,IAAA,CAAVK,UAAU;IAAAC,aAAA,GAAAN,IAAA,CACVO,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,KAAK,GAAAA,aAAA;IAChBE,KAAK,GAAAR,IAAA,CAALQ,KAAK;IACLC,YAAY,GAAAT,IAAA,CAAZS,YAAY;IACZC,kBAAkB,GAAAV,IAAA,CAAlBU,kBAAkB;IAClBC,iBAAiB,GAAAX,IAAA,CAAjBW,iBAAiB;IAAAC,qBAAA,GAAAZ,IAAA,CACjBa,iBAAiB;IAAjBA,iBAAiB,GAAAD,qBAAA,cAAG,QAAQ,GAAAA,qBAAA;IAC5BE,MAAM,GAAAd,IAAA,CAANc,MAAM;IAAAC,gBAAA,GAAAf,IAAA,CAGNgB,WAAW;IAAXA,WAAW,GAAAD,gBAAA,cAAGE,sCAAkB,CAACC,cAAc,CAACC,aAAa,CAACC,YAAY,GAAAL,gBAAA;IAAAM,qBAAA,GAAArB,IAAA,CAC1EsB,kBAAkB;IAAlBA,kBAAkB,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IAAAE,qBAAA,GAAAvB,IAAA,CACzBwB,iBAAiB;IAAjBA,iBAAiB,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IACxBE,kBAAkB,GAAAzB,IAAA,CAAlByB,kBAAkB;IAAAC,qBAAA,GAAA1B,IAAA,CAClB2B,oBAAoB;IAApBA,oBAAoB,GAAAD,qBAAA,cAAG,GAAG,GAAAA,qBAAA;IAAAE,qBAAA,GAAA5B,IAAA,CAG1B6B,oBAAoB;IAApBA,oBAAoB,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IAAAE,qBAAA,GAAA9B,IAAA,CAC3B+B,mBAAmB;IAAnBA,mBAAmB,GAAAD,qBAAA,cAAG,KAAK,GAAAA,qBAAA;IAAAE,qBAAA,GAAAhC,IAAA,CAC3BiC,oBAAoB;IAApBA,oBAAoB,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IAAAE,qBAAA,GAAAlC,IAAA,CAC3BmC,gBAAgB;IAAhBA,gBAAgB,GAAAD,qBAAA,cAAG,GAAG,GAAAA,qBAAA;IAAAE,qBAAA,GAAApC,IAAA,CAGtBqC,mBAAmB;IAAnBA,mBAAmB,GAAAD,qBAAA,cAAG,KAAK,GAAAA,qBAAA;IAC3BE,WAAW,GAAAtC,IAAA,CAAXsC,WAAW;IACXC,YAAY,GAAAvC,IAAA,CAAZuC,YAAY;IACZC,SAAS,GAAAxC,IAAA,CAATwC,SAAS;IACTC,WAAW,GAAAzC,IAAA,CAAXyC,WAAW;IAAAC,mBAAA,GAAA1C,IAAA,CACX2C,cAAc;IAAdA,cAAc,GAAAD,mBAAA,cAAG,EAAE,GAAAA,mBAAA;IAAAE,cAAA,GAAA5C,IAAA,CAGnB6C,SAAS;IAATA,SAAS,GAAAD,cAAA,cAAG,KAAK,GAAAA,cAAA;IAAAE,cAAA,GAAA9C,IAAA,CACjB+C,SAAS;IAATA,SAAS,GAAAD,cAAA,cAAG,IAAI,GAAAA,cAAA;IAChBE,QAAQ,GAAAhD,IAAA,CAARgD,QAAQ;EAER,IAAAC,SAAA,GAAmB,IAAAC,sBAAQ,EAAC,CAAC;IAArBC,MAAM,GAAAF,SAAA,CAANE,MAAM;EACd,IAAAC,SAAA,GAAkC,IAAAC,eAAQ,EAAC,KAAK,CAAC;IAAAC,UAAA,OAAAC,eAAA,CAAAzE,OAAA,EAAAsE,SAAA;IAA1CI,SAAS,GAAAF,UAAA;IAAEG,YAAY,GAAAH,UAAA;EAC9B,IAAAI,UAAA,GAAkC,IAAAL,eAAQ,EAAC,KAAK,CAAC;IAAAM,UAAA,OAAAJ,eAAA,CAAAzE,OAAA,EAAA4E,UAAA;IAA1CE,SAAS,GAAAD,UAAA;IAAEE,YAAY,GAAAF,UAAA;EAC9B,IAAAG,UAAA,GAAoC,IAAAT,eAAQ,EAAC;MAAE1D,KAAK,EAAE,CAAC;MAAEE,MAAM,EAAE;IAAE,CAAC,CAAC;IAAAkE,UAAA,OAAAR,eAAA,CAAAzE,OAAA,EAAAgF,UAAA;IAA9DE,UAAU,GAAAD,UAAA;IAAEE,aAAa,GAAAF,UAAA;EAEhC,IAAMG,QAAQ,GAAG,IAAAC,aAAM,EAAmB,IAAI,CAAC;EAC/C,IAAMC,iBAAiB,GAAG,IAAAD,aAAM,EAAC,IAAIE,qBAAQ,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO;EAC/D,IAAMC,cAAc,GAAG,IAAAL,aAAM,EAAC,IAAIE,qBAAQ,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO;EAG5D,IAAME,4BAA4B,GAAG,SAA/BA,4BAA4BA,CAAA,EAAoB;IACpD,IAAMC,SAAoB,GAAG,CAAC,CAAC;IAE/B,IAAIpD,kBAAkB,EAAE;MACtB,IAAMqD,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACb,UAAU,CAACrE,KAAK,EAAEqB,WAAW,CAAC;MACxD,IAAM8D,SAAS,GAAGF,IAAI,CAACC,GAAG,CAACb,UAAU,CAACnE,MAAM,EAAEmB,WAAW,CAAC;MAE1D0D,SAAS,CAACC,QAAQ,GAAGA,QAAQ;MAC7BD,SAAS,CAACI,SAAS,GAAGA,SAAS;MAG/B,IAAId,UAAU,CAACrE,KAAK,GAAGqB,WAAW,EAAE;QAClC,IAAM+D,iBAAiB,GAAG,CAAC/D,WAAW,GAAGgD,UAAU,CAACrE,KAAK,IAAI,CAAC;QAC9D+E,SAAS,CAACM,iBAAiB,GAAGD,iBAAiB;MACjD;MAEA,IAAIf,UAAU,CAACnE,MAAM,GAAGmB,WAAW,EAAE;QACnC,IAAMiE,eAAe,GAAG,CAACjE,WAAW,GAAGgD,UAAU,CAACnE,MAAM,IAAI,CAAC;QAC7D6E,SAAS,CAACQ,eAAe,GAAGD,eAAe;MAC7C;IACF;IAEA,OAAOP,SAAS;EAClB,CAAC;EAGD,IAAMS,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAoB;IACrC,IAAI,CAACvB,SAAS,EAAE,OAAO,CAAC,CAAC;IAEzB,OAAO;MACLwB,WAAW,EAAE,CAAC;MACdC,WAAW,EAAElC,MAAM,CAACmC,OAAO,CAACxG,OAAO;MACnCyG,YAAY,EAAE,CAAC;MACfC,WAAW,EAAErC,MAAM,CAACmC,OAAO,CAACxG,OAAO;MACnC2G,YAAY,EAAE;QAAE9F,KAAK,EAAE,CAAC;QAAEE,MAAM,EAAE;MAAE,CAAC;MACrC6F,aAAa,EAAE,GAAG;MAClBC,YAAY,EAAE,CAAC;MACfC,SAAS,EAAE;IACb,CAAC;EACH,CAAC;EAGD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,KAA4B,EAAK;IACtDrC,YAAY,CAAC,IAAI,CAAC;IAElB,IAAIxB,oBAAoB,EAAE;MACxBoC,qBAAQ,CAAC0B,QAAQ,CAAC,CAChB1B,qBAAQ,CAAC2B,MAAM,CAAC5B,iBAAiB,EAAE;QACjC6B,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE/D,gBAAgB;QAC1BgE,eAAe,EAAE;MACnB,CAAC,CAAC,EACF9B,qBAAQ,CAAC2B,MAAM,CAACxB,cAAc,EAAE;QAC9ByB,OAAO,EAAE,IAAI;QACbC,QAAQ,EAAE/D,gBAAgB;QAC1BgE,eAAe,EAAE;MACnB,CAAC,CAAC,CACH,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ;IAEA,IAAIvE,oBAAoB,IAAIwE,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE,CAGnD;IAEAlG,SAAS,YAATA,SAAS,CAAG0F,KAAK,CAAC;EACpB,CAAC;EAED,IAAMS,cAAc,GAAG,SAAjBA,cAAcA,CAAIT,KAA4B,EAAK;IACvDrC,YAAY,CAAC,KAAK,CAAC;IAEnB,IAAIxB,oBAAoB,EAAE;MACxBoC,qBAAQ,CAAC0B,QAAQ,CAAC,CAChB1B,qBAAQ,CAAC2B,MAAM,CAAC5B,iBAAiB,EAAE;QACjC6B,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE/D,gBAAgB;QAC1BgE,eAAe,EAAE;MACnB,CAAC,CAAC,EACF9B,qBAAQ,CAAC2B,MAAM,CAACxB,cAAc,EAAE;QAC9ByB,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE/D,gBAAgB;QAC1BgE,eAAe,EAAE;MACnB,CAAC,CAAC,CACH,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ;IAEA/F,UAAU,YAAVA,UAAU,CAAGyF,KAAK,CAAC;EACrB,CAAC;EAED,IAAMU,WAAW,GAAG,SAAdA,WAAWA,CAAIV,KAA4B,EAAK;IACpD,IAAIjE,oBAAoB,IAAIwE,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE,CAGnD;IAEApG,OAAO,YAAPA,OAAO,CAAG4F,KAAK,CAAC;EAClB,CAAC;EAGD,IAAMW,YAAY,GAAGC,yBAAY,CAACC,MAAM,CAAC;IACvCC,4BAA4B,EAAE,SAA9BA,4BAA4BA,CAAA;MAAA,OAAQvE,mBAAmB;IAAA;IACvDwE,2BAA2B,EAAE,SAA7BA,2BAA2BA,CAAA;MAAA,OAAQxE,mBAAmB;IAAA;IACtDyE,kBAAkB,EAAE,SAApBA,kBAAkBA,CAAA,EAAQ,CAAC,CAAC;IAC5BC,qBAAqB,EAAE,SAAvBA,qBAAqBA,CAAGC,GAAG,EAAEC,YAAY,EAAK;MAC5C,IAAI,CAAC5E,mBAAmB,EAAE;MAE1B,IAAQ6E,EAAE,GAASD,YAAY,CAAvBC,EAAE;QAAEC,EAAE,GAAKF,YAAY,CAAnBE,EAAE;MACd,IAAMC,KAAK,GAAGxC,IAAI,CAACyC,GAAG,CAACH,EAAE,CAAC;MAC1B,IAAMI,KAAK,GAAG1C,IAAI,CAACyC,GAAG,CAACF,EAAE,CAAC;MAE1B,IAAIC,KAAK,GAAGzE,cAAc,IAAI2E,KAAK,GAAG3E,cAAc,EAAE;QACpD,IAAIyE,KAAK,GAAGE,KAAK,EAAE;UAEjB,IAAIJ,EAAE,GAAG,CAAC,EAAE;YACV3E,YAAY,YAAZA,YAAY,CAAG,CAAC;UAClB,CAAC,MAAM;YACLD,WAAW,YAAXA,WAAW,CAAG,CAAC;UACjB;QACF,CAAC,MAAM;UAEL,IAAI6E,EAAE,GAAG,CAAC,EAAE;YACV1E,WAAW,YAAXA,WAAW,CAAG,CAAC;UACjB,CAAC,MAAM;YACLD,SAAS,YAATA,SAAS,CAAG,CAAC;UACf;QACF;MACF;IACF;EACF,CAAC,CAAC;EAGF,IAAA+E,gBAAS,EAAC,YAAM;IACd,IAAI1E,SAAS,IAAIqB,QAAQ,CAACK,OAAO,EAAE;MACjCiD,UAAU,CAAC,YAAM;QAAA,IAAAC,iBAAA;QACf,CAAAA,iBAAA,GAAAvD,QAAQ,CAACK,OAAO,aAAhBkD,iBAAA,CAAkBC,KAAK,YAAvBD,iBAAA,CAAkBC,KAAK,CAAG,CAAC;QAC3B7D,YAAY,CAAC,IAAI,CAAC;MACpB,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC,EAAE,CAAChB,SAAS,CAAC,CAAC;EAIf,IAAM8E,YAAY,GAAG,SAAfA,YAAYA,CAAI7B,KAAU,EAAK,CAIrC,CAAC;EAGD,IAAM8B,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAA,EAAS;IAClC,IAAMC,KAAU,GAAG;MACjBC,UAAU,EAAE,IAAI;MAChBjH,iBAAiB,EAAjBA,iBAAiB;MACjBH,kBAAkB,EAAlBA,kBAAkB;MAClBC,iBAAiB,EAAjBA,iBAAiB;MACjBoH,kBAAkB,EAAE;QAClBxH,QAAQ,EAARA,QAAQ;QACRyH,QAAQ,EAAExE;MACZ;IACF,CAAC;IAED,IAAI6C,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAItD,QAAQ,KAAKiF,SAAS,EAAE;MACnDJ,KAAK,CAAC7E,QAAQ,GAAGzC,QAAQ,GAAG,CAAC,CAAC,GAAGyC,QAAQ;IAC3C;IAEA,OAAO6E,KAAK;EACd,CAAC;EAGD,IAAMK,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAAS;IAChC,IAAI,CAAC1G,iBAAiB,IAAI,CAACS,oBAAoB,EAAE,OAAO,IAAI;IAE5D,IAAMkG,aAAa,GAAG1G,kBAAkB,IAAI0B,MAAM,CAACmC,OAAO,CAACxG,OAAO;IAElE,OACE,IAAAZ,WAAA,CAAAkK,GAAA,EAACtK,YAAA,CAAAuG,QAAQ,CAACgE,IAAI;MACZ7H,KAAK,EAAE,CACL8H,uBAAU,CAACC,kBAAkB,EAC7B;QACEC,eAAe,EAAEL,aAAa;QAC9BM,OAAO,EAAErE,iBAAiB,CAACsE,WAAW,CAAC;UACrCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAClBC,WAAW,EAAE,CAAC,CAAC,EAAEjH,oBAAoB;QACvC,CAAC,CAAC;QACF4D,YAAY,EAAE;MAChB,CAAC,CACD;MACFsD,aAAa,EAAC;IAAM,CACrB,CAAC;EAEN,CAAC;EAGD,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAAS;IAChC,IAAI,CAACC,OAAO,IAAI,CAACzH,kBAAkB,EAAE,OAAO,IAAI;IAEhD,IAAM0H,gBAAgB,GACpBhF,UAAU,CAACrE,KAAK,IAAIqB,WAAW,IAAIgD,UAAU,CAACnE,MAAM,IAAImB,WAAW;IAErE,OACE,IAAA9C,WAAA,CAAAkK,GAAA,EAACtK,YAAA,CAAAuK,IAAI;MACH7H,KAAK,EAAE,CACLyI,MAAM,CAACC,aAAa,EACpB;QACEV,eAAe,EAAEQ,gBAAgB,GAAG,SAAS,GAAG;MAClD,CAAC,CACD;MAAA/I,QAAA,EACF,IAAA/B,WAAA,CAAAiL,IAAA,EAACrL,YAAA,CAAAsL,IAAI;QAAC5I,KAAK,EAAEyI,MAAM,CAACI,iBAAkB;QAAApJ,QAAA,GACnC2E,IAAI,CAAC0E,KAAK,CAACtF,UAAU,CAACrE,KAAK,CAAC,EAAC,GAAC,EAACiF,IAAI,CAAC0E,KAAK,CAACtF,UAAU,CAACnE,MAAM,CAAC;MAAA,CACzD;IAAC,CACH,CAAC;EAEX,CAAC;EAED,IAAM0J,aAAa,GAAG,CACpB9E,4BAA4B,CAAC,CAAC,EAC9BU,aAAa,CAAC,CAAC,EACf3E,KAAK,EACL;IACEiI,OAAO,EAAElI,QAAQ,GAAG,GAAG,GAAG;EAC5B,CAAC,CACF;EAED,IAAMiJ,cAAc,GAAAnK,MAAA,CAAAoK,MAAA;IAClBC,GAAG,EAAExF,QAAQ;IACbhE,OAAO,EAAEsG,WAAW;IACpBrG,WAAW,EAAXA,WAAW;IACXC,SAAS,EAAEyF,aAAa;IACxBxF,UAAU,EAAEkG,cAAc;IAC1BhG,QAAQ,EAARA,QAAQ;IACRC,KAAK,EAAE+I,aAAa;IACpBzI,MAAM,EAANA,MAAM;IACN6I,OAAO,EAAE,SAATA,OAAOA,CAAA;MAAA,OAAQ9F,YAAY,CAAC,IAAI,CAAC;IAAA;IACjC+F,MAAM,EAAE,SAARA,MAAMA,CAAA;MAAA,OAAQ/F,YAAY,CAAC,KAAK,CAAC;IAAA;EAAA,GAC9B+D,qBAAqB,CAAC,CAAC,EACtBvF,mBAAmB,GAAGoE,YAAY,CAACoD,WAAW,GAAG,CAAC,CAAC,CACxD;EAED,OACE,IAAA3L,WAAA,CAAAkK,GAAA,EAACtK,YAAA,CAAAgM,gBAAgB,EAAAzK,MAAA,CAAAoK,MAAA,KAAKD,cAAc;IAAAvJ,QAAA,EAClC,IAAA/B,WAAA,CAAAiL,IAAA,EAACrL,YAAA,CAAAuG,QAAQ,CAACgE,IAAI;MACZ7H,KAAK,EAAE,CACLC,YAAY,EACZ;QACEsJ,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAExF;QAAe,CAAC;MACvC,CAAC,CACD;MACFyF,QAAQ,EAAEtC,YAAa;MAAA1H,QAAA,GACtBA,QAAQ,EACRiI,mBAAmB,CAAC,CAAC,EACrBY,mBAAmB,CAAC,CAAC;IAAA,CACT;EAAC,EACA,CAAC;AAEvB,CAAC;AAaM,IAAMoB,oBAAyD,GAAAnK,OAAA,CAAAmK,oBAAA,GAAG,SAA5DA,oBAAyDA,CAAAC,KAAA,EAQhE;EAAA,IAPJC,QAAQ,GAAAD,KAAA,CAARC,QAAQ;IAAAC,cAAA,GAAAF,KAAA,CACRG,QAAQ;IAARA,QAAQ,GAAAD,cAAA,cAAG,EAAE,GAAAA,cAAA;IACbE,SAAS,GAAAJ,KAAA,CAATI,SAAS;IACTC,KAAK,GAAAL,KAAA,CAALK,KAAK;IAAAC,eAAA,GAAAN,KAAA,CACLO,SAAS;IAATA,SAAS,GAAAD,eAAA,cAAG,KAAK,GAAAA,eAAA;IAAAE,mBAAA,GAAAR,KAAA,CACjBS,aAAa;IAAbA,aAAa,GAAAD,mBAAA,cAAG,QAAQ,GAAAA,mBAAA;IACrBE,gBAAgB,OAAAC,yBAAA,CAAAhM,OAAA,EAAAqL,KAAA,EAAAhM,SAAA;EAEnB,IAAA4M,UAAA,GAAmB,IAAA7H,sBAAQ,EAAC,CAAC;IAArBC,MAAM,GAAA4H,UAAA,CAAN5H,MAAM;EACd,IAAM6H,cAAc,GAAGT,SAAS,IAAIpH,MAAM,CAAC8H,IAAI,CAAC3F,OAAO;EAEvD,IAAM4F,UAAU,GAAG,SAAbA,UAAUA,CAAA;IAAA,OACd,IAAAhN,WAAA,CAAAkK,GAAA,EAAC1K,YAAA,CAAAyN,QAAQ;MAACC,IAAI,EAAEhB,QAAgB;MAACiB,IAAI,EAAEf,QAAS;MAACgB,KAAK,EAAEN;IAAe,CAAE,CAAC;EAAA,CAC3E;EAED,IAAMO,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;IACxB,IAAI,CAACb,SAAS,EAAE,OAAO,IAAI;IAE3B,OACE,IAAAxM,WAAA,CAAAkK,GAAA,EAACtK,YAAA,CAAAsL,IAAI;MACH5I,KAAK,EAAE,CACLyI,MAAM,CAACuC,eAAe,EACtB;QACEF,KAAK,EAAEnI,MAAM,CAAC8H,IAAI,CAACQ,SAAS;QAC5BC,SAAS,EAAEd,aAAa,KAAK,QAAQ,GAAG,CAAC,GAAG,CAAC;QAC7Ce,YAAY,EAAEf,aAAa,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC;QAC7CgB,UAAU,EAAEhB,aAAa,KAAK,OAAO,GAAG,CAAC,GAAG,CAAC;QAC7CiB,WAAW,EAAEjB,aAAa,KAAK,MAAM,GAAG,CAAC,GAAG;MAC9C,CAAC,CACD;MAAA3K,QAAA,EACDuK;IAAK,CACF,CAAC;EAEX,CAAC;EAED,IAAMsB,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAoB;IACvC,IAAMC,YAAY,GAAGnB,aAAa,KAAK,MAAM,IAAIA,aAAa,KAAK,OAAO;IAE1E,OAAO;MACLoB,aAAa,EAAED,YAAY,GAAG,KAAK,GAAG,QAAQ;MAC9CE,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE;IAClB,CAAC;EACH,CAAC;EAED,OACE,IAAAhO,WAAA,CAAAiL,IAAA,EAACrJ,mBAAmB,EAAAT,MAAA,CAAAoK,MAAA,KACdoB,gBAAgB;IACpBnK,kBAAkB,EAAEmK,gBAAgB,CAACnK,kBAAkB,IAAI8J,KAAM;IACjE/J,YAAY,EAAEqL,eAAe,CAAC,CAAE;IAAA7L,QAAA,GAC/B2K,aAAa,KAAK,KAAK,IAAIW,WAAW,CAAC,CAAC,EACxCX,aAAa,KAAK,MAAM,IAAIW,WAAW,CAAC,CAAC,EACzCL,UAAU,CAAC,CAAC,EACZN,aAAa,KAAK,OAAO,IAAIW,WAAW,CAAC,CAAC,EAC1CX,aAAa,KAAK,QAAQ,IAAIW,WAAW,CAAC,CAAC;EAAA,EACzB,CAAC;AAE1B,CAAC;AAED,IAAMtC,MAAM,GAAGX,uBAAU,CAAC3B,MAAM,CAAC;EAC/BuC,aAAa,EAAE;IACbiD,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,CAAC,EAAE;IACRC,KAAK,EAAE,CAAC,CAAC;IACTrH,iBAAiB,EAAE,CAAC;IACpBE,eAAe,EAAE,CAAC;IAClBK,YAAY,EAAE,CAAC;IACf+G,MAAM,EAAE;EACV,CAAC;EACDjD,iBAAiB,EAAE;IACjBiC,KAAK,EAAE,SAAS;IAChBiB,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE;EACd,CAAC;EACDhB,eAAe,EAAE;IACfe,QAAQ,EAAE,IAAAE,sCAAqB,EAAC,EAAE,CAAC;IACnCC,SAAS,EAAE;EACb;AACF,CAAC,CAAC", "ignoreList": []}