c3c0eb4db7f7898d3ce51558da1799e3
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.paymentService = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _apiClient = require("./apiClient");
var PaymentService = function () {
  function PaymentService() {
    (0, _classCallCheck2.default)(this, PaymentService);
    this.baseUrl = '/api/payments';
  }
  return (0, _createClass2.default)(PaymentService, [{
    key: "getPaymentConfig",
    value: (function () {
      var _getPaymentConfig = (0, _asyncToGenerator2.default)(function* () {
        try {
          console.log('💳 PaymentService: Getting payment configuration');
          var response = yield _apiClient.apiClient.get(`${this.baseUrl}/config/`, {}, true, {
            enabled: true,
            ttl: 10 * 60 * 1000
          });
          console.log('✅ PaymentService: Payment configuration retrieved');
          return response.data;
        } catch (error) {
          console.error('❌ PaymentService: Failed to get payment config:', error);
          throw new Error('Failed to get payment configuration');
        }
      });
      function getPaymentConfig() {
        return _getPaymentConfig.apply(this, arguments);
      }
      return getPaymentConfig;
    }())
  }, {
    key: "createPaymentIntent",
    value: (function () {
      var _createPaymentIntent = (0, _asyncToGenerator2.default)(function* (bookingId, amount) {
        var currency = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'CAD';
        var paymentMethodId = arguments.length > 3 ? arguments[3] : undefined;
        try {
          console.log('💳 PaymentService: Creating payment intent for booking:', bookingId);
          var response = yield _apiClient.apiClient.post(`${this.baseUrl}/intents/`, {
            booking_id: bookingId,
            amount: amount,
            currency: currency,
            payment_method_id: paymentMethodId
          }, true);
          console.log('✅ PaymentService: Payment intent created:', response.data.id);
          return response.data;
        } catch (error) {
          console.error('❌ PaymentService: Failed to create payment intent:', error);
          throw new Error('Failed to create payment intent');
        }
      });
      function createPaymentIntent(_x, _x2) {
        return _createPaymentIntent.apply(this, arguments);
      }
      return createPaymentIntent;
    }())
  }, {
    key: "confirmPayment",
    value: (function () {
      var _confirmPayment = (0, _asyncToGenerator2.default)(function* (paymentIntentId, paymentMethodId) {
        try {
          var response = yield _apiClient.apiClient.post(`${this.baseUrl}/confirm/`, {
            payment_intent_id: paymentIntentId,
            payment_method_id: paymentMethodId
          });
          return response.data;
        } catch (error) {
          console.error('Failed to confirm payment:', error);
          throw new Error('Failed to confirm payment');
        }
      });
      function confirmPayment(_x3, _x4) {
        return _confirmPayment.apply(this, arguments);
      }
      return confirmPayment;
    }())
  }, {
    key: "getPaymentMethods",
    value: (function () {
      var _getPaymentMethods = (0, _asyncToGenerator2.default)(function* () {
        try {
          console.log('💳 PaymentService: Getting user payment methods');
          var response = yield _apiClient.apiClient.get(`${this.baseUrl}/methods/`, {}, true, {
            enabled: true,
            ttl: 5 * 60 * 1000
          });
          console.log('✅ PaymentService: Payment methods retrieved:', response.data.length);
          return response.data;
        } catch (error) {
          console.error('❌ PaymentService: Failed to get payment methods:', error);
          throw new Error('Failed to get payment methods');
        }
      });
      function getPaymentMethods() {
        return _getPaymentMethods.apply(this, arguments);
      }
      return getPaymentMethods;
    }())
  }, {
    key: "addPaymentMethod",
    value: (function () {
      var _addPaymentMethod = (0, _asyncToGenerator2.default)(function* (paymentMethodData) {
        try {
          console.log('💳 PaymentService: Adding new payment method');
          var response = yield _apiClient.apiClient.post(`${this.baseUrl}/methods/`, paymentMethodData, true);
          console.log('✅ PaymentService: Payment method added:', response.data.id);
          return response.data;
        } catch (error) {
          console.error('❌ PaymentService: Failed to add payment method:', error);
          throw new Error('Failed to add payment method');
        }
      });
      function addPaymentMethod(_x5) {
        return _addPaymentMethod.apply(this, arguments);
      }
      return addPaymentMethod;
    }())
  }, {
    key: "deletePaymentMethod",
    value: (function () {
      var _deletePaymentMethod = (0, _asyncToGenerator2.default)(function* (paymentMethodId) {
        try {
          console.log('💳 PaymentService: Deleting payment method:', paymentMethodId);
          yield _apiClient.apiClient.delete(`${this.baseUrl}/methods/${paymentMethodId}/`, {}, true);
          console.log('✅ PaymentService: Payment method deleted');
        } catch (error) {
          console.error('❌ PaymentService: Failed to delete payment method:', error);
          throw new Error('Failed to delete payment method');
        }
      });
      function deletePaymentMethod(_x6) {
        return _deletePaymentMethod.apply(this, arguments);
      }
      return deletePaymentMethod;
    }())
  }, {
    key: "setDefaultPaymentMethod",
    value: (function () {
      var _setDefaultPaymentMethod = (0, _asyncToGenerator2.default)(function* (paymentMethodId) {
        try {
          console.log('💳 PaymentService: Setting default payment method:', paymentMethodId);
          var response = yield _apiClient.apiClient.patch(`${this.baseUrl}/methods/${paymentMethodId}/set-default/`, {}, true);
          console.log('✅ PaymentService: Default payment method set');
          return response.data;
        } catch (error) {
          console.error('❌ PaymentService: Failed to set default payment method:', error);
          throw new Error('Failed to set default payment method');
        }
      });
      function setDefaultPaymentMethod(_x7) {
        return _setDefaultPaymentMethod.apply(this, arguments);
      }
      return setDefaultPaymentMethod;
    }())
  }, {
    key: "getTransactionHistory",
    value: (function () {
      var _getTransactionHistory = (0, _asyncToGenerator2.default)(function* () {
        var page = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 1;
        var limit = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 20;
        var filters = arguments.length > 2 ? arguments[2] : undefined;
        try {
          console.log('💳 PaymentService: Getting transaction history');
          var params = new URLSearchParams(Object.assign({
            page: page.toString(),
            limit: limit.toString()
          }, filters));
          var response = yield _apiClient.apiClient.get(`${this.baseUrl}/transactions/?${params}`, {}, true, {
            enabled: true,
            ttl: 2 * 60 * 1000
          });
          console.log('✅ PaymentService: Transaction history retrieved:', response.data.results.length);
          return response.data;
        } catch (error) {
          console.error('❌ PaymentService: Failed to get transaction history:', error);
          throw new Error('Failed to get transaction history');
        }
      });
      function getTransactionHistory() {
        return _getTransactionHistory.apply(this, arguments);
      }
      return getTransactionHistory;
    }())
  }, {
    key: "requestRefund",
    value: (function () {
      var _requestRefund = (0, _asyncToGenerator2.default)(function* (refundData) {
        try {
          var response = yield _apiClient.apiClient.post(`${this.baseUrl}/refunds/`, refundData);
          return response.data;
        } catch (error) {
          console.error('Failed to request refund:', error);
          throw new Error('Failed to request refund');
        }
      });
      function requestRefund(_x8) {
        return _requestRefund.apply(this, arguments);
      }
      return requestRefund;
    }())
  }, {
    key: "getRefundStatus",
    value: (function () {
      var _getRefundStatus = (0, _asyncToGenerator2.default)(function* (refundId) {
        try {
          var response = yield _apiClient.apiClient.get(`${this.baseUrl}/refunds/${refundId}/`);
          return response.data;
        } catch (error) {
          console.error('Failed to get refund status:', error);
          throw new Error('Failed to get refund status');
        }
      });
      function getRefundStatus(_x9) {
        return _getRefundStatus.apply(this, arguments);
      }
      return getRefundStatus;
    }())
  }, {
    key: "getPaymentHistory",
    value: (function () {
      var _getPaymentHistory = (0, _asyncToGenerator2.default)(function* () {
        var limit = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 20;
        var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
        try {
          var response = yield _apiClient.apiClient.get(`${this.baseUrl}/history/`, {
            params: {
              limit: limit,
              offset: offset
            }
          });
          return response.data;
        } catch (error) {
          console.error('Failed to get payment history:', error);
          throw new Error('Failed to get payment history');
        }
      });
      function getPaymentHistory() {
        return _getPaymentHistory.apply(this, arguments);
      }
      return getPaymentHistory;
    }())
  }, {
    key: "calculateBookingTotal",
    value: function calculateBookingTotal(basePrice) {
      var taxRate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0.13;
      var serviceFee = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;
      var discountAmount = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;
      var subtotal = basePrice;
      var tax = subtotal * taxRate;
      var total = subtotal + tax + serviceFee - discountAmount;
      return {
        subtotal: Math.round(subtotal * 100) / 100,
        tax: Math.round(tax * 100) / 100,
        serviceFee: Math.round(serviceFee * 100) / 100,
        discount: Math.round(discountAmount * 100) / 100,
        total: Math.round(total * 100) / 100
      };
    }
  }, {
    key: "validatePaymentAmount",
    value: function validatePaymentAmount(amount) {
      return amount > 0 && amount <= 10000;
    }
  }, {
    key: "formatCurrency",
    value: function formatCurrency(amount) {
      var currency = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'CAD';
      return new Intl.NumberFormat('en-CA', {
        style: 'currency',
        currency: currency
      }).format(amount);
    }
  }, {
    key: "getPaymentMethodDisplayName",
    value: function getPaymentMethodDisplayName(paymentMethod) {
      var _paymentMethod$brand;
      switch (paymentMethod.type) {
        case 'card':
          return `${(_paymentMethod$brand = paymentMethod.brand) == null ? void 0 : _paymentMethod$brand.toUpperCase()} •••• ${paymentMethod.last4}`;
        case 'apple_pay':
          return 'Apple Pay';
        case 'google_pay':
          return 'Google Pay';
        case 'paypal':
          return 'PayPal';
        default:
          return 'Unknown Payment Method';
      }
    }
  }, {
    key: "isPaymentMethodExpired",
    value: function isPaymentMethodExpired(paymentMethod) {
      if (!paymentMethod.expiryMonth || !paymentMethod.expiryYear) {
        return false;
      }
      var now = new Date();
      var currentYear = now.getFullYear();
      var currentMonth = now.getMonth() + 1;
      return paymentMethod.expiryYear < currentYear || paymentMethod.expiryYear === currentYear && paymentMethod.expiryMonth < currentMonth;
    }
  }]);
}();
var paymentService = exports.paymentService = new PaymentService();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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