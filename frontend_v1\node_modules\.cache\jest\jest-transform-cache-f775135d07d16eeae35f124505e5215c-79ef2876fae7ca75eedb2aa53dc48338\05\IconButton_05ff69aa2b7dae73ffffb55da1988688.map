{"version": 3, "names": ["_vectorIcons", "require", "_react", "_interopRequireDefault", "_reactNative", "_Colors", "_jsxRuntime", "IconButton", "exports", "_ref", "name", "_ref$size", "size", "_ref$variant", "variant", "onPress", "_ref$disabled", "disabled", "style", "color", "testID", "accessibilityLabel", "accessibilityHint", "sizeConfig", "small", "iconSize", "buttonSize", "padding", "medium", "large", "variantConfig", "primary", "backgroundColor", "Colors", "interactive", "default", "iconColor", "text", "onPrimary", "pressedBackgroundColor", "pressed", "secondary", "onSage", "ghost", "hover", "danger", "errorLight", "error", "destructive", "currentSize", "currentV<PERSON>t", "buttonStyle", "styles", "button", "width", "height", "surface", "jsx", "TouchableOpacity", "accessibilityRole", "accessibilityState", "activeOpacity", "children", "Ionicons", "StyleSheet", "create", "borderRadius", "alignItems", "justifyContent"], "sources": ["IconButton.tsx"], "sourcesContent": ["/**\n * IconButton Component - Consistent Icon Button Implementation\n *\n * Component Contract:\n * - Provides consistent icon button styling across the app\n * - Supports different sizes, variants, and states\n * - Uses Ionicons for consistency with current implementation\n * - Follows design system guidelines\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport { Ionicons } from '@expo/vector-icons';\nimport React from 'react';\nimport { TouchableOpacity, StyleSheet, ViewStyle } from 'react-native';\n\nimport { Colors } from '../../constants/Colors';\n\ninterface IconButtonProps {\n  name: keyof typeof Ionicons.glyphMap;\n  size?: 'small' | 'medium' | 'large';\n  variant?: 'primary' | 'secondary' | 'ghost' | 'danger';\n  onPress: () => void;\n  disabled?: boolean;\n  style?: ViewStyle;\n  color?: string; // Custom color override\n  testID?: string;\n  accessibilityLabel?: string;\n  accessibilityHint?: string;\n}\n\nexport const IconButton: React.FC<IconButtonProps> = ({\n  name,\n  size = 'medium',\n  variant = 'ghost',\n  onPress,\n  disabled = false,\n  style,\n  color,\n  testID,\n  accessibilityLabel,\n  accessibilityHint,\n}) => {\n  // WCAG 2.1 AA compliant touch target sizes (minimum 44x44px)\n  const sizeConfig = {\n    small: { iconSize: 16, buttonSize: 44, padding: 14 }, // Increased from 32px to 44px\n    medium: { iconSize: 20, buttonSize: 48, padding: 14 }, // Increased from 40px to 48px\n    large: { iconSize: 24, buttonSize: 56, padding: 16 }, // Increased from 48px to 56px for better usability\n  };\n\n  const variantConfig = {\n    primary: {\n      backgroundColor: Colors.interactive.primary.default,\n      iconColor: Colors.text.onPrimary,\n      pressedBackgroundColor: Colors.interactive.primary.pressed,\n    },\n    secondary: {\n      backgroundColor: Colors.interactive.secondary.default,\n      iconColor: Colors.text.onSage,\n      pressedBackgroundColor: Colors.interactive.secondary.pressed,\n    },\n    ghost: {\n      backgroundColor: 'transparent',\n      iconColor: Colors.text.secondary,\n      pressedBackgroundColor: Colors.interactive.ghost.hover,\n    },\n    danger: {\n      backgroundColor: Colors.errorLight,\n      iconColor: Colors.error,\n      pressedBackgroundColor: Colors.interactive.destructive.hover,\n    },\n  };\n\n  const currentSize = sizeConfig[size];\n  const currentVariant = variantConfig[variant];\n\n  const buttonStyle = [\n    styles.button,\n    {\n      width: currentSize.buttonSize,\n      height: currentSize.buttonSize,\n      padding: currentSize.padding,\n      backgroundColor: disabled\n        ? Colors.surface.disabled\n        : currentVariant.backgroundColor,\n    },\n    style,\n  ];\n\n  const iconColor = disabled\n    ? Colors.text.disabled\n    : color || currentVariant.iconColor;\n\n  return (\n    <TouchableOpacity\n      style={buttonStyle}\n      onPress={onPress}\n      disabled={disabled}\n      testID={testID}\n      accessibilityRole=\"button\"\n      accessibilityLabel={accessibilityLabel}\n      accessibilityHint={accessibilityHint}\n      accessibilityState={{ disabled }}\n      activeOpacity={0.7}>\n      <Ionicons name={name} size={currentSize.iconSize} color={iconColor} />\n    </TouchableOpacity>\n  );\n};\n\nconst styles = StyleSheet.create({\n  button: {\n    borderRadius: 8,\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n});\n"], "mappings": ";;;;;AAaA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,YAAA,GAAAH,OAAA;AAEA,IAAAI,OAAA,GAAAJ,OAAA;AAAgD,IAAAK,WAAA,GAAAL,OAAA;AAezC,IAAMM,UAAqC,GAAAC,OAAA,CAAAD,UAAA,GAAG,SAAxCA,UAAqCA,CAAAE,IAAA,EAW5C;EAAA,IAVJC,IAAI,GAAAD,IAAA,CAAJC,IAAI;IAAAC,SAAA,GAAAF,IAAA,CACJG,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG,QAAQ,GAAAA,SAAA;IAAAE,YAAA,GAAAJ,IAAA,CACfK,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,OAAO,GAAAA,YAAA;IACjBE,OAAO,GAAAN,IAAA,CAAPM,OAAO;IAAAC,aAAA,GAAAP,IAAA,CACPQ,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,KAAK,GAAAA,aAAA;IAChBE,KAAK,GAAAT,IAAA,CAALS,KAAK;IACLC,KAAK,GAAAV,IAAA,CAALU,KAAK;IACLC,MAAM,GAAAX,IAAA,CAANW,MAAM;IACNC,kBAAkB,GAAAZ,IAAA,CAAlBY,kBAAkB;IAClBC,iBAAiB,GAAAb,IAAA,CAAjBa,iBAAiB;EAGjB,IAAMC,UAAU,GAAG;IACjBC,KAAK,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAEC,UAAU,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAG,CAAC;IACpDC,MAAM,EAAE;MAAEH,QAAQ,EAAE,EAAE;MAAEC,UAAU,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAG,CAAC;IACrDE,KAAK,EAAE;MAAEJ,QAAQ,EAAE,EAAE;MAAEC,UAAU,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAG;EACrD,CAAC;EAED,IAAMG,aAAa,GAAG;IACpBC,OAAO,EAAE;MACPC,eAAe,EAAEC,cAAM,CAACC,WAAW,CAACH,OAAO,CAACI,OAAO;MACnDC,SAAS,EAAEH,cAAM,CAACI,IAAI,CAACC,SAAS;MAChCC,sBAAsB,EAAEN,cAAM,CAACC,WAAW,CAACH,OAAO,CAACS;IACrD,CAAC;IACDC,SAAS,EAAE;MACTT,eAAe,EAAEC,cAAM,CAACC,WAAW,CAACO,SAAS,CAACN,OAAO;MACrDC,SAAS,EAAEH,cAAM,CAACI,IAAI,CAACK,MAAM;MAC7BH,sBAAsB,EAAEN,cAAM,CAACC,WAAW,CAACO,SAAS,CAACD;IACvD,CAAC;IACDG,KAAK,EAAE;MACLX,eAAe,EAAE,aAAa;MAC9BI,SAAS,EAAEH,cAAM,CAACI,IAAI,CAACI,SAAS;MAChCF,sBAAsB,EAAEN,cAAM,CAACC,WAAW,CAACS,KAAK,CAACC;IACnD,CAAC;IACDC,MAAM,EAAE;MACNb,eAAe,EAAEC,cAAM,CAACa,UAAU;MAClCV,SAAS,EAAEH,cAAM,CAACc,KAAK;MACvBR,sBAAsB,EAAEN,cAAM,CAACC,WAAW,CAACc,WAAW,CAACJ;IACzD;EACF,CAAC;EAED,IAAMK,WAAW,GAAG1B,UAAU,CAACX,IAAI,CAAC;EACpC,IAAMsC,cAAc,GAAGpB,aAAa,CAAChB,OAAO,CAAC;EAE7C,IAAMqC,WAAW,GAAG,CAClBC,MAAM,CAACC,MAAM,EACb;IACEC,KAAK,EAAEL,WAAW,CAACvB,UAAU;IAC7B6B,MAAM,EAAEN,WAAW,CAACvB,UAAU;IAC9BC,OAAO,EAAEsB,WAAW,CAACtB,OAAO;IAC5BK,eAAe,EAAEf,QAAQ,GACrBgB,cAAM,CAACuB,OAAO,CAACvC,QAAQ,GACvBiC,cAAc,CAAClB;EACrB,CAAC,EACDd,KAAK,CACN;EAED,IAAMkB,SAAS,GAAGnB,QAAQ,GACtBgB,cAAM,CAACI,IAAI,CAACpB,QAAQ,GACpBE,KAAK,IAAI+B,cAAc,CAACd,SAAS;EAErC,OACE,IAAA9B,WAAA,CAAAmD,GAAA,EAACrD,YAAA,CAAAsD,gBAAgB;IACfxC,KAAK,EAAEiC,WAAY;IACnBpC,OAAO,EAAEA,OAAQ;IACjBE,QAAQ,EAAEA,QAAS;IACnBG,MAAM,EAAEA,MAAO;IACfuC,iBAAiB,EAAC,QAAQ;IAC1BtC,kBAAkB,EAAEA,kBAAmB;IACvCC,iBAAiB,EAAEA,iBAAkB;IACrCsC,kBAAkB,EAAE;MAAE3C,QAAQ,EAARA;IAAS,CAAE;IACjC4C,aAAa,EAAE,GAAI;IAAAC,QAAA,EACnB,IAAAxD,WAAA,CAAAmD,GAAA,EAACzD,YAAA,CAAA+D,QAAQ;MAACrD,IAAI,EAAEA,IAAK;MAACE,IAAI,EAAEqC,WAAW,CAACxB,QAAS;MAACN,KAAK,EAAEiB;IAAU,CAAE;EAAC,CACtD,CAAC;AAEvB,CAAC;AAED,IAAMgB,MAAM,GAAGY,uBAAU,CAACC,MAAM,CAAC;EAC/BZ,MAAM,EAAE;IACNa,YAAY,EAAE,CAAC;IACfC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB;AACF,CAAC,CAAC", "ignoreList": []}