862bf5653ba90857b663cb845cbd7ee1
_getJestObj().mock("../../hooks/usePerformance", function () {
  return {
    usePerformance: function usePerformance() {
      return {
        trackUserInteraction: jest.fn(function (name, fn) {
          return fn();
        }),
        measureRenderTime: jest.fn(),
        trackMemoryUsage: jest.fn()
      };
    }
  };
});
_getJestObj().mock("../../hooks/useErrorHandling", function () {
  return {
    useErrorHandling: function useErrorHandling() {
      return {
        handleError: jest.fn(),
        clearError: jest.fn(),
        isError: false,
        error: null
      };
    }
  };
});
_getJestObj().mock("../../services/cacheService", function () {
  return {
    clear: jest.fn(),
    get: jest.fn(),
    set: jest.fn()
  };
});
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _native = require("@react-navigation/native");
var _stack = require("@react-navigation/stack");
var _reactNative = require("@testing-library/react-native");
var _react = _interopRequireDefault(require("react"));
var _reactRedux = require("react-redux");
var _ThemeContext = require("../../contexts/ThemeContext");
var _AccountSettingsScreen = require("../../screens/AccountSettingsScreen");
var _theme = require("../__mocks__/theme");
var _testUtils = require("../utils/testUtils");
var _jsxRuntime = require("react/jsx-runtime");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var mockStore = (0, _testUtils.createMockStore)({
  auth: {
    user: {
      id: 'user-1',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      avatar: 'https://example.com/avatar.jpg',
      role: 'customer'
    },
    isAuthenticated: true,
    logout: jest.fn()
  }
});
var Stack = (0, _stack.createStackNavigator)();
var TestWrapper = function TestWrapper(_ref) {
  var children = _ref.children;
  return (0, _jsxRuntime.jsx)(_reactRedux.Provider, {
    store: mockStore,
    children: (0, _jsxRuntime.jsx)(_ThemeContext.ThemeProvider, {
      theme: _theme.mockTheme,
      children: (0, _jsxRuntime.jsx)(_native.NavigationContainer, {
        children: (0, _jsxRuntime.jsx)(Stack.Navigator, {
          children: (0, _jsxRuntime.jsx)(Stack.Screen, {
            name: "AccountSettings",
            component: function component() {
              return children;
            }
          })
        })
      })
    })
  });
};
describe('AccountSettingsScreen', function () {
  var mockNavigation = (0, _testUtils.mockNavigationProps)();
  beforeEach(function () {
    jest.clearAllMocks();
  });
  describe('Component Rendering', function () {
    it('renders account settings screen with header and user profile', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      expect(_reactNative.screen.getByText('Account Settings')).toBeTruthy();
      expect(_reactNative.screen.getByTestId('settings-back-button')).toBeTruthy();
      expect(_reactNative.screen.getByText('John Doe')).toBeTruthy();
      expect(_reactNative.screen.getByText('<EMAIL>')).toBeTruthy();
      expect(_reactNative.screen.getByText('Customer')).toBeTruthy();
    }));
    it('displays user avatar or initials', function () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      expect(_reactNative.screen.getByText('JD')).toBeTruthy();
    });
    it('renders all settings sections', function () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      expect(_reactNative.screen.getByText('Profile Management')).toBeTruthy();
      expect(_reactNative.screen.getByText('Booking & Services')).toBeTruthy();
      expect(_reactNative.screen.getByText('Notifications')).toBeTruthy();
      expect(_reactNative.screen.getByText('App Preferences')).toBeTruthy();
      expect(_reactNative.screen.getByText('Privacy & Security')).toBeTruthy();
    });
  });
  describe('Profile Management Section', function () {
    it('navigates to edit profile screen', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      var editProfileButton = _reactNative.screen.getByTestId('setting-edit-profile');
      _reactNative.fireEvent.press(editProfileButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(mockNavigation.navigate).toHaveBeenCalledWith('EditProfile');
      });
    }));
    it('navigates to change password screen', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      var changePasswordButton = _reactNative.screen.getByTestId('setting-change-password');
      _reactNative.fireEvent.press(changePasswordButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(mockNavigation.navigate).toHaveBeenCalledWith('ChangePassword');
      });
    }));
    it('navigates to payment methods screen', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      var paymentMethodsButton = _reactNative.screen.getByTestId('setting-payment-methods');
      _reactNative.fireEvent.press(paymentMethodsButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(mockNavigation.navigate).toHaveBeenCalledWith('PaymentMethods');
      });
    }));
    it('shows role switch confirmation dialog', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      var roleSwitchButton = _reactNative.screen.getByTestId('setting-role-switch');
      _reactNative.fireEvent.press(roleSwitchButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Switch to Provider')).toBeTruthy();
        expect(_reactNative.screen.getByText('Would you like to switch to provider mode to offer services?')).toBeTruthy();
      });
    }));
  });
  describe('Booking & Services Section', function () {
    it('navigates to booking history screen', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      var bookingHistoryButton = _reactNative.screen.getByTestId('setting-booking-history');
      _reactNative.fireEvent.press(bookingHistoryButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(mockNavigation.navigate).toHaveBeenCalledWith('Bookings');
      });
    }));
    it('navigates to favorite providers screen', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      var favoriteProvidersButton = _reactNative.screen.getByTestId('setting-favorite-providers');
      _reactNative.fireEvent.press(favoriteProvidersButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(mockNavigation.navigate).toHaveBeenCalledWith('FavoriteProviders');
      });
    }));
    it('toggles booking reminders setting', function () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      var bookingRemindersToggle = _reactNative.screen.getByTestId('setting-auto-booking-reminders');
      var toggle = bookingRemindersToggle.findByType('Switch');
      expect(toggle.props.value).toBe(true);
      (0, _reactNative.fireEvent)(toggle, 'onValueChange', false);
      expect(toggle.props.value).toBe(false);
    });
  });
  describe('Notifications Section', function () {
    it('navigates to notification settings screen', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      var notificationSettingsButton = _reactNative.screen.getByTestId('setting-notification-settings');
      _reactNative.fireEvent.press(notificationSettingsButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(mockNavigation.navigate).toHaveBeenCalledWith('NotificationSettings');
      });
    }));
    it('toggles email notifications', function () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      var emailNotificationsToggle = _reactNative.screen.getByTestId('setting-email-notifications');
      var toggle = emailNotificationsToggle.findByType('Switch');
      (0, _reactNative.fireEvent)(toggle, 'onValueChange', false);
      expect(toggle.props.value).toBe(false);
    });
    it('toggles push notifications', function () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      var pushNotificationsToggle = _reactNative.screen.getByTestId('setting-push-notifications');
      var toggle = pushNotificationsToggle.findByType('Switch');
      (0, _reactNative.fireEvent)(toggle, 'onValueChange', false);
      expect(toggle.props.value).toBe(false);
    });
  });
  describe('App Preferences Section', function () {
    it('toggles dark mode', function () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      var darkModeToggle = _reactNative.screen.getByTestId('setting-dark-mode');
      var toggle = darkModeToggle.findByType('Switch');
      (0, _reactNative.fireEvent)(toggle, 'onValueChange', true);
    });
    it('toggles location services', function () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      var locationToggle = _reactNative.screen.getByTestId('setting-location');
      var toggle = locationToggle.findByType('Switch');
      (0, _reactNative.fireEvent)(toggle, 'onValueChange', false);
      expect(toggle.props.value).toBe(false);
    });
    it('toggles biometric authentication', function () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      var biometricToggle = _reactNative.screen.getByTestId('setting-biometric');
      var toggle = biometricToggle.findByType('Switch');
      (0, _reactNative.fireEvent)(toggle, 'onValueChange', true);
      expect(toggle.props.value).toBe(true);
    });
  });
  describe('Privacy & Security Section', function () {
    it('shows privacy settings coming soon dialog', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      var privacySettingsButton = _reactNative.screen.getByTestId('setting-privacy-settings');
      _reactNative.fireEvent.press(privacySettingsButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Privacy Settings')).toBeTruthy();
        expect(_reactNative.screen.getByText('Advanced privacy settings will be available soon.')).toBeTruthy();
      });
    }));
    it('shows data export coming soon dialog', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      var dataExportButton = _reactNative.screen.getByTestId('setting-data-export');
      _reactNative.fireEvent.press(dataExportButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Export Data')).toBeTruthy();
        expect(_reactNative.screen.getByText('Data export functionality will be available soon.')).toBeTruthy();
      });
    }));
  });
  describe('Logout Functionality', function () {
    it('shows logout confirmation dialog', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      var logoutButton = _reactNative.screen.getByTestId('setting-logout');
      _reactNative.fireEvent.press(logoutButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Sign Out')).toBeTruthy();
        expect(_reactNative.screen.getByText('Are you sure you want to sign out?')).toBeTruthy();
      });
    }));
    it('performs logout when confirmed', (0, _asyncToGenerator2.default)(function* () {
      var mockLogout = jest.fn();
      var storeWithLogout = (0, _testUtils.createMockStore)({
        auth: {
          user: {
            id: 'user-1',
            firstName: 'John',
            lastName: 'Doe'
          },
          isAuthenticated: true,
          logout: mockLogout
        }
      });
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(_reactRedux.Provider, {
        store: storeWithLogout,
        children: (0, _jsxRuntime.jsx)(_ThemeContext.ThemeProvider, {
          theme: _theme.mockTheme,
          children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
            navigation: mockNavigation
          })
        })
      }));
      var logoutButton = _reactNative.screen.getByTestId('setting-logout');
      _reactNative.fireEvent.press(logoutButton);
      yield (0, _reactNative.waitFor)(function () {
        var confirmButton = _reactNative.screen.getByText('Sign Out');
        _reactNative.fireEvent.press(confirmButton);
      });
      expect(mockLogout).toHaveBeenCalled();
    }));
  });
  describe('Accessibility', function () {
    it('has proper accessibility labels for all interactive elements', function () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      var editProfileButton = _reactNative.screen.getByTestId('setting-edit-profile');
      expect(editProfileButton.props.accessibilityLabel).toBe('Edit Profile');
      expect(editProfileButton.props.accessibilityHint).toBe('Update your personal information and preferences');
    });
    it('supports screen reader navigation', function () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      var screenReader = _reactNative.screen.getByTestId('account-settings-main');
      expect(screenReader.props.announceOnMount).toBe('Account settings screen loaded. Manage your profile and preferences.');
    });
    it('has proper touch target sizes', function () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      var settingItems = _reactNative.screen.getAllByTestId(/^setting-/);
      settingItems.forEach(function (item) {
        expect(item.props.minimumSize).toBe(64);
      });
    });
  });
  describe('Performance', function () {
    it('tracks user interactions for analytics', (0, _asyncToGenerator2.default)(function* () {
      var mockTrackUserInteraction = jest.fn(function (name, fn) {
        return fn();
      });
      jest.doMock("../../hooks/usePerformance", function () {
        return {
          usePerformance: function usePerformance() {
            return {
              trackUserInteraction: mockTrackUserInteraction
            };
          }
        };
      });
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      var editProfileButton = _reactNative.screen.getByTestId('setting-edit-profile');
      _reactNative.fireEvent.press(editProfileButton);
      expect(mockTrackUserInteraction).toHaveBeenCalledWith('edit_profile', expect.any(Function));
    }));
    it('implements proper error handling', (0, _asyncToGenerator2.default)(function* () {
      var mockHandleError = jest.fn();
      jest.doMock("../../hooks/useErrorHandling", function () {
        return {
          useErrorHandling: function useErrorHandling() {
            return {
              handleError: mockHandleError,
              clearError: jest.fn()
            };
          }
        };
      });
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      expect(mockHandleError).not.toHaveBeenCalled();
    }));
  });
  describe('Navigation', function () {
    it('navigates back when back button is pressed', function () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      var backButton = _reactNative.screen.getByTestId('settings-back-button');
      _reactNative.fireEvent.press(backButton);
      expect(mockNavigation.goBack).toHaveBeenCalled();
    });
    it('handles deep linking to specific settings sections', function () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_AccountSettingsScreen.AccountSettingsScreen, {
          navigation: mockNavigation
        })
      }));
      expect(_reactNative.screen.getByText('Account Settings')).toBeTruthy();
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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