525127dca4cc08c7a5953d2bb5b751e8
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.Zocial = exports.SimpleLineIcons = exports.Octicons = exports.MaterialIcons = exports.MaterialCommunityIcons = exports.Ionicons = exports.Foundation = exports.FontAwesome = exports.Feather = exports.EvilIcons = exports.Entypo = exports.AntDesign = void 0;
var React = require('react');
var _require = require('react-native'),
  Text = _require.Text;
var createIconComponent = function createIconComponent(name) {
  return React.forwardRef(function (props, ref) {
    return React.createElement(Text, Object.assign({}, props, {
      ref: ref,
      testID: props.testID || `icon-${name}`,
      accessibilityLabel: props.accessibilityLabel || `${name} icon`
    }), props.name || name);
  });
};
var Ionicons = exports.Ionicons = createIconComponent('Ionicons');
var MaterialIcons = exports.MaterialIcons = createIconComponent('MaterialIcons');
var FontAwesome = exports.FontAwesome = createIconComponent('FontAwesome');
var Entypo = exports.Entypo = createIconComponent('Entypo');
var AntDesign = exports.AntDesign = createIconComponent('AntDesign');
var MaterialCommunityIcons = exports.MaterialCommunityIcons = createIconComponent('MaterialCommunityIcons');
var Feather = exports.Feather = createIconComponent('Feather');
var Foundation = exports.Foundation = createIconComponent('Foundation');
var EvilIcons = exports.EvilIcons = createIconComponent('EvilIcons');
var Octicons = exports.Octicons = createIconComponent('Octicons');
var SimpleLineIcons = exports.SimpleLineIcons = createIconComponent('SimpleLineIcons');
var Zocial = exports.Zocial = createIconComponent('Zocial');
var _default = exports.default = {
  Ionicons: Ionicons,
  MaterialIcons: MaterialIcons,
  FontAwesome: FontAwesome,
  Entypo: Entypo,
  AntDesign: AntDesign,
  MaterialCommunityIcons: MaterialCommunityIcons,
  Feather: Feather,
  Foundation: Foundation,
  EvilIcons: EvilIcons,
  Octicons: Octicons,
  SimpleLineIcons: SimpleLineIcons,
  Zocial: Zocial
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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