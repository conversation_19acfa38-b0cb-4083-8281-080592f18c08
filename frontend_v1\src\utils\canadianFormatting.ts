/**
 * Canadian Formatting Utilities
 * 
 * Provides Canadian-specific formatting for dates, currency, postal codes,
 * phone numbers, and other locale-specific data formatting.
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

/**
 * Format currency in Canadian dollars
 */
export const formatCADCurrency = (amount: number, locale: string = 'en-CA'): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: 'CAD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

/**
 * Format date in Canadian format (DD/MM/YYYY for French, MM/DD/YYYY for English)
 */
export const formatCanadianDate = (date: Date, locale: string = 'en-CA'): string => {
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  };

  if (locale === 'fr-CA') {
    // French Canadian format: DD/MM/YYYY
    return date.toLocaleDateString('fr-CA', options);
  } else {
    // English Canadian format: MM/DD/YYYY
    return date.toLocaleDateString('en-CA', options);
  }
};

/**
 * Format time in Canadian 12-hour format
 */
export const formatCanadianTime = (date: Date, locale: string = 'en-CA'): string => {
  return date.toLocaleTimeString(locale, {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  });
};

/**
 * Validate and format Canadian postal code
 */
export const formatPostalCode = (postalCode: string): string => {
  // Remove all non-alphanumeric characters
  const cleaned = postalCode.replace(/[^A-Za-z0-9]/g, '').toUpperCase();
  
  // Check if it matches Canadian postal code pattern (A#A #A#)
  const postalCodeRegex = /^[A-Z]\d[A-Z]\d[A-Z]\d$/;
  
  if (cleaned.length === 6 && postalCodeRegex.test(cleaned)) {
    // Format as A#A #A#
    return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;
  }
  
  return postalCode; // Return original if invalid
};

/**
 * Validate Canadian postal code
 */
export const isValidPostalCode = (postalCode: string): boolean => {
  const cleaned = postalCode.replace(/[^A-Za-z0-9]/g, '').toUpperCase();
  const postalCodeRegex = /^[A-Z]\d[A-Z]\d[A-Z]\d$/;
  return cleaned.length === 6 && postalCodeRegex.test(cleaned);
};

/**
 * Format Canadian phone number
 */
export const formatCanadianPhoneNumber = (phoneNumber: string): string => {
  // Remove all non-numeric characters
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  // Check if it's a valid Canadian phone number (10 digits)
  if (cleaned.length === 10) {
    // Format as (XXX) XXX-XXXX
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  } else if (cleaned.length === 11 && cleaned.startsWith('1')) {
    // Format as +1 (XXX) XXX-XXXX
    return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
  }
  
  return phoneNumber; // Return original if invalid
};

/**
 * Validate Canadian phone number
 */
export const isValidCanadianPhoneNumber = (phoneNumber: string): boolean => {
  const cleaned = phoneNumber.replace(/\D/g, '');
  return cleaned.length === 10 || (cleaned.length === 11 && cleaned.startsWith('1'));
};

/**
 * Format Canadian address
 */
export const formatCanadianAddress = (address: {
  street: string;
  city: string;
  province: string;
  postalCode: string;
  country?: string;
}): string => {
  const { street, city, province, postalCode, country = 'Canada' } = address;
  
  return [
    street,
    `${city}, ${province} ${formatPostalCode(postalCode)}`,
    country
  ].filter(Boolean).join('\n');
};

/**
 * Canadian provinces and territories
 */
export const CANADIAN_PROVINCES = {
  'AB': 'Alberta',
  'BC': 'British Columbia',
  'MB': 'Manitoba',
  'NB': 'New Brunswick',
  'NL': 'Newfoundland and Labrador',
  'NS': 'Nova Scotia',
  'ON': 'Ontario',
  'PE': 'Prince Edward Island',
  'QC': 'Quebec',
  'SK': 'Saskatchewan',
  'NT': 'Northwest Territories',
  'NU': 'Nunavut',
  'YT': 'Yukon',
};

/**
 * Get province full name from abbreviation
 */
export const getProvinceName = (abbreviation: string): string => {
  return CANADIAN_PROVINCES[abbreviation.toUpperCase() as keyof typeof CANADIAN_PROVINCES] || abbreviation;
};

/**
 * Format business hours in Canadian format
 */
export const formatBusinessHours = (
  openTime: string,
  closeTime: string,
  locale: string = 'en-CA'
): string => {
  const open = new Date(`2000-01-01T${openTime}`);
  const close = new Date(`2000-01-01T${closeTime}`);
  
  const openFormatted = formatCanadianTime(open, locale);
  const closeFormatted = formatCanadianTime(close, locale);
  
  if (locale === 'fr-CA') {
    return `${openFormatted} à ${closeFormatted}`;
  } else {
    return `${openFormatted} - ${closeFormatted}`;
  }
};

/**
 * Format distance in Canadian metric system
 */
export const formatDistance = (distanceInKm: number, locale: string = 'en-CA'): string => {
  if (distanceInKm < 1) {
    const meters = Math.round(distanceInKm * 1000);
    return locale === 'fr-CA' ? `${meters} m` : `${meters} m`;
  } else {
    const km = distanceInKm.toFixed(1);
    return locale === 'fr-CA' ? `${km} km` : `${km} km`;
  }
};

/**
 * Format temperature in Celsius (Canadian standard)
 */
export const formatTemperature = (celsius: number, locale: string = 'en-CA'): string => {
  return locale === 'fr-CA' ? `${celsius}°C` : `${celsius}°C`;
};

/**
 * Canadian tax calculation utilities
 */
export const calculateCanadianTax = (amount: number, province: string): {
  gst: number;
  pst: number;
  hst: number;
  total: number;
} => {
  const GST_RATE = 0.05; // 5% GST
  
  // Provincial tax rates (simplified - actual rates may vary)
  const PST_RATES: { [key: string]: number } = {
    'AB': 0,      // No PST
    'BC': 0.07,   // 7% PST
    'MB': 0.07,   // 7% PST
    'NB': 0.15,   // 15% HST (replaces GST+PST)
    'NL': 0.15,   // 15% HST
    'NS': 0.15,   // 15% HST
    'ON': 0.13,   // 13% HST
    'PE': 0.15,   // 15% HST
    'QC': 0.09975, // 9.975% QST
    'SK': 0.06,   // 6% PST
    'NT': 0,      // No PST
    'NU': 0,      // No PST
    'YT': 0,      // No PST
  };
  
  const provinceCode = province.toUpperCase();
  const pstRate = PST_RATES[provinceCode] || 0;
  
  // HST provinces use HST instead of GST+PST
  const isHSTProvince = ['NB', 'NL', 'NS', 'ON', 'PE'].includes(provinceCode);
  
  if (isHSTProvince) {
    const hst = amount * pstRate;
    return {
      gst: 0,
      pst: 0,
      hst,
      total: amount + hst,
    };
  } else {
    const gst = amount * GST_RATE;
    const pst = amount * pstRate;
    return {
      gst,
      pst,
      hst: 0,
      total: amount + gst + pst,
    };
  }
};
