63687eedd5285d1968db5af87b0fd46e
'use strict';

var createStoreImpl = function createStoreImpl(createState) {
  var state;
  var listeners = new Set();
  var setState = function setState(partial, replace) {
    var nextState = typeof partial === "function" ? partial(state) : partial;
    if (!Object.is(nextState, state)) {
      var previousState = state;
      state = (replace != null ? replace : typeof nextState !== "object" || nextState === null) ? nextState : Object.assign({}, state, nextState);
      listeners.forEach(function (listener) {
        return listener(state, previousState);
      });
    }
  };
  var getState = function getState() {
    return state;
  };
  var getInitialState = function getInitialState() {
    return initialState;
  };
  var subscribe = function subscribe(listener) {
    listeners.add(listener);
    return function () {
      return listeners.delete(listener);
    };
  };
  var api = {
    setState: setState,
    getState: getState,
    getInitialState: getInitialState,
    subscribe: subscribe
  };
  var initialState = state = createState(setState, getState, api);
  return api;
};
var createStore = function createStore(createState) {
  return createState ? createStoreImpl(createState) : createStoreImpl;
};
exports.createStore = createStore;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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