d341f8bce9588cb29f8489ada25670fd
_getJestObj().mock("../../../utils/accessibilityUtils");
_getJestObj().mock("../../../contexts/ThemeContext");
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _reactNative = require("@testing-library/react-native");
var _react = _interopRequireDefault(require("react"));
var _ThemeContext = require("../../../contexts/ThemeContext");
var _accessibilityUtils = require("../../../utils/accessibilityUtils");
var _AccessibilityAudit = require("../AccessibilityAudit");
var _jsxRuntime = require("react/jsx-runtime");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var mockAccessibilityUtils = _accessibilityUtils.AccessibilityUtils;
var TestWrapper = function TestWrapper(_ref) {
  var children = _ref.children;
  return (0, _jsxRuntime.jsx)(_ThemeContext.ThemeProvider, {
    children: children
  });
};
describe('AccessibilityAudit', function () {
  beforeEach(function () {
    jest.clearAllMocks();
    mockAccessibilityUtils.getContrastRatio.mockReturnValue(4.5);
    mockAccessibilityUtils.meetsWCAGAA.mockReturnValue(true);
    mockAccessibilityUtils.WCAG_STANDARDS = {
      TOUCH_TARGETS: {
        MINIMUM_SIZE: 44
      }
    };
  });
  describe('Rendering', function () {
    it('should render audit component correctly', function () {
      var _render = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AccessibilityAudit.AccessibilityAudit, {
            showDetailedReport: true
          })
        })),
        getByText = _render.getByText;
      expect(getByText('Accessibility Audit (WCAG AA)')).toBeTruthy();
      expect(getByText('Run Audit')).toBeTruthy();
    });
    it('should not render when showDetailedReport is false', function () {
      var _render2 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AccessibilityAudit.AccessibilityAudit, {
            showDetailedReport: false
          })
        })),
        queryByText = _render2.queryByText;
      expect(queryByText('Accessibility Audit (WCAG AA)')).toBeFalsy();
    });
    it('should render with different compliance levels', function () {
      var _render3 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AccessibilityAudit.AccessibilityAudit, {
            complianceLevel: "AAA",
            showDetailedReport: true
          })
        })),
        getByText = _render3.getByText;
      expect(getByText('Accessibility Audit (WCAG AAA)')).toBeTruthy();
    });
  });
  describe('Audit Execution', function () {
    it('should run audit when button is pressed', (0, _asyncToGenerator2.default)(function* () {
      var mockOnAuditComplete = jest.fn();
      var _render4 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AccessibilityAudit.AccessibilityAudit, {
            showDetailedReport: true,
            onAuditComplete: mockOnAuditComplete
          })
        })),
        getByText = _render4.getByText;
      var runButton = getByText('Run Audit');
      _reactNative.fireEvent.press(runButton);
      expect(getByText('Auditing...')).toBeTruthy();
      yield (0, _reactNative.waitFor)(function () {
        expect(mockOnAuditComplete).toHaveBeenCalled();
      });
    }));
    it('should show progress during audit', (0, _asyncToGenerator2.default)(function* () {
      var _render5 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AccessibilityAudit.AccessibilityAudit, {
            showDetailedReport: true
          })
        })),
        getByText = _render5.getByText,
        getByTestId = _render5.getByTestId;
      var runButton = getByText('Run Audit');
      _reactNative.fireEvent.press(runButton);
      expect(getByTestId('progress-bar')).toBeTruthy();
      expect(getByText('Auditing...')).toBeTruthy();
    }));
    it('should call onIssueFound when issues are detected', (0, _asyncToGenerator2.default)(function* () {
      var mockOnIssueFound = jest.fn();
      mockAccessibilityUtils.meetsWCAGAA.mockReturnValue(false);
      mockAccessibilityUtils.getContrastRatio.mockReturnValue(2.0);
      var _render6 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AccessibilityAudit.AccessibilityAudit, {
            showDetailedReport: true,
            onIssueFound: mockOnIssueFound
          })
        })),
        getByText = _render6.getByText;
      var runButton = getByText('Run Audit');
      _reactNative.fireEvent.press(runButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(mockOnIssueFound).toHaveBeenCalledWith(expect.objectContaining({
          id: '1.4.3',
          criterion: 'Contrast (Minimum)',
          status: 'fail'
        }));
      });
    }));
  });
  describe('Real-time Auditing', function () {
    it('should start real-time auditing when enabled', function () {
      jest.useFakeTimers();
      var _render7 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AccessibilityAudit.AccessibilityAudit, {
            enableRealTimeAudit: true,
            showDetailedReport: true
          })
        })),
        getByText = _render7.getByText;
      jest.advanceTimersByTime(5000);
      expect(getByText('Auditing...')).toBeTruthy();
      jest.useRealTimers();
    });
    it('should stop real-time auditing when component unmounts', function () {
      jest.useFakeTimers();
      var clearIntervalSpy = jest.spyOn(global, 'clearInterval');
      var _render8 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AccessibilityAudit.AccessibilityAudit, {
            enableRealTimeAudit: true,
            showDetailedReport: true
          })
        })),
        unmount = _render8.unmount;
      unmount();
      expect(clearIntervalSpy).toHaveBeenCalled();
      jest.useRealTimers();
    });
  });
  describe('Audit Results Display', function () {
    it('should display audit results after completion', (0, _asyncToGenerator2.default)(function* () {
      var _render9 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AccessibilityAudit.AccessibilityAudit, {
            showDetailedReport: true
          })
        })),
        getByText = _render9.getByText;
      var runButton = getByText('Run Audit');
      _reactNative.fireEvent.press(runButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(getByText('1.1.1 - Non-text Content')).toBeTruthy();
        expect(getByText('1.4.3 - Contrast (Minimum)')).toBeTruthy();
        expect(getByText('2.1.1 - Keyboard')).toBeTruthy();
      });
    }));
    it('should show pass status for passing criteria', (0, _asyncToGenerator2.default)(function* () {
      var _render0 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AccessibilityAudit.AccessibilityAudit, {
            showDetailedReport: true
          })
        })),
        getByText = _render0.getByText,
        getAllByTestId = _render0.getAllByTestId;
      var runButton = getByText('Run Audit');
      _reactNative.fireEvent.press(runButton);
      yield (0, _reactNative.waitFor)(function () {
        var passIcons = getAllByTestId('pass-icon');
        expect(passIcons.length).toBeGreaterThan(0);
      });
    }));
    it('should show fail status for failing criteria', (0, _asyncToGenerator2.default)(function* () {
      mockAccessibilityUtils.meetsWCAGAA.mockReturnValue(false);
      mockAccessibilityUtils.getContrastRatio.mockReturnValue(2.0);
      var _render1 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AccessibilityAudit.AccessibilityAudit, {
            showDetailedReport: true
          })
        })),
        getByText = _render1.getByText,
        getAllByTestId = _render1.getAllByTestId;
      var runButton = getByText('Run Audit');
      _reactNative.fireEvent.press(runButton);
      yield (0, _reactNative.waitFor)(function () {
        var failIcons = getAllByTestId('fail-icon');
        expect(failIcons.length).toBeGreaterThan(0);
      });
    }));
    it('should display impact badges correctly', (0, _asyncToGenerator2.default)(function* () {
      var _render10 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AccessibilityAudit.AccessibilityAudit, {
            showDetailedReport: true
          })
        })),
        getByText = _render10.getByText;
      var runButton = getByText('Run Audit');
      _reactNative.fireEvent.press(runButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(getByText('low')).toBeTruthy();
      });
    }));
    it('should show recommendations for each criterion', (0, _asyncToGenerator2.default)(function* () {
      var _render11 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AccessibilityAudit.AccessibilityAudit, {
            showDetailedReport: true
          })
        })),
        getByText = _render11.getByText;
      var runButton = getByText('Run Audit');
      _reactNative.fireEvent.press(runButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(getByText(/Continue using semantic markup/)).toBeTruthy();
        expect(getByText(/Color contrast meets WCAG AA standards/)).toBeTruthy();
      });
    }));
  });
  describe('Compliance Level Filtering', function () {
    it('should filter criteria based on compliance level A', (0, _asyncToGenerator2.default)(function* () {
      var _render12 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AccessibilityAudit.AccessibilityAudit, {
            complianceLevel: "A",
            showDetailedReport: true
          })
        })),
        getByText = _render12.getByText;
      var runButton = getByText('Run Audit');
      _reactNative.fireEvent.press(runButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(getByText('1.1.1 - Non-text Content')).toBeTruthy();
        expect(getByText('2.1.1 - Keyboard')).toBeTruthy();
        expect(function () {
          return getByText('1.4.3 - Contrast (Minimum)');
        }).toThrow();
      });
    }));
    it('should include all criteria for AAA compliance level', (0, _asyncToGenerator2.default)(function* () {
      var _render13 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AccessibilityAudit.AccessibilityAudit, {
            complianceLevel: "AAA",
            showDetailedReport: true
          })
        })),
        getByText = _render13.getByText;
      var runButton = getByText('Run Audit');
      _reactNative.fireEvent.press(runButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(getByText('1.1.1 - Non-text Content')).toBeTruthy();
        expect(getByText('1.4.3 - Contrast (Minimum)')).toBeTruthy();
        expect(getByText('2.1.1 - Keyboard')).toBeTruthy();
      });
    }));
  });
  describe('Color Contrast Testing', function () {
    it('should test color contrast correctly', (0, _asyncToGenerator2.default)(function* () {
      var _render14 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AccessibilityAudit.AccessibilityAudit, {
            showDetailedReport: true
          })
        })),
        getByText = _render14.getByText;
      var runButton = getByText('Run Audit');
      _reactNative.fireEvent.press(runButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(mockAccessibilityUtils.getContrastRatio).toHaveBeenCalled();
        expect(mockAccessibilityUtils.meetsWCAGAA).toHaveBeenCalled();
      });
    }));
    it('should display contrast ratio in results', (0, _asyncToGenerator2.default)(function* () {
      mockAccessibilityUtils.getContrastRatio.mockReturnValue(4.51);
      var _render15 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AccessibilityAudit.AccessibilityAudit, {
            showDetailedReport: true
          })
        })),
        getByText = _render15.getByText;
      var runButton = getByText('Run Audit');
      _reactNative.fireEvent.press(runButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(getByText(/Color contrast ratio: 4.51:1/)).toBeTruthy();
      });
    }));
  });
  describe('Touch Target Testing', function () {
    it('should validate touch target sizes', (0, _asyncToGenerator2.default)(function* () {
      var _render16 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AccessibilityAudit.AccessibilityAudit, {
            showDetailedReport: true
          })
        })),
        getByText = _render16.getByText;
      var runButton = getByText('Run Audit');
      _reactNative.fireEvent.press(runButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(getByText(/Touch targets meet minimum size of 44x44px/)).toBeTruthy();
      });
    }));
  });
  describe('Accessibility', function () {
    it('should have proper accessibility attributes', function () {
      var _render17 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AccessibilityAudit.AccessibilityAudit, {
            showDetailedReport: true
          })
        })),
        getByRole = _render17.getByRole;
      var runButton = getByRole('button');
      expect(runButton.props.accessibilityLabel).toContain('Run Audit');
    });
    it('should be keyboard accessible', function () {
      var _render18 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AccessibilityAudit.AccessibilityAudit, {
            showDetailedReport: true
          })
        })),
        getByText = _render18.getByText;
      var runButton = getByText('Run Audit');
      expect(runButton.props.accessible).toBe(true);
    });
  });
  describe('Error Handling', function () {
    it('should handle audit errors gracefully', (0, _asyncToGenerator2.default)(function* () {
      mockAccessibilityUtils.getContrastRatio.mockImplementation(function () {
        throw new Error('Contrast calculation error');
      });
      var _render19 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_AccessibilityAudit.AccessibilityAudit, {
            showDetailedReport: true
          })
        })),
        getByText = _render19.getByText;
      var runButton = getByText('Run Audit');
      _reactNative.fireEvent.press(runButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(getByText('Run Audit')).toBeTruthy();
      });
    }));
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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