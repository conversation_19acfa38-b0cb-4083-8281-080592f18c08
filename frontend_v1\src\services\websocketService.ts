/**
 * WebSocket Service - Real-time Communication
 *
 * Service Contract:
 * - Manages WebSocket connections for real-time messaging
 * - Handles connection lifecycle and reconnection
 * - Provides event-based messaging interface
 * - Supports typing indicators and message status updates
 * - Implements proper error handling and fallback mechanisms
 * - Integrates with unified error handling system
 *
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

import { unifiedErrorHandlingService } from './unifiedErrorHandling';

export interface WebSocketMessage {
  type:
    | 'chat_message'
    | 'typing_indicator'
    | 'messages_read'
    | 'notification'
    | 'error';
  message?: any;
  user_id?: string;
  user_name?: string;
  is_typing?: boolean;
  message_ids?: string[];
  reader_id?: string;
  notification?: any;
  error?: string;
}

export interface WebSocketConfig {
  url: string;
  protocols?: string[];
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
}

class WebSocketService {
  private socket: WebSocket | null = null;
  private config: WebSocketConfig;
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private eventListeners: Map<string, Set<(data: any) => void>> = new Map();
  private connectionPromise: Promise<void> | null = null;

  constructor(config: WebSocketConfig) {
    this.config = {
      reconnectInterval: 3000,
      maxReconnectAttempts: 5,
      ...config,
    };
  }

  /**
   * Connect to WebSocket server
   */
  async connect(token?: string): Promise<void> {
    if (this.socket?.readyState === WebSocket.OPEN) {
      return Promise.resolve();
    }

    if (this.connectionPromise) {
      return this.connectionPromise;
    }

    this.connectionPromise = new Promise((resolve, reject) => {
      try {
        this.isConnecting = true;

        // Use provided token for WebSocket authentication
        const wsUrl = token
          ? `${this.config.url}?token=${token}`
          : this.config.url;

        this.socket = new WebSocket(wsUrl, this.config.protocols);

        this.socket.onopen = () => {
          console.log('WebSocket connected');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          this.connectionPromise = null;
          this.emit('connected', {});
          resolve();
        };

        this.socket.onmessage = event => {
          try {
            const data: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(data);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
            // Handle message parsing errors with unified error handling
            unifiedErrorHandlingService.handleWebSocketError(error as Error, {
              action: 'message_parse_error',
              additionalData: { rawMessage: event.data, url: this.config.url }
            });
          }
        };

        this.socket.onclose = async event => {
          console.log('WebSocket disconnected:', event.code, event.reason);
          this.isConnecting = false;
          this.connectionPromise = null;
          this.emit('disconnected', { code: event.code, reason: event.reason });

          // Handle different close codes
          if (event.code === 4001) {
            // Authentication failed
            await unifiedErrorHandlingService.handleAuthenticationError(
              new Error('WebSocket authentication failed'),
              {
                action: 'websocket_auth_failed',
                additionalData: { code: event.code, reason: event.reason, url: this.config.url }
              }
            );
          } else if (!event.wasClean && this.shouldReconnect()) {
            this.scheduleReconnect();
          }
        };

        this.socket.onerror = async error => {
          console.error('WebSocket error:', error);
          this.isConnecting = false;
          this.connectionPromise = null;

          // Use unified error handling
          await unifiedErrorHandlingService.handleWebSocketError(error as Error, {
            action: 'connection_error',
            additionalData: {
              url: this.config.url,
              readyState: this.socket?.readyState
            }
          });

          this.emit('error', { error });
          reject(error);
        };
      } catch (error) {
        this.isConnecting = false;
        this.connectionPromise = null;
        reject(error);
      }
    });

    return this.connectionPromise;
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.socket) {
      this.socket.close(1000, 'Client disconnect');
      this.socket = null;
    }

    this.connectionPromise = null;
    this.reconnectAttempts = 0;
  }

  /**
   * Send message through WebSocket
   */
  async send(message: any): Promise<boolean> {
    if (this.socket?.readyState === WebSocket.OPEN) {
      try {
        this.socket.send(JSON.stringify(message));
        return true;
      } catch (error) {
        console.error('Failed to send WebSocket message:', error);
        await unifiedErrorHandlingService.handleWebSocketError(error as Error, {
          action: 'send_message_error',
          additionalData: { message, url: this.config.url }
        });
        return false;
      }
    } else {
      console.warn('WebSocket not connected, message not sent:', message);
      await unifiedErrorHandlingService.handleWebSocketError(
        new Error('WebSocket not connected'),
        {
          action: 'send_message_not_connected',
          additionalData: {
            message,
            url: this.config.url,
            readyState: this.socket?.readyState
          }
        }
      );
      return false;
    }
  }

  /**
   * Send chat message
   */
  async sendChatMessage(content: string, replyTo?: string): Promise<boolean> {
    return await this.send({
      type: 'chat_message',
      content,
      reply_to: replyTo,
    });
  }

  /**
   * Send typing indicator
   */
  async sendTypingIndicator(isTyping: boolean): Promise<boolean> {
    return await this.send({
      type: 'typing_indicator',
      is_typing: isTyping,
    });
  }

  /**
   * Mark messages as read
   */
  async markMessagesRead(messageIds: string[]): Promise<boolean> {
    return await this.send({
      type: 'mark_read',
      message_ids: messageIds,
    });
  }

  /**
   * Add event listener
   */
  on(event: string, callback: (data: any) => void): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(callback);
  }

  /**
   * Remove event listener
   */
  off(event: string, callback: (data: any) => void): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  /**
   * Get connection status
   */
  isConnected(): boolean {
    return this.socket?.readyState === WebSocket.OPEN;
  }

  /**
   * Get connection state
   */
  getConnectionState(): string {
    if (!this.socket) return 'disconnected';

    switch (this.socket.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting';
      case WebSocket.OPEN:
        return 'connected';
      case WebSocket.CLOSING:
        return 'closing';
      case WebSocket.CLOSED:
        return 'disconnected';
      default:
        return 'unknown';
    }
  }

  // Private methods
  private handleMessage(data: WebSocketMessage): void {
    this.emit(data.type, data);
  }

  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(
            `Error in WebSocket event listener for ${event}:`,
            error,
          );
        }
      });
    }
  }

  private shouldReconnect(): boolean {
    return this.reconnectAttempts < this.config.maxReconnectAttempts!;
  }

  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    this.reconnectTimer = setTimeout(() => {
      this.reconnectAttempts++;
      console.log(
        `Attempting to reconnect (${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`,
      );
      this.connect().catch(error => {
        console.error('Reconnection failed:', error);
      });
    }, this.config.reconnectInterval);
  }
}

// WebSocket service factory
export const createWebSocketService = (
  config: WebSocketConfig,
): WebSocketService => {
  return new WebSocketService(config);
};

// Messaging WebSocket service factory (requires conversation ID)
export const createMessagingWebSocketService = (conversationId: string) =>
  createWebSocketService({
    url: `ws://192.168.2.65:8000/ws/messaging/${conversationId}/`,
    reconnectInterval: 3000,
    maxReconnectAttempts: 5,
  });

// Notifications WebSocket service factory (requires user ID)
export const createNotificationsWebSocketService = (userId: string) =>
  createWebSocketService({
    url: `ws://192.168.2.65:8000/ws/notifications/${userId}/`,
    reconnectInterval: 5000,
    maxReconnectAttempts: 3,
  });

export default WebSocketService;
