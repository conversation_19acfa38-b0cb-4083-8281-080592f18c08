{"version": 3, "names": ["_AndroidTextInputNativeComponent", "require", "_RCTSingelineTextInputNativeComponent", "_require", "findNodeHandle", "Platform", "default", "currentlyFocusedInputRef", "inputs", "Set", "currentlyFocusedInput", "currentlyFocusedField", "__DEV__", "console", "error", "focusInput", "textField", "blurInput", "focusField", "textFieldID", "blurField", "focusTextInput", "_textField$currentPro", "fieldCanBeFocused", "currentProps", "editable", "OS", "iOSTextInputCommands", "focus", "AndroidTextInputCommands", "blurTextInput", "blur", "registerInput", "add", "unregisterInput", "delete", "isTextInput", "has", "TextInputState", "_default", "exports"], "sources": ["TextInputState.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\n// This class is responsible for coordinating the \"focused\" state for\n// TextInputs. All calls relating to the keyboard should be funneled\n// through here.\n\nimport type {HostInstance} from '../../../src/private/types/HostInstance';\n\nimport {Commands as AndroidTextInputCommands} from '../../Components/TextInput/AndroidTextInputNativeComponent';\nimport {Commands as iOSTextInputCommands} from '../../Components/TextInput/RCTSingelineTextInputNativeComponent';\n\nconst {findNodeHandle} = require('../../ReactNative/RendererProxy');\nconst Platform = require('../../Utilities/Platform').default;\n\nlet currentlyFocusedInputRef: ?HostInstance = null;\nconst inputs = new Set<HostInstance>();\n\nfunction currentlyFocusedInput(): ?HostInstance {\n  return currentlyFocusedInputRef;\n}\n\n/**\n * Returns the ID of the currently focused text field, if one exists\n * If no text field is focused it returns null\n */\nfunction currentlyFocusedField(): ?number {\n  if (__DEV__) {\n    console.error(\n      'currentlyFocusedField is deprecated and will be removed in a future release. Use currentlyFocusedInput',\n    );\n  }\n\n  return findNodeHandle(currentlyFocusedInputRef);\n}\n\nfunction focusInput(textField: ?HostInstance): void {\n  if (currentlyFocusedInputRef !== textField && textField != null) {\n    currentlyFocusedInputRef = textField;\n  }\n}\n\nfunction blurInput(textField: ?HostInstance): void {\n  if (currentlyFocusedInputRef === textField && textField != null) {\n    currentlyFocusedInputRef = null;\n  }\n}\n\nfunction focusField(textFieldID: ?number): void {\n  if (__DEV__) {\n    console.error('focusField no longer works. Use focusInput');\n  }\n\n  return;\n}\n\nfunction blurField(textFieldID: ?number) {\n  if (__DEV__) {\n    console.error('blurField no longer works. Use blurInput');\n  }\n\n  return;\n}\n\n/**\n * @param {number} TextInputID id of the text field to focus\n * Focuses the specified text field\n * noop if the text field was already focused or if the field is not editable\n */\nfunction focusTextInput(textField: ?HostInstance) {\n  if (typeof textField === 'number') {\n    if (__DEV__) {\n      console.error(\n        'focusTextInput must be called with a host component. Passing a react tag is deprecated.',\n      );\n    }\n\n    return;\n  }\n\n  if (textField != null) {\n    const fieldCanBeFocused =\n      currentlyFocusedInputRef !== textField &&\n      // $FlowFixMe - `currentProps` is missing in `NativeMethods`\n      textField.currentProps?.editable !== false;\n\n    if (!fieldCanBeFocused) {\n      return;\n    }\n    focusInput(textField);\n    if (Platform.OS === 'ios') {\n      // This isn't necessarily a single line text input\n      // But commands don't actually care as long as the thing being passed in\n      // actually has a command with that name. So this should work with single\n      // and multiline text inputs. Ideally we'll merge them into one component\n      // in the future.\n      iOSTextInputCommands.focus(textField);\n    } else if (Platform.OS === 'android') {\n      AndroidTextInputCommands.focus(textField);\n    }\n  }\n}\n\n/**\n * @param {number} textFieldID id of the text field to unfocus\n * Unfocuses the specified text field\n * noop if it wasn't focused\n */\nfunction blurTextInput(textField: ?HostInstance) {\n  if (typeof textField === 'number') {\n    if (__DEV__) {\n      console.error(\n        'blurTextInput must be called with a host component. Passing a react tag is deprecated.',\n      );\n    }\n\n    return;\n  }\n\n  if (currentlyFocusedInputRef === textField && textField != null) {\n    blurInput(textField);\n    if (Platform.OS === 'ios') {\n      // This isn't necessarily a single line text input\n      // But commands don't actually care as long as the thing being passed in\n      // actually has a command with that name. So this should work with single\n      // and multiline text inputs. Ideally we'll merge them into one component\n      // in the future.\n      iOSTextInputCommands.blur(textField);\n    } else if (Platform.OS === 'android') {\n      AndroidTextInputCommands.blur(textField);\n    }\n  }\n}\n\nfunction registerInput(textField: HostInstance) {\n  if (typeof textField === 'number') {\n    if (__DEV__) {\n      console.error(\n        'registerInput must be called with a host component. Passing a react tag is deprecated.',\n      );\n    }\n\n    return;\n  }\n\n  inputs.add(textField);\n}\n\nfunction unregisterInput(textField: HostInstance) {\n  if (typeof textField === 'number') {\n    if (__DEV__) {\n      console.error(\n        'unregisterInput must be called with a host component. Passing a react tag is deprecated.',\n      );\n    }\n\n    return;\n  }\n  inputs.delete(textField);\n}\n\nfunction isTextInput(textField: HostInstance): boolean {\n  if (typeof textField === 'number') {\n    if (__DEV__) {\n      console.error(\n        'isTextInput must be called with a host component. Passing a react tag is deprecated.',\n      );\n    }\n\n    return false;\n  }\n\n  return inputs.has(textField);\n}\n\nconst TextInputState = {\n  currentlyFocusedInput,\n  focusInput,\n  blurInput,\n\n  currentlyFocusedField,\n  focusField,\n  blurField,\n  focusTextInput,\n  blurTextInput,\n  registerInput,\n  unregisterInput,\n  isTextInput,\n};\n\nexport default TextInputState;\n"], "mappings": ";;;;AAgBA,IAAAA,gCAAA,GAAAC,OAAA;AACA,IAAAC,qCAAA,GAAAD,OAAA;AAEA,IAAAE,QAAA,GAAyBF,OAAO,kCAAkC,CAAC;EAA5DG,cAAc,GAAAD,QAAA,CAAdC,cAAc;AACrB,IAAMC,QAAQ,GAAGJ,OAAO,2BAA2B,CAAC,CAACK,OAAO;AAE5D,IAAIC,wBAAuC,GAAG,IAAI;AAClD,IAAMC,MAAM,GAAG,IAAIC,GAAG,CAAe,CAAC;AAEtC,SAASC,qBAAqBA,CAAA,EAAkB;EAC9C,OAAOH,wBAAwB;AACjC;AAMA,SAASI,qBAAqBA,CAAA,EAAY;EACxC,IAAIC,OAAO,EAAE;IACXC,OAAO,CAACC,KAAK,CACX,wGACF,CAAC;EACH;EAEA,OAAOV,cAAc,CAACG,wBAAwB,CAAC;AACjD;AAEA,SAASQ,UAAUA,CAACC,SAAwB,EAAQ;EAClD,IAAIT,wBAAwB,KAAKS,SAAS,IAAIA,SAAS,IAAI,IAAI,EAAE;IAC/DT,wBAAwB,GAAGS,SAAS;EACtC;AACF;AAEA,SAASC,SAASA,CAACD,SAAwB,EAAQ;EACjD,IAAIT,wBAAwB,KAAKS,SAAS,IAAIA,SAAS,IAAI,IAAI,EAAE;IAC/DT,wBAAwB,GAAG,IAAI;EACjC;AACF;AAEA,SAASW,UAAUA,CAACC,WAAoB,EAAQ;EAC9C,IAAIP,OAAO,EAAE;IACXC,OAAO,CAACC,KAAK,CAAC,4CAA4C,CAAC;EAC7D;EAEA;AACF;AAEA,SAASM,SAASA,CAACD,WAAoB,EAAE;EACvC,IAAIP,OAAO,EAAE;IACXC,OAAO,CAACC,KAAK,CAAC,0CAA0C,CAAC;EAC3D;EAEA;AACF;AAOA,SAASO,cAAcA,CAACL,SAAwB,EAAE;EAChD,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;IACjC,IAAIJ,OAAO,EAAE;MACXC,OAAO,CAACC,KAAK,CACX,yFACF,CAAC;IACH;IAEA;EACF;EAEA,IAAIE,SAAS,IAAI,IAAI,EAAE;IAAA,IAAAM,qBAAA;IACrB,IAAMC,iBAAiB,GACrBhB,wBAAwB,KAAKS,SAAS,IAEtC,EAAAM,qBAAA,GAAAN,SAAS,CAACQ,YAAY,qBAAtBF,qBAAA,CAAwBG,QAAQ,MAAK,KAAK;IAE5C,IAAI,CAACF,iBAAiB,EAAE;MACtB;IACF;IACAR,UAAU,CAACC,SAAS,CAAC;IACrB,IAAIX,QAAQ,CAACqB,EAAE,KAAK,KAAK,EAAE;MAMzBC,8CAAoB,CAACC,KAAK,CAACZ,SAAS,CAAC;IACvC,CAAC,MAAM,IAAIX,QAAQ,CAACqB,EAAE,KAAK,SAAS,EAAE;MACpCG,yCAAwB,CAACD,KAAK,CAACZ,SAAS,CAAC;IAC3C;EACF;AACF;AAOA,SAASc,aAAaA,CAACd,SAAwB,EAAE;EAC/C,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;IACjC,IAAIJ,OAAO,EAAE;MACXC,OAAO,CAACC,KAAK,CACX,wFACF,CAAC;IACH;IAEA;EACF;EAEA,IAAIP,wBAAwB,KAAKS,SAAS,IAAIA,SAAS,IAAI,IAAI,EAAE;IAC/DC,SAAS,CAACD,SAAS,CAAC;IACpB,IAAIX,QAAQ,CAACqB,EAAE,KAAK,KAAK,EAAE;MAMzBC,8CAAoB,CAACI,IAAI,CAACf,SAAS,CAAC;IACtC,CAAC,MAAM,IAAIX,QAAQ,CAACqB,EAAE,KAAK,SAAS,EAAE;MACpCG,yCAAwB,CAACE,IAAI,CAACf,SAAS,CAAC;IAC1C;EACF;AACF;AAEA,SAASgB,aAAaA,CAAChB,SAAuB,EAAE;EAC9C,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;IACjC,IAAIJ,OAAO,EAAE;MACXC,OAAO,CAACC,KAAK,CACX,wFACF,CAAC;IACH;IAEA;EACF;EAEAN,MAAM,CAACyB,GAAG,CAACjB,SAAS,CAAC;AACvB;AAEA,SAASkB,eAAeA,CAAClB,SAAuB,EAAE;EAChD,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;IACjC,IAAIJ,OAAO,EAAE;MACXC,OAAO,CAACC,KAAK,CACX,0FACF,CAAC;IACH;IAEA;EACF;EACAN,MAAM,CAAC2B,MAAM,CAACnB,SAAS,CAAC;AAC1B;AAEA,SAASoB,WAAWA,CAACpB,SAAuB,EAAW;EACrD,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;IACjC,IAAIJ,OAAO,EAAE;MACXC,OAAO,CAACC,KAAK,CACX,sFACF,CAAC;IACH;IAEA,OAAO,KAAK;EACd;EAEA,OAAON,MAAM,CAAC6B,GAAG,CAACrB,SAAS,CAAC;AAC9B;AAEA,IAAMsB,cAAc,GAAG;EACrB5B,qBAAqB,EAArBA,qBAAqB;EACrBK,UAAU,EAAVA,UAAU;EACVE,SAAS,EAATA,SAAS;EAETN,qBAAqB,EAArBA,qBAAqB;EACrBO,UAAU,EAAVA,UAAU;EACVE,SAAS,EAATA,SAAS;EACTC,cAAc,EAAdA,cAAc;EACdS,aAAa,EAAbA,aAAa;EACbE,aAAa,EAAbA,aAAa;EACbE,eAAe,EAAfA,eAAe;EACfE,WAAW,EAAXA;AACF,CAAC;AAAC,IAAAG,QAAA,GAAAC,OAAA,CAAAlC,OAAA,GAEagC,cAAc", "ignoreList": []}