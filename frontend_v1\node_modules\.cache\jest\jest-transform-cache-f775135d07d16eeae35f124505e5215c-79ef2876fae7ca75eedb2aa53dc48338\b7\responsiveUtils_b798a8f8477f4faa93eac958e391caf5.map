{"version": 3, "names": ["_reactNative", "require", "_Dimensions$get", "Dimensions", "get", "SCREEN_WIDTH", "exports", "width", "SCREEN_HEIGHT", "height", "DeviceTypes", "PHONE", "TABLET", "SMALL_PHONE", "LARGE_PHONE", "Breakpoints", "LARGE_TABLET", "PlatformConstants", "iOS", "statusBarHeight", "Platform", "OS", "navigationBarHeight", "tabBarHeight", "safeAreaBottom", "borderRadius", "small", "medium", "large", "xlarge", "shadowStyle", "shadowColor", "shadowOffset", "shadowOpacity", "shadowRadius", "touchTargetSize", "Android", "StatusBar", "currentHeight", "elevation", "getDeviceType", "isTablet", "isSmallPhone", "isLargePhone", "hasNotch", "hasDynamicIsland", "isIPhoneX", "isIPhoneWithNotch", "isAndroidWithGestures", "Version", "getSafeAreaInsets", "top", "bottom", "left", "right", "getSafeAreaTop", "insets", "getSafeAreaBottom", "getResponsiveSpacing", "baseSpacing", "scale", "Math", "min", "round", "getResponsiveFontSize", "baseFontSize", "newSize", "getResponsiveIconSize", "baseSize", "getPrimaryTouchTarget", "getMinimumTouchTarget", "max", "getPlatformShadow", "arguments", "length", "undefined", "getPlatformBorderRadius", "size", "getScreenDimensions", "aspectRatio", "isLandscape", "isPortrait"], "sources": ["responsiveUtils.ts"], "sourcesContent": ["/**\n * Responsive Utilities - Device Compatibility & Safe Area Management\n *\n * Provides comprehensive utilities for handling device variations, safe areas,\n * and responsive design across iOS and Android platforms.\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport { Dimensions, Platform, StatusBar } from 'react-native';\n\n// Get screen dimensions\nconst { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');\n\n// Device type detection\nexport const DeviceTypes = {\n  PHONE: 'phone',\n  TABLET: 'tablet',\n  SMALL_PHONE: 'small_phone',\n  LARGE_PHONE: 'large_phone',\n} as const;\n\n// Screen size breakpoints\nexport const Breakpoints = {\n  SMALL_PHONE: 375,\n  LARGE_PHONE: 414,\n  TABLET: 768,\n  LARGE_TABLET: 1024,\n} as const;\n\n// Platform-specific constants\nexport const PlatformConstants = {\n  iOS: {\n    statusBarHeight:\n      Platform.OS === 'ios' ? (SCREEN_HEIGHT >= 812 ? 44 : 20) : 0,\n    navigationBarHeight:\n      Platform.OS === 'ios' ? (SCREEN_HEIGHT >= 812 ? 88 : 64) : 0,\n    tabBarHeight: Platform.OS === 'ios' ? (SCREEN_HEIGHT >= 812 ? 83 : 49) : 0,\n    safeAreaBottom: Platform.OS === 'ios' ? (SCREEN_HEIGHT >= 812 ? 34 : 0) : 0,\n    borderRadius: {\n      small: 8,\n      medium: 12,\n      large: 16,\n      xlarge: 20,\n    },\n    shadowStyle: {\n      shadowColor: '#000',\n      shadowOffset: { width: 0, height: 2 },\n      shadowOpacity: 0.1,\n      shadowRadius: 4,\n    },\n    touchTargetSize: 44, // Apple HIG minimum\n  },\n  Android: {\n    statusBarHeight: StatusBar.currentHeight || 24,\n    navigationBarHeight: 56,\n    tabBarHeight: 56,\n    safeAreaBottom: 0,\n    borderRadius: {\n      small: 4,\n      medium: 8,\n      large: 12,\n      xlarge: 16,\n    },\n    shadowStyle: {\n      elevation: 4,\n    },\n    touchTargetSize: 48, // Material Design minimum\n  },\n} as const;\n\n// Device detection functions\nexport const getDeviceType = (): string => {\n  if (SCREEN_WIDTH >= Breakpoints.TABLET) {\n    return DeviceTypes.TABLET;\n  } else if (SCREEN_WIDTH >= Breakpoints.LARGE_PHONE) {\n    return DeviceTypes.LARGE_PHONE;\n  } else if (SCREEN_WIDTH >= Breakpoints.SMALL_PHONE) {\n    return DeviceTypes.PHONE;\n  } else {\n    return DeviceTypes.SMALL_PHONE;\n  }\n};\n\nexport const isTablet = (): boolean => SCREEN_WIDTH >= Breakpoints.TABLET;\nexport const isSmallPhone = (): boolean =>\n  SCREEN_WIDTH < Breakpoints.SMALL_PHONE;\nexport const isLargePhone = (): boolean =>\n  SCREEN_WIDTH >= Breakpoints.LARGE_PHONE;\n\n// iOS device detection\nexport const hasNotch = (): boolean => {\n  return Platform.OS === 'ios' && SCREEN_HEIGHT >= 812;\n};\n\nexport const hasDynamicIsland = (): boolean => {\n  return Platform.OS === 'ios' && SCREEN_HEIGHT >= 852;\n};\n\nexport const isIPhoneX = (): boolean => {\n  return Platform.OS === 'ios' && SCREEN_HEIGHT === 812 && SCREEN_WIDTH === 375;\n};\n\n// Enhanced device detection functions\nexport const isIPhoneWithNotch = (): boolean => {\n  return Platform.OS === 'ios' && (hasNotch() || hasDynamicIsland());\n};\n\nexport const isAndroidWithGestures = (): boolean => {\n  // Detect Android devices with gesture navigation (Android 10+)\n  return Platform.OS === 'android' && Platform.Version >= 29;\n};\n\n// Safe area utilities\nexport const getSafeAreaInsets = () => {\n  if (Platform.OS === 'ios') {\n    if (hasDynamicIsland()) {\n      return {\n        top: 59,\n        bottom: 34,\n        left: 0,\n        right: 0,\n      };\n    } else if (hasNotch()) {\n      return {\n        top: 44,\n        bottom: 34,\n        left: 0,\n        right: 0,\n      };\n    } else {\n      return {\n        top: 20,\n        bottom: 0,\n        left: 0,\n        right: 0,\n      };\n    }\n  } else {\n    // Android\n    return {\n      top: StatusBar.currentHeight || 24,\n      bottom: 0,\n      left: 0,\n      right: 0,\n    };\n  }\n};\n\nexport const getSafeAreaTop = (): number => {\n  const insets = getSafeAreaInsets();\n  return insets.top;\n};\n\nexport const getSafeAreaBottom = (): number => {\n  const insets = getSafeAreaInsets();\n  return insets.bottom;\n};\n\n// Responsive spacing\nexport const getResponsiveSpacing = (baseSpacing: number): number => {\n  const scale = Math.min(SCREEN_WIDTH / 375, 1.2); // Cap scaling at 1.2x\n  return Math.round(baseSpacing * scale);\n};\n\n// Responsive font sizes\nexport const getResponsiveFontSize = (baseFontSize: number): number => {\n  const scale = SCREEN_WIDTH / 375;\n  const newSize = baseFontSize * scale;\n\n  // Ensure minimum and maximum font sizes\n  if (newSize < 12) return 12;\n  if (newSize > 32) return 32;\n\n  return Math.round(newSize);\n};\n\n// Responsive icon sizes\nexport const getResponsiveIconSize = (baseSize: number): number => {\n  const scale = Math.min(SCREEN_WIDTH / 375, 1.1); // Smaller scaling for icons\n  return Math.round(baseSize * scale);\n};\n\n// Touch target utilities\nexport const getPrimaryTouchTarget = (): number => {\n  return Platform.OS === 'ios'\n    ? PlatformConstants.iOS.touchTargetSize\n    : PlatformConstants.Android.touchTargetSize;\n};\n\nexport const getMinimumTouchTarget = (): number => {\n  return Math.max(44, getPrimaryTouchTarget());\n};\n\n// Platform-specific style utilities\nexport const getPlatformShadow = (elevation: number = 4) => {\n  if (Platform.OS === 'ios') {\n    return {\n      shadowColor: '#000',\n      shadowOffset: { width: 0, height: elevation / 2 },\n      shadowOpacity: 0.1,\n      shadowRadius: elevation,\n    };\n  } else {\n    return {\n      elevation,\n    };\n  }\n};\n\nexport const getPlatformBorderRadius = (\n  size: 'small' | 'medium' | 'large' | 'xlarge' = 'medium',\n): number => {\n  return Platform.OS === 'ios'\n    ? PlatformConstants.iOS.borderRadius[size]\n    : PlatformConstants.Android.borderRadius[size];\n};\n\n// Screen dimension utilities\nexport const getScreenDimensions = () => ({\n  width: SCREEN_WIDTH,\n  height: SCREEN_HEIGHT,\n  aspectRatio: SCREEN_WIDTH / SCREEN_HEIGHT,\n});\n\n// Orientation utilities\nexport const isLandscape = (): boolean => SCREEN_WIDTH > SCREEN_HEIGHT;\nexport const isPortrait = (): boolean => SCREEN_HEIGHT > SCREEN_WIDTH;\n\n// Export screen dimensions for convenience\nexport { SCREEN_WIDTH, SCREEN_HEIGHT };\n"], "mappings": ";;;;AAUA,IAAAA,YAAA,GAAAC,OAAA;AAGA,IAAAC,eAAA,GAAuDC,uBAAU,CAACC,GAAG,CAAC,QAAQ,CAAC;EAAhEC,YAAY,GAAAC,OAAA,CAAAD,YAAA,GAAAH,eAAA,CAAnBK,KAAK;EAAwBC,aAAa,GAAAF,OAAA,CAAAE,aAAA,GAAAN,eAAA,CAArBO,MAAM;AAG5B,IAAMC,WAAW,GAAAJ,OAAA,CAAAI,WAAA,GAAG;EACzBC,KAAK,EAAE,OAAO;EACdC,MAAM,EAAE,QAAQ;EAChBC,WAAW,EAAE,aAAa;EAC1BC,WAAW,EAAE;AACf,CAAU;AAGH,IAAMC,WAAW,GAAAT,OAAA,CAAAS,WAAA,GAAG;EACzBF,WAAW,EAAE,GAAG;EAChBC,WAAW,EAAE,GAAG;EAChBF,MAAM,EAAE,GAAG;EACXI,YAAY,EAAE;AAChB,CAAU;AAGH,IAAMC,iBAAiB,GAAAX,OAAA,CAAAW,iBAAA,GAAG;EAC/BC,GAAG,EAAE;IACHC,eAAe,EACbC,qBAAQ,CAACC,EAAE,KAAK,KAAK,GAAIb,aAAa,IAAI,GAAG,GAAG,EAAE,GAAG,EAAE,GAAI,CAAC;IAC9Dc,mBAAmB,EACjBF,qBAAQ,CAACC,EAAE,KAAK,KAAK,GAAIb,aAAa,IAAI,GAAG,GAAG,EAAE,GAAG,EAAE,GAAI,CAAC;IAC9De,YAAY,EAAEH,qBAAQ,CAACC,EAAE,KAAK,KAAK,GAAIb,aAAa,IAAI,GAAG,GAAG,EAAE,GAAG,EAAE,GAAI,CAAC;IAC1EgB,cAAc,EAAEJ,qBAAQ,CAACC,EAAE,KAAK,KAAK,GAAIb,aAAa,IAAI,GAAG,GAAG,EAAE,GAAG,CAAC,GAAI,CAAC;IAC3EiB,YAAY,EAAE;MACZC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE;IACV,CAAC;IACDC,WAAW,EAAE;MACXC,WAAW,EAAE,MAAM;MACnBC,YAAY,EAAE;QAAEzB,KAAK,EAAE,CAAC;QAAEE,MAAM,EAAE;MAAE,CAAC;MACrCwB,aAAa,EAAE,GAAG;MAClBC,YAAY,EAAE;IAChB,CAAC;IACDC,eAAe,EAAE;EACnB,CAAC;EACDC,OAAO,EAAE;IACPjB,eAAe,EAAEkB,sBAAS,CAACC,aAAa,IAAI,EAAE;IAC9ChB,mBAAmB,EAAE,EAAE;IACvBC,YAAY,EAAE,EAAE;IAChBC,cAAc,EAAE,CAAC;IACjBC,YAAY,EAAE;MACZC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE;IACV,CAAC;IACDC,WAAW,EAAE;MACXS,SAAS,EAAE;IACb,CAAC;IACDJ,eAAe,EAAE;EACnB;AACF,CAAU;AAGH,IAAMK,aAAa,GAAAlC,OAAA,CAAAkC,aAAA,GAAG,SAAhBA,aAAaA,CAAA,EAAiB;EACzC,IAAInC,YAAY,IAAIU,WAAW,CAACH,MAAM,EAAE;IACtC,OAAOF,WAAW,CAACE,MAAM;EAC3B,CAAC,MAAM,IAAIP,YAAY,IAAIU,WAAW,CAACD,WAAW,EAAE;IAClD,OAAOJ,WAAW,CAACI,WAAW;EAChC,CAAC,MAAM,IAAIT,YAAY,IAAIU,WAAW,CAACF,WAAW,EAAE;IAClD,OAAOH,WAAW,CAACC,KAAK;EAC1B,CAAC,MAAM;IACL,OAAOD,WAAW,CAACG,WAAW;EAChC;AACF,CAAC;AAEM,IAAM4B,QAAQ,GAAAnC,OAAA,CAAAmC,QAAA,GAAG,SAAXA,QAAQA,CAAA;EAAA,OAAkBpC,YAAY,IAAIU,WAAW,CAACH,MAAM;AAAA;AAClE,IAAM8B,YAAY,GAAApC,OAAA,CAAAoC,YAAA,GAAG,SAAfA,YAAYA,CAAA;EAAA,OACvBrC,YAAY,GAAGU,WAAW,CAACF,WAAW;AAAA;AACjC,IAAM8B,YAAY,GAAArC,OAAA,CAAAqC,YAAA,GAAG,SAAfA,YAAYA,CAAA;EAAA,OACvBtC,YAAY,IAAIU,WAAW,CAACD,WAAW;AAAA;AAGlC,IAAM8B,QAAQ,GAAAtC,OAAA,CAAAsC,QAAA,GAAG,SAAXA,QAAQA,CAAA,EAAkB;EACrC,OAAOxB,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAIb,aAAa,IAAI,GAAG;AACtD,CAAC;AAEM,IAAMqC,gBAAgB,GAAAvC,OAAA,CAAAuC,gBAAA,GAAG,SAAnBA,gBAAgBA,CAAA,EAAkB;EAC7C,OAAOzB,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAIb,aAAa,IAAI,GAAG;AACtD,CAAC;AAEM,IAAMsC,SAAS,GAAAxC,OAAA,CAAAwC,SAAA,GAAG,SAAZA,SAASA,CAAA,EAAkB;EACtC,OAAO1B,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAIb,aAAa,KAAK,GAAG,IAAIH,YAAY,KAAK,GAAG;AAC/E,CAAC;AAGM,IAAM0C,iBAAiB,GAAAzC,OAAA,CAAAyC,iBAAA,GAAG,SAApBA,iBAAiBA,CAAA,EAAkB;EAC9C,OAAO3B,qBAAQ,CAACC,EAAE,KAAK,KAAK,KAAKuB,QAAQ,CAAC,CAAC,IAAIC,gBAAgB,CAAC,CAAC,CAAC;AACpE,CAAC;AAEM,IAAMG,qBAAqB,GAAA1C,OAAA,CAAA0C,qBAAA,GAAG,SAAxBA,qBAAqBA,CAAA,EAAkB;EAElD,OAAO5B,qBAAQ,CAACC,EAAE,KAAK,SAAS,IAAID,qBAAQ,CAAC6B,OAAO,IAAI,EAAE;AAC5D,CAAC;AAGM,IAAMC,iBAAiB,GAAA5C,OAAA,CAAA4C,iBAAA,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;EACrC,IAAI9B,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IACzB,IAAIwB,gBAAgB,CAAC,CAAC,EAAE;MACtB,OAAO;QACLM,GAAG,EAAE,EAAE;QACPC,MAAM,EAAE,EAAE;QACVC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE;MACT,CAAC;IACH,CAAC,MAAM,IAAIV,QAAQ,CAAC,CAAC,EAAE;MACrB,OAAO;QACLO,GAAG,EAAE,EAAE;QACPC,MAAM,EAAE,EAAE;QACVC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE;MACT,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACLH,GAAG,EAAE,EAAE;QACPC,MAAM,EAAE,CAAC;QACTC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE;MACT,CAAC;IACH;EACF,CAAC,MAAM;IAEL,OAAO;MACLH,GAAG,EAAEd,sBAAS,CAACC,aAAa,IAAI,EAAE;MAClCc,MAAM,EAAE,CAAC;MACTC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE;IACT,CAAC;EACH;AACF,CAAC;AAEM,IAAMC,cAAc,GAAAjD,OAAA,CAAAiD,cAAA,GAAG,SAAjBA,cAAcA,CAAA,EAAiB;EAC1C,IAAMC,MAAM,GAAGN,iBAAiB,CAAC,CAAC;EAClC,OAAOM,MAAM,CAACL,GAAG;AACnB,CAAC;AAEM,IAAMM,iBAAiB,GAAAnD,OAAA,CAAAmD,iBAAA,GAAG,SAApBA,iBAAiBA,CAAA,EAAiB;EAC7C,IAAMD,MAAM,GAAGN,iBAAiB,CAAC,CAAC;EAClC,OAAOM,MAAM,CAACJ,MAAM;AACtB,CAAC;AAGM,IAAMM,oBAAoB,GAAApD,OAAA,CAAAoD,oBAAA,GAAG,SAAvBA,oBAAoBA,CAAIC,WAAmB,EAAa;EACnE,IAAMC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACzD,YAAY,GAAG,GAAG,EAAE,GAAG,CAAC;EAC/C,OAAOwD,IAAI,CAACE,KAAK,CAACJ,WAAW,GAAGC,KAAK,CAAC;AACxC,CAAC;AAGM,IAAMI,qBAAqB,GAAA1D,OAAA,CAAA0D,qBAAA,GAAG,SAAxBA,qBAAqBA,CAAIC,YAAoB,EAAa;EACrE,IAAML,KAAK,GAAGvD,YAAY,GAAG,GAAG;EAChC,IAAM6D,OAAO,GAAGD,YAAY,GAAGL,KAAK;EAGpC,IAAIM,OAAO,GAAG,EAAE,EAAE,OAAO,EAAE;EAC3B,IAAIA,OAAO,GAAG,EAAE,EAAE,OAAO,EAAE;EAE3B,OAAOL,IAAI,CAACE,KAAK,CAACG,OAAO,CAAC;AAC5B,CAAC;AAGM,IAAMC,qBAAqB,GAAA7D,OAAA,CAAA6D,qBAAA,GAAG,SAAxBA,qBAAqBA,CAAIC,QAAgB,EAAa;EACjE,IAAMR,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACzD,YAAY,GAAG,GAAG,EAAE,GAAG,CAAC;EAC/C,OAAOwD,IAAI,CAACE,KAAK,CAACK,QAAQ,GAAGR,KAAK,CAAC;AACrC,CAAC;AAGM,IAAMS,qBAAqB,GAAA/D,OAAA,CAAA+D,qBAAA,GAAG,SAAxBA,qBAAqBA,CAAA,EAAiB;EACjD,OAAOjD,qBAAQ,CAACC,EAAE,KAAK,KAAK,GACxBJ,iBAAiB,CAACC,GAAG,CAACiB,eAAe,GACrClB,iBAAiB,CAACmB,OAAO,CAACD,eAAe;AAC/C,CAAC;AAEM,IAAMmC,qBAAqB,GAAAhE,OAAA,CAAAgE,qBAAA,GAAG,SAAxBA,qBAAqBA,CAAA,EAAiB;EACjD,OAAOT,IAAI,CAACU,GAAG,CAAC,EAAE,EAAEF,qBAAqB,CAAC,CAAC,CAAC;AAC9C,CAAC;AAGM,IAAMG,iBAAiB,GAAAlE,OAAA,CAAAkE,iBAAA,GAAG,SAApBA,iBAAiBA,CAAA,EAA8B;EAAA,IAA1BjC,SAAiB,GAAAkC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EACrD,IAAIrD,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IACzB,OAAO;MACLU,WAAW,EAAE,MAAM;MACnBC,YAAY,EAAE;QAAEzB,KAAK,EAAE,CAAC;QAAEE,MAAM,EAAE8B,SAAS,GAAG;MAAE,CAAC;MACjDN,aAAa,EAAE,GAAG;MAClBC,YAAY,EAAEK;IAChB,CAAC;EACH,CAAC,MAAM;IACL,OAAO;MACLA,SAAS,EAATA;IACF,CAAC;EACH;AACF,CAAC;AAEM,IAAMqC,uBAAuB,GAAAtE,OAAA,CAAAsE,uBAAA,GAAG,SAA1BA,uBAAuBA,CAAA,EAEvB;EAAA,IADXC,IAA6C,GAAAJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,QAAQ;EAExD,OAAOrD,qBAAQ,CAACC,EAAE,KAAK,KAAK,GACxBJ,iBAAiB,CAACC,GAAG,CAACO,YAAY,CAACoD,IAAI,CAAC,GACxC5D,iBAAiB,CAACmB,OAAO,CAACX,YAAY,CAACoD,IAAI,CAAC;AAClD,CAAC;AAGM,IAAMC,mBAAmB,GAAAxE,OAAA,CAAAwE,mBAAA,GAAG,SAAtBA,mBAAmBA,CAAA;EAAA,OAAU;IACxCvE,KAAK,EAAEF,YAAY;IACnBI,MAAM,EAAED,aAAa;IACrBuE,WAAW,EAAE1E,YAAY,GAAGG;EAC9B,CAAC;AAAA,CAAC;AAGK,IAAMwE,WAAW,GAAA1E,OAAA,CAAA0E,WAAA,GAAG,SAAdA,WAAWA,CAAA;EAAA,OAAkB3E,YAAY,GAAGG,aAAa;AAAA;AAC/D,IAAMyE,UAAU,GAAA3E,OAAA,CAAA2E,UAAA,GAAG,SAAbA,UAAUA,CAAA;EAAA,OAAkBzE,aAAa,GAAGH,YAAY;AAAA", "ignoreList": []}