f990ed2883d001c67af4df2f1d11826a
'use strict';

_getJestObj().mock("../Libraries/Core/InitializeCore", function () {}).mock('../Libraries/Core/NativeExceptionsManager').mock('../Libraries/ReactNative/UIManager', function () {
  return {
    __esModule: true,
    default: {
      AndroidViewPager: {
        Commands: {
          setPage: jest.fn(),
          setPageWithoutAnimation: jest.fn()
        }
      },
      blur: jest.fn(),
      createView: jest.fn(),
      customBubblingEventTypes: {},
      customDirectEventTypes: {},
      dispatchViewManagerCommand: jest.fn(),
      focus: jest.fn(),
      getViewManagerConfig: jest.fn(function (name) {
        if (name === 'AndroidDrawerLayout') {
          return {
            Constants: {
              DrawerPosition: {
                Left: 10
              }
            }
          };
        }
      }),
      hasViewManagerConfig: jest.fn(function (name) {
        return name === 'AndroidDrawerLayout';
      }),
      measure: jest.fn(),
      manageChildren: jest.fn(),
      setChildren: jest.fn(),
      updateView: jest.fn(),
      AndroidDrawerLayout: {
        Constants: {
          DrawerPosition: {
            Left: 10
          }
        }
      },
      AndroidTextInput: {
        Commands: {}
      },
      ScrollView: {
        Constants: {}
      },
      View: {
        Constants: {}
      }
    }
  };
}).mock('../Libraries/Image/Image', function () {
  return {
    __esModule: true,
    default: mockComponent('../Libraries/Image/Image', null, true)
  };
}).mock('../Libraries/Text/Text', function () {
  return {
    __esModule: true,
    default: mockComponent('../Libraries/Text/Text', MockNativeMethods, true)
  };
}).mock('../Libraries/Components/TextInput/TextInput', function () {
  return {
    __esModule: true,
    default: mockComponent('../Libraries/Components/TextInput/TextInput', Object.assign({}, MockNativeMethods, {
      isFocused: jest.fn(),
      clear: jest.fn(),
      getNativeRef: jest.fn()
    }), true)
  };
}).mock('../Libraries/Modal/Modal', function () {
  var baseComponent = mockComponent('../Libraries/Modal/Modal', null, true);
  var mockModal = jest.requireActual("./mockModal");
  return {
    __esModule: true,
    default: mockModal(baseComponent)
  };
}).mock('../Libraries/Components/View/View', function () {
  return {
    __esModule: true,
    default: mockComponent('../Libraries/Components/View/View', MockNativeMethods, true)
  };
}).mock('../Libraries/Components/AccessibilityInfo/AccessibilityInfo', function () {
  return {
    __esModule: true,
    default: {
      addEventListener: jest.fn(function () {
        return {
          remove: jest.fn()
        };
      }),
      announceForAccessibility: jest.fn(),
      announceForAccessibilityWithOptions: jest.fn(),
      isAccessibilityServiceEnabled: jest.fn(function () {
        return Promise.resolve(false);
      }),
      isBoldTextEnabled: jest.fn(function () {
        return Promise.resolve(false);
      }),
      isGrayscaleEnabled: jest.fn(function () {
        return Promise.resolve(false);
      }),
      isInvertColorsEnabled: jest.fn(function () {
        return Promise.resolve(false);
      }),
      isReduceMotionEnabled: jest.fn(function () {
        return Promise.resolve(false);
      }),
      isHighTextContrastEnabled: jest.fn(function () {
        return Promise.resolve(false);
      }),
      isDarkerSystemColorsEnabled: jest.fn(function () {
        return Promise.resolve(false);
      }),
      prefersCrossFadeTransitions: jest.fn(function () {
        return Promise.resolve(false);
      }),
      isReduceTransparencyEnabled: jest.fn(function () {
        return Promise.resolve(false);
      }),
      isScreenReaderEnabled: jest.fn(function () {
        return Promise.resolve(false);
      }),
      setAccessibilityFocus: jest.fn(),
      sendAccessibilityEvent: jest.fn(),
      getRecommendedTimeoutMillis: jest.fn(function () {
        return Promise.resolve(false);
      })
    }
  };
}).mock('../Libraries/Components/Clipboard/Clipboard', function () {
  return {
    __esModule: true,
    default: {
      getString: jest.fn(function () {
        return '';
      }),
      setString: jest.fn()
    }
  };
}).mock('../Libraries/Components/RefreshControl/RefreshControl', function () {
  return {
    __esModule: true,
    default: jest.requireActual("../Libraries/Components/RefreshControl/__mocks__/RefreshControlMock")
  };
}).mock('../Libraries/Components/ScrollView/ScrollView', function () {
  var baseComponent = mockComponent('../Libraries/Components/ScrollView/ScrollView', Object.assign({}, MockNativeMethods, {
    getScrollResponder: jest.fn(),
    getScrollableNode: jest.fn(),
    getInnerViewNode: jest.fn(),
    getInnerViewRef: jest.fn(),
    getNativeScrollRef: jest.fn(),
    scrollTo: jest.fn(),
    scrollToEnd: jest.fn(),
    flashScrollIndicators: jest.fn(),
    scrollResponderZoomTo: jest.fn(),
    scrollResponderScrollNativeHandleToKeyboard: jest.fn()
  }), true);
  var mockScrollView = jest.requireActual("./mockScrollView");
  return {
    __esModule: true,
    default: mockScrollView(baseComponent)
  };
}).mock('../Libraries/Components/ActivityIndicator/ActivityIndicator', function () {
  return {
    __esModule: true,
    default: mockComponent('../Libraries/Components/ActivityIndicator/ActivityIndicator', null, true)
  };
}).mock('../Libraries/AppState/AppState', function () {
  return {
    __esModule: true,
    default: {
      addEventListener: jest.fn(function () {
        return {
          remove: jest.fn()
        };
      }),
      removeEventListener: jest.fn(),
      currentState: jest.fn()
    }
  };
}).mock('../Libraries/Linking/Linking', function () {
  return {
    __esModule: true,
    default: {
      openURL: jest.fn(),
      canOpenURL: jest.fn(function () {
        return Promise.resolve(true);
      }),
      openSettings: jest.fn(),
      addEventListener: jest.fn(function () {
        return {
          remove: jest.fn()
        };
      }),
      getInitialURL: jest.fn(function () {
        return Promise.resolve();
      }),
      sendIntent: jest.fn()
    }
  };
}).mock('../Libraries/BatchedBridge/NativeModules', function () {
  return {
    __esModule: true,
    default: {
      AlertManager: {
        alertWithArgs: jest.fn()
      },
      AsyncLocalStorage: {
        multiGet: jest.fn(function (keys, callback) {
          return process.nextTick(function () {
            return callback(null, []);
          });
        }),
        multiSet: jest.fn(function (entries, callback) {
          return process.nextTick(function () {
            return callback(null);
          });
        }),
        multiRemove: jest.fn(function (keys, callback) {
          return process.nextTick(function () {
            return callback(null);
          });
        }),
        multiMerge: jest.fn(function (entries, callback) {
          return process.nextTick(function () {
            return callback(null);
          });
        }),
        clear: jest.fn(function (callback) {
          return process.nextTick(function () {
            return callback(null);
          });
        }),
        getAllKeys: jest.fn(function (callback) {
          return process.nextTick(function () {
            return callback(null, []);
          });
        })
      },
      DeviceInfo: {
        getConstants: function getConstants() {
          return {
            Dimensions: {
              window: {
                fontScale: 2,
                height: 1334,
                scale: 2,
                width: 750
              },
              screen: {
                fontScale: 2,
                height: 1334,
                scale: 2,
                width: 750
              }
            }
          };
        }
      },
      DevSettings: {
        addMenuItem: jest.fn(),
        reload: jest.fn()
      },
      ImageLoader: {
        getSize: jest.fn(function (url) {
          return Promise.resolve([320, 240]);
        }),
        getSizeWithHeaders: jest.fn(function (url, headers) {
          return Promise.resolve({
            height: 222,
            width: 333
          });
        }),
        prefetchImage: jest.fn(),
        prefetchImageWithMetadata: jest.fn(),
        queryCache: jest.fn()
      },
      ImageViewManager: {
        getSize: jest.fn(function (uri, success) {
          return process.nextTick(function () {
            return success(320, 240);
          });
        }),
        prefetchImage: jest.fn()
      },
      KeyboardObserver: {
        addListener: jest.fn(),
        removeListeners: jest.fn()
      },
      NativeAnimatedModule: {
        createAnimatedNode: jest.fn(),
        updateAnimatedNodeConfig: jest.fn(),
        getValue: jest.fn(),
        startListeningToAnimatedNodeValue: jest.fn(),
        stopListeningToAnimatedNodeValue: jest.fn(),
        connectAnimatedNodes: jest.fn(),
        disconnectAnimatedNodes: jest.fn(),
        startAnimatingNode: jest.fn(function (animationId, nodeTag, config, endCallback) {
          setTimeout(function () {
            return endCallback({
              finished: true
            });
          }, 16);
        }),
        stopAnimation: jest.fn(),
        setAnimatedNodeValue: jest.fn(),
        setAnimatedNodeOffset: jest.fn(),
        flattenAnimatedNodeOffset: jest.fn(),
        extractAnimatedNodeOffset: jest.fn(),
        connectAnimatedNodeToView: jest.fn(),
        disconnectAnimatedNodeFromView: jest.fn(),
        restoreDefaultValues: jest.fn(),
        dropAnimatedNode: jest.fn(),
        addAnimatedEventToView: jest.fn(),
        removeAnimatedEventFromView: jest.fn(),
        addListener: jest.fn(),
        removeListener: jest.fn(),
        removeListeners: jest.fn()
      },
      Networking: {
        sendRequest: jest.fn(),
        abortRequest: jest.fn(),
        addListener: jest.fn(),
        removeListeners: jest.fn()
      },
      PlatformConstants: {
        getConstants: function getConstants() {
          return {
            reactNativeVersion: {
              major: 1000,
              minor: 0,
              patch: 0,
              prerelease: undefined
            }
          };
        }
      },
      PushNotificationManager: {
        presentLocalNotification: jest.fn(),
        scheduleLocalNotification: jest.fn(),
        cancelAllLocalNotifications: jest.fn(),
        removeAllDeliveredNotifications: jest.fn(),
        getDeliveredNotifications: jest.fn(function (callback) {
          return process.nextTick(function () {
            return [];
          });
        }),
        removeDeliveredNotifications: jest.fn(),
        setApplicationIconBadgeNumber: jest.fn(),
        getApplicationIconBadgeNumber: jest.fn(function (callback) {
          return process.nextTick(function () {
            return callback(0);
          });
        }),
        cancelLocalNotifications: jest.fn(),
        getScheduledLocalNotifications: jest.fn(function (callback) {
          return process.nextTick(function () {
            return callback();
          });
        }),
        requestPermissions: jest.fn(function () {
          return Promise.resolve({
            alert: true,
            badge: true,
            sound: true
          });
        }),
        abandonPermissions: jest.fn(),
        checkPermissions: jest.fn(function (callback) {
          return process.nextTick(function () {
            return callback({
              alert: true,
              badge: true,
              sound: true
            });
          });
        }),
        getInitialNotification: jest.fn(function () {
          return Promise.resolve(null);
        }),
        addListener: jest.fn(),
        removeListeners: jest.fn()
      },
      SourceCode: {
        getConstants: function getConstants() {
          return {
            scriptURL: null
          };
        }
      },
      StatusBarManager: {
        setColor: jest.fn(),
        setStyle: jest.fn(),
        setHidden: jest.fn(),
        setNetworkActivityIndicatorVisible: jest.fn(),
        setBackgroundColor: jest.fn(),
        setTranslucent: jest.fn(),
        getConstants: function getConstants() {
          return {
            HEIGHT: 42
          };
        }
      },
      Timing: {
        createTimer: jest.fn(),
        deleteTimer: jest.fn()
      },
      UIManager: {},
      BlobModule: {
        getConstants: function getConstants() {
          return {
            BLOB_URI_SCHEME: 'content',
            BLOB_URI_HOST: null
          };
        },
        addNetworkingHandler: jest.fn(),
        enableBlobSupport: jest.fn(),
        disableBlobSupport: jest.fn(),
        createFromParts: jest.fn(),
        sendBlob: jest.fn(),
        release: jest.fn()
      },
      WebSocketModule: {
        connect: jest.fn(),
        send: jest.fn(),
        sendBinary: jest.fn(),
        ping: jest.fn(),
        close: jest.fn(),
        addListener: jest.fn(),
        removeListeners: jest.fn()
      },
      I18nManager: {
        allowRTL: jest.fn(),
        forceRTL: jest.fn(),
        swapLeftAndRightInRTL: jest.fn(),
        getConstants: function getConstants() {
          return {
            isRTL: false,
            doLeftAndRightSwapInRTL: true
          };
        }
      }
    }
  };
}).mock('../Libraries/NativeComponent/NativeComponentRegistry', function () {
  return {
    get: jest.fn(function (name, viewConfigProvider) {
      return jest.requireActual("./mockNativeComponent").default(name);
    }),
    getWithFallback_DEPRECATED: jest.fn(function (name, viewConfigProvider) {
      return jest.requireActual("./mockNativeComponent").default(name);
    }),
    setRuntimeConfigProvider: jest.fn()
  };
}).mock('../Libraries/ReactNative/requireNativeComponent', function () {
  return jest.requireActual("./mockNativeComponent");
}).mock('../Libraries/Vibration/Vibration', function () {
  return {
    __esModule: true,
    default: {
      vibrate: jest.fn(),
      cancel: jest.fn()
    }
  };
}).mock('../Libraries/Components/View/ViewNativeComponent', function () {
  var React = require('react');
  var Component = function (_React$Component) {
    function Component() {
      (0, _classCallCheck2.default)(this, Component);
      return _callSuper(this, Component, arguments);
    }
    (0, _inherits2.default)(Component, _React$Component);
    return (0, _createClass2.default)(Component, [{
      key: "render",
      value: function render() {
        return React.createElement('View', this.props, this.props.children);
      }
    }]);
  }(React.Component);
  Component.displayName = 'View';
  return {
    __esModule: true,
    default: Component
  };
}).mock('../Libraries/ReactNative/RendererProxy', function () {
  return jest.requireActual("../Libraries/ReactNative/RendererImplementation");
}).mock('../Libraries/Utilities/useColorScheme', function () {
  return {
    __esModule: true,
    default: jest.fn().mockReturnValue('light')
  };
});
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
global.IS_REACT_ACT_ENVIRONMENT = true;
global.IS_REACT_NATIVE_TEST_ENVIRONMENT = true;
var MockNativeMethods = jest.requireActual("./MockNativeMethods");
var mockComponent = jest.requireActual("./mockComponent");
jest.requireActual('@react-native/js-polyfills/error-guard');
Object.defineProperties(global, {
  __DEV__: {
    configurable: true,
    enumerable: true,
    value: true,
    writable: true
  },
  cancelAnimationFrame: {
    configurable: true,
    enumerable: true,
    value: function value(id) {
      return clearTimeout(id);
    },
    writable: true
  },
  nativeFabricUIManager: {
    configurable: true,
    enumerable: true,
    value: {},
    writable: true
  },
  performance: {
    configurable: true,
    enumerable: true,
    value: {
      now: jest.fn(Date.now)
    },
    writable: true
  },
  regeneratorRuntime: {
    configurable: true,
    enumerable: true,
    value: jest.requireActual('regenerator-runtime/runtime'),
    writable: true
  },
  requestAnimationFrame: {
    configurable: true,
    enumerable: true,
    value: function value(callback) {
      return setTimeout(function () {
        return callback(jest.now());
      }, 0);
    },
    writable: true
  },
  window: {
    configurable: true,
    enumerable: true,
    value: global,
    writable: true
  }
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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