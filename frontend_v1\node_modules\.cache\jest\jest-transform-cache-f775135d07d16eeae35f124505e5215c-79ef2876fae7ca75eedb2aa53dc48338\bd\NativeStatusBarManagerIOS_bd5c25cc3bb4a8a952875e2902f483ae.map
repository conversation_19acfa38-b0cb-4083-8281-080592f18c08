{"version": 3, "names": ["TurboModuleRegistry", "_interopRequireWildcard", "require", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "NativeModule", "getEnforcing", "constants", "NativeStatusBarManager", "getConstants", "getHeight", "callback", "setNetworkActivityIndicatorVisible", "visible", "addListener", "eventType", "removeListeners", "count", "setStyle", "statusBarStyle", "animated", "setHidden", "hidden", "withAnimation", "_default", "exports"], "sources": ["NativeStatusBarManagerIOS.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\nimport type {TurboModule} from '../../../../Libraries/TurboModule/RCTExport';\n\nimport * as TurboModuleRegistry from '../../../../Libraries/TurboModule/TurboModuleRegistry';\n\nexport interface Spec extends TurboModule {\n  +getConstants: () => {\n    +HEIGHT: number,\n    +DEFAULT_BACKGROUND_COLOR?: number,\n  };\n\n  // TODO(*********) Can we remove this method?\n  +getHeight: (callback: (result: {height: number}) => void) => void;\n  +setNetworkActivityIndicatorVisible: (visible: boolean) => void;\n  +addListener: (eventType: string) => void;\n  +removeListeners: (count: number) => void;\n\n  /**\n   *  - statusBarStyles can be:\n   *    - 'default'\n   *    - 'dark-content'\n   *    - 'light-content'\n   */\n  +setStyle: (statusBarStyle?: ?string, animated: boolean) => void;\n  /**\n   *  - withAnimation can be: 'none' | 'fade' | 'slide'\n   */\n  +setHidden: (hidden: boolean, withAnimation: string) => void;\n}\n\nconst NativeModule = TurboModuleRegistry.getEnforcing<Spec>('StatusBarManager');\nlet constants = null;\n\nconst NativeStatusBarManager = {\n  getConstants(): {\n    +HEIGHT: number,\n    +DEFAULT_BACKGROUND_COLOR?: number,\n  } {\n    if (constants == null) {\n      constants = NativeModule.getConstants();\n    }\n    return constants;\n  },\n\n  // TODO(*********) Can we remove this method?\n  getHeight(callback: (result: {height: number}) => void): void {\n    NativeModule.getHeight(callback);\n  },\n\n  setNetworkActivityIndicatorVisible(visible: boolean): void {\n    NativeModule.setNetworkActivityIndicatorVisible(visible);\n  },\n\n  addListener(eventType: string): void {\n    NativeModule.addListener(eventType);\n  },\n\n  removeListeners(count: number): void {\n    NativeModule.removeListeners(count);\n  },\n\n  /**\n   *  - statusBarStyles can be:\n   *    - 'default'\n   *    - 'dark-content'\n   *    - 'light-content'\n   */\n  setStyle(statusBarStyle?: ?string, animated: boolean): void {\n    NativeModule.setStyle(statusBarStyle, animated);\n  },\n\n  /**\n   *  - withAnimation can be: 'none' | 'fade' | 'slide'\n   */\n  setHidden(hidden: boolean, withAnimation: string): void {\n    NativeModule.setHidden(hidden, withAnimation);\n  },\n};\n\nexport default NativeStatusBarManager;\n"], "mappings": ";;;;AAYA,IAAAA,mBAAA,GAAAC,uBAAA,CAAAC,OAAA;AAA6F,SAAAD,wBAAAE,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAJ,uBAAA,YAAAA,wBAAAE,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AA2B7F,IAAMmB,YAAY,GAAGvB,mBAAmB,CAACwB,YAAY,CAAO,kBAAkB,CAAC;AAC/E,IAAIC,SAAS,GAAG,IAAI;AAEpB,IAAMC,sBAAsB,GAAG;EAC7BC,YAAY,WAAZA,YAAYA,CAAA,EAGV;IACA,IAAIF,SAAS,IAAI,IAAI,EAAE;MACrBA,SAAS,GAAGF,YAAY,CAACI,YAAY,CAAC,CAAC;IACzC;IACA,OAAOF,SAAS;EAClB,CAAC;EAGDG,SAAS,WAATA,SAASA,CAACC,QAA4C,EAAQ;IAC5DN,YAAY,CAACK,SAAS,CAACC,QAAQ,CAAC;EAClC,CAAC;EAEDC,kCAAkC,WAAlCA,kCAAkCA,CAACC,OAAgB,EAAQ;IACzDR,YAAY,CAACO,kCAAkC,CAACC,OAAO,CAAC;EAC1D,CAAC;EAEDC,WAAW,WAAXA,WAAWA,CAACC,SAAiB,EAAQ;IACnCV,YAAY,CAACS,WAAW,CAACC,SAAS,CAAC;EACrC,CAAC;EAEDC,eAAe,WAAfA,eAAeA,CAACC,KAAa,EAAQ;IACnCZ,YAAY,CAACW,eAAe,CAACC,KAAK,CAAC;EACrC,CAAC;EAQDC,QAAQ,WAARA,QAAQA,CAACC,cAAwB,EAAEC,QAAiB,EAAQ;IAC1Df,YAAY,CAACa,QAAQ,CAACC,cAAc,EAAEC,QAAQ,CAAC;EACjD,CAAC;EAKDC,SAAS,WAATA,SAASA,CAACC,MAAe,EAAEC,aAAqB,EAAQ;IACtDlB,YAAY,CAACgB,SAAS,CAACC,MAAM,EAAEC,aAAa,CAAC;EAC/C;AACF,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAA9B,OAAA,GAEaa,sBAAsB", "ignoreList": []}