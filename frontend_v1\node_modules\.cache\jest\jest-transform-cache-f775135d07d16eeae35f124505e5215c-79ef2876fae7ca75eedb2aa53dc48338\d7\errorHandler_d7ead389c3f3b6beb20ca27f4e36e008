3b4da6ed05b9b7850a9743f48044bc95
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.handleValidationError = exports.handleNetworkError = exports.handleError = exports.handleCriticalError = exports.handleAuthError = exports.errorHandler = exports.ErrorType = exports.ErrorSeverity = void 0;
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var Haptics = _interopRequireWildcard(require("expo-haptics"));
var _reactNative = require("react-native");
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var ErrorType = exports.ErrorType = function (ErrorType) {
  ErrorType["NETWORK"] = "NETWORK";
  ErrorType["VALIDATION"] = "VALIDATION";
  ErrorType["AUTHENTICATION"] = "AUTHENTICATION";
  ErrorType["AUTHORIZATION"] = "AUTHORIZATION";
  ErrorType["NOT_FOUND"] = "NOT_FOUND";
  ErrorType["SERVER"] = "SERVER";
  ErrorType["CLIENT"] = "CLIENT";
  ErrorType["UNKNOWN"] = "UNKNOWN";
  return ErrorType;
}({});
var ErrorSeverity = exports.ErrorSeverity = function (ErrorSeverity) {
  ErrorSeverity["LOW"] = "LOW";
  ErrorSeverity["MEDIUM"] = "MEDIUM";
  ErrorSeverity["HIGH"] = "HIGH";
  ErrorSeverity["CRITICAL"] = "CRITICAL";
  return ErrorSeverity;
}({});
var ErrorHandler = function () {
  function ErrorHandler() {
    var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    (0, _classCallCheck2.default)(this, ErrorHandler);
    this.errorQueue = [];
    this.maxQueueSize = 100;
    this.config = Object.assign({
      enableLogging: true,
      enableHaptics: true,
      enableUserNotification: true,
      logToConsole: __DEV__,
      logToRemote: !__DEV__
    }, config);
  }
  return (0, _createClass2.default)(ErrorHandler, [{
    key: "handleError",
    value: function handleError(error, context) {
      var appError = this.normalizeError(error, context);
      if (this.config.enableLogging) {
        this.logError(appError);
      }
      if (this.config.enableHaptics && appError.severity !== ErrorSeverity.LOW) {
        this.provideHapticFeedback(appError.severity);
      }
      if (this.config.enableUserNotification && appError.userMessage) {
        this.showUserNotification(appError);
      }
      this.addToQueue(appError);
      return appError;
    }
  }, {
    key: "handleNetworkError",
    value: function handleNetworkError(error, context) {
      var appError = {
        id: this.generateErrorId(),
        type: ErrorType.NETWORK,
        severity: ErrorSeverity.MEDIUM,
        message: error.message,
        userMessage: 'Network connection issue. Please check your internet connection and try again.',
        details: error,
        timestamp: new Date(),
        stack: error.stack,
        context: context
      };
      return this.handleError(appError, context);
    }
  }, {
    key: "handleValidationError",
    value: function handleValidationError(message, field, context) {
      var appError = {
        id: this.generateErrorId(),
        type: ErrorType.VALIDATION,
        severity: ErrorSeverity.LOW,
        message: message,
        userMessage: message,
        details: {
          field: field
        },
        timestamp: new Date(),
        context: context
      };
      return this.handleError(appError, context);
    }
  }, {
    key: "handleAuthError",
    value: function handleAuthError(error, context) {
      var appError = {
        id: this.generateErrorId(),
        type: ErrorType.AUTHENTICATION,
        severity: ErrorSeverity.HIGH,
        message: error.message,
        userMessage: 'Authentication failed. Please log in again.',
        details: error,
        timestamp: new Date(),
        stack: error.stack,
        context: context
      };
      return this.handleError(appError, context);
    }
  }, {
    key: "handleCriticalError",
    value: function handleCriticalError(error, context) {
      var appError = {
        id: this.generateErrorId(),
        type: ErrorType.UNKNOWN,
        severity: ErrorSeverity.CRITICAL,
        message: error.message,
        userMessage: 'A critical error occurred. The app will restart.',
        details: error,
        timestamp: new Date(),
        stack: error.stack,
        context: context
      };
      return this.handleError(appError, context);
    }
  }, {
    key: "normalizeError",
    value: function normalizeError(error, context) {
      if (this.isAppError(error)) {
        return Object.assign({}, error, {
          context: Object.assign({}, error.context, context)
        });
      }
      return {
        id: this.generateErrorId(),
        type: this.determineErrorType(error),
        severity: this.determineSeverity(error),
        message: error.message,
        userMessage: this.generateUserMessage(error),
        details: error,
        timestamp: new Date(),
        stack: error.stack,
        context: context
      };
    }
  }, {
    key: "logError",
    value: function logError(error) {
      if (this.config.logToConsole) {
        console.group(`🚨 Error [${error.severity}] - ${error.type}`);
        console.error('Message:', error.message);
        console.error('User Message:', error.userMessage);
        console.error('Details:', error.details);
        console.error('Context:', error.context);
        console.error('Stack:', error.stack);
        console.groupEnd();
      }
      if (this.config.logToRemote) {}
    }
  }, {
    key: "provideHapticFeedback",
    value: function provideHapticFeedback(severity) {
      try {
        switch (severity) {
          case ErrorSeverity.LOW:
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
            break;
          case ErrorSeverity.MEDIUM:
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            break;
          case ErrorSeverity.HIGH:
          case ErrorSeverity.CRITICAL:
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            setTimeout(function () {
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            }, 100);
            break;
        }
      } catch (hapticError) {
        console.warn('Haptic feedback failed:', hapticError);
      }
    }
  }, {
    key: "showUserNotification",
    value: function showUserNotification(error) {
      var _this = this;
      var title = this.getErrorTitle(error.type);
      _reactNative.Alert.alert(title, error.userMessage || error.message, [{
        text: 'OK',
        style: 'default'
      }].concat((0, _toConsumableArray2.default)(error.severity === ErrorSeverity.CRITICAL ? [{
        text: 'Report Issue',
        style: 'default',
        onPress: function onPress() {
          return _this.reportIssue(error);
        }
      }] : [])));
    }
  }, {
    key: "addToQueue",
    value: function addToQueue(error) {
      this.errorQueue.push(error);
      if (this.errorQueue.length > this.maxQueueSize) {
        this.errorQueue.shift();
      }
    }
  }, {
    key: "getErrorTitle",
    value: function getErrorTitle(type) {
      switch (type) {
        case ErrorType.NETWORK:
          return 'Connection Issue';
        case ErrorType.VALIDATION:
          return 'Input Error';
        case ErrorType.AUTHENTICATION:
          return 'Authentication Required';
        case ErrorType.AUTHORIZATION:
          return 'Access Denied';
        case ErrorType.NOT_FOUND:
          return 'Not Found';
        case ErrorType.SERVER:
          return 'Server Error';
        default:
          return 'Error';
      }
    }
  }, {
    key: "determineErrorType",
    value: function determineErrorType(error) {
      var message = error.message.toLowerCase();
      if (message.includes('network') || message.includes('fetch')) {
        return ErrorType.NETWORK;
      }
      if (message.includes('unauthorized') || message.includes('401')) {
        return ErrorType.AUTHENTICATION;
      }
      if (message.includes('forbidden') || message.includes('403')) {
        return ErrorType.AUTHORIZATION;
      }
      if (message.includes('not found') || message.includes('404')) {
        return ErrorType.NOT_FOUND;
      }
      if (message.includes('server') || message.includes('500')) {
        return ErrorType.SERVER;
      }
      return ErrorType.UNKNOWN;
    }
  }, {
    key: "determineSeverity",
    value: function determineSeverity(error) {
      var message = error.message.toLowerCase();
      if (message.includes('critical') || message.includes('fatal')) {
        return ErrorSeverity.CRITICAL;
      }
      if (message.includes('unauthorized') || message.includes('forbidden')) {
        return ErrorSeverity.HIGH;
      }
      if (message.includes('network') || message.includes('server')) {
        return ErrorSeverity.MEDIUM;
      }
      return ErrorSeverity.LOW;
    }
  }, {
    key: "generateUserMessage",
    value: function generateUserMessage(error) {
      var type = this.determineErrorType(error);
      switch (type) {
        case ErrorType.NETWORK:
          return 'Please check your internet connection and try again.';
        case ErrorType.AUTHENTICATION:
          return 'Please log in to continue.';
        case ErrorType.AUTHORIZATION:
          return "You don't have permission to perform this action.";
        case ErrorType.NOT_FOUND:
          return 'The requested item could not be found.';
        case ErrorType.SERVER:
          return 'Server is temporarily unavailable. Please try again later.';
        default:
          return 'An unexpected error occurred. Please try again.';
      }
    }
  }, {
    key: "generateErrorId",
    value: function generateErrorId() {
      return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "isAppError",
    value: function isAppError(error) {
      return error && typeof error === 'object' && 'id' in error && 'type' in error;
    }
  }, {
    key: "reportIssue",
    value: function reportIssue(error) {
      console.log('Reporting issue:', error.id);
    }
  }, {
    key: "getErrorStats",
    value: function getErrorStats() {
      var stats = {
        total: this.errorQueue.length,
        byType: {},
        bySeverity: {}
      };
      this.errorQueue.forEach(function (error) {
        stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
        stats.bySeverity[error.severity] = (stats.bySeverity[error.severity] || 0) + 1;
      });
      return stats;
    }
  }, {
    key: "clearErrors",
    value: function clearErrors() {
      this.errorQueue = [];
    }
  }]);
}();
var errorHandler = exports.errorHandler = new ErrorHandler();
var handleError = exports.handleError = function handleError(error, context) {
  return errorHandler.handleError(error, context);
};
var handleNetworkError = exports.handleNetworkError = function handleNetworkError(error, context) {
  return errorHandler.handleNetworkError(error, context);
};
var handleValidationError = exports.handleValidationError = function handleValidationError(message, field, context) {
  return errorHandler.handleValidationError(message, field, context);
};
var handleAuthError = exports.handleAuthError = function handleAuthError(error, context) {
  return errorHandler.handleAuthError(error, context);
};
var handleCriticalError = exports.handleCriticalError = function handleCriticalError(error, context) {
  return errorHandler.handleCriticalError(error, context);
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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