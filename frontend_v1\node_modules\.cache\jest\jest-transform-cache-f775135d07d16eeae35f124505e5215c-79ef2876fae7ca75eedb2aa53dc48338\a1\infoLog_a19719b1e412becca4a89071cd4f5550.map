{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "infoLog", "_console", "console", "log", "apply", "arguments", "_default"], "sources": ["infoLog.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict\n */\n\n'use strict';\n\n/**\n * Intentional info-level logging for clear separation from ad-hoc console debug logging.\n */\nfunction infoLog(...args: Array<mixed>): void {\n  return console.log(...args);\n}\n\nexport default infoLog;\n"], "mappings": "AAUA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAKb,SAASC,OAAOA,CAAA,EAA8B;EAAA,IAAAC,QAAA;EAC5C,OAAO,CAAAA,QAAA,GAAAC,OAAO,EAACC,GAAG,CAAAC,KAAA,CAAAH,QAAA,EAAAI,SAAQ,CAAC;AAC7B;AAAC,IAAAC,QAAA,GAAAT,OAAA,CAAAE,OAAA,GAEcC,OAAO", "ignoreList": []}