{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_get2", "_inherits2", "_AnimatedInterpolation", "_AnimatedWithChildren2", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "AnimatedModulo", "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "a", "modulus", "config", "_this", "_a", "_modulus", "key", "__makeNative", "platformConfig", "__getValue", "interpolate", "AnimatedInterpolation", "__attach", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d", "__getNativeConfig", "type", "input", "__getNativeTag", "debugID", "__getDebugID", "AnimatedWithChildren"], "sources": ["AnimatedModulo.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n */\n\n'use strict';\n\nimport type {PlatformConfig} from '../AnimatedPlatformConfig';\nimport type {InterpolationConfigType} from './AnimatedInterpolation';\nimport type AnimatedNode from './AnimatedNode';\nimport type {AnimatedNodeConfig} from './AnimatedNode';\n\nimport AnimatedInterpolation from './AnimatedInterpolation';\nimport AnimatedWithChildren from './AnimatedWithChildren';\n\nexport default class AnimatedModulo extends AnimatedWithChildren {\n  _a: AnimatedNode;\n  _modulus: number;\n\n  constructor(a: AnimatedNode, modulus: number, config?: ?AnimatedNodeConfig) {\n    super(config);\n    this._a = a;\n    this._modulus = modulus;\n  }\n\n  __makeNative(platformConfig: ?PlatformConfig) {\n    this._a.__makeNative(platformConfig);\n    super.__makeNative(platformConfig);\n  }\n\n  __getValue(): number {\n    return (\n      ((this._a.__getValue() % this._modulus) + this._modulus) % this._modulus\n    );\n  }\n\n  interpolate<OutputT: number | string>(\n    config: InterpolationConfigType<OutputT>,\n  ): AnimatedInterpolation<OutputT> {\n    return new AnimatedInterpolation(this, config);\n  }\n\n  __attach(): void {\n    this._a.__addChild(this);\n    super.__attach();\n  }\n\n  __detach(): void {\n    this._a.__removeChild(this);\n    super.__detach();\n  }\n\n  __getNativeConfig(): any {\n    return {\n      type: 'modulus',\n      input: this._a.__getNativeTag(),\n      modulus: this._modulus,\n      debugID: this.__getDebugID(),\n    };\n  }\n}\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,2BAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,gBAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,KAAA,GAAAX,sBAAA,CAAAC,OAAA;AAAA,IAAAW,UAAA,GAAAZ,sBAAA,CAAAC,OAAA;AAOb,IAAAY,sBAAA,GAAAb,sBAAA,CAAAC,OAAA;AACA,IAAAa,sBAAA,GAAAd,sBAAA,CAAAC,OAAA;AAA0D,SAAAc,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAP,gBAAA,CAAAJ,OAAA,EAAAW,CAAA,OAAAR,2BAAA,CAAAH,OAAA,EAAAU,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,YAAAR,gBAAA,CAAAJ,OAAA,EAAAU,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAAA,SAAAY,cAAAZ,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAW,CAAA,QAAAC,CAAA,OAAAnB,KAAA,CAAAL,OAAA,MAAAI,gBAAA,CAAAJ,OAAA,MAAAuB,CAAA,GAAAb,CAAA,CAAAS,SAAA,GAAAT,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAW,CAAA,yBAAAC,CAAA,aAAAd,CAAA,WAAAc,CAAA,CAAAP,KAAA,CAAAL,CAAA,EAAAF,CAAA,OAAAc,CAAA;AAAA,IAErCC,cAAc,GAAA3B,OAAA,CAAAE,OAAA,aAAA0B,qBAAA;EAIjC,SAAAD,eAAYE,CAAe,EAAEC,OAAe,EAAEC,MAA4B,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAA7B,gBAAA,CAAAD,OAAA,QAAAyB,cAAA;IAC1EK,KAAA,GAAArB,UAAA,OAAAgB,cAAA,GAAMI,MAAM;IACZC,KAAA,CAAKC,EAAE,GAAGJ,CAAC;IACXG,KAAA,CAAKE,QAAQ,GAAGJ,OAAO;IAAC,OAAAE,KAAA;EAC1B;EAAC,IAAAxB,UAAA,CAAAN,OAAA,EAAAyB,cAAA,EAAAC,qBAAA;EAAA,WAAAxB,aAAA,CAAAF,OAAA,EAAAyB,cAAA;IAAAQ,GAAA;IAAAlC,KAAA,EAED,SAAAmC,YAAYA,CAACC,cAA+B,EAAE;MAC5C,IAAI,CAACJ,EAAE,CAACG,YAAY,CAACC,cAAc,CAAC;MACpCb,aAAA,CAAAG,cAAA,4BAAmBU,cAAc;IACnC;EAAC;IAAAF,GAAA;IAAAlC,KAAA,EAED,SAAAqC,UAAUA,CAAA,EAAW;MACnB,OACE,CAAE,IAAI,CAACL,EAAE,CAACK,UAAU,CAAC,CAAC,GAAG,IAAI,CAACJ,QAAQ,GAAI,IAAI,CAACA,QAAQ,IAAI,IAAI,CAACA,QAAQ;IAE5E;EAAC;IAAAC,GAAA;IAAAlC,KAAA,EAED,SAAAsC,WAAWA,CACTR,MAAwC,EACR;MAChC,OAAO,IAAIS,8BAAqB,CAAC,IAAI,EAAET,MAAM,CAAC;IAChD;EAAC;IAAAI,GAAA;IAAAlC,KAAA,EAED,SAAAwC,QAAQA,CAAA,EAAS;MACf,IAAI,CAACR,EAAE,CAACS,UAAU,CAAC,IAAI,CAAC;MACxBlB,aAAA,CAAAG,cAAA;IACF;EAAC;IAAAQ,GAAA;IAAAlC,KAAA,EAED,SAAA0C,QAAQA,CAAA,EAAS;MACf,IAAI,CAACV,EAAE,CAACW,aAAa,CAAC,IAAI,CAAC;MAC3BpB,aAAA,CAAAG,cAAA;IACF;EAAC;IAAAQ,GAAA;IAAAlC,KAAA,EAED,SAAA4C,iBAAiBA,CAAA,EAAQ;MACvB,OAAO;QACLC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,IAAI,CAACd,EAAE,CAACe,cAAc,CAAC,CAAC;QAC/BlB,OAAO,EAAE,IAAI,CAACI,QAAQ;QACtBe,OAAO,EAAE,IAAI,CAACC,YAAY,CAAC;MAC7B,CAAC;IACH;EAAC;AAAA,EA5CyCC,8BAAoB", "ignoreList": []}