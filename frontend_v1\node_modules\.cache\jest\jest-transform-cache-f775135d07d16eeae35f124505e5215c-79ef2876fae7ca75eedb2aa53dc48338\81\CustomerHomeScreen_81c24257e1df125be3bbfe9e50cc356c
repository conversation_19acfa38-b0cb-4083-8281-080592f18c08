dca578b78aac6aa33a51d47e3e621af8
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _vectorIcons = require("@expo/vector-icons");
var _native = require("@react-navigation/native");
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _Box = require("../components/atoms/Box");
var _IconButton = require("../components/atoms/IconButton");
var _StoreImage = require("../components/molecules/StoreImage");
var _SafeAreaWrapper = require("../components/ui/SafeAreaWrapper");
var _ThemeContext = require("../contexts/ThemeContext");
var _I18nContext = require("../contexts/I18nContext");
var _responsiveUtils = require("../utils/responsiveUtils");
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var CustomerHomeScreen = function CustomerHomeScreen() {
  var _colors$background;
  var navigation = (0, _native.useNavigation)();
  var _useTheme = (0, _ThemeContext.useTheme)(),
    colors = _useTheme.colors;
  var _useI18n = (0, _I18nContext.useI18n)(),
    t = _useI18n.t;
  var styles = createStyles(colors);
  var _useState = (0, _react.useState)(''),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    greeting = _useState2[0],
    setGreeting = _useState2[1];
  var _useState3 = (0, _react.useState)([]),
    _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
    categories = _useState4[0],
    setCategories = _useState4[1];
  var _useState5 = (0, _react.useState)([]),
    _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
    featuredProviders = _useState6[0],
    setFeaturedProviders = _useState6[1];
  var _useState7 = (0, _react.useState)(true),
    _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
    loading = _useState8[0],
    setLoading = _useState8[1];
  var _useState9 = (0, _react.useState)(true),
    _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
    providersLoading = _useState0[0],
    setProvidersLoading = _useState0[1];
  var _useState1 = (0, _react.useState)(null),
    _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
    error = _useState10[0],
    setError = _useState10[1];
  var _useState11 = (0, _react.useState)(null),
    _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
    providersError = _useState12[0],
    setProvidersError = _useState12[1];
  var _useState13 = (0, _react.useState)(false),
    _useState14 = (0, _slicedToArray2.default)(_useState13, 2),
    refreshing = _useState14[0],
    setRefreshing = _useState14[1];
  var fetchCategories = (0, _react.useCallback)((0, _asyncToGenerator2.default)(function* () {
    try {
      setLoading(true);
      setError(null);
      var response = yield fetch('http://************:8000/api/catalog/categories/');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      var data = yield response.json();
      var categoriesData = Array.isArray(data) ? data : data.results || [];
      var homeCategories = categoriesData.slice(0, 8);
      setCategories(homeCategories);
      console.log(`✅ Loaded ${homeCategories.length} categories for home screen`);
    } catch (err) {
      console.error('❌ Error fetching categories:', err);
      setError(err instanceof Error ? err.message : 'Failed to load categories');
      setCategories([{
        id: '1',
        name: 'Hair Services',
        slug: 'hair-services',
        color: colors.sage400 || '#8FBC8F',
        service_count: 24,
        icon: '💇‍♀️',
        description: 'Cuts, styling, coloring, and treatments',
        is_popular: true
      }, {
        id: '2',
        name: 'Nail Services',
        slug: 'nail-services',
        color: '#9ACD32',
        service_count: 18,
        icon: '💅',
        description: 'Manicures, pedicures, nail art',
        is_popular: true
      }, {
        id: '3',
        name: 'Lash Services',
        slug: 'lash-services',
        color: '#6B8E23',
        service_count: 12,
        icon: '👁️',
        description: 'Extensions, lifts, tinting',
        is_popular: true
      }, {
        id: '4',
        name: 'Braiding',
        slug: 'braiding',
        color: colors.sage400 || '#8FBC8F',
        service_count: 15,
        icon: '🤎',
        description: 'Protective styles and braiding',
        is_popular: true
      }, {
        id: '5',
        name: 'Skincare',
        slug: 'skincare',
        color: '#9ACD32',
        service_count: 20,
        icon: '✨',
        description: 'Facials, treatments, and skincare',
        is_popular: true
      }, {
        id: '6',
        name: 'Massage',
        slug: 'massage',
        color: '#6B8E23',
        service_count: 8,
        icon: '💆‍♀️',
        description: 'Relaxation and therapeutic massage',
        is_popular: true
      }, {
        id: '7',
        name: 'Makeup',
        slug: 'makeup',
        color: colors.sage400 || '#8FBC8F',
        service_count: 16,
        icon: '💄',
        description: 'Special occasion and everyday makeup',
        is_popular: true
      }, {
        id: '8',
        name: 'Locs & Twists',
        slug: 'locs-twists',
        color: '#9ACD32',
        service_count: 11,
        icon: '🌀',
        description: 'Loc maintenance and twist styles',
        is_popular: true
      }]);
    } finally {
      setLoading(false);
    }
  }), []);
  var fetchFeaturedProviders = (0, _react.useCallback)((0, _asyncToGenerator2.default)(function* () {
    try {
      setProvidersLoading(true);
      setProvidersError(null);
      var response = yield fetch('http://************:8000/api/catalog/providers/featured/?limit=10');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      var data = yield response.json();
      var providersData = Array.isArray(data) ? data : data.results || data.data || [];
      var transformedProviders = providersData.map(function (provider) {
        return Object.assign({}, provider, {
          rating: typeof provider.rating === 'string' ? parseFloat(provider.rating) || 0 : provider.rating || 0,
          review_count: provider.review_count || 0
        });
      });
      setFeaturedProviders(transformedProviders.slice(0, 6));
      console.log(`✅ Loaded ${providersData.length} featured providers`);
    } catch (err) {
      console.error('❌ Error fetching featured providers:', err);
      setProvidersError(err instanceof Error ? err.message : 'Failed to load featured providers');
      setFeaturedProviders([]);
    } finally {
      setProvidersLoading(false);
    }
  }), []);
  var handleRefresh = (0, _react.useCallback)((0, _asyncToGenerator2.default)(function* () {
    setRefreshing(true);
    try {
      yield Promise.all([fetchCategories(), fetchFeaturedProviders()]);
    } catch (error) {
      console.error('❌ Refresh error:', error);
    } finally {
      setRefreshing(false);
    }
  }), [fetchCategories, fetchFeaturedProviders]);
  (0, _react.useEffect)(function () {
    var hour = new Date().getHours();
    var timeOfDay = 'morning';
    if (hour >= 12 && hour < 18) {
      timeOfDay = 'afternoon';
    } else if (hour >= 18) {
      timeOfDay = 'evening';
    }
    setGreeting(t('home.greeting', {
      timeOfDay: timeOfDay
    }));
    fetchCategories();
    fetchFeaturedProviders();
  }, [fetchCategories, fetchFeaturedProviders]);
  var handleCategoryPress = function handleCategoryPress(categoryName) {
    console.log(`🔍 Category pressed: ${categoryName}`);
    navigation.navigate('Search', {
      categoryName: categoryName
    });
  };
  var handleFavoritesPress = function handleFavoritesPress() {
    console.log('❤️ Favorites pressed');
    navigation.navigate('Profile');
  };
  var handleProviderPress = function handleProviderPress(providerId) {
    console.log(`🏪 Provider pressed: ${providerId}`);
    navigation.navigate('ProviderDetails', {
      providerId: providerId
    });
  };
  var handleSeeAllCategories = function handleSeeAllCategories() {
    console.log('📋 See all categories pressed');
    navigation.navigate('Search');
  };
  var handleViewAllProviders = function handleViewAllProviders() {
    console.log('🏪 View all providers pressed');
    navigation.navigate('Search');
  };
  var renderCategoryCard = function renderCategoryCard(_ref4) {
    var _colors$text;
    var item = _ref4.item;
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      style: styles.categoryCard,
      onPress: function onPress() {
        return handleCategoryPress(item.name);
      },
      testID: `category-${item.id}`,
      accessibilityLabel: `${item.name} category with ${item.service_count} services`,
      accessibilityHint: `Browse ${item.name.toLowerCase()} services`,
      activeOpacity: 0.8,
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [styles.categoryIconContainer, {
          backgroundColor: item.color || colors.sage400 || '#8FBC8F'
        }],
        children: (0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
          name: item.mobile_icon || item.icon || 'ellipse-outline',
          size: 28,
          color: ((_colors$text = colors.text) == null ? void 0 : _colors$text.onPrimary) || '#FFFFFF',
          style: styles.categoryIcon
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.categoryInfo,
        children: [(0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: styles.categoryName,
          children: item.name
        }), (0, _jsxRuntime.jsxs)(_reactNative.Text, {
          style: styles.categoryServiceCount,
          children: [item.service_count, " services"]
        })]
      })]
    });
  };
  var renderProviderCard = function renderProviderCard(_ref5) {
    var _item$category_names, _item$category_names2;
    var item = _ref5.item;
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      style: styles.providerCard,
      onPress: function onPress() {
        return handleProviderPress(item.id);
      },
      testID: `provider-${item.id}`,
      accessibilityLabel: `${item.business_name} provider`,
      accessibilityHint: `View details for ${item.business_name}`,
      activeOpacity: 0.8,
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.featuredBadge,
        children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: styles.featuredBadgeText,
          children: "Featured"
        })
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.providerImageContainer,
        children: (0, _jsxRuntime.jsx)(_StoreImage.StoreImage, {
          providerId: item.id,
          providerName: item.business_name,
          category: ((_item$category_names = item.category_names) == null ? void 0 : _item$category_names[0]) || 'general',
          size: "large",
          testID: `featured-provider-image-${item.id}`
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.providerInfo,
        children: [(0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: styles.providerName,
          numberOfLines: 2,
          children: item.business_name
        }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: styles.providerCategory,
          numberOfLines: 1,
          children: ((_item$category_names2 = item.category_names) == null ? void 0 : _item$category_names2[0]) || 'Beauty Services'
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.providerMeta,
          children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.ratingContainer,
            children: [(0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
              name: "star",
              size: 14,
              color: colors.warning || '#FFC107'
            }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: styles.providerRating,
              children: item.rating ? item.rating.toFixed(1) : '0.0'
            }), (0, _jsxRuntime.jsxs)(_reactNative.Text, {
              style: styles.reviewCount,
              children: ["(", item.review_count || 0, ")"]
            })]
          })
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.providerActions,
          children: [(0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: styles.providerDistance,
            children: item.distance || '0.5 mi'
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: styles.quickBookButton,
            children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: styles.quickBookText,
              children: "Book"
            })
          })]
        })]
      })]
    });
  };
  return (0, _jsxRuntime.jsx)(_SafeAreaWrapper.SafeAreaScreen, {
    backgroundColor: ((_colors$background = colors.background) == null ? void 0 : _colors$background.primary) || '#FFFFFF',
    style: styles.container,
    children: (0, _jsxRuntime.jsxs)(_reactNative.ScrollView, {
      style: styles.scrollView,
      contentContainerStyle: styles.scrollContent,
      showsVerticalScrollIndicator: false,
      refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
        refreshing: refreshing,
        onRefresh: handleRefresh,
        colors: [colors.sage400 || '#8FBC8F'],
        tintColor: colors.sage400 || '#8FBC8F'
      }),
      children: [(0, _jsxRuntime.jsxs)(_Box.Box, {
        style: styles.header,
        children: [(0, _jsxRuntime.jsxs)(_Box.Box, {
          style: styles.headerTop,
          children: [(0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: styles.appName,
            children: "Vierla"
          }), (0, _jsxRuntime.jsx)(_IconButton.IconButton, {
            name: "heart-outline",
            size: "medium",
            variant: "ghost",
            onPress: handleFavoritesPress,
            testID: "favorites-button",
            accessibilityLabel: "Open favorites",
            style: styles.profileButton
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative.Text, {
          style: styles.greeting,
          children: [greeting, "!"]
        }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: styles.subtitle,
          children: t('home.search_placeholder')
        })]
      }), (0, _jsxRuntime.jsxs)(_Box.Box, {
        style: styles.browseServicesSection,
        children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.sectionHeader,
          children: [(0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: styles.sectionTitle,
            children: t('home.browse_services')
          }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            onPress: handleSeeAllCategories,
            style: styles.seeAllButton,
            testID: "see-all-categories",
            accessibilityLabel: "View all service categories",
            accessibilityRole: "button",
            children: [(0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: styles.seeAllText,
              children: t('home.view_all')
            }), (0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
              name: "chevron-forward",
              size: 16,
              color: colors.sage400 || '#8FBC8F'
            })]
          })]
        }), loading ? (0, _jsxRuntime.jsxs)(_Box.Box, {
          style: styles.loadingContainer,
          children: [(0, _jsxRuntime.jsx)(_reactNative.ActivityIndicator, {
            size: "large",
            color: colors.sage400 || '#8FBC8F'
          }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: styles.loadingText,
            children: "Loading categories..."
          })]
        }) : error ? (0, _jsxRuntime.jsxs)(_Box.Box, {
          style: styles.errorContainer,
          children: [(0, _jsxRuntime.jsxs)(_reactNative.Text, {
            style: styles.errorText,
            children: ["\u26A0\uFE0F ", error]
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: styles.retryButton,
            onPress: fetchCategories,
            children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: styles.retryButtonText,
              children: "Retry"
            })
          })]
        }) : (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
          data: categories,
          horizontal: true,
          showsHorizontalScrollIndicator: false,
          keyExtractor: function keyExtractor(item) {
            return item.id;
          },
          contentContainerStyle: styles.categoriesContainer,
          renderItem: renderCategoryCard,
          ItemSeparatorComponent: function ItemSeparatorComponent() {
            return (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: styles.categorySeparator
            });
          }
        })]
      }), (0, _jsxRuntime.jsxs)(_Box.Box, {
        style: styles.featuredSection,
        children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.sectionHeader,
          children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.sectionTitleContainer,
            children: [(0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: styles.sectionTitle,
              children: t('home.featured_providers')
            }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: styles.sectionSubtitle,
              children: "Top-rated professionals near you"
            })]
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: handleViewAllProviders,
            style: styles.viewAllButton,
            testID: "view-all-stores",
            accessibilityLabel: "View all service providers",
            accessibilityRole: "button",
            children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: styles.viewAllText,
              children: t('home.view_all')
            })
          })]
        }), providersLoading ? (0, _jsxRuntime.jsxs)(_Box.Box, {
          style: styles.loadingContainer,
          children: [(0, _jsxRuntime.jsx)(_reactNative.ActivityIndicator, {
            size: "large",
            color: colors.sage400 || '#8FBC8F'
          }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: styles.loadingText,
            children: "Loading providers..."
          })]
        }) : providersError ? (0, _jsxRuntime.jsxs)(_Box.Box, {
          style: styles.errorContainer,
          children: [(0, _jsxRuntime.jsxs)(_reactNative.Text, {
            style: styles.errorText,
            children: ["\u26A0\uFE0F ", providersError]
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: styles.retryButton,
            onPress: fetchFeaturedProviders,
            children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: styles.retryButtonText,
              children: "Retry"
            })
          })]
        }) : featuredProviders.length > 0 ? (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
          data: featuredProviders,
          horizontal: true,
          showsHorizontalScrollIndicator: false,
          keyExtractor: function keyExtractor(item) {
            return item.id;
          },
          contentContainerStyle: styles.providersContainer,
          renderItem: renderProviderCard,
          ItemSeparatorComponent: function ItemSeparatorComponent() {
            return (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: styles.providerSeparator
            });
          }
        }) : (0, _jsxRuntime.jsx)(_Box.Box, {
          style: styles.placeholderContainer,
          children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: styles.placeholderText,
            children: "No featured providers available"
          })
        })]
      })]
    })
  });
};
var createStyles = function createStyles(colors) {
  var _colors$background2, _colors$text2, _colors$text3, _colors$text4, _colors$text5, _colors$text6, _colors$text7, _colors$text8, _colors$text9, _colors$text0, _colors$text1, _colors$text10;
  return _reactNative.StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: ((_colors$background2 = colors.background) == null ? void 0 : _colors$background2.primary) || '#FFFFFF'
    },
    scrollView: {
      flex: 1
    },
    scrollContent: {
      paddingBottom: 20
    },
    header: {
      paddingHorizontal: (0, _responsiveUtils.getResponsiveSpacing)(20),
      paddingTop: (0, _responsiveUtils.getResponsiveSpacing)(16),
      paddingBottom: (0, _responsiveUtils.getResponsiveSpacing)(24),
      backgroundColor: colors.sage400 || '#8FBC8F',
      borderTopLeftRadius: (0, _responsiveUtils.getResponsiveSpacing)(24),
      borderTopRightRadius: (0, _responsiveUtils.getResponsiveSpacing)(24),
      borderBottomLeftRadius: (0, _responsiveUtils.getResponsiveSpacing)(24),
      borderBottomRightRadius: (0, _responsiveUtils.getResponsiveSpacing)(24)
    },
    headerTop: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: (0, _responsiveUtils.getResponsiveSpacing)(12)
    },
    appName: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(24),
      fontWeight: '700',
      color: ((_colors$text2 = colors.text) == null ? void 0 : _colors$text2.onPrimary) || '#FFFFFF',
      letterSpacing: 1
    },
    profileButton: {
      width: (0, _responsiveUtils.getMinimumTouchTarget)(),
      height: (0, _responsiveUtils.getMinimumTouchTarget)(),
      borderRadius: (0, _responsiveUtils.getResponsiveSpacing)(20),
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      justifyContent: 'center',
      alignItems: 'center'
    },
    profileIcon: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(20),
      color: ((_colors$text3 = colors.text) == null ? void 0 : _colors$text3.onPrimary) || '#FFFFFF'
    },
    greeting: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(28),
      fontWeight: '600',
      color: ((_colors$text4 = colors.text) == null ? void 0 : _colors$text4.onPrimary) || '#FFFFFF',
      marginBottom: (0, _responsiveUtils.getResponsiveSpacing)(4)
    },
    subtitle: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(16),
      color: (_colors$text5 = colors.text) != null && _colors$text5.onPrimary ? `${colors.text.onPrimary}E6` : 'rgba(255, 255, 255, 0.9)',
      fontWeight: '400',
      marginBottom: (0, _responsiveUtils.getResponsiveSpacing)(20)
    },
    browseServicesSection: {
      paddingHorizontal: (0, _responsiveUtils.getResponsiveSpacing)(20),
      paddingVertical: (0, _responsiveUtils.getResponsiveSpacing)(20)
    },
    featuredSection: {
      paddingHorizontal: (0, _responsiveUtils.getResponsiveSpacing)(20),
      paddingVertical: (0, _responsiveUtils.getResponsiveSpacing)(20)
    },
    sectionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: (0, _responsiveUtils.getResponsiveSpacing)(16)
    },
    sectionTitleContainer: {
      flex: 1
    },
    sectionTitle: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(20),
      fontWeight: '700',
      color: '#333333'
    },
    sectionSubtitle: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(12),
      color: '#666666',
      marginTop: (0, _responsiveUtils.getResponsiveSpacing)(2),
      lineHeight: (0, _responsiveUtils.getResponsiveFontSize)(16)
    },
    seeAllButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: (0, _responsiveUtils.getResponsiveSpacing)(8),
      paddingVertical: (0, _responsiveUtils.getResponsiveSpacing)(4),
      borderRadius: (0, _responsiveUtils.getResponsiveSpacing)(8),
      backgroundColor: 'transparent'
    },
    seeAllText: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(14),
      fontWeight: '500',
      color: colors.sage400 || '#8FBC8F',
      marginRight: (0, _responsiveUtils.getResponsiveSpacing)(4)
    },
    viewAllButton: {
      paddingHorizontal: (0, _responsiveUtils.getResponsiveSpacing)(12),
      paddingVertical: (0, _responsiveUtils.getResponsiveSpacing)(6),
      borderRadius: (0, _responsiveUtils.getResponsiveSpacing)(8),
      backgroundColor: colors.sage100 || 'rgba(143, 188, 143, 0.1)'
    },
    viewAllText: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(14),
      fontWeight: '600',
      color: colors.sage400 || '#8FBC8F'
    },
    categoriesContainer: {
      paddingLeft: 0
    },
    categoryCard: {
      width: (0, _responsiveUtils.getResponsiveSpacing)(120),
      height: (0, _responsiveUtils.getResponsiveSpacing)(140),
      backgroundColor: '#FFFFFF',
      borderRadius: (0, _responsiveUtils.getResponsiveSpacing)(16),
      padding: (0, _responsiveUtils.getResponsiveSpacing)(16),
      marginRight: (0, _responsiveUtils.getResponsiveSpacing)(12),
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2
      },
      shadowOpacity: 0.08,
      shadowRadius: 4,
      elevation: 3,
      borderWidth: 1,
      borderColor: colors.sage100 || 'rgba(143, 188, 143, 0.1)'
    },
    categoryIconContainer: {
      width: (0, _responsiveUtils.getResponsiveSpacing)(56),
      height: (0, _responsiveUtils.getResponsiveSpacing)(56),
      borderRadius: (0, _responsiveUtils.getResponsiveSpacing)(28),
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: (0, _responsiveUtils.getResponsiveSpacing)(12)
    },
    categoryIcon: {},
    categoryInfo: {
      alignItems: 'center'
    },
    categoryName: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(14),
      fontWeight: '600',
      color: ((_colors$text6 = colors.text) == null ? void 0 : _colors$text6.primary) || '#333333',
      textAlign: 'center',
      marginBottom: (0, _responsiveUtils.getResponsiveSpacing)(4)
    },
    categoryServiceCount: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(12),
      color: ((_colors$text7 = colors.text) == null ? void 0 : _colors$text7.secondary) || '#666666',
      textAlign: 'center'
    },
    placeholderContainer: {
      backgroundColor: '#F5F5F5',
      borderRadius: 12,
      padding: 20,
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: 100
    },
    placeholderText: {
      fontSize: 14,
      color: '#666666',
      textAlign: 'center'
    },
    loadingContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 40
    },
    loadingText: {
      fontSize: 14,
      color: '#666666',
      marginTop: 12
    },
    errorContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 40,
      backgroundColor: '#FFF5F5',
      borderRadius: 12,
      marginHorizontal: 4
    },
    errorText: {
      fontSize: 14,
      color: '#E53E3E',
      textAlign: 'center',
      marginBottom: 16
    },
    retryButton: {
      backgroundColor: colors.sage400 || '#8FBC8F',
      paddingHorizontal: 20,
      paddingVertical: 10,
      borderRadius: 8
    },
    retryButtonText: {
      color: '#FFFFFF',
      fontSize: 14,
      fontWeight: '600'
    },
    providersContainer: {
      paddingLeft: 0
    },
    providerCard: {
      width: (0, _responsiveUtils.getResponsiveSpacing)(200),
      backgroundColor: '#FFFFFF',
      borderRadius: (0, _responsiveUtils.getResponsiveSpacing)(20),
      padding: (0, _responsiveUtils.getResponsiveSpacing)(20),
      marginRight: (0, _responsiveUtils.getResponsiveSpacing)(16),
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2
      },
      shadowOpacity: 0.08,
      shadowRadius: 4,
      elevation: 3,
      borderWidth: 1,
      borderColor: colors.sage100 || 'rgba(143, 188, 143, 0.1)',
      position: 'relative'
    },
    providerSeparator: {
      width: 12
    },
    providerImageContainer: {
      position: 'relative',
      alignItems: 'center',
      marginBottom: 8
    },
    providerImagePlaceholder: {
      fontSize: 32,
      textAlign: 'center'
    },
    featuredBadge: {
      position: 'absolute',
      top: (0, _responsiveUtils.getResponsiveSpacing)(8),
      right: (0, _responsiveUtils.getResponsiveSpacing)(8),
      backgroundColor: colors.warning || '#FFC107',
      borderRadius: (0, _responsiveUtils.getResponsiveSpacing)(12),
      paddingHorizontal: (0, _responsiveUtils.getResponsiveSpacing)(8),
      paddingVertical: (0, _responsiveUtils.getResponsiveSpacing)(4),
      zIndex: 10
    },
    featuredBadgeText: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(10),
      fontWeight: '600',
      color: '#FFFFFF'
    },
    verifiedBadge: {
      position: 'absolute',
      top: (0, _responsiveUtils.getResponsiveSpacing)(-5),
      right: (0, _responsiveUtils.getResponsiveSpacing)(10),
      backgroundColor: '#4CAF50',
      borderRadius: (0, _responsiveUtils.getResponsiveSpacing)(10),
      width: (0, _responsiveUtils.getResponsiveSpacing)(20),
      height: (0, _responsiveUtils.getResponsiveSpacing)(20),
      justifyContent: 'center',
      alignItems: 'center'
    },
    verifiedIcon: {
      color: '#FFFFFF',
      fontSize: 12,
      fontWeight: 'bold'
    },
    providerInfo: {
      flex: 1
    },
    providerName: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(16),
      fontWeight: '600',
      color: ((_colors$text8 = colors.text) == null ? void 0 : _colors$text8.primary) || '#333333',
      marginBottom: (0, _responsiveUtils.getResponsiveSpacing)(4)
    },
    providerCategory: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(14),
      color: ((_colors$text9 = colors.text) == null ? void 0 : _colors$text9.secondary) || '#666666',
      marginBottom: (0, _responsiveUtils.getResponsiveSpacing)(8)
    },
    providerMeta: {
      marginBottom: (0, _responsiveUtils.getResponsiveSpacing)(12)
    },
    ratingContainer: {
      flexDirection: 'row',
      alignItems: 'center'
    },
    providerRating: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(14),
      fontWeight: '600',
      color: ((_colors$text0 = colors.text) == null ? void 0 : _colors$text0.primary) || '#333333',
      marginLeft: (0, _responsiveUtils.getResponsiveSpacing)(4)
    },
    reviewCount: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(14),
      color: ((_colors$text1 = colors.text) == null ? void 0 : _colors$text1.secondary) || '#666666',
      marginLeft: (0, _responsiveUtils.getResponsiveSpacing)(2)
    },
    providerActions: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center'
    },
    providerDistance: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(14),
      color: ((_colors$text10 = colors.text) == null ? void 0 : _colors$text10.secondary) || '#666666',
      fontWeight: '500'
    },
    quickBookButton: {
      backgroundColor: colors.sage400 || '#8FBC8F',
      borderRadius: (0, _responsiveUtils.getResponsiveSpacing)(16),
      paddingHorizontal: (0, _responsiveUtils.getResponsiveSpacing)(12),
      paddingVertical: (0, _responsiveUtils.getResponsiveSpacing)(6),
      alignItems: 'center',
      justifyContent: 'center'
    },
    quickBookText: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(12),
      fontWeight: '600',
      color: '#FFFFFF'
    },
    categorySeparator: {
      width: (0, _responsiveUtils.getResponsiveSpacing)(8)
    }
  });
};
var _default = exports.default = CustomerHomeScreen;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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