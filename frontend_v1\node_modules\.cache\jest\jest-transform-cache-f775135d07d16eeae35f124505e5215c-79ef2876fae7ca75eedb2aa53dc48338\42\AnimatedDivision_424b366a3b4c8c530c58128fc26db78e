479a93b516470e3972d05a4e05fcb633
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _AnimatedInterpolation = _interopRequireDefault(require("./AnimatedInterpolation"));
var _AnimatedNode = _interopRequireDefault(require("./AnimatedNode"));
var _AnimatedValue = _interopRequireDefault(require("./AnimatedValue"));
var _AnimatedWithChildren2 = _interopRequireDefault(require("./AnimatedWithChildren"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var AnimatedDivision = exports.default = function (_AnimatedWithChildren) {
  function AnimatedDivision(a, b, config) {
    var _this;
    (0, _classCallCheck2.default)(this, AnimatedDivision);
    _this = _callSuper(this, AnimatedDivision, [config]);
    _this._warnedAboutDivideByZero = false;
    if (b === 0 || b instanceof _AnimatedNode.default && b.__getValue() === 0) {
      console.error('Detected potential division by zero in AnimatedDivision');
    }
    _this._a = typeof a === 'number' ? new _AnimatedValue.default(a) : a;
    _this._b = typeof b === 'number' ? new _AnimatedValue.default(b) : b;
    return _this;
  }
  (0, _inherits2.default)(AnimatedDivision, _AnimatedWithChildren);
  return (0, _createClass2.default)(AnimatedDivision, [{
    key: "__makeNative",
    value: function __makeNative(platformConfig) {
      this._a.__makeNative(platformConfig);
      this._b.__makeNative(platformConfig);
      _superPropGet(AnimatedDivision, "__makeNative", this, 3)([platformConfig]);
    }
  }, {
    key: "__getValue",
    value: function __getValue() {
      var a = this._a.__getValue();
      var b = this._b.__getValue();
      if (b === 0) {
        if (!this._warnedAboutDivideByZero) {
          console.error('Detected division by zero in AnimatedDivision');
          this._warnedAboutDivideByZero = true;
        }
        return 0;
      }
      this._warnedAboutDivideByZero = false;
      return a / b;
    }
  }, {
    key: "interpolate",
    value: function interpolate(config) {
      return new _AnimatedInterpolation.default(this, config);
    }
  }, {
    key: "__attach",
    value: function __attach() {
      this._a.__addChild(this);
      this._b.__addChild(this);
      _superPropGet(AnimatedDivision, "__attach", this, 3)([]);
    }
  }, {
    key: "__detach",
    value: function __detach() {
      this._a.__removeChild(this);
      this._b.__removeChild(this);
      _superPropGet(AnimatedDivision, "__detach", this, 3)([]);
    }
  }, {
    key: "__getNativeConfig",
    value: function __getNativeConfig() {
      return {
        type: 'division',
        input: [this._a.__getNativeTag(), this._b.__getNativeTag()],
        debugID: this.__getDebugID()
      };
    }
  }]);
}(_AnimatedWithChildren2.default);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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