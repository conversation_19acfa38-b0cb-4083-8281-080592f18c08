{"version": 3, "names": ["ReactNativeFeatureFlags", "shouldEmitW3CPointerEvents", "shouldPressibilityUseW3CPointerEventsForHover", "_default", "exports", "default"], "sources": ["ReactNativeFeatureFlags.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\nexport type FeatureFlags = {\n  /**\n   * Function used to enable / disable W3C pointer event emitting in React Native.\n   * If enabled you must also flip the equivalent native flags on each platform:\n   * iOS -> RCTSetDispatchW3CPointerEvents\n   * Android -> ReactFeatureFlags.dispatchPointerEvents\n   */\n  shouldEmitW3CPointerEvents: () => boolean,\n  /**\n   * Function used to enable / disable Pressibility from using W3C Pointer Events\n   * for its hover callbacks\n   */\n  shouldPressibilityUseW3CPointerEventsForHover: () => boolean,\n};\n\nconst ReactNativeFeatureFlags: FeatureFlags = {\n  shouldEmitW3CPointerEvents: () => false,\n  shouldPressibilityUseW3CPointerEventsForHover: () => false,\n};\n\nexport default ReactNativeFeatureFlags;\n"], "mappings": ";;;;AAyBA,IAAMA,uBAAqC,GAAG;EAC5CC,0BAA0B,EAAE,SAA5BA,0BAA0BA,CAAA;IAAA,OAAQ,KAAK;EAAA;EACvCC,6CAA6C,EAAE,SAA/CA,6CAA6CA,CAAA;IAAA,OAAQ,KAAK;EAAA;AAC5D,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEaL,uBAAuB", "ignoreList": []}