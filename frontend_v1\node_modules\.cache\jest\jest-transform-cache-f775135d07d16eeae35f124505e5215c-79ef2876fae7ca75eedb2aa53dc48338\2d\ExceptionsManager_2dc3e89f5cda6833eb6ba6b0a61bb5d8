5b6cd0f34352f074f98f00237140cc1d
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.SyntheticError = void 0;
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _wrapNativeSuper2 = _interopRequireDefault(require("@babel/runtime/helpers/wrapNativeSuper"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var SyntheticError = exports.SyntheticError = function (_Error) {
  function SyntheticError() {
    var _this;
    (0, _classCallCheck2.default)(this, SyntheticError);
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _callSuper(this, SyntheticError, [].concat(args));
    _this.name = '';
    return _this;
  }
  (0, _inherits2.default)(SyntheticError, _Error);
  return (0, _createClass2.default)(SyntheticError);
}((0, _wrapNativeSuper2.default)(Error));
var userExceptionDecorator;
var inUserExceptionDecorator = false;
var decoratedExtraDataKey = 'RN$ErrorExtraDataKey';
function unstable_setExceptionDecorator(exceptionDecorator) {
  userExceptionDecorator = exceptionDecorator;
}
function preprocessException(data) {
  if (userExceptionDecorator && !inUserExceptionDecorator) {
    inUserExceptionDecorator = true;
    try {
      return userExceptionDecorator(data);
    } catch (_unused) {} finally {
      inUserExceptionDecorator = false;
    }
  }
  return data;
}
var exceptionID = 0;
function reportException(e, isFatal, reportToConsole) {
  var parseErrorStack = require("./Devtools/parseErrorStack").default;
  var stack = parseErrorStack(e == null ? void 0 : e.stack);
  var currentExceptionID = ++exceptionID;
  var originalMessage = e.message || '';
  var message = originalMessage;
  if (e.componentStack != null) {
    message += `\n\nThis error is located at:${e.componentStack}`;
  }
  var namePrefix = e.name == null || e.name === '' ? '' : `${e.name}: `;
  if (!message.startsWith(namePrefix)) {
    message = namePrefix + message;
  }
  message = e.jsEngine == null ? message : `${message}, js engine: ${e.jsEngine}`;
  var extraData = Object.assign({}, e[decoratedExtraDataKey], {
    jsEngine: e.jsEngine,
    rawStack: e.stack
  });
  if (e.cause != null && typeof e.cause === 'object') {
    extraData.stackSymbols = e.cause.stackSymbols;
    extraData.stackReturnAddresses = e.cause.stackReturnAddresses;
    extraData.stackElements = e.cause.stackElements;
  }
  var data = preprocessException({
    message: message,
    originalMessage: message === originalMessage ? null : originalMessage,
    name: e.name == null || e.name === '' ? null : e.name,
    componentStack: typeof e.componentStack === 'string' ? e.componentStack : null,
    stack: stack,
    id: currentExceptionID,
    isFatal: isFatal,
    extraData: extraData
  });
  if (reportToConsole) {
    console.error(data.message);
  }
  if (__DEV__) {
    var LogBox = require("../LogBox/LogBox").default;
    LogBox.addException(Object.assign({}, data, {
      isComponentError: !!e.isComponentError
    }));
  } else if (isFatal || e.type !== 'warn') {
    var NativeExceptionsManager = require("./NativeExceptionsManager").default;
    if (NativeExceptionsManager) {
      if (isFatal) {
        if (global.RN$hasHandledFatalException != null && global.RN$hasHandledFatalException()) {
          return;
        }
        global.RN$notifyOfFatalException == null || global.RN$notifyOfFatalException();
      }
      NativeExceptionsManager.reportException(data);
    }
  }
}
var inExceptionHandler = false;
function handleException(e, isFatal) {
  var reportToConsole = true;
  if (!global.RN$handleException || !global.RN$handleException(e, isFatal, reportToConsole)) {
    var error;
    if (e instanceof Error) {
      error = e;
    } else {
      error = new SyntheticError(e);
    }
    try {
      inExceptionHandler = true;
      reportException(error, isFatal, reportToConsole);
    } finally {
      inExceptionHandler = false;
    }
  }
}
function reactConsoleErrorHandler() {
  var _console;
  for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
    args[_key2] = arguments[_key2];
  }
  (_console = console)._errorOriginal.apply(_console, args);
  if (!console.reportErrorsAsExceptions) {
    return;
  }
  if (inExceptionHandler || global.RN$inExceptionHandler != null && global.RN$inExceptionHandler()) {
    return;
  }
  var error;
  var firstArg = args[0];
  if (firstArg != null && firstArg.stack) {
    error = firstArg;
  } else {
    var stringifySafe = require("../Utilities/stringifySafe").default;
    if (typeof firstArg === 'string' && firstArg.startsWith('Warning: ')) {
      return;
    }
    var message = args.map(function (arg) {
      return typeof arg === 'string' ? arg : stringifySafe(arg);
    }).join(' ');
    error = new SyntheticError(message);
    error.name = 'console.error';
  }
  var isFatal = false;
  var reportToConsole = false;
  if (!global.RN$handleException || !global.RN$handleException(error, isFatal, reportToConsole)) {
    reportException(error, isFatal, reportToConsole);
  }
}
function installConsoleErrorReporter() {
  if (console._errorOriginal) {
    return;
  }
  console._errorOriginal = console.error.bind(console);
  console.error = reactConsoleErrorHandler;
  if (console.reportErrorsAsExceptions === undefined) {
    console.reportErrorsAsExceptions = true;
  }
}
var ExceptionsManager = {
  decoratedExtraDataKey: decoratedExtraDataKey,
  handleException: handleException,
  installConsoleErrorReporter: installConsoleErrorReporter,
  SyntheticError: SyntheticError,
  unstable_setExceptionDecorator: unstable_setExceptionDecorator
};
var _default = exports.default = ExceptionsManager;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0IiwicmVxdWlyZSIsIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwiZGVmYXVsdCIsIlN5bnRoZXRpY0Vycm9yIiwiX2NyZWF0ZUNsYXNzMiIsIl9jbGFzc0NhbGxDaGVjazIiLCJfcG9zc2libGVDb25zdHJ1Y3RvclJldHVybjIiLCJfZ2V0UHJvdG90eXBlT2YyIiwiX2luaGVyaXRzMiIsIl93cmFwTmF0aXZlU3VwZXIyIiwiX2NhbGxTdXBlciIsInQiLCJvIiwiZSIsIl9pc05hdGl2ZVJlZmxlY3RDb25zdHJ1Y3QiLCJSZWZsZWN0IiwiY29uc3RydWN0IiwiY29uc3RydWN0b3IiLCJhcHBseSIsIkJvb2xlYW4iLCJwcm90b3R5cGUiLCJ2YWx1ZU9mIiwiY2FsbCIsIl9FcnJvciIsIl90aGlzIiwiX2xlbiIsImFyZ3VtZW50cyIsImxlbmd0aCIsImFyZ3MiLCJBcnJheSIsIl9rZXkiLCJjb25jYXQiLCJuYW1lIiwiRXJyb3IiLCJ1c2VyRXhjZXB0aW9uRGVjb3JhdG9yIiwiaW5Vc2VyRXhjZXB0aW9uRGVjb3JhdG9yIiwiZGVjb3JhdGVkRXh0cmFEYXRhS2V5IiwidW5zdGFibGVfc2V0RXhjZXB0aW9uRGVjb3JhdG9yIiwiZXhjZXB0aW9uRGVjb3JhdG9yIiwicHJlcHJvY2Vzc0V4Y2VwdGlvbiIsImRhdGEiLCJfdW51c2VkIiwiZXhjZXB0aW9uSUQiLCJyZXBvcnRFeGNlcHRpb24iLCJpc0ZhdGFsIiwicmVwb3J0VG9Db25zb2xlIiwicGFyc2VFcnJvclN0YWNrIiwic3RhY2siLCJjdXJyZW50RXhjZXB0aW9uSUQiLCJvcmlnaW5hbE1lc3NhZ2UiLCJtZXNzYWdlIiwiY29tcG9uZW50U3RhY2siLCJuYW1lUHJlZml4Iiwic3RhcnRzV2l0aCIsImpzRW5naW5lIiwiZXh0cmFEYXRhIiwiYXNzaWduIiwicmF3U3RhY2siLCJjYXVzZSIsInN0YWNrU3ltYm9scyIsInN0YWNrUmV0dXJuQWRkcmVzc2VzIiwic3RhY2tFbGVtZW50cyIsImlkIiwiY29uc29sZSIsImVycm9yIiwiX19ERVZfXyIsIkxvZ0JveCIsImFkZEV4Y2VwdGlvbiIsImlzQ29tcG9uZW50RXJyb3IiLCJ0eXBlIiwiTmF0aXZlRXhjZXB0aW9uc01hbmFnZXIiLCJnbG9iYWwiLCJSTiRoYXNIYW5kbGVkRmF0YWxFeGNlcHRpb24iLCJSTiRub3RpZnlPZkZhdGFsRXhjZXB0aW9uIiwiaW5FeGNlcHRpb25IYW5kbGVyIiwiaGFuZGxlRXhjZXB0aW9uIiwiUk4kaGFuZGxlRXhjZXB0aW9uIiwicmVhY3RDb25zb2xlRXJyb3JIYW5kbGVyIiwiX2NvbnNvbGUiLCJfbGVuMiIsIl9rZXkyIiwiX2Vycm9yT3JpZ2luYWwiLCJyZXBvcnRFcnJvcnNBc0V4Y2VwdGlvbnMiLCJSTiRpbkV4Y2VwdGlvbkhhbmRsZXIiLCJmaXJzdEFyZyIsInN0cmluZ2lmeVNhZmUiLCJtYXAiLCJhcmciLCJqb2luIiwiaW5zdGFsbENvbnNvbGVFcnJvclJlcG9ydGVyIiwiYmluZCIsInVuZGVmaW5lZCIsIkV4Y2VwdGlvbnNNYW5hZ2VyIiwiX2RlZmF1bHQiXSwic291cmNlcyI6WyJFeGNlcHRpb25zTWFuYWdlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICogQGZvcm1hdFxuICogQGZsb3cgc3RyaWN0XG4gKi9cblxuJ3VzZSBzdHJpY3QnO1xuXG5pbXBvcnQgdHlwZSB7RXh0ZW5kZWRFcnJvcn0gZnJvbSAnLi9FeHRlbmRlZEVycm9yJztcbmltcG9ydCB0eXBlIHtFeGNlcHRpb25EYXRhfSBmcm9tICcuL05hdGl2ZUV4Y2VwdGlvbnNNYW5hZ2VyJztcblxuZXhwb3J0IGNsYXNzIFN5bnRoZXRpY0Vycm9yIGV4dGVuZHMgRXJyb3Ige1xuICBuYW1lOiBzdHJpbmcgPSAnJztcbn1cblxudHlwZSBFeGNlcHRpb25EZWNvcmF0b3IgPSBFeGNlcHRpb25EYXRhID0+IEV4Y2VwdGlvbkRhdGE7XG5cbmxldCB1c2VyRXhjZXB0aW9uRGVjb3JhdG9yOiA/RXhjZXB0aW9uRGVjb3JhdG9yO1xubGV0IGluVXNlckV4Y2VwdGlvbkRlY29yYXRvciA9IGZhbHNlO1xuXG4vLyBUaGlzIHN0cmluZyBpcyB1c2VkIHRvIGRlY29yYXRlIGFuIEV4dGVuZGVkRXJyb3Igd2l0aCBleHRyYSBkYXRhIGluIHNlbGVjdCB1c2VjYXNlcy5cbi8vIE5vdGUgdGhhdCBkYXRhIHBhc3NlZCB1c2luZyB0aGlzIG1ldGhvZCBzaG91bGQgYmUgc3RyaWN0bHkgY29udGFpbmVkLFxuLy8gYXMgZGF0YSB0aGF0J3Mgbm90IHNlcmlhbGl6YWJsZS90b28gbGFyZ2UgbWF5IGNhdXNlIGlzc3VlcyB3aXRoIHBhc3NpbmcgdGhlIGVycm9yIHRvIHRoZSBuYXRpdmUgY29kZS5cbi8vIFRPRE8oVDIwNDE4NTUxNyk6IFdlIHNob3VsZCB1c2UgYSBTeW1ib2wgZm9yIHRoaXMsIGJ1dCBqc2kgdGhyb3VnaCBqc2MgZG9lc24ndCBzdXBwb3J0IGl0IHlldC5cbmNvbnN0IGRlY29yYXRlZEV4dHJhRGF0YUtleSA9ICdSTiRFcnJvckV4dHJhRGF0YUtleSc7XG5cbi8qKlxuICogQWxsb3dzIHRoZSBhcHAgdG8gYWRkIGluZm9ybWF0aW9uIHRvIHRoZSBleGNlcHRpb24gcmVwb3J0IGJlZm9yZSBpdCBpcyBzZW50XG4gKiB0byBuYXRpdmUuIFRoaXMgQVBJIGlzIG5vdCBmaW5hbC5cbiAqL1xuXG5mdW5jdGlvbiB1bnN0YWJsZV9zZXRFeGNlcHRpb25EZWNvcmF0b3IoXG4gIGV4Y2VwdGlvbkRlY29yYXRvcjogP0V4Y2VwdGlvbkRlY29yYXRvcixcbikge1xuICB1c2VyRXhjZXB0aW9uRGVjb3JhdG9yID0gZXhjZXB0aW9uRGVjb3JhdG9yO1xufVxuXG5mdW5jdGlvbiBwcmVwcm9jZXNzRXhjZXB0aW9uKGRhdGE6IEV4Y2VwdGlvbkRhdGEpOiBFeGNlcHRpb25EYXRhIHtcbiAgaWYgKHVzZXJFeGNlcHRpb25EZWNvcmF0b3IgJiYgIWluVXNlckV4Y2VwdGlvbkRlY29yYXRvcikge1xuICAgIGluVXNlckV4Y2VwdGlvbkRlY29yYXRvciA9IHRydWU7XG4gICAgdHJ5IHtcbiAgICAgIHJldHVybiB1c2VyRXhjZXB0aW9uRGVjb3JhdG9yKGRhdGEpO1xuICAgIH0gY2F0Y2gge1xuICAgICAgLy8gRmFsbCB0aHJvdWdoXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIGluVXNlckV4Y2VwdGlvbkRlY29yYXRvciA9IGZhbHNlO1xuICAgIH1cbiAgfVxuICByZXR1cm4gZGF0YTtcbn1cblxuLyoqXG4gKiBIYW5kbGVzIHRoZSBkZXZlbG9wZXItdmlzaWJsZSBhc3BlY3Qgb2YgZXJyb3JzIGFuZCBleGNlcHRpb25zXG4gKi9cbmxldCBleGNlcHRpb25JRCA9IDA7XG5mdW5jdGlvbiByZXBvcnRFeGNlcHRpb24oXG4gIGU6IEV4dGVuZGVkRXJyb3IsXG4gIGlzRmF0YWw6IGJvb2xlYW4sXG4gIHJlcG9ydFRvQ29uc29sZTogYm9vbGVhbiwgLy8gb25seSB0cnVlIHdoZW4gY29taW5nIGZyb20gaGFuZGxlRXhjZXB0aW9uOyB0aGUgZXJyb3IgaGFzIG5vdCB5ZXQgYmVlbiBsb2dnZWRcbikge1xuICBjb25zdCBwYXJzZUVycm9yU3RhY2sgPSByZXF1aXJlKCcuL0RldnRvb2xzL3BhcnNlRXJyb3JTdGFjaycpLmRlZmF1bHQ7XG4gIGNvbnN0IHN0YWNrID0gcGFyc2VFcnJvclN0YWNrKGU/LnN0YWNrKTtcbiAgY29uc3QgY3VycmVudEV4Y2VwdGlvbklEID0gKytleGNlcHRpb25JRDtcbiAgY29uc3Qgb3JpZ2luYWxNZXNzYWdlID0gZS5tZXNzYWdlIHx8ICcnO1xuICBsZXQgbWVzc2FnZSA9IG9yaWdpbmFsTWVzc2FnZTtcbiAgaWYgKGUuY29tcG9uZW50U3RhY2sgIT0gbnVsbCkge1xuICAgIG1lc3NhZ2UgKz0gYFxcblxcblRoaXMgZXJyb3IgaXMgbG9jYXRlZCBhdDoke2UuY29tcG9uZW50U3RhY2t9YDtcbiAgfVxuICBjb25zdCBuYW1lUHJlZml4ID0gZS5uYW1lID09IG51bGwgfHwgZS5uYW1lID09PSAnJyA/ICcnIDogYCR7ZS5uYW1lfTogYDtcblxuICBpZiAoIW1lc3NhZ2Uuc3RhcnRzV2l0aChuYW1lUHJlZml4KSkge1xuICAgIG1lc3NhZ2UgPSBuYW1lUHJlZml4ICsgbWVzc2FnZTtcbiAgfVxuXG4gIG1lc3NhZ2UgPVxuICAgIGUuanNFbmdpbmUgPT0gbnVsbCA/IG1lc3NhZ2UgOiBgJHttZXNzYWdlfSwganMgZW5naW5lOiAke2UuanNFbmdpbmV9YDtcblxuICAvLyAkRmxvd0ZpeE1lW3VuY2xlYXItdHlwZV1cbiAgY29uc3QgZXh0cmFEYXRhOiBPYmplY3QgPSB7XG4gICAgLy8gJEZsb3dGaXhNZVtpbmNvbXBhdGlibGUtdXNlXSB3ZSBjYW4ndCBkZWZpbmUgYSB0eXBlIHdpdGggYSBTeW1ib2wta2V5ZWQgZmllbGQgaW4gZmxvd1xuICAgIC4uLmVbZGVjb3JhdGVkRXh0cmFEYXRhS2V5XSxcbiAgICBqc0VuZ2luZTogZS5qc0VuZ2luZSxcbiAgICByYXdTdGFjazogZS5zdGFjayxcbiAgfTtcbiAgaWYgKGUuY2F1c2UgIT0gbnVsbCAmJiB0eXBlb2YgZS5jYXVzZSA9PT0gJ29iamVjdCcpIHtcbiAgICBleHRyYURhdGEuc3RhY2tTeW1ib2xzID0gZS5jYXVzZS5zdGFja1N5bWJvbHM7XG4gICAgZXh0cmFEYXRhLnN0YWNrUmV0dXJuQWRkcmVzc2VzID0gZS5jYXVzZS5zdGFja1JldHVybkFkZHJlc3NlcztcbiAgICBleHRyYURhdGEuc3RhY2tFbGVtZW50cyA9IGUuY2F1c2Uuc3RhY2tFbGVtZW50cztcbiAgfVxuXG4gIGNvbnN0IGRhdGEgPSBwcmVwcm9jZXNzRXhjZXB0aW9uKHtcbiAgICBtZXNzYWdlLFxuICAgIG9yaWdpbmFsTWVzc2FnZTogbWVzc2FnZSA9PT0gb3JpZ2luYWxNZXNzYWdlID8gbnVsbCA6IG9yaWdpbmFsTWVzc2FnZSxcbiAgICBuYW1lOiBlLm5hbWUgPT0gbnVsbCB8fCBlLm5hbWUgPT09ICcnID8gbnVsbCA6IGUubmFtZSxcbiAgICBjb21wb25lbnRTdGFjazpcbiAgICAgIHR5cGVvZiBlLmNvbXBvbmVudFN0YWNrID09PSAnc3RyaW5nJyA/IGUuY29tcG9uZW50U3RhY2sgOiBudWxsLFxuICAgIHN0YWNrLFxuICAgIGlkOiBjdXJyZW50RXhjZXB0aW9uSUQsXG4gICAgaXNGYXRhbCxcbiAgICBleHRyYURhdGEsXG4gIH0pO1xuXG4gIGlmIChyZXBvcnRUb0NvbnNvbGUpIHtcbiAgICAvLyB3ZSBmZWVkIGJhY2sgaW50byBjb25zb2xlLmVycm9yLCB0byBtYWtlIHN1cmUgYW55IG1ldGhvZHMgdGhhdCBhcmVcbiAgICAvLyBtb25rZXkgcGF0Y2hlZCBvbiB0b3Agb2YgY29uc29sZS5lcnJvciBhcmUgY2FsbGVkIHdoZW4gY29taW5nIGZyb21cbiAgICAvLyBoYW5kbGVFeGNlcHRpb25cbiAgICBjb25zb2xlLmVycm9yKGRhdGEubWVzc2FnZSk7XG4gIH1cblxuICBpZiAoX19ERVZfXykge1xuICAgIGNvbnN0IExvZ0JveCA9IHJlcXVpcmUoJy4uL0xvZ0JveC9Mb2dCb3gnKS5kZWZhdWx0O1xuICAgIExvZ0JveC5hZGRFeGNlcHRpb24oe1xuICAgICAgLi4uZGF0YSxcbiAgICAgIGlzQ29tcG9uZW50RXJyb3I6ICEhZS5pc0NvbXBvbmVudEVycm9yLFxuICAgIH0pO1xuICB9IGVsc2UgaWYgKGlzRmF0YWwgfHwgZS50eXBlICE9PSAnd2FybicpIHtcbiAgICBjb25zdCBOYXRpdmVFeGNlcHRpb25zTWFuYWdlciA9XG4gICAgICByZXF1aXJlKCcuL05hdGl2ZUV4Y2VwdGlvbnNNYW5hZ2VyJykuZGVmYXVsdDtcbiAgICBpZiAoTmF0aXZlRXhjZXB0aW9uc01hbmFnZXIpIHtcbiAgICAgIGlmIChpc0ZhdGFsKSB7XG4gICAgICAgIGlmIChnbG9iYWwuUk4kaGFzSGFuZGxlZEZhdGFsRXhjZXB0aW9uPy4oKSkge1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBnbG9iYWwuUk4kbm90aWZ5T2ZGYXRhbEV4Y2VwdGlvbj8uKCk7XG4gICAgICB9XG4gICAgICBOYXRpdmVFeGNlcHRpb25zTWFuYWdlci5yZXBvcnRFeGNlcHRpb24oZGF0YSk7XG4gICAgfVxuICB9XG59XG5cbmRlY2xhcmUgdmFyIGNvbnNvbGU6IHtcbiAgZXJyb3I6ICguLi5kYXRhOiAkUmVhZE9ubHlBcnJheTxtaXhlZD4pID0+IHZvaWQsXG4gIF9lcnJvck9yaWdpbmFsOiAoLi4uZGF0YTogJFJlYWRPbmx5QXJyYXk8bWl4ZWQ+KSA9PiB2b2lkLFxuICByZXBvcnRFcnJvcnNBc0V4Y2VwdGlvbnM6IGJvb2xlYW4sXG4gIC4uLlxufTtcblxuLy8gSWYgd2UgdHJpZ2dlciBjb25zb2xlLmVycm9yIF9mcm9tXyBoYW5kbGVFeGNlcHRpb24sXG4vLyB3ZSBkbyB3YW50IHRvIG1ha2Ugc3VyZSB0aGF0IGNvbnNvbGUuZXJyb3IgZG9lc24ndCB0cmlnZ2VyIGVycm9yIHJlcG9ydGluZyBhZ2FpblxubGV0IGluRXhjZXB0aW9uSGFuZGxlciA9IGZhbHNlO1xuXG4vKipcbiAqIExvZ3MgZXhjZXB0aW9ucyB0byB0aGUgKG5hdGl2ZSkgY29uc29sZSBhbmQgZGlzcGxheXMgdGhlbVxuICovXG5mdW5jdGlvbiBoYW5kbGVFeGNlcHRpb24oZTogbWl4ZWQsIGlzRmF0YWw6IGJvb2xlYW4pIHtcbiAgLy8gVE9ETyhUMTk2ODM0Mjk5KTogV2Ugc2hvdWxkIHJlYWxseSB1c2UgYSBjKysgdHVyYm9tb2R1bGUgZm9yIHRoaXNcbiAgY29uc3QgcmVwb3J0VG9Db25zb2xlID0gdHJ1ZTtcbiAgaWYgKFxuICAgICFnbG9iYWwuUk4kaGFuZGxlRXhjZXB0aW9uIHx8XG4gICAgIWdsb2JhbC5STiRoYW5kbGVFeGNlcHRpb24oZSwgaXNGYXRhbCwgcmVwb3J0VG9Db25zb2xlKVxuICApIHtcbiAgICBsZXQgZXJyb3I6IEVycm9yO1xuICAgIGlmIChlIGluc3RhbmNlb2YgRXJyb3IpIHtcbiAgICAgIGVycm9yID0gZTtcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gV29ya2Fyb3VuZCBmb3IgcmVwb3J0aW5nIGVycm9ycyBjYXVzZWQgYnkgYHRocm93ICdzb21lIHN0cmluZydgXG4gICAgICAvLyBVbmZvcnR1bmF0ZWx5IHRoZXJlIGlzIG5vIHdheSB0byBmaWd1cmUgb3V0IHRoZSBzdGFja3RyYWNlIGluIHRoaXNcbiAgICAgIC8vIGNhc2UsIHNvIGlmIHlvdSBlbmRlZCB1cCBoZXJlIHRyeWluZyB0byB0cmFjZSBhbiBlcnJvciwgbG9vayBmb3JcbiAgICAgIC8vIGB0aHJvdyAnPGVycm9yIG1lc3NhZ2U+J2Agc29tZXdoZXJlIGluIHlvdXIgY29kZWJhc2UuXG4gICAgICBlcnJvciA9IG5ldyBTeW50aGV0aWNFcnJvcihlKTtcbiAgICB9XG4gICAgdHJ5IHtcbiAgICAgIGluRXhjZXB0aW9uSGFuZGxlciA9IHRydWU7XG4gICAgICAvKiAkRmxvd0ZpeE1lW2NsYXNzLW9iamVjdC1zdWJ0eXBpbmddIGFkZGVkIHdoZW4gaW1wcm92aW5nIHR5cGluZyBmb3IgdGhpc1xuICAgICAgICogcGFyYW1ldGVycyAqL1xuICAgICAgLy8gJEZsb3dGaXhNZVtpbmNvbXBhdGlibGUtY2FsbF1cbiAgICAgIHJlcG9ydEV4Y2VwdGlvbihlcnJvciwgaXNGYXRhbCwgcmVwb3J0VG9Db25zb2xlKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgaW5FeGNlcHRpb25IYW5kbGVyID0gZmFsc2U7XG4gICAgfVxuICB9XG59XG5cbi8qICRGbG93Rml4TWVbbWlzc2luZy1sb2NhbC1hbm5vdF0gVGhlIHR5cGUgYW5ub3RhdGlvbihzKSByZXF1aXJlZCBieSBGbG93J3NcbiAqIExUSSB1cGRhdGUgY291bGQgbm90IGJlIGFkZGVkIHZpYSBjb2RlbW9kICovXG5mdW5jdGlvbiByZWFjdENvbnNvbGVFcnJvckhhbmRsZXIoLi4uYXJncykge1xuICAvLyBidWJibGUgdXAgdG8gYW55IG9yaWdpbmFsIGhhbmRsZXJzXG4gIGNvbnNvbGUuX2Vycm9yT3JpZ2luYWwoLi4uYXJncyk7XG4gIGlmICghY29uc29sZS5yZXBvcnRFcnJvcnNBc0V4Y2VwdGlvbnMpIHtcbiAgICByZXR1cm47XG4gIH1cbiAgaWYgKGluRXhjZXB0aW9uSGFuZGxlciB8fCBnbG9iYWwuUk4kaW5FeGNlcHRpb25IYW5kbGVyPy4oKSkge1xuICAgIC8vIFRoZSBmdW5kYW1lbnRhbCB0cmljayBoZXJlIGlzIHRoYXQgYXJlIG11bHRpcGxlIGVudHJ5IHBvaW50IHRvIGxvZ2dpbmcgZXJyb3JzOlxuICAgIC8vIChzZWUgRDE5NzQzMDc1IGZvciBtb3JlIGJhY2tncm91bmQpXG4gICAgLy9cbiAgICAvLyAxLiBBbiB1bmNhdWdodCBleGNlcHRpb24gYmVpbmcgY2F1Z2h0IGJ5IHRoZSBnbG9iYWwgaGFuZGxlclxuICAgIC8vIDIuIEFuIGVycm9yIGJlaW5nIGxvZ2dlZCB0aHJvdyBjb25zb2xlLmVycm9yXG4gICAgLy9cbiAgICAvLyBIb3dldmVyLCBjb25zb2xlLmVycm9yIGlzIG1vbmtleSBwYXRjaGVkIG11bHRpcGxlIHRpbWVzOiBieSB0aGlzIG1vZHVsZSwgYW5kIGJ5IHRoZVxuICAgIC8vIERldlRvb2xzIHNldHVwIHRoYXQgc2VuZHMgbWVzc2FnZXMgdG8gTWV0cm8uXG4gICAgLy8gVGhlIHBhdGNoaW5nIG9yZGVyIGNhbm5vdCBiZSByZWxpZWQgdXBvbi5cbiAgICAvL1xuICAgIC8vIFNvLCBzb21lIHNjZW5hcmlvcyB0aGF0IGFyZSBoYW5kbGVkIGJ5IHRoaXMgZmxhZzpcbiAgICAvL1xuICAgIC8vIExvZ2dpbmcgYW4gZXJyb3I6XG4gICAgLy8gMS4gY29uc29sZS5lcnJvciBjYWxsZWQgZnJvbSB1c2VyIGNvZGVcbiAgICAvLyAyLiAocG9zc2libHkpIGFycml2ZXMgX2ZpcnN0XyBhdCBEZXZUb29sIGhhbmRsZXIsIHNlbmQgdG8gTWV0cm9cbiAgICAvLyAzLiBCdWJibGVzIHRvIGhlcmVcbiAgICAvLyA0LiBnb2VzIGludG8gcmVwb3J0IEV4Y2VwdGlvbi5cbiAgICAvLyA1LiBzaG91bGQgbm90IHRyaWdnZXIgY29uc29sZS5lcnJvciBhZ2FpbiwgdG8gYXZvaWQgbG9vcGluZyAvIGxvZ2dpbmcgdHdpY2VcbiAgICAvLyA2LiBzaG91bGQgc3RpbGwgYnViYmxlIHVwIHRvIG9yaWdpbmFsIGNvbnNvbGVcbiAgICAvLyAgICAod2hpY2ggbWlnaHQgZWl0aGVyIGJlIGNvbnNvbGUubG9nLCBvciB0aGUgRGV2VG9vbHMgaGFuZGxlciBpbiBjYXNlIGl0IHBhdGNoZWQgX2VhcmxpZXJfIGFuZCAoMikgZGlkbid0IGhhcHBlbilcbiAgICAvL1xuICAgIC8vIFRocm93aW5nIGFuIHVuY2F1Z2h0IGV4Y2VwdGlvbjpcbiAgICAvLyAxLiBleGNlcHRpb24gdGhyb3duXG4gICAgLy8gMi4gcGlja2VkIHVwIGJ5IGhhbmRsZUV4Y2VwdGlvblxuICAgIC8vIDMuIHNob3VsZCBiZSBzZW50IHRvIGNvbnNvbGUuZXJyb3IgKG5vdCBjb25zb2xlLl9lcnJvck9yaWdpbmFsLCBhcyBEZXZUb29scyBtaWdodCBoYXZlIHBhdGNoZWQgX2xhdGVyXyBhbmQgaXQgbmVlZHMgdG8gc2VuZCBpdCB0byBNZXRybylcbiAgICAvLyA0LiB0aGF0IF9taWdodF8gYnViYmxlIGFnYWluIHRvIHRoZSBgcmVhY3RDb25zb2xlRXJyb3JIYW5kbGVgIGRlZmluZWQgaGVyZVxuICAgIC8vICAgIC0+IHNob3VsZCBub3QgaGFuZGxlIGV4Y2VwdGlvbiBfYWdhaW5fLCB0byBhdm9pZCBsb29waW5nIC8gc2hvd2luZyB0d2ljZSAodGhpcyBjb2RlIGJyYW5jaClcbiAgICAvLyA1LiBzaG91bGQgc3RpbGwgYnViYmxlIHVwIHRvIG9yaWdpbmFsIGNvbnNvbGUgKHdoaWNoIG1pZ2h0IGVpdGhlciBiZSBjb25zb2xlLmxvZywgb3IgdGhlIERldlRvb2xzIGhhbmRsZXIgaW4gY2FzZSB0aGF0IG9uZSBwYXRjaGVkIF9lYXJsaWVyXylcbiAgICByZXR1cm47XG4gIH1cblxuICBsZXQgZXJyb3I7XG5cbiAgY29uc3QgZmlyc3RBcmcgPSBhcmdzWzBdO1xuICBpZiAoZmlyc3RBcmc/LnN0YWNrKSB7XG4gICAgLy8gcmVwb3J0RXhjZXB0aW9uIHdpbGwgY29uc29sZS5lcnJvciB0aGlzIHdpdGggaGlnaCBlbm91Z2ggZmlkZWxpdHkuXG4gICAgZXJyb3IgPSBmaXJzdEFyZztcbiAgfSBlbHNlIHtcbiAgICBjb25zdCBzdHJpbmdpZnlTYWZlID0gcmVxdWlyZSgnLi4vVXRpbGl0aWVzL3N0cmluZ2lmeVNhZmUnKS5kZWZhdWx0O1xuICAgIGlmICh0eXBlb2YgZmlyc3RBcmcgPT09ICdzdHJpbmcnICYmIGZpcnN0QXJnLnN0YXJ0c1dpdGgoJ1dhcm5pbmc6ICcpKSB7XG4gICAgICAvLyBSZWFjdCB3YXJuaW5ncyB1c2UgY29uc29sZS5lcnJvciBzbyB0aGF0IGEgc3RhY2sgdHJhY2UgaXMgc2hvd24sIGJ1dFxuICAgICAgLy8gd2UgZG9uJ3QgKGN1cnJlbnRseSkgd2FudCB0aGVzZSB0byBzaG93IGEgcmVkYm94XG4gICAgICAvLyAoTm90ZTogTG9naWMgZHVwbGljYXRlZCBpbiBwb2x5ZmlsbHMvY29uc29sZS5qcy4pXG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIGNvbnN0IG1lc3NhZ2UgPSBhcmdzXG4gICAgICAubWFwKGFyZyA9PiAodHlwZW9mIGFyZyA9PT0gJ3N0cmluZycgPyBhcmcgOiBzdHJpbmdpZnlTYWZlKGFyZykpKVxuICAgICAgLmpvaW4oJyAnKTtcblxuICAgIGVycm9yID0gbmV3IFN5bnRoZXRpY0Vycm9yKG1lc3NhZ2UpO1xuICAgIGVycm9yLm5hbWUgPSAnY29uc29sZS5lcnJvcic7XG4gIH1cblxuICBjb25zdCBpc0ZhdGFsID0gZmFsc2U7XG4gIGNvbnN0IHJlcG9ydFRvQ29uc29sZSA9IGZhbHNlO1xuICBpZiAoXG4gICAgIWdsb2JhbC5STiRoYW5kbGVFeGNlcHRpb24gfHxcbiAgICAhZ2xvYmFsLlJOJGhhbmRsZUV4Y2VwdGlvbihlcnJvciwgaXNGYXRhbCwgcmVwb3J0VG9Db25zb2xlKVxuICApIHtcbiAgICByZXBvcnRFeGNlcHRpb24oXG4gICAgICAvKiAkRmxvd0ZpeE1lW2NsYXNzLW9iamVjdC1zdWJ0eXBpbmddIGFkZGVkIHdoZW4gaW1wcm92aW5nIHR5cGluZyBmb3IgdGhpc1xuICAgICAgICogcGFyYW1ldGVycyAqL1xuICAgICAgLy8gJEZsb3dGaXhNZVtpbmNvbXBhdGlibGUtY2FsbF1cbiAgICAgIGVycm9yLFxuICAgICAgaXNGYXRhbCxcbiAgICAgIHJlcG9ydFRvQ29uc29sZSxcbiAgICApO1xuICB9XG59XG5cbi8qKlxuICogU2hvd3MgYSByZWRib3ggd2l0aCBzdGFja3RyYWNlIGZvciBhbGwgY29uc29sZS5lcnJvciBtZXNzYWdlcy4gIERpc2FibGUgYnlcbiAqIHNldHRpbmcgYGNvbnNvbGUucmVwb3J0RXJyb3JzQXNFeGNlcHRpb25zID0gZmFsc2U7YCBpbiB5b3VyIGFwcC5cbiAqL1xuZnVuY3Rpb24gaW5zdGFsbENvbnNvbGVFcnJvclJlcG9ydGVyKCkge1xuICAvLyBFbmFibGUgcmVwb3J0RXJyb3JzQXNFeGNlcHRpb25zXG4gIGlmIChjb25zb2xlLl9lcnJvck9yaWdpbmFsKSB7XG4gICAgcmV0dXJuOyAvLyBhbHJlYWR5IGluc3RhbGxlZFxuICB9XG4gIC8vIEZsb3cgZG9lc24ndCBsaWtlIGl0IHdoZW4geW91IHNldCBhcmJpdHJhcnkgdmFsdWVzIG9uIGEgZ2xvYmFsIG9iamVjdFxuICBjb25zb2xlLl9lcnJvck9yaWdpbmFsID0gY29uc29sZS5lcnJvci5iaW5kKGNvbnNvbGUpO1xuICBjb25zb2xlLmVycm9yID0gcmVhY3RDb25zb2xlRXJyb3JIYW5kbGVyO1xuICBpZiAoY29uc29sZS5yZXBvcnRFcnJvcnNBc0V4Y2VwdGlvbnMgPT09IHVuZGVmaW5lZCkge1xuICAgIC8vIEluZGl2aWR1YWwgYXBwcyBjYW4gZGlzYWJsZSB0aGlzXG4gICAgLy8gRmxvdyBkb2Vzbid0IGxpa2UgaXQgd2hlbiB5b3Ugc2V0IGFyYml0cmFyeSB2YWx1ZXMgb24gYSBnbG9iYWwgb2JqZWN0XG4gICAgY29uc29sZS5yZXBvcnRFcnJvcnNBc0V4Y2VwdGlvbnMgPSB0cnVlO1xuICB9XG59XG5cbmNvbnN0IEV4Y2VwdGlvbnNNYW5hZ2VyID0ge1xuICBkZWNvcmF0ZWRFeHRyYURhdGFLZXksXG4gIGhhbmRsZUV4Y2VwdGlvbixcbiAgaW5zdGFsbENvbnNvbGVFcnJvclJlcG9ydGVyLFxuICBTeW50aGV0aWNFcnJvciwgLy8gPC0gZm9yIGJhY2t3YXJkcyBjb21wYXRpYmlsaXR5XG4gIHVuc3RhYmxlX3NldEV4Y2VwdGlvbkRlY29yYXRvcixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IEV4Y2VwdGlvbnNNYW5hZ2VyO1xuIl0sIm1hcHBpbmdzIjoiQUFVQSxZQUFZOztBQUFDLElBQUFBLHNCQUFBLEdBQUFDLE9BQUE7QUFBQUMsTUFBQSxDQUFBQyxjQUFBLENBQUFDLE9BQUE7RUFBQUMsS0FBQTtBQUFBO0FBQUFELE9BQUEsQ0FBQUUsT0FBQSxHQUFBRixPQUFBLENBQUFHLGNBQUE7QUFBQSxJQUFBQyxhQUFBLEdBQUFSLHNCQUFBLENBQUFDLE9BQUE7QUFBQSxJQUFBUSxnQkFBQSxHQUFBVCxzQkFBQSxDQUFBQyxPQUFBO0FBQUEsSUFBQVMsMkJBQUEsR0FBQVYsc0JBQUEsQ0FBQUMsT0FBQTtBQUFBLElBQUFVLGdCQUFBLEdBQUFYLHNCQUFBLENBQUFDLE9BQUE7QUFBQSxJQUFBVyxVQUFBLEdBQUFaLHNCQUFBLENBQUFDLE9BQUE7QUFBQSxJQUFBWSxpQkFBQSxHQUFBYixzQkFBQSxDQUFBQyxPQUFBO0FBQUEsU0FBQWEsV0FBQUMsQ0FBQSxFQUFBQyxDQUFBLEVBQUFDLENBQUEsV0FBQUQsQ0FBQSxPQUFBTCxnQkFBQSxDQUFBTCxPQUFBLEVBQUFVLENBQUEsT0FBQU4sMkJBQUEsQ0FBQUosT0FBQSxFQUFBUyxDQUFBLEVBQUFHLHlCQUFBLEtBQUFDLE9BQUEsQ0FBQUMsU0FBQSxDQUFBSixDQUFBLEVBQUFDLENBQUEsWUFBQU4sZ0JBQUEsQ0FBQUwsT0FBQSxFQUFBUyxDQUFBLEVBQUFNLFdBQUEsSUFBQUwsQ0FBQSxDQUFBTSxLQUFBLENBQUFQLENBQUEsRUFBQUUsQ0FBQTtBQUFBLFNBQUFDLDBCQUFBLGNBQUFILENBQUEsSUFBQVEsT0FBQSxDQUFBQyxTQUFBLENBQUFDLE9BQUEsQ0FBQUMsSUFBQSxDQUFBUCxPQUFBLENBQUFDLFNBQUEsQ0FBQUcsT0FBQSxpQ0FBQVIsQ0FBQSxhQUFBRyx5QkFBQSxZQUFBQSwwQkFBQSxhQUFBSCxDQUFBO0FBQUEsSUFLQVIsY0FBYyxHQUFBSCxPQUFBLENBQUFHLGNBQUEsYUFBQW9CLE1BQUE7RUFBQSxTQUFBcEIsZUFBQTtJQUFBLElBQUFxQixLQUFBO0lBQUEsSUFBQW5CLGdCQUFBLENBQUFILE9BQUEsUUFBQUMsY0FBQTtJQUFBLFNBQUFzQixJQUFBLEdBQUFDLFNBQUEsQ0FBQUMsTUFBQSxFQUFBQyxJQUFBLE9BQUFDLEtBQUEsQ0FBQUosSUFBQSxHQUFBSyxJQUFBLE1BQUFBLElBQUEsR0FBQUwsSUFBQSxFQUFBSyxJQUFBO01BQUFGLElBQUEsQ0FBQUUsSUFBQSxJQUFBSixTQUFBLENBQUFJLElBQUE7SUFBQTtJQUFBTixLQUFBLEdBQUFkLFVBQUEsT0FBQVAsY0FBQSxLQUFBNEIsTUFBQSxDQUFBSCxJQUFBO0lBQUFKLEtBQUEsQ0FDekJRLElBQUksR0FBVyxFQUFFO0lBQUEsT0FBQVIsS0FBQTtFQUFBO0VBQUEsSUFBQWhCLFVBQUEsQ0FBQU4sT0FBQSxFQUFBQyxjQUFBLEVBQUFvQixNQUFBO0VBQUEsV0FBQW5CLGFBQUEsQ0FBQUYsT0FBQSxFQUFBQyxjQUFBO0FBQUEsTUFBQU0saUJBQUEsQ0FBQVAsT0FBQSxFQURpQitCLEtBQUs7QUFNekMsSUFBSUMsc0JBQTJDO0FBQy9DLElBQUlDLHdCQUF3QixHQUFHLEtBQUs7QUFNcEMsSUFBTUMscUJBQXFCLEdBQUcsc0JBQXNCO0FBT3BELFNBQVNDLDhCQUE4QkEsQ0FDckNDLGtCQUF1QyxFQUN2QztFQUNBSixzQkFBc0IsR0FBR0ksa0JBQWtCO0FBQzdDO0FBRUEsU0FBU0MsbUJBQW1CQSxDQUFDQyxJQUFtQixFQUFpQjtFQUMvRCxJQUFJTixzQkFBc0IsSUFBSSxDQUFDQyx3QkFBd0IsRUFBRTtJQUN2REEsd0JBQXdCLEdBQUcsSUFBSTtJQUMvQixJQUFJO01BQ0YsT0FBT0Qsc0JBQXNCLENBQUNNLElBQUksQ0FBQztJQUNyQyxDQUFDLENBQUMsT0FBQUMsT0FBQSxFQUFNLENBRVIsQ0FBQyxTQUFTO01BQ1JOLHdCQUF3QixHQUFHLEtBQUs7SUFDbEM7RUFDRjtFQUNBLE9BQU9LLElBQUk7QUFDYjtBQUtBLElBQUlFLFdBQVcsR0FBRyxDQUFDO0FBQ25CLFNBQVNDLGVBQWVBLENBQ3RCOUIsQ0FBZ0IsRUFDaEIrQixPQUFnQixFQUNoQkMsZUFBd0IsRUFDeEI7RUFDQSxJQUFNQyxlQUFlLEdBQUdqRCxPQUFPLDZCQUE2QixDQUFDLENBQUNLLE9BQU87RUFDckUsSUFBTTZDLEtBQUssR0FBR0QsZUFBZSxDQUFDakMsQ0FBQyxvQkFBREEsQ0FBQyxDQUFFa0MsS0FBSyxDQUFDO0VBQ3ZDLElBQU1DLGtCQUFrQixHQUFHLEVBQUVOLFdBQVc7RUFDeEMsSUFBTU8sZUFBZSxHQUFHcEMsQ0FBQyxDQUFDcUMsT0FBTyxJQUFJLEVBQUU7RUFDdkMsSUFBSUEsT0FBTyxHQUFHRCxlQUFlO0VBQzdCLElBQUlwQyxDQUFDLENBQUNzQyxjQUFjLElBQUksSUFBSSxFQUFFO0lBQzVCRCxPQUFPLElBQUksZ0NBQWdDckMsQ0FBQyxDQUFDc0MsY0FBYyxFQUFFO0VBQy9EO0VBQ0EsSUFBTUMsVUFBVSxHQUFHdkMsQ0FBQyxDQUFDbUIsSUFBSSxJQUFJLElBQUksSUFBSW5CLENBQUMsQ0FBQ21CLElBQUksS0FBSyxFQUFFLEdBQUcsRUFBRSxHQUFHLEdBQUduQixDQUFDLENBQUNtQixJQUFJLElBQUk7RUFFdkUsSUFBSSxDQUFDa0IsT0FBTyxDQUFDRyxVQUFVLENBQUNELFVBQVUsQ0FBQyxFQUFFO0lBQ25DRixPQUFPLEdBQUdFLFVBQVUsR0FBR0YsT0FBTztFQUNoQztFQUVBQSxPQUFPLEdBQ0xyQyxDQUFDLENBQUN5QyxRQUFRLElBQUksSUFBSSxHQUFHSixPQUFPLEdBQUcsR0FBR0EsT0FBTyxnQkFBZ0JyQyxDQUFDLENBQUN5QyxRQUFRLEVBQUU7RUFHdkUsSUFBTUMsU0FBaUIsR0FBQXpELE1BQUEsQ0FBQTBELE1BQUEsS0FFbEIzQyxDQUFDLENBQUN1QixxQkFBcUIsQ0FBQztJQUMzQmtCLFFBQVEsRUFBRXpDLENBQUMsQ0FBQ3lDLFFBQVE7SUFDcEJHLFFBQVEsRUFBRTVDLENBQUMsQ0FBQ2tDO0VBQUssRUFDbEI7RUFDRCxJQUFJbEMsQ0FBQyxDQUFDNkMsS0FBSyxJQUFJLElBQUksSUFBSSxPQUFPN0MsQ0FBQyxDQUFDNkMsS0FBSyxLQUFLLFFBQVEsRUFBRTtJQUNsREgsU0FBUyxDQUFDSSxZQUFZLEdBQUc5QyxDQUFDLENBQUM2QyxLQUFLLENBQUNDLFlBQVk7SUFDN0NKLFNBQVMsQ0FBQ0ssb0JBQW9CLEdBQUcvQyxDQUFDLENBQUM2QyxLQUFLLENBQUNFLG9CQUFvQjtJQUM3REwsU0FBUyxDQUFDTSxhQUFhLEdBQUdoRCxDQUFDLENBQUM2QyxLQUFLLENBQUNHLGFBQWE7RUFDakQ7RUFFQSxJQUFNckIsSUFBSSxHQUFHRCxtQkFBbUIsQ0FBQztJQUMvQlcsT0FBTyxFQUFQQSxPQUFPO0lBQ1BELGVBQWUsRUFBRUMsT0FBTyxLQUFLRCxlQUFlLEdBQUcsSUFBSSxHQUFHQSxlQUFlO0lBQ3JFakIsSUFBSSxFQUFFbkIsQ0FBQyxDQUFDbUIsSUFBSSxJQUFJLElBQUksSUFBSW5CLENBQUMsQ0FBQ21CLElBQUksS0FBSyxFQUFFLEdBQUcsSUFBSSxHQUFHbkIsQ0FBQyxDQUFDbUIsSUFBSTtJQUNyRG1CLGNBQWMsRUFDWixPQUFPdEMsQ0FBQyxDQUFDc0MsY0FBYyxLQUFLLFFBQVEsR0FBR3RDLENBQUMsQ0FBQ3NDLGNBQWMsR0FBRyxJQUFJO0lBQ2hFSixLQUFLLEVBQUxBLEtBQUs7SUFDTGUsRUFBRSxFQUFFZCxrQkFBa0I7SUFDdEJKLE9BQU8sRUFBUEEsT0FBTztJQUNQVyxTQUFTLEVBQVRBO0VBQ0YsQ0FBQyxDQUFDO0VBRUYsSUFBSVYsZUFBZSxFQUFFO0lBSW5Ca0IsT0FBTyxDQUFDQyxLQUFLLENBQUN4QixJQUFJLENBQUNVLE9BQU8sQ0FBQztFQUM3QjtFQUVBLElBQUllLE9BQU8sRUFBRTtJQUNYLElBQU1DLE1BQU0sR0FBR3JFLE9BQU8sbUJBQW1CLENBQUMsQ0FBQ0ssT0FBTztJQUNsRGdFLE1BQU0sQ0FBQ0MsWUFBWSxDQUFBckUsTUFBQSxDQUFBMEQsTUFBQSxLQUNkaEIsSUFBSTtNQUNQNEIsZ0JBQWdCLEVBQUUsQ0FBQyxDQUFDdkQsQ0FBQyxDQUFDdUQ7SUFBZ0IsRUFDdkMsQ0FBQztFQUNKLENBQUMsTUFBTSxJQUFJeEIsT0FBTyxJQUFJL0IsQ0FBQyxDQUFDd0QsSUFBSSxLQUFLLE1BQU0sRUFBRTtJQUN2QyxJQUFNQyx1QkFBdUIsR0FDM0J6RSxPQUFPLDRCQUE0QixDQUFDLENBQUNLLE9BQU87SUFDOUMsSUFBSW9FLHVCQUF1QixFQUFFO01BQzNCLElBQUkxQixPQUFPLEVBQUU7UUFDWCxJQUFJMkIsTUFBTSxDQUFDQywyQkFBMkIsWUFBbENELE1BQU0sQ0FBQ0MsMkJBQTJCLENBQUcsQ0FBQyxFQUFFO1VBQzFDO1FBQ0Y7UUFDQUQsTUFBTSxDQUFDRSx5QkFBeUIsWUFBaENGLE1BQU0sQ0FBQ0UseUJBQXlCLENBQUcsQ0FBQztNQUN0QztNQUNBSCx1QkFBdUIsQ0FBQzNCLGVBQWUsQ0FBQ0gsSUFBSSxDQUFDO0lBQy9DO0VBQ0Y7QUFDRjtBQVdBLElBQUlrQyxrQkFBa0IsR0FBRyxLQUFLO0FBSzlCLFNBQVNDLGVBQWVBLENBQUM5RCxDQUFRLEVBQUUrQixPQUFnQixFQUFFO0VBRW5ELElBQU1DLGVBQWUsR0FBRyxJQUFJO0VBQzVCLElBQ0UsQ0FBQzBCLE1BQU0sQ0FBQ0ssa0JBQWtCLElBQzFCLENBQUNMLE1BQU0sQ0FBQ0ssa0JBQWtCLENBQUMvRCxDQUFDLEVBQUUrQixPQUFPLEVBQUVDLGVBQWUsQ0FBQyxFQUN2RDtJQUNBLElBQUltQixLQUFZO0lBQ2hCLElBQUluRCxDQUFDLFlBQVlvQixLQUFLLEVBQUU7TUFDdEIrQixLQUFLLEdBQUduRCxDQUFDO0lBQ1gsQ0FBQyxNQUFNO01BS0xtRCxLQUFLLEdBQUcsSUFBSTdELGNBQWMsQ0FBQ1UsQ0FBQyxDQUFDO0lBQy9CO0lBQ0EsSUFBSTtNQUNGNkQsa0JBQWtCLEdBQUcsSUFBSTtNQUl6Qi9CLGVBQWUsQ0FBQ3FCLEtBQUssRUFBRXBCLE9BQU8sRUFBRUMsZUFBZSxDQUFDO0lBQ2xELENBQUMsU0FBUztNQUNSNkIsa0JBQWtCLEdBQUcsS0FBSztJQUM1QjtFQUNGO0FBQ0Y7QUFJQSxTQUFTRyx3QkFBd0JBLENBQUEsRUFBVTtFQUFBLElBQUFDLFFBQUE7RUFBQSxTQUFBQyxLQUFBLEdBQUFyRCxTQUFBLENBQUFDLE1BQUEsRUFBTkMsSUFBSSxPQUFBQyxLQUFBLENBQUFrRCxLQUFBLEdBQUFDLEtBQUEsTUFBQUEsS0FBQSxHQUFBRCxLQUFBLEVBQUFDLEtBQUE7SUFBSnBELElBQUksQ0FBQW9ELEtBQUEsSUFBQXRELFNBQUEsQ0FBQXNELEtBQUE7RUFBQTtFQUV2QyxDQUFBRixRQUFBLEdBQUFmLE9BQU8sRUFBQ2tCLGNBQWMsQ0FBQS9ELEtBQUEsQ0FBQTRELFFBQUEsRUFBSWxELElBQUksQ0FBQztFQUMvQixJQUFJLENBQUNtQyxPQUFPLENBQUNtQix3QkFBd0IsRUFBRTtJQUNyQztFQUNGO0VBQ0EsSUFBSVIsa0JBQWtCLElBQUlILE1BQU0sQ0FBQ1kscUJBQXFCLFlBQTVCWixNQUFNLENBQUNZLHFCQUFxQixDQUFHLENBQUMsRUFBRTtJQTZCMUQ7RUFDRjtFQUVBLElBQUluQixLQUFLO0VBRVQsSUFBTW9CLFFBQVEsR0FBR3hELElBQUksQ0FBQyxDQUFDLENBQUM7RUFDeEIsSUFBSXdELFFBQVEsWUFBUkEsUUFBUSxDQUFFckMsS0FBSyxFQUFFO0lBRW5CaUIsS0FBSyxHQUFHb0IsUUFBUTtFQUNsQixDQUFDLE1BQU07SUFDTCxJQUFNQyxhQUFhLEdBQUd4RixPQUFPLDZCQUE2QixDQUFDLENBQUNLLE9BQU87SUFDbkUsSUFBSSxPQUFPa0YsUUFBUSxLQUFLLFFBQVEsSUFBSUEsUUFBUSxDQUFDL0IsVUFBVSxDQUFDLFdBQVcsQ0FBQyxFQUFFO01BSXBFO0lBQ0Y7SUFDQSxJQUFNSCxPQUFPLEdBQUd0QixJQUFJLENBQ2pCMEQsR0FBRyxDQUFDLFVBQUFDLEdBQUc7TUFBQSxPQUFLLE9BQU9BLEdBQUcsS0FBSyxRQUFRLEdBQUdBLEdBQUcsR0FBR0YsYUFBYSxDQUFDRSxHQUFHLENBQUM7SUFBQSxDQUFDLENBQUMsQ0FDaEVDLElBQUksQ0FBQyxHQUFHLENBQUM7SUFFWnhCLEtBQUssR0FBRyxJQUFJN0QsY0FBYyxDQUFDK0MsT0FBTyxDQUFDO0lBQ25DYyxLQUFLLENBQUNoQyxJQUFJLEdBQUcsZUFBZTtFQUM5QjtFQUVBLElBQU1ZLE9BQU8sR0FBRyxLQUFLO0VBQ3JCLElBQU1DLGVBQWUsR0FBRyxLQUFLO0VBQzdCLElBQ0UsQ0FBQzBCLE1BQU0sQ0FBQ0ssa0JBQWtCLElBQzFCLENBQUNMLE1BQU0sQ0FBQ0ssa0JBQWtCLENBQUNaLEtBQUssRUFBRXBCLE9BQU8sRUFBRUMsZUFBZSxDQUFDLEVBQzNEO0lBQ0FGLGVBQWUsQ0FJYnFCLEtBQUssRUFDTHBCLE9BQU8sRUFDUEMsZUFDRixDQUFDO0VBQ0g7QUFDRjtBQU1BLFNBQVM0QywyQkFBMkJBLENBQUEsRUFBRztFQUVyQyxJQUFJMUIsT0FBTyxDQUFDa0IsY0FBYyxFQUFFO0lBQzFCO0VBQ0Y7RUFFQWxCLE9BQU8sQ0FBQ2tCLGNBQWMsR0FBR2xCLE9BQU8sQ0FBQ0MsS0FBSyxDQUFDMEIsSUFBSSxDQUFDM0IsT0FBTyxDQUFDO0VBQ3BEQSxPQUFPLENBQUNDLEtBQUssR0FBR2Esd0JBQXdCO0VBQ3hDLElBQUlkLE9BQU8sQ0FBQ21CLHdCQUF3QixLQUFLUyxTQUFTLEVBQUU7SUFHbEQ1QixPQUFPLENBQUNtQix3QkFBd0IsR0FBRyxJQUFJO0VBQ3pDO0FBQ0Y7QUFFQSxJQUFNVSxpQkFBaUIsR0FBRztFQUN4QnhELHFCQUFxQixFQUFyQkEscUJBQXFCO0VBQ3JCdUMsZUFBZSxFQUFmQSxlQUFlO0VBQ2ZjLDJCQUEyQixFQUEzQkEsMkJBQTJCO0VBQzNCdEYsY0FBYyxFQUFkQSxjQUFjO0VBQ2RrQyw4QkFBOEIsRUFBOUJBO0FBQ0YsQ0FBQztBQUFDLElBQUF3RCxRQUFBLEdBQUE3RixPQUFBLENBQUFFLE9BQUEsR0FFYTBGLGlCQUFpQiIsImlnbm9yZUxpc3QiOltdfQ==