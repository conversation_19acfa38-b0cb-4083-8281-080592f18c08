/**
 * Developer Tools Dashboard
 *
 * Comprehensive development tools hub for debugging, performance monitoring,
 * accessibility compliance, and other development utilities.
 *
 * Features:
 * - Real-time performance metrics
 * - Bundle size analysis
 * - Memory usage monitoring
 * - Network performance tracking
 * - Core Web Vitals
 * - Performance alerts
 * - Accessibility compliance tools
 * - Development utilities
 *
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  ScrollView,
  Dimensions,
  StyleSheet,
  RefreshControl,
  ActivityIndicator,
  Pressable,
  TouchableOpacity,
} from 'react-native';

import { useTheme } from '../../contexts/ThemeContext';
import { getBundleMetrics } from '../../utils/bundleOptimization';
import { getLazyLoadingSummary } from '../../utils/lazyLoadingMetrics';
import { performanceMonitor } from '../../utils/performance';
import { Text } from '../atoms/Text';
import { Button } from '../atoms/Button';
import { BentoWidget } from '../ui/BentoGrid';
import { DashboardWidget, MetricData, ChartData } from '../ui/DashboardWidget';
import { EnhancedBentoGrid } from '../ui/EnhancedBentoGrid';
import {
  HyperMinimalistLayout,
  HyperMinimalistSection,
} from '../ui/HyperMinimalistLayout';

// Performance metric interfaces
interface PerformanceMetrics {
  renderTime: number;
  bundleSize: number;
  memoryUsage: number;
  networkLatency: number;
  cacheHitRate: number;
  errorRate: number;
}

interface CoreWebVitals {
  fcp: number; // First Contentful Paint
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  tti: number; // Time to Interactive
}

interface PerformanceAlert {
  id: string;
  type: 'warning' | 'error' | 'info';
  message: string;
  timestamp: number;
  metric: string;
  value: number;
  threshold: number;
}

// Accessibility audit interfaces
interface AccessibilityAuditResult {
  id: string;
  type: 'error' | 'warning' | 'info';
  message: string;
  element?: string;
  wcagLevel: 'A' | 'AA' | 'AAA';
  guideline: string;
  autoFixable: boolean;
}

// Dev tools interfaces
interface DevToolsState {
  debugMode: boolean;
  showPerformanceOverlay: boolean;
  enableNetworkLogging: boolean;
  showComponentBoundaries: boolean;
  enableAccessibilityHighlight: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
}

interface NetworkRequest {
  id: string;
  url: string;
  method: string;
  status: number;
  duration: number;
  timestamp: number;
  size: number;
}

interface ComponentInfo {
  name: string;
  renderCount: number;
  lastRenderTime: number;
  averageRenderTime: number;
  propsCount: number;
  stateUpdates: number;
}

export const DeveloperToolsDashboard: React.FC = () => {
  const { isDark, colors } = useTheme();

  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedWidget, setSelectedWidget] = useState<string | null>(null);
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    bundleSize: 0,
    memoryUsage: 0,
    networkLatency: 0,
    cacheHitRate: 0,
    errorRate: 0,
  });
  const [webVitals, setWebVitals] = useState<CoreWebVitals>({
    fcp: 0,
    lcp: 0,
    fid: 0,
    cls: 0,
    tti: 0,
  });
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const [selectedTab, setSelectedTab] = useState<
    'overview' | 'vitals' | 'bundle' | 'alerts' | 'accessibility' | 'devtools'
  >('overview');

  // Accessibility audit state
  const [isAuditing, setIsAuditing] = useState(false);
  const [auditResults, setAuditResults] = useState<AccessibilityAuditResult[]>([]);
  const [complianceScore, setComplianceScore] = useState(100);

  // Dev tools state
  const [devToolsState, setDevToolsState] = useState<DevToolsState>({
    debugMode: __DEV__,
    showPerformanceOverlay: false,
    enableNetworkLogging: true,
    showComponentBoundaries: false,
    enableAccessibilityHighlight: false,
    logLevel: 'info',
  });

  // Network monitoring state
  const [networkRequests, setNetworkRequests] = useState<NetworkRequest[]>([]);
  const [componentMetrics, setComponentMetrics] = useState<ComponentInfo[]>([]);

  // Bundle analysis state
  const [bundleAnalysis, setBundleAnalysis] = useState({
    totalSize: 0,
    gzippedSize: 0,
    chunks: [],
    duplicateModules: [],
    recommendations: [],
  });

  const updateInterval = useRef<NodeJS.Timeout>();

  // Accessibility audit functions
  const runAccessibilityAudit = async () => {
    setIsAuditing(true);
    try {
      // Simulate accessibility audit
      await new Promise(resolve => setTimeout(resolve, 2000));

      const mockResults: AccessibilityAuditResult[] = [
        {
          id: '1',
          type: 'warning',
          message: 'Missing alt text for image',
          element: 'Image component',
          wcagLevel: 'AA',
          guideline: '1.1.1 Non-text Content',
          autoFixable: false,
        },
        {
          id: '2',
          type: 'info',
          message: 'Color contrast ratio is acceptable',
          element: 'Text components',
          wcagLevel: 'AA',
          guideline: '1.4.3 Contrast (Minimum)',
          autoFixable: false,
        },
      ];

      setAuditResults(mockResults);
      setComplianceScore(95);
    } catch (error) {
      console.error('Accessibility audit failed:', error);
    } finally {
      setIsAuditing(false);
    }
  };

  const applyAccessibilityFixes = async () => {
    try {
      // Apply automatic fixes for fixable issues
      const fixableIssues = auditResults.filter(result => result.autoFixable);

      // Simulate applying fixes
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Remove fixed issues from results
      const remainingIssues = auditResults.filter(result => !result.autoFixable);
      setAuditResults(remainingIssues);

      // Update compliance score
      setComplianceScore(Math.min(100, complianceScore + fixableIssues.length * 2));
    } catch (error) {
      console.error('Failed to apply accessibility fixes:', error);
    }
  };

  // Dev tools functions
  const toggleDevToolsSetting = (setting: keyof DevToolsState) => {
    setDevToolsState(prev => ({
      ...prev,
      [setting]: !prev[setting],
    }));
  };

  const clearNetworkLogs = () => {
    setNetworkRequests([]);
  };

  const exportPerformanceReport = () => {
    const report = {
      timestamp: new Date().toISOString(),
      metrics,
      webVitals,
      alerts,
      accessibilityScore: complianceScore,
      auditResults,
      networkRequests: networkRequests.slice(0, 10), // Last 10 requests
      componentMetrics,
      bundleAnalysis,
    };

    console.log('Performance Report:', JSON.stringify(report, null, 2));
    // In a real app, this would download or share the report
  };

  const runBundleAnalysis = async () => {
    try {
      // Simulate bundle analysis
      await new Promise(resolve => setTimeout(resolve, 1500));

      setBundleAnalysis({
        totalSize: 2.1 * 1024 * 1024, // 2.1MB
        gzippedSize: 0.8 * 1024 * 1024, // 800KB
        chunks: [
          { name: 'main', size: 1.2 * 1024 * 1024, modules: ['App', 'Navigation'] },
          { name: 'vendor', size: 0.9 * 1024 * 1024, modules: ['react-native', 'expo'] },
        ],
        duplicateModules: ['lodash', 'moment'],
        recommendations: [
          'Consider code splitting for vendor libraries',
          'Remove duplicate lodash imports',
          'Optimize image assets',
        ],
      });
    } catch (error) {
      console.error('Bundle analysis failed:', error);
    }
  };

  // BentoGrid widgets configuration
  const performanceWidgets: BentoWidget[] = [
    {
      id: 'render-time',
      title: 'Render Time',
      size: 'small',
      priority: 10,
      component: DashboardWidget,
      data: {
        type: 'metric',
        value: formatDuration(metrics.renderTime),
        label: 'Average component render',
        trend: metrics.renderTime < 50 ? 'up' : 'down',
        color: getMetricColor(metrics.renderTime, { good: 50, warning: 100 }),
      } as MetricData,
    },
    {
      id: 'bundle-size',
      title: 'Bundle Size',
      size: 'small',
      priority: 9,
      component: DashboardWidget,
      data: {
        type: 'metric',
        value: formatBytes(metrics.bundleSize),
        label: 'Total bundle size',
        trend: 'neutral',
        color: colors.primary.default,
      } as MetricData,
    },
    {
      id: 'memory-usage',
      title: 'Memory Usage',
      size: 'small',
      priority: 8,
      component: DashboardWidget,
      data: {
        type: 'metric',
        value: `${metrics.memoryUsage.toFixed(1)}%`,
        label: 'JS heap usage',
        trend: metrics.memoryUsage < 50 ? 'up' : 'down',
        color: getMetricColor(metrics.memoryUsage, { good: 50, warning: 80 }),
      } as MetricData,
    },
    {
      id: 'core-web-vitals',
      title: 'Core Web Vitals',
      size: 'large',
      priority: 5,
      component: DashboardWidget,
      data: {
        type: 'chart',
        chartType: 'bar',
        values: [
          { label: 'FCP', value: webVitals.fcp },
          { label: 'LCP', value: webVitals.lcp },
          { label: 'FID', value: webVitals.fid },
          { label: 'CLS', value: webVitals.cls * 1000 },
          { label: 'TTI', value: webVitals.tti },
        ],
        color: colors.primary.default,
      } as ChartData,
    },
    {
      id: 'accessibility-compliance',
      title: 'Accessibility Compliance',
      size: 'medium',
      priority: 7,
      component: DashboardWidget,
      data: {
        type: 'metric',
        value: `${complianceScore}%`,
        label: 'WCAG AA Compliance',
        trend: complianceScore >= 95 ? 'up' : complianceScore >= 80 ? 'neutral' : 'down',
        color: getMetricColor(complianceScore, { good: 95, warning: 80 }),
      } as MetricData,
    },
    {
      id: 'dev-tools',
      title: 'Developer Tools',
      size: 'medium',
      priority: 6,
      component: DashboardWidget,
      data: {
        type: 'metric',
        value: `${auditResults.length}`,
        label: 'Issues Found',
        trend: auditResults.length === 0 ? 'up' : 'down',
        color: auditResults.length === 0 ? colors.success || '#10B981' : colors.warning || '#F59E0B',
      } as MetricData,
    },
    {
      id: 'network-monitor',
      title: 'Network Monitor',
      size: 'medium',
      priority: 7,
      component: DashboardWidget,
      data: {
        type: 'metric',
        value: `${networkRequests.length}`,
        label: 'Active Requests',
        trend: networkRequests.length > 5 ? 'up' : 'stable',
        color: colors.sage400,
      } as MetricData,
    },
    {
      id: 'bundle-analysis',
      title: 'Bundle Analysis',
      size: 'medium',
      priority: 8,
      component: DashboardWidget,
      data: {
        type: 'metric',
        value: bundleAnalysis.totalSize > 0 ? `${(bundleAnalysis.totalSize / 1024 / 1024).toFixed(1)}MB` : 'Not analyzed',
        label: 'Bundle Size',
        trend: bundleAnalysis.recommendations.length > 0 ? 'down' : 'stable',
        color: colors.sage500,
      } as MetricData,
    },
    {
      id: 'dev-tools',
      title: 'Dev Tools',
      size: 'large',
      priority: 9,
      component: DashboardWidget,
      data: {
        type: 'metric',
        value: devToolsState.debugMode ? 'Debug ON' : 'Debug OFF',
        label: 'Debug Mode',
        trend: 'stable',
        color: colors.sage600,
      } as MetricData,
    },
  ];

  // Widget press handler
  const handleWidgetPress = (widget: BentoWidget) => {
    setSelectedWidget(widget.id === selectedWidget ? null : widget.id);

    switch (widget.id) {
      case 'render-time':
        setSelectedTab('overview');
        break;
      case 'core-web-vitals':
        setSelectedTab('vitals');
        break;
      case 'accessibility-compliance':
        setSelectedTab('accessibility');
        break;
      case 'dev-tools':
        setSelectedTab('devtools');
        break;
      case 'bundle-size':
        setSelectedTab('bundle');
        break;
      default:
        setSelectedTab('overview');
    }
  };

  // Load performance data
  const loadPerformanceData = async () => {
    try {
      // Get performance metrics safely
      const perfMetrics = performanceMonitor.getMetrics();
      const bundleMetrics = getBundleMetrics();
      const lazyLoadingMetrics = getLazyLoadingSummary();

      // Calculate derived metrics safely
      const avgRenderTime =
        perfMetrics && perfMetrics.length > 0
          ? perfMetrics.reduce((sum, m) => sum + (m.value || 0), 0) /
            perfMetrics.length
          : Math.random() * 20 + 10; // Simulated value for development

      const bundleSize = bundleMetrics?.registry?.totalSize || 1024 * 1024; // 1MB default
      const memoryUsage = getMemoryUsage();
      const cacheHitRate = lazyLoadingMetrics?.cacheHitRate || 85 + Math.random() * 10;
      const errorRate = lazyLoadingMetrics?.errorRate || Math.random() * 2;

      setMetrics({
        renderTime: avgRenderTime,
        bundleSize,
        memoryUsage,
        networkLatency: 50 + Math.random() * 100, // Simulated network latency
        cacheHitRate,
        errorRate,
      });

      // Simulate Core Web Vitals (in real app, these would come from actual measurements)
      setWebVitals({
        fcp: 1200 + Math.random() * 300,
        lcp: 2100 + Math.random() * 500,
        fid: 50 + Math.random() * 50,
        cls: 0.05 + Math.random() * 0.1,
        tti: 3200 + Math.random() * 800,
      });

      // Check for performance alerts
      checkPerformanceAlerts();
    } catch (error) {
      console.error('[DeveloperToolsDashboard] Failed to load performance data:', error);
      // Set fallback metrics to prevent crashes
      setMetrics({
        renderTime: 15,
        bundleSize: 1024 * 1024,
        memoryUsage: 45,
        networkLatency: 100,
        cacheHitRate: 85,
        errorRate: 1,
      });
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  // Get memory usage
  const getMemoryUsage = (): number => {
    if (
      typeof window !== 'undefined' &&
      'performance' in window &&
      'memory' in window.performance
    ) {
      const memory = (window.performance as any).memory;
      return (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100;
    }
    return 0;
  };

  // Check for performance alerts
  const checkPerformanceAlerts = () => {
    const newAlerts: PerformanceAlert[] = [];
    const timestamp = Date.now();

    // Check render time
    if (metrics.renderTime > 100) {
      newAlerts.push({
        id: `render-time-${timestamp}`,
        type: 'warning',
        message: 'High render time detected',
        timestamp,
        metric: 'renderTime',
        value: metrics.renderTime,
        threshold: 100,
      });
    }

    // Check bundle size
    if (metrics.bundleSize > 2 * 1024 * 1024) {
      // 2MB
      newAlerts.push({
        id: `bundle-size-${timestamp}`,
        type: 'error',
        message: 'Bundle size exceeds recommended limit',
        timestamp,
        metric: 'bundleSize',
        value: metrics.bundleSize,
        threshold: 2 * 1024 * 1024,
      });
    }

    // Check memory usage
    if (metrics.memoryUsage > 80) {
      newAlerts.push({
        id: `memory-usage-${timestamp}`,
        type: 'warning',
        message: 'High memory usage detected',
        timestamp,
        metric: 'memoryUsage',
        value: metrics.memoryUsage,
        threshold: 80,
      });
    }

    // Check error rate
    if (metrics.errorRate > 5) {
      newAlerts.push({
        id: `error-rate-${timestamp}`,
        type: 'error',
        message: 'High error rate detected',
        timestamp,
        metric: 'errorRate',
        value: metrics.errorRate,
        threshold: 5,
      });
    }

    setAlerts(prev => [...newAlerts, ...prev.slice(0, 10)]); // Keep last 10 alerts
  };

  // Handle refresh
  const handleRefresh = () => {
    setRefreshing(true);
    loadPerformanceData();
  };

  // Initialize monitoring
  useEffect(() => {
    // Initial load
    loadPerformanceData();

    // Set up periodic updates with longer interval to prevent performance issues
    updateInterval.current = setInterval(() => {
      // Only update if component is still mounted and not currently loading
      if (!isLoading) {
        loadPerformanceData();
      }
    }, 60000); // Every 60 seconds (reduced frequency)

    return () => {
      if (updateInterval.current) {
        clearInterval(updateInterval.current);
        updateInterval.current = null;
      }
    };
  }, []); // Empty dependency array to run only once

  // Format bytes
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format duration
  const formatDuration = (ms: number): string => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  // Get metric color based on value
  const getMetricColor = (
    value: number,
    thresholds: { good: number; warning: number },
  ): string => {
    if (value <= thresholds.good) return colors.semantic.success;
    if (value <= thresholds.warning) return colors.semantic.warning;
    return colors.semantic.error;
  };

  // Render metric card
  const renderMetricCard = (
    title: string,
    value: string,
    subtitle?: string,
    color?: string,
  ) => (
    <View
      style={[
        styles.metricCard,
        { backgroundColor: colors.white, borderColor: colors.gray[200] },
      ]}>
      <Text variant="caption" color="secondary" style={styles.metricTitle}>
        {title}
      </Text>
      <Text variant="heading" style={[styles.metricValue, color && { color }]}>
        {value}
      </Text>
      {subtitle && (
        <Text variant="caption" color="tertiary" style={styles.metricSubtitle}>
          {subtitle}
        </Text>
      )}
    </View>
  );

  // Render tab navigation
  const renderTabNavigation = () => (
    <View style={[styles.tabNavigation, { backgroundColor: colors.gray[50] }]}>
      {(['overview', 'vitals', 'bundle', 'alerts', 'accessibility', 'devtools'] as const).map(tab => (
        <Pressable
          key={tab}
          style={[
            styles.tabButton,
            selectedTab === tab && { backgroundColor: colors.accent[400] },
          ]}
          onPress={() => setSelectedTab(tab)}>
          <Text
            variant="body"
            color={selectedTab === tab ? 'inverse' : 'secondary'}
            weight="medium">
            {tab === 'devtools' ? 'Dev Tools' : tab.charAt(0).toUpperCase() + tab.slice(1)}
          </Text>
        </Pressable>
      ))}
    </View>
  );

  if (isLoading) {
    return (
      <HyperMinimalistLayout>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.accent[400]} />
          <Text variant="body" color="secondary" style={styles.loadingText}>
            Loading performance data...
          </Text>
        </View>
      </HyperMinimalistLayout>
    );
  }

  return (
    <HyperMinimalistLayout scrollable>
      <ScrollView
        style={styles.container}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }>
        <HyperMinimalistSection spacing="comfortable">
          <HeadingText weight="semibold" color="primary">
            Performance Monitor
          </HeadingText>
          <Text variant="body" color="secondary">
            Real-time application performance insights
          </Text>
        </HyperMinimalistSection>

        {/* Enhanced Bento Grid Dashboard */}
        <HyperMinimalistSection spacing="generous">
          <SubheadingText weight="medium" color="primary">
            Performance Dashboard
          </SubheadingText>
          <EnhancedBentoGrid
            widgets={performanceWidgets}
            onWidgetPress={handleWidgetPress}
            selectedWidgetId={selectedWidget}
            config={{
              columns: 2,
              spacing: 24,
              minWidgetHeight: 140,
              maxWidgetHeight: 280,
              enableAnimations: true,
              enableLazyLoading: true,
              priorityThreshold: 5,
            }}
            testID="performance-dashboard-grid"
          />
        </HyperMinimalistSection>

        {renderTabNavigation()}

        {selectedTab === 'vitals' && (
          <HyperMinimalistSection spacing="comfortable">
            <SubheadingText weight="medium" color="primary">
              Core Web Vitals
            </SubheadingText>
            <View style={styles.metricsGrid}>
              {renderMetricCard(
                'First Contentful Paint',
                formatDuration(webVitals.fcp),
                'Time to first content',
                getMetricColor(webVitals.fcp, { good: 1800, warning: 3000 }),
              )}
              {renderMetricCard(
                'Largest Contentful Paint',
                formatDuration(webVitals.lcp),
                'Time to main content',
                getMetricColor(webVitals.lcp, { good: 2500, warning: 4000 }),
              )}
              {renderMetricCard(
                'First Input Delay',
                formatDuration(webVitals.fid),
                'Input responsiveness',
                getMetricColor(webVitals.fid, { good: 100, warning: 300 }),
              )}
              {renderMetricCard(
                'Cumulative Layout Shift',
                webVitals.cls.toFixed(3),
                'Visual stability',
                getMetricColor(webVitals.cls * 1000, {
                  good: 100,
                  warning: 250,
                }),
              )}
            </View>
          </HyperMinimalistSection>
        )}

        {selectedTab === 'alerts' && (
          <HyperMinimalistSection spacing="comfortable">
            <SubheadingText weight="medium" color="primary">
              Performance Alerts
            </SubheadingText>
            {alerts.length === 0 ? (
              <View style={styles.emptyState}>
                <Text variant="body" color="secondary">
                  No performance alerts
                </Text>
              </View>
            ) : (
              <View style={styles.alertsList}>
                {alerts.map(alert => (
                  <View
                    key={alert.id}
                    style={[
                      styles.alertCard,
                      {
                        backgroundColor: colors.white,
                        borderLeftColor:
                          alert.type === 'error'
                            ? colors.semantic.error
                            : colors.semantic.warning,
                      },
                    ]}>
                    <Text variant="body" weight="medium" color="primary">
                      {alert.message}
                    </Text>
                    <Text variant="caption" color="secondary">
                      {alert.metric}: {alert.value.toFixed(2)} (threshold:{' '}
                      {alert.threshold})
                    </Text>
                    <Text variant="micro" color="tertiary">
                      {new Date(alert.timestamp).toLocaleTimeString()}
                    </Text>
                  </View>
                ))}
              </View>
            )}
          </HyperMinimalistSection>
        )}

        {selectedTab === 'accessibility' && (
          <HyperMinimalistSection spacing="comfortable">
            <SubheadingText weight="medium" color="primary">
              Accessibility Compliance
            </SubheadingText>

            <View style={styles.accessibilityControls}>
              <Button
                onPress={runAccessibilityAudit}
                variant="primary"
                disabled={isAuditing}
                style={styles.auditButton}>
                {isAuditing ? 'Running Audit...' : 'Run Audit'}
              </Button>

              <Button
                onPress={applyAccessibilityFixes}
                variant="secondary"
                disabled={auditResults.length === 0}
                style={styles.fixButton}>
                Apply Fixes
              </Button>
            </View>

            <View style={styles.complianceScore}>
              <Text variant="heading" color="primary">
                {complianceScore}% WCAG AA
              </Text>
              <Text variant="body" color="secondary">
                Compliance Score
              </Text>
            </View>

            {auditResults.length > 0 && (
              <View style={styles.auditResults}>
                <Text variant="subheading" color="primary" style={{ marginBottom: 12 }}>
                  Audit Results
                </Text>
                {auditResults.map(result => (
                  <View
                    key={result.id}
                    style={[
                      styles.auditResultCard,
                      {
                        backgroundColor: colors.white,
                        borderLeftColor:
                          result.type === 'error'
                            ? colors.semantic?.error || '#EF4444'
                            : result.type === 'warning'
                            ? colors.semantic?.warning || '#F59E0B'
                            : colors.semantic?.info || '#3B82F6',
                      },
                    ]}>
                    <Text variant="body" weight="medium" color="primary">
                      {result.message}
                    </Text>
                    <Text variant="caption" color="secondary">
                      {result.guideline} • {result.wcagLevel}
                    </Text>
                    {result.element && (
                      <Text variant="micro" color="tertiary">
                        Element: {result.element}
                      </Text>
                    )}
                  </View>
                ))}
              </View>
            )}
          </HyperMinimalistSection>
        )}

        {selectedTab === 'devtools' && (
          <HyperMinimalistSection spacing="comfortable">
            <SubheadingText weight="medium" color="primary">
              Developer Tools
            </SubheadingText>

            <View style={styles.devToolsGrid}>
              <TouchableOpacity
                style={[styles.devToolCard, { backgroundColor: colors.white }]}
                onPress={() => setSelectedTab('accessibility')}>
                <Text variant="body" weight="medium" color="primary">
                  Accessibility Tools
                </Text>
                <Text variant="caption" color="secondary">
                  WCAG compliance and audit tools
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.devToolCard, { backgroundColor: colors.white }]}
                onPress={() => setSelectedTab('vitals')}>
                <Text variant="body" weight="medium" color="primary">
                  Performance Monitor
                </Text>
                <Text variant="caption" color="secondary">
                  Core Web Vitals and metrics
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.devToolCard, { backgroundColor: colors.white }]}
                onPress={() => setSelectedTab('bundle')}>
                <Text variant="body" weight="medium" color="primary">
                  Bundle Analyzer
                </Text>
                <Text variant="caption" color="secondary">
                  Bundle size and optimization
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.devToolCard, { backgroundColor: colors.white }]}
                onPress={() => console.log('Theme debugger')}>
                <Text variant="body" weight="medium" color="primary">
                  Theme Debugger
                </Text>
                <Text variant="caption" color="secondary">
                  Debug theme and styling issues
                </Text>
              </TouchableOpacity>
            </View>

            {/* Debug Settings */}
            <View style={styles.devToolsSection}>
              <Text variant="body" weight="medium" color="primary" style={styles.sectionTitle}>
                Debug Settings
              </Text>

              <View style={styles.settingsGrid}>
                <TouchableOpacity
                  style={[styles.settingCard, { backgroundColor: colors.white }]}
                  onPress={() => toggleDevToolsSetting('debugMode')}>
                  <Text variant="body" weight="medium" color="primary">
                    Debug Mode: {devToolsState.debugMode ? 'ON' : 'OFF'}
                  </Text>
                  <Text variant="caption" color="secondary">
                    Enable detailed logging and debug info
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.settingCard, { backgroundColor: colors.white }]}
                  onPress={() => toggleDevToolsSetting('showPerformanceOverlay')}>
                  <Text variant="body" weight="medium" color="primary">
                    Performance Overlay: {devToolsState.showPerformanceOverlay ? 'ON' : 'OFF'}
                  </Text>
                  <Text variant="caption" color="secondary">
                    Show real-time performance metrics
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.settingCard, { backgroundColor: colors.white }]}
                  onPress={() => toggleDevToolsSetting('enableNetworkLogging')}>
                  <Text variant="body" weight="medium" color="primary">
                    Network Logging: {devToolsState.enableNetworkLogging ? 'ON' : 'OFF'}
                  </Text>
                  <Text variant="caption" color="secondary">
                    Log all network requests and responses
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Quick Actions */}
            <View style={styles.quickActionsSection}>
              <Text variant="body" weight="medium" color="primary" style={styles.sectionTitle}>
                Quick Actions
              </Text>

              <View style={styles.actionButtonsGrid}>
                <Button
                  variant="outline"
                  size="small"
                  onPress={exportPerformanceReport}
                  style={styles.actionButton}>
                  Export Report
                </Button>

                <Button
                  variant="outline"
                  size="small"
                  onPress={clearNetworkLogs}
                  style={styles.actionButton}>
                  Clear Logs
                </Button>

                <Button
                  variant="outline"
                  size="small"
                  onPress={runBundleAnalysis}
                  style={styles.actionButton}>
                  Analyze Bundle
                </Button>
              </View>
            </View>
          </HyperMinimalistSection>
        )}
      </ScrollView>
    </HyperMinimalistLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },

  loadingText: {
    marginTop: 16,
    textAlign: 'center',
  },

  tabNavigation: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 4,
  },

  tabButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },

  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -8,
  },

  metricCard: {
    width: '48%',
    marginHorizontal: '1%',
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },

  metricTitle: {
    marginBottom: 4,
  },

  metricValue: {
    marginBottom: 4,
  },

  metricSubtitle: {
    opacity: 0.7,
  },

  emptyState: {
    padding: 24,
    alignItems: 'center',
  },

  alertsList: {
    gap: 12,
  },

  alertCard: {
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },

  // Accessibility styles
  accessibilityControls: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 20,
  },

  auditButton: {
    flex: 1,
  },

  fixButton: {
    flex: 1,
  },

  complianceScore: {
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    marginBottom: 20,
  },

  auditResults: {
    gap: 12,
  },

  auditResultCard: {
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    marginBottom: 8,
  },

  // Dev tools styles
  devToolsGrid: {
    gap: 12,
  },

  devToolCard: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },

  devToolsSection: {
    marginTop: 24,
    marginBottom: 16,
  },

  sectionTitle: {
    marginBottom: 12,
    fontSize: 16,
    fontWeight: '600',
  },

  settingsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },

  settingCard: {
    flex: 1,
    minWidth: 150,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },

  quickActionsSection: {
    marginTop: 24,
  },

  actionButtonsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },

  actionButton: {
    minWidth: 100,
  },
});

export default DeveloperToolsDashboard;
