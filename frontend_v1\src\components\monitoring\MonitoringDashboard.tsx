/**
 * Monitoring Dashboard Component
 * Real-time monitoring dashboard for performance, analytics, and error tracking
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Ionicons } from '@expo/vector-icons';
import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
  Alert,
} from 'react-native';

import { useTheme } from '../../contexts/ThemeContext';
import { analyticsService } from '../../services/analyticsService';
import { performanceMonitor } from '../../services/performanceMonitor';
import { logger } from '../../utils/logger';

interface DashboardMetrics {
  performance: {
    appStartTime: number;
    averageRenderTime: number;
    memoryUsage: number;
    errorRate: number;
    crashRate: number;
  };
  analytics: {
    activeUsers: number;
    sessionDuration: number;
    screenViews: number;
    conversionRate: number;
  };
  errors: {
    totalErrors: number;
    criticalErrors: number;
    resolvedErrors: number;
    errorTrends: Array<{ date: string; count: number }>;
  };
}

export const MonitoringDashboard: React.FC = () => {
  const { colors } = useTheme();
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTab, setSelectedTab] = useState<
    'performance' | 'analytics' | 'errors'
  >('performance');

  // Load dashboard metrics
  const loadMetrics = async () => {
    try {
      setLoading(true);

      // Get metrics from services
      const performanceReport = performanceMonitor.getPerformanceReport();
      const analyticsSummary = await analyticsService.getAnalyticsSummary();

      // Combine metrics
      const dashboardMetrics: DashboardMetrics = {
        performance: {
          appStartTime: performanceReport.appStartTime || 0,
          averageRenderTime: performanceReport.averageRenderTime || 0,
          memoryUsage: performanceReport.memoryUsage || 0,
          errorRate: performanceReport.errorRate || 0,
          crashRate: 0, // Would need to be calculated from error metrics
        },
        analytics: {
          activeUsers: analyticsSummary.currentSession ? 1 : 0,
          sessionDuration: analyticsSummary.currentSession?.duration || 0,
          screenViews: analyticsSummary.currentSession?.screenViews || 0,
          conversionRate: 0, // Would need to be calculated from business events
        },
        errors: {
          totalErrors: performanceReport.totalErrors || 0,
          criticalErrors: 0, // Would need to be calculated from error severity
          resolvedErrors: 0, // Would need to be tracked separately
          errorTrends: [], // Would need to be calculated from error history
        },
      };

      setMetrics(dashboardMetrics);
      logger.info('📊 Dashboard metrics loaded', dashboardMetrics);
    } catch (error) {
      logger.error('❌ Failed to load dashboard metrics', error);
      Alert.alert('Error', 'Failed to load monitoring data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Refresh metrics
  const handleRefresh = () => {
    setRefreshing(true);
    loadMetrics();
  };

  // Load metrics on mount
  useEffect(() => {
    loadMetrics();

    // Set up auto-refresh every 30 seconds
    const interval = setInterval(loadMetrics, 30000);
    return () => clearInterval(interval);
  }, []);

  // Render metric card
  const renderMetricCard = (
    title: string,
    value: string | number,
    icon: string,
    color: string,
    subtitle?: string,
  ) => (
    <View style={[styles.metricCard, { backgroundColor: colors.surface }]}>
      <View style={styles.metricHeader}>
        <Ionicons name={icon as any} size={24} color={color} />
        <Text style={[styles.metricTitle, { color: colors.text }]}>
          {title}
        </Text>
      </View>
      <Text style={[styles.metricValue, { color: colors.primary }]}>
        {value}
      </Text>
      {subtitle && (
        <Text style={[styles.metricSubtitle, { color: colors.textSecondary }]}>
          {subtitle}
        </Text>
      )}
    </View>
  );

  // Render performance tab
  const renderPerformanceTab = () => {
    if (!metrics) return null;

    return (
      <View style={styles.tabContent}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Performance Metrics
        </Text>

        <View style={styles.metricsGrid}>
          {renderMetricCard(
            'App Start Time',
            `${metrics.performance.appStartTime}ms`,
            'rocket-outline',
            colors.success,
            'Time to interactive',
          )}

          {renderMetricCard(
            'Render Time',
            `${metrics.performance.averageRenderTime.toFixed(1)}ms`,
            'speedometer-outline',
            colors.warning,
            'Average render time',
          )}

          {renderMetricCard(
            'Memory Usage',
            `${(metrics.performance.memoryUsage / 1024 / 1024).toFixed(1)}MB`,
            'hardware-chip-outline',
            colors.info,
            'Current memory usage',
          )}

          {renderMetricCard(
            'Error Rate',
            `${(metrics.performance.errorRate * 100).toFixed(2)}%`,
            'warning-outline',
            metrics.performance.errorRate > 0.05
              ? colors.error
              : colors.success,
            'Error occurrence rate',
          )}
        </View>

        <View style={styles.performanceIndicators}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Performance Health
          </Text>

          <View style={styles.healthIndicator}>
            <Text style={[styles.healthLabel, { color: colors.text }]}>
              Overall Performance
            </Text>
            <View
              style={[styles.healthBar, { backgroundColor: colors.border }]}>
              <View
                style={[
                  styles.healthBarFill,
                  {
                    backgroundColor: getPerformanceColor(metrics.performance),
                    width: `${getPerformanceScore(metrics.performance)}%`,
                  },
                ]}
              />
            </View>
            <Text style={[styles.healthScore, { color: colors.text }]}>
              {getPerformanceScore(metrics.performance)}%
            </Text>
          </View>
        </View>
      </View>
    );
  };

  // Render analytics tab
  const renderAnalyticsTab = () => {
    if (!metrics) return null;

    return (
      <View style={styles.tabContent}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Analytics Overview
        </Text>

        <View style={styles.metricsGrid}>
          {renderMetricCard(
            'Active Users',
            metrics.analytics.activeUsers.toString(),
            'people-outline',
            colors.primary,
            'Current session',
          )}

          {renderMetricCard(
            'Session Duration',
            `${Math.round(metrics.analytics.sessionDuration / 60)}min`,
            'time-outline',
            colors.info,
            'Average session time',
          )}

          {renderMetricCard(
            'Screen Views',
            metrics.analytics.screenViews.toString(),
            'eye-outline',
            colors.success,
            'Total page views',
          )}

          {renderMetricCard(
            'Conversion Rate',
            `${(metrics.analytics.conversionRate * 100).toFixed(1)}%`,
            'trending-up-outline',
            colors.warning,
            'User conversion rate',
          )}
        </View>
      </View>
    );
  };

  // Render errors tab
  const renderErrorsTab = () => {
    if (!metrics) return null;

    return (
      <View style={styles.tabContent}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Error Monitoring
        </Text>

        <View style={styles.metricsGrid}>
          {renderMetricCard(
            'Total Errors',
            metrics.errors.totalErrors.toString(),
            'bug-outline',
            colors.error,
            'All time errors',
          )}

          {renderMetricCard(
            'Critical Errors',
            metrics.errors.criticalErrors.toString(),
            'alert-circle-outline',
            colors.error,
            'High priority issues',
          )}

          {renderMetricCard(
            'Resolved Errors',
            metrics.errors.resolvedErrors.toString(),
            'checkmark-circle-outline',
            colors.success,
            'Fixed issues',
          )}

          {renderMetricCard(
            'Resolution Rate',
            `${((metrics.errors.resolvedErrors / Math.max(metrics.errors.totalErrors, 1)) * 100).toFixed(1)}%`,
            'analytics-outline',
            colors.info,
            'Error resolution rate',
          )}
        </View>
      </View>
    );
  };

  // Get performance score
  const getPerformanceScore = (
    performance: DashboardMetrics['performance'],
  ): number => {
    let score = 100;

    // Deduct points for poor performance
    if (performance.appStartTime > 3000) score -= 20;
    if (performance.averageRenderTime > 16) score -= 15;
    if (performance.memoryUsage > 100 * 1024 * 1024) score -= 15;
    if (performance.errorRate > 0.05) score -= 25;
    if (performance.crashRate > 0.01) score -= 25;

    return Math.max(0, score);
  };

  // Get performance color
  const getPerformanceColor = (
    performance: DashboardMetrics['performance'],
  ): string => {
    const score = getPerformanceScore(performance);
    if (score >= 80) return colors.success;
    if (score >= 60) return colors.warning;
    return colors.error;
  };

  if (loading && !metrics) {
    return (
      <View
        style={[
          styles.container,
          styles.centered,
          { backgroundColor: colors.background },
        ]}>
        <Ionicons name="analytics-outline" size={48} color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.text }]}>
          Loading monitoring data...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          Monitoring Dashboard
        </Text>
        <TouchableOpacity onPress={handleRefresh} disabled={refreshing}>
          <Ionicons
            name="refresh-outline"
            size={24}
            color={refreshing ? colors.textSecondary : colors.primary}
          />
        </TouchableOpacity>
      </View>

      {/* Tab Navigation */}
      <View style={[styles.tabNavigation, { backgroundColor: colors.surface }]}>
        {(['performance', 'analytics', 'errors'] as const).map(tab => (
          <TouchableOpacity
            key={tab}
            style={[
              styles.tabButton,
              selectedTab === tab && { backgroundColor: colors.primary?.default || '#5A7A63' },
            ]}
            onPress={() => setSelectedTab(tab)}>
            <Text
              style={[
                styles.tabButtonText,
                { color: selectedTab === tab ? colors.surface : colors.text },
              ]}>
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Content */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }>
        {selectedTab === 'performance' && renderPerformanceTab()}
        {selectedTab === 'analytics' && renderAnalyticsTab()}
        {selectedTab === 'errors' && renderErrorsTab()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  tabNavigation: {
    flexDirection: 'row',
    padding: 8,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginHorizontal: 4,
    borderRadius: 8,
    alignItems: 'center',
  },
  tabButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  tabContent: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  metricCard: {
    width: '48%',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  metricTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  metricSubtitle: {
    fontSize: 12,
  },
  performanceIndicators: {
    marginTop: 24,
  },
  healthIndicator: {
    marginBottom: 16,
  },
  healthLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  healthBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: 4,
  },
  healthBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  healthScore: {
    fontSize: 12,
    textAlign: 'right',
  },
});

export default MonitoringDashboard;
