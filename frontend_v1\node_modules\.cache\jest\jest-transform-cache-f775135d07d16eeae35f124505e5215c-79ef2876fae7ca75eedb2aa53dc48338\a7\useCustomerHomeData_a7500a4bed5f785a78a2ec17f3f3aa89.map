{"version": 3, "names": ["_react", "require", "_cacheService", "_interopRequireDefault", "_customerService", "_performanceMonitor", "_authSlice", "initialData", "categories", "featuredProviders", "favoriteProviders", "nearbyProviders", "dashboard", "recommendations", "recentBookings", "unreadNotifications", "initialLoading", "overall", "initialError", "useCustomerHomeData", "exports", "_useAuthStore", "useAuthStore", "isAuthenticated", "user", "_useState", "useState", "data", "loading", "error", "lastUpdated", "refreshing", "_useState2", "_slicedToArray2", "default", "state", "setState", "abortControllerRef", "useRef", "cacheRef", "CACHE_DURATION", "isCache<PERSON><PERSON>d", "useCallback", "key", "cached", "current", "Date", "now", "timestamp", "getCachedData", "setCache", "setLoading", "value", "prev", "Object", "assign", "_defineProperty2", "values", "some", "Boolean", "setError", "setData", "fetchCategories", "_asyncToGenerator2", "cache<PERSON>ey", "startTime", "cachedData", "cacheService", "get", "performanceMonitor", "trackNetworkRequest", "customerService", "getServiceCategories", "set", "console", "message", "status", "fetchFeaturedProviders", "providers", "getFeaturedProviders", "fetchFavoriteProviders", "getFavoriteProviders", "fetchNearbyProviders", "defaultLat", "defaultLng", "getNearbyProviders", "fetchDashboard", "getCustomerDashboard", "fetchRecommendations", "getPersonalizedRecommendations", "loadAllData", "abort", "AbortController", "Promise", "allSettled", "refresh", "useEffect", "loadData", "_ref9", "apply", "arguments"], "sources": ["useCustomerHomeData.ts"], "sourcesContent": ["/**\n * Customer Home Data Hook - Centralized data management for home screen\n *\n * Hook Contract:\n * - Manages all data fetching for customer home screen\n * - Provides loading states and error handling\n * - Implements caching and refresh functionality\n * - Supports offline mode with fallback data\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport { useState, useEffect, useCallback, useRef } from 'react';\n\nimport cacheService from '../services/cacheService';\nimport customerService, {\n  ServiceCategory,\n  FeaturedProvider,\n  NearbyProvider,\n  CustomerDashboard,\n} from '../services/customerService';\nimport { performanceMonitor } from '../services/performanceMonitor';\nimport { useAuthStore } from '../store/authSlice';\n\ninterface HomeScreenData {\n  categories: ServiceCategory[];\n  featuredProviders: FeaturedProvider[];\n  favoriteProviders: FeaturedProvider[];\n  nearbyProviders: NearbyProvider[];\n  dashboard: CustomerDashboard | null;\n  recommendations: FeaturedProvider[];\n  recentBookings: any[];\n  unreadNotifications: number;\n}\n\ninterface HomeScreenState {\n  data: HomeScreenData;\n  loading: {\n    categories: boolean;\n    featuredProviders: boolean;\n    favoriteProviders: boolean;\n    nearbyProviders: boolean;\n    dashboard: boolean;\n    recommendations: boolean;\n    recentBookings: boolean;\n    overall: boolean;\n  };\n  error: {\n    categories: string | null;\n    featuredProviders: string | null;\n    favoriteProviders: string | null;\n    nearbyProviders: string | null;\n    dashboard: string | null;\n    recentBookings: string | null;\n    recommendations: string | null;\n    overall: string | null;\n  };\n  lastUpdated: Date | null;\n  refreshing: boolean;\n}\n\nconst initialData: HomeScreenData = {\n  categories: [],\n  featuredProviders: [],\n  favoriteProviders: [],\n  nearbyProviders: [],\n  dashboard: null,\n  recommendations: [],\n  recentBookings: [],\n  unreadNotifications: 0,\n};\n\nconst initialLoading = {\n  categories: false,\n  featuredProviders: false,\n  favoriteProviders: false,\n  nearbyProviders: false,\n  dashboard: false,\n  recommendations: false,\n  recentBookings: false,\n  overall: false,\n};\n\nconst initialError = {\n  categories: null,\n  featuredProviders: null,\n  favoriteProviders: null,\n  nearbyProviders: null,\n  dashboard: null,\n  recommendations: null,\n  recentBookings: null,\n  overall: null,\n};\n\nexport const useCustomerHomeData = () => {\n  const { isAuthenticated, user } = useAuthStore();\n  const [state, setState] = useState<HomeScreenState>({\n    data: initialData,\n    loading: initialLoading,\n    error: initialError,\n    lastUpdated: null,\n    refreshing: false,\n  });\n\n  const abortControllerRef = useRef<AbortController | null>(null);\n  const cacheRef = useRef<{ [key: string]: { data: any; timestamp: number } }>(\n    {},\n  );\n  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes\n\n  // Helper function to check cache validity\n  const isCacheValid = useCallback((key: string): boolean => {\n    const cached = cacheRef.current[key];\n    if (!cached) return false;\n    return Date.now() - cached.timestamp < CACHE_DURATION;\n  }, []);\n\n  // Helper function to get cached data\n  const getCachedData = useCallback((key: string) => {\n    const cached = cacheRef.current[key];\n    return cached ? cached.data : null;\n  }, []);\n\n  // Helper function to set cache\n  const setCache = useCallback((key: string, data: any) => {\n    cacheRef.current[key] = {\n      data,\n      timestamp: Date.now(),\n    };\n  }, []);\n\n  // Update loading state for specific data type\n  const setLoading = useCallback(\n    (key: keyof typeof initialLoading, value: boolean) => {\n      setState(prev => ({\n        ...prev,\n        loading: {\n          ...prev.loading,\n          [key]: value,\n          overall:\n            value ||\n            Object.values({ ...prev.loading, [key]: value }).some(Boolean),\n        },\n      }));\n    },\n    [],\n  );\n\n  // Update error state for specific data type\n  const setError = useCallback(\n    (key: keyof typeof initialError, value: string | null) => {\n      setState(prev => ({\n        ...prev,\n        error: {\n          ...prev.error,\n          [key]: value,\n        },\n      }));\n    },\n    [],\n  );\n\n  // Update data for specific type\n  const setData = useCallback((key: keyof HomeScreenData, value: any) => {\n    setState(prev => ({\n      ...prev,\n      data: {\n        ...prev.data,\n        [key]: value,\n      },\n      lastUpdated: new Date(),\n    }));\n  }, []);\n\n  // Fetch service categories with advanced caching and performance monitoring\n  const fetchCategories = useCallback(async () => {\n    const cacheKey = 'customer_home_categories';\n    const startTime = Date.now();\n\n    setLoading('categories', true);\n    setError('categories', null);\n\n    try {\n      // Try to get from cache first\n      const cachedData = await cacheService.get<ServiceCategory[]>(cacheKey);\n\n      if (cachedData) {\n        setData('categories', cachedData);\n        setLoading('categories', false);\n\n        // Track cache hit\n        performanceMonitor.trackNetworkRequest(\n          '/api/catalog/categories/',\n          'GET',\n          Date.now() - startTime,\n          200,\n          0,\n          0,\n          true, // cached\n        );\n\n        return;\n      }\n\n      // Fetch from API if not in cache\n      const categories = await customerService.getServiceCategories();\n\n      // Track API request\n      performanceMonitor.trackNetworkRequest(\n        '/api/catalog/categories/',\n        'GET',\n        Date.now() - startTime,\n        200,\n        0,\n        0,\n        false, // not cached\n      );\n\n      // Store in cache\n      await cacheService.set(cacheKey, categories, 5 * 60 * 1000); // 5 minutes TTL\n\n      setData('categories', categories);\n    } catch (error: any) {\n      console.error('Failed to fetch categories:', error);\n      setError('categories', error.message || 'Failed to load categories');\n\n      // Track error\n      performanceMonitor.trackNetworkRequest(\n        '/api/catalog/categories/',\n        'GET',\n        Date.now() - startTime,\n        error.status || 500,\n        0,\n        0,\n        false,\n      );\n    } finally {\n      setLoading('categories', false);\n    }\n  }, [setData, setLoading, setError]);\n\n  // Fetch featured providers with caching and performance monitoring\n  const fetchFeaturedProviders = useCallback(async () => {\n    const cacheKey = 'customer_home_featured_providers';\n    const startTime = Date.now();\n\n    setLoading('featuredProviders', true);\n    setError('featuredProviders', null);\n\n    try {\n      // Try cache first\n      const cachedData = await cacheService.get<FeaturedProvider[]>(cacheKey);\n\n      if (cachedData) {\n        setData('featuredProviders', cachedData);\n        setLoading('featuredProviders', false);\n\n        performanceMonitor.trackNetworkRequest(\n          '/api/catalog/providers/featured/',\n          'GET',\n          Date.now() - startTime,\n          200,\n          0,\n          0,\n          true,\n        );\n\n        return;\n      }\n\n      // Fetch from API\n      const providers = await customerService.getFeaturedProviders(10);\n\n      performanceMonitor.trackNetworkRequest(\n        '/api/catalog/providers/featured/',\n        'GET',\n        Date.now() - startTime,\n        200,\n        0,\n        0,\n        false,\n      );\n\n      // Cache the result\n      await cacheService.set(cacheKey, providers, 10 * 60 * 1000); // 10 minutes TTL\n\n      setData('featuredProviders', providers);\n    } catch (error: any) {\n      console.error('Failed to fetch featured providers:', error);\n      setError(\n        'featuredProviders',\n        error.message || 'Failed to load featured providers',\n      );\n\n      performanceMonitor.trackNetworkRequest(\n        '/api/catalog/providers/featured/',\n        'GET',\n        Date.now() - startTime,\n        error.status || 500,\n        0,\n        0,\n        false,\n      );\n    } finally {\n      setLoading('featuredProviders', false);\n    }\n  }, [setData, setLoading, setError]);\n\n  // Fetch favorite providers\n  const fetchFavoriteProviders = useCallback(async () => {\n    if (!isAuthenticated) return;\n\n    const cacheKey = 'favoriteProviders';\n\n    if (isCacheValid(cacheKey)) {\n      setData('favoriteProviders', getCachedData(cacheKey));\n      return;\n    }\n\n    setLoading('favoriteProviders', true);\n    setError('favoriteProviders', null);\n\n    try {\n      const providers = await customerService.getFavoriteProviders();\n      setData('favoriteProviders', providers);\n      setCache(cacheKey, providers);\n    } catch (error: any) {\n      console.error('Failed to fetch favorite providers:', error);\n      setError(\n        'favoriteProviders',\n        error.message || 'Failed to load favorite providers',\n      );\n    } finally {\n      setLoading('favoriteProviders', false);\n    }\n  }, [\n    isAuthenticated,\n    isCacheValid,\n    getCachedData,\n    setData,\n    setCache,\n    setLoading,\n    setError,\n  ]);\n\n  // Fetch nearby providers\n  const fetchNearbyProviders = useCallback(async () => {\n    // For now, use default coordinates (Ottawa)\n    const defaultLat = 45.4215;\n    const defaultLng = -75.6972;\n\n    const cacheKey = 'nearbyProviders';\n\n    if (isCacheValid(cacheKey)) {\n      setData('nearbyProviders', getCachedData(cacheKey));\n      return;\n    }\n\n    setLoading('nearbyProviders', true);\n    setError('nearbyProviders', null);\n\n    try {\n      const providers = await customerService.getNearbyProviders(\n        defaultLat,\n        defaultLng,\n        10,\n        10,\n      );\n      setData('nearbyProviders', providers);\n      setCache(cacheKey, providers);\n    } catch (error: any) {\n      console.error('Failed to fetch nearby providers:', error);\n      setError(\n        'nearbyProviders',\n        error.message || 'Failed to load nearby providers',\n      );\n    } finally {\n      setLoading('nearbyProviders', false);\n    }\n  }, [isCacheValid, getCachedData, setData, setCache, setLoading, setError]);\n\n  // Fetch dashboard data\n  const fetchDashboard = useCallback(async () => {\n    if (!isAuthenticated) return;\n\n    const cacheKey = 'dashboard';\n\n    if (isCacheValid(cacheKey)) {\n      setData('dashboard', getCachedData(cacheKey));\n      return;\n    }\n\n    setLoading('dashboard', true);\n    setError('dashboard', null);\n\n    try {\n      const dashboard = await customerService.getCustomerDashboard();\n      setData('dashboard', dashboard);\n      setCache(cacheKey, dashboard);\n    } catch (error: any) {\n      console.error('Failed to fetch dashboard:', error);\n      setError('dashboard', error.message || 'Failed to load dashboard');\n    } finally {\n      setLoading('dashboard', false);\n    }\n  }, [\n    isAuthenticated,\n    isCacheValid,\n    getCachedData,\n    setData,\n    setCache,\n    setLoading,\n    setError,\n  ]);\n\n  // Fetch personalized recommendations\n  const fetchRecommendations = useCallback(async () => {\n    if (!isAuthenticated) return;\n\n    const cacheKey = 'recommendations';\n\n    if (isCacheValid(cacheKey)) {\n      setData('recommendations', getCachedData(cacheKey));\n      return;\n    }\n\n    setLoading('recommendations', true);\n    setError('recommendations', null);\n\n    try {\n      const recommendations =\n        await customerService.getPersonalizedRecommendations();\n      setData('recommendations', recommendations);\n      setCache(cacheKey, recommendations);\n    } catch (error: any) {\n      console.error('Failed to fetch recommendations:', error);\n      setError(\n        'recommendations',\n        error.message || 'Failed to load recommendations',\n      );\n    } finally {\n      setLoading('recommendations', false);\n    }\n  }, [\n    isAuthenticated,\n    isCacheValid,\n    getCachedData,\n    setData,\n    setCache,\n    setLoading,\n    setError,\n  ]);\n\n  // Load all data\n  const loadAllData = useCallback(async () => {\n    // Cancel any ongoing requests\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n    }\n    abortControllerRef.current = new AbortController();\n\n    // Load data in parallel\n    await Promise.allSettled([\n      fetchCategories(),\n      fetchFeaturedProviders(),\n      fetchFavoriteProviders(),\n      fetchNearbyProviders(),\n      fetchDashboard(),\n      fetchRecommendations(),\n    ]);\n  }, [\n    fetchCategories,\n    fetchFeaturedProviders,\n    fetchFavoriteProviders,\n    fetchNearbyProviders,\n    fetchDashboard,\n    fetchRecommendations,\n  ]);\n\n  // Refresh all data\n  const refresh = useCallback(async () => {\n    setState(prev => ({ ...prev, refreshing: true }));\n\n    // Clear cache\n    cacheRef.current = {};\n\n    try {\n      await loadAllData();\n    } finally {\n      setState(prev => ({ ...prev, refreshing: false }));\n    }\n  }, [loadAllData]);\n\n  // Initial data load - remove loadAllData dependency to prevent infinite loop\n  useEffect(() => {\n    // Load data in parallel on mount\n    const loadData = async () => {\n      // Cancel any ongoing requests\n      if (abortControllerRef.current) {\n        abortControllerRef.current.abort();\n      }\n      abortControllerRef.current = new AbortController();\n\n      // Load data in parallel\n      await Promise.allSettled([\n        fetchCategories(),\n        fetchFeaturedProviders(),\n        fetchFavoriteProviders(),\n        fetchNearbyProviders(),\n        fetchDashboard(),\n        fetchRecommendations(),\n      ]);\n    };\n\n    loadData();\n\n    // Cleanup on unmount\n    return () => {\n      if (abortControllerRef.current) {\n        abortControllerRef.current.abort();\n      }\n    };\n  }, []); // Empty dependency array to run only on mount\n\n  return {\n    ...state,\n    refresh,\n    loadAllData,\n    fetchCategories,\n    fetchFeaturedProviders,\n    fetchFavoriteProviders,\n    fetchNearbyProviders,\n    fetchDashboard,\n    fetchRecommendations,\n  };\n};\n"], "mappings": ";;;;;;;;AAaA,IAAAA,MAAA,GAAAC,OAAA;AAEA,IAAAC,aAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,gBAAA,GAAAD,sBAAA,CAAAF,OAAA;AAMA,IAAAI,mBAAA,GAAAJ,OAAA;AACA,IAAAK,UAAA,GAAAL,OAAA;AAuCA,IAAMM,WAA2B,GAAG;EAClCC,UAAU,EAAE,EAAE;EACdC,iBAAiB,EAAE,EAAE;EACrBC,iBAAiB,EAAE,EAAE;EACrBC,eAAe,EAAE,EAAE;EACnBC,SAAS,EAAE,IAAI;EACfC,eAAe,EAAE,EAAE;EACnBC,cAAc,EAAE,EAAE;EAClBC,mBAAmB,EAAE;AACvB,CAAC;AAED,IAAMC,cAAc,GAAG;EACrBR,UAAU,EAAE,KAAK;EACjBC,iBAAiB,EAAE,KAAK;EACxBC,iBAAiB,EAAE,KAAK;EACxBC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,KAAK;EAChBC,eAAe,EAAE,KAAK;EACtBC,cAAc,EAAE,KAAK;EACrBG,OAAO,EAAE;AACX,CAAC;AAED,IAAMC,YAAY,GAAG;EACnBV,UAAU,EAAE,IAAI;EAChBC,iBAAiB,EAAE,IAAI;EACvBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE,IAAI;EACrBC,SAAS,EAAE,IAAI;EACfC,eAAe,EAAE,IAAI;EACrBC,cAAc,EAAE,IAAI;EACpBG,OAAO,EAAE;AACX,CAAC;AAEM,IAAME,mBAAmB,GAAAC,OAAA,CAAAD,mBAAA,GAAG,SAAtBA,mBAAmBA,CAAA,EAAS;EACvC,IAAAE,aAAA,GAAkC,IAAAC,uBAAY,EAAC,CAAC;IAAxCC,eAAe,GAAAF,aAAA,CAAfE,eAAe;IAAEC,IAAI,GAAAH,aAAA,CAAJG,IAAI;EAC7B,IAAAC,SAAA,GAA0B,IAAAC,eAAQ,EAAkB;MAClDC,IAAI,EAAEpB,WAAW;MACjBqB,OAAO,EAAEZ,cAAc;MACvBa,KAAK,EAAEX,YAAY;MACnBY,WAAW,EAAE,IAAI;MACjBC,UAAU,EAAE;IACd,CAAC,CAAC;IAAAC,UAAA,OAAAC,eAAA,CAAAC,OAAA,EAAAT,SAAA;IANKU,KAAK,GAAAH,UAAA;IAAEI,QAAQ,GAAAJ,UAAA;EAQtB,IAAMK,kBAAkB,GAAG,IAAAC,aAAM,EAAyB,IAAI,CAAC;EAC/D,IAAMC,QAAQ,GAAG,IAAAD,aAAM,EACrB,CAAC,CACH,CAAC;EACD,IAAME,cAAc,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI;EAGpC,IAAMC,YAAY,GAAG,IAAAC,kBAAW,EAAC,UAACC,GAAW,EAAc;IACzD,IAAMC,MAAM,GAAGL,QAAQ,CAACM,OAAO,CAACF,GAAG,CAAC;IACpC,IAAI,CAACC,MAAM,EAAE,OAAO,KAAK;IACzB,OAAOE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGH,MAAM,CAACI,SAAS,GAAGR,cAAc;EACvD,CAAC,EAAE,EAAE,CAAC;EAGN,IAAMS,aAAa,GAAG,IAAAP,kBAAW,EAAC,UAACC,GAAW,EAAK;IACjD,IAAMC,MAAM,GAAGL,QAAQ,CAACM,OAAO,CAACF,GAAG,CAAC;IACpC,OAAOC,MAAM,GAAGA,MAAM,CAACjB,IAAI,GAAG,IAAI;EACpC,CAAC,EAAE,EAAE,CAAC;EAGN,IAAMuB,QAAQ,GAAG,IAAAR,kBAAW,EAAC,UAACC,GAAW,EAAEhB,IAAS,EAAK;IACvDY,QAAQ,CAACM,OAAO,CAACF,GAAG,CAAC,GAAG;MACtBhB,IAAI,EAAJA,IAAI;MACJqB,SAAS,EAAEF,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAGN,IAAMI,UAAU,GAAG,IAAAT,kBAAW,EAC5B,UAACC,GAAgC,EAAES,KAAc,EAAK;IACpDhB,QAAQ,CAAC,UAAAiB,IAAI;MAAA,OAAAC,MAAA,CAAAC,MAAA,KACRF,IAAI;QACPzB,OAAO,EAAA0B,MAAA,CAAAC,MAAA,KACFF,IAAI,CAACzB,OAAO,MAAA4B,gBAAA,CAAAtB,OAAA,MAAAsB,gBAAA,CAAAtB,OAAA,MACdS,GAAG,EAAGS,KAAK,cAEVA,KAAK,IACLE,MAAM,CAACG,MAAM,CAAAH,MAAA,CAAAC,MAAA,KAAMF,IAAI,CAACzB,OAAO,MAAA4B,gBAAA,CAAAtB,OAAA,MAAGS,GAAG,EAAGS,KAAK,EAAE,CAAC,CAACM,IAAI,CAACC,OAAO,CAAC;MACjE;IAAA,CACD,CAAC;EACL,CAAC,EACD,EACF,CAAC;EAGD,IAAMC,QAAQ,GAAG,IAAAlB,kBAAW,EAC1B,UAACC,GAA8B,EAAES,KAAoB,EAAK;IACxDhB,QAAQ,CAAC,UAAAiB,IAAI;MAAA,OAAAC,MAAA,CAAAC,MAAA,KACRF,IAAI;QACPxB,KAAK,EAAAyB,MAAA,CAAAC,MAAA,KACAF,IAAI,CAACxB,KAAK,MAAA2B,gBAAA,CAAAtB,OAAA,MACZS,GAAG,EAAGS,KAAK;MACb;IAAA,CACD,CAAC;EACL,CAAC,EACD,EACF,CAAC;EAGD,IAAMS,OAAO,GAAG,IAAAnB,kBAAW,EAAC,UAACC,GAAyB,EAAES,KAAU,EAAK;IACrEhB,QAAQ,CAAC,UAAAiB,IAAI;MAAA,OAAAC,MAAA,CAAAC,MAAA,KACRF,IAAI;QACP1B,IAAI,EAAA2B,MAAA,CAAAC,MAAA,KACCF,IAAI,CAAC1B,IAAI,MAAA6B,gBAAA,CAAAtB,OAAA,MACXS,GAAG,EAAGS,KAAK,EACb;QACDtB,WAAW,EAAE,IAAIgB,IAAI,CAAC;MAAC;IAAA,CACvB,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAGN,IAAMgB,eAAe,GAAG,IAAApB,kBAAW,MAAAqB,kBAAA,CAAA7B,OAAA,EAAC,aAAY;IAC9C,IAAM8B,QAAQ,GAAG,0BAA0B;IAC3C,IAAMC,SAAS,GAAGnB,IAAI,CAACC,GAAG,CAAC,CAAC;IAE5BI,UAAU,CAAC,YAAY,EAAE,IAAI,CAAC;IAC9BS,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC;IAE5B,IAAI;MAEF,IAAMM,UAAU,SAASC,qBAAY,CAACC,GAAG,CAAoBJ,QAAQ,CAAC;MAEtE,IAAIE,UAAU,EAAE;QACdL,OAAO,CAAC,YAAY,EAAEK,UAAU,CAAC;QACjCf,UAAU,CAAC,YAAY,EAAE,KAAK,CAAC;QAG/BkB,sCAAkB,CAACC,mBAAmB,CACpC,0BAA0B,EAC1B,KAAK,EACLxB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGkB,SAAS,EACtB,GAAG,EACH,CAAC,EACD,CAAC,EACD,IACF,CAAC;QAED;MACF;MAGA,IAAMzD,UAAU,SAAS+D,wBAAe,CAACC,oBAAoB,CAAC,CAAC;MAG/DH,sCAAkB,CAACC,mBAAmB,CACpC,0BAA0B,EAC1B,KAAK,EACLxB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGkB,SAAS,EACtB,GAAG,EACH,CAAC,EACD,CAAC,EACD,KACF,CAAC;MAGD,MAAME,qBAAY,CAACM,GAAG,CAACT,QAAQ,EAAExD,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;MAE3DqD,OAAO,CAAC,YAAY,EAAErD,UAAU,CAAC;IACnC,CAAC,CAAC,OAAOqB,KAAU,EAAE;MACnB6C,OAAO,CAAC7C,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD+B,QAAQ,CAAC,YAAY,EAAE/B,KAAK,CAAC8C,OAAO,IAAI,2BAA2B,CAAC;MAGpEN,sCAAkB,CAACC,mBAAmB,CACpC,0BAA0B,EAC1B,KAAK,EACLxB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGkB,SAAS,EACtBpC,KAAK,CAAC+C,MAAM,IAAI,GAAG,EACnB,CAAC,EACD,CAAC,EACD,KACF,CAAC;IACH,CAAC,SAAS;MACRzB,UAAU,CAAC,YAAY,EAAE,KAAK,CAAC;IACjC;EACF,CAAC,GAAE,CAACU,OAAO,EAAEV,UAAU,EAAES,QAAQ,CAAC,CAAC;EAGnC,IAAMiB,sBAAsB,GAAG,IAAAnC,kBAAW,MAAAqB,kBAAA,CAAA7B,OAAA,EAAC,aAAY;IACrD,IAAM8B,QAAQ,GAAG,kCAAkC;IACnD,IAAMC,SAAS,GAAGnB,IAAI,CAACC,GAAG,CAAC,CAAC;IAE5BI,UAAU,CAAC,mBAAmB,EAAE,IAAI,CAAC;IACrCS,QAAQ,CAAC,mBAAmB,EAAE,IAAI,CAAC;IAEnC,IAAI;MAEF,IAAMM,UAAU,SAASC,qBAAY,CAACC,GAAG,CAAqBJ,QAAQ,CAAC;MAEvE,IAAIE,UAAU,EAAE;QACdL,OAAO,CAAC,mBAAmB,EAAEK,UAAU,CAAC;QACxCf,UAAU,CAAC,mBAAmB,EAAE,KAAK,CAAC;QAEtCkB,sCAAkB,CAACC,mBAAmB,CACpC,kCAAkC,EAClC,KAAK,EACLxB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGkB,SAAS,EACtB,GAAG,EACH,CAAC,EACD,CAAC,EACD,IACF,CAAC;QAED;MACF;MAGA,IAAMa,SAAS,SAASP,wBAAe,CAACQ,oBAAoB,CAAC,EAAE,CAAC;MAEhEV,sCAAkB,CAACC,mBAAmB,CACpC,kCAAkC,EAClC,KAAK,EACLxB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGkB,SAAS,EACtB,GAAG,EACH,CAAC,EACD,CAAC,EACD,KACF,CAAC;MAGD,MAAME,qBAAY,CAACM,GAAG,CAACT,QAAQ,EAAEc,SAAS,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAE3DjB,OAAO,CAAC,mBAAmB,EAAEiB,SAAS,CAAC;IACzC,CAAC,CAAC,OAAOjD,KAAU,EAAE;MACnB6C,OAAO,CAAC7C,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D+B,QAAQ,CACN,mBAAmB,EACnB/B,KAAK,CAAC8C,OAAO,IAAI,mCACnB,CAAC;MAEDN,sCAAkB,CAACC,mBAAmB,CACpC,kCAAkC,EAClC,KAAK,EACLxB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGkB,SAAS,EACtBpC,KAAK,CAAC+C,MAAM,IAAI,GAAG,EACnB,CAAC,EACD,CAAC,EACD,KACF,CAAC;IACH,CAAC,SAAS;MACRzB,UAAU,CAAC,mBAAmB,EAAE,KAAK,CAAC;IACxC;EACF,CAAC,GAAE,CAACU,OAAO,EAAEV,UAAU,EAAES,QAAQ,CAAC,CAAC;EAGnC,IAAMoB,sBAAsB,GAAG,IAAAtC,kBAAW,MAAAqB,kBAAA,CAAA7B,OAAA,EAAC,aAAY;IACrD,IAAI,CAACX,eAAe,EAAE;IAEtB,IAAMyC,QAAQ,GAAG,mBAAmB;IAEpC,IAAIvB,YAAY,CAACuB,QAAQ,CAAC,EAAE;MAC1BH,OAAO,CAAC,mBAAmB,EAAEZ,aAAa,CAACe,QAAQ,CAAC,CAAC;MACrD;IACF;IAEAb,UAAU,CAAC,mBAAmB,EAAE,IAAI,CAAC;IACrCS,QAAQ,CAAC,mBAAmB,EAAE,IAAI,CAAC;IAEnC,IAAI;MACF,IAAMkB,SAAS,SAASP,wBAAe,CAACU,oBAAoB,CAAC,CAAC;MAC9DpB,OAAO,CAAC,mBAAmB,EAAEiB,SAAS,CAAC;MACvC5B,QAAQ,CAACc,QAAQ,EAAEc,SAAS,CAAC;IAC/B,CAAC,CAAC,OAAOjD,KAAU,EAAE;MACnB6C,OAAO,CAAC7C,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D+B,QAAQ,CACN,mBAAmB,EACnB/B,KAAK,CAAC8C,OAAO,IAAI,mCACnB,CAAC;IACH,CAAC,SAAS;MACRxB,UAAU,CAAC,mBAAmB,EAAE,KAAK,CAAC;IACxC;EACF,CAAC,GAAE,CACD5B,eAAe,EACfkB,YAAY,EACZQ,aAAa,EACbY,OAAO,EACPX,QAAQ,EACRC,UAAU,EACVS,QAAQ,CACT,CAAC;EAGF,IAAMsB,oBAAoB,GAAG,IAAAxC,kBAAW,MAAAqB,kBAAA,CAAA7B,OAAA,EAAC,aAAY;IAEnD,IAAMiD,UAAU,GAAG,OAAO;IAC1B,IAAMC,UAAU,GAAG,CAAC,OAAO;IAE3B,IAAMpB,QAAQ,GAAG,iBAAiB;IAElC,IAAIvB,YAAY,CAACuB,QAAQ,CAAC,EAAE;MAC1BH,OAAO,CAAC,iBAAiB,EAAEZ,aAAa,CAACe,QAAQ,CAAC,CAAC;MACnD;IACF;IAEAb,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC;IACnCS,QAAQ,CAAC,iBAAiB,EAAE,IAAI,CAAC;IAEjC,IAAI;MACF,IAAMkB,SAAS,SAASP,wBAAe,CAACc,kBAAkB,CACxDF,UAAU,EACVC,UAAU,EACV,EAAE,EACF,EACF,CAAC;MACDvB,OAAO,CAAC,iBAAiB,EAAEiB,SAAS,CAAC;MACrC5B,QAAQ,CAACc,QAAQ,EAAEc,SAAS,CAAC;IAC/B,CAAC,CAAC,OAAOjD,KAAU,EAAE;MACnB6C,OAAO,CAAC7C,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD+B,QAAQ,CACN,iBAAiB,EACjB/B,KAAK,CAAC8C,OAAO,IAAI,iCACnB,CAAC;IACH,CAAC,SAAS;MACRxB,UAAU,CAAC,iBAAiB,EAAE,KAAK,CAAC;IACtC;EACF,CAAC,GAAE,CAACV,YAAY,EAAEQ,aAAa,EAAEY,OAAO,EAAEX,QAAQ,EAAEC,UAAU,EAAES,QAAQ,CAAC,CAAC;EAG1E,IAAM0B,cAAc,GAAG,IAAA5C,kBAAW,MAAAqB,kBAAA,CAAA7B,OAAA,EAAC,aAAY;IAC7C,IAAI,CAACX,eAAe,EAAE;IAEtB,IAAMyC,QAAQ,GAAG,WAAW;IAE5B,IAAIvB,YAAY,CAACuB,QAAQ,CAAC,EAAE;MAC1BH,OAAO,CAAC,WAAW,EAAEZ,aAAa,CAACe,QAAQ,CAAC,CAAC;MAC7C;IACF;IAEAb,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC;IAC7BS,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC;IAE3B,IAAI;MACF,IAAMhD,SAAS,SAAS2D,wBAAe,CAACgB,oBAAoB,CAAC,CAAC;MAC9D1B,OAAO,CAAC,WAAW,EAAEjD,SAAS,CAAC;MAC/BsC,QAAQ,CAACc,QAAQ,EAAEpD,SAAS,CAAC;IAC/B,CAAC,CAAC,OAAOiB,KAAU,EAAE;MACnB6C,OAAO,CAAC7C,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD+B,QAAQ,CAAC,WAAW,EAAE/B,KAAK,CAAC8C,OAAO,IAAI,0BAA0B,CAAC;IACpE,CAAC,SAAS;MACRxB,UAAU,CAAC,WAAW,EAAE,KAAK,CAAC;IAChC;EACF,CAAC,GAAE,CACD5B,eAAe,EACfkB,YAAY,EACZQ,aAAa,EACbY,OAAO,EACPX,QAAQ,EACRC,UAAU,EACVS,QAAQ,CACT,CAAC;EAGF,IAAM4B,oBAAoB,GAAG,IAAA9C,kBAAW,MAAAqB,kBAAA,CAAA7B,OAAA,EAAC,aAAY;IACnD,IAAI,CAACX,eAAe,EAAE;IAEtB,IAAMyC,QAAQ,GAAG,iBAAiB;IAElC,IAAIvB,YAAY,CAACuB,QAAQ,CAAC,EAAE;MAC1BH,OAAO,CAAC,iBAAiB,EAAEZ,aAAa,CAACe,QAAQ,CAAC,CAAC;MACnD;IACF;IAEAb,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC;IACnCS,QAAQ,CAAC,iBAAiB,EAAE,IAAI,CAAC;IAEjC,IAAI;MACF,IAAM/C,eAAe,SACb0D,wBAAe,CAACkB,8BAA8B,CAAC,CAAC;MACxD5B,OAAO,CAAC,iBAAiB,EAAEhD,eAAe,CAAC;MAC3CqC,QAAQ,CAACc,QAAQ,EAAEnD,eAAe,CAAC;IACrC,CAAC,CAAC,OAAOgB,KAAU,EAAE;MACnB6C,OAAO,CAAC7C,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD+B,QAAQ,CACN,iBAAiB,EACjB/B,KAAK,CAAC8C,OAAO,IAAI,gCACnB,CAAC;IACH,CAAC,SAAS;MACRxB,UAAU,CAAC,iBAAiB,EAAE,KAAK,CAAC;IACtC;EACF,CAAC,GAAE,CACD5B,eAAe,EACfkB,YAAY,EACZQ,aAAa,EACbY,OAAO,EACPX,QAAQ,EACRC,UAAU,EACVS,QAAQ,CACT,CAAC;EAGF,IAAM8B,WAAW,GAAG,IAAAhD,kBAAW,MAAAqB,kBAAA,CAAA7B,OAAA,EAAC,aAAY;IAE1C,IAAIG,kBAAkB,CAACQ,OAAO,EAAE;MAC9BR,kBAAkB,CAACQ,OAAO,CAAC8C,KAAK,CAAC,CAAC;IACpC;IACAtD,kBAAkB,CAACQ,OAAO,GAAG,IAAI+C,eAAe,CAAC,CAAC;IAGlD,MAAMC,OAAO,CAACC,UAAU,CAAC,CACvBhC,eAAe,CAAC,CAAC,EACjBe,sBAAsB,CAAC,CAAC,EACxBG,sBAAsB,CAAC,CAAC,EACxBE,oBAAoB,CAAC,CAAC,EACtBI,cAAc,CAAC,CAAC,EAChBE,oBAAoB,CAAC,CAAC,CACvB,CAAC;EACJ,CAAC,GAAE,CACD1B,eAAe,EACfe,sBAAsB,EACtBG,sBAAsB,EACtBE,oBAAoB,EACpBI,cAAc,EACdE,oBAAoB,CACrB,CAAC;EAGF,IAAMO,OAAO,GAAG,IAAArD,kBAAW,MAAAqB,kBAAA,CAAA7B,OAAA,EAAC,aAAY;IACtCE,QAAQ,CAAC,UAAAiB,IAAI;MAAA,OAAAC,MAAA,CAAAC,MAAA,KAAUF,IAAI;QAAEtB,UAAU,EAAE;MAAI;IAAA,CAAG,CAAC;IAGjDQ,QAAQ,CAACM,OAAO,GAAG,CAAC,CAAC;IAErB,IAAI;MACF,MAAM6C,WAAW,CAAC,CAAC;IACrB,CAAC,SAAS;MACRtD,QAAQ,CAAC,UAAAiB,IAAI;QAAA,OAAAC,MAAA,CAAAC,MAAA,KAAUF,IAAI;UAAEtB,UAAU,EAAE;QAAK;MAAA,CAAG,CAAC;IACpD;EACF,CAAC,GAAE,CAAC2D,WAAW,CAAC,CAAC;EAGjB,IAAAM,gBAAS,EAAC,YAAM;IAEd,IAAMC,QAAQ;MAAA,IAAAC,KAAA,OAAAnC,kBAAA,CAAA7B,OAAA,EAAG,aAAY;QAE3B,IAAIG,kBAAkB,CAACQ,OAAO,EAAE;UAC9BR,kBAAkB,CAACQ,OAAO,CAAC8C,KAAK,CAAC,CAAC;QACpC;QACAtD,kBAAkB,CAACQ,OAAO,GAAG,IAAI+C,eAAe,CAAC,CAAC;QAGlD,MAAMC,OAAO,CAACC,UAAU,CAAC,CACvBhC,eAAe,CAAC,CAAC,EACjBe,sBAAsB,CAAC,CAAC,EACxBG,sBAAsB,CAAC,CAAC,EACxBE,oBAAoB,CAAC,CAAC,EACtBI,cAAc,CAAC,CAAC,EAChBE,oBAAoB,CAAC,CAAC,CACvB,CAAC;MACJ,CAAC;MAAA,gBAhBKS,QAAQA,CAAA;QAAA,OAAAC,KAAA,CAAAC,KAAA,OAAAC,SAAA;MAAA;IAAA,GAgBb;IAEDH,QAAQ,CAAC,CAAC;IAGV,OAAO,YAAM;MACX,IAAI5D,kBAAkB,CAACQ,OAAO,EAAE;QAC9BR,kBAAkB,CAACQ,OAAO,CAAC8C,KAAK,CAAC,CAAC;MACpC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OAAArC,MAAA,CAAAC,MAAA,KACKpB,KAAK;IACR4D,OAAO,EAAPA,OAAO;IACPL,WAAW,EAAXA,WAAW;IACX5B,eAAe,EAAfA,eAAe;IACfe,sBAAsB,EAAtBA,sBAAsB;IACtBG,sBAAsB,EAAtBA,sBAAsB;IACtBE,oBAAoB,EAApBA,oBAAoB;IACpBI,cAAc,EAAdA,cAAc;IACdE,oBAAoB,EAApBA;EAAoB;AAExB,CAAC", "ignoreList": []}