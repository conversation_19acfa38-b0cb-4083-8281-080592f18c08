{"version": 3, "names": ["NavigationAnalyticsService", "_classCallCheck2", "default", "screenStartTime", "currentScreen", "previousScreen", "currentSession", "generateSessionId", "currentFlow", "sessionId", "startTime", "Date", "now", "screens", "_createClass2", "key", "value", "Math", "random", "toString", "substr", "trackScreenView", "screenName", "params", "userRole", "timeSpent", "trackScreenMetrics", "loadTime", "exitMethod", "event", "undefined", "timestamp", "push", "__DEV__", "console", "log", "screen", "previous", "sendAnalyticsEvent", "trackScreenLoadTime", "trackNavigationAction", "action", "fromScreen", "toScreen", "metrics", "Object", "assign", "trackFlowCompletion", "flowName", "success", "metadata", "duration", "screenCount", "length", "trackNavigationError", "error", "getCurrentFlow", "totalDuration", "startNewSession", "getNavigationStats", "_Object$entries$sort$", "flow", "screenTimes", "screenCounts", "for<PERSON>ach", "index", "mostVisitedScreen", "entries", "sort", "_ref", "_ref2", "_ref3", "_slicedToArray2", "a", "_ref4", "b", "allTimes", "values", "flat", "averageScreenTime", "reduce", "sum", "time", "sessionDuration", "navigationPattern", "map", "s", "eventType", "data", "navigationAnalytics", "exports", "_default"], "sources": ["navigationAnalytics.ts"], "sourcesContent": ["/**\n * Navigation Analytics Service - Track navigation events and user flow\n *\n * Service Contract:\n * - Tracks navigation events for analytics\n * - Monitors user flow and screen transitions\n * - Provides performance metrics for navigation\n * - Implements privacy-compliant tracking\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\ninterface NavigationEvent {\n  screenName: string;\n  previousScreen?: string;\n  timestamp: number;\n  params?: Record<string, any>;\n  userRole?: 'customer' | 'provider';\n  sessionId: string;\n}\n\ninterface ScreenMetrics {\n  screenName: string;\n  loadTime: number;\n  timeSpent: number;\n  exitMethod: 'navigation' | 'back' | 'tab' | 'deep_link' | 'app_close';\n}\n\ninterface NavigationFlow {\n  sessionId: string;\n  startTime: number;\n  screens: NavigationEvent[];\n  totalDuration?: number;\n  userRole?: 'customer' | 'provider';\n}\n\nclass NavigationAnalyticsService {\n  private currentSession: string;\n  private currentFlow: NavigationFlow;\n  private screenStartTime: number = 0;\n  private currentScreen: string = '';\n  private previousScreen: string = '';\n\n  constructor() {\n    this.currentSession = this.generateSessionId();\n    this.currentFlow = {\n      sessionId: this.currentSession,\n      startTime: Date.now(),\n      screens: [],\n    };\n  }\n\n  /**\n   * Generate a unique session ID\n   */\n  private generateSessionId(): string {\n    return `nav_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  /**\n   * Track screen view event\n   */\n  trackScreenView(\n    screenName: string,\n    params?: Record<string, any>,\n    userRole?: 'customer' | 'provider',\n  ): void {\n    const now = Date.now();\n\n    // Calculate time spent on previous screen\n    if (this.currentScreen && this.screenStartTime > 0) {\n      const timeSpent = now - this.screenStartTime;\n      this.trackScreenMetrics({\n        screenName: this.currentScreen,\n        loadTime: 0, // Will be set separately\n        timeSpent,\n        exitMethod: 'navigation',\n      });\n    }\n\n    // Create navigation event\n    const event: NavigationEvent = {\n      screenName,\n      previousScreen: this.currentScreen || undefined,\n      timestamp: now,\n      params,\n      userRole,\n      sessionId: this.currentSession,\n    };\n\n    // Add to current flow\n    this.currentFlow.screens.push(event);\n    if (userRole) {\n      this.currentFlow.userRole = userRole;\n    }\n\n    // Update current state\n    this.previousScreen = this.currentScreen;\n    this.currentScreen = screenName;\n    this.screenStartTime = now;\n\n    // Log for development\n    if (__DEV__) {\n      console.log('📊 Navigation Analytics - Screen View:', {\n        screen: screenName,\n        previous: this.previousScreen,\n        params,\n        userRole,\n      });\n    }\n\n    // Send to analytics service (implement based on your analytics provider)\n    this.sendAnalyticsEvent('screen_view', event);\n  }\n\n  /**\n   * Track screen load performance\n   */\n  trackScreenLoadTime(screenName: string, loadTime: number): void {\n    if (__DEV__) {\n      console.log('⏱️ Navigation Analytics - Screen Load Time:', {\n        screen: screenName,\n        loadTime: `${loadTime}ms`,\n      });\n    }\n\n    this.sendAnalyticsEvent('screen_load_time', {\n      screenName,\n      loadTime,\n      timestamp: Date.now(),\n      sessionId: this.currentSession,\n    });\n  }\n\n  /**\n   * Track navigation action (button press, tab switch, etc.)\n   */\n  trackNavigationAction(\n    action:\n      | 'tab_switch'\n      | 'button_press'\n      | 'back_button'\n      | 'deep_link'\n      | 'swipe',\n    fromScreen: string,\n    toScreen: string,\n    params?: Record<string, any>,\n  ): void {\n    const event = {\n      action,\n      fromScreen,\n      toScreen,\n      params,\n      timestamp: Date.now(),\n      sessionId: this.currentSession,\n    };\n\n    if (__DEV__) {\n      console.log('🎯 Navigation Analytics - Action:', event);\n    }\n\n    this.sendAnalyticsEvent('navigation_action', event);\n  }\n\n  /**\n   * Track screen metrics (time spent, exit method)\n   */\n  private trackScreenMetrics(metrics: ScreenMetrics): void {\n    if (__DEV__) {\n      console.log('📈 Navigation Analytics - Screen Metrics:', {\n        screen: metrics.screenName,\n        timeSpent: `${metrics.timeSpent}ms`,\n        exitMethod: metrics.exitMethod,\n      });\n    }\n\n    this.sendAnalyticsEvent('screen_metrics', {\n      ...metrics,\n      timestamp: Date.now(),\n      sessionId: this.currentSession,\n    });\n  }\n\n  /**\n   * Track user flow completion\n   */\n  trackFlowCompletion(\n    flowName: string,\n    success: boolean,\n    metadata?: Record<string, any>,\n  ): void {\n    const event = {\n      flowName,\n      success,\n      metadata,\n      duration: Date.now() - this.currentFlow.startTime,\n      screenCount: this.currentFlow.screens.length,\n      timestamp: Date.now(),\n      sessionId: this.currentSession,\n    };\n\n    if (__DEV__) {\n      console.log('🎯 Navigation Analytics - Flow Completion:', event);\n    }\n\n    this.sendAnalyticsEvent('flow_completion', event);\n  }\n\n  /**\n   * Track navigation error\n   */\n  trackNavigationError(\n    error: string,\n    screenName: string,\n    params?: Record<string, any>,\n  ): void {\n    const event = {\n      error,\n      screenName,\n      params,\n      timestamp: Date.now(),\n      sessionId: this.currentSession,\n    };\n\n    if (__DEV__) {\n      console.error('❌ Navigation Analytics - Error:', event);\n    }\n\n    this.sendAnalyticsEvent('navigation_error', event);\n  }\n\n  /**\n   * Get current navigation flow\n   */\n  getCurrentFlow(): NavigationFlow {\n    return {\n      ...this.currentFlow,\n      totalDuration: Date.now() - this.currentFlow.startTime,\n    };\n  }\n\n  /**\n   * Start a new session\n   */\n  startNewSession(userRole?: 'customer' | 'provider'): void {\n    // Complete current flow\n    this.currentFlow.totalDuration = Date.now() - this.currentFlow.startTime;\n\n    // Start new session\n    this.currentSession = this.generateSessionId();\n    this.currentFlow = {\n      sessionId: this.currentSession,\n      startTime: Date.now(),\n      screens: [],\n      userRole,\n    };\n    this.currentScreen = '';\n    this.previousScreen = '';\n    this.screenStartTime = 0;\n\n    if (__DEV__) {\n      console.log(\n        '🔄 Navigation Analytics - New Session Started:',\n        this.currentSession,\n      );\n    }\n  }\n\n  /**\n   * Get navigation statistics\n   */\n  getNavigationStats(): {\n    sessionDuration: number;\n    screenCount: number;\n    averageScreenTime: number;\n    mostVisitedScreen: string;\n    navigationPattern: string[];\n  } {\n    const flow = this.getCurrentFlow();\n    const screenTimes: Record<string, number[]> = {};\n    const screenCounts: Record<string, number> = {};\n\n    // Calculate screen statistics\n    flow.screens.forEach((screen, index) => {\n      const screenName = screen.screenName;\n\n      // Count visits\n      screenCounts[screenName] = (screenCounts[screenName] || 0) + 1;\n\n      // Calculate time spent (if not the last screen)\n      if (index < flow.screens.length - 1) {\n        const timeSpent = flow.screens[index + 1].timestamp - screen.timestamp;\n        if (!screenTimes[screenName]) {\n          screenTimes[screenName] = [];\n        }\n        screenTimes[screenName].push(timeSpent);\n      }\n    });\n\n    // Find most visited screen\n    const mostVisitedScreen =\n      Object.entries(screenCounts).sort(([, a], [, b]) => b - a)[0]?.[0] || '';\n\n    // Calculate average screen time\n    const allTimes = Object.values(screenTimes).flat();\n    const averageScreenTime =\n      allTimes.length > 0\n        ? allTimes.reduce((sum, time) => sum + time, 0) / allTimes.length\n        : 0;\n\n    return {\n      sessionDuration: flow.totalDuration || 0,\n      screenCount: flow.screens.length,\n      averageScreenTime,\n      mostVisitedScreen,\n      navigationPattern: flow.screens.map(s => s.screenName),\n    };\n  }\n\n  /**\n   * Send analytics event to external service\n   */\n  private sendAnalyticsEvent(eventType: string, data: any): void {\n    // Implement based on your analytics provider (Firebase, Mixpanel, etc.)\n    // For now, just store locally for development\n\n    if (__DEV__) {\n      // In development, just log to console\n      return;\n    }\n\n    // Example implementation for Firebase Analytics:\n    // analytics().logEvent(eventType, data);\n\n    // Example implementation for custom analytics:\n    // analyticsService.track(eventType, data);\n  }\n}\n\n// Export singleton instance\nexport const navigationAnalytics = new NavigationAnalyticsService();\nexport default navigationAnalytics;\n"], "mappings": ";;;;;;;;IAqCMA,0BAA0B;EAO9B,SAAAA,2BAAA,EAAc;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAF,0BAAA;IAAA,KAJNG,eAAe,GAAW,CAAC;IAAA,KAC3BC,aAAa,GAAW,EAAE;IAAA,KAC1BC,cAAc,GAAW,EAAE;IAGjC,IAAI,CAACC,cAAc,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAC9C,IAAI,CAACC,WAAW,GAAG;MACjBC,SAAS,EAAE,IAAI,CAACH,cAAc;MAC9BI,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACrBC,OAAO,EAAE;IACX,CAAC;EACH;EAAC,WAAAC,aAAA,CAAAZ,OAAA,EAAAF,0BAAA;IAAAe,GAAA;IAAAC,KAAA,EAKD,SAAQT,iBAAiBA,CAAA,EAAW;MAClC,OAAO,OAAOI,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIK,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACvE;EAAC;IAAAL,GAAA;IAAAC,KAAA,EAKD,SAAAK,eAAeA,CACbC,UAAkB,EAClBC,MAA4B,EAC5BC,QAAkC,EAC5B;MACN,IAAMZ,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;MAGtB,IAAI,IAAI,CAACR,aAAa,IAAI,IAAI,CAACD,eAAe,GAAG,CAAC,EAAE;QAClD,IAAMsB,SAAS,GAAGb,GAAG,GAAG,IAAI,CAACT,eAAe;QAC5C,IAAI,CAACuB,kBAAkB,CAAC;UACtBJ,UAAU,EAAE,IAAI,CAAClB,aAAa;UAC9BuB,QAAQ,EAAE,CAAC;UACXF,SAAS,EAATA,SAAS;UACTG,UAAU,EAAE;QACd,CAAC,CAAC;MACJ;MAGA,IAAMC,KAAsB,GAAG;QAC7BP,UAAU,EAAVA,UAAU;QACVjB,cAAc,EAAE,IAAI,CAACD,aAAa,IAAI0B,SAAS;QAC/CC,SAAS,EAAEnB,GAAG;QACdW,MAAM,EAANA,MAAM;QACNC,QAAQ,EAARA,QAAQ;QACRf,SAAS,EAAE,IAAI,CAACH;MAClB,CAAC;MAGD,IAAI,CAACE,WAAW,CAACK,OAAO,CAACmB,IAAI,CAACH,KAAK,CAAC;MACpC,IAAIL,QAAQ,EAAE;QACZ,IAAI,CAAChB,WAAW,CAACgB,QAAQ,GAAGA,QAAQ;MACtC;MAGA,IAAI,CAACnB,cAAc,GAAG,IAAI,CAACD,aAAa;MACxC,IAAI,CAACA,aAAa,GAAGkB,UAAU;MAC/B,IAAI,CAACnB,eAAe,GAAGS,GAAG;MAG1B,IAAIqB,OAAO,EAAE;QACXC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;UACpDC,MAAM,EAAEd,UAAU;UAClBe,QAAQ,EAAE,IAAI,CAAChC,cAAc;UAC7BkB,MAAM,EAANA,MAAM;UACNC,QAAQ,EAARA;QACF,CAAC,CAAC;MACJ;MAGA,IAAI,CAACc,kBAAkB,CAAC,aAAa,EAAET,KAAK,CAAC;IAC/C;EAAC;IAAAd,GAAA;IAAAC,KAAA,EAKD,SAAAuB,mBAAmBA,CAACjB,UAAkB,EAAEK,QAAgB,EAAQ;MAC9D,IAAIM,OAAO,EAAE;QACXC,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE;UACzDC,MAAM,EAAEd,UAAU;UAClBK,QAAQ,EAAE,GAAGA,QAAQ;QACvB,CAAC,CAAC;MACJ;MAEA,IAAI,CAACW,kBAAkB,CAAC,kBAAkB,EAAE;QAC1ChB,UAAU,EAAVA,UAAU;QACVK,QAAQ,EAARA,QAAQ;QACRI,SAAS,EAAEpB,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBH,SAAS,EAAE,IAAI,CAACH;MAClB,CAAC,CAAC;IACJ;EAAC;IAAAS,GAAA;IAAAC,KAAA,EAKD,SAAAwB,qBAAqBA,CACnBC,MAKW,EACXC,UAAkB,EAClBC,QAAgB,EAChBpB,MAA4B,EACtB;MACN,IAAMM,KAAK,GAAG;QACZY,MAAM,EAANA,MAAM;QACNC,UAAU,EAAVA,UAAU;QACVC,QAAQ,EAARA,QAAQ;QACRpB,MAAM,EAANA,MAAM;QACNQ,SAAS,EAAEpB,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBH,SAAS,EAAE,IAAI,CAACH;MAClB,CAAC;MAED,IAAI2B,OAAO,EAAE;QACXC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEN,KAAK,CAAC;MACzD;MAEA,IAAI,CAACS,kBAAkB,CAAC,mBAAmB,EAAET,KAAK,CAAC;IACrD;EAAC;IAAAd,GAAA;IAAAC,KAAA,EAKD,SAAQU,kBAAkBA,CAACkB,OAAsB,EAAQ;MACvD,IAAIX,OAAO,EAAE;QACXC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE;UACvDC,MAAM,EAAEQ,OAAO,CAACtB,UAAU;UAC1BG,SAAS,EAAE,GAAGmB,OAAO,CAACnB,SAAS,IAAI;UACnCG,UAAU,EAAEgB,OAAO,CAAChB;QACtB,CAAC,CAAC;MACJ;MAEA,IAAI,CAACU,kBAAkB,CAAC,gBAAgB,EAAAO,MAAA,CAAAC,MAAA,KACnCF,OAAO;QACVb,SAAS,EAAEpB,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBH,SAAS,EAAE,IAAI,CAACH;MAAc,EAC/B,CAAC;IACJ;EAAC;IAAAS,GAAA;IAAAC,KAAA,EAKD,SAAA+B,mBAAmBA,CACjBC,QAAgB,EAChBC,OAAgB,EAChBC,QAA8B,EACxB;MACN,IAAMrB,KAAK,GAAG;QACZmB,QAAQ,EAARA,QAAQ;QACRC,OAAO,EAAPA,OAAO;QACPC,QAAQ,EAARA,QAAQ;QACRC,QAAQ,EAAExC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACJ,WAAW,CAACE,SAAS;QACjD0C,WAAW,EAAE,IAAI,CAAC5C,WAAW,CAACK,OAAO,CAACwC,MAAM;QAC5CtB,SAAS,EAAEpB,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBH,SAAS,EAAE,IAAI,CAACH;MAClB,CAAC;MAED,IAAI2B,OAAO,EAAE;QACXC,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEN,KAAK,CAAC;MAClE;MAEA,IAAI,CAACS,kBAAkB,CAAC,iBAAiB,EAAET,KAAK,CAAC;IACnD;EAAC;IAAAd,GAAA;IAAAC,KAAA,EAKD,SAAAsC,oBAAoBA,CAClBC,KAAa,EACbjC,UAAkB,EAClBC,MAA4B,EACtB;MACN,IAAMM,KAAK,GAAG;QACZ0B,KAAK,EAALA,KAAK;QACLjC,UAAU,EAAVA,UAAU;QACVC,MAAM,EAANA,MAAM;QACNQ,SAAS,EAAEpB,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBH,SAAS,EAAE,IAAI,CAACH;MAClB,CAAC;MAED,IAAI2B,OAAO,EAAE;QACXC,OAAO,CAACqB,KAAK,CAAC,iCAAiC,EAAE1B,KAAK,CAAC;MACzD;MAEA,IAAI,CAACS,kBAAkB,CAAC,kBAAkB,EAAET,KAAK,CAAC;IACpD;EAAC;IAAAd,GAAA;IAAAC,KAAA,EAKD,SAAAwC,cAAcA,CAAA,EAAmB;MAC/B,OAAAX,MAAA,CAAAC,MAAA,KACK,IAAI,CAACtC,WAAW;QACnBiD,aAAa,EAAE9C,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACJ,WAAW,CAACE;MAAS;IAE1D;EAAC;IAAAK,GAAA;IAAAC,KAAA,EAKD,SAAA0C,eAAeA,CAAClC,QAAkC,EAAQ;MAExD,IAAI,CAAChB,WAAW,CAACiD,aAAa,GAAG9C,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACJ,WAAW,CAACE,SAAS;MAGxE,IAAI,CAACJ,cAAc,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;MAC9C,IAAI,CAACC,WAAW,GAAG;QACjBC,SAAS,EAAE,IAAI,CAACH,cAAc;QAC9BI,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBC,OAAO,EAAE,EAAE;QACXW,QAAQ,EAARA;MACF,CAAC;MACD,IAAI,CAACpB,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,cAAc,GAAG,EAAE;MACxB,IAAI,CAACF,eAAe,GAAG,CAAC;MAExB,IAAI8B,OAAO,EAAE;QACXC,OAAO,CAACC,GAAG,CACT,gDAAgD,EAChD,IAAI,CAAC7B,cACP,CAAC;MACH;IACF;EAAC;IAAAS,GAAA;IAAAC,KAAA,EAKD,SAAA2C,kBAAkBA,CAAA,EAMhB;MAAA,IAAAC,qBAAA;MACA,IAAMC,IAAI,GAAG,IAAI,CAACL,cAAc,CAAC,CAAC;MAClC,IAAMM,WAAqC,GAAG,CAAC,CAAC;MAChD,IAAMC,YAAoC,GAAG,CAAC,CAAC;MAG/CF,IAAI,CAAChD,OAAO,CAACmD,OAAO,CAAC,UAAC5B,MAAM,EAAE6B,KAAK,EAAK;QACtC,IAAM3C,UAAU,GAAGc,MAAM,CAACd,UAAU;QAGpCyC,YAAY,CAACzC,UAAU,CAAC,GAAG,CAACyC,YAAY,CAACzC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;QAG9D,IAAI2C,KAAK,GAAGJ,IAAI,CAAChD,OAAO,CAACwC,MAAM,GAAG,CAAC,EAAE;UACnC,IAAM5B,SAAS,GAAGoC,IAAI,CAAChD,OAAO,CAACoD,KAAK,GAAG,CAAC,CAAC,CAAClC,SAAS,GAAGK,MAAM,CAACL,SAAS;UACtE,IAAI,CAAC+B,WAAW,CAACxC,UAAU,CAAC,EAAE;YAC5BwC,WAAW,CAACxC,UAAU,CAAC,GAAG,EAAE;UAC9B;UACAwC,WAAW,CAACxC,UAAU,CAAC,CAACU,IAAI,CAACP,SAAS,CAAC;QACzC;MACF,CAAC,CAAC;MAGF,IAAMyC,iBAAiB,GACrB,EAAAN,qBAAA,GAAAf,MAAM,CAACsB,OAAO,CAACJ,YAAY,CAAC,CAACK,IAAI,CAAC,UAAAC,IAAA,EAAAC,KAAA;QAAA,IAAAC,KAAA,OAAAC,eAAA,CAAAtE,OAAA,EAAAmE,IAAA;UAAII,CAAC,GAAAF,KAAA;QAAA,IAAAG,KAAA,OAAAF,eAAA,CAAAtE,OAAA,EAAAoE,KAAA;UAAMK,CAAC,GAAAD,KAAA;QAAA,OAAMC,CAAC,GAAGF,CAAC;MAAA,EAAC,CAAC,CAAC,CAAC,qBAA7Db,qBAAA,CAAgE,CAAC,CAAC,KAAI,EAAE;MAG1E,IAAMgB,QAAQ,GAAG/B,MAAM,CAACgC,MAAM,CAACf,WAAW,CAAC,CAACgB,IAAI,CAAC,CAAC;MAClD,IAAMC,iBAAiB,GACrBH,QAAQ,CAACvB,MAAM,GAAG,CAAC,GACfuB,QAAQ,CAACI,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI;QAAA,OAAKD,GAAG,GAAGC,IAAI;MAAA,GAAE,CAAC,CAAC,GAAGN,QAAQ,CAACvB,MAAM,GAC/D,CAAC;MAEP,OAAO;QACL8B,eAAe,EAAEtB,IAAI,CAACJ,aAAa,IAAI,CAAC;QACxCL,WAAW,EAAES,IAAI,CAAChD,OAAO,CAACwC,MAAM;QAChC0B,iBAAiB,EAAjBA,iBAAiB;QACjBb,iBAAiB,EAAjBA,iBAAiB;QACjBkB,iBAAiB,EAAEvB,IAAI,CAAChD,OAAO,CAACwE,GAAG,CAAC,UAAAC,CAAC;UAAA,OAAIA,CAAC,CAAChE,UAAU;QAAA;MACvD,CAAC;IACH;EAAC;IAAAP,GAAA;IAAAC,KAAA,EAKD,SAAQsB,kBAAkBA,CAACiD,SAAiB,EAAEC,IAAS,EAAQ;MAI7D,IAAIvD,OAAO,EAAE;QAEX;MACF;IAOF;EAAC;AAAA;AAII,IAAMwD,mBAAmB,GAAAC,OAAA,CAAAD,mBAAA,GAAG,IAAIzF,0BAA0B,CAAC,CAAC;AAAC,IAAA2F,QAAA,GAAAD,OAAA,CAAAxF,OAAA,GACrDuF,mBAAmB", "ignoreList": []}