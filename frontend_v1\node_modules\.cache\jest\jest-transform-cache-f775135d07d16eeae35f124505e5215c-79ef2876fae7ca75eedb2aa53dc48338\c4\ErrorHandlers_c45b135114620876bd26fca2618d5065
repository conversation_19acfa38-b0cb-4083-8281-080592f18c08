6878c192230f6108ac52cb09ee326a91
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.onCaughtError = onCaughtError;
exports.onRecoverableError = onRecoverableError;
exports.onUncaughtError = onUncaughtError;
var _ExceptionsManager = _interopRequireWildcard(require("../../../../Libraries/Core/ExceptionsManager"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
function getExtendedError(errorValue, errorInfo) {
  var error;
  if (errorValue instanceof Error) {
    error = errorValue;
  } else if (typeof errorValue === 'string') {
    error = new _ExceptionsManager.SyntheticError(errorValue);
  } else {
    error = new _ExceptionsManager.SyntheticError('Unspecified error');
  }
  try {
    error.componentStack = errorInfo.componentStack;
    error.isComponentError = true;
  } catch (_unused) {}
  return error;
}
function onUncaughtError(errorValue, errorInfo) {
  var error = getExtendedError(errorValue, errorInfo);
  _ExceptionsManager.default.handleException(error, true);
}
function onCaughtError(errorValue, errorInfo) {
  var error = getExtendedError(errorValue, errorInfo);
  _ExceptionsManager.default.handleException(error, false);
}
function onRecoverableError(errorValue, errorInfo) {
  var error = getExtendedError(errorValue, errorInfo);
  console.warn(error);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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