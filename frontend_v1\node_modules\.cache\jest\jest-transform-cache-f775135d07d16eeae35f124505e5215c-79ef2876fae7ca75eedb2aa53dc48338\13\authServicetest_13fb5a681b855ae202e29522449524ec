f5cba143c216ab24418fe77b9d943d4c
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _authService = require("../authService");
global.fetch = jest.fn();
describe('AuthService', function () {
  beforeEach(function () {
    jest.clearAllMocks();
  });
  describe('login', function () {
    it('should successfully login with valid credentials', (0, _asyncToGenerator2.default)(function* () {
      var mockResponse = {
        access: 'mock-access-token',
        refresh: 'mock-refresh-token',
        user: {
          id: '1',
          email: '<EMAIL>',
          first_name: 'John',
          last_name: '<PERSON>e',
          role: 'customer',
          is_verified: true
        }
      };
      fetch.mockResolvedValueOnce({
        ok: true,
        json: function () {
          var _json = (0, _asyncToGenerator2.default)(function* () {
            return mockResponse;
          });
          function json() {
            return _json.apply(this, arguments);
          }
          return json;
        }()
      });
      var result = yield _authService.authService.login({
        email: '<EMAIL>',
        password: 'password123'
      });
      expect(fetch).toHaveBeenCalledWith(expect.stringContaining('/api/auth/login/'), expect.objectContaining({
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      }));
      expect(result).toEqual(mockResponse);
    }));
    it('should handle login failure with error message', (0, _asyncToGenerator2.default)(function* () {
      var mockError = {
        detail: 'Invalid credentials'
      };
      fetch.mockResolvedValueOnce({
        ok: false,
        json: function () {
          var _json2 = (0, _asyncToGenerator2.default)(function* () {
            return mockError;
          });
          function json() {
            return _json2.apply(this, arguments);
          }
          return json;
        }()
      });
      yield expect(_authService.authService.login({
        email: '<EMAIL>',
        password: 'wrongpassword'
      })).rejects.toThrow('Invalid credentials');
    }));
    it('should handle network errors', (0, _asyncToGenerator2.default)(function* () {
      fetch.mockRejectedValueOnce(new Error('Network error'));
      yield expect(_authService.authService.login({
        email: '<EMAIL>',
        password: 'password123'
      })).rejects.toThrow('Network error');
    }));
  });
  describe('register', function () {
    it('should successfully register a new customer', (0, _asyncToGenerator2.default)(function* () {
      var mockResponse = {
        access: 'mock-access-token',
        refresh: 'mock-refresh-token',
        user: {
          id: '2',
          email: '<EMAIL>',
          first_name: 'Jane',
          last_name: 'Smith',
          role: 'customer',
          is_verified: false
        }
      };
      fetch.mockResolvedValueOnce({
        ok: true,
        json: function () {
          var _json3 = (0, _asyncToGenerator2.default)(function* () {
            return mockResponse;
          });
          function json() {
            return _json3.apply(this, arguments);
          }
          return json;
        }()
      });
      var result = yield _authService.authService.register({
        first_name: 'Jane',
        last_name: 'Smith',
        email: '<EMAIL>',
        password: 'password123',
        role: 'customer'
      });
      expect(fetch).toHaveBeenCalledWith(expect.stringContaining('/api/auth/register/'), expect.objectContaining({
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          first_name: 'Jane',
          last_name: 'Smith',
          email: '<EMAIL>',
          password: 'password123',
          role: 'customer'
        })
      }));
      expect(result).toEqual(mockResponse);
    }));
    it('should successfully register a new service provider', (0, _asyncToGenerator2.default)(function* () {
      var mockResponse = {
        access: 'mock-access-token',
        refresh: 'mock-refresh-token',
        user: {
          id: '3',
          email: '<EMAIL>',
          first_name: 'Bob',
          last_name: 'Johnson',
          role: 'service_provider',
          is_verified: false
        }
      };
      fetch.mockResolvedValueOnce({
        ok: true,
        json: function () {
          var _json4 = (0, _asyncToGenerator2.default)(function* () {
            return mockResponse;
          });
          function json() {
            return _json4.apply(this, arguments);
          }
          return json;
        }()
      });
      var result = yield _authService.authService.register({
        first_name: 'Bob',
        last_name: 'Johnson',
        email: '<EMAIL>',
        password: 'password123',
        role: 'service_provider'
      });
      expect(result.user.role).toBe('service_provider');
    }));
    it('should handle registration validation errors', (0, _asyncToGenerator2.default)(function* () {
      var mockError = {
        errors: {
          email: ['This email is already registered']
        }
      };
      fetch.mockResolvedValueOnce({
        ok: false,
        json: function () {
          var _json5 = (0, _asyncToGenerator2.default)(function* () {
            return mockError;
          });
          function json() {
            return _json5.apply(this, arguments);
          }
          return json;
        }()
      });
      yield expect(_authService.authService.register({
        first_name: 'Jane',
        last_name: 'Smith',
        email: '<EMAIL>',
        password: 'password123',
        role: 'customer'
      })).rejects.toThrow('This email is already registered');
    }));
  });
  describe('refreshToken', function () {
    it('should successfully refresh access token', (0, _asyncToGenerator2.default)(function* () {
      var mockResponse = {
        access: 'new-access-token'
      };
      fetch.mockResolvedValueOnce({
        ok: true,
        json: function () {
          var _json6 = (0, _asyncToGenerator2.default)(function* () {
            return mockResponse;
          });
          function json() {
            return _json6.apply(this, arguments);
          }
          return json;
        }()
      });
      var result = yield _authService.authService.refreshToken('refresh-token');
      expect(fetch).toHaveBeenCalledWith(expect.stringContaining('/api/auth/token/refresh/'), expect.objectContaining({
        method: 'POST',
        body: JSON.stringify({
          refresh: 'refresh-token'
        })
      }));
      expect(result).toEqual(mockResponse);
    }));
  });
  describe('logout', function () {
    it('should successfully logout', (0, _asyncToGenerator2.default)(function* () {
      fetch.mockResolvedValueOnce({
        ok: true,
        json: function () {
          var _json7 = (0, _asyncToGenerator2.default)(function* () {
            return {};
          });
          function json() {
            return _json7.apply(this, arguments);
          }
          return json;
        }()
      });
      yield expect(_authService.authService.logout('refresh-token')).resolves.not.toThrow();
      expect(fetch).toHaveBeenCalledWith(expect.stringContaining('/api/auth/logout/'), expect.objectContaining({
        method: 'POST',
        body: JSON.stringify({
          refresh: 'refresh-token'
        })
      }));
    }));
    it('should handle logout errors gracefully', (0, _asyncToGenerator2.default)(function* () {
      fetch.mockRejectedValueOnce(new Error('Network error'));
      yield expect(_authService.authService.logout('refresh-token')).resolves.not.toThrow();
    }));
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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