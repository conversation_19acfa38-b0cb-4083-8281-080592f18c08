/**
 * Provider Store Screen - Service Provider Store Management
 *
 * Component Contract:
 * - Allows service providers to manage their store profile and settings
 * - Provides store customization options following audit guidelines
 * - Integrates with portfolio management and service offerings
 * - Follows responsive design and accessibility standards
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
  RefreshControl,
} from 'react-native';

import { AccessibleImage } from '../components/accessibility/AccessibleImage';
import { FocusScrollManager } from '../components/accessibility/FocusScrollManager';
import { Box } from '../components/atoms/Box';
import { Button } from '../components/atoms/Button';
import { Card } from '../components/atoms/Card';
import { HeaderHelpButton } from '../components/help';
import { SafeAreaScreen } from '../components/ui/SafeAreaWrapper';
import { useTheme } from '../contexts/ThemeContext';
import { useAuthStore } from '../store/authSlice';
import { useUserStore } from '../store/userSlice';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
  getMinimumTouchTarget,
} from '../utils/responsiveUtils';

interface StoreData {
  businessName: string;
  description: string;
  isOpen: boolean;
  totalServices: number;
  activeServices: number;
  totalBookings: number;
  monthlyRevenue: number;
  averageRating: number;
  reviewCount: number;
  portfolioType: 'upload' | 'instagram';
  instagramConnected: boolean;
}

interface StoreMetric {
  id: string;
  title: string;
  value: string | number;
  icon: string;
  color: string;
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: string;
}

interface Service {
  id: string;
  name: string;
  category: string;
  price: number;
  duration: number;
  isActive: boolean;
  bookingsCount: number;
}

export const ProviderStoreScreen: React.FC = () => {
  const { colors } = useTheme();
  const navigation = useNavigation<any>();
  const { userRole } = useAuthStore();
  const { profile } = useUserStore();
  const styles = createStyles(colors);

  const [storeData, setStoreData] = useState<StoreData>({
    businessName: profile?.firstName
      ? `${profile.firstName}'s Beauty Studio`
      : 'My Beauty Studio',
    description: 'Professional beauty services with experienced stylists',
    isOpen: true,
    totalServices: 8,
    activeServices: 6,
    totalBookings: 127,
    monthlyRevenue: 2850,
    averageRating: 4.8,
    reviewCount: 89,
    portfolioType: 'instagram',
    instagramConnected: false,
  });

  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);

  // Mock services data
  const [services, setServices] = useState<Service[]>([
    {
      id: '1',
      name: 'Hair Cut & Style',
      category: 'Hair',
      price: 45,
      duration: 60,
      isActive: true,
      bookingsCount: 23,
    },
    {
      id: '2',
      name: 'Hair Color',
      category: 'Hair',
      price: 85,
      duration: 120,
      isActive: true,
      bookingsCount: 18,
    },
    {
      id: '3',
      name: 'Manicure',
      category: 'Nails',
      price: 35,
      duration: 45,
      isActive: true,
      bookingsCount: 31,
    },
    {
      id: '4',
      name: 'Facial Treatment',
      category: 'Skincare',
      price: 65,
      duration: 75,
      isActive: false,
      bookingsCount: 12,
    },
  ]);

  // Store metrics for dashboard
  const storeMetrics: StoreMetric[] = [
    {
      id: 'revenue',
      title: 'Monthly Revenue',
      value: `$${storeData.monthlyRevenue.toLocaleString()}`,
      icon: 'trending-up',
      color: colors.sage400,
      trend: 'up',
      trendValue: '+12%',
    },
    {
      id: 'bookings',
      title: 'Total Bookings',
      value: storeData.totalBookings,
      icon: 'calendar',
      color: colors.sage400,
      trend: 'up',
      trendValue: '+8%',
    },
    {
      id: 'rating',
      title: 'Average Rating',
      value: storeData.averageRating ? storeData.averageRating.toFixed(1) : '0.0',
      icon: 'star',
      color: '#FFD700',
      trend: 'neutral',
    },
    {
      id: 'services',
      title: 'Active Services',
      value: `${storeData.activeServices}/${storeData.totalServices}`,
      icon: 'list',
      color: colors.sage400,
    },
  ];

  useEffect(() => {
    loadStoreData();
  }, []);

  const loadStoreData = async () => {
    setLoading(true);
    try {
      // TODO: Implement API call to load store data
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
    } catch (error) {
      console.error('Error loading store data:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadStoreData();
    setRefreshing(false);
  };

  const handleToggleStoreStatus = () => {
    Alert.alert(
      storeData.isOpen ? 'Close Store' : 'Open Store',
      `Are you sure you want to ${storeData.isOpen ? 'close' : 'open'} your store?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Confirm',
          onPress: () => {
            setStoreData(prev => ({ ...prev, isOpen: !prev.isOpen }));
          },
        },
      ],
    );
  };

  const handleEditStore = () => {
    // Navigate to store customization screen
    navigation.navigate('StoreCustomization');
  };

  const handleManageServices = () => {
    // Navigate to service editor for creating new services
    navigation.navigate('ServiceEditor', { mode: 'create' });
  };

  const handleViewPortfolio = () => {
    // Navigate to portfolio management screen
    Alert.alert(
      'Portfolio Management',
      'Portfolio management functionality coming soon! This will allow you to manage your work gallery and Instagram integration.',
    );
  };

  const handleEditService = (serviceId: string) => {
    // Navigate to service editor for editing
    navigation.navigate('ServiceEditor', { mode: 'edit', serviceId });
  };

  const handleToggleService = (serviceId: string) => {
    setServices(prev =>
      prev.map(service =>
        service.id === serviceId
          ? { ...service, isActive: !service.isActive }
          : service,
      ),
    );
  };

  const handleViewAllServices = () => {
    // Navigate to full services list
    Alert.alert(
      'Services Management',
      'Full services management screen coming soon!',
    );
  };

  const handleConnectInstagram = () => {
    Alert.alert(
      'Connect Instagram',
      'Connect your Instagram account to automatically sync your portfolio.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Connect',
          onPress: () => {
            setStoreData(prev => ({ ...prev, instagramConnected: true }));
          },
        },
      ],
    );
  };

  const renderMetricCard = (metric: StoreMetric) => (
    <Card key={metric.id} style={styles.metricCard}>
      <View style={styles.metricHeader}>
        <Ionicons name={metric.icon as any} size={24} color={metric.color} />
        {metric.trend && (
          <View
            style={[
              styles.trendBadge,
              styles[
                `trend${metric.trend.charAt(0).toUpperCase() + metric.trend.slice(1)}`
              ],
            ]}>
            <Text style={styles.trendText}>{metric.trendValue}</Text>
          </View>
        )}
      </View>
      <Text style={styles.metricValue}>{metric.value}</Text>
      <Text style={styles.metricTitle}>{metric.title}</Text>
    </Card>
  );

  const renderQuickActions = () => (
    <Card style={styles.actionsCard}>
      <Text style={styles.sectionTitle}>Quick Actions</Text>

      <TouchableOpacity
        style={styles.actionItem}
        onPress={handleEditStore}
        accessibilityLabel="Edit store profile"
        accessibilityHint="Opens store profile editing screen">
        <View style={styles.actionLeft}>
          <Ionicons
            name="storefront-outline"
            size={24}
            color={colors.sage400}
          />
          <Text style={styles.actionText}>Edit Store Profile</Text>
        </View>
        <Ionicons name="chevron-forward" size={20} color={colors.sage300} />
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.actionItem}
        onPress={handleManageServices}
        accessibilityLabel="Add new service"
        accessibilityHint="Opens service creation screen">
        <View style={styles.actionLeft}>
          <Ionicons
            name="add-circle-outline"
            size={24}
            color={colors.sage400}
          />
          <Text style={styles.actionText}>Add New Service</Text>
        </View>
        <Ionicons name="chevron-forward" size={20} color={colors.sage300} />
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.actionItem}
        onPress={handleViewPortfolio}
        accessibilityLabel="View portfolio"
        accessibilityHint="Opens portfolio management screen">
        <View style={styles.actionLeft}>
          <Ionicons name="images-outline" size={24} color={colors.sage400} />
          <Text style={styles.actionText}>Portfolio & Gallery</Text>
        </View>
        <Ionicons name="chevron-forward" size={20} color={colors.sage300} />
      </TouchableOpacity>
    </Card>
  );

  const renderServicesSection = () => (
    <Card style={styles.servicesCard}>
      <View style={styles.servicesHeader}>
        <Text style={styles.sectionTitle}>My Services</Text>
        <TouchableOpacity
          onPress={handleViewAllServices}
          accessibilityLabel="View all services"
          accessibilityHint="Opens full services management screen">
          <Text style={styles.viewAllText}>View All</Text>
        </TouchableOpacity>
      </View>

      {services.slice(0, 3).map(service => (
        <TouchableOpacity
          key={service.id}
          style={styles.serviceItem}
          onPress={() => handleEditService(service.id)}
          accessibilityLabel={`Edit ${service.name}`}
          accessibilityHint="Opens service editor">
          <View style={styles.serviceLeft}>
            <View style={styles.serviceInfo}>
              <Text style={styles.serviceName}>{service.name}</Text>
              <Text style={styles.serviceDetails}>
                ${service.price} • {service.duration}min •{' '}
                {service.bookingsCount} bookings
              </Text>
            </View>
          </View>
          <View style={styles.serviceRight}>
            <TouchableOpacity
              style={[
                styles.statusBadge,
                service.isActive ? styles.activeBadge : styles.inactiveBadge,
              ]}
              onPress={() => handleToggleService(service.id)}
              accessibilityLabel={`${service.isActive ? 'Deactivate' : 'Activate'} ${service.name}`}>
              <Text
                style={[
                  styles.statusText,
                  service.isActive ? styles.activeText : styles.inactiveText,
                ]}>
                {service.isActive ? 'Active' : 'Inactive'}
              </Text>
            </TouchableOpacity>
            <Ionicons name="chevron-forward" size={20} color={colors.sage300} />
          </View>
        </TouchableOpacity>
      ))}
    </Card>
  );

  return (
    <SafeAreaScreen style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.headerTitle}>My Store</Text>
          <Text style={styles.headerSubtitle}>{storeData.businessName}</Text>
        </View>
        <View style={styles.headerRight}>
          <HeaderHelpButton />
        </View>
      </View>

      <FocusScrollManager
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
        stickyFooterHeight={80} // Account for tab bar
        focusPadding={20}>
        {/* Store Status */}
        <Card style={styles.statusCard}>
          <View style={styles.statusHeader}>
            <View style={styles.statusLeft}>
              <View
                style={[
                  styles.statusIndicator,
                  storeData.isOpen
                    ? styles.openIndicator
                    : styles.closedIndicator,
                ]}
              />
              <Text style={styles.statusText}>
                Store is {storeData.isOpen ? 'Open' : 'Closed'}
              </Text>
            </View>
            <Button
              title={storeData.isOpen ? 'Close Store' : 'Open Store'}
              onPress={handleToggleStoreStatus}
              variant="outline"
              size="small"
            />
          </View>
        </Card>

        {/* Metrics Grid */}
        <View style={styles.metricsGrid}>
          {storeMetrics.map(renderMetricCard)}
        </View>

        {/* Quick Actions */}
        {renderQuickActions()}

        {/* Services Section */}
        {renderServicesSection()}

        {/* Instagram Integration */}
        <Card style={styles.instagramCard}>
          <View style={styles.instagramHeader}>
            <View style={styles.instagramLeft}>
              <Ionicons name="logo-instagram" size={24} color="#E4405F" />
              <Text style={styles.instagramTitle}>Instagram Portfolio</Text>
            </View>
            {!storeData.instagramConnected && (
              <Button
                title="Connect"
                onPress={handleConnectInstagram}
                variant="outline"
                size="small"
              />
            )}
          </View>
          <Text style={styles.instagramDescription}>
            {storeData.instagramConnected
              ? 'Your Instagram posts are automatically synced to your portfolio.'
              : 'Connect your Instagram account to showcase your work automatically.'}
          </Text>
        </Card>
      </FocusScrollManager>
    </SafeAreaScreen>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background?.primary || '#FFFFFF',
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: getResponsiveSpacing(20),
      paddingVertical: getResponsiveSpacing(16),
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.sage100,
    },
    headerLeft: {
      flex: 1,
    },
    headerTitle: {
      fontSize: getResponsiveFontSize(24),
      fontWeight: '700',
      color: colors.text,
      marginBottom: 4,
    },
    headerSubtitle: {
      fontSize: getResponsiveFontSize(14),
      color: colors.sage400,
    },
    headerRight: {
      marginLeft: getResponsiveSpacing(16),
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      padding: getResponsiveSpacing(20),
      paddingBottom: getResponsiveSpacing(100), // Account for bottom navigation
    },
    statusCard: {
      marginBottom: getResponsiveSpacing(20),
    },
    statusHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    statusLeft: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    statusIndicator: {
      width: 12,
      height: 12,
      borderRadius: 6,
      marginRight: getResponsiveSpacing(12),
    },
    openIndicator: {
      backgroundColor: '#4CAF50',
    },
    closedIndicator: {
      backgroundColor: '#F44336',
    },
    statusText: {
      fontSize: getResponsiveFontSize(16),
      fontWeight: '600',
      color: colors.text,
    },
    metricsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginHorizontal: -getResponsiveSpacing(8),
      marginBottom: getResponsiveSpacing(20),
    },
    metricCard: {
      width: '48%',
      marginHorizontal: '1%',
      marginBottom: getResponsiveSpacing(16),
      padding: getResponsiveSpacing(16),
    },
    metricHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: getResponsiveSpacing(12),
    },
    trendBadge: {
      paddingHorizontal: getResponsiveSpacing(8),
      paddingVertical: getResponsiveSpacing(4),
      borderRadius: 12,
    },
    trendUp: {
      backgroundColor: '#E8F5E8',
    },
    trendDown: {
      backgroundColor: '#FFEBEE',
    },
    trendNeutral: {
      backgroundColor: colors.sage100,
    },
    trendText: {
      fontSize: getResponsiveFontSize(12),
      fontWeight: '600',
      color: colors.sage600,
    },
    metricValue: {
      fontSize: getResponsiveFontSize(24),
      fontWeight: '700',
      color: colors.text,
      marginBottom: getResponsiveSpacing(4),
    },
    metricTitle: {
      fontSize: getResponsiveFontSize(14),
      color: colors.sage400,
    },
    actionsCard: {
      marginBottom: getResponsiveSpacing(20),
    },
    sectionTitle: {
      fontSize: getResponsiveFontSize(18),
      fontWeight: '600',
      color: colors.text,
      marginBottom: getResponsiveSpacing(16),
    },
    servicesCard: {
      marginBottom: getResponsiveSpacing(20),
    },
    servicesHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: getResponsiveSpacing(16),
    },
    viewAllText: {
      fontSize: getResponsiveFontSize(14),
      color: colors.sage400,
      fontWeight: '600',
    },
    serviceItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: getResponsiveSpacing(12),
      borderBottomWidth: 1,
      borderBottomColor: colors.sage100,
      minHeight: getMinimumTouchTarget(),
    },
    serviceLeft: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
    },
    serviceInfo: {
      flex: 1,
    },
    serviceName: {
      fontSize: getResponsiveFontSize(16),
      fontWeight: '600',
      color: colors.text,
      marginBottom: 4,
    },
    serviceDetails: {
      fontSize: getResponsiveFontSize(14),
      color: colors.sage400,
    },
    serviceRight: {
      flexDirection: 'row',
      alignItems: 'center',
      marginLeft: getResponsiveSpacing(12),
    },
    statusBadge: {
      paddingHorizontal: getResponsiveSpacing(12),
      paddingVertical: getResponsiveSpacing(6),
      borderRadius: 16,
      marginRight: getResponsiveSpacing(8),
    },
    activeBadge: {
      backgroundColor: '#E8F5E8',
    },
    inactiveBadge: {
      backgroundColor: '#FFEBEE',
    },
    statusText: {
      fontSize: getResponsiveFontSize(12),
      fontWeight: '600',
    },
    activeText: {
      color: '#4CAF50',
    },
    inactiveText: {
      color: '#F44336',
    },
    actionItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: getResponsiveSpacing(16),
      borderBottomWidth: 1,
      borderBottomColor: colors.sage100,
      minHeight: getMinimumTouchTarget(),
    },
    actionLeft: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    actionText: {
      fontSize: getResponsiveFontSize(16),
      color: colors.text,
      marginLeft: getResponsiveSpacing(12),
    },
    instagramCard: {
      marginBottom: getResponsiveSpacing(20),
    },
    instagramHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: getResponsiveSpacing(12),
    },
    instagramLeft: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    instagramTitle: {
      fontSize: getResponsiveFontSize(16),
      fontWeight: '600',
      color: colors.text,
      marginLeft: getResponsiveSpacing(12),
    },
    instagramDescription: {
      fontSize: getResponsiveFontSize(14),
      color: colors.sage400,
      lineHeight: 20,
    },
  });
