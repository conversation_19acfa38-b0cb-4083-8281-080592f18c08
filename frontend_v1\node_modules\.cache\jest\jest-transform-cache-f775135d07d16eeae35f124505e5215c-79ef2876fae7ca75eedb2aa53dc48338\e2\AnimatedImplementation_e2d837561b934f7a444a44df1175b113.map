{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_AnimatedEvent", "_DecayAnimation", "_SpringAnimation", "_TimingAnimation", "_createAnimatedComponent", "_AnimatedAddition", "_AnimatedColor", "_AnimatedDiffClamp", "_AnimatedDivision", "_AnimatedInterpolation", "_AnimatedModulo", "_AnimatedMultiplication", "_AnimatedNode", "_AnimatedSubtraction", "_AnimatedTracking", "_AnimatedValue", "_AnimatedValueXY", "add", "a", "b", "AnimatedAddition", "subtract", "AnimatedSubtraction", "divide", "AnimatedDivision", "multiply", "AnimatedMultiplication", "modulo", "modulus", "AnimatedModulo", "diffClamp", "min", "max", "AnimatedDiffClamp", "_combineCallbacks", "callback", "config", "onComplete", "apply", "arguments", "maybeVectorAnim", "anim", "AnimatedValueXY", "configX", "assign", "configY", "key", "_config$key", "x", "y", "undefined", "aX", "aY", "parallel", "stopTogether", "AnimatedColor", "configR", "configG", "configB", "configA", "_config$_key", "r", "g", "aR", "aG", "aB", "aA", "spring", "start", "animatedValue", "configuration", "singleValue", "singleConfig", "stopTracking", "toValue", "AnimatedNode", "track", "AnimatedTracking", "SpringAnimation", "animate", "stop", "stopAnimation", "reset", "resetAnimation", "_startNativeLoop", "iterations", "_isUsingNativeDriver", "useNativeDriver", "timing", "TimingAnimation", "isLooping", "decay", "DecayAnimation", "sequence", "animations", "current", "result", "finished", "length", "for<PERSON>ach", "animation", "idx", "Error", "doneCount", "hasEnded", "cb", "endResult", "delay", "time", "AnimatedValue", "duration", "stagger", "map", "i", "loop", "_ref", "_ref$iterations", "_ref$resetBeforeItera", "resetBeforeIteration", "isFinished", "iterationsSoFar", "restart", "forkEvent", "event", "listener", "AnimatedEvent", "__addListener", "unforkEvent", "__removeListener", "arg<PERSON><PERSON><PERSON>", "animatedEvent", "__isNative", "__<PERSON><PERSON><PERSON><PERSON>", "_default", "Value", "ValueXY", "Color", "Interpolation", "AnimatedInterpolation", "Node", "createAnimatedComponent", "attachNativeEvent", "Event"], "sources": ["AnimatedImplementation.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n */\n\n'use strict';\n\nimport type {EventConfig, Mapping} from './AnimatedEvent';\nimport type {\n  AnimationConfig,\n  EndCallback,\n  EndResult,\n} from './animations/Animation';\nimport type {DecayAnimationConfig} from './animations/DecayAnimation';\nimport type {SpringAnimationConfig} from './animations/SpringAnimation';\nimport type {TimingAnimationConfig} from './animations/TimingAnimation';\n\nimport {AnimatedEvent, attachNativeEvent} from './AnimatedEvent';\nimport DecayAnimation from './animations/DecayAnimation';\nimport SpringAnimation from './animations/SpringAnimation';\nimport TimingAnimation from './animations/TimingAnimation';\nimport createAnimatedComponent from './createAnimatedComponent';\nimport AnimatedAddition from './nodes/AnimatedAddition';\nimport AnimatedColor from './nodes/AnimatedColor';\nimport AnimatedDiffClamp from './nodes/AnimatedDiffClamp';\nimport AnimatedDivision from './nodes/AnimatedDivision';\nimport AnimatedInterpolation from './nodes/AnimatedInterpolation';\nimport AnimatedModulo from './nodes/AnimatedModulo';\nimport AnimatedMultiplication from './nodes/AnimatedMultiplication';\nimport AnimatedNode from './nodes/AnimatedNode';\nimport AnimatedSubtraction from './nodes/AnimatedSubtraction';\nimport AnimatedTracking from './nodes/AnimatedTracking';\nimport AnimatedValue from './nodes/AnimatedValue';\nimport AnimatedValueXY from './nodes/AnimatedValueXY';\n\nexport type CompositeAnimation = {\n  start: (callback?: ?EndCallback, isLooping?: boolean) => void,\n  stop: () => void,\n  reset: () => void,\n  _startNativeLoop: (iterations?: number) => void,\n  _isUsingNativeDriver: () => boolean,\n  ...\n};\n\nconst add = function (\n  a: AnimatedNode | number,\n  b: AnimatedNode | number,\n): AnimatedAddition {\n  return new AnimatedAddition(a, b);\n};\n\nconst subtract = function (\n  a: AnimatedNode | number,\n  b: AnimatedNode | number,\n): AnimatedSubtraction {\n  return new AnimatedSubtraction(a, b);\n};\n\nconst divide = function (\n  a: AnimatedNode | number,\n  b: AnimatedNode | number,\n): AnimatedDivision {\n  return new AnimatedDivision(a, b);\n};\n\nconst multiply = function (\n  a: AnimatedNode | number,\n  b: AnimatedNode | number,\n): AnimatedMultiplication {\n  return new AnimatedMultiplication(a, b);\n};\n\nconst modulo = function (a: AnimatedNode, modulus: number): AnimatedModulo {\n  return new AnimatedModulo(a, modulus);\n};\n\nconst diffClamp = function (\n  a: AnimatedNode,\n  min: number,\n  max: number,\n): AnimatedDiffClamp {\n  return new AnimatedDiffClamp(a, min, max);\n};\n\nconst _combineCallbacks = function (\n  callback: ?EndCallback,\n  config: $ReadOnly<{...AnimationConfig, ...}>,\n) {\n  if (callback && config.onComplete) {\n    return (...args: Array<EndResult>) => {\n      config.onComplete && config.onComplete(...args);\n      callback && callback(...args);\n    };\n  } else {\n    return callback || config.onComplete;\n  }\n};\n\nconst maybeVectorAnim = function (\n  value: AnimatedValue | AnimatedValueXY | AnimatedColor,\n  config: Object,\n  anim: (value: AnimatedValue, config: Object) => CompositeAnimation,\n): ?CompositeAnimation {\n  if (value instanceof AnimatedValueXY) {\n    const configX = {...config};\n    const configY = {...config};\n    for (const key in config) {\n      const {x, y} = config[key];\n      if (x !== undefined && y !== undefined) {\n        configX[key] = x;\n        configY[key] = y;\n      }\n    }\n    const aX = anim((value: AnimatedValueXY).x, configX);\n    const aY = anim((value: AnimatedValueXY).y, configY);\n    // We use `stopTogether: false` here because otherwise tracking will break\n    // because the second animation will get stopped before it can update.\n    return parallel([aX, aY], {stopTogether: false});\n  } else if (value instanceof AnimatedColor) {\n    const configR = {...config};\n    const configG = {...config};\n    const configB = {...config};\n    const configA = {...config};\n    for (const key in config) {\n      const {r, g, b, a} = config[key];\n      if (\n        r !== undefined &&\n        g !== undefined &&\n        b !== undefined &&\n        a !== undefined\n      ) {\n        configR[key] = r;\n        configG[key] = g;\n        configB[key] = b;\n        configA[key] = a;\n      }\n    }\n    const aR = anim((value: AnimatedColor).r, configR);\n    const aG = anim((value: AnimatedColor).g, configG);\n    const aB = anim((value: AnimatedColor).b, configB);\n    const aA = anim((value: AnimatedColor).a, configA);\n    // We use `stopTogether: false` here because otherwise tracking will break\n    // because the second animation will get stopped before it can update.\n    return parallel([aR, aG, aB, aA], {stopTogether: false});\n  }\n  return null;\n};\n\nconst spring = function (\n  value: AnimatedValue | AnimatedValueXY | AnimatedColor,\n  config: SpringAnimationConfig,\n): CompositeAnimation {\n  const start = function (\n    animatedValue: AnimatedValue | AnimatedValueXY | AnimatedColor,\n    configuration: SpringAnimationConfig,\n    callback?: ?EndCallback,\n  ): void {\n    callback = _combineCallbacks(callback, configuration);\n    const singleValue: any = animatedValue;\n    const singleConfig: any = configuration;\n    singleValue.stopTracking();\n    if (configuration.toValue instanceof AnimatedNode) {\n      singleValue.track(\n        new AnimatedTracking(\n          singleValue,\n          configuration.toValue,\n          SpringAnimation,\n          singleConfig,\n          callback,\n        ),\n      );\n    } else {\n      singleValue.animate(new SpringAnimation(singleConfig), callback);\n    }\n  };\n  return (\n    maybeVectorAnim(value, config, spring) || {\n      start: function (callback?: ?EndCallback): void {\n        start(value, config, callback);\n      },\n\n      stop: function (): void {\n        value.stopAnimation();\n      },\n\n      reset: function (): void {\n        value.resetAnimation();\n      },\n\n      _startNativeLoop: function (iterations?: number): void {\n        const singleConfig = {...config, iterations};\n        start(value, singleConfig);\n      },\n\n      _isUsingNativeDriver: function (): boolean {\n        return config.useNativeDriver || false;\n      },\n    }\n  );\n};\n\nconst timing = function (\n  value: AnimatedValue | AnimatedValueXY | AnimatedColor,\n  config: TimingAnimationConfig,\n): CompositeAnimation {\n  const start = function (\n    animatedValue: AnimatedValue | AnimatedValueXY | AnimatedColor,\n    configuration: TimingAnimationConfig,\n    callback?: ?EndCallback,\n  ): void {\n    callback = _combineCallbacks(callback, configuration);\n    const singleValue: any = animatedValue;\n    const singleConfig: any = configuration;\n    singleValue.stopTracking();\n    if (configuration.toValue instanceof AnimatedNode) {\n      singleValue.track(\n        new AnimatedTracking(\n          singleValue,\n          configuration.toValue,\n          TimingAnimation,\n          singleConfig,\n          callback,\n        ),\n      );\n    } else {\n      singleValue.animate(new TimingAnimation(singleConfig), callback);\n    }\n  };\n\n  return (\n    maybeVectorAnim(value, config, timing) || {\n      start: function (callback?: ?EndCallback, isLooping?: boolean): void {\n        start(value, {...config, isLooping}, callback);\n      },\n\n      stop: function (): void {\n        value.stopAnimation();\n      },\n\n      reset: function (): void {\n        value.resetAnimation();\n      },\n\n      _startNativeLoop: function (iterations?: number): void {\n        const singleConfig = {...config, iterations};\n        start(value, singleConfig);\n      },\n\n      _isUsingNativeDriver: function (): boolean {\n        return config.useNativeDriver || false;\n      },\n    }\n  );\n};\n\nconst decay = function (\n  value: AnimatedValue | AnimatedValueXY | AnimatedColor,\n  config: DecayAnimationConfig,\n): CompositeAnimation {\n  const start = function (\n    animatedValue: AnimatedValue | AnimatedValueXY | AnimatedColor,\n    configuration: DecayAnimationConfig,\n    callback?: ?EndCallback,\n  ): void {\n    callback = _combineCallbacks(callback, configuration);\n    const singleValue: any = animatedValue;\n    const singleConfig: any = configuration;\n    singleValue.stopTracking();\n    singleValue.animate(new DecayAnimation(singleConfig), callback);\n  };\n\n  return (\n    maybeVectorAnim(value, config, decay) || {\n      start: function (callback?: ?EndCallback): void {\n        start(value, config, callback);\n      },\n\n      stop: function (): void {\n        value.stopAnimation();\n      },\n\n      reset: function (): void {\n        value.resetAnimation();\n      },\n\n      _startNativeLoop: function (iterations?: number): void {\n        const singleConfig = {...config, iterations};\n        start(value, singleConfig);\n      },\n\n      _isUsingNativeDriver: function (): boolean {\n        return config.useNativeDriver || false;\n      },\n    }\n  );\n};\n\nconst sequence = function (\n  animations: Array<CompositeAnimation>,\n): CompositeAnimation {\n  let current = 0;\n  return {\n    start: function (callback?: ?EndCallback, isLooping?: boolean) {\n      const onComplete = function (result: EndResult) {\n        if (!result.finished) {\n          callback && callback(result);\n          return;\n        }\n\n        current++;\n\n        if (current === animations.length) {\n          // if the start is called, even without a reset, it should start from the beginning\n          current = 0;\n          callback && callback(result);\n          return;\n        }\n\n        animations[current].start(onComplete, isLooping);\n      };\n\n      if (animations.length === 0) {\n        callback && callback({finished: true});\n      } else {\n        animations[current].start(onComplete, isLooping);\n      }\n    },\n\n    stop: function () {\n      if (current < animations.length) {\n        animations[current].stop();\n      }\n    },\n\n    reset: function () {\n      animations.forEach((animation, idx) => {\n        if (idx <= current) {\n          animation.reset();\n        }\n      });\n      current = 0;\n    },\n\n    _startNativeLoop: function () {\n      throw new Error(\n        'Loops run using the native driver cannot contain Animated.sequence animations',\n      );\n    },\n\n    _isUsingNativeDriver: function (): boolean {\n      return false;\n    },\n  };\n};\n\ntype ParallelConfig = {\n  // If one is stopped, stop all.  default: true\n  stopTogether?: boolean,\n  ...\n};\nconst parallel = function (\n  animations: Array<CompositeAnimation>,\n  config?: ?ParallelConfig,\n): CompositeAnimation {\n  let doneCount = 0;\n  // Make sure we only call stop() at most once for each animation\n  const hasEnded: {[number]: boolean} = {};\n  const stopTogether = !(config && config.stopTogether === false);\n\n  const result = {\n    start: function (callback?: ?EndCallback, isLooping?: boolean) {\n      if (doneCount === animations.length) {\n        callback && callback({finished: true});\n        return;\n      }\n\n      animations.forEach((animation, idx) => {\n        const cb = function (endResult: EndResult) {\n          hasEnded[idx] = true;\n          doneCount++;\n          if (doneCount === animations.length) {\n            doneCount = 0;\n            callback && callback(endResult);\n            return;\n          }\n\n          if (!endResult.finished && stopTogether) {\n            result.stop();\n          }\n        };\n\n        if (!animation) {\n          cb({finished: true});\n        } else {\n          animation.start(cb, isLooping);\n        }\n      });\n    },\n\n    stop: function (): void {\n      animations.forEach((animation, idx) => {\n        !hasEnded[idx] && animation.stop();\n        hasEnded[idx] = true;\n      });\n    },\n\n    reset: function (): void {\n      animations.forEach((animation, idx) => {\n        animation.reset();\n        hasEnded[idx] = false;\n        doneCount = 0;\n      });\n    },\n\n    _startNativeLoop: function (): empty {\n      throw new Error(\n        'Loops run using the native driver cannot contain Animated.parallel animations',\n      );\n    },\n\n    _isUsingNativeDriver: function (): boolean {\n      return false;\n    },\n  };\n\n  return result;\n};\n\nconst delay = function (time: number): CompositeAnimation {\n  // Would be nice to make a specialized implementation\n  return timing(new AnimatedValue(0), {\n    toValue: 0,\n    delay: time,\n    duration: 0,\n    useNativeDriver: false,\n  });\n};\n\nconst stagger = function (\n  time: number,\n  animations: Array<CompositeAnimation>,\n): CompositeAnimation {\n  return parallel(\n    animations.map((animation, i) => {\n      return sequence([delay(time * i), animation]);\n    }),\n  );\n};\n\ntype LoopAnimationConfig = {\n  iterations: number,\n  resetBeforeIteration?: boolean,\n  ...\n};\n\nconst loop = function (\n  animation: CompositeAnimation,\n  // $FlowFixMe[prop-missing]\n  {iterations = -1, resetBeforeIteration = true}: LoopAnimationConfig = {},\n): CompositeAnimation {\n  let isFinished = false;\n  let iterationsSoFar = 0;\n  return {\n    start: function (callback?: ?EndCallback) {\n      const restart = function (result: EndResult = {finished: true}): void {\n        if (\n          isFinished ||\n          iterationsSoFar === iterations ||\n          result.finished === false\n        ) {\n          callback && callback(result);\n        } else {\n          iterationsSoFar++;\n          resetBeforeIteration && animation.reset();\n          animation.start(restart, iterations === -1);\n        }\n      };\n      if (!animation || iterations === 0) {\n        callback && callback({finished: true});\n      } else {\n        if (animation._isUsingNativeDriver()) {\n          animation._startNativeLoop(iterations);\n        } else {\n          restart(); // Start looping recursively on the js thread\n        }\n      }\n    },\n\n    stop: function (): void {\n      isFinished = true;\n      animation.stop();\n    },\n\n    reset: function (): void {\n      iterationsSoFar = 0;\n      isFinished = false;\n      animation.reset();\n    },\n\n    _startNativeLoop: function () {\n      throw new Error(\n        'Loops run using the native driver cannot contain Animated.loop animations',\n      );\n    },\n\n    _isUsingNativeDriver: function (): boolean {\n      return animation._isUsingNativeDriver();\n    },\n  };\n};\n\nfunction forkEvent(\n  event: ?AnimatedEvent | ?Function,\n  listener: Function,\n): AnimatedEvent | Function {\n  if (!event) {\n    return listener;\n  } else if (event instanceof AnimatedEvent) {\n    event.__addListener(listener);\n    return event;\n  } else {\n    return (...args) => {\n      typeof event === 'function' && event(...args);\n      listener(...args);\n    };\n  }\n}\n\nfunction unforkEvent(\n  event: ?AnimatedEvent | ?Function,\n  listener: Function,\n): void {\n  if (event && event instanceof AnimatedEvent) {\n    event.__removeListener(listener);\n  }\n}\n\nconst event = function (\n  argMapping: $ReadOnlyArray<?Mapping>,\n  config: EventConfig,\n): any {\n  const animatedEvent = new AnimatedEvent(argMapping, config);\n  if (animatedEvent.__isNative) {\n    return animatedEvent;\n  } else {\n    return animatedEvent.__getHandler();\n  }\n};\n\n// All types of animated nodes that represent scalar numbers and can be interpolated (etc)\ntype AnimatedNumeric =\n  | AnimatedAddition\n  | AnimatedDiffClamp\n  | AnimatedDivision\n  | AnimatedInterpolation<number>\n  | AnimatedModulo\n  | AnimatedMultiplication\n  | AnimatedSubtraction\n  | AnimatedValue;\n\nexport type {AnimatedNumeric as Numeric};\n\n/**\n * The `Animated` library is designed to make animations fluid, powerful, and\n * easy to build and maintain. `Animated` focuses on declarative relationships\n * between inputs and outputs, with configurable transforms in between, and\n * simple `start`/`stop` methods to control time-based animation execution.\n * If additional transforms are added, be sure to include them in\n * AnimatedMock.js as well.\n *\n * See https://reactnative.dev/docs/animated\n */\nexport default {\n  /**\n   * Standard value class for driving animations.  Typically initialized with\n   * `new Animated.Value(0);`\n   *\n   * See https://reactnative.dev/docs/animated#value\n   */\n  Value: AnimatedValue,\n  /**\n   * 2D value class for driving 2D animations, such as pan gestures.\n   *\n   * See https://reactnative.dev/docs/animatedvaluexy\n   */\n  ValueXY: AnimatedValueXY,\n  /**\n   * Value class for driving color animations.\n   */\n  Color: AnimatedColor,\n  /**\n   * Exported to use the Interpolation type in flow.\n   *\n   * See https://reactnative.dev/docs/animated#interpolation\n   */\n  Interpolation: AnimatedInterpolation,\n  /**\n   * Exported for ease of type checking. All animated values derive from this\n   * class.\n   *\n   * See https://reactnative.dev/docs/animated#node\n   */\n  Node: AnimatedNode,\n\n  /**\n   * Animates a value from an initial velocity to zero based on a decay\n   * coefficient.\n   *\n   * See https://reactnative.dev/docs/animated#decay\n   */\n  decay,\n  /**\n   * Animates a value along a timed easing curve. The Easing module has tons of\n   * predefined curves, or you can use your own function.\n   *\n   * See https://reactnative.dev/docs/animated#timing\n   */\n  timing,\n  /**\n   * Animates a value according to an analytical spring model based on\n   * damped harmonic oscillation.\n   *\n   * See https://reactnative.dev/docs/animated#spring\n   */\n  spring,\n\n  /**\n   * Creates a new Animated value composed from two Animated values added\n   * together.\n   *\n   * See https://reactnative.dev/docs/animated#add\n   */\n  add,\n\n  /**\n   * Creates a new Animated value composed by subtracting the second Animated\n   * value from the first Animated value.\n   *\n   * See https://reactnative.dev/docs/animated#subtract\n   */\n  subtract,\n\n  /**\n   * Creates a new Animated value composed by dividing the first Animated value\n   * by the second Animated value.\n   *\n   * See https://reactnative.dev/docs/animated#divide\n   */\n  divide,\n\n  /**\n   * Creates a new Animated value composed from two Animated values multiplied\n   * together.\n   *\n   * See https://reactnative.dev/docs/animated#multiply\n   */\n  multiply,\n\n  /**\n   * Creates a new Animated value that is the (non-negative) modulo of the\n   * provided Animated value.\n   *\n   * See https://reactnative.dev/docs/animated#modulo\n   */\n  modulo,\n\n  /**\n   * Create a new Animated value that is limited between 2 values. It uses the\n   * difference between the last value so even if the value is far from the\n   * bounds it will start changing when the value starts getting closer again.\n   *\n   * See https://reactnative.dev/docs/animated#diffclamp\n   */\n  diffClamp,\n\n  /**\n   * Starts an animation after the given delay.\n   *\n   * See https://reactnative.dev/docs/animated#delay\n   */\n  delay,\n  /**\n   * Starts an array of animations in order, waiting for each to complete\n   * before starting the next. If the current running animation is stopped, no\n   * following animations will be started.\n   *\n   * See https://reactnative.dev/docs/animated#sequence\n   */\n  sequence,\n  /**\n   * Starts an array of animations all at the same time. By default, if one\n   * of the animations is stopped, they will all be stopped. You can override\n   * this with the `stopTogether` flag.\n   *\n   * See https://reactnative.dev/docs/animated#parallel\n   */\n  parallel,\n  /**\n   * Array of animations may run in parallel (overlap), but are started in\n   * sequence with successive delays.  Nice for doing trailing effects.\n   *\n   * See https://reactnative.dev/docs/animated#stagger\n   */\n  stagger,\n  /**\n   * Loops a given animation continuously, so that each time it reaches the\n   * end, it resets and begins again from the start.\n   *\n   * See https://reactnative.dev/docs/animated#loop\n   */\n  loop,\n\n  /**\n   * Takes an array of mappings and extracts values from each arg accordingly,\n   * then calls `setValue` on the mapped outputs.\n   *\n   * See https://reactnative.dev/docs/animated#event\n   */\n  event,\n\n  /**\n   * Make any React component Animatable.  Used to create `Animated.View`, etc.\n   *\n   * See https://reactnative.dev/docs/animated#createanimatedcomponent\n   */\n  createAnimatedComponent,\n\n  /**\n   * Imperative API to attach an animated value to an event on a view. Prefer\n   * using `Animated.event` with `useNativeDrive: true` if possible.\n   *\n   * See https://reactnative.dev/docs/animated#attachnativeevent\n   */\n  attachNativeEvent,\n\n  /**\n   * Advanced imperative API for snooping on animated events that are passed in\n   * through props. Use values directly where possible.\n   *\n   * See https://reactnative.dev/docs/animated#forkevent\n   */\n  forkEvent,\n  unforkEvent,\n\n  /**\n   * Expose Event class, so it can be used as a type for type checkers.\n   */\n  Event: AnimatedEvent,\n};\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAYb,IAAAC,cAAA,GAAAN,OAAA;AACA,IAAAO,eAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,gBAAA,GAAAT,sBAAA,CAAAC,OAAA;AACA,IAAAS,gBAAA,GAAAV,sBAAA,CAAAC,OAAA;AACA,IAAAU,wBAAA,GAAAX,sBAAA,CAAAC,OAAA;AACA,IAAAW,iBAAA,GAAAZ,sBAAA,CAAAC,OAAA;AACA,IAAAY,cAAA,GAAAb,sBAAA,CAAAC,OAAA;AACA,IAAAa,kBAAA,GAAAd,sBAAA,CAAAC,OAAA;AACA,IAAAc,iBAAA,GAAAf,sBAAA,CAAAC,OAAA;AACA,IAAAe,sBAAA,GAAAhB,sBAAA,CAAAC,OAAA;AACA,IAAAgB,eAAA,GAAAjB,sBAAA,CAAAC,OAAA;AACA,IAAAiB,uBAAA,GAAAlB,sBAAA,CAAAC,OAAA;AACA,IAAAkB,aAAA,GAAAnB,sBAAA,CAAAC,OAAA;AACA,IAAAmB,oBAAA,GAAApB,sBAAA,CAAAC,OAAA;AACA,IAAAoB,iBAAA,GAAArB,sBAAA,CAAAC,OAAA;AACA,IAAAqB,cAAA,GAAAtB,sBAAA,CAAAC,OAAA;AACA,IAAAsB,gBAAA,GAAAvB,sBAAA,CAAAC,OAAA;AAWA,IAAMuB,GAAG,GAAG,SAANA,GAAGA,CACPC,CAAwB,EACxBC,CAAwB,EACN;EAClB,OAAO,IAAIC,yBAAgB,CAACF,CAAC,EAAEC,CAAC,CAAC;AACnC,CAAC;AAED,IAAME,QAAQ,GAAG,SAAXA,QAAQA,CACZH,CAAwB,EACxBC,CAAwB,EACH;EACrB,OAAO,IAAIG,4BAAmB,CAACJ,CAAC,EAAEC,CAAC,CAAC;AACtC,CAAC;AAED,IAAMI,MAAM,GAAG,SAATA,MAAMA,CACVL,CAAwB,EACxBC,CAAwB,EACN;EAClB,OAAO,IAAIK,yBAAgB,CAACN,CAAC,EAAEC,CAAC,CAAC;AACnC,CAAC;AAED,IAAMM,QAAQ,GAAG,SAAXA,QAAQA,CACZP,CAAwB,EACxBC,CAAwB,EACA;EACxB,OAAO,IAAIO,+BAAsB,CAACR,CAAC,EAAEC,CAAC,CAAC;AACzC,CAAC;AAED,IAAMQ,MAAM,GAAG,SAATA,MAAMA,CAAaT,CAAe,EAAEU,OAAe,EAAkB;EACzE,OAAO,IAAIC,uBAAc,CAACX,CAAC,EAAEU,OAAO,CAAC;AACvC,CAAC;AAED,IAAME,SAAS,GAAG,SAAZA,SAASA,CACbZ,CAAe,EACfa,GAAW,EACXC,GAAW,EACQ;EACnB,OAAO,IAAIC,0BAAiB,CAACf,CAAC,EAAEa,GAAG,EAAEC,GAAG,CAAC;AAC3C,CAAC;AAED,IAAME,iBAAiB,GAAG,SAApBA,iBAAiBA,CACrBC,QAAsB,EACtBC,MAA4C,EAC5C;EACA,IAAID,QAAQ,IAAIC,MAAM,CAACC,UAAU,EAAE;IACjC,OAAO,YAA+B;MACpCD,MAAM,CAACC,UAAU,IAAID,MAAM,CAACC,UAAU,CAAAC,KAAA,CAAjBF,MAAM,EAAAG,SAAmB,CAAC;MAC/CJ,QAAQ,IAAIA,QAAQ,CAAAG,KAAA,SAAAC,SAAQ,CAAC;IAC/B,CAAC;EACH,CAAC,MAAM;IACL,OAAOJ,QAAQ,IAAIC,MAAM,CAACC,UAAU;EACtC;AACF,CAAC;AAED,IAAMG,eAAe,GAAG,SAAlBA,eAAeA,CACnB1C,KAAsD,EACtDsC,MAAc,EACdK,IAAkE,EAC7C;EACrB,IAAI3C,KAAK,YAAY4C,wBAAe,EAAE;IACpC,IAAMC,OAAO,GAAAhD,MAAA,CAAAiD,MAAA,KAAOR,MAAM,CAAC;IAC3B,IAAMS,OAAO,GAAAlD,MAAA,CAAAiD,MAAA,KAAOR,MAAM,CAAC;IAC3B,KAAK,IAAMU,GAAG,IAAIV,MAAM,EAAE;MACxB,IAAAW,WAAA,GAAeX,MAAM,CAACU,GAAG,CAAC;QAAnBE,CAAC,GAAAD,WAAA,CAADC,CAAC;QAAEC,CAAC,GAAAF,WAAA,CAADE,CAAC;MACX,IAAID,CAAC,KAAKE,SAAS,IAAID,CAAC,KAAKC,SAAS,EAAE;QACtCP,OAAO,CAACG,GAAG,CAAC,GAAGE,CAAC;QAChBH,OAAO,CAACC,GAAG,CAAC,GAAGG,CAAC;MAClB;IACF;IACA,IAAME,EAAE,GAAGV,IAAI,CAAE3C,KAAK,CAAmBkD,CAAC,EAAEL,OAAO,CAAC;IACpD,IAAMS,EAAE,GAAGX,IAAI,CAAE3C,KAAK,CAAmBmD,CAAC,EAAEJ,OAAO,CAAC;IAGpD,OAAOQ,QAAQ,CAAC,CAACF,EAAE,EAAEC,EAAE,CAAC,EAAE;MAACE,YAAY,EAAE;IAAK,CAAC,CAAC;EAClD,CAAC,MAAM,IAAIxD,KAAK,YAAYyD,sBAAa,EAAE;IACzC,IAAMC,OAAO,GAAA7D,MAAA,CAAAiD,MAAA,KAAOR,MAAM,CAAC;IAC3B,IAAMqB,OAAO,GAAA9D,MAAA,CAAAiD,MAAA,KAAOR,MAAM,CAAC;IAC3B,IAAMsB,OAAO,GAAA/D,MAAA,CAAAiD,MAAA,KAAOR,MAAM,CAAC;IAC3B,IAAMuB,OAAO,GAAAhE,MAAA,CAAAiD,MAAA,KAAOR,MAAM,CAAC;IAC3B,KAAK,IAAMU,IAAG,IAAIV,MAAM,EAAE;MACxB,IAAAwB,YAAA,GAAqBxB,MAAM,CAACU,IAAG,CAAC;QAAzBe,CAAC,GAAAD,YAAA,CAADC,CAAC;QAAEC,CAAC,GAAAF,YAAA,CAADE,CAAC;QAAE3C,CAAC,GAAAyC,YAAA,CAADzC,CAAC;QAAED,CAAC,GAAA0C,YAAA,CAAD1C,CAAC;MACjB,IACE2C,CAAC,KAAKX,SAAS,IACfY,CAAC,KAAKZ,SAAS,IACf/B,CAAC,KAAK+B,SAAS,IACfhC,CAAC,KAAKgC,SAAS,EACf;QACAM,OAAO,CAACV,IAAG,CAAC,GAAGe,CAAC;QAChBJ,OAAO,CAACX,IAAG,CAAC,GAAGgB,CAAC;QAChBJ,OAAO,CAACZ,IAAG,CAAC,GAAG3B,CAAC;QAChBwC,OAAO,CAACb,IAAG,CAAC,GAAG5B,CAAC;MAClB;IACF;IACA,IAAM6C,EAAE,GAAGtB,IAAI,CAAE3C,KAAK,CAAiB+D,CAAC,EAAEL,OAAO,CAAC;IAClD,IAAMQ,EAAE,GAAGvB,IAAI,CAAE3C,KAAK,CAAiBgE,CAAC,EAAEL,OAAO,CAAC;IAClD,IAAMQ,EAAE,GAAGxB,IAAI,CAAE3C,KAAK,CAAiBqB,CAAC,EAAEuC,OAAO,CAAC;IAClD,IAAMQ,EAAE,GAAGzB,IAAI,CAAE3C,KAAK,CAAiBoB,CAAC,EAAEyC,OAAO,CAAC;IAGlD,OAAON,QAAQ,CAAC,CAACU,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,EAAE;MAACZ,YAAY,EAAE;IAAK,CAAC,CAAC;EAC1D;EACA,OAAO,IAAI;AACb,CAAC;AAED,IAAMa,OAAM,GAAG,SAATA,MAAMA,CACVrE,KAAsD,EACtDsC,MAA6B,EACT;EACpB,IAAMgC,MAAK,GAAG,SAARA,KAAKA,CACTC,aAA8D,EAC9DC,aAAoC,EACpCnC,QAAuB,EACjB;IACNA,QAAQ,GAAGD,iBAAiB,CAACC,QAAQ,EAAEmC,aAAa,CAAC;IACrD,IAAMC,WAAgB,GAAGF,aAAa;IACtC,IAAMG,YAAiB,GAAGF,aAAa;IACvCC,WAAW,CAACE,YAAY,CAAC,CAAC;IAC1B,IAAIH,aAAa,CAACI,OAAO,YAAYC,qBAAY,EAAE;MACjDJ,WAAW,CAACK,KAAK,CACf,IAAIC,yBAAgB,CAClBN,WAAW,EACXD,aAAa,CAACI,OAAO,EACrBI,wBAAe,EACfN,YAAY,EACZrC,QACF,CACF,CAAC;IACH,CAAC,MAAM;MACLoC,WAAW,CAACQ,OAAO,CAAC,IAAID,wBAAe,CAACN,YAAY,CAAC,EAAErC,QAAQ,CAAC;IAClE;EACF,CAAC;EACD,OACEK,eAAe,CAAC1C,KAAK,EAAEsC,MAAM,EAAE+B,OAAM,CAAC,IAAI;IACxCC,KAAK,EAAE,SAAPA,KAAKA,CAAYjC,QAAuB,EAAQ;MAC9CiC,MAAK,CAACtE,KAAK,EAAEsC,MAAM,EAAED,QAAQ,CAAC;IAChC,CAAC;IAED6C,IAAI,EAAE,SAANA,IAAIA,CAAA,EAAoB;MACtBlF,KAAK,CAACmF,aAAa,CAAC,CAAC;IACvB,CAAC;IAEDC,KAAK,EAAE,SAAPA,KAAKA,CAAA,EAAoB;MACvBpF,KAAK,CAACqF,cAAc,CAAC,CAAC;IACxB,CAAC;IAEDC,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAYC,UAAmB,EAAQ;MACrD,IAAMb,YAAY,GAAA7E,MAAA,CAAAiD,MAAA,KAAOR,MAAM;QAAEiD,UAAU,EAAVA;MAAU,EAAC;MAC5CjB,MAAK,CAACtE,KAAK,EAAE0E,YAAY,CAAC;IAC5B,CAAC;IAEDc,oBAAoB,EAAE,SAAtBA,oBAAoBA,CAAA,EAAuB;MACzC,OAAOlD,MAAM,CAACmD,eAAe,IAAI,KAAK;IACxC;EACF,CAAC;AAEL,CAAC;AAED,IAAMC,OAAM,GAAG,SAATA,MAAMA,CACV1F,KAAsD,EACtDsC,MAA6B,EACT;EACpB,IAAMgC,OAAK,GAAG,SAARA,KAAKA,CACTC,aAA8D,EAC9DC,aAAoC,EACpCnC,QAAuB,EACjB;IACNA,QAAQ,GAAGD,iBAAiB,CAACC,QAAQ,EAAEmC,aAAa,CAAC;IACrD,IAAMC,WAAgB,GAAGF,aAAa;IACtC,IAAMG,YAAiB,GAAGF,aAAa;IACvCC,WAAW,CAACE,YAAY,CAAC,CAAC;IAC1B,IAAIH,aAAa,CAACI,OAAO,YAAYC,qBAAY,EAAE;MACjDJ,WAAW,CAACK,KAAK,CACf,IAAIC,yBAAgB,CAClBN,WAAW,EACXD,aAAa,CAACI,OAAO,EACrBe,wBAAe,EACfjB,YAAY,EACZrC,QACF,CACF,CAAC;IACH,CAAC,MAAM;MACLoC,WAAW,CAACQ,OAAO,CAAC,IAAIU,wBAAe,CAACjB,YAAY,CAAC,EAAErC,QAAQ,CAAC;IAClE;EACF,CAAC;EAED,OACEK,eAAe,CAAC1C,KAAK,EAAEsC,MAAM,EAAEoD,OAAM,CAAC,IAAI;IACxCpB,KAAK,EAAE,SAAPA,KAAKA,CAAYjC,QAAuB,EAAEuD,SAAmB,EAAQ;MACnEtB,OAAK,CAACtE,KAAK,EAAAH,MAAA,CAAAiD,MAAA,KAAMR,MAAM;QAAEsD,SAAS,EAATA;MAAS,IAAGvD,QAAQ,CAAC;IAChD,CAAC;IAED6C,IAAI,EAAE,SAANA,IAAIA,CAAA,EAAoB;MACtBlF,KAAK,CAACmF,aAAa,CAAC,CAAC;IACvB,CAAC;IAEDC,KAAK,EAAE,SAAPA,KAAKA,CAAA,EAAoB;MACvBpF,KAAK,CAACqF,cAAc,CAAC,CAAC;IACxB,CAAC;IAEDC,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAYC,UAAmB,EAAQ;MACrD,IAAMb,YAAY,GAAA7E,MAAA,CAAAiD,MAAA,KAAOR,MAAM;QAAEiD,UAAU,EAAVA;MAAU,EAAC;MAC5CjB,OAAK,CAACtE,KAAK,EAAE0E,YAAY,CAAC;IAC5B,CAAC;IAEDc,oBAAoB,EAAE,SAAtBA,oBAAoBA,CAAA,EAAuB;MACzC,OAAOlD,MAAM,CAACmD,eAAe,IAAI,KAAK;IACxC;EACF,CAAC;AAEL,CAAC;AAED,IAAMI,MAAK,GAAG,SAARA,KAAKA,CACT7F,KAAsD,EACtDsC,MAA4B,EACR;EACpB,IAAMgC,OAAK,GAAG,SAARA,KAAKA,CACTC,aAA8D,EAC9DC,aAAmC,EACnCnC,QAAuB,EACjB;IACNA,QAAQ,GAAGD,iBAAiB,CAACC,QAAQ,EAAEmC,aAAa,CAAC;IACrD,IAAMC,WAAgB,GAAGF,aAAa;IACtC,IAAMG,YAAiB,GAAGF,aAAa;IACvCC,WAAW,CAACE,YAAY,CAAC,CAAC;IAC1BF,WAAW,CAACQ,OAAO,CAAC,IAAIa,uBAAc,CAACpB,YAAY,CAAC,EAAErC,QAAQ,CAAC;EACjE,CAAC;EAED,OACEK,eAAe,CAAC1C,KAAK,EAAEsC,MAAM,EAAEuD,MAAK,CAAC,IAAI;IACvCvB,KAAK,EAAE,SAAPA,KAAKA,CAAYjC,QAAuB,EAAQ;MAC9CiC,OAAK,CAACtE,KAAK,EAAEsC,MAAM,EAAED,QAAQ,CAAC;IAChC,CAAC;IAED6C,IAAI,EAAE,SAANA,IAAIA,CAAA,EAAoB;MACtBlF,KAAK,CAACmF,aAAa,CAAC,CAAC;IACvB,CAAC;IAEDC,KAAK,EAAE,SAAPA,KAAKA,CAAA,EAAoB;MACvBpF,KAAK,CAACqF,cAAc,CAAC,CAAC;IACxB,CAAC;IAEDC,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAYC,UAAmB,EAAQ;MACrD,IAAMb,YAAY,GAAA7E,MAAA,CAAAiD,MAAA,KAAOR,MAAM;QAAEiD,UAAU,EAAVA;MAAU,EAAC;MAC5CjB,OAAK,CAACtE,KAAK,EAAE0E,YAAY,CAAC;IAC5B,CAAC;IAEDc,oBAAoB,EAAE,SAAtBA,oBAAoBA,CAAA,EAAuB;MACzC,OAAOlD,MAAM,CAACmD,eAAe,IAAI,KAAK;IACxC;EACF,CAAC;AAEL,CAAC;AAED,IAAMM,QAAQ,GAAG,SAAXA,QAAQA,CACZC,UAAqC,EACjB;EACpB,IAAIC,OAAO,GAAG,CAAC;EACf,OAAO;IACL3B,KAAK,EAAE,SAAPA,KAAKA,CAAYjC,QAAuB,EAAEuD,SAAmB,EAAE;MAC7D,IAAMrD,WAAU,GAAG,SAAbA,UAAUA,CAAa2D,MAAiB,EAAE;QAC9C,IAAI,CAACA,MAAM,CAACC,QAAQ,EAAE;UACpB9D,QAAQ,IAAIA,QAAQ,CAAC6D,MAAM,CAAC;UAC5B;QACF;QAEAD,OAAO,EAAE;QAET,IAAIA,OAAO,KAAKD,UAAU,CAACI,MAAM,EAAE;UAEjCH,OAAO,GAAG,CAAC;UACX5D,QAAQ,IAAIA,QAAQ,CAAC6D,MAAM,CAAC;UAC5B;QACF;QAEAF,UAAU,CAACC,OAAO,CAAC,CAAC3B,KAAK,CAAC/B,WAAU,EAAEqD,SAAS,CAAC;MAClD,CAAC;MAED,IAAII,UAAU,CAACI,MAAM,KAAK,CAAC,EAAE;QAC3B/D,QAAQ,IAAIA,QAAQ,CAAC;UAAC8D,QAAQ,EAAE;QAAI,CAAC,CAAC;MACxC,CAAC,MAAM;QACLH,UAAU,CAACC,OAAO,CAAC,CAAC3B,KAAK,CAAC/B,WAAU,EAAEqD,SAAS,CAAC;MAClD;IACF,CAAC;IAEDV,IAAI,EAAE,SAANA,IAAIA,CAAA,EAAc;MAChB,IAAIe,OAAO,GAAGD,UAAU,CAACI,MAAM,EAAE;QAC/BJ,UAAU,CAACC,OAAO,CAAC,CAACf,IAAI,CAAC,CAAC;MAC5B;IACF,CAAC;IAEDE,KAAK,EAAE,SAAPA,KAAKA,CAAA,EAAc;MACjBY,UAAU,CAACK,OAAO,CAAC,UAACC,SAAS,EAAEC,GAAG,EAAK;QACrC,IAAIA,GAAG,IAAIN,OAAO,EAAE;UAClBK,SAAS,CAAClB,KAAK,CAAC,CAAC;QACnB;MACF,CAAC,CAAC;MACFa,OAAO,GAAG,CAAC;IACb,CAAC;IAEDX,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAA,EAAc;MAC5B,MAAM,IAAIkB,KAAK,CACb,+EACF,CAAC;IACH,CAAC;IAEDhB,oBAAoB,EAAE,SAAtBA,oBAAoBA,CAAA,EAAuB;MACzC,OAAO,KAAK;IACd;EACF,CAAC;AACH,CAAC;AAOD,IAAMjC,QAAQ,GAAG,SAAXA,QAAQA,CACZyC,UAAqC,EACrC1D,MAAwB,EACJ;EACpB,IAAImE,SAAS,GAAG,CAAC;EAEjB,IAAMC,QAA6B,GAAG,CAAC,CAAC;EACxC,IAAMlD,YAAY,GAAG,EAAElB,MAAM,IAAIA,MAAM,CAACkB,YAAY,KAAK,KAAK,CAAC;EAE/D,IAAM0C,MAAM,GAAG;IACb5B,KAAK,EAAE,SAAPA,KAAKA,CAAYjC,QAAuB,EAAEuD,SAAmB,EAAE;MAC7D,IAAIa,SAAS,KAAKT,UAAU,CAACI,MAAM,EAAE;QACnC/D,QAAQ,IAAIA,QAAQ,CAAC;UAAC8D,QAAQ,EAAE;QAAI,CAAC,CAAC;QACtC;MACF;MAEAH,UAAU,CAACK,OAAO,CAAC,UAACC,SAAS,EAAEC,GAAG,EAAK;QACrC,IAAMI,EAAE,GAAG,SAALA,EAAEA,CAAaC,SAAoB,EAAE;UACzCF,QAAQ,CAACH,GAAG,CAAC,GAAG,IAAI;UACpBE,SAAS,EAAE;UACX,IAAIA,SAAS,KAAKT,UAAU,CAACI,MAAM,EAAE;YACnCK,SAAS,GAAG,CAAC;YACbpE,QAAQ,IAAIA,QAAQ,CAACuE,SAAS,CAAC;YAC/B;UACF;UAEA,IAAI,CAACA,SAAS,CAACT,QAAQ,IAAI3C,YAAY,EAAE;YACvC0C,MAAM,CAAChB,IAAI,CAAC,CAAC;UACf;QACF,CAAC;QAED,IAAI,CAACoB,SAAS,EAAE;UACdK,EAAE,CAAC;YAACR,QAAQ,EAAE;UAAI,CAAC,CAAC;QACtB,CAAC,MAAM;UACLG,SAAS,CAAChC,KAAK,CAACqC,EAAE,EAAEf,SAAS,CAAC;QAChC;MACF,CAAC,CAAC;IACJ,CAAC;IAEDV,IAAI,EAAE,SAANA,IAAIA,CAAA,EAAoB;MACtBc,UAAU,CAACK,OAAO,CAAC,UAACC,SAAS,EAAEC,GAAG,EAAK;QACrC,CAACG,QAAQ,CAACH,GAAG,CAAC,IAAID,SAAS,CAACpB,IAAI,CAAC,CAAC;QAClCwB,QAAQ,CAACH,GAAG,CAAC,GAAG,IAAI;MACtB,CAAC,CAAC;IACJ,CAAC;IAEDnB,KAAK,EAAE,SAAPA,KAAKA,CAAA,EAAoB;MACvBY,UAAU,CAACK,OAAO,CAAC,UAACC,SAAS,EAAEC,GAAG,EAAK;QACrCD,SAAS,CAAClB,KAAK,CAAC,CAAC;QACjBsB,QAAQ,CAACH,GAAG,CAAC,GAAG,KAAK;QACrBE,SAAS,GAAG,CAAC;MACf,CAAC,CAAC;IACJ,CAAC;IAEDnB,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAA,EAAqB;MACnC,MAAM,IAAIkB,KAAK,CACb,+EACF,CAAC;IACH,CAAC;IAEDhB,oBAAoB,EAAE,SAAtBA,oBAAoBA,CAAA,EAAuB;MACzC,OAAO,KAAK;IACd;EACF,CAAC;EAED,OAAOU,MAAM;AACf,CAAC;AAED,IAAMW,KAAK,GAAG,SAARA,KAAKA,CAAaC,IAAY,EAAsB;EAExD,OAAOpB,OAAM,CAAC,IAAIqB,sBAAa,CAAC,CAAC,CAAC,EAAE;IAClCnC,OAAO,EAAE,CAAC;IACViC,KAAK,EAAEC,IAAI;IACXE,QAAQ,EAAE,CAAC;IACXvB,eAAe,EAAE;EACnB,CAAC,CAAC;AACJ,CAAC;AAED,IAAMwB,OAAO,GAAG,SAAVA,OAAOA,CACXH,IAAY,EACZd,UAAqC,EACjB;EACpB,OAAOzC,QAAQ,CACbyC,UAAU,CAACkB,GAAG,CAAC,UAACZ,SAAS,EAAEa,CAAC,EAAK;IAC/B,OAAOpB,QAAQ,CAAC,CAACc,KAAK,CAACC,IAAI,GAAGK,CAAC,CAAC,EAAEb,SAAS,CAAC,CAAC;EAC/C,CAAC,CACH,CAAC;AACH,CAAC;AAQD,IAAMc,IAAI,GAAG,SAAPA,IAAIA,CACRd,SAA6B,EAGT;EAAA,IAAAe,IAAA,GAAA5E,SAAA,CAAA2D,MAAA,QAAA3D,SAAA,QAAAW,SAAA,GAAAX,SAAA,MADkD,CAAC,CAAC;IAAA6E,eAAA,GAAAD,IAAA,CAAvE9B,UAAU;IAAVA,UAAU,GAAA+B,eAAA,cAAG,CAAC,CAAC,GAAAA,eAAA;IAAAC,qBAAA,GAAAF,IAAA,CAAEG,oBAAoB;IAApBA,oBAAoB,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;EAE7C,IAAIE,UAAU,GAAG,KAAK;EACtB,IAAIC,eAAe,GAAG,CAAC;EACvB,OAAO;IACLpD,KAAK,EAAE,SAAPA,KAAKA,CAAYjC,QAAuB,EAAE;MACxC,IAAMsF,QAAO,GAAG,SAAVA,OAAOA,CAAA,EAAyD;QAAA,IAA5CzB,MAAiB,GAAAzD,SAAA,CAAA2D,MAAA,QAAA3D,SAAA,QAAAW,SAAA,GAAAX,SAAA,MAAG;UAAC0D,QAAQ,EAAE;QAAI,CAAC;QAC5D,IACEsB,UAAU,IACVC,eAAe,KAAKnC,UAAU,IAC9BW,MAAM,CAACC,QAAQ,KAAK,KAAK,EACzB;UACA9D,QAAQ,IAAIA,QAAQ,CAAC6D,MAAM,CAAC;QAC9B,CAAC,MAAM;UACLwB,eAAe,EAAE;UACjBF,oBAAoB,IAAIlB,SAAS,CAAClB,KAAK,CAAC,CAAC;UACzCkB,SAAS,CAAChC,KAAK,CAACqD,QAAO,EAAEpC,UAAU,KAAK,CAAC,CAAC,CAAC;QAC7C;MACF,CAAC;MACD,IAAI,CAACe,SAAS,IAAIf,UAAU,KAAK,CAAC,EAAE;QAClClD,QAAQ,IAAIA,QAAQ,CAAC;UAAC8D,QAAQ,EAAE;QAAI,CAAC,CAAC;MACxC,CAAC,MAAM;QACL,IAAIG,SAAS,CAACd,oBAAoB,CAAC,CAAC,EAAE;UACpCc,SAAS,CAAChB,gBAAgB,CAACC,UAAU,CAAC;QACxC,CAAC,MAAM;UACLoC,QAAO,CAAC,CAAC;QACX;MACF;IACF,CAAC;IAEDzC,IAAI,EAAE,SAANA,IAAIA,CAAA,EAAoB;MACtBuC,UAAU,GAAG,IAAI;MACjBnB,SAAS,CAACpB,IAAI,CAAC,CAAC;IAClB,CAAC;IAEDE,KAAK,EAAE,SAAPA,KAAKA,CAAA,EAAoB;MACvBsC,eAAe,GAAG,CAAC;MACnBD,UAAU,GAAG,KAAK;MAClBnB,SAAS,CAAClB,KAAK,CAAC,CAAC;IACnB,CAAC;IAEDE,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAA,EAAc;MAC5B,MAAM,IAAIkB,KAAK,CACb,2EACF,CAAC;IACH,CAAC;IAEDhB,oBAAoB,EAAE,SAAtBA,oBAAoBA,CAAA,EAAuB;MACzC,OAAOc,SAAS,CAACd,oBAAoB,CAAC,CAAC;IACzC;EACF,CAAC;AACH,CAAC;AAED,SAASoC,SAASA,CAChBC,KAAiC,EACjCC,QAAkB,EACQ;EAC1B,IAAI,CAACD,KAAK,EAAE;IACV,OAAOC,QAAQ;EACjB,CAAC,MAAM,IAAID,KAAK,YAAYE,4BAAa,EAAE;IACzCF,KAAK,CAACG,aAAa,CAACF,QAAQ,CAAC;IAC7B,OAAOD,KAAK;EACd,CAAC,MAAM;IACL,OAAO,YAAa;MAClB,OAAOA,KAAK,KAAK,UAAU,IAAIA,KAAK,CAAArF,KAAA,SAAAC,SAAQ,CAAC;MAC7CqF,QAAQ,CAAAtF,KAAA,SAAAC,SAAQ,CAAC;IACnB,CAAC;EACH;AACF;AAEA,SAASwF,WAAWA,CAClBJ,KAAiC,EACjCC,QAAkB,EACZ;EACN,IAAID,KAAK,IAAIA,KAAK,YAAYE,4BAAa,EAAE;IAC3CF,KAAK,CAACK,gBAAgB,CAACJ,QAAQ,CAAC;EAClC;AACF;AAEA,IAAMD,KAAK,GAAG,SAARA,KAAKA,CACTM,UAAoC,EACpC7F,MAAmB,EACd;EACL,IAAM8F,aAAa,GAAG,IAAIL,4BAAa,CAACI,UAAU,EAAE7F,MAAM,CAAC;EAC3D,IAAI8F,aAAa,CAACC,UAAU,EAAE;IAC5B,OAAOD,aAAa;EACtB,CAAC,MAAM;IACL,OAAOA,aAAa,CAACE,YAAY,CAAC,CAAC;EACrC;AACF,CAAC;AAAC,IAAAC,QAAA,GAAAxI,OAAA,CAAAE,OAAA,GAyBa;EAObuI,KAAK,EAAEzB,sBAAa;EAMpB0B,OAAO,EAAE7F,wBAAe;EAIxB8F,KAAK,EAAEjF,sBAAa;EAMpBkF,aAAa,EAAEC,8BAAqB;EAOpCC,IAAI,EAAEhE,qBAAY;EAQlBgB,KAAK,EAALA,MAAK;EAOLH,MAAM,EAANA,OAAM;EAONrB,MAAM,EAANA,OAAM;EAQNlD,GAAG,EAAHA,GAAG;EAQHI,QAAQ,EAARA,QAAQ;EAQRE,MAAM,EAANA,MAAM;EAQNE,QAAQ,EAARA,QAAQ;EAQRE,MAAM,EAANA,MAAM;EASNG,SAAS,EAATA,SAAS;EAOT6E,KAAK,EAALA,KAAK;EAQLd,QAAQ,EAARA,QAAQ;EAQRxC,QAAQ,EAARA,QAAQ;EAOR0D,OAAO,EAAPA,OAAO;EAOPG,IAAI,EAAJA,IAAI;EAQJS,KAAK,EAALA,KAAK;EAOLiB,uBAAuB,EAAvBA,gCAAuB;EAQvBC,iBAAiB,EAAjBA,gCAAiB;EAQjBnB,SAAS,EAATA,SAAS;EACTK,WAAW,EAAXA,WAAW;EAKXe,KAAK,EAAEjB;AACT,CAAC", "ignoreList": []}