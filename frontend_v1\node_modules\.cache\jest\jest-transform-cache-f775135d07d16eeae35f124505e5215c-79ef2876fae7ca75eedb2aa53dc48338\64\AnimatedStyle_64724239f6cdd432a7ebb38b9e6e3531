87bc09ab8214ca1d77cf79a7edbf81ad
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _classPrivateFieldLooseBase2 = _interopRequireDefault(require("@babel/runtime/helpers/classPrivateFieldLooseBase"));
var _classPrivateFieldLooseKey2 = _interopRequireDefault(require("@babel/runtime/helpers/classPrivateFieldLooseKey"));
var _NativeAnimatedValidation = require("../../../src/private/animated/NativeAnimatedValidation");
var ReactNativeFeatureFlags = _interopRequireWildcard(require("../../../src/private/featureflags/ReactNativeFeatureFlags"));
var _flattenStyle = _interopRequireDefault(require("../../StyleSheet/flattenStyle"));
var _Platform = _interopRequireDefault(require("../../Utilities/Platform"));
var _AnimatedNode = _interopRequireDefault(require("./AnimatedNode"));
var _AnimatedObject = _interopRequireDefault(require("./AnimatedObject"));
var _AnimatedTransform = _interopRequireDefault(require("./AnimatedTransform"));
var _AnimatedWithChildren2 = _interopRequireDefault(require("./AnimatedWithChildren"));
var _Object$hasOwn;
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
function createAnimatedStyle(inputStyle, allowlist, keepUnanimatedValues) {
  var nodeKeys = [];
  var nodes = [];
  var style = {};
  var keys = Object.keys(inputStyle);
  for (var ii = 0, length = keys.length; ii < length; ii++) {
    var key = keys[ii];
    var value = inputStyle[key];
    if (allowlist == null || hasOwn(allowlist, key)) {
      var node = void 0;
      if (value != null && key === 'transform') {
        node = ReactNativeFeatureFlags.shouldUseAnimatedObjectForTransform() ? _AnimatedObject.default.from(value) : _AnimatedTransform.default.from(value);
      } else if (value instanceof _AnimatedNode.default) {
        node = value;
      } else {
        node = _AnimatedObject.default.from(value);
      }
      if (node == null) {
        if (keepUnanimatedValues) {
          style[key] = value;
        }
      } else {
        nodeKeys.push(key);
        nodes.push(node);
        style[key] = node;
      }
    } else {
      if (__DEV__) {
        if (_AnimatedObject.default.from(inputStyle[key]) != null) {
          console.error(`AnimatedStyle: ${key} is not allowlisted for animation, but it ` + 'contains AnimatedNode values; styles allowing animation: ', allowlist);
        }
      }
      if (keepUnanimatedValues) {
        style[key] = value;
      }
    }
  }
  return [nodeKeys, nodes, style];
}
var _inputStyle = (0, _classPrivateFieldLooseKey2.default)("inputStyle");
var _nodeKeys = (0, _classPrivateFieldLooseKey2.default)("nodeKeys");
var _nodes = (0, _classPrivateFieldLooseKey2.default)("nodes");
var _style = (0, _classPrivateFieldLooseKey2.default)("style");
var AnimatedStyle = exports.default = function (_AnimatedWithChildren) {
  function AnimatedStyle(nodeKeys, nodes, style, inputStyle, config) {
    var _this;
    (0, _classCallCheck2.default)(this, AnimatedStyle);
    _this = _callSuper(this, AnimatedStyle, [config]);
    Object.defineProperty(_this, _inputStyle, {
      writable: true,
      value: void 0
    });
    Object.defineProperty(_this, _nodeKeys, {
      writable: true,
      value: void 0
    });
    Object.defineProperty(_this, _nodes, {
      writable: true,
      value: void 0
    });
    Object.defineProperty(_this, _style, {
      writable: true,
      value: void 0
    });
    (0, _classPrivateFieldLooseBase2.default)(_this, _nodeKeys)[_nodeKeys] = nodeKeys;
    (0, _classPrivateFieldLooseBase2.default)(_this, _nodes)[_nodes] = nodes;
    (0, _classPrivateFieldLooseBase2.default)(_this, _style)[_style] = style;
    (0, _classPrivateFieldLooseBase2.default)(_this, _inputStyle)[_inputStyle] = inputStyle;
    return _this;
  }
  (0, _inherits2.default)(AnimatedStyle, _AnimatedWithChildren);
  return (0, _createClass2.default)(AnimatedStyle, [{
    key: "__getValue",
    value: function __getValue() {
      var style = {};
      var keys = Object.keys((0, _classPrivateFieldLooseBase2.default)(this, _style)[_style]);
      for (var ii = 0, length = keys.length; ii < length; ii++) {
        var key = keys[ii];
        var value = (0, _classPrivateFieldLooseBase2.default)(this, _style)[_style][key];
        if (value instanceof _AnimatedNode.default) {
          style[key] = value.__getValue();
        } else {
          style[key] = value;
        }
      }
      return _Platform.default.OS === 'web' ? [(0, _classPrivateFieldLooseBase2.default)(this, _inputStyle)[_inputStyle], style] : style;
    }
  }, {
    key: "__getValueWithStaticStyle",
    value: function __getValueWithStaticStyle(staticStyle) {
      var flatStaticStyle = (0, _flattenStyle.default)(staticStyle);
      var style = flatStaticStyle == null ? {} : flatStaticStyle === staticStyle ? Object.assign({}, flatStaticStyle) : flatStaticStyle;
      var keys = Object.keys(style);
      for (var ii = 0, length = keys.length; ii < length; ii++) {
        var key = keys[ii];
        var maybeNode = (0, _classPrivateFieldLooseBase2.default)(this, _style)[_style][key];
        if (key === 'transform' && maybeNode instanceof _AnimatedTransform.default) {
          style[key] = maybeNode.__getValueWithStaticTransforms(Array.isArray(style[key]) ? style[key] : []);
        } else if (maybeNode instanceof _AnimatedObject.default) {
          style[key] = maybeNode.__getValueWithStaticObject(style[key]);
        } else if (maybeNode instanceof _AnimatedNode.default) {
          style[key] = maybeNode.__getValue();
        }
      }
      return _Platform.default.OS === 'web' ? [(0, _classPrivateFieldLooseBase2.default)(this, _inputStyle)[_inputStyle], style] : style;
    }
  }, {
    key: "__getAnimatedValue",
    value: function __getAnimatedValue() {
      var style = {};
      var nodeKeys = (0, _classPrivateFieldLooseBase2.default)(this, _nodeKeys)[_nodeKeys];
      var nodes = (0, _classPrivateFieldLooseBase2.default)(this, _nodes)[_nodes];
      for (var ii = 0, length = nodes.length; ii < length; ii++) {
        var key = nodeKeys[ii];
        var node = nodes[ii];
        style[key] = node.__getAnimatedValue();
      }
      return style;
    }
  }, {
    key: "__attach",
    value: function __attach() {
      var nodes = (0, _classPrivateFieldLooseBase2.default)(this, _nodes)[_nodes];
      for (var ii = 0, length = nodes.length; ii < length; ii++) {
        var node = nodes[ii];
        node.__addChild(this);
      }
      _superPropGet(AnimatedStyle, "__attach", this, 3)([]);
    }
  }, {
    key: "__detach",
    value: function __detach() {
      var nodes = (0, _classPrivateFieldLooseBase2.default)(this, _nodes)[_nodes];
      for (var ii = 0, length = nodes.length; ii < length; ii++) {
        var node = nodes[ii];
        node.__removeChild(this);
      }
      _superPropGet(AnimatedStyle, "__detach", this, 3)([]);
    }
  }, {
    key: "__makeNative",
    value: function __makeNative(platformConfig) {
      var nodes = (0, _classPrivateFieldLooseBase2.default)(this, _nodes)[_nodes];
      for (var ii = 0, length = nodes.length; ii < length; ii++) {
        var node = nodes[ii];
        node.__makeNative(platformConfig);
      }
      _superPropGet(AnimatedStyle, "__makeNative", this, 3)([platformConfig]);
    }
  }, {
    key: "__getNativeConfig",
    value: function __getNativeConfig() {
      var platformConfig = this.__getPlatformConfig();
      var styleConfig = {};
      var nodeKeys = (0, _classPrivateFieldLooseBase2.default)(this, _nodeKeys)[_nodeKeys];
      var nodes = (0, _classPrivateFieldLooseBase2.default)(this, _nodes)[_nodes];
      for (var ii = 0, length = nodes.length; ii < length; ii++) {
        var key = nodeKeys[ii];
        var node = nodes[ii];
        node.__makeNative(platformConfig);
        styleConfig[key] = node.__getNativeTag();
      }
      if (__DEV__) {
        (0, _NativeAnimatedValidation.validateStyles)(styleConfig);
      }
      return {
        type: 'style',
        style: styleConfig,
        debugID: this.__getDebugID()
      };
    }
  }], [{
    key: "from",
    value: function from(inputStyle, allowlist) {
      var flatStyle = (0, _flattenStyle.default)(inputStyle);
      if (flatStyle == null) {
        return null;
      }
      var _createAnimatedStyle = createAnimatedStyle(flatStyle, allowlist, _Platform.default.OS !== 'web'),
        _createAnimatedStyle2 = (0, _slicedToArray2.default)(_createAnimatedStyle, 3),
        nodeKeys = _createAnimatedStyle2[0],
        nodes = _createAnimatedStyle2[1],
        style = _createAnimatedStyle2[2];
      if (nodes.length === 0) {
        return null;
      }
      return new AnimatedStyle(nodeKeys, nodes, style, inputStyle);
    }
  }]);
}(_AnimatedWithChildren2.default);
var _hasOwnProp = Object.prototype.hasOwnProperty;
var hasOwn = (_Object$hasOwn = Object.hasOwn) != null ? _Object$hasOwn : function (obj, prop) {
  return _hasOwnProp.call(obj, prop);
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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