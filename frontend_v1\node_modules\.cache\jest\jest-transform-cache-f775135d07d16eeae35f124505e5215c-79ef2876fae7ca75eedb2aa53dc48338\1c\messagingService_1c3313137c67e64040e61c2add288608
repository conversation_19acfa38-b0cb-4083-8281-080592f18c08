b1332538e1f957e654438f7fdd5656a1
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.messagingService = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _apiClient = require("./apiClient");
var _unifiedErrorHandling = require("./unifiedErrorHandling");
var MessagingService = function () {
  function MessagingService() {
    (0, _classCallCheck2.default)(this, MessagingService);
    this.baseUrl = '/api/messaging';
  }
  return (0, _createClass2.default)(MessagingService, [{
    key: "getConversations",
    value: (function () {
      var _getConversations = (0, _asyncToGenerator2.default)(function* () {
        var page = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 1;
        var limit = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 20;
        var archived = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
        try {
          var response = yield _apiClient.apiClient.get(`${this.baseUrl}/conversations/`, {
            params: {
              page: page,
              limit: limit,
              archived: archived
            }
          });
          var conversations = Array.isArray(response.data) ? response.data : [];
          return {
            conversations: conversations,
            totalCount: conversations.length
          };
        } catch (error) {
          console.error('Failed to get conversations:', error);
          throw new Error('Failed to get conversations');
        }
      });
      function getConversations() {
        return _getConversations.apply(this, arguments);
      }
      return getConversations;
    }())
  }, {
    key: "getConversation",
    value: (function () {
      var _getConversation = (0, _asyncToGenerator2.default)(function* (conversationId) {
        try {
          var response = yield _apiClient.apiClient.get(`${this.baseUrl}/conversations/${conversationId}/`);
          return response.data;
        } catch (error) {
          console.error('Failed to get conversation:', error);
          throw new Error('Failed to get conversation');
        }
      });
      function getConversation(_x) {
        return _getConversation.apply(this, arguments);
      }
      return getConversation;
    }())
  }, {
    key: "createConversation",
    value: (function () {
      var _createConversation = (0, _asyncToGenerator2.default)(function* (participantIds) {
        var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'direct';
        var title = arguments.length > 2 ? arguments[2] : undefined;
        try {
          var response = yield _apiClient.apiClient.post(`${this.baseUrl}/conversations/`, {
            participant_ids: participantIds,
            type: type,
            title: title
          });
          return response.data;
        } catch (error) {
          console.error('Failed to create conversation:', error);
          throw new Error('Failed to create conversation');
        }
      });
      function createConversation(_x2) {
        return _createConversation.apply(this, arguments);
      }
      return createConversation;
    }())
  }, {
    key: "getMessages",
    value: (function () {
      var _getMessages = (0, _asyncToGenerator2.default)(function* (conversationId) {
        var page = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;
        var limit = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 50;
        var filters = arguments.length > 3 ? arguments[3] : undefined;
        try {
          var params = Object.assign({
            page: page,
            limit: limit
          }, filters);
          var response = yield _apiClient.apiClient.get(`${this.baseUrl}/conversations/${conversationId}/messages/`, {
            params: params
          });
          return response.data;
        } catch (error) {
          console.error('Failed to get messages:', error);
          throw new Error('Failed to get messages');
        }
      });
      function getMessages(_x3) {
        return _getMessages.apply(this, arguments);
      }
      return getMessages;
    }())
  }, {
    key: "sendMessage",
    value: (function () {
      var _sendMessage = (0, _asyncToGenerator2.default)(function* (conversationId, content) {
        var type = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'text';
        var attachments = arguments.length > 3 ? arguments[3] : undefined;
        var replyTo = arguments.length > 4 ? arguments[4] : undefined;
        try {
          var formData = new FormData();
          formData.append('content', content);
          formData.append('type', type);
          if (replyTo) {
            formData.append('reply_to', replyTo);
          }
          if (attachments && attachments.length > 0) {
            attachments.forEach(function (file, index) {
              formData.append(`attachments[${index}]`, file);
            });
          }
          var response = yield _apiClient.apiClient.post(`${this.baseUrl}/conversations/${conversationId}/send_message/`, formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          });
          return response.data;
        } catch (error) {
          console.error('Failed to send message:', error);
          yield _unifiedErrorHandling.unifiedErrorHandlingService.handleNetworkError(error, {
            action: 'send_message',
            additionalData: {
              conversationId: conversationId,
              content: content.substring(0, 50) + '...'
            }
          });
          throw error;
        }
      });
      function sendMessage(_x4, _x5) {
        return _sendMessage.apply(this, arguments);
      }
      return sendMessage;
    }())
  }, {
    key: "editMessage",
    value: (function () {
      var _editMessage = (0, _asyncToGenerator2.default)(function* (messageId, content) {
        try {
          var response = yield _apiClient.apiClient.patch(`${this.baseUrl}/messages/${messageId}/`, {
            content: content
          });
          return response.data;
        } catch (error) {
          console.error('Failed to edit message:', error);
          yield _unifiedErrorHandling.unifiedErrorHandlingService.handleNetworkError(error, {
            action: 'edit_message',
            additionalData: {
              messageId: messageId,
              content: content.substring(0, 50) + '...'
            }
          });
          throw error;
        }
      });
      function editMessage(_x6, _x7) {
        return _editMessage.apply(this, arguments);
      }
      return editMessage;
    }())
  }, {
    key: "deleteMessage",
    value: (function () {
      var _deleteMessage = (0, _asyncToGenerator2.default)(function* (messageId) {
        try {
          yield _apiClient.apiClient.delete(`${this.baseUrl}/messages/${messageId}/`);
        } catch (error) {
          console.error('Failed to delete message:', error);
          throw new Error('Failed to delete message');
        }
      });
      function deleteMessage(_x8) {
        return _deleteMessage.apply(this, arguments);
      }
      return deleteMessage;
    }())
  }, {
    key: "markMessagesAsRead",
    value: (function () {
      var _markMessagesAsRead = (0, _asyncToGenerator2.default)(function* (conversationId, messageIds) {
        try {
          yield _apiClient.apiClient.post(`${this.baseUrl}/conversations/${conversationId}/mark-read/`, {
            message_ids: messageIds
          });
        } catch (error) {
          console.error('Failed to mark messages as read:', error);
          throw new Error('Failed to mark messages as read');
        }
      });
      function markMessagesAsRead(_x9, _x0) {
        return _markMessagesAsRead.apply(this, arguments);
      }
      return markMessagesAsRead;
    }())
  }, {
    key: "addReaction",
    value: (function () {
      var _addReaction = (0, _asyncToGenerator2.default)(function* (messageId, emoji) {
        try {
          var response = yield _apiClient.apiClient.post(`${this.baseUrl}/messages/${messageId}/reactions/`, {
            emoji: emoji
          });
          return response.data;
        } catch (error) {
          console.error('Failed to add reaction:', error);
          throw new Error('Failed to add reaction');
        }
      });
      function addReaction(_x1, _x10) {
        return _addReaction.apply(this, arguments);
      }
      return addReaction;
    }())
  }, {
    key: "removeReaction",
    value: (function () {
      var _removeReaction = (0, _asyncToGenerator2.default)(function* (messageId, reactionId) {
        try {
          yield _apiClient.apiClient.delete(`${this.baseUrl}/messages/${messageId}/reactions/${reactionId}/`);
        } catch (error) {
          console.error('Failed to remove reaction:', error);
          throw new Error('Failed to remove reaction');
        }
      });
      function removeReaction(_x11, _x12) {
        return _removeReaction.apply(this, arguments);
      }
      return removeReaction;
    }())
  }, {
    key: "searchMessages",
    value: (function () {
      var _searchMessages = (0, _asyncToGenerator2.default)(function* (query, conversationId) {
        var limit = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 20;
        try {
          var params = {
            q: query,
            conversation_id: conversationId,
            limit: limit
          };
          var response = yield _apiClient.apiClient.get(`${this.baseUrl}/search/`, {
            params: params
          });
          return response.data.messages;
        } catch (error) {
          console.error('Failed to search messages:', error);
          throw new Error('Failed to search messages');
        }
      });
      function searchMessages(_x13, _x14) {
        return _searchMessages.apply(this, arguments);
      }
      return searchMessages;
    }())
  }, {
    key: "uploadAttachment",
    value: (function () {
      var _uploadAttachment = (0, _asyncToGenerator2.default)(function* (file) {
        try {
          var formData = new FormData();
          formData.append('file', file);
          var response = yield _apiClient.apiClient.post(`${this.baseUrl}/attachments/`, formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          });
          return response.data;
        } catch (error) {
          console.error('Failed to upload attachment:', error);
          throw new Error('Failed to upload attachment');
        }
      });
      function uploadAttachment(_x15) {
        return _uploadAttachment.apply(this, arguments);
      }
      return uploadAttachment;
    }())
  }, {
    key: "getConversationAnalytics",
    value: (function () {
      var _getConversationAnalytics = (0, _asyncToGenerator2.default)(function* (conversationId) {
        try {
          var response = yield _apiClient.apiClient.get(`${this.baseUrl}/conversations/${conversationId}/analytics/`);
          return response.data;
        } catch (error) {
          console.error('Failed to get conversation analytics:', error);
          throw new Error('Failed to get conversation analytics');
        }
      });
      function getConversationAnalytics(_x16) {
        return _getConversationAnalytics.apply(this, arguments);
      }
      return getConversationAnalytics;
    }())
  }, {
    key: "formatMessageTime",
    value: function formatMessageTime(timestamp) {
      var messageDate = new Date(timestamp);
      var now = new Date();
      var diffInMinutes = Math.floor((now.getTime() - messageDate.getTime()) / (1000 * 60));
      if (diffInMinutes < 1) {
        return 'Just now';
      } else if (diffInMinutes < 60) {
        return `${diffInMinutes}m ago`;
      } else if (diffInMinutes < 1440) {
        return `${Math.floor(diffInMinutes / 60)}h ago`;
      } else {
        return messageDate.toLocaleDateString();
      }
    }
  }, {
    key: "formatFileSize",
    value: function formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      var k = 1024;
      var sizes = ['Bytes', 'KB', 'MB', 'GB'];
      var i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
  }, {
    key: "validateMessage",
    value: function validateMessage(content, type) {
      if (!content.trim()) {
        return {
          isValid: false,
          error: 'Message cannot be empty'
        };
      }
      if (content.length > 5000) {
        return {
          isValid: false,
          error: 'Message is too long (max 5000 characters)'
        };
      }
      if (type === 'text' && content.includes('<script>')) {
        return {
          isValid: false,
          error: 'Invalid content detected'
        };
      }
      return {
        isValid: true
      };
    }
  }]);
}();
var messagingService = exports.messagingService = new MessagingService();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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