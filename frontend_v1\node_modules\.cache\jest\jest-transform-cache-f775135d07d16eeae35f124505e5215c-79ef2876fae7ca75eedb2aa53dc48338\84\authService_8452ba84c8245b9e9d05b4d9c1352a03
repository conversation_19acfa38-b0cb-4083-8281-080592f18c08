df88503b31d9e1f947271713e0825e0c
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.authService = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var getApiBaseUrl = function getApiBaseUrl() {
  if (!__DEV__) {
    return 'https://api.vierla.com';
  }
  return 'http://192.168.2.65:8000';
};
var API_BASE_URL = getApiBaseUrl();
var AuthService = function () {
  function AuthService() {
    (0, _classCallCheck2.default)(this, AuthService);
    this.baseUrl = API_BASE_URL;
  }
  return (0, _createClass2.default)(AuthService, [{
    key: "makeRequest",
    value: (function () {
      var _makeRequest = (0, _asyncToGenerator2.default)(function* (endpoint, options) {
        var url = `${this.baseUrl}${endpoint}`;
        console.log('🌐 Making request to:', url);
        console.log('📤 Request options:', {
          method: options.method,
          hasBody: !!options.body
        });
        try {
          var response = yield fetch(url, Object.assign({}, options, {
            headers: Object.assign({
              'Content-Type': 'application/json'
            }, options.headers)
          }));
          console.log('📥 Response status:', response.status, response.statusText);
          var data = yield response.json();
          console.log('📄 Response data keys:', Object.keys(data));
          if (!response.ok) {
            var error = data;
            var errorMessage = 'An error occurred';
            if (error.detail) {
              errorMessage = error.detail;
            } else if (error.message) {
              errorMessage = error.message;
            } else if (error.non_field_errors && error.non_field_errors.length > 0) {
              errorMessage = error.non_field_errors[0];
            } else if (error.errors) {
              var firstError = Object.values(error.errors)[0];
              if (firstError && firstError.length > 0) {
                errorMessage = firstError[0];
              }
            }
            throw new Error(errorMessage);
          }
          return data;
        } catch (error) {
          if (error instanceof Error) {
            throw error;
          }
          throw new Error('Network error occurred');
        }
      });
      function makeRequest(_x, _x2) {
        return _makeRequest.apply(this, arguments);
      }
      return makeRequest;
    }())
  }, {
    key: "login",
    value: (function () {
      var _login = (0, _asyncToGenerator2.default)(function* (credentials) {
        console.log('🔐 AuthService.login called with:', {
          email: credentials.email
        });
        console.log('🌐 API Base URL:', this.baseUrl);
        try {
          var result = yield this.makeRequest('/api/auth/login/', {
            method: 'POST',
            body: JSON.stringify(credentials)
          });
          console.log('✅ AuthService.login successful');
          return result;
        } catch (error) {
          console.error('❌ AuthService.login failed:', error);
          throw error;
        }
      });
      function login(_x3) {
        return _login.apply(this, arguments);
      }
      return login;
    }())
  }, {
    key: "register",
    value: (function () {
      var _register = (0, _asyncToGenerator2.default)(function* (userData) {
        return this.makeRequest('/api/auth/register/', {
          method: 'POST',
          body: JSON.stringify(userData)
        });
      });
      function register(_x4) {
        return _register.apply(this, arguments);
      }
      return register;
    }())
  }, {
    key: "clearAuthState",
    value: (function () {
      var _clearAuthState = (0, _asyncToGenerator2.default)(function* () {
        try {
          console.log('🧹 Clearing authentication state...');
          var AsyncStorage = yield import('@react-native-async-storage/async-storage');
          yield AsyncStorage.default.multiRemove(['auth_token', 'refresh_token', 'auth_user', 'auth-store']);
          console.log('✅ Authentication state cleared');
        } catch (error) {
          console.error('❌ Failed to clear auth state:', error);
        }
      });
      function clearAuthState() {
        return _clearAuthState.apply(this, arguments);
      }
      return clearAuthState;
    }())
  }, {
    key: "passwordlessLogin",
    value: (function () {
      var _passwordlessLogin = (0, _asyncToGenerator2.default)(function* (request) {
        return new Promise(function (resolve, reject) {
          setTimeout(function () {
            if (request.method === 'email' && request.email) {
              resolve({
                access: 'mock-access-token-passwordless',
                refresh: 'mock-refresh-token-passwordless',
                user: {
                  id: '1',
                  email: request.email,
                  first_name: 'Passwordless',
                  last_name: 'User',
                  role: 'customer',
                  is_verified: true
                }
              });
            } else if (request.method === 'phone' && request.phone) {
              resolve({
                access: 'mock-access-token-passwordless',
                refresh: 'mock-refresh-token-passwordless',
                user: {
                  id: '2',
                  email: '<EMAIL>',
                  first_name: 'Phone',
                  last_name: 'User',
                  role: 'customer',
                  is_verified: true,
                  phone: request.phone
                }
              });
            } else if (request.method === 'biometric') {
              resolve({
                access: 'mock-access-token-passwordless',
                refresh: 'mock-refresh-token-passwordless',
                user: {
                  id: '3',
                  email: '<EMAIL>',
                  first_name: 'Biometric',
                  last_name: 'User',
                  role: 'customer',
                  is_verified: true
                }
              });
            } else {
              reject(new Error('Invalid passwordless authentication request'));
            }
          }, 1000);
        });
      });
      function passwordlessLogin(_x5) {
        return _passwordlessLogin.apply(this, arguments);
      }
      return passwordlessLogin;
    }())
  }, {
    key: "refreshToken",
    value: (function () {
      var _refreshToken2 = (0, _asyncToGenerator2.default)(function* (_refreshToken) {
        return this.makeRequest('/api/auth/token/refresh/', {
          method: 'POST',
          body: JSON.stringify({
            refresh: _refreshToken
          })
        });
      });
      function refreshToken(_x6) {
        return _refreshToken2.apply(this, arguments);
      }
      return refreshToken;
    }())
  }, {
    key: "authenticateWithEmail",
    value: (function () {
      var _authenticateWithEmail = (0, _asyncToGenerator2.default)(function* (email) {
        return {
          success: true,
          user: {
            id: 'email_' + Date.now(),
            email: email,
            first_name: 'Email',
            last_name: 'User',
            role: 'customer',
            is_verified: true
          },
          token: 'mock_email_token_' + Date.now(),
          refreshToken: 'mock_email_refresh_' + Date.now()
        };
      });
      function authenticateWithEmail(_x7) {
        return _authenticateWithEmail.apply(this, arguments);
      }
      return authenticateWithEmail;
    }())
  }, {
    key: "authenticateWithPhone",
    value: (function () {
      var _authenticateWithPhone = (0, _asyncToGenerator2.default)(function* (phone) {
        return {
          success: true,
          user: {
            id: 'phone_' + Date.now(),
            phone: phone,
            first_name: 'Phone',
            last_name: 'User',
            role: 'customer',
            is_verified: true
          },
          token: 'mock_phone_token_' + Date.now(),
          refreshToken: 'mock_phone_refresh_' + Date.now()
        };
      });
      function authenticateWithPhone(_x8) {
        return _authenticateWithPhone.apply(this, arguments);
      }
      return authenticateWithPhone;
    }())
  }, {
    key: "authenticateWithBiometric",
    value: (function () {
      var _authenticateWithBiometric = (0, _asyncToGenerator2.default)(function* (userData) {
        return {
          success: true,
          user: userData,
          token: 'mock_biometric_token_' + Date.now(),
          refreshToken: 'mock_biometric_refresh_' + Date.now()
        };
      });
      function authenticateWithBiometric(_x9) {
        return _authenticateWithBiometric.apply(this, arguments);
      }
      return authenticateWithBiometric;
    }())
  }, {
    key: "logout",
    value: (function () {
      var _logout = (0, _asyncToGenerator2.default)(function* (refreshToken) {
        try {
          yield this.makeRequest('/api/auth/logout/', {
            method: 'POST',
            body: JSON.stringify({
              refresh: refreshToken
            })
          });
        } catch (error) {
          console.warn('Logout API call failed:', error);
        }
      });
      function logout(_x0) {
        return _logout.apply(this, arguments);
      }
      return logout;
    }())
  }, {
    key: "getProfile",
    value: (function () {
      var _getProfile = (0, _asyncToGenerator2.default)(function* (token) {
        return this.makeRequest('/api/auth/profile/', {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
      });
      function getProfile(_x1) {
        return _getProfile.apply(this, arguments);
      }
      return getProfile;
    }())
  }, {
    key: "updateProfile",
    value: (function () {
      var _updateProfile = (0, _asyncToGenerator2.default)(function* (profileData, token) {
        return this.makeRequest('/api/auth/profile/', {
          method: 'PATCH',
          body: JSON.stringify(profileData),
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
      });
      function updateProfile(_x10, _x11) {
        return _updateProfile.apply(this, arguments);
      }
      return updateProfile;
    }())
  }, {
    key: "requestPasswordReset",
    value: (function () {
      var _requestPasswordReset = (0, _asyncToGenerator2.default)(function* (data) {
        return this.makeRequest('/api/auth/password-reset/', {
          method: 'POST',
          body: JSON.stringify(data)
        });
      });
      function requestPasswordReset(_x12) {
        return _requestPasswordReset.apply(this, arguments);
      }
      return requestPasswordReset;
    }())
  }, {
    key: "confirmPasswordReset",
    value: (function () {
      var _confirmPasswordReset = (0, _asyncToGenerator2.default)(function* (data) {
        return this.makeRequest('/api/auth/password-reset/confirm/', {
          method: 'POST',
          body: JSON.stringify(data)
        });
      });
      function confirmPasswordReset(_x13) {
        return _confirmPasswordReset.apply(this, arguments);
      }
      return confirmPasswordReset;
    }())
  }, {
    key: "changePassword",
    value: (function () {
      var _changePassword = (0, _asyncToGenerator2.default)(function* (data, token) {
        return this.makeRequest('/api/auth/change-password/', {
          method: 'POST',
          body: JSON.stringify(data),
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
      });
      function changePassword(_x14, _x15) {
        return _changePassword.apply(this, arguments);
      }
      return changePassword;
    }())
  }, {
    key: "validateToken",
    value: (function () {
      var _validateToken = (0, _asyncToGenerator2.default)(function* (token) {
        try {
          yield this.makeRequest('/api/auth/validate-token/', {
            method: 'POST',
            headers: {
              Authorization: `Bearer ${token}`
            }
          });
          return true;
        } catch (_unused) {
          return false;
        }
      });
      function validateToken(_x16) {
        return _validateToken.apply(this, arguments);
      }
      return validateToken;
    }())
  }, {
    key: "verifyEmail",
    value: (function () {
      var _verifyEmail = (0, _asyncToGenerator2.default)(function* (token) {
        return this.makeRequest('/api/auth/verify-email/', {
          method: 'POST',
          body: JSON.stringify({
            token: token
          })
        });
      });
      function verifyEmail(_x17) {
        return _verifyEmail.apply(this, arguments);
      }
      return verifyEmail;
    }())
  }, {
    key: "resendVerificationEmail",
    value: (function () {
      var _resendVerificationEmail = (0, _asyncToGenerator2.default)(function* (email) {
        return this.makeRequest('/api/auth/resend-verification/', {
          method: 'POST',
          body: JSON.stringify({
            email: email
          })
        });
      });
      function resendVerificationEmail(_x18) {
        return _resendVerificationEmail.apply(this, arguments);
      }
      return resendVerificationEmail;
    }())
  }]);
}();
var authService = exports.authService = new AuthService();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJnZXRBcGlCYXNlVXJsIiwiX19ERVZfXyIsIkFQSV9CQVNFX1VSTCIsIkF1dGhTZXJ2aWNlIiwiX2NsYXNzQ2FsbENoZWNrMiIsImRlZmF1bHQiLCJiYXNlVXJsIiwiX2NyZWF0ZUNsYXNzMiIsImtleSIsInZhbHVlIiwiX21ha2VSZXF1ZXN0IiwiX2FzeW5jVG9HZW5lcmF0b3IyIiwiZW5kcG9pbnQiLCJvcHRpb25zIiwidXJsIiwiY29uc29sZSIsImxvZyIsIm1ldGhvZCIsImhhc0JvZHkiLCJib2R5IiwicmVzcG9uc2UiLCJmZXRjaCIsIk9iamVjdCIsImFzc2lnbiIsImhlYWRlcnMiLCJzdGF0dXMiLCJzdGF0dXNUZXh0IiwiZGF0YSIsImpzb24iLCJrZXlzIiwib2siLCJlcnJvciIsImVycm9yTWVzc2FnZSIsImRldGFpbCIsIm1lc3NhZ2UiLCJub25fZmllbGRfZXJyb3JzIiwibGVuZ3RoIiwiZXJyb3JzIiwiZmlyc3RFcnJvciIsInZhbHVlcyIsIkVycm9yIiwibWFrZVJlcXVlc3QiLCJfeCIsIl94MiIsImFwcGx5IiwiYXJndW1lbnRzIiwiX2xvZ2luIiwiY3JlZGVudGlhbHMiLCJlbWFpbCIsInJlc3VsdCIsIkpTT04iLCJzdHJpbmdpZnkiLCJsb2dpbiIsIl94MyIsIl9yZWdpc3RlciIsInVzZXJEYXRhIiwicmVnaXN0ZXIiLCJfeDQiLCJfY2xlYXJBdXRoU3RhdGUiLCJBc3luY1N0b3JhZ2UiLCJtdWx0aVJlbW92ZSIsImNsZWFyQXV0aFN0YXRlIiwiX3Bhc3N3b3JkbGVzc0xvZ2luIiwicmVxdWVzdCIsIlByb21pc2UiLCJyZXNvbHZlIiwicmVqZWN0Iiwic2V0VGltZW91dCIsImFjY2VzcyIsInJlZnJlc2giLCJ1c2VyIiwiaWQiLCJmaXJzdF9uYW1lIiwibGFzdF9uYW1lIiwicm9sZSIsImlzX3ZlcmlmaWVkIiwicGhvbmUiLCJwYXNzd29yZGxlc3NMb2dpbiIsIl94NSIsIl9yZWZyZXNoVG9rZW4yIiwicmVmcmVzaFRva2VuIiwiX3g2IiwiX2F1dGhlbnRpY2F0ZVdpdGhFbWFpbCIsInN1Y2Nlc3MiLCJEYXRlIiwibm93IiwidG9rZW4iLCJhdXRoZW50aWNhdGVXaXRoRW1haWwiLCJfeDciLCJfYXV0aGVudGljYXRlV2l0aFBob25lIiwiYXV0aGVudGljYXRlV2l0aFBob25lIiwiX3g4IiwiX2F1dGhlbnRpY2F0ZVdpdGhCaW9tZXRyaWMiLCJhdXRoZW50aWNhdGVXaXRoQmlvbWV0cmljIiwiX3g5IiwiX2xvZ291dCIsIndhcm4iLCJsb2dvdXQiLCJfeDAiLCJfZ2V0UHJvZmlsZSIsIkF1dGhvcml6YXRpb24iLCJnZXRQcm9maWxlIiwiX3gxIiwiX3VwZGF0ZVByb2ZpbGUiLCJwcm9maWxlRGF0YSIsInVwZGF0ZVByb2ZpbGUiLCJfeDEwIiwiX3gxMSIsIl9yZXF1ZXN0UGFzc3dvcmRSZXNldCIsInJlcXVlc3RQYXNzd29yZFJlc2V0IiwiX3gxMiIsIl9jb25maXJtUGFzc3dvcmRSZXNldCIsImNvbmZpcm1QYXNzd29yZFJlc2V0IiwiX3gxMyIsIl9jaGFuZ2VQYXNzd29yZCIsImNoYW5nZVBhc3N3b3JkIiwiX3gxNCIsIl94MTUiLCJfdmFsaWRhdGVUb2tlbiIsIl91bnVzZWQiLCJ2YWxpZGF0ZVRva2VuIiwiX3gxNiIsIl92ZXJpZnlFbWFpbCIsInZlcmlmeUVtYWlsIiwiX3gxNyIsIl9yZXNlbmRWZXJpZmljYXRpb25FbWFpbCIsInJlc2VuZFZlcmlmaWNhdGlvbkVtYWlsIiwiX3gxOCIsImF1dGhTZXJ2aWNlIiwiZXhwb3J0cyJdLCJzb3VyY2VzIjpbImF1dGhTZXJ2aWNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQXV0aGVudGljYXRpb24gU2VydmljZSAtIEFQSSBJbnRlZ3JhdGlvbiBmb3IgRnJvbnRlbmQgVjFcbiAqXG4gKiBTZXJ2aWNlIENvbnRyYWN0OlxuICogLSBIYW5kbGVzIGF1dGhlbnRpY2F0aW9uIEFQSSBjYWxscyB0byBiYWNrZW5kXG4gKiAtIFByb3ZpZGVzIGxvZ2luIGFuZCByZWdpc3RyYXRpb24gZnVuY3Rpb25hbGl0eVxuICogLSBTdXBwb3J0cyBkdWFsLXJvbGUgYXV0aGVudGljYXRpb24gKGN1c3RvbWVyL3Byb3ZpZGVyKVxuICogLSBJbXBsZW1lbnRzIHByb3BlciBlcnJvciBoYW5kbGluZyBhbmQgcmVzcG9uc2UgcGFyc2luZ1xuICogLSBGb2xsb3dzIFRERCBtZXRob2RvbG9neSB3aXRoIGNvbXByZWhlbnNpdmUgdGVzdCBjb3ZlcmFnZVxuICpcbiAqIEB2ZXJzaW9uIDEuMC4wXG4gKiBAYXV0aG9yIFZpZXJsYSBEZXZlbG9wbWVudCBUZWFtXG4gKi9cblxuaW1wb3J0IHsgUGxhdGZvcm0gfSBmcm9tICdyZWFjdC1uYXRpdmUnO1xuXG4vLyBBUEkgQ29uZmlndXJhdGlvblxuY29uc3QgZ2V0QXBpQmFzZVVybCA9ICgpID0+IHtcbiAgaWYgKCFfX0RFVl9fKSB7XG4gICAgcmV0dXJuICdodHRwczovL2FwaS52aWVybGEuY29tJztcbiAgfVxuXG4gIC8vIEluIGRldmVsb3BtZW50LCB1c2UgdGhlIGJhY2tlbmQgc2VydmVyIGFkZHJlc3NcbiAgcmV0dXJuICdodHRwOi8vMTkyLjE2OC4yLjY1OjgwMDAnO1xufTtcblxuY29uc3QgQVBJX0JBU0VfVVJMID0gZ2V0QXBpQmFzZVVybCgpO1xuXG4vLyBSZXF1ZXN0L1Jlc3BvbnNlIFR5cGVzXG5leHBvcnQgaW50ZXJmYWNlIExvZ2luUmVxdWVzdCB7XG4gIGVtYWlsOiBzdHJpbmc7XG4gIHBhc3N3b3JkOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUmVnaXN0ZXJSZXF1ZXN0IHtcbiAgZmlyc3RfbmFtZTogc3RyaW5nO1xuICBsYXN0X25hbWU6IHN0cmluZztcbiAgZW1haWw6IHN0cmluZztcbiAgcGFzc3dvcmQ6IHN0cmluZztcbiAgcm9sZTogJ2N1c3RvbWVyJyB8ICdzZXJ2aWNlX3Byb3ZpZGVyJztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBQYXNzd29yZGxlc3NMb2dpblJlcXVlc3Qge1xuICBtZXRob2Q6ICdlbWFpbCcgfCAncGhvbmUnIHwgJ2Jpb21ldHJpYyc7XG4gIGVtYWlsPzogc3RyaW5nO1xuICBwaG9uZT86IHN0cmluZztcbiAgdmVyaWZpY2F0aW9uQ29kZT86IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBBdXRoUmVzcG9uc2Uge1xuICBhY2Nlc3M6IHN0cmluZztcbiAgcmVmcmVzaDogc3RyaW5nO1xuICB1c2VyOiB7XG4gICAgaWQ6IHN0cmluZztcbiAgICBlbWFpbDogc3RyaW5nO1xuICAgIGZpcnN0X25hbWU6IHN0cmluZztcbiAgICBsYXN0X25hbWU6IHN0cmluZztcbiAgICByb2xlOiAnY3VzdG9tZXInIHwgJ3NlcnZpY2VfcHJvdmlkZXInO1xuICAgIGlzX3ZlcmlmaWVkOiBib29sZWFuO1xuICAgIHBob25lPzogc3RyaW5nO1xuICAgIGF2YXRhcj86IHN0cmluZztcbiAgfTtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBBcGlFcnJvciB7XG4gIGRldGFpbD86IHN0cmluZztcbiAgbWVzc2FnZT86IHN0cmluZztcbiAgZXJyb3JzPzogUmVjb3JkPHN0cmluZywgc3RyaW5nW10+O1xuICBub25fZmllbGRfZXJyb3JzPzogc3RyaW5nW107XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUHJvZmlsZVVwZGF0ZVJlcXVlc3Qge1xuICBmaXJzdF9uYW1lPzogc3RyaW5nO1xuICBsYXN0X25hbWU/OiBzdHJpbmc7XG4gIHBob25lX251bWJlcj86IHN0cmluZztcbiAgcHJvZmlsZV9pbWFnZT86IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBQYXNzd29yZFJlc2V0UmVxdWVzdCB7XG4gIGVtYWlsOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUGFzc3dvcmRSZXNldENvbmZpcm1SZXF1ZXN0IHtcbiAgdG9rZW46IHN0cmluZztcbiAgbmV3X3Bhc3N3b3JkOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQ2hhbmdlUGFzc3dvcmRSZXF1ZXN0IHtcbiAgY3VycmVudF9wYXNzd29yZDogc3RyaW5nO1xuICBuZXdfcGFzc3dvcmQ6IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBFbWFpbFZlcmlmaWNhdGlvblJlcXVlc3Qge1xuICB0b2tlbjogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFJlc2VuZFZlcmlmaWNhdGlvblJlcXVlc3Qge1xuICBlbWFpbDogc3RyaW5nO1xufVxuXG5jbGFzcyBBdXRoU2VydmljZSB7XG4gIHByaXZhdGUgYmFzZVVybCA9IEFQSV9CQVNFX1VSTDtcblxuICAvKipcbiAgICogTWFrZSBIVFRQIHJlcXVlc3Qgd2l0aCBwcm9wZXIgZXJyb3IgaGFuZGxpbmdcbiAgICovXG4gIHByaXZhdGUgYXN5bmMgbWFrZVJlcXVlc3Q8VD4oXG4gICAgZW5kcG9pbnQ6IHN0cmluZyxcbiAgICBvcHRpb25zOiBSZXF1ZXN0SW5pdCxcbiAgKTogUHJvbWlzZTxUPiB7XG4gICAgY29uc3QgdXJsID0gYCR7dGhpcy5iYXNlVXJsfSR7ZW5kcG9pbnR9YDtcbiAgICBjb25zb2xlLmxvZygn8J+MkCBNYWtpbmcgcmVxdWVzdCB0bzonLCB1cmwpO1xuICAgIGNvbnNvbGUubG9nKCfwn5OkIFJlcXVlc3Qgb3B0aW9uczonLCB7XG4gICAgICBtZXRob2Q6IG9wdGlvbnMubWV0aG9kLFxuICAgICAgaGFzQm9keTogISFvcHRpb25zLmJvZHksXG4gICAgfSk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCh1cmwsIHtcbiAgICAgICAgLi4ub3B0aW9ucyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICAgLi4ub3B0aW9ucy5oZWFkZXJzLFxuICAgICAgICB9LFxuICAgICAgfSk7XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5OlIFJlc3BvbnNlIHN0YXR1czonLCByZXNwb25zZS5zdGF0dXMsIHJlc3BvbnNlLnN0YXR1c1RleHQpO1xuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OEIFJlc3BvbnNlIGRhdGEga2V5czonLCBPYmplY3Qua2V5cyhkYXRhKSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgLy8gSGFuZGxlIEFQSSBlcnJvcnNcbiAgICAgICAgY29uc3QgZXJyb3IgPSBkYXRhIGFzIEFwaUVycm9yO1xuICAgICAgICBsZXQgZXJyb3JNZXNzYWdlID0gJ0FuIGVycm9yIG9jY3VycmVkJztcblxuICAgICAgICBpZiAoZXJyb3IuZGV0YWlsKSB7XG4gICAgICAgICAgZXJyb3JNZXNzYWdlID0gZXJyb3IuZGV0YWlsO1xuICAgICAgICB9IGVsc2UgaWYgKGVycm9yLm1lc3NhZ2UpIHtcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBlcnJvci5tZXNzYWdlO1xuICAgICAgICB9IGVsc2UgaWYgKFxuICAgICAgICAgIGVycm9yLm5vbl9maWVsZF9lcnJvcnMgJiZcbiAgICAgICAgICBlcnJvci5ub25fZmllbGRfZXJyb3JzLmxlbmd0aCA+IDBcbiAgICAgICAgKSB7XG4gICAgICAgICAgZXJyb3JNZXNzYWdlID0gZXJyb3Iubm9uX2ZpZWxkX2Vycm9yc1swXTtcbiAgICAgICAgfSBlbHNlIGlmIChlcnJvci5lcnJvcnMpIHtcbiAgICAgICAgICAvLyBIYW5kbGUgZmllbGQtc3BlY2lmaWMgZXJyb3JzXG4gICAgICAgICAgY29uc3QgZmlyc3RFcnJvciA9IE9iamVjdC52YWx1ZXMoZXJyb3IuZXJyb3JzKVswXTtcbiAgICAgICAgICBpZiAoZmlyc3RFcnJvciAmJiBmaXJzdEVycm9yLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgIGVycm9yTWVzc2FnZSA9IGZpcnN0RXJyb3JbMF07XG4gICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yTWVzc2FnZSk7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiBkYXRhO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBFcnJvcikge1xuICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgIH1cbiAgICAgIHRocm93IG5ldyBFcnJvcignTmV0d29yayBlcnJvciBvY2N1cnJlZCcpO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBMb2dpbiB1c2VyIHdpdGggZW1haWwgYW5kIHBhc3N3b3JkXG4gICAqL1xuICBhc3luYyBsb2dpbihjcmVkZW50aWFsczogTG9naW5SZXF1ZXN0KTogUHJvbWlzZTxBdXRoUmVzcG9uc2U+IHtcbiAgICBjb25zb2xlLmxvZygn8J+UkCBBdXRoU2VydmljZS5sb2dpbiBjYWxsZWQgd2l0aDonLCB7XG4gICAgICBlbWFpbDogY3JlZGVudGlhbHMuZW1haWwsXG4gICAgfSk7XG4gICAgY29uc29sZS5sb2coJ/CfjJAgQVBJIEJhc2UgVVJMOicsIHRoaXMuYmFzZVVybCk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgdGhpcy5tYWtlUmVxdWVzdDxBdXRoUmVzcG9uc2U+KCcvYXBpL2F1dGgvbG9naW4vJywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoY3JlZGVudGlhbHMpLFxuICAgICAgfSk7XG5cbiAgICAgIGNvbnNvbGUubG9nKCfinIUgQXV0aFNlcnZpY2UubG9naW4gc3VjY2Vzc2Z1bCcpO1xuICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIEF1dGhTZXJ2aWNlLmxvZ2luIGZhaWxlZDonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogUmVnaXN0ZXIgbmV3IHVzZXIgd2l0aCByb2xlIHNlbGVjdGlvblxuICAgKi9cbiAgYXN5bmMgcmVnaXN0ZXIodXNlckRhdGE6IFJlZ2lzdGVyUmVxdWVzdCk6IFByb21pc2U8QXV0aFJlc3BvbnNlPiB7XG4gICAgcmV0dXJuIHRoaXMubWFrZVJlcXVlc3Q8QXV0aFJlc3BvbnNlPignL2FwaS9hdXRoL3JlZ2lzdGVyLycsIHtcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkodXNlckRhdGEpLFxuICAgIH0pO1xuICB9XG5cbiAgLyoqXG4gICAqIENsZWFyIGF1dGhlbnRpY2F0aW9uIHN0YXRlIGFuZCBzdG9yYWdlXG4gICAqL1xuICBhc3luYyBjbGVhckF1dGhTdGF0ZSgpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ/Cfp7kgQ2xlYXJpbmcgYXV0aGVudGljYXRpb24gc3RhdGUuLi4nKTtcblxuICAgICAgLy8gQ2xlYXIgQXN5bmNTdG9yYWdlXG4gICAgICBjb25zdCBBc3luY1N0b3JhZ2UgPSBhd2FpdCBpbXBvcnQoXG4gICAgICAgICdAcmVhY3QtbmF0aXZlLWFzeW5jLXN0b3JhZ2UvYXN5bmMtc3RvcmFnZSdcbiAgICAgICk7XG4gICAgICBhd2FpdCBBc3luY1N0b3JhZ2UuZGVmYXVsdC5tdWx0aVJlbW92ZShbXG4gICAgICAgICdhdXRoX3Rva2VuJyxcbiAgICAgICAgJ3JlZnJlc2hfdG9rZW4nLFxuICAgICAgICAnYXV0aF91c2VyJyxcbiAgICAgICAgJ2F1dGgtc3RvcmUnLFxuICAgICAgXSk7XG5cbiAgICAgIGNvbnNvbGUubG9nKCfinIUgQXV0aGVudGljYXRpb24gc3RhdGUgY2xlYXJlZCcpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRmFpbGVkIHRvIGNsZWFyIGF1dGggc3RhdGU6JywgZXJyb3IpO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBQYXNzd29yZGxlc3MgYXV0aGVudGljYXRpb24gbG9naW5cbiAgICovXG4gIGFzeW5jIHBhc3N3b3JkbGVzc0xvZ2luKFxuICAgIHJlcXVlc3Q6IFBhc3N3b3JkbGVzc0xvZ2luUmVxdWVzdCxcbiAgKTogUHJvbWlzZTxBdXRoUmVzcG9uc2U+IHtcbiAgICAvLyBGb3Igbm93LCBzaW11bGF0ZSBwYXNzd29yZGxlc3MgYXV0aGVudGljYXRpb25cbiAgICAvLyBJbiBwcm9kdWN0aW9uLCB0aGlzIHdvdWxkIGludGVncmF0ZSB3aXRoIGFjdHVhbCBwYXNzd29yZGxlc3MgYXV0aCBwcm92aWRlcnNcbiAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIC8vIFNpbXVsYXRlIHN1Y2Nlc3NmdWwgYXV0aGVudGljYXRpb25cbiAgICAgICAgaWYgKHJlcXVlc3QubWV0aG9kID09PSAnZW1haWwnICYmIHJlcXVlc3QuZW1haWwpIHtcbiAgICAgICAgICByZXNvbHZlKHtcbiAgICAgICAgICAgIGFjY2VzczogJ21vY2stYWNjZXNzLXRva2VuLXBhc3N3b3JkbGVzcycsXG4gICAgICAgICAgICByZWZyZXNoOiAnbW9jay1yZWZyZXNoLXRva2VuLXBhc3N3b3JkbGVzcycsXG4gICAgICAgICAgICB1c2VyOiB7XG4gICAgICAgICAgICAgIGlkOiAnMScsXG4gICAgICAgICAgICAgIGVtYWlsOiByZXF1ZXN0LmVtYWlsLFxuICAgICAgICAgICAgICBmaXJzdF9uYW1lOiAnUGFzc3dvcmRsZXNzJyxcbiAgICAgICAgICAgICAgbGFzdF9uYW1lOiAnVXNlcicsXG4gICAgICAgICAgICAgIHJvbGU6ICdjdXN0b21lcicsXG4gICAgICAgICAgICAgIGlzX3ZlcmlmaWVkOiB0cnVlLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICB9KTtcbiAgICAgICAgfSBlbHNlIGlmIChyZXF1ZXN0Lm1ldGhvZCA9PT0gJ3Bob25lJyAmJiByZXF1ZXN0LnBob25lKSB7XG4gICAgICAgICAgcmVzb2x2ZSh7XG4gICAgICAgICAgICBhY2Nlc3M6ICdtb2NrLWFjY2Vzcy10b2tlbi1wYXNzd29yZGxlc3MnLFxuICAgICAgICAgICAgcmVmcmVzaDogJ21vY2stcmVmcmVzaC10b2tlbi1wYXNzd29yZGxlc3MnLFxuICAgICAgICAgICAgdXNlcjoge1xuICAgICAgICAgICAgICBpZDogJzInLFxuICAgICAgICAgICAgICBlbWFpbDogJ3Bob25lQGV4YW1wbGUuY29tJyxcbiAgICAgICAgICAgICAgZmlyc3RfbmFtZTogJ1Bob25lJyxcbiAgICAgICAgICAgICAgbGFzdF9uYW1lOiAnVXNlcicsXG4gICAgICAgICAgICAgIHJvbGU6ICdjdXN0b21lcicsXG4gICAgICAgICAgICAgIGlzX3ZlcmlmaWVkOiB0cnVlLFxuICAgICAgICAgICAgICBwaG9uZTogcmVxdWVzdC5waG9uZSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgfSk7XG4gICAgICAgIH0gZWxzZSBpZiAocmVxdWVzdC5tZXRob2QgPT09ICdiaW9tZXRyaWMnKSB7XG4gICAgICAgICAgcmVzb2x2ZSh7XG4gICAgICAgICAgICBhY2Nlc3M6ICdtb2NrLWFjY2Vzcy10b2tlbi1wYXNzd29yZGxlc3MnLFxuICAgICAgICAgICAgcmVmcmVzaDogJ21vY2stcmVmcmVzaC10b2tlbi1wYXNzd29yZGxlc3MnLFxuICAgICAgICAgICAgdXNlcjoge1xuICAgICAgICAgICAgICBpZDogJzMnLFxuICAgICAgICAgICAgICBlbWFpbDogJ2Jpb21ldHJpY0BleGFtcGxlLmNvbScsXG4gICAgICAgICAgICAgIGZpcnN0X25hbWU6ICdCaW9tZXRyaWMnLFxuICAgICAgICAgICAgICBsYXN0X25hbWU6ICdVc2VyJyxcbiAgICAgICAgICAgICAgcm9sZTogJ2N1c3RvbWVyJyxcbiAgICAgICAgICAgICAgaXNfdmVyaWZpZWQ6IHRydWUsXG4gICAgICAgICAgICB9LFxuICAgICAgICAgIH0pO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHJlamVjdChuZXcgRXJyb3IoJ0ludmFsaWQgcGFzc3dvcmRsZXNzIGF1dGhlbnRpY2F0aW9uIHJlcXVlc3QnKSk7XG4gICAgICAgIH1cbiAgICAgIH0sIDEwMDApO1xuICAgIH0pO1xuICB9XG5cbiAgLyoqXG4gICAqIFJlZnJlc2ggYXV0aGVudGljYXRpb24gdG9rZW5cbiAgICovXG4gIGFzeW5jIHJlZnJlc2hUb2tlbihyZWZyZXNoVG9rZW46IHN0cmluZyk6IFByb21pc2U8eyBhY2Nlc3M6IHN0cmluZyB9PiB7XG4gICAgcmV0dXJuIHRoaXMubWFrZVJlcXVlc3Q8eyBhY2Nlc3M6IHN0cmluZyB9PignL2FwaS9hdXRoL3Rva2VuL3JlZnJlc2gvJywge1xuICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IHJlZnJlc2g6IHJlZnJlc2hUb2tlbiB9KSxcbiAgICB9KTtcbiAgfVxuXG4gIC8qKlxuICAgKiBBdXRoZW50aWNhdGUgdXNlciB3aXRoIGVtYWlsIChmb3IgbWFnaWMgbGluaylcbiAgICovXG4gIGFzeW5jIGF1dGhlbnRpY2F0ZVdpdGhFbWFpbChlbWFpbDogc3RyaW5nKTogUHJvbWlzZTxhbnk+IHtcbiAgICAvLyBJbiBhIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQgdmFsaWRhdGUgdGhlIGVtYWlsIGFnYWluc3QgdGhlIGJhY2tlbmRcbiAgICAvLyBGb3IgZGVtbyBwdXJwb3Nlcywgd2UnbGwgY3JlYXRlIGEgbW9jayB1c2VyXG4gICAgcmV0dXJuIHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICB1c2VyOiB7XG4gICAgICAgIGlkOiAnZW1haWxfJyArIERhdGUubm93KCksXG4gICAgICAgIGVtYWlsLFxuICAgICAgICBmaXJzdF9uYW1lOiAnRW1haWwnLFxuICAgICAgICBsYXN0X25hbWU6ICdVc2VyJyxcbiAgICAgICAgcm9sZTogJ2N1c3RvbWVyJyxcbiAgICAgICAgaXNfdmVyaWZpZWQ6IHRydWUsXG4gICAgICB9LFxuICAgICAgdG9rZW46ICdtb2NrX2VtYWlsX3Rva2VuXycgKyBEYXRlLm5vdygpLFxuICAgICAgcmVmcmVzaFRva2VuOiAnbW9ja19lbWFpbF9yZWZyZXNoXycgKyBEYXRlLm5vdygpLFxuICAgIH07XG4gIH1cblxuICAvKipcbiAgICogQXV0aGVudGljYXRlIHVzZXIgd2l0aCBwaG9uZSAoZm9yIFNNUyBPVFApXG4gICAqL1xuICBhc3luYyBhdXRoZW50aWNhdGVXaXRoUGhvbmUocGhvbmU6IHN0cmluZyk6IFByb21pc2U8YW55PiB7XG4gICAgLy8gSW4gYSByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkIHZhbGlkYXRlIHRoZSBwaG9uZSBhZ2FpbnN0IHRoZSBiYWNrZW5kXG4gICAgLy8gRm9yIGRlbW8gcHVycG9zZXMsIHdlJ2xsIGNyZWF0ZSBhIG1vY2sgdXNlclxuICAgIHJldHVybiB7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgdXNlcjoge1xuICAgICAgICBpZDogJ3Bob25lXycgKyBEYXRlLm5vdygpLFxuICAgICAgICBwaG9uZSxcbiAgICAgICAgZmlyc3RfbmFtZTogJ1Bob25lJyxcbiAgICAgICAgbGFzdF9uYW1lOiAnVXNlcicsXG4gICAgICAgIHJvbGU6ICdjdXN0b21lcicsXG4gICAgICAgIGlzX3ZlcmlmaWVkOiB0cnVlLFxuICAgICAgfSxcbiAgICAgIHRva2VuOiAnbW9ja19waG9uZV90b2tlbl8nICsgRGF0ZS5ub3coKSxcbiAgICAgIHJlZnJlc2hUb2tlbjogJ21vY2tfcGhvbmVfcmVmcmVzaF8nICsgRGF0ZS5ub3coKSxcbiAgICB9O1xuICB9XG5cbiAgLyoqXG4gICAqIEF1dGhlbnRpY2F0ZSB1c2VyIHdpdGggYmlvbWV0cmljIGRhdGFcbiAgICovXG4gIGFzeW5jIGF1dGhlbnRpY2F0ZVdpdGhCaW9tZXRyaWModXNlckRhdGE6IGFueSk6IFByb21pc2U8YW55PiB7XG4gICAgLy8gSW4gYSByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkIHZhbGlkYXRlIGJpb21ldHJpYyBkYXRhIGFnYWluc3QgdGhlIGJhY2tlbmRcbiAgICAvLyBGb3IgZGVtbyBwdXJwb3Nlcywgd2UnbGwgcmV0dXJuIHRoZSBzdG9yZWQgdXNlciBkYXRhXG4gICAgcmV0dXJuIHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICB1c2VyOiB1c2VyRGF0YSxcbiAgICAgIHRva2VuOiAnbW9ja19iaW9tZXRyaWNfdG9rZW5fJyArIERhdGUubm93KCksXG4gICAgICByZWZyZXNoVG9rZW46ICdtb2NrX2Jpb21ldHJpY19yZWZyZXNoXycgKyBEYXRlLm5vdygpLFxuICAgIH07XG4gIH1cblxuICAvKipcbiAgICogTG9nb3V0IHVzZXIgKG9wdGlvbmFsIC0gZm9yIHNlcnZlci1zaWRlIGxvZ291dClcbiAgICovXG4gIGFzeW5jIGxvZ291dChyZWZyZXNoVG9rZW46IHN0cmluZyk6IFByb21pc2U8dm9pZD4ge1xuICAgIHRyeSB7XG4gICAgICBhd2FpdCB0aGlzLm1ha2VSZXF1ZXN0PHZvaWQ+KCcvYXBpL2F1dGgvbG9nb3V0LycsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgcmVmcmVzaDogcmVmcmVzaFRva2VuIH0pLFxuICAgICAgfSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIC8vIExvZ291dCBlcnJvcnMgYXJlIG5vdCBjcml0aWNhbCAtIHVzZXIgY2FuIHN0aWxsIGJlIGxvZ2dlZCBvdXQgbG9jYWxseVxuICAgICAgY29uc29sZS53YXJuKCdMb2dvdXQgQVBJIGNhbGwgZmFpbGVkOicsIGVycm9yKTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogR2V0IHVzZXIgcHJvZmlsZVxuICAgKi9cbiAgYXN5bmMgZ2V0UHJvZmlsZSh0b2tlbjogc3RyaW5nKTogUHJvbWlzZTxBdXRoUmVzcG9uc2VbJ3VzZXInXT4ge1xuICAgIHJldHVybiB0aGlzLm1ha2VSZXF1ZXN0PEF1dGhSZXNwb25zZVsndXNlciddPignL2FwaS9hdXRoL3Byb2ZpbGUvJywge1xuICAgICAgbWV0aG9kOiAnR0VUJyxcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAsXG4gICAgICB9LFxuICAgIH0pO1xuICB9XG5cbiAgLyoqXG4gICAqIFVwZGF0ZSB1c2VyIHByb2ZpbGVcbiAgICovXG4gIGFzeW5jIHVwZGF0ZVByb2ZpbGUoXG4gICAgcHJvZmlsZURhdGE6IFByb2ZpbGVVcGRhdGVSZXF1ZXN0LFxuICAgIHRva2VuOiBzdHJpbmcsXG4gICk6IFByb21pc2U8QXV0aFJlc3BvbnNlWyd1c2VyJ10+IHtcbiAgICByZXR1cm4gdGhpcy5tYWtlUmVxdWVzdDxBdXRoUmVzcG9uc2VbJ3VzZXInXT4oJy9hcGkvYXV0aC9wcm9maWxlLycsIHtcbiAgICAgIG1ldGhvZDogJ1BBVENIJyxcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHByb2ZpbGVEYXRhKSxcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAsXG4gICAgICB9LFxuICAgIH0pO1xuICB9XG5cbiAgLyoqXG4gICAqIFJlcXVlc3QgcGFzc3dvcmQgcmVzZXRcbiAgICovXG4gIGFzeW5jIHJlcXVlc3RQYXNzd29yZFJlc2V0KFxuICAgIGRhdGE6IFBhc3N3b3JkUmVzZXRSZXF1ZXN0LFxuICApOiBQcm9taXNlPHsgbWVzc2FnZTogc3RyaW5nIH0+IHtcbiAgICByZXR1cm4gdGhpcy5tYWtlUmVxdWVzdDx7IG1lc3NhZ2U6IHN0cmluZyB9PignL2FwaS9hdXRoL3Bhc3N3b3JkLXJlc2V0LycsIHtcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoZGF0YSksXG4gICAgfSk7XG4gIH1cblxuICAvKipcbiAgICogQ29uZmlybSBwYXNzd29yZCByZXNldFxuICAgKi9cbiAgYXN5bmMgY29uZmlybVBhc3N3b3JkUmVzZXQoXG4gICAgZGF0YTogUGFzc3dvcmRSZXNldENvbmZpcm1SZXF1ZXN0LFxuICApOiBQcm9taXNlPHsgbWVzc2FnZTogc3RyaW5nIH0+IHtcbiAgICByZXR1cm4gdGhpcy5tYWtlUmVxdWVzdDx7IG1lc3NhZ2U6IHN0cmluZyB9PihcbiAgICAgICcvYXBpL2F1dGgvcGFzc3dvcmQtcmVzZXQvY29uZmlybS8nLFxuICAgICAge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoZGF0YSksXG4gICAgICB9LFxuICAgICk7XG4gIH1cblxuICAvKipcbiAgICogQ2hhbmdlIHBhc3N3b3JkIChhdXRoZW50aWNhdGVkIHVzZXIpXG4gICAqL1xuICBhc3luYyBjaGFuZ2VQYXNzd29yZChcbiAgICBkYXRhOiBDaGFuZ2VQYXNzd29yZFJlcXVlc3QsXG4gICAgdG9rZW46IHN0cmluZyxcbiAgKTogUHJvbWlzZTx7IG1lc3NhZ2U6IHN0cmluZyB9PiB7XG4gICAgcmV0dXJuIHRoaXMubWFrZVJlcXVlc3Q8eyBtZXNzYWdlOiBzdHJpbmcgfT4oJy9hcGkvYXV0aC9jaGFuZ2UtcGFzc3dvcmQvJywge1xuICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShkYXRhKSxcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAsXG4gICAgICB9LFxuICAgIH0pO1xuICB9XG5cbiAgLyoqXG4gICAqIFZhbGlkYXRlIHRva2VuIHdpdGggYmFja2VuZFxuICAgKi9cbiAgYXN5bmMgdmFsaWRhdGVUb2tlbih0b2tlbjogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiB7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IHRoaXMubWFrZVJlcXVlc3QoJy9hcGkvYXV0aC92YWxpZGF0ZS10b2tlbi8nLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAsXG4gICAgICAgIH0sXG4gICAgICB9KTtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH0gY2F0Y2gge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBWZXJpZnkgZW1haWwgd2l0aCB0b2tlblxuICAgKi9cbiAgYXN5bmMgdmVyaWZ5RW1haWwodG9rZW46IHN0cmluZyk6IFByb21pc2U8eyBtZXNzYWdlOiBzdHJpbmcgfT4ge1xuICAgIHJldHVybiB0aGlzLm1ha2VSZXF1ZXN0PHsgbWVzc2FnZTogc3RyaW5nIH0+KCcvYXBpL2F1dGgvdmVyaWZ5LWVtYWlsLycsIHtcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyB0b2tlbiB9KSxcbiAgICB9KTtcbiAgfVxuXG4gIC8qKlxuICAgKiBSZXNlbmQgZW1haWwgdmVyaWZpY2F0aW9uXG4gICAqL1xuICBhc3luYyByZXNlbmRWZXJpZmljYXRpb25FbWFpbChlbWFpbDogc3RyaW5nKTogUHJvbWlzZTx7IG1lc3NhZ2U6IHN0cmluZyB9PiB7XG4gICAgcmV0dXJuIHRoaXMubWFrZVJlcXVlc3Q8eyBtZXNzYWdlOiBzdHJpbmcgfT4oXG4gICAgICAnL2FwaS9hdXRoL3Jlc2VuZC12ZXJpZmljYXRpb24vJyxcbiAgICAgIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgZW1haWwgfSksXG4gICAgICB9LFxuICAgICk7XG4gIH1cbn1cblxuLy8gRXhwb3J0IHNpbmdsZXRvbiBpbnN0YW5jZVxuZXhwb3J0IGNvbnN0IGF1dGhTZXJ2aWNlID0gbmV3IEF1dGhTZXJ2aWNlKCk7XG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBaUJBLElBQU1BLGFBQWEsR0FBRyxTQUFoQkEsYUFBYUEsQ0FBQSxFQUFTO0VBQzFCLElBQUksQ0FBQ0MsT0FBTyxFQUFFO0lBQ1osT0FBTyx3QkFBd0I7RUFDakM7RUFHQSxPQUFPLDBCQUEwQjtBQUNuQyxDQUFDO0FBRUQsSUFBTUMsWUFBWSxHQUFHRixhQUFhLENBQUMsQ0FBQztBQUFDLElBMEUvQkcsV0FBVztFQUFBLFNBQUFBLFlBQUE7SUFBQSxJQUFBQyxnQkFBQSxDQUFBQyxPQUFBLFFBQUFGLFdBQUE7SUFBQSxLQUNQRyxPQUFPLEdBQUdKLFlBQVk7RUFBQTtFQUFBLFdBQUFLLGFBQUEsQ0FBQUYsT0FBQSxFQUFBRixXQUFBO0lBQUFLLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUFDLFlBQUEsT0FBQUMsa0JBQUEsQ0FBQU4sT0FBQSxFQUs5QixXQUNFTyxRQUFnQixFQUNoQkMsT0FBb0IsRUFDUjtRQUNaLElBQU1DLEdBQUcsR0FBRyxHQUFHLElBQUksQ0FBQ1IsT0FBTyxHQUFHTSxRQUFRLEVBQUU7UUFDeENHLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHVCQUF1QixFQUFFRixHQUFHLENBQUM7UUFDekNDLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHFCQUFxQixFQUFFO1VBQ2pDQyxNQUFNLEVBQUVKLE9BQU8sQ0FBQ0ksTUFBTTtVQUN0QkMsT0FBTyxFQUFFLENBQUMsQ0FBQ0wsT0FBTyxDQUFDTTtRQUNyQixDQUFDLENBQUM7UUFFRixJQUFJO1VBQ0YsSUFBTUMsUUFBUSxTQUFTQyxLQUFLLENBQUNQLEdBQUcsRUFBQVEsTUFBQSxDQUFBQyxNQUFBLEtBQzNCVixPQUFPO1lBQ1ZXLE9BQU8sRUFBQUYsTUFBQSxDQUFBQyxNQUFBO2NBQ0wsY0FBYyxFQUFFO1lBQWtCLEdBQy9CVixPQUFPLENBQUNXLE9BQU87VUFDbkIsRUFDRixDQUFDO1VBRUZULE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHFCQUFxQixFQUFFSSxRQUFRLENBQUNLLE1BQU0sRUFBRUwsUUFBUSxDQUFDTSxVQUFVLENBQUM7VUFDeEUsSUFBTUMsSUFBSSxTQUFTUCxRQUFRLENBQUNRLElBQUksQ0FBQyxDQUFDO1VBQ2xDYixPQUFPLENBQUNDLEdBQUcsQ0FBQyx3QkFBd0IsRUFBRU0sTUFBTSxDQUFDTyxJQUFJLENBQUNGLElBQUksQ0FBQyxDQUFDO1VBRXhELElBQUksQ0FBQ1AsUUFBUSxDQUFDVSxFQUFFLEVBQUU7WUFFaEIsSUFBTUMsS0FBSyxHQUFHSixJQUFnQjtZQUM5QixJQUFJSyxZQUFZLEdBQUcsbUJBQW1CO1lBRXRDLElBQUlELEtBQUssQ0FBQ0UsTUFBTSxFQUFFO2NBQ2hCRCxZQUFZLEdBQUdELEtBQUssQ0FBQ0UsTUFBTTtZQUM3QixDQUFDLE1BQU0sSUFBSUYsS0FBSyxDQUFDRyxPQUFPLEVBQUU7Y0FDeEJGLFlBQVksR0FBR0QsS0FBSyxDQUFDRyxPQUFPO1lBQzlCLENBQUMsTUFBTSxJQUNMSCxLQUFLLENBQUNJLGdCQUFnQixJQUN0QkosS0FBSyxDQUFDSSxnQkFBZ0IsQ0FBQ0MsTUFBTSxHQUFHLENBQUMsRUFDakM7Y0FDQUosWUFBWSxHQUFHRCxLQUFLLENBQUNJLGdCQUFnQixDQUFDLENBQUMsQ0FBQztZQUMxQyxDQUFDLE1BQU0sSUFBSUosS0FBSyxDQUFDTSxNQUFNLEVBQUU7Y0FFdkIsSUFBTUMsVUFBVSxHQUFHaEIsTUFBTSxDQUFDaUIsTUFBTSxDQUFDUixLQUFLLENBQUNNLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQztjQUNqRCxJQUFJQyxVQUFVLElBQUlBLFVBQVUsQ0FBQ0YsTUFBTSxHQUFHLENBQUMsRUFBRTtnQkFDdkNKLFlBQVksR0FBR00sVUFBVSxDQUFDLENBQUMsQ0FBQztjQUM5QjtZQUNGO1lBRUEsTUFBTSxJQUFJRSxLQUFLLENBQUNSLFlBQVksQ0FBQztVQUMvQjtVQUVBLE9BQU9MLElBQUk7UUFDYixDQUFDLENBQUMsT0FBT0ksS0FBSyxFQUFFO1VBQ2QsSUFBSUEsS0FBSyxZQUFZUyxLQUFLLEVBQUU7WUFDMUIsTUFBTVQsS0FBSztVQUNiO1VBQ0EsTUFBTSxJQUFJUyxLQUFLLENBQUMsd0JBQXdCLENBQUM7UUFDM0M7TUFDRixDQUFDO01BQUEsU0F4RGFDLFdBQVdBLENBQUFDLEVBQUEsRUFBQUMsR0FBQTtRQUFBLE9BQUFqQyxZQUFBLENBQUFrQyxLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQVhKLFdBQVc7SUFBQTtFQUFBO0lBQUFqQyxHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBcUMsTUFBQSxPQUFBbkMsa0JBQUEsQ0FBQU4sT0FBQSxFQTZEekIsV0FBWTBDLFdBQXlCLEVBQXlCO1FBQzVEaEMsT0FBTyxDQUFDQyxHQUFHLENBQUMsbUNBQW1DLEVBQUU7VUFDL0NnQyxLQUFLLEVBQUVELFdBQVcsQ0FBQ0M7UUFDckIsQ0FBQyxDQUFDO1FBQ0ZqQyxPQUFPLENBQUNDLEdBQUcsQ0FBQyxrQkFBa0IsRUFBRSxJQUFJLENBQUNWLE9BQU8sQ0FBQztRQUU3QyxJQUFJO1VBQ0YsSUFBTTJDLE1BQU0sU0FBUyxJQUFJLENBQUNSLFdBQVcsQ0FBZSxrQkFBa0IsRUFBRTtZQUN0RXhCLE1BQU0sRUFBRSxNQUFNO1lBQ2RFLElBQUksRUFBRStCLElBQUksQ0FBQ0MsU0FBUyxDQUFDSixXQUFXO1VBQ2xDLENBQUMsQ0FBQztVQUVGaEMsT0FBTyxDQUFDQyxHQUFHLENBQUMsZ0NBQWdDLENBQUM7VUFDN0MsT0FBT2lDLE1BQU07UUFDZixDQUFDLENBQUMsT0FBT2xCLEtBQUssRUFBRTtVQUNkaEIsT0FBTyxDQUFDZ0IsS0FBSyxDQUFDLDZCQUE2QixFQUFFQSxLQUFLLENBQUM7VUFDbkQsTUFBTUEsS0FBSztRQUNiO01BQ0YsQ0FBQztNQUFBLFNBbEJLcUIsS0FBS0EsQ0FBQUMsR0FBQTtRQUFBLE9BQUFQLE1BQUEsQ0FBQUYsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFMTyxLQUFLO0lBQUE7RUFBQTtJQUFBNUMsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQTZDLFNBQUEsT0FBQTNDLGtCQUFBLENBQUFOLE9BQUEsRUF1QlgsV0FBZWtELFFBQXlCLEVBQXlCO1FBQy9ELE9BQU8sSUFBSSxDQUFDZCxXQUFXLENBQWUscUJBQXFCLEVBQUU7VUFDM0R4QixNQUFNLEVBQUUsTUFBTTtVQUNkRSxJQUFJLEVBQUUrQixJQUFJLENBQUNDLFNBQVMsQ0FBQ0ksUUFBUTtRQUMvQixDQUFDLENBQUM7TUFDSixDQUFDO01BQUEsU0FMS0MsUUFBUUEsQ0FBQUMsR0FBQTtRQUFBLE9BQUFILFNBQUEsQ0FBQVYsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFSVyxRQUFRO0lBQUE7RUFBQTtJQUFBaEQsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQWlELGVBQUEsT0FBQS9DLGtCQUFBLENBQUFOLE9BQUEsRUFVZCxhQUFzQztRQUNwQyxJQUFJO1VBQ0ZVLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHFDQUFxQyxDQUFDO1VBR2xELElBQU0yQyxZQUFZLFNBQVMsTUFBTSxDQUMvQiwyQ0FDRixDQUFDO1VBQ0QsTUFBTUEsWUFBWSxDQUFDdEQsT0FBTyxDQUFDdUQsV0FBVyxDQUFDLENBQ3JDLFlBQVksRUFDWixlQUFlLEVBQ2YsV0FBVyxFQUNYLFlBQVksQ0FDYixDQUFDO1VBRUY3QyxPQUFPLENBQUNDLEdBQUcsQ0FBQyxnQ0FBZ0MsQ0FBQztRQUMvQyxDQUFDLENBQUMsT0FBT2UsS0FBSyxFQUFFO1VBQ2RoQixPQUFPLENBQUNnQixLQUFLLENBQUMsK0JBQStCLEVBQUVBLEtBQUssQ0FBQztRQUN2RDtNQUNGLENBQUM7TUFBQSxTQW5CSzhCLGNBQWNBLENBQUE7UUFBQSxPQUFBSCxlQUFBLENBQUFkLEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBZGdCLGNBQWM7SUFBQTtFQUFBO0lBQUFyRCxHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBcUQsa0JBQUEsT0FBQW5ELGtCQUFBLENBQUFOLE9BQUEsRUF3QnBCLFdBQ0UwRCxPQUFpQyxFQUNWO1FBR3ZCLE9BQU8sSUFBSUMsT0FBTyxDQUFDLFVBQUNDLE9BQU8sRUFBRUMsTUFBTSxFQUFLO1VBQ3RDQyxVQUFVLENBQUMsWUFBTTtZQUVmLElBQUlKLE9BQU8sQ0FBQzlDLE1BQU0sS0FBSyxPQUFPLElBQUk4QyxPQUFPLENBQUNmLEtBQUssRUFBRTtjQUMvQ2lCLE9BQU8sQ0FBQztnQkFDTkcsTUFBTSxFQUFFLGdDQUFnQztnQkFDeENDLE9BQU8sRUFBRSxpQ0FBaUM7Z0JBQzFDQyxJQUFJLEVBQUU7a0JBQ0pDLEVBQUUsRUFBRSxHQUFHO2tCQUNQdkIsS0FBSyxFQUFFZSxPQUFPLENBQUNmLEtBQUs7a0JBQ3BCd0IsVUFBVSxFQUFFLGNBQWM7a0JBQzFCQyxTQUFTLEVBQUUsTUFBTTtrQkFDakJDLElBQUksRUFBRSxVQUFVO2tCQUNoQkMsV0FBVyxFQUFFO2dCQUNmO2NBQ0YsQ0FBQyxDQUFDO1lBQ0osQ0FBQyxNQUFNLElBQUlaLE9BQU8sQ0FBQzlDLE1BQU0sS0FBSyxPQUFPLElBQUk4QyxPQUFPLENBQUNhLEtBQUssRUFBRTtjQUN0RFgsT0FBTyxDQUFDO2dCQUNORyxNQUFNLEVBQUUsZ0NBQWdDO2dCQUN4Q0MsT0FBTyxFQUFFLGlDQUFpQztnQkFDMUNDLElBQUksRUFBRTtrQkFDSkMsRUFBRSxFQUFFLEdBQUc7a0JBQ1B2QixLQUFLLEVBQUUsbUJBQW1CO2tCQUMxQndCLFVBQVUsRUFBRSxPQUFPO2tCQUNuQkMsU0FBUyxFQUFFLE1BQU07a0JBQ2pCQyxJQUFJLEVBQUUsVUFBVTtrQkFDaEJDLFdBQVcsRUFBRSxJQUFJO2tCQUNqQkMsS0FBSyxFQUFFYixPQUFPLENBQUNhO2dCQUNqQjtjQUNGLENBQUMsQ0FBQztZQUNKLENBQUMsTUFBTSxJQUFJYixPQUFPLENBQUM5QyxNQUFNLEtBQUssV0FBVyxFQUFFO2NBQ3pDZ0QsT0FBTyxDQUFDO2dCQUNORyxNQUFNLEVBQUUsZ0NBQWdDO2dCQUN4Q0MsT0FBTyxFQUFFLGlDQUFpQztnQkFDMUNDLElBQUksRUFBRTtrQkFDSkMsRUFBRSxFQUFFLEdBQUc7a0JBQ1B2QixLQUFLLEVBQUUsdUJBQXVCO2tCQUM5QndCLFVBQVUsRUFBRSxXQUFXO2tCQUN2QkMsU0FBUyxFQUFFLE1BQU07a0JBQ2pCQyxJQUFJLEVBQUUsVUFBVTtrQkFDaEJDLFdBQVcsRUFBRTtnQkFDZjtjQUNGLENBQUMsQ0FBQztZQUNKLENBQUMsTUFBTTtjQUNMVCxNQUFNLENBQUMsSUFBSTFCLEtBQUssQ0FBQyw2Q0FBNkMsQ0FBQyxDQUFDO1lBQ2xFO1VBQ0YsQ0FBQyxFQUFFLElBQUksQ0FBQztRQUNWLENBQUMsQ0FBQztNQUNKLENBQUM7TUFBQSxTQXJES3FDLGlCQUFpQkEsQ0FBQUMsR0FBQTtRQUFBLE9BQUFoQixrQkFBQSxDQUFBbEIsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFqQmdDLGlCQUFpQjtJQUFBO0VBQUE7SUFBQXJFLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUFzRSxjQUFBLE9BQUFwRSxrQkFBQSxDQUFBTixPQUFBLEVBMER2QixXQUFtQjJFLGFBQW9CLEVBQStCO1FBQ3BFLE9BQU8sSUFBSSxDQUFDdkMsV0FBVyxDQUFxQiwwQkFBMEIsRUFBRTtVQUN0RXhCLE1BQU0sRUFBRSxNQUFNO1VBQ2RFLElBQUksRUFBRStCLElBQUksQ0FBQ0MsU0FBUyxDQUFDO1lBQUVrQixPQUFPLEVBQUVXO1VBQWEsQ0FBQztRQUNoRCxDQUFDLENBQUM7TUFDSixDQUFDO01BQUEsU0FMS0EsWUFBWUEsQ0FBQUMsR0FBQTtRQUFBLE9BQUFGLGNBQUEsQ0FBQW5DLEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBWm1DLFlBQVk7SUFBQTtFQUFBO0lBQUF4RSxHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBeUUsc0JBQUEsT0FBQXZFLGtCQUFBLENBQUFOLE9BQUEsRUFVbEIsV0FBNEIyQyxLQUFhLEVBQWdCO1FBR3ZELE9BQU87VUFDTG1DLE9BQU8sRUFBRSxJQUFJO1VBQ2JiLElBQUksRUFBRTtZQUNKQyxFQUFFLEVBQUUsUUFBUSxHQUFHYSxJQUFJLENBQUNDLEdBQUcsQ0FBQyxDQUFDO1lBQ3pCckMsS0FBSyxFQUFMQSxLQUFLO1lBQ0x3QixVQUFVLEVBQUUsT0FBTztZQUNuQkMsU0FBUyxFQUFFLE1BQU07WUFDakJDLElBQUksRUFBRSxVQUFVO1lBQ2hCQyxXQUFXLEVBQUU7VUFDZixDQUFDO1VBQ0RXLEtBQUssRUFBRSxtQkFBbUIsR0FBR0YsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQztVQUN2Q0wsWUFBWSxFQUFFLHFCQUFxQixHQUFHSSxJQUFJLENBQUNDLEdBQUcsQ0FBQztRQUNqRCxDQUFDO01BQ0gsQ0FBQztNQUFBLFNBaEJLRSxxQkFBcUJBLENBQUFDLEdBQUE7UUFBQSxPQUFBTixzQkFBQSxDQUFBdEMsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFyQjBDLHFCQUFxQjtJQUFBO0VBQUE7SUFBQS9FLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUFnRixzQkFBQSxPQUFBOUUsa0JBQUEsQ0FBQU4sT0FBQSxFQXFCM0IsV0FBNEJ1RSxLQUFhLEVBQWdCO1FBR3ZELE9BQU87VUFDTE8sT0FBTyxFQUFFLElBQUk7VUFDYmIsSUFBSSxFQUFFO1lBQ0pDLEVBQUUsRUFBRSxRQUFRLEdBQUdhLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUM7WUFDekJULEtBQUssRUFBTEEsS0FBSztZQUNMSixVQUFVLEVBQUUsT0FBTztZQUNuQkMsU0FBUyxFQUFFLE1BQU07WUFDakJDLElBQUksRUFBRSxVQUFVO1lBQ2hCQyxXQUFXLEVBQUU7VUFDZixDQUFDO1VBQ0RXLEtBQUssRUFBRSxtQkFBbUIsR0FBR0YsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQztVQUN2Q0wsWUFBWSxFQUFFLHFCQUFxQixHQUFHSSxJQUFJLENBQUNDLEdBQUcsQ0FBQztRQUNqRCxDQUFDO01BQ0gsQ0FBQztNQUFBLFNBaEJLSyxxQkFBcUJBLENBQUFDLEdBQUE7UUFBQSxPQUFBRixzQkFBQSxDQUFBN0MsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFyQjZDLHFCQUFxQjtJQUFBO0VBQUE7SUFBQWxGLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUFtRiwwQkFBQSxPQUFBakYsa0JBQUEsQ0FBQU4sT0FBQSxFQXFCM0IsV0FBZ0NrRCxRQUFhLEVBQWdCO1FBRzNELE9BQU87VUFDTDRCLE9BQU8sRUFBRSxJQUFJO1VBQ2JiLElBQUksRUFBRWYsUUFBUTtVQUNkK0IsS0FBSyxFQUFFLHVCQUF1QixHQUFHRixJQUFJLENBQUNDLEdBQUcsQ0FBQyxDQUFDO1VBQzNDTCxZQUFZLEVBQUUseUJBQXlCLEdBQUdJLElBQUksQ0FBQ0MsR0FBRyxDQUFDO1FBQ3JELENBQUM7TUFDSCxDQUFDO01BQUEsU0FUS1EseUJBQXlCQSxDQUFBQyxHQUFBO1FBQUEsT0FBQUYsMEJBQUEsQ0FBQWhELEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBekJnRCx5QkFBeUI7SUFBQTtFQUFBO0lBQUFyRixHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBc0YsT0FBQSxPQUFBcEYsa0JBQUEsQ0FBQU4sT0FBQSxFQWMvQixXQUFhMkUsWUFBb0IsRUFBaUI7UUFDaEQsSUFBSTtVQUNGLE1BQU0sSUFBSSxDQUFDdkMsV0FBVyxDQUFPLG1CQUFtQixFQUFFO1lBQ2hEeEIsTUFBTSxFQUFFLE1BQU07WUFDZEUsSUFBSSxFQUFFK0IsSUFBSSxDQUFDQyxTQUFTLENBQUM7Y0FBRWtCLE9BQU8sRUFBRVc7WUFBYSxDQUFDO1VBQ2hELENBQUMsQ0FBQztRQUNKLENBQUMsQ0FBQyxPQUFPakQsS0FBSyxFQUFFO1VBRWRoQixPQUFPLENBQUNpRixJQUFJLENBQUMseUJBQXlCLEVBQUVqRSxLQUFLLENBQUM7UUFDaEQ7TUFDRixDQUFDO01BQUEsU0FWS2tFLE1BQU1BLENBQUFDLEdBQUE7UUFBQSxPQUFBSCxPQUFBLENBQUFuRCxLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQU5vRCxNQUFNO0lBQUE7RUFBQTtJQUFBekYsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQTBGLFdBQUEsT0FBQXhGLGtCQUFBLENBQUFOLE9BQUEsRUFlWixXQUFpQmlGLEtBQWEsRUFBaUM7UUFDN0QsT0FBTyxJQUFJLENBQUM3QyxXQUFXLENBQXVCLG9CQUFvQixFQUFFO1VBQ2xFeEIsTUFBTSxFQUFFLEtBQUs7VUFDYk8sT0FBTyxFQUFFO1lBQ1A0RSxhQUFhLEVBQUUsVUFBVWQsS0FBSztVQUNoQztRQUNGLENBQUMsQ0FBQztNQUNKLENBQUM7TUFBQSxTQVBLZSxVQUFVQSxDQUFBQyxHQUFBO1FBQUEsT0FBQUgsV0FBQSxDQUFBdkQsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFWd0QsVUFBVTtJQUFBO0VBQUE7SUFBQTdGLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUE4RixjQUFBLE9BQUE1RixrQkFBQSxDQUFBTixPQUFBLEVBWWhCLFdBQ0VtRyxXQUFpQyxFQUNqQ2xCLEtBQWEsRUFDa0I7UUFDL0IsT0FBTyxJQUFJLENBQUM3QyxXQUFXLENBQXVCLG9CQUFvQixFQUFFO1VBQ2xFeEIsTUFBTSxFQUFFLE9BQU87VUFDZkUsSUFBSSxFQUFFK0IsSUFBSSxDQUFDQyxTQUFTLENBQUNxRCxXQUFXLENBQUM7VUFDakNoRixPQUFPLEVBQUU7WUFDUDRFLGFBQWEsRUFBRSxVQUFVZCxLQUFLO1VBQ2hDO1FBQ0YsQ0FBQyxDQUFDO01BQ0osQ0FBQztNQUFBLFNBWEttQixhQUFhQSxDQUFBQyxJQUFBLEVBQUFDLElBQUE7UUFBQSxPQUFBSixjQUFBLENBQUEzRCxLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQWI0RCxhQUFhO0lBQUE7RUFBQTtJQUFBakcsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQW1HLHFCQUFBLE9BQUFqRyxrQkFBQSxDQUFBTixPQUFBLEVBZ0JuQixXQUNFc0IsSUFBMEIsRUFDSTtRQUM5QixPQUFPLElBQUksQ0FBQ2MsV0FBVyxDQUFzQiwyQkFBMkIsRUFBRTtVQUN4RXhCLE1BQU0sRUFBRSxNQUFNO1VBQ2RFLElBQUksRUFBRStCLElBQUksQ0FBQ0MsU0FBUyxDQUFDeEIsSUFBSTtRQUMzQixDQUFDLENBQUM7TUFDSixDQUFDO01BQUEsU0FQS2tGLG9CQUFvQkEsQ0FBQUMsSUFBQTtRQUFBLE9BQUFGLHFCQUFBLENBQUFoRSxLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQXBCZ0Usb0JBQW9CO0lBQUE7RUFBQTtJQUFBckcsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQXNHLHFCQUFBLE9BQUFwRyxrQkFBQSxDQUFBTixPQUFBLEVBWTFCLFdBQ0VzQixJQUFpQyxFQUNIO1FBQzlCLE9BQU8sSUFBSSxDQUFDYyxXQUFXLENBQ3JCLG1DQUFtQyxFQUNuQztVQUNFeEIsTUFBTSxFQUFFLE1BQU07VUFDZEUsSUFBSSxFQUFFK0IsSUFBSSxDQUFDQyxTQUFTLENBQUN4QixJQUFJO1FBQzNCLENBQ0YsQ0FBQztNQUNILENBQUM7TUFBQSxTQVZLcUYsb0JBQW9CQSxDQUFBQyxJQUFBO1FBQUEsT0FBQUYscUJBQUEsQ0FBQW5FLEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBcEJtRSxvQkFBb0I7SUFBQTtFQUFBO0lBQUF4RyxHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBeUcsZUFBQSxPQUFBdkcsa0JBQUEsQ0FBQU4sT0FBQSxFQWUxQixXQUNFc0IsSUFBMkIsRUFDM0IyRCxLQUFhLEVBQ2lCO1FBQzlCLE9BQU8sSUFBSSxDQUFDN0MsV0FBVyxDQUFzQiw0QkFBNEIsRUFBRTtVQUN6RXhCLE1BQU0sRUFBRSxNQUFNO1VBQ2RFLElBQUksRUFBRStCLElBQUksQ0FBQ0MsU0FBUyxDQUFDeEIsSUFBSSxDQUFDO1VBQzFCSCxPQUFPLEVBQUU7WUFDUDRFLGFBQWEsRUFBRSxVQUFVZCxLQUFLO1VBQ2hDO1FBQ0YsQ0FBQyxDQUFDO01BQ0osQ0FBQztNQUFBLFNBWEs2QixjQUFjQSxDQUFBQyxJQUFBLEVBQUFDLElBQUE7UUFBQSxPQUFBSCxlQUFBLENBQUF0RSxLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQWRzRSxjQUFjO0lBQUE7RUFBQTtJQUFBM0csR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQTZHLGNBQUEsT0FBQTNHLGtCQUFBLENBQUFOLE9BQUEsRUFnQnBCLFdBQW9CaUYsS0FBYSxFQUFvQjtRQUNuRCxJQUFJO1VBQ0YsTUFBTSxJQUFJLENBQUM3QyxXQUFXLENBQUMsMkJBQTJCLEVBQUU7WUFDbER4QixNQUFNLEVBQUUsTUFBTTtZQUNkTyxPQUFPLEVBQUU7Y0FDUDRFLGFBQWEsRUFBRSxVQUFVZCxLQUFLO1lBQ2hDO1VBQ0YsQ0FBQyxDQUFDO1VBQ0YsT0FBTyxJQUFJO1FBQ2IsQ0FBQyxDQUFDLE9BQUFpQyxPQUFBLEVBQU07VUFDTixPQUFPLEtBQUs7UUFDZDtNQUNGLENBQUM7TUFBQSxTQVpLQyxhQUFhQSxDQUFBQyxJQUFBO1FBQUEsT0FBQUgsY0FBQSxDQUFBMUUsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFiMkUsYUFBYTtJQUFBO0VBQUE7SUFBQWhILEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUFpSCxZQUFBLE9BQUEvRyxrQkFBQSxDQUFBTixPQUFBLEVBaUJuQixXQUFrQmlGLEtBQWEsRUFBZ0M7UUFDN0QsT0FBTyxJQUFJLENBQUM3QyxXQUFXLENBQXNCLHlCQUF5QixFQUFFO1VBQ3RFeEIsTUFBTSxFQUFFLE1BQU07VUFDZEUsSUFBSSxFQUFFK0IsSUFBSSxDQUFDQyxTQUFTLENBQUM7WUFBRW1DLEtBQUssRUFBTEE7VUFBTSxDQUFDO1FBQ2hDLENBQUMsQ0FBQztNQUNKLENBQUM7TUFBQSxTQUxLcUMsV0FBV0EsQ0FBQUMsSUFBQTtRQUFBLE9BQUFGLFlBQUEsQ0FBQTlFLEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBWDhFLFdBQVc7SUFBQTtFQUFBO0lBQUFuSCxHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBb0gsd0JBQUEsT0FBQWxILGtCQUFBLENBQUFOLE9BQUEsRUFVakIsV0FBOEIyQyxLQUFhLEVBQWdDO1FBQ3pFLE9BQU8sSUFBSSxDQUFDUCxXQUFXLENBQ3JCLGdDQUFnQyxFQUNoQztVQUNFeEIsTUFBTSxFQUFFLE1BQU07VUFDZEUsSUFBSSxFQUFFK0IsSUFBSSxDQUFDQyxTQUFTLENBQUM7WUFBRUgsS0FBSyxFQUFMQTtVQUFNLENBQUM7UUFDaEMsQ0FDRixDQUFDO01BQ0gsQ0FBQztNQUFBLFNBUks4RSx1QkFBdUJBLENBQUFDLElBQUE7UUFBQSxPQUFBRix3QkFBQSxDQUFBakYsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUF2QmlGLHVCQUF1QjtJQUFBO0VBQUE7QUFBQTtBQVl4QixJQUFNRSxXQUFXLEdBQUFDLE9BQUEsQ0FBQUQsV0FBQSxHQUFHLElBQUk3SCxXQUFXLENBQUMsQ0FBQyIsImlnbm9yZUxpc3QiOltdfQ==