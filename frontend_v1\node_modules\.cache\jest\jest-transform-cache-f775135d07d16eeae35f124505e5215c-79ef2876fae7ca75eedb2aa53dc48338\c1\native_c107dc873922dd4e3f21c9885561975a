8aa9e5e83c57e907c054a59a69dc2dd4
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useRoute = exports.useNavigationContainerRef = exports.useNavigation = exports.useIsFocused = exports.useFocusEffect = exports.default = exports.createNavigationContainerRef = exports.TabActions = exports.StackActions = exports.NavigationContainer = exports.DrawerActions = exports.CommonActions = void 0;
var React = require('react');
var useNavigation = exports.useNavigation = function useNavigation() {
  return {
    navigate: jest.fn(),
    goBack: jest.fn(),
    reset: jest.fn(),
    setParams: jest.fn(),
    dispatch: jest.fn(),
    isFocused: jest.fn(function () {
      return true;
    }),
    canGoBack: jest.fn(function () {
      return true;
    }),
    getId: jest.fn(function () {
      return 'mock-route-id';
    }),
    getParent: jest.fn(),
    getState: jest.fn(function () {
      return {
        index: 0,
        routes: [{
          name: 'Home',
          key: 'home-key'
        }]
      };
    }),
    addListener: jest.fn(),
    removeListener: jest.fn(),
    setOptions: jest.fn()
  };
};
var useRoute = exports.useRoute = function useRoute() {
  return {
    key: 'mock-route-key',
    name: 'MockScreen',
    params: {}
  };
};
var useFocusEffect = exports.useFocusEffect = jest.fn();
var useIsFocused = exports.useIsFocused = jest.fn(function () {
  return true;
});
var NavigationContainer = exports.NavigationContainer = function NavigationContainer(_ref) {
  var children = _ref.children;
  return children;
};
var createNavigationContainerRef = exports.createNavigationContainerRef = jest.fn(function () {
  return {
    current: {
      navigate: jest.fn(),
      reset: jest.fn(),
      goBack: jest.fn(),
      dispatch: jest.fn(),
      isFocused: jest.fn(function () {
        return true;
      }),
      canGoBack: jest.fn(function () {
        return true;
      }),
      getRootState: jest.fn(function () {
        return {
          index: 0,
          routes: [{
            name: 'Home',
            key: 'home-key'
          }]
        };
      })
    }
  };
});
var useNavigationContainerRef = exports.useNavigationContainerRef = jest.fn(function () {
  return {
    current: {
      navigate: jest.fn(),
      reset: jest.fn(),
      goBack: jest.fn()
    }
  };
});
var CommonActions = exports.CommonActions = {
  navigate: jest.fn(),
  reset: jest.fn(),
  goBack: jest.fn(),
  setParams: jest.fn()
};
var StackActions = exports.StackActions = {
  push: jest.fn(),
  pop: jest.fn(),
  popToTop: jest.fn(),
  replace: jest.fn()
};
var TabActions = exports.TabActions = {
  jumpTo: jest.fn()
};
var DrawerActions = exports.DrawerActions = {
  openDrawer: jest.fn(),
  closeDrawer: jest.fn(),
  toggleDrawer: jest.fn(),
  jumpTo: jest.fn()
};
var _default = exports.default = {
  useNavigation: useNavigation,
  useRoute: useRoute,
  useFocusEffect: useFocusEffect,
  useIsFocused: useIsFocused,
  NavigationContainer: NavigationContainer,
  createNavigationContainerRef: createNavigationContainerRef,
  useNavigationContainerRef: useNavigationContainerRef,
  CommonActions: CommonActions,
  StackActions: StackActions,
  TabActions: TabActions,
  DrawerActions: DrawerActions
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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