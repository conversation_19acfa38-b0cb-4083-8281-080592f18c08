{"version": 3, "names": ["NativeComponentRegistry", "_interopRequireWildcard", "require", "_codegenNativeCommands", "_interopRequireDefault", "_RCTTextInputViewConfig", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "Commands", "exports", "codegenNativeCommands", "supportedCommands", "__INTERNAL_VIEW_CONFIG", "assign", "uiViewClassName", "RCTTextInputViewConfig", "validAttributes", "dataDetectorTypes", "MultilineTextInputNativeComponent", "_default"], "sources": ["RCTMultilineTextInputNativeComponent.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {HostComponent} from '../../../src/private/types/HostComponent';\nimport type {PartialViewConfig} from '../../Renderer/shims/ReactNativeTypes';\nimport type {TextInputNativeCommands} from './TextInputNativeCommands';\n\nimport * as NativeComponentRegistry from '../../NativeComponent/NativeComponentRegistry';\nimport codegenNativeCommands from '../../Utilities/codegenNativeCommands';\nimport RCTTextInputViewConfig from './RCTTextInputViewConfig';\n\ntype NativeType = HostComponent<{...}>;\n\ntype NativeCommands = TextInputNativeCommands<NativeType>;\n\nexport const Commands: NativeCommands = codegenNativeCommands<NativeCommands>({\n  supportedCommands: ['focus', 'blur', 'setTextAndSelection'],\n});\n\nexport const __INTERNAL_VIEW_CONFIG: PartialViewConfig = {\n  uiViewClassName: 'RCTMultilineTextInputView',\n  ...RCTTextInputViewConfig,\n  validAttributes: {\n    ...RCTTextInputViewConfig.validAttributes,\n    dataDetectorTypes: true,\n  },\n};\n\nconst MultilineTextInputNativeComponent: HostComponent<{...}> =\n  NativeComponentRegistry.get<{...}>(\n    'RCTMultilineTextInputView',\n    () => __INTERNAL_VIEW_CONFIG,\n  );\n\n// flowlint-next-line unclear-type:off\nexport default ((MultilineTextInputNativeComponent: any): HostComponent<{...}>);\n"], "mappings": ";;;;;AAcA,IAAAA,uBAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,sBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,uBAAA,GAAAD,sBAAA,CAAAF,OAAA;AAA8D,SAAAD,wBAAAK,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAP,uBAAA,YAAAA,wBAAAK,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAMvD,IAAMmB,QAAwB,GAAAC,OAAA,CAAAD,QAAA,GAAG,IAAAE,8BAAqB,EAAiB;EAC5EC,iBAAiB,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,qBAAqB;AAC5D,CAAC,CAAC;AAEK,IAAMC,sBAAyC,GAAAH,OAAA,CAAAG,sBAAA,GAAAP,MAAA,CAAAQ,MAAA;EACpDC,eAAe,EAAE;AAA2B,GACzCC,+BAAsB;EACzBC,eAAe,EAAAX,MAAA,CAAAQ,MAAA,KACVE,+BAAsB,CAACC,eAAe;IACzCC,iBAAiB,EAAE;EAAI;AACxB,EACF;AAED,IAAMC,iCAAuD,GAC3DpC,uBAAuB,CAACkB,GAAG,CACzB,2BAA2B,EAC3B;EAAA,OAAMY,sBAAsB;AAAA,CAC9B,CAAC;AAAC,IAAAO,QAAA,GAAAV,OAAA,CAAAX,OAAA,GAGaoB,iCAAiC", "ignoreList": []}