/**
 * Provider Authentication Hook
 *
 * Manages provider authentication state and provides authentication
 * utilities for the provider portal.
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { useState, useEffect } from 'react';
import { Alert } from 'react-native';
import { unifiedErrorHandlingService } from '../services/unifiedErrorHandling';

interface Provider {
  id: string;
  businessName: string;
  email: string;
  isVerified: boolean;
  profilePhoto?: string;
  contactInfo: {
    phone: string;
    email: string;
  };
  location: {
    city: string;
    province: string;
  };
}

interface UseProviderAuthReturn {
  provider: Provider | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  refreshProvider: () => Promise<void>;
  clearError: () => void;
}

const PROVIDER_STORAGE_KEY = '@vierla_provider_auth';

export const useProviderAuth = (): UseProviderAuthReturn => {
  const [provider, setProvider] = useState<Provider | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize authentication state
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      setIsLoading(true);

      // Check for stored authentication
      const storedAuth = await AsyncStorage.getItem(PROVIDER_STORAGE_KEY);

      if (storedAuth) {
        const authData = JSON.parse(storedAuth);

        // Validate stored token (in real app, verify with backend)
        if (authData.token && authData.provider) {
          setProvider(authData.provider);
          setIsAuthenticated(true);
        } else {
          // Clear invalid stored data
          await AsyncStorage.removeItem(PROVIDER_STORAGE_KEY);
        }
      }
    } catch (error) {
      // Use unified error handling
      await unifiedErrorHandlingService.handleError(error as Error, {
        component: 'useProviderAuth',
        action: 'initialize_auth',
        severity: 'medium'
      });

      // Clear potentially corrupted data
      await AsyncStorage.removeItem(PROVIDER_STORAGE_KEY);
      setError('Failed to initialize authentication');
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);

      // Real authentication with backend API
      const response = await fetch('http://192.168.2.65:8000/api/auth/provider/login/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      if (!response.ok) {
        throw new Error('Invalid credentials');
      }

      const authResponse = await response.json();

      // Transform backend response to frontend format
      const provider: Provider = {
        id: authResponse.user.id,
        businessName: authResponse.provider_profile.business_name,
        email: authResponse.user.email,
        isVerified: authResponse.provider_profile.is_verified,
        profilePhoto: authResponse.provider_profile.profile_image || 'https://via.placeholder.com/150',
        contactInfo: {
          phone: authResponse.provider_profile.business_phone,
          email: authResponse.user.email,
        },
        location: {
          city: authResponse.provider_profile.city,
          province: authResponse.provider_profile.state,
        },
      };

      const authData = {
        token: authResponse.access_token,
        provider: provider,
        expiresAt: Date.now() + 24 * 60 * 60 * 1000, // 24 hours
      };

        // Store authentication data
        await AsyncStorage.setItem(
          PROVIDER_STORAGE_KEY,
          JSON.stringify(authData),
        );

        setProvider(authData.provider);
        setIsAuthenticated(true);
        setError(null);

        return authData;
    } catch (error) {
      // Use unified error handling for authentication errors
      await unifiedErrorHandlingService.handleAuthError(error as Error, {
        component: 'useProviderAuth',
        action: 'provider_login',
        additionalData: { email }
      });

      setError('Login failed. Please check your credentials and try again.');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      setIsLoading(true);

      // Clear stored authentication
      await AsyncStorage.removeItem(PROVIDER_STORAGE_KEY);

      // Reset state
      setProvider(null);
      setIsAuthenticated(false);

      // In a real app, you might also want to:
      // - Invalidate the token on the backend
      // - Clear other provider-related cached data
      // - Navigate to login screen
    } catch (error) {
      console.error('Error during provider logout:', error);
      Alert.alert(
        'Logout Error',
        'An error occurred during logout. Please try again.',
      );
    } finally {
      setIsLoading(false);
    }
  };

  const refreshProvider = async (): Promise<void> => {
    try {
      if (!isAuthenticated || !provider) {
        return;
      }

      // In a real app, fetch updated provider data from backend
      // For now, we'll just simulate a refresh
      console.log('Refreshing provider data...');

      // Mock updated data
      const updatedProvider: Provider = {
        ...provider,
        // Add any updated fields here
      };

      setProvider(updatedProvider);

      // Update stored data
      const storedAuth = await AsyncStorage.getItem(PROVIDER_STORAGE_KEY);
      if (storedAuth) {
        const authData = JSON.parse(storedAuth);
        authData.provider = updatedProvider;
        await AsyncStorage.setItem(
          PROVIDER_STORAGE_KEY,
          JSON.stringify(authData),
        );
      }
    } catch (error) {
      // Use unified error handling
      await unifiedErrorHandlingService.handleError(error as Error, {
        component: 'useProviderAuth',
        action: 'refresh_provider',
        severity: 'medium'
      });
      setError('Failed to refresh provider data');
    }
  };

  const clearError = () => {
    setError(null);
  };

  return {
    provider,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout,
    refreshProvider,
    clearError,
  };
};
