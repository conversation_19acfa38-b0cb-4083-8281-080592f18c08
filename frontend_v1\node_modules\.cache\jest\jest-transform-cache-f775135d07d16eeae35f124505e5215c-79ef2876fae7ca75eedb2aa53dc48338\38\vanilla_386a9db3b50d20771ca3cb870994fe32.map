{"version": 3, "names": ["createStoreImpl", "createState", "state", "listeners", "Set", "setState", "partial", "replace", "nextState", "Object", "is", "previousState", "assign", "for<PERSON>ach", "listener", "getState", "getInitialState", "initialState", "subscribe", "add", "delete", "api", "createStore", "exports"], "sources": ["vanilla.js"], "sourcesContent": ["'use strict';\n\nconst createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = { setState, getState, getInitialState, subscribe };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\n\nexports.createStore = createStore;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAMA,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,WAAW,EAAK;EACvC,IAAIC,KAAK;EACT,IAAMC,SAAS,GAAmB,IAAIC,GAAG,CAAC,CAAC;EAC3C,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,OAAO,EAAEC,OAAO,EAAK;IACrC,IAAMC,SAAS,GAAG,OAAOF,OAAO,KAAK,UAAU,GAAGA,OAAO,CAACJ,KAAK,CAAC,GAAGI,OAAO;IAC1E,IAAI,CAACG,MAAM,CAACC,EAAE,CAACF,SAAS,EAAEN,KAAK,CAAC,EAAE;MAChC,IAAMS,aAAa,GAAGT,KAAK;MAC3BA,KAAK,GAAG,CAACK,OAAO,IAAI,IAAI,GAAGA,OAAO,GAAG,OAAOC,SAAS,KAAK,QAAQ,IAAIA,SAAS,KAAK,IAAI,IAAIA,SAAS,GAAGC,MAAM,CAACG,MAAM,CAAC,CAAC,CAAC,EAAEV,KAAK,EAAEM,SAAS,CAAC;MAC3IL,SAAS,CAACU,OAAO,CAAC,UAACC,QAAQ;QAAA,OAAKA,QAAQ,CAACZ,KAAK,EAAES,aAAa,CAAC;MAAA,EAAC;IACjE;EACF,CAAC;EACD,IAAMI,QAAQ,GAAG,SAAXA,QAAQA,CAAA;IAAA,OAASb,KAAK;EAAA;EAC5B,IAAMc,eAAe,GAAG,SAAlBA,eAAeA,CAAA;IAAA,OAASC,YAAY;EAAA;EAC1C,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAIJ,QAAQ,EAAK;IAC9BX,SAAS,CAACgB,GAAG,CAACL,QAAQ,CAAC;IACvB,OAAO;MAAA,OAAMX,SAAS,CAACiB,MAAM,CAACN,QAAQ,CAAC;IAAA;EACzC,CAAC;EACD,IAAMO,GAAG,GAAG;IAAEhB,QAAQ,EAARA,QAAQ;IAAEU,QAAQ,EAARA,QAAQ;IAAEC,eAAe,EAAfA,eAAe;IAAEE,SAAS,EAATA;EAAU,CAAC;EAC9D,IAAMD,YAAY,GAAGf,KAAK,GAAGD,WAAW,CAACI,QAAQ,EAAEU,QAAQ,EAAEM,GAAG,CAAC;EACjE,OAAOA,GAAG;AACZ,CAAC;AACD,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIrB,WAAW;EAAA,OAAKA,WAAW,GAAGD,eAAe,CAACC,WAAW,CAAC,GAAGD,eAAe;AAAA;AAEjGuB,OAAO,CAACD,WAAW,GAAGA,WAAW", "ignoreList": []}