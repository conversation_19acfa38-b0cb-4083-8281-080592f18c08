{"version": 3, "names": ["React", "require", "useNavigation", "exports", "navigate", "jest", "fn", "goBack", "reset", "setParams", "dispatch", "isFocused", "canGoBack", "getId", "getParent", "getState", "index", "routes", "name", "key", "addListener", "removeListener", "setOptions", "useRoute", "params", "useFocusEffect", "useIsFocused", "NavigationContainer", "_ref", "children", "createNavigationContainerRef", "current", "getRootState", "useNavigationContainerRef", "CommonActions", "StackActions", "push", "pop", "popToTop", "replace", "TabActions", "jumpTo", "DrawerActions", "openDrawer", "closeDrawer", "toggle<PERSON>rawer", "_default", "default"], "sources": ["native.js"], "sourcesContent": ["/**\n * Mock for @react-navigation/native\n */\n\nconst React = require('react');\n\nexport const useNavigation = () => ({\n  navigate: jest.fn(),\n  goBack: jest.fn(),\n  reset: jest.fn(),\n  setParams: jest.fn(),\n  dispatch: jest.fn(),\n  isFocused: jest.fn(() => true),\n  canGoBack: jest.fn(() => true),\n  getId: jest.fn(() => 'mock-route-id'),\n  getParent: jest.fn(),\n  getState: jest.fn(() => ({\n    index: 0,\n    routes: [{ name: 'Home', key: 'home-key' }],\n  })),\n  addListener: jest.fn(),\n  removeListener: jest.fn(),\n  setOptions: jest.fn(),\n});\n\nexport const useRoute = () => ({\n  key: 'mock-route-key',\n  name: 'MockScreen',\n  params: {},\n});\n\nexport const useFocusEffect = jest.fn();\n\nexport const useIsFocused = jest.fn(() => true);\n\nexport const NavigationContainer = ({ children }) => children;\n\nexport const createNavigationContainerRef = jest.fn(() => ({\n  current: {\n    navigate: jest.fn(),\n    reset: jest.fn(),\n    goBack: jest.fn(),\n    dispatch: jest.fn(),\n    isFocused: jest.fn(() => true),\n    canGoBack: jest.fn(() => true),\n    getRootState: jest.fn(() => ({\n      index: 0,\n      routes: [{ name: 'Home', key: 'home-key' }],\n    })),\n  },\n}));\n\nexport const useNavigationContainerRef = jest.fn(() => ({\n  current: {\n    navigate: jest.fn(),\n    reset: jest.fn(),\n    goBack: jest.fn(),\n  },\n}));\n\nexport const CommonActions = {\n  navigate: jest.fn(),\n  reset: jest.fn(),\n  goBack: jest.fn(),\n  setParams: jest.fn(),\n};\n\nexport const StackActions = {\n  push: jest.fn(),\n  pop: jest.fn(),\n  popToTop: jest.fn(),\n  replace: jest.fn(),\n};\n\nexport const TabActions = {\n  jumpTo: jest.fn(),\n};\n\nexport const DrawerActions = {\n  openDrawer: jest.fn(),\n  closeDrawer: jest.fn(),\n  toggleDrawer: jest.fn(),\n  jumpTo: jest.fn(),\n};\n\nexport default {\n  useNavigation,\n  useRoute,\n  useFocusEffect,\n  useIsFocused,\n  NavigationContainer,\n  createNavigationContainerRef,\n  useNavigationContainerRef,\n  CommonActions,\n  StackActions,\n  TabActions,\n  DrawerActions,\n};\n"], "mappings": ";;;;AAIA,IAAMA,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAEvB,IAAMC,aAAa,GAAAC,OAAA,CAAAD,aAAA,GAAG,SAAhBA,aAAaA,CAAA;EAAA,OAAU;IAClCE,QAAQ,EAAEC,IAAI,CAACC,EAAE,CAAC,CAAC;IACnBC,MAAM,EAAEF,IAAI,CAACC,EAAE,CAAC,CAAC;IACjBE,KAAK,EAAEH,IAAI,CAACC,EAAE,CAAC,CAAC;IAChBG,SAAS,EAAEJ,IAAI,CAACC,EAAE,CAAC,CAAC;IACpBI,QAAQ,EAAEL,IAAI,CAACC,EAAE,CAAC,CAAC;IACnBK,SAAS,EAAEN,IAAI,CAACC,EAAE,CAAC;MAAA,OAAM,IAAI;IAAA,EAAC;IAC9BM,SAAS,EAAEP,IAAI,CAACC,EAAE,CAAC;MAAA,OAAM,IAAI;IAAA,EAAC;IAC9BO,KAAK,EAAER,IAAI,CAACC,EAAE,CAAC;MAAA,OAAM,eAAe;IAAA,EAAC;IACrCQ,SAAS,EAAET,IAAI,CAACC,EAAE,CAAC,CAAC;IACpBS,QAAQ,EAAEV,IAAI,CAACC,EAAE,CAAC;MAAA,OAAO;QACvBU,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;UAAEC,IAAI,EAAE,MAAM;UAAEC,GAAG,EAAE;QAAW,CAAC;MAC5C,CAAC;IAAA,CAAC,CAAC;IACHC,WAAW,EAAEf,IAAI,CAACC,EAAE,CAAC,CAAC;IACtBe,cAAc,EAAEhB,IAAI,CAACC,EAAE,CAAC,CAAC;IACzBgB,UAAU,EAAEjB,IAAI,CAACC,EAAE,CAAC;EACtB,CAAC;AAAA,CAAC;AAEK,IAAMiB,QAAQ,GAAApB,OAAA,CAAAoB,QAAA,GAAG,SAAXA,QAAQA,CAAA;EAAA,OAAU;IAC7BJ,GAAG,EAAE,gBAAgB;IACrBD,IAAI,EAAE,YAAY;IAClBM,MAAM,EAAE,CAAC;EACX,CAAC;AAAA,CAAC;AAEK,IAAMC,cAAc,GAAAtB,OAAA,CAAAsB,cAAA,GAAGpB,IAAI,CAACC,EAAE,CAAC,CAAC;AAEhC,IAAMoB,YAAY,GAAAvB,OAAA,CAAAuB,YAAA,GAAGrB,IAAI,CAACC,EAAE,CAAC;EAAA,OAAM,IAAI;AAAA,EAAC;AAExC,IAAMqB,mBAAmB,GAAAxB,OAAA,CAAAwB,mBAAA,GAAG,SAAtBA,mBAAmBA,CAAAC,IAAA;EAAA,IAAMC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;EAAA,OAAOA,QAAQ;AAAA;AAEtD,IAAMC,4BAA4B,GAAA3B,OAAA,CAAA2B,4BAAA,GAAGzB,IAAI,CAACC,EAAE,CAAC;EAAA,OAAO;IACzDyB,OAAO,EAAE;MACP3B,QAAQ,EAAEC,IAAI,CAACC,EAAE,CAAC,CAAC;MACnBE,KAAK,EAAEH,IAAI,CAACC,EAAE,CAAC,CAAC;MAChBC,MAAM,EAAEF,IAAI,CAACC,EAAE,CAAC,CAAC;MACjBI,QAAQ,EAAEL,IAAI,CAACC,EAAE,CAAC,CAAC;MACnBK,SAAS,EAAEN,IAAI,CAACC,EAAE,CAAC;QAAA,OAAM,IAAI;MAAA,EAAC;MAC9BM,SAAS,EAAEP,IAAI,CAACC,EAAE,CAAC;QAAA,OAAM,IAAI;MAAA,EAAC;MAC9B0B,YAAY,EAAE3B,IAAI,CAACC,EAAE,CAAC;QAAA,OAAO;UAC3BU,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;YAAEC,IAAI,EAAE,MAAM;YAAEC,GAAG,EAAE;UAAW,CAAC;QAC5C,CAAC;MAAA,CAAC;IACJ;EACF,CAAC;AAAA,CAAC,CAAC;AAEI,IAAMc,yBAAyB,GAAA9B,OAAA,CAAA8B,yBAAA,GAAG5B,IAAI,CAACC,EAAE,CAAC;EAAA,OAAO;IACtDyB,OAAO,EAAE;MACP3B,QAAQ,EAAEC,IAAI,CAACC,EAAE,CAAC,CAAC;MACnBE,KAAK,EAAEH,IAAI,CAACC,EAAE,CAAC,CAAC;MAChBC,MAAM,EAAEF,IAAI,CAACC,EAAE,CAAC;IAClB;EACF,CAAC;AAAA,CAAC,CAAC;AAEI,IAAM4B,aAAa,GAAA/B,OAAA,CAAA+B,aAAA,GAAG;EAC3B9B,QAAQ,EAAEC,IAAI,CAACC,EAAE,CAAC,CAAC;EACnBE,KAAK,EAAEH,IAAI,CAACC,EAAE,CAAC,CAAC;EAChBC,MAAM,EAAEF,IAAI,CAACC,EAAE,CAAC,CAAC;EACjBG,SAAS,EAAEJ,IAAI,CAACC,EAAE,CAAC;AACrB,CAAC;AAEM,IAAM6B,YAAY,GAAAhC,OAAA,CAAAgC,YAAA,GAAG;EAC1BC,IAAI,EAAE/B,IAAI,CAACC,EAAE,CAAC,CAAC;EACf+B,GAAG,EAAEhC,IAAI,CAACC,EAAE,CAAC,CAAC;EACdgC,QAAQ,EAAEjC,IAAI,CAACC,EAAE,CAAC,CAAC;EACnBiC,OAAO,EAAElC,IAAI,CAACC,EAAE,CAAC;AACnB,CAAC;AAEM,IAAMkC,UAAU,GAAArC,OAAA,CAAAqC,UAAA,GAAG;EACxBC,MAAM,EAAEpC,IAAI,CAACC,EAAE,CAAC;AAClB,CAAC;AAEM,IAAMoC,aAAa,GAAAvC,OAAA,CAAAuC,aAAA,GAAG;EAC3BC,UAAU,EAAEtC,IAAI,CAACC,EAAE,CAAC,CAAC;EACrBsC,WAAW,EAAEvC,IAAI,CAACC,EAAE,CAAC,CAAC;EACtBuC,YAAY,EAAExC,IAAI,CAACC,EAAE,CAAC,CAAC;EACvBmC,MAAM,EAAEpC,IAAI,CAACC,EAAE,CAAC;AAClB,CAAC;AAAC,IAAAwC,QAAA,GAAA3C,OAAA,CAAA4C,OAAA,GAEa;EACb7C,aAAa,EAAbA,aAAa;EACbqB,QAAQ,EAARA,QAAQ;EACRE,cAAc,EAAdA,cAAc;EACdC,YAAY,EAAZA,YAAY;EACZC,mBAAmB,EAAnBA,mBAAmB;EACnBG,4BAA4B,EAA5BA,4BAA4B;EAC5BG,yBAAyB,EAAzBA,yBAAyB;EACzBC,aAAa,EAAbA,aAAa;EACbC,YAAY,EAAZA,YAAY;EACZK,UAAU,EAAVA,UAAU;EACVE,aAAa,EAAbA;AACF,CAAC", "ignoreList": []}