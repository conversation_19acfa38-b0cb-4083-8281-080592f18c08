2abad4a7b5027b5a6f0eff821062d0ae
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.AccountSettingsScreen = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _vectorIcons = require("@expo/vector-icons");
var _native = require("@react-navigation/native");
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _Button = require("../components/atoms/Button");
var _Card = require("../components/atoms/Card");
var _SafeAreaWrapper = require("../components/ui/SafeAreaWrapper");
var _ThemeContext = require("../contexts/ThemeContext");
var _authSlice = require("../store/authSlice");
var _responsiveUtils = require("../utils/responsiveUtils");
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var AccountSettingsScreen = exports.AccountSettingsScreen = function AccountSettingsScreen() {
  var _useAuthStore = (0, _authSlice.useAuthStore)(),
    logout = _useAuthStore.logout,
    userRole = _useAuthStore.userRole;
  var _useTheme = (0, _ThemeContext.useTheme)(),
    colors = _useTheme.colors,
    isDark = _useTheme.isDark,
    setTheme = _useTheme.setTheme;
  var navigation = (0, _native.useNavigation)();
  var _useState = (0, _react.useState)(true),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    notificationsEnabled = _useState2[0],
    setNotificationsEnabled = _useState2[1];
  var _useState3 = (0, _react.useState)(true),
    _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
    locationEnabled = _useState4[0],
    setLocationEnabled = _useState4[1];
  var styles = createStyles(colors);
  var mockUser = {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    avatar: null
  };
  var handleEditProfile = function handleEditProfile() {
    _reactNative.Alert.alert('Edit Profile', 'Profile editing functionality will be available soon.', [{
      text: 'OK'
    }]);
  };
  var handleAccountSettings = function handleAccountSettings() {
    _reactNative.Alert.alert('Account Settings', 'Account settings functionality will be available soon.', [{
      text: 'OK'
    }]);
  };
  var handleLogout = function handleLogout() {
    _reactNative.Alert.alert('Sign Out', 'Are you sure you want to sign out?', [{
      text: 'Cancel',
      style: 'cancel'
    }, {
      text: 'Sign Out',
      style: 'destructive',
      onPress: function () {
        var _onPress = (0, _asyncToGenerator2.default)(function* () {
          try {
            logout();
          } catch (error) {
            _reactNative.Alert.alert('Error', 'Failed to sign out');
          }
        });
        function onPress() {
          return _onPress.apply(this, arguments);
        }
        return onPress;
      }()
    }]);
  };
  var handleRoleSwitch = function handleRoleSwitch() {
    _reactNative.Alert.alert('Become a Provider', 'Provider registration functionality will be available soon.', [{
      text: 'OK'
    }]);
  };
  var handleChangePassword = function handleChangePassword() {
    _reactNative.Alert.alert('Change Password', 'Password change functionality will be available soon.', [{
      text: 'OK'
    }]);
  };
  var handleNotifications = function handleNotifications() {};
  var handlePrivacy = function handlePrivacy() {
    _reactNative.Alert.alert('Privacy Settings', 'Privacy settings will be available soon.', [{
      text: 'OK'
    }]);
  };
  var handleHelp = function handleHelp() {
    _reactNative.Alert.alert('Help & Support', 'Help and support will be available soon.', [{
      text: 'OK'
    }]);
  };
  var profileSections = [[{
    id: 'edit-profile',
    title: 'Edit Profile',
    subtitle: 'Update your personal information',
    icon: 'person-outline',
    action: handleEditProfile,
    showChevron: true
  }, {
    id: 'change-password',
    title: 'Change Password',
    subtitle: 'Update your account password',
    icon: 'lock-closed-outline',
    action: handleChangePassword,
    showChevron: true
  }], [{
    id: 'notifications',
    title: 'Push Notifications',
    subtitle: 'Receive booking and message alerts',
    icon: 'notifications-outline',
    action: handleNotifications,
    showSwitch: true,
    switchValue: notificationsEnabled,
    onSwitchChange: setNotificationsEnabled
  }, {
    id: 'privacy',
    title: 'Privacy Settings',
    subtitle: 'Control your privacy and data',
    icon: 'shield-checkmark-outline',
    action: handlePrivacy,
    showChevron: true
  }, {
    id: 'location',
    title: 'Location Services',
    subtitle: 'Allow location access for nearby services',
    icon: 'location-outline',
    action: function action() {},
    showSwitch: true,
    switchValue: locationEnabled,
    onSwitchChange: setLocationEnabled
  }], [{
    id: 'role-switch',
    title: 'Become a Provider',
    subtitle: 'Start offering your services',
    icon: 'business-outline',
    action: handleRoleSwitch,
    showChevron: true
  }, {
    id: 'help',
    title: 'Help & Support',
    subtitle: 'Get help and contact support',
    icon: 'help-circle-outline',
    action: handleHelp,
    showChevron: true
  }]];
  var renderProfileHeader = function renderProfileHeader() {
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.profileHeader,
      children: (0, _jsxRuntime.jsx)(_Card.Card, {
        style: styles.profileCard,
        children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.profileInfo,
          children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: styles.avatarContainer,
            children: (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: styles.avatar,
              children: (0, _jsxRuntime.jsxs)(_reactNative.Text, {
                style: styles.avatarText,
                children: [mockUser.firstName.charAt(0), mockUser.lastName.charAt(0)]
              })
            })
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.userDetails,
            children: [(0, _jsxRuntime.jsxs)(_reactNative.Text, {
              style: styles.userName,
              children: [mockUser.firstName, " ", mockUser.lastName]
            }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: styles.userEmail,
              children: mockUser.email
            }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: styles.userRole,
              children: "Customer"
            })]
          })]
        })
      })
    });
  };
  var renderMenuItem = function renderMenuItem(item) {
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      style: styles.menuItem,
      onPress: item.action,
      testID: `menu-${item.id}`,
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.menuIcon,
        children: (0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
          name: item.icon,
          size: 24,
          color: colors.text.primary
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.menuContent,
        children: [(0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: styles.menuTitle,
          children: item.title
        }), item.subtitle && (0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: styles.menuSubtitle,
          children: item.subtitle
        })]
      }), item.showSwitch && (0, _jsxRuntime.jsx)(_reactNative.Switch, {
        value: item.switchValue,
        onValueChange: item.onSwitchChange,
        trackColor: {
          false: colors.sage100,
          true: colors.sage400
        },
        thumbColor: item.switchValue ? '#FFFFFF' : '#F4F4F4'
      }), item.showChevron && (0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
        name: "chevron-forward",
        size: 20,
        color: colors.text.tertiary
      })]
    }, item.id);
  };
  var renderSection = function renderSection(section, index) {
    return (0, _jsxRuntime.jsx)(_Card.Card, {
      style: styles.section,
      children: section.map(renderMenuItem)
    }, index);
  };
  return (0, _jsxRuntime.jsx)(_SafeAreaWrapper.SafeAreaScreen, {
    backgroundColor: colors.background.secondary,
    statusBarStyle: isDark ? 'light-content' : 'dark-content',
    respectNotch: true,
    respectGestures: true,
    testID: "profile-screen",
    children: (0, _jsxRuntime.jsxs)(_reactNative.ScrollView, {
      style: styles.container,
      contentContainerStyle: styles.content,
      showsVerticalScrollIndicator: false,
      children: [renderProfileHeader(), profileSections.map(renderSection), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.logoutSection,
        children: (0, _jsxRuntime.jsxs)(_Button.Button, {
          onPress: handleLogout,
          variant: "secondary",
          style: styles.logoutButton,
          testID: "logout-button",
          children: [(0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
            name: "log-out-outline",
            size: 20,
            color: colors.text.secondary
          }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: styles.logoutButtonText,
            children: "Logout"
          })]
        })
      })]
    })
  });
};
var createStyles = function createStyles(colors) {
  return _reactNative.StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.secondary
    },
    content: {
      paddingBottom: (0, _responsiveUtils.getResponsiveSpacing)(32)
    },
    profileHeader: {
      paddingHorizontal: (0, _responsiveUtils.getResponsiveSpacing)(20),
      paddingTop: (0, _responsiveUtils.getResponsiveSpacing)(20),
      paddingBottom: (0, _responsiveUtils.getResponsiveSpacing)(16)
    },
    profileCard: {
      padding: (0, _responsiveUtils.getResponsiveSpacing)(20),
      backgroundColor: colors.background.primary,
      borderRadius: (0, _responsiveUtils.getResponsiveSpacing)(16),
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2
      },
      shadowOpacity: 0.1,
      shadowRadius: 3.84,
      elevation: 5
    },
    profileInfo: {
      flexDirection: 'row',
      alignItems: 'center'
    },
    avatarContainer: {
      marginRight: (0, _responsiveUtils.getResponsiveSpacing)(16)
    },
    avatar: {
      width: (0, _responsiveUtils.getMinimumTouchTarget)(64),
      height: (0, _responsiveUtils.getMinimumTouchTarget)(64),
      borderRadius: (0, _responsiveUtils.getMinimumTouchTarget)(32),
      backgroundColor: colors.sage400,
      alignItems: 'center',
      justifyContent: 'center'
    },
    avatarText: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(24),
      fontWeight: '600',
      color: '#FFFFFF'
    },
    userDetails: {
      flex: 1
    },
    userName: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(20),
      fontWeight: '600',
      color: colors.text.primary,
      marginBottom: (0, _responsiveUtils.getResponsiveSpacing)(4)
    },
    userEmail: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(16),
      color: colors.text.secondary,
      marginBottom: (0, _responsiveUtils.getResponsiveSpacing)(2)
    },
    userRole: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(14),
      color: colors.sage400,
      fontWeight: '500'
    },
    section: {
      marginHorizontal: (0, _responsiveUtils.getResponsiveSpacing)(20),
      marginBottom: (0, _responsiveUtils.getResponsiveSpacing)(16),
      backgroundColor: colors.background.primary,
      borderRadius: (0, _responsiveUtils.getResponsiveSpacing)(16),
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2
      },
      shadowOpacity: 0.1,
      shadowRadius: 3.84,
      elevation: 5
    },
    menuItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: (0, _responsiveUtils.getResponsiveSpacing)(20),
      paddingVertical: (0, _responsiveUtils.getResponsiveSpacing)(16),
      borderBottomWidth: 1,
      borderBottomColor: colors.sage50
    },
    menuIcon: {
      width: (0, _responsiveUtils.getMinimumTouchTarget)(32),
      alignItems: 'center',
      marginRight: (0, _responsiveUtils.getResponsiveSpacing)(16)
    },
    menuContent: {
      flex: 1
    },
    menuTitle: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(16),
      fontWeight: '500',
      color: colors.text.primary,
      marginBottom: (0, _responsiveUtils.getResponsiveSpacing)(2)
    },
    menuSubtitle: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(14),
      color: colors.text.secondary,
      lineHeight: (0, _responsiveUtils.getResponsiveFontSize)(20)
    },
    logoutSection: {
      paddingHorizontal: (0, _responsiveUtils.getResponsiveSpacing)(20),
      paddingTop: (0, _responsiveUtils.getResponsiveSpacing)(32)
    },
    logoutButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.background.primary,
      borderWidth: 1,
      borderColor: colors.sage200,
      paddingVertical: (0, _responsiveUtils.getResponsiveSpacing)(16),
      borderRadius: (0, _responsiveUtils.getResponsiveSpacing)(12)
    },
    logoutButtonText: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(16),
      fontWeight: '500',
      color: colors.text.secondary,
      marginLeft: (0, _responsiveUtils.getResponsiveSpacing)(8)
    }
  });
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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