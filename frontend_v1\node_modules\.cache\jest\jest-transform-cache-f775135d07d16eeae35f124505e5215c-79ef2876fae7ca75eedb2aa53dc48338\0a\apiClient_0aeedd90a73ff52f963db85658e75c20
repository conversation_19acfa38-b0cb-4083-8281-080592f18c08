a668262708a85db855c9f3b6a1c5c6f6
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.apiClient = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _asyncStorage = _interopRequireDefault(require("@react-native-async-storage/async-storage"));
var _authSlice = require("../store/authSlice");
var _performanceCacheService = require("./performanceCacheService");
var getApiBaseUrl = function getApiBaseUrl() {
  if (!__DEV__) {
    return 'https://api.vierla.com';
  }
  return 'http://192.168.2.65:8000';
};
var API_BASE_URL = getApiBaseUrl();
var DEFAULT_TIMEOUT = 10000;
var ApiClient = function () {
  function ApiClient() {
    var baseURL = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : API_BASE_URL;
    var timeout = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : DEFAULT_TIMEOUT;
    (0, _classCallCheck2.default)(this, ApiClient);
    this.authToken = null;
    this.baseURL = baseURL;
    this.defaultTimeout = timeout;
    this.loadAuthToken();
  }
  return (0, _createClass2.default)(ApiClient, [{
    key: "loadAuthToken",
    value: (function () {
      var _loadAuthToken = (0, _asyncToGenerator2.default)(function* () {
        try {
          var token = yield _asyncStorage.default.getItem('auth_token');
          this.authToken = token;
        } catch (error) {
          console.warn('Failed to load auth token:', error);
        }
      });
      function loadAuthToken() {
        return _loadAuthToken.apply(this, arguments);
      }
      return loadAuthToken;
    }())
  }, {
    key: "setAuthToken",
    value: function setAuthToken(token) {
      this.authToken = token;
      if (token) {
        _asyncStorage.default.setItem('auth_token', token);
      } else {
        _asyncStorage.default.removeItem('auth_token');
      }
    }
  }, {
    key: "refreshAuthToken",
    value: (function () {
      var _refreshAuthToken = (0, _asyncToGenerator2.default)(function* () {
        try {
          var refreshToken = yield _asyncStorage.default.getItem('refresh_token');
          if (!refreshToken) {
            throw new Error('No refresh token available');
          }
          var refreshResponse = yield fetch(`${this.baseURL}/api/auth/token/refresh/`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Accept: 'application/json'
            },
            body: JSON.stringify({
              refresh: refreshToken
            })
          });
          if (!refreshResponse.ok) {
            var errorData = yield refreshResponse.json();
            throw new Error(errorData.detail || 'Token refresh failed');
          }
          var refreshData = yield refreshResponse.json();
          if (refreshData.access) {
            this.setAuthToken(refreshData.access);
            var authState = _authSlice.useAuthStore.getState();
            authState.loginSuccess(refreshData.access, authState.userRole || 'customer');
            console.log('✅ API: Token refreshed successfully');
          }
          if (refreshData.refresh) {
            yield _asyncStorage.default.setItem('refresh_token', refreshData.refresh);
          }
        } catch (error) {
          console.error('❌ API: Token refresh failed:', error.message);
          this.setAuthToken(null);
          yield _asyncStorage.default.removeItem('refresh_token');
          var _authState = _authSlice.useAuthStore.getState();
          _authState.logout();
          throw error;
        }
      });
      function refreshAuthToken() {
        return _refreshAuthToken.apply(this, arguments);
      }
      return refreshAuthToken;
    }())
  }, {
    key: "buildUrl",
    value: function buildUrl(url, params) {
      var fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`;
      if (!params || Object.keys(params).length === 0) {
        return fullUrl;
      }
      var urlParams = new URLSearchParams();
      Object.entries(params).forEach(function (_ref) {
        var _ref2 = (0, _slicedToArray2.default)(_ref, 2),
          key = _ref2[0],
          value = _ref2[1];
        if (value !== undefined && value !== null) {
          urlParams.append(key, String(value));
        }
      });
      var separator = fullUrl.includes('?') ? '&' : '?';
      return `${fullUrl}${separator}${urlParams.toString()}`;
    }
  }, {
    key: "buildHeaders",
    value: function buildHeaders(customHeaders) {
      var requiresAuth = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
      var headers = Object.assign({
        'Content-Type': 'application/json',
        Accept: 'application/json'
      }, customHeaders);
      if (requiresAuth) {
        var authState = _authSlice.useAuthStore.getState();
        var token = authState.authToken || this.authToken;
        if (token) {
          headers.Authorization = `Bearer ${token}`;
        }
      }
      return headers;
    }
  }, {
    key: "makeRequest",
    value: (function () {
      var _makeRequest = (0, _asyncToGenerator2.default)(function* (config) {
        var method = config.method,
          url = config.url,
          data = config.data,
          params = config.params,
          customHeaders = config.headers,
          _config$timeout = config.timeout,
          timeout = _config$timeout === void 0 ? this.defaultTimeout : _config$timeout,
          _config$requiresAuth = config.requiresAuth,
          requiresAuth = _config$requiresAuth === void 0 ? true : _config$requiresAuth,
          onProgress = config.onProgress,
          onStatusUpdate = config.onStatusUpdate,
          cache = config.cache;
        if (method === 'GET' && cache != null && cache.enabled) {
          var cacheKey = cache.key || `${url}_${JSON.stringify(params || {})}`;
          var cachedResponse = yield _performanceCacheService.performanceCacheService.getCachedApiResponse(cacheKey);
          if (cachedResponse) {
            onStatusUpdate == null || onStatusUpdate('Loaded from cache');
            onProgress == null || onProgress({
              loaded: 100,
              total: 100,
              percentage: 100
            });
            return {
              data: cachedResponse,
              status: 200,
              statusText: 'OK (Cached)',
              headers: {}
            };
          }
        }
        var fullUrl = this.buildUrl(url, params);
        var headers = this.buildHeaders(customHeaders, requiresAuth);
        var controller = new AbortController();
        var timeoutId = setTimeout(function () {
          return controller.abort();
        }, timeout);
        try {
          onStatusUpdate == null || onStatusUpdate('Preparing request...');
          var requestInit = {
            method: method,
            headers: headers,
            signal: controller.signal
          };
          if (data && method !== 'GET') {
            requestInit.body = JSON.stringify(data);
          }
          onStatusUpdate == null || onStatusUpdate('Sending request...');
          onProgress == null || onProgress({
            loaded: 0,
            total: 100,
            percentage: 0
          });
          var response = yield fetch(fullUrl, requestInit);
          clearTimeout(timeoutId);
          onStatusUpdate == null || onStatusUpdate('Processing response...');
          onProgress == null || onProgress({
            loaded: 50,
            total: 100,
            percentage: 50
          });
          var responseData;
          var contentType = response.headers.get('content-type');
          onProgress == null || onProgress({
            loaded: 75,
            total: 100,
            percentage: 75
          });
          if (contentType && contentType.includes('application/json')) {
            responseData = yield response.json();
          } else {
            responseData = yield response.text();
          }
          if (!response.ok) {
            onStatusUpdate == null || onStatusUpdate('Request failed');
            if (response.status === 401 && requiresAuth) {
              console.warn('🔐 API: Authentication failed, attempting token refresh...');
              try {
                yield this.refreshAuthToken();
                console.log('🔄 API: Retrying request with refreshed token...');
                var retryHeaders = this.buildHeaders(customHeaders, requiresAuth);
                var retryResponse = yield fetch(fullUrl, Object.assign({}, requestInit, {
                  headers: retryHeaders
                }));
                if (retryResponse.ok) {
                  var retryData = yield retryResponse.json();
                  console.log('✅ API: Request succeeded after token refresh');
                  return {
                    data: retryData,
                    status: retryResponse.status,
                    statusText: retryResponse.statusText,
                    headers: Object.fromEntries(retryResponse.headers.entries())
                  };
                }
              } catch (refreshError) {
                console.error('❌ API: Token refresh failed:', refreshError);
              }
            }
            throw {
              message: responseData || response.statusText,
              status: response.status,
              details: responseData
            };
          }
          onStatusUpdate == null || onStatusUpdate('Request completed');
          onProgress == null || onProgress({
            loaded: 100,
            total: 100,
            percentage: 100
          });
          if (method === 'GET' && cache != null && cache.enabled && response.ok) {
            var _cacheKey = cache.key || `${url}_${JSON.stringify(params || {})}`;
            yield _performanceCacheService.performanceCacheService.cacheApiResponse(_cacheKey, responseData, {
              ttl: cache.ttl,
              priority: cache.priority
            });
          }
          return {
            data: responseData,
            status: response.status,
            statusText: response.statusText,
            headers: Object.fromEntries(response.headers.entries())
          };
        } catch (error) {
          clearTimeout(timeoutId);
          if (error.name === 'AbortError') {
            throw {
              message: 'Request timeout',
              status: 408
            };
          }
          if (error.status) {
            throw error;
          }
          throw {
            message: error.message || 'Network error',
            status: 0,
            details: error
          };
        }
      });
      function makeRequest(_x) {
        return _makeRequest.apply(this, arguments);
      }
      return makeRequest;
    }())
  }, {
    key: "get",
    value: (function () {
      var _get = (0, _asyncToGenerator2.default)(function* (url, params) {
        var _cacheOptions$enabled;
        var requiresAuth = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;
        var cacheOptions = arguments.length > 3 ? arguments[3] : undefined;
        return this.makeRequest({
          method: 'GET',
          url: url,
          params: params,
          requiresAuth: requiresAuth,
          cache: {
            enabled: (_cacheOptions$enabled = cacheOptions == null ? void 0 : cacheOptions.enabled) != null ? _cacheOptions$enabled : true,
            ttl: cacheOptions == null ? void 0 : cacheOptions.ttl,
            priority: cacheOptions == null ? void 0 : cacheOptions.priority
          }
        });
      });
      function get(_x2, _x3) {
        return _get.apply(this, arguments);
      }
      return get;
    }())
  }, {
    key: "post",
    value: (function () {
      var _post = (0, _asyncToGenerator2.default)(function* (url, data) {
        var requiresAuth = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;
        return this.makeRequest({
          method: 'POST',
          url: url,
          data: data,
          requiresAuth: requiresAuth
        });
      });
      function post(_x4, _x5) {
        return _post.apply(this, arguments);
      }
      return post;
    }())
  }, {
    key: "put",
    value: (function () {
      var _put = (0, _asyncToGenerator2.default)(function* (url, data) {
        var requiresAuth = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;
        return this.makeRequest({
          method: 'PUT',
          url: url,
          data: data,
          requiresAuth: requiresAuth
        });
      });
      function put(_x6, _x7) {
        return _put.apply(this, arguments);
      }
      return put;
    }())
  }, {
    key: "patch",
    value: (function () {
      var _patch = (0, _asyncToGenerator2.default)(function* (url, data) {
        var requiresAuth = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;
        return this.makeRequest({
          method: 'PATCH',
          url: url,
          data: data,
          requiresAuth: requiresAuth
        });
      });
      function patch(_x8, _x9) {
        return _patch.apply(this, arguments);
      }
      return patch;
    }())
  }, {
    key: "delete",
    value: (function () {
      var _delete2 = (0, _asyncToGenerator2.default)(function* (url) {
        var requiresAuth = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
        return this.makeRequest({
          method: 'DELETE',
          url: url,
          requiresAuth: requiresAuth
        });
      });
      function _delete(_x0) {
        return _delete2.apply(this, arguments);
      }
      return _delete;
    }())
  }]);
}();
var apiClient = exports.apiClient = new ApiClient();
var _default = exports.default = apiClient;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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