{"version": 3, "names": ["warnOnce", "require", "default", "invariant", "module", "exports", "registerCallableModule", "AccessibilityInfo", "ActivityIndicator", "<PERSON><PERSON>", "DrawerLayoutAndroid", "FlatList", "Image", "ImageBackground", "InputAccessoryView", "experimental_LayoutConformance", "KeyboardAvoidingView", "Modal", "Pressable", "ProgressBarAndroid", "RefreshControl", "SafeAreaView", "ScrollView", "SectionList", "StatusBar", "Switch", "Text", "TextInput", "Touchable", "TouchableHighlight", "TouchableNativeFeedback", "TouchableOpacity", "TouchableWithoutFeedback", "View", "VirtualizedList", "VirtualizedSectionList", "ActionSheetIOS", "<PERSON><PERSON>", "Animated", "Appearance", "AppRegistry", "AppState", "<PERSON><PERSON><PERSON><PERSON>", "Clipboard", "DeviceInfo", "DevMenu", "DevSettings", "Dimensions", "Easing", "findNodeHandle", "I18nManager", "InteractionManager", "Keyboard", "LayoutAnimation", "Linking", "LogBox", "NativeDialogManagerAndroid", "NativeEventEmitter", "Networking", "PanResponder", "PermissionsAndroid", "PixelRatio", "PushNotificationIOS", "Settings", "Share", "StyleSheet", "Systrace", "ToastAndroid", "TurboModuleRegistry", "UIManager", "unstable_batchedUpdates", "useAnimatedValue", "useColorScheme", "useWindowDimensions", "UTFSequence", "Vibration", "DeviceEventEmitter", "DynamicColorIOS", "NativeAppEventEmitter", "NativeModules", "Platform", "PlatformColor", "processColor", "requireNativeComponent", "RootTagContext", "__DEV__", "Object", "defineProperty", "configurable", "get"], "sources": ["index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\n// flowlint unsafe-getters-setters:off\n/* eslint-disable lint/no-commonjs-exports */\n\n'use strict';\n'use client';\n\n// ----------------------------------------------------------------------------\n// Runtime entry point for react-native.\n//\n// This module is separate from index.js.flow as it provides a more lenient\n// `module.exports` API at runtime, for lazy module loading and backwards\n// compatibility.\n//\n// IMPORTANT: Keep this file in sync with index.js.flow. Test your changes\n// whenever updating React Native's public API.\n// ----------------------------------------------------------------------------\n\nimport typeof * as ReactNativePublicAPI from './index.js.flow';\n\nconst warnOnce = require('./Libraries/Utilities/warnOnce').default;\nconst invariant = require('invariant');\n\nmodule.exports = {\n  get registerCallableModule() {\n    return require('./Libraries/Core/registerCallableModule').default;\n  },\n  // #region Components\n  get AccessibilityInfo() {\n    return require('./Libraries/Components/AccessibilityInfo/AccessibilityInfo')\n      .default;\n  },\n  get ActivityIndicator() {\n    return require('./Libraries/Components/ActivityIndicator/ActivityIndicator')\n      .default;\n  },\n  get Button() {\n    return require('./Libraries/Components/Button').default;\n  },\n  get DrawerLayoutAndroid() {\n    return require('./Libraries/Components/DrawerAndroid/DrawerLayoutAndroid')\n      .default;\n  },\n  get FlatList() {\n    return require('./Libraries/Lists/FlatList').default;\n  },\n  get Image() {\n    return require('./Libraries/Image/Image').default;\n  },\n  get ImageBackground() {\n    return require('./Libraries/Image/ImageBackground').default;\n  },\n  get InputAccessoryView() {\n    return require('./Libraries/Components/TextInput/InputAccessoryView')\n      .default;\n  },\n  get experimental_LayoutConformance() {\n    return require('./Libraries/Components/LayoutConformance/LayoutConformance')\n      .default;\n  },\n  get KeyboardAvoidingView() {\n    return require('./Libraries/Components/Keyboard/KeyboardAvoidingView')\n      .default;\n  },\n  get Modal() {\n    return require('./Libraries/Modal/Modal').default;\n  },\n  get Pressable() {\n    return require('./Libraries/Components/Pressable/Pressable').default;\n  },\n  get ProgressBarAndroid() {\n    warnOnce(\n      'progress-bar-android-moved',\n      'ProgressBarAndroid has been extracted from react-native core and will be removed in a future release. ' +\n        \"It can now be installed and imported from '@react-native-community/progress-bar-android' instead of 'react-native'. \" +\n        'See https://github.com/react-native-progress-view/progress-bar-android',\n    );\n    return require('./Libraries/Components/ProgressBarAndroid/ProgressBarAndroid')\n      .default;\n  },\n  get RefreshControl() {\n    return require('./Libraries/Components/RefreshControl/RefreshControl')\n      .default;\n  },\n  get SafeAreaView() {\n    return require('./Libraries/Components/SafeAreaView/SafeAreaView').default;\n  },\n  get ScrollView() {\n    return require('./Libraries/Components/ScrollView/ScrollView').default;\n  },\n  get SectionList() {\n    return require('./Libraries/Lists/SectionList').default;\n  },\n  get StatusBar() {\n    return require('./Libraries/Components/StatusBar/StatusBar').default;\n  },\n  get Switch() {\n    return require('./Libraries/Components/Switch/Switch').default;\n  },\n  get Text() {\n    return require('./Libraries/Text/Text').default;\n  },\n  get TextInput() {\n    return require('./Libraries/Components/TextInput/TextInput').default;\n  },\n  get Touchable() {\n    return require('./Libraries/Components/Touchable/Touchable').default;\n  },\n  get TouchableHighlight() {\n    return require('./Libraries/Components/Touchable/TouchableHighlight')\n      .default;\n  },\n  get TouchableNativeFeedback() {\n    return require('./Libraries/Components/Touchable/TouchableNativeFeedback')\n      .default;\n  },\n  get TouchableOpacity() {\n    return require('./Libraries/Components/Touchable/TouchableOpacity').default;\n  },\n  get TouchableWithoutFeedback() {\n    return require('./Libraries/Components/Touchable/TouchableWithoutFeedback')\n      .default;\n  },\n  get View() {\n    return require('./Libraries/Components/View/View').default;\n  },\n  get VirtualizedList() {\n    return require('./Libraries/Lists/VirtualizedList').default;\n  },\n  get VirtualizedSectionList() {\n    return require('./Libraries/Lists/VirtualizedSectionList').default;\n  },\n  // #endregion\n  // #region APIs\n  get ActionSheetIOS() {\n    return require('./Libraries/ActionSheetIOS/ActionSheetIOS').default;\n  },\n  get Alert() {\n    return require('./Libraries/Alert/Alert').default;\n  },\n  // Include any types exported in the Animated module together with its default export, so\n  // you can references types such as Animated.Numeric\n  get Animated() {\n    return require('./Libraries/Animated/Animated').default;\n  },\n  get Appearance() {\n    return require('./Libraries/Utilities/Appearance');\n  },\n  get AppRegistry() {\n    return require('./Libraries/ReactNative/AppRegistry').default;\n  },\n  get AppState() {\n    return require('./Libraries/AppState/AppState').default;\n  },\n  get BackHandler() {\n    return require('./Libraries/Utilities/BackHandler').default;\n  },\n  get Clipboard() {\n    warnOnce(\n      'clipboard-moved',\n      'Clipboard has been extracted from react-native core and will be removed in a future release. ' +\n        \"It can now be installed and imported from '@react-native-clipboard/clipboard' instead of 'react-native'. \" +\n        'See https://github.com/react-native-clipboard/clipboard',\n    );\n    return require('./Libraries/Components/Clipboard/Clipboard').default;\n  },\n  get DeviceInfo() {\n    return require('./Libraries/Utilities/DeviceInfo').default;\n  },\n  get DevMenu() {\n    return require('./src/private/devmenu/DevMenu').default;\n  },\n  get DevSettings() {\n    return require('./Libraries/Utilities/DevSettings').default;\n  },\n  get Dimensions() {\n    return require('./Libraries/Utilities/Dimensions').default;\n  },\n  get Easing() {\n    return require('./Libraries/Animated/Easing').default;\n  },\n  get findNodeHandle() {\n    return require('./Libraries/ReactNative/RendererProxy').findNodeHandle;\n  },\n  get I18nManager() {\n    return require('./Libraries/ReactNative/I18nManager').default;\n  },\n  get InteractionManager() {\n    return require('./Libraries/Interaction/InteractionManager').default;\n  },\n  get Keyboard() {\n    return require('./Libraries/Components/Keyboard/Keyboard').default;\n  },\n  get LayoutAnimation() {\n    return require('./Libraries/LayoutAnimation/LayoutAnimation').default;\n  },\n  get Linking() {\n    return require('./Libraries/Linking/Linking').default;\n  },\n  get LogBox() {\n    return require('./Libraries/LogBox/LogBox').default;\n  },\n  get NativeDialogManagerAndroid() {\n    return require('./Libraries/NativeModules/specs/NativeDialogManagerAndroid')\n      .default;\n  },\n  get NativeEventEmitter() {\n    return require('./Libraries/EventEmitter/NativeEventEmitter').default;\n  },\n  get Networking() {\n    return require('./Libraries/Network/RCTNetworking').default;\n  },\n  get PanResponder() {\n    return require('./Libraries/Interaction/PanResponder').default;\n  },\n  get PermissionsAndroid() {\n    return require('./Libraries/PermissionsAndroid/PermissionsAndroid').default;\n  },\n  get PixelRatio() {\n    return require('./Libraries/Utilities/PixelRatio').default;\n  },\n  get PushNotificationIOS() {\n    warnOnce(\n      'pushNotificationIOS-moved',\n      'PushNotificationIOS has been extracted from react-native core and will be removed in a future release. ' +\n        \"It can now be installed and imported from '@react-native-community/push-notification-ios' instead of 'react-native'. \" +\n        'See https://github.com/react-native-push-notification/ios',\n    );\n    return require('./Libraries/PushNotificationIOS/PushNotificationIOS')\n      .default;\n  },\n  get Settings() {\n    return require('./Libraries/Settings/Settings').default;\n  },\n  get Share() {\n    return require('./Libraries/Share/Share').default;\n  },\n  get StyleSheet() {\n    return require('./Libraries/StyleSheet/StyleSheet').default;\n  },\n  get Systrace() {\n    return require('./Libraries/Performance/Systrace');\n  },\n  get ToastAndroid() {\n    return require('./Libraries/Components/ToastAndroid/ToastAndroid').default;\n  },\n  get TurboModuleRegistry() {\n    return require('./Libraries/TurboModule/TurboModuleRegistry');\n  },\n  get UIManager() {\n    return require('./Libraries/ReactNative/UIManager').default;\n  },\n  get unstable_batchedUpdates() {\n    return require('./Libraries/ReactNative/RendererProxy')\n      .unstable_batchedUpdates;\n  },\n  get useAnimatedValue() {\n    return require('./Libraries/Animated/useAnimatedValue').default;\n  },\n  get useColorScheme() {\n    return require('./Libraries/Utilities/useColorScheme').default;\n  },\n  get useWindowDimensions() {\n    return require('./Libraries/Utilities/useWindowDimensions').default;\n  },\n  get UTFSequence() {\n    return require('./Libraries/UTFSequence').default;\n  },\n  get Vibration() {\n    return require('./Libraries/Vibration/Vibration').default;\n  },\n  // #endregion\n  // #region Plugins\n  get DeviceEventEmitter() {\n    return require('./Libraries/EventEmitter/RCTDeviceEventEmitter').default;\n  },\n  get DynamicColorIOS() {\n    return require('./Libraries/StyleSheet/PlatformColorValueTypesIOS')\n      .DynamicColorIOS;\n  },\n  get NativeAppEventEmitter() {\n    return require('./Libraries/EventEmitter/RCTNativeAppEventEmitter').default;\n  },\n  get NativeModules() {\n    return require('./Libraries/BatchedBridge/NativeModules').default;\n  },\n  get Platform() {\n    return require('./Libraries/Utilities/Platform').default;\n  },\n  get PlatformColor() {\n    return require('./Libraries/StyleSheet/PlatformColorValueTypes')\n      .PlatformColor;\n  },\n  get processColor() {\n    return require('./Libraries/StyleSheet/processColor').default;\n  },\n  get requireNativeComponent() {\n    return require('./Libraries/ReactNative/requireNativeComponent').default;\n  },\n  get RootTagContext() {\n    return require('./Libraries/ReactNative/RootTag').RootTagContext;\n  },\n  // #endregion\n} as ReactNativePublicAPI;\n\nif (__DEV__) {\n  /* $FlowFixMe[prop-missing] This is intentional: Flow will error when\n   * attempting to access ART. */\n  /* $FlowFixMe[invalid-export] This is intentional: Flow will error when\n   * attempting to access ART. */\n  Object.defineProperty(module.exports, 'ART', {\n    configurable: true,\n    get() {\n      invariant(\n        false,\n        'ART has been removed from React Native. ' +\n          \"Please upgrade to use either 'react-native-svg' or a similar package. \" +\n          \"If you cannot upgrade to a different library, please install the deprecated '@react-native-community/art' package. \" +\n          'See https://github.com/react-native-art/art',\n      );\n    },\n  });\n\n  /* $FlowFixMe[prop-missing] This is intentional: Flow will error when\n   * attempting to access ListView. */\n  /* $FlowFixMe[invalid-export] This is intentional: Flow will error when\n   * attempting to access ListView. */\n  Object.defineProperty(module.exports, 'ListView', {\n    configurable: true,\n    get() {\n      invariant(\n        false,\n        'ListView has been removed from React Native. ' +\n          'See https://fb.me/nolistview for more information or use ' +\n          '`deprecated-react-native-listview`.',\n      );\n    },\n  });\n\n  /* $FlowFixMe[prop-missing] This is intentional: Flow will error when\n   * attempting to access SwipeableListView. */\n  /* $FlowFixMe[invalid-export] This is intentional: Flow will error when\n   * attempting to access SwipeableListView. */\n  Object.defineProperty(module.exports, 'SwipeableListView', {\n    configurable: true,\n    get() {\n      invariant(\n        false,\n        'SwipeableListView has been removed from React Native. ' +\n          'See https://fb.me/nolistview for more information or use ' +\n          '`deprecated-react-native-swipeable-listview`.',\n      );\n    },\n  });\n\n  /* $FlowFixMe[prop-missing] This is intentional: Flow will error when\n   * attempting to access WebView. */\n  /* $FlowFixMe[invalid-export] This is intentional: Flow will error when\n   * attempting to access WebView. */\n  Object.defineProperty(module.exports, 'WebView', {\n    configurable: true,\n    get() {\n      invariant(\n        false,\n        'WebView has been removed from React Native. ' +\n          \"It can now be installed and imported from 'react-native-webview' instead of 'react-native'. \" +\n          'See https://github.com/react-native-webview/react-native-webview',\n      );\n    },\n  });\n\n  /* $FlowFixMe[prop-missing] This is intentional: Flow will error when\n   * attempting to access NetInfo. */\n  /* $FlowFixMe[invalid-export] This is intentional: Flow will error when\n   * attempting to access NetInfo. */\n  Object.defineProperty(module.exports, 'NetInfo', {\n    configurable: true,\n    get() {\n      invariant(\n        false,\n        'NetInfo has been removed from React Native. ' +\n          \"It can now be installed and imported from '@react-native-community/netinfo' instead of 'react-native'. \" +\n          'See https://github.com/react-native-netinfo/react-native-netinfo',\n      );\n    },\n  });\n\n  /* $FlowFixMe[prop-missing] This is intentional: Flow will error when\n   * attempting to access CameraRoll. */\n  /* $FlowFixMe[invalid-export] This is intentional: Flow will error when\n   * attempting to access CameraRoll. */\n  Object.defineProperty(module.exports, 'CameraRoll', {\n    configurable: true,\n    get() {\n      invariant(\n        false,\n        'CameraRoll has been removed from React Native. ' +\n          \"It can now be installed and imported from '@react-native-camera-roll/camera-roll' instead of 'react-native'. \" +\n          'See https://github.com/react-native-cameraroll/react-native-cameraroll',\n      );\n    },\n  });\n\n  /* $FlowFixMe[prop-missing] This is intentional: Flow will error when\n   * attempting to access ImageStore. */\n  /* $FlowFixMe[invalid-export] This is intentional: Flow will error when\n   * attempting to access ImageStore. */\n  Object.defineProperty(module.exports, 'ImageStore', {\n    configurable: true,\n    get() {\n      invariant(\n        false,\n        'ImageStore has been removed from React Native. ' +\n          'To get a base64-encoded string from a local image use either of the following third-party libraries:' +\n          \"* expo-file-system: `readAsStringAsync(filepath, 'base64')`\" +\n          \"* react-native-fs: `readFile(filepath, 'base64')`\",\n      );\n    },\n  });\n\n  /* $FlowFixMe[prop-missing] This is intentional: Flow will error when\n   * attempting to access ImageEditor. */\n  /* $FlowFixMe[invalid-export] This is intentional: Flow will error when\n   * attempting to access ImageEditor. */\n  Object.defineProperty(module.exports, 'ImageEditor', {\n    configurable: true,\n    get() {\n      invariant(\n        false,\n        'ImageEditor has been removed from React Native. ' +\n          \"It can now be installed and imported from '@react-native-community/image-editor' instead of 'react-native'. \" +\n          'See https://github.com/callstack/react-native-image-editor',\n      );\n    },\n  });\n\n  /* $FlowFixMe[prop-missing] This is intentional: Flow will error when\n   * attempting to access TimePickerAndroid. */\n  /* $FlowFixMe[invalid-export] This is intentional: Flow will error when\n   * attempting to access TimePickerAndroid. */\n  Object.defineProperty(module.exports, 'TimePickerAndroid', {\n    configurable: true,\n    get() {\n      invariant(\n        false,\n        'TimePickerAndroid has been removed from React Native. ' +\n          \"It can now be installed and imported from '@react-native-community/datetimepicker' instead of 'react-native'. \" +\n          'See https://github.com/react-native-datetimepicker/datetimepicker',\n      );\n    },\n  });\n\n  /* $FlowFixMe[prop-missing] This is intentional: Flow will error when\n   * attempting to access ToolbarAndroid. */\n  /* $FlowFixMe[invalid-export] This is intentional: Flow will error when\n   * attempting to access ToolbarAndroid. */\n  Object.defineProperty(module.exports, 'ToolbarAndroid', {\n    configurable: true,\n    get() {\n      invariant(\n        false,\n        'ToolbarAndroid has been removed from React Native. ' +\n          \"It can now be installed and imported from '@react-native-community/toolbar-android' instead of 'react-native'. \" +\n          'See https://github.com/react-native-toolbar-android/toolbar-android',\n      );\n    },\n  });\n\n  /* $FlowFixMe[prop-missing] This is intentional: Flow will error when\n   * attempting to access ViewPagerAndroid. */\n  /* $FlowFixMe[invalid-export] This is intentional: Flow will error when\n   * attempting to access ViewPagerAndroid. */\n  Object.defineProperty(module.exports, 'ViewPagerAndroid', {\n    configurable: true,\n    get() {\n      invariant(\n        false,\n        'ViewPagerAndroid has been removed from React Native. ' +\n          \"It can now be installed and imported from 'react-native-pager-view' instead of 'react-native'. \" +\n          'See https://github.com/callstack/react-native-pager-view',\n      );\n    },\n  });\n\n  /* $FlowFixMe[prop-missing] This is intentional: Flow will error when\n   * attempting to access CheckBox. */\n  /* $FlowFixMe[invalid-export] This is intentional: Flow will error when\n   * attempting to access CheckBox. */\n  Object.defineProperty(module.exports, 'CheckBox', {\n    configurable: true,\n    get() {\n      invariant(\n        false,\n        'CheckBox has been removed from React Native. ' +\n          \"It can now be installed and imported from '@react-native-community/checkbox' instead of 'react-native'. \" +\n          'See https://github.com/react-native-checkbox/react-native-checkbox',\n      );\n    },\n  });\n\n  /* $FlowFixMe[prop-missing] This is intentional: Flow will error when\n   * attempting to access SegmentedControlIOS. */\n  /* $FlowFixMe[invalid-export] This is intentional: Flow will error when\n   * attempting to access SegmentedControlIOS. */\n  Object.defineProperty(module.exports, 'SegmentedControlIOS', {\n    configurable: true,\n    get() {\n      invariant(\n        false,\n        'SegmentedControlIOS has been removed from React Native. ' +\n          \"It can now be installed and imported from '@react-native-segmented-control/segmented-control' instead of 'react-native'.\" +\n          'See https://github.com/react-native-segmented-control/segmented-control',\n      );\n    },\n  });\n\n  /* $FlowFixMe[prop-missing] This is intentional: Flow will error when\n   * attempting to access StatusBarIOS. */\n  /* $FlowFixMe[invalid-export] This is intentional: Flow will error when\n   * attempting to access StatusBarIOS. */\n  Object.defineProperty(module.exports, 'StatusBarIOS', {\n    configurable: true,\n    get() {\n      invariant(\n        false,\n        'StatusBarIOS has been removed from React Native. ' +\n          'Has been merged with StatusBar. ' +\n          'See https://reactnative.dev/docs/statusbar',\n      );\n    },\n  });\n\n  /* $FlowFixMe[prop-missing] This is intentional: Flow will error when\n   * attempting to access PickerIOS. */\n  /* $FlowFixMe[invalid-export] This is intentional: Flow will error when\n   * attempting to access PickerIOS. */\n  Object.defineProperty(module.exports, 'PickerIOS', {\n    configurable: true,\n    get() {\n      invariant(\n        false,\n        'PickerIOS has been removed from React Native. ' +\n          \"It can now be installed and imported from '@react-native-picker/picker' instead of 'react-native'. \" +\n          'See https://github.com/react-native-picker/picker',\n      );\n    },\n  });\n\n  /* $FlowFixMe[prop-missing] This is intentional: Flow will error when\n   * attempting to access Picker. */\n  /* $FlowFixMe[invalid-export] This is intentional: Flow will error when\n   * attempting to access Picker. */\n  Object.defineProperty(module.exports, 'Picker', {\n    configurable: true,\n    get() {\n      invariant(\n        false,\n        'Picker has been removed from React Native. ' +\n          \"It can now be installed and imported from '@react-native-picker/picker' instead of 'react-native'. \" +\n          'See https://github.com/react-native-picker/picker',\n      );\n    },\n  });\n  /* $FlowFixMe[prop-missing] This is intentional: Flow will error when\n   * attempting to access DatePickerAndroid. */\n  /* $FlowFixMe[invalid-export] This is intentional: Flow will error when\n   * attempting to access DatePickerAndroid. */\n  Object.defineProperty(module.exports, 'DatePickerAndroid', {\n    configurable: true,\n    get() {\n      invariant(\n        false,\n        'DatePickerAndroid has been removed from React Native. ' +\n          \"It can now be installed and imported from '@react-native-community/datetimepicker' instead of 'react-native'. \" +\n          'See https://github.com/react-native-datetimepicker/datetimepicker',\n      );\n    },\n  });\n  /* $FlowFixMe[prop-missing] This is intentional: Flow will error when\n   * attempting to access MaskedViewIOS. */\n  /* $FlowFixMe[invalid-export] This is intentional: Flow will error when\n   * attempting to access MaskedViewIOS. */\n  Object.defineProperty(module.exports, 'MaskedViewIOS', {\n    configurable: true,\n    get() {\n      invariant(\n        false,\n        'MaskedViewIOS has been removed from React Native. ' +\n          \"It can now be installed and imported from '@react-native-masked-view/masked-view' instead of 'react-native'. \" +\n          'See https://github.com/react-native-masked-view/masked-view',\n      );\n    },\n  });\n  /* $FlowFixMe[prop-missing] This is intentional: Flow will error when\n   * attempting to access AsyncStorage. */\n  /* $FlowFixMe[invalid-export] This is intentional: Flow will error when\n   * attempting to access AsyncStorage. */\n  Object.defineProperty(module.exports, 'AsyncStorage', {\n    configurable: true,\n    get() {\n      invariant(\n        false,\n        'AsyncStorage has been removed from react-native core. ' +\n          \"It can now be installed and imported from '@react-native-async-storage/async-storage' instead of 'react-native'. \" +\n          'See https://github.com/react-native-async-storage/async-storage',\n      );\n    },\n  });\n  /* $FlowFixMe[prop-missing] This is intentional: Flow will error when\n   * attempting to access ImagePickerIOS. */\n  /* $FlowFixMe[invalid-export] This is intentional: Flow will error when\n   * attempting to access ImagePickerIOS. */\n  Object.defineProperty(module.exports, 'ImagePickerIOS', {\n    configurable: true,\n    get() {\n      invariant(\n        false,\n        'ImagePickerIOS has been removed from React Native. ' +\n          \"Please upgrade to use either 'react-native-image-picker' or 'expo-image-picker'. \" +\n          \"If you cannot upgrade to a different library, please install the deprecated '@react-native-community/image-picker-ios' package. \" +\n          'See https://github.com/rnc-archive/react-native-image-picker-ios',\n      );\n    },\n  });\n  /* $FlowFixMe[prop-missing] This is intentional: Flow will error when\n   * attempting to access ProgressViewIOS. */\n  /* $FlowFixMe[invalid-export] This is intentional: Flow will error when\n   * attempting to access ProgressViewIOS. */\n  Object.defineProperty(module.exports, 'ProgressViewIOS', {\n    configurable: true,\n    get() {\n      invariant(\n        false,\n        'ProgressViewIOS has been removed from react-native core. ' +\n          \"It can now be installed and imported from '@react-native-community/progress-view' instead of 'react-native'. \" +\n          'See https://github.com/react-native-progress-view/progress-view',\n      );\n    },\n  });\n  /* $FlowFixMe[prop-missing] This is intentional: Flow will error when\n   * attempting to access DatePickerIOS. */\n  /* $FlowFixMe[invalid-export] This is intentional: Flow will error when\n   * attempting to access DatePickerIOS. */\n  Object.defineProperty(module.exports, 'DatePickerIOS', {\n    configurable: true,\n    get() {\n      invariant(\n        false,\n        'DatePickerIOS has been removed from react-native core. ' +\n          \"It can now be installed and imported from '@react-native-community/datetimepicker' instead of 'react-native'. \" +\n          'See https://github.com/react-native-datetimepicker/datetimepicker',\n      );\n    },\n  });\n  /* $FlowFixMe[prop-missing] This is intentional: Flow will error when\n   * attempting to access Slider. */\n  /* $FlowFixMe[invalid-export] This is intentional: Flow will error when\n   * attempting to access Slider. */\n  Object.defineProperty(module.exports, 'Slider', {\n    configurable: true,\n    get() {\n      invariant(\n        false,\n        'Slider has been removed from react-native core. ' +\n          \"It can now be installed and imported from '@react-native-community/slider' instead of 'react-native'. \" +\n          'See https://github.com/callstack/react-native-slider',\n      );\n    },\n  });\n}\n"], "mappings": "AAaA,YAAY;AACZ,YAAY;;AAeZ,IAAMA,QAAQ,GAAGC,OAAO,iCAAiC,CAAC,CAACC,OAAO;AAClE,IAAMC,SAAS,GAAGF,OAAO,CAAC,WAAW,CAAC;AAEtCG,MAAM,CAACC,OAAO,GAAG;EACf,IAAIC,sBAAsBA,CAAA,EAAG;IAC3B,OAAOL,OAAO,0CAA0C,CAAC,CAACC,OAAO;EACnE,CAAC;EAED,IAAIK,iBAAiBA,CAAA,EAAG;IACtB,OAAON,OAAO,6DAA6D,CAAC,CACzEC,OAAO;EACZ,CAAC;EACD,IAAIM,iBAAiBA,CAAA,EAAG;IACtB,OAAOP,OAAO,6DAA6D,CAAC,CACzEC,OAAO;EACZ,CAAC;EACD,IAAIO,MAAMA,CAAA,EAAG;IACX,OAAOR,OAAO,gCAAgC,CAAC,CAACC,OAAO;EACzD,CAAC;EACD,IAAIQ,mBAAmBA,CAAA,EAAG;IACxB,OAAOT,OAAO,2DAA2D,CAAC,CACvEC,OAAO;EACZ,CAAC;EACD,IAAIS,QAAQA,CAAA,EAAG;IACb,OAAOV,OAAO,6BAA6B,CAAC,CAACC,OAAO;EACtD,CAAC;EACD,IAAIU,KAAKA,CAAA,EAAG;IACV,OAAOX,OAAO,0BAA0B,CAAC,CAACC,OAAO;EACnD,CAAC;EACD,IAAIW,eAAeA,CAAA,EAAG;IACpB,OAAOZ,OAAO,oCAAoC,CAAC,CAACC,OAAO;EAC7D,CAAC;EACD,IAAIY,kBAAkBA,CAAA,EAAG;IACvB,OAAOb,OAAO,sDAAsD,CAAC,CAClEC,OAAO;EACZ,CAAC;EACD,IAAIa,8BAA8BA,CAAA,EAAG;IACnC,OAAOd,OAAO,6DAA6D,CAAC,CACzEC,OAAO;EACZ,CAAC;EACD,IAAIc,oBAAoBA,CAAA,EAAG;IACzB,OAAOf,OAAO,uDAAuD,CAAC,CACnEC,OAAO;EACZ,CAAC;EACD,IAAIe,KAAKA,CAAA,EAAG;IACV,OAAOhB,OAAO,0BAA0B,CAAC,CAACC,OAAO;EACnD,CAAC;EACD,IAAIgB,SAASA,CAAA,EAAG;IACd,OAAOjB,OAAO,6CAA6C,CAAC,CAACC,OAAO;EACtE,CAAC;EACD,IAAIiB,kBAAkBA,CAAA,EAAG;IACvBnB,QAAQ,CACN,4BAA4B,EAC5B,wGAAwG,GACtG,sHAAsH,GACtH,wEACJ,CAAC;IACD,OAAOC,OAAO,+DAA+D,CAAC,CAC3EC,OAAO;EACZ,CAAC;EACD,IAAIkB,cAAcA,CAAA,EAAG;IACnB,OAAOnB,OAAO,uDAAuD,CAAC,CACnEC,OAAO;EACZ,CAAC;EACD,IAAImB,YAAYA,CAAA,EAAG;IACjB,OAAOpB,OAAO,mDAAmD,CAAC,CAACC,OAAO;EAC5E,CAAC;EACD,IAAIoB,UAAUA,CAAA,EAAG;IACf,OAAOrB,OAAO,+CAA+C,CAAC,CAACC,OAAO;EACxE,CAAC;EACD,IAAIqB,WAAWA,CAAA,EAAG;IAChB,OAAOtB,OAAO,gCAAgC,CAAC,CAACC,OAAO;EACzD,CAAC;EACD,IAAIsB,SAASA,CAAA,EAAG;IACd,OAAOvB,OAAO,6CAA6C,CAAC,CAACC,OAAO;EACtE,CAAC;EACD,IAAIuB,MAAMA,CAAA,EAAG;IACX,OAAOxB,OAAO,uCAAuC,CAAC,CAACC,OAAO;EAChE,CAAC;EACD,IAAIwB,IAAIA,CAAA,EAAG;IACT,OAAOzB,OAAO,wBAAwB,CAAC,CAACC,OAAO;EACjD,CAAC;EACD,IAAIyB,SAASA,CAAA,EAAG;IACd,OAAO1B,OAAO,6CAA6C,CAAC,CAACC,OAAO;EACtE,CAAC;EACD,IAAI0B,SAASA,CAAA,EAAG;IACd,OAAO3B,OAAO,6CAA6C,CAAC,CAACC,OAAO;EACtE,CAAC;EACD,IAAI2B,kBAAkBA,CAAA,EAAG;IACvB,OAAO5B,OAAO,sDAAsD,CAAC,CAClEC,OAAO;EACZ,CAAC;EACD,IAAI4B,uBAAuBA,CAAA,EAAG;IAC5B,OAAO7B,OAAO,2DAA2D,CAAC,CACvEC,OAAO;EACZ,CAAC;EACD,IAAI6B,gBAAgBA,CAAA,EAAG;IACrB,OAAO9B,OAAO,oDAAoD,CAAC,CAACC,OAAO;EAC7E,CAAC;EACD,IAAI8B,wBAAwBA,CAAA,EAAG;IAC7B,OAAO/B,OAAO,4DAA4D,CAAC,CACxEC,OAAO;EACZ,CAAC;EACD,IAAI+B,IAAIA,CAAA,EAAG;IACT,OAAOhC,OAAO,mCAAmC,CAAC,CAACC,OAAO;EAC5D,CAAC;EACD,IAAIgC,eAAeA,CAAA,EAAG;IACpB,OAAOjC,OAAO,oCAAoC,CAAC,CAACC,OAAO;EAC7D,CAAC;EACD,IAAIiC,sBAAsBA,CAAA,EAAG;IAC3B,OAAOlC,OAAO,2CAA2C,CAAC,CAACC,OAAO;EACpE,CAAC;EAGD,IAAIkC,cAAcA,CAAA,EAAG;IACnB,OAAOnC,OAAO,4CAA4C,CAAC,CAACC,OAAO;EACrE,CAAC;EACD,IAAImC,KAAKA,CAAA,EAAG;IACV,OAAOpC,OAAO,0BAA0B,CAAC,CAACC,OAAO;EACnD,CAAC;EAGD,IAAIoC,QAAQA,CAAA,EAAG;IACb,OAAOrC,OAAO,gCAAgC,CAAC,CAACC,OAAO;EACzD,CAAC;EACD,IAAIqC,UAAUA,CAAA,EAAG;IACf,OAAOtC,OAAO,mCAAmC,CAAC;EACpD,CAAC;EACD,IAAIuC,WAAWA,CAAA,EAAG;IAChB,OAAOvC,OAAO,sCAAsC,CAAC,CAACC,OAAO;EAC/D,CAAC;EACD,IAAIuC,QAAQA,CAAA,EAAG;IACb,OAAOxC,OAAO,gCAAgC,CAAC,CAACC,OAAO;EACzD,CAAC;EACD,IAAIwC,WAAWA,CAAA,EAAG;IAChB,OAAOzC,OAAO,oCAAoC,CAAC,CAACC,OAAO;EAC7D,CAAC;EACD,IAAIyC,SAASA,CAAA,EAAG;IACd3C,QAAQ,CACN,iBAAiB,EACjB,+FAA+F,GAC7F,2GAA2G,GAC3G,yDACJ,CAAC;IACD,OAAOC,OAAO,6CAA6C,CAAC,CAACC,OAAO;EACtE,CAAC;EACD,IAAI0C,UAAUA,CAAA,EAAG;IACf,OAAO3C,OAAO,mCAAmC,CAAC,CAACC,OAAO;EAC5D,CAAC;EACD,IAAI2C,OAAOA,CAAA,EAAG;IACZ,OAAO5C,OAAO,gCAAgC,CAAC,CAACC,OAAO;EACzD,CAAC;EACD,IAAI4C,WAAWA,CAAA,EAAG;IAChB,OAAO7C,OAAO,oCAAoC,CAAC,CAACC,OAAO;EAC7D,CAAC;EACD,IAAI6C,UAAUA,CAAA,EAAG;IACf,OAAO9C,OAAO,mCAAmC,CAAC,CAACC,OAAO;EAC5D,CAAC;EACD,IAAI8C,MAAMA,CAAA,EAAG;IACX,OAAO/C,OAAO,8BAA8B,CAAC,CAACC,OAAO;EACvD,CAAC;EACD,IAAI+C,cAAcA,CAAA,EAAG;IACnB,OAAOhD,OAAO,wCAAwC,CAAC,CAACgD,cAAc;EACxE,CAAC;EACD,IAAIC,WAAWA,CAAA,EAAG;IAChB,OAAOjD,OAAO,sCAAsC,CAAC,CAACC,OAAO;EAC/D,CAAC;EACD,IAAIiD,kBAAkBA,CAAA,EAAG;IACvB,OAAOlD,OAAO,6CAA6C,CAAC,CAACC,OAAO;EACtE,CAAC;EACD,IAAIkD,QAAQA,CAAA,EAAG;IACb,OAAOnD,OAAO,2CAA2C,CAAC,CAACC,OAAO;EACpE,CAAC;EACD,IAAImD,eAAeA,CAAA,EAAG;IACpB,OAAOpD,OAAO,8CAA8C,CAAC,CAACC,OAAO;EACvE,CAAC;EACD,IAAIoD,OAAOA,CAAA,EAAG;IACZ,OAAOrD,OAAO,8BAA8B,CAAC,CAACC,OAAO;EACvD,CAAC;EACD,IAAIqD,MAAMA,CAAA,EAAG;IACX,OAAOtD,OAAO,4BAA4B,CAAC,CAACC,OAAO;EACrD,CAAC;EACD,IAAIsD,0BAA0BA,CAAA,EAAG;IAC/B,OAAOvD,OAAO,6DAA6D,CAAC,CACzEC,OAAO;EACZ,CAAC;EACD,IAAIuD,kBAAkBA,CAAA,EAAG;IACvB,OAAOxD,OAAO,8CAA8C,CAAC,CAACC,OAAO;EACvE,CAAC;EACD,IAAIwD,UAAUA,CAAA,EAAG;IACf,OAAOzD,OAAO,oCAAoC,CAAC,CAACC,OAAO;EAC7D,CAAC;EACD,IAAIyD,YAAYA,CAAA,EAAG;IACjB,OAAO1D,OAAO,uCAAuC,CAAC,CAACC,OAAO;EAChE,CAAC;EACD,IAAI0D,kBAAkBA,CAAA,EAAG;IACvB,OAAO3D,OAAO,oDAAoD,CAAC,CAACC,OAAO;EAC7E,CAAC;EACD,IAAI2D,UAAUA,CAAA,EAAG;IACf,OAAO5D,OAAO,mCAAmC,CAAC,CAACC,OAAO;EAC5D,CAAC;EACD,IAAI4D,mBAAmBA,CAAA,EAAG;IACxB9D,QAAQ,CACN,2BAA2B,EAC3B,yGAAyG,GACvG,uHAAuH,GACvH,2DACJ,CAAC;IACD,OAAOC,OAAO,sDAAsD,CAAC,CAClEC,OAAO;EACZ,CAAC;EACD,IAAI6D,QAAQA,CAAA,EAAG;IACb,OAAO9D,OAAO,gCAAgC,CAAC,CAACC,OAAO;EACzD,CAAC;EACD,IAAI8D,KAAKA,CAAA,EAAG;IACV,OAAO/D,OAAO,0BAA0B,CAAC,CAACC,OAAO;EACnD,CAAC;EACD,IAAI+D,UAAUA,CAAA,EAAG;IACf,OAAOhE,OAAO,oCAAoC,CAAC,CAACC,OAAO;EAC7D,CAAC;EACD,IAAIgE,QAAQA,CAAA,EAAG;IACb,OAAOjE,OAAO,mCAAmC,CAAC;EACpD,CAAC;EACD,IAAIkE,YAAYA,CAAA,EAAG;IACjB,OAAOlE,OAAO,mDAAmD,CAAC,CAACC,OAAO;EAC5E,CAAC;EACD,IAAIkE,mBAAmBA,CAAA,EAAG;IACxB,OAAOnE,OAAO,8CAA8C,CAAC;EAC/D,CAAC;EACD,IAAIoE,SAASA,CAAA,EAAG;IACd,OAAOpE,OAAO,oCAAoC,CAAC,CAACC,OAAO;EAC7D,CAAC;EACD,IAAIoE,uBAAuBA,CAAA,EAAG;IAC5B,OAAOrE,OAAO,wCAAwC,CAAC,CACpDqE,uBAAuB;EAC5B,CAAC;EACD,IAAIC,gBAAgBA,CAAA,EAAG;IACrB,OAAOtE,OAAO,wCAAwC,CAAC,CAACC,OAAO;EACjE,CAAC;EACD,IAAIsE,cAAcA,CAAA,EAAG;IACnB,OAAOvE,OAAO,uCAAuC,CAAC,CAACC,OAAO;EAChE,CAAC;EACD,IAAIuE,mBAAmBA,CAAA,EAAG;IACxB,OAAOxE,OAAO,4CAA4C,CAAC,CAACC,OAAO;EACrE,CAAC;EACD,IAAIwE,WAAWA,CAAA,EAAG;IAChB,OAAOzE,OAAO,0BAA0B,CAAC,CAACC,OAAO;EACnD,CAAC;EACD,IAAIyE,SAASA,CAAA,EAAG;IACd,OAAO1E,OAAO,kCAAkC,CAAC,CAACC,OAAO;EAC3D,CAAC;EAGD,IAAI0E,kBAAkBA,CAAA,EAAG;IACvB,OAAO3E,OAAO,iDAAiD,CAAC,CAACC,OAAO;EAC1E,CAAC;EACD,IAAI2E,eAAeA,CAAA,EAAG;IACpB,OAAO5E,OAAO,oDAAoD,CAAC,CAChE4E,eAAe;EACpB,CAAC;EACD,IAAIC,qBAAqBA,CAAA,EAAG;IAC1B,OAAO7E,OAAO,oDAAoD,CAAC,CAACC,OAAO;EAC7E,CAAC;EACD,IAAI6E,aAAaA,CAAA,EAAG;IAClB,OAAO9E,OAAO,0CAA0C,CAAC,CAACC,OAAO;EACnE,CAAC;EACD,IAAI8E,QAAQA,CAAA,EAAG;IACb,OAAO/E,OAAO,iCAAiC,CAAC,CAACC,OAAO;EAC1D,CAAC;EACD,IAAI+E,aAAaA,CAAA,EAAG;IAClB,OAAOhF,OAAO,iDAAiD,CAAC,CAC7DgF,aAAa;EAClB,CAAC;EACD,IAAIC,YAAYA,CAAA,EAAG;IACjB,OAAOjF,OAAO,sCAAsC,CAAC,CAACC,OAAO;EAC/D,CAAC;EACD,IAAIiF,sBAAsBA,CAAA,EAAG;IAC3B,OAAOlF,OAAO,iDAAiD,CAAC,CAACC,OAAO;EAC1E,CAAC;EACD,IAAIkF,cAAcA,CAAA,EAAG;IACnB,OAAOnF,OAAO,kCAAkC,CAAC,CAACmF,cAAc;EAClE;AAEF,CAAyB;AAEzB,IAAIC,OAAO,EAAE;EAKXC,MAAM,CAACC,cAAc,CAACnF,MAAM,CAACC,OAAO,EAAE,KAAK,EAAE;IAC3CmF,YAAY,EAAE,IAAI;IAClBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;MACJtF,SAAS,CACP,KAAK,EACL,0CAA0C,GACxC,wEAAwE,GACxE,qHAAqH,GACrH,6CACJ,CAAC;IACH;EACF,CAAC,CAAC;EAMFmF,MAAM,CAACC,cAAc,CAACnF,MAAM,CAACC,OAAO,EAAE,UAAU,EAAE;IAChDmF,YAAY,EAAE,IAAI;IAClBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;MACJtF,SAAS,CACP,KAAK,EACL,+CAA+C,GAC7C,2DAA2D,GAC3D,qCACJ,CAAC;IACH;EACF,CAAC,CAAC;EAMFmF,MAAM,CAACC,cAAc,CAACnF,MAAM,CAACC,OAAO,EAAE,mBAAmB,EAAE;IACzDmF,YAAY,EAAE,IAAI;IAClBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;MACJtF,SAAS,CACP,KAAK,EACL,wDAAwD,GACtD,2DAA2D,GAC3D,+CACJ,CAAC;IACH;EACF,CAAC,CAAC;EAMFmF,MAAM,CAACC,cAAc,CAACnF,MAAM,CAACC,OAAO,EAAE,SAAS,EAAE;IAC/CmF,YAAY,EAAE,IAAI;IAClBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;MACJtF,SAAS,CACP,KAAK,EACL,8CAA8C,GAC5C,8FAA8F,GAC9F,kEACJ,CAAC;IACH;EACF,CAAC,CAAC;EAMFmF,MAAM,CAACC,cAAc,CAACnF,MAAM,CAACC,OAAO,EAAE,SAAS,EAAE;IAC/CmF,YAAY,EAAE,IAAI;IAClBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;MACJtF,SAAS,CACP,KAAK,EACL,8CAA8C,GAC5C,yGAAyG,GACzG,kEACJ,CAAC;IACH;EACF,CAAC,CAAC;EAMFmF,MAAM,CAACC,cAAc,CAACnF,MAAM,CAACC,OAAO,EAAE,YAAY,EAAE;IAClDmF,YAAY,EAAE,IAAI;IAClBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;MACJtF,SAAS,CACP,KAAK,EACL,iDAAiD,GAC/C,+GAA+G,GAC/G,wEACJ,CAAC;IACH;EACF,CAAC,CAAC;EAMFmF,MAAM,CAACC,cAAc,CAACnF,MAAM,CAACC,OAAO,EAAE,YAAY,EAAE;IAClDmF,YAAY,EAAE,IAAI;IAClBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;MACJtF,SAAS,CACP,KAAK,EACL,iDAAiD,GAC/C,sGAAsG,GACtG,6DAA6D,GAC7D,mDACJ,CAAC;IACH;EACF,CAAC,CAAC;EAMFmF,MAAM,CAACC,cAAc,CAACnF,MAAM,CAACC,OAAO,EAAE,aAAa,EAAE;IACnDmF,YAAY,EAAE,IAAI;IAClBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;MACJtF,SAAS,CACP,KAAK,EACL,kDAAkD,GAChD,8GAA8G,GAC9G,4DACJ,CAAC;IACH;EACF,CAAC,CAAC;EAMFmF,MAAM,CAACC,cAAc,CAACnF,MAAM,CAACC,OAAO,EAAE,mBAAmB,EAAE;IACzDmF,YAAY,EAAE,IAAI;IAClBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;MACJtF,SAAS,CACP,KAAK,EACL,wDAAwD,GACtD,gHAAgH,GAChH,mEACJ,CAAC;IACH;EACF,CAAC,CAAC;EAMFmF,MAAM,CAACC,cAAc,CAACnF,MAAM,CAACC,OAAO,EAAE,gBAAgB,EAAE;IACtDmF,YAAY,EAAE,IAAI;IAClBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;MACJtF,SAAS,CACP,KAAK,EACL,qDAAqD,GACnD,iHAAiH,GACjH,qEACJ,CAAC;IACH;EACF,CAAC,CAAC;EAMFmF,MAAM,CAACC,cAAc,CAACnF,MAAM,CAACC,OAAO,EAAE,kBAAkB,EAAE;IACxDmF,YAAY,EAAE,IAAI;IAClBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;MACJtF,SAAS,CACP,KAAK,EACL,uDAAuD,GACrD,iGAAiG,GACjG,0DACJ,CAAC;IACH;EACF,CAAC,CAAC;EAMFmF,MAAM,CAACC,cAAc,CAACnF,MAAM,CAACC,OAAO,EAAE,UAAU,EAAE;IAChDmF,YAAY,EAAE,IAAI;IAClBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;MACJtF,SAAS,CACP,KAAK,EACL,+CAA+C,GAC7C,0GAA0G,GAC1G,oEACJ,CAAC;IACH;EACF,CAAC,CAAC;EAMFmF,MAAM,CAACC,cAAc,CAACnF,MAAM,CAACC,OAAO,EAAE,qBAAqB,EAAE;IAC3DmF,YAAY,EAAE,IAAI;IAClBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;MACJtF,SAAS,CACP,KAAK,EACL,0DAA0D,GACxD,0HAA0H,GAC1H,yEACJ,CAAC;IACH;EACF,CAAC,CAAC;EAMFmF,MAAM,CAACC,cAAc,CAACnF,MAAM,CAACC,OAAO,EAAE,cAAc,EAAE;IACpDmF,YAAY,EAAE,IAAI;IAClBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;MACJtF,SAAS,CACP,KAAK,EACL,mDAAmD,GACjD,kCAAkC,GAClC,4CACJ,CAAC;IACH;EACF,CAAC,CAAC;EAMFmF,MAAM,CAACC,cAAc,CAACnF,MAAM,CAACC,OAAO,EAAE,WAAW,EAAE;IACjDmF,YAAY,EAAE,IAAI;IAClBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;MACJtF,SAAS,CACP,KAAK,EACL,gDAAgD,GAC9C,qGAAqG,GACrG,mDACJ,CAAC;IACH;EACF,CAAC,CAAC;EAMFmF,MAAM,CAACC,cAAc,CAACnF,MAAM,CAACC,OAAO,EAAE,QAAQ,EAAE;IAC9CmF,YAAY,EAAE,IAAI;IAClBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;MACJtF,SAAS,CACP,KAAK,EACL,6CAA6C,GAC3C,qGAAqG,GACrG,mDACJ,CAAC;IACH;EACF,CAAC,CAAC;EAKFmF,MAAM,CAACC,cAAc,CAACnF,MAAM,CAACC,OAAO,EAAE,mBAAmB,EAAE;IACzDmF,YAAY,EAAE,IAAI;IAClBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;MACJtF,SAAS,CACP,KAAK,EACL,wDAAwD,GACtD,gHAAgH,GAChH,mEACJ,CAAC;IACH;EACF,CAAC,CAAC;EAKFmF,MAAM,CAACC,cAAc,CAACnF,MAAM,CAACC,OAAO,EAAE,eAAe,EAAE;IACrDmF,YAAY,EAAE,IAAI;IAClBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;MACJtF,SAAS,CACP,KAAK,EACL,oDAAoD,GAClD,+GAA+G,GAC/G,6DACJ,CAAC;IACH;EACF,CAAC,CAAC;EAKFmF,MAAM,CAACC,cAAc,CAACnF,MAAM,CAACC,OAAO,EAAE,cAAc,EAAE;IACpDmF,YAAY,EAAE,IAAI;IAClBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;MACJtF,SAAS,CACP,KAAK,EACL,wDAAwD,GACtD,mHAAmH,GACnH,iEACJ,CAAC;IACH;EACF,CAAC,CAAC;EAKFmF,MAAM,CAACC,cAAc,CAACnF,MAAM,CAACC,OAAO,EAAE,gBAAgB,EAAE;IACtDmF,YAAY,EAAE,IAAI;IAClBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;MACJtF,SAAS,CACP,KAAK,EACL,qDAAqD,GACnD,mFAAmF,GACnF,kIAAkI,GAClI,kEACJ,CAAC;IACH;EACF,CAAC,CAAC;EAKFmF,MAAM,CAACC,cAAc,CAACnF,MAAM,CAACC,OAAO,EAAE,iBAAiB,EAAE;IACvDmF,YAAY,EAAE,IAAI;IAClBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;MACJtF,SAAS,CACP,KAAK,EACL,2DAA2D,GACzD,+GAA+G,GAC/G,iEACJ,CAAC;IACH;EACF,CAAC,CAAC;EAKFmF,MAAM,CAACC,cAAc,CAACnF,MAAM,CAACC,OAAO,EAAE,eAAe,EAAE;IACrDmF,YAAY,EAAE,IAAI;IAClBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;MACJtF,SAAS,CACP,KAAK,EACL,yDAAyD,GACvD,gHAAgH,GAChH,mEACJ,CAAC;IACH;EACF,CAAC,CAAC;EAKFmF,MAAM,CAACC,cAAc,CAACnF,MAAM,CAACC,OAAO,EAAE,QAAQ,EAAE;IAC9CmF,YAAY,EAAE,IAAI;IAClBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;MACJtF,SAAS,CACP,KAAK,EACL,kDAAkD,GAChD,wGAAwG,GACxG,sDACJ,CAAC;IACH;EACF,CAAC,CAAC;AACJ", "ignoreList": []}