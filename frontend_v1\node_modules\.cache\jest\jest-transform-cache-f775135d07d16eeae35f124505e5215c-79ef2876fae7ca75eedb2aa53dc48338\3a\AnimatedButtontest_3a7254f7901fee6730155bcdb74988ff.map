{"version": 3, "names": ["_reactNative", "require", "_react", "_interopRequireDefault", "_reactNative2", "_jsxRuntime", "_excluded", "MockAnimatedButton", "_ref", "title", "onPress", "testID", "disabled", "props", "_objectWithoutProperties2", "default", "jsx", "TouchableOpacity", "Object", "assign", "undefined", "accessibilityRole", "children", "Text", "describe", "defaultProps", "jest", "fn", "beforeEach", "clearAllMocks", "it", "_render", "render", "getByTestId", "getByText", "expect", "toBeTruthy", "_render2", "variant", "onPressMock", "_render3", "fireEvent", "press", "toHaveBeenCalledTimes", "_render4", "not", "toHaveBeenCalled", "_render5", "accessibilityLabel", "button", "toBe"], "sources": ["AnimatedButton.test.tsx"], "sourcesContent": ["/**\n * AnimatedButton Component Tests\n *\n * Basic test suite for the AnimatedButton component.\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport { render, fireEvent } from '@testing-library/react-native';\nimport React from 'react';\nimport { Text, TouchableOpacity } from 'react-native';\n\n// Simple mock component for testing\nconst MockAnimatedButton = ({ title, onPress, testID, disabled, ...props }) => (\n  <TouchableOpacity\n    testID={testID}\n    onPress={disabled ? undefined : onPress}\n    accessibilityRole=\"button\"\n    {...props}>\n    <Text>{title}</Text>\n  </TouchableOpacity>\n);\n\ndescribe('AnimatedButton', () => {\n  const defaultProps = {\n    title: 'Test Button',\n    onPress: jest.fn(),\n    testID: 'animated-button',\n  };\n\n  beforeEach(() => {\n    jest.clearAllMocks();\n  });\n\n  describe('Rendering', () => {\n    it('renders correctly with default props', () => {\n      const { getByTestId, getByText } = render(\n        <MockAnimatedButton {...defaultProps} />,\n      );\n\n      expect(getByTestId('animated-button')).toBeTruthy();\n      expect(getByText('Test Button')).toBeTruthy();\n    });\n\n    it('renders with different variants', () => {\n      const { getByTestId } = render(\n        <MockAnimatedButton\n          {...defaultProps}\n          variant=\"primary\"\n          testID=\"button-primary\"\n        />,\n      );\n\n      expect(getByTestId('button-primary')).toBeTruthy();\n    });\n  });\n\n  describe('Functionality', () => {\n    it('calls onPress when pressed', () => {\n      const onPressMock = jest.fn();\n      const { getByTestId } = render(\n        <MockAnimatedButton {...defaultProps} onPress={onPressMock} />,\n      );\n\n      fireEvent.press(getByTestId('animated-button'));\n      expect(onPressMock).toHaveBeenCalledTimes(1);\n    });\n\n    it('does not call onPress when disabled', () => {\n      const onPressMock = jest.fn();\n      const { getByTestId } = render(\n        <MockAnimatedButton\n          {...defaultProps}\n          onPress={onPressMock}\n          disabled={true}\n        />,\n      );\n\n      fireEvent.press(getByTestId('animated-button'));\n      expect(onPressMock).not.toHaveBeenCalled();\n    });\n  });\n\n  describe('Accessibility', () => {\n    it('has proper accessibility properties', () => {\n      const { getByTestId } = render(\n        <MockAnimatedButton\n          {...defaultProps}\n          accessibilityLabel=\"Custom accessibility label\"\n        />,\n      );\n\n      const button = getByTestId('animated-button');\n      expect(button.props.accessibilityRole).toBe('button');\n    });\n  });\n});\n"], "mappings": ";;AASA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,aAAA,GAAAH,OAAA;AAAsD,IAAAI,WAAA,GAAAJ,OAAA;AAAA,IAAAK,SAAA;AAGtD,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAAC,IAAA;EAAA,IAAMC,KAAK,GAAAD,IAAA,CAALC,KAAK;IAAEC,OAAO,GAAAF,IAAA,CAAPE,OAAO;IAAEC,MAAM,GAAAH,IAAA,CAANG,MAAM;IAAEC,QAAQ,GAAAJ,IAAA,CAARI,QAAQ;IAAKC,KAAK,OAAAC,yBAAA,CAAAC,OAAA,EAAAP,IAAA,EAAAF,SAAA;EAAA,OACtE,IAAAD,WAAA,CAAAW,GAAA,EAACZ,aAAA,CAAAa,gBAAgB,EAAAC,MAAA,CAAAC,MAAA;IACfR,MAAM,EAAEA,MAAO;IACfD,OAAO,EAAEE,QAAQ,GAAGQ,SAAS,GAAGV,OAAQ;IACxCW,iBAAiB,EAAC;EAAQ,GACtBR,KAAK;IAAAS,QAAA,EACT,IAAAjB,WAAA,CAAAW,GAAA,EAACZ,aAAA,CAAAmB,IAAI;MAAAD,QAAA,EAAEb;IAAK,CAAO;EAAC,EACJ,CAAC;AAAA,CACpB;AAEDe,QAAQ,CAAC,gBAAgB,EAAE,YAAM;EAC/B,IAAMC,YAAY,GAAG;IACnBhB,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAEgB,IAAI,CAACC,EAAE,CAAC,CAAC;IAClBhB,MAAM,EAAE;EACV,CAAC;EAEDiB,UAAU,CAAC,YAAM;IACfF,IAAI,CAACG,aAAa,CAAC,CAAC;EACtB,CAAC,CAAC;EAEFL,QAAQ,CAAC,WAAW,EAAE,YAAM;IAC1BM,EAAE,CAAC,sCAAsC,EAAE,YAAM;MAC/C,IAAAC,OAAA,GAAmC,IAAAC,mBAAM,EACvC,IAAA3B,WAAA,CAAAW,GAAA,EAACT,kBAAkB,EAAAW,MAAA,CAAAC,MAAA,KAAKM,YAAY,CAAG,CACzC,CAAC;QAFOQ,WAAW,GAAAF,OAAA,CAAXE,WAAW;QAAEC,SAAS,GAAAH,OAAA,CAATG,SAAS;MAI9BC,MAAM,CAACF,WAAW,CAAC,iBAAiB,CAAC,CAAC,CAACG,UAAU,CAAC,CAAC;MACnDD,MAAM,CAACD,SAAS,CAAC,aAAa,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC;IAC/C,CAAC,CAAC;IAEFN,EAAE,CAAC,iCAAiC,EAAE,YAAM;MAC1C,IAAAO,QAAA,GAAwB,IAAAL,mBAAM,EAC5B,IAAA3B,WAAA,CAAAW,GAAA,EAACT,kBAAkB,EAAAW,MAAA,CAAAC,MAAA,KACbM,YAAY;UAChBa,OAAO,EAAC,SAAS;UACjB3B,MAAM,EAAC;QAAgB,EACxB,CACH,CAAC;QANOsB,WAAW,GAAAI,QAAA,CAAXJ,WAAW;MAQnBE,MAAM,CAACF,WAAW,CAAC,gBAAgB,CAAC,CAAC,CAACG,UAAU,CAAC,CAAC;IACpD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFZ,QAAQ,CAAC,eAAe,EAAE,YAAM;IAC9BM,EAAE,CAAC,4BAA4B,EAAE,YAAM;MACrC,IAAMS,WAAW,GAAGb,IAAI,CAACC,EAAE,CAAC,CAAC;MAC7B,IAAAa,QAAA,GAAwB,IAAAR,mBAAM,EAC5B,IAAA3B,WAAA,CAAAW,GAAA,EAACT,kBAAkB,EAAAW,MAAA,CAAAC,MAAA,KAAKM,YAAY;UAAEf,OAAO,EAAE6B;QAAY,EAAE,CAC/D,CAAC;QAFON,WAAW,GAAAO,QAAA,CAAXP,WAAW;MAInBQ,sBAAS,CAACC,KAAK,CAACT,WAAW,CAAC,iBAAiB,CAAC,CAAC;MAC/CE,MAAM,CAACI,WAAW,CAAC,CAACI,qBAAqB,CAAC,CAAC,CAAC;IAC9C,CAAC,CAAC;IAEFb,EAAE,CAAC,qCAAqC,EAAE,YAAM;MAC9C,IAAMS,WAAW,GAAGb,IAAI,CAACC,EAAE,CAAC,CAAC;MAC7B,IAAAiB,QAAA,GAAwB,IAAAZ,mBAAM,EAC5B,IAAA3B,WAAA,CAAAW,GAAA,EAACT,kBAAkB,EAAAW,MAAA,CAAAC,MAAA,KACbM,YAAY;UAChBf,OAAO,EAAE6B,WAAY;UACrB3B,QAAQ,EAAE;QAAK,EAChB,CACH,CAAC;QANOqB,WAAW,GAAAW,QAAA,CAAXX,WAAW;MAQnBQ,sBAAS,CAACC,KAAK,CAACT,WAAW,CAAC,iBAAiB,CAAC,CAAC;MAC/CE,MAAM,CAACI,WAAW,CAAC,CAACM,GAAG,CAACC,gBAAgB,CAAC,CAAC;IAC5C,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFtB,QAAQ,CAAC,eAAe,EAAE,YAAM;IAC9BM,EAAE,CAAC,qCAAqC,EAAE,YAAM;MAC9C,IAAAiB,QAAA,GAAwB,IAAAf,mBAAM,EAC5B,IAAA3B,WAAA,CAAAW,GAAA,EAACT,kBAAkB,EAAAW,MAAA,CAAAC,MAAA,KACbM,YAAY;UAChBuB,kBAAkB,EAAC;QAA4B,EAChD,CACH,CAAC;QALOf,WAAW,GAAAc,QAAA,CAAXd,WAAW;MAOnB,IAAMgB,MAAM,GAAGhB,WAAW,CAAC,iBAAiB,CAAC;MAC7CE,MAAM,CAACc,MAAM,CAACpC,KAAK,CAACQ,iBAAiB,CAAC,CAAC6B,IAAI,CAAC,QAAQ,CAAC;IACvD,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}