2f3deda67ab21c8ccdb3ffa8d3c6ff99
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _reactNative = require("@testing-library/react-native");
var _react = _interopRequireDefault(require("react"));
var _reactNative2 = require("react-native");
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["title", "onPress", "testID", "disabled"];
var MockAnimatedButton = function MockAnimatedButton(_ref) {
  var title = _ref.title,
    onPress = _ref.onPress,
    testID = _ref.testID,
    disabled = _ref.disabled,
    props = (0, _objectWithoutProperties2.default)(_ref, _excluded);
  return (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, Object.assign({
    testID: testID,
    onPress: disabled ? undefined : onPress,
    accessibilityRole: "button"
  }, props, {
    children: (0, _jsxRuntime.jsx)(_reactNative2.Text, {
      children: title
    })
  }));
};
describe('AnimatedButton', function () {
  var defaultProps = {
    title: 'Test Button',
    onPress: jest.fn(),
    testID: 'animated-button'
  };
  beforeEach(function () {
    jest.clearAllMocks();
  });
  describe('Rendering', function () {
    it('renders correctly with default props', function () {
      var _render = (0, _reactNative.render)((0, _jsxRuntime.jsx)(MockAnimatedButton, Object.assign({}, defaultProps))),
        getByTestId = _render.getByTestId,
        getByText = _render.getByText;
      expect(getByTestId('animated-button')).toBeTruthy();
      expect(getByText('Test Button')).toBeTruthy();
    });
    it('renders with different variants', function () {
      var _render2 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(MockAnimatedButton, Object.assign({}, defaultProps, {
          variant: "primary",
          testID: "button-primary"
        }))),
        getByTestId = _render2.getByTestId;
      expect(getByTestId('button-primary')).toBeTruthy();
    });
  });
  describe('Functionality', function () {
    it('calls onPress when pressed', function () {
      var onPressMock = jest.fn();
      var _render3 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(MockAnimatedButton, Object.assign({}, defaultProps, {
          onPress: onPressMock
        }))),
        getByTestId = _render3.getByTestId;
      _reactNative.fireEvent.press(getByTestId('animated-button'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });
    it('does not call onPress when disabled', function () {
      var onPressMock = jest.fn();
      var _render4 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(MockAnimatedButton, Object.assign({}, defaultProps, {
          onPress: onPressMock,
          disabled: true
        }))),
        getByTestId = _render4.getByTestId;
      _reactNative.fireEvent.press(getByTestId('animated-button'));
      expect(onPressMock).not.toHaveBeenCalled();
    });
  });
  describe('Accessibility', function () {
    it('has proper accessibility properties', function () {
      var _render5 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(MockAnimatedButton, Object.assign({}, defaultProps, {
          accessibilityLabel: "Custom accessibility label"
        }))),
        getByTestId = _render5.getByTestId;
      var button = getByTestId('animated-button');
      expect(button.props.accessibilityRole).toBe('button');
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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