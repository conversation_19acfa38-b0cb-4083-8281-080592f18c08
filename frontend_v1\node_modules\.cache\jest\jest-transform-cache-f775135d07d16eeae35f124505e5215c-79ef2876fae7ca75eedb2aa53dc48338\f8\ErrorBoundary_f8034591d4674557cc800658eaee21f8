846dd7f0eb81cd2725aeeb4010507365
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.ErrorBoundary = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _performanceMonitor = require("../../services/performanceMonitor");
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var ErrorBoundary = exports.ErrorBoundary = function (_Component) {
  function ErrorBoundary(props) {
    var _this;
    (0, _classCallCheck2.default)(this, ErrorBoundary);
    _this = _callSuper(this, ErrorBoundary, [props]);
    _this.resetTimeoutId = null;
    _this.resetErrorBoundary = function () {
      if (_this.resetTimeoutId) {
        clearTimeout(_this.resetTimeoutId);
      }
      _this.setState({
        hasError: false,
        error: null,
        errorInfo: null
      });
    };
    _this.handleRetry = function () {
      _this.resetErrorBoundary();
    };
    _this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
    return _this;
  }
  (0, _inherits2.default)(ErrorBoundary, _Component);
  return (0, _createClass2.default)(ErrorBoundary, [{
    key: "componentDidCatch",
    value: function componentDidCatch(error, errorInfo) {
      this.setState({
        error: error,
        errorInfo: errorInfo
      });
      console.error('🚨 ErrorBoundary: Error caught', {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        errorBoundary: true,
        props: this.props
      });
      try {
        _performanceMonitor.performanceMonitor.trackError(error, {
          component: 'ErrorBoundary',
          errorInfo: errorInfo.componentStack
        });
      } catch (monitorError) {
        console.warn('Failed to report error to performance monitor:', monitorError);
      }
      if (this.props.onError) {
        this.props.onError(error, errorInfo);
      }
      console.error('ErrorBoundary caught an error:', error);
      console.error('Error info:', errorInfo);
    }
  }, {
    key: "componentDidUpdate",
    value: function componentDidUpdate(prevProps) {
      var _this$props = this.props,
        resetKeys = _this$props.resetKeys,
        resetOnPropsChange = _this$props.resetOnPropsChange;
      var hasError = this.state.hasError;
      if (hasError && prevProps.resetKeys !== resetKeys) {
        if (resetKeys) {
          var hasResetKeyChanged = resetKeys.some(function (key, index) {
            var _prevProps$resetKeys;
            return ((_prevProps$resetKeys = prevProps.resetKeys) == null ? void 0 : _prevProps$resetKeys[index]) !== key;
          });
          if (hasResetKeyChanged) {
            this.resetErrorBoundary();
          }
        }
      }
      if (hasError && resetOnPropsChange && prevProps.children !== this.props.children) {
        this.resetErrorBoundary();
      }
    }
  }, {
    key: "render",
    value: function render() {
      var _this$state = this.state,
        hasError = _this$state.hasError,
        error = _this$state.error;
      var _this$props2 = this.props,
        children = _this$props2.children,
        fallback = _this$props2.fallback;
      if (hasError) {
        if (fallback) {
          return fallback;
        }
        return (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.container,
          testID: "error-boundary-container",
          accessibilityRole: "alert",
          children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.content,
            children: [(0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: styles.title,
              children: "Something went wrong"
            }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: styles.message,
              children: (error == null ? void 0 : error.message) || 'An unexpected error occurred'
            }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              style: styles.retryButton,
              onPress: this.handleRetry,
              testID: "error-boundary-retry",
              children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
                style: styles.retryButtonText,
                children: "Try Again"
              })
            })]
          })
        });
      }
      return children;
    }
  }], [{
    key: "getDerivedStateFromError",
    value: function getDerivedStateFromError(error) {
      return {
        hasError: true,
        error: error
      };
    }
  }]);
}(_react.Component);
var styles = _reactNative.StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f8f9fa'
  },
  content: {
    alignItems: 'center',
    maxWidth: 300
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#dc3545',
    marginBottom: 12,
    textAlign: 'center'
  },
  message: {
    fontSize: 16,
    color: '#6c757d',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24
  },
  retryButton: {
    backgroundColor: '#007bff',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8
  },
  retryButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600'
  }
});
var _default = exports.default = ErrorBoundary;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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