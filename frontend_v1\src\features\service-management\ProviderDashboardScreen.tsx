/**
 * Provider Dashboard Screen
 *
 * Allows service providers to manage their services, view performance metrics,
 * and handle booking management.
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import React, { useState, useEffect, useMemo } from 'react';
import {
  ScrollView,
  View,
  RefreshControl,
  Alert,
  StyleSheet,
} from 'react-native';

import { Box } from '../../components/atoms/Box';
import { Button } from '../../components/atoms/Button';
import { Text } from '../../components/atoms/Text';
import { HeaderHelpButton } from '../../components/help';
import { BentoGrid, BentoWidget } from '../../components/ui/BentoGrid';
import {
  DashboardWidget,
  MetricData,
  ListData,
  ActionData,
} from '../../components/ui/DashboardWidget';
import { EnhancedBentoGrid } from '../../components/ui/EnhancedBentoGrid';
import { SafeAreaScreen } from '../../components/ui/SafeAreaWrapper';
import { ALL_SERVICE_PROVIDERS } from '../../config/testAccounts';
import { Colors } from '../../constants/Colors';
import { useTheme } from '../../contexts/ThemeContext';
import type { ProviderStackParamList } from '../../navigation/types';
import { useAuthStore } from '../../store/authSlice';
import { useUserStore } from '../../store/userSlice';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
  getMinimumTouchTarget,
} from '../../utils/responsiveUtils';

// Import test accounts for provider services

type ProviderDashboardNavigationProp =
  StackNavigationProp<ProviderStackParamList>;

interface Service {
  id: string;
  name: string;
  category: string;
  price: number;
  duration: number;
  description: string;
  isActive: boolean;
  bookingsCount: number;
  rating: number;
  reviewsCount: number;
}

interface DashboardMetrics {
  totalServices: number;
  activeServices: number;
  totalBookings: number;
  pendingBookings: number;
  totalRevenue: number;
  averageRating: number;
}

export const ProviderDashboardScreen: React.FC = () => {
  const navigation = useNavigation<ProviderDashboardNavigationProp>();
  const { userRole } = useAuthStore();
  const { profile } = useUserStore();
  const { colors } = useTheme();
  const [services, setServices] = useState<Service[]>([]);
  const [metrics, setMetrics] = useState<DashboardMetrics>({
    totalServices: 0,
    activeServices: 0,
    totalBookings: 0,
    pendingBookings: 0,
    totalRevenue: 0,
    averageRating: 0,
  });
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedWidget, setSelectedWidget] = useState<string | null>(null);

  // Generate services based on current provider's profile
  const generateProviderServices = (): Service[] => {
    if (!profile?.email) return [];

    // Find the current provider in test accounts
    const currentProvider = ALL_SERVICE_PROVIDERS.find(
      provider => provider.email === profile.email,
    );

    // If no matching provider found, use default services for testing
    const category = currentProvider?.category || 'hair';
    const baseServices = getServicesByCategory(category);

    return baseServices.map((service, index) => ({
      id: `service-${index + 1}`,
      name: service.name,
      category: category,
      price: service.price,
      duration: service.duration,
      description: service.description,
      isActive: Math.random() > 0.2, // 80% chance of being active
      bookingsCount: Math.floor(Math.random() * 20) + 5, // 5-25 bookings
      rating: 4.0 + Math.random() * 1.0, // 4.0-5.0 rating
      reviewsCount: Math.floor(Math.random() * 15) + 3, // 3-18 reviews
    }));
  };

  // Get services based on category
  const getServicesByCategory = (category: string) => {
    const serviceTemplates: Record<
      string,
      Array<{
        name: string;
        price: number;
        duration: number;
        description: string;
      }>
    > = {
      barber: [
        {
          name: 'Classic Haircut',
          price: 35,
          duration: 45,
          description: 'Traditional barbershop cut',
        },
        {
          name: 'Beard Trim',
          price: 25,
          duration: 30,
          description: 'Professional beard trimming',
        },
        {
          name: 'Hot Towel Shave',
          price: 40,
          duration: 45,
          description: 'Traditional straight razor shave',
        },
        {
          name: 'Beard Styling',
          price: 30,
          duration: 30,
          description: 'Beard shaping and styling',
        },
      ],
      salon: [
        {
          name: 'Hair Color',
          price: 85,
          duration: 120,
          description: 'Full hair coloring service',
        },
        {
          name: 'Hair Highlights',
          price: 95,
          duration: 150,
          description: 'Professional highlighting service',
        },
        {
          name: 'Blowout',
          price: 65,
          duration: 60,
          description: 'Professional hair blowout and styling',
        },
        {
          name: 'Hair Treatment',
          price: 75,
          duration: 75,
          description: 'Deep conditioning treatment',
        },
      ],
      nails: [
        {
          name: 'Manicure',
          price: 30,
          duration: 45,
          description: 'Complete nail care and polish',
        },
        {
          name: 'Pedicure',
          price: 40,
          duration: 60,
          description: 'Complete foot and nail care',
        },
        {
          name: 'Gel Nails',
          price: 50,
          duration: 75,
          description: 'Long-lasting gel nail application',
        },
        {
          name: 'Nail Art',
          price: 25,
          duration: 30,
          description: 'Custom nail art and design',
        },
      ],
      skincare: [
        {
          name: 'Facial Treatment',
          price: 75,
          duration: 90,
          description: 'Deep cleansing facial treatment',
        },
        {
          name: 'Chemical Peel',
          price: 120,
          duration: 60,
          description: 'Professional chemical peel service',
        },
        {
          name: 'Microdermabrasion',
          price: 90,
          duration: 75,
          description: 'Skin resurfacing treatment',
        },
        {
          name: 'Anti-Aging Treatment',
          price: 150,
          duration: 120,
          description: 'Advanced anti-aging facial',
        },
      ],
      massage: [
        {
          name: 'Swedish Massage',
          price: 80,
          duration: 60,
          description: 'Relaxing full-body massage',
        },
        {
          name: 'Deep Tissue Massage',
          price: 95,
          duration: 75,
          description: 'Therapeutic deep tissue work',
        },
        {
          name: 'Hot Stone Massage',
          price: 110,
          duration: 90,
          description: 'Relaxing hot stone therapy',
        },
        {
          name: 'Aromatherapy Massage',
          price: 85,
          duration: 60,
          description: 'Essential oil massage therapy',
        },
      ],
      general: [
        {
          name: 'Consultation',
          price: 25,
          duration: 30,
          description: 'Initial consultation service',
        },
        {
          name: 'Basic Service',
          price: 50,
          duration: 60,
          description: 'Standard beauty service',
        },
        {
          name: 'Premium Service',
          price: 100,
          duration: 90,
          description: 'Premium beauty treatment',
        },
      ],
    };

    return serviceTemplates[category] || serviceTemplates.general;
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Import the provider API service
      const { providerApiService } = await import('../../services/providerApi');

      // Fetch real dashboard data from backend
      const [dashboardData, servicesResponse] = await Promise.all([
        providerApiService.getDashboard(),
        providerApiService.getServices(),
      ]);

      // Transform backend services to frontend format
      const transformedServices = servicesResponse.services.map(service => ({
        id: service.id,
        name: service.name,
        description: service.description,
        price: service.base_price,
        duration: service.duration,
        category: service.category || 'General',
        isActive: service.is_active,
        bookingsCount: 0, // Will be populated from analytics
        revenue: 0, // Will be populated from analytics
      }));

      setServices(transformedServices);

      // Use real metrics from backend
      const activeServices = dashboardData.active_services;
      const totalBookings = dashboardData.total_bookings;
      const totalRevenue = dashboardData.monthly_revenue;
      const averageRating =
        providerServices.length > 0
          ? providerServices.reduce((sum, s) => sum + s.rating, 0) /
            providerServices.length
          : 0;

      const calculatedMetrics: DashboardMetrics = {
        totalServices: providerServices.length,
        activeServices,
        totalBookings,
        pendingBookings: Math.floor(totalBookings * 0.1), // 10% pending
        totalRevenue,
        averageRating,
      };

      setMetrics(calculatedMetrics);
    } catch (error) {
      Alert.alert('Error', 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  // Create Bento Grid widgets from dashboard data
  const dashboardWidgets = useMemo((): BentoWidget[] => {
    const revenueData: MetricData = {
      value: `$${metrics.totalRevenue.toFixed(2)}`,
      label: 'Total Revenue',
      change: {
        value: 12.5,
        type: 'increase',
      },
    };

    const bookingsData: MetricData = {
      value: metrics.totalBookings,
      label: 'Total Bookings',
      change: {
        value: 8.3,
        type: 'increase',
      },
    };

    const servicesData: MetricData = {
      value: `${metrics.activeServices}/${metrics.totalServices}`,
      label: 'Active Services',
      change: {
        value:
          metrics.activeServices > metrics.totalServices * 0.8 ? 5.2 : -2.1,
        type:
          metrics.activeServices > metrics.totalServices * 0.8
            ? 'increase'
            : 'decrease',
      },
    };

    const ratingData: MetricData = {
      value: metrics.averageRating ? metrics.averageRating.toFixed(1) : '0.0',
      label: 'Average Rating',
      change: {
        value: 0.3,
        type: 'increase',
      },
    };

    const recentServicesData: ListData = {
      items: services.slice(0, 5).map(service => ({
        id: service.id,
        title: service.name,
        subtitle: service.category,
        value: `$${service.price}`,
        status: service.isActive ? 'active' : 'inactive',
        contextActions: [
          {
            id: 'edit',
            label: 'Edit Service',
            onPress: () => handleEditService(service.id),
          },
          {
            id: 'toggle',
            label: service.isActive ? 'Deactivate' : 'Activate',
            onPress: () =>
              handleToggleServiceStatus(service.id, service.isActive),
          },
          {
            id: 'analytics',
            label: 'View Analytics',
            onPress: () =>
              Alert.alert(
                'Analytics',
                `Analytics for ${service.name} coming soon!`,
              ),
          },
          {
            id: 'duplicate',
            label: 'Duplicate',
            onPress: () => {
              Alert.alert(
                'Duplicate Service',
                `Create a copy of ${service.name}?`,
                [
                  { text: 'Cancel', style: 'cancel' },
                  {
                    text: 'Duplicate',
                    onPress: () => {
                      // Handle service duplication
                      console.log('Duplicating service:', service.name);
                    },
                  },
                ],
              );
            },
          },
        ],
      })),
      onItemSelect: (item: any) => {
        // Navigate to service details when item is selected
        handleEditService(item.id);
      },
      onItemLongPress: (item: any) => {
        // Show service options on long press
        Alert.alert(item.title, 'Choose an action', [
          { text: 'Edit', onPress: () => handleEditService(item.id) },
          {
            text: 'View Analytics',
            onPress: () =>
              Alert.alert(
                'Analytics',
                `Analytics for ${item.title} coming soon!`,
              ),
          },
          { text: 'Cancel', style: 'cancel' },
        ]);
      },
    };

    const quickActionsData: ActionData = {
      primaryAction: {
        label: 'Add New Service',
        onPress: () =>
          navigation.navigate(
            'ServiceEditor' as never,
            { mode: 'create' } as never,
          ),
      },
      secondaryAction: {
        label: 'View Analytics',
        onPress: () => navigation.navigate('ProviderAnalytics' as never),
      },
    };

    const customerManagementData: ActionData = {
      primaryAction: {
        label: 'Manage Customers',
        onPress: () => navigation.navigate('CustomerManagement' as never),
      },
      secondaryAction: {
        label: 'Customer Analytics',
        onPress: () => navigation.navigate('ProviderAnalytics' as never),
      },
    };

    return [
      {
        id: 'revenue',
        title: 'Revenue',
        size: 'medium',
        component: DashboardWidget,
        data: revenueData,
        priority: 10,
        accessibilityLabel: 'Revenue metrics widget',
        accessibilityHint: 'Shows total revenue and growth percentage',
      },
      {
        id: 'bookings',
        title: 'Bookings',
        size: 'medium',
        component: DashboardWidget,
        data: bookingsData,
        priority: 9,
        accessibilityLabel: 'Bookings metrics widget',
        accessibilityHint: 'Shows total bookings and growth percentage',
      },
      {
        id: 'services',
        title: 'Services',
        size: 'small',
        component: DashboardWidget,
        data: servicesData,
        priority: 8,
        accessibilityLabel: 'Services metrics widget',
        accessibilityHint: 'Shows active services count and status',
      },
      {
        id: 'rating',
        title: 'Rating',
        size: 'small',
        component: DashboardWidget,
        data: ratingData,
        priority: 7,
        accessibilityLabel: 'Rating metrics widget',
        accessibilityHint: 'Shows average service rating',
      },
      {
        id: 'recent-services',
        title: 'Recent Services',
        size: 'wide',
        component: DashboardWidget,
        data: recentServicesData,
        priority: 6,
        accessibilityLabel: 'Recent services list widget',
        accessibilityHint: 'Shows list of recently added services',
      },
      {
        id: 'quick-actions',
        title: 'Quick Actions',
        size: 'medium',
        component: DashboardWidget,
        data: quickActionsData,
        priority: 5,
        accessibilityLabel: 'Quick actions widget',
        accessibilityHint: 'Provides quick access to common actions',
      },
      {
        id: 'customer-management',
        title: 'Customer Management',
        size: 'medium',
        component: DashboardWidget,
        data: customerManagementData,
        priority: 4,
        accessibilityLabel: 'Customer management widget',
        accessibilityHint: 'Provides access to customer management features',
      },
    ].map(widget => ({
      ...widget,
      component: (props: any) => (
        <DashboardWidget
          {...props}
          type={
            widget.id === 'recent-services'
              ? 'list'
              : widget.id === 'quick-actions'
                ? 'action'
                : 'metric'
          }
          loading={loading}
        />
      ),
    }));
  }, [metrics, services, loading, navigation]);

  // Handle widget interactions
  const handleWidgetPress = (widget: BentoWidget) => {
    setSelectedWidget(widget.id);

    // Handle specific widget actions
    switch (widget.id) {
      case 'revenue':
        navigation.navigate('ProviderAnalytics' as never);
        break;
      case 'bookings':
        // Navigate to provider bookings management
        navigation.navigate('ProviderTabs', { screen: 'Bookings' });
        break;
      case 'services':
        // Navigate to service editor
        navigation.navigate('ServiceEditor', { mode: 'create' });
        break;
      case 'rating':
        Alert.alert('Rating Details', 'Detailed rating analytics coming soon!');
        break;
      case 'customer-management':
        navigation.navigate('CustomerManagement' as never);
        break;
      default:
        break;
    }
  };

  const handleCreateService = () => {
    navigation.navigate('ServiceEditor', { mode: 'create' });
  };

  const handleEditService = (serviceId: string) => {
    navigation.navigate('ServiceEditor', { mode: 'edit', serviceId });
  };

  const handleToggleServiceStatus = async (
    serviceId: string,
    isActive: boolean,
  ) => {
    try {
      // TODO: Implement API call to toggle service status
      setServices(prev =>
        prev.map(service =>
          service.id === serviceId
            ? { ...service, isActive: !isActive }
            : service,
        ),
      );
      Alert.alert(
        'Success',
        `Service ${!isActive ? 'activated' : 'deactivated'} successfully`,
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to update service status');
    }
  };

  const styles = createStyles(colors);

  if (loading) {
    return (
      <SafeAreaScreen style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading dashboard...</Text>
        </View>
      </SafeAreaScreen>
    );
  }

  return (
    <SafeAreaScreen style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.headerTitle}>Provider Dashboard</Text>
          <Text style={styles.headerSubtitle}>
            Welcome back, {profile?.first_name || 'Business Owner'}!
          </Text>
        </View>
        <View style={styles.headerRight}>
          <HeaderHelpButton
            size="medium"
            testID="provider-dashboard-help-button"
          />
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}>
        {/* Enhanced Bento Grid Dashboard */}
        <EnhancedBentoGrid
          widgets={dashboardWidgets}
          onWidgetPress={handleWidgetPress}
          selectedWidgetId={selectedWidget}
          config={{
            columns: 2,
            spacing: 24,
            minWidgetHeight: 160,
            maxWidgetHeight: 320,
            enableAnimations: true,
            enableLazyLoading: true,
            priorityThreshold: 8, // Higher threshold for provider dashboard
          }}
          testID="provider-dashboard-grid"
        />
      </ScrollView>
    </SafeAreaScreen>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor:
        colors.background || colors.background?.primary || '#FFFFFF',
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      fontSize: getResponsiveFontSize(16),
      color: colors.text?.primary || '#000000',
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: getResponsiveSpacing(20),
      paddingVertical: getResponsiveSpacing(16),
      backgroundColor:
        colors.surface || colors.background?.primary || '#FFFFFF',
      borderBottomWidth: 1,
      borderBottomColor: colors.sage100 || colors.border?.light || '#E5E5E5',
    },
    headerLeft: {
      flex: 1,
    },
    headerTitle: {
      fontSize: getResponsiveFontSize(24),
      fontWeight: '700',
      color: colors.text || colors.text?.primary || '#000000',
      marginBottom: 4,
    },
    headerSubtitle: {
      fontSize: getResponsiveFontSize(14),
      color: colors.sage400 || colors.text?.secondary || '#666666',
    },
    headerRight: {
      marginLeft: getResponsiveSpacing(16),
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      padding: getResponsiveSpacing(20),
      paddingBottom: getResponsiveSpacing(100), // Account for bottom navigation
    },
  });
