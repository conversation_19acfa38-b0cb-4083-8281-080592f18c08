"""
Django Management Command: Populate Mock Messaging Data

This command creates mock conversations and messages in the database
to replace the frontend mock data and enable full backend integration.

Usage:
    python manage.py populate_mock_messaging_data [--clear]
"""

from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
import random

from apps.messaging.models import Conversation, Message, MessageAttachment
from apps.bookings.models import BookingNotification

User = get_user_model()


class Command(BaseCommand):
    help = 'Populate database with mock messaging data for development and testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing messaging data before populating',
        )
        parser.add_argument(
            '--users-only',
            action='store_true',
            help='Only create test users, skip messaging data',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 Starting mock messaging data population...'))

        if options['clear']:
            self.clear_existing_data()

        # Create test users
        users = self.create_test_users()
        
        if options['users_only']:
            self.stdout.write(self.style.SUCCESS('✅ Test users created successfully!'))
            return

        # Create conversations and messages
        conversations = self.create_conversations(users)
        self.create_messages(conversations, users)
        # Skip notifications for now as they require bookings
        # self.create_notifications(users)

        self.stdout.write(self.style.SUCCESS('✅ Mock messaging data populated successfully!'))

    def clear_existing_data(self):
        """Clear existing messaging data"""
        self.stdout.write('🧹 Clearing existing messaging data...')
        
        Message.objects.all().delete()
        Conversation.objects.all().delete()
        
        # Clear test users (be careful not to delete real users)
        test_emails = [
            '<EMAIL>', '<EMAIL>', '<EMAIL>',
            '<EMAIL>', '<EMAIL>', '<EMAIL>'
        ]
        test_usernames = [
            'customer1', 'customer2', 'customer3',
            'provider1', 'provider2', 'provider3'
        ]
        User.objects.filter(email__in=test_emails).delete()
        User.objects.filter(username__in=test_usernames).delete()
        
        self.stdout.write(self.style.WARNING('   Cleared existing data'))

    def create_test_users(self):
        """Create test users for messaging"""
        self.stdout.write('👥 Creating test users...')
        
        users = {}
        
        # Create customers
        customers_data = [
            {'email': '<EMAIL>', 'username': 'customer1', 'first_name': 'Sarah', 'last_name': 'Johnson', 'role': 'customer'},
            {'email': '<EMAIL>', 'username': 'customer2', 'first_name': 'Mike', 'last_name': 'Chen', 'role': 'customer'},
            {'email': '<EMAIL>', 'username': 'customer3', 'first_name': 'Emma', 'last_name': 'Davis', 'role': 'customer'},
        ]

        # Create providers
        providers_data = [
            {'email': '<EMAIL>', 'username': 'provider1', 'first_name': 'Elite', 'last_name': 'Beauty Salon', 'role': 'provider'},
            {'email': '<EMAIL>', 'username': 'provider2', 'first_name': 'John', 'last_name': 'Smith', 'role': 'provider'},
            {'email': '<EMAIL>', 'username': 'provider3', 'first_name': 'Maria', 'last_name': 'Garcia', 'role': 'provider'},
        ]
        
        all_users_data = customers_data + providers_data
        
        for user_data in all_users_data:
            user, created = User.objects.get_or_create(
                email=user_data['email'],
                defaults={
                    'username': user_data['username'],
                    'first_name': user_data['first_name'],
                    'last_name': user_data['last_name'],
                    'role': user_data['role'],
                    'is_active': True,
                }
            )
            if created:
                user.set_password('testpass123')
                user.save()
                self.stdout.write(f'   Created user: {user.email}')
            else:
                self.stdout.write(f'   User exists: {user.email}')
            
            users[user_data['role'] + str(len([u for u in users.values() if u.role == user_data['role']]) + 1)] = user
        
        return users

    def create_conversations(self, users):
        """Create mock conversations"""
        self.stdout.write('💬 Creating conversations...')
        
        conversations = []
        
        # Conversation 1: Customer1 <-> Provider1
        conv1, created = Conversation.objects.get_or_create(
            title="Appointment Discussion",
            defaults={
                'conversation_type': 'booking',
                'is_active': True,
                'created_at': timezone.now() - timedelta(days=2),
                'updated_at': timezone.now() - timedelta(minutes=30)
            }
        )
        if created:
            conv1.participants.add(users['customer1'], users['provider1'])
            self.stdout.write(f'   Created conversation: {conv1.title}')
        conversations.append(conv1)
        
        # Conversation 2: Customer2 <-> Provider2  
        conv2, created = Conversation.objects.get_or_create(
            title="Service Inquiry",
            defaults={
                'conversation_type': 'general',
                'is_active': True,
                'created_at': timezone.now() - timedelta(hours=2),
                'updated_at': timezone.now() - timedelta(hours=1)
            }
        )
        if created:
            conv2.participants.add(users['customer2'], users['provider2'])
            self.stdout.write(f'   Created conversation: {conv2.title}')
        conversations.append(conv2)
        
        # Conversation 3: Customer3 <-> Provider3
        conv3, created = Conversation.objects.get_or_create(
            title="Follow-up Questions",
            defaults={
                'conversation_type': 'support',
                'is_active': True,
                'created_at': timezone.now() - timedelta(days=1),
                'updated_at': timezone.now() - timedelta(hours=3)
            }
        )
        if created:
            conv3.participants.add(users['customer3'], users['provider3'])
            self.stdout.write(f'   Created conversation: {conv3.title}')
        conversations.append(conv3)
        
        return conversations

    def create_messages(self, conversations, users):
        """Create mock messages"""
        self.stdout.write('📝 Creating messages...')
        
        # Messages for Conversation 1
        conv1_messages = [
            {
                'content': 'Hi! I would like to book an appointment for next week.',
                'sender': users['customer1'],
                'timestamp': timezone.now() - timedelta(hours=2),
                'message_type': 'text'
            },
            {
                'content': 'Hello! I have availability on Tuesday and Wednesday. What time works best for you?',
                'sender': users['provider1'],
                'timestamp': timezone.now() - timedelta(hours=1, minutes=45),
                'message_type': 'text'
            },
            {
                'content': 'Tuesday at 2 PM would be perfect!',
                'sender': users['customer1'],
                'timestamp': timezone.now() - timedelta(hours=1, minutes=30),
                'message_type': 'text'
            },
            {
                'content': 'Great! I\'ve booked you for Tuesday at 2 PM. Looking forward to seeing you!',
                'sender': users['provider1'],
                'timestamp': timezone.now() - timedelta(minutes=30),
                'message_type': 'text'
            }
        ]
        
        for msg_data in conv1_messages:
            Message.objects.get_or_create(
                conversation=conversations[0],
                sender=msg_data['sender'],
                content=msg_data['content'],
                defaults={
                    'message_type': msg_data['message_type'],
                    'created_at': msg_data['timestamp'],
                    'updated_at': msg_data['timestamp']
                }
            )
        
        # Messages for Conversation 2
        conv2_messages = [
            {
                'content': 'When can we schedule the next appointment?',
                'sender': users['customer2'],
                'timestamp': timezone.now() - timedelta(hours=2),
                'message_type': 'text'
            },
            {
                'content': 'I have openings this Friday afternoon or next Monday morning. Which works better?',
                'sender': users['provider2'],
                'timestamp': timezone.now() - timedelta(hours=1),
                'message_type': 'text'
            }
        ]
        
        for msg_data in conv2_messages:
            Message.objects.get_or_create(
                conversation=conversations[1],
                sender=msg_data['sender'],
                content=msg_data['content'],
                defaults={
                    'message_type': msg_data['message_type'],
                    'created_at': msg_data['timestamp'],
                    'updated_at': msg_data['timestamp']
                }
            )
        
        # Messages for Conversation 3
        conv3_messages = [
            {
                'content': 'Thank you for the great service!',
                'sender': users['customer3'],
                'timestamp': timezone.now() - timedelta(hours=4),
                'message_type': 'text'
            },
            {
                'content': 'You\'re very welcome! It was a pleasure working with you.',
                'sender': users['provider3'],
                'timestamp': timezone.now() - timedelta(hours=3, minutes=30),
                'message_type': 'text'
            },
            {
                'content': 'Perfect! See you tomorrow.',
                'sender': users['customer3'],
                'timestamp': timezone.now() - timedelta(hours=3),
                'message_type': 'text'
            }
        ]
        
        for msg_data in conv3_messages:
            Message.objects.get_or_create(
                conversation=conversations[2],
                sender=msg_data['sender'],
                content=msg_data['content'],
                defaults={
                    'message_type': msg_data['message_type'],
                    'created_at': msg_data['timestamp'],
                    'updated_at': msg_data['timestamp']
                }
            )
        
        self.stdout.write(f'   Created messages for {len(conversations)} conversations')

    def create_notifications(self, users):
        """Create mock notifications"""
        self.stdout.write('🔔 Creating notifications...')
        
        # Sample notifications
        notifications_data = [
            {
                'recipient': users['customer1'],
                'title': 'New Message',
                'message': 'You have a new message from Elite Beauty Salon',
                'notification_type': 'message',
                'channel': 'push',
                'status': 'delivered'
            },
            {
                'recipient': users['customer2'],
                'title': 'Appointment Reminder',
                'message': 'Your appointment is tomorrow at 2 PM',
                'notification_type': 'reminder',
                'channel': 'push',
                'status': 'sent'
            },
            {
                'recipient': users['provider1'],
                'title': 'Booking Confirmed',
                'message': 'Sarah Johnson has confirmed the appointment',
                'notification_type': 'booking',
                'channel': 'push',
                'status': 'read'
            }
        ]
        
        for notif_data in notifications_data:
            BookingNotification.objects.get_or_create(
                recipient=notif_data['recipient'],
                title=notif_data['title'],
                defaults={
                    'message': notif_data['message'],
                    'notification_type': notif_data['notification_type'],
                    'channel': notif_data['channel'],
                    'status': notif_data['status'],
                    'created_at': timezone.now() - timedelta(hours=random.randint(1, 24))
                }
            )
        
        self.stdout.write(f'   Created {len(notifications_data)} notifications')
