{"version": 3, "names": ["_ExceptionsManager", "_interopRequireWildcard", "require", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "getExtendedError", "errorValue", "errorInfo", "error", "Error", "SyntheticError", "componentStack", "isComponentError", "_unused", "onUncaughtError", "ExceptionsManager", "handleException", "onCaughtError", "onRecoverableError", "console", "warn"], "sources": ["ErrorHandlers.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict\n */\n\nimport type {ExtendedError} from '../../../../Libraries/Core/ExtendedError';\nimport type {Component as ReactComponent} from 'react';\n\nimport ExceptionsManager, {\n  SyntheticError,\n} from '../../../../Libraries/Core/ExceptionsManager';\n\ntype ErrorInfo = {\n  +componentStack?: ?string,\n  // $FlowFixMe[unclear-type] unknown props and state.\n  +errorBoundary?: ?ReactComponent<any, any>,\n};\n\nfunction getExtendedError(\n  errorValue: mixed,\n  errorInfo: ErrorInfo,\n): ExtendedError {\n  let error;\n\n  // Typically, `errorValue` should be an error. However, other values such as\n  // strings (or even null) are sometimes thrown.\n  if (errorValue instanceof Error) {\n    /* $FlowFixMe[class-object-subtyping] added when improving typing for\n     * this parameters */\n    // $FlowFixMe[incompatible-cast]\n    error = (errorValue: ExtendedError);\n  } else if (typeof errorValue === 'string') {\n    /* $FlowFixMe[class-object-subtyping] added when improving typing for\n     * this parameters */\n    // $FlowFixMe[incompatible-cast]\n    error = (new SyntheticError(errorValue): ExtendedError);\n  } else {\n    /* $FlowFixMe[class-object-subtyping] added when improving typing for\n     * this parameters */\n    // $FlowFixMe[incompatible-cast]\n    error = (new SyntheticError('Unspecified error'): ExtendedError);\n  }\n  try {\n    // $FlowFixMe[incompatible-use] this is in try/catch.\n    error.componentStack = errorInfo.componentStack;\n    error.isComponentError = true;\n  } catch {\n    // Ignored.\n  }\n\n  return error;\n}\n\nexport function onUncaughtError(errorValue: mixed, errorInfo: ErrorInfo): void {\n  const error = getExtendedError(errorValue, errorInfo);\n\n  // Uncaught errors are fatal.\n  ExceptionsManager.handleException(error, true);\n}\n\nexport function onCaughtError(errorValue: mixed, errorInfo: ErrorInfo): void {\n  const error = getExtendedError(errorValue, errorInfo);\n\n  // Caught errors are not fatal.\n  ExceptionsManager.handleException(error, false);\n}\n\nexport function onRecoverableError(\n  errorValue: mixed,\n  errorInfo: ErrorInfo,\n): void {\n  const error = getExtendedError(errorValue, errorInfo);\n\n  // Recoverable errors should only be warnings.\n  // This will make it a soft error in LogBox.\n  // TODO: improve the logging for recoverable errors in prod.\n  console.warn(error);\n}\n"], "mappings": ";;;;;;AAaA,IAAAA,kBAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEsD,SAAAD,wBAAAE,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAJ,uBAAA,YAAAA,wBAAAE,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAQtD,SAASmB,gBAAgBA,CACvBC,UAAiB,EACjBC,SAAoB,EACL;EACf,IAAIC,KAAK;EAIT,IAAIF,UAAU,YAAYG,KAAK,EAAE;IAI/BD,KAAK,GAAIF,UAA0B;EACrC,CAAC,MAAM,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;IAIzCE,KAAK,GAAI,IAAIE,iCAAc,CAACJ,UAAU,CAAiB;EACzD,CAAC,MAAM;IAILE,KAAK,GAAI,IAAIE,iCAAc,CAAC,mBAAmB,CAAiB;EAClE;EACA,IAAI;IAEFF,KAAK,CAACG,cAAc,GAAGJ,SAAS,CAACI,cAAc;IAC/CH,KAAK,CAACI,gBAAgB,GAAG,IAAI;EAC/B,CAAC,CAAC,OAAAC,OAAA,EAAM,CAER;EAEA,OAAOL,KAAK;AACd;AAEO,SAASM,eAAeA,CAACR,UAAiB,EAAEC,SAAoB,EAAQ;EAC7E,IAAMC,KAAK,GAAGH,gBAAgB,CAACC,UAAU,EAAEC,SAAS,CAAC;EAGrDQ,0BAAiB,CAACC,eAAe,CAACR,KAAK,EAAE,IAAI,CAAC;AAChD;AAEO,SAASS,aAAaA,CAACX,UAAiB,EAAEC,SAAoB,EAAQ;EAC3E,IAAMC,KAAK,GAAGH,gBAAgB,CAACC,UAAU,EAAEC,SAAS,CAAC;EAGrDQ,0BAAiB,CAACC,eAAe,CAACR,KAAK,EAAE,KAAK,CAAC;AACjD;AAEO,SAASU,kBAAkBA,CAChCZ,UAAiB,EACjBC,SAAoB,EACd;EACN,IAAMC,KAAK,GAAGH,gBAAgB,CAACC,UAAU,EAAEC,SAAS,CAAC;EAKrDY,OAAO,CAACC,IAAI,CAACZ,KAAK,CAAC;AACrB", "ignoreList": []}