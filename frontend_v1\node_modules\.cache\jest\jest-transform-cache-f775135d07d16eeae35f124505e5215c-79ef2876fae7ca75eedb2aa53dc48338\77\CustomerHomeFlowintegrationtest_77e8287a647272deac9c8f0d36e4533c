52e71e847ecb7d2288f52fa954e9b485
_getJestObj().mock("../../services/customerService", function () {
  return {
    __esModule: true,
    default: {
      getServiceCategories: jest.fn(),
      getFeaturedProviders: jest.fn(),
      getNearbyProviders: jest.fn(),
      getFavoriteProviders: jest.fn(),
      getRecentBookings: jest.fn(),
      getCustomerDashboard: jest.fn()
    }
  };
});
_getJestObj().mock("../../services/performanceMonitor", function () {
  return {
    performanceMonitor: {
      startMonitoring: jest.fn(),
      trackRender: jest.fn(),
      trackUserInteraction: jest.fn(),
      trackNetworkRequest: jest.fn()
    }
  };
});
_getJestObj().mock("../../store/authSlice");
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _native = require("@react-navigation/native");
var _reactNative = require("@testing-library/react-native");
var _react = _interopRequireDefault(require("react"));
var _ThemeContext = require("../../contexts/ThemeContext");
var _CustomerHomeScreen = require("../../screens/CustomerHomeScreen");
var _cacheService = _interopRequireDefault(require("../../services/cacheService"));
var _customerService = _interopRequireDefault(require("../../services/customerService"));
var _performanceMonitor = require("../../services/performanceMonitor");
var _jsxRuntime = require("react/jsx-runtime");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var mockCustomerService = _customerService.default;
var mockPerformanceMonitor = _performanceMonitor.performanceMonitor;
var IntegrationTestWrapper = function IntegrationTestWrapper(_ref) {
  var children = _ref.children;
  return (0, _jsxRuntime.jsx)(_native.NavigationContainer, {
    children: (0, _jsxRuntime.jsx)(_ThemeContext.ThemeProvider, {
      children: children
    })
  });
};
var mockCategories = [{
  id: 1,
  name: 'Hair',
  slug: 'hair',
  icon: 'cut',
  color: '#FF6B6B'
}, {
  id: 2,
  name: 'Nails',
  slug: 'nails',
  icon: 'hand',
  color: '#4ECDC4'
}, {
  id: 3,
  name: 'Skincare',
  slug: 'skincare',
  icon: 'face',
  color: '#45B7D1'
}];
var mockProviders = [{
  id: 1,
  name: 'Beauty Studio',
  rating: 4.8,
  reviewCount: 150,
  imageUrl: 'https://example.com/provider1.jpg',
  services: ['Hair', 'Makeup'],
  distance: 2.5
}, {
  id: 2,
  name: 'Spa Wellness',
  rating: 4.6,
  reviewCount: 89,
  imageUrl: 'https://example.com/provider2.jpg',
  services: ['Skincare', 'Massage'],
  distance: 1.8
}];
describe('Customer Home Flow Integration', function () {
  beforeEach(function () {
    jest.clearAllMocks();
    _cacheService.default.clear();
    mockCustomerService.getServiceCategories.mockResolvedValue(mockCategories);
    mockCustomerService.getFeaturedProviders.mockResolvedValue(mockProviders);
    mockCustomerService.getNearbyProviders.mockResolvedValue([]);
    mockCustomerService.getFavoriteProviders.mockResolvedValue([]);
    mockCustomerService.getRecentBookings.mockResolvedValue([]);
    mockCustomerService.getCustomerDashboard.mockResolvedValue(null);
    mockPerformanceMonitor.startMonitoring.mockImplementation(function () {});
    mockPerformanceMonitor.trackRender.mockImplementation(function () {});
    mockPerformanceMonitor.trackUserInteraction.mockImplementation(function () {});
    mockPerformanceMonitor.trackNetworkRequest.mockImplementation(function () {});
  });
  describe('Complete User Journey', function () {
    it('loads home screen and displays all data correctly', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(IntegrationTestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Browse Services')).toBeTruthy();
      });
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Hair')).toBeTruthy();
        expect(_reactNative.screen.getByText('Nails')).toBeTruthy();
        expect(_reactNative.screen.getByText('Skincare')).toBeTruthy();
      });
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Beauty Studio')).toBeTruthy();
        expect(_reactNative.screen.getByText('Spa Wellness')).toBeTruthy();
      });
      expect(mockCustomerService.getServiceCategories).toHaveBeenCalled();
      expect(mockCustomerService.getFeaturedProviders).toHaveBeenCalled();
    }));
    it('handles category selection flow', (0, _asyncToGenerator2.default)(function* () {
      var mockNavigate = jest.fn();
      jest.doMock("../../hooks/useNavigationGuard", function () {
        return {
          useNavigationGuard: function useNavigationGuard() {
            return {
              navigate: mockNavigate,
              canNavigate: function canNavigate() {
                return true;
              }
            };
          }
        };
      });
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(IntegrationTestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Hair')).toBeTruthy();
      });
      _reactNative.fireEvent.press(_reactNative.screen.getByText('Hair'));
      yield (0, _reactNative.waitFor)(function () {
        expect(mockNavigate).toHaveBeenCalledWith('Search', {
          category: 'hair'
        });
      });
      expect(mockPerformanceMonitor.trackUserInteraction).toHaveBeenCalledWith('category_press', expect.any(Number), expect.any(Object));
    }));
    it('handles provider selection flow', (0, _asyncToGenerator2.default)(function* () {
      var mockNavigate = jest.fn();
      jest.doMock("../../hooks/useNavigationGuard", function () {
        return {
          useNavigationGuard: function useNavigationGuard() {
            return {
              navigate: mockNavigate,
              canNavigate: function canNavigate() {
                return true;
              }
            };
          }
        };
      });
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(IntegrationTestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Beauty Studio')).toBeTruthy();
      });
      _reactNative.fireEvent.press(_reactNative.screen.getByText('Beauty Studio'));
      yield (0, _reactNative.waitFor)(function () {
        expect(mockNavigate).toHaveBeenCalledWith('ProviderDetails', {
          providerId: 1
        });
      });
    }));
  });
  describe('Error Handling Integration', function () {
    it('handles service failures gracefully', (0, _asyncToGenerator2.default)(function* () {
      mockCustomerService.getServiceCategories.mockRejectedValue(new Error('Network error'));
      mockCustomerService.getFeaturedProviders.mockRejectedValue(new Error('Server error'));
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(IntegrationTestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByTestId('customer-home-screen')).toBeTruthy();
      });
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByTestId('categories-error')).toBeTruthy();
        expect(_reactNative.screen.getByTestId('featured-providers-error')).toBeTruthy();
      });
    }));
    it('handles partial service failures', (0, _asyncToGenerator2.default)(function* () {
      mockCustomerService.getServiceCategories.mockRejectedValue(new Error('Categories failed'));
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(IntegrationTestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByTestId('categories-error')).toBeTruthy();
      });
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Beauty Studio')).toBeTruthy();
      });
    }));
    it('handles retry functionality', (0, _asyncToGenerator2.default)(function* () {
      var callCount = 0;
      mockCustomerService.getServiceCategories.mockImplementation(function () {
        callCount++;
        if (callCount === 1) {
          return Promise.reject(new Error('First call fails'));
        }
        return Promise.resolve(mockCategories);
      });
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(IntegrationTestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByTestId('categories-error')).toBeTruthy();
      });
      var retryButton = _reactNative.screen.getByTestId('categories-retry-button');
      _reactNative.fireEvent.press(retryButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Barber')).toBeTruthy();
      });
      expect(callCount).toBe(2);
    }));
  });
  describe('Performance Integration', function () {
    it('tracks performance metrics during normal flow', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(IntegrationTestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Browse Services')).toBeTruthy();
      });
      expect(mockPerformanceMonitor.trackRender).toHaveBeenCalledWith('CustomerHomeScreen', expect.any(Number), expect.any(Object));
      expect(mockPerformanceMonitor.trackNetworkRequest).toHaveBeenCalledWith('/api/v1/catalog/categories/', 'GET', expect.any(Number), 200, 0, 0, expect.any(Boolean));
    }));
    it('handles performance under load', (0, _asyncToGenerator2.default)(function* () {
      mockCustomerService.getServiceCategories.mockImplementation(function () {
        return new Promise(function (resolve) {
          return setTimeout(function () {
            return resolve(mockCategories);
          }, 2000);
        });
      });
      var startTime = Date.now();
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(IntegrationTestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      expect(_reactNative.screen.getByTestId('categories-loading')).toBeTruthy();
      (0, _reactNative.act)(function () {
        jest.advanceTimersByTime(2000);
      });
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Hair')).toBeTruthy();
      });
      var endTime = Date.now();
      var loadTime = endTime - startTime;
      expect(mockPerformanceMonitor.trackNetworkRequest).toHaveBeenCalledWith(expect.any(String), 'GET', expect.any(Number), 200, 0, 0, false);
    }));
  });
  describe('Cache Integration', function () {
    it('uses cached data when available', (0, _asyncToGenerator2.default)(function* () {
      yield _cacheService.default.set('customer_home_categories', mockCategories);
      yield _cacheService.default.set('customer_home_featured_providers', mockProviders);
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(IntegrationTestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Hair')).toBeTruthy();
        expect(_reactNative.screen.getByText('Beauty Studio')).toBeTruthy();
      });
      expect(mockPerformanceMonitor.trackNetworkRequest).toHaveBeenCalledWith(expect.any(String), 'GET', expect.any(Number), 200, 0, 0, true);
    }));
    it('falls back to API when cache is expired', (0, _asyncToGenerator2.default)(function* () {
      yield _cacheService.default.set('customer_home_categories', mockCategories, 1);
      yield new Promise(function (resolve) {
        return setTimeout(resolve, 10);
      });
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(IntegrationTestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Hair')).toBeTruthy();
      });
      expect(mockCustomerService.getServiceCategories).toHaveBeenCalled();
    }));
    it('updates cache after successful API calls', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(IntegrationTestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Hair')).toBeTruthy();
      });
      var cachedCategories = yield _cacheService.default.get('customer_home_categories');
      expect(cachedCategories).toEqual(mockCategories);
      var cachedProviders = yield _cacheService.default.get('customer_home_featured_providers');
      expect(cachedProviders).toEqual(mockProviders);
    }));
  });
  describe('Refresh Integration', function () {
    it('handles pull-to-refresh correctly', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(IntegrationTestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Barber')).toBeTruthy();
      });
      jest.clearAllMocks();
      var scrollView = _reactNative.screen.getByTestId('home-scroll-view');
      (0, _reactNative.fireEvent)(scrollView, 'refresh');
      yield (0, _reactNative.waitFor)(function () {
        expect(mockCustomerService.getServiceCategories).toHaveBeenCalled();
        expect(mockCustomerService.getFeaturedProviders).toHaveBeenCalled();
      });
    }));
    it('shows refreshing state during refresh', (0, _asyncToGenerator2.default)(function* () {
      mockCustomerService.getServiceCategories.mockImplementation(function () {
        return new Promise(function (resolve) {
          return setTimeout(function () {
            return resolve(mockCategories);
          }, 1000);
        });
      });
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(IntegrationTestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Hair')).toBeTruthy();
      });
      var scrollView = _reactNative.screen.getByTestId('home-scroll-view');
      (0, _reactNative.fireEvent)(scrollView, 'refresh');
      expect(scrollView.props.refreshControl.props.refreshing).toBe(true);
      (0, _reactNative.act)(function () {
        jest.advanceTimersByTime(1000);
      });
      yield (0, _reactNative.waitFor)(function () {
        expect(scrollView.props.refreshControl.props.refreshing).toBe(false);
      });
    }));
  });
  describe('Memory and Resource Management', function () {
    it('cleans up resources on unmount', function () {
      var _render = (0, _reactNative.render)((0, _jsxRuntime.jsx)(IntegrationTestWrapper, {
          children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
        })),
        unmount = _render.unmount;
      unmount();
      expect(mockPerformanceMonitor.trackRender).toHaveBeenCalledWith('CustomerHomeScreen', expect.any(Number), expect.objectContaining({
        type: 'unmount'
      }));
    });
    it('handles multiple rapid re-renders efficiently', (0, _asyncToGenerator2.default)(function* () {
      var _render2 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(IntegrationTestWrapper, {
          children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
        })),
        rerender = _render2.rerender;
      for (var i = 0; i < 10; i++) {
        rerender((0, _jsxRuntime.jsx)(IntegrationTestWrapper, {
          children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
        }));
      }
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Hair')).toBeTruthy();
      });
      expect(mockCustomerService.getServiceCategories).toHaveBeenCalledTimes(1);
    }));
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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