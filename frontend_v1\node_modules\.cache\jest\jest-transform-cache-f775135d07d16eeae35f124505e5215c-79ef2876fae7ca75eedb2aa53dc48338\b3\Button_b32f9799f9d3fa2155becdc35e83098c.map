{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_StyleSheet", "_Text", "_Platform", "_TouchableNativeFeedback", "_TouchableOpacity", "_View", "_invariant", "React", "_interopRequireWildcard", "_jsxRuntime", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "has", "get", "set", "_t", "hasOwnProperty", "call", "getOwnPropertyDescriptor", "Touchable", "Platform", "OS", "TouchableNativeFeedback", "TouchableOpacity", "<PERSON><PERSON>", "forwardRef", "props", "ref", "_accessibilityState2", "_accessibilityState3", "accessibilityLabel", "accessibilityState", "ariaBusy", "ariaChe<PERSON>", "ariaDisabled", "ariaExpanded", "aria<PERSON><PERSON><PERSON>", "ariaSelected", "importantForAccessibility", "color", "onPress", "touchSoundDisabled", "title", "hasTVPreferredFocus", "nextFocusDown", "nextFocusForward", "nextFocusLeft", "nextFocusRight", "nextFocusUp", "testID", "accessible", "accessibilityActions", "accessibilityHint", "accessibilityLanguage", "onAccessibilityAction", "buttonStyles", "styles", "button", "textStyles", "text", "push", "backgroundColor", "_accessibilityState", "busy", "checked", "disabled", "expanded", "selected", "assign", "buttonDisabled", "textDisabled", "invariant", "formattedTitle", "toUpperCase", "_importantForAccessibility", "jsx", "accessibilityRole", "children", "style", "displayName", "StyleSheet", "create", "select", "ios", "android", "elevation", "borderRadius", "textAlign", "margin", "fontSize", "fontWeight", "_default"], "sources": ["Button.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow\n */\n\n'use strict';\n\nimport type {TextStyleProp, ViewStyleProp} from '../StyleSheet/StyleSheet';\nimport type {GestureResponderEvent} from '../Types/CoreEventTypes';\nimport type {\n  AccessibilityActionEvent,\n  AccessibilityActionInfo,\n  AccessibilityState,\n} from './View/ViewAccessibility';\n\nimport StyleSheet, {type ColorValue} from '../StyleSheet/StyleSheet';\nimport Text from '../Text/Text';\nimport Platform from '../Utilities/Platform';\nimport TouchableNativeFeedback from './Touchable/TouchableNativeFeedback';\nimport TouchableOpacity from './Touchable/TouchableOpacity';\nimport View from './View/View';\nimport invariant from 'invariant';\nimport * as React from 'react';\n\nexport type ButtonProps = $ReadOnly<{\n  /**\n    Text to display inside the button. On Android the given title will be\n    converted to the uppercased form.\n   */\n  title: string,\n\n  /**\n    Handler to be called when the user taps the button. The first function\n    argument is an event in form of [GestureResponderEvent](pressevent).\n   */\n  onPress: (event?: GestureResponderEvent) => mixed,\n\n  /**\n    If `true`, doesn't play system sound on touch.\n\n    @platform android\n\n    @default false\n   */\n  touchSoundDisabled?: ?boolean,\n\n  /**\n    Color of the text (iOS), or background color of the button (Android).\n\n    @default {@platform android} '#2196F3'\n    @default {@platform ios} '#007AFF'\n   */\n  color?: ?ColorValue,\n\n  /**\n    TV preferred focus.\n\n    @platform tv\n\n    @default false\n   */\n  hasTVPreferredFocus?: ?boolean,\n\n  /**\n    Designates the next view to receive focus when the user navigates down. See\n    the [Android documentation][android:nextFocusDown].\n\n    [android:nextFocusDown]:\n    https://developer.android.com/reference/android/view/View.html#attr_android:nextFocusDown\n\n    @platform android, tv\n   */\n  nextFocusDown?: ?number,\n\n  /**\n    Designates the next view to receive focus when the user navigates forward.\n    See the [Android documentation][android:nextFocusForward].\n\n    [android:nextFocusForward]:\n    https://developer.android.com/reference/android/view/View.html#attr_android:nextFocusForward\n\n    @platform android, tv\n   */\n  nextFocusForward?: ?number,\n\n  /**\n    Designates the next view to receive focus when the user navigates left. See\n    the [Android documentation][android:nextFocusLeft].\n\n    [android:nextFocusLeft]:\n    https://developer.android.com/reference/android/view/View.html#attr_android:nextFocusLeft\n\n    @platform android, tv\n   */\n  nextFocusLeft?: ?number,\n\n  /**\n    Designates the next view to receive focus when the user navigates right. See\n    the [Android documentation][android:nextFocusRight].\n\n    [android:nextFocusRight]:\n    https://developer.android.com/reference/android/view/View.html#attr_android:nextFocusRight\n\n    @platform android, tv\n   */\n  nextFocusRight?: ?number,\n\n  /**\n    Designates the next view to receive focus when the user navigates up. See\n    the [Android documentation][android:nextFocusUp].\n\n    [android:nextFocusUp]:\n    https://developer.android.com/reference/android/view/View.html#attr_android:nextFocusUp\n\n    @platform android, tv\n   */\n  nextFocusUp?: ?number,\n\n  /**\n    Text to display for blindness accessibility features.\n   */\n  accessibilityLabel?: ?string,\n  /**\n   * Alias for accessibilityLabel  https://reactnative.dev/docs/view#accessibilitylabel\n   * https://github.com/facebook/react-native/issues/34424\n   */\n  'aria-label'?: ?string,\n  /**\n    If `true`, disable all interactions for this component.\n\n    @default false\n   */\n  disabled?: ?boolean,\n\n  /**\n    Used to locate this view in end-to-end tests.\n   */\n  testID?: ?string,\n\n  /**\n   * Accessibility props.\n   */\n  accessible?: ?boolean,\n  accessibilityActions?: ?$ReadOnlyArray<AccessibilityActionInfo>,\n  onAccessibilityAction?: ?(event: AccessibilityActionEvent) => mixed,\n  accessibilityState?: ?AccessibilityState,\n\n  /**\n   * alias for accessibilityState\n   *\n   * see https://reactnative.dev/docs/accessibility#accessibilitystate\n   */\n  'aria-busy'?: ?boolean,\n  'aria-checked'?: ?boolean | 'mixed',\n  'aria-disabled'?: ?boolean,\n  'aria-expanded'?: ?boolean,\n  'aria-selected'?: ?boolean,\n\n  /**\n   * [Android] Controlling if a view fires accessibility events and if it is reported to accessibility services.\n   */\n  importantForAccessibility?: ?('auto' | 'yes' | 'no' | 'no-hide-descendants'),\n  accessibilityHint?: ?string,\n  accessibilityLanguage?: ?Stringish,\n}>;\n\n/**\n  A basic button component that should render nicely on any platform. Supports a\n  minimal level of customization.\n\n  If this button doesn't look right for your app, you can build your own button\n  using [TouchableOpacity](touchableopacity) or\n  [TouchableWithoutFeedback](touchablewithoutfeedback). For inspiration, look at\n  the [source code for this button component][button:source]. Or, take a look at\n  the [wide variety of button components built by the community]\n  [button:examples].\n\n  [button:source]:\n  https://github.com/facebook/react-native/blob/HEAD/Libraries/Components/Button.js\n\n  [button:examples]:\n  https://js.coach/?menu%5Bcollections%5D=React%20Native&page=1&query=button\n\n  ```jsx\n  <Button\n    onPress={onPressLearnMore}\n    title=\"Learn More\"\n    color=\"#841584\"\n    accessibilityLabel=\"Learn more about this purple button\"\n  />\n  ```\n\n  ```SnackPlayer name=Button%20Example\n  import React from 'react';\n  import { StyleSheet, Button, View, SafeAreaView, Text, Alert } from 'react-native';\n\n  const Separator = () => (\n    <View style={styles.separator} />\n  );\n\n  const App = () => (\n    <SafeAreaView style={styles.container}>\n      <View>\n        <Text style={styles.title}>\n          The title and onPress handler are required. It is recommended to set accessibilityLabel to help make your app usable by everyone.\n        </Text>\n        <Button\n          title=\"Press me\"\n          onPress={() => Alert.alert('Simple Button pressed')}\n        />\n      </View>\n      <Separator />\n      <View>\n        <Text style={styles.title}>\n          Adjust the color in a way that looks standard on each platform. On  iOS, the color prop controls the color of the text. On Android, the color adjusts the background color of the button.\n        </Text>\n        <Button\n          title=\"Press me\"\n          color=\"#f194ff\"\n          onPress={() => Alert.alert('Button with adjusted color pressed')}\n        />\n      </View>\n      <Separator />\n      <View>\n        <Text style={styles.title}>\n          All interaction for the component are disabled.\n        </Text>\n        <Button\n          title=\"Press me\"\n          disabled\n          onPress={() => Alert.alert('Cannot press this one')}\n        />\n      </View>\n      <Separator />\n      <View>\n        <Text style={styles.title}>\n          This layout strategy lets the title define the width of the button.\n        </Text>\n        <View style={styles.fixToText}>\n          <Button\n            title=\"Left button\"\n            onPress={() => Alert.alert('Left button pressed')}\n          />\n          <Button\n            title=\"Right button\"\n            onPress={() => Alert.alert('Right button pressed')}\n          />\n        </View>\n      </View>\n    </SafeAreaView>\n  );\n\n  const styles = StyleSheet.create({\n    container: {\n      flex: 1,\n      justifyContent: 'center',\n      marginHorizontal: 16,\n    },\n    title: {\n      textAlign: 'center',\n      marginVertical: 8,\n    },\n    fixToText: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n    },\n    separator: {\n      marginVertical: 8,\n      borderBottomColor: '#737373',\n      borderBottomWidth: StyleSheet.hairlineWidth,\n    },\n  });\n\n  export default App;\n  ```\n */\n\nconst Touchable: typeof TouchableNativeFeedback | typeof TouchableOpacity =\n  Platform.OS === 'android' ? TouchableNativeFeedback : TouchableOpacity;\n\ntype ButtonRef = React.ElementRef<typeof Touchable>;\n\nconst Button: component(\n  ref?: React.RefSetter<ButtonRef>,\n  ...props: ButtonProps\n) = React.forwardRef((props: ButtonProps, ref: React.RefSetter<ButtonRef>) => {\n  const {\n    accessibilityLabel,\n    accessibilityState,\n    'aria-busy': ariaBusy,\n    'aria-checked': ariaChecked,\n    'aria-disabled': ariaDisabled,\n    'aria-expanded': ariaExpanded,\n    'aria-label': ariaLabel,\n    'aria-selected': ariaSelected,\n    importantForAccessibility,\n    color,\n    onPress,\n    touchSoundDisabled,\n    title,\n    hasTVPreferredFocus,\n    nextFocusDown,\n    nextFocusForward,\n    nextFocusLeft,\n    nextFocusRight,\n    nextFocusUp,\n    testID,\n    accessible,\n    accessibilityActions,\n    accessibilityHint,\n    accessibilityLanguage,\n    onAccessibilityAction,\n  } = props;\n  const buttonStyles: Array<ViewStyleProp> = [styles.button];\n  const textStyles: Array<TextStyleProp> = [styles.text];\n  if (color) {\n    if (Platform.OS === 'ios') {\n      textStyles.push({color: color});\n    } else {\n      buttonStyles.push({backgroundColor: color});\n    }\n  }\n\n  let _accessibilityState = {\n    busy: ariaBusy ?? accessibilityState?.busy,\n    checked: ariaChecked ?? accessibilityState?.checked,\n    disabled: ariaDisabled ?? accessibilityState?.disabled,\n    expanded: ariaExpanded ?? accessibilityState?.expanded,\n    selected: ariaSelected ?? accessibilityState?.selected,\n  };\n\n  const disabled =\n    props.disabled != null ? props.disabled : _accessibilityState?.disabled;\n\n  _accessibilityState =\n    disabled !== _accessibilityState?.disabled\n      ? {..._accessibilityState, disabled}\n      : _accessibilityState;\n\n  if (disabled) {\n    buttonStyles.push(styles.buttonDisabled);\n    textStyles.push(styles.textDisabled);\n  }\n\n  invariant(\n    typeof title === 'string',\n    'The title prop of a Button must be a string',\n  );\n  const formattedTitle =\n    Platform.OS === 'android' ? title.toUpperCase() : title;\n\n  // If `no` is specified for `importantForAccessibility`, it will be changed to `no-hide-descendants` because the text inside should not be focused.\n  const _importantForAccessibility =\n    importantForAccessibility === 'no'\n      ? 'no-hide-descendants'\n      : importantForAccessibility;\n\n  return (\n    <Touchable\n      accessible={accessible}\n      accessibilityActions={accessibilityActions}\n      onAccessibilityAction={onAccessibilityAction}\n      accessibilityLabel={ariaLabel || accessibilityLabel}\n      accessibilityHint={accessibilityHint}\n      accessibilityLanguage={accessibilityLanguage}\n      accessibilityRole=\"button\"\n      accessibilityState={_accessibilityState}\n      importantForAccessibility={_importantForAccessibility}\n      hasTVPreferredFocus={hasTVPreferredFocus}\n      nextFocusDown={nextFocusDown}\n      nextFocusForward={nextFocusForward}\n      nextFocusLeft={nextFocusLeft}\n      nextFocusRight={nextFocusRight}\n      nextFocusUp={nextFocusUp}\n      testID={testID}\n      disabled={disabled}\n      onPress={onPress}\n      touchSoundDisabled={touchSoundDisabled}\n      // $FlowFixMe[incompatible-exact]\n      // $FlowFixMe[prop-missing]\n      // $FlowFixMe[incompatible-type-arg]\n      ref={ref}>\n      <View style={buttonStyles}>\n        <Text style={textStyles} disabled={disabled}>\n          {formattedTitle}\n        </Text>\n      </View>\n    </Touchable>\n  );\n});\n\nButton.displayName = 'Button';\n\nconst styles = StyleSheet.create({\n  button: Platform.select({\n    ios: {},\n    android: {\n      elevation: 4,\n      // Material design blue from https://material.google.com/style/color.html#color-color-palette\n      backgroundColor: '#2196F3',\n      borderRadius: 2,\n    },\n  }),\n  text: {\n    textAlign: 'center',\n    margin: 8,\n    ...Platform.select({\n      ios: {\n        // iOS blue from https://developer.apple.com/ios/human-interface-guidelines/visual-design/color/\n        color: '#007AFF',\n        fontSize: 18,\n      },\n      android: {\n        color: 'white',\n        fontWeight: '500',\n      },\n    }),\n  },\n  buttonDisabled: Platform.select({\n    ios: {},\n    android: {\n      elevation: 0,\n      backgroundColor: '#dfdfdf',\n    },\n  }),\n  textDisabled: Platform.select({\n    ios: {\n      color: '#cdcdcd',\n    },\n    android: {\n      color: '#a1a1a1',\n    },\n  }),\n});\n\nexport default Button;\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAUb,IAAAC,WAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,KAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,SAAA,GAAAT,sBAAA,CAAAC,OAAA;AACA,IAAAS,wBAAA,GAAAV,sBAAA,CAAAC,OAAA;AACA,IAAAU,iBAAA,GAAAX,sBAAA,CAAAC,OAAA;AACA,IAAAW,KAAA,GAAAZ,sBAAA,CAAAC,OAAA;AACA,IAAAY,UAAA,GAAAb,sBAAA,CAAAC,OAAA;AACA,IAAAa,KAAA,GAAAC,uBAAA,CAAAd,OAAA;AAA+B,IAAAe,WAAA,GAAAf,OAAA;AAAA,SAAAc,wBAAAE,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAJ,uBAAA,YAAAA,wBAAAE,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAApB,OAAA,EAAAW,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAI,GAAA,CAAAV,CAAA,UAAAM,CAAA,CAAAK,GAAA,CAAAX,CAAA,GAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,EAAAQ,CAAA,cAAAK,EAAA,IAAAb,CAAA,gBAAAa,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAa,EAAA,OAAAN,CAAA,IAAAD,CAAA,GAAArB,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAA+B,wBAAA,CAAAhB,CAAA,EAAAa,EAAA,OAAAN,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAK,EAAA,EAAAN,CAAA,IAAAC,CAAA,CAAAK,EAAA,IAAAb,CAAA,CAAAa,EAAA,WAAAL,CAAA,KAAAR,CAAA,EAAAC,CAAA;AA+P/B,IAAMgB,SAAmE,GACvEC,iBAAQ,CAACC,EAAE,KAAK,SAAS,GAAGC,gCAAuB,GAAGC,yBAAgB;AAIxE,IAAMC,MAGL,GAAGzB,KAAK,CAAC0B,UAAU,CAAC,UAACC,KAAkB,EAAEC,GAA+B,EAAK;EAAA,IAAAC,oBAAA,EAAAC,oBAAA;EAC5E,IACEC,kBAAkB,GAyBhBJ,KAAK,CAzBPI,kBAAkB;IAClBC,kBAAkB,GAwBhBL,KAAK,CAxBPK,kBAAkB;IACLC,QAAQ,GAuBnBN,KAAK,CAvBP,WAAW;IACKO,WAAW,GAsBzBP,KAAK,CAtBP,cAAc;IACGQ,YAAY,GAqB3BR,KAAK,CArBP,eAAe;IACES,YAAY,GAoB3BT,KAAK,CApBP,eAAe;IACDU,SAAS,GAmBrBV,KAAK,CAnBP,YAAY;IACKW,YAAY,GAkB3BX,KAAK,CAlBP,eAAe;IACfY,yBAAyB,GAiBvBZ,KAAK,CAjBPY,yBAAyB;IACzBC,KAAK,GAgBHb,KAAK,CAhBPa,KAAK;IACLC,OAAO,GAeLd,KAAK,CAfPc,OAAO;IACPC,kBAAkB,GAchBf,KAAK,CAdPe,kBAAkB;IAClBC,KAAK,GAaHhB,KAAK,CAbPgB,KAAK;IACLC,mBAAmB,GAYjBjB,KAAK,CAZPiB,mBAAmB;IACnBC,aAAa,GAWXlB,KAAK,CAXPkB,aAAa;IACbC,gBAAgB,GAUdnB,KAAK,CAVPmB,gBAAgB;IAChBC,aAAa,GASXpB,KAAK,CATPoB,aAAa;IACbC,cAAc,GAQZrB,KAAK,CARPqB,cAAc;IACdC,WAAW,GAOTtB,KAAK,CAPPsB,WAAW;IACXC,MAAM,GAMJvB,KAAK,CANPuB,MAAM;IACNC,UAAU,GAKRxB,KAAK,CALPwB,UAAU;IACVC,oBAAoB,GAIlBzB,KAAK,CAJPyB,oBAAoB;IACpBC,iBAAiB,GAGf1B,KAAK,CAHP0B,iBAAiB;IACjBC,qBAAqB,GAEnB3B,KAAK,CAFP2B,qBAAqB;IACrBC,qBAAqB,GACnB5B,KAAK,CADP4B,qBAAqB;EAEvB,IAAMC,YAAkC,GAAG,CAACC,MAAM,CAACC,MAAM,CAAC;EAC1D,IAAMC,UAAgC,GAAG,CAACF,MAAM,CAACG,IAAI,CAAC;EACtD,IAAIpB,KAAK,EAAE;IACT,IAAInB,iBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;MACzBqC,UAAU,CAACE,IAAI,CAAC;QAACrB,KAAK,EAAEA;MAAK,CAAC,CAAC;IACjC,CAAC,MAAM;MACLgB,YAAY,CAACK,IAAI,CAAC;QAACC,eAAe,EAAEtB;MAAK,CAAC,CAAC;IAC7C;EACF;EAEA,IAAIuB,mBAAmB,GAAG;IACxBC,IAAI,EAAE/B,QAAQ,WAARA,QAAQ,GAAID,kBAAkB,oBAAlBA,kBAAkB,CAAEgC,IAAI;IAC1CC,OAAO,EAAE/B,WAAW,WAAXA,WAAW,GAAIF,kBAAkB,oBAAlBA,kBAAkB,CAAEiC,OAAO;IACnDC,QAAQ,EAAE/B,YAAY,WAAZA,YAAY,GAAIH,kBAAkB,oBAAlBA,kBAAkB,CAAEkC,QAAQ;IACtDC,QAAQ,EAAE/B,YAAY,WAAZA,YAAY,GAAIJ,kBAAkB,oBAAlBA,kBAAkB,CAAEmC,QAAQ;IACtDC,QAAQ,EAAE9B,YAAY,WAAZA,YAAY,GAAIN,kBAAkB,oBAAlBA,kBAAkB,CAAEoC;EAChD,CAAC;EAED,IAAMF,QAAQ,GACZvC,KAAK,CAACuC,QAAQ,IAAI,IAAI,GAAGvC,KAAK,CAACuC,QAAQ,IAAArC,oBAAA,GAAGkC,mBAAmB,qBAAnBlC,oBAAA,CAAqBqC,QAAQ;EAEzEH,mBAAmB,GACjBG,QAAQ,OAAApC,oBAAA,GAAKiC,mBAAmB,qBAAnBjC,oBAAA,CAAqBoC,QAAQ,IAAA9E,MAAA,CAAAiF,MAAA,KAClCN,mBAAmB;IAAEG,QAAQ,EAARA;EAAQ,KACjCH,mBAAmB;EAEzB,IAAIG,QAAQ,EAAE;IACZV,YAAY,CAACK,IAAI,CAACJ,MAAM,CAACa,cAAc,CAAC;IACxCX,UAAU,CAACE,IAAI,CAACJ,MAAM,CAACc,YAAY,CAAC;EACtC;EAEA,IAAAC,kBAAS,EACP,OAAO7B,KAAK,KAAK,QAAQ,EACzB,6CACF,CAAC;EACD,IAAM8B,cAAc,GAClBpD,iBAAQ,CAACC,EAAE,KAAK,SAAS,GAAGqB,KAAK,CAAC+B,WAAW,CAAC,CAAC,GAAG/B,KAAK;EAGzD,IAAMgC,0BAA0B,GAC9BpC,yBAAyB,KAAK,IAAI,GAC9B,qBAAqB,GACrBA,yBAAyB;EAE/B,OACE,IAAArC,WAAA,CAAA0E,GAAA,EAACxD,SAAS;IACR+B,UAAU,EAAEA,UAAW;IACvBC,oBAAoB,EAAEA,oBAAqB;IAC3CG,qBAAqB,EAAEA,qBAAsB;IAC7CxB,kBAAkB,EAAEM,SAAS,IAAIN,kBAAmB;IACpDsB,iBAAiB,EAAEA,iBAAkB;IACrCC,qBAAqB,EAAEA,qBAAsB;IAC7CuB,iBAAiB,EAAC,QAAQ;IAC1B7C,kBAAkB,EAAE+B,mBAAoB;IACxCxB,yBAAyB,EAAEoC,0BAA2B;IACtD/B,mBAAmB,EAAEA,mBAAoB;IACzCC,aAAa,EAAEA,aAAc;IAC7BC,gBAAgB,EAAEA,gBAAiB;IACnCC,aAAa,EAAEA,aAAc;IAC7BC,cAAc,EAAEA,cAAe;IAC/BC,WAAW,EAAEA,WAAY;IACzBC,MAAM,EAAEA,MAAO;IACfgB,QAAQ,EAAEA,QAAS;IACnBzB,OAAO,EAAEA,OAAQ;IACjBC,kBAAkB,EAAEA,kBAAmB;IAIvCd,GAAG,EAAEA,GAAI;IAAAkD,QAAA,EACT,IAAA5E,WAAA,CAAA0E,GAAA,EAAC9E,KAAA,CAAAN,OAAI;MAACuF,KAAK,EAAEvB,YAAa;MAAAsB,QAAA,EACxB,IAAA5E,WAAA,CAAA0E,GAAA,EAAClF,KAAA,CAAAF,OAAI;QAACuF,KAAK,EAAEpB,UAAW;QAACO,QAAQ,EAAEA,QAAS;QAAAY,QAAA,EACzCL;MAAc,CACX;IAAC,CACH;EAAC,CACE,CAAC;AAEhB,CAAC,CAAC;AAEFhD,MAAM,CAACuD,WAAW,GAAG,QAAQ;AAE7B,IAAMvB,MAAM,GAAGwB,mBAAU,CAACC,MAAM,CAAC;EAC/BxB,MAAM,EAAErC,iBAAQ,CAAC8D,MAAM,CAAC;IACtBC,GAAG,EAAE,CAAC,CAAC;IACPC,OAAO,EAAE;MACPC,SAAS,EAAE,CAAC;MAEZxB,eAAe,EAAE,SAAS;MAC1ByB,YAAY,EAAE;IAChB;EACF,CAAC,CAAC;EACF3B,IAAI,EAAAxE,MAAA,CAAAiF,MAAA;IACFmB,SAAS,EAAE,QAAQ;IACnBC,MAAM,EAAE;EAAC,GACNpE,iBAAQ,CAAC8D,MAAM,CAAC;IACjBC,GAAG,EAAE;MAEH5C,KAAK,EAAE,SAAS;MAChBkD,QAAQ,EAAE;IACZ,CAAC;IACDL,OAAO,EAAE;MACP7C,KAAK,EAAE,OAAO;MACdmD,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH;EACDrB,cAAc,EAAEjD,iBAAQ,CAAC8D,MAAM,CAAC;IAC9BC,GAAG,EAAE,CAAC,CAAC;IACPC,OAAO,EAAE;MACPC,SAAS,EAAE,CAAC;MACZxB,eAAe,EAAE;IACnB;EACF,CAAC,CAAC;EACFS,YAAY,EAAElD,iBAAQ,CAAC8D,MAAM,CAAC;IAC5BC,GAAG,EAAE;MACH5C,KAAK,EAAE;IACT,CAAC;IACD6C,OAAO,EAAE;MACP7C,KAAK,EAAE;IACT;EACF,CAAC;AACH,CAAC,CAAC;AAAC,IAAAoD,QAAA,GAAAtG,OAAA,CAAAE,OAAA,GAEYiC,MAAM", "ignoreList": []}