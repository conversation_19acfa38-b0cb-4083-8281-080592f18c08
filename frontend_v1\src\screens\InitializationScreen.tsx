/**
 * Optimized Vierla Initialization Screen
 * Enhanced with onboarding thematic styling while retaining background
 * Updated text to 'Self-Care, Simplified'
 *
 * @version 4.0.0
 * <AUTHOR> Development Team
 */

import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useRef, useState } from 'react';
import { View, Text, Dimensions, StyleSheet, Animated } from 'react-native';

import { AnimatedGradientBackground } from '../components/ui/AnimatedGradientBackground';
import { SafeAreaScreen } from '../components/ui/SafeAreaWrapper';
import { useTheme } from '../contexts/ThemeContext';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../utils/responsiveUtils';

const { width, height } = Dimensions.get('window');

interface InitializationScreenProps {
  onComplete: () => void;
}

export const InitializationScreen: React.FC<InitializationScreenProps> = ({
  onComplete,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const [isReady, setIsReady] = useState(false);

  // Enhanced animation refs for onboarding-style animations
  const containerOpacity = useRef(new Animated.Value(0)).current;
  const logoOpacity = useRef(new Animated.Value(0)).current;
  const logoTranslateY = useRef(new Animated.Value(30)).current;
  const logoScale = useRef(new Animated.Value(0.9)).current;
  const taglineOpacity = useRef(new Animated.Value(0)).current;
  const taglineTranslateY = useRef(new Animated.Value(20)).current;
  const loadingOpacity = useRef(new Animated.Value(0)).current;

  // Optimized initialization effect
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Minimal initialization delay to ensure components are ready
        await new Promise(resolve => setTimeout(resolve, 300));

        // Start container fade-in
        Animated.timing(containerOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }).start();

        // Staggered animations for logo and content
        setTimeout(() => {
          // Logo animation
          Animated.parallel([
            Animated.timing(logoOpacity, {
              toValue: 1,
              duration: 800,
              useNativeDriver: true,
            }),
            Animated.timing(logoTranslateY, {
              toValue: 0,
              duration: 800,
              useNativeDriver: true,
            }),
            Animated.spring(logoScale, {
              toValue: 1,
              tension: 50,
              friction: 8,
              useNativeDriver: true,
            }),
          ]).start();
        }, 200);

        // Tagline animation
        setTimeout(() => {
          Animated.parallel([
            Animated.timing(taglineOpacity, {
              toValue: 1,
              duration: 600,
              useNativeDriver: true,
            }),
            Animated.timing(taglineTranslateY, {
              toValue: 0,
              duration: 600,
              useNativeDriver: true,
            }),
          ]).start();
        }, 600);

        // Loading indicator
        setTimeout(() => {
          Animated.timing(loadingOpacity, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
          }).start();
        }, 1000);

        setIsReady(true);

        // Auto-complete after full animation sequence
        setTimeout(() => {
          onComplete();
        }, 2500);
      } catch (error) {
        console.error('Initialization error:', error);
        // Fallback: complete immediately if there's an error
        onComplete();
      }
    };

    initializeApp();
  }, [containerOpacity, logoOpacity, logoTranslateY, logoScale, taglineOpacity, taglineTranslateY, loadingOpacity, onComplete]);

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={[
          colors.primary?.default || '#2E7D32',
          colors.primary?.light || '#4CAF50',
        ]}
        style={styles.gradient}>
        <Animated.View
          style={[
            styles.content,
            {
              opacity: containerOpacity,
            },
          ]}>
          <View style={styles.logoContainer}>
            <Text
              style={[
                styles.logo,
                { color: colors.text?.inverse || '#FFFFFF' },
              ]}>
              Vierla
            </Text>
          </View>

          <Text
            style={[
              styles.tagline,
              { color: colors.text?.inverse || '#FFFFFF' },
            ]}>
            Self-Care, Simplified
          </Text>

          {isReady && (
            <Animated.View
              style={[styles.loadingContainer, { opacity: loadingOpacity }]}>
              <Text
                style={[
                  styles.loadingText,
                  { color: colors.text?.inverse || '#FFFFFF' },
                ]}>
                Loading...
              </Text>
            </Animated.View>
          )}
        </Animated.View>
      </LinearGradient>
    </SafeAreaView>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: getResponsiveSpacing(32),
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(40),
  },
  logoIconContainer: {
    marginBottom: getResponsiveSpacing(16),
    alignItems: 'center',
    justifyContent: 'center',
    width: getResponsiveFontSize(80),
    height: getResponsiveFontSize(80),
    borderRadius: getResponsiveFontSize(40),
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  logoIcon: {
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  logo: {
    fontSize: getResponsiveFontSize(42),
    fontWeight: '700',
    textAlign: 'center',
    letterSpacing: 2,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  taglineContainer: {
    marginBottom: getResponsiveSpacing(60),
    paddingHorizontal: getResponsiveSpacing(20),
  },
  tagline: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: '500',
    textAlign: 'center',
    opacity: 0.95,
    letterSpacing: 1,
    lineHeight: getResponsiveFontSize(28),
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  loadingContainer: {
    position: 'absolute',
    bottom: getResponsiveSpacing(80),
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '400',
    textAlign: 'center',
    opacity: 0.8,
    letterSpacing: 0.5,
  },
});
