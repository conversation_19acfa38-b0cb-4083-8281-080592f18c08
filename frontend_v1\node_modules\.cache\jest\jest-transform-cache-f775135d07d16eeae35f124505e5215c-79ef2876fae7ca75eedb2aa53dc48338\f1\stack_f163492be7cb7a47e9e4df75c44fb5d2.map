{"version": 3, "names": ["React", "require", "createStackNavigator", "exports", "jest", "fn", "Navigator", "_ref", "children", "Screen", "_ref2", "CardStyleInterpolators", "forHorizontalIOS", "forVerticalIOS", "forModalPresentationIOS", "TransitionPresets", "SlideFromRightIOS", "ModalSlideFromBottomIOS", "_default", "default"], "sources": ["stack.js"], "sourcesContent": ["/**\n * Mock for @react-navigation/stack\n */\n\nconst React = require('react');\n\nexport const createStackNavigator = jest.fn(() => ({\n  Navigator: ({ children }) => children,\n  Screen: ({ children }) => children,\n}));\n\nexport const CardStyleInterpolators = {\n  forHorizontalIOS: {},\n  forVerticalIOS: {},\n  forModalPresentationIOS: {},\n};\n\nexport const TransitionPresets = {\n  SlideFromRightIOS: {},\n  ModalSlideFromBottomIOS: {},\n};\n\nexport default {\n  createStackNavigator,\n  CardStyleInterpolators,\n  TransitionPresets,\n};\n"], "mappings": ";;;;AAIA,IAAMA,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAEvB,IAAMC,oBAAoB,GAAAC,OAAA,CAAAD,oBAAA,GAAGE,IAAI,CAACC,EAAE,CAAC;EAAA,OAAO;IACjDC,SAAS,EAAE,SAAXA,SAASA,CAAAC,IAAA;MAAA,IAAKC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;MAAA,OAAOA,QAAQ;IAAA;IACrCC,MAAM,EAAE,SAARA,MAAMA,CAAAC,KAAA;MAAA,IAAKF,QAAQ,GAAAE,KAAA,CAARF,QAAQ;MAAA,OAAOA,QAAQ;IAAA;EACpC,CAAC;AAAA,CAAC,CAAC;AAEI,IAAMG,sBAAsB,GAAAR,OAAA,CAAAQ,sBAAA,GAAG;EACpCC,gBAAgB,EAAE,CAAC,CAAC;EACpBC,cAAc,EAAE,CAAC,CAAC;EAClBC,uBAAuB,EAAE,CAAC;AAC5B,CAAC;AAEM,IAAMC,iBAAiB,GAAAZ,OAAA,CAAAY,iBAAA,GAAG;EAC/BC,iBAAiB,EAAE,CAAC,CAAC;EACrBC,uBAAuB,EAAE,CAAC;AAC5B,CAAC;AAAC,IAAAC,QAAA,GAAAf,OAAA,CAAAgB,OAAA,GAEa;EACbjB,oBAAoB,EAApBA,oBAAoB;EACpBS,sBAAsB,EAAtBA,sBAAsB;EACtBI,iBAAiB,EAAjBA;AACF,CAAC", "ignoreList": []}