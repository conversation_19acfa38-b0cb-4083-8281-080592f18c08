{"version": 3, "names": ["_asyncStorage", "_interopRequireDefault", "require", "_zustand", "_middleware", "initialState", "authToken", "refreshToken", "user", "userRole", "status", "error", "tokenExpiresAt", "isAuthenticated", "createAuthStore", "create", "devtools", "persist", "set", "get", "Object", "assign", "loginStart", "state", "loginSuccess", "token", "role", "Date", "now", "loginFailure", "registerStart", "registerSuccess", "registerFailure", "updateProfile", "userUpdates", "updateTokens", "logout", "reset", "checkAuthStatus", "_checkAuthStatus", "_asyncToGenerator2", "default", "currentState", "storedToken", "AsyncStorage", "getItem", "storedRefreshToken", "storedUser", "JSON", "parse", "parseError", "console", "multiRemove", "apply", "arguments", "validateToken", "_validateToken", "_yield$import", "authService", "response", "access", "setItem", "refreshError", "name", "storage", "_getItem", "value", "parsed", "log", "removeItem", "_unused", "_x", "_setItem", "stringify", "_x2", "_x3", "_removeItem", "_x4", "partialize", "_version", "migrate", "persistedState", "version", "_checkAuthStatus2", "_validateToken2", "useAuthStore", "exports", "useSafeAuthStore", "store", "warn", "_checkAuthStatus3", "_validateToken3", "_checkAuthStatus4", "_validateToken4"], "sources": ["authSlice.ts"], "sourcesContent": ["/**\n * Auth Slice - Zustand Store for Authentication State\n *\n * Component Contract:\n * - Manages authentication state for dual-role system (customer/provider)\n * - Handles login, registration, and logout actions\n * - Provides computed properties for authentication status\n * - Maintains error state for UI feedback\n * - Follows immutable state update patterns\n * - Supports state persistence and hydration\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport AsyncStorage from '@react-native-async-storage/async-storage';\nimport { create } from 'zustand';\nimport { devtools, persist } from 'zustand/middleware';\n\nexport type UserRole = 'customer' | 'provider';\n\nexport type AuthStatus = 'idle' | 'loading' | 'success' | 'error';\n\nexport interface User {\n  id: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  role: UserRole;\n  profileImage?: string;\n  phoneNumber?: string;\n  isVerified: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface AuthState {\n  // State\n  authToken: string | null;\n  refreshToken: string | null;\n  user: User | null;\n  userRole: UserRole | null;\n  status: AuthStatus;\n  error: string | null;\n  tokenExpiresAt: number | null;\n\n  // Computed properties\n  isAuthenticated: boolean;\n\n  // Actions\n  loginStart: () => void;\n  loginSuccess: (token: string, refreshToken: string, user: User) => void;\n  loginFailure: (error: string) => void;\n\n  registerStart: () => void;\n  registerSuccess: (token: string, refreshToken: string, user: User) => void;\n  registerFailure: (error: string) => void;\n\n  updateProfile: (user: Partial<User>) => void;\n  updateTokens: (token: string, refreshToken?: string) => void;\n\n  logout: () => void;\n  reset: () => void;\n  checkAuthStatus: () => Promise<void>;\n  validateToken: () => Promise<boolean>;\n}\n\nconst initialState = {\n  authToken: null,\n  refreshToken: null,\n  user: null,\n  userRole: null,\n  status: 'idle' as AuthStatus,\n  error: null,\n  tokenExpiresAt: null,\n  isAuthenticated: false,\n};\n\n// Create a safe auth store with error handling\nconst createAuthStore = () => {\n  try {\n    return create<AuthState>()(\n      devtools(\n        persist(\n          (set, get) => ({\n            // Initial state\n            ...initialState,\n\n            // Computed properties\n            isAuthenticated: false,\n\n            // Login actions\n            loginStart: () =>\n              set(\n                state => ({\n                  ...state,\n                  status: 'loading',\n                  error: null,\n                }),\n                false,\n                'auth/loginStart',\n              ),\n\n            loginSuccess: (token: string, refreshToken: string, user: User) =>\n              set(\n                state => ({\n                  ...state,\n                  authToken: token,\n                  refreshToken,\n                  user,\n                  userRole: user.role,\n                  status: 'success',\n                  error: null,\n                  isAuthenticated: true,\n                  tokenExpiresAt: Date.now() + 30 * 60 * 1000, // 30 minutes from now\n                }),\n                false,\n                'auth/loginSuccess',\n              ),\n\n            loginFailure: (error: string) =>\n              set(\n                state => ({\n                  ...state,\n                  authToken: null,\n                  userRole: null,\n                  status: 'error',\n                  error,\n                  isAuthenticated: false,\n                }),\n                false,\n                'auth/loginFailure',\n              ),\n\n            // Registration actions\n            registerStart: () =>\n              set(\n                state => ({\n                  ...state,\n                  status: 'loading',\n                  error: null,\n                }),\n                false,\n                'auth/registerStart',\n              ),\n\n            registerSuccess: (\n              token: string,\n              refreshToken: string,\n              user: User,\n            ) =>\n              set(\n                state => ({\n                  ...state,\n                  authToken: token,\n                  refreshToken,\n                  user,\n                  userRole: user.role,\n                  status: 'success',\n                  error: null,\n                  isAuthenticated: true,\n                  tokenExpiresAt: Date.now() + 30 * 60 * 1000, // 30 minutes from now\n                }),\n                false,\n                'auth/registerSuccess',\n              ),\n\n            registerFailure: (error: string) =>\n              set(\n                state => ({\n                  ...state,\n                  authToken: null,\n                  refreshToken: null,\n                  user: null,\n                  userRole: null,\n                  status: 'error',\n                  error,\n                  isAuthenticated: false,\n                  tokenExpiresAt: null,\n                }),\n                false,\n                'auth/registerFailure',\n              ),\n\n            // Profile management\n            updateProfile: (userUpdates: Partial<User>) =>\n              set(\n                state => ({\n                  ...state,\n                  user: state.user ? { ...state.user, ...userUpdates } : null,\n                }),\n                false,\n                'auth/updateProfile',\n              ),\n\n            // Token management\n            updateTokens: (token: string, refreshToken?: string) =>\n              set(\n                state => ({\n                  ...state,\n                  authToken: token,\n                  refreshToken: refreshToken || state.refreshToken,\n                  tokenExpiresAt: Date.now() + 30 * 60 * 1000, // 30 minutes from now\n                }),\n                false,\n                'auth/updateTokens',\n              ),\n\n            // Logout action\n            logout: () =>\n              set(\n                () => ({\n                  ...initialState,\n                }),\n                false,\n                'auth/logout',\n              ),\n\n            // Reset action (for testing)\n            reset: () =>\n              set(\n                () => ({\n                  ...initialState,\n                }),\n                false,\n                'auth/reset',\n              ),\n\n            // Check authentication status (for initialization)\n            checkAuthStatus: async () => {\n              try {\n                const currentState = get();\n\n                // If already authenticated and token is not expired, no need to check again\n                if (\n                  currentState.isAuthenticated &&\n                  currentState.authToken &&\n                  currentState.tokenExpiresAt\n                ) {\n                  if (Date.now() < currentState.tokenExpiresAt) {\n                    return;\n                  }\n                }\n\n                // Try to load tokens from AsyncStorage\n                const storedToken = await AsyncStorage.getItem('auth_token');\n                const storedRefreshToken =\n                  await AsyncStorage.getItem('refresh_token');\n                const storedUser = await AsyncStorage.getItem('auth_user');\n\n                if (storedToken && storedUser) {\n                  try {\n                    const user = JSON.parse(storedUser);\n                    set(\n                      state => ({\n                        ...state,\n                        authToken: storedToken,\n                        refreshToken: storedRefreshToken,\n                        user,\n                        userRole: user.role,\n                        isAuthenticated: true,\n                        status: 'success',\n                        error: null,\n                      }),\n                      false,\n                      'auth/checkAuthStatus',\n                    );\n                  } catch (parseError) {\n                    console.error(\n                      'Failed to parse stored user data:',\n                      parseError,\n                    );\n                    await AsyncStorage.multiRemove([\n                      'auth_token',\n                      'refresh_token',\n                      'auth_user',\n                    ]);\n                    set(() => initialState, false, 'auth/checkAuthStatusError');\n                  }\n                } else {\n                  set(() => initialState, false, 'auth/checkAuthStatus');\n                }\n              } catch (error) {\n                console.error('Auth status check failed:', error);\n                set(() => initialState, false, 'auth/checkAuthStatusError');\n              }\n            },\n\n            // Validate current token\n            validateToken: async (): Promise<boolean> => {\n              try {\n                const currentState = get();\n\n                if (!currentState.authToken) {\n                  return false;\n                }\n\n                // Check if token is expired\n                if (\n                  currentState.tokenExpiresAt &&\n                  Date.now() >= currentState.tokenExpiresAt\n                ) {\n                  // Try to refresh token\n                  if (currentState.refreshToken) {\n                    try {\n                      // Import authService dynamically to avoid circular dependencies\n                      const { authService } = await import(\n                        '../services/authService'\n                      );\n                      const response = await authService.refreshToken(\n                        currentState.refreshToken,\n                      );\n\n                      // Update tokens\n                      set(\n                        state => ({\n                          ...state,\n                          authToken: response.access,\n                          tokenExpiresAt: Date.now() + 30 * 60 * 1000,\n                        }),\n                        false,\n                        'auth/tokenRefreshed',\n                      );\n\n                      // Update AsyncStorage\n                      await AsyncStorage.setItem('auth_token', response.access);\n\n                      return true;\n                    } catch (refreshError) {\n                      console.error('Token refresh failed:', refreshError);\n                      set(() => initialState, false, 'auth/tokenExpired');\n                      await AsyncStorage.multiRemove([\n                        'auth_token',\n                        'refresh_token',\n                        'auth_user',\n                      ]);\n                      return false;\n                    }\n                  } else {\n                    set(() => initialState, false, 'auth/tokenExpired');\n                    await AsyncStorage.multiRemove([\n                      'auth_token',\n                      'refresh_token',\n                      'auth_user',\n                    ]);\n                    return false;\n                  }\n                }\n\n                return true;\n              } catch (error) {\n                console.error('Token validation failed:', error);\n                return false;\n              }\n            },\n          }),\n          {\n            name: 'auth-store',\n            storage: {\n              getItem: async (name: string) => {\n                try {\n                  const value = await AsyncStorage.getItem(name);\n                  if (value) {\n                    const parsed = JSON.parse(value);\n                    // Check if the stored data has the old loginSuccess signature\n                    // If so, clear it to prevent errors\n                    if (\n                      parsed &&\n                      typeof parsed === 'object' &&\n                      'loginSuccess' in parsed\n                    ) {\n                      console.log(\n                        '🔄 Clearing old auth store data due to structure change',\n                      );\n                      await AsyncStorage.removeItem(name);\n                      return null;\n                    }\n                    return parsed;\n                  }\n                  return null;\n                } catch (error) {\n                  console.error('Failed to load auth state:', error);\n                  // Clear corrupted data\n                  try {\n                    await AsyncStorage.removeItem(name);\n                  } catch {}\n                  return null;\n                }\n              },\n              setItem: async (name: string, value: any) => {\n                try {\n                  await AsyncStorage.setItem(name, JSON.stringify(value));\n                } catch (error) {\n                  console.error('Failed to save auth state:', error);\n                }\n              },\n              removeItem: async (name: string) => {\n                try {\n                  await AsyncStorage.removeItem(name);\n                } catch (error) {\n                  console.error('Failed to remove auth state:', error);\n                }\n              },\n            },\n            partialize: state => ({\n              authToken: state.authToken,\n              refreshToken: state.refreshToken,\n              user: state.user,\n              userRole: state.userRole,\n              isAuthenticated: state.isAuthenticated,\n              tokenExpiresAt: state.tokenExpiresAt,\n              // Add version to track store structure changes\n              _version: '2.0.0',\n            }),\n            // Add migration logic for store structure changes\n            migrate: (persistedState: any, version: number) => {\n              // If no version or old version, reset the store\n              if (\n                !persistedState ||\n                !persistedState._version ||\n                persistedState._version !== '2.0.0'\n              ) {\n                console.log(\n                  '🔄 Migrating auth store to new version, clearing old data',\n                );\n                return initialState;\n              }\n              return persistedState;\n            },\n            version: 1,\n          },\n          {\n            name: 'auth-store',\n          },\n        ),\n      ),\n    );\n  } catch (error) {\n    console.error('Failed to create auth store:', error);\n    // Return a minimal fallback store\n    return create<AuthState>()(() => ({\n      ...initialState,\n      isAuthenticated: false,\n      loginStart: () => {},\n      loginSuccess: () => {},\n      loginFailure: () => {},\n      registerStart: () => {},\n      registerSuccess: () => {},\n      registerFailure: () => {},\n      updateProfile: () => {},\n      updateTokens: () => {},\n      logout: () => {},\n      reset: () => {},\n      checkAuthStatus: async () => {},\n      validateToken: async () => false,\n    }));\n  }\n};\n\nexport const useAuthStore = createAuthStore();\n\n// Safe hook for accessing auth store with error handling\nexport const useSafeAuthStore = () => {\n  try {\n    const store = useAuthStore();\n    // Ensure all required properties exist\n    if (!store || typeof store !== 'object') {\n      console.warn('Auth store is not properly initialized');\n      return {\n        ...initialState,\n        isAuthenticated: false,\n        loginStart: () => {},\n        loginSuccess: () => {},\n        loginFailure: () => {},\n        registerStart: () => {},\n        registerSuccess: () => {},\n        registerFailure: () => {},\n        updateProfile: () => {},\n        updateTokens: () => {},\n        logout: () => {},\n        reset: () => {},\n        checkAuthStatus: async () => {},\n        validateToken: async () => false,\n      };\n    }\n    return store;\n  } catch (error) {\n    console.error('Error accessing auth store:', error);\n    return {\n      ...initialState,\n      isAuthenticated: false,\n      loginStart: () => {},\n      loginSuccess: () => {},\n      loginFailure: () => {},\n      registerStart: () => {},\n      registerSuccess: () => {},\n      registerFailure: () => {},\n      updateProfile: () => {},\n      updateTokens: () => {},\n      logout: () => {},\n      reset: () => {},\n      checkAuthStatus: async () => {},\n      validateToken: async () => false,\n    };\n  }\n};\n"], "mappings": ";;;;;;AAeA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAF,OAAA;AAkDA,IAAMG,YAAY,GAAG;EACnBC,SAAS,EAAE,IAAI;EACfC,YAAY,EAAE,IAAI;EAClBC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,MAAoB;EAC5BC,KAAK,EAAE,IAAI;EACXC,cAAc,EAAE,IAAI;EACpBC,eAAe,EAAE;AACnB,CAAC;AAGD,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;EAC5B,IAAI;IACF,OAAO,IAAAC,eAAM,EAAY,CAAC,CACxB,IAAAC,oBAAQ,EACN,IAAAC,mBAAO,EACL,UAACC,GAAG,EAAEC,GAAG;MAAA,OAAAC,MAAA,CAAAC,MAAA,KAEJhB,YAAY;QAGfQ,eAAe,EAAE,KAAK;QAGtBS,UAAU,EAAE,SAAZA,UAAUA,CAAA;UAAA,OACRJ,GAAG,CACD,UAAAK,KAAK;YAAA,OAAAH,MAAA,CAAAC,MAAA,KACAE,KAAK;cACRb,MAAM,EAAE,SAAS;cACjBC,KAAK,EAAE;YAAI;UAAA,CACX,EACF,KAAK,EACL,iBACF,CAAC;QAAA;QAEHa,YAAY,EAAE,SAAdA,YAAYA,CAAGC,KAAa,EAAElB,YAAoB,EAAEC,IAAU;UAAA,OAC5DU,GAAG,CACD,UAAAK,KAAK;YAAA,OAAAH,MAAA,CAAAC,MAAA,KACAE,KAAK;cACRjB,SAAS,EAAEmB,KAAK;cAChBlB,YAAY,EAAZA,YAAY;cACZC,IAAI,EAAJA,IAAI;cACJC,QAAQ,EAAED,IAAI,CAACkB,IAAI;cACnBhB,MAAM,EAAE,SAAS;cACjBC,KAAK,EAAE,IAAI;cACXE,eAAe,EAAE,IAAI;cACrBD,cAAc,EAAEe,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;YAAI;UAAA,CAC3C,EACF,KAAK,EACL,mBACF,CAAC;QAAA;QAEHC,YAAY,EAAE,SAAdA,YAAYA,CAAGlB,KAAa;UAAA,OAC1BO,GAAG,CACD,UAAAK,KAAK;YAAA,OAAAH,MAAA,CAAAC,MAAA,KACAE,KAAK;cACRjB,SAAS,EAAE,IAAI;cACfG,QAAQ,EAAE,IAAI;cACdC,MAAM,EAAE,OAAO;cACfC,KAAK,EAALA,KAAK;cACLE,eAAe,EAAE;YAAK;UAAA,CACtB,EACF,KAAK,EACL,mBACF,CAAC;QAAA;QAGHiB,aAAa,EAAE,SAAfA,aAAaA,CAAA;UAAA,OACXZ,GAAG,CACD,UAAAK,KAAK;YAAA,OAAAH,MAAA,CAAAC,MAAA,KACAE,KAAK;cACRb,MAAM,EAAE,SAAS;cACjBC,KAAK,EAAE;YAAI;UAAA,CACX,EACF,KAAK,EACL,oBACF,CAAC;QAAA;QAEHoB,eAAe,EAAE,SAAjBA,eAAeA,CACbN,KAAa,EACblB,YAAoB,EACpBC,IAAU;UAAA,OAEVU,GAAG,CACD,UAAAK,KAAK;YAAA,OAAAH,MAAA,CAAAC,MAAA,KACAE,KAAK;cACRjB,SAAS,EAAEmB,KAAK;cAChBlB,YAAY,EAAZA,YAAY;cACZC,IAAI,EAAJA,IAAI;cACJC,QAAQ,EAAED,IAAI,CAACkB,IAAI;cACnBhB,MAAM,EAAE,SAAS;cACjBC,KAAK,EAAE,IAAI;cACXE,eAAe,EAAE,IAAI;cACrBD,cAAc,EAAEe,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;YAAI;UAAA,CAC3C,EACF,KAAK,EACL,sBACF,CAAC;QAAA;QAEHI,eAAe,EAAE,SAAjBA,eAAeA,CAAGrB,KAAa;UAAA,OAC7BO,GAAG,CACD,UAAAK,KAAK;YAAA,OAAAH,MAAA,CAAAC,MAAA,KACAE,KAAK;cACRjB,SAAS,EAAE,IAAI;cACfC,YAAY,EAAE,IAAI;cAClBC,IAAI,EAAE,IAAI;cACVC,QAAQ,EAAE,IAAI;cACdC,MAAM,EAAE,OAAO;cACfC,KAAK,EAALA,KAAK;cACLE,eAAe,EAAE,KAAK;cACtBD,cAAc,EAAE;YAAI;UAAA,CACpB,EACF,KAAK,EACL,sBACF,CAAC;QAAA;QAGHqB,aAAa,EAAE,SAAfA,aAAaA,CAAGC,WAA0B;UAAA,OACxChB,GAAG,CACD,UAAAK,KAAK;YAAA,OAAAH,MAAA,CAAAC,MAAA,KACAE,KAAK;cACRf,IAAI,EAAEe,KAAK,CAACf,IAAI,GAAAY,MAAA,CAAAC,MAAA,KAAQE,KAAK,CAACf,IAAI,EAAK0B,WAAW,IAAK;YAAI;UAAA,CAC3D,EACF,KAAK,EACL,oBACF,CAAC;QAAA;QAGHC,YAAY,EAAE,SAAdA,YAAYA,CAAGV,KAAa,EAAElB,YAAqB;UAAA,OACjDW,GAAG,CACD,UAAAK,KAAK;YAAA,OAAAH,MAAA,CAAAC,MAAA,KACAE,KAAK;cACRjB,SAAS,EAAEmB,KAAK;cAChBlB,YAAY,EAAEA,YAAY,IAAIgB,KAAK,CAAChB,YAAY;cAChDK,cAAc,EAAEe,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;YAAI;UAAA,CAC3C,EACF,KAAK,EACL,mBACF,CAAC;QAAA;QAGHQ,MAAM,EAAE,SAARA,MAAMA,CAAA;UAAA,OACJlB,GAAG,CACD;YAAA,OAAAE,MAAA,CAAAC,MAAA,KACKhB,YAAY;UAAA,CACf,EACF,KAAK,EACL,aACF,CAAC;QAAA;QAGHgC,KAAK,EAAE,SAAPA,KAAKA,CAAA;UAAA,OACHnB,GAAG,CACD;YAAA,OAAAE,MAAA,CAAAC,MAAA,KACKhB,YAAY;UAAA,CACf,EACF,KAAK,EACL,YACF,CAAC;QAAA;QAGHiC,eAAe;UAAA,IAAAC,gBAAA,OAAAC,kBAAA,CAAAC,OAAA,EAAE,aAAY;YAC3B,IAAI;cACF,IAAMC,YAAY,GAAGvB,GAAG,CAAC,CAAC;cAG1B,IACEuB,YAAY,CAAC7B,eAAe,IAC5B6B,YAAY,CAACpC,SAAS,IACtBoC,YAAY,CAAC9B,cAAc,EAC3B;gBACA,IAAIe,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGc,YAAY,CAAC9B,cAAc,EAAE;kBAC5C;gBACF;cACF;cAGA,IAAM+B,WAAW,SAASC,qBAAY,CAACC,OAAO,CAAC,YAAY,CAAC;cAC5D,IAAMC,kBAAkB,SAChBF,qBAAY,CAACC,OAAO,CAAC,eAAe,CAAC;cAC7C,IAAME,UAAU,SAASH,qBAAY,CAACC,OAAO,CAAC,WAAW,CAAC;cAE1D,IAAIF,WAAW,IAAII,UAAU,EAAE;gBAC7B,IAAI;kBACF,IAAMvC,IAAI,GAAGwC,IAAI,CAACC,KAAK,CAACF,UAAU,CAAC;kBACnC7B,GAAG,CACD,UAAAK,KAAK;oBAAA,OAAAH,MAAA,CAAAC,MAAA,KACAE,KAAK;sBACRjB,SAAS,EAAEqC,WAAW;sBACtBpC,YAAY,EAAEuC,kBAAkB;sBAChCtC,IAAI,EAAJA,IAAI;sBACJC,QAAQ,EAAED,IAAI,CAACkB,IAAI;sBACnBb,eAAe,EAAE,IAAI;sBACrBH,MAAM,EAAE,SAAS;sBACjBC,KAAK,EAAE;oBAAI;kBAAA,CACX,EACF,KAAK,EACL,sBACF,CAAC;gBACH,CAAC,CAAC,OAAOuC,UAAU,EAAE;kBACnBC,OAAO,CAACxC,KAAK,CACX,mCAAmC,EACnCuC,UACF,CAAC;kBACD,MAAMN,qBAAY,CAACQ,WAAW,CAAC,CAC7B,YAAY,EACZ,eAAe,EACf,WAAW,CACZ,CAAC;kBACFlC,GAAG,CAAC;oBAAA,OAAMb,YAAY;kBAAA,GAAE,KAAK,EAAE,2BAA2B,CAAC;gBAC7D;cACF,CAAC,MAAM;gBACLa,GAAG,CAAC;kBAAA,OAAMb,YAAY;gBAAA,GAAE,KAAK,EAAE,sBAAsB,CAAC;cACxD;YACF,CAAC,CAAC,OAAOM,KAAK,EAAE;cACdwC,OAAO,CAACxC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;cACjDO,GAAG,CAAC;gBAAA,OAAMb,YAAY;cAAA,GAAE,KAAK,EAAE,2BAA2B,CAAC;YAC7D;UACF,CAAC;UAAA,SAzDDiC,eAAeA,CAAA;YAAA,OAAAC,gBAAA,CAAAc,KAAA,OAAAC,SAAA;UAAA;UAAA,OAAfhB,eAAe;QAAA,GAyDd;QAGDiB,aAAa;UAAA,IAAAC,cAAA,OAAAhB,kBAAA,CAAAC,OAAA,EAAE,aAA8B;YAC3C,IAAI;cACF,IAAMC,YAAY,GAAGvB,GAAG,CAAC,CAAC;cAE1B,IAAI,CAACuB,YAAY,CAACpC,SAAS,EAAE;gBAC3B,OAAO,KAAK;cACd;cAGA,IACEoC,YAAY,CAAC9B,cAAc,IAC3Be,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIc,YAAY,CAAC9B,cAAc,EACzC;gBAEA,IAAI8B,YAAY,CAACnC,YAAY,EAAE;kBAC7B,IAAI;oBAEF,IAAAkD,aAAA,SAA8B,MAAM,0BAEpC,CAAC;sBAFOC,WAAW,GAAAD,aAAA,CAAXC,WAAW;oBAGnB,IAAMC,QAAQ,SAASD,WAAW,CAACnD,YAAY,CAC7CmC,YAAY,CAACnC,YACf,CAAC;oBAGDW,GAAG,CACD,UAAAK,KAAK;sBAAA,OAAAH,MAAA,CAAAC,MAAA,KACAE,KAAK;wBACRjB,SAAS,EAAEqD,QAAQ,CAACC,MAAM;wBAC1BhD,cAAc,EAAEe,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;sBAAI;oBAAA,CAC3C,EACF,KAAK,EACL,qBACF,CAAC;oBAGD,MAAMgB,qBAAY,CAACiB,OAAO,CAAC,YAAY,EAAEF,QAAQ,CAACC,MAAM,CAAC;oBAEzD,OAAO,IAAI;kBACb,CAAC,CAAC,OAAOE,YAAY,EAAE;oBACrBX,OAAO,CAACxC,KAAK,CAAC,uBAAuB,EAAEmD,YAAY,CAAC;oBACpD5C,GAAG,CAAC;sBAAA,OAAMb,YAAY;oBAAA,GAAE,KAAK,EAAE,mBAAmB,CAAC;oBACnD,MAAMuC,qBAAY,CAACQ,WAAW,CAAC,CAC7B,YAAY,EACZ,eAAe,EACf,WAAW,CACZ,CAAC;oBACF,OAAO,KAAK;kBACd;gBACF,CAAC,MAAM;kBACLlC,GAAG,CAAC;oBAAA,OAAMb,YAAY;kBAAA,GAAE,KAAK,EAAE,mBAAmB,CAAC;kBACnD,MAAMuC,qBAAY,CAACQ,WAAW,CAAC,CAC7B,YAAY,EACZ,eAAe,EACf,WAAW,CACZ,CAAC;kBACF,OAAO,KAAK;gBACd;cACF;cAEA,OAAO,IAAI;YACb,CAAC,CAAC,OAAOzC,KAAK,EAAE;cACdwC,OAAO,CAACxC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;cAChD,OAAO,KAAK;YACd;UACF,CAAC;UAAA,SAjED4C,aAAaA,CAAA;YAAA,OAAAC,cAAA,CAAAH,KAAA,OAAAC,SAAA;UAAA;UAAA,OAAbC,aAAa;QAAA;MAiEZ;IAAA,CACD,EACF;MACEQ,IAAI,EAAE,YAAY;MAClBC,OAAO,EAAE;QACPnB,OAAO;UAAA,IAAAoB,QAAA,OAAAzB,kBAAA,CAAAC,OAAA,EAAE,WAAOsB,IAAY,EAAK;YAC/B,IAAI;cACF,IAAMG,KAAK,SAAStB,qBAAY,CAACC,OAAO,CAACkB,IAAI,CAAC;cAC9C,IAAIG,KAAK,EAAE;gBACT,IAAMC,MAAM,GAAGnB,IAAI,CAACC,KAAK,CAACiB,KAAK,CAAC;gBAGhC,IACEC,MAAM,IACN,OAAOA,MAAM,KAAK,QAAQ,IAC1B,cAAc,IAAIA,MAAM,EACxB;kBACAhB,OAAO,CAACiB,GAAG,CACT,yDACF,CAAC;kBACD,MAAMxB,qBAAY,CAACyB,UAAU,CAACN,IAAI,CAAC;kBACnC,OAAO,IAAI;gBACb;gBACA,OAAOI,MAAM;cACf;cACA,OAAO,IAAI;YACb,CAAC,CAAC,OAAOxD,KAAK,EAAE;cACdwC,OAAO,CAACxC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;cAElD,IAAI;gBACF,MAAMiC,qBAAY,CAACyB,UAAU,CAACN,IAAI,CAAC;cACrC,CAAC,CAAC,OAAAO,OAAA,EAAM,CAAC;cACT,OAAO,IAAI;YACb;UACF,CAAC;UAAA,SA7BDzB,OAAOA,CAAA0B,EAAA;YAAA,OAAAN,QAAA,CAAAZ,KAAA,OAAAC,SAAA;UAAA;UAAA,OAAPT,OAAO;QAAA,GA6BN;QACDgB,OAAO;UAAA,IAAAW,QAAA,OAAAhC,kBAAA,CAAAC,OAAA,EAAE,WAAOsB,IAAY,EAAEG,KAAU,EAAK;YAC3C,IAAI;cACF,MAAMtB,qBAAY,CAACiB,OAAO,CAACE,IAAI,EAAEf,IAAI,CAACyB,SAAS,CAACP,KAAK,CAAC,CAAC;YACzD,CAAC,CAAC,OAAOvD,KAAK,EAAE;cACdwC,OAAO,CAACxC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;YACpD;UACF,CAAC;UAAA,SANDkD,OAAOA,CAAAa,GAAA,EAAAC,GAAA;YAAA,OAAAH,QAAA,CAAAnB,KAAA,OAAAC,SAAA;UAAA;UAAA,OAAPO,OAAO;QAAA,GAMN;QACDQ,UAAU;UAAA,IAAAO,WAAA,OAAApC,kBAAA,CAAAC,OAAA,EAAE,WAAOsB,IAAY,EAAK;YAClC,IAAI;cACF,MAAMnB,qBAAY,CAACyB,UAAU,CAACN,IAAI,CAAC;YACrC,CAAC,CAAC,OAAOpD,KAAK,EAAE;cACdwC,OAAO,CAACxC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;YACtD;UACF,CAAC;UAAA,SAND0D,UAAUA,CAAAQ,GAAA;YAAA,OAAAD,WAAA,CAAAvB,KAAA,OAAAC,SAAA;UAAA;UAAA,OAAVe,UAAU;QAAA;MAOZ,CAAC;MACDS,UAAU,EAAE,SAAZA,UAAUA,CAAEvD,KAAK;QAAA,OAAK;UACpBjB,SAAS,EAAEiB,KAAK,CAACjB,SAAS;UAC1BC,YAAY,EAAEgB,KAAK,CAAChB,YAAY;UAChCC,IAAI,EAAEe,KAAK,CAACf,IAAI;UAChBC,QAAQ,EAAEc,KAAK,CAACd,QAAQ;UACxBI,eAAe,EAAEU,KAAK,CAACV,eAAe;UACtCD,cAAc,EAAEW,KAAK,CAACX,cAAc;UAEpCmE,QAAQ,EAAE;QACZ,CAAC;MAAA,CAAC;MAEFC,OAAO,EAAE,SAATA,OAAOA,CAAGC,cAAmB,EAAEC,OAAe,EAAK;QAEjD,IACE,CAACD,cAAc,IACf,CAACA,cAAc,CAACF,QAAQ,IACxBE,cAAc,CAACF,QAAQ,KAAK,OAAO,EACnC;UACA5B,OAAO,CAACiB,GAAG,CACT,2DACF,CAAC;UACD,OAAO/D,YAAY;QACrB;QACA,OAAO4E,cAAc;MACvB,CAAC;MACDC,OAAO,EAAE;IACX,CAAC,EACD;MACEnB,IAAI,EAAE;IACR,CACF,CACF,CACF,CAAC;EACH,CAAC,CAAC,OAAOpD,KAAK,EAAE;IACdwC,OAAO,CAACxC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IAEpD,OAAO,IAAAI,eAAM,EAAY,CAAC,CAAC;MAAA,OAAAK,MAAA,CAAAC,MAAA,KACtBhB,YAAY;QACfQ,eAAe,EAAE,KAAK;QACtBS,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAQ,CAAC,CAAC;QACpBE,YAAY,EAAE,SAAdA,YAAYA,CAAA,EAAQ,CAAC,CAAC;QACtBK,YAAY,EAAE,SAAdA,YAAYA,CAAA,EAAQ,CAAC,CAAC;QACtBC,aAAa,EAAE,SAAfA,aAAaA,CAAA,EAAQ,CAAC,CAAC;QACvBC,eAAe,EAAE,SAAjBA,eAAeA,CAAA,EAAQ,CAAC,CAAC;QACzBC,eAAe,EAAE,SAAjBA,eAAeA,CAAA,EAAQ,CAAC,CAAC;QACzBC,aAAa,EAAE,SAAfA,aAAaA,CAAA,EAAQ,CAAC,CAAC;QACvBE,YAAY,EAAE,SAAdA,YAAYA,CAAA,EAAQ,CAAC,CAAC;QACtBC,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ,CAAC,CAAC;QAChBC,KAAK,EAAE,SAAPA,KAAKA,CAAA,EAAQ,CAAC,CAAC;QACfC,eAAe;UAAA,IAAA6C,iBAAA,OAAA3C,kBAAA,CAAAC,OAAA,EAAE,aAAY,CAAC,CAAC;UAAA,SAA/BH,eAAeA,CAAA;YAAA,OAAA6C,iBAAA,CAAA9B,KAAA,OAAAC,SAAA;UAAA;UAAA,OAAfhB,eAAe;QAAA,GAAgB;QAC/BiB,aAAa;UAAA,IAAA6B,eAAA,OAAA5C,kBAAA,CAAAC,OAAA,EAAE;YAAA,OAAY,KAAK;UAAA;UAAA,SAAhCc,aAAaA,CAAA;YAAA,OAAA6B,eAAA,CAAA/B,KAAA,OAAAC,SAAA;UAAA;UAAA,OAAbC,aAAa;QAAA;MAAmB;IAAA,CAChC,CAAC;EACL;AACF,CAAC;AAEM,IAAM8B,YAAY,GAAAC,OAAA,CAAAD,YAAA,GAAGvE,eAAe,CAAC,CAAC;AAGtC,IAAMyE,gBAAgB,GAAAD,OAAA,CAAAC,gBAAA,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;EACpC,IAAI;IACF,IAAMC,KAAK,GAAGH,YAAY,CAAC,CAAC;IAE5B,IAAI,CAACG,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MACvCrC,OAAO,CAACsC,IAAI,CAAC,wCAAwC,CAAC;MACtD,OAAArE,MAAA,CAAAC,MAAA,KACKhB,YAAY;QACfQ,eAAe,EAAE,KAAK;QACtBS,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAQ,CAAC,CAAC;QACpBE,YAAY,EAAE,SAAdA,YAAYA,CAAA,EAAQ,CAAC,CAAC;QACtBK,YAAY,EAAE,SAAdA,YAAYA,CAAA,EAAQ,CAAC,CAAC;QACtBC,aAAa,EAAE,SAAfA,aAAaA,CAAA,EAAQ,CAAC,CAAC;QACvBC,eAAe,EAAE,SAAjBA,eAAeA,CAAA,EAAQ,CAAC,CAAC;QACzBC,eAAe,EAAE,SAAjBA,eAAeA,CAAA,EAAQ,CAAC,CAAC;QACzBC,aAAa,EAAE,SAAfA,aAAaA,CAAA,EAAQ,CAAC,CAAC;QACvBE,YAAY,EAAE,SAAdA,YAAYA,CAAA,EAAQ,CAAC,CAAC;QACtBC,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ,CAAC,CAAC;QAChBC,KAAK,EAAE,SAAPA,KAAKA,CAAA,EAAQ,CAAC,CAAC;QACfC,eAAe;UAAA,IAAAoD,iBAAA,OAAAlD,kBAAA,CAAAC,OAAA,EAAE,aAAY,CAAC,CAAC;UAAA,SAA/BH,eAAeA,CAAA;YAAA,OAAAoD,iBAAA,CAAArC,KAAA,OAAAC,SAAA;UAAA;UAAA,OAAfhB,eAAe;QAAA,GAAgB;QAC/BiB,aAAa;UAAA,IAAAoC,eAAA,OAAAnD,kBAAA,CAAAC,OAAA,EAAE;YAAA,OAAY,KAAK;UAAA;UAAA,SAAhCc,aAAaA,CAAA;YAAA,OAAAoC,eAAA,CAAAtC,KAAA,OAAAC,SAAA;UAAA;UAAA,OAAbC,aAAa;QAAA;MAAmB;IAEpC;IACA,OAAOiC,KAAK;EACd,CAAC,CAAC,OAAO7E,KAAK,EAAE;IACdwC,OAAO,CAACxC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnD,OAAAS,MAAA,CAAAC,MAAA,KACKhB,YAAY;MACfQ,eAAe,EAAE,KAAK;MACtBS,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAQ,CAAC,CAAC;MACpBE,YAAY,EAAE,SAAdA,YAAYA,CAAA,EAAQ,CAAC,CAAC;MACtBK,YAAY,EAAE,SAAdA,YAAYA,CAAA,EAAQ,CAAC,CAAC;MACtBC,aAAa,EAAE,SAAfA,aAAaA,CAAA,EAAQ,CAAC,CAAC;MACvBC,eAAe,EAAE,SAAjBA,eAAeA,CAAA,EAAQ,CAAC,CAAC;MACzBC,eAAe,EAAE,SAAjBA,eAAeA,CAAA,EAAQ,CAAC,CAAC;MACzBC,aAAa,EAAE,SAAfA,aAAaA,CAAA,EAAQ,CAAC,CAAC;MACvBE,YAAY,EAAE,SAAdA,YAAYA,CAAA,EAAQ,CAAC,CAAC;MACtBC,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ,CAAC,CAAC;MAChBC,KAAK,EAAE,SAAPA,KAAKA,CAAA,EAAQ,CAAC,CAAC;MACfC,eAAe;QAAA,IAAAsD,iBAAA,OAAApD,kBAAA,CAAAC,OAAA,EAAE,aAAY,CAAC,CAAC;QAAA,SAA/BH,eAAeA,CAAA;UAAA,OAAAsD,iBAAA,CAAAvC,KAAA,OAAAC,SAAA;QAAA;QAAA,OAAfhB,eAAe;MAAA,GAAgB;MAC/BiB,aAAa;QAAA,IAAAsC,eAAA,OAAArD,kBAAA,CAAAC,OAAA,EAAE;UAAA,OAAY,KAAK;QAAA;QAAA,SAAhCc,aAAaA,CAAA;UAAA,OAAAsC,eAAA,CAAAxC,KAAA,OAAAC,SAAA;QAAA;QAAA,OAAbC,aAAa;MAAA;IAAmB;EAEpC;AACF,CAAC", "ignoreList": []}