d64b33ad2855d6fcf5bd671c753c12d5
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _AnimatedValue = _interopRequireDefault(require("./AnimatedValue"));
var _AnimatedWithChildren2 = _interopRequireDefault(require("./AnimatedWithChildren"));
var _invariant = _interopRequireDefault(require("invariant"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var _uniqueId = 1;
var AnimatedValueXY = exports.default = function (_AnimatedWithChildren) {
  function AnimatedValueXY(valueIn, config) {
    var _this;
    (0, _classCallCheck2.default)(this, AnimatedValueXY);
    _this = _callSuper(this, AnimatedValueXY, [config]);
    var value = valueIn || {
      x: 0,
      y: 0
    };
    if (typeof value.x === 'number' && typeof value.y === 'number') {
      _this.x = new _AnimatedValue.default(value.x);
      _this.y = new _AnimatedValue.default(value.y);
    } else {
      (0, _invariant.default)(value.x instanceof _AnimatedValue.default && value.y instanceof _AnimatedValue.default, 'AnimatedValueXY must be initialized with an object of numbers or ' + 'AnimatedValues.');
      _this.x = value.x;
      _this.y = value.y;
    }
    _this._listeners = {};
    if (config && config.useNativeDriver) {
      _this.__makeNative();
    }
    return _this;
  }
  (0, _inherits2.default)(AnimatedValueXY, _AnimatedWithChildren);
  return (0, _createClass2.default)(AnimatedValueXY, [{
    key: "setValue",
    value: function setValue(value) {
      this.x.setValue(value.x);
      this.y.setValue(value.y);
    }
  }, {
    key: "setOffset",
    value: function setOffset(offset) {
      this.x.setOffset(offset.x);
      this.y.setOffset(offset.y);
    }
  }, {
    key: "flattenOffset",
    value: function flattenOffset() {
      this.x.flattenOffset();
      this.y.flattenOffset();
    }
  }, {
    key: "extractOffset",
    value: function extractOffset() {
      this.x.extractOffset();
      this.y.extractOffset();
    }
  }, {
    key: "__getValue",
    value: function __getValue() {
      return {
        x: this.x.__getValue(),
        y: this.y.__getValue()
      };
    }
  }, {
    key: "resetAnimation",
    value: function resetAnimation(callback) {
      this.x.resetAnimation();
      this.y.resetAnimation();
      callback && callback(this.__getValue());
    }
  }, {
    key: "stopAnimation",
    value: function stopAnimation(callback) {
      this.x.stopAnimation();
      this.y.stopAnimation();
      callback && callback(this.__getValue());
    }
  }, {
    key: "addListener",
    value: function addListener(callback) {
      var _this2 = this;
      var id = String(_uniqueId++);
      var jointCallback = function jointCallback(_ref) {
        var number = _ref.value;
        callback(_this2.__getValue());
      };
      this._listeners[id] = {
        x: this.x.addListener(jointCallback),
        y: this.y.addListener(jointCallback)
      };
      return id;
    }
  }, {
    key: "removeListener",
    value: function removeListener(id) {
      this.x.removeListener(this._listeners[id].x);
      this.y.removeListener(this._listeners[id].y);
      delete this._listeners[id];
    }
  }, {
    key: "removeAllListeners",
    value: function removeAllListeners() {
      this.x.removeAllListeners();
      this.y.removeAllListeners();
      this._listeners = {};
    }
  }, {
    key: "getLayout",
    value: function getLayout() {
      return {
        left: this.x,
        top: this.y
      };
    }
  }, {
    key: "getTranslateTransform",
    value: function getTranslateTransform() {
      return [{
        translateX: this.x
      }, {
        translateY: this.y
      }];
    }
  }, {
    key: "__attach",
    value: function __attach() {
      this.x.__addChild(this);
      this.y.__addChild(this);
      _superPropGet(AnimatedValueXY, "__attach", this, 3)([]);
    }
  }, {
    key: "__detach",
    value: function __detach() {
      this.x.__removeChild(this);
      this.y.__removeChild(this);
      _superPropGet(AnimatedValueXY, "__detach", this, 3)([]);
    }
  }, {
    key: "__makeNative",
    value: function __makeNative(platformConfig) {
      this.x.__makeNative(platformConfig);
      this.y.__makeNative(platformConfig);
      _superPropGet(AnimatedValueXY, "__makeNative", this, 3)([platformConfig]);
    }
  }]);
}(_AnimatedWithChildren2.default);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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