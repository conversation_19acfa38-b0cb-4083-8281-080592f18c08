94a6df19d39921c90d8b65d286a6f5ae
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _NativeAnimatedHelper = _interopRequireDefault(require("../../../src/private/animated/NativeAnimatedHelper"));
var _normalizeColor = _interopRequireDefault(require("../../StyleSheet/normalizeColor"));
var _PlatformColorValueTypes = require("../../StyleSheet/PlatformColorValueTypes");
var _AnimatedValue = _interopRequireWildcard(require("./AnimatedValue"));
var _AnimatedWithChildren2 = _interopRequireDefault(require("./AnimatedWithChildren"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var NativeAnimatedAPI = _NativeAnimatedHelper.default.API;
var defaultColor = {
  r: 0,
  g: 0,
  b: 0,
  a: 1.0
};
function processColor(color) {
  if (color === undefined || color === null) {
    return null;
  }
  if (isRgbaValue(color)) {
    return color;
  }
  var normalizedColor = (0, _normalizeColor.default)(color);
  if (normalizedColor === undefined || normalizedColor === null) {
    return null;
  }
  if (typeof normalizedColor === 'object') {
    var processedColorObj = (0, _PlatformColorValueTypes.processColorObject)(normalizedColor);
    if (processedColorObj != null) {
      return processedColorObj;
    }
  } else if (typeof normalizedColor === 'number') {
    var r = (normalizedColor & 0xff000000) >>> 24;
    var g = (normalizedColor & 0x00ff0000) >>> 16;
    var b = (normalizedColor & 0x0000ff00) >>> 8;
    var a = (normalizedColor & 0x000000ff) / 255;
    return {
      r: r,
      g: g,
      b: b,
      a: a
    };
  }
  return null;
}
function isRgbaValue(value) {
  return value && typeof value.r === 'number' && typeof value.g === 'number' && typeof value.b === 'number' && typeof value.a === 'number';
}
function isRgbaAnimatedValue(value) {
  return value && value.r instanceof _AnimatedValue.default && value.g instanceof _AnimatedValue.default && value.b instanceof _AnimatedValue.default && value.a instanceof _AnimatedValue.default;
}
var AnimatedColor = exports.default = function (_AnimatedWithChildren) {
  function AnimatedColor(valueIn, config) {
    var _this;
    (0, _classCallCheck2.default)(this, AnimatedColor);
    _this = _callSuper(this, AnimatedColor, [config]);
    _this._suspendCallbacks = 0;
    var value = valueIn != null ? valueIn : defaultColor;
    if (isRgbaAnimatedValue(value)) {
      var rgbaAnimatedValue = value;
      _this.r = rgbaAnimatedValue.r;
      _this.g = rgbaAnimatedValue.g;
      _this.b = rgbaAnimatedValue.b;
      _this.a = rgbaAnimatedValue.a;
    } else {
      var _processColor;
      var processedColor = (_processColor = processColor(value)) != null ? _processColor : defaultColor;
      var initColor = defaultColor;
      if (isRgbaValue(processedColor)) {
        initColor = processedColor;
      } else {
        _this.nativeColor = processedColor;
      }
      _this.r = new _AnimatedValue.default(initColor.r);
      _this.g = new _AnimatedValue.default(initColor.g);
      _this.b = new _AnimatedValue.default(initColor.b);
      _this.a = new _AnimatedValue.default(initColor.a);
    }
    if (config != null && config.useNativeDriver) {
      _this.__makeNative();
    }
    return _this;
  }
  (0, _inherits2.default)(AnimatedColor, _AnimatedWithChildren);
  return (0, _createClass2.default)(AnimatedColor, [{
    key: "setValue",
    value: function setValue(value) {
      var _processColor2,
        _this2 = this;
      var shouldUpdateNodeConfig = false;
      if (this.__isNative) {
        var nativeTag = this.__getNativeTag();
        NativeAnimatedAPI.setWaitingForIdentifier(nativeTag.toString());
      }
      var processedColor = (_processColor2 = processColor(value)) != null ? _processColor2 : defaultColor;
      this._withSuspendedCallbacks(function () {
        if (isRgbaValue(processedColor)) {
          var rgbaValue = processedColor;
          _this2.r.setValue(rgbaValue.r);
          _this2.g.setValue(rgbaValue.g);
          _this2.b.setValue(rgbaValue.b);
          _this2.a.setValue(rgbaValue.a);
          if (_this2.nativeColor != null) {
            _this2.nativeColor = null;
            shouldUpdateNodeConfig = true;
          }
        } else {
          var nativeColor = processedColor;
          if (_this2.nativeColor !== nativeColor) {
            _this2.nativeColor = nativeColor;
            shouldUpdateNodeConfig = true;
          }
        }
      });
      if (this.__isNative) {
        var _nativeTag = this.__getNativeTag();
        if (shouldUpdateNodeConfig) {
          NativeAnimatedAPI.updateAnimatedNodeConfig(_nativeTag, this.__getNativeConfig());
        }
        NativeAnimatedAPI.unsetWaitingForIdentifier(_nativeTag.toString());
      } else {
        (0, _AnimatedValue.flushValue)(this);
      }
      this.__callListeners(this.__getValue());
    }
  }, {
    key: "setOffset",
    value: function setOffset(offset) {
      this.r.setOffset(offset.r);
      this.g.setOffset(offset.g);
      this.b.setOffset(offset.b);
      this.a.setOffset(offset.a);
    }
  }, {
    key: "flattenOffset",
    value: function flattenOffset() {
      this.r.flattenOffset();
      this.g.flattenOffset();
      this.b.flattenOffset();
      this.a.flattenOffset();
    }
  }, {
    key: "extractOffset",
    value: function extractOffset() {
      this.r.extractOffset();
      this.g.extractOffset();
      this.b.extractOffset();
      this.a.extractOffset();
    }
  }, {
    key: "stopAnimation",
    value: function stopAnimation(callback) {
      this.r.stopAnimation();
      this.g.stopAnimation();
      this.b.stopAnimation();
      this.a.stopAnimation();
      callback && callback(this.__getValue());
    }
  }, {
    key: "resetAnimation",
    value: function resetAnimation(callback) {
      this.r.resetAnimation();
      this.g.resetAnimation();
      this.b.resetAnimation();
      this.a.resetAnimation();
      callback && callback(this.__getValue());
    }
  }, {
    key: "__getValue",
    value: function __getValue() {
      if (this.nativeColor != null) {
        return this.nativeColor;
      } else {
        return `rgba(${this.r.__getValue()}, ${this.g.__getValue()}, ${this.b.__getValue()}, ${this.a.__getValue()})`;
      }
    }
  }, {
    key: "__attach",
    value: function __attach() {
      this.r.__addChild(this);
      this.g.__addChild(this);
      this.b.__addChild(this);
      this.a.__addChild(this);
      _superPropGet(AnimatedColor, "__attach", this, 3)([]);
    }
  }, {
    key: "__detach",
    value: function __detach() {
      this.r.__removeChild(this);
      this.g.__removeChild(this);
      this.b.__removeChild(this);
      this.a.__removeChild(this);
      _superPropGet(AnimatedColor, "__detach", this, 3)([]);
    }
  }, {
    key: "_withSuspendedCallbacks",
    value: function _withSuspendedCallbacks(callback) {
      this._suspendCallbacks++;
      callback();
      this._suspendCallbacks--;
    }
  }, {
    key: "__callListeners",
    value: function __callListeners(value) {
      if (this._suspendCallbacks === 0) {
        _superPropGet(AnimatedColor, "__callListeners", this, 3)([value]);
      }
    }
  }, {
    key: "__makeNative",
    value: function __makeNative(platformConfig) {
      this.r.__makeNative(platformConfig);
      this.g.__makeNative(platformConfig);
      this.b.__makeNative(platformConfig);
      this.a.__makeNative(platformConfig);
      _superPropGet(AnimatedColor, "__makeNative", this, 3)([platformConfig]);
    }
  }, {
    key: "__getNativeConfig",
    value: function __getNativeConfig() {
      return {
        type: 'color',
        r: this.r.__getNativeTag(),
        g: this.g.__getNativeTag(),
        b: this.b.__getNativeTag(),
        a: this.a.__getNativeTag(),
        nativeColor: this.nativeColor,
        debugID: this.__getDebugID()
      };
    }
  }]);
}(_AnimatedWithChildren2.default);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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