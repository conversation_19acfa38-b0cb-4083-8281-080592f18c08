/**
 * Unified Button Component - Consolidates All Button Implementations
 *
 * Replaces: Button.tsx, StandardizedButton.tsx, MinimalistButton.tsx
 *
 * Features:
 * - Consistent API across all button variants
 * - Standardized terminology and accessibility
 * - Unified styling system using design tokens
 * - Comprehensive interaction patterns
 * - WCAG 2.2 AA compliance
 * - Haptic feedback integration
 * - Loading states and progress indicators
 * - Icon support with flexible positioning
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Ionicons } from '@expo/vector-icons';
import React, { useState, useRef } from 'react';
import {
  TouchableOpacity,
  Text,
  View,
  StyleSheet,
  ActivityIndicator,
  Animated,
  ViewStyle,
  TextStyle,
  AccessibilityRole,
} from 'react-native';

import { useTheme } from '../../contexts/ThemeContext';
import { HyperMinimalistTheme } from '../../design-system/HyperMinimalistTheme';
import { AccessibilityUtils } from '../../utils/accessibilityUtils';
import { HapticPatterns } from '../../utils/hapticPatterns';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../../utils/responsiveUtils';

// Unified button variants
export type ButtonVariant =
  | 'primary'
  | 'secondary'
  | 'outline'
  | 'ghost'
  | 'destructive'
  | 'success'
  | 'minimal';

// Unified button sizes
export type ButtonSize = 'small' | 'medium' | 'large';

// Icon position options
export type IconPosition = 'left' | 'right' | 'top' | 'bottom';

// Unified button props interface
export interface UnifiedButtonProps {
  // Core properties
  title?: string;
  children?: React.ReactNode;
  onPress: () => void;

  // Appearance
  variant?: ButtonVariant;
  size?: ButtonSize;

  // State management
  disabled?: boolean;
  loading?: boolean;

  // Icon system
  icon?: keyof typeof Ionicons.glyphMap;
  iconPosition?: IconPosition;
  iconOnly?: boolean;

  // Layout
  fullWidth?: boolean;

  // Styling
  style?: ViewStyle;
  textStyle?: TextStyle;

  // Accessibility
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityRole?: AccessibilityRole;

  // Testing
  testID?: string;

  // Interaction
  enableHaptics?: boolean;
  onLongPress?: () => void;
}

export const UnifiedButton: React.FC<UnifiedButtonProps> = ({
  title,
  children,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  iconOnly = false,
  fullWidth = false,
  style,
  textStyle,
  accessibilityLabel,
  accessibilityHint,
  accessibilityRole = 'button',
  testID,
  enableHaptics = true,
  onLongPress,
}) => {
  const { isDark, colors } = useTheme();

  const [isPressed, setIsPressed] = useState(false);
  const scaleAnim = useRef(new Animated.Value(1)).current;

  // Get button content
  const buttonContent = children || title;

  // Handle press with haptic feedback
  const handlePress = () => {
    if (disabled || loading) return;

    if (enableHaptics) {
      switch (variant) {
        case 'destructive':
          HapticPatterns.warningPress();
          break;
        case 'success':
          HapticPatterns.successPress();
          break;
        default:
          HapticPatterns.lightImpact();
      }
    }

    onPress();
  };

  // Handle press animation
  const handlePressIn = () => {
    setIsPressed(true);
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    setIsPressed(false);
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  // Get size configuration with WCAG 2.2 AA touch target compliance
  const getSizeConfig = () => {
    const configs = {
      small: {
        height: Math.max(36, 44), // Ensure minimum 44px for WCAG 2.2 AA
        paddingHorizontal: getResponsiveSpacing(12),
        fontSize: getResponsiveFontSize(14),
        iconSize: 16,
        minTouchTarget: 44,
      },
      medium: {
        height: 44, // WCAG 2.2 AA minimum touch target
        paddingHorizontal: getResponsiveSpacing(16),
        fontSize: getResponsiveFontSize(16),
        iconSize: 20,
        minTouchTarget: 44,
      },
      large: {
        height: 52, // Exceeds WCAG requirements
        paddingHorizontal: getResponsiveSpacing(20),
        fontSize: getResponsiveFontSize(18),
        iconSize: 24,
        minTouchTarget: 52,
      },
    };
    return configs[size];
  };

  // Get variant colors
  const getVariantColors = () => {
    const variants = {
      primary: {
        background: colors.primary?.default || '#5A7A63',
        text: colors.primary?.contrast || '#FFFFFF',
        border: colors.primary?.default || '#5A7A63',
      },
      secondary: {
        background: colors.background?.secondary || '#F9FAFB',
        text: colors.text?.primary || '#1F2937',
        border: colors.border?.light || '#E5E7EB',
      },
      outline: {
        background: 'transparent',
        text: colors.primary?.default || '#5A7A63',
        border: colors.primary?.default || '#5A7A63',
      },
      ghost: {
        background: 'transparent',
        text: colors.primary?.default || '#5A7A63',
        border: 'transparent',
      },
      destructive: {
        background: colors.error || '#EF4444',
        text: colors.primary?.contrast || '#FFFFFF',
        border: colors.error || '#EF4444',
      },
      success: {
        background: colors.success || '#10B981',
        text: colors.primary?.contrast || '#FFFFFF',
        border: colors.success || '#10B981',
      },
      minimal: {
        background: 'transparent',
        text: colors.text?.secondary || '#6B7280',
        border: 'transparent',
      },
    };
    return variants[variant];
  };

  const sizeConfig = getSizeConfig();
  const variantColors = getVariantColors();

  // Create styles with WCAG 2.2 AA touch target compliance
  const buttonStyles = [
    styles.base,
    {
      height: sizeConfig.height,
      paddingHorizontal: iconOnly
        ? sizeConfig.height / 2
        : sizeConfig.paddingHorizontal,
      backgroundColor: disabled ? colors.text?.tertiary || '#9CA3AF' : variantColors.background,
      borderColor: disabled ? colors.text?.tertiary || '#9CA3AF' : variantColors.border,
      borderWidth: variant === 'outline' ? 1 : 0,
      width: fullWidth ? '100%' : iconOnly ? sizeConfig.height : 'auto',
      minWidth: iconOnly
        ? Math.max(sizeConfig.height, sizeConfig.minTouchTarget)
        : sizeConfig.minTouchTarget,
      minHeight: sizeConfig.minTouchTarget, // Ensure minimum touch target
    },
    style,
  ];

  const textStyles = [
    styles.text,
    {
      fontSize: sizeConfig.fontSize,
      color: disabled ? colors.text?.secondary || '#6B7280' : variantColors.text,
    },
    textStyle,
  ];

  // Render icon
  const renderIcon = () => {
    if (!icon) return null;

    return (
      <Ionicons
        name={icon}
        size={sizeConfig.iconSize}
        color={disabled ? colors.text?.secondary || '#6B7280' : variantColors.text}
        style={[
          iconPosition === 'right' && !iconOnly && styles.iconRight,
          iconPosition === 'left' && !iconOnly && styles.iconLeft,
        ]}
      />
    );
  };

  // Render content based on layout
  const renderContent = () => {
    if (loading) {
      return (
        <ActivityIndicator
          size="small"
          color={disabled ? colors.text?.secondary || '#6B7280' : variantColors.text}
        />
      );
    }

    if (iconOnly) {
      return renderIcon();
    }

    const iconElement = renderIcon();
    const textElement = buttonContent ? (
      <Text style={textStyles} numberOfLines={1}>
        {buttonContent}
      </Text>
    ) : null;

    if (iconPosition === 'right') {
      return (
        <>
          {textElement}
          {iconElement}
        </>
      );
    }

    return (
      <>
        {iconElement}
        {textElement}
      </>
    );
  };

  return (
    <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
      <TouchableOpacity
        style={buttonStyles}
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        onLongPress={onLongPress}
        disabled={disabled || loading}
        activeOpacity={0.8}
        accessibilityRole={accessibilityRole}
        accessibilityLabel={accessibilityLabel || title}
        accessibilityHint={accessibilityHint}
        accessibilityState={{
          disabled: disabled || loading,
          busy: loading,
        }}
        testID={testID}>
        <View style={styles.content}>{renderContent()}</View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  base: {
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontWeight: '600',
    textAlign: 'center',
  },
  iconLeft: {
    marginRight: 8,
  },
  iconRight: {
    marginLeft: 8,
  },
});
