06748bde21e6b5802fbdcbefafdc940a
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.accessibilityTestUtils = void 0;
Object.defineProperty(exports, "act", {
  enumerable: true,
  get: function get() {
    return _reactNative.act;
  }
});
exports.createMockStore = exports.asyncTestUtils = exports.apiTestUtils = void 0;
Object.defineProperty(exports, "fireEvent", {
  enumerable: true,
  get: function get() {
    return _reactNative.fireEvent;
  }
});
exports.userInteractionTestUtils = exports.testDataFactories = exports.screenTestUtils = exports.renderWithProviders = exports.render = exports.performanceTestUtils = void 0;
Object.defineProperty(exports, "waitFor", {
  enumerable: true,
  get: function get() {
    return _reactNative.waitFor;
  }
});
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _native = require("@react-navigation/native");
var _toolkit = require("@reduxjs/toolkit");
var _reactNative = require("@testing-library/react-native");
var _react = _interopRequireDefault(require("react"));
var _reactRedux = require("react-redux");
var _AccessibilityComplianceSystem = require("../components/accessibility/AccessibilityComplianceSystem");
var _EnhancedErrorBoundary = require("../components/error/EnhancedErrorBoundary");
var _EnhancedToastSystem = require("../components/feedback/EnhancedToastSystem");
var _ThemeContext = require("../contexts/ThemeContext");
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["initialState", "store", "navigationOptions", "themeOptions", "accessibilityOptions"];
var createMockStore = exports.createMockStore = function createMockStore() {
  var initialState = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  return (0, _toolkit.configureStore)({
    reducer: Object.assign({
      auth: function auth() {
        var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {
          user: null,
          isAuthenticated: false
        };
        var action = arguments.length > 1 ? arguments[1] : undefined;
        return state;
      },
      providers: function providers() {
        var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {
          items: [],
          loading: false
        };
        var action = arguments.length > 1 ? arguments[1] : undefined;
        return state;
      },
      bookings: function bookings() {
        var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {
          items: [],
          loading: false
        };
        var action = arguments.length > 1 ? arguments[1] : undefined;
        return state;
      },
      messages: function messages() {
        var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {
          conversations: [],
          loading: false
        };
        var action = arguments.length > 1 ? arguments[1] : undefined;
        return state;
      }
    }, initialState),
    middleware: function middleware(getDefaultMiddleware) {
      return getDefaultMiddleware({
        serializableCheck: false
      });
    }
  });
};
var renderWithProviders = exports.render = exports.renderWithProviders = function renderWithProviders(ui) {
  var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var _ref$initialState = _ref.initialState,
    initialState = _ref$initialState === void 0 ? {} : _ref$initialState,
    _ref$store = _ref.store,
    store = _ref$store === void 0 ? createMockStore(initialState) : _ref$store,
    _ref$navigationOption = _ref.navigationOptions,
    navigationOptions = _ref$navigationOption === void 0 ? {} : _ref$navigationOption,
    _ref$themeOptions = _ref.themeOptions,
    themeOptions = _ref$themeOptions === void 0 ? {} : _ref$themeOptions,
    _ref$accessibilityOpt = _ref.accessibilityOptions,
    accessibilityOptions = _ref$accessibilityOpt === void 0 ? {} : _ref$accessibilityOpt,
    renderOptions = (0, _objectWithoutProperties2.default)(_ref, _excluded);
  var AllTheProviders = function AllTheProviders(_ref2) {
    var children = _ref2.children;
    return (0, _jsxRuntime.jsx)(_reactRedux.Provider, {
      store: store,
      children: (0, _jsxRuntime.jsx)(_native.NavigationContainer, Object.assign({}, navigationOptions, {
        children: (0, _jsxRuntime.jsx)(_ThemeContext.ThemeProvider, Object.assign({}, themeOptions, {
          children: (0, _jsxRuntime.jsx)(_AccessibilityComplianceSystem.AccessibilityComplianceSystem, Object.assign({}, accessibilityOptions, {
            children: (0, _jsxRuntime.jsx)(_EnhancedErrorBoundary.EnhancedErrorBoundary, {
              children: (0, _jsxRuntime.jsx)(_EnhancedToastSystem.EnhancedToastProvider, {
                children: children
              })
            })
          }))
        }))
      }))
    });
  };
  return (0, _reactNative.render)(ui, Object.assign({
    wrapper: AllTheProviders
  }, renderOptions));
};
var accessibilityTestUtils = exports.accessibilityTestUtils = {
  testScreenReaderAccessibility: function () {
    var _testScreenReaderAccessibility = (0, _asyncToGenerator2.default)(function* (component) {
      var _renderWithProviders = renderWithProviders(component),
        getAllByRole = _renderWithProviders.getAllByRole,
        getByLabelText = _renderWithProviders.getByLabelText;
      var buttons = getAllByRole('button');
      var headings = getAllByRole('header');
      var textInputs = getAllByRole('text');
      buttons.forEach(function (button) {
        expect(button.props.accessibilityLabel || button.props.children).toBeTruthy();
      });
      headings.forEach(function (heading) {
        expect(heading.props.accessibilityLabel || heading.props.children).toBeTruthy();
      });
      return {
        buttons: buttons.length,
        headings: headings.length,
        textInputs: textInputs.length
      };
    });
    function testScreenReaderAccessibility(_x) {
      return _testScreenReaderAccessibility.apply(this, arguments);
    }
    return testScreenReaderAccessibility;
  }(),
  testKeyboardNavigation: function () {
    var _testKeyboardNavigation = (0, _asyncToGenerator2.default)(function* (component) {
      var _renderWithProviders2 = renderWithProviders(component),
        getAllByRole = _renderWithProviders2.getAllByRole;
      var focusableElements = getAllByRole('button');
      for (var i = 0; i < focusableElements.length; i++) {
        (0, _reactNative.fireEvent)(focusableElements[i], 'focus');
        expect(focusableElements[i]).toHaveFocus();
      }
      return focusableElements.length;
    });
    function testKeyboardNavigation(_x2) {
      return _testKeyboardNavigation.apply(this, arguments);
    }
    return testKeyboardNavigation;
  }(),
  testTouchTargetSizes: function testTouchTargetSizes(component) {
    var _renderWithProviders3 = renderWithProviders(component),
      getAllByRole = _renderWithProviders3.getAllByRole;
    var touchableElements = getAllByRole('button');
    touchableElements.forEach(function (element) {
      var style = element.props.style || {};
      var minSize = 44;
      if (style.width && style.width < minSize) {
        console.warn(`Touch target too small: ${style.width}px width`);
      }
      if (style.height && style.height < minSize) {
        console.warn(`Touch target too small: ${style.height}px height`);
      }
    });
    return touchableElements.length;
  }
};
var performanceTestUtils = exports.performanceTestUtils = {
  measureRenderTime: function () {
    var _measureRenderTime = (0, _asyncToGenerator2.default)(function* (component) {
      var startTime = performance.now();
      renderWithProviders(component);
      var endTime = performance.now();
      return endTime - startTime;
    });
    function measureRenderTime(_x3) {
      return _measureRenderTime.apply(this, arguments);
    }
    return measureRenderTime;
  }(),
  testReRenderPerformance: function () {
    var _testReRenderPerformance = (0, _asyncToGenerator2.default)(function* (component, updates) {
      var _renderWithProviders4 = renderWithProviders(component),
        rerender = _renderWithProviders4.rerender;
      var renderTimes = [];
      for (var update of updates) {
        var startTime = performance.now();
        rerender(_react.default.cloneElement(component, update));
        var endTime = performance.now();
        renderTimes.push(endTime - startTime);
      }
      return {
        averageRenderTime: renderTimes.reduce(function (a, b) {
          return a + b;
        }, 0) / renderTimes.length,
        maxRenderTime: Math.max.apply(Math, renderTimes),
        minRenderTime: Math.min.apply(Math, renderTimes),
        renderTimes: renderTimes
      };
    });
    function testReRenderPerformance(_x4, _x5) {
      return _testReRenderPerformance.apply(this, arguments);
    }
    return testReRenderPerformance;
  }(),
  testMemoryUsage: function () {
    var _testMemoryUsage = (0, _asyncToGenerator2.default)(function* (component) {
      var _memory, _memory2, _memory3;
      var initialMemory = ((_memory = performance.memory) == null ? void 0 : _memory.usedJSHeapSize) || 0;
      var _renderWithProviders5 = renderWithProviders(component),
        unmount = _renderWithProviders5.unmount;
      var afterRenderMemory = ((_memory2 = performance.memory) == null ? void 0 : _memory2.usedJSHeapSize) || 0;
      unmount();
      var afterUnmountMemory = ((_memory3 = performance.memory) == null ? void 0 : _memory3.usedJSHeapSize) || 0;
      return {
        renderMemoryIncrease: afterRenderMemory - initialMemory,
        memoryLeakage: afterUnmountMemory - initialMemory
      };
    });
    function testMemoryUsage(_x6) {
      return _testMemoryUsage.apply(this, arguments);
    }
    return testMemoryUsage;
  }()
};
var userInteractionTestUtils = exports.userInteractionTestUtils = {
  simulateUserJourney: function () {
    var _simulateUserJourney = (0, _asyncToGenerator2.default)(function* (steps) {
      for (var step of steps) {
        switch (step.action) {
          case 'press':
            if (step.target) {
              var element = document.querySelector(`[data-testid="${step.target}"]`);
              if (element) {
                _reactNative.fireEvent.press(element);
              }
            }
            break;
          case 'type':
            if (step.target && step.value) {
              var _element = document.querySelector(`[data-testid="${step.target}"]`);
              if (_element) {
                _reactNative.fireEvent.changeText(_element, step.value);
              }
            }
            break;
          case 'wait':
            yield (0, _reactNative.waitFor)(function () {}, {
              timeout: step.duration || 1000
            });
            break;
        }
      }
    });
    function simulateUserJourney(_x7) {
      return _simulateUserJourney.apply(this, arguments);
    }
    return simulateUserJourney;
  }(),
  testFormInteractions: function () {
    var _testFormInteractions = (0, _asyncToGenerator2.default)(function* (component, formData) {
      var _renderWithProviders6 = renderWithProviders(component),
        getByTestId = _renderWithProviders6.getByTestId,
        getByRole = _renderWithProviders6.getByRole;
      for (var _ref3 of Object.entries(formData)) {
        var _ref4 = (0, _slicedToArray2.default)(_ref3, 2);
        var fieldName = _ref4[0];
        var value = _ref4[1];
        var field = getByTestId(fieldName);
        _reactNative.fireEvent.changeText(field, value);
        expect(field.props.value).toBe(value);
      }
      var submitButton = getByRole('button');
      _reactNative.fireEvent.press(submitButton);
      return true;
    });
    function testFormInteractions(_x8, _x9) {
      return _testFormInteractions.apply(this, arguments);
    }
    return testFormInteractions;
  }(),
  testNavigationInteractions: function () {
    var _testNavigationInteractions = (0, _asyncToGenerator2.default)(function* (component, navigationTargets) {
      var _renderWithProviders7 = renderWithProviders(component),
        getByTestId = _renderWithProviders7.getByTestId;
      var _loop = function* _loop() {
        var navElement = getByTestId(target);
        _reactNative.fireEvent.press(navElement);
        yield (0, _reactNative.waitFor)(function () {
          expect(navElement).toBeTruthy();
        });
      };
      for (var target of navigationTargets) {
        yield* _loop();
      }
      return navigationTargets.length;
    });
    function testNavigationInteractions(_x0, _x1) {
      return _testNavigationInteractions.apply(this, arguments);
    }
    return testNavigationInteractions;
  }()
};
var apiTestUtils = exports.apiTestUtils = {
  mockApiResponse: function mockApiResponse(endpoint, response) {
    var delay = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;
    return jest.fn().mockImplementation(function () {
      return new Promise(function (resolve) {
        return setTimeout(function () {
          return resolve(response);
        }, delay);
      });
    });
  },
  mockApiError: function mockApiError(endpoint, error) {
    var delay = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;
    return jest.fn().mockImplementation(function () {
      return new Promise(function (_, reject) {
        return setTimeout(function () {
          return reject(error);
        }, delay);
      });
    });
  },
  testLoadingStates: function () {
    var _testLoadingStates = (0, _asyncToGenerator2.default)(function* (component, apiCall) {
      var _renderWithProviders8 = renderWithProviders(component),
        getByTestId = _renderWithProviders8.getByTestId,
        queryByTestId = _renderWithProviders8.queryByTestId;
      var apiPromise = apiCall();
      yield (0, _reactNative.waitFor)(function () {
        expect(queryByTestId('loading-indicator')).toBeTruthy();
      });
      yield apiPromise;
      yield (0, _reactNative.waitFor)(function () {
        expect(queryByTestId('loading-indicator')).toBeFalsy();
      });
      return true;
    });
    function testLoadingStates(_x10, _x11) {
      return _testLoadingStates.apply(this, arguments);
    }
    return testLoadingStates;
  }(),
  testErrorStates: function () {
    var _testErrorStates = (0, _asyncToGenerator2.default)(function* (component, errorApiCall) {
      var _renderWithProviders9 = renderWithProviders(component),
        getByTestId = _renderWithProviders9.getByTestId,
        queryByTestId = _renderWithProviders9.queryByTestId;
      try {
        yield errorApiCall();
      } catch (error) {}
      yield (0, _reactNative.waitFor)(function () {
        expect(queryByTestId('error-display')).toBeTruthy();
      });
      return true;
    });
    function testErrorStates(_x12, _x13) {
      return _testErrorStates.apply(this, arguments);
    }
    return testErrorStates;
  }()
};
var screenTestUtils = exports.screenTestUtils = {
  testScreenRendering: function testScreenRendering(screen) {
    var _renderWithProviders0 = renderWithProviders(screen),
      getByTestId = _renderWithProviders0.getByTestId;
    expect(getByTestId('screen-container')).toBeTruthy();
    return true;
  },
  testScreenNavigation: function () {
    var _testScreenNavigation = (0, _asyncToGenerator2.default)(function* (screen, navigationProps) {
      var mockNavigation = Object.assign({
        navigate: jest.fn(),
        goBack: jest.fn()
      }, navigationProps);
      var ScreenWithNavigation = function ScreenWithNavigation() {
        return _react.default.cloneElement(screen, {
          navigation: mockNavigation
        });
      };
      renderWithProviders((0, _jsxRuntime.jsx)(ScreenWithNavigation, {}));
      return mockNavigation;
    });
    function testScreenNavigation(_x14, _x15) {
      return _testScreenNavigation.apply(this, arguments);
    }
    return testScreenNavigation;
  }(),
  testScreenDataLoading: function () {
    var _testScreenDataLoading = (0, _asyncToGenerator2.default)(function* (screen, mockData) {
      var _renderWithProviders1 = renderWithProviders(screen),
        getByTestId = _renderWithProviders1.getByTestId,
        queryByTestId = _renderWithProviders1.queryByTestId;
      expect(queryByTestId('loading-indicator')).toBeTruthy();
      yield (0, _reactNative.waitFor)(function () {
        expect(queryByTestId('loading-indicator')).toBeFalsy();
        expect(getByTestId('screen-content')).toBeTruthy();
      });
      return true;
    });
    function testScreenDataLoading(_x16, _x17) {
      return _testScreenDataLoading.apply(this, arguments);
    }
    return testScreenDataLoading;
  }()
};
var testDataFactories = exports.testDataFactories = {
  user: function user() {
    var overrides = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    return Object.assign({
      id: '1',
      name: 'Test User',
      email: '<EMAIL>',
      phone: '+**********',
      avatar: 'https://example.com/avatar.jpg'
    }, overrides);
  },
  provider: function provider() {
    var overrides = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    return Object.assign({
      id: '1',
      name: 'Test Provider',
      description: 'Test provider description',
      rating: 4.5,
      reviewCount: 100,
      services: ['Service 1', 'Service 2'],
      location: 'Test Location'
    }, overrides);
  },
  booking: function booking() {
    var overrides = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    return Object.assign({
      id: '1',
      providerId: '1',
      userId: '1',
      serviceId: '1',
      date: new Date().toISOString(),
      status: 'confirmed',
      price: 100
    }, overrides);
  },
  message: function message() {
    var overrides = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    return Object.assign({
      id: '1',
      senderId: '1',
      receiverId: '2',
      content: 'Test message',
      timestamp: new Date().toISOString(),
      read: false
    }, overrides);
  },
  service: function service() {
    var overrides = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    return Object.assign({
      id: '1',
      name: 'Test Service',
      description: 'Test service description',
      price: 50,
      duration: 60,
      category: 'Test Category'
    }, overrides);
  }
};
var asyncTestUtils = exports.asyncTestUtils = {
  waitForElement: function () {
    var _waitForElement = (0, _asyncToGenerator2.default)(function* (getByTestId, testId) {
      var timeout = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 5000;
      return (0, _reactNative.waitFor)(function () {
        return getByTestId(testId);
      }, {
        timeout: timeout
      });
    });
    function waitForElement(_x18, _x19) {
      return _waitForElement.apply(this, arguments);
    }
    return waitForElement;
  }(),
  waitForElementToDisappear: function () {
    var _waitForElementToDisappear = (0, _asyncToGenerator2.default)(function* (queryByTestId, testId) {
      var timeout = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 5000;
      return (0, _reactNative.waitFor)(function () {
        return expect(queryByTestId(testId)).toBeFalsy();
      }, {
        timeout: timeout
      });
    });
    function waitForElementToDisappear(_x20, _x21) {
      return _waitForElementToDisappear.apply(this, arguments);
    }
    return waitForElementToDisappear;
  }(),
  waitForAsyncOperation: function () {
    var _waitForAsyncOperation = (0, _asyncToGenerator2.default)(function* (operation) {
      var timeout = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 5000;
      return (0, _reactNative.act)((0, _asyncToGenerator2.default)(function* () {
        yield Promise.race([operation(), new Promise(function (_, reject) {
          return setTimeout(function () {
            return reject(new Error('Operation timeout'));
          }, timeout);
        })]);
      }));
    });
    function waitForAsyncOperation(_x22) {
      return _waitForAsyncOperation.apply(this, arguments);
    }
    return waitForAsyncOperation;
  }()
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************