40bc2ebc1730a42fa8db7557b5ea9bd3
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.EnhancedTouchTarget = exports.AccessibleIconButton = void 0;
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _vectorIcons = require("@expo/vector-icons");
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _ThemeContext = require("../../contexts/ThemeContext");
var _accessibilityUtils = require("../../utils/accessibilityUtils");
var _responsiveUtils = require("../../utils/responsiveUtils");
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["iconName", "iconSize", "iconColor", "label", "showLabel", "labelPosition"];
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var _Dimensions$get = _reactNative.Dimensions.get('window'),
  screenWidth = _Dimensions$get.width,
  screenHeight = _Dimensions$get.height;
var EnhancedTouchTarget = exports.EnhancedTouchTarget = function EnhancedTouchTarget(_ref) {
  var children = _ref.children,
    onPress = _ref.onPress,
    onLongPress = _ref.onLongPress,
    onPressIn = _ref.onPressIn,
    onPressOut = _ref.onPressOut,
    _ref$disabled = _ref.disabled,
    disabled = _ref$disabled === void 0 ? false : _ref$disabled,
    style = _ref.style,
    contentStyle = _ref.contentStyle,
    accessibilityLabel = _ref.accessibilityLabel,
    accessibilityHint = _ref.accessibilityHint,
    _ref$accessibilityRol = _ref.accessibilityRole,
    accessibilityRole = _ref$accessibilityRol === void 0 ? 'button' : _ref$accessibilityRol,
    testID = _ref.testID,
    _ref$minimumSize = _ref.minimumSize,
    minimumSize = _ref$minimumSize === void 0 ? _accessibilityUtils.AccessibilityUtils.WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE : _ref$minimumSize,
    _ref$enforceMinimumSi = _ref.enforceMinimumSize,
    enforceMinimumSize = _ref$enforceMinimumSi === void 0 ? true : _ref$enforceMinimumSi,
    _ref$showTouchFeedbac = _ref.showTouchFeedback,
    showTouchFeedback = _ref$showTouchFeedbac === void 0 ? true : _ref$showTouchFeedbac,
    touchFeedbackColor = _ref.touchFeedbackColor,
    _ref$touchFeedbackOpa = _ref.touchFeedbackOpacity,
    touchFeedbackOpacity = _ref$touchFeedbackOpa === void 0 ? 0.1 : _ref$touchFeedbackOpa,
    _ref$enableHapticFeed = _ref.enableHapticFeedback,
    enableHapticFeedback = _ref$enableHapticFeed === void 0 ? true : _ref$enableHapticFeed,
    _ref$enableSoundFeedb = _ref.enableSoundFeedback,
    enableSoundFeedback = _ref$enableSoundFeedb === void 0 ? false : _ref$enableSoundFeedb,
    _ref$enableVisualFeed = _ref.enableVisualFeedback,
    enableVisualFeedback = _ref$enableVisualFeed === void 0 ? true : _ref$enableVisualFeed,
    _ref$feedbackDuration = _ref.feedbackDuration,
    feedbackDuration = _ref$feedbackDuration === void 0 ? 150 : _ref$feedbackDuration,
    _ref$enableSwipeGestu = _ref.enableSwipeGestures,
    enableSwipeGestures = _ref$enableSwipeGestu === void 0 ? false : _ref$enableSwipeGestu,
    onSwipeLeft = _ref.onSwipeLeft,
    onSwipeRight = _ref.onSwipeRight,
    onSwipeUp = _ref.onSwipeUp,
    onSwipeDown = _ref.onSwipeDown,
    _ref$swipeThreshold = _ref.swipeThreshold,
    swipeThreshold = _ref$swipeThreshold === void 0 ? 50 : _ref$swipeThreshold,
    _ref$autoFocus = _ref.autoFocus,
    autoFocus = _ref$autoFocus === void 0 ? false : _ref$autoFocus,
    _ref$focusable = _ref.focusable,
    focusable = _ref$focusable === void 0 ? true : _ref$focusable,
    tabIndex = _ref.tabIndex;
  var _useTheme = (0, _ThemeContext.useTheme)(),
    colors = _useTheme.colors;
  var _useState = (0, _react.useState)(false),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    isPressed = _useState2[0],
    setIsPressed = _useState2[1];
  var _useState3 = (0, _react.useState)(false),
    _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
    isFocused = _useState4[0],
    setIsFocused = _useState4[1];
  var _useState5 = (0, _react.useState)({
      width: 0,
      height: 0
    }),
    _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
    dimensions = _useState6[0],
    setDimensions = _useState6[1];
  var touchRef = (0, _react.useRef)(null);
  var feedbackAnimation = (0, _react.useRef)(new _reactNative.Animated.Value(0)).current;
  var scaleAnimation = (0, _react.useRef)(new _reactNative.Animated.Value(1)).current;
  var getEffectiveTouchTargetStyle = function getEffectiveTouchTargetStyle() {
    var baseStyle = {};
    if (enforceMinimumSize) {
      var minWidth = Math.max(dimensions.width, minimumSize);
      var minHeight = Math.max(dimensions.height, minimumSize);
      baseStyle.minWidth = minWidth;
      baseStyle.minHeight = minHeight;
      if (dimensions.width < minimumSize) {
        var horizontalPadding = (minimumSize - dimensions.width) / 2;
        baseStyle.paddingHorizontal = horizontalPadding;
      }
      if (dimensions.height < minimumSize) {
        var verticalPadding = (minimumSize - dimensions.height) / 2;
        baseStyle.paddingVertical = verticalPadding;
      }
    }
    return baseStyle;
  };
  var getFocusStyle = function getFocusStyle() {
    if (!isFocused) return {};
    return {
      borderWidth: 2,
      borderColor: colors.primary.default,
      borderRadius: 4,
      shadowColor: colors.primary.default,
      shadowOffset: {
        width: 0,
        height: 0
      },
      shadowOpacity: 0.3,
      shadowRadius: 4,
      elevation: 4
    };
  };
  var handlePressIn = function handlePressIn(event) {
    setIsPressed(true);
    if (enableVisualFeedback) {
      _reactNative.Animated.parallel([_reactNative.Animated.timing(feedbackAnimation, {
        toValue: 1,
        duration: feedbackDuration,
        useNativeDriver: false
      }), _reactNative.Animated.timing(scaleAnimation, {
        toValue: 0.95,
        duration: feedbackDuration,
        useNativeDriver: true
      })]).start();
    }
    if (enableHapticFeedback && _reactNative.Platform.OS === 'ios') {}
    onPressIn == null || onPressIn(event);
  };
  var handlePressOut = function handlePressOut(event) {
    setIsPressed(false);
    if (enableVisualFeedback) {
      _reactNative.Animated.parallel([_reactNative.Animated.timing(feedbackAnimation, {
        toValue: 0,
        duration: feedbackDuration,
        useNativeDriver: false
      }), _reactNative.Animated.timing(scaleAnimation, {
        toValue: 1,
        duration: feedbackDuration,
        useNativeDriver: true
      })]).start();
    }
    onPressOut == null || onPressOut(event);
  };
  var handlePress = function handlePress(event) {
    if (enableHapticFeedback && _reactNative.Platform.OS === 'ios') {}
    onPress == null || onPress(event);
  };
  var panResponder = _reactNative.PanResponder.create({
    onStartShouldSetPanResponder: function onStartShouldSetPanResponder() {
      return enableSwipeGestures;
    },
    onMoveShouldSetPanResponder: function onMoveShouldSetPanResponder() {
      return enableSwipeGestures;
    },
    onPanResponderMove: function onPanResponderMove() {},
    onPanResponderRelease: function onPanResponderRelease(evt, gestureState) {
      if (!enableSwipeGestures) return;
      var dx = gestureState.dx,
        dy = gestureState.dy;
      var absDx = Math.abs(dx);
      var absDy = Math.abs(dy);
      if (absDx > swipeThreshold || absDy > swipeThreshold) {
        if (absDx > absDy) {
          if (dx > 0) {
            onSwipeRight == null || onSwipeRight();
          } else {
            onSwipeLeft == null || onSwipeLeft();
          }
        } else {
          if (dy > 0) {
            onSwipeDown == null || onSwipeDown();
          } else {
            onSwipeUp == null || onSwipeUp();
          }
        }
      }
    }
  });
  (0, _react.useEffect)(function () {
    if (autoFocus && touchRef.current) {
      setTimeout(function () {
        var _touchRef$current;
        (_touchRef$current = touchRef.current) == null || _touchRef$current.focus == null || _touchRef$current.focus();
        setIsFocused(true);
      }, 100);
    }
  }, [autoFocus]);
  var handleLayout = function handleLayout(event) {};
  var getAccessibilityProps = function getAccessibilityProps() {
    var props = {
      accessible: true,
      accessibilityRole: accessibilityRole,
      accessibilityLabel: accessibilityLabel,
      accessibilityHint: accessibilityHint,
      accessibilityState: {
        disabled: disabled,
        selected: isPressed
      }
    };
    if (_reactNative.Platform.OS === 'web' && tabIndex !== undefined) {
      props.tabIndex = disabled ? -1 : tabIndex;
    }
    return props;
  };
  var renderTouchFeedback = function renderTouchFeedback() {
    if (!showTouchFeedback || !enableVisualFeedback) return null;
    var feedbackColor = touchFeedbackColor || colors.primary.default;
    return (0, _jsxRuntime.jsx)(_reactNative.Animated.View, {
      style: [_reactNative.StyleSheet.absoluteFillObject, {
        backgroundColor: feedbackColor,
        opacity: feedbackAnimation.interpolate({
          inputRange: [0, 1],
          outputRange: [0, touchFeedbackOpacity]
        }),
        borderRadius: 4
      }],
      pointerEvents: "none"
    });
  };
  var renderSizeIndicator = function renderSizeIndicator() {
    if (!__DEV__ || !enforceMinimumSize) return null;
    var meetsMinimumSize = dimensions.width >= minimumSize && dimensions.height >= minimumSize;
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: [styles.sizeIndicator, {
        backgroundColor: meetsMinimumSize ? '#4CAF50' : '#FF6B6B'
      }],
      children: (0, _jsxRuntime.jsxs)(_reactNative.Text, {
        style: styles.sizeIndicatorText,
        children: [Math.round(dimensions.width), "x", Math.round(dimensions.height)]
      })
    });
  };
  var combinedStyle = [getEffectiveTouchTargetStyle(), getFocusStyle(), style, {
    opacity: disabled ? 0.6 : 1
  }];
  var touchableProps = Object.assign({
    ref: touchRef,
    onPress: handlePress,
    onLongPress: onLongPress,
    onPressIn: handlePressIn,
    onPressOut: handlePressOut,
    disabled: disabled,
    style: combinedStyle,
    testID: testID,
    onFocus: function onFocus() {
      return setIsFocused(true);
    },
    onBlur: function onBlur() {
      return setIsFocused(false);
    }
  }, getAccessibilityProps(), enableSwipeGestures ? panResponder.panHandlers : {});
  return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, Object.assign({}, touchableProps, {
    children: (0, _jsxRuntime.jsxs)(_reactNative.Animated.View, {
      style: [contentStyle, {
        transform: [{
          scale: scaleAnimation
        }]
      }],
      onLayout: handleLayout,
      children: [children, renderTouchFeedback(), renderSizeIndicator()]
    })
  }));
};
var AccessibleIconButton = exports.AccessibleIconButton = function AccessibleIconButton(_ref2) {
  var iconName = _ref2.iconName,
    _ref2$iconSize = _ref2.iconSize,
    iconSize = _ref2$iconSize === void 0 ? 24 : _ref2$iconSize,
    iconColor = _ref2.iconColor,
    label = _ref2.label,
    _ref2$showLabel = _ref2.showLabel,
    showLabel = _ref2$showLabel === void 0 ? false : _ref2$showLabel,
    _ref2$labelPosition = _ref2.labelPosition,
    labelPosition = _ref2$labelPosition === void 0 ? 'bottom' : _ref2$labelPosition,
    touchTargetProps = (0, _objectWithoutProperties2.default)(_ref2, _excluded);
  var _useTheme2 = (0, _ThemeContext.useTheme)(),
    colors = _useTheme2.colors;
  var finalIconColor = iconColor || colors.text.primary;
  var renderIcon = function renderIcon() {
    return (0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
      name: iconName,
      size: iconSize,
      color: finalIconColor
    });
  };
  var renderLabel = function renderLabel() {
    if (!showLabel) return null;
    return (0, _jsxRuntime.jsx)(_reactNative.Text, {
      style: [styles.iconButtonLabel, {
        color: colors.text.secondary,
        marginTop: labelPosition === 'bottom' ? 4 : 0,
        marginBottom: labelPosition === 'top' ? 4 : 0,
        marginLeft: labelPosition === 'right' ? 4 : 0,
        marginRight: labelPosition === 'left' ? 4 : 0
      }],
      children: label
    });
  };
  var getContentStyle = function getContentStyle() {
    var isHorizontal = labelPosition === 'left' || labelPosition === 'right';
    return {
      flexDirection: isHorizontal ? 'row' : 'column',
      alignItems: 'center',
      justifyContent: 'center'
    };
  };
  return (0, _jsxRuntime.jsxs)(EnhancedTouchTarget, Object.assign({}, touchTargetProps, {
    accessibilityLabel: touchTargetProps.accessibilityLabel || label,
    contentStyle: getContentStyle(),
    children: [labelPosition === 'top' && renderLabel(), labelPosition === 'left' && renderLabel(), renderIcon(), labelPosition === 'right' && renderLabel(), labelPosition === 'bottom' && renderLabel()]
  }));
};
var styles = _reactNative.StyleSheet.create({
  sizeIndicator: {
    position: 'absolute',
    top: -15,
    right: -5,
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
    zIndex: 1000
  },
  sizeIndicatorText: {
    color: '#FFFFFF',
    fontSize: 8,
    fontWeight: 'bold'
  },
  iconButtonLabel: {
    fontSize: (0, _responsiveUtils.getResponsiveFontSize)(12),
    textAlign: 'center'
  }
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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