376b049306248824b9d88e92cf909509
Object.defineProperty(exports, "__esModule", {
  value: true
});
require("@testing-library/jest-native/extend-expect");
afterEach(function () {
  jest.clearAllMocks();
});
describe('Test Setup', function () {
  it('should configure test environment', function () {
    expect(jest).toBeDefined();
    expect(afterEach).toBeDefined();
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJyZXF1aXJlIiwiYWZ0ZXJFYWNoIiwiamVzdCIsImNsZWFyQWxsTW9ja3MiLCJkZXNjcmliZSIsIml0IiwiZXhwZWN0IiwidG9CZURlZmluZWQiXSwic291cmNlcyI6WyJzZXR1cC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEplc3QgVGVzdCBTZXR1cFxuICpcbiAqIEdsb2JhbCB0ZXN0IHNldHVwIGFuZCBjb25maWd1cmF0aW9uIGZvciB0aGUgVmllcmxhIEZyb250ZW5kIHYxIHRlc3Qgc3VpdGUuXG4gKiBJbmNsdWRlcyBtb2NrcywgcG9seWZpbGxzLCBhbmQgdGVzdCB1dGlsaXRpZXMuXG4gKlxuICogQHZlcnNpb24gMS4wLjBcbiAqIEBhdXRob3IgVmllcmxhIERldmVsb3BtZW50IFRlYW1cbiAqL1xuXG5pbXBvcnQgJ0B0ZXN0aW5nLWxpYnJhcnkvamVzdC1uYXRpdmUvZXh0ZW5kLWV4cGVjdCc7XG5cbi8vIENsZWFudXAgYWZ0ZXIgZWFjaCB0ZXN0XG5hZnRlckVhY2goKCkgPT4ge1xuICBqZXN0LmNsZWFyQWxsTW9ja3MoKTtcbn0pO1xuXG4vLyBTaW1wbGUgdGVzdCB0byBwcmV2ZW50IFwibm8gdGVzdHNcIiBlcnJvclxuZGVzY3JpYmUoJ1Rlc3QgU2V0dXAnLCAoKSA9PiB7XG4gIGl0KCdzaG91bGQgY29uZmlndXJlIHRlc3QgZW52aXJvbm1lbnQnLCAoKSA9PiB7XG4gICAgZXhwZWN0KGplc3QpLnRvQmVEZWZpbmVkKCk7XG4gICAgZXhwZWN0KGFmdGVyRWFjaCkudG9CZURlZmluZWQoKTtcbiAgfSk7XG59KTtcblxuZXhwb3J0IHt9O1xuIl0sIm1hcHBpbmdzIjoiOzs7QUFVQUEsT0FBQTtBQUdBQyxTQUFTLENBQUMsWUFBTTtFQUNkQyxJQUFJLENBQUNDLGFBQWEsQ0FBQyxDQUFDO0FBQ3RCLENBQUMsQ0FBQztBQUdGQyxRQUFRLENBQUMsWUFBWSxFQUFFLFlBQU07RUFDM0JDLEVBQUUsQ0FBQyxtQ0FBbUMsRUFBRSxZQUFNO0lBQzVDQyxNQUFNLENBQUNKLElBQUksQ0FBQyxDQUFDSyxXQUFXLENBQUMsQ0FBQztJQUMxQkQsTUFBTSxDQUFDTCxTQUFTLENBQUMsQ0FBQ00sV0FBVyxDQUFDLENBQUM7RUFDakMsQ0FBQyxDQUFDO0FBQ0osQ0FBQyxDQUFDIiwiaWdub3JlTGlzdCI6W119