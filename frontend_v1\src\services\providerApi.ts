/**
 * Provider API Service
 * Handles all provider-related API calls to the backend
 */

import { apiClient } from './apiClient';
import { ProviderProfile, ProviderAnalytics, Service } from '../types/provider';

export interface ProviderDashboardData {
  total_bookings: number;
  weekly_bookings: number;
  monthly_revenue: number;
  active_services: number;
  average_rating: number;
  upcoming_appointments: any[];
}

export interface ProviderBusinessSettings {
  business_name: string;
  business_description: string;
  business_phone: string;
  business_email: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  website: string;
  instagram_handle: string;
  facebook_url: string;
  is_active: boolean;
}

export interface ProviderService {
  id: string;
  name: string;
  description: string;
  base_price: number;
  duration: number;
  is_active: boolean;
  category: string | null;
  created_at: string;
  updated_at: string;
}

export interface ProviderServicesResponse {
  services: ProviderService[];
  total_services: number;
  active_services: number;
  inactive_services: number;
}

class ProviderApiService {
  private baseUrl = '/api/v1/provider';

  /**
   * Get provider profile
   */
  async getProfile(): Promise<ProviderProfile> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/profile/`);
      
      // Transform backend response to frontend format
      const data = response.data;
      return {
        id: data.id,
        businessName: data.business_name,
        description: data.business_description,
        category: 'Beauty & Wellness', // Default category
        subcategories: [], // Will be populated from services
        address: {
          street: data.address,
          city: data.city,
          state: data.state,
          zipCode: data.zip_code,
          country: 'Canada',
          coordinates: {
            latitude: 43.6532, // Default coordinates
            longitude: -79.3832,
          },
        },
        contact: {
          phone: data.business_phone,
          email: data.business_email,
          website: data.website || '',
          socialMedia: {
            instagram: data.instagram_handle || '',
            facebook: data.facebook_url || '',
          },
        },
        verification: {
          isVerified: data.is_verified,
          verificationDate: data.join_date,
          documents: [],
        },
        rating: {
          average: data.rating || 0,
          count: 0, // Will be populated from reviews
          distribution: {
            5: 0,
            4: 0,
            3: 0,
            2: 0,
            1: 0,
          },
        },
        services: [], // Will be populated separately
        availability: [], // Will be populated separately
        portfolio: {
          images: [],
          videos: [],
          beforeAfter: [],
        },
        reviews: [], // Will be populated separately
        analytics: {
          totalBookings: 0,
          completedBookings: 0,
          cancelledBookings: 0,
          averageRating: data.rating || 0,
          totalRevenue: data.total_earnings || 0,
          monthlyRevenue: 0,
          repeatCustomers: 0,
          responseTime: '< 1 hour',
          completionRate: 95,
          bookingTrends: [],
          popularServices: [],
          peakHours: [],
          customerSatisfaction: data.rating || 0,
        },
        settings: {
          notifications: {
            bookingConfirmations: true,
            cancellations: true,
            reviews: true,
            promotions: false,
          },
          privacy: {
            showPhone: true,
            showEmail: false,
            showAddress: true,
          },
          booking: {
            autoConfirm: false,
            requireDeposit: false,
            cancellationPolicy: '24 hours',
            bufferTime: 15,
          },
        },
        isActive: data.is_active,
        createdAt: data.join_date,
        updatedAt: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error fetching provider profile:', error);
      throw error;
    }
  }

  /**
   * Update provider profile
   */
  async updateProfile(profileData: Partial<ProviderProfile>): Promise<ProviderProfile> {
    try {
      // Transform frontend format to backend format
      const backendData = {
        business_name: profileData.businessName,
        business_description: profileData.description,
        business_phone: profileData.contact?.phone,
        business_email: profileData.contact?.email,
        address: profileData.address?.street,
        city: profileData.address?.city,
        state: profileData.address?.state,
        zip_code: profileData.address?.zipCode,
        website: profileData.contact?.website,
        instagram_handle: profileData.contact?.socialMedia?.instagram,
        facebook_url: profileData.contact?.socialMedia?.facebook,
        is_active: profileData.isActive,
      };

      const response = await apiClient.put(`${this.baseUrl}/profile/`, backendData);
      return this.getProfile(); // Return updated profile
    } catch (error) {
      console.error('Error updating provider profile:', error);
      throw error;
    }
  }

  /**
   * Get provider dashboard data
   */
  async getDashboard(): Promise<ProviderDashboardData> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/dashboard/overview/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching provider dashboard:', error);
      throw error;
    }
  }

  /**
   * Get provider business settings
   */
  async getBusinessSettings(): Promise<ProviderBusinessSettings> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/business-settings/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching business settings:', error);
      throw error;
    }
  }

  /**
   * Update provider business settings
   */
  async updateBusinessSettings(settings: Partial<ProviderBusinessSettings>): Promise<ProviderBusinessSettings> {
    try {
      const response = await apiClient.put(`${this.baseUrl}/business-settings/`, settings);
      return response.data;
    } catch (error) {
      console.error('Error updating business settings:', error);
      throw error;
    }
  }

  /**
   * Get provider services
   */
  async getServices(): Promise<ProviderServicesResponse> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/services/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching provider services:', error);
      throw error;
    }
  }

  /**
   * Create a new service
   */
  async createService(serviceData: {
    name: string;
    description: string;
    base_price: number;
    duration: number;
    category_id?: string;
    is_active?: boolean;
  }): Promise<ProviderService> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/services/`, serviceData);
      return response.data.service;
    } catch (error) {
      console.error('Error creating service:', error);
      throw error;
    }
  }

  /**
   * Toggle service active status
   */
  async toggleServiceActive(serviceId: string): Promise<{
    service_id: string;
    service_name: string;
    is_active: boolean;
    message: string;
  }> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/services/${serviceId}/toggle-active/`);
      return response.data;
    } catch (error) {
      console.error('Error toggling service active status:', error);
      throw error;
    }
  }

  /**
   * Bulk update services
   */
  async bulkUpdateServices(services: Array<{
    id: string;
    name?: string;
    description?: string;
    base_price?: number;
    duration?: number;
    is_active?: boolean;
  }>): Promise<{
    updated_services: Array<{ id: string; name: string; updated: boolean }>;
    errors: Array<{ id?: string; error: string }>;
    total_updated: number;
    total_errors: number;
  }> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/services/bulk-update/`, {
        services,
      });
      return response.data;
    } catch (error) {
      console.error('Error bulk updating services:', error);
      throw error;
    }
  }
}

export const providerApiService = new ProviderApiService();
