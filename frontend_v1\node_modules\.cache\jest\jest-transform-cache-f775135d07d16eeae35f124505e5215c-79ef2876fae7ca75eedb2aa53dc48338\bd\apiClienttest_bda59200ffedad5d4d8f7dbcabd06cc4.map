{"version": 3, "names": ["_getJest<PERSON>bj", "mock", "getItem", "jest", "fn", "setItem", "removeItem", "useAuthStore", "getState", "loginSuccess", "logout", "performanceCacheService", "get", "set", "clear", "_interopRequireDefault", "require", "_asyncToGenerator2", "_asyncStorage", "_apiClient", "_require", "global", "fetch", "describe", "beforeEach", "clearAllMocks", "mockClear", "AsyncStorage", "it", "default", "mockToken", "mockResolvedValue", "client", "ApiClient", "Promise", "resolve", "setTimeout", "expect", "toHaveBeenCalledWith", "testToken", "apiClient", "setAuthToken", "ok", "status", "statusText", "headers", "Map", "json", "data", "testUrl", "testParams", "page", "limit", "params", "stringContaining", "objectContaining", "method", "Accept", "testData", "name", "value", "post", "body", "JSON", "stringify", "any", "String", "Authorization", "mockRejectedValue", "Error", "rejects", "toThrow", "detail", "mockResolvedValueOnce", "access", "result", "toHaveBeenCalledTimes", "toEqual", "customTimeout", "timeout", "toHaveBeenCalled", "customHeaders"], "sources": ["apiClient.test.ts"], "sourcesContent": ["/**\n * API Client Tests\n *\n * Tests for the centralized HTTP client service\n */\n\nimport AsyncStorage from '@react-native-async-storage/async-storage';\n\nimport { apiClient } from '../apiClient';\n\n// Mock AsyncStorage\njest.mock('@react-native-async-storage/async-storage', () => ({\n  getItem: jest.fn(),\n  setItem: jest.fn(),\n  removeItem: jest.fn(),\n}));\n\n// Mock fetch\nglobal.fetch = jest.fn();\n\n// Mock auth store\njest.mock('../../store/authSlice', () => ({\n  useAuthStore: {\n    getState: jest.fn(() => ({\n      loginSuccess: jest.fn(),\n      logout: jest.fn(),\n    })),\n  },\n}));\n\n// Mock performance cache service\njest.mock('../performanceCacheService', () => ({\n  performanceCacheService: {\n    get: jest.fn(),\n    set: jest.fn(),\n    clear: jest.fn(),\n  },\n}));\n\ndescribe('ApiClient', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n    (fetch as jest.Mock).mockClear();\n    (AsyncStorage.getItem as jest.Mock).mockClear();\n    (AsyncStorage.setItem as jest.Mock).mockClear();\n    (AsyncStorage.removeItem as jest.Mock).mockClear();\n  });\n\n  describe('Authentication Token Management', () => {\n    it('should load auth token from AsyncStorage on initialization', async () => {\n      const mockToken = 'test-token-123';\n      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(mockToken);\n\n      // Create new instance to trigger loadAuthToken\n      const client = new (require('../apiClient').ApiClient)();\n\n      // Wait for async initialization\n      await new Promise(resolve => setTimeout(resolve, 0));\n\n      expect(AsyncStorage.getItem).toHaveBeenCalledWith('auth_token');\n    });\n\n    it('should set auth token and save to AsyncStorage', () => {\n      const testToken = 'new-test-token';\n\n      apiClient.setAuthToken(testToken);\n\n      expect(AsyncStorage.setItem).toHaveBeenCalledWith(\n        'auth_token',\n        testToken,\n      );\n    });\n\n    it('should remove auth token from AsyncStorage when set to null', () => {\n      apiClient.setAuthToken(null);\n\n      expect(AsyncStorage.removeItem).toHaveBeenCalledWith('auth_token');\n    });\n  });\n\n  describe('HTTP Request Methods', () => {\n    beforeEach(() => {\n      (fetch as jest.Mock).mockResolvedValue({\n        ok: true,\n        status: 200,\n        statusText: 'OK',\n        headers: new Map([['content-type', 'application/json']]),\n        json: jest.fn().mockResolvedValue({ data: 'test' }),\n      });\n    });\n\n    it('should make GET request with correct parameters', async () => {\n      const testUrl = '/api/test';\n      const testParams = { page: 1, limit: 10 };\n\n      await apiClient.get(testUrl, { params: testParams });\n\n      expect(fetch).toHaveBeenCalledWith(\n        expect.stringContaining('page=1&limit=10'),\n        expect.objectContaining({\n          method: 'GET',\n          headers: expect.objectContaining({\n            'Content-Type': 'application/json',\n            Accept: 'application/json',\n          }),\n        }),\n      );\n    });\n\n    it('should make POST request with data', async () => {\n      const testUrl = '/api/test';\n      const testData = { name: 'test', value: 123 };\n\n      await apiClient.post(testUrl, testData);\n\n      expect(fetch).toHaveBeenCalledWith(\n        expect.stringContaining(testUrl),\n        expect.objectContaining({\n          method: 'POST',\n          headers: expect.objectContaining({\n            'Content-Type': 'application/json',\n            Accept: 'application/json',\n          }),\n          body: JSON.stringify(testData),\n        }),\n      );\n    });\n\n    it('should include auth token in headers when available', async () => {\n      const testToken = 'bearer-token-123';\n      apiClient.setAuthToken(testToken);\n\n      await apiClient.get('/api/protected');\n\n      expect(fetch).toHaveBeenCalledWith(\n        expect.any(String),\n        expect.objectContaining({\n          headers: expect.objectContaining({\n            Authorization: `Bearer ${testToken}`,\n          }),\n        }),\n      );\n    });\n  });\n\n  describe('Error Handling', () => {\n    it('should handle network errors', async () => {\n      (fetch as jest.Mock).mockRejectedValue(new Error('Network error'));\n\n      await expect(apiClient.get('/api/test')).rejects.toThrow('Network error');\n    });\n\n    it('should handle HTTP error responses', async () => {\n      (fetch as jest.Mock).mockResolvedValue({\n        ok: false,\n        status: 404,\n        statusText: 'Not Found',\n        json: jest.fn().mockResolvedValue({ detail: 'Resource not found' }),\n      });\n\n      await expect(apiClient.get('/api/nonexistent')).rejects.toThrow();\n    });\n\n    it('should handle 401 unauthorized and attempt token refresh', async () => {\n      // Mock refresh token in storage\n      (AsyncStorage.getItem as jest.Mock)\n        .mockResolvedValueOnce('refresh-token-123') // for refresh token\n        .mockResolvedValueOnce(null); // for auth token\n\n      // Mock 401 response first, then success after refresh\n      (fetch as jest.Mock)\n        .mockResolvedValueOnce({\n          ok: false,\n          status: 401,\n          statusText: 'Unauthorized',\n          json: jest.fn().mockResolvedValue({ detail: 'Token expired' }),\n        })\n        .mockResolvedValueOnce({\n          ok: true,\n          status: 200,\n          json: jest.fn().mockResolvedValue({ access: 'new-token-123' }),\n        })\n        .mockResolvedValueOnce({\n          ok: true,\n          status: 200,\n          json: jest.fn().mockResolvedValue({ data: 'success' }),\n        });\n\n      const result = await apiClient.get('/api/protected');\n\n      expect(fetch).toHaveBeenCalledTimes(3); // Original request, refresh, retry\n      expect(result.data).toEqual({ data: 'success' });\n    });\n  });\n\n  describe('Request Configuration', () => {\n    it('should respect custom timeout', async () => {\n      const customTimeout = 5000;\n\n      await apiClient.get('/api/test', { timeout: customTimeout });\n\n      // Note: Testing timeout behavior would require more complex mocking\n      expect(fetch).toHaveBeenCalled();\n    });\n\n    it('should handle custom headers', async () => {\n      const customHeaders = { 'X-Custom-Header': 'test-value' };\n\n      await apiClient.get('/api/test', { headers: customHeaders });\n\n      expect(fetch).toHaveBeenCalledWith(\n        expect.any(String),\n        expect.objectContaining({\n          headers: expect.objectContaining(customHeaders),\n        }),\n      );\n    });\n  });\n});\n"], "mappings": "AAWAA,WAAA,GAAKC,IAAI,CAAC,2CAA2C,EAAE;EAAA,OAAO;IAC5DC,OAAO,EAAEC,IAAI,CAACC,EAAE,CAAC,CAAC;IAClBC,OAAO,EAAEF,IAAI,CAACC,EAAE,CAAC,CAAC;IAClBE,UAAU,EAAEH,IAAI,CAACC,EAAE,CAAC;EACtB,CAAC;AAAA,CAAC,CAAC;AAMHJ,WAAA,GAAKC,IAAI,0BAA0B;EAAA,OAAO;IACxCM,YAAY,EAAE;MACZC,QAAQ,EAAEL,IAAI,CAACC,EAAE,CAAC;QAAA,OAAO;UACvBK,YAAY,EAAEN,IAAI,CAACC,EAAE,CAAC,CAAC;UACvBM,MAAM,EAAEP,IAAI,CAACC,EAAE,CAAC;QAClB,CAAC;MAAA,CAAC;IACJ;EACF,CAAC;AAAA,CAAC,CAAC;AAGHJ,WAAA,GAAKC,IAAI,+BAA+B;EAAA,OAAO;IAC7CU,uBAAuB,EAAE;MACvBC,GAAG,EAAET,IAAI,CAACC,EAAE,CAAC,CAAC;MACdS,GAAG,EAAEV,IAAI,CAACC,EAAE,CAAC,CAAC;MACdU,KAAK,EAAEX,IAAI,CAACC,EAAE,CAAC;IACjB;EACF,CAAC;AAAA,CAAC,CAAC;AAAC,IAAAW,sBAAA,GAAAC,OAAA;AAAA,IAAAC,kBAAA,GAAAF,sBAAA,CAAAC,OAAA;AA/BJ,IAAAE,aAAA,GAAAH,sBAAA,CAAAC,OAAA;AAEA,IAAAG,UAAA,GAAAH,OAAA;AAAyC,SAAAhB,YAAA;EAAA,IAAAoB,QAAA,GAAAJ,OAAA;IAAAb,IAAA,GAAAiB,QAAA,CAAAjB,IAAA;EAAAH,WAAA,YAAAA,YAAA;IAAA,OAAAG,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAUzCkB,MAAM,CAACC,KAAK,GAAGnB,IAAI,CAACC,EAAE,CAAC,CAAC;AAqBxBmB,QAAQ,CAAC,WAAW,EAAE,YAAM;EAC1BC,UAAU,CAAC,YAAM;IACfrB,IAAI,CAACsB,aAAa,CAAC,CAAC;IACnBH,KAAK,CAAeI,SAAS,CAAC,CAAC;IAC/BC,qBAAY,CAACzB,OAAO,CAAewB,SAAS,CAAC,CAAC;IAC9CC,qBAAY,CAACtB,OAAO,CAAeqB,SAAS,CAAC,CAAC;IAC9CC,qBAAY,CAACrB,UAAU,CAAeoB,SAAS,CAAC,CAAC;EACpD,CAAC,CAAC;EAEFH,QAAQ,CAAC,iCAAiC,EAAE,YAAM;IAChDK,EAAE,CAAC,4DAA4D,MAAAX,kBAAA,CAAAY,OAAA,EAAE,aAAY;MAC3E,IAAMC,SAAS,GAAG,gBAAgB;MACjCH,qBAAY,CAACzB,OAAO,CAAe6B,iBAAiB,CAACD,SAAS,CAAC;MAGhE,IAAME,MAAM,GAAG,KAAKhB,OAAO,eAAe,CAAC,CAACiB,SAAS,EAAE,CAAC;MAGxD,MAAM,IAAIC,OAAO,CAAC,UAAAC,OAAO;QAAA,OAAIC,UAAU,CAACD,OAAO,EAAE,CAAC,CAAC;MAAA,EAAC;MAEpDE,MAAM,CAACV,qBAAY,CAACzB,OAAO,CAAC,CAACoC,oBAAoB,CAAC,YAAY,CAAC;IACjE,CAAC,EAAC;IAEFV,EAAE,CAAC,gDAAgD,EAAE,YAAM;MACzD,IAAMW,SAAS,GAAG,gBAAgB;MAElCC,oBAAS,CAACC,YAAY,CAACF,SAAS,CAAC;MAEjCF,MAAM,CAACV,qBAAY,CAACtB,OAAO,CAAC,CAACiC,oBAAoB,CAC/C,YAAY,EACZC,SACF,CAAC;IACH,CAAC,CAAC;IAEFX,EAAE,CAAC,6DAA6D,EAAE,YAAM;MACtEY,oBAAS,CAACC,YAAY,CAAC,IAAI,CAAC;MAE5BJ,MAAM,CAACV,qBAAY,CAACrB,UAAU,CAAC,CAACgC,oBAAoB,CAAC,YAAY,CAAC;IACpE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFf,QAAQ,CAAC,sBAAsB,EAAE,YAAM;IACrCC,UAAU,CAAC,YAAM;MACdF,KAAK,CAAeS,iBAAiB,CAAC;QACrCW,EAAE,EAAE,IAAI;QACRC,MAAM,EAAE,GAAG;QACXC,UAAU,EAAE,IAAI;QAChBC,OAAO,EAAE,IAAIC,GAAG,CAAC,CAAC,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC,CAAC;QACxDC,IAAI,EAAE5C,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC2B,iBAAiB,CAAC;UAAEiB,IAAI,EAAE;QAAO,CAAC;MACpD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFpB,EAAE,CAAC,iDAAiD,MAAAX,kBAAA,CAAAY,OAAA,EAAE,aAAY;MAChE,IAAMoB,OAAO,GAAG,WAAW;MAC3B,IAAMC,UAAU,GAAG;QAAEC,IAAI,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAG,CAAC;MAEzC,MAAMZ,oBAAS,CAAC5B,GAAG,CAACqC,OAAO,EAAE;QAAEI,MAAM,EAAEH;MAAW,CAAC,CAAC;MAEpDb,MAAM,CAACf,KAAK,CAAC,CAACgB,oBAAoB,CAChCD,MAAM,CAACiB,gBAAgB,CAAC,iBAAiB,CAAC,EAC1CjB,MAAM,CAACkB,gBAAgB,CAAC;QACtBC,MAAM,EAAE,KAAK;QACbX,OAAO,EAAER,MAAM,CAACkB,gBAAgB,CAAC;UAC/B,cAAc,EAAE,kBAAkB;UAClCE,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CACH,CAAC;IACH,CAAC,EAAC;IAEF7B,EAAE,CAAC,oCAAoC,MAAAX,kBAAA,CAAAY,OAAA,EAAE,aAAY;MACnD,IAAMoB,OAAO,GAAG,WAAW;MAC3B,IAAMS,QAAQ,GAAG;QAAEC,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAI,CAAC;MAE7C,MAAMpB,oBAAS,CAACqB,IAAI,CAACZ,OAAO,EAAES,QAAQ,CAAC;MAEvCrB,MAAM,CAACf,KAAK,CAAC,CAACgB,oBAAoB,CAChCD,MAAM,CAACiB,gBAAgB,CAACL,OAAO,CAAC,EAChCZ,MAAM,CAACkB,gBAAgB,CAAC;QACtBC,MAAM,EAAE,MAAM;QACdX,OAAO,EAAER,MAAM,CAACkB,gBAAgB,CAAC;UAC/B,cAAc,EAAE,kBAAkB;UAClCE,MAAM,EAAE;QACV,CAAC,CAAC;QACFK,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACN,QAAQ;MAC/B,CAAC,CACH,CAAC;IACH,CAAC,EAAC;IAEF9B,EAAE,CAAC,qDAAqD,MAAAX,kBAAA,CAAAY,OAAA,EAAE,aAAY;MACpE,IAAMU,SAAS,GAAG,kBAAkB;MACpCC,oBAAS,CAACC,YAAY,CAACF,SAAS,CAAC;MAEjC,MAAMC,oBAAS,CAAC5B,GAAG,CAAC,gBAAgB,CAAC;MAErCyB,MAAM,CAACf,KAAK,CAAC,CAACgB,oBAAoB,CAChCD,MAAM,CAAC4B,GAAG,CAACC,MAAM,CAAC,EAClB7B,MAAM,CAACkB,gBAAgB,CAAC;QACtBV,OAAO,EAAER,MAAM,CAACkB,gBAAgB,CAAC;UAC/BY,aAAa,EAAE,UAAU5B,SAAS;QACpC,CAAC;MACH,CAAC,CACH,CAAC;IACH,CAAC,EAAC;EACJ,CAAC,CAAC;EAEFhB,QAAQ,CAAC,gBAAgB,EAAE,YAAM;IAC/BK,EAAE,CAAC,8BAA8B,MAAAX,kBAAA,CAAAY,OAAA,EAAE,aAAY;MAC5CP,KAAK,CAAe8C,iBAAiB,CAAC,IAAIC,KAAK,CAAC,eAAe,CAAC,CAAC;MAElE,MAAMhC,MAAM,CAACG,oBAAS,CAAC5B,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC0D,OAAO,CAACC,OAAO,CAAC,eAAe,CAAC;IAC3E,CAAC,EAAC;IAEF3C,EAAE,CAAC,oCAAoC,MAAAX,kBAAA,CAAAY,OAAA,EAAE,aAAY;MAClDP,KAAK,CAAeS,iBAAiB,CAAC;QACrCW,EAAE,EAAE,KAAK;QACTC,MAAM,EAAE,GAAG;QACXC,UAAU,EAAE,WAAW;QACvBG,IAAI,EAAE5C,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC2B,iBAAiB,CAAC;UAAEyC,MAAM,EAAE;QAAqB,CAAC;MACpE,CAAC,CAAC;MAEF,MAAMnC,MAAM,CAACG,oBAAS,CAAC5B,GAAG,CAAC,kBAAkB,CAAC,CAAC,CAAC0D,OAAO,CAACC,OAAO,CAAC,CAAC;IACnE,CAAC,EAAC;IAEF3C,EAAE,CAAC,0DAA0D,MAAAX,kBAAA,CAAAY,OAAA,EAAE,aAAY;MAExEF,qBAAY,CAACzB,OAAO,CAClBuE,qBAAqB,CAAC,mBAAmB,CAAC,CAC1CA,qBAAqB,CAAC,IAAI,CAAC;MAG7BnD,KAAK,CACHmD,qBAAqB,CAAC;QACrB/B,EAAE,EAAE,KAAK;QACTC,MAAM,EAAE,GAAG;QACXC,UAAU,EAAE,cAAc;QAC1BG,IAAI,EAAE5C,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC2B,iBAAiB,CAAC;UAAEyC,MAAM,EAAE;QAAgB,CAAC;MAC/D,CAAC,CAAC,CACDC,qBAAqB,CAAC;QACrB/B,EAAE,EAAE,IAAI;QACRC,MAAM,EAAE,GAAG;QACXI,IAAI,EAAE5C,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC2B,iBAAiB,CAAC;UAAE2C,MAAM,EAAE;QAAgB,CAAC;MAC/D,CAAC,CAAC,CACDD,qBAAqB,CAAC;QACrB/B,EAAE,EAAE,IAAI;QACRC,MAAM,EAAE,GAAG;QACXI,IAAI,EAAE5C,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC2B,iBAAiB,CAAC;UAAEiB,IAAI,EAAE;QAAU,CAAC;MACvD,CAAC,CAAC;MAEJ,IAAM2B,MAAM,SAASnC,oBAAS,CAAC5B,GAAG,CAAC,gBAAgB,CAAC;MAEpDyB,MAAM,CAACf,KAAK,CAAC,CAACsD,qBAAqB,CAAC,CAAC,CAAC;MACtCvC,MAAM,CAACsC,MAAM,CAAC3B,IAAI,CAAC,CAAC6B,OAAO,CAAC;QAAE7B,IAAI,EAAE;MAAU,CAAC,CAAC;IAClD,CAAC,EAAC;EACJ,CAAC,CAAC;EAEFzB,QAAQ,CAAC,uBAAuB,EAAE,YAAM;IACtCK,EAAE,CAAC,+BAA+B,MAAAX,kBAAA,CAAAY,OAAA,EAAE,aAAY;MAC9C,IAAMiD,aAAa,GAAG,IAAI;MAE1B,MAAMtC,oBAAS,CAAC5B,GAAG,CAAC,WAAW,EAAE;QAAEmE,OAAO,EAAED;MAAc,CAAC,CAAC;MAG5DzC,MAAM,CAACf,KAAK,CAAC,CAAC0D,gBAAgB,CAAC,CAAC;IAClC,CAAC,EAAC;IAEFpD,EAAE,CAAC,8BAA8B,MAAAX,kBAAA,CAAAY,OAAA,EAAE,aAAY;MAC7C,IAAMoD,aAAa,GAAG;QAAE,iBAAiB,EAAE;MAAa,CAAC;MAEzD,MAAMzC,oBAAS,CAAC5B,GAAG,CAAC,WAAW,EAAE;QAAEiC,OAAO,EAAEoC;MAAc,CAAC,CAAC;MAE5D5C,MAAM,CAACf,KAAK,CAAC,CAACgB,oBAAoB,CAChCD,MAAM,CAAC4B,GAAG,CAACC,MAAM,CAAC,EAClB7B,MAAM,CAACkB,gBAAgB,CAAC;QACtBV,OAAO,EAAER,MAAM,CAACkB,gBAAgB,CAAC0B,aAAa;MAChD,CAAC,CACH,CAAC;IACH,CAAC,EAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}