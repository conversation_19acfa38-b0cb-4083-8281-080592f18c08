f67ecb5f4928ef489426a636d3652202
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.isValidImageUrl = exports.getStoreImage = exports.getRandomStoreImage = exports.getFallbackImage = exports.getAvailableCategories = exports.STORE_IMAGES = void 0;
var STORE_IMAGES = exports.STORE_IMAGES = {
  hair_salon_1: 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400&h=400&fit=crop&crop=center',
  hair_salon_2: 'https://images.unsplash.com/photo-1521590832167-7bcbfaa6381f?w=400&h=400&fit=crop&crop=center',
  hair_salon_3: 'https://images.unsplash.com/photo-1562322140-8baeececf3df?w=400&h=400&fit=crop&crop=center',
  hair_salon_4: 'https://images.unsplash.com/photo-1559599101-f09722fb4948?w=400&h=400&fit=crop&crop=center',
  hair_salon_5: 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=400&fit=crop&crop=center',
  nail_salon_1: 'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400&h=400&fit=crop&crop=center',
  nail_salon_2: 'https://images.unsplash.com/photo-1607779097040-26e80aa78e66?w=400&h=400&fit=crop&crop=center',
  nail_salon_3: 'https://images.unsplash.com/photo-1610992015732-2449b76344bc?w=400&h=400&fit=crop&crop=center',
  nail_salon_4: 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=400&fit=crop&crop=center',
  nail_salon_5: 'https://images.unsplash.com/photo-1607779097040-26e80aa78e66?w=400&h=400&fit=crop&crop=center',
  lash_salon_1: 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=400&h=400&fit=crop&crop=center',
  lash_salon_2: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop&crop=center',
  lash_salon_3: 'https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?w=400&h=400&fit=crop&crop=center',
  lash_salon_4: 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=400&h=400&fit=crop&crop=center',
  lash_salon_5: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop&crop=center',
  braiding_salon_1: 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=400&fit=crop&crop=center',
  braiding_salon_2: 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400&h=400&fit=crop&crop=center',
  braiding_salon_3: 'https://images.unsplash.com/photo-1521590832167-7bcbfaa6381f?w=400&h=400&fit=crop&crop=center',
  braiding_salon_4: 'https://images.unsplash.com/photo-1562322140-8baeececf3df?w=400&h=400&fit=crop&crop=center',
  braiding_salon_5: 'https://images.unsplash.com/photo-1559599101-f09722fb4948?w=400&h=400&fit=crop&crop=center',
  massage_salon_1: 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400&h=400&fit=crop&crop=center',
  massage_salon_2: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop&crop=center',
  massage_salon_3: 'https://images.unsplash.com/photo-1540555700478-4be289fbecef?w=400&h=400&fit=crop&crop=center',
  massage_salon_4: 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400&h=400&fit=crop&crop=center',
  massage_salon_5: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop&crop=center',
  skincare_salon_1: 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400&h=400&fit=crop&crop=center',
  skincare_salon_2: 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=400&h=400&fit=crop&crop=center',
  skincare_salon_3: 'https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?w=400&h=400&fit=crop&crop=center',
  skincare_salon_4: 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400&h=400&fit=crop&crop=center',
  skincare_salon_5: 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=400&h=400&fit=crop&crop=center'
};
var CATEGORY_IMAGES = {
  Barber: ['barber_shop_1', 'barber_shop_2', 'barber_shop_3', 'barber_shop_4', 'barber_shop_5'],
  Salon: ['hair_salon_1', 'hair_salon_2', 'hair_salon_3', 'hair_salon_4', 'hair_salon_5'],
  'Nail Services': ['nail_salon_1', 'nail_salon_2', 'nail_salon_3', 'nail_salon_4', 'nail_salon_5'],
  'Lash Services': ['lash_salon_1', 'lash_salon_2', 'lash_salon_3', 'lash_salon_4', 'lash_salon_5'],
  Braiding: ['braiding_salon_1', 'braiding_salon_2', 'braiding_salon_3', 'braiding_salon_4', 'braiding_salon_5'],
  Massage: ['massage_salon_1', 'massage_salon_2', 'massage_salon_3', 'massage_salon_4', 'massage_salon_5'],
  Skincare: ['skincare_salon_1', 'skincare_salon_2', 'skincare_salon_3', 'skincare_salon_4', 'skincare_salon_5']
};
var getStoreImage = exports.getStoreImage = function getStoreImage(providerId, category) {
  if (!category) {
    return STORE_IMAGES.hair_salon_1;
  }
  var categoryImages = CATEGORY_IMAGES[category];
  if (!categoryImages) {
    return STORE_IMAGES.hair_salon_1;
  }
  var safeProviderId = providerId || 'default';
  var hash = safeProviderId.split('').reduce(function (acc, char) {
    return acc + char.charCodeAt(0);
  }, 0);
  var imageIndex = hash % categoryImages.length;
  var imageKey = categoryImages[imageIndex];
  return STORE_IMAGES[imageKey];
};
var getRandomStoreImage = exports.getRandomStoreImage = function getRandomStoreImage(category) {
  if (!category) {
    var allImages = Object.values(STORE_IMAGES);
    return allImages[Math.floor(Math.random() * allImages.length)];
  }
  var categoryImages = CATEGORY_IMAGES[category];
  if (!categoryImages) {
    return STORE_IMAGES.hair_salon_1;
  }
  var randomIndex = Math.floor(Math.random() * categoryImages.length);
  var imageKey = categoryImages[randomIndex];
  return STORE_IMAGES[imageKey];
};
var getAvailableCategories = exports.getAvailableCategories = function getAvailableCategories() {
  return Object.keys(CATEGORY_IMAGES);
};
var isValidImageUrl = exports.isValidImageUrl = function isValidImageUrl(url) {
  try {
    new URL(url);
    return true;
  } catch (_unused) {
    return false;
  }
};
var getFallbackImage = exports.getFallbackImage = function getFallbackImage(providerName) {
  var safeName = providerName || 'Provider';
  var firstLetter = safeName.charAt(0).toUpperCase();
  var charCode = firstLetter.charCodeAt(0);
  var allImages = Object.values(STORE_IMAGES);
  var imageIndex = charCode % allImages.length;
  return allImages[imageIndex];
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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