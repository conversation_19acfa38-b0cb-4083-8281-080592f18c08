{"version": 3, "names": ["TurboModuleRegistry", "_interopRequireWildcard", "require", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "NativeModule", "getEnforcing", "constants", "NativeStatusBarManager", "getConstants", "setColor", "color", "animated", "setTranslucent", "translucent", "setStyle", "statusBarStyle", "setHidden", "hidden", "_default", "exports"], "sources": ["NativeStatusBarManagerAndroid.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\nimport type {TurboModule} from '../../../../Libraries/TurboModule/RCTExport';\n\nimport * as TurboModuleRegistry from '../../../../Libraries/TurboModule/TurboModuleRegistry';\n\nexport interface Spec extends TurboModule {\n  +getConstants: () => {\n    +HEIGHT: number,\n    +DEFAULT_BACKGROUND_COLOR: number,\n  };\n  +setColor: (color: number, animated: boolean) => void;\n  +setTranslucent: (translucent: boolean) => void;\n\n  /**\n   *  - statusBarStyles can be:\n   *    - 'default'\n   *    - 'dark-content'\n   */\n  +setStyle: (statusBarStyle?: ?string) => void;\n  +setHidden: (hidden: boolean) => void;\n}\n\nconst NativeModule = TurboModuleRegistry.getEnforcing<Spec>('StatusBarManager');\nlet constants = null;\n\nconst NativeStatusBarManager = {\n  getConstants(): {\n    +HEIGHT: number,\n    +DEFAULT_BACKGROUND_COLOR?: number,\n  } {\n    if (constants == null) {\n      constants = NativeModule.getConstants();\n    }\n    return constants;\n  },\n\n  setColor(color: number, animated: boolean): void {\n    NativeModule.setColor(color, animated);\n  },\n\n  setTranslucent(translucent: boolean): void {\n    NativeModule.setTranslucent(translucent);\n  },\n\n  /**\n   *  - statusBarStyles can be:\n   *    - 'default'\n   *    - 'dark-content'\n   */\n  setStyle(statusBarStyle?: ?string): void {\n    NativeModule.setStyle(statusBarStyle);\n  },\n\n  setHidden(hidden: boolean): void {\n    NativeModule.setHidden(hidden);\n  },\n};\n\nexport default NativeStatusBarManager;\n"], "mappings": ";;;;AAYA,IAAAA,mBAAA,GAAAC,uBAAA,CAAAC,OAAA;AAA6F,SAAAD,wBAAAE,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAJ,uBAAA,YAAAA,wBAAAE,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAmB7F,IAAMmB,YAAY,GAAGvB,mBAAmB,CAACwB,YAAY,CAAO,kBAAkB,CAAC;AAC/E,IAAIC,SAAS,GAAG,IAAI;AAEpB,IAAMC,sBAAsB,GAAG;EAC7BC,YAAY,WAAZA,YAAYA,CAAA,EAGV;IACA,IAAIF,SAAS,IAAI,IAAI,EAAE;MACrBA,SAAS,GAAGF,YAAY,CAACI,YAAY,CAAC,CAAC;IACzC;IACA,OAAOF,SAAS;EAClB,CAAC;EAEDG,QAAQ,WAARA,QAAQA,CAACC,KAAa,EAAEC,QAAiB,EAAQ;IAC/CP,YAAY,CAACK,QAAQ,CAACC,KAAK,EAAEC,QAAQ,CAAC;EACxC,CAAC;EAEDC,cAAc,WAAdA,cAAcA,CAACC,WAAoB,EAAQ;IACzCT,YAAY,CAACQ,cAAc,CAACC,WAAW,CAAC;EAC1C,CAAC;EAODC,QAAQ,WAARA,QAAQA,CAACC,cAAwB,EAAQ;IACvCX,YAAY,CAACU,QAAQ,CAACC,cAAc,CAAC;EACvC,CAAC;EAEDC,SAAS,WAATA,SAASA,CAACC,MAAe,EAAQ;IAC/Bb,YAAY,CAACY,SAAS,CAACC,MAAM,CAAC;EAChC;AACF,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAzB,OAAA,GAEaa,sBAAsB", "ignoreList": []}