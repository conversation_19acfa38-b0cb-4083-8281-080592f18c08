{"version": 3, "names": ["ReactNativeFeatureFlags", "_interopRequireWildcard", "require", "_EventEmitter", "_interopRequireDefault", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "BatchedBridge", "infoLog", "TaskQueue", "invariant", "_emitter", "EventEmitter", "DEBUG_DELAY", "DEBUG", "InteractionManagerImpl", "Events", "interactionStart", "interactionComplete", "runAfterInteractions", "task", "tasks", "promise", "Promise", "resolve", "_scheduleUpdate", "push", "run", "name", "_taskQueue", "enqueueTasks", "then", "bind", "cancel", "cancelTasks", "createInteractionHandle", "handle", "_inc", "_addInteractionSet", "add", "clearInteractionHandle", "delete", "_deleteInteractionSet", "addListener", "setDeadline", "deadline", "_deadline", "_interactionSet", "Set", "onMoreTasks", "_nextUpdate<PERSON><PERSON>le", "setTimeout", "_processUpdate", "setImmediate", "interactionCount", "size", "for<PERSON>ach", "nextInteractionCount", "emit", "InteractionManager", "hasTasksToProcess", "processNext", "getEventLoopRunningTime", "clear", "disableInteractionManager", "_default", "exports"], "sources": ["InteractionManager.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\nimport type {Task} from './TaskQueue';\n\nimport * as ReactNativeFeatureFlags from '../../src/private/featureflags/ReactNativeFeatureFlags';\nimport EventEmitter from '../vendor/emitter/EventEmitter';\nimport type {EventSubscription} from '../vendor/emitter/EventEmitter';\n\nconst BatchedBridge = require('../BatchedBridge/BatchedBridge').default;\nconst infoLog = require('../Utilities/infoLog').default;\nconst TaskQueue = require('./TaskQueue').default;\nconst invariant = require('invariant');\n\nexport type {Task, SimpleTask, PromiseTask} from './TaskQueue';\n\nexport type Handle = number;\n\nconst _emitter = new EventEmitter<{\n  interactionComplete: [],\n  interactionStart: [],\n}>();\n\nconst DEBUG_DELAY: 0 = 0;\nconst DEBUG: false = false;\n\n/**\n * InteractionManager allows long-running work to be scheduled after any\n * interactions/animations have completed. In particular, this allows JavaScript\n * animations to run smoothly.\n *\n * Applications can schedule tasks to run after interactions with the following:\n *\n * ```\n * InteractionManager.runAfterInteractions(() => {\n *   // ...long-running synchronous task...\n * });\n * ```\n *\n * Compare this to other scheduling alternatives:\n *\n * - requestAnimationFrame(): for code that animates a view over time.\n * - setImmediate/setTimeout(): run code later, note this may delay animations.\n * - runAfterInteractions(): run code later, without delaying active animations.\n *\n * The touch handling system considers one or more active touches to be an\n * 'interaction' and will delay `runAfterInteractions()` callbacks until all\n * touches have ended or been cancelled.\n *\n * InteractionManager also allows applications to register animations by\n * creating an interaction 'handle' on animation start, and clearing it upon\n * completion:\n *\n * ```\n * var handle = InteractionManager.createInteractionHandle();\n * // run animation... (`runAfterInteractions` tasks are queued)\n * // later, on animation completion:\n * InteractionManager.clearInteractionHandle(handle);\n * // queued tasks run if all handles were cleared\n * ```\n *\n * `runAfterInteractions` takes either a plain callback function, or a\n * `PromiseTask` object with a `gen` method that returns a `Promise`.  If a\n * `PromiseTask` is supplied, then it is fully resolved (including asynchronous\n * dependencies that also schedule more tasks via `runAfterInteractions`) before\n * starting on the next task that might have been queued up synchronously\n * earlier.\n *\n * By default, queued tasks are executed together in a loop in one\n * `setImmediate` batch. If `setDeadline` is called with a positive number, then\n * tasks will only be executed until the deadline (in terms of js event loop run\n * time) approaches, at which point execution will yield via setTimeout,\n * allowing events such as touches to start interactions and block queued tasks\n * from executing, making apps more responsive.\n */\nconst InteractionManagerImpl = {\n  Events: {\n    interactionStart: 'interactionStart',\n    interactionComplete: 'interactionComplete',\n  },\n\n  /**\n   * Schedule a function to run after all interactions have completed. Returns a cancellable\n   * \"promise\".\n   */\n  runAfterInteractions(task: ?Task): {\n    then: <U>(\n      onFulfill?: ?(void) => ?(Promise<U> | U),\n      onReject?: ?(error: mixed) => ?(Promise<U> | U),\n    ) => Promise<U>,\n    cancel: () => void,\n    ...\n  } {\n    const tasks: Array<Task> = [];\n    const promise = new Promise((resolve: () => void) => {\n      _scheduleUpdate();\n      if (task) {\n        tasks.push(task);\n      }\n      tasks.push({\n        run: resolve,\n        name: 'resolve ' + ((task && task.name) || '?'),\n      });\n      _taskQueue.enqueueTasks(tasks);\n    });\n    return {\n      // $FlowFixMe[method-unbinding] added when improving typing for this parameters\n      then: promise.then.bind(promise),\n      cancel: function () {\n        _taskQueue.cancelTasks(tasks);\n      },\n    };\n  },\n\n  /**\n   * Notify manager that an interaction has started.\n   */\n  createInteractionHandle(): Handle {\n    DEBUG && infoLog('InteractionManager: create interaction handle');\n    _scheduleUpdate();\n    const handle = ++_inc;\n    _addInteractionSet.add(handle);\n    return handle;\n  },\n\n  /**\n   * Notify manager that an interaction has completed.\n   */\n  clearInteractionHandle(handle: Handle) {\n    DEBUG && infoLog('InteractionManager: clear interaction handle');\n    invariant(!!handle, 'InteractionManager: Must provide a handle to clear.');\n    _scheduleUpdate();\n    _addInteractionSet.delete(handle);\n    _deleteInteractionSet.add(handle);\n  },\n\n  // $FlowFixMe[unclear-type] unclear type of _emitter\n  // $FlowFixMe[method-unbinding] added when improving typing for this parameters\n  addListener: _emitter.addListener.bind(_emitter) as (\n    eventType: string,\n    // $FlowFixMe[unclear-type] unclear type of arguments\n    listener: (...args: any) => mixed,\n    context: mixed,\n  ) => EventSubscription,\n\n  /**\n   * A positive number will use setTimeout to schedule any tasks after the\n   * eventLoopRunningTime hits the deadline value, otherwise all tasks will be\n   * executed in one setImmediate batch (default).\n   */\n  setDeadline(deadline: number) {\n    _deadline = deadline;\n  },\n};\n\nconst _interactionSet = new Set<number | Handle>();\nconst _addInteractionSet = new Set<number | Handle>();\nconst _deleteInteractionSet = new Set<Handle>();\nconst _taskQueue = new TaskQueue({onMoreTasks: _scheduleUpdate});\nlet _nextUpdateHandle: $FlowFixMe | TimeoutID = 0;\nlet _inc = 0;\nlet _deadline = -1;\n\n/**\n * Schedule an asynchronous update to the interaction state.\n */\nfunction _scheduleUpdate() {\n  if (!_nextUpdateHandle) {\n    if (_deadline > 0) {\n      _nextUpdateHandle = setTimeout(_processUpdate, 0 + DEBUG_DELAY);\n    } else {\n      _nextUpdateHandle = setImmediate(_processUpdate);\n    }\n  }\n}\n\n/**\n * Notify listeners, process queue, etc\n */\nfunction _processUpdate() {\n  _nextUpdateHandle = 0;\n\n  const interactionCount = _interactionSet.size;\n  _addInteractionSet.forEach(handle => _interactionSet.add(handle));\n  _deleteInteractionSet.forEach(handle => _interactionSet.delete(handle));\n  const nextInteractionCount = _interactionSet.size;\n\n  if (interactionCount !== 0 && nextInteractionCount === 0) {\n    // transition from 1+ --> 0 interactions\n    _emitter.emit(InteractionManager.Events.interactionComplete);\n  } else if (interactionCount === 0 && nextInteractionCount !== 0) {\n    // transition from 0 --> 1+ interactions\n    _emitter.emit(InteractionManager.Events.interactionStart);\n  }\n\n  // process the queue regardless of a transition\n  if (nextInteractionCount === 0) {\n    while (_taskQueue.hasTasksToProcess()) {\n      _taskQueue.processNext();\n      if (\n        _deadline > 0 &&\n        BatchedBridge.getEventLoopRunningTime() >= _deadline\n      ) {\n        // Hit deadline before processing all tasks, so process more later.\n        _scheduleUpdate();\n        break;\n      }\n    }\n  }\n  _addInteractionSet.clear();\n  _deleteInteractionSet.clear();\n}\n\nconst InteractionManager = (\n  ReactNativeFeatureFlags.disableInteractionManager()\n    ? require('./InteractionManagerStub').default\n    : InteractionManagerImpl\n) as typeof InteractionManagerImpl;\n\nexport default InteractionManager;\n"], "mappings": ";;;;;AAYA,IAAAA,uBAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAC,sBAAA,CAAAF,OAAA;AAA0D,SAAAD,wBAAAI,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAN,uBAAA,YAAAA,wBAAAI,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAG1D,IAAMmB,aAAa,GAAGvB,OAAO,iCAAiC,CAAC,CAACa,OAAO;AACvE,IAAMW,OAAO,GAAGxB,OAAO,uBAAuB,CAAC,CAACa,OAAO;AACvD,IAAMY,SAAS,GAAGzB,OAAO,cAAc,CAAC,CAACa,OAAO;AAChD,IAAMa,SAAS,GAAG1B,OAAO,CAAC,WAAW,CAAC;AAMtC,IAAM2B,QAAQ,GAAG,IAAIC,qBAAY,CAG9B,CAAC;AAEJ,IAAMC,WAAc,GAAG,CAAC;AACxB,IAAMC,KAAY,GAAG,KAAK;AAmD1B,IAAMC,sBAAsB,GAAG;EAC7BC,MAAM,EAAE;IACNC,gBAAgB,EAAE,kBAAkB;IACpCC,mBAAmB,EAAE;EACvB,CAAC;EAMDC,oBAAoB,WAApBA,oBAAoBA,CAACC,IAAW,EAO9B;IACA,IAAMC,KAAkB,GAAG,EAAE;IAC7B,IAAMC,OAAO,GAAG,IAAIC,OAAO,CAAC,UAACC,OAAmB,EAAK;MACnDC,eAAe,CAAC,CAAC;MACjB,IAAIL,IAAI,EAAE;QACRC,KAAK,CAACK,IAAI,CAACN,IAAI,CAAC;MAClB;MACAC,KAAK,CAACK,IAAI,CAAC;QACTC,GAAG,EAAEH,OAAO;QACZI,IAAI,EAAE,UAAU,IAAKR,IAAI,IAAIA,IAAI,CAACQ,IAAI,IAAK,GAAG;MAChD,CAAC,CAAC;MACFC,UAAU,CAACC,YAAY,CAACT,KAAK,CAAC;IAChC,CAAC,CAAC;IACF,OAAO;MAELU,IAAI,EAAET,OAAO,CAACS,IAAI,CAACC,IAAI,CAACV,OAAO,CAAC;MAChCW,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAc;QAClBJ,UAAU,CAACK,WAAW,CAACb,KAAK,CAAC;MAC/B;IACF,CAAC;EACH,CAAC;EAKDc,uBAAuB,WAAvBA,uBAAuBA,CAAA,EAAW;IAChCrB,KAAK,IAAIN,OAAO,CAAC,+CAA+C,CAAC;IACjEiB,eAAe,CAAC,CAAC;IACjB,IAAMW,MAAM,GAAG,EAAEC,IAAI;IACrBC,kBAAkB,CAACC,GAAG,CAACH,MAAM,CAAC;IAC9B,OAAOA,MAAM;EACf,CAAC;EAKDI,sBAAsB,WAAtBA,sBAAsBA,CAACJ,MAAc,EAAE;IACrCtB,KAAK,IAAIN,OAAO,CAAC,8CAA8C,CAAC;IAChEE,SAAS,CAAC,CAAC,CAAC0B,MAAM,EAAE,qDAAqD,CAAC;IAC1EX,eAAe,CAAC,CAAC;IACjBa,kBAAkB,CAACG,MAAM,CAACL,MAAM,CAAC;IACjCM,qBAAqB,CAACH,GAAG,CAACH,MAAM,CAAC;EACnC,CAAC;EAIDO,WAAW,EAAEhC,QAAQ,CAACgC,WAAW,CAACX,IAAI,CAACrB,QAAQ,CAKzB;EAOtBiC,WAAW,WAAXA,WAAWA,CAACC,QAAgB,EAAE;IAC5BC,SAAS,GAAGD,QAAQ;EACtB;AACF,CAAC;AAED,IAAME,eAAe,GAAG,IAAIC,GAAG,CAAkB,CAAC;AAClD,IAAMV,kBAAkB,GAAG,IAAIU,GAAG,CAAkB,CAAC;AACrD,IAAMN,qBAAqB,GAAG,IAAIM,GAAG,CAAS,CAAC;AAC/C,IAAMnB,UAAU,GAAG,IAAIpB,SAAS,CAAC;EAACwC,WAAW,EAAExB;AAAe,CAAC,CAAC;AAChE,IAAIyB,iBAAyC,GAAG,CAAC;AACjD,IAAIb,IAAI,GAAG,CAAC;AACZ,IAAIS,SAAS,GAAG,CAAC,CAAC;AAKlB,SAASrB,eAAeA,CAAA,EAAG;EACzB,IAAI,CAACyB,iBAAiB,EAAE;IACtB,IAAIJ,SAAS,GAAG,CAAC,EAAE;MACjBI,iBAAiB,GAAGC,UAAU,CAACC,cAAc,EAAE,CAAC,GAAGvC,WAAW,CAAC;IACjE,CAAC,MAAM;MACLqC,iBAAiB,GAAGG,YAAY,CAACD,cAAc,CAAC;IAClD;EACF;AACF;AAKA,SAASA,cAAcA,CAAA,EAAG;EACxBF,iBAAiB,GAAG,CAAC;EAErB,IAAMI,gBAAgB,GAAGP,eAAe,CAACQ,IAAI;EAC7CjB,kBAAkB,CAACkB,OAAO,CAAC,UAAApB,MAAM;IAAA,OAAIW,eAAe,CAACR,GAAG,CAACH,MAAM,CAAC;EAAA,EAAC;EACjEM,qBAAqB,CAACc,OAAO,CAAC,UAAApB,MAAM;IAAA,OAAIW,eAAe,CAACN,MAAM,CAACL,MAAM,CAAC;EAAA,EAAC;EACvE,IAAMqB,oBAAoB,GAAGV,eAAe,CAACQ,IAAI;EAEjD,IAAID,gBAAgB,KAAK,CAAC,IAAIG,oBAAoB,KAAK,CAAC,EAAE;IAExD9C,QAAQ,CAAC+C,IAAI,CAACC,kBAAkB,CAAC3C,MAAM,CAACE,mBAAmB,CAAC;EAC9D,CAAC,MAAM,IAAIoC,gBAAgB,KAAK,CAAC,IAAIG,oBAAoB,KAAK,CAAC,EAAE;IAE/D9C,QAAQ,CAAC+C,IAAI,CAACC,kBAAkB,CAAC3C,MAAM,CAACC,gBAAgB,CAAC;EAC3D;EAGA,IAAIwC,oBAAoB,KAAK,CAAC,EAAE;IAC9B,OAAO5B,UAAU,CAAC+B,iBAAiB,CAAC,CAAC,EAAE;MACrC/B,UAAU,CAACgC,WAAW,CAAC,CAAC;MACxB,IACEf,SAAS,GAAG,CAAC,IACbvC,aAAa,CAACuD,uBAAuB,CAAC,CAAC,IAAIhB,SAAS,EACpD;QAEArB,eAAe,CAAC,CAAC;QACjB;MACF;IACF;EACF;EACAa,kBAAkB,CAACyB,KAAK,CAAC,CAAC;EAC1BrB,qBAAqB,CAACqB,KAAK,CAAC,CAAC;AAC/B;AAEA,IAAMJ,kBAAkB,GACtB7E,uBAAuB,CAACkF,yBAAyB,CAAC,CAAC,GAC/ChF,OAAO,2BAA2B,CAAC,CAACa,OAAO,GAC3CkB,sBAC4B;AAAC,IAAAkD,QAAA,GAAAC,OAAA,CAAArE,OAAA,GAEpB8D,kBAAkB", "ignoreList": []}