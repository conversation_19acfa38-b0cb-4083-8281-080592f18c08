{"version": 3, "names": ["PerformanceMonitoringService", "_classCallCheck2", "default", "metrics", "alerts", "observers", "isMonitoring", "reportingInterval", "thresholds", "metric", "warning", "critical", "unit", "sessionId", "generateSessionId", "initializeMonitoring", "_createClass2", "key", "value", "startMonitoring", "setupPerformanceObservers", "startPeriodicReporting", "trackAppLaunch", "console", "log", "stopMonitoring", "cleanupObservers", "stopPeriodicReporting", "trackMetric", "name", "arguments", "length", "undefined", "context", "tags", "timestamp", "Date", "now", "push", "checkThresholds", "__DEV__", "trackCustomMetric", "trackScreenRender", "screenName", "renderTime", "screen", "trackApiRequest", "endpoint", "method", "duration", "status", "size", "trackMemoryUsage", "performance", "memory", "usedJSHeapSize", "total", "totalJSHeapSize", "limit", "jsHeapSizeLimit", "trackBundleSize", "trackInteraction", "type", "element", "getPerformanceReport", "deviceInfo", "getDeviceInfo", "networkInfo", "getNetworkInfo", "appInfo", "getAppInfo", "_toConsumableArray2", "coreWebVitals", "getCoreWebVitals", "customMetrics", "getCustomMetrics", "errors", "get<PERSON><PERSON><PERSON>", "clearData", "exportData", "report", "JSON", "stringify", "for<PERSON>ach", "observer", "disconnect", "error", "warn", "_this", "setInterval", "sendPerformanceReport", "clearInterval", "launchTime", "threshold", "find", "t", "alertType", "thresholdValue", "alert", "id", "Math", "random", "toString", "substr", "message", "resolved", "platform", "osVersion", "deviceModel", "screenSize", "width", "height", "isLowEndDevice", "effectiveType", "version", "buildNumber", "environment", "_this2", "getMetricValue", "m", "lcp", "fid", "cls", "fcp", "ttfb", "includes", "performanceMonitoringService", "exports"], "sources": ["performanceMonitoringService.ts"], "sourcesContent": ["/**\n * Performance Monitoring Service - Advanced Performance Analytics\n *\n * Service Contract:\n * - Provides comprehensive performance monitoring and analytics\n * - Tracks Core Web Vitals and custom metrics\n * - Implements real-time performance alerts\n * - Handles performance data collection and reporting\n * - Supports A/B testing for performance optimizations\n * - Integrates with analytics platforms\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nexport interface PerformanceMetric {\n  name: string;\n  value: number;\n  unit: string;\n  timestamp: number;\n  context?: Record<string, any>;\n  tags?: string[];\n}\n\nexport interface CoreWebVitals {\n  lcp: number; // Largest Contentful Paint\n  fid: number; // First Input Delay\n  cls: number; // Cumulative Layout Shift\n  fcp: number; // First Contentful Paint\n  ttfb: number; // Time to First Byte\n}\n\nexport interface PerformanceReport {\n  sessionId: string;\n  userId?: string;\n  deviceInfo: DeviceInfo;\n  networkInfo: NetworkInfo;\n  appInfo: AppInfo;\n  metrics: PerformanceMetric[];\n  coreWebVitals: CoreWebVitals;\n  customMetrics: Record<string, number>;\n  errors: PerformanceError[];\n  timestamp: number;\n}\n\nexport interface DeviceInfo {\n  platform: string;\n  osVersion: string;\n  deviceModel: string;\n  screenSize: { width: number; height: number };\n  memorySize?: number;\n  cpuCores?: number;\n  isLowEndDevice: boolean;\n}\n\nexport interface NetworkInfo {\n  type: 'wifi' | 'cellular' | 'ethernet' | 'unknown';\n  effectiveType: '2g' | '3g' | '4g' | '5g' | 'unknown';\n  downlink?: number;\n  rtt?: number;\n  saveData?: boolean;\n}\n\nexport interface AppInfo {\n  version: string;\n  buildNumber: string;\n  environment: 'development' | 'staging' | 'production';\n  bundleSize?: number;\n  jsHeapSize?: number;\n}\n\nexport interface PerformanceError {\n  type: 'javascript' | 'network' | 'render' | 'memory';\n  message: string;\n  stack?: string;\n  timestamp: number;\n  context?: Record<string, any>;\n}\n\nexport interface PerformanceThreshold {\n  metric: string;\n  warning: number;\n  critical: number;\n  unit: string;\n}\n\nexport interface PerformanceAlert {\n  id: string;\n  type: 'warning' | 'critical';\n  metric: string;\n  value: number;\n  threshold: number;\n  message: string;\n  timestamp: number;\n  resolved: boolean;\n}\n\nclass PerformanceMonitoringService {\n  private sessionId: string;\n  private metrics: PerformanceMetric[] = [];\n  private alerts: PerformanceAlert[] = [];\n  private observers: PerformanceObserver[] = [];\n  private isMonitoring = false;\n  private reportingInterval: NodeJS.Timeout | null = null;\n\n  // Performance thresholds\n  private thresholds: PerformanceThreshold[] = [\n    { metric: 'lcp', warning: 2500, critical: 4000, unit: 'ms' },\n    { metric: 'fid', warning: 100, critical: 300, unit: 'ms' },\n    { metric: 'cls', warning: 0.1, critical: 0.25, unit: 'score' },\n    { metric: 'fcp', warning: 1800, critical: 3000, unit: 'ms' },\n    { metric: 'ttfb', warning: 800, critical: 1800, unit: 'ms' },\n    { metric: 'memory_usage', warning: 50, critical: 80, unit: 'mb' },\n    { metric: 'bundle_size', warning: 1000, critical: 2000, unit: 'kb' },\n  ];\n\n  constructor() {\n    this.sessionId = this.generateSessionId();\n    this.initializeMonitoring();\n  }\n\n  /**\n   * Start performance monitoring\n   */\n  startMonitoring(): void {\n    if (this.isMonitoring) return;\n\n    this.isMonitoring = true;\n    this.setupPerformanceObservers();\n    this.startPeriodicReporting();\n    this.trackAppLaunch();\n\n    console.log('[Performance] Monitoring started');\n  }\n\n  /**\n   * Stop performance monitoring\n   */\n  stopMonitoring(): void {\n    if (!this.isMonitoring) return;\n\n    this.isMonitoring = false;\n    this.cleanupObservers();\n    this.stopPeriodicReporting();\n\n    console.log('[Performance] Monitoring stopped');\n  }\n\n  /**\n   * Track custom performance metric\n   */\n  trackMetric(\n    name: string,\n    value: number,\n    unit: string = 'ms',\n    context?: Record<string, any>,\n    tags?: string[],\n  ): void {\n    const metric: PerformanceMetric = {\n      name,\n      value,\n      unit,\n      timestamp: Date.now(),\n      context,\n      tags,\n    };\n\n    this.metrics.push(metric);\n    this.checkThresholds(metric);\n\n    // Log metric in development\n    if (__DEV__) {\n      console.log(`[Performance] ${name}: ${value}${unit}`, context);\n    }\n  }\n\n  /**\n   * Track custom metric (alias for trackMetric for compatibility)\n   */\n  trackCustomMetric(\n    name: string,\n    value: number,\n    unit: string = 'ms',\n    context?: Record<string, any>,\n    tags?: string[],\n  ): void {\n    this.trackMetric(name, value, unit, context, tags);\n  }\n\n  /**\n   * Track screen render time\n   */\n  trackScreenRender(screenName: string, renderTime: number): void {\n    this.trackMetric(\n      'screen_render_time',\n      renderTime,\n      'ms',\n      {\n        screen: screenName,\n      },\n      ['render', 'screen'],\n    );\n  }\n\n  /**\n   * Track API request performance\n   */\n  trackApiRequest(\n    endpoint: string,\n    method: string,\n    duration: number,\n    status: number,\n    size?: number,\n  ): void {\n    this.trackMetric(\n      'api_request_duration',\n      duration,\n      'ms',\n      {\n        endpoint,\n        method,\n        status,\n        size,\n      },\n      ['api', 'network'],\n    );\n\n    if (size) {\n      this.trackMetric(\n        'api_response_size',\n        size,\n        'bytes',\n        {\n          endpoint,\n          method,\n        },\n        ['api', 'network', 'size'],\n      );\n    }\n  }\n\n  /**\n   * Track memory usage\n   */\n  trackMemoryUsage(): void {\n    if (typeof performance !== 'undefined' && 'memory' in performance) {\n      const memory = (performance as any).memory;\n\n      this.trackMetric(\n        'memory_used',\n        memory.usedJSHeapSize / 1024 / 1024,\n        'mb',\n        {\n          total: memory.totalJSHeapSize / 1024 / 1024,\n          limit: memory.jsHeapSizeLimit / 1024 / 1024,\n        },\n        ['memory'],\n      );\n    }\n  }\n\n  /**\n   * Track bundle size\n   */\n  trackBundleSize(size: number): void {\n    this.trackMetric('bundle_size', size / 1024, 'kb', {}, ['bundle', 'size']);\n  }\n\n  /**\n   * Track user interaction performance\n   */\n  trackInteraction(\n    type: 'tap' | 'scroll' | 'swipe' | 'input',\n    duration: number,\n    element?: string,\n  ): void {\n    this.trackMetric(\n      'interaction_duration',\n      duration,\n      'ms',\n      {\n        type,\n        element,\n      },\n      ['interaction', 'user'],\n    );\n  }\n\n  /**\n   * Get current performance report\n   */\n  getPerformanceReport(): PerformanceReport {\n    return {\n      sessionId: this.sessionId,\n      deviceInfo: this.getDeviceInfo(),\n      networkInfo: this.getNetworkInfo(),\n      appInfo: this.getAppInfo(),\n      metrics: [...this.metrics],\n      coreWebVitals: this.getCoreWebVitals(),\n      customMetrics: this.getCustomMetrics(),\n      errors: [],\n      timestamp: Date.now(),\n    };\n  }\n\n  /**\n   * Get performance alerts\n   */\n  getAlerts(): PerformanceAlert[] {\n    return [...this.alerts];\n  }\n\n  /**\n   * Clear performance data\n   */\n  clearData(): void {\n    this.metrics = [];\n    this.alerts = [];\n    this.sessionId = this.generateSessionId();\n  }\n\n  /**\n   * Export performance data\n   */\n  exportData(): string {\n    const report = this.getPerformanceReport();\n    return JSON.stringify(report, null, 2);\n  }\n\n  private initializeMonitoring(): void {\n    // Auto-start monitoring in production\n    if (!__DEV__) {\n      this.startMonitoring();\n    }\n  }\n\n  private setupPerformanceObservers(): void {\n    // This would be implemented differently in React Native\n    // For now, we'll use manual tracking\n  }\n\n  private cleanupObservers(): void {\n    this.observers.forEach(observer => {\n      try {\n        observer.disconnect();\n      } catch (error) {\n        console.warn('[Performance] Failed to disconnect observer:', error);\n      }\n    });\n    this.observers = [];\n  }\n\n  private startPeriodicReporting(): void {\n    this.reportingInterval = setInterval(() => {\n      this.trackMemoryUsage();\n      this.sendPerformanceReport();\n    }, 30000); // Report every 30 seconds\n  }\n\n  private stopPeriodicReporting(): void {\n    if (this.reportingInterval) {\n      clearInterval(this.reportingInterval);\n      this.reportingInterval = null;\n    }\n  }\n\n  private trackAppLaunch(): void {\n    const launchTime = Date.now();\n    this.trackMetric('app_launch_time', launchTime, 'ms', {}, ['launch']);\n  }\n\n  private checkThresholds(metric: PerformanceMetric): void {\n    const threshold = this.thresholds.find(t => t.metric === metric.name);\n    if (!threshold) return;\n\n    let alertType: 'warning' | 'critical' | null = null;\n    let thresholdValue = 0;\n\n    if (metric.value >= threshold.critical) {\n      alertType = 'critical';\n      thresholdValue = threshold.critical;\n    } else if (metric.value >= threshold.warning) {\n      alertType = 'warning';\n      thresholdValue = threshold.warning;\n    }\n\n    if (alertType) {\n      const alert: PerformanceAlert = {\n        id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        type: alertType,\n        metric: metric.name,\n        value: metric.value,\n        threshold: thresholdValue,\n        message: `${metric.name} exceeded ${alertType} threshold: ${metric.value}${metric.unit} > ${thresholdValue}${threshold.unit}`,\n        timestamp: Date.now(),\n        resolved: false,\n      };\n\n      this.alerts.push(alert);\n\n      if (__DEV__) {\n        console.warn(`[Performance Alert] ${alert.message}`);\n      }\n    }\n  }\n\n  private sendPerformanceReport(): void {\n    // In a real implementation, this would send data to analytics service\n    const report = this.getPerformanceReport();\n\n    if (__DEV__) {\n      console.log('[Performance] Report generated:', {\n        metrics: report.metrics.length,\n        alerts: this.alerts.length,\n      });\n    }\n  }\n\n  private getDeviceInfo(): DeviceInfo {\n    // This would be implemented using react-native-device-info\n    return {\n      platform: 'ios', // or 'android'\n      osVersion: '17.0',\n      deviceModel: 'iPhone 15',\n      screenSize: { width: 393, height: 852 },\n      isLowEndDevice: false,\n    };\n  }\n\n  private getNetworkInfo(): NetworkInfo {\n    // This would be implemented using @react-native-community/netinfo\n    return {\n      type: 'wifi',\n      effectiveType: '4g',\n    };\n  }\n\n  private getAppInfo(): AppInfo {\n    return {\n      version: '1.0.0',\n      buildNumber: '1',\n      environment: __DEV__ ? 'development' : 'production',\n    };\n  }\n\n  private getCoreWebVitals(): CoreWebVitals {\n    // Extract Core Web Vitals from collected metrics\n    const getMetricValue = (name: string) => {\n      const metric = this.metrics.find(m => m.name === name);\n      return metric ? metric.value : 0;\n    };\n\n    return {\n      lcp: getMetricValue('lcp'),\n      fid: getMetricValue('fid'),\n      cls: getMetricValue('cls'),\n      fcp: getMetricValue('fcp'),\n      ttfb: getMetricValue('ttfb'),\n    };\n  }\n\n  private getCustomMetrics(): Record<string, number> {\n    const customMetrics: Record<string, number> = {};\n\n    this.metrics.forEach(metric => {\n      if (!['lcp', 'fid', 'cls', 'fcp', 'ttfb'].includes(metric.name)) {\n        customMetrics[metric.name] = metric.value;\n      }\n    });\n\n    return customMetrics;\n  }\n\n  private generateSessionId(): string {\n    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n}\n\nexport const performanceMonitoringService = new PerformanceMonitoringService();\n"], "mappings": ";;;;;;;;IAiGMA,4BAA4B;EAmBhC,SAAAA,6BAAA,EAAc;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAF,4BAAA;IAAA,KAjBNG,OAAO,GAAwB,EAAE;IAAA,KACjCC,MAAM,GAAuB,EAAE;IAAA,KAC/BC,SAAS,GAA0B,EAAE;IAAA,KACrCC,YAAY,GAAG,KAAK;IAAA,KACpBC,iBAAiB,GAA0B,IAAI;IAAA,KAG/CC,UAAU,GAA2B,CAC3C;MAAEC,MAAM,EAAE,KAAK;MAAEC,OAAO,EAAE,IAAI;MAAEC,QAAQ,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAK,CAAC,EAC5D;MAAEH,MAAM,EAAE,KAAK;MAAEC,OAAO,EAAE,GAAG;MAAEC,QAAQ,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAK,CAAC,EAC1D;MAAEH,MAAM,EAAE,KAAK;MAAEC,OAAO,EAAE,GAAG;MAAEC,QAAQ,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAQ,CAAC,EAC9D;MAAEH,MAAM,EAAE,KAAK;MAAEC,OAAO,EAAE,IAAI;MAAEC,QAAQ,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAK,CAAC,EAC5D;MAAEH,MAAM,EAAE,MAAM;MAAEC,OAAO,EAAE,GAAG;MAAEC,QAAQ,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAK,CAAC,EAC5D;MAAEH,MAAM,EAAE,cAAc;MAAEC,OAAO,EAAE,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAK,CAAC,EACjE;MAAEH,MAAM,EAAE,aAAa;MAAEC,OAAO,EAAE,IAAI;MAAEC,QAAQ,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAK,CAAC,CACrE;IAGC,IAAI,CAACC,SAAS,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACzC,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC7B;EAAC,WAAAC,aAAA,CAAAd,OAAA,EAAAF,4BAAA;IAAAiB,GAAA;IAAAC,KAAA,EAKD,SAAAC,eAAeA,CAAA,EAAS;MACtB,IAAI,IAAI,CAACb,YAAY,EAAE;MAEvB,IAAI,CAACA,YAAY,GAAG,IAAI;MACxB,IAAI,CAACc,yBAAyB,CAAC,CAAC;MAChC,IAAI,CAACC,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAACC,cAAc,CAAC,CAAC;MAErBC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IACjD;EAAC;IAAAP,GAAA;IAAAC,KAAA,EAKD,SAAAO,cAAcA,CAAA,EAAS;MACrB,IAAI,CAAC,IAAI,CAACnB,YAAY,EAAE;MAExB,IAAI,CAACA,YAAY,GAAG,KAAK;MACzB,IAAI,CAACoB,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACC,qBAAqB,CAAC,CAAC;MAE5BJ,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IACjD;EAAC;IAAAP,GAAA;IAAAC,KAAA,EAKD,SAAAU,WAAWA,CACTC,IAAY,EACZX,KAAa,EAIP;MAAA,IAHNN,IAAY,GAAAkB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MAAA,IACnBG,OAA6B,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;MAAA,IAC7BE,IAAe,GAAAJ,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;MAEf,IAAMvB,MAAyB,GAAG;QAChCoB,IAAI,EAAJA,IAAI;QACJX,KAAK,EAALA,KAAK;QACLN,IAAI,EAAJA,IAAI;QACJuB,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBJ,OAAO,EAAPA,OAAO;QACPC,IAAI,EAAJA;MACF,CAAC;MAED,IAAI,CAAC/B,OAAO,CAACmC,IAAI,CAAC7B,MAAM,CAAC;MACzB,IAAI,CAAC8B,eAAe,CAAC9B,MAAM,CAAC;MAG5B,IAAI+B,OAAO,EAAE;QACXjB,OAAO,CAACC,GAAG,CAAC,iBAAiBK,IAAI,KAAKX,KAAK,GAAGN,IAAI,EAAE,EAAEqB,OAAO,CAAC;MAChE;IACF;EAAC;IAAAhB,GAAA;IAAAC,KAAA,EAKD,SAAAuB,iBAAiBA,CACfZ,IAAY,EACZX,KAAa,EAIP;MAAA,IAHNN,IAAY,GAAAkB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MAAA,IACnBG,OAA6B,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;MAAA,IAC7BE,IAAe,GAAAJ,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;MAEf,IAAI,CAACJ,WAAW,CAACC,IAAI,EAAEX,KAAK,EAAEN,IAAI,EAAEqB,OAAO,EAAEC,IAAI,CAAC;IACpD;EAAC;IAAAjB,GAAA;IAAAC,KAAA,EAKD,SAAAwB,iBAAiBA,CAACC,UAAkB,EAAEC,UAAkB,EAAQ;MAC9D,IAAI,CAAChB,WAAW,CACd,oBAAoB,EACpBgB,UAAU,EACV,IAAI,EACJ;QACEC,MAAM,EAAEF;MACV,CAAC,EACD,CAAC,QAAQ,EAAE,QAAQ,CACrB,CAAC;IACH;EAAC;IAAA1B,GAAA;IAAAC,KAAA,EAKD,SAAA4B,eAAeA,CACbC,QAAgB,EAChBC,MAAc,EACdC,QAAgB,EAChBC,MAAc,EACdC,IAAa,EACP;MACN,IAAI,CAACvB,WAAW,CACd,sBAAsB,EACtBqB,QAAQ,EACR,IAAI,EACJ;QACEF,QAAQ,EAARA,QAAQ;QACRC,MAAM,EAANA,MAAM;QACNE,MAAM,EAANA,MAAM;QACNC,IAAI,EAAJA;MACF,CAAC,EACD,CAAC,KAAK,EAAE,SAAS,CACnB,CAAC;MAED,IAAIA,IAAI,EAAE;QACR,IAAI,CAACvB,WAAW,CACd,mBAAmB,EACnBuB,IAAI,EACJ,OAAO,EACP;UACEJ,QAAQ,EAARA,QAAQ;UACRC,MAAM,EAANA;QACF,CAAC,EACD,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,CAC3B,CAAC;MACH;IACF;EAAC;IAAA/B,GAAA;IAAAC,KAAA,EAKD,SAAAkC,gBAAgBA,CAAA,EAAS;MACvB,IAAI,OAAOC,WAAW,KAAK,WAAW,IAAI,QAAQ,IAAIA,WAAW,EAAE;QACjE,IAAMC,MAAM,GAAID,WAAW,CAASC,MAAM;QAE1C,IAAI,CAAC1B,WAAW,CACd,aAAa,EACb0B,MAAM,CAACC,cAAc,GAAG,IAAI,GAAG,IAAI,EACnC,IAAI,EACJ;UACEC,KAAK,EAAEF,MAAM,CAACG,eAAe,GAAG,IAAI,GAAG,IAAI;UAC3CC,KAAK,EAAEJ,MAAM,CAACK,eAAe,GAAG,IAAI,GAAG;QACzC,CAAC,EACD,CAAC,QAAQ,CACX,CAAC;MACH;IACF;EAAC;IAAA1C,GAAA;IAAAC,KAAA,EAKD,SAAA0C,eAAeA,CAACT,IAAY,EAAQ;MAClC,IAAI,CAACvB,WAAW,CAAC,aAAa,EAAEuB,IAAI,GAAG,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IAC5E;EAAC;IAAAlC,GAAA;IAAAC,KAAA,EAKD,SAAA2C,gBAAgBA,CACdC,IAA0C,EAC1Cb,QAAgB,EAChBc,OAAgB,EACV;MACN,IAAI,CAACnC,WAAW,CACd,sBAAsB,EACtBqB,QAAQ,EACR,IAAI,EACJ;QACEa,IAAI,EAAJA,IAAI;QACJC,OAAO,EAAPA;MACF,CAAC,EACD,CAAC,aAAa,EAAE,MAAM,CACxB,CAAC;IACH;EAAC;IAAA9C,GAAA;IAAAC,KAAA,EAKD,SAAA8C,oBAAoBA,CAAA,EAAsB;MACxC,OAAO;QACLnD,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBoD,UAAU,EAAE,IAAI,CAACC,aAAa,CAAC,CAAC;QAChCC,WAAW,EAAE,IAAI,CAACC,cAAc,CAAC,CAAC;QAClCC,OAAO,EAAE,IAAI,CAACC,UAAU,CAAC,CAAC;QAC1BnE,OAAO,MAAAoE,mBAAA,CAAArE,OAAA,EAAM,IAAI,CAACC,OAAO,CAAC;QAC1BqE,aAAa,EAAE,IAAI,CAACC,gBAAgB,CAAC,CAAC;QACtCC,aAAa,EAAE,IAAI,CAACC,gBAAgB,CAAC,CAAC;QACtCC,MAAM,EAAE,EAAE;QACVzC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;MACtB,CAAC;IACH;EAAC;IAAApB,GAAA;IAAAC,KAAA,EAKD,SAAA2D,SAASA,CAAA,EAAuB;MAC9B,WAAAN,mBAAA,CAAArE,OAAA,EAAW,IAAI,CAACE,MAAM;IACxB;EAAC;IAAAa,GAAA;IAAAC,KAAA,EAKD,SAAA4D,SAASA,CAAA,EAAS;MAChB,IAAI,CAAC3E,OAAO,GAAG,EAAE;MACjB,IAAI,CAACC,MAAM,GAAG,EAAE;MAChB,IAAI,CAACS,SAAS,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAC3C;EAAC;IAAAG,GAAA;IAAAC,KAAA,EAKD,SAAA6D,UAAUA,CAAA,EAAW;MACnB,IAAMC,MAAM,GAAG,IAAI,CAAChB,oBAAoB,CAAC,CAAC;MAC1C,OAAOiB,IAAI,CAACC,SAAS,CAACF,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;IACxC;EAAC;IAAA/D,GAAA;IAAAC,KAAA,EAED,SAAQH,oBAAoBA,CAAA,EAAS;MAEnC,IAAI,CAACyB,OAAO,EAAE;QACZ,IAAI,CAACrB,eAAe,CAAC,CAAC;MACxB;IACF;EAAC;IAAAF,GAAA;IAAAC,KAAA,EAED,SAAQE,yBAAyBA,CAAA,EAAS,CAG1C;EAAC;IAAAH,GAAA;IAAAC,KAAA,EAED,SAAQQ,gBAAgBA,CAAA,EAAS;MAC/B,IAAI,CAACrB,SAAS,CAAC8E,OAAO,CAAC,UAAAC,QAAQ,EAAI;QACjC,IAAI;UACFA,QAAQ,CAACC,UAAU,CAAC,CAAC;QACvB,CAAC,CAAC,OAAOC,KAAK,EAAE;UACd/D,OAAO,CAACgE,IAAI,CAAC,8CAA8C,EAAED,KAAK,CAAC;QACrE;MACF,CAAC,CAAC;MACF,IAAI,CAACjF,SAAS,GAAG,EAAE;IACrB;EAAC;IAAAY,GAAA;IAAAC,KAAA,EAED,SAAQG,sBAAsBA,CAAA,EAAS;MAAA,IAAAmE,KAAA;MACrC,IAAI,CAACjF,iBAAiB,GAAGkF,WAAW,CAAC,YAAM;QACzCD,KAAI,CAACpC,gBAAgB,CAAC,CAAC;QACvBoC,KAAI,CAACE,qBAAqB,CAAC,CAAC;MAC9B,CAAC,EAAE,KAAK,CAAC;IACX;EAAC;IAAAzE,GAAA;IAAAC,KAAA,EAED,SAAQS,qBAAqBA,CAAA,EAAS;MACpC,IAAI,IAAI,CAACpB,iBAAiB,EAAE;QAC1BoF,aAAa,CAAC,IAAI,CAACpF,iBAAiB,CAAC;QACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;MAC/B;IACF;EAAC;IAAAU,GAAA;IAAAC,KAAA,EAED,SAAQI,cAAcA,CAAA,EAAS;MAC7B,IAAMsE,UAAU,GAAGxD,IAAI,CAACC,GAAG,CAAC,CAAC;MAC7B,IAAI,CAACT,WAAW,CAAC,iBAAiB,EAAEgE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;IACvE;EAAC;IAAA3E,GAAA;IAAAC,KAAA,EAED,SAAQqB,eAAeA,CAAC9B,MAAyB,EAAQ;MACvD,IAAMoF,SAAS,GAAG,IAAI,CAACrF,UAAU,CAACsF,IAAI,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAACtF,MAAM,KAAKA,MAAM,CAACoB,IAAI;MAAA,EAAC;MACrE,IAAI,CAACgE,SAAS,EAAE;MAEhB,IAAIG,SAAwC,GAAG,IAAI;MACnD,IAAIC,cAAc,GAAG,CAAC;MAEtB,IAAIxF,MAAM,CAACS,KAAK,IAAI2E,SAAS,CAAClF,QAAQ,EAAE;QACtCqF,SAAS,GAAG,UAAU;QACtBC,cAAc,GAAGJ,SAAS,CAAClF,QAAQ;MACrC,CAAC,MAAM,IAAIF,MAAM,CAACS,KAAK,IAAI2E,SAAS,CAACnF,OAAO,EAAE;QAC5CsF,SAAS,GAAG,SAAS;QACrBC,cAAc,GAAGJ,SAAS,CAACnF,OAAO;MACpC;MAEA,IAAIsF,SAAS,EAAE;QACb,IAAME,KAAuB,GAAG;UAC9BC,EAAE,EAAE,SAAS/D,IAAI,CAACC,GAAG,CAAC,CAAC,IAAI+D,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACpEzC,IAAI,EAAEkC,SAAS;UACfvF,MAAM,EAAEA,MAAM,CAACoB,IAAI;UACnBX,KAAK,EAAET,MAAM,CAACS,KAAK;UACnB2E,SAAS,EAAEI,cAAc;UACzBO,OAAO,EAAE,GAAG/F,MAAM,CAACoB,IAAI,aAAamE,SAAS,eAAevF,MAAM,CAACS,KAAK,GAAGT,MAAM,CAACG,IAAI,MAAMqF,cAAc,GAAGJ,SAAS,CAACjF,IAAI,EAAE;UAC7HuB,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;UACrBoE,QAAQ,EAAE;QACZ,CAAC;QAED,IAAI,CAACrG,MAAM,CAACkC,IAAI,CAAC4D,KAAK,CAAC;QAEvB,IAAI1D,OAAO,EAAE;UACXjB,OAAO,CAACgE,IAAI,CAAC,uBAAuBW,KAAK,CAACM,OAAO,EAAE,CAAC;QACtD;MACF;IACF;EAAC;IAAAvF,GAAA;IAAAC,KAAA,EAED,SAAQwE,qBAAqBA,CAAA,EAAS;MAEpC,IAAMV,MAAM,GAAG,IAAI,CAAChB,oBAAoB,CAAC,CAAC;MAE1C,IAAIxB,OAAO,EAAE;QACXjB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;UAC7CrB,OAAO,EAAE6E,MAAM,CAAC7E,OAAO,CAAC4B,MAAM;UAC9B3B,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC2B;QACtB,CAAC,CAAC;MACJ;IACF;EAAC;IAAAd,GAAA;IAAAC,KAAA,EAED,SAAQgD,aAAaA,CAAA,EAAe;MAElC,OAAO;QACLwC,QAAQ,EAAE,KAAK;QACfC,SAAS,EAAE,MAAM;QACjBC,WAAW,EAAE,WAAW;QACxBC,UAAU,EAAE;UAAEC,KAAK,EAAE,GAAG;UAAEC,MAAM,EAAE;QAAI,CAAC;QACvCC,cAAc,EAAE;MAClB,CAAC;IACH;EAAC;IAAA/F,GAAA;IAAAC,KAAA,EAED,SAAQkD,cAAcA,CAAA,EAAgB;MAEpC,OAAO;QACLN,IAAI,EAAE,MAAM;QACZmD,aAAa,EAAE;MACjB,CAAC;IACH;EAAC;IAAAhG,GAAA;IAAAC,KAAA,EAED,SAAQoD,UAAUA,CAAA,EAAY;MAC5B,OAAO;QACL4C,OAAO,EAAE,OAAO;QAChBC,WAAW,EAAE,GAAG;QAChBC,WAAW,EAAE5E,OAAO,GAAG,aAAa,GAAG;MACzC,CAAC;IACH;EAAC;IAAAvB,GAAA;IAAAC,KAAA,EAED,SAAQuD,gBAAgBA,CAAA,EAAkB;MAAA,IAAA4C,MAAA;MAExC,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAIzF,IAAY,EAAK;QACvC,IAAMpB,MAAM,GAAG4G,MAAI,CAAClH,OAAO,CAAC2F,IAAI,CAAC,UAAAyB,CAAC;UAAA,OAAIA,CAAC,CAAC1F,IAAI,KAAKA,IAAI;QAAA,EAAC;QACtD,OAAOpB,MAAM,GAAGA,MAAM,CAACS,KAAK,GAAG,CAAC;MAClC,CAAC;MAED,OAAO;QACLsG,GAAG,EAAEF,cAAc,CAAC,KAAK,CAAC;QAC1BG,GAAG,EAAEH,cAAc,CAAC,KAAK,CAAC;QAC1BI,GAAG,EAAEJ,cAAc,CAAC,KAAK,CAAC;QAC1BK,GAAG,EAAEL,cAAc,CAAC,KAAK,CAAC;QAC1BM,IAAI,EAAEN,cAAc,CAAC,MAAM;MAC7B,CAAC;IACH;EAAC;IAAArG,GAAA;IAAAC,KAAA,EAED,SAAQyD,gBAAgBA,CAAA,EAA2B;MACjD,IAAMD,aAAqC,GAAG,CAAC,CAAC;MAEhD,IAAI,CAACvE,OAAO,CAACgF,OAAO,CAAC,UAAA1E,MAAM,EAAI;QAC7B,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAACoH,QAAQ,CAACpH,MAAM,CAACoB,IAAI,CAAC,EAAE;UAC/D6C,aAAa,CAACjE,MAAM,CAACoB,IAAI,CAAC,GAAGpB,MAAM,CAACS,KAAK;QAC3C;MACF,CAAC,CAAC;MAEF,OAAOwD,aAAa;IACtB;EAAC;IAAAzD,GAAA;IAAAC,KAAA,EAED,SAAQJ,iBAAiBA,CAAA,EAAW;MAClC,OAAO,WAAWsB,IAAI,CAACC,GAAG,CAAC,CAAC,IAAI+D,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAC3E;EAAC;AAAA;AAGI,IAAMuB,4BAA4B,GAAAC,OAAA,CAAAD,4BAAA,GAAG,IAAI9H,4BAA4B,CAAC,CAAC", "ignoreList": []}