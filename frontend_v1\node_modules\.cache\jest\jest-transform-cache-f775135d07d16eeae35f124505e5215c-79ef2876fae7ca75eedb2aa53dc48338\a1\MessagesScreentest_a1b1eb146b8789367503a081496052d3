7747c7b2520e611e4baecf7fe78ba16e
_getJestObj().mock("../../services/messagingService", function () {
  return {
    getConversations: jest.fn(),
    getMessages: jest.fn(),
    sendMessage: jest.fn(),
    markAsRead: jest.fn()
  };
});
_getJestObj().mock("../../services/websocketService", function () {
  return {
    connect: jest.fn(),
    disconnect: jest.fn(),
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn()
  };
});
_getJestObj().mock("../../hooks/usePerformance", function () {
  return {
    usePerformance: function usePerformance() {
      return {
        trackUserInteraction: jest.fn(function (name, fn) {
          return fn();
        }),
        measureRenderTime: jest.fn(),
        trackMemoryUsage: jest.fn()
      };
    }
  };
});
_getJestObj().mock("../../hooks/useErrorHandling", function () {
  return {
    useErrorHandling: function useErrorHandling() {
      return {
        handleError: jest.fn(),
        clearError: jest.fn(),
        isError: false,
        error: null
      };
    }
  };
});
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _native = require("@react-navigation/native");
var _stack = require("@react-navigation/stack");
var _reactNative = require("@testing-library/react-native");
var _react = _interopRequireDefault(require("react"));
var _reactRedux = require("react-redux");
var _ThemeContext = require("../../contexts/ThemeContext");
var _MessagesScreen = require("../../screens/MessagesScreen");
var _messagingData = require("../__mocks__/messagingData");
var _theme = require("../__mocks__/theme");
var _testUtils = require("../utils/testUtils");
var _jsxRuntime = require("react/jsx-runtime");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var mockStore = (0, _testUtils.createMockStore)({
  auth: {
    user: {
      id: 'user-1',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>'
    },
    isAuthenticated: true
  }
});
var Stack = (0, _stack.createStackNavigator)();
var TestWrapper = function TestWrapper(_ref) {
  var children = _ref.children;
  return (0, _jsxRuntime.jsx)(_reactRedux.Provider, {
    store: mockStore,
    children: (0, _jsxRuntime.jsx)(_ThemeContext.ThemeProvider, {
      theme: _theme.mockTheme,
      children: (0, _jsxRuntime.jsx)(_native.NavigationContainer, {
        children: (0, _jsxRuntime.jsx)(Stack.Navigator, {
          children: (0, _jsxRuntime.jsx)(Stack.Screen, {
            name: "Messages",
            component: function component() {
              return children;
            }
          })
        })
      })
    })
  });
};
describe('MessagesScreen', function () {
  var mockMessagingService = require("../../services/messagingService");
  var mockWebSocketService = require("../../services/websocketService");
  beforeEach(function () {
    jest.clearAllMocks();
    mockMessagingService.getConversations.mockResolvedValue({
      conversations: _messagingData.mockConversations,
      total: _messagingData.mockConversations.length
    });
  });
  afterEach(function () {
    jest.clearAllTimers();
  });
  describe('Component Rendering', function () {
    it('renders messages screen with header and conversation list', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_MessagesScreen.MessagesScreen, {})
      }));
      expect(_reactNative.screen.getByText('Messages')).toBeTruthy();
      expect(_reactNative.screen.getByTestId('messages-search-input')).toBeTruthy();
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('John Smith')).toBeTruthy();
      });
    }));
    it('displays loading state initially', function () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_MessagesScreen.MessagesScreen, {})
      }));
      expect(_reactNative.screen.getByTestId('messages-loading-indicator')).toBeTruthy();
    });
    it('displays empty state when no conversations exist', (0, _asyncToGenerator2.default)(function* () {
      mockMessagingService.getConversations.mockResolvedValue({
        conversations: [],
        total: 0
      });
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_MessagesScreen.MessagesScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('No conversations yet')).toBeTruthy();
        expect(_reactNative.screen.getByText('Start a conversation with a service provider')).toBeTruthy();
      });
    }));
  });
  describe('Conversation Management', function () {
    it('loads conversations on mount', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_MessagesScreen.MessagesScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(mockMessagingService.getConversations).toHaveBeenCalledWith(1, 20);
      });
    }));
    it('displays conversation list with proper information', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_MessagesScreen.MessagesScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('John Smith')).toBeTruthy();
        expect(_reactNative.screen.getByText('Hello, I need help with...')).toBeTruthy();
        expect(_reactNative.screen.getByText('2 min ago')).toBeTruthy();
      });
    }));
    it('navigates to chat screen when conversation is pressed', (0, _asyncToGenerator2.default)(function* () {
      var mockNavigation = (0, _testUtils.mockNavigationProps)();
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_MessagesScreen.MessagesScreen, {
          navigation: mockNavigation
        })
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('John Smith')).toBeTruthy();
      });
      _reactNative.fireEvent.press(_reactNative.screen.getByTestId('conversation-item-1'));
      expect(mockNavigation.navigate).toHaveBeenCalledWith('Chat', {
        providerId: 'provider-1',
        providerName: 'John Smith',
        conversationId: 'conv-1',
        providerAvatar: 'https://example.com/avatar1.jpg'
      });
    }));
  });
  describe('Search Functionality', function () {
    it('filters conversations based on search query', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_MessagesScreen.MessagesScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('John Smith')).toBeTruthy();
        expect(_reactNative.screen.getByText('Jane Doe')).toBeTruthy();
      });
      var searchInput = _reactNative.screen.getByTestId('messages-search-input');
      _reactNative.fireEvent.changeText(searchInput, 'John');
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('John Smith')).toBeTruthy();
        expect(_reactNative.screen.queryByText('Jane Doe')).toBeNull();
      });
    }));
    it('shows no results message when search yields no matches', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_MessagesScreen.MessagesScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('John Smith')).toBeTruthy();
      });
      var searchInput = _reactNative.screen.getByTestId('messages-search-input');
      _reactNative.fireEvent.changeText(searchInput, 'NonExistentProvider');
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('No conversations found')).toBeTruthy();
      });
    }));
    it('clears search when search input is cleared', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_MessagesScreen.MessagesScreen, {})
      }));
      var searchInput = _reactNative.screen.getByTestId('messages-search-input');
      _reactNative.fireEvent.changeText(searchInput, 'John');
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.queryByText('Jane Doe')).toBeNull();
      });
      _reactNative.fireEvent.changeText(searchInput, '');
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('John Smith')).toBeTruthy();
        expect(_reactNative.screen.getByText('Jane Doe')).toBeTruthy();
      });
    }));
  });
  describe('Real-time Features', function () {
    it('sets up WebSocket connection on mount', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_MessagesScreen.MessagesScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(mockWebSocketService.connect).toHaveBeenCalled();
        expect(mockWebSocketService.on).toHaveBeenCalledWith('chat_message', expect.any(Function));
        expect(mockWebSocketService.on).toHaveBeenCalledWith('conversation_updated', expect.any(Function));
        expect(mockWebSocketService.on).toHaveBeenCalledWith('typing_indicator', expect.any(Function));
      });
    }));
    it('updates conversation list when new message is received', (0, _asyncToGenerator2.default)(function* () {
      var messageHandler;
      mockWebSocketService.on.mockImplementation(function (event, handler) {
        if (event === 'chat_message') {
          messageHandler = handler;
        }
      });
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_MessagesScreen.MessagesScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Hello, I need help with...')).toBeTruthy();
      });
      (0, _reactNative.act)(function () {
        messageHandler({
          conversation_id: 'conv-1',
          content: 'New message received',
          created_at: new Date().toISOString()
        });
      });
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('New message received')).toBeTruthy();
      });
    }));
    it('displays typing indicators when provider is typing', (0, _asyncToGenerator2.default)(function* () {
      var typingHandler;
      mockWebSocketService.on.mockImplementation(function (event, handler) {
        if (event === 'typing_indicator') {
          typingHandler = handler;
        }
      });
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_MessagesScreen.MessagesScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('John Smith')).toBeTruthy();
      });
      (0, _reactNative.act)(function () {
        typingHandler({
          conversation_id: 'conv-1',
          user_id: 'provider-1',
          is_typing: true
        });
      });
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByTestId('typing-indicator-conv-1')).toBeTruthy();
      });
    }));
  });
  describe('Error Handling', function () {
    it('displays error message when conversation loading fails', (0, _asyncToGenerator2.default)(function* () {
      mockMessagingService.getConversations.mockRejectedValue(new Error('Network error'));
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_MessagesScreen.MessagesScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Failed to load conversations')).toBeTruthy();
        expect(_reactNative.screen.getByText('Try Again')).toBeTruthy();
      });
    }));
    it('allows retry when conversation loading fails', (0, _asyncToGenerator2.default)(function* () {
      mockMessagingService.getConversations.mockRejectedValueOnce(new Error('Network error')).mockResolvedValueOnce({
        conversations: _messagingData.mockConversations,
        total: _messagingData.mockConversations.length
      });
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_MessagesScreen.MessagesScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Try Again')).toBeTruthy();
      });
      _reactNative.fireEvent.press(_reactNative.screen.getByText('Try Again'));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('John Smith')).toBeTruthy();
      });
    }));
  });
  describe('Accessibility', function () {
    it('has proper accessibility labels and hints', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_MessagesScreen.MessagesScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        var searchInput = _reactNative.screen.getByTestId('messages-search-input');
        expect(searchInput.props.accessibilityLabel).toBe('Search conversations');
        expect(searchInput.props.accessibilityHint).toBe('Type to search through your conversations');
      });
    }));
    it('announces screen content to screen readers', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_MessagesScreen.MessagesScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        var screenReader = _reactNative.screen.getByTestId('messages-screen-reader');
        expect(screenReader.props.announceOnMount).toBe('Messages screen loaded. View and manage your conversations.');
      });
    }));
    it('supports keyboard navigation for conversation items', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_MessagesScreen.MessagesScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        var conversationItem = _reactNative.screen.getByTestId('conversation-item-1');
        expect(conversationItem.props.accessible).toBe(true);
        expect(conversationItem.props.accessibilityRole).toBe('button');
      });
    }));
  });
  describe('Performance', function () {
    it('tracks user interactions for analytics', (0, _asyncToGenerator2.default)(function* () {
      var mockTrackUserInteraction = jest.fn(function (name, fn) {
        return fn();
      });
      jest.doMock("../../hooks/usePerformance", function () {
        return {
          usePerformance: function usePerformance() {
            return {
              trackUserInteraction: mockTrackUserInteraction
            };
          }
        };
      });
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_MessagesScreen.MessagesScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('John Smith')).toBeTruthy();
      });
      _reactNative.fireEvent.press(_reactNative.screen.getByTestId('conversation-item-1'));
      expect(mockTrackUserInteraction).toHaveBeenCalledWith('open_conversation', expect.any(Function));
    }));
    it('implements pull-to-refresh functionality', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_MessagesScreen.MessagesScreen, {})
      }));
      var scrollView = _reactNative.screen.getByTestId('messages-scroll-view');
      (0, _reactNative.fireEvent)(scrollView, 'refresh');
      yield (0, _reactNative.waitFor)(function () {
        expect(mockMessagingService.getConversations).toHaveBeenCalledTimes(2);
      });
    }));
  });
  describe('State Management', function () {
    it('manages unread counts correctly', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_MessagesScreen.MessagesScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('2')).toBeTruthy();
      });
    }));
    it('updates last seen timestamps', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_MessagesScreen.MessagesScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('2 min ago')).toBeTruthy();
      });
    }));
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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