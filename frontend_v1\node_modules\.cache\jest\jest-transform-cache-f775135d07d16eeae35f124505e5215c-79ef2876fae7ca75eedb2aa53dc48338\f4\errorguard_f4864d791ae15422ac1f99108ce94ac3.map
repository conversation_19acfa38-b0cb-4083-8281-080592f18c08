{"version": 3, "names": ["_inGuard", "_globalHandler", "global", "RN$useAlwaysAvailableJSErrorHandling", "RN$handleException", "e", "isFatal", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setGlobalHandler", "fun", "getGlobalHandler", "reportError", "error", "reportFatalError", "applyWithGuard", "context", "args", "unused_onError", "unused_name", "apply", "applyWithGuardIfNeeded", "inGuard", "guard", "name", "_ref", "console", "warn", "<PERSON><PERSON><PERSON>", "guarded", "_len", "arguments", "length", "Array", "_key"], "sources": ["error-guard.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict\n * @polyfill\n */\n\nlet _inGuard = 0;\n\ntype ErrorHandler = (error: mixed, isFatal: boolean) => void;\ntype Fn<Args, Return> = (...Args) => Return;\n\n/**\n * This is the error handler that is called when we encounter an exception\n * when loading a module. This will report any errors encountered before\n * ExceptionsManager is configured.\n */\nlet _globalHandler: ErrorHandler =\n  global.RN$useAlwaysAvailableJSErrorHandling === true\n    ? global.RN$handleException\n    : (e: mixed, isFatal: boolean) => {\n        throw e;\n      };\n\n/**\n * The particular require runtime that we are using looks for a global\n * `ErrorUtils` object and if it exists, then it requires modules with the\n * error handler specified via ErrorUtils.setGlobalHandler by calling the\n * require function with applyWithGuard. Since the require module is loaded\n * before any of the modules, this ErrorUtils must be defined (and the handler\n * set) globally before requiring anything.\n */\nconst ErrorUtils = {\n  setGlobalHandler(fun: ErrorHandler): void {\n    _globalHandler = fun;\n  },\n  getGlobalHandler(): ErrorHandler {\n    return _globalHandler;\n  },\n  reportError(error: mixed): void {\n    _globalHandler && _globalHandler(error, false);\n  },\n  reportFatalError(error: mixed): void {\n    // NOTE: This has an untyped call site in Metro.\n    _globalHandler && _globalHandler(error, true);\n  },\n  applyWithGuard<TArgs: $ReadOnlyArray<mixed>, TOut>(\n    fun: Fn<TArgs, TOut>,\n    context?: ?mixed,\n    args?: ?TArgs,\n    // Unused, but some code synced from www sets it to null.\n    unused_onError?: null,\n    // Some callers pass a name here, which we ignore.\n    unused_name?: ?string,\n  ): ?TOut {\n    try {\n      _inGuard++;\n      /* $FlowFixMe[incompatible-call] : TODO ********* (1) apply(context,\n       * null) is fine. (2) array -> rest array should work */\n      /* $FlowFixMe[incompatible-type] : TODO ********* (1) apply(context,\n       * null) is fine. (2) array -> rest array should work */\n      return fun.apply(context, args);\n    } catch (e) {\n      ErrorUtils.reportError(e);\n    } finally {\n      _inGuard--;\n    }\n    return null;\n  },\n  applyWithGuardIfNeeded<TArgs: $ReadOnlyArray<mixed>, TOut>(\n    fun: Fn<TArgs, TOut>,\n    context?: ?mixed,\n    args?: ?TArgs,\n  ): ?TOut {\n    if (ErrorUtils.inGuard()) {\n      /* $FlowFixMe[incompatible-call] : TODO ********* (1) apply(context,\n       * null) is fine. (2) array -> rest array should work */\n      /* $FlowFixMe[incompatible-type] : TODO ********* (1) apply(context,\n       * null) is fine. (2) array -> rest array should work */\n      return fun.apply(context, args);\n    } else {\n      ErrorUtils.applyWithGuard(fun, context, args);\n    }\n    return null;\n  },\n  inGuard(): boolean {\n    return !!_inGuard;\n  },\n  guard<TArgs: $ReadOnlyArray<mixed>, TOut>(\n    fun: Fn<TArgs, TOut>,\n    name?: ?string,\n    context?: ?mixed,\n  ): ?(...TArgs) => ?TOut {\n    // TODO: (moti) T48204753 Make sure this warning is never hit and remove it - types\n    // should be sufficient.\n    if (typeof fun !== 'function') {\n      console.warn('A function must be passed to ErrorUtils.guard, got ', fun);\n      return null;\n    }\n    const guardName = name ?? fun.name ?? '<generated guard>';\n    /* $FlowFixMe[missing-this-annot] The 'this' type annotation(s) required by\n     * Flow's LTI update could not be added via codemod */\n    function guarded(...args: TArgs): ?TOut {\n      return ErrorUtils.applyWithGuard(\n        fun,\n        context ?? this,\n        args,\n        null,\n        guardName,\n      );\n    }\n\n    return guarded;\n  },\n};\n\nglobal.ErrorUtils = ErrorUtils;\n\nexport type ErrorUtilsT = typeof ErrorUtils;\n"], "mappings": "AAWA,IAAIA,QAAQ,GAAG,CAAC;AAUhB,IAAIC,cAA4B,GAC9BC,MAAM,CAACC,oCAAoC,KAAK,IAAI,GAChDD,MAAM,CAACE,kBAAkB,GACzB,UAACC,CAAQ,EAAEC,OAAgB,EAAK;EAC9B,MAAMD,CAAC;AACT,CAAC;AAUP,IAAME,UAAU,GAAG;EACjBC,gBAAgB,WAAhBA,gBAAgBA,CAACC,GAAiB,EAAQ;IACxCR,cAAc,GAAGQ,GAAG;EACtB,CAAC;EACDC,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAiB;IAC/B,OAAOT,cAAc;EACvB,CAAC;EACDU,WAAW,WAAXA,WAAWA,CAACC,KAAY,EAAQ;IAC9BX,cAAc,IAAIA,cAAc,CAACW,KAAK,EAAE,KAAK,CAAC;EAChD,CAAC;EACDC,gBAAgB,WAAhBA,gBAAgBA,CAACD,KAAY,EAAQ;IAEnCX,cAAc,IAAIA,cAAc,CAACW,KAAK,EAAE,IAAI,CAAC;EAC/C,CAAC;EACDE,cAAc,WAAdA,cAAcA,CACZL,GAAoB,EACpBM,OAAgB,EAChBC,IAAa,EAEbC,cAAqB,EAErBC,WAAqB,EACd;IACP,IAAI;MACFlB,QAAQ,EAAE;MAKV,OAAOS,GAAG,CAACU,KAAK,CAACJ,OAAO,EAAEC,IAAI,CAAC;IACjC,CAAC,CAAC,OAAOX,CAAC,EAAE;MACVE,UAAU,CAACI,WAAW,CAACN,CAAC,CAAC;IAC3B,CAAC,SAAS;MACRL,QAAQ,EAAE;IACZ;IACA,OAAO,IAAI;EACb,CAAC;EACDoB,sBAAsB,WAAtBA,sBAAsBA,CACpBX,GAAoB,EACpBM,OAAgB,EAChBC,IAAa,EACN;IACP,IAAIT,UAAU,CAACc,OAAO,CAAC,CAAC,EAAE;MAKxB,OAAOZ,GAAG,CAACU,KAAK,CAACJ,OAAO,EAAEC,IAAI,CAAC;IACjC,CAAC,MAAM;MACLT,UAAU,CAACO,cAAc,CAACL,GAAG,EAAEM,OAAO,EAAEC,IAAI,CAAC;IAC/C;IACA,OAAO,IAAI;EACb,CAAC;EACDK,OAAO,WAAPA,OAAOA,CAAA,EAAY;IACjB,OAAO,CAAC,CAACrB,QAAQ;EACnB,CAAC;EACDsB,KAAK,WAALA,KAAKA,CACHb,GAAoB,EACpBc,IAAc,EACdR,OAAgB,EACM;IAAA,IAAAS,IAAA;IAGtB,IAAI,OAAOf,GAAG,KAAK,UAAU,EAAE;MAC7BgB,OAAO,CAACC,IAAI,CAAC,qDAAqD,EAAEjB,GAAG,CAAC;MACxE,OAAO,IAAI;IACb;IACA,IAAMkB,SAAS,IAAAH,IAAA,GAAGD,IAAI,WAAJA,IAAI,GAAId,GAAG,CAACc,IAAI,YAAAC,IAAA,GAAI,mBAAmB;IAGzD,SAASI,OAAOA,CAAA,EAAwB;MAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAApBf,IAAI,OAAAgB,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;QAAJjB,IAAI,CAAAiB,IAAA,IAAAH,SAAA,CAAAG,IAAA;MAAA;MACtB,OAAO1B,UAAU,CAACO,cAAc,CAC9BL,GAAG,EACHM,OAAO,WAAPA,OAAO,GAAI,IAAI,EACfC,IAAI,EACJ,IAAI,EACJW,SACF,CAAC;IACH;IAEA,OAAOC,OAAO;EAChB;AACF,CAAC;AAED1B,MAAM,CAACK,UAAU,GAAGA,UAAU", "ignoreList": []}