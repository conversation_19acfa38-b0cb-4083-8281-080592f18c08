{"version": 3, "names": ["_shouldUseTurboAnimatedModule", "_interopRequireDefault", "require", "TurboModuleRegistry", "_interopRequireWildcard", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "NativeModule", "shouldUseTurboAnimatedModule", "_default", "exports"], "sources": ["NativeAnimatedTurboModule.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n */\n\nimport type {TurboModule} from '../../../../Libraries/TurboModule/RCTExport';\n\nimport shouldUseTurboAnimatedModule from '../../../../Libraries/Animated/shouldUseTurboAnimatedModule';\nimport * as TurboModuleRegistry from '../../../../Libraries/TurboModule/TurboModuleRegistry';\n\ntype EndResult = {finished: boolean, value?: number, ...};\ntype EndCallback = (result: EndResult) => void;\ntype SaveValueCallback = (value: number) => void;\n\nexport type EventMapping = {\n  nativeEventPath: Array<string>,\n  animatedValueTag: ?number,\n};\n\n// The config has different keys depending on the type of the Node\n// TODO(*********): Make these types strict\nexport type AnimatedNodeConfig = Object;\nexport type AnimatingNodeConfig = Object;\n\nexport interface Spec extends TurboModule {\n  +startOperationBatch: () => void;\n  +finishOperationBatch: () => void;\n  +createAnimatedNode: (tag: number, config: AnimatedNodeConfig) => void;\n  +updateAnimatedNodeConfig?: (tag: number, config: AnimatedNodeConfig) => void;\n  +getValue: (tag: number, saveValueCallback: SaveValueCallback) => void;\n  +startListeningToAnimatedNodeValue: (tag: number) => void;\n  +stopListeningToAnimatedNodeValue: (tag: number) => void;\n  +connectAnimatedNodes: (parentTag: number, childTag: number) => void;\n  +disconnectAnimatedNodes: (parentTag: number, childTag: number) => void;\n  +startAnimatingNode: (\n    animationId: number,\n    nodeTag: number,\n    config: AnimatingNodeConfig,\n    endCallback: EndCallback,\n  ) => void;\n  +stopAnimation: (animationId: number) => void;\n  +setAnimatedNodeValue: (nodeTag: number, value: number) => void;\n  +setAnimatedNodeOffset: (nodeTag: number, offset: number) => void;\n  +flattenAnimatedNodeOffset: (nodeTag: number) => void;\n  +extractAnimatedNodeOffset: (nodeTag: number) => void;\n  +connectAnimatedNodeToView: (nodeTag: number, viewTag: number) => void;\n  +disconnectAnimatedNodeFromView: (nodeTag: number, viewTag: number) => void;\n  +restoreDefaultValues: (nodeTag: number) => void;\n  +dropAnimatedNode: (tag: number) => void;\n  +addAnimatedEventToView: (\n    viewTag: number,\n    eventName: string,\n    eventMapping: EventMapping,\n  ) => void;\n  +removeAnimatedEventFromView: (\n    viewTag: number,\n    eventName: string,\n    animatedNodeTag: number,\n  ) => void;\n\n  // Events\n  +addListener: (eventName: string) => void;\n  +removeListeners: (count: number) => void;\n\n  // All of the above in a batched mode\n  +queueAndExecuteBatchedOperations?: (operationsAndArgs: Array<any>) => void;\n}\n\nconst NativeModule: ?Spec = shouldUseTurboAnimatedModule()\n  ? TurboModuleRegistry.get<Spec>('NativeAnimatedTurboModule')\n  : null;\n\nexport default NativeModule;\n"], "mappings": ";;;;;AAYA,IAAAA,6BAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,mBAAA,GAAAC,uBAAA,CAAAF,OAAA;AAA6F,SAAAE,wBAAAC,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAH,uBAAA,YAAAA,wBAAAC,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AA4D7F,IAAMmB,YAAmB,GAAG,IAAAC,qCAA4B,EAAC,CAAC,GACtDvB,mBAAmB,CAACc,GAAG,CAAO,2BAA2B,CAAC,GAC1D,IAAI;AAAC,IAAAU,QAAA,GAAAC,OAAA,CAAAb,OAAA,GAEMU,YAAY", "ignoreList": []}