/**
 * Consistency Standards System
 *
 * Comprehensive design system that ensures consistency across all UI components
 * and interactions. Implements standardized patterns, behaviors, and styling.
 *
 * Features:
 * - Standardized component behaviors
 * - Consistent interaction patterns
 * - Unified styling system
 * - Accessibility standards
 * - Platform conventions
 * - Brand consistency
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { createContext, useContext } from 'react';
import { ViewStyle, TextStyle, ImageStyle } from 'react-native';

import { HyperMinimalistTheme } from './HyperMinimalistTheme';

// Standard spacing scale
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,
} as const;

// Standard border radius scale
export const BORDER_RADIUS = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  full: 9999,
} as const;

// Standard elevation/shadow levels
export const ELEVATION = {
  none: {
    elevation: 0,
    shadowOpacity: 0,
  },
  sm: {
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  md: {
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  lg: {
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
  },
  xl: {
    elevation: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
  },
} as const;

// Standard animation durations
export const ANIMATION_DURATION = {
  fast: 150,
  normal: 250,
  slow: 350,
  slower: 500,
} as const;

// Standard component sizes
export const COMPONENT_SIZES = {
  button: {
    sm: { height: 32, paddingHorizontal: 12, fontSize: 14 },
    md: { height: 40, paddingHorizontal: 16, fontSize: 16 },
    lg: { height: 48, paddingHorizontal: 20, fontSize: 18 },
  },
  input: {
    sm: { height: 32, paddingHorizontal: 12, fontSize: 14 },
    md: { height: 40, paddingHorizontal: 16, fontSize: 16 },
    lg: { height: 48, paddingHorizontal: 20, fontSize: 18 },
  },
  icon: {
    xs: 12,
    sm: 16,
    md: 20,
    lg: 24,
    xl: 32,
  },
} as const;

// Standard interaction states
export interface InteractionStates {
  default: ViewStyle | TextStyle;
  hover?: ViewStyle | TextStyle;
  active?: ViewStyle | TextStyle;
  disabled?: ViewStyle | TextStyle;
  focus?: ViewStyle | TextStyle;
}

// Component behavior standards
export interface ComponentBehaviorStandards {
  // Button behaviors
  button: {
    minimumTouchTarget: number;
    hapticFeedback: boolean;
    loadingState: boolean;
    disabledOpacity: number;
    pressAnimation: 'scale' | 'opacity' | 'none';
  };

  // Input behaviors
  input: {
    autoFocus: boolean;
    clearButton: boolean;
    validation: 'realtime' | 'onBlur' | 'onSubmit';
    errorDisplay: 'inline' | 'tooltip' | 'modal';
  };

  // Modal behaviors
  modal: {
    backdropDismiss: boolean;
    escapeKeyDismiss: boolean;
    focusTrap: boolean;
    returnFocus: boolean;
  };

  // Navigation behaviors
  navigation: {
    backGesture: boolean;
    swipeGesture: boolean;
    breadcrumbs: boolean;
    persistentState: boolean;
  };
}

// Standard accessibility requirements
export interface AccessibilityStandards {
  minimumTouchTarget: number;
  colorContrast: {
    normal: number;
    large: number;
  };
  focusIndicator: {
    width: number;
    color: string;
    style: 'solid' | 'dashed';
  };
  screenReader: {
    labels: boolean;
    hints: boolean;
    roles: boolean;
    states: boolean;
  };
}

// Default standards configuration
export const DEFAULT_STANDARDS: {
  behavior: ComponentBehaviorStandards;
  accessibility: AccessibilityStandards;
} = {
  behavior: {
    button: {
      minimumTouchTarget: 44,
      hapticFeedback: true,
      loadingState: true,
      disabledOpacity: 0.6,
      pressAnimation: 'scale',
    },
    input: {
      autoFocus: false,
      clearButton: true,
      validation: 'realtime',
      errorDisplay: 'inline',
    },
    modal: {
      backdropDismiss: true,
      escapeKeyDismiss: true,
      focusTrap: true,
      returnFocus: true,
    },
    navigation: {
      backGesture: true,
      swipeGesture: true,
      breadcrumbs: true,
      persistentState: true,
    },
  },
  accessibility: {
    minimumTouchTarget: 44,
    colorContrast: {
      normal: 4.5,
      large: 3.0,
    },
    focusIndicator: {
      width: 2,
      color: '#3b82f6',
      style: 'solid',
    },
    screenReader: {
      labels: true,
      hints: true,
      roles: true,
      states: true,
    },
  },
};

// Consistency context
interface ConsistencyContextType {
  standards: typeof DEFAULT_STANDARDS;
  theme: typeof HyperMinimalistTheme;
  updateStandards: (updates: Partial<typeof DEFAULT_STANDARDS>) => void;
}

const ConsistencyContext = createContext<ConsistencyContextType | null>(null);

export const useConsistencyStandards = () => {
  const context = useContext(ConsistencyContext);
  if (!context) {
    throw new Error(
      'useConsistencyStandards must be used within ConsistencyProvider',
    );
  }
  return context;
};

// Standard component style generators
export const createStandardStyles = (
  theme: typeof HyperMinimalistTheme,
  isDark: boolean,
) => {
  const colors = isDark ? theme.darkColors : theme.colors;

  return {
    // Standard button styles
    button: {
      primary: {
        backgroundColor: colors.primary?.default || '#5A7A63',
        borderColor: colors.primary?.default || '#5A7A63',
        borderWidth: 1,
        borderRadius: BORDER_RADIUS.md,
        ...ELEVATION.sm,
      } as ViewStyle,

      secondary: {
        backgroundColor: 'transparent',
        borderColor: colors.primary?.default || '#5A7A63',
        borderWidth: 1,
        borderRadius: BORDER_RADIUS.md,
      } as ViewStyle,

      ghost: {
        backgroundColor: 'transparent',
        borderColor: 'transparent',
        borderWidth: 1,
        borderRadius: BORDER_RADIUS.md,
      } as ViewStyle,

      destructive: {
        backgroundColor: colors.error || '#DC2626',
        borderColor: colors.error || '#DC2626',
        borderWidth: 1,
        borderRadius: BORDER_RADIUS.md,
        ...ELEVATION.sm,
      } as ViewStyle,
    },

    // Standard input styles
    input: {
      default: {
        borderWidth: 1,
        borderColor: colors.gray[300],
        borderRadius: BORDER_RADIUS.md,
        backgroundColor: colors.white,
        paddingHorizontal: SPACING.md,
        paddingVertical: SPACING.sm,
      } as ViewStyle,

      focused: {
        borderColor: colors.accent[500],
        ...ELEVATION.sm,
      } as ViewStyle,

      error: {
        borderColor: colors.error[500],
      } as ViewStyle,

      disabled: {
        backgroundColor: colors.gray[100],
        borderColor: colors.gray[200],
        opacity: 0.6,
      } as ViewStyle,
    },

    // Standard card styles
    card: {
      default: {
        backgroundColor: colors.white,
        borderRadius: BORDER_RADIUS.lg,
        padding: SPACING.lg,
        ...ELEVATION.sm,
      } as ViewStyle,

      elevated: {
        backgroundColor: colors.white,
        borderRadius: BORDER_RADIUS.lg,
        padding: SPACING.lg,
        ...ELEVATION.md,
      } as ViewStyle,

      outlined: {
        backgroundColor: colors.white,
        borderRadius: BORDER_RADIUS.lg,
        padding: SPACING.lg,
        borderWidth: 1,
        borderColor: colors.gray[200],
      } as ViewStyle,
    },

    // Standard text styles
    text: {
      heading1: {
        fontSize: 32,
        fontWeight: '700',
        lineHeight: 40,
        color: colors.gray[900],
      } as TextStyle,

      heading2: {
        fontSize: 24,
        fontWeight: '600',
        lineHeight: 32,
        color: colors.gray[900],
      } as TextStyle,

      heading3: {
        fontSize: 20,
        fontWeight: '600',
        lineHeight: 28,
        color: colors.gray[900],
      } as TextStyle,

      body: {
        fontSize: 16,
        fontWeight: '400',
        lineHeight: 24,
        color: colors.gray[700],
      } as TextStyle,

      caption: {
        fontSize: 14,
        fontWeight: '400',
        lineHeight: 20,
        color: colors.gray[600],
      } as TextStyle,

      label: {
        fontSize: 14,
        fontWeight: '500',
        lineHeight: 20,
        color: colors.gray[700],
      } as TextStyle,
    },

    // Standard layout styles
    layout: {
      container: {
        flex: 1,
        backgroundColor: colors.background,
      } as ViewStyle,

      section: {
        paddingHorizontal: SPACING.lg,
        paddingVertical: SPACING.md,
      } as ViewStyle,

      row: {
        flexDirection: 'row' as const,
        alignItems: 'center' as const,
      } as ViewStyle,

      column: {
        flexDirection: 'column' as const,
      } as ViewStyle,

      center: {
        justifyContent: 'center' as const,
        alignItems: 'center' as const,
      } as ViewStyle,

      spaceBetween: {
        justifyContent: 'space-between' as const,
      } as ViewStyle,
    },
  };
};

// Standard interaction patterns
export const createInteractionPatterns = (
  theme: typeof HyperMinimalistTheme,
  isDark: boolean,
) => {
  const colors = isDark ? theme.darkColors : theme.colors;

  return {
    // Press interactions
    press: {
      scale: {
        transform: [{ scale: 0.95 }],
      },
      opacity: {
        opacity: 0.7,
      },
      highlight: {
        backgroundColor: colors.gray[100],
      },
    },

    // Focus interactions
    focus: {
      outline: {
        borderWidth: 2,
        borderColor: colors.accent[500],
      },
      shadow: {
        ...ELEVATION.md,
      },
      background: {
        backgroundColor: colors.accent[50],
      },
    },

    // Hover interactions (for web)
    hover: {
      elevate: {
        ...ELEVATION.md,
      },
      brighten: {
        opacity: 0.9,
      },
      darken: {
        opacity: 1.1,
      },
    },
  };
};

// Consistency provider component
interface ConsistencyProviderProps {
  children: React.ReactNode;
  customStandards?: Partial<typeof DEFAULT_STANDARDS>;
}

export const ConsistencyProvider: React.FC<ConsistencyProviderProps> = ({
  children,
  customStandards = {},
}) => {
  const [standards, setStandards] = React.useState({
    ...DEFAULT_STANDARDS,
    ...customStandards,
  });

  const updateStandards = React.useCallback(
    (updates: Partial<typeof DEFAULT_STANDARDS>) => {
      setStandards(prev => ({
        ...prev,
        ...updates,
      }));
    },
    [],
  );

  const contextValue: ConsistencyContextType = {
    standards,
    theme: HyperMinimalistTheme,
    updateStandards,
  };

  return (
    <ConsistencyContext.Provider value={contextValue}>
      {children}
    </ConsistencyContext.Provider>
  );
};
