/**
 * Unified Error Handling Service
 * 
 * Central service that consolidates all error handling functionality
 * into a single, consistent system. Replaces multiple overlapping
 * error handling services with a unified approach.
 * 
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';
import NetInfo from '@react-native-community/netinfo';

import {
  UnifiedError,
  ErrorReport,
  ErrorContext,
  ErrorType,
  ErrorSeverity,
  ErrorCategory,
  RecoveryStrategy,
  UserFeedbackConfig,
  UnifiedErrorHandlingConfig,
  ErrorListener,
  ErrorMetrics,
  ErrorBreadcrumb
} from './types';
import { userFeedbackService } from './UserFeedbackService';
import { analyticsIntegrationService } from './AnalyticsIntegrationService';
import { errorMonitoringService } from './ErrorMonitoringService';

class UnifiedErrorHandlingService {
  private config: UnifiedErrorHandlingConfig;
  private errorQueue: ErrorReport[] = [];
  private errorListeners: ErrorListener[] = [];
  private recoveryStrategies: Map<string, RecoveryStrategy> = new Map();
  private breadcrumbs: ErrorBreadcrumb[] = [];
  private metrics: ErrorMetrics;
  private isInitialized = false;

  constructor() {
    this.config = this.getDefaultConfig();
    this.metrics = this.initializeMetrics();
    this.setupRecoveryStrategies();
  }

  /**
   * Initialize the error handling service
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Initialize sub-services
      await userFeedbackService.initialize?.();
      await analyticsIntegrationService.initialize();
      await errorMonitoringService.initialize();

      // Load offline errors
      await this.loadOfflineErrors();

      // Setup network monitoring
      this.setupNetworkMonitoring();

      // Setup periodic error queue flush
      this.setupErrorQueueFlush();

      this.isInitialized = true;
      console.log('✅ UnifiedErrorHandlingService initialized');
    } catch (error) {
      console.error('❌ Failed to initialize UnifiedErrorHandlingService:', error);
    }
  }

  /**
   * Main error handling method
   */
  async handleError(
    error: Error | UnifiedError | string,
    context: Partial<ErrorContext> = {},
    userMessage?: string
  ): Promise<ErrorReport> {
    // Convert to UnifiedError if needed
    const unifiedError = this.normalizeError(error, context);
    
    // Create error report
    const errorReport = await this.createErrorReport(unifiedError, userMessage);
    
    // Add to breadcrumbs
    this.addBreadcrumb({
      timestamp: Date.now(),
      category: 'system_event',
      message: `Error handled: ${unifiedError.type}`,
      level: 'error',
      data: { errorId: unifiedError.id, type: unifiedError.type }
    });
    
    // Process error through pipeline
    await this.processError(errorReport);
    
    return errorReport;
  }

  /**
   * Handle specific error types with optimized processing
   */
  async handleNetworkError(error: Error, context: Partial<ErrorContext> = {}): Promise<ErrorReport> {
    const networkContext = {
      ...context,
      type: ErrorType.NETWORK,
      severity: ErrorSeverity.MEDIUM
    };
    
    return this.handleError(error, networkContext);
  }

  async handleAuthError(error: Error, context: Partial<ErrorContext> = {}): Promise<ErrorReport> {
    const authContext = {
      ...context,
      type: ErrorType.AUTHENTICATION,
      severity: ErrorSeverity.HIGH
    };
    
    return this.handleError(error, authContext);
  }

  async handleValidationError(error: Error, context: Partial<ErrorContext> = {}): Promise<ErrorReport> {
    const validationContext = {
      ...context,
      type: ErrorType.VALIDATION,
      severity: ErrorSeverity.LOW
    };
    
    return this.handleError(error, validationContext);
  }

  async handleWebSocketError(error: Error, context: Partial<ErrorContext> = {}): Promise<ErrorReport> {
    const wsContext = {
      ...context,
      type: ErrorType.WEBSOCKET,
      severity: ErrorSeverity.MEDIUM
    };
    
    return this.handleError(error, wsContext);
  }

  /**
   * Add breadcrumb for error tracking
   */
  addBreadcrumb(breadcrumb: ErrorBreadcrumb): void {
    this.breadcrumbs.push(breadcrumb);
    
    // Keep only last 50 breadcrumbs
    if (this.breadcrumbs.length > 50) {
      this.breadcrumbs = this.breadcrumbs.slice(-50);
    }
  }

  /**
   * Add error listener
   */
  addErrorListener(listener: ErrorListener): () => void {
    this.errorListeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.errorListeners.indexOf(listener);
      if (index > -1) {
        this.errorListeners.splice(index, 1);
      }
    };
  }

  /**
   * Add recovery strategy
   */
  addRecoveryStrategy(strategy: RecoveryStrategy): void {
    this.recoveryStrategies.set(strategy.id, strategy);
  }

  /**
   * Get error metrics
   */
  getMetrics(): ErrorMetrics {
    return { ...this.metrics };
  }

  /**
   * Clear error queue
   */
  clearErrorQueue(): void {
    this.errorQueue = [];
    AsyncStorage.removeItem('unified_error_queue');
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<UnifiedErrorHandlingConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  // Private Methods

  private normalizeError(
    error: Error | UnifiedError | string,
    context: Partial<ErrorContext>
  ): UnifiedError {
    if (error instanceof UnifiedError) {
      return error;
    }

    if (typeof error === 'string') {
      return new UnifiedError(
        error,
        context.type as ErrorType || ErrorType.UNKNOWN,
        context.severity as ErrorSeverity || ErrorSeverity.MEDIUM,
        context
      );
    }

    // Determine error type from error properties
    const errorType = this.determineErrorType(error, context);
    const severity = this.determineSeverity(error, context);

    return new UnifiedError(
      error.message,
      errorType,
      severity,
      context,
      error
    );
  }

  private determineErrorType(error: Error, context: Partial<ErrorContext>): ErrorType {
    // Check context first
    if (context.type) return context.type as ErrorType;

    // Check error message patterns
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      return ErrorType.NETWORK;
    }
    if (message.includes('unauthorized') || message.includes('401')) {
      return ErrorType.AUTHENTICATION;
    }
    if (message.includes('forbidden') || message.includes('403')) {
      return ErrorType.AUTHORIZATION;
    }
    if (message.includes('not found') || message.includes('404')) {
      return ErrorType.NOT_FOUND;
    }
    if (message.includes('timeout')) {
      return ErrorType.TIMEOUT;
    }
    if (message.includes('websocket') || message.includes('ws')) {
      return ErrorType.WEBSOCKET;
    }
    if (message.includes('theme') || message.includes('colors')) {
      return ErrorType.THEME;
    }
    if (message.includes('lazy') || message.includes('loading')) {
      return ErrorType.LAZY_LOADING;
    }

    return ErrorType.UNKNOWN;
  }

  private determineSeverity(error: Error, context: Partial<ErrorContext>): ErrorSeverity {
    if (context.severity) return context.severity as ErrorSeverity;

    const errorType = this.determineErrorType(error, context);
    
    switch (errorType) {
      case ErrorType.AUTHENTICATION:
      case ErrorType.AUTHORIZATION:
      case ErrorType.SERVER_ERROR:
        return ErrorSeverity.HIGH;
      case ErrorType.SYSTEM:
      case ErrorType.THEME:
        return ErrorSeverity.CRITICAL;
      case ErrorType.VALIDATION:
      case ErrorType.NOT_FOUND:
        return ErrorSeverity.LOW;
      default:
        return ErrorSeverity.MEDIUM;
    }
  }

  private async createErrorReport(
    error: UnifiedError,
    userMessage?: string
  ): Promise<ErrorReport> {
    const report: ErrorReport = {
      id: error.id,
      type: error.type,
      category: error.category,
      severity: error.severity,
      message: error.message,
      technicalMessage: error.technicalMessage,
      userMessage: userMessage || error.userMessage,
      stack: error.stack,
      context: {
        ...error.context,
        breadcrumbs: [...this.breadcrumbs]
      },
      timestamp: error.timestamp,
      recoveryStrategy: undefined,
      recoveryAttempts: 0,
      recovered: false,
      reported: false,
      userFeedback: this.createUserFeedbackConfig(error),
      analyticsData: this.createAnalyticsData(error)
    };

    return report;
  }

  private createUserFeedbackConfig(error: UnifiedError): UserFeedbackConfig {
    const shouldShow = error.severity !== ErrorSeverity.LOW && this.config.enableUserFeedback;
    
    return {
      showToUser: shouldShow,
      title: this.getUserFeedbackTitle(error.type),
      message: error.userMessage,
      variant: this.config.defaultFeedbackVariant,
      dismissible: error.severity !== ErrorSeverity.CRITICAL,
      autoHide: error.severity === ErrorSeverity.LOW,
      hideDelay: 5000
    };
  }

  private getUserFeedbackTitle(type: ErrorType): string {
    const titles = {
      [ErrorType.NETWORK]: 'Connection Problem',
      [ErrorType.AUTHENTICATION]: 'Sign In Required',
      [ErrorType.AUTHORIZATION]: 'Access Denied',
      [ErrorType.VALIDATION]: 'Input Error',
      [ErrorType.NOT_FOUND]: 'Not Found',
      [ErrorType.SERVER_ERROR]: 'Server Error',
      [ErrorType.TIMEOUT]: 'Request Timeout',
      [ErrorType.WEBSOCKET]: 'Connection Lost',
      [ErrorType.THEME]: 'Display Issue',
      [ErrorType.LAZY_LOADING]: 'Loading Error'
    };
    
    return titles[type] || 'Error';
  }

  private createAnalyticsData(error: UnifiedError): Record<string, any> {
    return {
      errorType: error.type,
      errorCategory: error.category,
      severity: error.severity,
      component: error.context.component,
      screen: error.context.screen,
      action: error.context.action,
      timestamp: error.timestamp,
      platform: error.context.deviceInfo?.platform,
      networkStatus: error.context.deviceInfo?.networkStatus
    };
  }

  private getDefaultConfig(): UnifiedErrorHandlingConfig {
    return {
      enableLogging: true,
      enableReporting: true,
      enableUserFeedback: true,
      enableRecovery: true,
      enableAnalytics: true,
      userFeedbackThreshold: ErrorSeverity.MEDIUM,
      defaultFeedbackVariant: 'toast',
      maxRecoveryAttempts: 3,
      defaultRetryDelay: 2000,
      enableProgressiveRetry: true,
      reportingThreshold: ErrorSeverity.MEDIUM,
      enableOfflineStorage: true,
      maxOfflineErrors: 100,
      maxErrorQueueSize: 50,
      errorQueueFlushInterval: 30000,
      enableDebugMode: __DEV__,
      enableStackTrace: __DEV__
    };
  }

  private initializeMetrics(): ErrorMetrics {
    return {
      totalErrors: 0,
      errorsByType: {} as Record<ErrorType, number>,
      errorsBySeverity: {} as Record<ErrorSeverity, number>,
      errorsByCategory: {} as Record<ErrorCategory, number>,
      recoverySuccessRate: 0,
      averageRecoveryTime: 0
    };
  }

  private setupRecoveryStrategies(): void {
    // Network retry strategy
    this.addRecoveryStrategy({
      id: 'network_retry',
      name: 'Network Retry',
      description: 'Retry network requests after connection is restored',
      canRecover: (error) => error.type === ErrorType.NETWORK,
      recover: async () => {
        // Wait for network connection
        const netInfo = await NetInfo.fetch();
        return netInfo.isConnected || false;
      },
      maxAttempts: 3,
      retryDelay: 2000,
      progressiveDelay: true
    });

    // WebSocket reconnection strategy
    this.addRecoveryStrategy({
      id: 'websocket_reconnect',
      name: 'WebSocket Reconnect',
      description: 'Reconnect WebSocket connections',
      canRecover: (error) => error.type === ErrorType.WEBSOCKET,
      recover: async () => {
        // Implementation will be added when integrating with WebSocket services
        return true;
      },
      maxAttempts: 5,
      retryDelay: 1000,
      progressiveDelay: true
    });
  }

  private async processError(errorReport: ErrorReport): Promise<void> {
    // Add to queue
    this.errorQueue.push(errorReport);
    
    // Maintain queue size
    if (this.errorQueue.length > this.config.maxErrorQueueSize) {
      this.errorQueue.shift();
    }

    // Update metrics
    this.updateMetrics(errorReport);

    // Log error
    if (this.config.enableLogging) {
      this.logError(errorReport);
    }

    // Attempt recovery
    if (this.config.enableRecovery) {
      await this.attemptRecovery(errorReport);
    }

    // Show user feedback
    if (this.config.enableUserFeedback && errorReport.userFeedback?.showToUser) {
      await userFeedbackService.showFeedback(errorReport);
    }

    // Notify listeners
    this.notifyListeners(errorReport);

    // Store offline if needed
    if (this.config.enableOfflineStorage) {
      await this.storeOfflineError(errorReport);
    }

    // Report to analytics
    if (this.config.enableReporting && errorReport.severity >= this.config.reportingThreshold) {
      await analyticsIntegrationService.reportError(errorReport);
    }

    // Record in monitoring service
    await errorMonitoringService.recordError(errorReport);
  }

  private updateMetrics(errorReport: ErrorReport): void {
    this.metrics.totalErrors++;
    
    // Update by type
    this.metrics.errorsByType[errorReport.type] = 
      (this.metrics.errorsByType[errorReport.type] || 0) + 1;
    
    // Update by severity
    this.metrics.errorsBySeverity[errorReport.severity] = 
      (this.metrics.errorsBySeverity[errorReport.severity] || 0) + 1;
    
    // Update by category
    this.metrics.errorsByCategory[errorReport.category] = 
      (this.metrics.errorsByCategory[errorReport.category] || 0) + 1;
  }

  private logError(errorReport: ErrorReport): void {
    const logLevel = this.getLogLevel(errorReport.severity);
    const logMessage = `[UnifiedErrorHandling] ${errorReport.type}: ${errorReport.message}`;
    
    console[logLevel](logMessage, {
      id: errorReport.id,
      type: errorReport.type,
      severity: errorReport.severity,
      context: errorReport.context,
      stack: this.config.enableStackTrace ? errorReport.stack : undefined
    });
  }

  private getLogLevel(severity: ErrorSeverity): 'log' | 'warn' | 'error' {
    switch (severity) {
      case ErrorSeverity.LOW:
        return 'log';
      case ErrorSeverity.MEDIUM:
        return 'warn';
      case ErrorSeverity.HIGH:
      case ErrorSeverity.CRITICAL:
        return 'error';
      default:
        return 'warn';
    }
  }

  private async attemptRecovery(errorReport: ErrorReport): Promise<void> {
    // Find applicable recovery strategies
    const strategies = Array.from(this.recoveryStrategies.values())
      .filter(strategy => strategy.canRecover(errorReport as any, errorReport.context));

    for (const strategy of strategies) {
      if (errorReport.recoveryAttempts >= strategy.maxAttempts) continue;

      try {
        errorReport.recoveryAttempts++;
        
        // Calculate delay
        const delay = strategy.progressiveDelay 
          ? strategy.retryDelay! * errorReport.recoveryAttempts
          : strategy.retryDelay || this.config.defaultRetryDelay;

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, delay));

        // Attempt recovery
        const recovered = await strategy.recover(errorReport as any, errorReport.context);
        
        if (recovered) {
          errorReport.recovered = true;
          errorReport.recoveryStrategy = strategy.name;
          
          console.log(`✅ Error recovered using strategy: ${strategy.name}`);
          break;
        }
      } catch (recoveryError) {
        console.warn(`❌ Recovery strategy failed: ${strategy.name}`, recoveryError);
      }
    }
  }

  // Removed showUserFeedback method - now handled by UserFeedbackService

  private notifyListeners(errorReport: ErrorReport): void {
    this.errorListeners.forEach(listener => {
      try {
        listener(errorReport);
      } catch (error) {
        console.warn('Error listener failed:', error);
      }
    });
  }

  private async storeOfflineError(errorReport: ErrorReport): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem('unified_error_queue');
      const errors = stored ? JSON.parse(stored) : [];
      
      errors.push(errorReport);
      
      // Maintain max offline errors
      if (errors.length > this.config.maxOfflineErrors) {
        errors.splice(0, errors.length - this.config.maxOfflineErrors);
      }
      
      await AsyncStorage.setItem('unified_error_queue', JSON.stringify(errors));
    } catch (error) {
      console.warn('Failed to store offline error:', error);
    }
  }

  private async loadOfflineErrors(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem('unified_error_queue');
      if (stored) {
        const errors = JSON.parse(stored);
        this.errorQueue.push(...errors);
        
        // Clear offline storage
        await AsyncStorage.removeItem('unified_error_queue');
      }
    } catch (error) {
      console.warn('Failed to load offline errors:', error);
    }
  }

  // Removed reportError method - now handled by AnalyticsIntegrationService

  private setupNetworkMonitoring(): void {
    NetInfo.addEventListener(state => {
      // Update device info for all future errors
      if (this.config.enableLogging) {
        console.log('📶 Network status changed:', state.isConnected ? 'online' : 'offline');
      }
    });
  }

  private setupErrorQueueFlush(): void {
    setInterval(() => {
      if (this.errorQueue.length > 0 && this.config.enableReporting) {
        // Process any unreported errors
        const unreported = this.errorQueue.filter(error => !error.reported);
        unreported.forEach(error => analyticsIntegrationService.reportError(error));
      }
    }, this.config.errorQueueFlushInterval);
  }

  /**
   * Get comprehensive error monitoring metrics
   */
  getMonitoringMetrics(): ErrorMetrics {
    return errorMonitoringService.getErrorMetrics();
  }

  /**
   * Get system health assessment
   */
  getSystemHealth() {
    return errorMonitoringService.getSystemHealth();
  }

  /**
   * Get error trends over time
   */
  getErrorTrends(days?: number) {
    return errorMonitoringService.getErrorTrends(days);
  }

  /**
   * Export error data for analysis
   */
  async exportErrorData(format?: 'json' | 'csv') {
    return errorMonitoringService.exportErrorData(format);
  }

  /**
   * Clear error monitoring history
   */
  async clearMonitoringHistory() {
    return errorMonitoringService.clearErrorHistory();
  }
}

// Export singleton instance with Hermes compatibility
let _serviceInstance: UnifiedErrorHandlingService | null = null;

export const unifiedErrorHandlingService = (() => {
  if (!_serviceInstance) {
    _serviceInstance = new UnifiedErrorHandlingService();
  }
  return _serviceInstance;
})();

export default unifiedErrorHandlingService;
