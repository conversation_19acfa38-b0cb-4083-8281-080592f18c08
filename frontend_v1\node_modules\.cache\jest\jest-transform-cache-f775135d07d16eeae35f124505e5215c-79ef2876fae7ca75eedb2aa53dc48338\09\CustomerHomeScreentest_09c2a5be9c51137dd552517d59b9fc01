a84a0111f0d921177230bd50908578be
_getJestObj().mock("../../hooks/useCustomerHomeData");
_getJestObj().mock("../../store/authSlice");
_getJestObj().mock("../../hooks/useNavigationGuard");
_getJestObj().mock("../../services/performanceMonitor");
_getJestObj().mock("../../hooks/usePerformance");
_getJestObj().mock("../../hooks/useErrorHandling");
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _native = require("@react-navigation/native");
var _reactNative = require("@testing-library/react-native");
var _react = _interopRequireDefault(require("react"));
var _ThemeContext = require("../../contexts/ThemeContext");
var _useCustomerHomeData = require("../../hooks/useCustomerHomeData");
var _useNavigationGuard = require("../../hooks/useNavigationGuard");
var _performanceMonitor = require("../../services/performanceMonitor");
var _authSlice = require("../../store/authSlice");
var _enhancedTestUtils = require("../../testing/enhancedTestUtils");
var _accessibilityComplianceChecker = require("../../utils/accessibilityComplianceChecker");
var _CustomerHomeScreen = require("../CustomerHomeScreen");
var _jsxRuntime = require("react/jsx-runtime");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var mockUseCustomerHomeData = _useCustomerHomeData.useCustomerHomeData;
var mockUseAuthStore = _authSlice.useAuthStore;
var mockUseNavigationGuard = _useNavigationGuard.useNavigationGuard;
var TestWrapper = function TestWrapper(_ref) {
  var children = _ref.children;
  return (0, _jsxRuntime.jsx)(_native.NavigationContainer, {
    children: (0, _jsxRuntime.jsx)(_ThemeContext.ThemeProvider, {
      children: children
    })
  });
};
var mockCategories = [{
  id: 1,
  name: 'Hair',
  slug: 'hair',
  icon: 'cut',
  color: '#FF6B6B'
}, {
  id: 2,
  name: 'Nails',
  slug: 'nails',
  icon: 'hand',
  color: '#4ECDC4'
}, {
  id: 3,
  name: 'Skincare',
  slug: 'skincare',
  icon: 'face',
  color: '#45B7D1'
}];
var mockFeaturedProviders = [{
  id: 1,
  name: 'Beauty Studio',
  rating: 4.8,
  reviewCount: 150,
  imageUrl: 'https://example.com/provider1.jpg',
  services: ['Hair', 'Makeup'],
  distance: 2.5
}, {
  id: 2,
  name: 'Spa Wellness',
  rating: 4.6,
  reviewCount: 89,
  imageUrl: 'https://example.com/provider2.jpg',
  services: ['Skincare', 'Massage'],
  distance: 1.8
}];
var mockUser = {
  id: 1,
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  role: 'customer'
};
describe('CustomerHomeScreen', function () {
  var mockNavigate = jest.fn();
  var mockRefresh = jest.fn();
  beforeEach(function () {
    jest.clearAllMocks();
    mockUseAuthStore.mockReturnValue({
      isAuthenticated: true,
      user: mockUser,
      login: jest.fn(),
      logout: jest.fn(),
      register: jest.fn()
    });
    mockUseNavigationGuard.mockReturnValue({
      navigate: mockNavigate,
      canNavigate: jest.fn(function () {
        return true;
      }),
      guardedNavigate: mockNavigate
    });
    mockUseCustomerHomeData.mockReturnValue({
      data: {
        categories: mockCategories,
        featuredProviders: mockFeaturedProviders,
        nearbyProviders: [],
        favoriteProviders: [],
        recentBookings: [],
        dashboard: null
      },
      loading: {
        categories: false,
        featuredProviders: false,
        nearbyProviders: false,
        favoriteProviders: false,
        recentBookings: false,
        dashboard: false
      },
      error: {
        categories: null,
        featuredProviders: null,
        nearbyProviders: null,
        favoriteProviders: null,
        recentBookings: null,
        dashboard: null
      },
      refreshing: false,
      refresh: mockRefresh
    });
  });
  describe('Rendering', function () {
    it('renders the home screen correctly', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      expect(_reactNative.screen.getByTestId('customer-home-screen')).toBeTruthy();
      expect(_reactNative.screen.getByText('Browse Services')).toBeTruthy();
      expect(_reactNative.screen.getByText('Featured Providers')).toBeTruthy();
    }));
    it('displays user greeting when authenticated', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText(/Hello, Test/)).toBeTruthy();
      });
    }));
    it('renders categories correctly', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Barber')).toBeTruthy();
        expect(_reactNative.screen.getByText('Nails')).toBeTruthy();
        expect(_reactNative.screen.getByText('Skincare')).toBeTruthy();
      });
    }));
    it('renders featured providers correctly', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Beauty Studio')).toBeTruthy();
        expect(_reactNative.screen.getByText('Spa Wellness')).toBeTruthy();
        expect(_reactNative.screen.getByText('4.8')).toBeTruthy();
        expect(_reactNative.screen.getByText('4.6')).toBeTruthy();
      });
    }));
  });
  describe('Loading States', function () {
    it('shows loading indicators when data is loading', (0, _asyncToGenerator2.default)(function* () {
      mockUseCustomerHomeData.mockReturnValue({
        data: {
          categories: [],
          featuredProviders: [],
          nearbyProviders: [],
          favoriteProviders: [],
          recentBookings: [],
          dashboard: null
        },
        loading: {
          categories: true,
          featuredProviders: true,
          nearbyProviders: false,
          favoriteProviders: false,
          recentBookings: false,
          dashboard: false
        },
        error: {
          categories: null,
          featuredProviders: null,
          nearbyProviders: null,
          favoriteProviders: null,
          recentBookings: null,
          dashboard: null
        },
        refreshing: false,
        refresh: mockRefresh
      });
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      expect(_reactNative.screen.getByTestId('categories-loading')).toBeTruthy();
      expect(_reactNative.screen.getByTestId('featured-providers-loading')).toBeTruthy();
    }));
    it('shows refreshing state correctly', (0, _asyncToGenerator2.default)(function* () {
      mockUseCustomerHomeData.mockReturnValue({
        data: {
          categories: mockCategories,
          featuredProviders: mockFeaturedProviders,
          nearbyProviders: [],
          favoriteProviders: [],
          recentBookings: [],
          dashboard: null
        },
        loading: {
          categories: false,
          featuredProviders: false,
          nearbyProviders: false,
          favoriteProviders: false,
          recentBookings: false,
          dashboard: false
        },
        error: {
          categories: null,
          featuredProviders: null,
          nearbyProviders: null,
          favoriteProviders: null,
          recentBookings: null,
          dashboard: null
        },
        refreshing: true,
        refresh: mockRefresh
      });
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      var scrollView = _reactNative.screen.getByTestId('home-scroll-view');
      expect(scrollView.props.refreshControl.props.refreshing).toBe(true);
    }));
  });
  describe('Error States', function () {
    it('handles category loading errors gracefully', (0, _asyncToGenerator2.default)(function* () {
      mockUseCustomerHomeData.mockReturnValue({
        data: {
          categories: [],
          featuredProviders: mockFeaturedProviders,
          nearbyProviders: [],
          favoriteProviders: [],
          recentBookings: [],
          dashboard: null
        },
        loading: {
          categories: false,
          featuredProviders: false,
          nearbyProviders: false,
          favoriteProviders: false,
          recentBookings: false,
          dashboard: false
        },
        error: {
          categories: new Error('Failed to load categories'),
          featuredProviders: null,
          nearbyProviders: null,
          favoriteProviders: null,
          recentBookings: null,
          dashboard: null
        },
        refreshing: false,
        refresh: mockRefresh
      });
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByTestId('categories-error')).toBeTruthy();
      });
    }));
    it('handles provider loading errors gracefully', (0, _asyncToGenerator2.default)(function* () {
      mockUseCustomerHomeData.mockReturnValue({
        data: {
          categories: mockCategories,
          featuredProviders: [],
          nearbyProviders: [],
          favoriteProviders: [],
          recentBookings: [],
          dashboard: null
        },
        loading: {
          categories: false,
          featuredProviders: false,
          nearbyProviders: false,
          favoriteProviders: false,
          recentBookings: false,
          dashboard: false
        },
        error: {
          categories: null,
          featuredProviders: new Error('Failed to load providers'),
          nearbyProviders: null,
          favoriteProviders: null,
          recentBookings: null,
          dashboard: null
        },
        refreshing: false,
        refresh: mockRefresh
      });
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByTestId('featured-providers-error')).toBeTruthy();
      });
    }));
  });
  describe('User Interactions', function () {
    it('handles category press correctly', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        var barberCategory = _reactNative.screen.getByText('Barber');
        _reactNative.fireEvent.press(barberCategory);
      });
      expect(mockNavigate).toHaveBeenCalledWith('Search', {
        category: 'barber'
      });
    }));
    it('handles provider press correctly', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        var provider = _reactNative.screen.getByText('Beauty Studio');
        _reactNative.fireEvent.press(provider);
      });
      expect(mockNavigate).toHaveBeenCalledWith('ProviderDetails', {
        providerId: 1
      });
    }));
    it('handles pull to refresh correctly', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      var scrollView = _reactNative.screen.getByTestId('home-scroll-view');
      yield (0, _reactNative.act)((0, _asyncToGenerator2.default)(function* () {
        (0, _reactNative.fireEvent)(scrollView, 'refresh');
      }));
      expect(mockRefresh).toHaveBeenCalled();
    }));
    it('handles see all buttons correctly', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        var seeAllButton = _reactNative.screen.getByTestId('featured-providers-see-all');
        _reactNative.fireEvent.press(seeAllButton);
      });
      expect(mockNavigate).toHaveBeenCalledWith('Search', {
        filter: 'featured'
      });
    }));
  });
  describe('Performance', function () {
    it('tracks component render performance', (0, _asyncToGenerator2.default)(function* () {
      var performanceSpy = jest.spyOn(_performanceMonitor.performanceMonitor, 'trackRender');
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(performanceSpy).toHaveBeenCalledWith('CustomerHomeScreen', expect.any(Number), expect.any(Object));
      });
    }));
    it('tracks user interaction performance', (0, _asyncToGenerator2.default)(function* () {
      var interactionSpy = jest.spyOn(_performanceMonitor.performanceMonitor, 'trackUserInteraction');
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        var hairCategory = _reactNative.screen.getByText('Hair');
        _reactNative.fireEvent.press(hairCategory);
      });
      expect(interactionSpy).toHaveBeenCalledWith('category_press', expect.any(Number), expect.any(Object));
    }));
    it('renders within performance threshold', (0, _asyncToGenerator2.default)(function* () {
      var renderTime = yield measureAsyncPerformance('CustomerHomeScreen render', (0, _asyncToGenerator2.default)(function* () {
        (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
        }));
        yield (0, _reactNative.waitFor)(function () {
          expect(_reactNative.screen.getByTestId('customer-home-screen')).toBeTruthy();
        });
      }));
      expect(renderTime).toBeLessThan(1000);
    }));
  });
  describe('Accessibility', function () {
    it('has proper accessibility labels', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      expect(_reactNative.screen.getByLabelText('Customer Home Screen')).toBeTruthy();
      expect(_reactNative.screen.getByLabelText('Main content area')).toBeTruthy();
      expect(_reactNative.screen.getByLabelText('Pull to refresh')).toBeTruthy();
    }));
    it('has proper accessibility roles', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      var scrollView = _reactNative.screen.getByTestId('home-scroll-view');
      expect(scrollView.props.accessibilityRole).toBe('scrollbar');
    }));
    it('supports screen reader navigation', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        var categories = _reactNative.screen.getAllByRole('button');
        expect(categories.length).toBeGreaterThan(0);
        categories.forEach(function (category) {
          expect(category.props.accessibilityLabel).toBeTruthy();
        });
      });
    }));
  });
  describe('Integration', function () {
    it('integrates with auth store correctly', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      expect(mockUseAuthStore).toHaveBeenCalled();
    }));
    it('integrates with navigation guard correctly', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      expect(mockUseNavigationGuard).toHaveBeenCalled();
    }));
    it('integrates with customer home data hook correctly', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      expect(mockUseCustomerHomeData).toHaveBeenCalled();
    }));
  });
  describe('Enhanced Accessibility Compliance', function () {
    it('should meet WCAG 2.1 AA standards', (0, _asyncToGenerator2.default)(function* () {
      var component = (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {
        navigation: mockNavigation,
        route: mockRoute
      });
      var accessibilityResults = yield _enhancedTestUtils.accessibilityTestUtils.testScreenReaderAccessibility(component);
      expect(accessibilityResults.buttons).toBeGreaterThan(0);
      expect(accessibilityResults.headings).toBeGreaterThan(0);
    }));
    it('should support comprehensive keyboard navigation', (0, _asyncToGenerator2.default)(function* () {
      var component = (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {
        navigation: mockNavigation,
        route: mockRoute
      });
      var focusableElements = yield _enhancedTestUtils.accessibilityTestUtils.testKeyboardNavigation(component);
      expect(focusableElements).toBeGreaterThan(0);
    }));
    it('should have proper touch target sizes for all interactive elements', function () {
      var component = (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {
        navigation: mockNavigation,
        route: mockRoute
      });
      var touchTargets = _enhancedTestUtils.accessibilityTestUtils.testTouchTargetSizes(component);
      expect(touchTargets).toBeGreaterThan(0);
    });
    it('should have semantic headings with proper hierarchy', function () {
      var _render = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {
            navigation: mockNavigation,
            route: mockRoute
          })
        })),
        getAllByRole = _render.getAllByRole;
      var headings = getAllByRole('header');
      expect(headings.length).toBeGreaterThan(0);
    });
    it('should run accessibility compliance check', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {
          navigation: mockNavigation,
          route: mockRoute
        })
      }));
      var report = _accessibilityComplianceChecker.accessibilityComplianceChecker.generateReport();
      expect(report.complianceScore).toBeGreaterThan(80);
    }));
  });
  describe('Enhanced Performance', function () {
    it('should render within acceptable time limits', (0, _asyncToGenerator2.default)(function* () {
      var component = (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {
        navigation: mockNavigation,
        route: mockRoute
      });
      var renderTime = yield _enhancedTestUtils.performanceTestUtils.measureRenderTime(component);
      expect(renderTime).toBeLessThan(1000);
    }));
    it('should handle re-renders efficiently', (0, _asyncToGenerator2.default)(function* () {
      var component = (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {
        navigation: mockNavigation,
        route: mockRoute
      });
      var updates = [{
        key: 'update1'
      }, {
        key: 'update2'
      }, {
        key: 'update3'
      }];
      var performanceResults = yield _enhancedTestUtils.performanceTestUtils.testReRenderPerformance(component, updates);
      expect(performanceResults.averageRenderTime).toBeLessThan(100);
      expect(performanceResults.maxRenderTime).toBeLessThan(500);
    }));
    it('should not have memory leaks', (0, _asyncToGenerator2.default)(function* () {
      var component = (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {
        navigation: mockNavigation,
        route: mockRoute
      });
      var memoryResults = yield _enhancedTestUtils.performanceTestUtils.testMemoryUsage(component);
      expect(memoryResults.memoryLeakage).toBeLessThan(1000000);
    }));
  });
  describe('Enhanced User Journey', function () {
    it('should handle complete user journey correctly', (0, _asyncToGenerator2.default)(function* () {
      var _render2 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {
            navigation: mockNavigation,
            route: mockRoute
          })
        })),
        getByTestId = _render2.getByTestId;
      yield _enhancedTestUtils.userInteractionTestUtils.simulateUserJourney([{
        action: 'press',
        target: 'provider-card-1'
      }, {
        action: 'wait',
        duration: 500
      }, {
        action: 'press',
        target: 'see-all-featured'
      }, {
        action: 'wait',
        duration: 300
      }, {
        action: 'press',
        target: 'category-card-1'
      }]);
      expect(mockNavigation.navigate).toHaveBeenCalledTimes(3);
    }));
    it('should handle rapid user interactions gracefully', (0, _asyncToGenerator2.default)(function* () {
      var _render3 = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {
            navigation: mockNavigation,
            route: mockRoute
          })
        })),
        getByTestId = _render3.getByTestId;
      var providerCard = getByTestId('provider-card-1');
      _reactNative.fireEvent.press(providerCard);
      _reactNative.fireEvent.press(providerCard);
      _reactNative.fireEvent.press(providerCard);
      expect(mockNavigation.navigate).toHaveBeenCalledTimes(1);
    }));
  });
  afterEach(function () {
    jest.clearAllMocks();
    _performanceMonitor.performanceMonitor.reset();
    _accessibilityComplianceChecker.accessibilityComplianceChecker.reset();
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************