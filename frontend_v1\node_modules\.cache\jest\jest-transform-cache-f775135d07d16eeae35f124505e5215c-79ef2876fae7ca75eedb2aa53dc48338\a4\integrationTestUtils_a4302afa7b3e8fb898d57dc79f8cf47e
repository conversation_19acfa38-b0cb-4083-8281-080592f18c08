47207118a1d82827651e6700347998b1
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.testBookingFlow = exports.setupIntegrationTest = exports.renderWithIntegrationSetup = exports.performanceTestUtils = exports.mockApiResponses = exports.errorTestUtils = exports.default = exports.createTestUser = exports.createTestProvider = exports.createTestBooking = exports.accessibilityTestUtils = exports.MockWebSocket = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _native = require("@react-navigation/native");
var _reactNative = require("@testing-library/react-native");
var _react = _interopRequireDefault(require("react"));
var _reactRedux = require("react-redux");
var _ThemeContext = require("../contexts/ThemeContext");
var _realTimeNotificationService = require("../services/realTimeNotificationService");
var _jsxRuntime = require("react/jsx-runtime");
var MockWebSocket = exports.MockWebSocket = function () {
  function MockWebSocket(url) {
    (0, _classCallCheck2.default)(this, MockWebSocket);
    this.url = url;
    this.listeners = new Map();
    this.isConnected = false;
  }
  return (0, _createClass2.default)(MockWebSocket, [{
    key: "connect",
    value: function connect() {
      this.isConnected = true;
      this.emit('connect', {});
    }
  }, {
    key: "disconnect",
    value: function disconnect() {
      this.isConnected = false;
      this.emit('disconnect', {});
    }
  }, {
    key: "send",
    value: function send(data) {
      console.log('MockWebSocket send:', data);
    }
  }, {
    key: "on",
    value: function on(event, callback) {
      if (!this.listeners.has(event)) {
        this.listeners.set(event, new Set());
      }
      this.listeners.get(event).add(callback);
    }
  }, {
    key: "off",
    value: function off(event, callback) {
      var eventListeners = this.listeners.get(event);
      if (eventListeners) {
        eventListeners.delete(callback);
      }
    }
  }, {
    key: "emit",
    value: function emit(event, data) {
      var eventListeners = this.listeners.get(event);
      if (eventListeners) {
        eventListeners.forEach(function (callback) {
          return callback(data);
        });
      }
    }
  }, {
    key: "simulateBookingUpdate",
    value: function simulateBookingUpdate(bookingId, status) {
      this.emit('booking_update', {
        bookingId: bookingId,
        status: status,
        message: `Booking ${status}`,
        timestamp: new Date().toISOString()
      });
    }
  }, {
    key: "simulateNewMessage",
    value: function simulateNewMessage(conversationId, message) {
      this.emit('new_message', {
        conversationId: conversationId,
        senderId: 'test_sender',
        senderName: 'Test Sender',
        message: message,
        messageType: 'text'
      });
    }
  }, {
    key: "simulateProviderLocation",
    value: function simulateProviderLocation(bookingId, location) {
      this.emit('provider_location_update', {
        bookingId: bookingId,
        location: location
      });
    }
  }]);
}();
var mockApiResponses = exports.mockApiResponses = {
  createBooking: {
    id: 'test_booking_123',
    status: 'confirmed',
    providerId: 'test_provider_123',
    serviceId: 'test_service_123',
    scheduledDate: '2024-01-15',
    scheduledTime: '10:00',
    totalAmount: 50.0
  },
  getBookingDetails: {
    id: 'test_booking_123',
    status: 'confirmed',
    provider: {
      id: 'test_provider_123',
      name: 'Test Provider',
      phone: '+**********',
      profileImage: 'https://example.com/avatar.jpg'
    },
    service: {
      name: 'Test Service',
      duration: 60,
      price: 50.0
    },
    customerLocation: {
      latitude: 45.4215,
      longitude: -75.6972
    },
    estimatedArrival: '2024-01-15T10:30:00Z'
  },
  getProviders: [{
    id: 'test_provider_1',
    name: 'Test Provider 1',
    rating: 4.8,
    reviewCount: 125,
    services: ['haircut', 'styling'],
    location: {
      latitude: 45.4215,
      longitude: -75.6972
    }
  }, {
    id: 'test_provider_2',
    name: 'Test Provider 2',
    rating: 4.6,
    reviewCount: 89,
    services: ['massage', 'therapy'],
    location: {
      latitude: 45.4225,
      longitude: -75.6982
    }
  }],
  getServices: [{
    id: 'test_service_1',
    name: 'Haircut',
    description: 'Professional haircut service',
    price: 30.0,
    duration: 45,
    category: 'Hair'
  }, {
    id: 'test_service_2',
    name: 'Massage',
    description: 'Relaxing massage therapy',
    price: 80.0,
    duration: 90,
    category: 'Wellness'
  }],
  searchResults: {
    providers: [],
    services: [],
    total: 0,
    page: 1,
    limit: 20
  }
};
var createTestUser = exports.createTestUser = function createTestUser() {
  var overrides = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  return Object.assign({
    id: 'test_user_123',
    firstName: 'Test',
    lastName: 'User',
    email: '<EMAIL>',
    phone: '+**********',
    profileImage: null
  }, overrides);
};
var createTestBooking = exports.createTestBooking = function createTestBooking() {
  var overrides = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  return Object.assign({
    id: 'test_booking_123',
    status: 'confirmed',
    providerId: 'test_provider_123',
    serviceId: 'test_service_123',
    scheduledDate: '2024-01-15',
    scheduledTime: '10:00',
    totalAmount: 50.0
  }, overrides);
};
var createTestProvider = exports.createTestProvider = function createTestProvider() {
  var overrides = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  return Object.assign({
    id: 'test_provider_123',
    name: 'Test Provider',
    businessName: 'Test Business',
    description: 'Test provider description',
    rating: 4.8,
    reviewCount: 125,
    isVerified: true,
    isOnline: true,
    avatar: 'https://example.com/avatar.jpg',
    coverImage: 'https://example.com/cover.jpg',
    contact: {
      phone: '+**********',
      email: '<EMAIL>'
    },
    location: {
      latitude: 45.4215,
      longitude: -75.6972,
      address: '123 Test Street, Test City'
    }
  }, overrides);
};
var setupIntegrationTest = exports.setupIntegrationTest = function setupIntegrationTest() {
  var mockWebSocket = new MockWebSocket('ws://test');
  jest.spyOn(_realTimeNotificationService.realTimeNotificationService, 'initialize').mockResolvedValue(undefined);
  jest.spyOn(_realTimeNotificationService.realTimeNotificationService, 'isServiceConnected').mockReturnValue(true);
  return {
    mockWebSocket: mockWebSocket,
    cleanup: function cleanup() {
      jest.restoreAllMocks();
    }
  };
};
var testBookingFlow = exports.testBookingFlow = {
  selectService: function () {
    var _selectService = (0, _asyncToGenerator2.default)(function* (getByTestId, serviceId) {
      var serviceCard = getByTestId(`service-card-${serviceId}`);
      _reactNative.fireEvent.press(serviceCard);
      var continueButton = getByTestId('continue-button');
      _reactNative.fireEvent.press(continueButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(getByTestId('time-slot-selection')).toBeTruthy();
      });
    });
    function selectService(_x, _x2) {
      return _selectService.apply(this, arguments);
    }
    return selectService;
  }(),
  selectTimeSlot: function () {
    var _selectTimeSlot = (0, _asyncToGenerator2.default)(function* (getByTestId, timeSlot) {
      var timeSlotButton = getByTestId(`time-slot-${timeSlot}`);
      _reactNative.fireEvent.press(timeSlotButton);
      var continueButton = getByTestId('continue-button');
      _reactNative.fireEvent.press(continueButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(getByTestId('customer-info-form')).toBeTruthy();
      });
    });
    function selectTimeSlot(_x3, _x4) {
      return _selectTimeSlot.apply(this, arguments);
    }
    return selectTimeSlot;
  }(),
  fillCustomerInfo: function () {
    var _fillCustomerInfo = (0, _asyncToGenerator2.default)(function* (getByTestId, info) {
      var firstNameInput = getByTestId('first-name-input');
      var lastNameInput = getByTestId('last-name-input');
      var emailInput = getByTestId('email-input');
      var phoneInput = getByTestId('phone-input');
      _reactNative.fireEvent.changeText(firstNameInput, info.firstName);
      _reactNative.fireEvent.changeText(lastNameInput, info.lastName);
      _reactNative.fireEvent.changeText(emailInput, info.email);
      _reactNative.fireEvent.changeText(phoneInput, info.phone);
      var continueButton = getByTestId('continue-button');
      _reactNative.fireEvent.press(continueButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(getByTestId('payment-selection')).toBeTruthy();
      });
    });
    function fillCustomerInfo(_x5, _x6) {
      return _fillCustomerInfo.apply(this, arguments);
    }
    return fillCustomerInfo;
  }(),
  selectPaymentMethod: function () {
    var _selectPaymentMethod = (0, _asyncToGenerator2.default)(function* (getByTestId, paymentMethodId) {
      var paymentMethod = getByTestId(`payment-method-${paymentMethodId}`);
      _reactNative.fireEvent.press(paymentMethod);
      var continueButton = getByTestId('continue-button');
      _reactNative.fireEvent.press(continueButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(getByTestId('booking-summary')).toBeTruthy();
      });
    });
    function selectPaymentMethod(_x7, _x8) {
      return _selectPaymentMethod.apply(this, arguments);
    }
    return selectPaymentMethod;
  }(),
  confirmBooking: function () {
    var _confirmBooking = (0, _asyncToGenerator2.default)(function* (getByTestId) {
      var confirmButton = getByTestId('confirm-booking-button');
      _reactNative.fireEvent.press(confirmButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(getByTestId('booking-confirmation')).toBeTruthy();
      });
    });
    function confirmBooking(_x9) {
      return _confirmBooking.apply(this, arguments);
    }
    return confirmBooking;
  }()
};
var performanceTestUtils = exports.performanceTestUtils = {
  measureRenderTime: function () {
    var _measureRenderTime = (0, _asyncToGenerator2.default)(function* (component) {
      var startTime = performance.now();
      yield (0, _reactNative.act)((0, _asyncToGenerator2.default)(function* () {
        (0, _reactNative.render)(component);
      }));
      var endTime = performance.now();
      return endTime - startTime;
    });
    function measureRenderTime(_x0) {
      return _measureRenderTime.apply(this, arguments);
    }
    return measureRenderTime;
  }(),
  measureInteractionTime: function () {
    var _measureInteractionTime = (0, _asyncToGenerator2.default)(function* (getByTestId, testId) {
      var startTime = performance.now();
      yield (0, _reactNative.act)((0, _asyncToGenerator2.default)(function* () {
        var element = getByTestId(testId);
        _reactNative.fireEvent.press(element);
      }));
      var endTime = performance.now();
      return endTime - startTime;
    });
    function measureInteractionTime(_x1, _x10) {
      return _measureInteractionTime.apply(this, arguments);
    }
    return measureInteractionTime;
  }(),
  expectPerformanceWithinBounds: function expectPerformanceWithinBounds(actualTime, maxTime) {
    expect(actualTime).toBeLessThan(maxTime);
  }
};
var errorTestUtils = exports.errorTestUtils = {
  simulateNetworkError: function simulateNetworkError() {
    return new Error('Network request failed');
  },
  simulateValidationError: function simulateValidationError(field) {
    var error = new Error(`Validation failed for ${field}`);
    error.name = 'ValidationError';
    return error;
  },
  simulateAuthenticationError: function simulateAuthenticationError() {
    var error = new Error('Authentication failed');
    error.name = 'AuthenticationError';
    return error;
  },
  testErrorRecovery: function () {
    var _testErrorRecovery = (0, _asyncToGenerator2.default)(function* (getByTestId, errorType) {
      var error = this.simulateNetworkError();
      yield (0, _reactNative.act)((0, _asyncToGenerator2.default)(function* () {
        throw error;
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(getByTestId('error-message')).toBeTruthy();
      });
      var retryButton = getByTestId('retry-button');
      _reactNative.fireEvent.press(retryButton);
      yield (0, _reactNative.waitFor)(function () {
        expect(getByTestId('loading-indicator')).toBeTruthy();
      });
    });
    function testErrorRecovery(_x11, _x12) {
      return _testErrorRecovery.apply(this, arguments);
    }
    return testErrorRecovery;
  }()
};
var accessibilityTestUtils = exports.accessibilityTestUtils = {
  checkAccessibilityLabels: function checkAccessibilityLabels(getByTestId, testIds) {
    testIds.forEach(function (testId) {
      var element = getByTestId(testId);
      expect(element.props.accessibilityLabel).toBeTruthy();
    });
  },
  checkAccessibilityRoles: function checkAccessibilityRoles(getByTestId, testIds) {
    testIds.forEach(function (testId) {
      var element = getByTestId(testId);
      expect(element.props.accessibilityRole).toBeTruthy();
    });
  },
  checkMinimumTouchTargets: function checkMinimumTouchTargets(getByTestId, testIds) {
    testIds.forEach(function (testId) {
      var element = getByTestId(testId);
      var style = element.props.style;
      if (style && (style.width || style.height)) {
        expect(style.width).toBeGreaterThanOrEqual(44);
        expect(style.height).toBeGreaterThanOrEqual(44);
      }
    });
  }
};
var renderWithIntegrationSetup = exports.renderWithIntegrationSetup = function renderWithIntegrationSetup(ui) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var _setupIntegrationTest = setupIntegrationTest(),
    mockWebSocket = _setupIntegrationTest.mockWebSocket,
    cleanup = _setupIntegrationTest.cleanup;
  var AllTheProviders = function AllTheProviders(_ref4) {
    var children = _ref4.children;
    return (0, _jsxRuntime.jsx)(_reactRedux.Provider, {
      store: options.store || createMockStore(),
      children: (0, _jsxRuntime.jsx)(_ThemeContext.ThemeProvider, {
        children: (0, _jsxRuntime.jsx)(_native.NavigationContainer, {
          children: children
        })
      })
    });
  };
  var result = (0, _reactNative.render)(ui, Object.assign({
    wrapper: AllTheProviders
  }, options));
  return Object.assign({}, result, {
    mockWebSocket: mockWebSocket,
    cleanup: cleanup
  });
};
var createMockStore = function createMockStore() {
  var initialState = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  return {
    getState: function getState() {
      return Object.assign({
        auth: {
          user: createTestUser(),
          authToken: 'test_token',
          isAuthenticated: true
        },
        bookings: {
          bookings: [createTestBooking()],
          loading: false
        }
      }, initialState);
    },
    dispatch: jest.fn(),
    subscribe: jest.fn()
  };
};
var _default = exports.default = {
  setupIntegrationTest: setupIntegrationTest,
  testBookingFlow: testBookingFlow,
  performanceTestUtils: performanceTestUtils,
  errorTestUtils: errorTestUtils,
  accessibilityTestUtils: accessibilityTestUtils,
  renderWithIntegrationSetup: renderWithIntegrationSetup,
  mockApiResponses: mockApiResponses,
  createTestUser: createTestUser,
  createTestBooking: createTestBooking,
  createTestProvider: createTestProvider
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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