{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "customDirectEventTypes", "customBubblingEventTypes", "get", "register", "_invariant", "viewConfigCallbacks", "Map", "viewConfigs", "processEventTypes", "viewConfig", "bubblingEventTypes", "directEventTypes", "__DEV__", "topLevelType", "invariant", "name", "callback", "has", "set", "test"], "sources": ["ReactNativeViewConfigRegistry.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @noformat\n * @nolint\n * @flow strict-local\n * @generated SignedSource<<83073425aa3f71ced2c8c51f25a25938>>\n */\n\n'use strict';\n\nimport {type ViewConfig} from './ReactNativeTypes';\nimport invariant from 'invariant';\n\n// Event configs\nexport const customBubblingEventTypes: {\n  [eventName: string]: $ReadOnly<{\n    phasedRegistrationNames: $ReadOnly<{\n      captured: string,\n      bubbled: string,\n      skipBubbling?: ?boolean,\n    }>,\n  }>,\n} = {};\nexport const customDirectEventTypes: {\n  [eventName: string]: $ReadOnly<{\n    registrationName: string,\n  }>,\n} = {};\n\nconst viewConfigCallbacks = new Map<string, ?() => ViewConfig>();\nconst viewConfigs = new Map<string, ViewConfig>();\n\nfunction processEventTypes(viewConfig: ViewConfig): void {\n  const {bubblingEventTypes, directEventTypes} = viewConfig;\n\n  if (__DEV__) {\n    if (bubblingEventTypes != null && directEventTypes != null) {\n      for (const topLevelType in directEventTypes) {\n        invariant(\n          bubblingEventTypes[topLevelType] == null,\n          'Event cannot be both direct and bubbling: %s',\n          topLevelType,\n        );\n      }\n    }\n  }\n\n  if (bubblingEventTypes != null) {\n    for (const topLevelType in bubblingEventTypes) {\n      if (customBubblingEventTypes[topLevelType] == null) {\n        customBubblingEventTypes[topLevelType] =\n          bubblingEventTypes[topLevelType];\n      }\n    }\n  }\n\n  if (directEventTypes != null) {\n    for (const topLevelType in directEventTypes) {\n      if (customDirectEventTypes[topLevelType] == null) {\n        customDirectEventTypes[topLevelType] = directEventTypes[topLevelType];\n      }\n    }\n  }\n}\n\n/**\n * Registers a native view/component by name.\n * A callback is provided to load the view config from UIManager.\n * The callback is deferred until the view is actually rendered.\n */\nexport function register(name: string, callback: () => ViewConfig): string {\n  invariant(\n    !viewConfigCallbacks.has(name),\n    'Tried to register two views with the same name %s',\n    name,\n  );\n  invariant(\n    typeof callback === 'function',\n    'View config getter callback for component `%s` must be a function (received `%s`)',\n    name,\n    callback === null ? 'null' : typeof callback,\n  );\n  viewConfigCallbacks.set(name, callback);\n  return name;\n}\n\n/**\n * Retrieves a config for the specified view.\n * If this is the first time the view has been used,\n * This configuration will be lazy-loaded from UIManager.\n */\nexport function get(name: string): ViewConfig {\n  let viewConfig = viewConfigs.get(name);\n  if (viewConfig == null) {\n    const callback = viewConfigCallbacks.get(name);\n    if (typeof callback !== 'function') {\n      invariant(\n        false,\n        'View config getter callback for component `%s` must be a function (received `%s`).%s',\n        name,\n        callback === null ? 'null' : typeof callback,\n        // $FlowFixMe[recursive-definition]\n        typeof name[0] === 'string' && /[a-z]/.test(name[0])\n          ? ' Make sure to start component names with a capital letter.'\n          : '',\n      );\n    }\n    viewConfig = callback();\n    invariant(viewConfig, 'View config not found for component `%s`', name);\n\n    processEventTypes(viewConfig);\n    viewConfigs.set(name, viewConfig);\n\n    // Clear the callback after the config is set so that\n    // we don't mask any errors during registration.\n    viewConfigCallbacks.set(name, null);\n  }\n  return viewConfig;\n}\n"], "mappings": "AAYA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,sBAAA,GAAAF,OAAA,CAAAG,wBAAA;AAAAH,OAAA,CAAAI,GAAA,GAAAA,GAAA;AAAAJ,OAAA,CAAAK,QAAA,GAAAA,QAAA;AAGb,IAAAC,UAAA,GAAAV,sBAAA,CAAAC,OAAA;AAGO,IAAMM,wBAQZ,GAAAH,OAAA,CAAAG,wBAAA,GAAG,CAAC,CAAC;AACC,IAAMD,sBAIZ,GAAAF,OAAA,CAAAE,sBAAA,GAAG,CAAC,CAAC;AAEN,IAAMK,mBAAmB,GAAG,IAAIC,GAAG,CAA4B,CAAC;AAChE,IAAMC,WAAW,GAAG,IAAID,GAAG,CAAqB,CAAC;AAEjD,SAASE,iBAAiBA,CAACC,UAAsB,EAAQ;EACvD,IAAOC,kBAAkB,GAAsBD,UAAU,CAAlDC,kBAAkB;IAAEC,gBAAgB,GAAIF,UAAU,CAA9BE,gBAAgB;EAE3C,IAAIC,OAAO,EAAE;IACX,IAAIF,kBAAkB,IAAI,IAAI,IAAIC,gBAAgB,IAAI,IAAI,EAAE;MAC1D,KAAK,IAAME,YAAY,IAAIF,gBAAgB,EAAE;QAC3C,IAAAG,kBAAS,EACPJ,kBAAkB,CAACG,YAAY,CAAC,IAAI,IAAI,EACxC,8CAA8C,EAC9CA,YACF,CAAC;MACH;IACF;EACF;EAEA,IAAIH,kBAAkB,IAAI,IAAI,EAAE;IAC9B,KAAK,IAAMG,aAAY,IAAIH,kBAAkB,EAAE;MAC7C,IAAIT,wBAAwB,CAACY,aAAY,CAAC,IAAI,IAAI,EAAE;QAClDZ,wBAAwB,CAACY,aAAY,CAAC,GACpCH,kBAAkB,CAACG,aAAY,CAAC;MACpC;IACF;EACF;EAEA,IAAIF,gBAAgB,IAAI,IAAI,EAAE;IAC5B,KAAK,IAAME,cAAY,IAAIF,gBAAgB,EAAE;MAC3C,IAAIX,sBAAsB,CAACa,cAAY,CAAC,IAAI,IAAI,EAAE;QAChDb,sBAAsB,CAACa,cAAY,CAAC,GAAGF,gBAAgB,CAACE,cAAY,CAAC;MACvE;IACF;EACF;AACF;AAOO,SAASV,QAAQA,CAACY,IAAY,EAAEC,QAA0B,EAAU;EACzE,IAAAF,kBAAS,EACP,CAACT,mBAAmB,CAACY,GAAG,CAACF,IAAI,CAAC,EAC9B,mDAAmD,EACnDA,IACF,CAAC;EACD,IAAAD,kBAAS,EACP,OAAOE,QAAQ,KAAK,UAAU,EAC9B,mFAAmF,EACnFD,IAAI,EACJC,QAAQ,KAAK,IAAI,GAAG,MAAM,GAAG,OAAOA,QACtC,CAAC;EACDX,mBAAmB,CAACa,GAAG,CAACH,IAAI,EAAEC,QAAQ,CAAC;EACvC,OAAOD,IAAI;AACb;AAOO,SAASb,GAAGA,CAACa,IAAY,EAAc;EAC5C,IAAIN,UAAU,GAAGF,WAAW,CAACL,GAAG,CAACa,IAAI,CAAC;EACtC,IAAIN,UAAU,IAAI,IAAI,EAAE;IACtB,IAAMO,QAAQ,GAAGX,mBAAmB,CAACH,GAAG,CAACa,IAAI,CAAC;IAC9C,IAAI,OAAOC,QAAQ,KAAK,UAAU,EAAE;MAClC,IAAAF,kBAAS,EACP,KAAK,EACL,sFAAsF,EACtFC,IAAI,EACJC,QAAQ,KAAK,IAAI,GAAG,MAAM,GAAG,OAAOA,QAAQ,EAE5C,OAAOD,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,OAAO,CAACI,IAAI,CAACJ,IAAI,CAAC,CAAC,CAAC,CAAC,GAChD,4DAA4D,GAC5D,EACN,CAAC;IACH;IACAN,UAAU,GAAGO,QAAQ,CAAC,CAAC;IACvB,IAAAF,kBAAS,EAACL,UAAU,EAAE,0CAA0C,EAAEM,IAAI,CAAC;IAEvEP,iBAAiB,CAACC,UAAU,CAAC;IAC7BF,WAAW,CAACW,GAAG,CAACH,IAAI,EAAEN,UAAU,CAAC;IAIjCJ,mBAAmB,CAACa,GAAG,CAACH,IAAI,EAAE,IAAI,CAAC;EACrC;EACA,OAAON,UAAU;AACnB", "ignoreList": []}