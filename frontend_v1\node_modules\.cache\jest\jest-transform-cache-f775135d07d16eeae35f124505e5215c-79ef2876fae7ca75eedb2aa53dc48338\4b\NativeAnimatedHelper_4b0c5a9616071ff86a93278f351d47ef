adffe8ada99ebde0e741bb706c218338
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _NativeAnimatedModule = _interopRequireDefault(require("../../../Libraries/Animated/NativeAnimatedModule"));
var _NativeAnimatedTurboModule = _interopRequireDefault(require("../../../Libraries/Animated/NativeAnimatedTurboModule"));
var _NativeEventEmitter = _interopRequireDefault(require("../../../Libraries/EventEmitter/NativeEventEmitter"));
var _RCTDeviceEventEmitter = _interopRequireDefault(require("../../../Libraries/EventEmitter/RCTDeviceEventEmitter"));
var _Platform = _interopRequireDefault(require("../../../Libraries/Utilities/Platform"));
var ReactNativeFeatureFlags = _interopRequireWildcard(require("../featureflags/ReactNativeFeatureFlags"));
var _invariant = _interopRequireDefault(require("invariant"));
var _nullthrows = _interopRequireDefault(require("nullthrows"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var NativeAnimatedModule = _NativeAnimatedModule.default != null ? _NativeAnimatedModule.default : _NativeAnimatedTurboModule.default;
var __nativeAnimatedNodeTagCount = 1;
var __nativeAnimationIdCount = 1;
var nativeEventEmitter;
var waitingForQueuedOperations = new Set();
var queueOperations = false;
var queue = [];
var singleOpQueue = [];
var isSingleOpBatching = _Platform.default.OS === 'android' && (NativeAnimatedModule == null ? void 0 : NativeAnimatedModule.queueAndExecuteBatchedOperations) != null && ReactNativeFeatureFlags.animatedShouldUseSingleOp();
var flushQueueImmediate = null;
var eventListenerGetValueCallbacks = {};
var eventListenerAnimationFinishedCallbacks = {};
var globalEventEmitterGetValueListener = null;
var globalEventEmitterAnimationFinishedListener = null;
function createNativeOperations() {
  var methodNames = ['createAnimatedNode', 'updateAnimatedNodeConfig', 'getValue', 'startListeningToAnimatedNodeValue', 'stopListeningToAnimatedNodeValue', 'connectAnimatedNodes', 'disconnectAnimatedNodes', 'startAnimatingNode', 'stopAnimation', 'setAnimatedNodeValue', 'setAnimatedNodeOffset', 'flattenAnimatedNodeOffset', 'extractAnimatedNodeOffset', 'connectAnimatedNodeToView', 'disconnectAnimatedNodeFromView', 'restoreDefaultValues', 'dropAnimatedNode', 'addAnimatedEventToView', 'removeAnimatedEventFromView', 'addListener', 'removeListener'];
  var nativeOperations = {};
  if (isSingleOpBatching) {
    var _loop = function _loop() {
      var methodName = methodNames[ii];
      var operationID = ii + 1;
      nativeOperations[methodName] = function () {
        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
          args[_key] = arguments[_key];
        }
        singleOpQueue.push.apply(singleOpQueue, [operationID].concat(args));
      };
    };
    for (var ii = 0, length = methodNames.length; ii < length; ii++) {
      _loop();
    }
  } else {
    var _loop2 = function _loop2() {
      var methodName = methodNames[_ii];
      nativeOperations[methodName] = function () {
        for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
          args[_key2] = arguments[_key2];
        }
        var method = (0, _nullthrows.default)(NativeAnimatedModule)[methodName];
        if (queueOperations || queue.length !== 0) {
          queue.push(function () {
            return method.apply(void 0, args);
          });
        } else {
          method.apply(void 0, args);
        }
      };
    };
    for (var _ii = 0, _length = methodNames.length; _ii < _length; _ii++) {
      _loop2();
    }
  }
  return nativeOperations;
}
var NativeOperations = createNativeOperations();
var API = {
  getValue: isSingleOpBatching ? function (tag, saveValueCallback) {
    if (saveValueCallback) {
      eventListenerGetValueCallbacks[tag] = saveValueCallback;
    }
    NativeOperations.getValue(tag);
  } : function (tag, saveValueCallback) {
    NativeOperations.getValue(tag, saveValueCallback);
  },
  setWaitingForIdentifier: function setWaitingForIdentifier(id) {
    waitingForQueuedOperations.add(id);
    queueOperations = true;
    if (ReactNativeFeatureFlags.animatedShouldDebounceQueueFlush() && flushQueueImmediate) {
      if (ReactNativeFeatureFlags.enableAnimatedClearImmediateFix()) {
        clearImmediate(flushQueueImmediate);
      } else {
        clearTimeout(flushQueueImmediate);
      }
    }
  },
  unsetWaitingForIdentifier: function unsetWaitingForIdentifier(id) {
    waitingForQueuedOperations.delete(id);
    if (waitingForQueuedOperations.size === 0) {
      queueOperations = false;
      API.disableQueue();
    }
  },
  disableQueue: function disableQueue() {
    (0, _invariant.default)(NativeAnimatedModule, 'Native animated module is not available');
    if (ReactNativeFeatureFlags.animatedShouldDebounceQueueFlush()) {
      var prevImmediate = flushQueueImmediate;
      clearImmediate(prevImmediate);
      flushQueueImmediate = setImmediate(API.flushQueue);
    } else {
      API.flushQueue();
    }
  },
  flushQueue: isSingleOpBatching ? function () {
    (0, _invariant.default)(NativeAnimatedModule, 'Native animated module is not available');
    flushQueueImmediate = null;
    if (singleOpQueue.length === 0) {
      return;
    }
    ensureGlobalEventEmitterListeners();
    NativeAnimatedModule == null || NativeAnimatedModule.queueAndExecuteBatchedOperations == null || NativeAnimatedModule.queueAndExecuteBatchedOperations(singleOpQueue);
    singleOpQueue.length = 0;
  } : function () {
    (0, _invariant.default)(NativeAnimatedModule, 'Native animated module is not available');
    flushQueueImmediate = null;
    if (queue.length === 0) {
      return;
    }
    if (_Platform.default.OS === 'android') {
      NativeAnimatedModule == null || NativeAnimatedModule.startOperationBatch == null || NativeAnimatedModule.startOperationBatch();
    }
    for (var q = 0, l = queue.length; q < l; q++) {
      queue[q]();
    }
    queue.length = 0;
    if (_Platform.default.OS === 'android') {
      NativeAnimatedModule == null || NativeAnimatedModule.finishOperationBatch == null || NativeAnimatedModule.finishOperationBatch();
    }
  },
  createAnimatedNode: function createAnimatedNode(tag, config) {
    NativeOperations.createAnimatedNode(tag, config);
  },
  updateAnimatedNodeConfig: function updateAnimatedNodeConfig(tag, config) {
    NativeOperations.updateAnimatedNodeConfig == null || NativeOperations.updateAnimatedNodeConfig(tag, config);
  },
  startListeningToAnimatedNodeValue: function startListeningToAnimatedNodeValue(tag) {
    NativeOperations.startListeningToAnimatedNodeValue(tag);
  },
  stopListeningToAnimatedNodeValue: function stopListeningToAnimatedNodeValue(tag) {
    NativeOperations.stopListeningToAnimatedNodeValue(tag);
  },
  connectAnimatedNodes: function connectAnimatedNodes(parentTag, childTag) {
    NativeOperations.connectAnimatedNodes(parentTag, childTag);
  },
  disconnectAnimatedNodes: function disconnectAnimatedNodes(parentTag, childTag) {
    NativeOperations.disconnectAnimatedNodes(parentTag, childTag);
  },
  startAnimatingNode: isSingleOpBatching ? function (animationId, nodeTag, config, endCallback) {
    if (endCallback) {
      eventListenerAnimationFinishedCallbacks[animationId] = endCallback;
    }
    NativeOperations.startAnimatingNode(animationId, nodeTag, config);
  } : function (animationId, nodeTag, config, endCallback) {
    NativeOperations.startAnimatingNode(animationId, nodeTag, config, endCallback);
  },
  stopAnimation: function stopAnimation(animationId) {
    NativeOperations.stopAnimation(animationId);
  },
  setAnimatedNodeValue: function setAnimatedNodeValue(nodeTag, value) {
    NativeOperations.setAnimatedNodeValue(nodeTag, value);
  },
  setAnimatedNodeOffset: function setAnimatedNodeOffset(nodeTag, offset) {
    NativeOperations.setAnimatedNodeOffset(nodeTag, offset);
  },
  flattenAnimatedNodeOffset: function flattenAnimatedNodeOffset(nodeTag) {
    NativeOperations.flattenAnimatedNodeOffset(nodeTag);
  },
  extractAnimatedNodeOffset: function extractAnimatedNodeOffset(nodeTag) {
    NativeOperations.extractAnimatedNodeOffset(nodeTag);
  },
  connectAnimatedNodeToView: function connectAnimatedNodeToView(nodeTag, viewTag) {
    NativeOperations.connectAnimatedNodeToView(nodeTag, viewTag);
  },
  disconnectAnimatedNodeFromView: function disconnectAnimatedNodeFromView(nodeTag, viewTag) {
    NativeOperations.disconnectAnimatedNodeFromView(nodeTag, viewTag);
  },
  restoreDefaultValues: function restoreDefaultValues(nodeTag) {
    NativeOperations.restoreDefaultValues == null || NativeOperations.restoreDefaultValues(nodeTag);
  },
  dropAnimatedNode: function dropAnimatedNode(tag) {
    NativeOperations.dropAnimatedNode(tag);
  },
  addAnimatedEventToView: function addAnimatedEventToView(viewTag, eventName, eventMapping) {
    NativeOperations.addAnimatedEventToView(viewTag, eventName, eventMapping);
  },
  removeAnimatedEventFromView: function removeAnimatedEventFromView(viewTag, eventName, animatedNodeTag) {
    NativeOperations.removeAnimatedEventFromView(viewTag, eventName, animatedNodeTag);
  }
};
function ensureGlobalEventEmitterListeners() {
  if (globalEventEmitterGetValueListener && globalEventEmitterAnimationFinishedListener) {
    return;
  }
  globalEventEmitterGetValueListener = _RCTDeviceEventEmitter.default.addListener('onNativeAnimatedModuleGetValue', function (params) {
    var tag = params.tag;
    var callback = eventListenerGetValueCallbacks[tag];
    if (!callback) {
      return;
    }
    callback(params.value);
    delete eventListenerGetValueCallbacks[tag];
  });
  globalEventEmitterAnimationFinishedListener = _RCTDeviceEventEmitter.default.addListener('onNativeAnimatedModuleAnimationFinished', function (params) {
    var animations = Array.isArray(params) ? params : [params];
    for (var animation of animations) {
      var animationId = animation.animationId;
      var callback = eventListenerAnimationFinishedCallbacks[animationId];
      if (callback) {
        callback(animation);
        delete eventListenerAnimationFinishedCallbacks[animationId];
      }
    }
  });
}
function generateNewNodeTag() {
  return __nativeAnimatedNodeTagCount++;
}
function generateNewAnimationId() {
  return __nativeAnimationIdCount++;
}
function assertNativeAnimatedModule() {
  (0, _invariant.default)(NativeAnimatedModule, 'Native animated module is not available');
}
var _warnedMissingNativeAnimated = false;
function shouldUseNativeDriver(config) {
  if (config.useNativeDriver == null) {
    console.warn('Animated: `useNativeDriver` was not specified. This is a required ' + 'option and must be explicitly set to `true` or `false`');
  }
  if (config.useNativeDriver === true && !NativeAnimatedModule) {
    if (process.env.NODE_ENV !== 'test') {
      if (!_warnedMissingNativeAnimated) {
        console.warn('Animated: `useNativeDriver` is not supported because the native ' + 'animated module is missing. Falling back to JS-based animation. To ' + 'resolve this, add `RCTAnimation` module to this app, or remove ' + '`useNativeDriver`. ' + 'Make sure to run `bundle exec pod install` first. Read more about autolinking: https://github.com/react-native-community/cli/blob/master/docs/autolinking.md');
        _warnedMissingNativeAnimated = true;
      }
    }
    return false;
  }
  return config.useNativeDriver || false;
}
function transformDataType(value) {
  if (typeof value !== 'string') {
    return value;
  }
  if (value.endsWith('deg')) {
    var degrees = parseFloat(value) || 0;
    return degrees * Math.PI / 180.0;
  } else if (value.endsWith('rad')) {
    return parseFloat(value) || 0;
  } else {
    return value;
  }
}
var _default = exports.default = {
  API: API,
  generateNewNodeTag: generateNewNodeTag,
  generateNewAnimationId: generateNewAnimationId,
  assertNativeAnimatedModule: assertNativeAnimatedModule,
  shouldUseNativeDriver: shouldUseNativeDriver,
  transformDataType: transformDataType,
  get nativeEventEmitter() {
    if (!nativeEventEmitter) {
      nativeEventEmitter = new _NativeEventEmitter.default(_Platform.default.OS !== 'ios' ? null : NativeAnimatedModule);
    }
    return nativeEventEmitter;
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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