{"version": 3, "names": ["NativeComponentRegistry", "_interopRequireWildcard", "require", "_codegenNativeCommands", "_interopRequireDefault", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "Commands", "exports", "codegenNativeCommands", "supportedCommands", "__INTERNAL_VIEW_CONFIG", "uiViewClassName", "bubblingEventTypes", "topBlur", "phasedRegistrationNames", "bubbled", "captured", "topEndEditing", "topFocus", "topKeyPress", "topSubmitEditing", "directEventTypes", "topScroll", "registrationName", "validAttributes", "maxFontSizeMultiplier", "adjustsFontSizeToFit", "minimumFontScale", "autoFocus", "placeholder", "inlineImagePadding", "contextMenuHidden", "textShadowColor", "process", "max<PERSON><PERSON><PERSON>", "selectTextOnFocus", "textShadowRadius", "underlineColorAndroid", "textDecorationLine", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "textAlignVertical", "fontStyle", "textShadowOffset", "selectionColor", "selectionHandleColor", "placeholderTextColor", "importantForAutofill", "lineHeight", "textTransform", "returnKeyType", "keyboardType", "multiline", "color", "autoComplete", "numberOfLines", "letterSpacing", "returnKeyLabel", "fontSize", "onKeyPress", "cursorColor", "text", "showSoftInputOnFocus", "textAlign", "autoCapitalize", "autoCorrect", "caretHidden", "secureTextEntry", "textBreakStrategy", "onScroll", "onContentSizeChange", "disableFullscreenUI", "includeFontPadding", "fontWeight", "fontFamily", "allowFontScaling", "onSelectionChange", "mostRecentEventCount", "inlineImageLeft", "editable", "fontVariant", "borderBottomRightRadius", "borderBottomColor", "borderRadius", "borderRightColor", "borderColor", "borderTopRightRadius", "borderStyle", "borderBottomLeftRadius", "borderLeftColor", "borderTopLeftRadius", "borderTopColor", "AndroidTextInputNativeComponent", "_default"], "sources": ["AndroidTextInputNativeComponent.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {HostComponent} from '../../../src/private/types/HostComponent';\nimport type {PartialViewConfig} from '../../Renderer/shims/ReactNativeTypes';\nimport type {\n  ColorValue,\n  TextStyleProp,\n  ViewStyleProp,\n} from '../../StyleSheet/StyleSheet';\nimport type {\n  Bubbling<PERSON>ventHandler,\n  DirectEventHandler,\n  Double,\n  Float,\n  Int32,\n  WithDefault,\n} from '../../Types/CodegenTypes';\nimport type {ViewProps} from '../View/ViewPropTypes';\nimport type {TextInputNativeCommands} from './TextInputNativeCommands';\n\nimport * as NativeComponentRegistry from '../../NativeComponent/NativeComponentRegistry';\nimport codegenNativeCommands from '../../Utilities/codegenNativeCommands';\n\nexport type KeyboardType =\n  // Cross Platform\n  | 'default'\n  | 'email-address'\n  | 'numeric'\n  | 'phone-pad'\n  | 'number-pad'\n  | 'decimal-pad'\n  | 'url'\n  // iOS-only\n  | 'ascii-capable'\n  | 'numbers-and-punctuation'\n  | 'name-phone-pad'\n  | 'twitter'\n  | 'web-search'\n  // Android-only\n  | 'visible-password';\n\nexport type ReturnKeyType =\n  // Cross Platform\n  | 'done'\n  | 'go'\n  | 'next'\n  | 'search'\n  | 'send'\n  // Android-only\n  | 'none'\n  | 'previous'\n  // iOS-only\n  | 'default'\n  | 'emergency-call'\n  | 'google'\n  | 'join'\n  | 'route'\n  | 'yahoo';\n\nexport type SubmitBehavior = 'submit' | 'blurAndSubmit' | 'newline';\n\nexport type NativeProps = $ReadOnly<{\n  // This allows us to inherit everything from ViewProps except for style (see below)\n  // This must be commented for Fabric codegen to work.\n  ...$Diff<ViewProps, $ReadOnly<{style: ?ViewStyleProp}>>,\n\n  /**\n   * Android props after this\n   */\n  /**\n   * Specifies autocomplete hints for the system, so it can provide autofill. On Android, the system will always attempt to offer autofill by using heuristics to identify the type of content.\n   * To disable autocomplete, set `autoComplete` to `off`.\n   *\n   * *Android Only*\n   *\n   * Possible values for `autoComplete` are:\n   *\n   * - `birthdate-day`\n   * - `birthdate-full`\n   * - `birthdate-month`\n   * - `birthdate-year`\n   * - `cc-csc`\n   * - `cc-exp`\n   * - `cc-exp-day`\n   * - `cc-exp-month`\n   * - `cc-exp-year`\n   * - `cc-number`\n   * - `email`\n   * - `gender`\n   * - `name`\n   * - `name-family`\n   * - `name-given`\n   * - `name-middle`\n   * - `name-middle-initial`\n   * - `name-prefix`\n   * - `name-suffix`\n   * - `password`\n   * - `password-new`\n   * - `postal-address`\n   * - `postal-address-country`\n   * - `postal-address-extended`\n   * - `postal-address-extended-postal-code`\n   * - `postal-address-locality`\n   * - `postal-address-region`\n   * - `postal-code`\n   * - `street-address`\n   * - `sms-otp`\n   * - `tel`\n   * - `tel-country-code`\n   * - `tel-national`\n   * - `tel-device`\n   * - `username`\n   * - `username-new`\n   * - `off`\n   *\n   * @platform android\n   */\n  autoComplete?: WithDefault<\n    | 'birthdate-day'\n    | 'birthdate-full'\n    | 'birthdate-month'\n    | 'birthdate-year'\n    | 'cc-csc'\n    | 'cc-exp'\n    | 'cc-exp-day'\n    | 'cc-exp-month'\n    | 'cc-exp-year'\n    | 'cc-number'\n    | 'email'\n    | 'gender'\n    | 'name'\n    | 'name-family'\n    | 'name-given'\n    | 'name-middle'\n    | 'name-middle-initial'\n    | 'name-prefix'\n    | 'name-suffix'\n    | 'password'\n    | 'password-new'\n    | 'postal-address'\n    | 'postal-address-country'\n    | 'postal-address-extended'\n    | 'postal-address-extended-postal-code'\n    | 'postal-address-locality'\n    | 'postal-address-region'\n    | 'postal-code'\n    | 'street-address'\n    | 'sms-otp'\n    | 'tel'\n    | 'tel-country-code'\n    | 'tel-national'\n    | 'tel-device'\n    | 'username'\n    | 'username-new'\n    | 'off',\n    'off',\n  >,\n\n  /**\n   * Sets the return key to the label. Use it instead of `returnKeyType`.\n   * @platform android\n   */\n  returnKeyLabel?: ?string,\n\n  /**\n   * Sets the number of lines for a `TextInput`. Use it with multiline set to\n   * `true` to be able to fill the lines.\n   * @platform android\n   */\n  numberOfLines?: ?Int32,\n\n  /**\n   * When `false`, if there is a small amount of space available around a text input\n   * (e.g. landscape orientation on a phone), the OS may choose to have the user edit\n   * the text inside of a full screen text input mode. When `true`, this feature is\n   * disabled and users will always edit the text directly inside of the text input.\n   * Defaults to `false`.\n   * @platform android\n   */\n  disableFullscreenUI?: ?boolean,\n\n  /**\n   * Set text break strategy on Android API Level 23+, possible values are `simple`, `highQuality`, `balanced`\n   * The default value is `simple`.\n   * @platform android\n   */\n  textBreakStrategy?: WithDefault<\n    'simple' | 'highQuality' | 'balanced',\n    'simple',\n  >,\n\n  /**\n   * The color of the `TextInput` underline.\n   * @platform android\n   */\n  underlineColorAndroid?: ?ColorValue,\n\n  /**\n   * If defined, the provided image resource will be rendered on the left.\n   * The image resource must be inside `/android/app/src/main/res/drawable` and referenced\n   * like\n   * ```\n   * <TextInput\n   *  inlineImageLeft='search_icon'\n   * />\n   * ```\n   * @platform android\n   */\n  inlineImageLeft?: ?string,\n\n  /**\n   * Padding between the inline image, if any, and the text input itself.\n   * @platform android\n   */\n  inlineImagePadding?: ?Int32,\n\n  importantForAutofill?: string /*?(\n    | 'auto'\n    | 'no'\n    | 'noExcludeDescendants'\n    | 'yes'\n    | 'yesExcludeDescendants'\n  ),*/,\n\n  /**\n   * When `false`, it will prevent the soft keyboard from showing when the field is focused.\n   * Defaults to `true`.\n   */\n  showSoftInputOnFocus?: ?boolean,\n\n  /**\n   * TextInput props after this\n   */\n  /**\n   * Can tell `TextInput` to automatically capitalize certain characters.\n   *\n   * - `characters`: all characters.\n   * - `words`: first letter of each word.\n   * - `sentences`: first letter of each sentence (*default*).\n   * - `none`: don't auto capitalize anything.\n   */\n  autoCapitalize?: WithDefault<\n    'none' | 'sentences' | 'words' | 'characters',\n    'none',\n  >,\n\n  /**\n   * If `false`, disables auto-correct. The default value is `true`.\n   */\n  autoCorrect?: ?boolean,\n\n  /**\n   * If `true`, focuses the input on `componentDidMount`.\n   * The default value is `false`.\n   */\n  autoFocus?: ?boolean,\n\n  /**\n   * Specifies whether fonts should scale to respect Text Size accessibility settings. The\n   * default is `true`.\n   */\n  allowFontScaling?: ?boolean,\n\n  /**\n   * Specifies largest possible scale a font can reach when `allowFontScaling` is enabled.\n   * Possible values:\n   * `null/undefined` (default): inherit from the parent node or the global default (0)\n   * `0`: no max, ignore parent/global default\n   * `>= 1`: sets the maxFontSizeMultiplier of this node to this value\n   */\n  maxFontSizeMultiplier?: ?Float,\n\n  /**\n   * If `false`, text is not editable. The default value is `true`.\n   */\n  editable?: ?boolean,\n\n  /**\n   * Determines which keyboard to open, e.g.`numeric`.\n   *\n   * The following values work across platforms:\n   *\n   * - `default`\n   * - `numeric`\n   * - `number-pad`\n   * - `decimal-pad`\n   * - `email-address`\n   * - `phone-pad`\n   * - `url`\n   *\n   * *Android Only*\n   *\n   * The following values work on Android only:\n   *\n   * - `visible-password`\n   */\n  keyboardType?: WithDefault<KeyboardType, 'default'>,\n\n  /**\n   * Determines how the return key should look. On Android you can also use\n   * `returnKeyLabel`.\n   *\n   * *Cross platform*\n   *\n   * The following values work across platforms:\n   *\n   * - `done`\n   * - `go`\n   * - `next`\n   * - `search`\n   * - `send`\n   *\n   * *Android Only*\n   *\n   * The following values work on Android only:\n   *\n   * - `none`\n   * - `previous`\n   */\n  returnKeyType?: WithDefault<ReturnKeyType, 'done'>,\n\n  /**\n   * Limits the maximum number of characters that can be entered. Use this\n   * instead of implementing the logic in JS to avoid flicker.\n   */\n  maxLength?: ?Int32,\n\n  /**\n   * If `true`, the text input can be multiple lines.\n   * The default value is `false`.\n   */\n  multiline?: ?boolean,\n\n  /**\n   * Callback that is called when the text input is blurred.\n   * `target` is the reactTag of the element\n   */\n  onBlur?: ?BubblingEventHandler<$ReadOnly<{target: Int32}>>,\n\n  /**\n   * Callback that is called when the text input is focused.\n   * `target` is the reactTag of the element\n   */\n  onFocus?: ?BubblingEventHandler<$ReadOnly<{target: Int32}>>,\n\n  /**\n   * Callback that is called when the text input's text changes.\n   * `target` is the reactTag of the element\n   * TODO: differentiate between onChange and onChangeText\n   */\n  onChange?: ?BubblingEventHandler<\n    $ReadOnly<{target: Int32, eventCount: Int32, text: string}>,\n  >,\n\n  /**\n   * Callback that is called when the text input's text changes.\n   * Changed text is passed as an argument to the callback handler.\n   * TODO: differentiate between onChange and onChangeText\n   */\n  onChangeText?: ?BubblingEventHandler<\n    $ReadOnly<{target: Int32, eventCount: Int32, text: string}>,\n  >,\n\n  /**\n   * Callback that is called when the text input's content size changes.\n   * This will be called with\n   * `{ nativeEvent: { contentSize: { width, height } } }`.\n   *\n   * Only called for multiline text inputs.\n   */\n  onContentSizeChange?: ?DirectEventHandler<\n    $ReadOnly<{\n      target: Int32,\n      contentSize: $ReadOnly<{width: Double, height: Double}>,\n    }>,\n  >,\n\n  /**\n   * Callback that is called when text input ends.\n   */\n  onEndEditing?: ?BubblingEventHandler<\n    $ReadOnly<{target: Int32, text: string}>,\n  >,\n\n  /**\n   * Callback that is called when the text input selection is changed.\n   * This will be called with\n   * `{ nativeEvent: { selection: { start, end } } }`.\n   */\n  onSelectionChange?: ?DirectEventHandler<\n    $ReadOnly<{\n      target: Int32,\n      selection: $ReadOnly<{start: Double, end: Double}>,\n    }>,\n  >,\n\n  /**\n   * Callback that is called when the text input's submit button is pressed.\n   * Invalid if `multiline={true}` is specified.\n   */\n  onSubmitEditing?: ?BubblingEventHandler<\n    $ReadOnly<{target: Int32, text: string}>,\n  >,\n\n  /**\n   * Callback that is called when a key is pressed.\n   * This will be called with `{ nativeEvent: { key: keyValue } }`\n   * where `keyValue` is `'Enter'` or `'Backspace'` for respective keys and\n   * the typed-in character otherwise including `' '` for space.\n   * Fires before `onChange` callbacks.\n   */\n  onKeyPress?: ?BubblingEventHandler<$ReadOnly<{target: Int32, key: string}>>,\n\n  /**\n   * Invoked on content scroll with `{ nativeEvent: { contentOffset: { x, y } } }`.\n   * May also contain other properties from ScrollEvent but on Android contentSize\n   * is not provided for performance reasons.\n   */\n  onScroll?: ?DirectEventHandler<\n    $ReadOnly<{\n      target: Int32,\n      responderIgnoreScroll: boolean,\n      contentInset: $ReadOnly<{\n        top: Double, // always 0 on Android\n        bottom: Double, // always 0 on Android\n        left: Double, // always 0 on Android\n        right: Double, // always 0 on Android\n      }>,\n      contentOffset: $ReadOnly<{\n        x: Double,\n        y: Double,\n      }>,\n      contentSize: $ReadOnly<{\n        width: Double, // always 0 on Android\n        height: Double, // always 0 on Android\n      }>,\n      layoutMeasurement: $ReadOnly<{\n        width: Double,\n        height: Double,\n      }>,\n      velocity: $ReadOnly<{\n        x: Double, // always 0 on Android\n        y: Double, // always 0 on Android\n      }>,\n    }>,\n  >,\n\n  /**\n   * The string that will be rendered before text input has been entered.\n   */\n  placeholder?: ?Stringish,\n\n  /**\n   * The text color of the placeholder string.\n   */\n  placeholderTextColor?: ?ColorValue,\n\n  /**\n   * If `true`, the text input obscures the text entered so that sensitive text\n   * like passwords stay secure. The default value is `false`. Does not work with 'multiline={true}'.\n   */\n  secureTextEntry?: ?boolean,\n\n  /**\n   * The highlight and cursor color of the text input.\n   */\n  selectionColor?: ?ColorValue,\n\n  /**\n   * The text selection handle color.\n   */\n  selectionHandleColor?: ?ColorValue,\n\n  /**\n   * The start and end of the text input's selection. Set start and end to\n   * the same value to position the cursor.\n   */\n  selection?: ?$ReadOnly<{\n    start: Int32,\n    end?: ?Int32,\n  }>,\n\n  /**\n   * The value to show for the text input. `TextInput` is a controlled\n   * component, which means the native value will be forced to match this\n   * value prop if provided. For most uses, this works great, but in some\n   * cases this may cause flickering - one common cause is preventing edits\n   * by keeping value the same. In addition to simply setting the same value,\n   * either set `editable={false}`, or set/update `maxLength` to prevent\n   * unwanted edits without flicker.\n   */\n  value?: ?string,\n\n  /**\n   * Provides an initial value that will change when the user starts typing.\n   * Useful for simple use-cases where you do not want to deal with listening\n   * to events and updating the value prop to keep the controlled state in sync.\n   */\n  defaultValue?: ?string,\n\n  /**\n   * If `true`, all text will automatically be selected on focus.\n   */\n  selectTextOnFocus?: ?boolean,\n\n  /**\n   * If `true`, the text field will blur when submitted.\n   * The default value is true for single-line fields and false for\n   * multiline fields. Note that for multiline fields, setting `blurOnSubmit`\n   * to `true` means that pressing return will blur the field and trigger the\n   * `onSubmitEditing` event instead of inserting a newline into the field.\n   *\n   * @deprecated\n   * Note that `submitBehavior` now takes the place of `blurOnSubmit` and will\n   * override any behavior defined by `blurOnSubmit`.\n   * @see submitBehavior\n   */\n  blurOnSubmit?: ?boolean,\n\n  /**\n   * When the return key is pressed,\n   *\n   * For single line inputs:\n   *\n   * - `'newline`' defaults to `'blurAndSubmit'`\n   * - `undefined` defaults to `'blurAndSubmit'`\n   *\n   * For multiline inputs:\n   *\n   * - `'newline'` adds a newline\n   * - `undefined` defaults to `'newline'`\n   *\n   * For both single line and multiline inputs:\n   *\n   * - `'submit'` will only send a submit event and not blur the input\n   * - `'blurAndSubmit`' will both blur the input and send a submit event\n   */\n  submitBehavior?: ?SubmitBehavior,\n\n  /**\n   * Note that not all Text styles are supported, an incomplete list of what is not supported includes:\n   *\n   * - `borderLeftWidth`\n   * - `borderTopWidth`\n   * - `borderRightWidth`\n   * - `borderBottomWidth`\n   * - `borderTopLeftRadius`\n   * - `borderTopRightRadius`\n   * - `borderBottomRightRadius`\n   * - `borderBottomLeftRadius`\n   *\n   * see [Issue#7070](https://github.com/facebook/react-native/issues/7070)\n   * for more detail.\n   *\n   * [Styles](docs/style.html)\n   */\n  // TODO: figure out what to do with this style prop for codegen/Fabric purposes\n  // This must be commented for Fabric codegen to work; it's currently not possible\n  // to override the default View style prop in codegen.\n  style?: ?TextStyleProp,\n\n  /**\n   * If `true`, caret is hidden. The default value is `false`.\n   * This property is supported only for single-line TextInput component on iOS.\n   */\n  caretHidden?: ?boolean,\n\n  /*\n   * If `true`, contextMenuHidden is hidden. The default value is `false`.\n   */\n  contextMenuHidden?: ?boolean,\n\n  /**\n   * The following are props that `BaseTextShadowNode` takes. It is unclear if they\n   * are used by TextInput.\n   */\n  textShadowColor?: ?ColorValue,\n  textShadowRadius?: ?Float,\n  textDecorationLine?: ?string,\n  fontStyle?: ?string,\n  textShadowOffset?: ?$ReadOnly<{width?: ?Double, height?: ?Double}>,\n  lineHeight?: ?Float,\n  textTransform?: ?string,\n  color?: ?Int32,\n  letterSpacing?: ?Float,\n  fontSize?: ?Float,\n  textAlign?: ?string,\n  includeFontPadding?: ?boolean,\n  fontWeight?: ?string,\n  fontFamily?: ?string,\n\n  /**\n   * I cannot find where these are defined but JS complains without them.\n   */\n  textAlignVertical?: ?string,\n  cursorColor?: ?ColorValue,\n\n  /**\n   * \"Private\" fields used by TextInput.js and not users of this component directly\n   */\n  mostRecentEventCount: Int32,\n  text?: ?string,\n}>;\n\ntype NativeType = HostComponent<NativeProps>;\n\ntype NativeCommands = TextInputNativeCommands<NativeType>;\n\nexport const Commands: NativeCommands = codegenNativeCommands<NativeCommands>({\n  supportedCommands: ['focus', 'blur', 'setTextAndSelection'],\n});\n\nexport const __INTERNAL_VIEW_CONFIG: PartialViewConfig = {\n  uiViewClassName: 'AndroidTextInput',\n  bubblingEventTypes: {\n    topBlur: {\n      phasedRegistrationNames: {\n        bubbled: 'onBlur',\n        captured: 'onBlurCapture',\n      },\n    },\n    topEndEditing: {\n      phasedRegistrationNames: {\n        bubbled: 'onEndEditing',\n        captured: 'onEndEditingCapture',\n      },\n    },\n    topFocus: {\n      phasedRegistrationNames: {\n        bubbled: 'onFocus',\n        captured: 'onFocusCapture',\n      },\n    },\n    topKeyPress: {\n      phasedRegistrationNames: {\n        bubbled: 'onKeyPress',\n        captured: 'onKeyPressCapture',\n      },\n    },\n    topSubmitEditing: {\n      phasedRegistrationNames: {\n        bubbled: 'onSubmitEditing',\n        captured: 'onSubmitEditingCapture',\n      },\n    },\n  },\n  directEventTypes: {\n    topScroll: {\n      registrationName: 'onScroll',\n    },\n  },\n  validAttributes: {\n    maxFontSizeMultiplier: true,\n    adjustsFontSizeToFit: true,\n    minimumFontScale: true,\n    autoFocus: true,\n    placeholder: true,\n    inlineImagePadding: true,\n    contextMenuHidden: true,\n    textShadowColor: {\n      process: require('../../StyleSheet/processColor').default,\n    },\n    maxLength: true,\n    selectTextOnFocus: true,\n    textShadowRadius: true,\n    underlineColorAndroid: {\n      process: require('../../StyleSheet/processColor').default,\n    },\n    textDecorationLine: true,\n    submitBehavior: true,\n    textAlignVertical: true,\n    fontStyle: true,\n    textShadowOffset: true,\n    selectionColor: {process: require('../../StyleSheet/processColor').default},\n    selectionHandleColor: {\n      process: require('../../StyleSheet/processColor').default,\n    },\n    placeholderTextColor: {\n      process: require('../../StyleSheet/processColor').default,\n    },\n    importantForAutofill: true,\n    lineHeight: true,\n    textTransform: true,\n    returnKeyType: true,\n    keyboardType: true,\n    multiline: true,\n    color: {process: require('../../StyleSheet/processColor').default},\n    autoComplete: true,\n    numberOfLines: true,\n    letterSpacing: true,\n    returnKeyLabel: true,\n    fontSize: true,\n    onKeyPress: true,\n    cursorColor: {process: require('../../StyleSheet/processColor').default},\n    text: true,\n    showSoftInputOnFocus: true,\n    textAlign: true,\n    autoCapitalize: true,\n    autoCorrect: true,\n    caretHidden: true,\n    secureTextEntry: true,\n    textBreakStrategy: true,\n    onScroll: true,\n    onContentSizeChange: true,\n    disableFullscreenUI: true,\n    includeFontPadding: true,\n    fontWeight: true,\n    fontFamily: true,\n    allowFontScaling: true,\n    onSelectionChange: true,\n    mostRecentEventCount: true,\n    inlineImageLeft: true,\n    editable: true,\n    fontVariant: true,\n    borderBottomRightRadius: true,\n    borderBottomColor: {\n      process: require('../../StyleSheet/processColor').default,\n    },\n    borderRadius: true,\n    borderRightColor: {\n      process: require('../../StyleSheet/processColor').default,\n    },\n    borderColor: {process: require('../../StyleSheet/processColor').default},\n    borderTopRightRadius: true,\n    borderStyle: true,\n    borderBottomLeftRadius: true,\n    borderLeftColor: {\n      process: require('../../StyleSheet/processColor').default,\n    },\n    borderTopLeftRadius: true,\n    borderTopColor: {process: require('../../StyleSheet/processColor').default},\n  },\n};\n\nlet AndroidTextInputNativeComponent = NativeComponentRegistry.get<NativeProps>(\n  'AndroidTextInput',\n  () => __INTERNAL_VIEW_CONFIG,\n);\n\n// flowlint-next-line unclear-type:off\nexport default ((AndroidTextInputNativeComponent: any): HostComponent<NativeProps>);\n"], "mappings": ";;;;;AA4BA,IAAAA,uBAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,sBAAA,GAAAC,sBAAA,CAAAF,OAAA;AAA0E,SAAAD,wBAAAI,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAN,uBAAA,YAAAA,wBAAAI,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AA2kBnE,IAAMmB,QAAwB,GAAAC,OAAA,CAAAD,QAAA,GAAG,IAAAE,8BAAqB,EAAiB;EAC5EC,iBAAiB,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,qBAAqB;AAC5D,CAAC,CAAC;AAEK,IAAMC,sBAAyC,GAAAH,OAAA,CAAAG,sBAAA,GAAG;EACvDC,eAAe,EAAE,kBAAkB;EACnCC,kBAAkB,EAAE;IAClBC,OAAO,EAAE;MACPC,uBAAuB,EAAE;QACvBC,OAAO,EAAE,QAAQ;QACjBC,QAAQ,EAAE;MACZ;IACF,CAAC;IACDC,aAAa,EAAE;MACbH,uBAAuB,EAAE;QACvBC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE;MACZ;IACF,CAAC;IACDE,QAAQ,EAAE;MACRJ,uBAAuB,EAAE;QACvBC,OAAO,EAAE,SAAS;QAClBC,QAAQ,EAAE;MACZ;IACF,CAAC;IACDG,WAAW,EAAE;MACXL,uBAAuB,EAAE;QACvBC,OAAO,EAAE,YAAY;QACrBC,QAAQ,EAAE;MACZ;IACF,CAAC;IACDI,gBAAgB,EAAE;MAChBN,uBAAuB,EAAE;QACvBC,OAAO,EAAE,iBAAiB;QAC1BC,QAAQ,EAAE;MACZ;IACF;EACF,CAAC;EACDK,gBAAgB,EAAE;IAChBC,SAAS,EAAE;MACTC,gBAAgB,EAAE;IACpB;EACF,CAAC;EACDC,eAAe,EAAE;IACfC,qBAAqB,EAAE,IAAI;IAC3BC,oBAAoB,EAAE,IAAI;IAC1BC,gBAAgB,EAAE,IAAI;IACtBC,SAAS,EAAE,IAAI;IACfC,WAAW,EAAE,IAAI;IACjBC,kBAAkB,EAAE,IAAI;IACxBC,iBAAiB,EAAE,IAAI;IACvBC,eAAe,EAAE;MACfC,OAAO,EAAElD,OAAO,gCAAgC,CAAC,CAACa;IACpD,CAAC;IACDsC,SAAS,EAAE,IAAI;IACfC,iBAAiB,EAAE,IAAI;IACvBC,gBAAgB,EAAE,IAAI;IACtBC,qBAAqB,EAAE;MACrBJ,OAAO,EAAElD,OAAO,gCAAgC,CAAC,CAACa;IACpD,CAAC;IACD0C,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE,IAAI;IACpBC,iBAAiB,EAAE,IAAI;IACvBC,SAAS,EAAE,IAAI;IACfC,gBAAgB,EAAE,IAAI;IACtBC,cAAc,EAAE;MAACV,OAAO,EAAElD,OAAO,gCAAgC,CAAC,CAACa;IAAO,CAAC;IAC3EgD,oBAAoB,EAAE;MACpBX,OAAO,EAAElD,OAAO,gCAAgC,CAAC,CAACa;IACpD,CAAC;IACDiD,oBAAoB,EAAE;MACpBZ,OAAO,EAAElD,OAAO,gCAAgC,CAAC,CAACa;IACpD,CAAC;IACDkD,oBAAoB,EAAE,IAAI;IAC1BC,UAAU,EAAE,IAAI;IAChBC,aAAa,EAAE,IAAI;IACnBC,aAAa,EAAE,IAAI;IACnBC,YAAY,EAAE,IAAI;IAClBC,SAAS,EAAE,IAAI;IACfC,KAAK,EAAE;MAACnB,OAAO,EAAElD,OAAO,gCAAgC,CAAC,CAACa;IAAO,CAAC;IAClEyD,YAAY,EAAE,IAAI;IAClBC,aAAa,EAAE,IAAI;IACnBC,aAAa,EAAE,IAAI;IACnBC,cAAc,EAAE,IAAI;IACpBC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,IAAI;IAChBC,WAAW,EAAE;MAAC1B,OAAO,EAAElD,OAAO,gCAAgC,CAAC,CAACa;IAAO,CAAC;IACxEgE,IAAI,EAAE,IAAI;IACVC,oBAAoB,EAAE,IAAI;IAC1BC,SAAS,EAAE,IAAI;IACfC,cAAc,EAAE,IAAI;IACpBC,WAAW,EAAE,IAAI;IACjBC,WAAW,EAAE,IAAI;IACjBC,eAAe,EAAE,IAAI;IACrBC,iBAAiB,EAAE,IAAI;IACvBC,QAAQ,EAAE,IAAI;IACdC,mBAAmB,EAAE,IAAI;IACzBC,mBAAmB,EAAE,IAAI;IACzBC,kBAAkB,EAAE,IAAI;IACxBC,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,IAAI;IAChBC,gBAAgB,EAAE,IAAI;IACtBC,iBAAiB,EAAE,IAAI;IACvBC,oBAAoB,EAAE,IAAI;IAC1BC,eAAe,EAAE,IAAI;IACrBC,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAE,IAAI;IACjBC,uBAAuB,EAAE,IAAI;IAC7BC,iBAAiB,EAAE;MACjBhD,OAAO,EAAElD,OAAO,gCAAgC,CAAC,CAACa;IACpD,CAAC;IACDsF,YAAY,EAAE,IAAI;IAClBC,gBAAgB,EAAE;MAChBlD,OAAO,EAAElD,OAAO,gCAAgC,CAAC,CAACa;IACpD,CAAC;IACDwF,WAAW,EAAE;MAACnD,OAAO,EAAElD,OAAO,gCAAgC,CAAC,CAACa;IAAO,CAAC;IACxEyF,oBAAoB,EAAE,IAAI;IAC1BC,WAAW,EAAE,IAAI;IACjBC,sBAAsB,EAAE,IAAI;IAC5BC,eAAe,EAAE;MACfvD,OAAO,EAAElD,OAAO,gCAAgC,CAAC,CAACa;IACpD,CAAC;IACD6F,mBAAmB,EAAE,IAAI;IACzBC,cAAc,EAAE;MAACzD,OAAO,EAAElD,OAAO,gCAAgC,CAAC,CAACa;IAAO;EAC5E;AACF,CAAC;AAED,IAAI+F,+BAA+B,GAAG9G,uBAAuB,CAACiB,GAAG,CAC/D,kBAAkB,EAClB;EAAA,OAAMY,sBAAsB;AAAA,CAC9B,CAAC;AAAC,IAAAkF,QAAA,GAAArF,OAAA,CAAAX,OAAA,GAGe+F,+BAA+B", "ignoreList": []}