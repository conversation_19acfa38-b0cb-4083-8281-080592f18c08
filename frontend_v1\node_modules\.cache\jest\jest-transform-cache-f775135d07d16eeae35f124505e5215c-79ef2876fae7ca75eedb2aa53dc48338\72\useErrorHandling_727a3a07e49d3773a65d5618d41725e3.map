{"version": 3, "names": ["_react", "require", "_reactNative", "_performanceMonitor", "_errorHandlingUtils", "useErrorHandling", "exports", "options", "arguments", "length", "undefined", "_options$maxRetries", "maxRetries", "_options$retryDelay", "retry<PERSON><PERSON><PERSON>", "_options$progressiveR", "progressiveRetryDelay", "_options$autoRetryOnA", "autoRetryOnAppFocus", "_options$autoRetryOnC", "autoRetryOnConnectionRestored", "_options$reportErrors", "reportErrors", "_options$errorContext", "errorContext", "onError", "onRetry", "onMaxRetriesExceeded", "_useState", "useState", "error", "isError", "retryCount", "lastRetryTime", "canRetry", "_useState2", "_slicedToArray2", "default", "state", "setState", "appStateRef", "useRef", "AppState", "currentState", "retryTimeoutRef", "componentMountedRef", "useEffect", "current", "clearTimeout", "handleAppStateChange", "nextAppState", "match", "retry", "subscription", "addEventListener", "remove", "handleError", "useCallback", "errorInput", "Error", "appError", "AppError", "createAppError", "component", "action", "additionalData", "prev", "Date", "now", "logError", "performanceMonitor", "trackUserInteraction", "errorType", "name", "errorMessage", "message", "clearError", "_asyncToGenerator2", "_state$error", "_state$error2", "currentRetryDelay", "Math", "pow", "Object", "assign", "Promise", "resolve", "setTimeout", "getErrorMessage", "isNetworkError", "isServerError", "isAuthError", "toLowerCase", "includes", "_default"], "sources": ["useErrorHandling.ts"], "sourcesContent": ["/**\n * Enhanced Error Handling Hook - Comprehensive Error Management with Aura Design System\n *\n * Hook Contract:\n * - Provides standardized error handling across the app with Aura design integration\n * - Manages error state, retry logic, and graceful recovery mechanisms\n * - Integrates with performance monitoring and analytics tracking\n * - Supports offline fallback, error reporting, and user-friendly feedback\n * - Implements progressive error recovery with smart retry strategies\n * - Enhanced UX with haptic feedback, toast notifications, and accessibility support\n * - WCAG 2.1 AA compliant error messaging and recovery flows\n *\n * @version 3.0.0 - Enhanced with Aura Design System and Advanced Error Recovery\n * <AUTHOR> Development Team\n */\n\nimport { useState, useCallback, useEffect, useRef } from 'react';\nimport { AppState, AppStateStatus } from 'react-native';\n\nimport { performanceMonitor } from '../services/performanceMonitor';\nimport {\n  createAppError,\n  logError,\n  AppError,\n} from '../utils/errorHandlingUtils';\n\ninterface ErrorHandlingOptions {\n  // Error handling configuration\n  maxRetries?: number;\n  retryDelay?: number;\n  progressiveRetryDelay?: boolean;\n  autoRetryOnAppFocus?: boolean;\n  autoRetryOnConnectionRestored?: boolean;\n\n  // Error reporting and analytics\n  reportErrors?: boolean;\n  errorContext?: Record<string, any>;\n  trackErrorMetrics?: boolean;\n\n  // Enhanced user feedback\n  showToastOnError?: boolean;\n  showToastOnRetry?: boolean;\n  showToastOnRecovery?: boolean;\n  enableHapticFeedback?: boolean;\n  customErrorMessages?: Record<string, string>;\n\n  // Accessibility and UX\n  announceErrorsToScreenReader?: boolean;\n  errorSeverityLevel?: 'low' | 'medium' | 'high' | 'critical';\n  fallbackComponent?: React.ComponentType<any>;\n\n  // Callbacks\n  onError?: (error: Error | AppError) => void;\n  onRetry?: (retryCount: number) => void;\n  onMaxRetriesExceeded?: () => void;\n  onRecovery?: () => void;\n  onFallbackActivated?: () => void;\n}\n\ninterface ErrorHandlingState {\n  error: Error | AppError | null;\n  isError: boolean;\n  retryCount: number;\n  lastRetryTime: number | null;\n  canRetry: boolean;\n  isRetrying: boolean;\n  errorHistory: Array<{\n    error: Error | AppError;\n    timestamp: number;\n    context?: Record<string, any>;\n  }>;\n  recoveryAttempts: number;\n  lastRecoveryTime: number | null;\n}\n\ninterface ErrorHandlingResult {\n  // Error state\n  error: Error | AppError | null;\n  isError: boolean;\n  retryCount: number;\n  canRetry: boolean;\n\n  // Error actions\n  handleError: (error: Error | string) => void;\n  clearError: () => void;\n  retry: () => Promise<void>;\n\n  // Error utilities\n  getErrorMessage: () => string;\n  isNetworkError: () => boolean;\n  isServerError: () => boolean;\n  isAuthError: () => boolean;\n}\n\n/**\n * Hook for standardized error handling across the app\n */\nexport const useErrorHandling = (\n  options: ErrorHandlingOptions = {},\n): ErrorHandlingResult => {\n  const {\n    maxRetries = 3,\n    retryDelay = 2000,\n    progressiveRetryDelay = true,\n    autoRetryOnAppFocus = true,\n    autoRetryOnConnectionRestored = true,\n    reportErrors = true,\n    errorContext = {},\n    onError,\n    onRetry,\n    onMaxRetriesExceeded,\n  } = options;\n\n  // Error state\n  const [state, setState] = useState<ErrorHandlingState>({\n    error: null,\n    isError: false,\n    retryCount: 0,\n    lastRetryTime: null,\n    canRetry: true,\n  });\n\n  // Refs for tracking app state\n  const appStateRef = useRef<AppStateStatus>(AppState.currentState);\n  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n  const componentMountedRef = useRef<boolean>(true);\n\n  // Clear any pending timeouts on unmount\n  useEffect(() => {\n    return () => {\n      componentMountedRef.current = false;\n      if (retryTimeoutRef.current) {\n        clearTimeout(retryTimeoutRef.current);\n      }\n    };\n  }, []);\n\n  // Listen for app state changes to retry on app focus\n  useEffect(() => {\n    if (!autoRetryOnAppFocus) return;\n\n    const handleAppStateChange = (nextAppState: AppStateStatus) => {\n      if (\n        appStateRef.current.match(/inactive|background/) &&\n        nextAppState === 'active' &&\n        state.isError &&\n        state.retryCount < maxRetries\n      ) {\n        // App has come to the foreground and there's an error\n        retry();\n      }\n      appStateRef.current = nextAppState;\n    };\n\n    const subscription = AppState.addEventListener(\n      'change',\n      handleAppStateChange,\n    );\n    return () => {\n      subscription.remove();\n    };\n  }, [state.isError, state.retryCount, maxRetries, autoRetryOnAppFocus]);\n\n  /**\n   * Handle an error\n   */\n  const handleError = useCallback(\n    (errorInput: Error | string) => {\n      // Convert string errors to Error objects\n      const error =\n        typeof errorInput === 'string' ? new Error(errorInput) : errorInput;\n\n      // Create an AppError for consistent error handling\n      const appError =\n        error instanceof AppError\n          ? error\n          : createAppError(error, {\n              component: 'useErrorHandling',\n              action: 'handleError',\n              additionalData: errorContext,\n            });\n\n      // Update error state\n      setState(prev => ({\n        error: appError,\n        isError: true,\n        retryCount: prev.retryCount,\n        lastRetryTime: Date.now(),\n        canRetry: prev.retryCount < maxRetries,\n      }));\n\n      // Log error\n      if (reportErrors) {\n        logError(appError);\n      }\n\n      // Track error in performance monitor\n      performanceMonitor.trackUserInteraction('error_handled', 0, {\n        errorType: appError.name,\n        errorMessage: appError.message,\n        retryCount: state.retryCount,\n        component: errorContext.component,\n      });\n\n      // Call onError callback\n      if (onError) {\n        onError(appError);\n      }\n\n      // Check if max retries exceeded\n      if (state.retryCount >= maxRetries && onMaxRetriesExceeded) {\n        onMaxRetriesExceeded();\n      }\n    },\n    [\n      errorContext,\n      maxRetries,\n      onError,\n      onMaxRetriesExceeded,\n      reportErrors,\n      state.retryCount,\n    ],\n  );\n\n  /**\n   * Clear the current error\n   */\n  const clearError = useCallback(() => {\n    setState({\n      error: null,\n      isError: false,\n      retryCount: 0,\n      lastRetryTime: null,\n      canRetry: true,\n    });\n\n    // Clear any pending retry timeouts\n    if (retryTimeoutRef.current) {\n      clearTimeout(retryTimeoutRef.current);\n      retryTimeoutRef.current = null;\n    }\n  }, []);\n\n  /**\n   * Retry after an error\n   */\n  const retry = useCallback(async () => {\n    if (!state.isError || state.retryCount >= maxRetries) {\n      return;\n    }\n\n    // Calculate progressive retry delay if enabled\n    const currentRetryDelay = progressiveRetryDelay\n      ? retryDelay * Math.pow(1.5, state.retryCount)\n      : retryDelay;\n\n    // Update retry count\n    setState(prev => ({\n      ...prev,\n      retryCount: prev.retryCount + 1,\n      lastRetryTime: Date.now(),\n      canRetry: prev.retryCount + 1 < maxRetries,\n    }));\n\n    // Track retry in performance monitor\n    performanceMonitor.trackUserInteraction('error_retry', 0, {\n      errorType: state.error?.name || 'Unknown',\n      errorMessage: state.error?.message || 'Unknown error',\n      retryCount: state.retryCount + 1,\n      retryDelay: currentRetryDelay,\n    });\n\n    // Call onRetry callback\n    if (onRetry) {\n      onRetry(state.retryCount + 1);\n    }\n\n    // Wait for retry delay\n    await new Promise<void>(resolve => {\n      retryTimeoutRef.current = setTimeout(() => {\n        if (componentMountedRef.current) {\n          resolve();\n        }\n      }, currentRetryDelay);\n    });\n\n    // If component is still mounted, clear error state\n    if (componentMountedRef.current) {\n      clearError();\n    }\n  }, [\n    state.isError,\n    state.retryCount,\n    state.error,\n    maxRetries,\n    progressiveRetryDelay,\n    retryDelay,\n    onRetry,\n    clearError,\n  ]);\n\n  /**\n   * Get a user-friendly error message\n   */\n  const getErrorMessage = useCallback((): string => {\n    if (!state.error) return '';\n\n    // Handle network errors\n    if (isNetworkError()) {\n      return 'Unable to connect to the server. Please check your internet connection and try again.';\n    }\n\n    // Handle server errors\n    if (isServerError()) {\n      return 'Our servers are experiencing issues. Please try again later.';\n    }\n\n    // Handle authentication errors\n    if (isAuthError()) {\n      return 'Your session has expired. Please sign in again.';\n    }\n\n    // Return the error message or a generic fallback\n    return (\n      state.error.message || 'An unexpected error occurred. Please try again.'\n    );\n  }, [state.error]);\n\n  /**\n   * Check if the current error is a network error\n   */\n  const isNetworkError = useCallback((): boolean => {\n    if (!state.error) return false;\n\n    const errorMessage = state.error.message.toLowerCase();\n    return (\n      errorMessage.includes('network') ||\n      errorMessage.includes('connection') ||\n      errorMessage.includes('offline') ||\n      errorMessage.includes('internet') ||\n      errorMessage.includes('timeout') ||\n      errorMessage.includes('abort')\n    );\n  }, [state.error]);\n\n  /**\n   * Check if the current error is a server error\n   */\n  const isServerError = useCallback((): boolean => {\n    if (!state.error) return false;\n\n    const errorMessage = state.error.message.toLowerCase();\n    return (\n      errorMessage.includes('500') ||\n      errorMessage.includes('502') ||\n      errorMessage.includes('503') ||\n      errorMessage.includes('504') ||\n      errorMessage.includes('server error') ||\n      errorMessage.includes('internal server')\n    );\n  }, [state.error]);\n\n  /**\n   * Check if the current error is an authentication error\n   */\n  const isAuthError = useCallback((): boolean => {\n    if (!state.error) return false;\n\n    const errorMessage = state.error.message.toLowerCase();\n    return (\n      errorMessage.includes('401') ||\n      errorMessage.includes('403') ||\n      errorMessage.includes('unauthorized') ||\n      errorMessage.includes('forbidden') ||\n      errorMessage.includes('authentication') ||\n      errorMessage.includes('not authenticated') ||\n      errorMessage.includes('token') ||\n      errorMessage.includes('session expired')\n    );\n  }, [state.error]);\n\n  return {\n    // Error state\n    error: state.error,\n    isError: state.isError,\n    retryCount: state.retryCount,\n    canRetry: state.canRetry,\n\n    // Error actions\n    handleError,\n    clearError,\n    retry,\n\n    // Error utilities\n    getErrorMessage,\n    isNetworkError,\n    isServerError,\n    isAuthError,\n  };\n};\n\nexport default useErrorHandling;\n"], "mappings": ";;;;;;;AAgBA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,mBAAA,GAAAF,OAAA;AACA,IAAAG,mBAAA,GAAAH,OAAA;AA6EO,IAAMI,gBAAgB,GAAAC,OAAA,CAAAD,gBAAA,GAAG,SAAnBA,gBAAgBA,CAAA,EAEH;EAAA,IADxBE,OAA6B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAElC,IAAAG,mBAAA,GAWIJ,OAAO,CAVTK,UAAU;IAAVA,UAAU,GAAAD,mBAAA,cAAG,CAAC,GAAAA,mBAAA;IAAAE,mBAAA,GAUZN,OAAO,CATTO,UAAU;IAAVA,UAAU,GAAAD,mBAAA,cAAG,IAAI,GAAAA,mBAAA;IAAAE,qBAAA,GASfR,OAAO,CARTS,qBAAqB;IAArBA,qBAAqB,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IAAAE,qBAAA,GAQ1BV,OAAO,CAPTW,mBAAmB;IAAnBA,mBAAmB,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IAAAE,qBAAA,GAOxBZ,OAAO,CANTa,6BAA6B;IAA7BA,6BAA6B,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IAAAE,qBAAA,GAMlCd,OAAO,CALTe,YAAY;IAAZA,YAAY,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IAAAE,qBAAA,GAKjBhB,OAAO,CAJTiB,YAAY;IAAZA,YAAY,GAAAD,qBAAA,cAAG,CAAC,CAAC,GAAAA,qBAAA;IACjBE,OAAO,GAGLlB,OAAO,CAHTkB,OAAO;IACPC,OAAO,GAELnB,OAAO,CAFTmB,OAAO;IACPC,oBAAoB,GAClBpB,OAAO,CADToB,oBAAoB;EAItB,IAAAC,SAAA,GAA0B,IAAAC,eAAQ,EAAqB;MACrDC,KAAK,EAAE,IAAI;MACXC,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE,CAAC;MACbC,aAAa,EAAE,IAAI;MACnBC,QAAQ,EAAE;IACZ,CAAC,CAAC;IAAAC,UAAA,OAAAC,eAAA,CAAAC,OAAA,EAAAT,SAAA;IANKU,KAAK,GAAAH,UAAA;IAAEI,QAAQ,GAAAJ,UAAA;EAStB,IAAMK,WAAW,GAAG,IAAAC,aAAM,EAAiBC,qBAAQ,CAACC,YAAY,CAAC;EACjE,IAAMC,eAAe,GAAG,IAAAH,aAAM,EAAwB,IAAI,CAAC;EAC3D,IAAMI,mBAAmB,GAAG,IAAAJ,aAAM,EAAU,IAAI,CAAC;EAGjD,IAAAK,gBAAS,EAAC,YAAM;IACd,OAAO,YAAM;MACXD,mBAAmB,CAACE,OAAO,GAAG,KAAK;MACnC,IAAIH,eAAe,CAACG,OAAO,EAAE;QAC3BC,YAAY,CAACJ,eAAe,CAACG,OAAO,CAAC;MACvC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAGN,IAAAD,gBAAS,EAAC,YAAM;IACd,IAAI,CAAC5B,mBAAmB,EAAE;IAE1B,IAAM+B,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIC,YAA4B,EAAK;MAC7D,IACEV,WAAW,CAACO,OAAO,CAACI,KAAK,CAAC,qBAAqB,CAAC,IAChDD,YAAY,KAAK,QAAQ,IACzBZ,KAAK,CAACP,OAAO,IACbO,KAAK,CAACN,UAAU,GAAGpB,UAAU,EAC7B;QAEAwC,KAAK,CAAC,CAAC;MACT;MACAZ,WAAW,CAACO,OAAO,GAAGG,YAAY;IACpC,CAAC;IAED,IAAMG,YAAY,GAAGX,qBAAQ,CAACY,gBAAgB,CAC5C,QAAQ,EACRL,oBACF,CAAC;IACD,OAAO,YAAM;MACXI,YAAY,CAACE,MAAM,CAAC,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACjB,KAAK,CAACP,OAAO,EAAEO,KAAK,CAACN,UAAU,EAAEpB,UAAU,EAAEM,mBAAmB,CAAC,CAAC;EAKtE,IAAMsC,WAAW,GAAG,IAAAC,kBAAW,EAC7B,UAACC,UAA0B,EAAK;IAE9B,IAAM5B,KAAK,GACT,OAAO4B,UAAU,KAAK,QAAQ,GAAG,IAAIC,KAAK,CAACD,UAAU,CAAC,GAAGA,UAAU;IAGrE,IAAME,QAAQ,GACZ9B,KAAK,YAAY+B,4BAAQ,GACrB/B,KAAK,GACL,IAAAgC,kCAAc,EAAChC,KAAK,EAAE;MACpBiC,SAAS,EAAE,kBAAkB;MAC7BC,MAAM,EAAE,aAAa;MACrBC,cAAc,EAAEzC;IAClB,CAAC,CAAC;IAGRe,QAAQ,CAAC,UAAA2B,IAAI;MAAA,OAAK;QAChBpC,KAAK,EAAE8B,QAAQ;QACf7B,OAAO,EAAE,IAAI;QACbC,UAAU,EAAEkC,IAAI,CAAClC,UAAU;QAC3BC,aAAa,EAAEkC,IAAI,CAACC,GAAG,CAAC,CAAC;QACzBlC,QAAQ,EAAEgC,IAAI,CAAClC,UAAU,GAAGpB;MAC9B,CAAC;IAAA,CAAC,CAAC;IAGH,IAAIU,YAAY,EAAE;MAChB,IAAA+C,4BAAQ,EAACT,QAAQ,CAAC;IACpB;IAGAU,sCAAkB,CAACC,oBAAoB,CAAC,eAAe,EAAE,CAAC,EAAE;MAC1DC,SAAS,EAAEZ,QAAQ,CAACa,IAAI;MACxBC,YAAY,EAAEd,QAAQ,CAACe,OAAO;MAC9B3C,UAAU,EAAEM,KAAK,CAACN,UAAU;MAC5B+B,SAAS,EAAEvC,YAAY,CAACuC;IAC1B,CAAC,CAAC;IAGF,IAAItC,OAAO,EAAE;MACXA,OAAO,CAACmC,QAAQ,CAAC;IACnB;IAGA,IAAItB,KAAK,CAACN,UAAU,IAAIpB,UAAU,IAAIe,oBAAoB,EAAE;MAC1DA,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,EACD,CACEH,YAAY,EACZZ,UAAU,EACVa,OAAO,EACPE,oBAAoB,EACpBL,YAAY,EACZgB,KAAK,CAACN,UAAU,CAEpB,CAAC;EAKD,IAAM4C,UAAU,GAAG,IAAAnB,kBAAW,EAAC,YAAM;IACnClB,QAAQ,CAAC;MACPT,KAAK,EAAE,IAAI;MACXC,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE,CAAC;MACbC,aAAa,EAAE,IAAI;MACnBC,QAAQ,EAAE;IACZ,CAAC,CAAC;IAGF,IAAIU,eAAe,CAACG,OAAO,EAAE;MAC3BC,YAAY,CAACJ,eAAe,CAACG,OAAO,CAAC;MACrCH,eAAe,CAACG,OAAO,GAAG,IAAI;IAChC;EACF,CAAC,EAAE,EAAE,CAAC;EAKN,IAAMK,KAAK,GAAG,IAAAK,kBAAW,MAAAoB,kBAAA,CAAAxC,OAAA,EAAC,aAAY;IAAA,IAAAyC,YAAA,EAAAC,aAAA;IACpC,IAAI,CAACzC,KAAK,CAACP,OAAO,IAAIO,KAAK,CAACN,UAAU,IAAIpB,UAAU,EAAE;MACpD;IACF;IAGA,IAAMoE,iBAAiB,GAAGhE,qBAAqB,GAC3CF,UAAU,GAAGmE,IAAI,CAACC,GAAG,CAAC,GAAG,EAAE5C,KAAK,CAACN,UAAU,CAAC,GAC5ClB,UAAU;IAGdyB,QAAQ,CAAC,UAAA2B,IAAI;MAAA,OAAAiB,MAAA,CAAAC,MAAA,KACRlB,IAAI;QACPlC,UAAU,EAAEkC,IAAI,CAAClC,UAAU,GAAG,CAAC;QAC/BC,aAAa,EAAEkC,IAAI,CAACC,GAAG,CAAC,CAAC;QACzBlC,QAAQ,EAAEgC,IAAI,CAAClC,UAAU,GAAG,CAAC,GAAGpB;MAAU;IAAA,CAC1C,CAAC;IAGH0D,sCAAkB,CAACC,oBAAoB,CAAC,aAAa,EAAE,CAAC,EAAE;MACxDC,SAAS,EAAE,EAAAM,YAAA,GAAAxC,KAAK,CAACR,KAAK,qBAAXgD,YAAA,CAAaL,IAAI,KAAI,SAAS;MACzCC,YAAY,EAAE,EAAAK,aAAA,GAAAzC,KAAK,CAACR,KAAK,qBAAXiD,aAAA,CAAaJ,OAAO,KAAI,eAAe;MACrD3C,UAAU,EAAEM,KAAK,CAACN,UAAU,GAAG,CAAC;MAChClB,UAAU,EAAEkE;IACd,CAAC,CAAC;IAGF,IAAItD,OAAO,EAAE;MACXA,OAAO,CAACY,KAAK,CAACN,UAAU,GAAG,CAAC,CAAC;IAC/B;IAGA,MAAM,IAAIqD,OAAO,CAAO,UAAAC,OAAO,EAAI;MACjC1C,eAAe,CAACG,OAAO,GAAGwC,UAAU,CAAC,YAAM;QACzC,IAAI1C,mBAAmB,CAACE,OAAO,EAAE;UAC/BuC,OAAO,CAAC,CAAC;QACX;MACF,CAAC,EAAEN,iBAAiB,CAAC;IACvB,CAAC,CAAC;IAGF,IAAInC,mBAAmB,CAACE,OAAO,EAAE;MAC/B6B,UAAU,CAAC,CAAC;IACd;EACF,CAAC,GAAE,CACDtC,KAAK,CAACP,OAAO,EACbO,KAAK,CAACN,UAAU,EAChBM,KAAK,CAACR,KAAK,EACXlB,UAAU,EACVI,qBAAqB,EACrBF,UAAU,EACVY,OAAO,EACPkD,UAAU,CACX,CAAC;EAKF,IAAMY,eAAe,GAAG,IAAA/B,kBAAW,EAAC,YAAc;IAChD,IAAI,CAACnB,KAAK,CAACR,KAAK,EAAE,OAAO,EAAE;IAG3B,IAAI2D,cAAc,CAAC,CAAC,EAAE;MACpB,OAAO,uFAAuF;IAChG;IAGA,IAAIC,aAAa,CAAC,CAAC,EAAE;MACnB,OAAO,8DAA8D;IACvE;IAGA,IAAIC,WAAW,CAAC,CAAC,EAAE;MACjB,OAAO,iDAAiD;IAC1D;IAGA,OACErD,KAAK,CAACR,KAAK,CAAC6C,OAAO,IAAI,iDAAiD;EAE5E,CAAC,EAAE,CAACrC,KAAK,CAACR,KAAK,CAAC,CAAC;EAKjB,IAAM2D,cAAc,GAAG,IAAAhC,kBAAW,EAAC,YAAe;IAChD,IAAI,CAACnB,KAAK,CAACR,KAAK,EAAE,OAAO,KAAK;IAE9B,IAAM4C,YAAY,GAAGpC,KAAK,CAACR,KAAK,CAAC6C,OAAO,CAACiB,WAAW,CAAC,CAAC;IACtD,OACElB,YAAY,CAACmB,QAAQ,CAAC,SAAS,CAAC,IAChCnB,YAAY,CAACmB,QAAQ,CAAC,YAAY,CAAC,IACnCnB,YAAY,CAACmB,QAAQ,CAAC,SAAS,CAAC,IAChCnB,YAAY,CAACmB,QAAQ,CAAC,UAAU,CAAC,IACjCnB,YAAY,CAACmB,QAAQ,CAAC,SAAS,CAAC,IAChCnB,YAAY,CAACmB,QAAQ,CAAC,OAAO,CAAC;EAElC,CAAC,EAAE,CAACvD,KAAK,CAACR,KAAK,CAAC,CAAC;EAKjB,IAAM4D,aAAa,GAAG,IAAAjC,kBAAW,EAAC,YAAe;IAC/C,IAAI,CAACnB,KAAK,CAACR,KAAK,EAAE,OAAO,KAAK;IAE9B,IAAM4C,YAAY,GAAGpC,KAAK,CAACR,KAAK,CAAC6C,OAAO,CAACiB,WAAW,CAAC,CAAC;IACtD,OACElB,YAAY,CAACmB,QAAQ,CAAC,KAAK,CAAC,IAC5BnB,YAAY,CAACmB,QAAQ,CAAC,KAAK,CAAC,IAC5BnB,YAAY,CAACmB,QAAQ,CAAC,KAAK,CAAC,IAC5BnB,YAAY,CAACmB,QAAQ,CAAC,KAAK,CAAC,IAC5BnB,YAAY,CAACmB,QAAQ,CAAC,cAAc,CAAC,IACrCnB,YAAY,CAACmB,QAAQ,CAAC,iBAAiB,CAAC;EAE5C,CAAC,EAAE,CAACvD,KAAK,CAACR,KAAK,CAAC,CAAC;EAKjB,IAAM6D,WAAW,GAAG,IAAAlC,kBAAW,EAAC,YAAe;IAC7C,IAAI,CAACnB,KAAK,CAACR,KAAK,EAAE,OAAO,KAAK;IAE9B,IAAM4C,YAAY,GAAGpC,KAAK,CAACR,KAAK,CAAC6C,OAAO,CAACiB,WAAW,CAAC,CAAC;IACtD,OACElB,YAAY,CAACmB,QAAQ,CAAC,KAAK,CAAC,IAC5BnB,YAAY,CAACmB,QAAQ,CAAC,KAAK,CAAC,IAC5BnB,YAAY,CAACmB,QAAQ,CAAC,cAAc,CAAC,IACrCnB,YAAY,CAACmB,QAAQ,CAAC,WAAW,CAAC,IAClCnB,YAAY,CAACmB,QAAQ,CAAC,gBAAgB,CAAC,IACvCnB,YAAY,CAACmB,QAAQ,CAAC,mBAAmB,CAAC,IAC1CnB,YAAY,CAACmB,QAAQ,CAAC,OAAO,CAAC,IAC9BnB,YAAY,CAACmB,QAAQ,CAAC,iBAAiB,CAAC;EAE5C,CAAC,EAAE,CAACvD,KAAK,CAACR,KAAK,CAAC,CAAC;EAEjB,OAAO;IAELA,KAAK,EAAEQ,KAAK,CAACR,KAAK;IAClBC,OAAO,EAAEO,KAAK,CAACP,OAAO;IACtBC,UAAU,EAAEM,KAAK,CAACN,UAAU;IAC5BE,QAAQ,EAAEI,KAAK,CAACJ,QAAQ;IAGxBsB,WAAW,EAAXA,WAAW;IACXoB,UAAU,EAAVA,UAAU;IACVxB,KAAK,EAALA,KAAK;IAGLoC,eAAe,EAAfA,eAAe;IACfC,cAAc,EAAdA,cAAc;IACdC,aAAa,EAAbA,aAAa;IACbC,WAAW,EAAXA;EACF,CAAC;AACH,CAAC;AAAC,IAAAG,QAAA,GAAAxF,OAAA,CAAA+B,OAAA,GAEahC,gBAAgB", "ignoreList": []}