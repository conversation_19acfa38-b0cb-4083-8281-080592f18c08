{"version": 3, "names": ["_invariant", "_interopRequireDefault", "require", "INDEX_X", "INDEX_Y", "INDEX_Z", "processTransformOrigin", "transform<PERSON><PERSON>in", "transformOriginString", "regex", "transformOriginArray", "index", "matches", "outer", "exec", "nextIndex", "value", "valueLower", "toLowerCase", "invariant", "horizontal", "endsWith", "parseFloat", "__DEV__", "_validateTransformOrigin", "length", "_transform<PERSON><PERSON><PERSON>", "_slicedToArray2", "default", "x", "y", "z"], "sources": ["processTransformOrigin.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow\n */\n\nimport invariant from 'invariant';\n\nconst INDEX_X = 0;\nconst INDEX_Y = 1;\nconst INDEX_Z = 2;\n\n/* eslint-disable no-labels */\nexport default function processTransformOrigin(\n  transformOrigin: Array<string | number> | string,\n): Array<string | number> {\n  if (typeof transformOrigin === 'string') {\n    const transformOriginString = transformOrigin;\n    const regex = /(top|bottom|left|right|center|\\d+(?:%|px)|0)/gi;\n    const transformOriginArray: Array<string | number> = ['50%', '50%', 0];\n\n    let index = INDEX_X;\n    let matches;\n    outer: while ((matches = regex.exec(transformOriginString))) {\n      let nextIndex = index + 1;\n\n      const value = matches[0];\n      const valueLower = value.toLowerCase();\n\n      switch (valueLower) {\n        case 'left':\n        case 'right': {\n          invariant(\n            index === INDEX_X,\n            'Transform-origin %s can only be used for x-position',\n            value,\n          );\n          transformOriginArray[INDEX_X] = valueLower === 'left' ? 0 : '100%';\n          break;\n        }\n        case 'top':\n        case 'bottom': {\n          invariant(\n            index !== INDEX_Z,\n            'Transform-origin %s can only be used for y-position',\n            value,\n          );\n          transformOriginArray[INDEX_Y] = valueLower === 'top' ? 0 : '100%';\n\n          // Handle [[ center | left | right ] && [ center | top | bottom ]] <length>?\n          if (index === INDEX_X) {\n            const horizontal = regex.exec(transformOriginString);\n            if (horizontal == null) {\n              break outer;\n            }\n\n            switch (horizontal[0].toLowerCase()) {\n              case 'left':\n                transformOriginArray[INDEX_X] = 0;\n                break;\n              case 'right':\n                transformOriginArray[INDEX_X] = '100%';\n                break;\n              case 'center':\n                transformOriginArray[INDEX_X] = '50%';\n                break;\n              default:\n                invariant(\n                  false,\n                  'Could not parse transform-origin: %s',\n                  transformOriginString,\n                );\n            }\n            nextIndex = INDEX_Z;\n          }\n\n          break;\n        }\n        case 'center': {\n          invariant(\n            index !== INDEX_Z,\n            'Transform-origin value %s cannot be used for z-position',\n            value,\n          );\n          transformOriginArray[index] = '50%';\n          break;\n        }\n        default: {\n          if (value.endsWith('%')) {\n            transformOriginArray[index] = value;\n          } else {\n            transformOriginArray[index] = parseFloat(value); // Remove `px`\n          }\n          break;\n        }\n      }\n\n      index = nextIndex;\n    }\n\n    transformOrigin = transformOriginArray;\n  }\n\n  if (__DEV__) {\n    _validateTransformOrigin(transformOrigin);\n  }\n\n  return transformOrigin;\n}\n\nfunction _validateTransformOrigin(transformOrigin: Array<string | number>) {\n  invariant(\n    transformOrigin.length === 3,\n    'Transform origin must have exactly 3 values.',\n  );\n  const [x, y, z] = transformOrigin;\n  invariant(\n    typeof x === 'number' || (typeof x === 'string' && x.endsWith('%')),\n    'Transform origin x-position must be a number. Passed value: %s.',\n    x,\n  );\n  invariant(\n    typeof y === 'number' || (typeof y === 'string' && y.endsWith('%')),\n    'Transform origin y-position must be a number. Passed value: %s.',\n    y,\n  );\n  invariant(\n    typeof z === 'number',\n    'Transform origin z-position must be a number. Passed value: %s.',\n    z,\n  );\n}\n"], "mappings": ";;;;;;AAUA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAMC,OAAO,GAAG,CAAC;AACjB,IAAMC,OAAO,GAAG,CAAC;AACjB,IAAMC,OAAO,GAAG,CAAC;AAGF,SAASC,sBAAsBA,CAC5CC,eAAgD,EACxB;EACxB,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;IACvC,IAAMC,qBAAqB,GAAGD,eAAe;IAC7C,IAAME,KAAK,GAAG,gDAAgD;IAC9D,IAAMC,oBAA4C,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;IAEtE,IAAIC,KAAK,GAAGR,OAAO;IACnB,IAAIS,OAAO;IACXC,KAAK,EAAE,OAAQD,OAAO,GAAGH,KAAK,CAACK,IAAI,CAACN,qBAAqB,CAAC,EAAG;MAC3D,IAAIO,SAAS,GAAGJ,KAAK,GAAG,CAAC;MAEzB,IAAMK,KAAK,GAAGJ,OAAO,CAAC,CAAC,CAAC;MACxB,IAAMK,UAAU,GAAGD,KAAK,CAACE,WAAW,CAAC,CAAC;MAEtC,QAAQD,UAAU;QAChB,KAAK,MAAM;QACX,KAAK,OAAO;UAAE;YACZ,IAAAE,kBAAS,EACPR,KAAK,KAAKR,OAAO,EACjB,qDAAqD,EACrDa,KACF,CAAC;YACDN,oBAAoB,CAACP,OAAO,CAAC,GAAGc,UAAU,KAAK,MAAM,GAAG,CAAC,GAAG,MAAM;YAClE;UACF;QACA,KAAK,KAAK;QACV,KAAK,QAAQ;UAAE;YACb,IAAAE,kBAAS,EACPR,KAAK,KAAKN,OAAO,EACjB,qDAAqD,EACrDW,KACF,CAAC;YACDN,oBAAoB,CAACN,OAAO,CAAC,GAAGa,UAAU,KAAK,KAAK,GAAG,CAAC,GAAG,MAAM;YAGjE,IAAIN,KAAK,KAAKR,OAAO,EAAE;cACrB,IAAMiB,UAAU,GAAGX,KAAK,CAACK,IAAI,CAACN,qBAAqB,CAAC;cACpD,IAAIY,UAAU,IAAI,IAAI,EAAE;gBACtB,MAAMP,KAAK;cACb;cAEA,QAAQO,UAAU,CAAC,CAAC,CAAC,CAACF,WAAW,CAAC,CAAC;gBACjC,KAAK,MAAM;kBACTR,oBAAoB,CAACP,OAAO,CAAC,GAAG,CAAC;kBACjC;gBACF,KAAK,OAAO;kBACVO,oBAAoB,CAACP,OAAO,CAAC,GAAG,MAAM;kBACtC;gBACF,KAAK,QAAQ;kBACXO,oBAAoB,CAACP,OAAO,CAAC,GAAG,KAAK;kBACrC;gBACF;kBACE,IAAAgB,kBAAS,EACP,KAAK,EACL,sCAAsC,EACtCX,qBACF,CAAC;cACL;cACAO,SAAS,GAAGV,OAAO;YACrB;YAEA;UACF;QACA,KAAK,QAAQ;UAAE;YACb,IAAAc,kBAAS,EACPR,KAAK,KAAKN,OAAO,EACjB,yDAAyD,EACzDW,KACF,CAAC;YACDN,oBAAoB,CAACC,KAAK,CAAC,GAAG,KAAK;YACnC;UACF;QACA;UAAS;YACP,IAAIK,KAAK,CAACK,QAAQ,CAAC,GAAG,CAAC,EAAE;cACvBX,oBAAoB,CAACC,KAAK,CAAC,GAAGK,KAAK;YACrC,CAAC,MAAM;cACLN,oBAAoB,CAACC,KAAK,CAAC,GAAGW,UAAU,CAACN,KAAK,CAAC;YACjD;YACA;UACF;MACF;MAEAL,KAAK,GAAGI,SAAS;IACnB;IAEAR,eAAe,GAAGG,oBAAoB;EACxC;EAEA,IAAIa,OAAO,EAAE;IACXC,wBAAwB,CAACjB,eAAe,CAAC;EAC3C;EAEA,OAAOA,eAAe;AACxB;AAEA,SAASiB,wBAAwBA,CAACjB,eAAuC,EAAE;EACzE,IAAAY,kBAAS,EACPZ,eAAe,CAACkB,MAAM,KAAK,CAAC,EAC5B,8CACF,CAAC;EACD,IAAAC,gBAAA,OAAAC,eAAA,CAAAC,OAAA,EAAkBrB,eAAe;IAA1BsB,CAAC,GAAAH,gBAAA;IAAEI,CAAC,GAAAJ,gBAAA;IAAEK,CAAC,GAAAL,gBAAA;EACd,IAAAP,kBAAS,EACP,OAAOU,CAAC,KAAK,QAAQ,IAAK,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACR,QAAQ,CAAC,GAAG,CAAE,EACnE,iEAAiE,EACjEQ,CACF,CAAC;EACD,IAAAV,kBAAS,EACP,OAAOW,CAAC,KAAK,QAAQ,IAAK,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACT,QAAQ,CAAC,GAAG,CAAE,EACnE,iEAAiE,EACjES,CACF,CAAC;EACD,IAAAX,kBAAS,EACP,OAAOY,CAAC,KAAK,QAAQ,EACrB,iEAAiE,EACjEA,CACF,CAAC;AACH", "ignoreList": []}