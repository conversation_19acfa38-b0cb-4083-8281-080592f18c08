a3b969973ae7e46f782012645ad56380
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
exports.currency = exports.formatCurrency = formatCurrency;
exports.date = exports.formatDate = formatDate;
exports.formatPhoneNumber = formatPhoneNumber;
exports.formatPostalCode = formatPostalCode;
exports.time = exports.formatTime = formatTime;
exports.getAvailableLocales = getAvailableLocales;
exports.getCurrentLocale = getCurrentLocale;
exports.getProvinces = getProvinces;
exports.initializeI18n = initializeI18n;
exports.isFrenchCanadian = isFrenchCanadian;
exports.onLocaleChange = onLocaleChange;
exports.setLocale = setLocale;
exports.translate = exports.t = t;
exports.translatePlural = exports.tp = tp;
exports.validateTranslations = validateTranslations;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _asyncStorage = _interopRequireDefault(require("@react-native-async-storage/async-storage"));
var _reactNative = require("react-native");
var _enCA = _interopRequireDefault(require("../locales/en-CA.json"));
var _frCA = _interopRequireDefault(require("../locales/fr-CA.json"));
var translations = {
  'en-CA': _enCA.default,
  'fr-CA': _frCA.default
};
var currentLocale = 'en-CA';
var currentTranslations = translations[currentLocale];
var LOCALE_STORAGE_KEY = '@vierla_locale';
function getDeviceLocale() {
  var deviceLocale = 'en-CA';
  if (_reactNative.Platform.OS === 'ios') {
    var _NativeModules$Settin, _NativeModules$Settin2;
    deviceLocale = ((_NativeModules$Settin = _reactNative.NativeModules.SettingsManager) == null || (_NativeModules$Settin = _NativeModules$Settin.settings) == null ? void 0 : _NativeModules$Settin.AppleLocale) || ((_NativeModules$Settin2 = _reactNative.NativeModules.SettingsManager) == null || (_NativeModules$Settin2 = _NativeModules$Settin2.settings) == null || (_NativeModules$Settin2 = _NativeModules$Settin2.AppleLanguages) == null ? void 0 : _NativeModules$Settin2[0]) || 'en-CA';
  } else if (_reactNative.Platform.OS === 'android') {
    var _NativeModules$I18nMa;
    deviceLocale = ((_NativeModules$I18nMa = _reactNative.NativeModules.I18nManager) == null ? void 0 : _NativeModules$I18nMa.localeIdentifier) || 'en-CA';
  } else if (_reactNative.Platform.OS === 'web') {
    deviceLocale = navigator.language || 'en-CA';
  }
  if (deviceLocale.startsWith('fr')) {
    return 'fr-CA';
  } else {
    return 'en-CA';
  }
}
function initializeI18n() {
  return _initializeI18n.apply(this, arguments);
}
function _initializeI18n() {
  _initializeI18n = (0, _asyncToGenerator2.default)(function* () {
    try {
      var savedLocale = yield _asyncStorage.default.getItem(LOCALE_STORAGE_KEY);
      if (savedLocale && (savedLocale === 'en-CA' || savedLocale === 'fr-CA')) {
        currentLocale = savedLocale;
      } else {
        currentLocale = getDeviceLocale();
      }
      currentTranslations = translations[currentLocale];
      return currentLocale;
    } catch (error) {
      console.warn('Failed to initialize i18n:', error);
      currentLocale = 'en-CA';
      currentTranslations = translations[currentLocale];
      return currentLocale;
    }
  });
  return _initializeI18n.apply(this, arguments);
}
function setLocale(_x) {
  return _setLocale.apply(this, arguments);
}
function _setLocale() {
  _setLocale = (0, _asyncToGenerator2.default)(function* (locale) {
    try {
      currentLocale = locale;
      currentTranslations = translations[locale];
      yield _asyncStorage.default.setItem(LOCALE_STORAGE_KEY, locale);
      localeChangeListeners.forEach(function (listener) {
        return listener(locale);
      });
    } catch (error) {
      console.error('Failed to set locale:', error);
    }
  });
  return _setLocale.apply(this, arguments);
}
function getCurrentLocale() {
  return currentLocale;
}
function isFrenchCanadian() {
  return currentLocale === 'fr-CA';
}
function t(key, params) {
  var keys = key.split('.');
  var value = currentTranslations;
  for (var k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      if (currentLocale !== 'en-CA') {
        var fallbackKeys = key.split('.');
        var fallbackValue = translations['en-CA'];
        for (var fk of fallbackKeys) {
          if (fallbackValue && typeof fallbackValue === 'object' && fk in fallbackValue) {
            fallbackValue = fallbackValue[fk];
          } else {
            return `[Missing: ${key}]`;
          }
        }
        value = fallbackValue;
      } else {
        return `[Missing: ${key}]`;
      }
      break;
    }
  }
  if (typeof value !== 'string') {
    return `[Invalid: ${key}]`;
  }
  if (params) {
    return value.replace(/\{\{(\w+)\}\}/g, function (match, paramKey) {
      var _params$paramKey;
      return ((_params$paramKey = params[paramKey]) == null ? void 0 : _params$paramKey.toString()) || match;
    });
  }
  return value;
}
function tp(key, count, params) {
  var pluralKey = count === 1 ? `${key}.singular` : `${key}.plural`;
  var fallbackKey = key;
  var translation = t(pluralKey);
  if (translation.startsWith('[Missing:') || translation.startsWith('[Invalid:')) {
    translation = t(fallbackKey);
  }
  var allParams = Object.assign({}, params, {
    count: count
  });
  return t(translation, allParams);
}
function formatCurrency(amount, options) {
  var locale = (options == null ? void 0 : options.locale) || currentLocale;
  var showCents = (options == null ? void 0 : options.showCents) !== false;
  var formatter = new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: 'CAD',
    minimumFractionDigits: showCents ? 2 : 0,
    maximumFractionDigits: showCents ? 2 : 0
  });
  return formatter.format(amount);
}
function formatDate(date, options) {
  var locale = (options == null ? void 0 : options.locale) || currentLocale;
  var style = (options == null ? void 0 : options.style) || 'medium';
  var formatOptions = {
    year: 'numeric',
    month: style === 'short' ? 'numeric' : style === 'medium' ? 'short' : 'long',
    day: 'numeric'
  };
  if (style === 'full') {
    formatOptions.weekday = 'long';
  }
  return new Intl.DateTimeFormat(locale, formatOptions).format(date);
}
function formatTime(date, options) {
  var locale = (options == null ? void 0 : options.locale) || currentLocale;
  var format = (options == null ? void 0 : options.format) || '12h';
  var formatOptions = {
    hour: 'numeric',
    minute: '2-digit',
    hour12: format === '12h'
  };
  return new Intl.DateTimeFormat(locale, formatOptions).format(date);
}
function formatPhoneNumber(phoneNumber) {
  var digits = phoneNumber.replace(/\D/g, '');
  if (digits.length === 10) {
    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
  } else if (digits.length === 11 && digits.startsWith('1')) {
    return `+1 (${digits.slice(1, 4)}) ${digits.slice(4, 7)}-${digits.slice(7)}`;
  }
  return phoneNumber;
}
function formatPostalCode(postalCode) {
  var cleaned = postalCode.replace(/\s/g, '').toUpperCase();
  if (cleaned.length === 6 && /^[A-Z]\d[A-Z]\d[A-Z]\d$/.test(cleaned)) {
    return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;
  }
  return postalCode;
}
function getProvinces() {
  var provinces = [{
    code: 'AB',
    nameKey: 'provinces.alberta'
  }, {
    code: 'BC',
    nameKey: 'provinces.britishColumbia'
  }, {
    code: 'MB',
    nameKey: 'provinces.manitoba'
  }, {
    code: 'NB',
    nameKey: 'provinces.newBrunswick'
  }, {
    code: 'NL',
    nameKey: 'provinces.newfoundlandLabrador'
  }, {
    code: 'NS',
    nameKey: 'provinces.novaScotia'
  }, {
    code: 'ON',
    nameKey: 'provinces.ontario'
  }, {
    code: 'PE',
    nameKey: 'provinces.princeEdwardIsland'
  }, {
    code: 'QC',
    nameKey: 'provinces.quebec'
  }, {
    code: 'SK',
    nameKey: 'provinces.saskatchewan'
  }, {
    code: 'NT',
    nameKey: 'provinces.northwestTerritories'
  }, {
    code: 'NU',
    nameKey: 'provinces.nunavut'
  }, {
    code: 'YT',
    nameKey: 'provinces.yukon'
  }];
  return provinces.map(function (province) {
    return {
      code: province.code,
      name: t(province.nameKey) || province.code
    };
  });
}
var localeChangeListeners = [];
function onLocaleChange(listener) {
  localeChangeListeners.push(listener);
  return function () {
    var index = localeChangeListeners.indexOf(listener);
    if (index > -1) {
      localeChangeListeners.splice(index, 1);
    }
  };
}
function getAvailableLocales() {
  return [{
    code: 'en-CA',
    name: 'English (Canada)',
    nativeName: 'English (Canada)'
  }, {
    code: 'fr-CA',
    name: 'French (Canada)',
    nativeName: 'Français (Canada)'
  }];
}
function validateTranslations() {
  var enKeys = getAllKeys(translations['en-CA']);
  var frKeys = getAllKeys(translations['fr-CA']);
  var missing = enKeys.filter(function (key) {
    return !frKeys.includes(key);
  });
  var extra = frKeys.filter(function (key) {
    return !enKeys.includes(key);
  });
  return {
    missing: missing,
    extra: extra,
    complete: missing.length === 0 && extra.length === 0
  };
}
function getAllKeys(obj) {
  var prefix = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';
  var keys = [];
  for (var key in obj) {
    var fullKey = prefix ? `${prefix}.${key}` : key;
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      keys = keys.concat(getAllKeys(obj[key], fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  return keys;
}
var _default = exports.default = {
  t: t,
  tp: tp,
  setLocale: setLocale,
  getCurrentLocale: getCurrentLocale,
  isFrenchCanadian: isFrenchCanadian,
  formatCurrency: formatCurrency,
  formatDate: formatDate,
  formatTime: formatTime,
  formatPhoneNumber: formatPhoneNumber,
  formatPostalCode: formatPostalCode,
  getProvinces: getProvinces,
  onLocaleChange: onLocaleChange,
  getAvailableLocales: getAvailableLocales,
  initializeI18n: initializeI18n
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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