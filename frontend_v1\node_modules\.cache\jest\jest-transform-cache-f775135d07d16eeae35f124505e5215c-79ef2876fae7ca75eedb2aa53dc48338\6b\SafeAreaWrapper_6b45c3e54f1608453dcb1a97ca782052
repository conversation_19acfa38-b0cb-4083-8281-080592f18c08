04b9d6610d26244fb225651e89d8bc4c
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useSafeAreaTop = exports.useSafeAreaBottom = exports.useCustomSafeAreaInsets = exports.SafeAreaWrapper = exports.SafeAreaScreen = exports.SafeAreaModal = exports.SafeAreaHeader = exports.EnhancedSafeAreaView = void 0;
var _react = _interopRequireDefault(require("react"));
var _reactNative = require("react-native");
var _platformUtils = require("../../utils/platformUtils");
var _responsiveUtils = require("../../utils/responsiveUtils");
var _jsxRuntime = require("react/jsx-runtime");
var SafeAreaWrapper = exports.SafeAreaWrapper = function SafeAreaWrapper(_ref) {
  var _forceInsets$top, _forceInsets$bottom, _forceInsets$left, _forceInsets$right;
  var children = _ref.children,
    style = _ref.style,
    _ref$edges = _ref.edges,
    edges = _ref$edges === void 0 ? ['top', 'bottom'] : _ref$edges,
    _ref$mode = _ref.mode,
    mode = _ref$mode === void 0 ? 'padding' : _ref$mode,
    _ref$backgroundColor = _ref.backgroundColor,
    backgroundColor = _ref$backgroundColor === void 0 ? 'transparent' : _ref$backgroundColor,
    forceInsets = _ref.forceInsets,
    testID = _ref.testID,
    _ref$respectNotch = _ref.respectNotch,
    respectNotch = _ref$respectNotch === void 0 ? true : _ref$respectNotch,
    _ref$respectGestures = _ref.respectGestures,
    respectGestures = _ref$respectGestures === void 0 ? true : _ref$respectGestures,
    _ref$statusBarStyle = _ref.statusBarStyle,
    statusBarStyle = _ref$statusBarStyle === void 0 ? 'dark-content' : _ref$statusBarStyle,
    statusBarBackgroundColor = _ref.statusBarBackgroundColor,
    customTopPadding = _ref.customTopPadding,
    customBottomPadding = _ref.customBottomPadding;
  var insets = (0, _responsiveUtils.getSafeAreaInsets)();
  var getEnhancedTopInset = function getEnhancedTopInset() {
    if (customTopPadding !== undefined) return customTopPadding;
    if (respectNotch && (0, _responsiveUtils.isIPhoneWithNotch)()) {
      if ((0, _responsiveUtils.hasDynamicIsland)()) return Math.max(insets.top, 59);
      if ((0, _responsiveUtils.hasNotch)()) return Math.max(insets.top, 44);
    }
    return insets.top;
  };
  var getEnhancedBottomInset = function getEnhancedBottomInset() {
    if (customBottomPadding !== undefined) return customBottomPadding;
    if (respectGestures) {
      if ((0, _responsiveUtils.isIPhoneWithNotch)()) return Math.max(insets.bottom, 34);
      if ((0, _responsiveUtils.isAndroidWithGestures)()) return Math.max(insets.bottom, 16);
    }
    return insets.bottom;
  };
  var finalInsets = {
    top: (_forceInsets$top = forceInsets == null ? void 0 : forceInsets.top) != null ? _forceInsets$top : getEnhancedTopInset(),
    bottom: (_forceInsets$bottom = forceInsets == null ? void 0 : forceInsets.bottom) != null ? _forceInsets$bottom : getEnhancedBottomInset(),
    left: (_forceInsets$left = forceInsets == null ? void 0 : forceInsets.left) != null ? _forceInsets$left : insets.left,
    right: (_forceInsets$right = forceInsets == null ? void 0 : forceInsets.right) != null ? _forceInsets$right : insets.right
  };
  var dynamicStyle = {};
  edges.forEach(function (edge) {
    var insetValue = finalInsets[edge];
    if (insetValue > 0) {
      if (mode === 'padding') {
        dynamicStyle[`padding${edge.charAt(0).toUpperCase() + edge.slice(1)}`] = insetValue;
      } else {
        dynamicStyle[`margin${edge.charAt(0).toUpperCase() + edge.slice(1)}`] = insetValue;
      }
    }
  });
  return (0, _jsxRuntime.jsxs)(_reactNative.View, {
    style: [styles.container, {
      backgroundColor: backgroundColor
    }, dynamicStyle, style],
    testID: testID,
    children: [_reactNative.Platform.OS !== 'web' && (0, _jsxRuntime.jsx)(_reactNative.StatusBar, {
      barStyle: statusBarStyle,
      backgroundColor: statusBarBackgroundColor || backgroundColor,
      translucent: _reactNative.Platform.OS === 'android'
    }), children]
  });
};
var EnhancedSafeAreaView = exports.EnhancedSafeAreaView = function EnhancedSafeAreaView(_ref2) {
  var children = _ref2.children,
    style = _ref2.style,
    _ref2$edges = _ref2.edges,
    edges = _ref2$edges === void 0 ? ['top', 'bottom'] : _ref2$edges,
    _ref2$backgroundColor = _ref2.backgroundColor,
    backgroundColor = _ref2$backgroundColor === void 0 ? 'transparent' : _ref2$backgroundColor,
    testID = _ref2.testID;
  var insets = (0, _responsiveUtils.getSafeAreaInsets)();
  var paddingStyle = {
    paddingTop: edges.includes('top') ? insets.top : 0,
    paddingBottom: edges.includes('bottom') ? insets.bottom : 0,
    paddingLeft: edges.includes('left') ? insets.left : 0,
    paddingRight: edges.includes('right') ? insets.right : 0
  };
  return (0, _jsxRuntime.jsx)(_reactNative.View, {
    style: [styles.container, {
      backgroundColor: backgroundColor
    }, paddingStyle, style],
    testID: testID,
    children: children
  });
};
var SafeAreaScreen = exports.SafeAreaScreen = function SafeAreaScreen(_ref3) {
  var children = _ref3.children,
    style = _ref3.style,
    _ref3$backgroundColor = _ref3.backgroundColor,
    backgroundColor = _ref3$backgroundColor === void 0 ? '#FFFFFF' : _ref3$backgroundColor,
    _ref3$includeStatusBa = _ref3.includeStatusBar,
    includeStatusBar = _ref3$includeStatusBa === void 0 ? true : _ref3$includeStatusBa,
    _ref3$includeTabBar = _ref3.includeTabBar,
    includeTabBar = _ref3$includeTabBar === void 0 ? true : _ref3$includeTabBar,
    _ref3$statusBarStyle = _ref3.statusBarStyle,
    statusBarStyle = _ref3$statusBarStyle === void 0 ? 'dark-content' : _ref3$statusBarStyle,
    _ref3$respectNotch = _ref3.respectNotch,
    respectNotch = _ref3$respectNotch === void 0 ? true : _ref3$respectNotch,
    _ref3$respectGestures = _ref3.respectGestures,
    respectGestures = _ref3$respectGestures === void 0 ? true : _ref3$respectGestures,
    testID = _ref3.testID;
  var edges = [];
  if (includeStatusBar) edges.push('top');
  if (includeTabBar) edges.push('bottom');
  return (0, _jsxRuntime.jsx)(SafeAreaWrapper, {
    edges: edges,
    backgroundColor: backgroundColor,
    statusBarStyle: statusBarStyle,
    respectNotch: respectNotch,
    respectGestures: respectGestures,
    style: [styles.screen, style],
    testID: testID,
    children: children
  });
};
var SafeAreaModal = exports.SafeAreaModal = function SafeAreaModal(_ref4) {
  var children = _ref4.children,
    style = _ref4.style,
    _ref4$backgroundColor = _ref4.backgroundColor,
    backgroundColor = _ref4$backgroundColor === void 0 ? '#FFFFFF' : _ref4$backgroundColor,
    testID = _ref4.testID;
  return (0, _jsxRuntime.jsx)(SafeAreaWrapper, {
    edges: ['top', 'bottom'],
    backgroundColor: backgroundColor,
    style: [styles.modal, style],
    testID: testID,
    children: children
  });
};
var SafeAreaHeader = exports.SafeAreaHeader = function SafeAreaHeader(_ref5) {
  var children = _ref5.children,
    style = _ref5.style,
    _ref5$backgroundColor = _ref5.backgroundColor,
    backgroundColor = _ref5$backgroundColor === void 0 ? '#FFFFFF' : _ref5$backgroundColor,
    testID = _ref5.testID;
  return (0, _jsxRuntime.jsx)(SafeAreaWrapper, {
    edges: ['top'],
    backgroundColor: backgroundColor,
    style: [styles.header, style],
    testID: testID,
    children: children
  });
};
var useCustomSafeAreaInsets = exports.useCustomSafeAreaInsets = function useCustomSafeAreaInsets() {
  return (0, _responsiveUtils.getSafeAreaInsets)();
};
var useSafeAreaTop = exports.useSafeAreaTop = function useSafeAreaTop() {
  return (0, _responsiveUtils.getSafeAreaTop)();
};
var useSafeAreaBottom = exports.useSafeAreaBottom = function useSafeAreaBottom() {
  return (0, _responsiveUtils.getSafeAreaBottom)();
};
var styles = _reactNative.StyleSheet.create({
  container: {
    flex: 1
  },
  screen: {
    flex: 1
  },
  modal: Object.assign({
    flex: 1
  }, (0, _platformUtils.safePlatformSelect)({
    ios: {
      borderTopLeftRadius: 12,
      borderTopRightRadius: 12
    },
    android: {
      borderTopLeftRadius: 8,
      borderTopRightRadius: 8
    },
    default: {}
  })),
  header: Object.assign({}, (0, _platformUtils.safePlatformSelect)({
    ios: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 1
      },
      shadowOpacity: 0.1,
      shadowRadius: 2
    },
    android: {
      elevation: 2
    },
    default: {}
  }))
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfcmVhY3QiLCJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0IiwicmVxdWlyZSIsIl9yZWFjdE5hdGl2ZSIsIl9wbGF0Zm9ybVV0aWxzIiwiX3Jlc3BvbnNpdmVVdGlscyIsIl9qc3hSdW50aW1lIiwiU2FmZUFyZWFXcmFwcGVyIiwiZXhwb3J0cyIsIl9yZWYiLCJfZm9yY2VJbnNldHMkdG9wIiwiX2ZvcmNlSW5zZXRzJGJvdHRvbSIsIl9mb3JjZUluc2V0cyRsZWZ0IiwiX2ZvcmNlSW5zZXRzJHJpZ2h0IiwiY2hpbGRyZW4iLCJzdHlsZSIsIl9yZWYkZWRnZXMiLCJlZGdlcyIsIl9yZWYkbW9kZSIsIm1vZGUiLCJfcmVmJGJhY2tncm91bmRDb2xvciIsImJhY2tncm91bmRDb2xvciIsImZvcmNlSW5zZXRzIiwidGVzdElEIiwiX3JlZiRyZXNwZWN0Tm90Y2giLCJyZXNwZWN0Tm90Y2giLCJfcmVmJHJlc3BlY3RHZXN0dXJlcyIsInJlc3BlY3RHZXN0dXJlcyIsIl9yZWYkc3RhdHVzQmFyU3R5bGUiLCJzdGF0dXNCYXJTdHlsZSIsInN0YXR1c0JhckJhY2tncm91bmRDb2xvciIsImN1c3RvbVRvcFBhZGRpbmciLCJjdXN0b21Cb3R0b21QYWRkaW5nIiwiaW5zZXRzIiwiZ2V0U2FmZUFyZWFJbnNldHMiLCJnZXRFbmhhbmNlZFRvcEluc2V0IiwidW5kZWZpbmVkIiwiaXNJUGhvbmVXaXRoTm90Y2giLCJoYXNEeW5hbWljSXNsYW5kIiwiTWF0aCIsIm1heCIsInRvcCIsImhhc05vdGNoIiwiZ2V0RW5oYW5jZWRCb3R0b21JbnNldCIsImJvdHRvbSIsImlzQW5kcm9pZFdpdGhHZXN0dXJlcyIsImZpbmFsSW5zZXRzIiwibGVmdCIsInJpZ2h0IiwiZHluYW1pY1N0eWxlIiwiZm9yRWFjaCIsImVkZ2UiLCJpbnNldFZhbHVlIiwiY2hhckF0IiwidG9VcHBlckNhc2UiLCJzbGljZSIsImpzeHMiLCJWaWV3Iiwic3R5bGVzIiwiY29udGFpbmVyIiwiUGxhdGZvcm0iLCJPUyIsImpzeCIsIlN0YXR1c0JhciIsImJhclN0eWxlIiwidHJhbnNsdWNlbnQiLCJFbmhhbmNlZFNhZmVBcmVhVmlldyIsIl9yZWYyIiwiX3JlZjIkZWRnZXMiLCJfcmVmMiRiYWNrZ3JvdW5kQ29sb3IiLCJwYWRkaW5nU3R5bGUiLCJwYWRkaW5nVG9wIiwiaW5jbHVkZXMiLCJwYWRkaW5nQm90dG9tIiwicGFkZGluZ0xlZnQiLCJwYWRkaW5nUmlnaHQiLCJTYWZlQXJlYVNjcmVlbiIsIl9yZWYzIiwiX3JlZjMkYmFja2dyb3VuZENvbG9yIiwiX3JlZjMkaW5jbHVkZVN0YXR1c0JhIiwiaW5jbHVkZVN0YXR1c0JhciIsIl9yZWYzJGluY2x1ZGVUYWJCYXIiLCJpbmNsdWRlVGFiQmFyIiwiX3JlZjMkc3RhdHVzQmFyU3R5bGUiLCJfcmVmMyRyZXNwZWN0Tm90Y2giLCJfcmVmMyRyZXNwZWN0R2VzdHVyZXMiLCJwdXNoIiwic2NyZWVuIiwiU2FmZUFyZWFNb2RhbCIsIl9yZWY0IiwiX3JlZjQkYmFja2dyb3VuZENvbG9yIiwibW9kYWwiLCJTYWZlQXJlYUhlYWRlciIsIl9yZWY1IiwiX3JlZjUkYmFja2dyb3VuZENvbG9yIiwiaGVhZGVyIiwidXNlQ3VzdG9tU2FmZUFyZWFJbnNldHMiLCJ1c2VTYWZlQXJlYVRvcCIsImdldFNhZmVBcmVhVG9wIiwidXNlU2FmZUFyZWFCb3R0b20iLCJnZXRTYWZlQXJlYUJvdHRvbSIsIlN0eWxlU2hlZXQiLCJjcmVhdGUiLCJmbGV4IiwiT2JqZWN0IiwiYXNzaWduIiwic2FmZVBsYXRmb3JtU2VsZWN0IiwiaW9zIiwiYm9yZGVyVG9wTGVmdFJhZGl1cyIsImJvcmRlclRvcFJpZ2h0UmFkaXVzIiwiYW5kcm9pZCIsImRlZmF1bHQiLCJzaGFkb3dDb2xvciIsInNoYWRvd09mZnNldCIsIndpZHRoIiwiaGVpZ2h0Iiwic2hhZG93T3BhY2l0eSIsInNoYWRvd1JhZGl1cyIsImVsZXZhdGlvbiJdLCJzb3VyY2VzIjpbIlNhZmVBcmVhV3JhcHBlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBTYWZlQXJlYVdyYXBwZXIgLSBFbmhhbmNlZCBTYWZlIEFyZWEgTWFuYWdlbWVudFxuICpcbiAqIENvbXBvbmVudCBDb250cmFjdDpcbiAqIC0gUHJvdmlkZXMgY29uc2lzdGVudCBzYWZlIGFyZWEgaGFuZGxpbmcgYWNyb3NzIGlPUyBhbmQgQW5kcm9pZFxuICogLSBSZXNwZWN0cyBkZXZpY2Ugbm90Y2hlcywgc3RhdHVzIGJhcnMsIGFuZCBuYXZpZ2F0aW9uIGJhcnNcbiAqIC0gT2ZmZXJzIGZsZXhpYmxlIGNvbmZpZ3VyYXRpb24gZm9yIGRpZmZlcmVudCBzY3JlZW4gdHlwZXNcbiAqIC0gSW50ZWdyYXRlcyB3aXRoIHJlc3BvbnNpdmUgdXRpbGl0aWVzIGZvciBvcHRpbWFsIHNwYWNpbmdcbiAqIC0gU3VwcG9ydHMgY3VzdG9tIGVkZ2UgY29uZmlndXJhdGlvbnNcbiAqXG4gKiBAdmVyc2lvbiAxLjAuMFxuICogQGF1dGhvciBWaWVybGEgRGV2ZWxvcG1lbnQgVGVhbVxuICovXG5cbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBWaWV3LCBQbGF0Zm9ybSwgVmlld1N0eWxlLCBTdHlsZVNoZWV0LCBTdGF0dXNCYXIgfSBmcm9tICdyZWFjdC1uYXRpdmUnO1xuXG5pbXBvcnQgeyBzYWZlUGxhdGZvcm1TZWxlY3QgfSBmcm9tICcuLi8uLi91dGlscy9wbGF0Zm9ybVV0aWxzJztcbmltcG9ydCB7XG4gIGdldFNhZmVBcmVhSW5zZXRzLFxuICBnZXRTYWZlQXJlYVRvcCxcbiAgZ2V0U2FmZUFyZWFCb3R0b20sXG4gIGlzSVBob25lV2l0aE5vdGNoLFxuICBpc0FuZHJvaWRXaXRoR2VzdHVyZXMsXG4gIGhhc05vdGNoLFxuICBoYXNEeW5hbWljSXNsYW5kLFxufSBmcm9tICcuLi8uLi91dGlscy9yZXNwb25zaXZlVXRpbHMnO1xuXG5pbnRlcmZhY2UgU2FmZUFyZWFXcmFwcGVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xuICBzdHlsZT86IFZpZXdTdHlsZTtcbiAgZWRnZXM/OiAoJ3RvcCcgfCAnYm90dG9tJyB8ICdsZWZ0JyB8ICdyaWdodCcpW107XG4gIG1vZGU/OiAncGFkZGluZycgfCAnbWFyZ2luJztcbiAgYmFja2dyb3VuZENvbG9yPzogc3RyaW5nO1xuICBmb3JjZUluc2V0cz86IHtcbiAgICB0b3A/OiBudW1iZXI7XG4gICAgYm90dG9tPzogbnVtYmVyO1xuICAgIGxlZnQ/OiBudW1iZXI7XG4gICAgcmlnaHQ/OiBudW1iZXI7XG4gIH07XG4gIHRlc3RJRD86IHN0cmluZztcbiAgLy8gRW5oYW5jZWQgZGV2aWNlIGNvbXBhdGliaWxpdHkgcHJvcHNcbiAgcmVzcGVjdE5vdGNoPzogYm9vbGVhbjtcbiAgcmVzcGVjdEdlc3R1cmVzPzogYm9vbGVhbjtcbiAgc3RhdHVzQmFyU3R5bGU/OiAnbGlnaHQtY29udGVudCcgfCAnZGFyay1jb250ZW50JztcbiAgc3RhdHVzQmFyQmFja2dyb3VuZENvbG9yPzogc3RyaW5nO1xuICBjdXN0b21Ub3BQYWRkaW5nPzogbnVtYmVyO1xuICBjdXN0b21Cb3R0b21QYWRkaW5nPzogbnVtYmVyO1xufVxuXG5leHBvcnQgY29uc3QgU2FmZUFyZWFXcmFwcGVyOiBSZWFjdC5GQzxTYWZlQXJlYVdyYXBwZXJQcm9wcz4gPSAoe1xuICBjaGlsZHJlbixcbiAgc3R5bGUsXG4gIGVkZ2VzID0gWyd0b3AnLCAnYm90dG9tJ10sXG4gIG1vZGUgPSAncGFkZGluZycsXG4gIGJhY2tncm91bmRDb2xvciA9ICd0cmFuc3BhcmVudCcsXG4gIGZvcmNlSW5zZXRzLFxuICB0ZXN0SUQsXG4gIHJlc3BlY3ROb3RjaCA9IHRydWUsXG4gIHJlc3BlY3RHZXN0dXJlcyA9IHRydWUsXG4gIHN0YXR1c0JhclN0eWxlID0gJ2RhcmstY29udGVudCcsXG4gIHN0YXR1c0JhckJhY2tncm91bmRDb2xvcixcbiAgY3VzdG9tVG9wUGFkZGluZyxcbiAgY3VzdG9tQm90dG9tUGFkZGluZyxcbn0pID0+IHtcbiAgLy8gR2V0IHNhZmUgYXJlYSBpbnNldHMgd2l0aCBlbmhhbmNlZCBkZXZpY2UgZGV0ZWN0aW9uXG4gIGNvbnN0IGluc2V0cyA9IGdldFNhZmVBcmVhSW5zZXRzKCk7XG5cbiAgLy8gRW5oYW5jZWQgZGV2aWNlLXNwZWNpZmljIGNhbGN1bGF0aW9uc1xuICBjb25zdCBnZXRFbmhhbmNlZFRvcEluc2V0ID0gKCkgPT4ge1xuICAgIGlmIChjdXN0b21Ub3BQYWRkaW5nICE9PSB1bmRlZmluZWQpIHJldHVybiBjdXN0b21Ub3BQYWRkaW5nO1xuXG4gICAgaWYgKHJlc3BlY3ROb3RjaCAmJiBpc0lQaG9uZVdpdGhOb3RjaCgpKSB7XG4gICAgICAvLyBFbnN1cmUgbWluaW11bSBwYWRkaW5nIGZvciBpUGhvbmUgbm90Y2gvRHluYW1pYyBJc2xhbmRcbiAgICAgIGlmIChoYXNEeW5hbWljSXNsYW5kKCkpIHJldHVybiBNYXRoLm1heChpbnNldHMudG9wLCA1OSk7XG4gICAgICBpZiAoaGFzTm90Y2goKSkgcmV0dXJuIE1hdGgubWF4KGluc2V0cy50b3AsIDQ0KTtcbiAgICB9XG5cbiAgICByZXR1cm4gaW5zZXRzLnRvcDtcbiAgfTtcblxuICBjb25zdCBnZXRFbmhhbmNlZEJvdHRvbUluc2V0ID0gKCkgPT4ge1xuICAgIGlmIChjdXN0b21Cb3R0b21QYWRkaW5nICE9PSB1bmRlZmluZWQpIHJldHVybiBjdXN0b21Cb3R0b21QYWRkaW5nO1xuXG4gICAgaWYgKHJlc3BlY3RHZXN0dXJlcykge1xuICAgICAgaWYgKGlzSVBob25lV2l0aE5vdGNoKCkpIHJldHVybiBNYXRoLm1heChpbnNldHMuYm90dG9tLCAzNCk7XG4gICAgICBpZiAoaXNBbmRyb2lkV2l0aEdlc3R1cmVzKCkpIHJldHVybiBNYXRoLm1heChpbnNldHMuYm90dG9tLCAxNik7XG4gICAgfVxuXG4gICAgcmV0dXJuIGluc2V0cy5ib3R0b207XG4gIH07XG5cbiAgLy8gQXBwbHkgZm9yY2VkIGluc2V0cyBpZiBwcm92aWRlZCwgb3RoZXJ3aXNlIHVzZSBlbmhhbmNlZCBjYWxjdWxhdGlvbnNcbiAgY29uc3QgZmluYWxJbnNldHMgPSB7XG4gICAgdG9wOiBmb3JjZUluc2V0cz8udG9wID8/IGdldEVuaGFuY2VkVG9wSW5zZXQoKSxcbiAgICBib3R0b206IGZvcmNlSW5zZXRzPy5ib3R0b20gPz8gZ2V0RW5oYW5jZWRCb3R0b21JbnNldCgpLFxuICAgIGxlZnQ6IGZvcmNlSW5zZXRzPy5sZWZ0ID8/IGluc2V0cy5sZWZ0LFxuICAgIHJpZ2h0OiBmb3JjZUluc2V0cz8ucmlnaHQgPz8gaW5zZXRzLnJpZ2h0LFxuICB9O1xuXG4gIC8vIENyZWF0ZSBkeW5hbWljIHN0eWxlcyBiYXNlZCBvbiBlZGdlcyBhbmQgbW9kZVxuICBjb25zdCBkeW5hbWljU3R5bGU6IFZpZXdTdHlsZSA9IHt9O1xuXG4gIGVkZ2VzLmZvckVhY2goZWRnZSA9PiB7XG4gICAgY29uc3QgaW5zZXRWYWx1ZSA9IGZpbmFsSW5zZXRzW2VkZ2VdO1xuICAgIGlmIChpbnNldFZhbHVlID4gMCkge1xuICAgICAgaWYgKG1vZGUgPT09ICdwYWRkaW5nJykge1xuICAgICAgICBkeW5hbWljU3R5bGVbXG4gICAgICAgICAgYHBhZGRpbmcke2VkZ2UuY2hhckF0KDApLnRvVXBwZXJDYXNlKCkgKyBlZGdlLnNsaWNlKDEpfWAgYXMga2V5b2YgVmlld1N0eWxlXG4gICAgICAgIF0gPSBpbnNldFZhbHVlO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgZHluYW1pY1N0eWxlW1xuICAgICAgICAgIGBtYXJnaW4ke2VkZ2UuY2hhckF0KDApLnRvVXBwZXJDYXNlKCkgKyBlZGdlLnNsaWNlKDEpfWAgYXMga2V5b2YgVmlld1N0eWxlXG4gICAgICAgIF0gPSBpbnNldFZhbHVlO1xuICAgICAgfVxuICAgIH1cbiAgfSk7XG5cbiAgcmV0dXJuIChcbiAgICA8Vmlld1xuICAgICAgc3R5bGU9e1tzdHlsZXMuY29udGFpbmVyLCB7IGJhY2tncm91bmRDb2xvciB9LCBkeW5hbWljU3R5bGUsIHN0eWxlXX1cbiAgICAgIHRlc3RJRD17dGVzdElEfT5cbiAgICAgIHtQbGF0Zm9ybS5PUyAhPT0gJ3dlYicgJiYgKFxuICAgICAgICA8U3RhdHVzQmFyXG4gICAgICAgICAgYmFyU3R5bGU9e3N0YXR1c0JhclN0eWxlfVxuICAgICAgICAgIGJhY2tncm91bmRDb2xvcj17c3RhdHVzQmFyQmFja2dyb3VuZENvbG9yIHx8IGJhY2tncm91bmRDb2xvcn1cbiAgICAgICAgICB0cmFuc2x1Y2VudD17UGxhdGZvcm0uT1MgPT09ICdhbmRyb2lkJ31cbiAgICAgICAgLz5cbiAgICAgICl9XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9WaWV3PlxuICApO1xufTtcblxuLy8gQWx0ZXJuYXRpdmUgY29tcG9uZW50IHVzaW5nIG5hdGl2ZSBWaWV3IHdpdGggY2FsY3VsYXRlZCBzYWZlIGFyZWFzXG5leHBvcnQgY29uc3QgRW5oYW5jZWRTYWZlQXJlYVZpZXc6IFJlYWN0LkZDPFNhZmVBcmVhV3JhcHBlclByb3BzPiA9ICh7XG4gIGNoaWxkcmVuLFxuICBzdHlsZSxcbiAgZWRnZXMgPSBbJ3RvcCcsICdib3R0b20nXSxcbiAgYmFja2dyb3VuZENvbG9yID0gJ3RyYW5zcGFyZW50JyxcbiAgdGVzdElELFxufSkgPT4ge1xuICBjb25zdCBpbnNldHMgPSBnZXRTYWZlQXJlYUluc2V0cygpO1xuXG4gIGNvbnN0IHBhZGRpbmdTdHlsZSA9IHtcbiAgICBwYWRkaW5nVG9wOiBlZGdlcy5pbmNsdWRlcygndG9wJykgPyBpbnNldHMudG9wIDogMCxcbiAgICBwYWRkaW5nQm90dG9tOiBlZGdlcy5pbmNsdWRlcygnYm90dG9tJykgPyBpbnNldHMuYm90dG9tIDogMCxcbiAgICBwYWRkaW5nTGVmdDogZWRnZXMuaW5jbHVkZXMoJ2xlZnQnKSA/IGluc2V0cy5sZWZ0IDogMCxcbiAgICBwYWRkaW5nUmlnaHQ6IGVkZ2VzLmluY2x1ZGVzKCdyaWdodCcpID8gaW5zZXRzLnJpZ2h0IDogMCxcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxWaWV3XG4gICAgICBzdHlsZT17W3N0eWxlcy5jb250YWluZXIsIHsgYmFja2dyb3VuZENvbG9yIH0sIHBhZGRpbmdTdHlsZSwgc3R5bGVdfVxuICAgICAgdGVzdElEPXt0ZXN0SUR9PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvVmlldz5cbiAgKTtcbn07XG5cbi8vIFNwZWNpYWxpemVkIGNvbXBvbmVudHMgZm9yIGNvbW1vbiB1c2UgY2FzZXNcbmV4cG9ydCBjb25zdCBTYWZlQXJlYVNjcmVlbjogUmVhY3QuRkM8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xuICBzdHlsZT86IFZpZXdTdHlsZTtcbiAgYmFja2dyb3VuZENvbG9yPzogc3RyaW5nO1xuICBpbmNsdWRlU3RhdHVzQmFyPzogYm9vbGVhbjtcbiAgaW5jbHVkZVRhYkJhcj86IGJvb2xlYW47XG4gIHN0YXR1c0JhclN0eWxlPzogJ2xpZ2h0LWNvbnRlbnQnIHwgJ2RhcmstY29udGVudCc7XG4gIHJlc3BlY3ROb3RjaD86IGJvb2xlYW47XG4gIHJlc3BlY3RHZXN0dXJlcz86IGJvb2xlYW47XG4gIHRlc3RJRD86IHN0cmluZztcbn0+ID0gKHtcbiAgY2hpbGRyZW4sXG4gIHN0eWxlLFxuICBiYWNrZ3JvdW5kQ29sb3IgPSAnI0ZGRkZGRicsXG4gIGluY2x1ZGVTdGF0dXNCYXIgPSB0cnVlLFxuICBpbmNsdWRlVGFiQmFyID0gdHJ1ZSxcbiAgc3RhdHVzQmFyU3R5bGUgPSAnZGFyay1jb250ZW50JyxcbiAgcmVzcGVjdE5vdGNoID0gdHJ1ZSxcbiAgcmVzcGVjdEdlc3R1cmVzID0gdHJ1ZSxcbiAgdGVzdElELFxufSkgPT4ge1xuICBjb25zdCBlZGdlczogKCd0b3AnIHwgJ2JvdHRvbScgfCAnbGVmdCcgfCAncmlnaHQnKVtdID0gW107XG5cbiAgaWYgKGluY2x1ZGVTdGF0dXNCYXIpIGVkZ2VzLnB1c2goJ3RvcCcpO1xuICBpZiAoaW5jbHVkZVRhYkJhcikgZWRnZXMucHVzaCgnYm90dG9tJyk7XG5cbiAgcmV0dXJuIChcbiAgICA8U2FmZUFyZWFXcmFwcGVyXG4gICAgICBlZGdlcz17ZWRnZXN9XG4gICAgICBiYWNrZ3JvdW5kQ29sb3I9e2JhY2tncm91bmRDb2xvcn1cbiAgICAgIHN0YXR1c0JhclN0eWxlPXtzdGF0dXNCYXJTdHlsZX1cbiAgICAgIHJlc3BlY3ROb3RjaD17cmVzcGVjdE5vdGNofVxuICAgICAgcmVzcGVjdEdlc3R1cmVzPXtyZXNwZWN0R2VzdHVyZXN9XG4gICAgICBzdHlsZT17W3N0eWxlcy5zY3JlZW4sIHN0eWxlXX1cbiAgICAgIHRlc3RJRD17dGVzdElEfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1NhZmVBcmVhV3JhcHBlcj5cbiAgKTtcbn07XG5cbi8vIE1vZGFsIHNhZmUgYXJlYSBjb21wb25lbnRcbmV4cG9ydCBjb25zdCBTYWZlQXJlYU1vZGFsOiBSZWFjdC5GQzx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG4gIHN0eWxlPzogVmlld1N0eWxlO1xuICBiYWNrZ3JvdW5kQ29sb3I/OiBzdHJpbmc7XG4gIHRlc3RJRD86IHN0cmluZztcbn0+ID0gKHsgY2hpbGRyZW4sIHN0eWxlLCBiYWNrZ3JvdW5kQ29sb3IgPSAnI0ZGRkZGRicsIHRlc3RJRCB9KSA9PiB7XG4gIHJldHVybiAoXG4gICAgPFNhZmVBcmVhV3JhcHBlclxuICAgICAgZWRnZXM9e1sndG9wJywgJ2JvdHRvbSddfVxuICAgICAgYmFja2dyb3VuZENvbG9yPXtiYWNrZ3JvdW5kQ29sb3J9XG4gICAgICBzdHlsZT17W3N0eWxlcy5tb2RhbCwgc3R5bGVdfVxuICAgICAgdGVzdElEPXt0ZXN0SUR9PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvU2FmZUFyZWFXcmFwcGVyPlxuICApO1xufTtcblxuLy8gSGVhZGVyIHNhZmUgYXJlYSBjb21wb25lbnRcbmV4cG9ydCBjb25zdCBTYWZlQXJlYUhlYWRlcjogUmVhY3QuRkM8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xuICBzdHlsZT86IFZpZXdTdHlsZTtcbiAgYmFja2dyb3VuZENvbG9yPzogc3RyaW5nO1xuICB0ZXN0SUQ/OiBzdHJpbmc7XG59PiA9ICh7IGNoaWxkcmVuLCBzdHlsZSwgYmFja2dyb3VuZENvbG9yID0gJyNGRkZGRkYnLCB0ZXN0SUQgfSkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxTYWZlQXJlYVdyYXBwZXJcbiAgICAgIGVkZ2VzPXtbJ3RvcCddfVxuICAgICAgYmFja2dyb3VuZENvbG9yPXtiYWNrZ3JvdW5kQ29sb3J9XG4gICAgICBzdHlsZT17W3N0eWxlcy5oZWFkZXIsIHN0eWxlXX1cbiAgICAgIHRlc3RJRD17dGVzdElEfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1NhZmVBcmVhV3JhcHBlcj5cbiAgKTtcbn07XG5cbi8vIFV0aWxpdHkgaG9va3MgZm9yIHNhZmUgYXJlYSB2YWx1ZXNcbmV4cG9ydCBjb25zdCB1c2VDdXN0b21TYWZlQXJlYUluc2V0cyA9ICgpID0+IHtcbiAgcmV0dXJuIGdldFNhZmVBcmVhSW5zZXRzKCk7XG59O1xuXG5leHBvcnQgY29uc3QgdXNlU2FmZUFyZWFUb3AgPSAoKSA9PiB7XG4gIHJldHVybiBnZXRTYWZlQXJlYVRvcCgpO1xufTtcblxuZXhwb3J0IGNvbnN0IHVzZVNhZmVBcmVhQm90dG9tID0gKCkgPT4ge1xuICByZXR1cm4gZ2V0U2FmZUFyZWFCb3R0b20oKTtcbn07XG5cbmNvbnN0IHN0eWxlcyA9IFN0eWxlU2hlZXQuY3JlYXRlKHtcbiAgY29udGFpbmVyOiB7XG4gICAgZmxleDogMSxcbiAgfSxcbiAgc2NyZWVuOiB7XG4gICAgZmxleDogMSxcbiAgfSxcbiAgbW9kYWw6IHtcbiAgICBmbGV4OiAxLFxuICAgIC4uLnNhZmVQbGF0Zm9ybVNlbGVjdCh7XG4gICAgICBpb3M6IHtcbiAgICAgICAgYm9yZGVyVG9wTGVmdFJhZGl1czogMTIsXG4gICAgICAgIGJvcmRlclRvcFJpZ2h0UmFkaXVzOiAxMixcbiAgICAgIH0sXG4gICAgICBhbmRyb2lkOiB7XG4gICAgICAgIGJvcmRlclRvcExlZnRSYWRpdXM6IDgsXG4gICAgICAgIGJvcmRlclRvcFJpZ2h0UmFkaXVzOiA4LFxuICAgICAgfSxcbiAgICAgIGRlZmF1bHQ6IHt9LFxuICAgIH0pLFxuICB9LFxuICBoZWFkZXI6IHtcbiAgICAuLi5zYWZlUGxhdGZvcm1TZWxlY3Qoe1xuICAgICAgaW9zOiB7XG4gICAgICAgIHNoYWRvd0NvbG9yOiAnIzAwMCcsXG4gICAgICAgIHNoYWRvd09mZnNldDogeyB3aWR0aDogMCwgaGVpZ2h0OiAxIH0sXG4gICAgICAgIHNoYWRvd09wYWNpdHk6IDAuMSxcbiAgICAgICAgc2hhZG93UmFkaXVzOiAyLFxuICAgICAgfSxcbiAgICAgIGFuZHJvaWQ6IHtcbiAgICAgICAgZWxldmF0aW9uOiAyLFxuICAgICAgfSxcbiAgICAgIGRlZmF1bHQ6IHt9LFxuICAgIH0pLFxuICB9LFxufSk7XG4iXSwibWFwcGluZ3MiOiI7Ozs7O0FBY0EsSUFBQUEsTUFBQSxHQUFBQyxzQkFBQSxDQUFBQyxPQUFBO0FBQ0EsSUFBQUMsWUFBQSxHQUFBRCxPQUFBO0FBRUEsSUFBQUUsY0FBQSxHQUFBRixPQUFBO0FBQ0EsSUFBQUcsZ0JBQUEsR0FBQUgsT0FBQTtBQVFxQyxJQUFBSSxXQUFBLEdBQUFKLE9BQUE7QUF3QjlCLElBQU1LLGVBQStDLEdBQUFDLE9BQUEsQ0FBQUQsZUFBQSxHQUFHLFNBQWxEQSxlQUErQ0EsQ0FBQUUsSUFBQSxFQWN0RDtFQUFBLElBQUFDLGdCQUFBLEVBQUFDLG1CQUFBLEVBQUFDLGlCQUFBLEVBQUFDLGtCQUFBO0VBQUEsSUFiSkMsUUFBUSxHQUFBTCxJQUFBLENBQVJLLFFBQVE7SUFDUkMsS0FBSyxHQUFBTixJQUFBLENBQUxNLEtBQUs7SUFBQUMsVUFBQSxHQUFBUCxJQUFBLENBQ0xRLEtBQUs7SUFBTEEsS0FBSyxHQUFBRCxVQUFBLGNBQUcsQ0FBQyxLQUFLLEVBQUUsUUFBUSxDQUFDLEdBQUFBLFVBQUE7SUFBQUUsU0FBQSxHQUFBVCxJQUFBLENBQ3pCVSxJQUFJO0lBQUpBLElBQUksR0FBQUQsU0FBQSxjQUFHLFNBQVMsR0FBQUEsU0FBQTtJQUFBRSxvQkFBQSxHQUFBWCxJQUFBLENBQ2hCWSxlQUFlO0lBQWZBLGVBQWUsR0FBQUQsb0JBQUEsY0FBRyxhQUFhLEdBQUFBLG9CQUFBO0lBQy9CRSxXQUFXLEdBQUFiLElBQUEsQ0FBWGEsV0FBVztJQUNYQyxNQUFNLEdBQUFkLElBQUEsQ0FBTmMsTUFBTTtJQUFBQyxpQkFBQSxHQUFBZixJQUFBLENBQ05nQixZQUFZO0lBQVpBLFlBQVksR0FBQUQsaUJBQUEsY0FBRyxJQUFJLEdBQUFBLGlCQUFBO0lBQUFFLG9CQUFBLEdBQUFqQixJQUFBLENBQ25Ca0IsZUFBZTtJQUFmQSxlQUFlLEdBQUFELG9CQUFBLGNBQUcsSUFBSSxHQUFBQSxvQkFBQTtJQUFBRSxtQkFBQSxHQUFBbkIsSUFBQSxDQUN0Qm9CLGNBQWM7SUFBZEEsY0FBYyxHQUFBRCxtQkFBQSxjQUFHLGNBQWMsR0FBQUEsbUJBQUE7SUFDL0JFLHdCQUF3QixHQUFBckIsSUFBQSxDQUF4QnFCLHdCQUF3QjtJQUN4QkMsZ0JBQWdCLEdBQUF0QixJQUFBLENBQWhCc0IsZ0JBQWdCO0lBQ2hCQyxtQkFBbUIsR0FBQXZCLElBQUEsQ0FBbkJ1QixtQkFBbUI7RUFHbkIsSUFBTUMsTUFBTSxHQUFHLElBQUFDLGtDQUFpQixFQUFDLENBQUM7RUFHbEMsSUFBTUMsbUJBQW1CLEdBQUcsU0FBdEJBLG1CQUFtQkEsQ0FBQSxFQUFTO0lBQ2hDLElBQUlKLGdCQUFnQixLQUFLSyxTQUFTLEVBQUUsT0FBT0wsZ0JBQWdCO0lBRTNELElBQUlOLFlBQVksSUFBSSxJQUFBWSxrQ0FBaUIsRUFBQyxDQUFDLEVBQUU7TUFFdkMsSUFBSSxJQUFBQyxpQ0FBZ0IsRUFBQyxDQUFDLEVBQUUsT0FBT0MsSUFBSSxDQUFDQyxHQUFHLENBQUNQLE1BQU0sQ0FBQ1EsR0FBRyxFQUFFLEVBQUUsQ0FBQztNQUN2RCxJQUFJLElBQUFDLHlCQUFRLEVBQUMsQ0FBQyxFQUFFLE9BQU9ILElBQUksQ0FBQ0MsR0FBRyxDQUFDUCxNQUFNLENBQUNRLEdBQUcsRUFBRSxFQUFFLENBQUM7SUFDakQ7SUFFQSxPQUFPUixNQUFNLENBQUNRLEdBQUc7RUFDbkIsQ0FBQztFQUVELElBQU1FLHNCQUFzQixHQUFHLFNBQXpCQSxzQkFBc0JBLENBQUEsRUFBUztJQUNuQyxJQUFJWCxtQkFBbUIsS0FBS0ksU0FBUyxFQUFFLE9BQU9KLG1CQUFtQjtJQUVqRSxJQUFJTCxlQUFlLEVBQUU7TUFDbkIsSUFBSSxJQUFBVSxrQ0FBaUIsRUFBQyxDQUFDLEVBQUUsT0FBT0UsSUFBSSxDQUFDQyxHQUFHLENBQUNQLE1BQU0sQ0FBQ1csTUFBTSxFQUFFLEVBQUUsQ0FBQztNQUMzRCxJQUFJLElBQUFDLHNDQUFxQixFQUFDLENBQUMsRUFBRSxPQUFPTixJQUFJLENBQUNDLEdBQUcsQ0FBQ1AsTUFBTSxDQUFDVyxNQUFNLEVBQUUsRUFBRSxDQUFDO0lBQ2pFO0lBRUEsT0FBT1gsTUFBTSxDQUFDVyxNQUFNO0VBQ3RCLENBQUM7RUFHRCxJQUFNRSxXQUFXLEdBQUc7SUFDbEJMLEdBQUcsR0FBQS9CLGdCQUFBLEdBQUVZLFdBQVcsb0JBQVhBLFdBQVcsQ0FBRW1CLEdBQUcsWUFBQS9CLGdCQUFBLEdBQUl5QixtQkFBbUIsQ0FBQyxDQUFDO0lBQzlDUyxNQUFNLEdBQUFqQyxtQkFBQSxHQUFFVyxXQUFXLG9CQUFYQSxXQUFXLENBQUVzQixNQUFNLFlBQUFqQyxtQkFBQSxHQUFJZ0Msc0JBQXNCLENBQUMsQ0FBQztJQUN2REksSUFBSSxHQUFBbkMsaUJBQUEsR0FBRVUsV0FBVyxvQkFBWEEsV0FBVyxDQUFFeUIsSUFBSSxZQUFBbkMsaUJBQUEsR0FBSXFCLE1BQU0sQ0FBQ2MsSUFBSTtJQUN0Q0MsS0FBSyxHQUFBbkMsa0JBQUEsR0FBRVMsV0FBVyxvQkFBWEEsV0FBVyxDQUFFMEIsS0FBSyxZQUFBbkMsa0JBQUEsR0FBSW9CLE1BQU0sQ0FBQ2U7RUFDdEMsQ0FBQztFQUdELElBQU1DLFlBQXVCLEdBQUcsQ0FBQyxDQUFDO0VBRWxDaEMsS0FBSyxDQUFDaUMsT0FBTyxDQUFDLFVBQUFDLElBQUksRUFBSTtJQUNwQixJQUFNQyxVQUFVLEdBQUdOLFdBQVcsQ0FBQ0ssSUFBSSxDQUFDO0lBQ3BDLElBQUlDLFVBQVUsR0FBRyxDQUFDLEVBQUU7TUFDbEIsSUFBSWpDLElBQUksS0FBSyxTQUFTLEVBQUU7UUFDdEI4QixZQUFZLENBQ1YsVUFBVUUsSUFBSSxDQUFDRSxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUNDLFdBQVcsQ0FBQyxDQUFDLEdBQUdILElBQUksQ0FBQ0ksS0FBSyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQ3pELEdBQUdILFVBQVU7TUFDaEIsQ0FBQyxNQUFNO1FBQ0xILFlBQVksQ0FDVixTQUFTRSxJQUFJLENBQUNFLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQ0MsV0FBVyxDQUFDLENBQUMsR0FBR0gsSUFBSSxDQUFDSSxLQUFLLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FDeEQsR0FBR0gsVUFBVTtNQUNoQjtJQUNGO0VBQ0YsQ0FBQyxDQUFDO0VBRUYsT0FDRSxJQUFBOUMsV0FBQSxDQUFBa0QsSUFBQSxFQUFDckQsWUFBQSxDQUFBc0QsSUFBSTtJQUNIMUMsS0FBSyxFQUFFLENBQUMyQyxNQUFNLENBQUNDLFNBQVMsRUFBRTtNQUFFdEMsZUFBZSxFQUFmQTtJQUFnQixDQUFDLEVBQUU0QixZQUFZLEVBQUVsQyxLQUFLLENBQUU7SUFDcEVRLE1BQU0sRUFBRUEsTUFBTztJQUFBVCxRQUFBLEdBQ2Q4QyxxQkFBUSxDQUFDQyxFQUFFLEtBQUssS0FBSyxJQUNwQixJQUFBdkQsV0FBQSxDQUFBd0QsR0FBQSxFQUFDM0QsWUFBQSxDQUFBNEQsU0FBUztNQUNSQyxRQUFRLEVBQUVuQyxjQUFlO01BQ3pCUixlQUFlLEVBQUVTLHdCQUF3QixJQUFJVCxlQUFnQjtNQUM3RDRDLFdBQVcsRUFBRUwscUJBQVEsQ0FBQ0MsRUFBRSxLQUFLO0lBQVUsQ0FDeEMsQ0FDRixFQUNBL0MsUUFBUTtFQUFBLENBQ0wsQ0FBQztBQUVYLENBQUM7QUFHTSxJQUFNb0Qsb0JBQW9ELEdBQUExRCxPQUFBLENBQUEwRCxvQkFBQSxHQUFHLFNBQXZEQSxvQkFBb0RBLENBQUFDLEtBQUEsRUFNM0Q7RUFBQSxJQUxKckQsUUFBUSxHQUFBcUQsS0FBQSxDQUFSckQsUUFBUTtJQUNSQyxLQUFLLEdBQUFvRCxLQUFBLENBQUxwRCxLQUFLO0lBQUFxRCxXQUFBLEdBQUFELEtBQUEsQ0FDTGxELEtBQUs7SUFBTEEsS0FBSyxHQUFBbUQsV0FBQSxjQUFHLENBQUMsS0FBSyxFQUFFLFFBQVEsQ0FBQyxHQUFBQSxXQUFBO0lBQUFDLHFCQUFBLEdBQUFGLEtBQUEsQ0FDekI5QyxlQUFlO0lBQWZBLGVBQWUsR0FBQWdELHFCQUFBLGNBQUcsYUFBYSxHQUFBQSxxQkFBQTtJQUMvQjlDLE1BQU0sR0FBQTRDLEtBQUEsQ0FBTjVDLE1BQU07RUFFTixJQUFNVSxNQUFNLEdBQUcsSUFBQUMsa0NBQWlCLEVBQUMsQ0FBQztFQUVsQyxJQUFNb0MsWUFBWSxHQUFHO0lBQ25CQyxVQUFVLEVBQUV0RCxLQUFLLENBQUN1RCxRQUFRLENBQUMsS0FBSyxDQUFDLEdBQUd2QyxNQUFNLENBQUNRLEdBQUcsR0FBRyxDQUFDO0lBQ2xEZ0MsYUFBYSxFQUFFeEQsS0FBSyxDQUFDdUQsUUFBUSxDQUFDLFFBQVEsQ0FBQyxHQUFHdkMsTUFBTSxDQUFDVyxNQUFNLEdBQUcsQ0FBQztJQUMzRDhCLFdBQVcsRUFBRXpELEtBQUssQ0FBQ3VELFFBQVEsQ0FBQyxNQUFNLENBQUMsR0FBR3ZDLE1BQU0sQ0FBQ2MsSUFBSSxHQUFHLENBQUM7SUFDckQ0QixZQUFZLEVBQUUxRCxLQUFLLENBQUN1RCxRQUFRLENBQUMsT0FBTyxDQUFDLEdBQUd2QyxNQUFNLENBQUNlLEtBQUssR0FBRztFQUN6RCxDQUFDO0VBRUQsT0FDRSxJQUFBMUMsV0FBQSxDQUFBd0QsR0FBQSxFQUFDM0QsWUFBQSxDQUFBc0QsSUFBSTtJQUNIMUMsS0FBSyxFQUFFLENBQUMyQyxNQUFNLENBQUNDLFNBQVMsRUFBRTtNQUFFdEMsZUFBZSxFQUFmQTtJQUFnQixDQUFDLEVBQUVpRCxZQUFZLEVBQUV2RCxLQUFLLENBQUU7SUFDcEVRLE1BQU0sRUFBRUEsTUFBTztJQUFBVCxRQUFBLEVBQ2RBO0VBQVEsQ0FDTCxDQUFDO0FBRVgsQ0FBQztBQUdNLElBQU04RCxjQVVYLEdBQUFwRSxPQUFBLENBQUFvRSxjQUFBLEdBQUcsU0FWUUEsY0FVWEEsQ0FBQUMsS0FBQSxFQVVJO0VBQUEsSUFUSi9ELFFBQVEsR0FBQStELEtBQUEsQ0FBUi9ELFFBQVE7SUFDUkMsS0FBSyxHQUFBOEQsS0FBQSxDQUFMOUQsS0FBSztJQUFBK0QscUJBQUEsR0FBQUQsS0FBQSxDQUNMeEQsZUFBZTtJQUFmQSxlQUFlLEdBQUF5RCxxQkFBQSxjQUFHLFNBQVMsR0FBQUEscUJBQUE7SUFBQUMscUJBQUEsR0FBQUYsS0FBQSxDQUMzQkcsZ0JBQWdCO0lBQWhCQSxnQkFBZ0IsR0FBQUQscUJBQUEsY0FBRyxJQUFJLEdBQUFBLHFCQUFBO0lBQUFFLG1CQUFBLEdBQUFKLEtBQUEsQ0FDdkJLLGFBQWE7SUFBYkEsYUFBYSxHQUFBRCxtQkFBQSxjQUFHLElBQUksR0FBQUEsbUJBQUE7SUFBQUUsb0JBQUEsR0FBQU4sS0FBQSxDQUNwQmhELGNBQWM7SUFBZEEsY0FBYyxHQUFBc0Qsb0JBQUEsY0FBRyxjQUFjLEdBQUFBLG9CQUFBO0lBQUFDLGtCQUFBLEdBQUFQLEtBQUEsQ0FDL0JwRCxZQUFZO0lBQVpBLFlBQVksR0FBQTJELGtCQUFBLGNBQUcsSUFBSSxHQUFBQSxrQkFBQTtJQUFBQyxxQkFBQSxHQUFBUixLQUFBLENBQ25CbEQsZUFBZTtJQUFmQSxlQUFlLEdBQUEwRCxxQkFBQSxjQUFHLElBQUksR0FBQUEscUJBQUE7SUFDdEI5RCxNQUFNLEdBQUFzRCxLQUFBLENBQU50RCxNQUFNO0VBRU4sSUFBTU4sS0FBOEMsR0FBRyxFQUFFO0VBRXpELElBQUkrRCxnQkFBZ0IsRUFBRS9ELEtBQUssQ0FBQ3FFLElBQUksQ0FBQyxLQUFLLENBQUM7RUFDdkMsSUFBSUosYUFBYSxFQUFFakUsS0FBSyxDQUFDcUUsSUFBSSxDQUFDLFFBQVEsQ0FBQztFQUV2QyxPQUNFLElBQUFoRixXQUFBLENBQUF3RCxHQUFBLEVBQUN2RCxlQUFlO0lBQ2RVLEtBQUssRUFBRUEsS0FBTTtJQUNiSSxlQUFlLEVBQUVBLGVBQWdCO0lBQ2pDUSxjQUFjLEVBQUVBLGNBQWU7SUFDL0JKLFlBQVksRUFBRUEsWUFBYTtJQUMzQkUsZUFBZSxFQUFFQSxlQUFnQjtJQUNqQ1osS0FBSyxFQUFFLENBQUMyQyxNQUFNLENBQUM2QixNQUFNLEVBQUV4RSxLQUFLLENBQUU7SUFDOUJRLE1BQU0sRUFBRUEsTUFBTztJQUFBVCxRQUFBLEVBQ2RBO0VBQVEsQ0FDTSxDQUFDO0FBRXRCLENBQUM7QUFHTSxJQUFNMEUsYUFLWCxHQUFBaEYsT0FBQSxDQUFBZ0YsYUFBQSxHQUFHLFNBTFFBLGFBS1hBLENBQUFDLEtBQUEsRUFBaUU7RUFBQSxJQUEzRDNFLFFBQVEsR0FBQTJFLEtBQUEsQ0FBUjNFLFFBQVE7SUFBRUMsS0FBSyxHQUFBMEUsS0FBQSxDQUFMMUUsS0FBSztJQUFBMkUscUJBQUEsR0FBQUQsS0FBQSxDQUFFcEUsZUFBZTtJQUFmQSxlQUFlLEdBQUFxRSxxQkFBQSxjQUFHLFNBQVMsR0FBQUEscUJBQUE7SUFBRW5FLE1BQU0sR0FBQWtFLEtBQUEsQ0FBTmxFLE1BQU07RUFDMUQsT0FDRSxJQUFBakIsV0FBQSxDQUFBd0QsR0FBQSxFQUFDdkQsZUFBZTtJQUNkVSxLQUFLLEVBQUUsQ0FBQyxLQUFLLEVBQUUsUUFBUSxDQUFFO0lBQ3pCSSxlQUFlLEVBQUVBLGVBQWdCO0lBQ2pDTixLQUFLLEVBQUUsQ0FBQzJDLE1BQU0sQ0FBQ2lDLEtBQUssRUFBRTVFLEtBQUssQ0FBRTtJQUM3QlEsTUFBTSxFQUFFQSxNQUFPO0lBQUFULFFBQUEsRUFDZEE7RUFBUSxDQUNNLENBQUM7QUFFdEIsQ0FBQztBQUdNLElBQU04RSxjQUtYLEdBQUFwRixPQUFBLENBQUFvRixjQUFBLEdBQUcsU0FMUUEsY0FLWEEsQ0FBQUMsS0FBQSxFQUFpRTtFQUFBLElBQTNEL0UsUUFBUSxHQUFBK0UsS0FBQSxDQUFSL0UsUUFBUTtJQUFFQyxLQUFLLEdBQUE4RSxLQUFBLENBQUw5RSxLQUFLO0lBQUErRSxxQkFBQSxHQUFBRCxLQUFBLENBQUV4RSxlQUFlO0lBQWZBLGVBQWUsR0FBQXlFLHFCQUFBLGNBQUcsU0FBUyxHQUFBQSxxQkFBQTtJQUFFdkUsTUFBTSxHQUFBc0UsS0FBQSxDQUFOdEUsTUFBTTtFQUMxRCxPQUNFLElBQUFqQixXQUFBLENBQUF3RCxHQUFBLEVBQUN2RCxlQUFlO0lBQ2RVLEtBQUssRUFBRSxDQUFDLEtBQUssQ0FBRTtJQUNmSSxlQUFlLEVBQUVBLGVBQWdCO0lBQ2pDTixLQUFLLEVBQUUsQ0FBQzJDLE1BQU0sQ0FBQ3FDLE1BQU0sRUFBRWhGLEtBQUssQ0FBRTtJQUM5QlEsTUFBTSxFQUFFQSxNQUFPO0lBQUFULFFBQUEsRUFDZEE7RUFBUSxDQUNNLENBQUM7QUFFdEIsQ0FBQztBQUdNLElBQU1rRix1QkFBdUIsR0FBQXhGLE9BQUEsQ0FBQXdGLHVCQUFBLEdBQUcsU0FBMUJBLHVCQUF1QkEsQ0FBQSxFQUFTO0VBQzNDLE9BQU8sSUFBQTlELGtDQUFpQixFQUFDLENBQUM7QUFDNUIsQ0FBQztBQUVNLElBQU0rRCxjQUFjLEdBQUF6RixPQUFBLENBQUF5RixjQUFBLEdBQUcsU0FBakJBLGNBQWNBLENBQUEsRUFBUztFQUNsQyxPQUFPLElBQUFDLCtCQUFjLEVBQUMsQ0FBQztBQUN6QixDQUFDO0FBRU0sSUFBTUMsaUJBQWlCLEdBQUEzRixPQUFBLENBQUEyRixpQkFBQSxHQUFHLFNBQXBCQSxpQkFBaUJBLENBQUEsRUFBUztFQUNyQyxPQUFPLElBQUFDLGtDQUFpQixFQUFDLENBQUM7QUFDNUIsQ0FBQztBQUVELElBQU0xQyxNQUFNLEdBQUcyQyx1QkFBVSxDQUFDQyxNQUFNLENBQUM7RUFDL0IzQyxTQUFTLEVBQUU7SUFDVDRDLElBQUksRUFBRTtFQUNSLENBQUM7RUFDRGhCLE1BQU0sRUFBRTtJQUNOZ0IsSUFBSSxFQUFFO0VBQ1IsQ0FBQztFQUNEWixLQUFLLEVBQUFhLE1BQUEsQ0FBQUMsTUFBQTtJQUNIRixJQUFJLEVBQUU7RUFBQyxHQUNKLElBQUFHLGlDQUFrQixFQUFDO0lBQ3BCQyxHQUFHLEVBQUU7TUFDSEMsbUJBQW1CLEVBQUUsRUFBRTtNQUN2QkMsb0JBQW9CLEVBQUU7SUFDeEIsQ0FBQztJQUNEQyxPQUFPLEVBQUU7TUFDUEYsbUJBQW1CLEVBQUUsQ0FBQztNQUN0QkMsb0JBQW9CLEVBQUU7SUFDeEIsQ0FBQztJQUNERSxPQUFPLEVBQUUsQ0FBQztFQUNaLENBQUMsQ0FBQyxDQUNIO0VBQ0RoQixNQUFNLEVBQUFTLE1BQUEsQ0FBQUMsTUFBQSxLQUNELElBQUFDLGlDQUFrQixFQUFDO0lBQ3BCQyxHQUFHLEVBQUU7TUFDSEssV0FBVyxFQUFFLE1BQU07TUFDbkJDLFlBQVksRUFBRTtRQUFFQyxLQUFLLEVBQUUsQ0FBQztRQUFFQyxNQUFNLEVBQUU7TUFBRSxDQUFDO01BQ3JDQyxhQUFhLEVBQUUsR0FBRztNQUNsQkMsWUFBWSxFQUFFO0lBQ2hCLENBQUM7SUFDRFAsT0FBTyxFQUFFO01BQ1BRLFNBQVMsRUFBRTtJQUNiLENBQUM7SUFDRFAsT0FBTyxFQUFFLENBQUM7RUFDWixDQUFDLENBQUM7QUFFTixDQUFDLENBQUMiLCJpZ25vcmVMaXN0IjpbXX0=