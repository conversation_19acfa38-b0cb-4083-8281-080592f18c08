{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_reactNative", "_performanceMonitor", "_jsxRuntime", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_callSuper", "_getPrototypeOf2", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "Error<PERSON>ou<PERSON><PERSON>", "exports", "_Component", "props", "_this", "_classCallCheck2", "resetTimeoutId", "resetErrorBoundary", "clearTimeout", "setState", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorInfo", "handleRetry", "state", "_inherits2", "_createClass2", "key", "value", "componentDidCatch", "console", "message", "stack", "componentStack", "errorBoundary", "performanceMonitor", "trackError", "component", "monitorError", "warn", "onError", "componentDidUpdate", "prevProps", "_this$props", "resetKeys", "resetOnPropsChange", "hasResetKeyChanged", "some", "index", "_prevProps$resetKeys", "children", "render", "_this$state", "_this$props2", "fallback", "jsx", "View", "style", "styles", "container", "testID", "accessibilityRole", "jsxs", "content", "Text", "title", "TouchableOpacity", "retryButton", "onPress", "retryButtonText", "getDerivedStateFromError", "Component", "StyleSheet", "create", "flex", "justifyContent", "alignItems", "padding", "backgroundColor", "max<PERSON><PERSON><PERSON>", "fontSize", "fontWeight", "color", "marginBottom", "textAlign", "lineHeight", "paddingHorizontal", "paddingVertical", "borderRadius", "_default"], "sources": ["ErrorBoundary.tsx"], "sourcesContent": ["/**\n * ErrorBoundary Component\n *\n * React Error Boundary component for catching and handling JavaScript errors\n * in the component tree. Provides fallback UI and error reporting.\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport React, { Component, ReactNode } from 'react';\nimport { View, Text, TouchableOpacity, StyleSheet } from 'react-native';\n\nimport { performanceMonitor } from '../../services/performanceMonitor';\n\nexport interface ErrorBoundaryProps {\n  children: ReactNode;\n  fallback?: ReactNode;\n  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;\n  resetOnPropsChange?: boolean;\n  resetKeys?: Array<string | number>;\n}\n\ninterface ErrorBoundaryState {\n  hasError: boolean;\n  error: Error | null;\n  errorInfo: React.ErrorInfo | null;\n}\n\nexport class ErrorBoundary extends Component<\n  ErrorBoundaryProps,\n  ErrorBoundaryState\n> {\n  private resetTimeoutId: number | null = null;\n\n  constructor(props: ErrorBoundaryProps) {\n    super(props);\n    this.state = {\n      hasError: false,\n      error: null,\n      errorInfo: null,\n    };\n  }\n\n  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {\n    return {\n      hasError: true,\n      error,\n    };\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    this.setState({\n      error,\n      errorInfo,\n    });\n\n    // Log error context for debugging\n    console.error('🚨 ErrorBoundary: Error caught', {\n      error: error.message,\n      stack: error.stack,\n      componentStack: errorInfo.componentStack,\n      errorBoundary: true,\n      props: this.props,\n    });\n\n    // Report error to performance monitor\n    try {\n      performanceMonitor.trackError(error, {\n        component: 'ErrorBoundary',\n        errorInfo: errorInfo.componentStack,\n      });\n    } catch (monitorError) {\n      console.warn(\n        'Failed to report error to performance monitor:',\n        monitorError,\n      );\n    }\n\n    // Call custom error handler if provided\n    if (this.props.onError) {\n      this.props.onError(error, errorInfo);\n    }\n\n    // Log error for debugging\n    console.error('ErrorBoundary caught an error:', error);\n    console.error('Error info:', errorInfo);\n  }\n\n  componentDidUpdate(prevProps: ErrorBoundaryProps) {\n    const { resetKeys, resetOnPropsChange } = this.props;\n    const { hasError } = this.state;\n\n    if (hasError && prevProps.resetKeys !== resetKeys) {\n      if (resetKeys) {\n        const hasResetKeyChanged = resetKeys.some(\n          (key, index) => prevProps.resetKeys?.[index] !== key,\n        );\n        if (hasResetKeyChanged) {\n          this.resetErrorBoundary();\n        }\n      }\n    }\n\n    if (\n      hasError &&\n      resetOnPropsChange &&\n      prevProps.children !== this.props.children\n    ) {\n      this.resetErrorBoundary();\n    }\n  }\n\n  resetErrorBoundary = () => {\n    if (this.resetTimeoutId) {\n      clearTimeout(this.resetTimeoutId);\n    }\n\n    this.setState({\n      hasError: false,\n      error: null,\n      errorInfo: null,\n    });\n  };\n\n  handleRetry = () => {\n    this.resetErrorBoundary();\n  };\n\n  render() {\n    const { hasError, error } = this.state;\n    const { children, fallback } = this.props;\n\n    if (hasError) {\n      if (fallback) {\n        return fallback;\n      }\n\n      return (\n        <View\n          style={styles.container}\n          testID=\"error-boundary-container\"\n          accessibilityRole=\"alert\">\n          <View style={styles.content}>\n            <Text style={styles.title}>Something went wrong</Text>\n            <Text style={styles.message}>\n              {error?.message || 'An unexpected error occurred'}\n            </Text>\n            <TouchableOpacity\n              style={styles.retryButton}\n              onPress={this.handleRetry}\n              testID=\"error-boundary-retry\">\n              <Text style={styles.retryButtonText}>Try Again</Text>\n            </TouchableOpacity>\n          </View>\n        </View>\n      );\n    }\n\n    return children;\n  }\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    padding: 20,\n    backgroundColor: '#f8f9fa',\n  },\n  content: {\n    alignItems: 'center',\n    maxWidth: 300,\n  },\n  title: {\n    fontSize: 20,\n    fontWeight: 'bold',\n    color: '#dc3545',\n    marginBottom: 12,\n    textAlign: 'center',\n  },\n  message: {\n    fontSize: 16,\n    color: '#6c757d',\n    textAlign: 'center',\n    marginBottom: 24,\n    lineHeight: 24,\n  },\n  retryButton: {\n    backgroundColor: '#007bff',\n    paddingHorizontal: 24,\n    paddingVertical: 12,\n    borderRadius: 8,\n  },\n  retryButtonText: {\n    color: '#ffffff',\n    fontSize: 16,\n    fontWeight: '600',\n  },\n});\n\nexport default ErrorBoundary;\n"], "mappings": ";;;;;;;;;;AAUA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,mBAAA,GAAAF,OAAA;AAAuE,IAAAG,WAAA,GAAAH,OAAA;AAAA,SAAAD,wBAAAK,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAP,uBAAA,YAAAA,wBAAAK,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,SAAAmB,WAAAnB,CAAA,EAAAK,CAAA,EAAAN,CAAA,WAAAM,CAAA,OAAAe,gBAAA,CAAAX,OAAA,EAAAJ,CAAA,OAAAgB,2BAAA,CAAAZ,OAAA,EAAAT,CAAA,EAAAsB,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAnB,CAAA,EAAAN,CAAA,YAAAqB,gBAAA,CAAAX,OAAA,EAAAT,CAAA,EAAAyB,WAAA,IAAApB,CAAA,CAAAqB,KAAA,CAAA1B,CAAA,EAAAD,CAAA;AAAA,SAAAuB,0BAAA,cAAAtB,CAAA,IAAA2B,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAd,IAAA,CAAAQ,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAA3B,CAAA,aAAAsB,yBAAA,YAAAA,0BAAA,aAAAtB,CAAA;AAAA,IAgB1D8B,aAAa,GAAAC,OAAA,CAAAD,aAAA,aAAAE,UAAA;EAMxB,SAAAF,cAAYG,KAAyB,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAA1B,OAAA,QAAAqB,aAAA;IACrCI,KAAA,GAAAf,UAAA,OAAAW,aAAA,GAAMG,KAAK;IAAEC,KAAA,CAHPE,cAAc,GAAkB,IAAI;IAAAF,KAAA,CAgF5CG,kBAAkB,GAAG,YAAM;MACzB,IAAIH,KAAA,CAAKE,cAAc,EAAE;QACvBE,YAAY,CAACJ,KAAA,CAAKE,cAAc,CAAC;MACnC;MAEAF,KAAA,CAAKK,QAAQ,CAAC;QACZC,QAAQ,EAAE,KAAK;QACfC,KAAK,EAAE,IAAI;QACXC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC;IAAAR,KAAA,CAEDS,WAAW,GAAG,YAAM;MAClBT,KAAA,CAAKG,kBAAkB,CAAC,CAAC;IAC3B,CAAC;IA1FCH,KAAA,CAAKU,KAAK,GAAG;MACXJ,QAAQ,EAAE,KAAK;MACfC,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE;IACb,CAAC;IAAC,OAAAR,KAAA;EACJ;EAAC,IAAAW,UAAA,CAAApC,OAAA,EAAAqB,aAAA,EAAAE,UAAA;EAAA,WAAAc,aAAA,CAAArC,OAAA,EAAAqB,aAAA;IAAAiB,GAAA;IAAAC,KAAA,EASD,SAAAC,iBAAiBA,CAACR,KAAY,EAAEC,SAA0B,EAAE;MAC1D,IAAI,CAACH,QAAQ,CAAC;QACZE,KAAK,EAALA,KAAK;QACLC,SAAS,EAATA;MACF,CAAC,CAAC;MAGFQ,OAAO,CAACT,KAAK,CAAC,gCAAgC,EAAE;QAC9CA,KAAK,EAAEA,KAAK,CAACU,OAAO;QACpBC,KAAK,EAAEX,KAAK,CAACW,KAAK;QAClBC,cAAc,EAAEX,SAAS,CAACW,cAAc;QACxCC,aAAa,EAAE,IAAI;QACnBrB,KAAK,EAAE,IAAI,CAACA;MACd,CAAC,CAAC;MAGF,IAAI;QACFsB,sCAAkB,CAACC,UAAU,CAACf,KAAK,EAAE;UACnCgB,SAAS,EAAE,eAAe;UAC1Bf,SAAS,EAAEA,SAAS,CAACW;QACvB,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOK,YAAY,EAAE;QACrBR,OAAO,CAACS,IAAI,CACV,gDAAgD,EAChDD,YACF,CAAC;MACH;MAGA,IAAI,IAAI,CAACzB,KAAK,CAAC2B,OAAO,EAAE;QACtB,IAAI,CAAC3B,KAAK,CAAC2B,OAAO,CAACnB,KAAK,EAAEC,SAAS,CAAC;MACtC;MAGAQ,OAAO,CAACT,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDS,OAAO,CAACT,KAAK,CAAC,aAAa,EAAEC,SAAS,CAAC;IACzC;EAAC;IAAAK,GAAA;IAAAC,KAAA,EAED,SAAAa,kBAAkBA,CAACC,SAA6B,EAAE;MAChD,IAAAC,WAAA,GAA0C,IAAI,CAAC9B,KAAK;QAA5C+B,SAAS,GAAAD,WAAA,CAATC,SAAS;QAAEC,kBAAkB,GAAAF,WAAA,CAAlBE,kBAAkB;MACrC,IAAQzB,QAAQ,GAAK,IAAI,CAACI,KAAK,CAAvBJ,QAAQ;MAEhB,IAAIA,QAAQ,IAAIsB,SAAS,CAACE,SAAS,KAAKA,SAAS,EAAE;QACjD,IAAIA,SAAS,EAAE;UACb,IAAME,kBAAkB,GAAGF,SAAS,CAACG,IAAI,CACvC,UAACpB,GAAG,EAAEqB,KAAK;YAAA,IAAAC,oBAAA;YAAA,OAAK,EAAAA,oBAAA,GAAAP,SAAS,CAACE,SAAS,qBAAnBK,oBAAA,CAAsBD,KAAK,CAAC,MAAKrB,GAAG;UAAA,CACtD,CAAC;UACD,IAAImB,kBAAkB,EAAE;YACtB,IAAI,CAAC7B,kBAAkB,CAAC,CAAC;UAC3B;QACF;MACF;MAEA,IACEG,QAAQ,IACRyB,kBAAkB,IAClBH,SAAS,CAACQ,QAAQ,KAAK,IAAI,CAACrC,KAAK,CAACqC,QAAQ,EAC1C;QACA,IAAI,CAACjC,kBAAkB,CAAC,CAAC;MAC3B;IACF;EAAC;IAAAU,GAAA;IAAAC,KAAA,EAkBD,SAAAuB,MAAMA,CAAA,EAAG;MACP,IAAAC,WAAA,GAA4B,IAAI,CAAC5B,KAAK;QAA9BJ,QAAQ,GAAAgC,WAAA,CAARhC,QAAQ;QAAEC,KAAK,GAAA+B,WAAA,CAAL/B,KAAK;MACvB,IAAAgC,YAAA,GAA+B,IAAI,CAACxC,KAAK;QAAjCqC,QAAQ,GAAAG,YAAA,CAARH,QAAQ;QAAEI,QAAQ,GAAAD,YAAA,CAARC,QAAQ;MAE1B,IAAIlC,QAAQ,EAAE;QACZ,IAAIkC,QAAQ,EAAE;UACZ,OAAOA,QAAQ;QACjB;QAEA,OACE,IAAA5E,WAAA,CAAA6E,GAAA,EAAC/E,YAAA,CAAAgF,IAAI;UACHC,KAAK,EAAEC,MAAM,CAACC,SAAU;UACxBC,MAAM,EAAC,0BAA0B;UACjCC,iBAAiB,EAAC,OAAO;UAAAX,QAAA,EACzB,IAAAxE,WAAA,CAAAoF,IAAA,EAACtF,YAAA,CAAAgF,IAAI;YAACC,KAAK,EAAEC,MAAM,CAACK,OAAQ;YAAAb,QAAA,GAC1B,IAAAxE,WAAA,CAAA6E,GAAA,EAAC/E,YAAA,CAAAwF,IAAI;cAACP,KAAK,EAAEC,MAAM,CAACO,KAAM;cAAAf,QAAA,EAAC;YAAoB,CAAM,CAAC,EACtD,IAAAxE,WAAA,CAAA6E,GAAA,EAAC/E,YAAA,CAAAwF,IAAI;cAACP,KAAK,EAAEC,MAAM,CAAC3B,OAAQ;cAAAmB,QAAA,EACzB,CAAA7B,KAAK,oBAALA,KAAK,CAAEU,OAAO,KAAI;YAA8B,CAC7C,CAAC,EACP,IAAArD,WAAA,CAAA6E,GAAA,EAAC/E,YAAA,CAAA0F,gBAAgB;cACfT,KAAK,EAAEC,MAAM,CAACS,WAAY;cAC1BC,OAAO,EAAE,IAAI,CAAC7C,WAAY;cAC1BqC,MAAM,EAAC,sBAAsB;cAAAV,QAAA,EAC7B,IAAAxE,WAAA,CAAA6E,GAAA,EAAC/E,YAAA,CAAAwF,IAAI;gBAACP,KAAK,EAAEC,MAAM,CAACW,eAAgB;gBAAAnB,QAAA,EAAC;cAAS,CAAM;YAAC,CACrC,CAAC;UAAA,CACf;QAAC,CACH,CAAC;MAEX;MAEA,OAAOA,QAAQ;IACjB;EAAC;IAAAvB,GAAA;IAAAC,KAAA,EApHD,SAAO0C,wBAAwBA,CAACjD,KAAY,EAA+B;MACzE,OAAO;QACLD,QAAQ,EAAE,IAAI;QACdC,KAAK,EAALA;MACF,CAAC;IACH;EAAC;AAAA,EApBgCkD,gBAAS;AAsI5C,IAAMb,MAAM,GAAGc,uBAAU,CAACC,MAAM,CAAC;EAC/Bd,SAAS,EAAE;IACTe,IAAI,EAAE,CAAC;IACPC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE,EAAE;IACXC,eAAe,EAAE;EACnB,CAAC;EACDf,OAAO,EAAE;IACPa,UAAU,EAAE,QAAQ;IACpBG,QAAQ,EAAE;EACZ,CAAC;EACDd,KAAK,EAAE;IACLe,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,SAAS;IAChBC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC;EACDrD,OAAO,EAAE;IACPiD,QAAQ,EAAE,EAAE;IACZE,KAAK,EAAE,SAAS;IAChBE,SAAS,EAAE,QAAQ;IACnBD,YAAY,EAAE,EAAE;IAChBE,UAAU,EAAE;EACd,CAAC;EACDlB,WAAW,EAAE;IACXW,eAAe,EAAE,SAAS;IAC1BQ,iBAAiB,EAAE,EAAE;IACrBC,eAAe,EAAE,EAAE;IACnBC,YAAY,EAAE;EAChB,CAAC;EACDnB,eAAe,EAAE;IACfa,KAAK,EAAE,SAAS;IAChBF,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAAC,IAAAQ,QAAA,GAAA9E,OAAA,CAAAtB,OAAA,GAEYqB,aAAa", "ignoreList": []}