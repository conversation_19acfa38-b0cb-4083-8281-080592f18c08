2ffcf6008d1dfff5be86cd858b2d9e56
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.performanceCacheService = exports.default = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _advancedCachingService = require("./advancedCachingService");
var _cacheService = require("./cacheService");
var _performanceMonitoringService = require("./performanceMonitoringService");
var PerformanceCacheService = function () {
  function PerformanceCacheService() {
    var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    (0, _classCallCheck2.default)(this, PerformanceCacheService);
    this.preloadQueue = [];
    this.isPreloading = false;
    this.config = Object.assign({
      enableImageCaching: true,
      enableApiCaching: true,
      enableComponentCaching: true,
      preloadCriticalAssets: true,
      aggressiveCaching: true,
      cacheCompressionLevel: 6
    }, config);
    this.advancedCache = _advancedCachingService.advancedCachingService;
    this.basicCache = _cacheService.cacheService;
    this.initializePerformanceOptimizations();
  }
  return (0, _createClass2.default)(PerformanceCacheService, [{
    key: "initializePerformanceOptimizations",
    value: (function () {
      var _initializePerformanceOptimizations = (0, _asyncToGenerator2.default)(function* () {
        if (this.config.preloadCriticalAssets) {
          yield this.preloadCriticalAssets();
        }
        this.startBackgroundPreloading();
      });
      function initializePerformanceOptimizations() {
        return _initializePerformanceOptimizations.apply(this, arguments);
      }
      return initializePerformanceOptimizations;
    }())
  }, {
    key: "preloadCriticalAssets",
    value: (function () {
      var _preloadCriticalAssets = (0, _asyncToGenerator2.default)(function* () {
        var _this = this;
        var criticalAssets = ['user_profile', 'service_categories', 'featured_providers', 'user_preferences', 'app_logo', 'default_avatar', 'placeholder_images', 'search_components', 'booking_components', 'navigation_components'];
        var _loop = function* _loop(asset) {
          _this.addToPreloadQueue((0, _asyncToGenerator2.default)(function* () {
            var cached = yield _this.advancedCache.get(asset);
            if (!cached) {
              yield _this.advancedCache.set(`${asset}_priority`, true, {
                ttl: 60000
              });
            }
          }));
        };
        for (var asset of criticalAssets) {
          yield* _loop(asset);
        }
      });
      function preloadCriticalAssets() {
        return _preloadCriticalAssets.apply(this, arguments);
      }
      return preloadCriticalAssets;
    }())
  }, {
    key: "addToPreloadQueue",
    value: function addToPreloadQueue(preloadFn) {
      this.preloadQueue.push(preloadFn);
      if (!this.isPreloading) {
        this.processPreloadQueue();
      }
    }
  }, {
    key: "processPreloadQueue",
    value: (function () {
      var _processPreloadQueue = (0, _asyncToGenerator2.default)(function* () {
        if (this.isPreloading || this.preloadQueue.length === 0) {
          return;
        }
        this.isPreloading = true;
        try {
          var batchSize = 3;
          while (this.preloadQueue.length > 0) {
            var batch = this.preloadQueue.splice(0, batchSize);
            yield Promise.allSettled(batch.map(function (fn) {
              return fn();
            }));
            yield new Promise(function (resolve) {
              return setTimeout(resolve, 10);
            });
          }
        } catch (error) {
          console.warn('Preload queue processing error:', error);
        } finally {
          this.isPreloading = false;
        }
      });
      function processPreloadQueue() {
        return _processPreloadQueue.apply(this, arguments);
      }
      return processPreloadQueue;
    }())
  }, {
    key: "startBackgroundPreloading",
    value: function startBackgroundPreloading() {
      var _this2 = this;
      setTimeout(function () {
        _this2.processPreloadQueue();
      }, 2000);
      setInterval(function () {
        if (_this2.preloadQueue.length > 0) {
          _this2.processPreloadQueue();
        }
      }, 30000);
    }
  }, {
    key: "cacheApiResponse",
    value: (function () {
      var _cacheApiResponse = (0, _asyncToGenerator2.default)(function* (key, data) {
        var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
        if (!this.config.enableApiCaching) return;
        var cacheKey = `api_${key}`;
        var ttl = options.ttl || 30 * 60 * 1000;
        try {
          if (options.priority) {
            yield this.advancedCache.set(cacheKey, data, {
              ttl: ttl
            });
          } else {
            yield this.basicCache.set(cacheKey, data, ttl);
          }
          _performanceMonitoringService.performanceMonitoringService.trackCustomMetric('cache_write', {
            key: cacheKey,
            size: JSON.stringify(data).length,
            priority: options.priority || false
          });
        } catch (error) {
          console.warn('Cache write error:', error);
        }
      });
      function cacheApiResponse(_x, _x2) {
        return _cacheApiResponse.apply(this, arguments);
      }
      return cacheApiResponse;
    }())
  }, {
    key: "getCachedApiResponse",
    value: (function () {
      var _getCachedApiResponse = (0, _asyncToGenerator2.default)(function* (key) {
        if (!this.config.enableApiCaching) return null;
        var cacheKey = `api_${key}`;
        var startTime = Date.now();
        try {
          var result = yield this.advancedCache.get(cacheKey);
          if (!result) {
            result = yield this.basicCache.get(cacheKey);
          }
          var duration = Date.now() - startTime;
          _performanceMonitoringService.performanceMonitoringService.trackCustomMetric('cache_read', {
            key: cacheKey,
            hit: result !== null,
            duration: duration
          });
          return result;
        } catch (error) {
          console.warn('Cache read error:', error);
          return null;
        }
      });
      function getCachedApiResponse(_x3) {
        return _getCachedApiResponse.apply(this, arguments);
      }
      return getCachedApiResponse;
    }())
  }, {
    key: "cacheImage",
    value: (function () {
      var _cacheImage = (0, _asyncToGenerator2.default)(function* (uri, data) {
        if (!this.config.enableImageCaching) return;
        var cacheKey = `image_${uri}`;
        try {
          yield this.advancedCache.set(cacheKey, data, {
            ttl: 7 * 24 * 60 * 60 * 1000,
            tags: ['images']
          });
        } catch (error) {
          console.warn('Image cache error:', error);
        }
      });
      function cacheImage(_x4, _x5) {
        return _cacheImage.apply(this, arguments);
      }
      return cacheImage;
    }())
  }, {
    key: "getCachedImage",
    value: (function () {
      var _getCachedImage = (0, _asyncToGenerator2.default)(function* (uri) {
        if (!this.config.enableImageCaching) return null;
        var cacheKey = `image_${uri}`;
        try {
          return yield this.advancedCache.get(cacheKey);
        } catch (error) {
          console.warn('Image cache read error:', error);
          return null;
        }
      });
      function getCachedImage(_x6) {
        return _getCachedImage.apply(this, arguments);
      }
      return getCachedImage;
    }())
  }, {
    key: "warmCache",
    value: (function () {
      var _warmCache = (0, _asyncToGenerator2.default)(function* (key, dataLoader) {
        var _this3 = this;
        this.addToPreloadQueue((0, _asyncToGenerator2.default)(function* () {
          try {
            var data = yield dataLoader();
            yield _this3.cacheApiResponse(key, data, {
              priority: true
            });
          } catch (error) {
            console.warn('Cache warming error:', error);
          }
        }));
      });
      function warmCache(_x7, _x8) {
        return _warmCache.apply(this, arguments);
      }
      return warmCache;
    }())
  }, {
    key: "clearPerformanceCaches",
    value: (function () {
      var _clearPerformanceCaches = (0, _asyncToGenerator2.default)(function* () {
        try {
          yield this.advancedCache.clear();
          yield this.basicCache.clear();
          this.preloadQueue = [];
        } catch (error) {
          console.warn('Cache clear error:', error);
        }
      });
      function clearPerformanceCaches() {
        return _clearPerformanceCaches.apply(this, arguments);
      }
      return clearPerformanceCaches;
    }())
  }, {
    key: "getCacheStats",
    value: (function () {
      var _getCacheStats = (0, _asyncToGenerator2.default)(function* () {
        try {
          var advancedStats = yield this.advancedCache.getStats();
          var basicStats = yield this.basicCache.getStats();
          return {
            advanced: advancedStats,
            basic: basicStats,
            preloadQueue: this.preloadQueue.length,
            isPreloading: this.isPreloading
          };
        } catch (error) {
          console.warn('Cache stats error:', error);
          return null;
        }
      });
      function getCacheStats() {
        return _getCacheStats.apply(this, arguments);
      }
      return getCacheStats;
    }())
  }]);
}();
var performanceCacheService = exports.performanceCacheService = new PerformanceCacheService();
var _default = exports.default = performanceCacheService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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