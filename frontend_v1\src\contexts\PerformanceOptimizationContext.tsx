/**
 * Performance Optimization Context
 *
 * Global context provider for advanced performance optimization features.
 * Provides centralized performance management, monitoring, and optimization
 * across the entire application.
 *
 * Features:
 * - Global performance state management
 * - Centralized optimization controls
 * - Performance monitoring coordination
 * - Adaptive performance tuning
 * - Performance budget enforcement
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, {
  createContext,
  useContext,
  useCallback,
  useEffect,
  useState,
  ReactNode,
} from 'react';
import { AppState, AppStateStatus } from 'react-native';

import { useAdvancedPerformanceOptimization } from '../utils/advancedPerformanceOptimization';
import { intelligentCache } from '../utils/intelligentCaching';
import { performanceMonitor } from '../utils/performance';

// Performance optimization context interface
interface PerformanceOptimizationContextType {
  // Global performance state
  isOptimizationEnabled: boolean;
  performanceLevel: 'low' | 'medium' | 'high';
  globalMetrics: GlobalPerformanceMetrics;

  // Optimization controls
  enableOptimization: (enabled: boolean) => void;
  setPerformanceLevel: (level: 'low' | 'medium' | 'high') => void;
  optimizeForDevice: () => Promise<void>;
  clearAllCaches: () => Promise<void>;

  // Performance monitoring
  startGlobalMonitoring: () => void;
  stopGlobalMonitoring: () => void;
  getPerformanceReport: () => PerformanceReport;

  // Budget management
  setPerformanceBudget: (budget: Partial<PerformanceBudget>) => void;
  checkBudgetCompliance: () => BudgetComplianceReport;

  // Dashboard controls
  showPerformanceDashboard: boolean;
  togglePerformanceDashboard: () => void;
}

// Global performance metrics
interface GlobalPerformanceMetrics {
  averageRenderTime: number;
  totalMemoryUsage: number;
  cacheEfficiency: number;
  networkLatency: number;
  frameDropRate: number;
  appStartupTime: number;
  activeComponents: number;
}

// Performance budget
interface PerformanceBudget {
  maxRenderTime: number;
  maxMemoryUsage: number;
  maxBundleSize: number;
  maxNetworkRequests: number;
  minFrameRate: number;
}

// Performance report
interface PerformanceReport {
  timestamp: number;
  metrics: GlobalPerformanceMetrics;
  recommendations: string[];
  budgetCompliance: BudgetComplianceReport;
  optimizationHistory: OptimizationEvent[];
}

// Budget compliance report
interface BudgetComplianceReport {
  isCompliant: boolean;
  violations: Array<{
    metric: keyof PerformanceBudget;
    current: number;
    budget: number;
    severity: 'low' | 'medium' | 'high' | 'critical';
  }>;
  score: number; // 0-100
}

// Optimization event
interface OptimizationEvent {
  timestamp: number;
  type: 'manual' | 'automatic' | 'adaptive';
  action: string;
  impact: 'positive' | 'negative' | 'neutral';
  metrics: Partial<GlobalPerformanceMetrics>;
}

// Provider props
interface PerformanceOptimizationProviderProps {
  children: ReactNode;
  enableGlobalOptimization?: boolean;
  performanceBudget?: Partial<PerformanceBudget>;
  enableDashboard?: boolean;
}

// Default performance budget
const DEFAULT_PERFORMANCE_BUDGET: PerformanceBudget = {
  maxRenderTime: 16, // 60fps target
  maxMemoryUsage: 150, // 150MB
  maxBundleSize: 3072, // 3MB
  maxNetworkRequests: 10, // Concurrent requests
  minFrameRate: 55, // Minimum acceptable frame rate
};

// Create context
const PerformanceOptimizationContext =
  createContext<PerformanceOptimizationContextType | null>(null);

/**
 * Performance Optimization Provider
 */
export const PerformanceOptimizationProvider: React.FC<
  PerformanceOptimizationProviderProps
> = ({
  children,
  enableGlobalOptimization = true,
  performanceBudget = {},
  enableDashboard = __DEV__, // Enable dashboard in development by default
}) => {
  // State management - temporarily disabled to fix infinite loop
  const [isOptimizationEnabled, setIsOptimizationEnabled] = useState(false);
  const [performanceLevel, setPerformanceLevel] = useState<
    'low' | 'medium' | 'high'
  >('medium');
  const [showPerformanceDashboard, setShowPerformanceDashboard] =
    useState(false);
  const [currentBudget, setCurrentBudget] = useState<PerformanceBudget>({
    ...DEFAULT_PERFORMANCE_BUDGET,
    ...performanceBudget,
  });

  const [globalMetrics, setGlobalMetrics] = useState<GlobalPerformanceMetrics>({
    averageRenderTime: 0,
    totalMemoryUsage: 0,
    cacheEfficiency: 0,
    networkLatency: 0,
    frameDropRate: 0,
    appStartupTime: 0,
    activeComponents: 0,
  });

  const [optimizationHistory, setOptimizationHistory] = useState<
    OptimizationEvent[]
  >([]);
  const [isMonitoring, setIsMonitoring] = useState(false);

  // Advanced performance optimization for the provider itself
  // Use stable configuration to prevent hooks order changes
  const stableConfig = React.useMemo(() => ({
    enableIntelligentRendering: true, // Always enabled to prevent hooks order changes
    enableAdaptivePerformance: true, // Always enabled to prevent hooks order changes
    componentPriority: 'critical' as const,
    performanceBudget: DEFAULT_PERFORMANCE_BUDGET, // Use stable default budget
  }), []);

  const {
    metrics: providerMetrics,
    adaptivePerformanceTuning,
    memoryOptimization,
    collectMetrics,
  } = useAdvancedPerformanceOptimization('PerformanceOptimizationProvider', stableConfig);

  /**
   * Enable/disable global optimization
   */
  const enableOptimization = useCallback(
    (enabled: boolean) => {
      setIsOptimizationEnabled(enabled);

      // Record optimization event
      const event: OptimizationEvent = {
        timestamp: Date.now(),
        type: 'manual',
        action: enabled ? 'enabled_optimization' : 'disabled_optimization',
        impact: enabled ? 'positive' : 'neutral',
        metrics: globalMetrics,
      };

      setOptimizationHistory(prev => [...prev.slice(-49), event]); // Keep last 50 events
    },
    [globalMetrics],
  );

  /**
   * Optimize for current device capabilities
   */
  const optimizeForDevice = useCallback(async () => {
    try {
      // Run adaptive performance tuning
      const tuningResult = adaptivePerformanceTuning();

      // Optimize memory usage
      memoryOptimization();

      // Optimize cache based on device capabilities
      const cacheStats = intelligentCache.getCacheStats();
      if (cacheStats.utilizationRate > 0.8) {
        await intelligentCache.clearCache(['low-priority']);
      }

      // Record optimization event
      const event: OptimizationEvent = {
        timestamp: Date.now(),
        type: 'automatic',
        action: 'device_optimization',
        impact: 'positive',
        metrics: globalMetrics,
      };

      setOptimizationHistory(prev => [...prev.slice(-49), event]);
    } catch (error) {
      console.error(
        '[PerformanceOptimization] Device optimization failed:',
        error,
      );
    }
  }, [adaptivePerformanceTuning, memoryOptimization, globalMetrics]);

  /**
   * Clear all caches
   */
  const clearAllCaches = useCallback(async () => {
    try {
      await intelligentCache.clearCache();

      // Record optimization event
      const event: OptimizationEvent = {
        timestamp: Date.now(),
        type: 'manual',
        action: 'clear_all_caches',
        impact: 'neutral',
        metrics: globalMetrics,
      };

      setOptimizationHistory(prev => [...prev.slice(-49), event]);
    } catch (error) {
      console.error('[PerformanceOptimization] Cache clearing failed:', error);
    }
  }, [globalMetrics]);

  /**
   * Collect global performance metrics
   */
  const collectGlobalMetrics = useCallback(() => {
    // Temporarily disabled to prevent infinite loops
    // TODO: Fix the infinite loop issue and re-enable
    return;

    try {
      // Get cache statistics
      const cacheStats = intelligentCache.getCacheStats();

      // Get performance data from monitor
      const performanceData = performanceMonitor.getMetrics();

      // Use current state values without dependencies to prevent infinite loops
      setGlobalMetrics(prevGlobalMetrics => {
        // Combine metrics from various sources
        const newMetrics: GlobalPerformanceMetrics = {
          averageRenderTime: providerMetrics.renderTime || 0,
          totalMemoryUsage: providerMetrics.memoryUsage || 0,
          cacheEfficiency: cacheStats.hitRate,
          networkLatency: providerMetrics.networkLatency || 0,
          frameDropRate: (providerMetrics.frameDrops || 0) / 60, // Convert to rate
          appStartupTime: 0, // Would need to be measured at app start
          activeComponents: 1, // Simplified - would need component registry
        };

        return newMetrics;
      });
    } catch (error) {
      console.error(
        '[PerformanceOptimization] Metrics collection failed:',
        error,
      );
    }
  }, []); // No dependencies to prevent infinite loops

  /**
   * Start global performance monitoring
   */
  const startGlobalMonitoring = useCallback(() => {
    setIsMonitoring(prev => {
      if (prev) return prev; // Already monitoring

      // Start periodic metrics collection
      const interval = setInterval(() => {
        collectGlobalMetrics();
      }, 10000); // Every 10 seconds (further reduced frequency)

      // Store interval reference for cleanup
      (globalThis as any).__performanceMonitoringInterval = interval;

      console.log('[PerformanceOptimization] Global monitoring started');
      return true;
    });
  }, []); // No dependencies to prevent infinite re-renders

  /**
   * Stop global performance monitoring
   */
  const stopGlobalMonitoring = useCallback(() => {
    setIsMonitoring(prev => {
      if (!prev) return prev; // Already stopped

      // Clear monitoring interval
      const interval = (globalThis as any).__performanceMonitoringInterval;
      if (interval) {
        clearInterval(interval);
        delete (globalThis as any).__performanceMonitoringInterval;
      }

      console.log('[PerformanceOptimization] Global monitoring stopped');
      return false;
    });
  }, []); // No dependencies to prevent infinite re-renders

  /**
   * Check budget compliance
   */
  const checkBudgetCompliance = useCallback((): BudgetComplianceReport => {
    const violations: BudgetComplianceReport['violations'] = [];

    // Check each budget metric
    if (globalMetrics.averageRenderTime > currentBudget.maxRenderTime) {
      violations.push({
        metric: 'maxRenderTime',
        current: globalMetrics.averageRenderTime,
        budget: currentBudget.maxRenderTime,
        severity:
          globalMetrics.averageRenderTime > currentBudget.maxRenderTime * 2
            ? 'critical'
            : 'high',
      });
    }

    if (globalMetrics.totalMemoryUsage > currentBudget.maxMemoryUsage) {
      violations.push({
        metric: 'maxMemoryUsage',
        current: globalMetrics.totalMemoryUsage,
        budget: currentBudget.maxMemoryUsage,
        severity:
          globalMetrics.totalMemoryUsage > currentBudget.maxMemoryUsage * 1.5
            ? 'critical'
            : 'high',
      });
    }

    if (60 - globalMetrics.frameDropRate < currentBudget.minFrameRate) {
      violations.push({
        metric: 'minFrameRate',
        current: 60 - globalMetrics.frameDropRate,
        budget: currentBudget.minFrameRate,
        severity:
          60 - globalMetrics.frameDropRate < currentBudget.minFrameRate * 0.8
            ? 'critical'
            : 'medium',
      });
    }

    // Calculate compliance score
    const totalMetrics = 5; // Number of metrics we're checking
    const violationCount = violations.length;
    const score = Math.max(
      0,
      ((totalMetrics - violationCount) / totalMetrics) * 100,
    );

    return {
      isCompliant: violations.length === 0,
      violations,
      score,
    };
  }, [globalMetrics, currentBudget]);

  /**
   * Generate performance report
   */
  const getPerformanceReport = useCallback((): PerformanceReport => {
    const budgetCompliance = checkBudgetCompliance();

    // Generate recommendations based on current state
    const recommendations: string[] = [];

    if (globalMetrics.averageRenderTime > 16) {
      recommendations.push(
        'Consider enabling intelligent rendering optimization',
      );
    }

    if (globalMetrics.totalMemoryUsage > 100) {
      recommendations.push('Run memory optimization to reduce memory usage');
    }

    if (globalMetrics.cacheEfficiency < 0.6) {
      recommendations.push('Review cache strategy to improve hit rate');
    }

    if (!budgetCompliance.isCompliant) {
      recommendations.push('Address performance budget violations');
    }

    return {
      timestamp: Date.now(),
      metrics: globalMetrics,
      recommendations,
      budgetCompliance,
      optimizationHistory: optimizationHistory.slice(-10), // Last 10 events
    };
  }, [globalMetrics, checkBudgetCompliance, optimizationHistory]);

  /**
   * Set performance budget
   */
  const setPerformanceBudget = useCallback(
    (budget: Partial<PerformanceBudget>) => {
      setCurrentBudget(prev => ({ ...prev, ...budget }));
    },
    [],
  );

  /**
   * Toggle performance dashboard
   */
  const togglePerformanceDashboard = useCallback(() => {
    setShowPerformanceDashboard(prev => !prev);
  }, []);

  // Initialize monitoring on mount
  useEffect(() => {
    if (isOptimizationEnabled) {
      startGlobalMonitoring();
    }

    return () => {
      stopGlobalMonitoring();
    };
  }, [isOptimizationEnabled, startGlobalMonitoring, stopGlobalMonitoring]);

  // Handle app state changes
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'background' && isMonitoring) {
        // Stop monitoring when app is in background
        stopGlobalMonitoring();
      } else if (
        nextAppState === 'active' &&
        isOptimizationEnabled &&
        !isMonitoring
      ) {
        // Resume monitoring when app becomes active
        startGlobalMonitoring();
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => subscription?.remove();
  }, [
    isOptimizationEnabled,
    isMonitoring,
    startGlobalMonitoring,
    stopGlobalMonitoring,
  ]);

  // Context value
  const contextValue: PerformanceOptimizationContextType = {
    // Global performance state
    isOptimizationEnabled,
    performanceLevel,
    globalMetrics,

    // Optimization controls
    enableOptimization,
    setPerformanceLevel,
    optimizeForDevice,
    clearAllCaches,

    // Performance monitoring
    startGlobalMonitoring,
    stopGlobalMonitoring,
    getPerformanceReport,

    // Budget management
    setPerformanceBudget,
    checkBudgetCompliance,

    // Dashboard controls
    showPerformanceDashboard,
    togglePerformanceDashboard,
  };

  return (
    <PerformanceOptimizationContext.Provider value={contextValue}>
      {children}
    </PerformanceOptimizationContext.Provider>
  );
};

/**
 * Hook to use performance optimization context
 */
export const usePerformanceOptimizationContext =
  (): PerformanceOptimizationContextType => {
    const context = useContext(PerformanceOptimizationContext);

    if (!context) {
      throw new Error(
        'usePerformanceOptimizationContext must be used within a PerformanceOptimizationProvider',
      );
    }

    return context;
  };

// Alias for backward compatibility
export const usePerformanceOptimization = usePerformanceOptimizationContext;

export default PerformanceOptimizationProvider;
