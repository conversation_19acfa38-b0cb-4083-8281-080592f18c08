be770d39f6460aefcd470b837c42454f
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.createStackNavigator = exports.TransitionPresets = exports.CardStyleInterpolators = void 0;
var React = require('react');
var createStackNavigator = exports.createStackNavigator = jest.fn(function () {
  return {
    Navigator: function Navigator(_ref) {
      var children = _ref.children;
      return children;
    },
    Screen: function Screen(_ref2) {
      var children = _ref2.children;
      return children;
    }
  };
});
var CardStyleInterpolators = exports.CardStyleInterpolators = {
  forHorizontalIOS: {},
  forVerticalIOS: {},
  forModalPresentationIOS: {}
};
var TransitionPresets = exports.TransitionPresets = {
  SlideFromRightIOS: {},
  ModalSlideFromBottomIOS: {}
};
var _default = exports.default = {
  createStackNavigator: createStackNavigator,
  CardStyleInterpolators: CardStyleInterpolators,
  TransitionPresets: TransitionPresets
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJSZWFjdCIsInJlcXVpcmUiLCJjcmVhdGVTdGFja05hdmlnYXRvciIsImV4cG9ydHMiLCJqZXN0IiwiZm4iLCJOYXZpZ2F0b3IiLCJfcmVmIiwiY2hpbGRyZW4iLCJTY3JlZW4iLCJfcmVmMiIsIkNhcmRTdHlsZUludGVycG9sYXRvcnMiLCJmb3JIb3Jpem9udGFsSU9TIiwiZm9yVmVydGljYWxJT1MiLCJmb3JNb2RhbFByZXNlbnRhdGlvbklPUyIsIlRyYW5zaXRpb25QcmVzZXRzIiwiU2xpZGVGcm9tUmlnaHRJT1MiLCJNb2RhbFNsaWRlRnJvbUJvdHRvbUlPUyIsIl9kZWZhdWx0IiwiZGVmYXVsdCJdLCJzb3VyY2VzIjpbInN0YWNrLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogTW9jayBmb3IgQHJlYWN0LW5hdmlnYXRpb24vc3RhY2tcbiAqL1xuXG5jb25zdCBSZWFjdCA9IHJlcXVpcmUoJ3JlYWN0Jyk7XG5cbmV4cG9ydCBjb25zdCBjcmVhdGVTdGFja05hdmlnYXRvciA9IGplc3QuZm4oKCkgPT4gKHtcbiAgTmF2aWdhdG9yOiAoeyBjaGlsZHJlbiB9KSA9PiBjaGlsZHJlbixcbiAgU2NyZWVuOiAoeyBjaGlsZHJlbiB9KSA9PiBjaGlsZHJlbixcbn0pKTtcblxuZXhwb3J0IGNvbnN0IENhcmRTdHlsZUludGVycG9sYXRvcnMgPSB7XG4gIGZvckhvcml6b250YWxJT1M6IHt9LFxuICBmb3JWZXJ0aWNhbElPUzoge30sXG4gIGZvck1vZGFsUHJlc2VudGF0aW9uSU9TOiB7fSxcbn07XG5cbmV4cG9ydCBjb25zdCBUcmFuc2l0aW9uUHJlc2V0cyA9IHtcbiAgU2xpZGVGcm9tUmlnaHRJT1M6IHt9LFxuICBNb2RhbFNsaWRlRnJvbUJvdHRvbUlPUzoge30sXG59O1xuXG5leHBvcnQgZGVmYXVsdCB7XG4gIGNyZWF0ZVN0YWNrTmF2aWdhdG9yLFxuICBDYXJkU3R5bGVJbnRlcnBvbGF0b3JzLFxuICBUcmFuc2l0aW9uUHJlc2V0cyxcbn07XG4iXSwibWFwcGluZ3MiOiI7Ozs7QUFJQSxJQUFNQSxLQUFLLEdBQUdDLE9BQU8sQ0FBQyxPQUFPLENBQUM7QUFFdkIsSUFBTUMsb0JBQW9CLEdBQUFDLE9BQUEsQ0FBQUQsb0JBQUEsR0FBR0UsSUFBSSxDQUFDQyxFQUFFLENBQUM7RUFBQSxPQUFPO0lBQ2pEQyxTQUFTLEVBQUUsU0FBWEEsU0FBU0EsQ0FBQUMsSUFBQTtNQUFBLElBQUtDLFFBQVEsR0FBQUQsSUFBQSxDQUFSQyxRQUFRO01BQUEsT0FBT0EsUUFBUTtJQUFBO0lBQ3JDQyxNQUFNLEVBQUUsU0FBUkEsTUFBTUEsQ0FBQUMsS0FBQTtNQUFBLElBQUtGLFFBQVEsR0FBQUUsS0FBQSxDQUFSRixRQUFRO01BQUEsT0FBT0EsUUFBUTtJQUFBO0VBQ3BDLENBQUM7QUFBQSxDQUFDLENBQUM7QUFFSSxJQUFNRyxzQkFBc0IsR0FBQVIsT0FBQSxDQUFBUSxzQkFBQSxHQUFHO0VBQ3BDQyxnQkFBZ0IsRUFBRSxDQUFDLENBQUM7RUFDcEJDLGNBQWMsRUFBRSxDQUFDLENBQUM7RUFDbEJDLHVCQUF1QixFQUFFLENBQUM7QUFDNUIsQ0FBQztBQUVNLElBQU1DLGlCQUFpQixHQUFBWixPQUFBLENBQUFZLGlCQUFBLEdBQUc7RUFDL0JDLGlCQUFpQixFQUFFLENBQUMsQ0FBQztFQUNyQkMsdUJBQXVCLEVBQUUsQ0FBQztBQUM1QixDQUFDO0FBQUMsSUFBQUMsUUFBQSxHQUFBZixPQUFBLENBQUFnQixPQUFBLEdBRWE7RUFDYmpCLG9CQUFvQixFQUFwQkEsb0JBQW9CO0VBQ3BCUyxzQkFBc0IsRUFBdEJBLHNCQUFzQjtFQUN0QkksaUJBQWlCLEVBQWpCQTtBQUNGLENBQUMiLCJpZ25vcmVMaXN0IjpbXX0=