// CRITICAL: Metro module loading fix MUST be absolutely first
import './src/utils/metroModuleLoadingFix';
// CRITICAL: Hermes engine protection MUST be absolutely first
import './src/utils/hermesEngineProtection';
// CRITICAL: Pre-bundle fix MUST be absolutely first
import './src/utils/preBundleFix';
// CRITICAL: Emergency module loader MUST be first to catch module loading errors
import './src/utils/emergencyModuleLoader';
// CRITICAL: Import Hermes fixes FIRST before any other modules
import './src/utils/hermesErrorFix';
// CRITICAL: Initialize unified error handling system IMMEDIATELY
import './src/services/unifiedErrorHandling';
// CRITICAL: Install theme property interceptor for emergency safety
import './src/utils/themePropertyInterceptor';
// CRITICAL: Apply targeted fix for 'medium' property errors
import './src/utils/mediumPropertyFix';
// CRITICAL: Runtime medium property fix for persistent errors
import './src/utils/runtimeMediumPropertyFix';
// CRITICAL: Initialize global theme safety system IMMEDIATELY
import './src/utils/globalThemeSafety';
// CRITICAL: Override theme system to ensure all theme access is safe
import './src/utils/themeOverride';
// CRITICAL: Initialize theme system early to prevent property access errors
import './src/utils/themeInitializer';
import './src/utils/hermesErrorFix';
import './src/utils/hermesCompatibility';
import './src/utils/hermesModuleResolutionFix';

// CRITICAL: Import Hermes compatibility fixes FIRST before any React Native modules
// This must be imported before any React Native components or libraries
import './src/utils/hermesModuleResolutionFix';

// Import React Native core initialization to prevent "Unknown named module" errors
import 'react-native/Libraries/Core/InitializeCore';

import { registerRootComponent } from 'expo';

import App from './App';

// registerRootComponent calls AppRegistry.registerComponent('main', () => App);
// It also ensures that whether you load the app in Expo Go or in a native build,
// the environment is set up appropriately
registerRootComponent(App);
