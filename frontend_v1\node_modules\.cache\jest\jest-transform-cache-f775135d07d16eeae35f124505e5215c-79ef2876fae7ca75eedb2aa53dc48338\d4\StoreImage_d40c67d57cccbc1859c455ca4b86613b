8203a0ba352983c3c7c6ea0bd81f0fb8
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.StoreImage = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _vectorIcons = require("@expo/vector-icons");
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _ThemeContext = require("../../contexts/ThemeContext");
var _imageAccessibilityUtils = require("../../utils/imageAccessibilityUtils");
var _responsiveUtils = require("../../utils/responsiveUtils");
var _storeImages = require("../../utils/storeImages");
var _LazyImage = require("../ui/LazyImage");
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var SIZE_CONFIG = {
  small: {
    width: 40,
    height: 40,
    borderRadius: 20,
    fontSize: 16,
    iconSize: 20
  },
  medium: {
    width: 60,
    height: 60,
    borderRadius: 30,
    fontSize: 24,
    iconSize: 30
  },
  large: {
    width: 80,
    height: 80,
    borderRadius: 40,
    fontSize: 32,
    iconSize: 40
  }
};
var StoreImage = exports.StoreImage = function StoreImage(_ref) {
  var providerId = _ref.providerId,
    providerName = _ref.providerName,
    category = _ref.category,
    imageUrl = _ref.imageUrl,
    _ref$size = _ref.size,
    size = _ref$size === void 0 ? 'medium' : _ref$size,
    style = _ref.style,
    imageStyle = _ref.imageStyle,
    _ref$showFallbackIcon = _ref.showFallbackIcon,
    showFallbackIcon = _ref$showFallbackIcon === void 0 ? true : _ref$showFallbackIcon,
    testID = _ref.testID;
  var _useTheme = (0, _ThemeContext.useTheme)(),
    colors = _useTheme.colors;
  var _useState = (0, _react.useState)(true),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    isLoading = _useState2[0],
    setIsLoading = _useState2[1];
  var _useState3 = (0, _react.useState)(false),
    _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
    hasError = _useState4[0],
    setHasError = _useState4[1];
  var sizeConfig = SIZE_CONFIG[size];
  var styles = createStyles(colors, sizeConfig);
  var finalImageUrl = imageUrl || (0, _storeImages.getStoreImage)(providerId, category);
  var handleImageLoad = function handleImageLoad() {
    setIsLoading(false);
    setHasError(false);
  };
  var handleImageError = function handleImageError() {
    setIsLoading(false);
    setHasError(true);
  };
  var renderFallback = function renderFallback() {
    if (showFallbackIcon) {
      return (0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
        name: "storefront-outline",
        size: sizeConfig.iconSize,
        color: colors.text.onPrimary
      });
    }
    return (0, _jsxRuntime.jsx)(_reactNative.Text, {
      style: styles.fallbackText,
      children: (providerName || 'P').charAt(0).toUpperCase()
    });
  };
  return (0, _jsxRuntime.jsxs)(_reactNative.View, {
    style: [styles.container, style],
    testID: testID,
    children: [(0, _jsxRuntime.jsx)(_LazyImage.LazyImage, {
      source: {
        uri: finalImageUrl
      },
      fallback: {
        uri: (0, _storeImages.getFallbackImage)(providerName)
      },
      width: sizeConfig.width,
      height: sizeConfig.height,
      lazy: true,
      threshold: 0.1,
      fadeInDuration: 300,
      onLoadStart: function onLoadStart() {
        return setIsLoading(true);
      },
      onLoadEnd: handleImageLoad,
      onError: handleImageError,
      containerStyle: styles.image,
      imageStyle: [styles.image, imageStyle],
      testID: `${testID}-lazy-image`,
      imageContext: _imageAccessibilityUtils.ImageContexts.storeImage(providerName),
      resizeMode: "cover"
    }), hasError && (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.fallbackContainer,
      children: renderFallback()
    })]
  });
};
var createStyles = function createStyles(colors, sizeConfig) {
  return _reactNative.StyleSheet.create({
    container: {
      width: (0, _responsiveUtils.getResponsiveSpacing)(sizeConfig.width),
      height: (0, _responsiveUtils.getResponsiveSpacing)(sizeConfig.height),
      borderRadius: (0, _responsiveUtils.getResponsiveSpacing)(sizeConfig.borderRadius),
      overflow: 'hidden',
      backgroundColor: colors.surface.secondary,
      borderWidth: 1,
      borderColor: colors.border.light,
      position: 'relative'
    },
    image: {
      width: '100%',
      height: '100%'
    },
    loadingContainer: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.surface.secondary
    },
    fallbackContainer: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.primary.default
    },
    fallbackText: {
      fontSize: (0, _responsiveUtils.getResponsiveFontSize)(sizeConfig.fontSize),
      fontWeight: '700',
      color: colors.text.onPrimary,
      textAlign: 'center'
    }
  });
};
var _default = exports.default = StoreImage;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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