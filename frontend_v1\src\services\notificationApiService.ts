/**
 * Notification API Service - Backend Integration for Notification System
 *
 * Component Contract:
 * - <PERSON>les all notification-related API calls to the backend
 * - Provides push notification registration and management
 * - Supports notification preferences and settings
 * - Integrates with backend notification endpoints
 * - <PERSON>les notification history and status updates
 * - Provides Expo Go compatible notification fallbacks
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Platform, Alert } from 'react-native';

import { apiClient } from './apiClient';
import { unifiedErrorHandlingService } from './unifiedErrorHandling';

// Types
export interface Notification {
  id: string;
  type:
    | 'booking_reminder'
    | 'booking_update'
    | 'message'
    | 'promotion'
    | 'system'
    | 'payment';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  priority: 'high' | 'medium' | 'low';
  action_url?: string;
  metadata?: {
    booking_id?: string;
    conversation_id?: string;
    provider_id?: string;
    amount?: number;
  };
}

export interface NotificationSettings {
  push_notifications: boolean;
  booking_reminders: boolean;
  booking_updates: boolean;
  messages: boolean;
  promotions: boolean;
  system_updates: boolean;
  payment_alerts: boolean;
  email_notifications: boolean;
  sms_notifications: boolean;
  quiet_hours_enabled: boolean;
  quiet_hours_start: string; // HH:MM format
  quiet_hours_end: string; // HH:MM format
}

export interface PushTokenRegistration {
  token: string;
  device_type: 'ios' | 'android' | 'web';
  device_id: string;
  app_version: string;
}

export interface NotificationResponse {
  notifications: Notification[];
  unread_count: number;
  total_count: number;
  page: number;
  has_next: boolean;
  has_previous: boolean;
}

class NotificationApiService {
  private readonly baseUrl = '/api/notifications';

  /**
   * Get user notifications with pagination
   */
  async getNotifications(
    page: number = 1,
    limit: number = 20,
    unread_only: boolean = false,
  ): Promise<NotificationResponse> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });

      if (unread_only) {
        params.append('unread_only', 'true');
      }

      const url = `${this.baseUrl}/?${params.toString()}`;
      const response = await apiClient.get<any>(url);

      return {
        notifications: response.data.results || [],
        unread_count: response.data.unread_count || 0,
        total_count: response.data.count || 0,
        page: page,
        has_next: !!response.data.next,
        has_previous: !!response.data.previous,
      };
    } catch (error) {
      console.error('Get notifications API error:', error);
      await unifiedErrorHandlingService.handleNetworkError(error as Error, {
        action: 'get_notifications',
        additionalData: { page, limit }
      });
      throw error;
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: string): Promise<void> {
    try {
      await apiClient.patch(`${this.baseUrl}/${notificationId}/`, {
        read: true,
      });
    } catch (error) {
      console.error('Mark notification as read API error:', error);
      await unifiedErrorHandlingService.handleNetworkError(error as Error, {
        action: 'mark_notification_read',
        additionalData: { notificationId }
      });
      throw error;
    }
  }

  /**
   * Mark all notifications as read
   */
  async markAllAsRead(): Promise<void> {
    try {
      await apiClient.post(`${this.baseUrl}/mark-all-read/`);
    } catch (error) {
      console.error('Mark all notifications as read API error:', error);
      throw error;
    }
  }

  /**
   * Delete notification
   */
  async deleteNotification(notificationId: string): Promise<void> {
    try {
      await apiClient.delete(`${this.baseUrl}/${notificationId}/`);
    } catch (error) {
      console.error('Delete notification API error:', error);
      throw error;
    }
  }

  /**
   * Clear all notifications
   */
  async clearAllNotifications(): Promise<void> {
    try {
      await apiClient.delete(`${this.baseUrl}/clear-all/`);
    } catch (error) {
      console.error('Clear all notifications API error:', error);
      throw error;
    }
  }

  /**
   * Get notification settings
   */
  async getNotificationSettings(): Promise<NotificationSettings> {
    try {
      const response = await apiClient.get<NotificationSettings>(
        `${this.baseUrl}/settings/`,
      );
      return response.data;
    } catch (error) {
      console.error('Get notification settings API error:', error);
      throw error;
    }
  }

  /**
   * Update notification settings
   */
  async updateNotificationSettings(
    settings: Partial<NotificationSettings>,
  ): Promise<NotificationSettings> {
    try {
      const response = await apiClient.patch<NotificationSettings>(
        `${this.baseUrl}/settings/`,
        settings,
      );
      return response.data;
    } catch (error) {
      console.error('Update notification settings API error:', error);
      throw error;
    }
  }

  /**
   * Register push notification token
   */
  async registerPushToken(tokenData: PushTokenRegistration): Promise<void> {
    try {
      await apiClient.post(`${this.baseUrl}/register-token/`, tokenData);
    } catch (error) {
      console.error('Register push token API error:', error);
      throw error;
    }
  }

  /**
   * Unregister push notification token
   */
  async unregisterPushToken(deviceId: string): Promise<void> {
    try {
      await apiClient.delete(`${this.baseUrl}/unregister-token/`, {
        data: { device_id: deviceId },
      });
    } catch (error) {
      console.error('Unregister push token API error:', error);
      throw error;
    }
  }

  /**
   * Get unread notification count
   */
  async getUnreadCount(): Promise<number> {
    try {
      const response = await apiClient.get<{ unread_count: number }>(
        `${this.baseUrl}/unread-count/`,
      );
      return response.data.unread_count;
    } catch (error) {
      console.error('Get unread count API error:', error);
      return 0;
    }
  }

  /**
   * Test push notification (for development)
   */
  async sendTestNotification(title: string, message: string): Promise<void> {
    try {
      await apiClient.post(`${this.baseUrl}/test-notification/`, {
        title,
        message,
      });
    } catch (error) {
      console.error('Send test notification API error:', error);
      throw error;
    }
  }

  /**
   * Get notification statistics
   */
  async getNotificationStats(): Promise<{
    total_sent: number;
    total_read: number;
    total_unread: number;
    by_type: Record<string, number>;
    by_priority: Record<string, number>;
  }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/stats/`);
      return response.data;
    } catch (error) {
      console.error('Get notification stats API error:', error);
      throw error;
    }
  }

  /**
   * Subscribe to notification topic (for categorized notifications)
   */
  async subscribeToTopic(topic: string): Promise<void> {
    try {
      await apiClient.post(`${this.baseUrl}/subscribe-topic/`, {
        topic,
      });
    } catch (error) {
      console.error('Subscribe to topic API error:', error);
      throw error;
    }
  }

  /**
   * Unsubscribe from notification topic
   */
  async unsubscribeFromTopic(topic: string): Promise<void> {
    try {
      await apiClient.post(`${this.baseUrl}/unsubscribe-topic/`, {
        topic,
      });
    } catch (error) {
      console.error('Unsubscribe from topic API error:', error);
      throw error;
    }
  }

  /**
   * Get user's subscribed topics
   */
  async getSubscribedTopics(): Promise<string[]> {
    try {
      const response = await apiClient.get<{ topics: string[] }>(
        `${this.baseUrl}/subscribed-topics/`,
      );
      return response.data.topics;
    } catch (error) {
      console.error('Get subscribed topics API error:', error);
      return [];
    }
  }
}

/**
 * Expo Go Compatible Notification Service
 * Provides fallback functionality when expo-notifications is not available
 */
class ExpoGoNotificationService {
  private isExpoGo(): boolean {
    // Simple detection: check if we're in a development environment
    // In Expo Go, __DEV__ is typically true and we can detect the environment
    return (
      __DEV__ && typeof global !== 'undefined' && global.expo !== undefined
    );
  }

  private isNotificationSupported(): boolean {
    // Always return false for now since we know expo-notifications causes issues in Expo Go SDK 53+
    // This provides a consistent fallback experience
    try {
      // Try to import expo-notifications dynamically
      const Notifications = require('expo-notifications');
      // Even if it imports, it may not work properly in Expo Go
      return false; // Force fallback for now to avoid runtime errors
    } catch {
      return false;
    }
  }

  async requestPermissionsAsync(): Promise<{
    status: 'granted' | 'denied' | 'undetermined';
  }> {
    if (!this.isNotificationSupported()) {
      // In Expo Go, we can't request actual notification permissions
      // Return a mock response and show user-friendly message
      Alert.alert(
        'Notifications Not Available',
        'Push notifications are not available in Expo Go. To enable notifications, please use a development build.',
        [{ text: 'OK' }],
      );
      return { status: 'denied' };
    }

    try {
      const Notifications = require('expo-notifications');
      return await Notifications.requestPermissionsAsync();
    } catch (error) {
      console.error('Failed to request notification permissions:', error);
      return { status: 'denied' };
    }
  }

  async scheduleNotificationAsync(notificationRequest: {
    content: {
      title: string;
      body: string;
      data?: any;
    };
    trigger: Date | number;
  }): Promise<string | null> {
    if (!this.isNotificationSupported()) {
      // In Expo Go, we can't schedule actual notifications
      // Show a user-friendly message and offer alternative
      Alert.alert(
        'Reminder Set',
        "Your reminder has been noted. Since you're using Expo Go, we'll send you an in-app reminder instead of a push notification.",
        [{ text: 'OK' }],
      );

      // Store the reminder in local storage or state for in-app handling
      this.scheduleInAppReminder(notificationRequest);
      return 'expo-go-fallback-' + Date.now();
    }

    try {
      const Notifications = require('expo-notifications');
      return await Notifications.scheduleNotificationAsync(notificationRequest);
    } catch (error) {
      console.error('Failed to schedule notification:', error);
      Alert.alert('Error', 'Failed to set reminder');
      return null;
    }
  }

  private scheduleInAppReminder(notificationRequest: any): void {
    // This could be enhanced to integrate with the app's reminder system
    // For now, we'll just log it and potentially store it for future in-app reminders
    console.log('In-app reminder scheduled:', notificationRequest);

    // TODO: Integrate with app's reminder/notification system
    // This could store the reminder in AsyncStorage or send it to the backend
    // to be delivered as an in-app notification when the user opens the app
  }

  async cancelNotificationAsync(notificationId: string): Promise<void> {
    if (!this.isNotificationSupported()) {
      // Handle cancellation for Expo Go fallback
      console.log('Cancelling in-app reminder:', notificationId);
      return;
    }

    try {
      const Notifications = require('expo-notifications');
      await Notifications.cancelScheduledNotificationAsync(notificationId);
    } catch (error) {
      console.error('Failed to cancel notification:', error);
    }
  }
}

export const notificationApiService = new NotificationApiService();
export const expoGoNotificationService = new ExpoGoNotificationService();
export default notificationApiService;
