/**
 * Store Customization Screen - Provider Store Appearance Settings
 *
 * Component Contract:
 * - Allows service providers to customize their store appearance
 * - Provides theme selection, layout options, and display preferences
 * - Integrates with portfolio management settings
 * - Follows responsive design and accessibility standards
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Switch,
  Alert,
} from 'react-native';

import { Box } from '../components/atoms/Box';
import { Button } from '../components/atoms/Button';
import { Card } from '../components/atoms/Card';
import { HeaderHelpButton } from '../components/help';
import { SafeAreaScreen } from '../components/ui/SafeAreaWrapper';
import { useTheme } from '../contexts/ThemeContext';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
  getMinimumTouchTarget,
} from '../utils/responsiveUtils';

interface StoreTheme {
  primaryColor: string;
  accentColor: string;
  layout: 'grid' | 'list' | 'card';
}

interface DisplayOptions {
  showPrices: boolean;
  showDuration: boolean;
  showRatings: boolean;
  showAvailability: boolean;
}

interface PortfolioSettings {
  portfolioType: 'upload' | 'instagram';
  instagramMaxPosts: number;
  showInstagramCaptions: boolean;
  portfolioLayout: 'grid' | 'masonry' | 'carousel';
}

export const StoreCustomizationScreen: React.FC = () => {
  const { colors } = useTheme();
  const navigation = useNavigation<any>();
  const styles = createStyles(colors);

  const [storeTheme, setStoreTheme] = useState<StoreTheme>({
    primaryColor: '#7C9A85',
    accentColor: '#A8C4A2',
    layout: 'card',
  });

  const [displayOptions, setDisplayOptions] = useState<DisplayOptions>({
    showPrices: true,
    showDuration: true,
    showRatings: true,
    showAvailability: true,
  });

  const [portfolioSettings, setPortfolioSettings] = useState<PortfolioSettings>(
    {
      portfolioType: 'instagram',
      instagramMaxPosts: 12,
      showInstagramCaptions: false,
      portfolioLayout: 'grid',
    },
  );

  const [hasChanges, setHasChanges] = useState(false);

  const themeColors = [
    { name: 'Sage Green', color: '#7C9A85' },
    { name: 'Lavender', color: '#B19CD9' },
    { name: 'Rose Gold', color: '#E8B4B8' },
    { name: 'Ocean Blue', color: '#6BB6FF' },
    { name: 'Warm Coral', color: '#FF8A80' },
    { name: 'Forest Green', color: '#4CAF50' },
  ];

  const layoutOptions = [
    { id: 'grid', name: 'Grid View', icon: 'grid-outline' },
    { id: 'list', name: 'List View', icon: 'list-outline' },
    { id: 'card', name: 'Card View', icon: 'card-outline' },
  ];

  const portfolioLayouts = [
    { id: 'grid', name: 'Grid', icon: 'grid-outline' },
    { id: 'masonry', name: 'Masonry', icon: 'apps-outline' },
    { id: 'carousel', name: 'Carousel', icon: 'swap-horizontal-outline' },
  ];

  const handleSaveChanges = () => {
    Alert.alert(
      'Save Changes',
      'Are you sure you want to save these customization changes?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Save',
          onPress: () => {
            // TODO: Implement API call to save customization
            setHasChanges(false);
            Alert.alert('Success', 'Your store customization has been saved.');
          },
        },
      ],
    );
  };

  const handleDiscardChanges = () => {
    Alert.alert(
      'Discard Changes',
      'Are you sure you want to discard your changes?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Discard',
          style: 'destructive',
          onPress: () => {
            setHasChanges(false);
            navigation.goBack();
          },
        },
      ],
    );
  };

  const updateStoreTheme = (updates: Partial<StoreTheme>) => {
    setStoreTheme(prev => ({ ...prev, ...updates }));
    setHasChanges(true);
  };

  const updateDisplayOptions = (updates: Partial<DisplayOptions>) => {
    setDisplayOptions(prev => ({ ...prev, ...updates }));
    setHasChanges(true);
  };

  const updatePortfolioSettings = (updates: Partial<PortfolioSettings>) => {
    setPortfolioSettings(prev => ({ ...prev, ...updates }));
    setHasChanges(true);
  };

  const renderColorPicker = () => (
    <Card style={styles.sectionCard}>
      <Text style={styles.sectionTitle}>Store Colors</Text>
      <Text style={styles.sectionDescription}>
        Choose colors that represent your brand
      </Text>

      <View style={styles.colorGrid}>
        {themeColors.map(colorOption => (
          <TouchableOpacity
            key={colorOption.color}
            style={[
              styles.colorOption,
              { backgroundColor: colorOption.color },
              storeTheme.primaryColor === colorOption.color &&
                styles.selectedColor,
            ]}
            onPress={() =>
              updateStoreTheme({ primaryColor: colorOption.color })
            }
            accessibilityLabel={`Select ${colorOption.name} theme`}
            accessibilityRole="button">
            {storeTheme.primaryColor === colorOption.color && (
              <Ionicons name="checkmark" size={20} color="#FFFFFF" />
            )}
          </TouchableOpacity>
        ))}
      </View>
    </Card>
  );

  const renderLayoutOptions = () => (
    <Card style={styles.sectionCard}>
      <Text style={styles.sectionTitle}>Layout Style</Text>
      <Text style={styles.sectionDescription}>
        Choose how your services are displayed
      </Text>

      <View style={styles.optionsGrid}>
        {layoutOptions.map(option => (
          <TouchableOpacity
            key={option.id}
            style={[
              styles.layoutOption,
              storeTheme.layout === option.id && styles.selectedLayout,
            ]}
            onPress={() => updateStoreTheme({ layout: option.id as any })}
            accessibilityLabel={`Select ${option.name} layout`}
            accessibilityRole="button">
            <Ionicons
              name={option.icon as any}
              size={24}
              color={
                storeTheme.layout === option.id
                  ? colors.sage600
                  : colors.sage300
              }
            />
            <Text
              style={[
                styles.layoutOptionText,
                storeTheme.layout === option.id && styles.selectedLayoutText,
              ]}>
              {option.name}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </Card>
  );

  const renderDisplaySettings = () => (
    <Card style={styles.sectionCard}>
      <Text style={styles.sectionTitle}>Display Options</Text>
      <Text style={styles.sectionDescription}>
        Control what information is shown to customers
      </Text>

      <View style={styles.settingsList}>
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>Show Prices</Text>
          <Switch
            value={displayOptions.showPrices}
            onValueChange={value => updateDisplayOptions({ showPrices: value })}
            trackColor={{ false: colors.sage200, true: colors.sage400 }}
            thumbColor={
              displayOptions.showPrices ? colors.sage600 : colors.sage300
            }
          />
        </View>

        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>Show Duration</Text>
          <Switch
            value={displayOptions.showDuration}
            onValueChange={value =>
              updateDisplayOptions({ showDuration: value })
            }
            trackColor={{ false: colors.sage200, true: colors.sage400 }}
            thumbColor={
              displayOptions.showDuration ? colors.sage600 : colors.sage300
            }
          />
        </View>

        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>Show Ratings</Text>
          <Switch
            value={displayOptions.showRatings}
            onValueChange={value =>
              updateDisplayOptions({ showRatings: value })
            }
            trackColor={{ false: colors.sage200, true: colors.sage400 }}
            thumbColor={
              displayOptions.showRatings ? colors.sage600 : colors.sage300
            }
          />
        </View>

        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>Show Availability</Text>
          <Switch
            value={displayOptions.showAvailability}
            onValueChange={value =>
              updateDisplayOptions({ showAvailability: value })
            }
            trackColor={{ false: colors.sage200, true: colors.sage400 }}
            thumbColor={
              displayOptions.showAvailability ? colors.sage600 : colors.sage300
            }
          />
        </View>
      </View>
    </Card>
  );

  const renderPortfolioSettings = () => (
    <Card style={styles.sectionCard}>
      <Text style={styles.sectionTitle}>Portfolio Settings</Text>
      <Text style={styles.sectionDescription}>
        Configure how your portfolio is displayed
      </Text>

      <View style={styles.portfolioTypeContainer}>
        <TouchableOpacity
          style={[
            styles.portfolioTypeOption,
            portfolioSettings.portfolioType === 'upload' &&
              styles.selectedPortfolioType,
          ]}
          onPress={() => updatePortfolioSettings({ portfolioType: 'upload' })}>
          <Ionicons
            name="cloud-upload-outline"
            size={24}
            color={colors.sage400}
          />
          <Text style={styles.portfolioTypeText}>Upload Photos</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.portfolioTypeOption,
            portfolioSettings.portfolioType === 'instagram' &&
              styles.selectedPortfolioType,
          ]}
          onPress={() =>
            updatePortfolioSettings({ portfolioType: 'instagram' })
          }>
          <Ionicons name="logo-instagram" size={24} color="#E4405F" />
          <Text style={styles.portfolioTypeText}>Instagram Sync</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.portfolioLayoutContainer}>
        <Text style={styles.subSectionTitle}>Portfolio Layout</Text>
        <View style={styles.optionsGrid}>
          {portfolioLayouts.map(layout => (
            <TouchableOpacity
              key={layout.id}
              style={[
                styles.layoutOption,
                portfolioSettings.portfolioLayout === layout.id &&
                  styles.selectedLayout,
              ]}
              onPress={() =>
                updatePortfolioSettings({ portfolioLayout: layout.id as any })
              }>
              <Ionicons
                name={layout.icon as any}
                size={20}
                color={
                  portfolioSettings.portfolioLayout === layout.id
                    ? colors.sage600
                    : colors.sage300
                }
              />
              <Text
                style={[
                  styles.layoutOptionText,
                  portfolioSettings.portfolioLayout === layout.id &&
                    styles.selectedLayoutText,
                ]}>
                {layout.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </Card>
  );

  return (
    <SafeAreaScreen style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() =>
              hasChanges ? handleDiscardChanges() : navigation.goBack()
            }
            accessibilityLabel="Go back"
            accessibilityRole="button">
            <Ionicons name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Store Customization</Text>
        </View>
        <HeaderHelpButton />
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}>
        {renderColorPicker()}
        {renderLayoutOptions()}
        {renderDisplaySettings()}
        {renderPortfolioSettings()}
      </ScrollView>

      {/* Save/Discard Actions */}
      {hasChanges && (
        <View style={styles.actionsContainer}>
          <Button
            title="Discard"
            onPress={handleDiscardChanges}
            variant="outline"
            style={styles.actionButton}
          />
          <Button
            title="Save Changes"
            onPress={handleSaveChanges}
            variant="primary"
            style={styles.actionButton}
          />
        </View>
      )}
    </SafeAreaScreen>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background?.primary || '#FFFFFF',
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: getResponsiveSpacing(20),
      paddingVertical: getResponsiveSpacing(16),
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.sage100,
    },
    headerLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    backButton: {
      marginRight: getResponsiveSpacing(16),
      padding: getResponsiveSpacing(4),
    },
    headerTitle: {
      fontSize: getResponsiveFontSize(20),
      fontWeight: '600',
      color: colors.text,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      padding: getResponsiveSpacing(20),
      paddingBottom: getResponsiveSpacing(120),
    },
    sectionCard: {
      marginBottom: getResponsiveSpacing(20),
    },
    sectionTitle: {
      fontSize: getResponsiveFontSize(18),
      fontWeight: '600',
      color: colors.text,
      marginBottom: getResponsiveSpacing(8),
    },
    sectionDescription: {
      fontSize: getResponsiveFontSize(14),
      color: colors.sage400,
      marginBottom: getResponsiveSpacing(16),
      lineHeight: 20,
    },
    colorGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginHorizontal: -getResponsiveSpacing(8),
    },
    colorOption: {
      width: 50,
      height: 50,
      borderRadius: 25,
      marginHorizontal: getResponsiveSpacing(8),
      marginBottom: getResponsiveSpacing(16),
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 3,
      borderColor: 'transparent',
    },
    selectedColor: {
      borderColor: colors.sage600,
    },
    optionsGrid: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    layoutOption: {
      flex: 1,
      alignItems: 'center',
      paddingVertical: getResponsiveSpacing(16),
      paddingHorizontal: getResponsiveSpacing(8),
      marginHorizontal: getResponsiveSpacing(4),
      borderRadius: 8,
      borderWidth: 1,
      borderColor: colors.sage200,
      backgroundColor: colors.surface,
    },
    selectedLayout: {
      borderColor: colors.sage600,
      backgroundColor: colors.sage50,
    },
    layoutOptionText: {
      fontSize: getResponsiveFontSize(12),
      color: colors.sage400,
      marginTop: getResponsiveSpacing(8),
      textAlign: 'center',
    },
    selectedLayoutText: {
      color: colors.sage600,
      fontWeight: '600',
    },
    settingsList: {
      gap: getResponsiveSpacing(16),
    },
    settingItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: getResponsiveSpacing(4),
    },
    settingLabel: {
      fontSize: getResponsiveFontSize(16),
      color: colors.text,
    },
    portfolioTypeContainer: {
      flexDirection: 'row',
      marginBottom: getResponsiveSpacing(20),
      gap: getResponsiveSpacing(12),
    },
    portfolioTypeOption: {
      flex: 1,
      alignItems: 'center',
      paddingVertical: getResponsiveSpacing(16),
      borderRadius: 8,
      borderWidth: 1,
      borderColor: colors.sage200,
      backgroundColor: colors.surface,
    },
    selectedPortfolioType: {
      borderColor: colors.sage600,
      backgroundColor: colors.sage50,
    },
    portfolioTypeText: {
      fontSize: getResponsiveFontSize(14),
      color: colors.text,
      marginTop: getResponsiveSpacing(8),
      textAlign: 'center',
    },
    portfolioLayoutContainer: {
      marginTop: getResponsiveSpacing(16),
    },
    subSectionTitle: {
      fontSize: getResponsiveFontSize(16),
      fontWeight: '600',
      color: colors.text,
      marginBottom: getResponsiveSpacing(12),
    },
    actionsContainer: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      flexDirection: 'row',
      paddingHorizontal: getResponsiveSpacing(20),
      paddingVertical: getResponsiveSpacing(16),
      backgroundColor: colors.surface,
      borderTopWidth: 1,
      borderTopColor: colors.sage100,
      gap: getResponsiveSpacing(12),
    },
    actionButton: {
      flex: 1,
    },
  });
