40ad1829314235cb87c69ef5f6d3473a
_getJestObj().mock('react-native', function () {
  return {
    AccessibilityInfo: {
      isScreenReaderEnabled: jest.fn(),
      announceForAccessibility: jest.fn(),
      setAccessibilityFocus: jest.fn()
    },
    Platform: {
      OS: 'ios'
    }
  };
});
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _accessibility = require("../accessibility");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
describe('Accessibility Utilities', function () {
  beforeEach(function () {
    jest.clearAllMocks();
  });
  describe('WCAG_CONSTANTS', function () {
    it('defines correct contrast ratios', function () {
      expect(_accessibility.WCAG_CONSTANTS.CONTRAST_RATIOS.NORMAL_TEXT).toBe(4.5);
      expect(_accessibility.WCAG_CONSTANTS.CONTRAST_RATIOS.LARGE_TEXT).toBe(3.0);
      expect(_accessibility.WCAG_CONSTANTS.CONTRAST_RATIOS.NON_TEXT).toBe(3.0);
    });
    it('defines correct touch target sizes', function () {
      expect(_accessibility.WCAG_CONSTANTS.TOUCH_TARGET.MIN_SIZE).toBe(44);
      expect(_accessibility.WCAG_CONSTANTS.TOUCH_TARGET.RECOMMENDED_SIZE).toBe(48);
    });
    it('defines correct animation timing', function () {
      expect(_accessibility.WCAG_CONSTANTS.ANIMATION.MAX_DURATION).toBe(5000);
      expect(_accessibility.WCAG_CONSTANTS.ANIMATION.REDUCED_MOTION_DURATION).toBe(200);
    });
  });
  describe('ScreenReaderUtils', function () {
    describe('generateFormFieldLabel', function () {
      it('generates basic label', function () {
        var label = _accessibility.ScreenReaderUtils.generateFormFieldLabel('Email');
        expect(label).toBe('Email');
      });
      it('adds required indicator', function () {
        var label = _accessibility.ScreenReaderUtils.generateFormFieldLabel('Email', true);
        expect(label).toBe('Email, required');
      });
      it('adds error message', function () {
        var label = _accessibility.ScreenReaderUtils.generateFormFieldLabel('Email', false, 'Invalid email');
        expect(label).toBe('Email, error: Invalid email');
      });
      it('combines required and error', function () {
        var label = _accessibility.ScreenReaderUtils.generateFormFieldLabel('Email', true, 'Invalid email');
        expect(label).toBe('Email, required, error: Invalid email');
      });
    });
    describe('generateInteractionHint', function () {
      it('generates basic hint', function () {
        var hint = _accessibility.ScreenReaderUtils.generateInteractionHint('submit');
        expect(hint).toBe('Double tap to submit');
      });
      it('adds additional info', function () {
        var hint = _accessibility.ScreenReaderUtils.generateInteractionHint('submit', 'This will save your changes');
        expect(hint).toBe('Double tap to submit. This will save your changes');
      });
    });
  });
  describe('ColorContrastUtils', function () {
    describe('getRelativeLuminance', function () {
      it('calculates luminance for white', function () {
        var luminance = _accessibility.ColorContrastUtils.getRelativeLuminance('#FFFFFF');
        expect(luminance).toBeCloseTo(1, 2);
      });
      it('calculates luminance for black', function () {
        var luminance = _accessibility.ColorContrastUtils.getRelativeLuminance('#000000');
        expect(luminance).toBeCloseTo(0, 2);
      });
      it('calculates luminance for gray', function () {
        var luminance = _accessibility.ColorContrastUtils.getRelativeLuminance('#808080');
        expect(luminance).toBeGreaterThan(0);
        expect(luminance).toBeLessThan(1);
      });
    });
    describe('getContrastRatio', function () {
      it('calculates maximum contrast ratio', function () {
        var ratio = _accessibility.ColorContrastUtils.getContrastRatio('#FFFFFF', '#000000');
        expect(ratio).toBeCloseTo(21, 0);
      });
      it('calculates minimum contrast ratio', function () {
        var ratio = _accessibility.ColorContrastUtils.getContrastRatio('#FFFFFF', '#FFFFFF');
        expect(ratio).toBeCloseTo(1, 2);
      });
      it('calculates intermediate contrast ratio', function () {
        var ratio = _accessibility.ColorContrastUtils.getContrastRatio('#FFFFFF', '#808080');
        expect(ratio).toBeGreaterThan(1);
        expect(ratio).toBeLessThan(21);
      });
    });
    describe('meetsWCAGAA', function () {
      it('passes for high contrast combinations', function () {
        var passes = _accessibility.ColorContrastUtils.meetsWCAGAA('#FFFFFF', '#000000');
        expect(passes).toBe(true);
      });
      it('fails for low contrast combinations', function () {
        var passes = _accessibility.ColorContrastUtils.meetsWCAGAA('#FFFFFF', '#F0F0F0');
        expect(passes).toBe(false);
      });
      it('uses different thresholds for large text', function () {
        var normalText = _accessibility.ColorContrastUtils.meetsWCAGAA('#FFFFFF', '#777777', false);
        var largeText = _accessibility.ColorContrastUtils.meetsWCAGAA('#FFFFFF', '#777777', true);
        expect(normalText).toBe(false);
        expect(largeText).toBe(true);
      });
    });
    describe('suggestAccessibleColor', function () {
      it('returns null for already accessible combinations', function () {
        var suggestion = _accessibility.ColorContrastUtils.suggestAccessibleColor('#FFFFFF', '#000000');
        expect(suggestion).toBeNull();
      });
      it('suggests darker color for low contrast', function () {
        var suggestion = _accessibility.ColorContrastUtils.suggestAccessibleColor('#F0F0F0', '#FFFFFF');
        expect(suggestion).toBeTruthy();
        if (suggestion) {
          expect(suggestion).toMatch(/^#[0-9A-Fa-f]{6}$/);
        }
      });
    });
  });
  describe('SemanticMarkupUtils', function () {
    describe('generateHeadingProps', function () {
      it('generates heading props', function () {
        var props = _accessibility.SemanticMarkupUtils.generateHeadingProps(1, 'Main Title');
        expect(props).toEqual({
          accessibilityRole: 'header',
          accessibilityLevel: 1,
          accessibilityLabel: 'Main Title'
        });
      });
      it('handles different heading levels', function () {
        var props = _accessibility.SemanticMarkupUtils.generateHeadingProps(3, 'Subtitle');
        expect(props.accessibilityLevel).toBe(3);
      });
    });
    describe('generateListProps', function () {
      it('generates list props', function () {
        var props = _accessibility.SemanticMarkupUtils.generateListProps(5);
        expect(props).toEqual({
          accessibilityRole: 'list',
          accessibilityLabel: 'List with 5 items'
        });
      });
    });
    describe('generateListItemProps', function () {
      it('generates list item props', function () {
        var props = _accessibility.SemanticMarkupUtils.generateListItemProps(0, 3, 'First item');
        expect(props).toEqual({
          accessibilityRole: 'listitem',
          accessibilityLabel: 'First item, 1 of 3'
        });
      });
    });
    describe('generateButtonProps', function () {
      it('generates basic button props', function () {
        var props = _accessibility.SemanticMarkupUtils.generateButtonProps('Submit');
        expect(props.accessibilityRole).toBe('button');
        expect(props.accessibilityLabel).toBe('Submit');
      });
      it('includes action hint', function () {
        var props = _accessibility.SemanticMarkupUtils.generateButtonProps('Submit', 'save form');
        expect(props.accessibilityHint).toBe('Double tap to save form');
      });
      it('includes state', function () {
        var state = {
          disabled: true
        };
        var props = _accessibility.SemanticMarkupUtils.generateButtonProps('Submit', undefined, state);
        expect(props.accessibilityState).toEqual(state);
      });
    });
    describe('generateInputProps', function () {
      it('generates basic input props', function () {
        var props = _accessibility.SemanticMarkupUtils.generateInputProps('Email');
        expect(props.accessibilityLabel).toBe('Email');
        expect(props.accessibilityState).toEqual({
          disabled: false
        });
      });
      it('includes value', function () {
        var props = _accessibility.SemanticMarkupUtils.generateInputProps('Email', '<EMAIL>');
        expect(props.accessibilityValue).toEqual({
          text: '<EMAIL>'
        });
      });
      it('includes required and error info', function () {
        var props = _accessibility.SemanticMarkupUtils.generateInputProps('Email', undefined, true, 'Invalid email');
        expect(props.accessibilityLabel).toBe('Email, required, error: Invalid email');
      });
    });
  });
  describe('AccessibilityTestUtils', function () {
    describe('validateAccessibilityProps', function () {
      it('passes for well-formed interactive element', function () {
        var props = {
          accessibilityRole: 'button',
          accessibilityLabel: 'Submit',
          onPress: jest.fn()
        };
        var issues = _accessibility.AccessibilityTestUtils.validateAccessibilityProps(props);
        expect(issues).toHaveLength(0);
      });
      it('flags missing accessibility role', function () {
        var props = {
          onPress: jest.fn()
        };
        var issues = _accessibility.AccessibilityTestUtils.validateAccessibilityProps(props);
        expect(issues).toContain('Interactive element missing accessibilityRole');
      });
      it('flags missing accessibility label', function () {
        var props = {
          accessibilityRole: 'button'
        };
        var issues = _accessibility.AccessibilityTestUtils.validateAccessibilityProps(props);
        expect(issues).toContain('Element missing accessibilityLabel or text content');
      });
      it('flags small touch targets', function () {
        var props = {
          accessibilityRole: 'button',
          accessibilityLabel: 'Small button',
          style: {
            width: 30,
            height: 30
          }
        };
        var issues = _accessibility.AccessibilityTestUtils.validateAccessibilityProps(props);
        expect(issues).toContain('Touch target too small: 30x30. Minimum: 44x44');
      });
      it('passes for adequate touch targets', function () {
        var props = {
          accessibilityRole: 'button',
          accessibilityLabel: 'Good button',
          style: {
            width: 48,
            height: 48
          }
        };
        var issues = _accessibility.AccessibilityTestUtils.validateAccessibilityProps(props);
        expect(issues).toHaveLength(0);
      });
    });
    describe('generateAccessibilityReport', function () {
      it('generates report for component tree', function () {
        var componentTree = [{
          type: 'Button',
          props: {
            accessibilityRole: 'button',
            accessibilityLabel: 'Good button',
            onPress: jest.fn()
          }
        }, {
          type: 'Button',
          props: {
            onPress: jest.fn()
          }
        }];
        var report = _accessibility.AccessibilityTestUtils.generateAccessibilityReport(componentTree);
        expect(report.passed).toBe(1);
        expect(report.failed).toBe(1);
        expect(report.issues).toHaveLength(1);
        expect(report.issues[0].component).toBe('Button');
        expect(report.issues[0].issues).toContain('Interactive element missing accessibilityRole');
      });
      it('handles empty component tree', function () {
        var report = _accessibility.AccessibilityTestUtils.generateAccessibilityReport([]);
        expect(report.passed).toBe(0);
        expect(report.failed).toBe(0);
        expect(report.issues).toHaveLength(0);
      });
    });
  });
  describe('AdvancedAccessibilityUtils', function () {
    beforeEach(function () {
      jest.clearAllMocks();
    });
    describe('announceLiveRegion', function () {
      it('should announce with polite priority', function () {
        var mockAccessibilityInfo = require('react-native').AccessibilityInfo;
        _accessibility.AdvancedAccessibilityUtils.announceLiveRegion('Test message', 'polite');
        expect(mockAccessibilityInfo.announceForAccessibility).toHaveBeenCalledWith('Test message');
      });
      it('should announce with assertive priority', function () {
        var mockAccessibilityInfo = require('react-native').AccessibilityInfo;
        _accessibility.AdvancedAccessibilityUtils.announceLiveRegion('Urgent message', 'assertive');
        expect(mockAccessibilityInfo.announceForAccessibility).toHaveBeenCalledWith('Urgent message');
      });
    });
    describe('announceContentChange', function () {
      it('should announce content changes', function () {
        var mockAccessibilityInfo = require('react-native').AccessibilityInfo;
        _accessibility.AdvancedAccessibilityUtils.announceContentChange('Item', 'added', 'to cart');
        expect(mockAccessibilityInfo.announceForAccessibility).toHaveBeenCalledWith('Item added: to cart');
      });
    });
    describe('announceFormValidation', function () {
      it('should announce valid form field', function () {
        var mockAccessibilityInfo = require('react-native').AccessibilityInfo;
        _accessibility.AdvancedAccessibilityUtils.announceFormValidation('Email', true);
        expect(mockAccessibilityInfo.announceForAccessibility).toHaveBeenCalledWith('Email is valid');
      });
      it('should announce invalid form field', function () {
        var mockAccessibilityInfo = require('react-native').AccessibilityInfo;
        _accessibility.AdvancedAccessibilityUtils.announceFormValidation('Email', false, 'Invalid format');
        expect(mockAccessibilityInfo.announceForAccessibility).toHaveBeenCalledWith('Email error: Invalid format');
      });
    });
    describe('announceProgress', function () {
      it('should announce progress updates', function () {
        var mockAccessibilityInfo = require('react-native').AccessibilityInfo;
        _accessibility.AdvancedAccessibilityUtils.announceProgress(3, 5, 'Upload');
        expect(mockAccessibilityInfo.announceForAccessibility).toHaveBeenCalledWith('Upload: 60% complete, 3 of 5');
      });
    });
    describe('announceLoadingState', function () {
      it('should announce loading start', function () {
        var mockAccessibilityInfo = require('react-native').AccessibilityInfo;
        _accessibility.AdvancedAccessibilityUtils.announceLoadingState(true, 'Data');
        expect(mockAccessibilityInfo.announceForAccessibility).toHaveBeenCalledWith('Data loading');
      });
      it('should announce loading complete', function () {
        var mockAccessibilityInfo = require('react-native').AccessibilityInfo;
        _accessibility.AdvancedAccessibilityUtils.announceLoadingState(false, 'Data');
        expect(mockAccessibilityInfo.announceForAccessibility).toHaveBeenCalledWith('Data loaded');
      });
    });
  });
  describe('AccessibilityMonitoringUtils', function () {
    beforeEach(function () {
      jest.clearAllMocks();
    });
    describe('trackAccessibilityUsage', function () {
      it('should track accessibility usage statistics', (0, _asyncToGenerator2.default)(function* () {
        var mockAccessibilityInfo = require('react-native').AccessibilityInfo;
        mockAccessibilityInfo.isScreenReaderEnabled.mockResolvedValue(true);
        var stats = yield _accessibility.AccessibilityMonitoringUtils.trackAccessibilityUsage();
        expect(stats.screenReaderUsage).toBe(1);
        expect(stats.reducedMotionPreference).toBe(false);
      }));
    });
    describe('validateCompliance', function () {
      it('should validate accessibility compliance', (0, _asyncToGenerator2.default)(function* () {
        var mockAccessibilityInfo = require('react-native').AccessibilityInfo;
        mockAccessibilityInfo.isScreenReaderEnabled.mockResolvedValue(true);
        var compliance = yield _accessibility.AccessibilityMonitoringUtils.validateCompliance();
        expect(compliance.screenReaderSupport).toBe(true);
        expect(compliance.keyboardNavigation).toBe(true);
        expect(compliance.colorContrast).toBe(true);
        expect(compliance.touchTargets).toBe(true);
        expect(compliance.textScaling).toBe(true);
      }));
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************