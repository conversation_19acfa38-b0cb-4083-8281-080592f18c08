8d648ec580270d3f5934b85140e28896
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Input = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _ThemeContext = require("../../contexts/ThemeContext");
var _Text = require("./Text");
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["label", "placeholder", "value", "onChangeText", "disabled", "error", "helperText", "success", "size", "variant", "required", "leftIcon", "rightIcon", "containerStyle", "inputStyle", "accessibilityLabel", "accessibilityHint", "onFocus", "onBlur"];
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var Input = exports.Input = (0, _react.forwardRef)(function (_ref, ref) {
  var _colors$text, _colors$gray;
  var label = _ref.label,
    placeholder = _ref.placeholder,
    value = _ref.value,
    onChangeText = _ref.onChangeText,
    _ref$disabled = _ref.disabled,
    disabled = _ref$disabled === void 0 ? false : _ref$disabled,
    error = _ref.error,
    helperText = _ref.helperText,
    success = _ref.success,
    _ref$size = _ref.size,
    size = _ref$size === void 0 ? 'md' : _ref$size,
    _ref$variant = _ref.variant,
    variant = _ref$variant === void 0 ? 'default' : _ref$variant,
    _ref$required = _ref.required,
    required = _ref$required === void 0 ? false : _ref$required,
    leftIcon = _ref.leftIcon,
    rightIcon = _ref.rightIcon,
    containerStyle = _ref.containerStyle,
    inputStyle = _ref.inputStyle,
    accessibilityLabel = _ref.accessibilityLabel,
    accessibilityHint = _ref.accessibilityHint,
    onFocus = _ref.onFocus,
    onBlur = _ref.onBlur,
    props = (0, _objectWithoutProperties2.default)(_ref, _excluded);
  var _useTheme = (0, _ThemeContext.useTheme)(),
    colors = _useTheme.colors;
  var _useState = (0, _react.useState)(false),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    isFocused = _useState2[0],
    setIsFocused = _useState2[1];
  var styles = createStyles(colors);
  var handleFocus = function handleFocus(e) {
    setIsFocused(true);
    onFocus == null || onFocus(e);
  };
  var handleBlur = function handleBlur(e) {
    setIsFocused(false);
    onBlur == null || onBlur(e);
  };
  var hasError = Boolean(error);
  var hasSuccess = Boolean(success) && !hasError;
  var isDisabled = disabled;
  var containerStyleArray = [styles.container, containerStyle].filter(Boolean);
  var inputContainerStyleArray = [styles.inputContainer, styles[variant], styles[size], isFocused && styles.focused, hasError && styles.error, hasSuccess && styles.success, isDisabled && styles.disabled].filter(Boolean);
  var inputStyleArray = [styles.input, styles[`${size}Input`], isDisabled && styles.disabledInput, inputStyle].filter(Boolean);
  return (0, _jsxRuntime.jsxs)(_reactNative.View, {
    style: containerStyleArray,
    children: [label && (0, _jsxRuntime.jsxs)(_Text.Text, {
      variant: "label",
      color: hasError ? 'error' : 'primary',
      style: styles.label,
      children: [label, required && (0, _jsxRuntime.jsx)(_Text.Text, {
        color: "error",
        children: " *"
      })]
    }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: inputContainerStyleArray,
      children: [leftIcon && (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.leftIconContainer,
        children: leftIcon
      }), (0, _jsxRuntime.jsx)(_reactNative.TextInput, Object.assign({
        ref: ref,
        style: inputStyleArray,
        placeholder: placeholder,
        placeholderTextColor: ((_colors$text = colors.text) == null ? void 0 : _colors$text.secondary) || ((_colors$gray = colors.gray) == null ? void 0 : _colors$gray[400]) || '#9CA3AF',
        value: value,
        onChangeText: onChangeText,
        editable: !isDisabled,
        onFocus: handleFocus,
        onBlur: handleBlur,
        accessibilityLabel: accessibilityLabel || label,
        accessibilityHint: accessibilityHint,
        accessibilityRole: "none",
        accessibilityState: {
          disabled: isDisabled,
          invalid: hasError
        }
      }, props)), rightIcon && (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.rightIconContainer,
        children: rightIcon
      })]
    }), (error || helperText || success) && (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.messageContainer,
      children: [error && (0, _jsxRuntime.jsx)(_Text.Text, {
        variant: "caption",
        color: "error",
        style: styles.message,
        children: error
      }), success && !error && (0, _jsxRuntime.jsx)(_Text.Text, {
        variant: "caption",
        color: "success",
        style: styles.message,
        children: success
      }), helperText && !error && !success && (0, _jsxRuntime.jsx)(_Text.Text, {
        variant: "caption",
        color: "tertiary",
        style: styles.message,
        children: helperText
      })]
    })]
  });
});
var createStyles = function createStyles(colors) {
  var _colors$surface, _colors$text2, _colors$surface2, _colors$text3, _colors$text4, _colors$primary, _colors$surface3, _colors$text5, _colors$text6;
  return _reactNative.StyleSheet.create({
    container: {
      width: '100%'
    },
    label: {
      marginBottom: 4
    },
    inputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: 8,
      borderWidth: 1,
      backgroundColor: ((_colors$surface = colors.surface) == null ? void 0 : _colors$surface.primary) || '#FFFFFF',
      minHeight: 44
    },
    default: {
      borderColor: ((_colors$text2 = colors.text) == null ? void 0 : _colors$text2.tertiary) || '#9CA3AF',
      backgroundColor: ((_colors$surface2 = colors.surface) == null ? void 0 : _colors$surface2.primary) || '#FFFFFF'
    },
    outline: {
      borderColor: ((_colors$text3 = colors.text) == null ? void 0 : _colors$text3.tertiary) || '#9CA3AF',
      backgroundColor: 'transparent'
    },
    minimal: {
      borderWidth: 0,
      borderBottomWidth: 1,
      borderRadius: 0,
      borderBottomColor: ((_colors$text4 = colors.text) == null ? void 0 : _colors$text4.tertiary) || '#9CA3AF',
      backgroundColor: 'transparent'
    },
    sm: {
      minHeight: 36,
      paddingHorizontal: 12,
      paddingVertical: 8
    },
    md: {
      minHeight: 44,
      paddingHorizontal: 16,
      paddingVertical: 12
    },
    lg: {
      minHeight: 52,
      paddingHorizontal: 20,
      paddingVertical: 16
    },
    focused: {
      borderColor: ((_colors$primary = colors.primary) == null ? void 0 : _colors$primary.default) || '#4A6B52',
      borderWidth: 2
    },
    error: {
      borderColor: '#EF4444'
    },
    success: {
      borderColor: '#10B981'
    },
    disabled: {
      opacity: 0.5,
      backgroundColor: ((_colors$surface3 = colors.surface) == null ? void 0 : _colors$surface3.secondary) || '#F9FAFB'
    },
    input: {
      flex: 1,
      fontFamily: 'System',
      fontSize: 16,
      color: ((_colors$text5 = colors.text) == null ? void 0 : _colors$text5.primary) || '#1A1A1A',
      padding: 0
    },
    smInput: {
      fontSize: 14
    },
    mdInput: {
      fontSize: 16
    },
    lgInput: {
      fontSize: 18
    },
    disabledInput: {
      color: ((_colors$text6 = colors.text) == null ? void 0 : _colors$text6.tertiary) || '#9CA3AF'
    },
    leftIconContainer: {
      marginRight: 8,
      justifyContent: 'center',
      alignItems: 'center'
    },
    rightIconContainer: {
      marginLeft: 8,
      justifyContent: 'center',
      alignItems: 'center'
    },
    messageContainer: {
      marginTop: 4
    },
    message: {
      fontSize: 12
    }
  });
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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