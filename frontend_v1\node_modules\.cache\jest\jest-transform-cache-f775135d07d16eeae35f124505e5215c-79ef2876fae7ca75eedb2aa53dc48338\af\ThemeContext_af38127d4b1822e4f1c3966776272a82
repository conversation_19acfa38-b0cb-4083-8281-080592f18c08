ed531a0d96ed4e51e70cee9da7e90603
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useTheme = exports.ThemeProvider = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _asyncStorage = _interopRequireDefault(require("@react-native-async-storage/async-storage"));
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _accessibilityUtils = require("../utils/accessibilityUtils");
var _globalErrorInterceptor = require("../utils/globalErrorInterceptor");
var _moduleInitializer = require("../utils/moduleInitializer");
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var colorsModule = (0, _moduleInitializer.safeModuleLoader)('Colors', function () {
  var module = require("../constants/Colors");
  if (!module.Colors || !module.DarkModeColors) {
    throw new Error('Colors module missing required exports');
  }
  return module;
}, {
  Colors: {
    primary: {
      default: '#4A6B52',
      light: '#6B8A74',
      dark: '#2A4B32',
      contrast: '#FFFFFF'
    },
    text: {
      primary: '#1A1A1A',
      secondary: '#6B7280',
      tertiary: '#9CA3AF'
    },
    background: {
      primary: '#FFFFFF',
      secondary: '#F9FAFB',
      tertiary: '#F3F4F6'
    },
    surface: {
      primary: '#FFFFFF',
      secondary: '#F9FAFB',
      tertiary: '#F3F4F6'
    }
  },
  DarkModeColors: {
    sage200: '#1F3A26',
    sage300: '#2A4B32',
    sage400: '#4A6B52',
    sage500: '#5A7A63',
    sage600: '#6B8A74'
  }
});
var Colors = colorsModule.Colors;
var DarkModeColors = colorsModule.DarkModeColors;
if (!(0, _moduleInitializer.checkModuleHealth)('Colors', Colors)) {
  console.error('[ThemeContext] Colors module failed health check');
}
var DarkColors = Object.assign({}, Colors, {
  background: {
    primary: '#121212',
    secondary: '#1E1E1E',
    tertiary: '#2C2C2C',
    elevated: '#1F1F1F',
    overlay: 'rgba(0, 0, 0, 0.8)',
    sage: '#2A4B32'
  },
  surface: {
    primary: '#1E1E1E',
    secondary: '#2C2C2C',
    tertiary: '#383838',
    inverse: '#F9FAFB',
    disabled: '#2C2C2C'
  },
  text: {
    primary: '#FFFFFF',
    secondary: '#D1D5DB',
    tertiary: '#9CA3AF',
    inverse: '#1A1A1A',
    disabled: '#6B7280',
    onPrimary: '#FFFFFF',
    onSecondary: '#FFFFFF',
    link: (DarkModeColors == null ? void 0 : DarkModeColors.sage600) || '#6B8A74',
    linkHover: (DarkModeColors == null ? void 0 : DarkModeColors.sage500) || '#5A7A63'
  },
  border: {
    light: '#404040',
    medium: '#525252',
    dark: '#737373',
    focus: (DarkModeColors == null ? void 0 : DarkModeColors.sage600) || '#6B8A74',
    error: '#F87171',
    success: '#34D399'
  },
  primary: {
    default: (DarkModeColors == null ? void 0 : DarkModeColors.sage300) || '#2A4B32',
    light: (DarkModeColors == null ? void 0 : DarkModeColors.sage400) || '#4A6B52',
    dark: (DarkModeColors == null ? void 0 : DarkModeColors.sage200) || '#1F3A26',
    contrast: '#FFFFFF'
  },
  primaryDark: (DarkModeColors == null ? void 0 : DarkModeColors.sage200) || '#1F3A26',
  primaryLight: (DarkModeColors == null ? void 0 : DarkModeColors.sage400) || '#4A6B52',
  sage400: (DarkModeColors == null ? void 0 : DarkModeColors.sage300) || '#2A4B32',
  sage500: (DarkModeColors == null ? void 0 : DarkModeColors.sage500) || '#4A6B52',
  sage600: (DarkModeColors == null ? void 0 : DarkModeColors.sage600) || '#6B8A74'
});
var ThemeContext = (0, _react.createContext)(undefined);
var THEME_STORAGE_KEY = '@vierla_theme_preference';
var ThemeProvider = exports.ThemeProvider = function ThemeProvider(_ref) {
  var children = _ref.children;
  var systemColorScheme = (0, _reactNative.useColorScheme)();
  var _useState = (0, _react.useState)(false),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    isDark = _useState2[0],
    setIsDark = _useState2[1];
  (0, _react.useEffect)(function () {
    var loadThemePreference = function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        try {
          var savedTheme = yield _asyncStorage.default.getItem(THEME_STORAGE_KEY);
          if (savedTheme !== null) {
            setIsDark(savedTheme === 'dark');
          } else {
            setIsDark(systemColorScheme === 'dark');
          }
        } catch (error) {
          console.warn('Failed to load theme preference:', error);
          setIsDark(systemColorScheme === 'dark');
        }
      });
      return function loadThemePreference() {
        return _ref2.apply(this, arguments);
      };
    }();
    loadThemePreference();
  }, [systemColorScheme]);
  var currentColors = _react.default.useMemo(function () {
    if ((0, _globalErrorInterceptor.hasRecentThemeErrors)()) {
      console.warn('[ThemeContext] Recent theme errors detected, using fallback theme');
      var fallbackTheme = (0, _globalErrorInterceptor.getFallbackTheme)();
      return fallbackTheme.colors;
    }
    var colors = isDark ? DarkColors : Colors;
    if (!colors || typeof colors !== 'object') {
      console.warn('[ThemeContext] Theme colors object is invalid, using fallback');
      var _fallbackTheme = (0, _globalErrorInterceptor.getFallbackTheme)();
      return _fallbackTheme.colors;
    }
    if (!colors.primary) {
      console.warn('[ThemeContext] Theme colors missing primary property, adding fallback');
      return Object.assign({}, colors, {
        primary: Colors.primary
      });
    }
    return colors;
  }, [isDark]);
  var saveThemePreference = function () {
    var _ref3 = (0, _asyncToGenerator2.default)(function* (darkMode) {
      try {
        yield _asyncStorage.default.setItem(THEME_STORAGE_KEY, darkMode ? 'dark' : 'light');
      } catch (error) {
        console.warn('Failed to save theme preference:', error);
      }
    });
    return function saveThemePreference(_x) {
      return _ref3.apply(this, arguments);
    };
  }();
  var toggleTheme = function toggleTheme() {
    var newIsDark = !isDark;
    setIsDark(newIsDark);
    saveThemePreference(newIsDark);
  };
  var setTheme = function setTheme(darkMode) {
    setIsDark(darkMode);
    saveThemePreference(darkMode);
  };
  var safeColors = _react.default.useMemo(function () {
    if (!currentColors || typeof currentColors !== 'object') {
      console.warn('Current colors is invalid, using fallback Colors');
      return Colors;
    }
    var requiredProperties = ['primary', 'text', 'background', 'surface'];
    var missingProperties = requiredProperties.filter(function (prop) {
      return !currentColors[prop];
    });
    if (missingProperties.length > 0) {
      console.warn(`Theme colors missing properties: ${missingProperties.join(', ')}, using fallback`);
      return Object.assign({}, Colors, currentColors, {
        primary: currentColors.primary || Colors.primary,
        text: currentColors.text || Colors.text,
        background: currentColors.background || Colors.background,
        surface: currentColors.surface || Colors.surface
      });
    }
    return currentColors;
  }, [currentColors]);
  var getTypography = function getTypography(size) {
    var typographySizes = {
      xs: 12,
      sm: 14,
      base: 16,
      lg: 18,
      xl: 20,
      '2xl': 24,
      '3xl': 30,
      '4xl': 36
    };
    return typographySizes[size] || 16;
  };
  var getAccessibleColor = function getAccessibleColor(color, background) {
    var targetBackground = background || (isDark ? '#121212' : '#FFFFFF');
    return _accessibilityUtils.ColorContrastUtils.enhanceColorContrast(color, targetBackground, _accessibilityUtils.WCAG_STANDARDS.CONTRAST_RATIOS.AA_NORMAL);
  };
  var validateColorContrast = function validateColorContrast(foreground, background) {
    var ratio = _accessibilityUtils.ColorContrastUtils.getContrastRatio(foreground, background);
    return ratio >= _accessibilityUtils.WCAG_STANDARDS.CONTRAST_RATIOS.AA_NORMAL;
  };
  var getWCAGCompliantColors = function getWCAGCompliantColors() {
    var baseColors = isDark ? DarkColors : Colors;
    var backgroundColor = isDark ? '#121212' : '#FFFFFF';
    var compliantColors = Object.assign({}, baseColors, {
      text: Object.assign({}, baseColors.text, {
        primary: getAccessibleColor(baseColors.text.primary, backgroundColor),
        secondary: getAccessibleColor(baseColors.text.secondary, backgroundColor),
        tertiary: getAccessibleColor(baseColors.text.tertiary, backgroundColor)
      }),
      primary: Object.assign({}, baseColors.primary, {
        default: getAccessibleColor(baseColors.primary.default, backgroundColor)
      })
    });
    return compliantColors;
  };
  var contextValue = {
    colors: safeColors,
    isDark: isDark,
    isDarkMode: isDark,
    toggleTheme: toggleTheme,
    setTheme: setTheme,
    getTypography: getTypography,
    getAccessibleColor: getAccessibleColor,
    validateColorContrast: validateColorContrast,
    getWCAGCompliantColors: getWCAGCompliantColors
  };
  return (0, _jsxRuntime.jsx)(ThemeContext.Provider, {
    value: contextValue,
    children: children
  });
};
var useTheme = exports.useTheme = function useTheme() {
  var context = (0, _react.useContext)(ThemeContext);
  var fallbackGetTypography = function fallbackGetTypography(size) {
    var typographySizes = {
      xs: 12,
      sm: 14,
      base: 16,
      lg: 18,
      xl: 20,
      '2xl': 24,
      '3xl': 30,
      '4xl': 36
    };
    return typographySizes[size] || 16;
  };
  if (context === undefined) {
    var _global;
    console.warn('[useTheme] useTheme used outside ThemeProvider, using fallback theme');
    var globalFallback = (_global = global) == null ? void 0 : _global.__VIERLA_FALLBACK_THEME__;
    var fallbackColors = (globalFallback == null ? void 0 : globalFallback.colors) || (0, _globalErrorInterceptor.getFallbackTheme)().colors;
    return {
      colors: fallbackColors,
      isDark: false,
      isDarkMode: false,
      toggleTheme: function toggleTheme() {},
      setTheme: function setTheme() {},
      getTypography: fallbackGetTypography,
      getAccessibleColor: function getAccessibleColor(color, background) {
        return color;
      },
      validateColorContrast: function validateColorContrast() {
        return true;
      },
      getWCAGCompliantColors: function getWCAGCompliantColors() {
        return fallbackColors;
      }
    };
  }
  if (!context.colors || typeof context.colors !== 'object') {
    var _global2;
    console.warn('[useTheme] Theme colors object is invalid, using fallback');
    var _globalFallback = (_global2 = global) == null ? void 0 : _global2.__VIERLA_FALLBACK_THEME__;
    var _fallbackColors = (_globalFallback == null ? void 0 : _globalFallback.colors) || (0, _globalErrorInterceptor.getFallbackTheme)().colors;
    return Object.assign({}, context, {
      colors: _fallbackColors,
      isDarkMode: context.isDark,
      getTypography: context.getTypography || fallbackGetTypography
    });
  }
  if (!context.colors.primary) {
    console.warn('Theme primary colors missing, using fallback');
    return Object.assign({}, context, {
      colors: Object.assign({}, context.colors, {
        primary: Colors.primary
      }),
      getTypography: context.getTypography || fallbackGetTypography
    });
  }
  if (!context.colors.text || typeof context.colors.text !== 'object') {
    console.warn('Theme text colors missing, using fallback');
    return Object.assign({}, context, {
      colors: Object.assign({}, context.colors, {
        text: Colors.text
      }),
      getTypography: context.getTypography || fallbackGetTypography
    });
  }
  if (!context.colors.background || typeof context.colors.background !== 'object') {
    console.warn('Theme background colors missing, using fallback');
    return Object.assign({}, context, {
      colors: Object.assign({}, context.colors, {
        background: Colors.background
      }),
      getTypography: context.getTypography || fallbackGetTypography
    });
  }
  if (!context.getTypography) {
    return Object.assign({}, context, {
      getTypography: fallbackGetTypography
    });
  }
  return context;
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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