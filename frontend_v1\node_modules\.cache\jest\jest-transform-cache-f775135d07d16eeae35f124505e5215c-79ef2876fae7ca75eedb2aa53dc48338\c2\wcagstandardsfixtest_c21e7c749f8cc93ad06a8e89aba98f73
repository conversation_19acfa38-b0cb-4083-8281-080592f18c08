a207d7a01dea65d5c47f86c6bc8dcbd7
var _accessibilityUtils = require("../accessibilityUtils");
describe('WCAG_STANDARDS Fix', function () {
  it('should have correct TOUCH_TARGETS structure', function () {
    expect(_accessibilityUtils.WCAG_STANDARDS.TOUCH_TARGETS).toBeDefined();
    expect(_accessibilityUtils.WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE).toBe(44);
    expect(_accessibilityUtils.WCAG_STANDARDS.TOUCH_TARGETS.RECOMMENDED_SIZE).toBe(48);
    expect(_accessibilityUtils.WCAG_STANDARDS.TOUCH_TARGETS.SPACING).toBe(8);
  });
  it('should have backward compatibility TARGET_SIZE alias', function () {
    expect(_accessibilityUtils.WCAG_STANDARDS.TARGET_SIZE).toBeDefined();
    expect(_accessibilityUtils.WCAG_STANDARDS.TARGET_SIZE.MINIMUM).toBe(44);
  });
  it('should have correct FOCUS_INDICATORS structure', function () {
    expect(_accessibilityUtils.WCAG_STANDARDS.FOCUS_INDICATORS).toBeDefined();
    expect(_accessibilityUtils.WCAG_STANDARDS.FOCUS_INDICATORS.MIN_WIDTH).toBe(2);
    expect(_accessibilityUtils.WCAG_STANDARDS.FOCUS_INDICATORS.RECOMMENDED_WIDTH).toBe(3);
    expect(_accessibilityUtils.WCAG_STANDARDS.FOCUS_INDICATORS.OFFSET).toBe(2);
  });
  it('should have correct CONTRAST_RATIOS structure', function () {
    expect(_accessibilityUtils.WCAG_STANDARDS.CONTRAST_RATIOS).toBeDefined();
    expect(_accessibilityUtils.WCAG_STANDARDS.CONTRAST_RATIOS.AA_NORMAL).toBe(4.5);
    expect(_accessibilityUtils.WCAG_STANDARDS.CONTRAST_RATIOS.AA_LARGE).toBe(3.0);
    expect(_accessibilityUtils.WCAG_STANDARDS.CONTRAST_RATIOS.AAA_NORMAL).toBe(7.0);
    expect(_accessibilityUtils.WCAG_STANDARDS.CONTRAST_RATIOS.AAA_LARGE).toBe(4.5);
  });
  it('should be frozen to prevent modification', function () {
    expect(Object.isFrozen(_accessibilityUtils.WCAG_STANDARDS)).toBe(true);
    expect(Object.isFrozen(_accessibilityUtils.WCAG_STANDARDS.TOUCH_TARGETS)).toBe(true);
    expect(Object.isFrozen(_accessibilityUtils.WCAG_STANDARDS.TARGET_SIZE)).toBe(true);
    expect(Object.isFrozen(_accessibilityUtils.WCAG_STANDARDS.FOCUS_INDICATORS)).toBe(true);
    expect(Object.isFrozen(_accessibilityUtils.WCAG_STANDARDS.CONTRAST_RATIOS)).toBe(true);
  });
  it('should not throw runtime errors when accessing properties', function () {
    expect(function () {
      var minSize = _accessibilityUtils.WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE;
      var aliasSize = _accessibilityUtils.WCAG_STANDARDS.TARGET_SIZE.MINIMUM;
      var focusWidth = _accessibilityUtils.WCAG_STANDARDS.FOCUS_INDICATORS.RECOMMENDED_WIDTH;
      var contrastRatio = _accessibilityUtils.WCAG_STANDARDS.CONTRAST_RATIOS.AA_NORMAL;
      expect(typeof minSize).toBe('number');
      expect(typeof aliasSize).toBe('number');
      expect(typeof focusWidth).toBe('number');
      expect(typeof contrastRatio).toBe('number');
    }).not.toThrow();
  });
  it('should maintain consistency between TOUCH_TARGETS.MINIMUM_SIZE and TARGET_SIZE.MINIMUM', function () {
    expect(_accessibilityUtils.WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE).toBe(_accessibilityUtils.WCAG_STANDARDS.TARGET_SIZE.MINIMUM);
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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