528a8aae6413129e23977a738edcdbfe
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.navigationAnalytics = exports.default = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var NavigationAnalyticsService = function () {
  function NavigationAnalyticsService() {
    (0, _classCallCheck2.default)(this, NavigationAnalyticsService);
    this.screenStartTime = 0;
    this.currentScreen = '';
    this.previousScreen = '';
    this.currentSession = this.generateSessionId();
    this.currentFlow = {
      sessionId: this.currentSession,
      startTime: Date.now(),
      screens: []
    };
  }
  return (0, _createClass2.default)(NavigationAnalyticsService, [{
    key: "generateSessionId",
    value: function generateSessionId() {
      return `nav_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "trackScreenView",
    value: function trackScreenView(screenName, params, userRole) {
      var now = Date.now();
      if (this.currentScreen && this.screenStartTime > 0) {
        var timeSpent = now - this.screenStartTime;
        this.trackScreenMetrics({
          screenName: this.currentScreen,
          loadTime: 0,
          timeSpent: timeSpent,
          exitMethod: 'navigation'
        });
      }
      var event = {
        screenName: screenName,
        previousScreen: this.currentScreen || undefined,
        timestamp: now,
        params: params,
        userRole: userRole,
        sessionId: this.currentSession
      };
      this.currentFlow.screens.push(event);
      if (userRole) {
        this.currentFlow.userRole = userRole;
      }
      this.previousScreen = this.currentScreen;
      this.currentScreen = screenName;
      this.screenStartTime = now;
      if (__DEV__) {
        console.log('📊 Navigation Analytics - Screen View:', {
          screen: screenName,
          previous: this.previousScreen,
          params: params,
          userRole: userRole
        });
      }
      this.sendAnalyticsEvent('screen_view', event);
    }
  }, {
    key: "trackScreenLoadTime",
    value: function trackScreenLoadTime(screenName, loadTime) {
      if (__DEV__) {
        console.log('⏱️ Navigation Analytics - Screen Load Time:', {
          screen: screenName,
          loadTime: `${loadTime}ms`
        });
      }
      this.sendAnalyticsEvent('screen_load_time', {
        screenName: screenName,
        loadTime: loadTime,
        timestamp: Date.now(),
        sessionId: this.currentSession
      });
    }
  }, {
    key: "trackNavigationAction",
    value: function trackNavigationAction(action, fromScreen, toScreen, params) {
      var event = {
        action: action,
        fromScreen: fromScreen,
        toScreen: toScreen,
        params: params,
        timestamp: Date.now(),
        sessionId: this.currentSession
      };
      if (__DEV__) {
        console.log('🎯 Navigation Analytics - Action:', event);
      }
      this.sendAnalyticsEvent('navigation_action', event);
    }
  }, {
    key: "trackScreenMetrics",
    value: function trackScreenMetrics(metrics) {
      if (__DEV__) {
        console.log('📈 Navigation Analytics - Screen Metrics:', {
          screen: metrics.screenName,
          timeSpent: `${metrics.timeSpent}ms`,
          exitMethod: metrics.exitMethod
        });
      }
      this.sendAnalyticsEvent('screen_metrics', Object.assign({}, metrics, {
        timestamp: Date.now(),
        sessionId: this.currentSession
      }));
    }
  }, {
    key: "trackFlowCompletion",
    value: function trackFlowCompletion(flowName, success, metadata) {
      var event = {
        flowName: flowName,
        success: success,
        metadata: metadata,
        duration: Date.now() - this.currentFlow.startTime,
        screenCount: this.currentFlow.screens.length,
        timestamp: Date.now(),
        sessionId: this.currentSession
      };
      if (__DEV__) {
        console.log('🎯 Navigation Analytics - Flow Completion:', event);
      }
      this.sendAnalyticsEvent('flow_completion', event);
    }
  }, {
    key: "trackNavigationError",
    value: function trackNavigationError(error, screenName, params) {
      var event = {
        error: error,
        screenName: screenName,
        params: params,
        timestamp: Date.now(),
        sessionId: this.currentSession
      };
      if (__DEV__) {
        console.error('❌ Navigation Analytics - Error:', event);
      }
      this.sendAnalyticsEvent('navigation_error', event);
    }
  }, {
    key: "getCurrentFlow",
    value: function getCurrentFlow() {
      return Object.assign({}, this.currentFlow, {
        totalDuration: Date.now() - this.currentFlow.startTime
      });
    }
  }, {
    key: "startNewSession",
    value: function startNewSession(userRole) {
      this.currentFlow.totalDuration = Date.now() - this.currentFlow.startTime;
      this.currentSession = this.generateSessionId();
      this.currentFlow = {
        sessionId: this.currentSession,
        startTime: Date.now(),
        screens: [],
        userRole: userRole
      };
      this.currentScreen = '';
      this.previousScreen = '';
      this.screenStartTime = 0;
      if (__DEV__) {
        console.log('🔄 Navigation Analytics - New Session Started:', this.currentSession);
      }
    }
  }, {
    key: "getNavigationStats",
    value: function getNavigationStats() {
      var _Object$entries$sort$;
      var flow = this.getCurrentFlow();
      var screenTimes = {};
      var screenCounts = {};
      flow.screens.forEach(function (screen, index) {
        var screenName = screen.screenName;
        screenCounts[screenName] = (screenCounts[screenName] || 0) + 1;
        if (index < flow.screens.length - 1) {
          var timeSpent = flow.screens[index + 1].timestamp - screen.timestamp;
          if (!screenTimes[screenName]) {
            screenTimes[screenName] = [];
          }
          screenTimes[screenName].push(timeSpent);
        }
      });
      var mostVisitedScreen = ((_Object$entries$sort$ = Object.entries(screenCounts).sort(function (_ref, _ref2) {
        var _ref3 = (0, _slicedToArray2.default)(_ref, 2),
          a = _ref3[1];
        var _ref4 = (0, _slicedToArray2.default)(_ref2, 2),
          b = _ref4[1];
        return b - a;
      })[0]) == null ? void 0 : _Object$entries$sort$[0]) || '';
      var allTimes = Object.values(screenTimes).flat();
      var averageScreenTime = allTimes.length > 0 ? allTimes.reduce(function (sum, time) {
        return sum + time;
      }, 0) / allTimes.length : 0;
      return {
        sessionDuration: flow.totalDuration || 0,
        screenCount: flow.screens.length,
        averageScreenTime: averageScreenTime,
        mostVisitedScreen: mostVisitedScreen,
        navigationPattern: flow.screens.map(function (s) {
          return s.screenName;
        })
      };
    }
  }, {
    key: "sendAnalyticsEvent",
    value: function sendAnalyticsEvent(eventType, data) {
      if (__DEV__) {
        return;
      }
    }
  }]);
}();
var navigationAnalytics = exports.navigationAnalytics = new NavigationAnalyticsService();
var _default = exports.default = navigationAnalytics;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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