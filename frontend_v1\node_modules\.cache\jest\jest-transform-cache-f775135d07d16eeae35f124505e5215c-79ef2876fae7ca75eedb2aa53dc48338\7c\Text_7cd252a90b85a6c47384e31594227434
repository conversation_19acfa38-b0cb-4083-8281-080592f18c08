7a80e41d1e6cf565310d9ca705c6571d
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Text = void 0;
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _react = _interopRequireDefault(require("react"));
var _reactNative = require("react-native");
var _ThemeContext = require("../../contexts/ThemeContext");
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["children", "variant", "size", "color", "weight", "align", "italic", "underline", "strikethrough", "style"];
var Text = exports.Text = function Text(_ref) {
  var children = _ref.children,
    _ref$variant = _ref.variant,
    variant = _ref$variant === void 0 ? 'body' : _ref$variant,
    _ref$size = _ref.size,
    size = _ref$size === void 0 ? 'base' : _ref$size,
    _ref$color = _ref.color,
    color = _ref$color === void 0 ? 'primary' : _ref$color,
    _ref$weight = _ref.weight,
    weight = _ref$weight === void 0 ? 'normal' : _ref$weight,
    _ref$align = _ref.align,
    align = _ref$align === void 0 ? 'left' : _ref$align,
    _ref$italic = _ref.italic,
    italic = _ref$italic === void 0 ? false : _ref$italic,
    _ref$underline = _ref.underline,
    underline = _ref$underline === void 0 ? false : _ref$underline,
    _ref$strikethrough = _ref.strikethrough,
    strikethrough = _ref$strikethrough === void 0 ? false : _ref$strikethrough,
    style = _ref.style,
    props = (0, _objectWithoutProperties2.default)(_ref, _excluded);
  var _useTheme = (0, _ThemeContext.useTheme)(),
    colors = _useTheme.colors;
  var styles = createStyles(colors);
  var textStyleArray = [styles.base, styles[variant], styles[`size_${size}`], styles[`color_${color}`], styles[`weight_${weight}`], styles[`align_${align}`], italic && styles.italic, underline && styles.underline, strikethrough && styles.strikethrough, style].filter(Boolean);
  return (0, _jsxRuntime.jsx)(_reactNative.Text, Object.assign({
    style: textStyleArray
  }, props, {
    children: children
  }));
};
var createStyles = function createStyles(colors) {
  var _colors$text, _colors$text2, _colors$text3, _colors$text4, _colors$primary, _colors$text5;
  return _reactNative.StyleSheet.create({
    base: {
      fontFamily: 'System',
      fontSize: 16,
      lineHeight: 24,
      color: ((_colors$text = colors.text) == null ? void 0 : _colors$text.primary) || '#1A1A1A'
    },
    body: {
      fontSize: 16,
      fontWeight: '400',
      lineHeight: 24
    },
    heading: {
      fontSize: 24,
      fontWeight: '600',
      lineHeight: 32
    },
    caption: {
      fontSize: 14,
      fontWeight: '400',
      lineHeight: 20
    },
    label: {
      fontSize: 14,
      fontWeight: '500',
      lineHeight: 20
    },
    display: {
      fontSize: 36,
      fontWeight: '700',
      lineHeight: 44
    },
    size_xs: {
      fontSize: 12
    },
    size_sm: {
      fontSize: 14
    },
    size_base: {
      fontSize: 16
    },
    size_lg: {
      fontSize: 18
    },
    size_xl: {
      fontSize: 20
    },
    size_2xl: {
      fontSize: 24
    },
    size_3xl: {
      fontSize: 30
    },
    size_4xl: {
      fontSize: 36
    },
    size_5xl: {
      fontSize: 48
    },
    color_primary: {
      color: ((_colors$text2 = colors.text) == null ? void 0 : _colors$text2.primary) || '#1A1A1A'
    },
    color_secondary: {
      color: ((_colors$text3 = colors.text) == null ? void 0 : _colors$text3.secondary) || '#6B7280'
    },
    color_tertiary: {
      color: ((_colors$text4 = colors.text) == null ? void 0 : _colors$text4.tertiary) || '#9CA3AF'
    },
    color_inverse: {
      color: ((_colors$primary = colors.primary) == null ? void 0 : _colors$primary.contrast) || '#FFFFFF'
    },
    color_disabled: {
      color: ((_colors$text5 = colors.text) == null ? void 0 : _colors$text5.tertiary) || '#9CA3AF'
    },
    color_success: {
      color: '#10B981'
    },
    color_warning: {
      color: '#F59E0B'
    },
    color_error: {
      color: '#EF4444'
    },
    color_info: {
      color: '#3B82F6'
    },
    weight_light: {
      fontWeight: '300'
    },
    weight_normal: {
      fontWeight: '400'
    },
    weight_medium: {
      fontWeight: '500'
    },
    weight_semibold: {
      fontWeight: '600'
    },
    weight_bold: {
      fontWeight: '700'
    },
    align_left: {
      textAlign: 'left'
    },
    align_center: {
      textAlign: 'center'
    },
    align_right: {
      textAlign: 'right'
    },
    align_justify: {
      textAlign: 'justify'
    },
    italic: {
      fontStyle: 'italic'
    },
    underline: {
      textDecorationLine: 'underline'
    },
    strikethrough: {
      textDecorationLine: 'line-through'
    }
  });
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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