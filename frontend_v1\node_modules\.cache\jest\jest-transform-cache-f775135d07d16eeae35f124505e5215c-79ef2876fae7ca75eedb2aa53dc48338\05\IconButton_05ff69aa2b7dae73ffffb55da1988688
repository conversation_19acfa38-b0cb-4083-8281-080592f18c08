a4a2c531d2bbb53a2390e444e4d22f05
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.IconButton = void 0;
var _vectorIcons = require("@expo/vector-icons");
var _react = _interopRequireDefault(require("react"));
var _reactNative = require("react-native");
var _Colors = require("../../constants/Colors");
var _jsxRuntime = require("react/jsx-runtime");
var IconButton = exports.IconButton = function IconButton(_ref) {
  var name = _ref.name,
    _ref$size = _ref.size,
    size = _ref$size === void 0 ? 'medium' : _ref$size,
    _ref$variant = _ref.variant,
    variant = _ref$variant === void 0 ? 'ghost' : _ref$variant,
    onPress = _ref.onPress,
    _ref$disabled = _ref.disabled,
    disabled = _ref$disabled === void 0 ? false : _ref$disabled,
    style = _ref.style,
    color = _ref.color,
    testID = _ref.testID,
    accessibilityLabel = _ref.accessibilityLabel,
    accessibilityHint = _ref.accessibilityHint;
  var sizeConfig = {
    small: {
      iconSize: 16,
      buttonSize: 44,
      padding: 14
    },
    medium: {
      iconSize: 20,
      buttonSize: 48,
      padding: 14
    },
    large: {
      iconSize: 24,
      buttonSize: 56,
      padding: 16
    }
  };
  var variantConfig = {
    primary: {
      backgroundColor: _Colors.Colors.interactive.primary.default,
      iconColor: _Colors.Colors.text.onPrimary,
      pressedBackgroundColor: _Colors.Colors.interactive.primary.pressed
    },
    secondary: {
      backgroundColor: _Colors.Colors.interactive.secondary.default,
      iconColor: _Colors.Colors.text.onSage,
      pressedBackgroundColor: _Colors.Colors.interactive.secondary.pressed
    },
    ghost: {
      backgroundColor: 'transparent',
      iconColor: _Colors.Colors.text.secondary,
      pressedBackgroundColor: _Colors.Colors.interactive.ghost.hover
    },
    danger: {
      backgroundColor: _Colors.Colors.errorLight,
      iconColor: _Colors.Colors.error,
      pressedBackgroundColor: _Colors.Colors.interactive.destructive.hover
    }
  };
  var currentSize = sizeConfig[size];
  var currentVariant = variantConfig[variant];
  var buttonStyle = [styles.button, {
    width: currentSize.buttonSize,
    height: currentSize.buttonSize,
    padding: currentSize.padding,
    backgroundColor: disabled ? _Colors.Colors.surface.disabled : currentVariant.backgroundColor
  }, style];
  var iconColor = disabled ? _Colors.Colors.text.disabled : color || currentVariant.iconColor;
  return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
    style: buttonStyle,
    onPress: onPress,
    disabled: disabled,
    testID: testID,
    accessibilityRole: "button",
    accessibilityLabel: accessibilityLabel,
    accessibilityHint: accessibilityHint,
    accessibilityState: {
      disabled: disabled
    },
    activeOpacity: 0.7,
    children: (0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
      name: name,
      size: currentSize.iconSize,
      color: iconColor
    })
  });
};
var styles = _reactNative.StyleSheet.create({
  button: {
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center'
  }
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfdmVjdG9ySWNvbnMiLCJyZXF1aXJlIiwiX3JlYWN0IiwiX2ludGVyb3BSZXF1aXJlRGVmYXVsdCIsIl9yZWFjdE5hdGl2ZSIsIl9Db2xvcnMiLCJfanN4UnVudGltZSIsIkljb25CdXR0b24iLCJleHBvcnRzIiwiX3JlZiIsIm5hbWUiLCJfcmVmJHNpemUiLCJzaXplIiwiX3JlZiR2YXJpYW50IiwidmFyaWFudCIsIm9uUHJlc3MiLCJfcmVmJGRpc2FibGVkIiwiZGlzYWJsZWQiLCJzdHlsZSIsImNvbG9yIiwidGVzdElEIiwiYWNjZXNzaWJpbGl0eUxhYmVsIiwiYWNjZXNzaWJpbGl0eUhpbnQiLCJzaXplQ29uZmlnIiwic21hbGwiLCJpY29uU2l6ZSIsImJ1dHRvblNpemUiLCJwYWRkaW5nIiwibWVkaXVtIiwibGFyZ2UiLCJ2YXJpYW50Q29uZmlnIiwicHJpbWFyeSIsImJhY2tncm91bmRDb2xvciIsIkNvbG9ycyIsImludGVyYWN0aXZlIiwiZGVmYXVsdCIsImljb25Db2xvciIsInRleHQiLCJvblByaW1hcnkiLCJwcmVzc2VkQmFja2dyb3VuZENvbG9yIiwicHJlc3NlZCIsInNlY29uZGFyeSIsIm9uU2FnZSIsImdob3N0IiwiaG92ZXIiLCJkYW5nZXIiLCJlcnJvckxpZ2h0IiwiZXJyb3IiLCJkZXN0cnVjdGl2ZSIsImN1cnJlbnRTaXplIiwiY3VycmVudFZhcmlhbnQiLCJidXR0b25TdHlsZSIsInN0eWxlcyIsImJ1dHRvbiIsIndpZHRoIiwiaGVpZ2h0Iiwic3VyZmFjZSIsImpzeCIsIlRvdWNoYWJsZU9wYWNpdHkiLCJhY2Nlc3NpYmlsaXR5Um9sZSIsImFjY2Vzc2liaWxpdHlTdGF0ZSIsImFjdGl2ZU9wYWNpdHkiLCJjaGlsZHJlbiIsIklvbmljb25zIiwiU3R5bGVTaGVldCIsImNyZWF0ZSIsImJvcmRlclJhZGl1cyIsImFsaWduSXRlbXMiLCJqdXN0aWZ5Q29udGVudCJdLCJzb3VyY2VzIjpbIkljb25CdXR0b24udHN4Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogSWNvbkJ1dHRvbiBDb21wb25lbnQgLSBDb25zaXN0ZW50IEljb24gQnV0dG9uIEltcGxlbWVudGF0aW9uXG4gKlxuICogQ29tcG9uZW50IENvbnRyYWN0OlxuICogLSBQcm92aWRlcyBjb25zaXN0ZW50IGljb24gYnV0dG9uIHN0eWxpbmcgYWNyb3NzIHRoZSBhcHBcbiAqIC0gU3VwcG9ydHMgZGlmZmVyZW50IHNpemVzLCB2YXJpYW50cywgYW5kIHN0YXRlc1xuICogLSBVc2VzIElvbmljb25zIGZvciBjb25zaXN0ZW5jeSB3aXRoIGN1cnJlbnQgaW1wbGVtZW50YXRpb25cbiAqIC0gRm9sbG93cyBkZXNpZ24gc3lzdGVtIGd1aWRlbGluZXNcbiAqXG4gKiBAdmVyc2lvbiAxLjAuMFxuICogQGF1dGhvciBWaWVybGEgRGV2ZWxvcG1lbnQgVGVhbVxuICovXG5cbmltcG9ydCB7IElvbmljb25zIH0gZnJvbSAnQGV4cG8vdmVjdG9yLWljb25zJztcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBUb3VjaGFibGVPcGFjaXR5LCBTdHlsZVNoZWV0LCBWaWV3U3R5bGUgfSBmcm9tICdyZWFjdC1uYXRpdmUnO1xuXG5pbXBvcnQgeyBDb2xvcnMgfSBmcm9tICcuLi8uLi9jb25zdGFudHMvQ29sb3JzJztcblxuaW50ZXJmYWNlIEljb25CdXR0b25Qcm9wcyB7XG4gIG5hbWU6IGtleW9mIHR5cGVvZiBJb25pY29ucy5nbHlwaE1hcDtcbiAgc2l6ZT86ICdzbWFsbCcgfCAnbWVkaXVtJyB8ICdsYXJnZSc7XG4gIHZhcmlhbnQ/OiAncHJpbWFyeScgfCAnc2Vjb25kYXJ5JyB8ICdnaG9zdCcgfCAnZGFuZ2VyJztcbiAgb25QcmVzczogKCkgPT4gdm9pZDtcbiAgZGlzYWJsZWQ/OiBib29sZWFuO1xuICBzdHlsZT86IFZpZXdTdHlsZTtcbiAgY29sb3I/OiBzdHJpbmc7IC8vIEN1c3RvbSBjb2xvciBvdmVycmlkZVxuICB0ZXN0SUQ/OiBzdHJpbmc7XG4gIGFjY2Vzc2liaWxpdHlMYWJlbD86IHN0cmluZztcbiAgYWNjZXNzaWJpbGl0eUhpbnQ/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBjb25zdCBJY29uQnV0dG9uOiBSZWFjdC5GQzxJY29uQnV0dG9uUHJvcHM+ID0gKHtcbiAgbmFtZSxcbiAgc2l6ZSA9ICdtZWRpdW0nLFxuICB2YXJpYW50ID0gJ2dob3N0JyxcbiAgb25QcmVzcyxcbiAgZGlzYWJsZWQgPSBmYWxzZSxcbiAgc3R5bGUsXG4gIGNvbG9yLFxuICB0ZXN0SUQsXG4gIGFjY2Vzc2liaWxpdHlMYWJlbCxcbiAgYWNjZXNzaWJpbGl0eUhpbnQsXG59KSA9PiB7XG4gIC8vIFdDQUcgMi4xIEFBIGNvbXBsaWFudCB0b3VjaCB0YXJnZXQgc2l6ZXMgKG1pbmltdW0gNDR4NDRweClcbiAgY29uc3Qgc2l6ZUNvbmZpZyA9IHtcbiAgICBzbWFsbDogeyBpY29uU2l6ZTogMTYsIGJ1dHRvblNpemU6IDQ0LCBwYWRkaW5nOiAxNCB9LCAvLyBJbmNyZWFzZWQgZnJvbSAzMnB4IHRvIDQ0cHhcbiAgICBtZWRpdW06IHsgaWNvblNpemU6IDIwLCBidXR0b25TaXplOiA0OCwgcGFkZGluZzogMTQgfSwgLy8gSW5jcmVhc2VkIGZyb20gNDBweCB0byA0OHB4XG4gICAgbGFyZ2U6IHsgaWNvblNpemU6IDI0LCBidXR0b25TaXplOiA1NiwgcGFkZGluZzogMTYgfSwgLy8gSW5jcmVhc2VkIGZyb20gNDhweCB0byA1NnB4IGZvciBiZXR0ZXIgdXNhYmlsaXR5XG4gIH07XG5cbiAgY29uc3QgdmFyaWFudENvbmZpZyA9IHtcbiAgICBwcmltYXJ5OiB7XG4gICAgICBiYWNrZ3JvdW5kQ29sb3I6IENvbG9ycy5pbnRlcmFjdGl2ZS5wcmltYXJ5LmRlZmF1bHQsXG4gICAgICBpY29uQ29sb3I6IENvbG9ycy50ZXh0Lm9uUHJpbWFyeSxcbiAgICAgIHByZXNzZWRCYWNrZ3JvdW5kQ29sb3I6IENvbG9ycy5pbnRlcmFjdGl2ZS5wcmltYXJ5LnByZXNzZWQsXG4gICAgfSxcbiAgICBzZWNvbmRhcnk6IHtcbiAgICAgIGJhY2tncm91bmRDb2xvcjogQ29sb3JzLmludGVyYWN0aXZlLnNlY29uZGFyeS5kZWZhdWx0LFxuICAgICAgaWNvbkNvbG9yOiBDb2xvcnMudGV4dC5vblNhZ2UsXG4gICAgICBwcmVzc2VkQmFja2dyb3VuZENvbG9yOiBDb2xvcnMuaW50ZXJhY3RpdmUuc2Vjb25kYXJ5LnByZXNzZWQsXG4gICAgfSxcbiAgICBnaG9zdDoge1xuICAgICAgYmFja2dyb3VuZENvbG9yOiAndHJhbnNwYXJlbnQnLFxuICAgICAgaWNvbkNvbG9yOiBDb2xvcnMudGV4dC5zZWNvbmRhcnksXG4gICAgICBwcmVzc2VkQmFja2dyb3VuZENvbG9yOiBDb2xvcnMuaW50ZXJhY3RpdmUuZ2hvc3QuaG92ZXIsXG4gICAgfSxcbiAgICBkYW5nZXI6IHtcbiAgICAgIGJhY2tncm91bmRDb2xvcjogQ29sb3JzLmVycm9yTGlnaHQsXG4gICAgICBpY29uQ29sb3I6IENvbG9ycy5lcnJvcixcbiAgICAgIHByZXNzZWRCYWNrZ3JvdW5kQ29sb3I6IENvbG9ycy5pbnRlcmFjdGl2ZS5kZXN0cnVjdGl2ZS5ob3ZlcixcbiAgICB9LFxuICB9O1xuXG4gIGNvbnN0IGN1cnJlbnRTaXplID0gc2l6ZUNvbmZpZ1tzaXplXTtcbiAgY29uc3QgY3VycmVudFZhcmlhbnQgPSB2YXJpYW50Q29uZmlnW3ZhcmlhbnRdO1xuXG4gIGNvbnN0IGJ1dHRvblN0eWxlID0gW1xuICAgIHN0eWxlcy5idXR0b24sXG4gICAge1xuICAgICAgd2lkdGg6IGN1cnJlbnRTaXplLmJ1dHRvblNpemUsXG4gICAgICBoZWlnaHQ6IGN1cnJlbnRTaXplLmJ1dHRvblNpemUsXG4gICAgICBwYWRkaW5nOiBjdXJyZW50U2l6ZS5wYWRkaW5nLFxuICAgICAgYmFja2dyb3VuZENvbG9yOiBkaXNhYmxlZFxuICAgICAgICA/IENvbG9ycy5zdXJmYWNlLmRpc2FibGVkXG4gICAgICAgIDogY3VycmVudFZhcmlhbnQuYmFja2dyb3VuZENvbG9yLFxuICAgIH0sXG4gICAgc3R5bGUsXG4gIF07XG5cbiAgY29uc3QgaWNvbkNvbG9yID0gZGlzYWJsZWRcbiAgICA/IENvbG9ycy50ZXh0LmRpc2FibGVkXG4gICAgOiBjb2xvciB8fCBjdXJyZW50VmFyaWFudC5pY29uQ29sb3I7XG5cbiAgcmV0dXJuIChcbiAgICA8VG91Y2hhYmxlT3BhY2l0eVxuICAgICAgc3R5bGU9e2J1dHRvblN0eWxlfVxuICAgICAgb25QcmVzcz17b25QcmVzc31cbiAgICAgIGRpc2FibGVkPXtkaXNhYmxlZH1cbiAgICAgIHRlc3RJRD17dGVzdElEfVxuICAgICAgYWNjZXNzaWJpbGl0eVJvbGU9XCJidXR0b25cIlxuICAgICAgYWNjZXNzaWJpbGl0eUxhYmVsPXthY2Nlc3NpYmlsaXR5TGFiZWx9XG4gICAgICBhY2Nlc3NpYmlsaXR5SGludD17YWNjZXNzaWJpbGl0eUhpbnR9XG4gICAgICBhY2Nlc3NpYmlsaXR5U3RhdGU9e3sgZGlzYWJsZWQgfX1cbiAgICAgIGFjdGl2ZU9wYWNpdHk9ezAuN30+XG4gICAgICA8SW9uaWNvbnMgbmFtZT17bmFtZX0gc2l6ZT17Y3VycmVudFNpemUuaWNvblNpemV9IGNvbG9yPXtpY29uQ29sb3J9IC8+XG4gICAgPC9Ub3VjaGFibGVPcGFjaXR5PlxuICApO1xufTtcblxuY29uc3Qgc3R5bGVzID0gU3R5bGVTaGVldC5jcmVhdGUoe1xuICBidXR0b246IHtcbiAgICBib3JkZXJSYWRpdXM6IDgsXG4gICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICB9LFxufSk7XG4iXSwibWFwcGluZ3MiOiI7Ozs7O0FBYUEsSUFBQUEsWUFBQSxHQUFBQyxPQUFBO0FBQ0EsSUFBQUMsTUFBQSxHQUFBQyxzQkFBQSxDQUFBRixPQUFBO0FBQ0EsSUFBQUcsWUFBQSxHQUFBSCxPQUFBO0FBRUEsSUFBQUksT0FBQSxHQUFBSixPQUFBO0FBQWdELElBQUFLLFdBQUEsR0FBQUwsT0FBQTtBQWV6QyxJQUFNTSxVQUFxQyxHQUFBQyxPQUFBLENBQUFELFVBQUEsR0FBRyxTQUF4Q0EsVUFBcUNBLENBQUFFLElBQUEsRUFXNUM7RUFBQSxJQVZKQyxJQUFJLEdBQUFELElBQUEsQ0FBSkMsSUFBSTtJQUFBQyxTQUFBLEdBQUFGLElBQUEsQ0FDSkcsSUFBSTtJQUFKQSxJQUFJLEdBQUFELFNBQUEsY0FBRyxRQUFRLEdBQUFBLFNBQUE7SUFBQUUsWUFBQSxHQUFBSixJQUFBLENBQ2ZLLE9BQU87SUFBUEEsT0FBTyxHQUFBRCxZQUFBLGNBQUcsT0FBTyxHQUFBQSxZQUFBO0lBQ2pCRSxPQUFPLEdBQUFOLElBQUEsQ0FBUE0sT0FBTztJQUFBQyxhQUFBLEdBQUFQLElBQUEsQ0FDUFEsUUFBUTtJQUFSQSxRQUFRLEdBQUFELGFBQUEsY0FBRyxLQUFLLEdBQUFBLGFBQUE7SUFDaEJFLEtBQUssR0FBQVQsSUFBQSxDQUFMUyxLQUFLO0lBQ0xDLEtBQUssR0FBQVYsSUFBQSxDQUFMVSxLQUFLO0lBQ0xDLE1BQU0sR0FBQVgsSUFBQSxDQUFOVyxNQUFNO0lBQ05DLGtCQUFrQixHQUFBWixJQUFBLENBQWxCWSxrQkFBa0I7SUFDbEJDLGlCQUFpQixHQUFBYixJQUFBLENBQWpCYSxpQkFBaUI7RUFHakIsSUFBTUMsVUFBVSxHQUFHO0lBQ2pCQyxLQUFLLEVBQUU7TUFBRUMsUUFBUSxFQUFFLEVBQUU7TUFBRUMsVUFBVSxFQUFFLEVBQUU7TUFBRUMsT0FBTyxFQUFFO0lBQUcsQ0FBQztJQUNwREMsTUFBTSxFQUFFO01BQUVILFFBQVEsRUFBRSxFQUFFO01BQUVDLFVBQVUsRUFBRSxFQUFFO01BQUVDLE9BQU8sRUFBRTtJQUFHLENBQUM7SUFDckRFLEtBQUssRUFBRTtNQUFFSixRQUFRLEVBQUUsRUFBRTtNQUFFQyxVQUFVLEVBQUUsRUFBRTtNQUFFQyxPQUFPLEVBQUU7SUFBRztFQUNyRCxDQUFDO0VBRUQsSUFBTUcsYUFBYSxHQUFHO0lBQ3BCQyxPQUFPLEVBQUU7TUFDUEMsZUFBZSxFQUFFQyxjQUFNLENBQUNDLFdBQVcsQ0FBQ0gsT0FBTyxDQUFDSSxPQUFPO01BQ25EQyxTQUFTLEVBQUVILGNBQU0sQ0FBQ0ksSUFBSSxDQUFDQyxTQUFTO01BQ2hDQyxzQkFBc0IsRUFBRU4sY0FBTSxDQUFDQyxXQUFXLENBQUNILE9BQU8sQ0FBQ1M7SUFDckQsQ0FBQztJQUNEQyxTQUFTLEVBQUU7TUFDVFQsZUFBZSxFQUFFQyxjQUFNLENBQUNDLFdBQVcsQ0FBQ08sU0FBUyxDQUFDTixPQUFPO01BQ3JEQyxTQUFTLEVBQUVILGNBQU0sQ0FBQ0ksSUFBSSxDQUFDSyxNQUFNO01BQzdCSCxzQkFBc0IsRUFBRU4sY0FBTSxDQUFDQyxXQUFXLENBQUNPLFNBQVMsQ0FBQ0Q7SUFDdkQsQ0FBQztJQUNERyxLQUFLLEVBQUU7TUFDTFgsZUFBZSxFQUFFLGFBQWE7TUFDOUJJLFNBQVMsRUFBRUgsY0FBTSxDQUFDSSxJQUFJLENBQUNJLFNBQVM7TUFDaENGLHNCQUFzQixFQUFFTixjQUFNLENBQUNDLFdBQVcsQ0FBQ1MsS0FBSyxDQUFDQztJQUNuRCxDQUFDO0lBQ0RDLE1BQU0sRUFBRTtNQUNOYixlQUFlLEVBQUVDLGNBQU0sQ0FBQ2EsVUFBVTtNQUNsQ1YsU0FBUyxFQUFFSCxjQUFNLENBQUNjLEtBQUs7TUFDdkJSLHNCQUFzQixFQUFFTixjQUFNLENBQUNDLFdBQVcsQ0FBQ2MsV0FBVyxDQUFDSjtJQUN6RDtFQUNGLENBQUM7RUFFRCxJQUFNSyxXQUFXLEdBQUcxQixVQUFVLENBQUNYLElBQUksQ0FBQztFQUNwQyxJQUFNc0MsY0FBYyxHQUFHcEIsYUFBYSxDQUFDaEIsT0FBTyxDQUFDO0VBRTdDLElBQU1xQyxXQUFXLEdBQUcsQ0FDbEJDLE1BQU0sQ0FBQ0MsTUFBTSxFQUNiO0lBQ0VDLEtBQUssRUFBRUwsV0FBVyxDQUFDdkIsVUFBVTtJQUM3QjZCLE1BQU0sRUFBRU4sV0FBVyxDQUFDdkIsVUFBVTtJQUM5QkMsT0FBTyxFQUFFc0IsV0FBVyxDQUFDdEIsT0FBTztJQUM1QkssZUFBZSxFQUFFZixRQUFRLEdBQ3JCZ0IsY0FBTSxDQUFDdUIsT0FBTyxDQUFDdkMsUUFBUSxHQUN2QmlDLGNBQWMsQ0FBQ2xCO0VBQ3JCLENBQUMsRUFDRGQsS0FBSyxDQUNOO0VBRUQsSUFBTWtCLFNBQVMsR0FBR25CLFFBQVEsR0FDdEJnQixjQUFNLENBQUNJLElBQUksQ0FBQ3BCLFFBQVEsR0FDcEJFLEtBQUssSUFBSStCLGNBQWMsQ0FBQ2QsU0FBUztFQUVyQyxPQUNFLElBQUE5QixXQUFBLENBQUFtRCxHQUFBLEVBQUNyRCxZQUFBLENBQUFzRCxnQkFBZ0I7SUFDZnhDLEtBQUssRUFBRWlDLFdBQVk7SUFDbkJwQyxPQUFPLEVBQUVBLE9BQVE7SUFDakJFLFFBQVEsRUFBRUEsUUFBUztJQUNuQkcsTUFBTSxFQUFFQSxNQUFPO0lBQ2Z1QyxpQkFBaUIsRUFBQyxRQUFRO0lBQzFCdEMsa0JBQWtCLEVBQUVBLGtCQUFtQjtJQUN2Q0MsaUJBQWlCLEVBQUVBLGlCQUFrQjtJQUNyQ3NDLGtCQUFrQixFQUFFO01BQUUzQyxRQUFRLEVBQVJBO0lBQVMsQ0FBRTtJQUNqQzRDLGFBQWEsRUFBRSxHQUFJO0lBQUFDLFFBQUEsRUFDbkIsSUFBQXhELFdBQUEsQ0FBQW1ELEdBQUEsRUFBQ3pELFlBQUEsQ0FBQStELFFBQVE7TUFBQ3JELElBQUksRUFBRUEsSUFBSztNQUFDRSxJQUFJLEVBQUVxQyxXQUFXLENBQUN4QixRQUFTO01BQUNOLEtBQUssRUFBRWlCO0lBQVUsQ0FBRTtFQUFDLENBQ3RELENBQUM7QUFFdkIsQ0FBQztBQUVELElBQU1nQixNQUFNLEdBQUdZLHVCQUFVLENBQUNDLE1BQU0sQ0FBQztFQUMvQlosTUFBTSxFQUFFO0lBQ05hLFlBQVksRUFBRSxDQUFDO0lBQ2ZDLFVBQVUsRUFBRSxRQUFRO0lBQ3BCQyxjQUFjLEVBQUU7RUFDbEI7QUFDRixDQUFDLENBQUMiLCJpZ25vcmVMaXN0IjpbXX0=