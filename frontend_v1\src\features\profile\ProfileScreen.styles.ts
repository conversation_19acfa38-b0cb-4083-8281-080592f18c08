/**
 * Profile Screen Styles - Responsive Design System
 *
 * Style Contract:
 * - Implements responsive design principles
 * - Follows Vierla design system
 * - Supports theme-based styling
 * - Ensures accessibility compliance
 * - Optimized for both iOS and Android
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { StyleSheet } from 'react-native';

import { Colors } from '../../constants/Colors';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../../utils/responsiveUtils';

export const createStyles = (colors: typeof Colors.light) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },

    scrollView: {
      flex: 1,
    },

    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: getResponsiveSpacing(20),
    },

    loadingText: {
      fontSize: getResponsiveFontSize(16),
      color: colors.textSecondary,
      marginTop: getResponsiveSpacing(12),
      textAlign: 'center',
    },

    header: {
      alignItems: 'center',
      paddingVertical: getResponsiveSpacing(32),
      paddingHorizontal: getResponsiveSpacing(20),
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },

    avatarContainer: {
      marginBottom: getResponsiveSpacing(16),
    },

    avatar: {
      width: getResponsiveSpacing(100),
      height: getResponsiveSpacing(100),
      borderRadius: getResponsiveSpacing(50),
      backgroundColor: colors.surfaceVariant,
    },

    avatarPlaceholder: {
      width: getResponsiveSpacing(100),
      height: getResponsiveSpacing(100),
      borderRadius: getResponsiveSpacing(50),
      backgroundColor: colors.primary?.default || '#5A7A63',
      justifyContent: 'center',
      alignItems: 'center',
    },

    avatarText: {
      fontSize: getResponsiveFontSize(32),
      fontWeight: '600',
      color: colors.white,
    },

    userName: {
      fontSize: getResponsiveFontSize(24),
      fontWeight: '600',
      color: colors.text,
      marginBottom: getResponsiveSpacing(4),
      textAlign: 'center',
    },

    userRole: {
      fontSize: getResponsiveFontSize(16),
      color: colors.textSecondary,
      textAlign: 'center',
    },

    formContainer: {
      paddingHorizontal: getResponsiveSpacing(20),
      paddingVertical: getResponsiveSpacing(24),
    },

    sectionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: getResponsiveSpacing(24),
    },

    sectionTitle: {
      fontSize: getResponsiveFontSize(20),
      fontWeight: '600',
      color: colors.text,
    },

    editButton: {
      paddingHorizontal: getResponsiveSpacing(16),
      paddingVertical: getResponsiveSpacing(8),
      backgroundColor: colors.primary?.default || '#5A7A63',
      borderRadius: getResponsiveSpacing(8),
    },

    editButtonText: {
      fontSize: getResponsiveFontSize(14),
      fontWeight: '600',
      color: colors.white,
    },

    actionButtons: {
      flexDirection: 'row',
      gap: getResponsiveSpacing(12),
    },

    cancelButton: {
      paddingHorizontal: getResponsiveSpacing(16),
      paddingVertical: getResponsiveSpacing(8),
      backgroundColor: colors.surfaceVariant,
      borderRadius: getResponsiveSpacing(8),
    },

    cancelButtonText: {
      fontSize: getResponsiveFontSize(14),
      fontWeight: '600',
      color: colors.text,
    },

    saveButton: {
      paddingHorizontal: getResponsiveSpacing(16),
      paddingVertical: getResponsiveSpacing(8),
      backgroundColor: colors.primary?.default || '#5A7A63',
      borderRadius: getResponsiveSpacing(8),
      minWidth: getResponsiveSpacing(60),
      alignItems: 'center',
    },

    saveButtonDisabled: {
      opacity: 0.6,
    },

    saveButtonText: {
      fontSize: getResponsiveFontSize(14),
      fontWeight: '600',
      color: colors.white,
    },

    fieldContainer: {
      marginBottom: getResponsiveSpacing(20),
    },

    fieldLabel: {
      fontSize: getResponsiveFontSize(16),
      fontWeight: '500',
      color: colors.text,
      marginBottom: getResponsiveSpacing(8),
    },

    fieldInput: {
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: getResponsiveSpacing(8),
      paddingHorizontal: getResponsiveSpacing(16),
      paddingVertical: getResponsiveSpacing(12),
      fontSize: getResponsiveFontSize(16),
      color: colors.text,
      backgroundColor: colors.surface,
    },

    fieldInputError: {
      borderColor: colors.error,
    },

    fieldInputDisabled: {
      backgroundColor: colors.surfaceVariant,
      color: colors.textSecondary,
    },

    errorText: {
      fontSize: getResponsiveFontSize(14),
      color: colors.error,
      marginTop: getResponsiveSpacing(4),
    },

    actionsContainer: {
      paddingHorizontal: getResponsiveSpacing(20),
      paddingVertical: getResponsiveSpacing(24),
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },

    actionButton: {
      paddingVertical: getResponsiveSpacing(16),
      paddingHorizontal: getResponsiveSpacing(20),
      backgroundColor: colors.surface,
      borderRadius: getResponsiveSpacing(8),
      borderWidth: 1,
      borderColor: colors.border,
      marginBottom: getResponsiveSpacing(12),
    },

    actionButtonText: {
      fontSize: getResponsiveFontSize(16),
      fontWeight: '500',
      color: colors.text,
      textAlign: 'center',
    },

    dangerButton: {
      borderColor: colors.error,
    },

    dangerButtonText: {
      color: colors.error,
    },
  });
