292183ea4a1ed0126dd553744c47ef4e
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var TurboModuleRegistry = _interopRequireWildcard(require("../../../../Libraries/TurboModule/TurboModuleRegistry"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var NativeModule = TurboModuleRegistry.getEnforcing('StatusBarManager');
var constants = null;
var NativeStatusBarManager = {
  getConstants: function getConstants() {
    if (constants == null) {
      constants = NativeModule.getConstants();
    }
    return constants;
  },
  setColor: function setColor(color, animated) {
    NativeModule.setColor(color, animated);
  },
  setTranslucent: function setTranslucent(translucent) {
    NativeModule.setTranslucent(translucent);
  },
  setStyle: function setStyle(statusBarStyle) {
    NativeModule.setStyle(statusBarStyle);
  },
  setHidden: function setHidden(hidden) {
    NativeModule.setHidden(hidden);
  }
};
var _default = exports.default = NativeStatusBarManager;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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