{"version": 3, "names": ["_native", "require", "_react", "_navigationAnalytics", "_interopRequireDefault", "_navigationGuards", "_authSlice", "useNavigationGuard", "exports", "options", "arguments", "length", "undefined", "_options$trackAnalyti", "trackAnalytics", "_options$enforceGuard", "enforceGuards", "_options$logNavigatio", "logNavigation", "__DEV__", "navigation", "useNavigation", "route", "useRoute", "_useAuthStore", "useAuthStore", "userRole", "isNavigatingRef", "useRef", "screenStartTimeRef", "currentScreenRef", "name", "useFocusEffect", "useCallback", "screenName", "startTime", "Date", "now", "current", "navigationAnalytics", "trackScreenView", "params", "loadTime", "trackScreenLoadTime", "console", "log", "timeSpent", "navigate", "_ref", "_asyncToGenerator2", "default", "routeName", "warn", "guard<PERSON><PERSON><PERSON>", "navigationGuards", "canNavigate", "allowed", "reason", "redirectTo", "reset", "index", "routes", "flowResult", "validateNavigationFlow", "from", "to", "trackNavigationAction", "error", "trackNavigationError", "Error", "message", "setTimeout", "_x", "_x2", "apply", "goBack", "state", "_state$routes", "isNavigating", "useNavigationPerformance", "startTimeRef", "useEffect", "trackLoadTime", "customStartTime", "getLoadTime", "useNavigationAnalytics", "_useAuthStore2", "trackEvent", "eventType", "data", "action", "fromScreen", "toScreen", "trackFlowCompletion", "flowName", "success", "metadata", "getStats", "getNavigationStats", "currentScreen"], "sources": ["useNavigationGuard.ts"], "sourcesContent": ["/**\n * Navigation Guard Hook - Enhanced navigation with guards and analytics\n *\n * Hook Contract:\n * - Provides navigation with built-in guards and analytics\n * - Handles authentication and role-based navigation\n * - Tracks navigation events automatically\n * - Provides error handling for navigation failures\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport {\n  useNavigation,\n  useRoute,\n  useFocusEffect,\n} from '@react-navigation/native';\nimport { useCallback, useRef, useEffect } from 'react';\n\nimport navigationAnalytics from '../services/navigationAnalytics';\nimport navigationGuards, {\n  NavigationGuardResult,\n} from '../services/navigationGuards';\nimport { useAuthStore } from '../store/authSlice';\n\ninterface UseNavigationGuardOptions {\n  trackAnalytics?: boolean;\n  enforceGuards?: boolean;\n  logNavigation?: boolean;\n}\n\ninterface GuardedNavigationResult {\n  navigate: (routeName: string, params?: any) => Promise<boolean>;\n  goBack: () => void;\n  reset: (state: any) => void;\n  canNavigate: (routeName: string, params?: any) => NavigationGuardResult;\n  trackScreenView: (params?: any) => void;\n  isNavigating: boolean;\n}\n\nexport const useNavigationGuard = (\n  options: UseNavigationGuardOptions = {},\n): GuardedNavigationResult => {\n  const {\n    trackAnalytics = true,\n    enforceGuards = true,\n    logNavigation = __DEV__,\n  } = options;\n\n  const navigation = useNavigation();\n  const route = useRoute();\n  const { userRole } = useAuthStore();\n\n  const isNavigatingRef = useRef(false);\n  const screenStartTimeRef = useRef<number>(0);\n  const currentScreenRef = useRef<string>(route.name);\n\n  // Track screen focus for analytics\n  useFocusEffect(\n    useCallback(() => {\n      const screenName = route.name;\n      const startTime = Date.now();\n      screenStartTimeRef.current = startTime;\n      currentScreenRef.current = screenName;\n\n      if (trackAnalytics) {\n        navigationAnalytics.trackScreenView(screenName, route.params, userRole);\n\n        // Track screen load time\n        const loadTime = startTime - (screenStartTimeRef.current || startTime);\n        if (loadTime > 0) {\n          navigationAnalytics.trackScreenLoadTime(screenName, loadTime);\n        }\n      }\n\n      if (logNavigation) {\n        console.log(`🧭 Navigation: Focused on ${screenName}`, route.params);\n      }\n\n      // Cleanup function when screen loses focus\n      return () => {\n        if (trackAnalytics && screenStartTimeRef.current > 0) {\n          const timeSpent = Date.now() - screenStartTimeRef.current;\n          // Track time spent on screen (this would be handled by navigationAnalytics internally)\n        }\n      };\n    }, [route.name, route.params, trackAnalytics, userRole, logNavigation]),\n  );\n\n  /**\n   * Enhanced navigate function with guards and analytics\n   */\n  const navigate = useCallback(\n    async (routeName: string, params?: any): Promise<boolean> => {\n      if (isNavigatingRef.current) {\n        if (logNavigation) {\n          console.warn('🧭 Navigation: Already navigating, ignoring request');\n        }\n        return false;\n      }\n\n      isNavigatingRef.current = true;\n\n      try {\n        // Check navigation guards\n        if (enforceGuards) {\n          const guardResult = navigationGuards.canNavigate(routeName, params);\n\n          if (!guardResult.allowed) {\n            if (logNavigation) {\n              console.warn('🧭 Navigation: Blocked by guard', {\n                route: routeName,\n                reason: guardResult.reason,\n                redirectTo: guardResult.redirectTo,\n              });\n            }\n\n            // Handle guard failure\n            if (guardResult.redirectTo) {\n              navigation.reset({\n                index: 0,\n                routes: [{ name: guardResult.redirectTo }],\n              });\n            }\n\n            return false;\n          }\n        }\n\n        // Validate navigation flow\n        const flowResult = navigationGuards.validateNavigationFlow(\n          currentScreenRef.current,\n          routeName,\n          params,\n        );\n\n        if (!flowResult.allowed) {\n          if (logNavigation) {\n            console.warn('🧭 Navigation: Invalid flow', {\n              from: currentScreenRef.current,\n              to: routeName,\n              reason: flowResult.reason,\n            });\n          }\n          return false;\n        }\n\n        // Track navigation action\n        if (trackAnalytics) {\n          navigationAnalytics.trackNavigationAction(\n            'button_press',\n            currentScreenRef.current,\n            routeName,\n            params,\n          );\n        }\n\n        // Perform navigation\n        navigation.navigate(routeName as never, params as never);\n\n        if (logNavigation) {\n          console.log('🧭 Navigation: Success', {\n            from: currentScreenRef.current,\n            to: routeName,\n            params,\n          });\n        }\n\n        return true;\n      } catch (error) {\n        console.error('🧭 Navigation: Error', error);\n\n        if (trackAnalytics) {\n          navigationAnalytics.trackNavigationError(\n            error instanceof Error ? error.message : 'Unknown navigation error',\n            routeName,\n            params,\n          );\n        }\n\n        return false;\n      } finally {\n        // Reset navigation flag after a short delay\n        setTimeout(() => {\n          isNavigatingRef.current = false;\n        }, 100);\n      }\n    },\n    [navigation, enforceGuards, trackAnalytics, logNavigation],\n  );\n\n  /**\n   * Enhanced goBack function with analytics\n   */\n  const goBack = useCallback(() => {\n    if (trackAnalytics) {\n      navigationAnalytics.trackNavigationAction(\n        'back_button',\n        currentScreenRef.current,\n        'previous_screen',\n      );\n    }\n\n    if (logNavigation) {\n      console.log('🧭 Navigation: Going back from', currentScreenRef.current);\n    }\n\n    navigation.goBack();\n  }, [navigation, trackAnalytics, logNavigation]);\n\n  /**\n   * Enhanced reset function with analytics\n   */\n  const reset = useCallback(\n    (state: any) => {\n      if (trackAnalytics) {\n        navigationAnalytics.trackNavigationAction(\n          'deep_link',\n          currentScreenRef.current,\n          state.routes?.[state.index]?.name || 'unknown',\n        );\n      }\n\n      if (logNavigation) {\n        console.log('🧭 Navigation: Reset to', state);\n      }\n\n      navigation.reset(state);\n    },\n    [navigation, trackAnalytics, logNavigation],\n  );\n\n  /**\n   * Check if navigation is allowed without actually navigating\n   */\n  const canNavigate = useCallback(\n    (routeName: string, params?: any): NavigationGuardResult => {\n      return navigationGuards.canNavigate(routeName, params);\n    },\n    [],\n  );\n\n  /**\n   * Manually track screen view (useful for modal screens or custom flows)\n   */\n  const trackScreenView = useCallback(\n    (params?: any) => {\n      if (trackAnalytics) {\n        navigationAnalytics.trackScreenView(route.name, params, userRole);\n      }\n    },\n    [route.name, trackAnalytics, userRole],\n  );\n\n  return {\n    navigate,\n    goBack,\n    reset,\n    canNavigate,\n    trackScreenView,\n    isNavigating: isNavigatingRef.current,\n  };\n};\n\n/**\n * Hook for tracking navigation performance\n */\nexport const useNavigationPerformance = () => {\n  const route = useRoute();\n  const startTimeRef = useRef<number>(Date.now());\n\n  useEffect(() => {\n    startTimeRef.current = Date.now();\n  }, [route.name]);\n\n  const trackLoadTime = useCallback(\n    (customStartTime?: number) => {\n      const loadTime = Date.now() - (customStartTime || startTimeRef.current);\n      navigationAnalytics.trackScreenLoadTime(route.name, loadTime);\n      return loadTime;\n    },\n    [route.name],\n  );\n\n  return {\n    trackLoadTime,\n    getLoadTime: () => Date.now() - startTimeRef.current,\n  };\n};\n\n/**\n * Hook for navigation analytics only (without guards)\n */\nexport const useNavigationAnalytics = () => {\n  const route = useRoute();\n  const { userRole } = useAuthStore();\n\n  const trackEvent = useCallback(\n    (\n      eventType:\n        | 'screen_view'\n        | 'navigation_action'\n        | 'flow_completion'\n        | 'error',\n      data: any,\n    ) => {\n      switch (eventType) {\n        case 'screen_view':\n          navigationAnalytics.trackScreenView(\n            data.screenName,\n            data.params,\n            userRole,\n          );\n          break;\n        case 'navigation_action':\n          navigationAnalytics.trackNavigationAction(\n            data.action,\n            data.fromScreen,\n            data.toScreen,\n            data.params,\n          );\n          break;\n        case 'flow_completion':\n          navigationAnalytics.trackFlowCompletion(\n            data.flowName,\n            data.success,\n            data.metadata,\n          );\n          break;\n        case 'error':\n          navigationAnalytics.trackNavigationError(\n            data.error,\n            data.screenName,\n            data.params,\n          );\n          break;\n      }\n    },\n    [userRole],\n  );\n\n  const getStats = useCallback(() => {\n    return navigationAnalytics.getNavigationStats();\n  }, []);\n\n  return {\n    trackEvent,\n    getStats,\n    currentScreen: route.name,\n  };\n};\n"], "mappings": ";;;;;;AAaA,IAAAA,OAAA,GAAAC,OAAA;AAKA,IAAAC,MAAA,GAAAD,OAAA;AAEA,IAAAE,oBAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,iBAAA,GAAAD,sBAAA,CAAAH,OAAA;AAGA,IAAAK,UAAA,GAAAL,OAAA;AAiBO,IAAMM,kBAAkB,GAAAC,OAAA,CAAAD,kBAAA,GAAG,SAArBA,kBAAkBA,CAAA,EAED;EAAA,IAD5BE,OAAkC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAEvC,IAAAG,qBAAA,GAIIJ,OAAO,CAHTK,cAAc;IAAdA,cAAc,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IAAAE,qBAAA,GAGnBN,OAAO,CAFTO,aAAa;IAAbA,aAAa,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IAAAE,qBAAA,GAElBR,OAAO,CADTS,aAAa;IAAbA,aAAa,GAAAD,qBAAA,cAAGE,OAAO,GAAAF,qBAAA;EAGzB,IAAMG,UAAU,GAAG,IAAAC,qBAAa,EAAC,CAAC;EAClC,IAAMC,KAAK,GAAG,IAAAC,gBAAQ,EAAC,CAAC;EACxB,IAAAC,aAAA,GAAqB,IAAAC,uBAAY,EAAC,CAAC;IAA3BC,QAAQ,GAAAF,aAAA,CAARE,QAAQ;EAEhB,IAAMC,eAAe,GAAG,IAAAC,aAAM,EAAC,KAAK,CAAC;EACrC,IAAMC,kBAAkB,GAAG,IAAAD,aAAM,EAAS,CAAC,CAAC;EAC5C,IAAME,gBAAgB,GAAG,IAAAF,aAAM,EAASN,KAAK,CAACS,IAAI,CAAC;EAGnD,IAAAC,sBAAc,EACZ,IAAAC,kBAAW,EAAC,YAAM;IAChB,IAAMC,UAAU,GAAGZ,KAAK,CAACS,IAAI;IAC7B,IAAMI,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAC5BR,kBAAkB,CAACS,OAAO,GAAGH,SAAS;IACtCL,gBAAgB,CAACQ,OAAO,GAAGJ,UAAU;IAErC,IAAIpB,cAAc,EAAE;MAClByB,4BAAmB,CAACC,eAAe,CAACN,UAAU,EAAEZ,KAAK,CAACmB,MAAM,EAAEf,QAAQ,CAAC;MAGvE,IAAMgB,QAAQ,GAAGP,SAAS,IAAIN,kBAAkB,CAACS,OAAO,IAAIH,SAAS,CAAC;MACtE,IAAIO,QAAQ,GAAG,CAAC,EAAE;QAChBH,4BAAmB,CAACI,mBAAmB,CAACT,UAAU,EAAEQ,QAAQ,CAAC;MAC/D;IACF;IAEA,IAAIxB,aAAa,EAAE;MACjB0B,OAAO,CAACC,GAAG,CAAC,6BAA6BX,UAAU,EAAE,EAAEZ,KAAK,CAACmB,MAAM,CAAC;IACtE;IAGA,OAAO,YAAM;MACX,IAAI3B,cAAc,IAAIe,kBAAkB,CAACS,OAAO,GAAG,CAAC,EAAE;QACpD,IAAMQ,SAAS,GAAGV,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGR,kBAAkB,CAACS,OAAO;MAE3D;IACF,CAAC;EACH,CAAC,EAAE,CAAChB,KAAK,CAACS,IAAI,EAAET,KAAK,CAACmB,MAAM,EAAE3B,cAAc,EAAEY,QAAQ,EAAER,aAAa,CAAC,CACxE,CAAC;EAKD,IAAM6B,QAAQ,GAAG,IAAAd,kBAAW;IAAA,IAAAe,IAAA,OAAAC,kBAAA,CAAAC,OAAA,EAC1B,WAAOC,SAAiB,EAAEV,MAAY,EAAuB;MAC3D,IAAId,eAAe,CAACW,OAAO,EAAE;QAC3B,IAAIpB,aAAa,EAAE;UACjB0B,OAAO,CAACQ,IAAI,CAAC,qDAAqD,CAAC;QACrE;QACA,OAAO,KAAK;MACd;MAEAzB,eAAe,CAACW,OAAO,GAAG,IAAI;MAE9B,IAAI;QAEF,IAAItB,aAAa,EAAE;UACjB,IAAMqC,WAAW,GAAGC,yBAAgB,CAACC,WAAW,CAACJ,SAAS,EAAEV,MAAM,CAAC;UAEnE,IAAI,CAACY,WAAW,CAACG,OAAO,EAAE;YACxB,IAAItC,aAAa,EAAE;cACjB0B,OAAO,CAACQ,IAAI,CAAC,iCAAiC,EAAE;gBAC9C9B,KAAK,EAAE6B,SAAS;gBAChBM,MAAM,EAAEJ,WAAW,CAACI,MAAM;gBAC1BC,UAAU,EAAEL,WAAW,CAACK;cAC1B,CAAC,CAAC;YACJ;YAGA,IAAIL,WAAW,CAACK,UAAU,EAAE;cAC1BtC,UAAU,CAACuC,KAAK,CAAC;gBACfC,KAAK,EAAE,CAAC;gBACRC,MAAM,EAAE,CAAC;kBAAE9B,IAAI,EAAEsB,WAAW,CAACK;gBAAW,CAAC;cAC3C,CAAC,CAAC;YACJ;YAEA,OAAO,KAAK;UACd;QACF;QAGA,IAAMI,UAAU,GAAGR,yBAAgB,CAACS,sBAAsB,CACxDjC,gBAAgB,CAACQ,OAAO,EACxBa,SAAS,EACTV,MACF,CAAC;QAED,IAAI,CAACqB,UAAU,CAACN,OAAO,EAAE;UACvB,IAAItC,aAAa,EAAE;YACjB0B,OAAO,CAACQ,IAAI,CAAC,6BAA6B,EAAE;cAC1CY,IAAI,EAAElC,gBAAgB,CAACQ,OAAO;cAC9B2B,EAAE,EAAEd,SAAS;cACbM,MAAM,EAAEK,UAAU,CAACL;YACrB,CAAC,CAAC;UACJ;UACA,OAAO,KAAK;QACd;QAGA,IAAI3C,cAAc,EAAE;UAClByB,4BAAmB,CAAC2B,qBAAqB,CACvC,cAAc,EACdpC,gBAAgB,CAACQ,OAAO,EACxBa,SAAS,EACTV,MACF,CAAC;QACH;QAGArB,UAAU,CAAC2B,QAAQ,CAACI,SAAS,EAAWV,MAAe,CAAC;QAExD,IAAIvB,aAAa,EAAE;UACjB0B,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;YACpCmB,IAAI,EAAElC,gBAAgB,CAACQ,OAAO;YAC9B2B,EAAE,EAAEd,SAAS;YACbV,MAAM,EAANA;UACF,CAAC,CAAC;QACJ;QAEA,OAAO,IAAI;MACb,CAAC,CAAC,OAAO0B,KAAK,EAAE;QACdvB,OAAO,CAACuB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAE5C,IAAIrD,cAAc,EAAE;UAClByB,4BAAmB,CAAC6B,oBAAoB,CACtCD,KAAK,YAAYE,KAAK,GAAGF,KAAK,CAACG,OAAO,GAAG,0BAA0B,EACnEnB,SAAS,EACTV,MACF,CAAC;QACH;QAEA,OAAO,KAAK;MACd,CAAC,SAAS;QAER8B,UAAU,CAAC,YAAM;UACf5C,eAAe,CAACW,OAAO,GAAG,KAAK;QACjC,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC;IAAA,iBAAAkC,EAAA,EAAAC,GAAA;MAAA,OAAAzB,IAAA,CAAA0B,KAAA,OAAAhE,SAAA;IAAA;EAAA,KACD,CAACU,UAAU,EAAEJ,aAAa,EAAEF,cAAc,EAAEI,aAAa,CAC3D,CAAC;EAKD,IAAMyD,MAAM,GAAG,IAAA1C,kBAAW,EAAC,YAAM;IAC/B,IAAInB,cAAc,EAAE;MAClByB,4BAAmB,CAAC2B,qBAAqB,CACvC,aAAa,EACbpC,gBAAgB,CAACQ,OAAO,EACxB,iBACF,CAAC;IACH;IAEA,IAAIpB,aAAa,EAAE;MACjB0B,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEf,gBAAgB,CAACQ,OAAO,CAAC;IACzE;IAEAlB,UAAU,CAACuD,MAAM,CAAC,CAAC;EACrB,CAAC,EAAE,CAACvD,UAAU,EAAEN,cAAc,EAAEI,aAAa,CAAC,CAAC;EAK/C,IAAMyC,KAAK,GAAG,IAAA1B,kBAAW,EACvB,UAAC2C,KAAU,EAAK;IACd,IAAI9D,cAAc,EAAE;MAAA,IAAA+D,aAAA;MAClBtC,4BAAmB,CAAC2B,qBAAqB,CACvC,WAAW,EACXpC,gBAAgB,CAACQ,OAAO,EACxB,EAAAuC,aAAA,GAAAD,KAAK,CAACf,MAAM,cAAAgB,aAAA,GAAZA,aAAA,CAAeD,KAAK,CAAChB,KAAK,CAAC,qBAA3BiB,aAAA,CAA6B9C,IAAI,KAAI,SACvC,CAAC;IACH;IAEA,IAAIb,aAAa,EAAE;MACjB0B,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE+B,KAAK,CAAC;IAC/C;IAEAxD,UAAU,CAACuC,KAAK,CAACiB,KAAK,CAAC;EACzB,CAAC,EACD,CAACxD,UAAU,EAAEN,cAAc,EAAEI,aAAa,CAC5C,CAAC;EAKD,IAAMqC,WAAW,GAAG,IAAAtB,kBAAW,EAC7B,UAACkB,SAAiB,EAAEV,MAAY,EAA4B;IAC1D,OAAOa,yBAAgB,CAACC,WAAW,CAACJ,SAAS,EAAEV,MAAM,CAAC;EACxD,CAAC,EACD,EACF,CAAC;EAKD,IAAMD,eAAe,GAAG,IAAAP,kBAAW,EACjC,UAACQ,MAAY,EAAK;IAChB,IAAI3B,cAAc,EAAE;MAClByB,4BAAmB,CAACC,eAAe,CAAClB,KAAK,CAACS,IAAI,EAAEU,MAAM,EAAEf,QAAQ,CAAC;IACnE;EACF,CAAC,EACD,CAACJ,KAAK,CAACS,IAAI,EAAEjB,cAAc,EAAEY,QAAQ,CACvC,CAAC;EAED,OAAO;IACLqB,QAAQ,EAARA,QAAQ;IACR4B,MAAM,EAANA,MAAM;IACNhB,KAAK,EAALA,KAAK;IACLJ,WAAW,EAAXA,WAAW;IACXf,eAAe,EAAfA,eAAe;IACfsC,YAAY,EAAEnD,eAAe,CAACW;EAChC,CAAC;AACH,CAAC;AAKM,IAAMyC,wBAAwB,GAAAvE,OAAA,CAAAuE,wBAAA,GAAG,SAA3BA,wBAAwBA,CAAA,EAAS;EAC5C,IAAMzD,KAAK,GAAG,IAAAC,gBAAQ,EAAC,CAAC;EACxB,IAAMyD,YAAY,GAAG,IAAApD,aAAM,EAASQ,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAE/C,IAAA4C,gBAAS,EAAC,YAAM;IACdD,YAAY,CAAC1C,OAAO,GAAGF,IAAI,CAACC,GAAG,CAAC,CAAC;EACnC,CAAC,EAAE,CAACf,KAAK,CAACS,IAAI,CAAC,CAAC;EAEhB,IAAMmD,aAAa,GAAG,IAAAjD,kBAAW,EAC/B,UAACkD,eAAwB,EAAK;IAC5B,IAAMzC,QAAQ,GAAGN,IAAI,CAACC,GAAG,CAAC,CAAC,IAAI8C,eAAe,IAAIH,YAAY,CAAC1C,OAAO,CAAC;IACvEC,4BAAmB,CAACI,mBAAmB,CAACrB,KAAK,CAACS,IAAI,EAAEW,QAAQ,CAAC;IAC7D,OAAOA,QAAQ;EACjB,CAAC,EACD,CAACpB,KAAK,CAACS,IAAI,CACb,CAAC;EAED,OAAO;IACLmD,aAAa,EAAbA,aAAa;IACbE,WAAW,EAAE,SAAbA,WAAWA,CAAA;MAAA,OAAQhD,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG2C,YAAY,CAAC1C,OAAO;IAAA;EACtD,CAAC;AACH,CAAC;AAKM,IAAM+C,sBAAsB,GAAA7E,OAAA,CAAA6E,sBAAA,GAAG,SAAzBA,sBAAsBA,CAAA,EAAS;EAC1C,IAAM/D,KAAK,GAAG,IAAAC,gBAAQ,EAAC,CAAC;EACxB,IAAA+D,cAAA,GAAqB,IAAA7D,uBAAY,EAAC,CAAC;IAA3BC,QAAQ,GAAA4D,cAAA,CAAR5D,QAAQ;EAEhB,IAAM6D,UAAU,GAAG,IAAAtD,kBAAW,EAC5B,UACEuD,SAIW,EACXC,IAAS,EACN;IACH,QAAQD,SAAS;MACf,KAAK,aAAa;QAChBjD,4BAAmB,CAACC,eAAe,CACjCiD,IAAI,CAACvD,UAAU,EACfuD,IAAI,CAAChD,MAAM,EACXf,QACF,CAAC;QACD;MACF,KAAK,mBAAmB;QACtBa,4BAAmB,CAAC2B,qBAAqB,CACvCuB,IAAI,CAACC,MAAM,EACXD,IAAI,CAACE,UAAU,EACfF,IAAI,CAACG,QAAQ,EACbH,IAAI,CAAChD,MACP,CAAC;QACD;MACF,KAAK,iBAAiB;QACpBF,4BAAmB,CAACsD,mBAAmB,CACrCJ,IAAI,CAACK,QAAQ,EACbL,IAAI,CAACM,OAAO,EACZN,IAAI,CAACO,QACP,CAAC;QACD;MACF,KAAK,OAAO;QACVzD,4BAAmB,CAAC6B,oBAAoB,CACtCqB,IAAI,CAACtB,KAAK,EACVsB,IAAI,CAACvD,UAAU,EACfuD,IAAI,CAAChD,MACP,CAAC;QACD;IACJ;EACF,CAAC,EACD,CAACf,QAAQ,CACX,CAAC;EAED,IAAMuE,QAAQ,GAAG,IAAAhE,kBAAW,EAAC,YAAM;IACjC,OAAOM,4BAAmB,CAAC2D,kBAAkB,CAAC,CAAC;EACjD,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACLX,UAAU,EAAVA,UAAU;IACVU,QAAQ,EAARA,QAAQ;IACRE,aAAa,EAAE7E,KAAK,CAACS;EACvB,CAAC;AACH,CAAC", "ignoreList": []}