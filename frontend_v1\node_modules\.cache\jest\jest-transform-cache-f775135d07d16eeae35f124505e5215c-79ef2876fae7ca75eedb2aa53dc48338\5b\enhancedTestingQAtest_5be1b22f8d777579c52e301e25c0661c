fff7d46ff4c00d384d751c8cc967da2f
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _react = _interopRequireDefault(require("react"));
var _reactNative = require("react-native");
var _enhancedTestingQA = require("../enhancedTestingQA");
var mockPerformanceNow = jest.fn();
Object.defineProperty(global, 'performance', {
  value: {
    now: mockPerformanceNow
  },
  writable: true
});
describe('Enhanced Testing and QA System', function () {
  var testingQA;
  beforeEach(function () {
    testingQA = new _enhancedTestingQA.EnhancedTestingQA();
    mockPerformanceNow.mockReturnValue(0);
    jest.clearAllMocks();
  });
  describe('REC-TEST-001 through REC-TEST-008: Complete Testing Infrastructure', function () {
    it('should initialize with default configuration', function () {
      expect(testingQA).toBeInstanceOf(_enhancedTestingQA.EnhancedTestingQA);
      var metrics = testingQA.getMetrics();
      expect(metrics.totalTests).toBe(0);
      expect(metrics.performanceScore).toBe(0);
      expect(metrics.accessibilityScore).toBe(0);
      expect(metrics.codeQualityScore).toBe(0);
    });
    it('should support custom configuration', function () {
      var customConfig = {
        enablePerformanceTesting: false,
        testTimeout: 5000,
        coverageThreshold: {
          statements: 90,
          branches: 85,
          functions: 90,
          lines: 90
        }
      };
      var customTestingQA = new _enhancedTestingQA.EnhancedTestingQA(customConfig);
      expect(customTestingQA).toBeInstanceOf(_enhancedTestingQA.EnhancedTestingQA);
    });
    it('should validate default configuration values', function () {
      expect(_enhancedTestingQA.DEFAULT_ENHANCED_TEST_CONFIG.enablePerformanceTesting).toBe(true);
      expect(_enhancedTestingQA.DEFAULT_ENHANCED_TEST_CONFIG.enableAccessibilityTesting).toBe(true);
      expect(_enhancedTestingQA.DEFAULT_ENHANCED_TEST_CONFIG.enableVisualRegression).toBe(false);
      expect(_enhancedTestingQA.DEFAULT_ENHANCED_TEST_CONFIG.testTimeout).toBe(10000);
      expect(_enhancedTestingQA.DEFAULT_ENHANCED_TEST_CONFIG.coverageThreshold.statements).toBe(85);
      expect(_enhancedTestingQA.DEFAULT_ENHANCED_TEST_CONFIG.performanceBudget.renderTime).toBe(16);
    });
  });
  describe('Performance Testing', function () {
    it('should validate performance metrics', function () {
      var consoleSpy = jest.spyOn(console, 'warn').mockImplementation(function () {});
      testingQA.validatePerformance({
        renderTime: 10
      });
      expect(consoleSpy).not.toHaveBeenCalled();
      testingQA.validatePerformance({
        renderTime: 20
      });
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Performance Warning: Render time'));
      testingQA.validatePerformance({
        renderTime: 10,
        memoryUsage: 60
      });
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Memory usage'));
      consoleSpy.mockRestore();
    });
    it('should calculate performance scores correctly', function () {
      testingQA.validatePerformance({
        renderTime: 8
      });
      var metrics = testingQA.getMetrics();
      expect(metrics.performanceScore).toBeGreaterThan(50);
      testingQA.validatePerformance({
        renderTime: 64
      });
      metrics = testingQA.getMetrics();
      expect(metrics.performanceScore).toBeLessThanOrEqual(50);
    });
  });
  describe('Accessibility Testing', function () {
    it('should validate accessibility for elements with labels', function () {
      var mockContainer = {
        querySelectorAll: jest.fn(function (selector) {
          if (selector.includes('button')) {
            return [{
              accessibilityLabel: 'Test Button'
            }, {
              getAttribute: function getAttribute() {
                return 'Test Button 2';
              }
            }];
          }
          if (selector.includes('heading')) {
            return [{
              role: 'heading'
            }];
          }
          return [];
        })
      };
      var issues = testingQA.validateAccessibility(mockContainer);
      expect(issues).toHaveLength(0);
    });
    it('should detect accessibility issues', function () {
      var consoleSpy = jest.spyOn(console, 'warn').mockImplementation(function () {});
      var mockContainer = {
        querySelectorAll: jest.fn(function (selector) {
          if (selector.includes('button')) {
            return [{
              getAttribute: function getAttribute() {
                return null;
              }
            }, {
              accessibilityLabel: 'Good Button'
            }];
          }
          if (selector.includes('heading')) {
            return [];
          }
          return [];
        })
      };
      var issues = testingQA.validateAccessibility(mockContainer);
      expect(issues.length).toBeGreaterThan(0);
      expect(consoleSpy).toHaveBeenCalledWith('♿ Accessibility Issues:', expect.any(Array));
      consoleSpy.mockRestore();
    });
    it('should calculate accessibility scores', function () {
      var mockContainer = {
        querySelectorAll: jest.fn(function () {
          return [{
            accessibilityLabel: 'Button 1'
          }, {
            accessibilityLabel: 'Button 2'
          }];
        })
      };
      testingQA.validateAccessibility(mockContainer);
      var metrics = testingQA.getMetrics();
      expect(metrics.accessibilityScore).toBe(100);
    });
  });
  describe('Code Quality Testing', function () {
    it('should detect code quality issues', function () {
      var badCode = `
        console.log('debug info');
        const data: any = {};
        // TODO: fix this later
        // FIXME: broken implementation
      `;
      var issues = testingQA.validateCodeQuality(badCode);
      expect(issues.length).toBeGreaterThan(0);
      expect(issues.some(function (issue) {
        return issue.includes('Console.log');
      })).toBe(true);
      expect(issues.some(function (issue) {
        return issue.includes('TODO/FIXME');
      })).toBe(true);
      expect(issues.some(function (issue) {
        return issue.includes('any');
      })).toBe(true);
    });
    it('should pass clean code validation', function () {
      var goodCode = `
        const data: string = 'clean code';
        const handleClick = (event: Event) => {
          // Proper implementation
        };
      `;
      var issues = testingQA.validateCodeQuality(goodCode);
      expect(issues).toHaveLength(0);
    });
    it('should calculate code quality scores', function () {
      var badCode = 'console.log("test"); const x: any = {};';
      testingQA.validateCodeQuality(badCode);
      var metrics = testingQA.getMetrics();
      expect(metrics.codeQualityScore).toBeLessThan(100);
    });
  });
  describe('Test Data Factories', function () {
    it('should create user test data', function () {
      var user = _enhancedTestingQA.testDataFactories.user();
      expect(user).toHaveProperty('id');
      expect(user).toHaveProperty('firstName', 'Test');
      expect(user).toHaveProperty('lastName', 'User');
      expect(user).toHaveProperty('email', '<EMAIL>');
    });
    it('should create user test data with overrides', function () {
      var user = _enhancedTestingQA.testDataFactories.user({
        firstName: 'Custom',
        email: '<EMAIL>'
      });
      expect(user.firstName).toBe('Custom');
      expect(user.email).toBe('<EMAIL>');
      expect(user.lastName).toBe('User');
    });
    it('should create service test data', function () {
      var service = _enhancedTestingQA.testDataFactories.service();
      expect(service).toHaveProperty('id');
      expect(service).toHaveProperty('name', 'Test Service');
      expect(service).toHaveProperty('price', 50);
      expect(service).toHaveProperty('duration', 60);
    });
    it('should create booking test data', function () {
      var booking = _enhancedTestingQA.testDataFactories.booking();
      expect(booking).toHaveProperty('id');
      expect(booking).toHaveProperty('userId');
      expect(booking).toHaveProperty('serviceId');
      expect(booking).toHaveProperty('status', 'confirmed');
    });
    it('should create provider test data', function () {
      var provider = _enhancedTestingQA.testDataFactories.provider();
      expect(provider).toHaveProperty('id');
      expect(provider).toHaveProperty('businessName', 'Test Provider');
      expect(provider).toHaveProperty('rating', 4.5);
      expect(provider).toHaveProperty('location');
      expect(provider.location).toHaveProperty('address');
    });
  });
  describe('Enhanced Assertions', function () {
    var TestComponent = function TestComponent() {
      return _react.default.createElement(_reactNative.Text, null, 'Test');
    };
    it('should provide enhanced assertion helpers', function () {
      expect(_enhancedTestingQA.enhancedAssertions.expectToRenderWithoutErrors).toBeDefined();
      expect(_enhancedTestingQA.enhancedAssertions.expectToBeAccessible).toBeDefined();
      expect(_enhancedTestingQA.enhancedAssertions.expectToMeetPerformanceBudget).toBeDefined();
      expect(_enhancedTestingQA.enhancedAssertions.expectToHaveTestCoverage).toBeDefined();
    });
    it('should validate test coverage expectations', function () {
      var goodCoverage = {
        coverage: {
          statements: {
            pct: 90
          },
          branches: {
            pct: 85
          },
          functions: {
            pct: 90
          },
          lines: {
            pct: 88
          }
        }
      };
      expect(function () {
        _enhancedTestingQA.enhancedAssertions.expectToHaveTestCoverage(goodCoverage);
      }).not.toThrow();
      var badCoverage = {
        coverage: {
          statements: {
            pct: 70
          },
          branches: {
            pct: 60
          },
          functions: {
            pct: 65
          },
          lines: {
            pct: 68
          }
        }
      };
      expect(function () {
        _enhancedTestingQA.enhancedAssertions.expectToHaveTestCoverage(badCoverage);
      }).toThrow();
    });
  });
  describe('Test Suite Utilities', function () {
    it('should provide test suite creation utilities', function () {
      expect(_enhancedTestingQA.testSuiteUtils.createComprehensiveTestSuite).toBeDefined();
      expect(_enhancedTestingQA.testSuiteUtils.createIntegrationTestSuite).toBeDefined();
    });
    it('should create comprehensive test suites', function () {
      var TestComponent = function TestComponent() {
        return _react.default.createElement(_reactNative.Text, null, 'Test');
      };
      var suiteConfig = _enhancedTestingQA.testSuiteUtils.createComprehensiveTestSuite('TestComponent', TestComponent(), [{
        name: 'should handle custom test case',
        test: function test() {
          return expect(true).toBe(true);
        }
      }]);
      expect(suiteConfig).toHaveProperty('suiteName');
      expect(suiteConfig).toHaveProperty('tests');
      expect(suiteConfig.suiteName).toBe('TestComponent - Comprehensive Test Suite');
      expect(suiteConfig.tests).toHaveLength(4);
    });
  });
  describe('Quality Reporting', function () {
    it('should generate quality reports', function () {
      testingQA.validatePerformance({
        renderTime: 10
      });
      testingQA.validateCodeQuality('const x: string = "clean";');
      var report = testingQA.generateQualityReport();
      expect(report).toContain('Test Quality Report');
      expect(report).toContain('Quality Scores');
      expect(report).toContain('Performance:');
      expect(report).toContain('Accessibility:');
      expect(report).toContain('Code Quality:');
      expect(report).toContain('Overall:');
    });
    it('should provide current metrics', function () {
      var metrics = testingQA.getMetrics();
      expect(metrics).toHaveProperty('totalTests');
      expect(metrics).toHaveProperty('passingTests');
      expect(metrics).toHaveProperty('failingTests');
      expect(metrics).toHaveProperty('performanceScore');
      expect(metrics).toHaveProperty('accessibilityScore');
      expect(metrics).toHaveProperty('codeQualityScore');
    });
  });
  describe('Singleton Instance', function () {
    it('should provide singleton instance', function () {
      expect(_enhancedTestingQA.enhancedTestingQA).toBeInstanceOf(_enhancedTestingQA.EnhancedTestingQA);
    });
    it('should export utility functions', function () {
      expect(_enhancedTestingQA.testDataFactories).toBeDefined();
      expect(_enhancedTestingQA.enhancedAssertions).toBeDefined();
      expect(_enhancedTestingQA.testSuiteUtils).toBeDefined();
    });
  });
  describe('Error Handling', function () {
    it('should handle missing performance object gracefully', function () {
      var originalPerformance = global.performance;
      delete global.performance;
      expect(function () {
        testingQA.validatePerformance({
          renderTime: 10
        });
      }).not.toThrow();
      global.performance = originalPerformance;
    });
    it('should handle invalid container in accessibility validation', function () {
      var consoleSpy = jest.spyOn(console, 'warn').mockImplementation(function () {});
      var invalidContainer = {
        querySelectorAll: jest.fn(function () {
          throw new Error('Invalid selector');
        })
      };
      expect(function () {
        testingQA.validateAccessibility(invalidContainer);
      }).not.toThrow();
      expect(consoleSpy).toHaveBeenCalledWith('Accessibility validation failed:', expect.any(Error));
      consoleSpy.mockRestore();
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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