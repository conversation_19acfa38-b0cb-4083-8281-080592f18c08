d432210d82897ee0a7076115ee9df1bd
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _ViewConfigIgnore = require("../../NativeComponent/ViewConfigIgnore");
var RCTTextInputViewConfig = {
  bubblingEventTypes: {
    topBlur: {
      phasedRegistrationNames: {
        bubbled: 'onBlur',
        captured: 'onBlurCapture'
      }
    },
    topChange: {
      phasedRegistrationNames: {
        bubbled: 'onChange',
        captured: 'onChangeCapture'
      }
    },
    topEndEditing: {
      phasedRegistrationNames: {
        bubbled: 'onEndEditing',
        captured: 'onEndEditingCapture'
      }
    },
    topFocus: {
      phasedRegistrationNames: {
        bubbled: 'onFocus',
        captured: 'onFocusCapture'
      }
    },
    topKeyPress: {
      phasedRegistrationNames: {
        bubbled: 'onKeyPress',
        captured: 'onKeyPressCapture'
      }
    },
    topSubmitEditing: {
      phasedRegistrationNames: {
        bubbled: 'onSubmitEditing',
        captured: 'onSubmitEditingCapture'
      }
    },
    topTouchCancel: {
      phasedRegistrationNames: {
        bubbled: 'onTouchCancel',
        captured: 'onTouchCancelCapture'
      }
    },
    topTouchEnd: {
      phasedRegistrationNames: {
        bubbled: 'onTouchEnd',
        captured: 'onTouchEndCapture'
      }
    },
    topTouchMove: {
      phasedRegistrationNames: {
        bubbled: 'onTouchMove',
        captured: 'onTouchMoveCapture'
      }
    }
  },
  directEventTypes: {
    topScroll: {
      registrationName: 'onScroll'
    },
    topSelectionChange: {
      registrationName: 'onSelectionChange'
    },
    topContentSizeChange: {
      registrationName: 'onContentSizeChange'
    },
    topChangeSync: {
      registrationName: 'onChangeSync'
    },
    topKeyPressSync: {
      registrationName: 'onKeyPressSync'
    }
  },
  validAttributes: Object.assign({
    dynamicTypeRamp: true,
    fontSize: true,
    fontWeight: true,
    fontVariant: true,
    textShadowOffset: {
      diff: require("../../Utilities/differ/sizesDiffer").default
    },
    allowFontScaling: true,
    fontStyle: true,
    textTransform: true,
    textAlign: true,
    fontFamily: true,
    lineHeight: true,
    isHighlighted: true,
    writingDirection: true,
    textDecorationLine: true,
    textShadowRadius: true,
    letterSpacing: true,
    textDecorationStyle: true,
    textDecorationColor: {
      process: require("../../StyleSheet/processColor").default
    },
    color: {
      process: require("../../StyleSheet/processColor").default
    },
    maxFontSizeMultiplier: true,
    textShadowColor: {
      process: require("../../StyleSheet/processColor").default
    },
    editable: true,
    inputAccessoryViewID: true,
    inputAccessoryViewButtonLabel: true,
    caretHidden: true,
    enablesReturnKeyAutomatically: true,
    placeholderTextColor: {
      process: require("../../StyleSheet/processColor").default
    },
    clearButtonMode: true,
    keyboardType: true,
    selection: true,
    returnKeyType: true,
    submitBehavior: true,
    mostRecentEventCount: true,
    scrollEnabled: true,
    selectionColor: {
      process: require("../../StyleSheet/processColor").default
    },
    contextMenuHidden: true,
    secureTextEntry: true,
    placeholder: true,
    autoCorrect: true,
    multiline: true,
    numberOfLines: true,
    textContentType: true,
    maxLength: true,
    autoCapitalize: true,
    keyboardAppearance: true,
    passwordRules: true,
    spellCheck: true,
    selectTextOnFocus: true,
    text: true,
    clearTextOnFocus: true,
    showSoftInputOnFocus: true,
    autoFocus: true,
    lineBreakStrategyIOS: true,
    lineBreakModeIOS: true,
    smartInsertDelete: true
  }, (0, _ViewConfigIgnore.ConditionallyIgnoredEventHandlers)({
    onChange: true,
    onSelectionChange: true,
    onContentSizeChange: true,
    onScroll: true,
    onChangeSync: true,
    onKeyPressSync: true
  }), {
    disableKeyboardShortcuts: true
  })
};
var _default = exports.default = RCTTextInputViewConfig;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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