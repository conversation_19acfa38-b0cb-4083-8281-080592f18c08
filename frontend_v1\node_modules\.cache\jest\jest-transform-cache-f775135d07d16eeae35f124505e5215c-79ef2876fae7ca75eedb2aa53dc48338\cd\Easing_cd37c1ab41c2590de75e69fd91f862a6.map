{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "ease", "Easing", "step0", "n", "step1", "linear", "t", "bezier", "quad", "cubic", "poly", "Math", "pow", "sin", "cos", "PI", "circle", "sqrt", "exp", "elastic", "bounciness", "arguments", "length", "undefined", "p", "back", "s", "bounce", "t2", "x1", "y1", "x2", "y2", "_bezier", "require", "in", "easing", "out", "inOut", "_default"], "sources": ["Easing.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict\n */\n\n'use strict';\n\nlet ease;\n\n/**\n * The `Easing` module implements common easing functions. This module is used\n * by [Animate.timing()](docs/animate.html#timing) to convey physically\n * believable motion in animations.\n *\n * You can find a visualization of some common easing functions at\n * http://easings.net/\n *\n * ### Predefined animations\n *\n * The `Easing` module provides several predefined animations through the\n * following methods:\n *\n * - [`back`](docs/easing.html#back) provides a simple animation where the\n *   object goes slightly back before moving forward\n * - [`bounce`](docs/easing.html#bounce) provides a bouncing animation\n * - [`ease`](docs/easing.html#ease) provides a simple inertial animation\n * - [`elastic`](docs/easing.html#elastic) provides a simple spring interaction\n *\n * ### Standard functions\n *\n * Three standard easing functions are provided:\n *\n * - [`linear`](docs/easing.html#linear)\n * - [`quad`](docs/easing.html#quad)\n * - [`cubic`](docs/easing.html#cubic)\n *\n * The [`poly`](docs/easing.html#poly) function can be used to implement\n * quartic, quintic, and other higher power functions.\n *\n * ### Additional functions\n *\n * Additional mathematical functions are provided by the following methods:\n *\n * - [`bezier`](docs/easing.html#bezier) provides a cubic bezier curve\n * - [`circle`](docs/easing.html#circle) provides a circular function\n * - [`sin`](docs/easing.html#sin) provides a sinusoidal function\n * - [`exp`](docs/easing.html#exp) provides an exponential function\n *\n * The following helpers are used to modify other easing functions.\n *\n * - [`in`](docs/easing.html#in) runs an easing function forwards\n * - [`inOut`](docs/easing.html#inout) makes any easing function symmetrical\n * - [`out`](docs/easing.html#out) runs an easing function backwards\n */\nconst Easing = {\n  /**\n   * A stepping function, returns 1 for any positive value of `n`.\n   */\n  step0(n: number): number {\n    return n > 0 ? 1 : 0;\n  },\n\n  /**\n   * A stepping function, returns 1 if `n` is greater than or equal to 1.\n   */\n  step1(n: number): number {\n    return n >= 1 ? 1 : 0;\n  },\n\n  /**\n   * A linear function, `f(t) = t`. Position correlates to elapsed time one to\n   * one.\n   *\n   * http://cubic-bezier.com/#0,0,1,1\n   */\n  linear(t: number): number {\n    return t;\n  },\n\n  /**\n   * A simple inertial interaction, similar to an object slowly accelerating to\n   * speed.\n   *\n   * http://cubic-bezier.com/#.42,0,1,1\n   */\n  ease(t: number): number {\n    if (!ease) {\n      ease = Easing.bezier(0.42, 0, 1, 1);\n    }\n    return ease(t);\n  },\n\n  /**\n   * A quadratic function, `f(t) = t * t`. Position equals the square of elapsed\n   * time.\n   *\n   * http://easings.net/#easeInQuad\n   */\n  quad(t: number): number {\n    return t * t;\n  },\n\n  /**\n   * A cubic function, `f(t) = t * t * t`. Position equals the cube of elapsed\n   * time.\n   *\n   * http://easings.net/#easeInCubic\n   */\n  cubic(t: number): number {\n    return t * t * t;\n  },\n\n  /**\n   * A power function. Position is equal to the Nth power of elapsed time.\n   *\n   * n = 4: http://easings.net/#easeInQuart\n   * n = 5: http://easings.net/#easeInQuint\n   */\n  poly(n: number): (t: number) => number {\n    return (t: number) => Math.pow(t, n);\n  },\n\n  /**\n   * A sinusoidal function.\n   *\n   * http://easings.net/#easeInSine\n   */\n  sin(t: number): number {\n    return 1 - Math.cos((t * Math.PI) / 2);\n  },\n\n  /**\n   * A circular function.\n   *\n   * http://easings.net/#easeInCirc\n   */\n  circle(t: number): number {\n    return 1 - Math.sqrt(1 - t * t);\n  },\n\n  /**\n   * An exponential function.\n   *\n   * http://easings.net/#easeInExpo\n   */\n  exp(t: number): number {\n    return Math.pow(2, 10 * (t - 1));\n  },\n\n  /**\n   * A simple elastic interaction, similar to a spring oscillating back and\n   * forth.\n   *\n   * Default bounciness is 1, which overshoots a little bit once. 0 bounciness\n   * doesn't overshoot at all, and bounciness of N > 1 will overshoot about N\n   * times.\n   *\n   * http://easings.net/#easeInElastic\n   */\n  elastic(bounciness: number = 1): (t: number) => number {\n    const p = bounciness * Math.PI;\n    return t => 1 - Math.pow(Math.cos((t * Math.PI) / 2), 3) * Math.cos(t * p);\n  },\n\n  /**\n   * Use with `Animated.parallel()` to create a simple effect where the object\n   * animates back slightly as the animation starts.\n   *\n   * https://easings.net/#easeInBack\n   */\n  back(s: number = 1.70158): (t: number) => number {\n    return t => t * t * ((s + 1) * t - s);\n  },\n\n  /**\n   * Provides a simple bouncing effect.\n   *\n   * http://easings.net/#easeInBounce\n   */\n  bounce(t: number): number {\n    if (t < 1 / 2.75) {\n      return 7.5625 * t * t;\n    }\n\n    if (t < 2 / 2.75) {\n      const t2 = t - 1.5 / 2.75;\n      return 7.5625 * t2 * t2 + 0.75;\n    }\n\n    if (t < 2.5 / 2.75) {\n      const t2 = t - 2.25 / 2.75;\n      return 7.5625 * t2 * t2 + 0.9375;\n    }\n\n    const t2 = t - 2.625 / 2.75;\n    return 7.5625 * t2 * t2 + 0.984375;\n  },\n\n  /**\n   * Provides a cubic bezier curve, equivalent to CSS Transitions'\n   * `transition-timing-function`.\n   *\n   * A useful tool to visualize cubic bezier curves can be found at\n   * http://cubic-bezier.com/\n   */\n  bezier(\n    x1: number,\n    y1: number,\n    x2: number,\n    y2: number,\n  ): (t: number) => number {\n    const _bezier = require('./bezier').default;\n    return _bezier(x1, y1, x2, y2);\n  },\n\n  /**\n   * Runs an easing function forwards.\n   */\n  in(easing: (t: number) => number): (t: number) => number {\n    return easing;\n  },\n\n  /**\n   * Runs an easing function backwards.\n   */\n  out(easing: (t: number) => number): (t: number) => number {\n    return t => 1 - easing(1 - t);\n  },\n\n  /**\n   * Makes any easing function symmetrical. The easing function will run\n   * forwards for half of the duration, then backwards for the rest of the\n   * duration.\n   */\n  inOut(easing: (t: number) => number): (t: number) => number {\n    return t => {\n      if (t < 0.5) {\n        return easing(t * 2) / 2;\n      }\n      return 1 - easing((1 - t) * 2) / 2;\n    };\n  },\n};\n\nexport default Easing;\n"], "mappings": "AAUA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAEb,IAAIC,KAAI;AA+CR,IAAMC,MAAM,GAAG;EAIbC,KAAK,WAALA,KAAKA,CAACC,CAAS,EAAU;IACvB,OAAOA,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;EACtB,CAAC;EAKDC,KAAK,WAALA,KAAKA,CAACD,CAAS,EAAU;IACvB,OAAOA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;EACvB,CAAC;EAQDE,MAAM,WAANA,MAAMA,CAACC,CAAS,EAAU;IACxB,OAAOA,CAAC;EACV,CAAC;EAQDN,IAAI,WAAJA,IAAIA,CAACM,CAAS,EAAU;IACtB,IAAI,CAACN,KAAI,EAAE;MACTA,KAAI,GAAGC,MAAM,CAACM,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrC;IACA,OAAOP,KAAI,CAACM,CAAC,CAAC;EAChB,CAAC;EAQDE,IAAI,WAAJA,IAAIA,CAACF,CAAS,EAAU;IACtB,OAAOA,CAAC,GAAGA,CAAC;EACd,CAAC;EAQDG,KAAK,WAALA,KAAKA,CAACH,CAAS,EAAU;IACvB,OAAOA,CAAC,GAAGA,CAAC,GAAGA,CAAC;EAClB,CAAC;EAQDI,IAAI,WAAJA,IAAIA,CAACP,CAAS,EAAyB;IACrC,OAAO,UAACG,CAAS;MAAA,OAAKK,IAAI,CAACC,GAAG,CAACN,CAAC,EAAEH,CAAC,CAAC;IAAA;EACtC,CAAC;EAODU,GAAG,WAAHA,GAAGA,CAACP,CAAS,EAAU;IACrB,OAAO,CAAC,GAAGK,IAAI,CAACG,GAAG,CAAER,CAAC,GAAGK,IAAI,CAACI,EAAE,GAAI,CAAC,CAAC;EACxC,CAAC;EAODC,MAAM,WAANA,MAAMA,CAACV,CAAS,EAAU;IACxB,OAAO,CAAC,GAAGK,IAAI,CAACM,IAAI,CAAC,CAAC,GAAGX,CAAC,GAAGA,CAAC,CAAC;EACjC,CAAC;EAODY,GAAG,WAAHA,GAAGA,CAACZ,CAAS,EAAU;IACrB,OAAOK,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,IAAIN,CAAC,GAAG,CAAC,CAAC,CAAC;EAClC,CAAC;EAYDa,OAAO,WAAPA,OAAOA,CAAA,EAAgD;IAAA,IAA/CC,UAAkB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;IAC5B,IAAMG,CAAC,GAAGJ,UAAU,GAAGT,IAAI,CAACI,EAAE;IAC9B,OAAO,UAAAT,CAAC;MAAA,OAAI,CAAC,GAAGK,IAAI,CAACC,GAAG,CAACD,IAAI,CAACG,GAAG,CAAER,CAAC,GAAGK,IAAI,CAACI,EAAE,GAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGJ,IAAI,CAACG,GAAG,CAACR,CAAC,GAAGkB,CAAC,CAAC;IAAA;EAC5E,CAAC;EAQDC,IAAI,WAAJA,IAAIA,CAAA,EAA6C;IAAA,IAA5CC,CAAS,GAAAL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,OAAO;IACtB,OAAO,UAAAf,CAAC;MAAA,OAAIA,CAAC,GAAGA,CAAC,IAAI,CAACoB,CAAC,GAAG,CAAC,IAAIpB,CAAC,GAAGoB,CAAC,CAAC;IAAA;EACvC,CAAC;EAODC,MAAM,WAANA,MAAMA,CAACrB,CAAS,EAAU;IACxB,IAAIA,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE;MAChB,OAAO,MAAM,GAAGA,CAAC,GAAGA,CAAC;IACvB;IAEA,IAAIA,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE;MAChB,IAAMsB,EAAE,GAAGtB,CAAC,GAAG,GAAG,GAAG,IAAI;MACzB,OAAO,MAAM,GAAGsB,EAAE,GAAGA,EAAE,GAAG,IAAI;IAChC;IAEA,IAAItB,CAAC,GAAG,GAAG,GAAG,IAAI,EAAE;MAClB,IAAMsB,GAAE,GAAGtB,CAAC,GAAG,IAAI,GAAG,IAAI;MAC1B,OAAO,MAAM,GAAGsB,GAAE,GAAGA,GAAE,GAAG,MAAM;IAClC;IAEA,IAAMA,EAAE,GAAGtB,CAAC,GAAG,KAAK,GAAG,IAAI;IAC3B,OAAO,MAAM,GAAGsB,EAAE,GAAGA,EAAE,GAAG,QAAQ;EACpC,CAAC;EASDrB,MAAM,WAANA,MAAMA,CACJsB,EAAU,EACVC,EAAU,EACVC,EAAU,EACVC,EAAU,EACa;IACvB,IAAMC,OAAO,GAAGC,OAAO,WAAW,CAAC,CAACnC,OAAO;IAC3C,OAAOkC,OAAO,CAACJ,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EAChC,CAAC;EAKDG,EAAE,WAAFA,GAAEA,CAACC,MAA6B,EAAyB;IACvD,OAAOA,MAAM;EACf,CAAC;EAKDC,GAAG,WAAHA,GAAGA,CAACD,MAA6B,EAAyB;IACxD,OAAO,UAAA9B,CAAC;MAAA,OAAI,CAAC,GAAG8B,MAAM,CAAC,CAAC,GAAG9B,CAAC,CAAC;IAAA;EAC/B,CAAC;EAODgC,KAAK,WAALA,KAAKA,CAACF,MAA6B,EAAyB;IAC1D,OAAO,UAAA9B,CAAC,EAAI;MACV,IAAIA,CAAC,GAAG,GAAG,EAAE;QACX,OAAO8B,MAAM,CAAC9B,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MAC1B;MACA,OAAO,CAAC,GAAG8B,MAAM,CAAC,CAAC,CAAC,GAAG9B,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;IACpC,CAAC;EACH;AACF,CAAC;AAAC,IAAAiC,QAAA,GAAA1C,OAAA,CAAAE,OAAA,GAEaE,MAAM", "ignoreList": []}