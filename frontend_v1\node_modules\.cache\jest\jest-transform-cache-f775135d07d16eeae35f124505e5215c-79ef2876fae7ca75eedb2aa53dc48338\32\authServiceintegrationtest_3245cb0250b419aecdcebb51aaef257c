bd59704962778186c52edf386ef6e19a
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _authService = require("../authService");
var BACKEND_AVAILABLE = process.env.TEST_BACKEND === 'true';
describe('AuthService Integration Tests', function () {
  beforeAll(function () {
    if (!BACKEND_AVAILABLE) {}
  });
  describe('login integration', function () {
    it.skip('should successfully login with test credentials', (0, _asyncToGenerator2.default)(function* () {
      if (!BACKEND_AVAILABLE) return;
      try {
        var result = yield _authService.authService.login({
          email: '<EMAIL>',
          password: 'Testpass123!'
        });
        expect(result).toBeDefined();
        expect(result.access).toBeDefined();
        expect(result.user).toBeDefined();
        expect(result.user.email).toBe('<EMAIL>');
        expect(result.user.role).toBe('customer');
      } catch (error) {
        console.error('Integration test failed:', error);
        throw error;
      }
    }));
    it.skip('should fail with invalid credentials', (0, _asyncToGenerator2.default)(function* () {
      if (!BACKEND_AVAILABLE) return;
      yield expect(_authService.authService.login({
        email: '<EMAIL>',
        password: 'wrongpassword'
      })).rejects.toThrow();
    }));
  });
  describe('register integration', function () {
    it.skip('should handle registration attempt', (0, _asyncToGenerator2.default)(function* () {
      if (!BACKEND_AVAILABLE) return;
      try {
        var result = yield _authService.authService.register({
          first_name: 'Test',
          last_name: 'User',
          email: '<EMAIL>',
          password: 'Testpass123!',
          role: 'customer'
        });
        expect(result).toBeDefined();
        expect(result.user).toBeDefined();
      } catch (error) {}
    }));
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************