{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_classCallCheck2", "_createClass2", "Dimensions", "PixelRatio", "key", "get", "scale", "getFontScale", "fontScale", "getPixelSizeForLayoutSize", "layoutSize", "Math", "round", "roundToNearestPixel", "ratio", "startDetecting", "_default"], "sources": ["PixelRatio.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\n'use strict';\n\nconst Dimensions = require('./Dimensions').default;\n\n/**\n * PixelRatio class gives access to the device pixel density.\n *\n * ## Fetching a correctly sized image\n *\n * You should get a higher resolution image if you are on a high pixel density\n * device. A good rule of thumb is to multiply the size of the image you display\n * by the pixel ratio.\n *\n * ```\n * var image = getImage({\n *   width: PixelRatio.getPixelSizeForLayoutSize(200),\n *   height: PixelRatio.getPixelSizeForLayoutSize(100),\n * });\n * <Image source={image} style={{width: 200, height: 100}} />\n * ```\n *\n * ## Pixel grid snapping\n *\n * In iOS, you can specify positions and dimensions for elements with arbitrary\n * precision, for example 29.674825. But, ultimately the physical display only\n * have a fixed number of pixels, for example 640×960 for iPhone 4 or 750×1334\n * for iPhone 6. iOS tries to be as faithful as possible to the user value by\n * spreading one original pixel into multiple ones to trick the eye. The\n * downside of this technique is that it makes the resulting element look\n * blurry.\n *\n * In practice, we found out that developers do not want this feature and they\n * have to work around it by doing manual rounding in order to avoid having\n * blurry elements. In React Native, we are rounding all the pixels\n * automatically.\n *\n * We have to be careful when to do this rounding. You never want to work with\n * rounded and unrounded values at the same time as you're going to accumulate\n * rounding errors. Having even one rounding error is deadly because a one\n * pixel border may vanish or be twice as big.\n *\n * In React Native, everything in JavaScript and within the layout engine works\n * with arbitrary precision numbers. It's only when we set the position and\n * dimensions of the native element on the main thread that we round. Also,\n * rounding is done relative to the root rather than the parent, again to avoid\n * accumulating rounding errors.\n *\n */\nclass PixelRatio {\n  /**\n   * Returns the device pixel density. Some examples:\n   *\n   *   - PixelRatio.get() === 1\n   *     - mdpi Android devices (160 dpi)\n   *   - PixelRatio.get() === 1.5\n   *     - hdpi Android devices (240 dpi)\n   *   - PixelRatio.get() === 2\n   *     - iPhone 4, 4S\n   *     - iPhone 5, 5c, 5s\n   *     - iPhone 6\n   *     - iPhone 7\n   *     - iPhone 8\n   *     - iPhone SE\n   *     - xhdpi Android devices (320 dpi)\n   *   - PixelRatio.get() === 3\n   *     - iPhone 6 Plus\n   *     - iPhone 7 Plus\n   *     - iPhone 8 Plus\n   *     - iPhone X\n   *     - xxhdpi Android devices (480 dpi)\n   *   - PixelRatio.get() === 3.5\n   *     - Nexus 6\n   */\n  static get(): number {\n    return Dimensions.get('window').scale;\n  }\n\n  /**\n   * Returns the scaling factor for font sizes. This is the ratio that is used to calculate the\n   * absolute font size, so any elements that heavily depend on that should use this to do\n   * calculations.\n   *\n   * If a font scale is not set, this returns the device pixel ratio.\n   *\n   * This reflects the user preference set in:\n   *  - Settings > Display > Font size on Android,\n   *  - Settings > Display & Brightness > Text Size on iOS.\n   */\n  static getFontScale(): number {\n    return Dimensions.get('window').fontScale || PixelRatio.get();\n  }\n\n  /**\n   * Converts a layout size (dp) to pixel size (px).\n   *\n   * Guaranteed to return an integer number.\n   */\n  static getPixelSizeForLayoutSize(layoutSize: number): number {\n    return Math.round(layoutSize * PixelRatio.get());\n  }\n\n  /**\n   * Rounds a layout size (dp) to the nearest layout size that corresponds to\n   * an integer number of pixels. For example, on a device with a PixelRatio\n   * of 3, `PixelRatio.roundToNearestPixel(8.4) = 8.33`, which corresponds to\n   * exactly (8.33 * 3) = 25 pixels.\n   */\n  static roundToNearestPixel(layoutSize: number): number {\n    const ratio = PixelRatio.get();\n    return Math.round(layoutSize * ratio) / ratio;\n  }\n\n  // No-op for iOS, but used on the web. Should not be documented.\n  static startDetecting() {}\n}\n\nexport default PixelRatio;\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AAEb,IAAMQ,UAAU,GAAGR,OAAO,eAAe,CAAC,CAACK,OAAO;AAAC,IA8C7CI,UAAU;EAAA,SAAAA,WAAA;IAAA,IAAAH,gBAAA,CAAAD,OAAA,QAAAI,UAAA;EAAA;EAAA,WAAAF,aAAA,CAAAF,OAAA,EAAAI,UAAA;IAAAC,GAAA;IAAAN,KAAA,EAyBd,SAAOO,GAAGA,CAAA,EAAW;MACnB,OAAOH,UAAU,CAACG,GAAG,CAAC,QAAQ,CAAC,CAACC,KAAK;IACvC;EAAC;IAAAF,GAAA;IAAAN,KAAA,EAaD,SAAOS,YAAYA,CAAA,EAAW;MAC5B,OAAOL,UAAU,CAACG,GAAG,CAAC,QAAQ,CAAC,CAACG,SAAS,IAAIL,UAAU,CAACE,GAAG,CAAC,CAAC;IAC/D;EAAC;IAAAD,GAAA;IAAAN,KAAA,EAOD,SAAOW,yBAAyBA,CAACC,UAAkB,EAAU;MAC3D,OAAOC,IAAI,CAACC,KAAK,CAACF,UAAU,GAAGP,UAAU,CAACE,GAAG,CAAC,CAAC,CAAC;IAClD;EAAC;IAAAD,GAAA;IAAAN,KAAA,EAQD,SAAOe,mBAAmBA,CAACH,UAAkB,EAAU;MACrD,IAAMI,KAAK,GAAGX,UAAU,CAACE,GAAG,CAAC,CAAC;MAC9B,OAAOM,IAAI,CAACC,KAAK,CAACF,UAAU,GAAGI,KAAK,CAAC,GAAGA,KAAK;IAC/C;EAAC;IAAAV,GAAA;IAAAN,KAAA,EAGD,SAAOiB,cAAcA,CAAA,EAAG,CAAC;EAAC;AAAA;AAAA,IAAAC,QAAA,GAAAnB,OAAA,CAAAE,OAAA,GAGbI,UAAU", "ignoreList": []}