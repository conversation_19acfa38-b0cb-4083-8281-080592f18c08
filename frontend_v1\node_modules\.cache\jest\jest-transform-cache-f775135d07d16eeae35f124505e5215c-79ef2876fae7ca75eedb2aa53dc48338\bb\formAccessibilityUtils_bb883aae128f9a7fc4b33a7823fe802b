e81969ec3f17c4948a15299523800118
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.validateFormAccessibility = exports.getAccessibilityValue = exports.generateLabelProps = exports.generateHelperTextProps = exports.generateFormFieldIds = exports.generateFormFieldAccessibilityProps = exports.generateErrorMessageProps = exports.generateAccessibilityLabel = exports.generateAccessibilityHint = exports.default = exports.FORM_ACCESSIBILITY_CONFIG = void 0;
var _reactNative = require("react-native");
var FORM_ACCESSIBILITY_CONFIG = exports.FORM_ACCESSIBILITY_CONFIG = {
  LABEL_ASSOCIATION: {
    USE_ARIA_LABELLEDBY: true,
    USE_ARIA_DESCRIBEDBY: true,
    INCLUDE_REQUIRED_INDICATOR: true
  },
  ERROR_MESSAGING: {
    ANNOUNCE_ERRORS: true,
    ERROR_ROLE: 'alert',
    LIVE_REGION: 'polite'
  },
  FIELD_VALIDATION: {
    ANNOUNCE_SUCCESS: false,
    ANNOUNCE_ERRORS: true,
    DEBOUNCE_DELAY: 300
  }
};
var generateFormFieldIds = exports.generateFormFieldIds = function generateFormFieldIds(baseId) {
  var timestamp = Date.now();
  var random = Math.random().toString(36).substr(2, 5);
  var uniqueId = `${baseId}_${timestamp}_${random}`;
  return {
    fieldId: uniqueId,
    labelId: `${uniqueId}_label`,
    errorId: `${uniqueId}_error`,
    helperId: `${uniqueId}_helper`,
    descriptionId: `${uniqueId}_description`
  };
};
var generateFormFieldAccessibilityProps = exports.generateFormFieldAccessibilityProps = function generateFormFieldAccessibilityProps(props) {
  var id = props.id,
    label = props.label,
    helperText = props.helperText,
    errorMessage = props.errorMessage,
    _props$required = props.required,
    required = _props$required === void 0 ? false : _props$required,
    _props$disabled = props.disabled,
    disabled = _props$disabled === void 0 ? false : _props$disabled,
    _props$fieldType = props.fieldType,
    fieldType = _props$fieldType === void 0 ? 'text' : _props$fieldType,
    value = props.value,
    ariaLabel = props.ariaLabel,
    ariaDescribedBy = props.ariaDescribedBy,
    ariaLabelledBy = props.ariaLabelledBy;
  var ids = id ? generateFormFieldIds(id) : generateFormFieldIds('field');
  var describedByIds = [];
  if (errorMessage) describedByIds.push(ids.errorId);
  if (helperText) describedByIds.push(ids.helperId);
  if (ariaDescribedBy) describedByIds.push(ariaDescribedBy);
  var labelledByIds = [];
  if (label) labelledByIds.push(ids.labelId);
  if (ariaLabelledBy) labelledByIds.push(ariaLabelledBy);
  var accessibilityLabel = generateAccessibilityLabel({
    label: label,
    required: required,
    fieldType: fieldType,
    errorMessage: errorMessage,
    customLabel: ariaLabel
  });
  var accessibilityHint = generateAccessibilityHint({
    helperText: helperText,
    fieldType: fieldType,
    errorMessage: errorMessage,
    required: required
  });
  var baseProps = Object.assign({
    accessibilityLabel: accessibilityLabel,
    accessibilityHint: accessibilityHint,
    accessibilityState: Object.assign({
      disabled: disabled,
      invalid: !!errorMessage,
      required: required
    }, fieldType === 'checkbox' || fieldType === 'radio' ? {
      checked: !!value
    } : {}),
    accessibilityValue: getAccessibilityValue(fieldType, value)
  }, _reactNative.Platform.OS === 'web' && {
    'aria-label': accessibilityLabel,
    'aria-describedby': describedByIds.length > 0 ? describedByIds.join(' ') : undefined,
    'aria-labelledby': labelledByIds.length > 0 ? labelledByIds.join(' ') : undefined,
    'aria-required': required,
    'aria-invalid': !!errorMessage
  });
  return Object.assign({}, baseProps, {
    ids: ids
  });
};
var generateAccessibilityLabel = exports.generateAccessibilityLabel = function generateAccessibilityLabel(options) {
  var label = options.label,
    required = options.required,
    fieldType = options.fieldType,
    errorMessage = options.errorMessage,
    customLabel = options.customLabel;
  if (customLabel) return customLabel;
  var accessibilityLabel = label || '';
  if (fieldType && label && !label.toLowerCase().includes(fieldType)) {
    var typeLabels = {
      email: 'email address',
      password: 'password',
      phone: 'phone number',
      search: 'search',
      url: 'website URL',
      number: 'number',
      date: 'date',
      time: 'time'
    };
    var typeLabel = typeLabels[fieldType];
    if (typeLabel && !label.toLowerCase().includes(typeLabel)) {
      accessibilityLabel += ` ${typeLabel}`;
    }
  }
  if (required) {
    accessibilityLabel += ', required';
  }
  if (errorMessage) {
    accessibilityLabel += ', has error';
  }
  return accessibilityLabel.trim();
};
var generateAccessibilityHint = exports.generateAccessibilityHint = function generateAccessibilityHint(options) {
  var helperText = options.helperText,
    fieldType = options.fieldType,
    errorMessage = options.errorMessage,
    required = options.required;
  var hints = [];
  if (helperText && !errorMessage) {
    hints.push(helperText);
  }
  var fieldHints = {
    password: 'Double tap to toggle password visibility',
    select: 'Double tap to open options',
    checkbox: 'Double tap to toggle selection',
    radio: 'Double tap to select option',
    date: 'Double tap to open date picker',
    time: 'Double tap to open time picker'
  };
  if (fieldType && fieldHints[fieldType]) {
    hints.push(fieldHints[fieldType]);
  }
  if (errorMessage) {
    hints.push(`Error: ${errorMessage}`);
  }
  return hints.join('. ');
};
var getAccessibilityValue = exports.getAccessibilityValue = function getAccessibilityValue(fieldType, value) {
  if (value === undefined || value === null) return undefined;
  switch (fieldType) {
    case 'checkbox':
    case 'radio':
      return undefined;
    case 'number':
      return {
        now: Number(value)
      };
    case 'text':
    case 'email':
    case 'password':
    case 'phone':
    case 'name':
    case 'search':
    case 'url':
    case 'textarea':
    default:
      return {
        text: String(value)
      };
  }
};
var generateErrorMessageProps = exports.generateErrorMessageProps = function generateErrorMessageProps(errorMessage, errorId) {
  return Object.assign({
    accessibilityRole: 'alert',
    accessibilityLiveRegion: 'polite',
    accessibilityLabel: `Error: ${errorMessage}`
  }, _reactNative.Platform.OS === 'web' && {
    role: 'alert',
    'aria-live': 'polite',
    id: errorId
  });
};
var generateHelperTextProps = exports.generateHelperTextProps = function generateHelperTextProps(helperText, helperId) {
  return Object.assign({
    accessibilityLabel: helperText
  }, _reactNative.Platform.OS === 'web' && {
    id: helperId
  });
};
var generateLabelProps = exports.generateLabelProps = function generateLabelProps(label, labelId) {
  var required = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
  return Object.assign({
    accessibilityLabel: required ? `${label}, required` : label
  }, _reactNative.Platform.OS === 'web' && {
    id: labelId,
    htmlFor: labelId.replace('_label', '')
  });
};
var validateFormAccessibility = exports.validateFormAccessibility = function validateFormAccessibility(formFields) {
  var errors = [];
  var warnings = [];
  formFields.forEach(function (field, index) {
    var _field$label, _field$ariaLabel;
    var fieldName = field.label || `Field ${index + 1}`;
    if (!field.label && !field.ariaLabel && !field.placeholder) {
      errors.push(`${fieldName}: Missing accessible label`);
    }
    if (field.required && !((_field$label = field.label) != null && _field$label.includes('*')) && !((_field$ariaLabel = field.ariaLabel) != null && _field$ariaLabel.includes('required'))) {
      warnings.push(`${fieldName}: Required field should have clear indicator`);
    }
    if (field.errorMessage && !field.ariaDescribedBy) {
      warnings.push(`${fieldName}: Error message should be programmatically associated`);
    }
    if (field.helperText && !field.ariaDescribedBy) {
      warnings.push(`${fieldName}: Helper text should be programmatically associated`);
    }
  });
  return {
    isValid: errors.length === 0,
    errors: errors,
    warnings: warnings
  };
};
var _default = exports.default = {
  FORM_ACCESSIBILITY_CONFIG: FORM_ACCESSIBILITY_CONFIG,
  generateFormFieldIds: generateFormFieldIds,
  generateFormFieldAccessibilityProps: generateFormFieldAccessibilityProps,
  generateAccessibilityLabel: generateAccessibilityLabel,
  generateAccessibilityHint: generateAccessibilityHint,
  getAccessibilityValue: getAccessibilityValue,
  generateErrorMessageProps: generateErrorMessageProps,
  generateHelperTextProps: generateHelperTextProps,
  generateLabelProps: generateLabelProps,
  validateFormAccessibility: validateFormAccessibility
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfcmVhY3ROYXRpdmUiLCJyZXF1aXJlIiwiRk9STV9BQ0NFU1NJQklMSVRZX0NPTkZJRyIsImV4cG9ydHMiLCJMQUJFTF9BU1NPQ0lBVElPTiIsIlVTRV9BUklBX0xBQkVMTEVEQlkiLCJVU0VfQVJJQV9ERVNDUklCRURCWSIsIklOQ0xVREVfUkVRVUlSRURfSU5ESUNBVE9SIiwiRVJST1JfTUVTU0FHSU5HIiwiQU5OT1VOQ0VfRVJST1JTIiwiRVJST1JfUk9MRSIsIkxJVkVfUkVHSU9OIiwiRklFTERfVkFMSURBVElPTiIsIkFOTk9VTkNFX1NVQ0NFU1MiLCJERUJPVU5DRV9ERUxBWSIsImdlbmVyYXRlRm9ybUZpZWxkSWRzIiwiYmFzZUlkIiwidGltZXN0YW1wIiwiRGF0ZSIsIm5vdyIsInJhbmRvbSIsIk1hdGgiLCJ0b1N0cmluZyIsInN1YnN0ciIsInVuaXF1ZUlkIiwiZmllbGRJZCIsImxhYmVsSWQiLCJlcnJvcklkIiwiaGVscGVySWQiLCJkZXNjcmlwdGlvbklkIiwiZ2VuZXJhdGVGb3JtRmllbGRBY2Nlc3NpYmlsaXR5UHJvcHMiLCJwcm9wcyIsImlkIiwibGFiZWwiLCJoZWxwZXJUZXh0IiwiZXJyb3JNZXNzYWdlIiwiX3Byb3BzJHJlcXVpcmVkIiwicmVxdWlyZWQiLCJfcHJvcHMkZGlzYWJsZWQiLCJkaXNhYmxlZCIsIl9wcm9wcyRmaWVsZFR5cGUiLCJmaWVsZFR5cGUiLCJ2YWx1ZSIsImFyaWFMYWJlbCIsImFyaWFEZXNjcmliZWRCeSIsImFyaWFMYWJlbGxlZEJ5IiwiaWRzIiwiZGVzY3JpYmVkQnlJZHMiLCJwdXNoIiwibGFiZWxsZWRCeUlkcyIsImFjY2Vzc2liaWxpdHlMYWJlbCIsImdlbmVyYXRlQWNjZXNzaWJpbGl0eUxhYmVsIiwiY3VzdG9tTGFiZWwiLCJhY2Nlc3NpYmlsaXR5SGludCIsImdlbmVyYXRlQWNjZXNzaWJpbGl0eUhpbnQiLCJiYXNlUHJvcHMiLCJPYmplY3QiLCJhc3NpZ24iLCJhY2Nlc3NpYmlsaXR5U3RhdGUiLCJpbnZhbGlkIiwiY2hlY2tlZCIsImFjY2Vzc2liaWxpdHlWYWx1ZSIsImdldEFjY2Vzc2liaWxpdHlWYWx1ZSIsIlBsYXRmb3JtIiwiT1MiLCJsZW5ndGgiLCJqb2luIiwidW5kZWZpbmVkIiwib3B0aW9ucyIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJ0eXBlTGFiZWxzIiwiZW1haWwiLCJwYXNzd29yZCIsInBob25lIiwic2VhcmNoIiwidXJsIiwibnVtYmVyIiwiZGF0ZSIsInRpbWUiLCJ0eXBlTGFiZWwiLCJ0cmltIiwiaGludHMiLCJmaWVsZEhpbnRzIiwic2VsZWN0IiwiY2hlY2tib3giLCJyYWRpbyIsIk51bWJlciIsInRleHQiLCJTdHJpbmciLCJnZW5lcmF0ZUVycm9yTWVzc2FnZVByb3BzIiwiYWNjZXNzaWJpbGl0eVJvbGUiLCJhY2Nlc3NpYmlsaXR5TGl2ZVJlZ2lvbiIsInJvbGUiLCJnZW5lcmF0ZUhlbHBlclRleHRQcm9wcyIsImdlbmVyYXRlTGFiZWxQcm9wcyIsImFyZ3VtZW50cyIsImh0bWxGb3IiLCJyZXBsYWNlIiwidmFsaWRhdGVGb3JtQWNjZXNzaWJpbGl0eSIsImZvcm1GaWVsZHMiLCJlcnJvcnMiLCJ3YXJuaW5ncyIsImZvckVhY2giLCJmaWVsZCIsImluZGV4IiwiX2ZpZWxkJGxhYmVsIiwiX2ZpZWxkJGFyaWFMYWJlbCIsImZpZWxkTmFtZSIsInBsYWNlaG9sZGVyIiwiaXNWYWxpZCIsIl9kZWZhdWx0IiwiZGVmYXVsdCJdLCJzb3VyY2VzIjpbImZvcm1BY2Nlc3NpYmlsaXR5VXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBGb3JtIEFjY2Vzc2liaWxpdHkgVXRpbGl0aWVzXG4gKlxuICogSW1wbGVtZW50cyBSRUMtRk9STS0wMDM6IEFkZCBwcm9wZXIgbGFiZWxzLCBBUklBIGF0dHJpYnV0ZXMsIGFuZCBlcnJvciBtZXNzYWdpbmcuXG4gKiBQcm92aWRlcyBjb21wcmVoZW5zaXZlIGZvcm0gYWNjZXNzaWJpbGl0eSB1dGlsaXRpZXMgZm9yIFdDQUcgMi4xIEFBIGNvbXBsaWFuY2UuXG4gKlxuICogRmVhdHVyZXM6XG4gKiAtIFByb2dyYW1tYXRpYyBsYWJlbCBhc3NvY2lhdGlvbnNcbiAqIC0gRW5oYW5jZWQgQVJJQSBhdHRyaWJ1dGVzXG4gKiAtIEVycm9yIG1lc3NhZ2UgYWNjZXNzaWJpbGl0eVxuICogLSBSZXF1aXJlZCBmaWVsZCBpbmRpY2F0b3JzXG4gKiAtIFNjcmVlbiByZWFkZXIgb3B0aW1pemF0aW9uc1xuICpcbiAqIEB2ZXJzaW9uIDEuMC4wXG4gKiBAYXV0aG9yIFZpZXJsYSBEZXZlbG9wbWVudCBUZWFtXG4gKi9cblxuaW1wb3J0IHsgUGxhdGZvcm0gfSBmcm9tICdyZWFjdC1uYXRpdmUnO1xuXG4vLyBGb3JtIGFjY2Vzc2liaWxpdHkgY29uZmlndXJhdGlvblxuZXhwb3J0IGNvbnN0IEZPUk1fQUNDRVNTSUJJTElUWV9DT05GSUcgPSB7XG4gIExBQkVMX0FTU09DSUFUSU9OOiB7XG4gICAgVVNFX0FSSUFfTEFCRUxMRURCWTogdHJ1ZSxcbiAgICBVU0VfQVJJQV9ERVNDUklCRURCWTogdHJ1ZSxcbiAgICBJTkNMVURFX1JFUVVJUkVEX0lORElDQVRPUjogdHJ1ZSxcbiAgfSxcbiAgRVJST1JfTUVTU0FHSU5HOiB7XG4gICAgQU5OT1VOQ0VfRVJST1JTOiB0cnVlLFxuICAgIEVSUk9SX1JPTEU6ICdhbGVydCcsXG4gICAgTElWRV9SRUdJT046ICdwb2xpdGUnLFxuICB9LFxuICBGSUVMRF9WQUxJREFUSU9OOiB7XG4gICAgQU5OT1VOQ0VfU1VDQ0VTUzogZmFsc2UsIC8vIERvbid0IGFubm91bmNlIHN1Y2Nlc3MgdG8gYXZvaWQgbm9pc2VcbiAgICBBTk5PVU5DRV9FUlJPUlM6IHRydWUsXG4gICAgREVCT1VOQ0VfREVMQVk6IDMwMCxcbiAgfSxcbn0gYXMgY29uc3Q7XG5cbi8vIEZvcm0gZmllbGQgdHlwZXMgZm9yIGVuaGFuY2VkIGFjY2Vzc2liaWxpdHlcbmV4cG9ydCB0eXBlIEZvcm1GaWVsZFR5cGUgPVxuICB8ICd0ZXh0J1xuICB8ICdlbWFpbCdcbiAgfCAncGFzc3dvcmQnXG4gIHwgJ3Bob25lJ1xuICB8ICduYW1lJ1xuICB8ICdzZWFyY2gnXG4gIHwgJ3VybCdcbiAgfCAnbnVtYmVyJ1xuICB8ICd0ZXh0YXJlYSdcbiAgfCAnc2VsZWN0J1xuICB8ICdjaGVja2JveCdcbiAgfCAncmFkaW8nXG4gIHwgJ2RhdGUnXG4gIHwgJ3RpbWUnO1xuXG4vLyBFbmhhbmNlZCBmb3JtIGZpZWxkIHByb3BzIGludGVyZmFjZVxuZXhwb3J0IGludGVyZmFjZSBFbmhhbmNlZEZvcm1GaWVsZFByb3BzIHtcbiAgaWQ/OiBzdHJpbmc7XG4gIGxhYmVsPzogc3RyaW5nO1xuICBwbGFjZWhvbGRlcj86IHN0cmluZztcbiAgaGVscGVyVGV4dD86IHN0cmluZztcbiAgZXJyb3JNZXNzYWdlPzogc3RyaW5nO1xuICByZXF1aXJlZD86IGJvb2xlYW47XG4gIGRpc2FibGVkPzogYm9vbGVhbjtcbiAgZmllbGRUeXBlPzogRm9ybUZpZWxkVHlwZTtcbiAgdmFsdWU/OiBzdHJpbmcgfCBib29sZWFuIHwgbnVtYmVyO1xuICBhcmlhTGFiZWw/OiBzdHJpbmc7XG4gIGFyaWFEZXNjcmliZWRCeT86IHN0cmluZztcbiAgYXJpYUxhYmVsbGVkQnk/OiBzdHJpbmc7XG59XG5cbi8qKlxuICogR2VuZXJhdGUgdW5pcXVlIElEcyBmb3IgZm9ybSBmaWVsZCBhc3NvY2lhdGlvbnNcbiAqL1xuZXhwb3J0IGNvbnN0IGdlbmVyYXRlRm9ybUZpZWxkSWRzID0gKGJhc2VJZDogc3RyaW5nKSA9PiB7XG4gIGNvbnN0IHRpbWVzdGFtcCA9IERhdGUubm93KCk7XG4gIGNvbnN0IHJhbmRvbSA9IE1hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA1KTtcbiAgY29uc3QgdW5pcXVlSWQgPSBgJHtiYXNlSWR9XyR7dGltZXN0YW1wfV8ke3JhbmRvbX1gO1xuXG4gIHJldHVybiB7XG4gICAgZmllbGRJZDogdW5pcXVlSWQsXG4gICAgbGFiZWxJZDogYCR7dW5pcXVlSWR9X2xhYmVsYCxcbiAgICBlcnJvcklkOiBgJHt1bmlxdWVJZH1fZXJyb3JgLFxuICAgIGhlbHBlcklkOiBgJHt1bmlxdWVJZH1faGVscGVyYCxcbiAgICBkZXNjcmlwdGlvbklkOiBgJHt1bmlxdWVJZH1fZGVzY3JpcHRpb25gLFxuICB9O1xufTtcblxuLyoqXG4gKiBHZW5lcmF0ZSBjb21wcmVoZW5zaXZlIGFjY2Vzc2liaWxpdHkgcHJvcHMgZm9yIGZvcm0gZmllbGRzXG4gKi9cbmV4cG9ydCBjb25zdCBnZW5lcmF0ZUZvcm1GaWVsZEFjY2Vzc2liaWxpdHlQcm9wcyA9IChcbiAgcHJvcHM6IEVuaGFuY2VkRm9ybUZpZWxkUHJvcHMsXG4pOiBhbnkgPT4ge1xuICBjb25zdCB7XG4gICAgaWQsXG4gICAgbGFiZWwsXG4gICAgaGVscGVyVGV4dCxcbiAgICBlcnJvck1lc3NhZ2UsXG4gICAgcmVxdWlyZWQgPSBmYWxzZSxcbiAgICBkaXNhYmxlZCA9IGZhbHNlLFxuICAgIGZpZWxkVHlwZSA9ICd0ZXh0JyxcbiAgICB2YWx1ZSxcbiAgICBhcmlhTGFiZWwsXG4gICAgYXJpYURlc2NyaWJlZEJ5LFxuICAgIGFyaWFMYWJlbGxlZEJ5LFxuICB9ID0gcHJvcHM7XG5cbiAgLy8gR2VuZXJhdGUgSURzIGZvciBhc3NvY2lhdGlvbnNcbiAgY29uc3QgaWRzID0gaWQgPyBnZW5lcmF0ZUZvcm1GaWVsZElkcyhpZCkgOiBnZW5lcmF0ZUZvcm1GaWVsZElkcygnZmllbGQnKTtcblxuICAvLyBCdWlsZCBkZXNjcmliZWRCeSBhcnJheVxuICBjb25zdCBkZXNjcmliZWRCeUlkczogc3RyaW5nW10gPSBbXTtcbiAgaWYgKGVycm9yTWVzc2FnZSkgZGVzY3JpYmVkQnlJZHMucHVzaChpZHMuZXJyb3JJZCk7XG4gIGlmIChoZWxwZXJUZXh0KSBkZXNjcmliZWRCeUlkcy5wdXNoKGlkcy5oZWxwZXJJZCk7XG4gIGlmIChhcmlhRGVzY3JpYmVkQnkpIGRlc2NyaWJlZEJ5SWRzLnB1c2goYXJpYURlc2NyaWJlZEJ5KTtcblxuICAvLyBCdWlsZCBsYWJlbGxlZEJ5IGFycmF5XG4gIGNvbnN0IGxhYmVsbGVkQnlJZHM6IHN0cmluZ1tdID0gW107XG4gIGlmIChsYWJlbCkgbGFiZWxsZWRCeUlkcy5wdXNoKGlkcy5sYWJlbElkKTtcbiAgaWYgKGFyaWFMYWJlbGxlZEJ5KSBsYWJlbGxlZEJ5SWRzLnB1c2goYXJpYUxhYmVsbGVkQnkpO1xuXG4gIC8vIEdlbmVyYXRlIGFjY2Vzc2liaWxpdHkgbGFiZWxcbiAgY29uc3QgYWNjZXNzaWJpbGl0eUxhYmVsID0gZ2VuZXJhdGVBY2Nlc3NpYmlsaXR5TGFiZWwoe1xuICAgIGxhYmVsLFxuICAgIHJlcXVpcmVkLFxuICAgIGZpZWxkVHlwZSxcbiAgICBlcnJvck1lc3NhZ2UsXG4gICAgY3VzdG9tTGFiZWw6IGFyaWFMYWJlbCxcbiAgfSk7XG5cbiAgLy8gR2VuZXJhdGUgYWNjZXNzaWJpbGl0eSBoaW50XG4gIGNvbnN0IGFjY2Vzc2liaWxpdHlIaW50ID0gZ2VuZXJhdGVBY2Nlc3NpYmlsaXR5SGludCh7XG4gICAgaGVscGVyVGV4dCxcbiAgICBmaWVsZFR5cGUsXG4gICAgZXJyb3JNZXNzYWdlLFxuICAgIHJlcXVpcmVkLFxuICB9KTtcblxuICAvLyBCYXNlIGFjY2Vzc2liaWxpdHkgcHJvcHNcbiAgY29uc3QgYmFzZVByb3BzID0ge1xuICAgIGFjY2Vzc2liaWxpdHlMYWJlbCxcbiAgICBhY2Nlc3NpYmlsaXR5SGludCxcbiAgICBhY2Nlc3NpYmlsaXR5U3RhdGU6IHtcbiAgICAgIGRpc2FibGVkLFxuICAgICAgaW52YWxpZDogISFlcnJvck1lc3NhZ2UsXG4gICAgICByZXF1aXJlZCxcbiAgICAgIC4uLihmaWVsZFR5cGUgPT09ICdjaGVja2JveCcgfHwgZmllbGRUeXBlID09PSAncmFkaW8nXG4gICAgICAgID8ge1xuICAgICAgICAgICAgY2hlY2tlZDogISF2YWx1ZSxcbiAgICAgICAgICB9XG4gICAgICAgIDoge30pLFxuICAgIH0sXG4gICAgYWNjZXNzaWJpbGl0eVZhbHVlOiBnZXRBY2Nlc3NpYmlsaXR5VmFsdWUoZmllbGRUeXBlLCB2YWx1ZSksXG4gICAgLy8gUmVhY3QgTmF0aXZlIHNwZWNpZmljIHByb3BzXG4gICAgLi4uKFBsYXRmb3JtLk9TID09PSAnd2ViJyAmJiB7XG4gICAgICAnYXJpYS1sYWJlbCc6IGFjY2Vzc2liaWxpdHlMYWJlbCxcbiAgICAgICdhcmlhLWRlc2NyaWJlZGJ5JzpcbiAgICAgICAgZGVzY3JpYmVkQnlJZHMubGVuZ3RoID4gMCA/IGRlc2NyaWJlZEJ5SWRzLmpvaW4oJyAnKSA6IHVuZGVmaW5lZCxcbiAgICAgICdhcmlhLWxhYmVsbGVkYnknOlxuICAgICAgICBsYWJlbGxlZEJ5SWRzLmxlbmd0aCA+IDAgPyBsYWJlbGxlZEJ5SWRzLmpvaW4oJyAnKSA6IHVuZGVmaW5lZCxcbiAgICAgICdhcmlhLXJlcXVpcmVkJzogcmVxdWlyZWQsXG4gICAgICAnYXJpYS1pbnZhbGlkJzogISFlcnJvck1lc3NhZ2UsXG4gICAgfSksXG4gIH07XG5cbiAgcmV0dXJuIHtcbiAgICAuLi5iYXNlUHJvcHMsXG4gICAgaWRzLFxuICB9O1xufTtcblxuLyoqXG4gKiBHZW5lcmF0ZSBhY2Nlc3NpYmlsaXR5IGxhYmVsIGZvciBmb3JtIGZpZWxkc1xuICovXG5leHBvcnQgY29uc3QgZ2VuZXJhdGVBY2Nlc3NpYmlsaXR5TGFiZWwgPSAob3B0aW9uczoge1xuICBsYWJlbD86IHN0cmluZztcbiAgcmVxdWlyZWQ/OiBib29sZWFuO1xuICBmaWVsZFR5cGU/OiBGb3JtRmllbGRUeXBlO1xuICBlcnJvck1lc3NhZ2U/OiBzdHJpbmc7XG4gIGN1c3RvbUxhYmVsPzogc3RyaW5nO1xufSk6IHN0cmluZyA9PiB7XG4gIGNvbnN0IHsgbGFiZWwsIHJlcXVpcmVkLCBmaWVsZFR5cGUsIGVycm9yTWVzc2FnZSwgY3VzdG9tTGFiZWwgfSA9IG9wdGlvbnM7XG5cbiAgaWYgKGN1c3RvbUxhYmVsKSByZXR1cm4gY3VzdG9tTGFiZWw7XG5cbiAgbGV0IGFjY2Vzc2liaWxpdHlMYWJlbCA9IGxhYmVsIHx8ICcnO1xuXG4gIC8vIEFkZCBmaWVsZCB0eXBlIGNvbnRleHQgaWYgaGVscGZ1bCBhbmQgbm90IGFscmVhZHkgaW5jbHVkZWRcbiAgaWYgKGZpZWxkVHlwZSAmJiBsYWJlbCAmJiAhbGFiZWwudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhmaWVsZFR5cGUpKSB7XG4gICAgY29uc3QgdHlwZUxhYmVscyA9IHtcbiAgICAgIGVtYWlsOiAnZW1haWwgYWRkcmVzcycsXG4gICAgICBwYXNzd29yZDogJ3Bhc3N3b3JkJyxcbiAgICAgIHBob25lOiAncGhvbmUgbnVtYmVyJyxcbiAgICAgIHNlYXJjaDogJ3NlYXJjaCcsXG4gICAgICB1cmw6ICd3ZWJzaXRlIFVSTCcsXG4gICAgICBudW1iZXI6ICdudW1iZXInLFxuICAgICAgZGF0ZTogJ2RhdGUnLFxuICAgICAgdGltZTogJ3RpbWUnLFxuICAgIH07XG5cbiAgICBjb25zdCB0eXBlTGFiZWwgPSB0eXBlTGFiZWxzW2ZpZWxkVHlwZSBhcyBrZXlvZiB0eXBlb2YgdHlwZUxhYmVsc107XG4gICAgaWYgKHR5cGVMYWJlbCAmJiAhbGFiZWwudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyh0eXBlTGFiZWwpKSB7XG4gICAgICBhY2Nlc3NpYmlsaXR5TGFiZWwgKz0gYCAke3R5cGVMYWJlbH1gO1xuICAgIH1cbiAgfVxuXG4gIC8vIEFkZCByZXF1aXJlZCBpbmRpY2F0b3JcbiAgaWYgKHJlcXVpcmVkKSB7XG4gICAgYWNjZXNzaWJpbGl0eUxhYmVsICs9ICcsIHJlcXVpcmVkJztcbiAgfVxuXG4gIC8vIEFkZCBlcnJvciBzdGF0ZVxuICBpZiAoZXJyb3JNZXNzYWdlKSB7XG4gICAgYWNjZXNzaWJpbGl0eUxhYmVsICs9ICcsIGhhcyBlcnJvcic7XG4gIH1cblxuICByZXR1cm4gYWNjZXNzaWJpbGl0eUxhYmVsLnRyaW0oKTtcbn07XG5cbi8qKlxuICogR2VuZXJhdGUgYWNjZXNzaWJpbGl0eSBoaW50IGZvciBmb3JtIGZpZWxkc1xuICovXG5leHBvcnQgY29uc3QgZ2VuZXJhdGVBY2Nlc3NpYmlsaXR5SGludCA9IChvcHRpb25zOiB7XG4gIGhlbHBlclRleHQ/OiBzdHJpbmc7XG4gIGZpZWxkVHlwZT86IEZvcm1GaWVsZFR5cGU7XG4gIGVycm9yTWVzc2FnZT86IHN0cmluZztcbiAgcmVxdWlyZWQ/OiBib29sZWFuO1xufSk6IHN0cmluZyA9PiB7XG4gIGNvbnN0IHsgaGVscGVyVGV4dCwgZmllbGRUeXBlLCBlcnJvck1lc3NhZ2UsIHJlcXVpcmVkIH0gPSBvcHRpb25zO1xuXG4gIGNvbnN0IGhpbnRzOiBzdHJpbmdbXSA9IFtdO1xuXG4gIC8vIEFkZCBoZWxwZXIgdGV4dFxuICBpZiAoaGVscGVyVGV4dCAmJiAhZXJyb3JNZXNzYWdlKSB7XG4gICAgaGludHMucHVzaChoZWxwZXJUZXh0KTtcbiAgfVxuXG4gIC8vIEFkZCBmaWVsZC1zcGVjaWZpYyBoaW50c1xuICBjb25zdCBmaWVsZEhpbnRzID0ge1xuICAgIHBhc3N3b3JkOiAnRG91YmxlIHRhcCB0byB0b2dnbGUgcGFzc3dvcmQgdmlzaWJpbGl0eScsXG4gICAgc2VsZWN0OiAnRG91YmxlIHRhcCB0byBvcGVuIG9wdGlvbnMnLFxuICAgIGNoZWNrYm94OiAnRG91YmxlIHRhcCB0byB0b2dnbGUgc2VsZWN0aW9uJyxcbiAgICByYWRpbzogJ0RvdWJsZSB0YXAgdG8gc2VsZWN0IG9wdGlvbicsXG4gICAgZGF0ZTogJ0RvdWJsZSB0YXAgdG8gb3BlbiBkYXRlIHBpY2tlcicsXG4gICAgdGltZTogJ0RvdWJsZSB0YXAgdG8gb3BlbiB0aW1lIHBpY2tlcicsXG4gIH07XG5cbiAgaWYgKGZpZWxkVHlwZSAmJiBmaWVsZEhpbnRzW2ZpZWxkVHlwZSBhcyBrZXlvZiB0eXBlb2YgZmllbGRIaW50c10pIHtcbiAgICBoaW50cy5wdXNoKGZpZWxkSGludHNbZmllbGRUeXBlIGFzIGtleW9mIHR5cGVvZiBmaWVsZEhpbnRzXSk7XG4gIH1cblxuICAvLyBBZGQgZXJyb3IgbWVzc2FnZSBhcyBoaW50IGlmIHByZXNlbnRcbiAgaWYgKGVycm9yTWVzc2FnZSkge1xuICAgIGhpbnRzLnB1c2goYEVycm9yOiAke2Vycm9yTWVzc2FnZX1gKTtcbiAgfVxuXG4gIHJldHVybiBoaW50cy5qb2luKCcuICcpO1xufTtcblxuLyoqXG4gKiBHZXQgYWNjZXNzaWJpbGl0eSB2YWx1ZSBiYXNlZCBvbiBmaWVsZCB0eXBlXG4gKi9cbmV4cG9ydCBjb25zdCBnZXRBY2Nlc3NpYmlsaXR5VmFsdWUgPSAoXG4gIGZpZWxkVHlwZT86IEZvcm1GaWVsZFR5cGUsXG4gIHZhbHVlPzogc3RyaW5nIHwgYm9vbGVhbiB8IG51bWJlcixcbik6IGFueSA9PiB7XG4gIGlmICh2YWx1ZSA9PT0gdW5kZWZpbmVkIHx8IHZhbHVlID09PSBudWxsKSByZXR1cm4gdW5kZWZpbmVkO1xuXG4gIHN3aXRjaCAoZmllbGRUeXBlKSB7XG4gICAgY2FzZSAnY2hlY2tib3gnOlxuICAgIGNhc2UgJ3JhZGlvJzpcbiAgICAgIHJldHVybiB1bmRlZmluZWQ7IC8vIEhhbmRsZWQgYnkgYWNjZXNzaWJpbGl0eVN0YXRlLmNoZWNrZWRcbiAgICBjYXNlICdudW1iZXInOlxuICAgICAgcmV0dXJuIHsgbm93OiBOdW1iZXIodmFsdWUpIH07XG4gICAgY2FzZSAndGV4dCc6XG4gICAgY2FzZSAnZW1haWwnOlxuICAgIGNhc2UgJ3Bhc3N3b3JkJzpcbiAgICBjYXNlICdwaG9uZSc6XG4gICAgY2FzZSAnbmFtZSc6XG4gICAgY2FzZSAnc2VhcmNoJzpcbiAgICBjYXNlICd1cmwnOlxuICAgIGNhc2UgJ3RleHRhcmVhJzpcbiAgICBkZWZhdWx0OlxuICAgICAgcmV0dXJuIHsgdGV4dDogU3RyaW5nKHZhbHVlKSB9O1xuICB9XG59O1xuXG4vKipcbiAqIEdlbmVyYXRlIGVycm9yIG1lc3NhZ2UgYWNjZXNzaWJpbGl0eSBwcm9wc1xuICovXG5leHBvcnQgY29uc3QgZ2VuZXJhdGVFcnJvck1lc3NhZ2VQcm9wcyA9IChcbiAgZXJyb3JNZXNzYWdlOiBzdHJpbmcsXG4gIGVycm9ySWQ6IHN0cmluZyxcbikgPT4gKHtcbiAgYWNjZXNzaWJpbGl0eVJvbGU6ICdhbGVydCcgYXMgY29uc3QsXG4gIGFjY2Vzc2liaWxpdHlMaXZlUmVnaW9uOiAncG9saXRlJyBhcyBjb25zdCxcbiAgYWNjZXNzaWJpbGl0eUxhYmVsOiBgRXJyb3I6ICR7ZXJyb3JNZXNzYWdlfWAsXG4gIC4uLihQbGF0Zm9ybS5PUyA9PT0gJ3dlYicgJiYge1xuICAgIHJvbGU6ICdhbGVydCcsXG4gICAgJ2FyaWEtbGl2ZSc6ICdwb2xpdGUnLFxuICAgIGlkOiBlcnJvcklkLFxuICB9KSxcbn0pO1xuXG4vKipcbiAqIEdlbmVyYXRlIGhlbHBlciB0ZXh0IGFjY2Vzc2liaWxpdHkgcHJvcHNcbiAqL1xuZXhwb3J0IGNvbnN0IGdlbmVyYXRlSGVscGVyVGV4dFByb3BzID0gKFxuICBoZWxwZXJUZXh0OiBzdHJpbmcsXG4gIGhlbHBlcklkOiBzdHJpbmcsXG4pID0+ICh7XG4gIGFjY2Vzc2liaWxpdHlMYWJlbDogaGVscGVyVGV4dCxcbiAgLi4uKFBsYXRmb3JtLk9TID09PSAnd2ViJyAmJiB7XG4gICAgaWQ6IGhlbHBlcklkLFxuICB9KSxcbn0pO1xuXG4vKipcbiAqIEdlbmVyYXRlIGxhYmVsIGFjY2Vzc2liaWxpdHkgcHJvcHNcbiAqL1xuZXhwb3J0IGNvbnN0IGdlbmVyYXRlTGFiZWxQcm9wcyA9IChcbiAgbGFiZWw6IHN0cmluZyxcbiAgbGFiZWxJZDogc3RyaW5nLFxuICByZXF1aXJlZDogYm9vbGVhbiA9IGZhbHNlLFxuKSA9PiAoe1xuICBhY2Nlc3NpYmlsaXR5TGFiZWw6IHJlcXVpcmVkID8gYCR7bGFiZWx9LCByZXF1aXJlZGAgOiBsYWJlbCxcbiAgLi4uKFBsYXRmb3JtLk9TID09PSAnd2ViJyAmJiB7XG4gICAgaWQ6IGxhYmVsSWQsXG4gICAgaHRtbEZvcjogbGFiZWxJZC5yZXBsYWNlKCdfbGFiZWwnLCAnJyksXG4gIH0pLFxufSk7XG5cbi8qKlxuICogVmFsaWRhdGUgZm9ybSBhY2Nlc3NpYmlsaXR5IGltcGxlbWVudGF0aW9uXG4gKi9cbmV4cG9ydCBjb25zdCB2YWxpZGF0ZUZvcm1BY2Nlc3NpYmlsaXR5ID0gKFxuICBmb3JtRmllbGRzOiBFbmhhbmNlZEZvcm1GaWVsZFByb3BzW10sXG4pOiB7XG4gIGlzVmFsaWQ6IGJvb2xlYW47XG4gIGVycm9yczogc3RyaW5nW107XG4gIHdhcm5pbmdzOiBzdHJpbmdbXTtcbn0gPT4ge1xuICBjb25zdCBlcnJvcnM6IHN0cmluZ1tdID0gW107XG4gIGNvbnN0IHdhcm5pbmdzOiBzdHJpbmdbXSA9IFtdO1xuXG4gIGZvcm1GaWVsZHMuZm9yRWFjaCgoZmllbGQsIGluZGV4KSA9PiB7XG4gICAgY29uc3QgZmllbGROYW1lID0gZmllbGQubGFiZWwgfHwgYEZpZWxkICR7aW5kZXggKyAxfWA7XG5cbiAgICAvLyBDaGVjayBmb3IgbWlzc2luZyBsYWJlbHNcbiAgICBpZiAoIWZpZWxkLmxhYmVsICYmICFmaWVsZC5hcmlhTGFiZWwgJiYgIWZpZWxkLnBsYWNlaG9sZGVyKSB7XG4gICAgICBlcnJvcnMucHVzaChgJHtmaWVsZE5hbWV9OiBNaXNzaW5nIGFjY2Vzc2libGUgbGFiZWxgKTtcbiAgICB9XG5cbiAgICAvLyBDaGVjayBmb3IgcmVxdWlyZWQgZmllbGQgaW5kaWNhdG9yc1xuICAgIGlmIChcbiAgICAgIGZpZWxkLnJlcXVpcmVkICYmXG4gICAgICAhZmllbGQubGFiZWw/LmluY2x1ZGVzKCcqJykgJiZcbiAgICAgICFmaWVsZC5hcmlhTGFiZWw/LmluY2x1ZGVzKCdyZXF1aXJlZCcpXG4gICAgKSB7XG4gICAgICB3YXJuaW5ncy5wdXNoKGAke2ZpZWxkTmFtZX06IFJlcXVpcmVkIGZpZWxkIHNob3VsZCBoYXZlIGNsZWFyIGluZGljYXRvcmApO1xuICAgIH1cblxuICAgIC8vIENoZWNrIGZvciBlcnJvciBtZXNzYWdlIGFjY2Vzc2liaWxpdHlcbiAgICBpZiAoZmllbGQuZXJyb3JNZXNzYWdlICYmICFmaWVsZC5hcmlhRGVzY3JpYmVkQnkpIHtcbiAgICAgIHdhcm5pbmdzLnB1c2goXG4gICAgICAgIGAke2ZpZWxkTmFtZX06IEVycm9yIG1lc3NhZ2Ugc2hvdWxkIGJlIHByb2dyYW1tYXRpY2FsbHkgYXNzb2NpYXRlZGAsXG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIENoZWNrIGZvciBoZWxwZXIgdGV4dCBhY2Nlc3NpYmlsaXR5XG4gICAgaWYgKGZpZWxkLmhlbHBlclRleHQgJiYgIWZpZWxkLmFyaWFEZXNjcmliZWRCeSkge1xuICAgICAgd2FybmluZ3MucHVzaChcbiAgICAgICAgYCR7ZmllbGROYW1lfTogSGVscGVyIHRleHQgc2hvdWxkIGJlIHByb2dyYW1tYXRpY2FsbHkgYXNzb2NpYXRlZGAsXG4gICAgICApO1xuICAgIH1cbiAgfSk7XG5cbiAgcmV0dXJuIHtcbiAgICBpc1ZhbGlkOiBlcnJvcnMubGVuZ3RoID09PSAwLFxuICAgIGVycm9ycyxcbiAgICB3YXJuaW5ncyxcbiAgfTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IHtcbiAgRk9STV9BQ0NFU1NJQklMSVRZX0NPTkZJRyxcbiAgZ2VuZXJhdGVGb3JtRmllbGRJZHMsXG4gIGdlbmVyYXRlRm9ybUZpZWxkQWNjZXNzaWJpbGl0eVByb3BzLFxuICBnZW5lcmF0ZUFjY2Vzc2liaWxpdHlMYWJlbCxcbiAgZ2VuZXJhdGVBY2Nlc3NpYmlsaXR5SGludCxcbiAgZ2V0QWNjZXNzaWJpbGl0eVZhbHVlLFxuICBnZW5lcmF0ZUVycm9yTWVzc2FnZVByb3BzLFxuICBnZW5lcmF0ZUhlbHBlclRleHRQcm9wcyxcbiAgZ2VuZXJhdGVMYWJlbFByb3BzLFxuICB2YWxpZGF0ZUZvcm1BY2Nlc3NpYmlsaXR5LFxufTtcbiJdLCJtYXBwaW5ncyI6Ijs7OztBQWlCQSxJQUFBQSxZQUFBLEdBQUFDLE9BQUE7QUFHTyxJQUFNQyx5QkFBeUIsR0FBQUMsT0FBQSxDQUFBRCx5QkFBQSxHQUFHO0VBQ3ZDRSxpQkFBaUIsRUFBRTtJQUNqQkMsbUJBQW1CLEVBQUUsSUFBSTtJQUN6QkMsb0JBQW9CLEVBQUUsSUFBSTtJQUMxQkMsMEJBQTBCLEVBQUU7RUFDOUIsQ0FBQztFQUNEQyxlQUFlLEVBQUU7SUFDZkMsZUFBZSxFQUFFLElBQUk7SUFDckJDLFVBQVUsRUFBRSxPQUFPO0lBQ25CQyxXQUFXLEVBQUU7RUFDZixDQUFDO0VBQ0RDLGdCQUFnQixFQUFFO0lBQ2hCQyxnQkFBZ0IsRUFBRSxLQUFLO0lBQ3ZCSixlQUFlLEVBQUUsSUFBSTtJQUNyQkssY0FBYyxFQUFFO0VBQ2xCO0FBQ0YsQ0FBVTtBQXNDSCxJQUFNQyxvQkFBb0IsR0FBQVosT0FBQSxDQUFBWSxvQkFBQSxHQUFHLFNBQXZCQSxvQkFBb0JBLENBQUlDLE1BQWMsRUFBSztFQUN0RCxJQUFNQyxTQUFTLEdBQUdDLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUM7RUFDNUIsSUFBTUMsTUFBTSxHQUFHQyxJQUFJLENBQUNELE1BQU0sQ0FBQyxDQUFDLENBQUNFLFFBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQ0MsTUFBTSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUM7RUFDdEQsSUFBTUMsUUFBUSxHQUFHLEdBQUdSLE1BQU0sSUFBSUMsU0FBUyxJQUFJRyxNQUFNLEVBQUU7RUFFbkQsT0FBTztJQUNMSyxPQUFPLEVBQUVELFFBQVE7SUFDakJFLE9BQU8sRUFBRSxHQUFHRixRQUFRLFFBQVE7SUFDNUJHLE9BQU8sRUFBRSxHQUFHSCxRQUFRLFFBQVE7SUFDNUJJLFFBQVEsRUFBRSxHQUFHSixRQUFRLFNBQVM7SUFDOUJLLGFBQWEsRUFBRSxHQUFHTCxRQUFRO0VBQzVCLENBQUM7QUFDSCxDQUFDO0FBS00sSUFBTU0sbUNBQW1DLEdBQUEzQixPQUFBLENBQUEyQixtQ0FBQSxHQUFHLFNBQXRDQSxtQ0FBbUNBLENBQzlDQyxLQUE2QixFQUNyQjtFQUNSLElBQ0VDLEVBQUUsR0FXQUQsS0FBSyxDQVhQQyxFQUFFO0lBQ0ZDLEtBQUssR0FVSEYsS0FBSyxDQVZQRSxLQUFLO0lBQ0xDLFVBQVUsR0FTUkgsS0FBSyxDQVRQRyxVQUFVO0lBQ1ZDLFlBQVksR0FRVkosS0FBSyxDQVJQSSxZQUFZO0lBQUFDLGVBQUEsR0FRVkwsS0FBSyxDQVBQTSxRQUFRO0lBQVJBLFFBQVEsR0FBQUQsZUFBQSxjQUFHLEtBQUssR0FBQUEsZUFBQTtJQUFBRSxlQUFBLEdBT2RQLEtBQUssQ0FOUFEsUUFBUTtJQUFSQSxRQUFRLEdBQUFELGVBQUEsY0FBRyxLQUFLLEdBQUFBLGVBQUE7SUFBQUUsZ0JBQUEsR0FNZFQsS0FBSyxDQUxQVSxTQUFTO0lBQVRBLFNBQVMsR0FBQUQsZ0JBQUEsY0FBRyxNQUFNLEdBQUFBLGdCQUFBO0lBQ2xCRSxLQUFLLEdBSUhYLEtBQUssQ0FKUFcsS0FBSztJQUNMQyxTQUFTLEdBR1BaLEtBQUssQ0FIUFksU0FBUztJQUNUQyxlQUFlLEdBRWJiLEtBQUssQ0FGUGEsZUFBZTtJQUNmQyxjQUFjLEdBQ1pkLEtBQUssQ0FEUGMsY0FBYztFQUloQixJQUFNQyxHQUFHLEdBQUdkLEVBQUUsR0FBR2pCLG9CQUFvQixDQUFDaUIsRUFBRSxDQUFDLEdBQUdqQixvQkFBb0IsQ0FBQyxPQUFPLENBQUM7RUFHekUsSUFBTWdDLGNBQXdCLEdBQUcsRUFBRTtFQUNuQyxJQUFJWixZQUFZLEVBQUVZLGNBQWMsQ0FBQ0MsSUFBSSxDQUFDRixHQUFHLENBQUNuQixPQUFPLENBQUM7RUFDbEQsSUFBSU8sVUFBVSxFQUFFYSxjQUFjLENBQUNDLElBQUksQ0FBQ0YsR0FBRyxDQUFDbEIsUUFBUSxDQUFDO0VBQ2pELElBQUlnQixlQUFlLEVBQUVHLGNBQWMsQ0FBQ0MsSUFBSSxDQUFDSixlQUFlLENBQUM7RUFHekQsSUFBTUssYUFBdUIsR0FBRyxFQUFFO0VBQ2xDLElBQUloQixLQUFLLEVBQUVnQixhQUFhLENBQUNELElBQUksQ0FBQ0YsR0FBRyxDQUFDcEIsT0FBTyxDQUFDO0VBQzFDLElBQUltQixjQUFjLEVBQUVJLGFBQWEsQ0FBQ0QsSUFBSSxDQUFDSCxjQUFjLENBQUM7RUFHdEQsSUFBTUssa0JBQWtCLEdBQUdDLDBCQUEwQixDQUFDO0lBQ3BEbEIsS0FBSyxFQUFMQSxLQUFLO0lBQ0xJLFFBQVEsRUFBUkEsUUFBUTtJQUNSSSxTQUFTLEVBQVRBLFNBQVM7SUFDVE4sWUFBWSxFQUFaQSxZQUFZO0lBQ1ppQixXQUFXLEVBQUVUO0VBQ2YsQ0FBQyxDQUFDO0VBR0YsSUFBTVUsaUJBQWlCLEdBQUdDLHlCQUF5QixDQUFDO0lBQ2xEcEIsVUFBVSxFQUFWQSxVQUFVO0lBQ1ZPLFNBQVMsRUFBVEEsU0FBUztJQUNUTixZQUFZLEVBQVpBLFlBQVk7SUFDWkUsUUFBUSxFQUFSQTtFQUNGLENBQUMsQ0FBQztFQUdGLElBQU1rQixTQUFTLEdBQUFDLE1BQUEsQ0FBQUMsTUFBQTtJQUNiUCxrQkFBa0IsRUFBbEJBLGtCQUFrQjtJQUNsQkcsaUJBQWlCLEVBQWpCQSxpQkFBaUI7SUFDakJLLGtCQUFrQixFQUFBRixNQUFBLENBQUFDLE1BQUE7TUFDaEJsQixRQUFRLEVBQVJBLFFBQVE7TUFDUm9CLE9BQU8sRUFBRSxDQUFDLENBQUN4QixZQUFZO01BQ3ZCRSxRQUFRLEVBQVJBO0lBQVEsR0FDSkksU0FBUyxLQUFLLFVBQVUsSUFBSUEsU0FBUyxLQUFLLE9BQU8sR0FDakQ7TUFDRW1CLE9BQU8sRUFBRSxDQUFDLENBQUNsQjtJQUNiLENBQUMsR0FDRCxDQUFDLENBQUMsQ0FDUDtJQUNEbUIsa0JBQWtCLEVBQUVDLHFCQUFxQixDQUFDckIsU0FBUyxFQUFFQyxLQUFLO0VBQUMsR0FFdkRxQixxQkFBUSxDQUFDQyxFQUFFLEtBQUssS0FBSyxJQUFJO0lBQzNCLFlBQVksRUFBRWQsa0JBQWtCO0lBQ2hDLGtCQUFrQixFQUNoQkgsY0FBYyxDQUFDa0IsTUFBTSxHQUFHLENBQUMsR0FBR2xCLGNBQWMsQ0FBQ21CLElBQUksQ0FBQyxHQUFHLENBQUMsR0FBR0MsU0FBUztJQUNsRSxpQkFBaUIsRUFDZmxCLGFBQWEsQ0FBQ2dCLE1BQU0sR0FBRyxDQUFDLEdBQUdoQixhQUFhLENBQUNpQixJQUFJLENBQUMsR0FBRyxDQUFDLEdBQUdDLFNBQVM7SUFDaEUsZUFBZSxFQUFFOUIsUUFBUTtJQUN6QixjQUFjLEVBQUUsQ0FBQyxDQUFDRjtFQUNwQixDQUFDLENBQ0Y7RUFFRCxPQUFBcUIsTUFBQSxDQUFBQyxNQUFBLEtBQ0tGLFNBQVM7SUFDWlQsR0FBRyxFQUFIQTtFQUFHO0FBRVAsQ0FBQztBQUtNLElBQU1LLDBCQUEwQixHQUFBaEQsT0FBQSxDQUFBZ0QsMEJBQUEsR0FBRyxTQUE3QkEsMEJBQTBCQSxDQUFJaUIsT0FNMUMsRUFBYTtFQUNaLElBQVFuQyxLQUFLLEdBQXFEbUMsT0FBTyxDQUFqRW5DLEtBQUs7SUFBRUksUUFBUSxHQUEyQytCLE9BQU8sQ0FBMUQvQixRQUFRO0lBQUVJLFNBQVMsR0FBZ0MyQixPQUFPLENBQWhEM0IsU0FBUztJQUFFTixZQUFZLEdBQWtCaUMsT0FBTyxDQUFyQ2pDLFlBQVk7SUFBRWlCLFdBQVcsR0FBS2dCLE9BQU8sQ0FBdkJoQixXQUFXO0VBRTdELElBQUlBLFdBQVcsRUFBRSxPQUFPQSxXQUFXO0VBRW5DLElBQUlGLGtCQUFrQixHQUFHakIsS0FBSyxJQUFJLEVBQUU7RUFHcEMsSUFBSVEsU0FBUyxJQUFJUixLQUFLLElBQUksQ0FBQ0EsS0FBSyxDQUFDb0MsV0FBVyxDQUFDLENBQUMsQ0FBQ0MsUUFBUSxDQUFDN0IsU0FBUyxDQUFDLEVBQUU7SUFDbEUsSUFBTThCLFVBQVUsR0FBRztNQUNqQkMsS0FBSyxFQUFFLGVBQWU7TUFDdEJDLFFBQVEsRUFBRSxVQUFVO01BQ3BCQyxLQUFLLEVBQUUsY0FBYztNQUNyQkMsTUFBTSxFQUFFLFFBQVE7TUFDaEJDLEdBQUcsRUFBRSxhQUFhO01BQ2xCQyxNQUFNLEVBQUUsUUFBUTtNQUNoQkMsSUFBSSxFQUFFLE1BQU07TUFDWkMsSUFBSSxFQUFFO0lBQ1IsQ0FBQztJQUVELElBQU1DLFNBQVMsR0FBR1QsVUFBVSxDQUFDOUIsU0FBUyxDQUE0QjtJQUNsRSxJQUFJdUMsU0FBUyxJQUFJLENBQUMvQyxLQUFLLENBQUNvQyxXQUFXLENBQUMsQ0FBQyxDQUFDQyxRQUFRLENBQUNVLFNBQVMsQ0FBQyxFQUFFO01BQ3pEOUIsa0JBQWtCLElBQUksSUFBSThCLFNBQVMsRUFBRTtJQUN2QztFQUNGO0VBR0EsSUFBSTNDLFFBQVEsRUFBRTtJQUNaYSxrQkFBa0IsSUFBSSxZQUFZO0VBQ3BDO0VBR0EsSUFBSWYsWUFBWSxFQUFFO0lBQ2hCZSxrQkFBa0IsSUFBSSxhQUFhO0VBQ3JDO0VBRUEsT0FBT0Esa0JBQWtCLENBQUMrQixJQUFJLENBQUMsQ0FBQztBQUNsQyxDQUFDO0FBS00sSUFBTTNCLHlCQUF5QixHQUFBbkQsT0FBQSxDQUFBbUQseUJBQUEsR0FBRyxTQUE1QkEseUJBQXlCQSxDQUFJYyxPQUt6QyxFQUFhO0VBQ1osSUFBUWxDLFVBQVUsR0FBd0NrQyxPQUFPLENBQXpEbEMsVUFBVTtJQUFFTyxTQUFTLEdBQTZCMkIsT0FBTyxDQUE3QzNCLFNBQVM7SUFBRU4sWUFBWSxHQUFlaUMsT0FBTyxDQUFsQ2pDLFlBQVk7SUFBRUUsUUFBUSxHQUFLK0IsT0FBTyxDQUFwQi9CLFFBQVE7RUFFckQsSUFBTTZDLEtBQWUsR0FBRyxFQUFFO0VBRzFCLElBQUloRCxVQUFVLElBQUksQ0FBQ0MsWUFBWSxFQUFFO0lBQy9CK0MsS0FBSyxDQUFDbEMsSUFBSSxDQUFDZCxVQUFVLENBQUM7RUFDeEI7RUFHQSxJQUFNaUQsVUFBVSxHQUFHO0lBQ2pCVixRQUFRLEVBQUUsMENBQTBDO0lBQ3BEVyxNQUFNLEVBQUUsNEJBQTRCO0lBQ3BDQyxRQUFRLEVBQUUsZ0NBQWdDO0lBQzFDQyxLQUFLLEVBQUUsNkJBQTZCO0lBQ3BDUixJQUFJLEVBQUUsZ0NBQWdDO0lBQ3RDQyxJQUFJLEVBQUU7RUFDUixDQUFDO0VBRUQsSUFBSXRDLFNBQVMsSUFBSTBDLFVBQVUsQ0FBQzFDLFNBQVMsQ0FBNEIsRUFBRTtJQUNqRXlDLEtBQUssQ0FBQ2xDLElBQUksQ0FBQ21DLFVBQVUsQ0FBQzFDLFNBQVMsQ0FBNEIsQ0FBQztFQUM5RDtFQUdBLElBQUlOLFlBQVksRUFBRTtJQUNoQitDLEtBQUssQ0FBQ2xDLElBQUksQ0FBQyxVQUFVYixZQUFZLEVBQUUsQ0FBQztFQUN0QztFQUVBLE9BQU8rQyxLQUFLLENBQUNoQixJQUFJLENBQUMsSUFBSSxDQUFDO0FBQ3pCLENBQUM7QUFLTSxJQUFNSixxQkFBcUIsR0FBQTNELE9BQUEsQ0FBQTJELHFCQUFBLEdBQUcsU0FBeEJBLHFCQUFxQkEsQ0FDaENyQixTQUF5QixFQUN6QkMsS0FBaUMsRUFDekI7RUFDUixJQUFJQSxLQUFLLEtBQUt5QixTQUFTLElBQUl6QixLQUFLLEtBQUssSUFBSSxFQUFFLE9BQU95QixTQUFTO0VBRTNELFFBQVExQixTQUFTO0lBQ2YsS0FBSyxVQUFVO0lBQ2YsS0FBSyxPQUFPO01BQ1YsT0FBTzBCLFNBQVM7SUFDbEIsS0FBSyxRQUFRO01BQ1gsT0FBTztRQUFFaEQsR0FBRyxFQUFFb0UsTUFBTSxDQUFDN0MsS0FBSztNQUFFLENBQUM7SUFDL0IsS0FBSyxNQUFNO0lBQ1gsS0FBSyxPQUFPO0lBQ1osS0FBSyxVQUFVO0lBQ2YsS0FBSyxPQUFPO0lBQ1osS0FBSyxNQUFNO0lBQ1gsS0FBSyxRQUFRO0lBQ2IsS0FBSyxLQUFLO0lBQ1YsS0FBSyxVQUFVO0lBQ2Y7TUFDRSxPQUFPO1FBQUU4QyxJQUFJLEVBQUVDLE1BQU0sQ0FBQy9DLEtBQUs7TUFBRSxDQUFDO0VBQ2xDO0FBQ0YsQ0FBQztBQUtNLElBQU1nRCx5QkFBeUIsR0FBQXZGLE9BQUEsQ0FBQXVGLHlCQUFBLEdBQUcsU0FBNUJBLHlCQUF5QkEsQ0FDcEN2RCxZQUFvQixFQUNwQlIsT0FBZTtFQUFBLE9BQUE2QixNQUFBLENBQUFDLE1BQUE7SUFFZmtDLGlCQUFpQixFQUFFLE9BQWdCO0lBQ25DQyx1QkFBdUIsRUFBRSxRQUFpQjtJQUMxQzFDLGtCQUFrQixFQUFFLFVBQVVmLFlBQVk7RUFBRSxHQUN4QzRCLHFCQUFRLENBQUNDLEVBQUUsS0FBSyxLQUFLLElBQUk7SUFDM0I2QixJQUFJLEVBQUUsT0FBTztJQUNiLFdBQVcsRUFBRSxRQUFRO0lBQ3JCN0QsRUFBRSxFQUFFTDtFQUNOLENBQUM7QUFBQSxDQUNEO0FBS0ssSUFBTW1FLHVCQUF1QixHQUFBM0YsT0FBQSxDQUFBMkYsdUJBQUEsR0FBRyxTQUExQkEsdUJBQXVCQSxDQUNsQzVELFVBQWtCLEVBQ2xCTixRQUFnQjtFQUFBLE9BQUE0QixNQUFBLENBQUFDLE1BQUE7SUFFaEJQLGtCQUFrQixFQUFFaEI7RUFBVSxHQUMxQjZCLHFCQUFRLENBQUNDLEVBQUUsS0FBSyxLQUFLLElBQUk7SUFDM0JoQyxFQUFFLEVBQUVKO0VBQ04sQ0FBQztBQUFBLENBQ0Q7QUFLSyxJQUFNbUUsa0JBQWtCLEdBQUE1RixPQUFBLENBQUE0RixrQkFBQSxHQUFHLFNBQXJCQSxrQkFBa0JBLENBQzdCOUQsS0FBYSxFQUNiUCxPQUFlO0VBQUEsSUFDZlcsUUFBaUIsR0FBQTJELFNBQUEsQ0FBQS9CLE1BQUEsUUFBQStCLFNBQUEsUUFBQTdCLFNBQUEsR0FBQTZCLFNBQUEsTUFBRyxLQUFLO0VBQUEsT0FBQXhDLE1BQUEsQ0FBQUMsTUFBQTtJQUV6QlAsa0JBQWtCLEVBQUViLFFBQVEsR0FBRyxHQUFHSixLQUFLLFlBQVksR0FBR0E7RUFBSyxHQUN2RDhCLHFCQUFRLENBQUNDLEVBQUUsS0FBSyxLQUFLLElBQUk7SUFDM0JoQyxFQUFFLEVBQUVOLE9BQU87SUFDWHVFLE9BQU8sRUFBRXZFLE9BQU8sQ0FBQ3dFLE9BQU8sQ0FBQyxRQUFRLEVBQUUsRUFBRTtFQUN2QyxDQUFDO0FBQUEsQ0FDRDtBQUtLLElBQU1DLHlCQUF5QixHQUFBaEcsT0FBQSxDQUFBZ0cseUJBQUEsR0FBRyxTQUE1QkEseUJBQXlCQSxDQUNwQ0MsVUFBb0MsRUFLakM7RUFDSCxJQUFNQyxNQUFnQixHQUFHLEVBQUU7RUFDM0IsSUFBTUMsUUFBa0IsR0FBRyxFQUFFO0VBRTdCRixVQUFVLENBQUNHLE9BQU8sQ0FBQyxVQUFDQyxLQUFLLEVBQUVDLEtBQUssRUFBSztJQUFBLElBQUFDLFlBQUEsRUFBQUMsZ0JBQUE7SUFDbkMsSUFBTUMsU0FBUyxHQUFHSixLQUFLLENBQUN2RSxLQUFLLElBQUksU0FBU3dFLEtBQUssR0FBRyxDQUFDLEVBQUU7SUFHckQsSUFBSSxDQUFDRCxLQUFLLENBQUN2RSxLQUFLLElBQUksQ0FBQ3VFLEtBQUssQ0FBQzdELFNBQVMsSUFBSSxDQUFDNkQsS0FBSyxDQUFDSyxXQUFXLEVBQUU7TUFDMURSLE1BQU0sQ0FBQ3JELElBQUksQ0FBQyxHQUFHNEQsU0FBUyw0QkFBNEIsQ0FBQztJQUN2RDtJQUdBLElBQ0VKLEtBQUssQ0FBQ25FLFFBQVEsSUFDZCxHQUFBcUUsWUFBQSxHQUFDRixLQUFLLENBQUN2RSxLQUFLLGFBQVh5RSxZQUFBLENBQWFwQyxRQUFRLENBQUMsR0FBRyxDQUFDLEtBQzNCLEdBQUFxQyxnQkFBQSxHQUFDSCxLQUFLLENBQUM3RCxTQUFTLGFBQWZnRSxnQkFBQSxDQUFpQnJDLFFBQVEsQ0FBQyxVQUFVLENBQUMsR0FDdEM7TUFDQWdDLFFBQVEsQ0FBQ3RELElBQUksQ0FBQyxHQUFHNEQsU0FBUyw4Q0FBOEMsQ0FBQztJQUMzRTtJQUdBLElBQUlKLEtBQUssQ0FBQ3JFLFlBQVksSUFBSSxDQUFDcUUsS0FBSyxDQUFDNUQsZUFBZSxFQUFFO01BQ2hEMEQsUUFBUSxDQUFDdEQsSUFBSSxDQUNYLEdBQUc0RCxTQUFTLHVEQUNkLENBQUM7SUFDSDtJQUdBLElBQUlKLEtBQUssQ0FBQ3RFLFVBQVUsSUFBSSxDQUFDc0UsS0FBSyxDQUFDNUQsZUFBZSxFQUFFO01BQzlDMEQsUUFBUSxDQUFDdEQsSUFBSSxDQUNYLEdBQUc0RCxTQUFTLHFEQUNkLENBQUM7SUFDSDtFQUNGLENBQUMsQ0FBQztFQUVGLE9BQU87SUFDTEUsT0FBTyxFQUFFVCxNQUFNLENBQUNwQyxNQUFNLEtBQUssQ0FBQztJQUM1Qm9DLE1BQU0sRUFBTkEsTUFBTTtJQUNOQyxRQUFRLEVBQVJBO0VBQ0YsQ0FBQztBQUNILENBQUM7QUFBQyxJQUFBUyxRQUFBLEdBQUE1RyxPQUFBLENBQUE2RyxPQUFBLEdBRWE7RUFDYjlHLHlCQUF5QixFQUF6QkEseUJBQXlCO0VBQ3pCYSxvQkFBb0IsRUFBcEJBLG9CQUFvQjtFQUNwQmUsbUNBQW1DLEVBQW5DQSxtQ0FBbUM7RUFDbkNxQiwwQkFBMEIsRUFBMUJBLDBCQUEwQjtFQUMxQkcseUJBQXlCLEVBQXpCQSx5QkFBeUI7RUFDekJRLHFCQUFxQixFQUFyQkEscUJBQXFCO0VBQ3JCNEIseUJBQXlCLEVBQXpCQSx5QkFBeUI7RUFDekJJLHVCQUF1QixFQUF2QkEsdUJBQXVCO0VBQ3ZCQyxrQkFBa0IsRUFBbEJBLGtCQUFrQjtFQUNsQkkseUJBQXlCLEVBQXpCQTtBQUNGLENBQUMiLCJpZ25vcmVMaXN0IjpbXX0=