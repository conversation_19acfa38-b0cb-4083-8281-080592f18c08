baeb98210b7bb0c817268f126102bd9e
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
function flattenStyle(style) {
  if (style === null || typeof style !== 'object') {
    return undefined;
  }
  if (!Array.isArray(style)) {
    return style;
  }
  var result = {};
  for (var i = 0, styleLength = style.length; i < styleLength; ++i) {
    var computedStyle = flattenStyle(style[i]);
    if (computedStyle) {
      for (var key in computedStyle) {
        result[key] = computedStyle[key];
      }
    }
  }
  return result;
}
var _default = exports.default = flattenStyle;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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