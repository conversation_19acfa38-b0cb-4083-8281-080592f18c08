7467e0fad6c752786ff9a462bbd7112c
'use strict';

var React = require('react');
var vanilla = require('zustand/vanilla');
var identity = function identity(arg) {
  return arg;
};
function useStore(api) {
  var selector = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : identity;
  var slice = React.useSyncExternalStore(api.subscribe, function () {
    return selector(api.getState());
  }, function () {
    return selector(api.getInitialState());
  });
  React.useDebugValue(slice);
  return slice;
}
var createImpl = function createImpl(createState) {
  var api = vanilla.createStore(createState);
  var useBoundStore = function useBoundStore(selector) {
    return useStore(api, selector);
  };
  Object.assign(useBoundStore, api);
  return useBoundStore;
};
var create = function create(createState) {
  return createState ? createImpl(createState) : createImpl;
};
exports.create = create;
exports.useStore = useStore;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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