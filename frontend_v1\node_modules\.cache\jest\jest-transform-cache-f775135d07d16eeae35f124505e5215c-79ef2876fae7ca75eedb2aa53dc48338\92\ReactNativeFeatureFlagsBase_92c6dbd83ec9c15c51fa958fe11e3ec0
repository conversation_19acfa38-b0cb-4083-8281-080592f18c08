5cb10eb1466ccf111965681569bd09c5
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.createJavaScriptFlagGetter = createJavaScriptFlagGetter;
exports.createNativeFlagGetter = createNativeFlagGetter;
exports.getOverrides = getOverrides;
exports.setOverrides = setOverrides;
var _NativeReactNativeFeatureFlags = _interopRequireDefault(require("./specs/NativeReactNativeFeatureFlags"));
var accessedFeatureFlags = new Set();
var overrides;
function createGetter(configName, customValueGetter, defaultValue) {
  var cachedValue;
  return function () {
    if (cachedValue == null) {
      var _customValueGetter;
      cachedValue = (_customValueGetter = customValueGetter()) != null ? _customValueGetter : defaultValue;
    }
    return cachedValue;
  };
}
function createJavaScriptFlagGetter(configName, defaultValue) {
  return createGetter(configName, function () {
    var _overrides, _overrides$configName;
    accessedFeatureFlags.add(configName);
    return (_overrides = overrides) == null || (_overrides$configName = _overrides[configName]) == null ? void 0 : _overrides$configName.call(_overrides, defaultValue);
  }, defaultValue);
}
function createNativeFlagGetter(configName, defaultValue) {
  var skipUnavailableNativeModuleError = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
  return createGetter(configName, function () {
    var _NativeReactNativeFea;
    maybeLogUnavailableNativeModuleError(configName);
    return _NativeReactNativeFeatureFlags.default == null || (_NativeReactNativeFea = _NativeReactNativeFeatureFlags.default[configName]) == null ? void 0 : _NativeReactNativeFea.call(_NativeReactNativeFeatureFlags.default);
  }, defaultValue);
}
function getOverrides() {
  return overrides;
}
function setOverrides(newOverrides) {
  if (overrides != null) {
    throw new Error('Feature flags cannot be overridden more than once');
  }
  if (accessedFeatureFlags.size > 0) {
    var accessedFeatureFlagsStr = Array.from(accessedFeatureFlags).join(', ');
    throw new Error(`Feature flags were accessed before being overridden: ${accessedFeatureFlagsStr}`);
  }
  overrides = newOverrides;
}
var reportedConfigNames = new Set();
function maybeLogUnavailableNativeModuleError(configName) {
  if (!_NativeReactNativeFeatureFlags.default && !reportedConfigNames.has(configName)) {
    reportedConfigNames.add(configName);
    console.error(`Could not access feature flag '${configName}' because native module method was not available`);
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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