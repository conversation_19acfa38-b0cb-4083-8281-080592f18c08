{"version": 3, "names": ["_NativeAnimatedHelper", "_interopRequireDefault", "require", "ReactNativeFeatureFlags", "_interopRequireWildcard", "_AnimatedProps", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "startNativeAnimationNextId", "_nativeID", "_classPrivateFieldLooseKey2", "_onEnd", "_useNativeDriver", "Animation", "exports", "config", "_config$isInteraction", "_config$iterations", "_classCallCheck2", "writable", "value", "_classPrivateFieldLooseBase2", "NativeAnimatedHelper", "shouldUseNativeDriver", "__active", "__isInteraction", "isInteraction", "__isLooping", "isLooping", "__iterations", "iterations", "__DEV__", "__debugID", "debugID", "_createClass2", "key", "start", "fromValue", "onUpdate", "onEnd", "previousAnimation", "animatedValue", "__isNative", "Error", "stop", "nativeID", "identifier", "API", "setWaitingForIdentifier", "stopAnimation", "unsetWaitingForIdentifier", "__getNativeAnimationConfig", "__findAnimatedPropsNodes", "node", "result", "AnimatedProps", "push", "child", "__get<PERSON><PERSON><PERSON><PERSON>", "apply", "_toConsumableArray2", "__startAnimationIfNative", "_this", "startNativeAnimationWaitId", "__makeNative", "platformConfig", "generateNewAnimationId", "startAnimatingNode", "__getNativeTag", "__notifyAnimationEnd", "__onAnimatedValueUpdateReceived", "for<PERSON>ach", "update", "callback", "__getDebugID", "undefined"], "sources": ["Animation.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {PlatformConfig} from '../AnimatedPlatformConfig';\nimport type AnimatedNode from '../nodes/AnimatedNode';\nimport type AnimatedValue from '../nodes/AnimatedValue';\n\nimport NativeAnimatedHelper from '../../../src/private/animated/NativeAnimatedHelper';\nimport * as ReactNativeFeatureFlags from '../../../src/private/featureflags/ReactNativeFeatureFlags';\nimport AnimatedProps from '../nodes/AnimatedProps';\n\nexport type EndResult = {finished: boolean, value?: number, ...};\nexport type EndCallback = (result: EndResult) => void;\n\nexport type AnimationConfig = $ReadOnly<{\n  isInteraction?: boolean,\n  useNativeDriver: boolean,\n  platformConfig?: PlatformConfig,\n  onComplete?: ?EndCallback,\n  iterations?: number,\n  isLooping?: boolean,\n  debugID?: ?string,\n  ...\n}>;\n\nlet startNativeAnimationNextId = 1;\n\n// Important note: start() and stop() will only be called at most once.\n// Once an animation has been stopped or finished its course, it will\n// not be reused.\nexport default class Animation {\n  #nativeID: ?number;\n  #onEnd: ?EndCallback;\n  #useNativeDriver: boolean;\n\n  __active: boolean;\n  __isInteraction: boolean;\n  __isLooping: ?boolean;\n  __iterations: number;\n  __debugID: ?string;\n\n  constructor(config: AnimationConfig) {\n    this.#useNativeDriver = NativeAnimatedHelper.shouldUseNativeDriver(config);\n\n    this.__active = false;\n    this.__isInteraction = config.isInteraction ?? !this.#useNativeDriver;\n    this.__isLooping = config.isLooping;\n    this.__iterations = config.iterations ?? 1;\n    if (__DEV__) {\n      this.__debugID = config.debugID;\n    }\n  }\n\n  start(\n    fromValue: number,\n    onUpdate: (value: number) => void,\n    onEnd: ?EndCallback,\n    previousAnimation: ?Animation,\n    animatedValue: AnimatedValue,\n  ): void {\n    if (!this.#useNativeDriver && animatedValue.__isNative === true) {\n      throw new Error(\n        'Attempting to run JS driven animation on animated node ' +\n          'that has been moved to \"native\" earlier by starting an ' +\n          'animation with `useNativeDriver: true`',\n      );\n    }\n\n    this.#onEnd = onEnd;\n    this.__active = true;\n  }\n\n  stop(): void {\n    if (this.#nativeID != null) {\n      const nativeID = this.#nativeID;\n      const identifier = `${nativeID}:stopAnimation`;\n      try {\n        // This is only required when singleOpBatching is used, as otherwise\n        // we flush calls immediately when there's no pending queue.\n        NativeAnimatedHelper.API.setWaitingForIdentifier(identifier);\n        NativeAnimatedHelper.API.stopAnimation(nativeID);\n      } finally {\n        NativeAnimatedHelper.API.unsetWaitingForIdentifier(identifier);\n      }\n    }\n    this.__active = false;\n  }\n\n  __getNativeAnimationConfig(): $ReadOnly<{\n    platformConfig: ?PlatformConfig,\n    ...\n  }> {\n    // Subclasses that have corresponding animation implementation done in native\n    // should override this method\n    throw new Error('This animation type cannot be offloaded to native');\n  }\n\n  __findAnimatedPropsNodes(node: AnimatedNode): Array<AnimatedProps> {\n    const result = [];\n\n    if (node instanceof AnimatedProps) {\n      result.push(node);\n      return result;\n    }\n\n    for (const child of node.__getChildren()) {\n      result.push(...this.__findAnimatedPropsNodes(child));\n    }\n\n    return result;\n  }\n\n  __startAnimationIfNative(animatedValue: AnimatedValue): boolean {\n    if (!this.#useNativeDriver) {\n      return false;\n    }\n\n    const startNativeAnimationWaitId = `${startNativeAnimationNextId}:startAnimation`;\n    startNativeAnimationNextId += 1;\n    NativeAnimatedHelper.API.setWaitingForIdentifier(\n      startNativeAnimationWaitId,\n    );\n    try {\n      const config = this.__getNativeAnimationConfig();\n      animatedValue.__makeNative(config.platformConfig);\n      this.#nativeID = NativeAnimatedHelper.generateNewAnimationId();\n      NativeAnimatedHelper.API.startAnimatingNode(\n        this.#nativeID,\n        animatedValue.__getNativeTag(),\n        config,\n        result => {\n          this.__notifyAnimationEnd(result);\n\n          // When using natively driven animations, once the animation completes,\n          // we need to ensure that the JS side nodes are synced with the updated\n          // values.\n          const {value} = result;\n          if (value != null) {\n            animatedValue.__onAnimatedValueUpdateReceived(value);\n\n            if (this.__isLooping === true) {\n              return;\n            }\n\n            // Once the JS side node is synced with the updated values, trigger an\n            // update on the AnimatedProps nodes to call any registered callbacks.\n            this.__findAnimatedPropsNodes(animatedValue).forEach(node =>\n              node.update(),\n            );\n          }\n        },\n      );\n\n      return true;\n    } catch (e) {\n      throw e;\n    } finally {\n      NativeAnimatedHelper.API.unsetWaitingForIdentifier(\n        startNativeAnimationWaitId,\n      );\n    }\n  }\n\n  /**\n   * Notify the completion callback that the animation has ended. The completion\n   * callback will never be called more than once.\n   */\n  __notifyAnimationEnd(result: EndResult): void {\n    const callback = this.#onEnd;\n    if (callback != null) {\n      this.#onEnd = null;\n      callback(result);\n    }\n  }\n\n  __getDebugID(): ?string {\n    if (__DEV__) {\n      return this.__debugID;\n    }\n    return undefined;\n  }\n}\n"], "mappings": ";;;;;;;;;;AAcA,IAAAA,qBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,cAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAAmD,SAAAE,wBAAAE,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAJ,uBAAA,YAAAA,wBAAAE,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAgBnD,IAAImB,0BAA0B,GAAG,CAAC;AAAC,IAAAC,SAAA,OAAAC,2BAAA,CAAAZ,OAAA;AAAA,IAAAa,MAAA,OAAAD,2BAAA,CAAAZ,OAAA;AAAA,IAAAc,gBAAA,OAAAF,2BAAA,CAAAZ,OAAA;AAAA,IAKde,SAAS,GAAAC,OAAA,CAAAhB,OAAA;EAW5B,SAAAe,UAAYE,MAAuB,EAAE;IAAA,IAAAC,qBAAA,EAAAC,kBAAA;IAAA,IAAAC,gBAAA,CAAApB,OAAA,QAAAe,SAAA;IAAAR,MAAA,CAAAC,cAAA,OAAAG,SAAA;MAAAU,QAAA;MAAAC,KAAA;IAAA;IAAAf,MAAA,CAAAC,cAAA,OAAAK,MAAA;MAAAQ,QAAA;MAAAC,KAAA;IAAA;IAAAf,MAAA,CAAAC,cAAA,OAAAM,gBAAA;MAAAO,QAAA;MAAAC,KAAA;IAAA;IACnC,IAAAC,4BAAA,CAAAvB,OAAA,MAAI,EAAAc,gBAAA,EAAAA,gBAAA,IAAoBU,6BAAoB,CAACC,qBAAqB,CAACR,MAAM,CAAC;IAE1E,IAAI,CAACS,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,eAAe,IAAAT,qBAAA,GAAGD,MAAM,CAACW,aAAa,YAAAV,qBAAA,GAAI,KAAAK,4BAAA,CAAAvB,OAAA,EAAC,IAAI,EAAAc,gBAAA,EAAAA,gBAAA,CAAiB;IACrE,IAAI,CAACe,WAAW,GAAGZ,MAAM,CAACa,SAAS;IACnC,IAAI,CAACC,YAAY,IAAAZ,kBAAA,GAAGF,MAAM,CAACe,UAAU,YAAAb,kBAAA,GAAI,CAAC;IAC1C,IAAIc,OAAO,EAAE;MACX,IAAI,CAACC,SAAS,GAAGjB,MAAM,CAACkB,OAAO;IACjC;EACF;EAAC,WAAAC,aAAA,CAAApC,OAAA,EAAAe,SAAA;IAAAsB,GAAA;IAAAf,KAAA,EAED,SAAAgB,KAAKA,CACHC,SAAiB,EACjBC,QAAiC,EACjCC,KAAmB,EACnBC,iBAA6B,EAC7BC,aAA4B,EACtB;MACN,IAAI,KAAApB,4BAAA,CAAAvB,OAAA,EAAC,IAAI,EAAAc,gBAAA,EAAAA,gBAAA,CAAiB,IAAI6B,aAAa,CAACC,UAAU,KAAK,IAAI,EAAE;QAC/D,MAAM,IAAIC,KAAK,CACb,yDAAyD,GACvD,yDAAyD,GACzD,wCACJ,CAAC;MACH;MAEA,IAAAtB,4BAAA,CAAAvB,OAAA,MAAI,EAAAa,MAAA,EAAAA,MAAA,IAAU4B,KAAK;MACnB,IAAI,CAACf,QAAQ,GAAG,IAAI;IACtB;EAAC;IAAAW,GAAA;IAAAf,KAAA,EAED,SAAAwB,IAAIA,CAAA,EAAS;MACX,IAAI,IAAAvB,4BAAA,CAAAvB,OAAA,MAAI,EAAAW,SAAA,EAAAA,SAAA,KAAc,IAAI,EAAE;QAC1B,IAAMoC,QAAQ,OAAAxB,4BAAA,CAAAvB,OAAA,EAAG,IAAI,EAAAW,SAAA,EAAAA,SAAA,CAAU;QAC/B,IAAMqC,UAAU,GAAG,GAAGD,QAAQ,gBAAgB;QAC9C,IAAI;UAGFvB,6BAAoB,CAACyB,GAAG,CAACC,uBAAuB,CAACF,UAAU,CAAC;UAC5DxB,6BAAoB,CAACyB,GAAG,CAACE,aAAa,CAACJ,QAAQ,CAAC;QAClD,CAAC,SAAS;UACRvB,6BAAoB,CAACyB,GAAG,CAACG,yBAAyB,CAACJ,UAAU,CAAC;QAChE;MACF;MACA,IAAI,CAACtB,QAAQ,GAAG,KAAK;IACvB;EAAC;IAAAW,GAAA;IAAAf,KAAA,EAED,SAAA+B,0BAA0BA,CAAA,EAGvB;MAGD,MAAM,IAAIR,KAAK,CAAC,mDAAmD,CAAC;IACtE;EAAC;IAAAR,GAAA;IAAAf,KAAA,EAED,SAAAgC,wBAAwBA,CAACC,IAAkB,EAAwB;MACjE,IAAMC,MAAM,GAAG,EAAE;MAEjB,IAAID,IAAI,YAAYE,sBAAa,EAAE;QACjCD,MAAM,CAACE,IAAI,CAACH,IAAI,CAAC;QACjB,OAAOC,MAAM;MACf;MAEA,KAAK,IAAMG,KAAK,IAAIJ,IAAI,CAACK,aAAa,CAAC,CAAC,EAAE;QACxCJ,MAAM,CAACE,IAAI,CAAAG,KAAA,CAAXL,MAAM,MAAAM,mBAAA,CAAA9D,OAAA,EAAS,IAAI,CAACsD,wBAAwB,CAACK,KAAK,CAAC,EAAC;MACtD;MAEA,OAAOH,MAAM;IACf;EAAC;IAAAnB,GAAA;IAAAf,KAAA,EAED,SAAAyC,wBAAwBA,CAACpB,aAA4B,EAAW;MAAA,IAAAqB,KAAA;MAC9D,IAAI,KAAAzC,4BAAA,CAAAvB,OAAA,EAAC,IAAI,EAAAc,gBAAA,EAAAA,gBAAA,CAAiB,EAAE;QAC1B,OAAO,KAAK;MACd;MAEA,IAAMmD,0BAA0B,GAAG,GAAGvD,0BAA0B,iBAAiB;MACjFA,0BAA0B,IAAI,CAAC;MAC/Bc,6BAAoB,CAACyB,GAAG,CAACC,uBAAuB,CAC9Ce,0BACF,CAAC;MACD,IAAI;QACF,IAAMhD,MAAM,GAAG,IAAI,CAACoC,0BAA0B,CAAC,CAAC;QAChDV,aAAa,CAACuB,YAAY,CAACjD,MAAM,CAACkD,cAAc,CAAC;QACjD,IAAA5C,4BAAA,CAAAvB,OAAA,MAAI,EAAAW,SAAA,EAAAA,SAAA,IAAaa,6BAAoB,CAAC4C,sBAAsB,CAAC,CAAC;QAC9D5C,6BAAoB,CAACyB,GAAG,CAACoB,kBAAkB,KAAA9C,4BAAA,CAAAvB,OAAA,EACzC,IAAI,EAAAW,SAAA,EAAAA,SAAA,GACJgC,aAAa,CAAC2B,cAAc,CAAC,CAAC,EAC9BrD,MAAM,EACN,UAAAuC,MAAM,EAAI;UACRQ,KAAI,CAACO,oBAAoB,CAACf,MAAM,CAAC;UAKjC,IAAOlC,KAAK,GAAIkC,MAAM,CAAflC,KAAK;UACZ,IAAIA,KAAK,IAAI,IAAI,EAAE;YACjBqB,aAAa,CAAC6B,+BAA+B,CAAClD,KAAK,CAAC;YAEpD,IAAI0C,KAAI,CAACnC,WAAW,KAAK,IAAI,EAAE;cAC7B;YACF;YAIAmC,KAAI,CAACV,wBAAwB,CAACX,aAAa,CAAC,CAAC8B,OAAO,CAAC,UAAAlB,IAAI;cAAA,OACvDA,IAAI,CAACmB,MAAM,CAAC,CAAC;YAAA,CACf,CAAC;UACH;QACF,CACF,CAAC;QAED,OAAO,IAAI;MACb,CAAC,CAAC,OAAOpF,CAAC,EAAE;QACV,MAAMA,CAAC;MACT,CAAC,SAAS;QACRkC,6BAAoB,CAACyB,GAAG,CAACG,yBAAyB,CAChDa,0BACF,CAAC;MACH;IACF;EAAC;IAAA5B,GAAA;IAAAf,KAAA,EAMD,SAAAiD,oBAAoBA,CAACf,MAAiB,EAAQ;MAC5C,IAAMmB,QAAQ,OAAApD,4BAAA,CAAAvB,OAAA,EAAG,IAAI,EAAAa,MAAA,EAAAA,MAAA,CAAO;MAC5B,IAAI8D,QAAQ,IAAI,IAAI,EAAE;QACpB,IAAApD,4BAAA,CAAAvB,OAAA,MAAI,EAAAa,MAAA,EAAAA,MAAA,IAAU,IAAI;QAClB8D,QAAQ,CAACnB,MAAM,CAAC;MAClB;IACF;EAAC;IAAAnB,GAAA;IAAAf,KAAA,EAED,SAAAsD,YAAYA,CAAA,EAAY;MACtB,IAAI3C,OAAO,EAAE;QACX,OAAO,IAAI,CAACC,SAAS;MACvB;MACA,OAAO2C,SAAS;IAClB;EAAC;AAAA", "ignoreList": []}