{"C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\screens\\__tests__\\CustomerHomeScreen.test.tsx": [1, 0], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\tests\\e2e\\MessagingSystem.test.ts": [1, 0], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\__tests__\\screens\\AccountSettingsScreen.test.tsx": [1, 0], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\accessibility.test.ts": [1, 301], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\__tests__\\integration\\EnhancedFeaturesIntegration.test.tsx": [1, 0], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\__tests__\\integration\\CustomerHomeFlow.integration.test.tsx": [0, 514], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\services\\__tests__\\passwordlessAuthService.test.ts": [1, 237], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\components\\accessibility\\__tests__\\EnhancedTouchTarget.test.tsx": [0, 1536], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\tests\\e2e\\ProviderServiceManagement.test.ts": [1, 0], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\screens\\__tests__\\BookingFlowScreen.test.tsx": [1, 0], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\__tests__\\screens\\MessagesScreen.test.tsx": [1, 0], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\services\\__tests__\\cacheService.test.ts": [0, 40306], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\hooks\\__tests__\\useErrorHandling.test.ts": [0, 10470], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\tests\\e2e\\CoreIntegration.test.ts": [1, 170], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\tests\\e2e\\CustomerBookingFlow.test.ts": [1, 0], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\services\\__tests__\\testAccountsService.test.ts": [1, 224], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\enhancedTestingQA.test.ts": [1, 291], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\imageAccessibilityValidation.test.ts": [1, 186], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\services\\__tests__\\performanceMonitor.test.ts": [0, 277], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\performance.test.ts": [1, 217], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\components\\accessibility\\__tests__\\AccessibilityAudit.test.tsx": [0, 432], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\components\\atoms\\__tests__\\Input.test.tsx": [1, 677], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\tests\\e2e\\EndToEndIntegration.test.ts": [0, 354], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\formAccessibilityValidation.test.ts": [1, 187], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\components\\ui\\__tests__\\UnifiedButton.test.tsx": [1, 1866], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\components\\error\\__tests__\\ErrorBoundary.test.tsx": [0, 529], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\components\\atoms\\__tests__\\Text.test.tsx": [1, 306], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\tests\\payment\\PaymentSystemIntegration.test.ts": [0, 214], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\formValidation.test.ts": [1, 201], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\focusIndicatorValidation.test.ts": [1, 171], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\features\\authentication\\__tests__\\authSlice.test.ts": [1, 166], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\store\\__tests__\\authSlice.test.ts": [0, 177], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\contrastValidation.test.ts": [1, 188], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\errorHandler.test.ts": [1, 1835], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\components\\atoms\\__tests__\\Card.test.tsx": [1, 381], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\services\\__tests__\\apiClient.test.ts": [0, 239], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\services\\__tests__\\authService.test.ts": [1, 283], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\contrastEnhancer.test.ts": [1, 170], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\store\\__tests__\\providersSlice.test.ts": [1, 1670], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\navigation\\__tests__\\types.test.ts": [1, 111], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\wcag-standards-fix.test.ts": [1, 111], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\__tests__\\components\\AnimatedButton.test.tsx": [1, 235], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\services\\__tests__\\authService.integration.test.ts": [1, 113]}